# Development Preferences & Guidelines

This document outlines the coding standards, preferences, and guidelines that should be followed when creating or modifying files in this project.

## 📋 Table of Contents
- [Code Style Preferences](#code-style-preferences)
- [JavaScript Guidelines](#javascript-guidelines)
- [UI/UX Preferences](#uiux-preferences)
- [Database Guidelines](#database-guidelines)
- [File Organization](#file-organization)
- [Testing Preferences](#testing-preferences)
- [Security Guidelines](#security-guidelines)

## 🎨 Code Style Preferences

### General Principles
- **Clean Code**: Prefer clean implementations without fallbacks or duplicate code
- **No Console Logging**: Avoid console logging in production code (use console for debugging only)
- **Centralized Approach**: Organize code in external files rather than inline scripts
- **Consistent Naming**: Use descriptive, consistent naming conventions

### Code Quality
- Eliminate duplicate IDs in HTML
- Use meaningful variable and function names
- Add comments for complex logic
- Follow DRY (Don't Repeat Yourself) principle

## 🟨 JavaScript Guidelines

### jQuery Preferences
- **Document Ready Pattern**: Always use `$(document).ready()` for event handling
- **Event Delegation**: Use `$(document).on('click', '#elementId', ...)` syntax everywhere
- **ID Selectors**: Prefer using `id` attributes for event binding instead of class selectors
- **No Inline Handlers**: Avoid inline `onclick` handlers to prevent reference errors
- **AJAX Over Forms**: Use AJAX jQuery calls to process and retrieve data instead of form submissions

### Event Handling Pattern
```javascript
// Preferred pattern
$(document).ready(function() {
  $(document).on('click', '#elementId', function(e) {
    e.preventDefault();
    // Handle click
  });
});

// Avoid direct handlers
$('#element').click(function() { ... }); // Don't use this
```

### External Files
- Organize JavaScript code in external files
- Use centralized jQuery implementation
- Implement uniform methods for JavaScript functionality
- Follow document ready pattern consistently

## 🎯 UI/UX Preferences

### Modal Design
- Modals should cover entire page width
- Ensure mobile-friendly responsive design
- Use consistent modal structure across the application

### Navigation
- Logout button in top navigation bar on the right side
- Don't show links for other views in navigation (keep navigation clean)
- Separate profile pages for each user role (admin, teacher, principal, student)

### Table Design
- Limited visible columns with expandable row details on click
- Collapsible filter sections
- Horizontal scroll prevention - content should not go outside margins
- Delete button shows as icon only when records are selected
- Edit/view buttons should open modals for editing and viewing data
- Icons should display correctly
- Trash/edit/view functionality displayed in modals rather than separate pages

### Button Design
- Use icons instead of text for action buttons when appropriate
- Consistent hover effects and transitions
- Color-coded buttons based on action type (view=blue, edit=green, delete=red)

### Role-Specific Views
- **Principal View**: Completely different from teacher view with unique color scheme
- **Principal View**: Read-only with view functionality only (no delete or trash buttons)
- **Admin View**: Full CRUD operations including delete and trash functionality
- Don't reuse teacher view components or design for principal

## 🗄️ Database Guidelines

### SQL Queries
- Use the centralized `config/sql-queries.js` file for all prepared queries
- Organize queries by functionality (students, analytics, filters, etc.)
- Include proper parameterized queries to prevent SQL injection
- Add descriptive comments for complex queries

### Data Management
- Use soft deletes (`is_active` flag) instead of hard deletes where possible
- Include `created_at` and `updated_at` timestamps
- Maintain data integrity with proper foreign key relationships

### Student Data Structure
- Student table includes session column and specific fields
- Each classroom has capacity of 50 students
- Disability column is varchar data type, not boolean
- Support for comprehensive student information including banking details

## 📁 File Organization

### Directory Structure
- `/config/` - Configuration files including SQL queries
- `/public/js/` - External JavaScript files
- `/views/` - EJS templates organized by role
- `/routes/` - Route handlers organized by functionality
- `/controllers/` - Business logic separated from routes

### Naming Conventions
- Use kebab-case for file names (`student-data.js`)
- Use camelCase for JavaScript variables and functions
- Use PascalCase for JavaScript classes and constructors
- Use descriptive names that indicate purpose

## 🧪 Testing Preferences

### Testing Approach
- Prefer alternative approaches over creating new test scripts when current methods aren't working
- Write unit tests for new functionality
- Test code by writing tests and running them before deployment
- Iterate on tests until they pass for better outcomes

### Test Coverage
- Focus on critical business logic
- Test edge cases and error conditions
- Ensure mobile responsiveness testing

## 🔒 Security Guidelines

### Authentication & Authorization
- Implement proper role-based access control
- Validate user permissions for each action
- Use session management for user state

### Data Protection
- Sanitize all user inputs
- Use parameterized queries to prevent SQL injection
- Validate data on both client and server side
- Implement proper error handling without exposing sensitive information

## 🚀 Deployment Guidelines

### CSS Framework
- Avoid using `cdn.tailwindcss.com` in production
- Install Tailwind CSS as PostCSS plugin or use Tailwind CLI for production deployments
- Use production-optimized builds

### Performance
- Minimize external dependencies
- Optimize images and assets
- Use efficient database queries
- Implement proper caching strategies

## 📝 Feature-Specific Guidelines

### Voucher Functionality
- Support voucher functionality for both returned and issued items
- Make voucher data presentation presentable and tidy
- Place school name at top with reduced font size for voucher title

### Infrastructure Management
- Detailed equipment tracking with serial numbers
- Room-wise inventory management
- IT equipment synchronization with classroom data

### Data Import
- Support CSV import with proper validation
- Handle different date formats and data structures
- Provide clear error messages for import issues

---

## 🔄 Before Creating Any File

**Always review this document before creating or modifying files to ensure consistency with project preferences and standards.**

### Quick Checklist
- [ ] Does the code follow the JavaScript guidelines?
- [ ] Are UI elements consistent with design preferences?
- [ ] Is the database interaction using centralized queries?
- [ ] Are security best practices followed?
- [ ] Is the file organization logical and consistent?
- [ ] Does the implementation avoid duplicate code?
- [ ] Are proper error handling and validation in place?

---

*Last Updated: [Current Date]*
*This document should be updated as new preferences and standards are established.*
