/**
 * <PERSON><PERSON><PERSON> to add class_id column to instruction_plans table
 */

const db = require('./config/database');

async function addClassIdToInstructionPlans() {
  try {
    console.log('Checking if class_id column exists in instruction_plans table...');
    
    // Check if column exists
    const [columns] = await db.query(`
      SHOW COLUMNS FROM instruction_plans LIKE 'class_id'
    `);
    
    if (columns.length > 0) {
      console.log('✅ class_id column already exists in instruction_plans table');
      return;
    }
    
    console.log('Adding class_id column to instruction_plans table...');
    
    // Add the column
    await db.query(`
      ALTER TABLE instruction_plans 
      ADD COLUMN class_id INT NULL AFTER subject_id,
      ADD CONSTRAINT fk_instruction_plans_class
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL
    `);
    
    console.log('✅ class_id column added successfully to instruction_plans table');
    
  } catch (error) {
    console.error('❌ Error adding class_id column:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
addClassIdToInstructionPlans();
