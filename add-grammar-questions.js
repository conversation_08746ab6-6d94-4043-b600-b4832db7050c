const db = require('./config/database');

async function addGrammarQuestions() {
    try {
        console.log('Adding grammar, antonyms, and synonyms questions to K12 test...');
        
        // Get admin user ID
        const [adminUsers] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (adminUsers.length === 0) {
            throw new Error('No admin user found. Please create an admin user first.');
        }
        
        const adminId = adminUsers[0].id;
        
        // Find the K12 Reading Comprehension Test
        const [exams] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ?', ['K12 Reading Comprehension Test']);
        if (exams.length === 0) {
            throw new Error('K12 Reading Comprehension Test not found. Please run the sample-essay-questions.js script first.');
        }
        
        const examId = exams[0].exam_id;
        
        // Get the highest section position
        const [sections] = await db.query('SELECT MAX(position) as max_position FROM sections WHERE exam_id = ?', [examId]);
        const nextPosition = (sections[0].max_position || 0) + 1;
        
        // Create a new section for grammar questions
        const [grammarSectionResult] = await db.query(
            'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
            [examId, 'Additional Questions on Grammar', nextPosition]
        );
        
        const grammarSectionId = grammarSectionResult.insertId;
        
        // Create a new section for antonyms and synonyms
        const [synonymSectionResult] = await db.query(
            'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
            [examId, 'Antonyms and Synonyms', nextPosition + 1]
        );
        
        const synonymSectionId = synonymSectionResult.insertId;
        
        // Sample grammar questions (multiple choice)
        const grammarQuestions = [
            {
                question_type: 'multiple_choice',
                question_text: 'Which sentence contains a grammatical error?',
                options: [
                    { option_text: 'She went to the store yesterday.', is_correct: false },
                    { option_text: 'They are going to the beach tomorrow.', is_correct: false },
                    { option_text: 'He don\'t like chocolate ice cream.', is_correct: true },
                    { option_text: 'We have been waiting for an hour.', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: 'The correct form should be "He doesn\'t like chocolate ice cream." The third-person singular form of "do" in the present tense is "does."',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Identify the sentence with the correct use of punctuation.',
                options: [
                    { option_text: 'Mary said "I\'ll be there at 5 PM."', is_correct: false },
                    { option_text: 'Mary said, "I\'ll be there at 5 PM."', is_correct: true },
                    { option_text: 'Mary said: "I\'ll be there at 5 PM"', is_correct: false },
                    { option_text: 'Mary said "I\'ll be there at 5 PM"', is_correct: false }
                ],
                correct_answer: '1', // Index of the correct option
                solution_text: 'When reporting direct speech, a comma should be placed after the reporting verb, and the quotation should be enclosed in quotation marks with the final punctuation inside the closing quotation mark.',
                marks: 1
            },
            {
                question_type: 'true_false',
                question_text: 'In the sentence "The committee are divided on this issue," the use of "are" is grammatically correct.',
                correct_answer: 'true',
                solution_text: 'This is correct in British English, where collective nouns can take plural verbs when emphasizing the individual members of the group. In American English, "is" would be preferred.',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which of the following is a complex sentence?',
                options: [
                    { option_text: 'I went to the store.', is_correct: false },
                    { option_text: 'I went to the store and bought some milk.', is_correct: false },
                    { option_text: 'Although it was raining, I went to the store.', is_correct: true },
                    { option_text: 'I went to the store; I bought some milk.', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: 'A complex sentence contains an independent clause and at least one dependent clause. "Although it was raining" is a dependent clause, and "I went to the store" is an independent clause.',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which word is a preposition in the sentence: "The book is on the table near the window."',
                options: [
                    { option_text: 'book', is_correct: false },
                    { option_text: 'is', is_correct: false },
                    { option_text: 'on', is_correct: true },
                    { option_text: 'the', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: '"On" is a preposition that shows the relationship between the book and the table. "Near" is also a preposition in this sentence.',
                marks: 1
            }
        ];
        
        // Sample antonyms and synonyms questions
        const synonymQuestions = [
            {
                question_type: 'multiple_choice',
                question_text: 'Which word is an antonym of "benevolent"?',
                options: [
                    { option_text: 'Generous', is_correct: false },
                    { option_text: 'Malevolent', is_correct: true },
                    { option_text: 'Charitable', is_correct: false },
                    { option_text: 'Kindly', is_correct: false }
                ],
                correct_answer: '1', // Index of the correct option
                solution_text: '"Malevolent" means wishing evil or harm to others, which is the opposite of "benevolent," which means wishing good to others.',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which word is a synonym of "lethargic"?',
                options: [
                    { option_text: 'Energetic', is_correct: false },
                    { option_text: 'Vibrant', is_correct: false },
                    { option_text: 'Sluggish', is_correct: true },
                    { option_text: 'Animated', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: '"Sluggish" means slow to respond or move, which is similar to "lethargic," meaning lacking energy.',
                marks: 1
            },
            {
                question_type: 'true_false',
                question_text: '"Verbose" and "laconic" are synonyms.',
                correct_answer: 'false',
                solution_text: 'These words are antonyms. "Verbose" means using more words than necessary, while "laconic" means using very few words.',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which pair contains words that are antonyms?',
                options: [
                    { option_text: 'Luminous - Bright', is_correct: false },
                    { option_text: 'Frugal - Economical', is_correct: false },
                    { option_text: 'Audacious - Timid', is_correct: true },
                    { option_text: 'Colossal - Enormous', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: '"Audacious" means bold or daring, while "timid" means lacking courage or confidence. These words have opposite meanings.',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which word is a synonym of "ubiquitous"?',
                options: [
                    { option_text: 'Rare', is_correct: false },
                    { option_text: 'Omnipresent', is_correct: true },
                    { option_text: 'Unique', is_correct: false },
                    { option_text: 'Scarce', is_correct: false }
                ],
                correct_answer: '1', // Index of the correct option
                solution_text: '"Omnipresent" means present everywhere at the same time, which is synonymous with "ubiquitous."',
                marks: 1
            }
        ];
        
        // Insert grammar questions
        console.log('Adding grammar questions...');
        for (const q of grammarQuestions) {
            // Insert the question
            const [questionResult] = await db.query(
                `INSERT INTO questions (
                    section_id, 
                    question_type, 
                    question_text, 
                    correct_answer, 
                    solution_text, 
                    marks
                ) VALUES (?, ?, ?, ?, ?, ?)`,
                [
                    grammarSectionId,
                    q.question_type,
                    q.question_text,
                    q.correct_answer,
                    q.solution_text,
                    q.marks
                ]
            );
            
            const questionId = questionResult.insertId;
            
            // If it's a multiple choice question, insert the options
            if (q.question_type === 'multiple_choice' && q.options) {
                // Insert options into the options table
                for (let i = 0; i < q.options.length; i++) {
                    const option = q.options[i];
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, option.option_text, option.is_correct ? 1 : 0, i + 1]
                    );
                }
            }
        }
        
        // Insert synonym and antonym questions
        console.log('Adding synonym and antonym questions...');
        for (const q of synonymQuestions) {
            // Insert the question
            const [questionResult] = await db.query(
                `INSERT INTO questions (
                    section_id, 
                    question_type, 
                    question_text, 
                    correct_answer, 
                    solution_text, 
                    marks
                ) VALUES (?, ?, ?, ?, ?, ?)`,
                [
                    synonymSectionId,
                    q.question_type,
                    q.question_text,
                    q.correct_answer,
                    q.solution_text,
                    q.marks
                ]
            );
            
            const questionId = questionResult.insertId;
            
            // If it's a multiple choice question, insert the options
            if (q.question_type === 'multiple_choice' && q.options) {
                // Insert options into the options table
                for (let i = 0; i < q.options.length; i++) {
                    const option = q.options[i];
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, option.option_text, option.is_correct ? 1 : 0, i + 1]
                    );
                }
            }
        }
        
        console.log('Grammar, antonyms, and synonyms questions added successfully!');
        console.log(`Added to exam ID: ${examId}`);
        console.log(`Created grammar section ID: ${grammarSectionId}`);
        console.log(`Created antonyms/synonyms section ID: ${synonymSectionId}`);
        process.exit(0);
    } catch (error) {
        console.error('Error adding grammar questions:', error);
        process.exit(1);
    }
}

// Run the function
addGrammarQuestions();
