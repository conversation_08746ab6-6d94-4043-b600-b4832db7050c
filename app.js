const express = require('express');
const flash = require('connect-flash');
const path = require('path');
const cookieParser = require('cookie-parser');
const http = require('http');
const i18n = require('./config/i18n');
const expressLayouts = require('express-ejs-layouts');
// express-fileupload removed in favor of multer
const { checkTestMode } = require('./middleware/test-mode-middleware');
const { checkSessionTimeout, updateLastActivity } = require('./middleware/session-timeout-middleware');
const { sessionParser } = require('./config/session-config');
const { sessionManager } = require('./middleware/session-manager');
const { initWebSocketServer } = require('./websocket-server');

// Create Express app
const app = express();

// Create HTTP server
const server = http.createServer(app);

// Initialize WebSocket server
const wss = initWebSocketServer(server);

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Add error handling for JSON parsing errors
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    console.error('JSON parsing error:', err.message);
    return res.status(400).json({
      success: false,
      message: 'Invalid JSON in request body',
      error: err.message
    });
  }
  next(err);
});

// Serve vouchers directory
app.use('/uploads/vouchers', express.static(path.join(__dirname, 'public/uploads/vouchers')));
app.use(cookieParser());

// express-fileupload configuration removed in favor of multer
console.log('Using multer for file uploads instead of express-fileupload');

// Language middleware for debugging
const languageMiddleware = require('./middleware/language-middleware');

// Initialize i18n
app.use(i18n.init);
app.use(languageMiddleware);

// Initialize session
app.use(sessionParser);

// Add session manager middleware
app.use(sessionManager);

// Add flash middleware
app.use(flash());

// Add session timeout middleware
app.use(checkSessionTimeout);

// Update last activity time on each request
app.use(updateLastActivity);

// Add test mode middleware
app.use(checkTestMode);

// Import helpers
const { formatDate, formatDateTime } = require('./utils/date-formatter');
const permissionHelper = require('./utils/permission-helper');

// Pass flash messages and helpers to templates
app.use((req, res, next) => {
    res.locals.flashSuccess = req.session.flashSuccess;
    res.locals.flashError = req.session.flashError;

    // Add helpers to all templates
    res.locals.formatDate = formatDate;
    res.locals.formatDateTime = formatDateTime;

    // Add permission helper to all templates
    res.locals.hasPermission = async (permissionName) => {
        if (!req.session.userId) return false;
        return await permissionHelper.hasPermission(req.session.userId, permissionName);
    };
    res.locals.getUserPermissions = async () => {
        if (!req.session.userId) return [];
        return await permissionHelper.getUserPermissions(req.session.userId);
    };

    // Add i18n functions to all templates
    res.locals.__ = res.__ = function() {
        return i18n.__.apply(req, arguments);
    };
    res.locals.__n = res.__n = function() {
        return i18n.__n.apply(req, arguments);
    };

    // Make current language available to templates
    res.locals.currentLanguage = req.getLocale();
    res.locals.availableLanguages = i18n.getLocales();

    // Make session data available to templates
    if (req.session) {
        res.locals.userRole = req.session.userRole;
        res.locals.permissions = req.session.permissions || [];
        res.locals.userId = req.session.userId;
        res.locals.isLoggedIn = !!req.session.userId;
        res.locals.sessionID = req.sessionID;
    }

    // Clear flash messages after passing to templates
    delete req.session.flashSuccess;
    delete req.session.flashError;

    next();
});

// View engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');
// Enable express-ejs-layouts
app.use(expressLayouts);
app.set('layout', 'layouts/default');
app.set("layout extractScripts", true);
app.set("layout extractStyles", true);

// Add contentFor middleware to make contentFor function available in all views
const contentForMiddleware = require('./middleware/content-for-middleware');
app.use(contentForMiddleware);

// Authentication middleware
const authMiddleware = (req, res, next) => {
    if (!req.session.userId) {
        return res.redirect('/login');
    }
    next();
};

// Import routes
const authRoutes = require('./routes/auth-routes');
const adminRoutes = require('./routes/admin-routes');
const testRoutes = require('./routes/test-routes');
const profileRoutes = require('./routes/profile-routes');
const profileUpdateRoutes = require('./routes/profile-update-routes');
const notificationRoutes = require('./routes/notification-routes');
const questionsRoutes = require('./routes/questions-routes');
const reportsRoutes = require('./routes/reports-routes');
const settingsRoutes = require('./routes/settings-routes');
const languageRoutes = require('./routes/language-routes');
const helpRoutes = require('./routes/help-routes');
const groupRoutes = require('./routes/group-routes');
const adminAccessRequestsRoutes = require('./routes/admin-access-requests-routes');
const adminTestAssignmentsRoutes = require('./routes/admin-test-assignments-routes');
const testUploadRoutes = require('./routes/test-upload-routes');
const teacherRoutes = require('./routes/teacher-routes');
const demoLoginRoutes = require('./routes/demo-login-routes');
const itAdminRoutes = require('./routes/it-admin-routes');
const studentRoutes = require('./routes/student-routes');
const principalRoutes = require('./routes/principal-routes');

// Try to load the simple upload test route, but don't fail if formidable isn't installed yet
let simpleUploadTestRoutes;
try {
    simpleUploadTestRoutes = require('./routes/simple-upload-test');
} catch (error) {
    console.warn('Simple upload test routes not loaded:', error.message);
}

// Register routes
app.use('/language', languageRoutes);  // Language routes (no auth required)
app.use('/', authRoutes);
app.use('/', demoLoginRoutes);  // Demo login routes (no auth required)
app.use('/admin', authMiddleware, adminRoutes);  // Admin routes
app.use('/admin/tests', authMiddleware, testRoutes);  // Test management routes
app.use('/admin/access-requests', authMiddleware, adminAccessRequestsRoutes);  // Access requests routes
app.use('/admin/test-assignments', authMiddleware, adminTestAssignmentsRoutes);  // Test assignments routes
app.use('/profile', authMiddleware, profileRoutes);  // Profile routes
app.use('/profile-update', authMiddleware, profileUpdateRoutes);  // Profile update routes
app.use('/notifications', authMiddleware, notificationRoutes);  // Notification routes
app.use('/questions', authMiddleware, questionsRoutes);  // Questions routes
app.use('/reports', authMiddleware, reportsRoutes);  // Reports routes
app.use('/settings', authMiddleware, settingsRoutes);  // Settings routes
app.use('/help', helpRoutes);  // Help routes
app.use('/groups', groupRoutes);  // Group routes
app.use('/test-upload', testUploadRoutes);  // Test upload routes (no auth required for testing)
app.use('/teacher', authMiddleware, teacherRoutes);  // Teacher routes
app.use('/it-admin', authMiddleware, itAdminRoutes);  // IT Admin routes
app.use('/student', authMiddleware, studentRoutes);  // Student routes
app.use('/principal', authMiddleware, principalRoutes);  // Principal routes

// API routes
const teacherApiRoutes = require('./routes/api/teacher-api');
app.use('/api/teacher', authMiddleware, teacherApiRoutes);  // Teacher API routes

// Register simple upload test routes if available
if (simpleUploadTestRoutes) {
    app.use('/simple-upload', simpleUploadTestRoutes);  // Simple upload test routes (no auth required for testing)
}

// Teacher fields demo route (for demonstration purposes)
app.get('/teacher-fields-demo', (req, res) => {
    res.render('teacher-fields-demo', {
        layout: false  // Use standalone layout for demo
    });
});

// Error handling middleware
app.use((req, res, next) => {
    // Check if the request is for an API endpoint
    if (req.path.startsWith('/api/') || req.path.includes('/procurement/')) {
        // Return JSON for API requests
        return res.status(404).json({
            success: false,
            message: 'Endpoint not found',
            error: { status: 404 }
        });
    }

    // Render HTML for regular requests
    res.status(404).render('error', {
        title: 'Page Not Found',
        message: 'Page Not Found',
        error: { status: 404, stack: '' }
    });
});

app.use((err, req, res, next) => {
    const status = err.status || 500;

    // Log the error for debugging
    console.error('Server error:', {
        path: req.path,
        method: req.method,
        error: err.message,
        stack: err.stack
    });

    // Check if the request is for an API endpoint
    if (req.path.startsWith('/api/') || req.path.includes('/procurement/')) {
        // Return JSON for API requests
        return res.status(status).json({
            success: false,
            message: err.message || 'Internal Server Error',
            error: {
                status: status,
                details: process.env.NODE_ENV === 'development' ? err.stack : undefined
            }
        });
    }

    // Render HTML for regular requests
    res.status(status).render('error', {
        title: 'Internal Server Error',
        message: err.message || 'Internal Server Error',
        error: {
            status: status,
            stack: process.env.NODE_ENV === 'development' ? err.stack : ''
        }
    });
});

// Start server
const PORT = process.env.PORT || 3003;
server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`WebSocket server is running on ws://localhost:${PORT}/ws`);
});

module.exports = app;
