/**
 * <PERSON><PERSON><PERSON> to assign classes to a teacher and add subjects
 */

const db = require('./config/database');

async function assignClassesToTeacher() {
  try {
    console.log('Starting class assignment process...');

    // Get the demo teacher (computer teacher)
    const [teachers] = await db.query(`
      SELECT id, name, email, role
      FROM users
      WHERE email = '<EMAIL>' AND role = 'teacher'
    `);

    if (teachers.length === 0) {
      console.log('Demo teacher not found. Creating demo teacher...');

      // First check if the user already exists with a different query
      const [existingUser] = await db.query(`
        SELECT id FROM users WHERE email = '<EMAIL>'
      `);

      let result;

      if (existingUser.length > 0) {
        // Update existing user
        [result] = await db.query(`
          UPDATE users
          SET role = 'teacher', name = 'Demo Computer Teacher', username = 'csstudent'
          WHERE id = ?
        `, [existingUser[0].id]);

        result = { id: existingUser[0].id };
        console.log(`Updated existing user with ID: ${existingUser[0].id}`);
      } else {
        // Get all required fields from users table
        const [userColumns] = await db.query(`
          DESCRIBE users
        `);

        const requiredFields = userColumns
          .filter(col => col.Null === 'NO' && col.Default === null && col.Extra !== 'auto_increment')
          .map(col => col.Field);

        console.log('Required fields for user creation:', requiredFields);

        // Create a dynamic query with all required fields
        const fields = ['name', 'username', 'email', 'password', 'role', 'bio', 'created_at', 'updated_at'];
        const values = [
          'Demo Computer Teacher',
          'csstudent',
          '<EMAIL>',
          '$2b$10$XVXJnDDSN.KtWR9lMeRYFuIUmOt18XUP5xALLJJxXyl8OlP9Mqmwi',
          'teacher',
          'Computer Science Teacher',
          'NOW()',
          'NOW()'
        ];

        // Create the SQL query
        const sql = `
          INSERT INTO users (${fields.join(', ')})
          VALUES (${fields.map((_, i) => '?').join(', ')})
        `;

        // Replace NOW() values with actual function calls
        const finalValues = values.map(v => v === 'NOW()' ? db.raw('NOW()') : v);

        [result] = await db.query(sql, finalValues);
        console.log(`Created new user with ID: ${result.insertId}`);
      }

      const teacherId = result.insertId || result.id;
      console.log(`Created/Updated demo teacher with ID: ${teacherId}`);
    } else {
      console.log(`Found demo teacher: ${teachers[0].name} (ID: ${teachers[0].id})`);
    }

    const teacherId = teachers.length > 0 ? teachers[0].id : (existingUser && existingUser.length > 0 ? existingUser[0].id : null);

    if (!teacherId) {
      console.error('Failed to get or create teacher');
      process.exit(1);
    }

    // Get or create required subjects
    const requiredSubjects = [
      { name: 'Computer Science', code: 'CS' },
      { name: 'Computer Applications', code: 'CA' },
      { name: 'Robotics', code: 'ROB' }
    ];

    const subjectIds = [];

    for (const subject of requiredSubjects) {
      // Check if subject exists
      const [existingSubject] = await db.query(`
        SELECT id FROM subjects WHERE name = ? OR code = ?
      `, [subject.name, subject.code]);

      let subjectId;

      if (existingSubject.length > 0) {
        subjectId = existingSubject[0].id;
        console.log(`Found existing subject: ${subject.name} (ID: ${subjectId})`);
      } else {
        // Create subject if not exists
        const [result] = await db.query(`
          INSERT INTO subjects (name, code, created_at, updated_at)
          VALUES (?, ?, NOW(), NOW())
        `, [subject.name, subject.code]);

        subjectId = result.insertId;
        console.log(`Created new subject: ${subject.name} (ID: ${subjectId})`);
      }

      subjectIds.push({ id: subjectId, name: subject.name, code: subject.code });

      // Assign subject to teacher
      await db.query(`
        INSERT INTO teacher_subjects (teacher_id, subject_id, created_at, updated_at)
        VALUES (?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `, [teacherId, subjectId]);

      console.log(`Assigned subject ${subject.name} to teacher`);
    }

    // Get required classes
    const requiredClasses = [
      { grade: '12', trade: 'Non-Medical', section: 'B' },
      { grade: '12', trade: 'Non-Medical', section: 'E' },
      { grade: '12', trade: 'Medical', section: 'A' },
      { grade: '12', trade: 'Commerce', section: 'A' },
      { grade: '12', trade: 'Commerce', section: 'B' }
    ];

    // Get or create trades
    const trades = {};
    for (const cls of requiredClasses) {
      if (!trades[cls.trade]) {
        // Check if trade exists
        const [existingTrade] = await db.query(`
          SELECT id FROM trades WHERE name = ?
        `, [cls.trade]);

        if (existingTrade.length > 0) {
          trades[cls.trade] = existingTrade[0].id;
          console.log(`Found existing trade: ${cls.trade} (ID: ${trades[cls.trade]})`);
        } else {
          // Create trade if not exists
          const [result] = await db.query(`
            INSERT INTO trades (name, created_at, updated_at)
            VALUES (?, NOW(), NOW())
          `, [cls.trade]);

          trades[cls.trade] = result.insertId;
          console.log(`Created new trade: ${cls.trade} (ID: ${trades[cls.trade]})`);
        }
      }
    }

    // Get or create classes
    for (const cls of requiredClasses) {
      // Check if class exists
      const [existingClass] = await db.query(`
        SELECT id FROM classes WHERE grade = ?
      `, [cls.grade]);

      let classId;

      if (existingClass.length > 0) {
        classId = existingClass[0].id;
        console.log(`Found existing class: Grade ${cls.grade} (ID: ${classId})`);
      } else {
        // Create class if not exists
        const [result] = await db.query(`
          INSERT INTO classes (grade, created_at, updated_at)
          VALUES (?, NOW(), NOW())
        `, [cls.grade]);

        classId = result.insertId;
        console.log(`Created new class: Grade ${cls.grade} (ID: ${classId})`);
      }

      // Get or create room
      const roomNumber = Math.floor(Math.random() * 20) + 1; // Random room between 1-20

      const [existingRoom] = await db.query(`
        SELECT id FROM rooms WHERE room_number = ?
      `, [roomNumber]);

      let roomId;

      if (existingRoom.length > 0) {
        roomId = existingRoom[0].id;
        console.log(`Found existing room: ${roomNumber} (ID: ${roomId})`);
      } else {
        // Create room if not exists
        const [result] = await db.query(`
          INSERT INTO rooms (room_number, created_at, updated_at)
          VALUES (?, NOW(), NOW())
        `, [roomNumber]);

        roomId = result.insertId;
        console.log(`Created new room: ${roomNumber} (ID: ${roomId})`);
      }

      // Check if classroom exists
      const [existingClassroom] = await db.query(`
        SELECT id FROM classrooms
        WHERE class_id = ? AND trade_id = ? AND section = ?
      `, [classId, trades[cls.trade], cls.section]);

      let classroomId;

      if (existingClassroom.length > 0) {
        classroomId = existingClassroom[0].id;
        console.log(`Found existing classroom: Grade ${cls.grade} ${cls.trade} ${cls.section} (ID: ${classroomId})`);

        // Update classroom to use our room
        await db.query(`
          UPDATE classrooms
          SET room_id = ?, session = '2025-26', is_active = 1, updated_at = NOW()
          WHERE id = ?
        `, [roomId, classroomId]);

        console.log(`Updated classroom to use room ${roomNumber}`);
      } else {
        // Create classroom if not exists
        const [result] = await db.query(`
          INSERT INTO classrooms (class_id, trade_id, section, room_id, session, is_active, created_at, updated_at)
          VALUES (?, ?, ?, ?, '2025-26', 1, NOW(), NOW())
        `, [classId, trades[cls.trade], cls.section, roomId]);

        classroomId = result.insertId;
        console.log(`Created new classroom: Grade ${cls.grade} ${cls.trade} ${cls.section} (ID: ${classroomId})`);
      }

      // Assign classroom to teacher
      await db.query(`
        INSERT INTO teacher_classes (teacher_id, classroom_id, created_at, updated_at)
        VALUES (?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `, [teacherId, classroomId]);

      console.log(`Assigned classroom Grade ${cls.grade} ${cls.trade} ${cls.section} to teacher`);
    }

    console.log('\nClass assignment process completed successfully!');
    console.log(`Teacher ID: ${teacherId}`);
    console.log('Assigned subjects:');
    subjectIds.forEach(subject => console.log(`- ${subject.name} (${subject.code})`));
    console.log('Assigned classes:');
    requiredClasses.forEach(cls => console.log(`- Grade ${cls.grade} ${cls.trade} ${cls.section}`));

    process.exit(0);
  } catch (error) {
    console.error('Error assigning classes to teacher:', error);
    process.exit(1);
  }
}

// Run the function
assignClassesToTeacher();
