/**
 * Debug script for the teacher profile API
 */

const db = require('./config/database');

// Create a mock session
const mockSession = {
  userId: 99,
  userRole: 'teacher',
  username: 'teacher'
};

// Mock request and response objects
const req = {
  session: mockSession,
  isApiRequest: true,
  path: '/api/teacher/profile'
};

const res = {
  status: function(code) {
    console.log('Response status:', code);
    return this;
  },
  json: function(data) {
    console.log('Response data:', JSON.stringify(data, null, 2));
    if (data.success && data.teacher) {
      console.log('\nProfile fetch successful!');
      console.log('Teacher name:', data.teacher.full_name);
      console.log('Teacher email:', data.teacher.email);
      console.log('Teacher has', data.teacher.assigned_subjects?.length || 0, 'subjects');
      console.log('Teacher has', data.teacher.assigned_classes?.length || 0, 'classes');
    } else {
      console.log('Profile data is incomplete or invalid');
    }
  }
};

// Import the teacher profile API handler
const teacherProfileHandler = require('./routes/api/teacher-profile-api');

// Find the profile route handler
const profileRoute = teacherProfileHandler.stack.find(layer =>
  layer.route && layer.route.path === '/profile' && layer.route.methods.get
);

if (!profileRoute) {
  console.error('Could not find profile route handler');
  process.exit(1);
}

// Get the handler function
const handler = profileRoute.route.stack[0].handle;

// Call the handler directly
console.log('Calling profile handler directly...');
handler(req, res).catch(error => {
  console.error('Error calling profile handler:', error);
});
