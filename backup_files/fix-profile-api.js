/**
 * Fix script for the teacher profile API
 */

const db = require('./config/database');

async function run() {
  try {
    console.log('Starting profile API fix...');

    // Get teacher ID 99
    const [teachers] = await db.query(
      `SELECT id, username, name, email, profile_image, subjects, bio
       FROM users
       WHERE id = 99`
    );

    if (teachers.length === 0) {
      console.log('Teacher with ID 99 not found');
      return;
    }

    const teacher = teachers[0];
    console.log('Found teacher:', teacher);

    // Update the teacher's name if it's null
    if (!teacher.name) {
      console.log('Teacher name is null, updating...');
      await db.query(
        `UPDATE users SET name = 'Demo Teacher' WHERE id = 99`
      );
      console.log('Teacher name updated to "Demo Teacher"');
    }

    // Update the teacher's email if it's null
    if (!teacher.email) {
      console.log('Teacher email is null, updating...');
      await db.query(
        `UPDATE users SET email = '<EMAIL>' WHERE id = 99`
      );
      console.log('Teacher email updated to "<EMAIL>"');
    }

    // Check if the teacher has subjects
    if (!teacher.subjects) {
      console.log('Teacher subjects is null, updating...');
      await db.query(
        `UPDATE users SET subjects = 'Computer Science, Mathematics, Physics' WHERE id = 99`
      );
      console.log('Teacher subjects updated');
    }

    // Check if the teacher has a bio
    if (!teacher.bio) {
      console.log('Teacher bio is null, updating...');
      await db.query(
        `UPDATE users SET bio = 'Experienced teacher with a passion for computer science education.' WHERE id = 99`
      );
      console.log('Teacher bio updated');
    }

    // Note: qualifications field doesn't exist in the database schema

    // Get the updated teacher data
    const [updatedTeachers] = await db.query(
      `SELECT id, username, name, email, profile_image, subjects, bio
       FROM users
       WHERE id = 99`
    );

    console.log('Updated teacher data:', updatedTeachers[0]);
    console.log('Profile API fix completed successfully');

    process.exit(0);
  } catch (error) {
    console.error('Error fixing profile API:', error);
    process.exit(1);
  }
}

run();
