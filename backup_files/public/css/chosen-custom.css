/* Custom styles for Chosen multi-select fields */

/* Reduce font size and improve UI for multi-selected values */
.chosen-container-multi .chosen-choices li.search-choice {
    background-color: #e5e7eb;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    padding: 0.1rem 0.5rem;
    padding-right: 20px; /* Add extra padding for the close button */
    margin-right: 0.25rem;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    line-height: 1.5;
    position: relative; /* Ensure proper positioning of close button */
}

/* Style for the remove button */
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
    color: #6b7280;
    margin-right: 0;
    font-weight: bold;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    background-color: transparent;
    width: 12px;
    height: 12px;
}

.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
    background-position: -42px -10px;
}

/* Style for the dropdown */
.chosen-container .chosen-results li.highlighted {
    background-color: #4f46e5;
    background-image: none;
    color: white;
}

/* Style for the container */
.chosen-container-multi .chosen-choices {
    border-color: #d1d5db;
    min-height: 38px;
    padding: 4px;
    border-radius: 0.25rem;
}

/* Style for the search box */
.chosen-container-multi .chosen-choices li.search-field input[type="text"] {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    height: auto;
}

/* Style for the dropdown container */
.chosen-container.chosen-with-drop .chosen-drop {
    border-color: #4f46e5;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Style for selected option in dropdown */
.chosen-container .chosen-results li.result-selected {
    background-color: #e5e7eb;
}

/* Color-coded categories */
.chosen-container-multi .chosen-choices li.search-choice {
    background-color: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
    background-image: none;
}

/* Different colors for different categories (will cycle through these) */
.chosen-container-multi .chosen-choices li.search-choice:nth-child(3n+1) {
    background-color: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
}

.chosen-container-multi .chosen-choices li.search-choice:nth-child(3n+2) {
    background-color: #dcfce7;
    border-color: #86efac;
    color: #166534;
}

.chosen-container-multi .chosen-choices li.search-choice:nth-child(3n+3) {
    background-color: #fef3c7;
    border-color: #fcd34d;
    color: #92400e;
}

/* Fix for chosen container width */
.chosen-container {
    width: 100% !important;
}

/* Fix for chosen container focus */
.chosen-container-active .chosen-choices {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}
