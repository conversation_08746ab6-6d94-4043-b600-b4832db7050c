/* Unified Chat Icon Styles */

/* Ensure chat icons are always visible for logged-in users */
body.logged-in #chatIcon,
body.logged-in #floatingChatButton,
body.logged-in #emergencyChatButton,
body.logged-in #unifiedChatButton {
    display: flex !important;
    position: fixed !important;
    bottom: 80px !important;
    right: 20px !important;
    z-index: 99999 !important;
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    cursor: pointer !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #7c3aed !important; /* Purple 600 */
    transition: transform 0.3s ease !important;
}

@media (min-width: 640px) {
    body.logged-in #chatIcon,
    body.logged-in #floatingChatButton,
    body.logged-in #emergencyChatButton,
    body.logged-in #unifiedChatButton {
        width: 56px !important;
        height: 56px !important;
    }
}

body.logged-in #chatIcon:hover,
body.logged-in #floatingChatButton:hover,
body.logged-in #emergencyChatButton:hover,
body.logged-in #unifiedChatButton:hover {
    transform: scale(1.1) !important;
}

/* Ensure chat icons are not displayed for non-logged-in users */
body:not(.logged-in) #chatIcon,
body:not(.logged-in) #floatingChatButton,
body:not(.logged-in) #emergencyChatButton,
body:not(.logged-in) #unifiedChatButton {
    display: none !important;
}
