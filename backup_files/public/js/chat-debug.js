/**
 * Chat Debug Script
 * This script helps diagnose issues with the chat icon
 */
console.log('Chat Debug Script Loaded');

// Function to check if an element is visible
function isElementVisible(el) {
    if (!el) return false;
    const style = window.getComputedStyle(el);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           el.offsetWidth > 0 && 
           el.offsetHeight > 0;
}

// Function to create a debug overlay
function createDebugOverlay() {
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '10px';
    overlay.style.left = '10px';
    overlay.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
    overlay.style.color = 'white';
    overlay.style.padding = '10px';
    overlay.style.borderRadius = '5px';
    overlay.style.zIndex = '999999';
    overlay.style.maxWidth = '80%';
    overlay.style.maxHeight = '80%';
    overlay.style.overflow = 'auto';
    overlay.style.fontSize = '12px';
    overlay.style.fontFamily = 'monospace';
    
    return overlay;
}

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, running chat debug');
    
    // Check if body has logged-in class
    const isLoggedIn = document.body.classList.contains('logged-in');
    console.log('Body has logged-in class:', isLoggedIn);
    
    // Check for existing chat buttons
    const chatIcon = document.getElementById('chatIcon');
    const floatingChatButton = document.getElementById('floatingChatButton');
    const directChatButton = document.getElementById('directChatButton');
    
    console.log('chatIcon exists:', !!chatIcon);
    console.log('floatingChatButton exists:', !!floatingChatButton);
    console.log('directChatButton exists:', !!directChatButton);
    
    // Check visibility of existing buttons
    console.log('chatIcon visible:', chatIcon ? isElementVisible(chatIcon) : false);
    console.log('floatingChatButton visible:', floatingChatButton ? isElementVisible(floatingChatButton) : false);
    console.log('directChatButton visible:', directChatButton ? isElementVisible(directChatButton) : false);
    
    // Check for session timer overlay
    const sessionInfo = document.getElementById('session-info');
    console.log('Session timer overlay exists:', !!sessionInfo);
    console.log('Session timer overlay visible:', sessionInfo ? isElementVisible(sessionInfo) : false);
    
    // Create debug overlay with information
    const debugOverlay = createDebugOverlay();
    debugOverlay.innerHTML = `
        <h3>Chat Debug Info</h3>
        <p>Body has logged-in class: ${isLoggedIn}</p>
        <p>chatIcon exists: ${!!chatIcon}</p>
        <p>floatingChatButton exists: ${!!floatingChatButton}</p>
        <p>directChatButton exists: ${!!directChatButton}</p>
        <p>chatIcon visible: ${chatIcon ? isElementVisible(chatIcon) : false}</p>
        <p>floatingChatButton visible: ${floatingChatButton ? isElementVisible(floatingChatButton) : false}</p>
        <p>directChatButton visible: ${directChatButton ? isElementVisible(directChatButton) : false}</p>
        <p>Session timer overlay exists: ${!!sessionInfo}</p>
        <p>Session timer overlay visible: ${sessionInfo ? isElementVisible(sessionInfo) : false}</p>
        <button id="fixChatButton" style="background-color: green; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px;">Fix Chat Icon</button>
        <button id="closeDebugOverlay" style="background-color: gray; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px; margin-left: 5px;">Close</button>
    `;
    
    // Add event listeners to buttons
    debugOverlay.querySelector('#fixChatButton').addEventListener('click', function() {
        // Force create a chat button
        const forcedChatButton = document.createElement('div');
        forcedChatButton.id = 'forcedChatButton';
        forcedChatButton.style.cssText = 'position: fixed !important; bottom: 80px !important; right: 20px !important; z-index: 9999999 !important; width: 50px !important; height: 50px !important; border-radius: 50% !important; background-color: #7c3aed !important; display: flex !important; align-items: center !important; justify-content: center !important; cursor: pointer !important; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;';
        forcedChatButton.innerHTML = `
            <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
        `;
        
        document.body.appendChild(forcedChatButton);
        console.log('Forced chat button created');
        
        // Add logged-in class to body if missing
        if (!document.body.classList.contains('logged-in')) {
            document.body.classList.add('logged-in');
            console.log('Added logged-in class to body');
        }
        
        // Update debug overlay
        debugOverlay.innerHTML += '<p style="color: lime;">Fixed! Forced chat button created.</p>';
    });
    
    debugOverlay.querySelector('#closeDebugOverlay').addEventListener('click', function() {
        document.body.removeChild(debugOverlay);
    });
    
    document.body.appendChild(debugOverlay);
});
