/**
 * Chat Icon Debug
 * This script helps diagnose issues with the chat icon
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Chat Icon Debug Script Loaded');
    
    // Check if body has logged-in class
    const isLoggedIn = document.body.classList.contains('logged-in');
    console.log('Body has logged-in class:', isLoggedIn);
    
    // Check for existing chat buttons
    const chatIcon = document.getElementById('chatIcon');
    const floatingChatButton = document.getElementById('floatingChatButton');
    const emergencyChatButton = document.getElementById('emergencyChatButton');
    const unifiedChatButton = document.getElementById('unifiedChatButton');
    
    console.log('chatIcon exists:', !!chatIcon);
    console.log('floatingChatButton exists:', !!floatingChatButton);
    console.log('emergencyChatButton exists:', !!emergencyChatButton);
    console.log('unifiedChatButton exists:', !!unifiedChatButton);
    
    // Check visibility of existing buttons
    if (chatIcon) {
        const style = window.getComputedStyle(chatIcon);
        console.log('chatIcon display:', style.display);
        console.log('chatIcon visibility:', style.visibility);
        console.log('chatIcon position:', style.position);
        console.log('chatIcon z-index:', style.zIndex);
    }
    
    if (floatingChatButton) {
        const style = window.getComputedStyle(floatingChatButton);
        console.log('floatingChatButton display:', style.display);
        console.log('floatingChatButton visibility:', style.visibility);
        console.log('floatingChatButton position:', style.position);
        console.log('floatingChatButton z-index:', style.zIndex);
    }
    
    if (emergencyChatButton) {
        const style = window.getComputedStyle(emergencyChatButton);
        console.log('emergencyChatButton display:', style.display);
        console.log('emergencyChatButton visibility:', style.visibility);
        console.log('emergencyChatButton position:', style.position);
        console.log('emergencyChatButton z-index:', style.zIndex);
    }
    
    if (unifiedChatButton) {
        const style = window.getComputedStyle(unifiedChatButton);
        console.log('unifiedChatButton display:', style.display);
        console.log('unifiedChatButton visibility:', style.visibility);
        console.log('unifiedChatButton position:', style.position);
        console.log('unifiedChatButton z-index:', style.zIndex);
    }
    
    // Add debug button
    const debugButton = document.createElement('div');
    debugButton.id = 'chatIconDebugButton';
    debugButton.style.cssText = 'position: fixed; bottom: 150px; right: 20px; background-color: red; color: white; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 100000;';
    debugButton.textContent = 'Chat Icon Debug';
    
    debugButton.addEventListener('click', function() {
        // Force create a chat button
        const forcedChatButton = document.createElement('div');
        forcedChatButton.id = 'forcedChatButton';
        forcedChatButton.className = 'fixed bottom-20 right-6 bg-red-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg cursor-pointer z-[100000]';
        forcedChatButton.style.cssText = 'position: fixed !important; bottom: 20px !important; right: 20px !important; z-index: 100000 !important; background-color: red !important; display: flex !important;';
        forcedChatButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
        `;
        
        document.body.appendChild(forcedChatButton);
        console.log('Forced chat button created');
        
        // Add logged-in class to body if missing
        if (!document.body.classList.contains('logged-in')) {
            document.body.classList.add('logged-in');
            console.log('Added logged-in class to body');
        }
        
        // Show debug info
        alert('Chat Icon Debug Info:\n' +
              'Body has logged-in class: ' + isLoggedIn + '\n' +
              'chatIcon exists: ' + !!chatIcon + '\n' +
              'floatingChatButton exists: ' + !!floatingChatButton + '\n' +
              'emergencyChatButton exists: ' + !!emergencyChatButton + '\n' +
              'unifiedChatButton exists: ' + !!unifiedChatButton + '\n' +
              'Forced chat button created');
    });
    
    document.body.appendChild(debugButton);
});
