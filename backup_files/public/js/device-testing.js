/**
 * Device Testing Utility
 * 
 * This script provides a simple way to test responsive design across different device sizes.
 * It adds a floating panel that allows developers to quickly switch between common device sizes.
 */

const DeviceTesting = {
  // Common device sizes
  devices: {
    mobile: {
      name: 'Mobile',
      width: 375,
      height: 667
    },
    mobileL: {
      name: 'Mobile L',
      width: 425,
      height: 812
    },
    tablet: {
      name: 'Tablet',
      width: 768,
      height: 1024
    },
    laptop: {
      name: 'Laptop',
      width: 1024,
      height: 768
    },
    laptopL: {
      name: 'Laptop L',
      width: 1440,
      height: 900
    },
    desktop: {
      name: 'Desktop',
      width: 1920,
      height: 1080
    }
  },
  
  // Initialize the testing panel
  init: function() {
    // Only initialize in development mode
    if (!this._isDevelopment()) return;
    
    // Create the panel
    this._createPanel();
    
    // Add event listeners
    this._addEventListeners();
  },
  
  // Check if we're in development mode
  _isDevelopment: function() {
    // Check for development mode query parameter
    return window.location.search.includes('dev_mode=true');
  },
  
  // Create the testing panel
  _createPanel: function() {
    // Create panel container
    const panel = document.createElement('div');
    panel.id = 'device-testing-panel';
    panel.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      z-index: 9999;
      font-family: sans-serif;
      font-size: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    `;
    
    // Add title
    const title = document.createElement('div');
    title.textContent = 'Device Testing';
    title.style.cssText = `
      font-weight: bold;
      margin-bottom: 8px;
      padding-bottom: 5px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      cursor: move;
    `;
    panel.appendChild(title);
    
    // Add device buttons
    const buttonsContainer = document.createElement('div');
    buttonsContainer.style.cssText = `
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    `;
    
    // Add buttons for each device
    for (const [key, device] of Object.entries(this.devices)) {
      const button = document.createElement('button');
      button.textContent = device.name;
      button.dataset.device = key;
      button.style.cssText = `
        background-color: #4a5568;
        border: none;
        color: white;
        padding: 5px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        transition: background-color 0.2s;
      `;
      button.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#2d3748';
      });
      button.addEventListener('mouseout', function() {
        this.style.backgroundColor = '#4a5568';
      });
      buttonsContainer.appendChild(button);
    }
    
    // Add responsive info
    const infoContainer = document.createElement('div');
    infoContainer.style.cssText = `
      margin-top: 8px;
      padding-top: 5px;
      border-top: 1px solid rgba(255, 255, 255, 0.3);
    `;
    
    const currentSize = document.createElement('div');
    currentSize.id = 'current-size';
    currentSize.textContent = `Current: ${window.innerWidth}px × ${window.innerHeight}px`;
    infoContainer.appendChild(currentSize);
    
    const breakpoint = document.createElement('div');
    breakpoint.id = 'current-breakpoint';
    breakpoint.textContent = `Breakpoint: ${this._getCurrentBreakpoint()}`;
    infoContainer.appendChild(breakpoint);
    
    // Add toggle button
    const toggleButton = document.createElement('button');
    toggleButton.textContent = 'Hide';
    toggleButton.id = 'toggle-panel';
    toggleButton.style.cssText = `
      background-color: #e53e3e;
      border: none;
      color: white;
      padding: 5px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      margin-top: 8px;
      width: 100%;
      transition: background-color 0.2s;
    `;
    toggleButton.addEventListener('mouseover', function() {
      this.style.backgroundColor = '#c53030';
    });
    toggleButton.addEventListener('mouseout', function() {
      this.style.backgroundColor = '#e53e3e';
    });
    
    // Add elements to panel
    panel.appendChild(buttonsContainer);
    panel.appendChild(infoContainer);
    panel.appendChild(toggleButton);
    
    // Add panel to body
    document.body.appendChild(panel);
    
    // Make panel draggable
    this._makeDraggable(panel, title);
  },
  
  // Add event listeners
  _addEventListeners: function() {
    // Device buttons
    const buttons = document.querySelectorAll('#device-testing-panel button[data-device]');
    buttons.forEach(button => {
      button.addEventListener('click', () => {
        const deviceKey = button.dataset.device;
        const device = this.devices[deviceKey];
        if (device) {
          this._resizeWindow(device.width, device.height);
        }
      });
    });
    
    // Toggle button
    const toggleButton = document.getElementById('toggle-panel');
    if (toggleButton) {
      toggleButton.addEventListener('click', () => {
        const panel = document.getElementById('device-testing-panel');
        const buttonsContainer = panel.querySelector('div:first-of-type + div');
        const infoContainer = panel.querySelector('div:first-of-type + div + div');
        
        if (buttonsContainer.style.display === 'none') {
          // Show panel
          buttonsContainer.style.display = 'flex';
          infoContainer.style.display = 'block';
          toggleButton.textContent = 'Hide';
        } else {
          // Hide panel
          buttonsContainer.style.display = 'none';
          infoContainer.style.display = 'none';
          toggleButton.textContent = 'Show';
        }
      });
    }
    
    // Update size info on resize
    window.addEventListener('resize', () => {
      this._updateSizeInfo();
    });
  },
  
  // Make an element draggable
  _makeDraggable: function(element, handle) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    
    handle.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      // Get the mouse cursor position at startup
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      // Call a function whenever the cursor moves
      document.onmousemove = elementDrag;
    }
    
    function elementDrag(e) {
      e = e || window.event;
      e.preventDefault();
      // Calculate the new cursor position
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      // Set the element's new position
      element.style.top = (element.offsetTop - pos2) + "px";
      element.style.left = (element.offsetLeft - pos1) + "px";
      element.style.right = 'auto';
      element.style.bottom = 'auto';
    }
    
    function closeDragElement() {
      // Stop moving when mouse button is released
      document.onmouseup = null;
      document.onmousemove = null;
    }
  },
  
  // Resize the window to simulate a device
  _resizeWindow: function(width, height) {
    window.resizeTo(width, height);
  },
  
  // Update the size information
  _updateSizeInfo: function() {
    const currentSize = document.getElementById('current-size');
    const currentBreakpoint = document.getElementById('current-breakpoint');
    
    if (currentSize) {
      currentSize.textContent = `Current: ${window.innerWidth}px × ${window.innerHeight}px`;
    }
    
    if (currentBreakpoint) {
      currentBreakpoint.textContent = `Breakpoint: ${this._getCurrentBreakpoint()}`;
    }
  },
  
  // Get the current Tailwind breakpoint
  _getCurrentBreakpoint: function() {
    const width = window.innerWidth;
    
    if (width < 640) return 'xs';
    if (width < 768) return 'sm';
    if (width < 1024) return 'md';
    if (width < 1280) return 'lg';
    if (width < 1536) return 'xl';
    return '2xl';
  }
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  DeviceTesting.init();
});
