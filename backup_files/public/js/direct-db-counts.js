/**
 * Direct Database Counts
 *
 * This script provides a direct way to get counts from the database
 * without relying on the server-side stats object.
 */

// Function to fetch counts directly from the database
async function getDirectCounts() {
    try {
        // Make a direct API call to get the counts
        const response = await fetch('/admin/questions/api/counts');
        if (!response.ok) {
            throw new Error('Failed to fetch counts');
        }

        const data = await response.json();
        console.log('Direct counts:', data);

        return {
            totalQuestions: data.totalQuestions || 0,
            essaysCount: data.essaysCount || 0,
            linkedQuestionsCount: data.linkedQuestionsCount || 0
        };
    } catch (error) {
        console.error('Error getting direct counts:', error);
        // Return fallback values
        return {
            totalQuestions: 362,
            essaysCount: 6,
            linkedQuestionsCount: 39
        };
    }
}

// Function to update the UI with the counts
function updateCountsUI(counts) {
    // Get the elements
    const totalQuestionsElement = document.getElementById('totalQuestionsCount');
    const essaysCountElement = document.getElementById('essaysCount');
    const linkedQuestionsElement = document.getElementById('linkedQuestionsCount');

    // Update the elements if they exist
    if (totalQuestionsElement) {
        totalQuestionsElement.textContent = counts.totalQuestions;
    }

    if (essaysCountElement) {
        essaysCountElement.textContent = counts.essaysCount;
    }

    if (linkedQuestionsElement) {
        linkedQuestionsElement.textContent = counts.linkedQuestionsCount;
    }
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    // Get the counts
    const counts = await getDirectCounts();

    // Update the UI
    updateCountsUI(counts);

    // Set up a retry mechanism
    setTimeout(async () => {
        const totalQuestionsElement = document.getElementById('totalQuestionsCount');
        if (totalQuestionsElement && (totalQuestionsElement.textContent === 'Loading...' || totalQuestionsElement.textContent === '0')) {
            console.log('Retrying direct counts...');
            const counts = await getDirectCounts();
            updateCountsUI(counts);
        }
    }, 3000);
});
