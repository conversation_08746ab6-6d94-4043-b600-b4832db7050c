/**
 * Ensure Cha<PERSON>
 * This script ensures that the chat button is always visible at the bottom right of the screen
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only run if user is logged in
    if (!document.body.classList.contains('logged-in')) return;

    // Check if the chat button exists after a delay
    setTimeout(function() {
        const chatButton = document.getElementById('floatingChatButton');
        const fallbackButton = document.getElementById('fallbackChatButton');

        // If neither button exists, create one
        if (!chatButton && !fallbackButton) {
            console.log('Creating emergency chat button');
            const emergencyChatButton = document.createElement('div');
            emergencyChatButton.id = 'emergencyChatButton';
            emergencyChatButton.className = 'fixed bottom-6 right-6 bg-purple-600 text-white rounded-full w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center shadow-lg cursor-pointer hover:bg-purple-700 transition-all';
            emergencyChatButton.style.cssText = 'position: fixed !important; bottom: 20px !important; right: 20px !important; z-index: 99999 !important; background-color: #7c3aed !important;';
            emergencyChatButton.innerHTML = `
                <svg class="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            `;

            // Add click event to open chat
            emergencyChatButton.addEventListener('click', function() {
                const chatPopup = document.getElementById('chatPopup');
                if (chatPopup) {
                    if (chatPopup.classList.contains('hidden')) {
                        chatPopup.classList.remove('hidden');
                        chatPopup.classList.remove('scale-95', 'opacity-0');
                        chatPopup.classList.add('scale-100', 'opacity-100');
                    } else {
                        chatPopup.classList.add('scale-95', 'opacity-0');
                        setTimeout(() => {
                            chatPopup.classList.add('hidden');
                        }, 300);
                    }
                }
            });

            document.body.appendChild(emergencyChatButton);
        }

        // If the fallback button exists but is not visible, show it
        if (fallbackButton && window.getComputedStyle(fallbackButton).display === 'none') {
            fallbackButton.style.display = 'flex';
        }

        // If the main button exists but is not visible, show it
        if (chatButton && window.getComputedStyle(chatButton).display === 'none') {
            chatButton.style.display = 'flex';
        }
    }, 3000); // Check after 3 seconds
});
