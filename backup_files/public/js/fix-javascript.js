/**
 * JavaScript Fix
 * This script fixes common JavaScript issues across the application
 */

console.log('JavaScript Fix Script Loaded');

// Function to fix toggle password visibility
function fixPasswordToggle() {
    console.log('Fixing password toggle functionality');

    // Find all toggle password buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');

    if (toggleButtons.length === 0) {
        console.log('No toggle password buttons found');
        return;
    }

    console.log(`Found ${toggleButtons.length} toggle password buttons`);

    // Remove any existing event listeners (to avoid duplicates)
    toggleButtons.forEach(button => {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add new event listener
        newButton.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordField = document.getElementById(targetId);
            const eyeIcon = this.querySelector('.eye-icon');
            const eyeSlashIcon = this.querySelector('.eye-slash-icon');

            console.log(`Toggle password for ${targetId}`);

            if (passwordField) {
                // Toggle password visibility
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    if (eyeIcon) eyeIcon.classList.add('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.remove('hidden');
                } else {
                    passwordField.type = 'password';
                    if (eyeIcon) eyeIcon.classList.remove('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.add('hidden');
                }
            } else {
                console.error(`Password field ${targetId} not found`);
            }
        });
    });
}

// Function to fix form validation
function fixFormValidation() {
    console.log('Fixing form validation');

    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        console.log('Found login form, fixing validation');

        // Email validation
        const emailInput = document.getElementById('email');
        if (emailInput) {
            emailInput.addEventListener('input', function() {
                const email = this.value.trim();
                const emailFeedback = document.getElementById('email-feedback');

                if (!emailFeedback) return;

                // Simple email validation
                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
                if (!email) {
                    emailFeedback.textContent = '';
                    return;
                }

                if (!emailRegex.test(email)) {
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                } else {
                    emailFeedback.textContent = 'Valid email format';
                    emailFeedback.style.color = 'green';
                }
            });
        }

        // Form submission
        loginForm.addEventListener('submit', function(e) {
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            if (!emailInput || !passwordInput) return;

            const email = emailInput.value.trim();
            const password = passwordInput.value;
            let hasErrors = false;

            // Get or create feedback elements
            let emailFeedback = document.getElementById('email-feedback');
            if (!emailFeedback) {
                emailFeedback = document.createElement('div');
                emailFeedback.id = 'email-feedback';
                emailFeedback.className = 'mt-1 text-sm';
                emailFeedback.style.minHeight = '20px';
                emailInput.parentNode.appendChild(emailFeedback);
            }

            let passwordFeedback = document.getElementById('password-feedback');
            if (!passwordFeedback) {
                passwordFeedback = document.createElement('div');
                passwordFeedback.id = 'password-feedback';
                passwordFeedback.className = 'mt-1 text-sm';
                passwordFeedback.style.minHeight = '20px';
                passwordInput.parentNode.appendChild(passwordFeedback);
            }

            // Basic validation
            if (!email) {
                e.preventDefault();
                hasErrors = true;
                emailFeedback.textContent = 'Email is required';
                emailFeedback.style.color = 'red';
            }

            if (!password) {
                e.preventDefault();
                hasErrors = true;
                passwordFeedback.textContent = 'Password is required';
                passwordFeedback.style.color = 'red';
            }

            // Email format validation
            if (email) {
                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                }
            }

            if (!hasErrors) {
                // Disable button to prevent double submission
                const loginButton = document.getElementById('loginButton');
                if (loginButton) {
                    loginButton.disabled = true;
                    loginButton.textContent = 'Signing in...';
                }
            }
        });
    }

    // Registration form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        console.log('Found registration form, fixing validation');

        // Password strength validation
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm_password');

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;

                // Update strength bars
                const strengthBars = [
                    document.getElementById('strength-bar-1'),
                    document.getElementById('strength-bar-2'),
                    document.getElementById('strength-bar-3'),
                    document.getElementById('strength-bar-4')
                ];

                const strengthText = document.getElementById('password-strength-text');

                if (strengthBars[0] && strengthText) {
                    // Calculate strength
                    let score = 0;

                    // Length check
                    if (password.length >= 8) score += 1;
                    if (password.length >= 12) score += 1;

                    // Complexity checks
                    if (/[A-Z]/.test(password)) score += 1; // Has uppercase
                    if (/[a-z]/.test(password)) score += 1; // Has lowercase
                    if (/[0-9]/.test(password)) score += 1; // Has number
                    if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

                    // Normalize score to 0-4 range
                    const strength = Math.min(4, Math.floor(score / 1.5));

                    // Reset all bars
                    strengthBars.forEach(bar => {
                        if (bar) bar.className = 'h-1 w-1/4 bg-gray-200 rounded-sm';
                    });

                    // Set text based on strength
                    const strengthLabels = ['Very Weak', 'Weak', 'Medium', 'Strong', 'Very Strong'];
                    strengthText.textContent = strengthLabels[strength];

                    // Set colors based on strength
                    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];

                    // Update bars based on strength
                    for (let i = 0; i < strength; i++) {
                        if (strengthBars[i]) {
                            strengthBars[i].className = `h-1 w-1/4 ${colors[Math.min(i, colors.length - 1)]} rounded-sm`;
                        }
                    }
                }

                // Check password match
                if (confirmPasswordInput && confirmPasswordInput.value) {
                    const passwordMatchFeedback = document.getElementById('password-match-feedback');
                    if (passwordMatchFeedback) {
                        if (password === confirmPasswordInput.value) {
                            passwordMatchFeedback.textContent = 'Passwords match';
                            passwordMatchFeedback.style.color = 'green';
                        } else {
                            passwordMatchFeedback.textContent = 'Passwords do not match';
                            passwordMatchFeedback.style.color = 'red';
                        }
                    }
                }
            });
        }

        // Password match validation
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                const password = passwordInput ? passwordInput.value : '';
                const confirmPassword = this.value;
                const passwordMatchFeedback = document.getElementById('password-match-feedback');

                if (!passwordMatchFeedback) return;

                if (!confirmPassword) {
                    passwordMatchFeedback.textContent = '';
                    return;
                }

                if (password === confirmPassword) {
                    passwordMatchFeedback.textContent = 'Passwords match';
                    passwordMatchFeedback.style.color = 'green';
                } else {
                    passwordMatchFeedback.textContent = 'Passwords do not match';
                    passwordMatchFeedback.style.color = 'red';
                }
            });
        }

        // Form submission
        registerForm.addEventListener('submit', function(e) {
            const usernameInput = document.getElementById('username');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const dateOfBirthInput = document.getElementById('date_of_birth');

            if (!usernameInput || !emailInput || !passwordInput || !confirmPasswordInput || !dateOfBirthInput) return;

            const username = usernameInput.value.trim();
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const dateOfBirth = dateOfBirthInput.value;
            let hasErrors = false;

            // Get or create feedback elements
            let usernameFeedback = document.getElementById('username-feedback');
            if (!usernameFeedback) {
                usernameFeedback = document.createElement('div');
                usernameFeedback.id = 'username-feedback';
                usernameFeedback.className = 'mt-1 text-sm';
                usernameFeedback.style.minHeight = '20px';
                usernameInput.parentNode.appendChild(usernameFeedback);
            }

            let emailFeedback = document.getElementById('email-feedback');
            if (!emailFeedback) {
                emailFeedback = document.createElement('div');
                emailFeedback.id = 'email-feedback';
                emailFeedback.className = 'mt-1 text-sm';
                emailFeedback.style.minHeight = '20px';
                emailInput.parentNode.appendChild(emailFeedback);
            }

            let passwordFeedback = document.getElementById('password-feedback');
            if (!passwordFeedback) {
                passwordFeedback = document.createElement('div');
                passwordFeedback.id = 'password-feedback';
                passwordFeedback.className = 'mt-1 text-sm';
                passwordFeedback.style.minHeight = '20px';
                passwordInput.parentNode.appendChild(passwordFeedback);
            }

            let passwordMatchFeedback = document.getElementById('password-match-feedback');
            if (!passwordMatchFeedback) {
                passwordMatchFeedback = document.createElement('div');
                passwordMatchFeedback.id = 'password-match-feedback';
                passwordMatchFeedback.className = 'mt-1 text-sm';
                passwordMatchFeedback.style.minHeight = '20px';
                confirmPasswordInput.parentNode.appendChild(passwordMatchFeedback);
            }

            let dobFeedback = document.getElementById('dob-feedback');
            if (!dobFeedback) {
                dobFeedback = document.createElement('div');
                dobFeedback.id = 'dob-feedback';
                dobFeedback.className = 'mt-1 text-sm';
                dobFeedback.style.minHeight = '20px';
                dateOfBirthInput.parentNode.appendChild(dobFeedback);
            }

            // Basic validation
            if (!username) {
                e.preventDefault();
                hasErrors = true;
                usernameFeedback.textContent = 'Username is required';
                usernameFeedback.style.color = 'red';
            }

            if (!email) {
                e.preventDefault();
                hasErrors = true;
                emailFeedback.textContent = 'Email is required';
                emailFeedback.style.color = 'red';
            }

            if (!password) {
                e.preventDefault();
                hasErrors = true;
                passwordFeedback.textContent = 'Password is required';
                passwordFeedback.style.color = 'red';
            }

            if (!confirmPassword) {
                e.preventDefault();
                hasErrors = true;
                passwordMatchFeedback.textContent = 'Please confirm your password';
                passwordMatchFeedback.style.color = 'red';
            }

            if (!dateOfBirth) {
                e.preventDefault();
                hasErrors = true;
                dobFeedback.textContent = 'Date of birth is required';
                dobFeedback.style.color = 'red';
            }

            // Username validation
            if (username && username.length < 3) {
                e.preventDefault();
                hasErrors = true;
                usernameFeedback.textContent = 'Username must be at least 3 characters long';
                usernameFeedback.style.color = 'red';
            }

            // Email validation
            if (email) {
                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                }
            }

            // Password validation
            if (password && password.length < 8) {
                e.preventDefault();
                hasErrors = true;
                passwordFeedback.textContent = 'Password must be at least 8 characters long';
                passwordFeedback.style.color = 'red';
            }

            // Password match validation
            if (password && confirmPassword && password !== confirmPassword) {
                e.preventDefault();
                hasErrors = true;
                passwordMatchFeedback.textContent = 'Passwords do not match';
                passwordMatchFeedback.style.color = 'red';
            }

            // Date of birth validation
            if (dateOfBirth) {
                const today = new Date();
                const birthDate = new Date(dateOfBirth);
                const age = today.getFullYear() - birthDate.getFullYear();

                if (age < 13) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'You must be at least 13 years old to register';
                    dobFeedback.style.color = 'red';
                }
            }

            if (!hasErrors) {
                // Disable button to prevent double submission
                const registerButton = document.getElementById('registerButton');
                if (registerButton) {
                    registerButton.disabled = true;
                    registerButton.textContent = 'Registering...';
                }
            }
        });
    }
}

// Initialize fixes when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing JavaScript fixes');

    // Fix password toggle
    fixPasswordToggle();

    // Fix form validation
    fixFormValidation();

    // Add logged-in class to body if user is logged in
    const isLoggedIn = document.cookie.includes('userId=') ||
                      document.cookie.includes('token=') ||
                      document.body.getAttribute('data-user-id');

    if (isLoggedIn && !document.body.classList.contains('logged-in')) {
        console.log('Adding logged-in class to body');
        document.body.classList.add('logged-in');
    }
});

// Also run fixes after window load to catch any late changes
window.addEventListener('load', function() {
    console.log('Window loaded, running JavaScript fixes again');

    // Fix password toggle
    fixPasswordToggle();

    // Fix form validation
    fixFormValidation();
});
