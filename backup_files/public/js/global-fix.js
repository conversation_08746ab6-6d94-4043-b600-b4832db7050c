/**
 * Global JavaScript Fix
 * This script fixes common JavaScript issues across the application
 */

console.log('Global JavaScript Fix Loaded');

// Function to fix all JavaScript issues
function fixAllJavaScriptIssues() {
    console.log('Fixing all JavaScript issues');

    // Fix password toggle
    fixPasswordToggle();

    // Fix form validation
    fixFormValidation();

    // Fix chat icon
    fixChatIcon();

    // Add logged-in class to body if user is logged in
    addLoggedInClass();
}

// Function to fix password toggle
function fixPasswordToggle() {
    console.log('Fixing password toggle');

    // Find all toggle password buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');

    if (toggleButtons.length === 0) {
        console.log('No toggle password buttons found');
        return;
    }

    console.log(`Found ${toggleButtons.length} toggle password buttons`);

    // Add event listeners to toggle buttons
    toggleButtons.forEach(button => {
        // Remove existing event listeners
        const newButton = button.cloneNode(true);
        if (button.parentNode) {
            button.parentNode.replaceChild(newButton, button);
        }

        // Add new event listener
        newButton.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target') || 'password';
            const passwordField = document.getElementById(targetId);
            const eyeIcon = this.querySelector('.eye-icon');
            const eyeSlashIcon = this.querySelector('.eye-slash-icon');

            console.log(`Toggle password for ${targetId}`);

            if (passwordField) {
                // Toggle password visibility
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    if (eyeIcon) eyeIcon.classList.add('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.remove('hidden');
                } else {
                    passwordField.type = 'password';
                    if (eyeIcon) eyeIcon.classList.remove('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.add('hidden');
                }
            } else {
                console.error(`Password field ${targetId} not found`);
            }
        });
    });
}

// Function to fix form validation
function fixFormValidation() {
    console.log('Fixing form validation');

    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        console.log('Found login form, fixing validation');

        // Create feedback elements if they don't exist
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');

        if (emailInput) {
            let emailFeedback = document.getElementById('email-feedback');
            if (!emailFeedback) {
                emailFeedback = document.createElement('div');
                emailFeedback.id = 'email-feedback';
                emailFeedback.className = 'mt-1 text-sm';
                emailFeedback.style.minHeight = '20px';
                emailInput.parentNode.appendChild(emailFeedback);
            }

            // Email validation
            emailInput.addEventListener('input', function() {
                const email = this.value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (!email) {
                    emailFeedback.textContent = '';
                    return;
                }

                if (!emailRegex.test(email)) {
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                } else {
                    emailFeedback.textContent = 'Valid email format';
                    emailFeedback.style.color = 'green';
                }
            });
        }

        // Add form submission validation with inline feedback
        loginForm.addEventListener('submit', function(e) {
            let hasErrors = false;
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const emailFeedback = document.getElementById('email-feedback');

            if (!emailInput || !passwordInput) return;

            const email = emailInput.value.trim();
            const password = passwordInput.value;

            // Basic validation
            if (!email) {
                e.preventDefault();
                hasErrors = true;
                if (emailFeedback) {
                    emailFeedback.textContent = 'Email is required';
                    emailFeedback.style.color = 'red';
                }
            }

            if (!password) {
                e.preventDefault();
                hasErrors = true;
                // Create password feedback if it doesn't exist
                let passwordFeedback = document.getElementById('password-feedback');
                if (!passwordFeedback) {
                    passwordFeedback = document.createElement('div');
                    passwordFeedback.id = 'password-feedback';
                    passwordFeedback.className = 'mt-1 text-sm text-red-500';
                    passwordInput.parentNode.appendChild(passwordFeedback);
                }
                passwordFeedback.textContent = 'Password is required';
            }

            // Email format validation
            if (email && emailFeedback) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                }
            }

            if (hasErrors) {
                // Add a general error message at the top of the form
                let generalError = document.getElementById('general-error');
                if (!generalError) {
                    generalError = document.createElement('div');
                    generalError.id = 'general-error';
                    generalError.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                    loginForm.insertBefore(generalError, loginForm.firstChild);
                }
                generalError.textContent = 'Please correct the errors below';
            }
        });
    }

    // Skip registration form validation if register-validation.js is loaded
    // This will allow the password strength indicator to work properly
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        console.log('Found registration form, but not modifying it to allow register-validation.js to work');
        // Check if register-validation.js is loaded
        if (!window.registerValidationLoaded) {
            console.log('register-validation.js not loaded, loading it now');
            // Load register-validation.js dynamically
            const script = document.createElement('script');
            script.src = '/js/register-validation.js';
            document.head.appendChild(script);
        } else {
            console.log('register-validation.js already loaded');
        }
        // Do not add any validation here to avoid conflicts with register-validation.js
    }
}

// Function to fix chat icon
function fixChatIcon() {
    console.log('Fixing chat icon');

    // Only run if user is logged in
    if (!document.body.classList.contains('logged-in')) {
        console.log('User not logged in, not fixing chat icon');
        return;
    }

    // Check if any chat button already exists
    const existingChatButton = document.getElementById('chatIcon') ||
                              document.getElementById('floatingChatButton') ||
                              document.getElementById('emergencyChatButton') ||
                              document.getElementById('directChatButton') ||
                              document.getElementById('guaranteedChatButton');

    if (existingChatButton) {
        console.log('Existing chat button found:', existingChatButton.id);
        // Make sure it's visible
        existingChatButton.style.display = 'flex';
        existingChatButton.style.position = 'fixed';
        existingChatButton.style.bottom = '80px';
        existingChatButton.style.right = '20px';
        existingChatButton.style.zIndex = '99999';
        return;
    }

    console.log('No existing chat button found, creating new one');

    // Create a new chat button
    const chatButton = document.createElement('div');
    chatButton.id = 'guaranteedChatButton';
    chatButton.className = 'fixed bottom-20 right-6 bg-purple-600 text-white rounded-full w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center shadow-lg cursor-pointer z-[99999] hover:bg-purple-700 transition-all';
    chatButton.style.cssText = 'position: fixed !important; bottom: 80px !important; right: 20px !important; z-index: 99999 !important; background-color: #7c3aed !important; display: flex !important;';
    chatButton.innerHTML = `
        <svg class="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    `;

    // Add click event to open chat
    chatButton.addEventListener('click', function() {
        const chatPopup = document.getElementById('chatPopup');
        if (chatPopup) {
            if (chatPopup.classList.contains('hidden')) {
                chatPopup.classList.remove('hidden');
                chatPopup.classList.remove('scale-95', 'opacity-0');
                chatPopup.classList.add('scale-100', 'opacity-100');
            } else {
                chatPopup.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    chatPopup.classList.add('hidden');
                }, 300);
            }
        } else {
            console.log('Chat popup not found');
            // Redirect to chat page if popup doesn't exist
            window.location.href = '/chat';
        }
    });

    document.body.appendChild(chatButton);
    console.log('Guaranteed chat button created');
}

// Function to add logged-in class to body if user is logged in
function addLoggedInClass() {
    console.log('Adding logged-in class to body if user is logged in');

    // Check if user is logged in
    const isLoggedIn = document.cookie.includes('userId=') ||
                      document.cookie.includes('token=') ||
                      document.body.getAttribute('data-user-id');

    if (isLoggedIn && !document.body.classList.contains('logged-in')) {
        console.log('Adding logged-in class to body');
        document.body.classList.add('logged-in');
    }
}

// Run fixes when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, running JavaScript fixes');
    fixAllJavaScriptIssues();
});

// Also run fixes after window load to catch any late changes
window.addEventListener('load', function() {
    console.log('Window loaded, running JavaScript fixes again');
    fixAllJavaScriptIssues();
});

// Run fixes immediately if DOM is already loaded
if (document.readyState === 'interactive' || document.readyState === 'complete') {
    console.log('DOM already loaded, running JavaScript fixes immediately');
    fixAllJavaScriptIssues();
}
