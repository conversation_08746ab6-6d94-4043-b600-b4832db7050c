/**
 * Guaranteed Chat Icon
 * This script ensures that a chat icon is always visible for logged-in users
 * It uses a different approach that bypasses potential issues with other scripts
 */

// Immediately execute this script
(function() {
    console.log('Guaranteed Chat Icon Script Loaded');
    
    // Function to create the chat icon
    function createChatIcon() {
        console.log('Creating guaranteed chat icon');
        
        // Check if icon already exists
        if (document.getElementById('guaranteedChatIcon')) {
            console.log('Guaranteed chat icon already exists');
            return;
        }
        
        // Create the chat icon
        const chatIcon = document.createElement('div');
        chatIcon.id = 'guaranteedChatIcon';
        chatIcon.style.cssText = 'position: fixed !important; bottom: 80px !important; right: 20px !important; z-index: 9999999 !important; width: 50px !important; height: 50px !important; border-radius: 50% !important; background-color: #7c3aed !important; display: flex !important; align-items: center !important; justify-content: center !important; cursor: pointer !important; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important; transition: transform 0.3s ease !important;';
        chatIcon.innerHTML = `
            <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
        `;
        
        // Add hover effect
        chatIcon.addEventListener('mouseover', function() {
            this.style.transform = 'scale(1.1)';
        });
        
        chatIcon.addEventListener('mouseout', function() {
            this.style.transform = 'scale(1)';
        });
        
        // Add click event to open chat
        chatIcon.addEventListener('click', function() {
            const chatPopup = document.getElementById('chatPopup');
            if (chatPopup) {
                if (chatPopup.classList.contains('hidden')) {
                    chatPopup.classList.remove('hidden');
                    chatPopup.classList.remove('scale-95', 'opacity-0');
                    chatPopup.classList.add('scale-100', 'opacity-100');
                } else {
                    chatPopup.classList.add('scale-95', 'opacity-0');
                    setTimeout(() => {
                        chatPopup.classList.add('hidden');
                    }, 300);
                }
            } else {
                // If chatPopup doesn't exist, try to find other chat windows
                const chatWindow = document.getElementById('chatWindow');
                if (chatWindow) {
                    if (chatWindow.classList.contains('hidden')) {
                        chatWindow.classList.remove('hidden');
                        setTimeout(() => {
                            chatWindow.classList.remove('scale-95', 'opacity-0');
                            chatWindow.classList.add('scale-100', 'opacity-100');
                        }, 10);
                    } else {
                        chatWindow.classList.remove('scale-100', 'opacity-100');
                        chatWindow.classList.add('scale-95', 'opacity-0');
                        setTimeout(() => {
                            chatWindow.classList.add('hidden');
                        }, 300);
                    }
                } else {
                    // If no chat window exists, redirect to chat page
                    window.location.href = '/chat';
                }
            }
        });
        
        // Add to the body
        document.body.appendChild(chatIcon);
        console.log('Guaranteed chat icon created');
    }
    
    // Function to check if user is logged in
    function isUserLoggedIn() {
        return document.body.classList.contains('logged-in') || 
               document.body.getAttribute('data-user-id') || 
               document.cookie.includes('userId=');
    }
    
    // Create chat icon when DOM is loaded
    function initialize() {
        if (isUserLoggedIn()) {
            createChatIcon();
            
            // Set up periodic check to ensure the icon remains visible
            setInterval(function() {
                const chatIcon = document.getElementById('guaranteedChatIcon');
                if (!chatIcon) {
                    createChatIcon();
                }
            }, 5000);
        } else {
            console.log('User not logged in, chat icon not created');
        }
    }
    
    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // DOM already loaded, run immediately
        initialize();
    }
    
    // Also initialize after window load to catch any late changes
    window.addEventListener('load', initialize);
})();
