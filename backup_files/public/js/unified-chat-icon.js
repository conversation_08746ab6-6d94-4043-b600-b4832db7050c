/**
 * Unified Chat Icon
 * This script ensures that a chat icon is always visible for logged-in users
 * except when they are taking a test
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (!document.body.classList.contains('logged-in')) {
        console.log('User not logged in, chat icon not initialized');
        return;
    }

    // Don't show chat icon if user is in test mode
    if (document.body.classList.contains('in-test-mode')) {
        console.log('User in test mode, chat icon not initialized');
        return;
    }

    console.log('Initializing unified chat icon');

    // Check if any chat button already exists
    const existingChatButton = document.getElementById('chatIcon') || 
                              document.getElementById('floatingChatButton') || 
                              document.getElementById('emergencyChatButton');

    if (existingChatButton) {
        console.log('Existing chat button found:', existingChatButton.id);
        // Make sure it's visible
        existingChatButton.style.display = 'flex';
        existingChatButton.style.position = 'fixed';
        existingChatButton.style.bottom = '80px';
        existingChatButton.style.right = '20px';
        existingChatButton.style.zIndex = '99999';
        return;
    }

    console.log('No existing chat button found, creating new one');

    // Create a new chat button
    const chatButton = document.createElement('div');
    chatButton.id = 'unifiedChatButton';
    chatButton.className = 'fixed bottom-20 right-6 bg-purple-600 text-white rounded-full w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center shadow-lg cursor-pointer z-[99999] hover:bg-purple-700 transition-all';
    chatButton.style.cssText = 'position: fixed !important; bottom: 80px !important; right: 20px !important; z-index: 99999 !important; background-color: #7c3aed !important; display: flex !important;';
    chatButton.innerHTML = `
        <svg class="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    `;

    // Add click event to open chat
    chatButton.addEventListener('click', function() {
        const chatPopup = document.getElementById('chatPopup');
        if (chatPopup) {
            if (chatPopup.classList.contains('hidden')) {
                chatPopup.classList.remove('hidden');
                chatPopup.classList.remove('scale-95', 'opacity-0');
                chatPopup.classList.add('scale-100', 'opacity-100');
            } else {
                chatPopup.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    chatPopup.classList.add('hidden');
                }, 300);
            }
        } else {
            console.log('Chat popup not found');
            // Redirect to chat page if popup doesn't exist
            window.location.href = '/chat';
        }
    });

    document.body.appendChild(chatButton);
    console.log('Unified chat button created');

    // Set up a periodic check to ensure the button remains visible
    setInterval(function() {
        const button = document.getElementById('unifiedChatButton');
        if (button && window.getComputedStyle(button).display === 'none') {
            console.log('Chat button was hidden, making it visible again');
            button.style.display = 'flex';
        }
    }, 5000);
});
