const db = require('./config/database');

async function checkClassSectionsTable() {
  try {
    console.log('Checking if class sections table exists...');
    
    // Check if there's a table named 'class_sections'
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'class_sections'
    `);
    
    if (tables.length > 0) {
      console.log('class_sections table exists');
      
      // Get columns from class_sections table
      const [columns] = await db.query(`
        SHOW COLUMNS FROM class_sections
      `);
      
      console.log('\nclass_sections table columns:');
      columns.forEach(column => {
        console.log(`- ${column.Field} (${column.Type})`);
      });
      
      // Get a sample row from class_sections table
      const [rows] = await db.query(`
        SELECT * FROM class_sections LIMIT 1
      `);
      
      if (rows.length > 0) {
        console.log('\nSample row from class_sections table:');
        console.log(rows[0]);
      } else {
        console.log('\nNo rows found in class_sections table');
      }
    } else {
      console.log('class_sections table does not exist');
      
      // Check if there's another table for sections
      const [otherTables] = await db.query(`
        SHOW TABLES LIKE '%section%'
      `);
      
      console.log('\nTables with "section" in their name:');
      otherTables.forEach(table => {
        console.log(`- ${Object.values(table)[0]}`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkClassSectionsTable();
