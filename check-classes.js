const db = require('./config/database');

async function checkClasses() {
  try {
    // Check if classes table exists
    const [classesTable] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'exam_prep_platform' 
      AND TABLE_NAME = 'classes'
    `);
    
    console.log('Classes table exists:', classesTable.length > 0);
    
    // Check if there's a class_sections table
    const [classSectionsTable] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'exam_prep_platform' 
      AND TABLE_NAME = 'class_sections'
    `);
    
    console.log('Class sections table exists:', classSectionsTable.length > 0);
    
    // Check if there's a trades table
    const [tradesTable] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'exam_prep_platform' 
      AND TABLE_NAME = 'trades'
    `);
    
    console.log('Trades table exists:', tradesTable.length > 0);
    
    // Check tables that reference classes
    const [teacherLecturesColumns] = await db.query(`
      SHOW COLUMNS FROM teacher_lectures LIKE 'class_name'
    `);
    
    console.log('Teacher lectures has class_name column:', teacherLecturesColumns.length > 0);
    
    // Check if users table has class_id or similar
    const [userClassColumns] = await db.query(`
      SHOW COLUMNS FROM users WHERE Field LIKE '%class%'
    `);
    
    console.log('User class columns:', userClassColumns);
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking classes:', error);
    process.exit(1);
  }
}

checkClasses();
