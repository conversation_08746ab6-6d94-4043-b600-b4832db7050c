const db = require('./config/database');

async function checkComputerTeacher() {
  try {
    // First, find the computer teacher with ID 1
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email 
      FROM users 
      WHERE id = 1 AND role = 'teacher'
    `);
    
    if (teacher.length === 0) {
      console.log('Computer teacher with ID 1 not found');
      return;
    }
    
    console.log('COMPUTER TEACHER DETAILS:');
    console.log('========================');
    console.log(`ID: ${teacher[0].id}`);
    console.log(`Name: ${teacher[0].full_name}`);
    console.log(`Username: ${teacher[0].username}`);
    console.log(`Email: ${teacher[0].email}`);
    console.log('========================\n');
    
    // Check assigned classes through teacher_classes table
    console.log('ASSIGNED CLASSES:');
    console.log('========================');
    
    const [classes] = await db.query(`
      SELECT c.id, c.name, c.grade, c.trade, c.section, cl.room_number, cl.session
      FROM teacher_classes tc
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      WHERE tc.teacher_id = 1
    `);
    
    if (classes.length === 0) {
      console.log('No classes assigned through teacher_classes table');
    } else {
      console.table(classes);
    }
    
    // Check assigned subjects
    console.log('\nASSIGNED SUBJECTS:');
    console.log('========================');
    
    const [subjects] = await db.query(`
      SELECT s.id, s.name, s.code, s.description
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = 1
    `);
    
    if (subjects.length === 0) {
      console.log('No subjects assigned through teacher_subjects table');
    } else {
      console.table(subjects);
    }
    
    // Check class-subject combinations (what subjects are taught to which classes)
    console.log('\nCLASS-SUBJECT COMBINATIONS:');
    console.log('========================');
    
    const [classSubjects] = await db.query(`
      SELECT 
        c.name AS class_name, 
        c.grade, 
        c.trade, 
        c.section,
        s.name AS subject_name,
        s.code AS subject_code,
        IFNULL(sca.num_theory_lectures, 0) AS theory_lectures,
        IFNULL(sca.num_practical_lectures, 0) AS practical_lectures
      FROM teacher_classes tc
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      JOIN teacher_subjects ts ON ts.teacher_id = tc.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      LEFT JOIN subject_class_assignment sca ON (sca.subject_id = s.id AND sca.class_id = c.id)
      WHERE tc.teacher_id = 1
    `);
    
    if (classSubjects.length === 0) {
      console.log('No class-subject combinations found');
    } else {
      console.table(classSubjects);
    }
    
    // Check scheduled lectures
    console.log('\nSCHEDULED LECTURES:');
    console.log('========================');
    
    const [lectures] = await db.query(`
      SELECT 
        ls.day_of_week,
        ls.start_time,
        ls.end_time,
        ls.classroom,
        c.name AS class_name,
        c.grade,
        c.trade,
        c.section,
        s.name AS subject_name
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = 1
      ORDER BY 
        CASE 
          WHEN ls.day_of_week = 'Monday' THEN 1
          WHEN ls.day_of_week = 'Tuesday' THEN 2
          WHEN ls.day_of_week = 'Wednesday' THEN 3
          WHEN ls.day_of_week = 'Thursday' THEN 4
          WHEN ls.day_of_week = 'Friday' THEN 5
          WHEN ls.day_of_week = 'Saturday' THEN 6
          ELSE 7
        END,
        ls.start_time
    `);
    
    if (lectures.length === 0) {
      console.log('No scheduled lectures found');
    } else {
      console.table(lectures);
    }
    
    // Check practicals
    console.log('\nPRACTICAL SESSIONS:');
    console.log('========================');
    
    const [practicals] = await db.query(`
      SELECT 
        p.date,
        p.start_time,
        p.end_time,
        c.name AS class_name,
        c.grade,
        c.trade,
        c.section,
        s.name AS subject_name,
        p.status
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      WHERE p.teacher_id = 1
      ORDER BY p.date, p.start_time
    `);
    
    if (practicals.length === 0) {
      console.log('No practical sessions found');
    } else {
      console.table(practicals);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
checkComputerTeacher();
