const db = require('./config/database');

async function checkCSTeacherDetails() {
  try {
    const teacherId = 103; // CS Teacher ID
    
    console.log(`Checking detailed information for CS Teacher (ID: ${teacherId})`);
    console.log('=======================================================');
    
    // Get teacher details
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email, role, date_of_birth, profile_image, bio
      FROM users
      WHERE id = ? AND role = 'teacher'
    `, [teacherId]);
    
    if (teacher.length === 0) {
      console.log(`Teacher with ID ${teacherId} not found`);
      return;
    }
    
    console.log('TEACHER DETAILS:');
    console.log('--------------');
    console.log(`ID: ${teacher[0].id}`);
    console.log(`Name: ${teacher[0].full_name}`);
    console.log(`Username: ${teacher[0].username}`);
    console.log(`Email: ${teacher[0].email}`);
    console.log(`Role: ${teacher[0].role}`);
    console.log(`Date of Birth: ${teacher[0].date_of_birth || 'Not specified'}`);
    console.log(`Bio: ${teacher[0].bio || 'Not specified'}`);
    console.log('--------------\n');
    
    // Get subjects taught
    console.log('SUBJECTS TAUGHT:');
    console.log('--------------');
    
    const [subjects] = await db.query(`
      SELECT s.id, s.name, s.code, s.description
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = ?
    `, [teacherId]);
    
    if (subjects.length === 0) {
      console.log('No subjects assigned');
    } else {
      console.table(subjects);
    }
    
    // Get classes taught with detailed information
    console.log('\nCLASSES TAUGHT:');
    console.log('--------------');
    
    const [classes] = await db.query(`
      SELECT 
        c.id AS class_id, 
        c.name AS class_name, 
        c.grade, 
        c.trade, 
        c.section,
        cl.id AS classroom_id,
        cl.room_number,
        cl.session,
        cl.max_capacity
      FROM teacher_classes tc
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);
    
    if (classes.length === 0) {
      console.log('No classes assigned');
    } else {
      console.table(classes);
    }
    
    // Get detailed class-subject combinations
    console.log('\nCLASS-SUBJECT COMBINATIONS:');
    console.log('--------------');
    
    const [classSubjects] = await db.query(`
      SELECT 
        c.id AS class_id,
        c.name AS class_name, 
        c.grade, 
        c.trade, 
        c.section,
        s.id AS subject_id,
        s.name AS subject_name,
        s.code AS subject_code,
        IFNULL(sca.num_theory_lectures, 0) AS theory_lectures,
        IFNULL(sca.num_practical_lectures, 0) AS practical_lectures,
        IFNULL(sca.class_subject_teacher_ratio, '1:1') AS teacher_ratio
      FROM teacher_classes tc
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      JOIN teacher_subjects ts ON ts.teacher_id = tc.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      LEFT JOIN subject_class_assignment sca ON (sca.subject_id = s.id AND sca.class_id = c.id)
      WHERE tc.teacher_id = ?
      ORDER BY c.grade, c.trade, c.section, s.name
    `, [teacherId]);
    
    if (classSubjects.length === 0) {
      console.log('No class-subject combinations found');
    } else {
      console.table(classSubjects);
    }
    
    // Get lecture schedule
    console.log('\nLECTURE SCHEDULE:');
    console.log('--------------');
    
    const [lectures] = await db.query(`
      SELECT 
        ls.id,
        ls.day_of_week,
        ls.start_time,
        ls.end_time,
        ls.classroom,
        c.name AS class_name,
        c.grade,
        c.trade,
        c.section,
        s.name AS subject_name,
        s.code AS subject_code
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = ?
      ORDER BY 
        CASE 
          WHEN ls.day_of_week = 'Monday' THEN 1
          WHEN ls.day_of_week = 'Tuesday' THEN 2
          WHEN ls.day_of_week = 'Wednesday' THEN 3
          WHEN ls.day_of_week = 'Thursday' THEN 4
          WHEN ls.day_of_week = 'Friday' THEN 5
          WHEN ls.day_of_week = 'Saturday' THEN 6
          ELSE 7
        END,
        ls.start_time
    `, [teacherId]);
    
    if (lectures.length === 0) {
      console.log('No lecture schedule found');
    } else {
      console.table(lectures);
    }
    
    // Get practical sessions
    console.log('\nPRACTICAL SESSIONS:');
    console.log('--------------');
    
    const [practicals] = await db.query(`
      SELECT 
        p.id,
        p.date,
        p.start_time,
        p.end_time,
        c.name AS class_name,
        c.grade,
        c.trade,
        c.section,
        s.name AS subject_name,
        s.code AS subject_code,
        p.status,
        l.name AS lab_name
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN labs l ON p.lab_id = l.id
      WHERE p.teacher_id = ?
      ORDER BY p.date, p.start_time
    `, [teacherId]);
    
    if (practicals.length === 0) {
      console.log('No practical sessions found');
    } else {
      console.table(practicals);
    }
    
    // Get student count in each class
    console.log('\nSTUDENT COUNT IN EACH CLASS:');
    console.log('--------------');
    
    const [studentCounts] = await db.query(`
      SELECT 
        c.id AS class_id,
        c.name AS class_name, 
        c.grade, 
        c.trade, 
        c.section,
        COUNT(sc.student_id) AS total_students,
        SUM(CASE WHEN u.gender = 'male' THEN 1 ELSE 0 END) AS male_students,
        SUM(CASE WHEN u.gender = 'female' THEN 1 ELSE 0 END) AS female_students
      FROM teacher_classes tc
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      LEFT JOIN student_classes sc ON c.id = sc.class_id
      LEFT JOIN users u ON sc.student_id = u.id
      WHERE tc.teacher_id = ?
      GROUP BY c.id, c.name, c.grade, c.trade, c.section
    `, [teacherId]);
    
    if (studentCounts.length === 0) {
      console.log('No student count information found');
    } else {
      console.table(studentCounts);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
checkCSTeacherDetails();
