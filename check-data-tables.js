const db = require('./config/database');

async function checkData() {
  try {
    console.log('=== Checking Data in Key Tables ===\n');

    // Check users table
    console.log('=== Users Table ===');
    const [users] = await db.query(`
      SELECT id, username, name, email, role 
      FROM users 
      WHERE role IN ('teacher', 'student', 'admin')
      ORDER BY role, id
      LIMIT 20
    `);
    
    console.log(`Found ${users.length} users`);
    console.log('Sample users:');
    users.forEach(user => {
      console.log(`  ${user.id}: ${user.username} (${user.name}) - ${user.email} - ${user.role}`);
    });
    
    // Get teacher count
    const [teacherCount] = await db.query(`
      SELECT COUNT(*) as count FROM users WHERE role = 'teacher'
    `);
    console.log(`\nTotal teachers: ${teacherCount[0].count}`);
    
    // Get student count
    const [studentCount] = await db.query(`
      SELECT COUNT(*) as count FROM users WHERE role = 'student'
    `);
    console.log(`Total students: ${studentCount[0].count}`);
    
    // Check classes table
    console.log('\n=== Classes Table ===');
    const [classes] = await db.query(`
      SELECT id, name, grade, trade, section, academic_year
      FROM classes
      ORDER BY grade, trade, section
      LIMIT 20
    `);
    
    console.log(`Found ${classes.length} classes`);
    console.log('Sample classes:');
    classes.forEach(cls => {
      console.log(`  ${cls.id}: ${cls.name} (Grade ${cls.grade} ${cls.trade} ${cls.section}) - ${cls.academic_year}`);
    });
    
    // Check classrooms table
    console.log('\n=== Classrooms Table ===');
    const [classrooms] = await db.query(`
      SELECT cr.id, cr.room_number, cr.session, c.name as class_name, cr.section, t.name as trade_name
      FROM classrooms cr
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      ORDER BY cr.id
      LIMIT 20
    `);
    
    console.log(`Found ${classrooms.length} classrooms`);
    console.log('Sample classrooms:');
    classrooms.forEach(cr => {
      console.log(`  ${cr.id}: Room ${cr.room_number} - ${cr.class_name} ${cr.section} (${cr.trade_name}) - ${cr.session}`);
    });
    
    // Check teacher_classes table
    console.log('\n=== Teacher Classes Table ===');
    const [teacherClasses] = await db.query(`
      SELECT tc.id, tc.teacher_id, u.name as teacher_name, cr.room_number, c.name as class_name
      FROM teacher_classes tc
      JOIN users u ON tc.teacher_id = u.id
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      ORDER BY tc.teacher_id, tc.id
      LIMIT 20
    `);
    
    console.log(`Found ${teacherClasses.length} teacher-class assignments`);
    console.log('Sample teacher-class assignments:');
    teacherClasses.forEach(tc => {
      console.log(`  ${tc.id}: Teacher ${tc.teacher_id} (${tc.teacher_name}) - Room ${tc.room_number} - ${tc.class_name}`);
    });
    
    // Check student_classrooms table
    console.log('\n=== Student Classrooms Table ===');
    const [studentClassrooms] = await db.query(`
      SELECT sc.id, sc.student_id, u.name as student_name, cr.room_number, c.name as class_name
      FROM student_classrooms sc
      JOIN users u ON sc.student_id = u.id
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      ORDER BY sc.student_id, sc.id
      LIMIT 20
    `);
    
    console.log(`Found ${studentClassrooms.length} student-classroom assignments`);
    console.log('Sample student-classroom assignments:');
    studentClassrooms.forEach(sc => {
      console.log(`  ${sc.id}: Student ${sc.student_id} (${sc.student_name}) - Room ${sc.room_number} - ${sc.class_name}`);
    });
    
    // Check class_incharge table
    console.log('\n=== Class Incharge Table ===');
    const [classIncharge] = await db.query(`
      SELECT ci.id, ci.class_id, c.name as class_name, ci.teacher_id, u.name as teacher_name
      FROM class_incharge ci
      JOIN classes c ON ci.class_id = c.id
      JOIN users u ON ci.teacher_id = u.id
      ORDER BY ci.class_id
      LIMIT 20
    `);
    
    console.log(`Found ${classIncharge.length} class incharge assignments`);
    console.log('Sample class incharge assignments:');
    classIncharge.forEach(ci => {
      console.log(`  ${ci.id}: Class ${ci.class_id} (${ci.class_name}) - Teacher ${ci.teacher_id} (${ci.teacher_name})`);
    });
    
    // Check if there's a demo teacher
    console.log('\n=== Demo Teacher Check ===');
    const [demoTeacher] = await db.query(`
      SELECT id, username, name, email 
      FROM users 
      WHERE role = 'teacher' AND (email = '<EMAIL>' OR username = 'csteacher')
      LIMIT 1
    `);
    
    if (demoTeacher.length > 0) {
      console.log(`Found demo teacher: ${demoTeacher[0].id} - ${demoTeacher[0].username} (${demoTeacher[0].name})`);
      
      // Check classes assigned to demo teacher
      const [demoTeacherClasses] = await db.query(`
        SELECT tc.id, cr.room_number, c.name as class_name, c.grade, c.trade, c.section
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        WHERE tc.teacher_id = ?
        ORDER BY c.grade, c.trade, c.section
      `, [demoTeacher[0].id]);
      
      console.log(`Demo teacher has ${demoTeacherClasses.length} assigned classes:`);
      demoTeacherClasses.forEach(tc => {
        console.log(`  Room ${tc.room_number} - ${tc.class_name} (Grade ${tc.grade} ${tc.trade} ${tc.section})`);
      });
    } else {
      console.log('Demo teacher not found');
    }
    
    // Check if there's a demo student
    console.log('\n=== Demo Student Check ===');
    const [demoStudent] = await db.query(`
      SELECT id, username, name, email 
      FROM users 
      WHERE role = 'student' AND (email = '<EMAIL>' OR username = 'csstudent')
      LIMIT 1
    `);
    
    if (demoStudent.length > 0) {
      console.log(`Found demo student: ${demoStudent[0].id} - ${demoStudent[0].username} (${demoStudent[0].name})`);
      
      // Check classroom assigned to demo student
      const [demoStudentClassroom] = await db.query(`
        SELECT sc.id, cr.room_number, c.name as class_name, c.grade, c.trade, c.section
        FROM student_classrooms sc
        JOIN classrooms cr ON sc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        WHERE sc.student_id = ?
        ORDER BY c.grade, c.trade, c.section
      `, [demoStudent[0].id]);
      
      console.log(`Demo student has ${demoStudentClassroom.length} assigned classrooms:`);
      demoStudentClassroom.forEach(sc => {
        console.log(`  Room ${sc.room_number} - ${sc.class_name} (Grade ${sc.grade} ${sc.trade} ${sc.section})`);
      });
    } else {
      console.log('Demo student not found');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking data:', error);
    process.exit(1);
  }
}

checkData();
