const db = require('./config/database');

async function checkData() {
  try {
    // Check teacher_lectures data
    const [lectures] = await db.query(`SELECT COUNT(*) as count FROM teacher_lectures`);
    console.log('Teacher lectures count:', lectures[0].count);
    
    // Check teacher_syllabus data
    const [syllabus] = await db.query(`SELECT COUNT(*) as count FROM teacher_syllabus`);
    console.log('Teacher syllabus count:', syllabus[0].count);
    
    // Check teacher_practicals data
    const [practicals] = await db.query(`SELECT COUNT(*) as count FROM teacher_practicals`);
    console.log('Teacher practicals count:', practicals[0].count);
    
    // Check syllabus_progress data
    const [progress] = await db.query(`SELECT COUNT(*) as count FROM syllabus_progress`);
    console.log('Syllabus progress count:', progress[0].count);
    
    // Check student_practical_records data
    const [records] = await db.query(`SELECT COUNT(*) as count FROM student_practical_records`);
    console.log('Student practical records count:', records[0].count);
    
    // Check if there are any teachers in the system
    const [teachers] = await db.query(`SELECT COUNT(*) as count FROM users WHERE role = 'teacher'`);
    console.log('Teachers count:', teachers[0].count);
    
    if (teachers[0].count > 0) {
      // Get teacher IDs
      const [teacherIds] = await db.query(`SELECT id FROM users WHERE role = 'teacher'`);
      console.log('Teacher IDs:', teacherIds.map(t => t.id));
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking data:', error);
    process.exit(1);
  }
}

checkData();
