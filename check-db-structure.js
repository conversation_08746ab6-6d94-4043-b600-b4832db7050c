/**
 * <PERSON><PERSON><PERSON> to check database structure and assign classes to a teacher
 */

const db = require('./config/database');

async function checkDatabaseStructure() {
  try {
    console.log('Checking database structure...');
    
    // Get all tables
    const [tables] = await db.query(`
      SHOW TABLES
    `);
    
    console.log('Tables in database:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`- ${tableName}`);
    });
    
    // Check if teacher_classes table exists
    const teacherClassesExists = tables.some(table => 
      Object.values(table)[0] === 'teacher_classes'
    );
    
    if (teacherClassesExists) {
      console.log('\nChecking teacher_classes table structure:');
      const [columns] = await db.query(`
        DESCRIBE teacher_classes
      `);
      
      columns.forEach(column => {
        console.log(`- ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : ''} ${column.Key}`);
      });
      
      // Check if classrooms table exists
      const classroomsExists = tables.some(table => 
        Object.values(table)[0] === 'classrooms'
      );
      
      if (classroomsExists) {
        console.log('\nChecking classrooms table structure:');
        const [classroomColumns] = await db.query(`
          DESCRIBE classrooms
        `);
        
        classroomColumns.forEach(column => {
          console.log(`- ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : ''} ${column.Key}`);
        });
        
        // Get sample data from classrooms
        const [classrooms] = await db.query(`
          SELECT * FROM classrooms LIMIT 5
        `);
        
        console.log('\nSample classrooms:');
        classrooms.forEach(classroom => {
          console.log(classroom);
        });
      }
      
      // Check if teacher_subjects table exists
      const teacherSubjectsExists = tables.some(table => 
        Object.values(table)[0] === 'teacher_subjects'
      );
      
      if (teacherSubjectsExists) {
        console.log('\nChecking teacher_subjects table structure:');
        const [columns] = await db.query(`
          DESCRIBE teacher_subjects
        `);
        
        columns.forEach(column => {
          console.log(`- ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : ''} ${column.Key}`);
        });
      }
      
      // Check if users table exists and get teacher information
      const usersExists = tables.some(table => 
        Object.values(table)[0] === 'users'
      );
      
      if (usersExists) {
        console.log('\nFinding teacher users:');
        const [teachers] = await db.query(`
          SELECT id, name, email, role FROM users WHERE role = 'teacher' LIMIT 5
        `);
        
        if (teachers.length > 0) {
          console.log('Teachers found:');
          teachers.forEach(teacher => {
            console.log(`- ID: ${teacher.id}, Name: ${teacher.name}, Email: ${teacher.email}`);
          });
        } else {
          console.log('No teachers found in the users table');
        }
      }
    } else {
      console.log('teacher_classes table does not exist');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking database structure:', error);
    process.exit(1);
  }
}

// Run the function
checkDatabaseStructure();
