const mysql = require('mysql2/promise');

async function checkDatabase() {
  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'exam_prep_platform'
    });

    console.log('Connected to database');

    // Check inventory_items table structure
    const [columns] = await connection.query('DESCRIBE inventory_items');
    console.log('Inventory Items Table Structure:');
    columns.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check if there are any items in the inventory
    const [items] = await connection.query('SELECT * FROM inventory_items LIMIT 5');
    console.log(`\nFound ${items.length} inventory items`);
    
    if (items.length > 0) {
      console.log('Sample item:');
      console.log(items[0]);
    }

    // Close connection
    await connection.end();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkDatabase();
