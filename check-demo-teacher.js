/**
 * <PERSON><PERSON>t to check demo teacher account and reset password if needed
 */

const db = require('./config/database');
const bcrypt = require('bcrypt');

async function checkDemoTeacher() {
  try {
    console.log('Checking demo teacher account...');
    
    // Check if demo teacher exists
    const [demoTeacher] = await db.query(`
      SELECT id, name, username, email, role FROM users 
      WHERE email = '<EMAIL>' OR username = 'csstudent'
    `);
    
    if (demoTeacher.length > 0) {
      console.log('Demo teacher found:');
      console.log(`ID: ${demoTeacher[0].id}`);
      console.log(`Name: ${demoTeacher[0].name}`);
      console.log(`Username: ${demoTeacher[0].username}`);
      console.log(`Email: ${demoTeacher[0].email}`);
      console.log(`Role: ${demoTeacher[0].role}`);
      
      // Reset password to a known value
      const password = 'Merit123#';
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      
      await db.query(`
        UPDATE users SET password = ? WHERE id = ?
      `, [hashedPassword, demoTeacher[0].id]);
      
      console.log('\nPassword reset to: Merit123#');
      
      // Check assigned classes
      const [classes] = await db.query(`
        SELECT tc.id, cr.id as classroom_id, c.grade, t.name as trade, cr.section, r.room_number
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN rooms r ON cr.room_id = r.id
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        WHERE tc.teacher_id = ?
      `, [demoTeacher[0].id]);
      
      console.log('\nAssigned classes:');
      if (classes.length > 0) {
        classes.forEach(cls => {
          console.log(`- Grade ${cls.grade} ${cls.trade} ${cls.section} (Room ${cls.room_number})`);
        });
      } else {
        console.log('No classes assigned to demo teacher');
      }
      
      // Check assigned subjects
      const [subjects] = await db.query(`
        SELECT ts.id, s.name, s.code
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        WHERE ts.teacher_id = ?
      `, [demoTeacher[0].id]);
      
      console.log('\nAssigned subjects:');
      if (subjects.length > 0) {
        subjects.forEach(subj => {
          console.log(`- ${subj.name} (${subj.code || 'No code'})`);
        });
      } else {
        console.log('No subjects assigned to demo teacher');
      }
      
      console.log('\nLogin credentials:');
      console.log(`Username: ${demoTeacher[0].username}`);
      console.log(`Email: ${demoTeacher[0].email}`);
      console.log(`Password: Merit123#`);
    } else {
      console.log('Demo teacher not found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking demo teacher:', error);
    process.exit(1);
  }
}

// Run the function
checkDemoTeacher();
