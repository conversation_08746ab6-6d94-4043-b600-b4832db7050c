const db = require('./config/database');

async function checkFillUpQuestions() {
    try {
        console.log('Checking for fill-up questions in the database...');
        
        // Check if there are any fill-up questions
        const [questionTypes] = await db.query(`
            SELECT question_type, COUNT(*) as count 
            FROM questions 
            GROUP BY question_type
        `);
        
        console.log('Question types in the database:');
        questionTypes.forEach(type => {
            console.log(`${type.question_type}: ${type.count}`);
        });
        
        // Check if fill_up is a valid question type in the database schema
        const [columns] = await db.query(`
            SHOW COLUMNS FROM questions WHERE Field = 'question_type'
        `);
        
        if (columns.length > 0) {
            console.log('\nQuestion type column definition:');
            console.log(columns[0].Type);
        }
        
        // Create a sample fill-up question if none exist
        const fillUpExists = questionTypes.some(type => type.question_type === 'fill_up');
        
        if (!fillUpExists) {
            console.log('\nNo fill-up questions found. Creating a sample fill-up question...');
            
            // Get a section ID to use
            const [sections] = await db.query(`
                SELECT section_id FROM sections LIMIT 1
            `);
            
            if (sections.length > 0) {
                const sectionId = sections[0].section_id;
                
                // Insert a sample fill-up question
                const [result] = await db.query(`
                    INSERT INTO questions (
                        section_id, 
                        question_type, 
                        question_text, 
                        correct_answer, 
                        solution_text, 
                        marks
                    ) VALUES (?, ?, ?, ?, ?, ?)
                `, [
                    sectionId,
                    'fill_up',
                    'The capital of France is _______.',
                    'Paris',
                    'Paris is the capital and most populous city of France.',
                    1
                ]);
                
                console.log(`Created sample fill-up question with ID: ${result.insertId}`);
            } else {
                console.log('No sections found to create a sample question.');
            }
        }
        
        process.exit(0);
    } catch (error) {
        console.error('Error checking fill-up questions:', error);
        process.exit(1);
    }
}

// Run the function
checkFillUpQuestions();
