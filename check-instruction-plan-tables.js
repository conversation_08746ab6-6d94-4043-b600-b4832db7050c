/**
 * <PERSON><PERSON><PERSON> to check if instruction plan related tables exist in the database
 * and create them if they don't exist
 */

const db = require('./config/database');

async function checkInstructionPlanTables() {
  try {
    console.log('Checking instruction plan related tables...');
    
    // Get all tables in the database
    const [allTables] = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
    `);
    
    const tableNames = allTables.map(t => t.TABLE_NAME);
    console.log('Existing tables:', tableNames.join(', '));
    
    // Check if instruction_plans table exists
    const hasInstructionPlans = tableNames.includes('instruction_plans');
    console.log('instruction_plans table exists:', hasInstructionPlans);
    
    // Check if instruction_plan_views table exists
    const hasInstructionPlanViews = tableNames.includes('instruction_plan_views');
    console.log('instruction_plan_views table exists:', hasInstructionPlanViews);
    
    // Check if instruction_plan_completions table exists
    const hasInstructionPlanCompletions = tableNames.includes('instruction_plan_completions');
    console.log('instruction_plan_completions table exists:', hasInstructionPlanCompletions);
    
    // Check if instruction_plan_resources table exists
    const hasInstructionPlanResources = tableNames.includes('instruction_plan_resources');
    console.log('instruction_plan_resources table exists:', hasInstructionPlanResources);
    
    // Create instruction_plans table if it doesn't exist
    if (!hasInstructionPlans) {
      console.log('Creating instruction_plans table...');
      await db.query(`
        CREATE TABLE instruction_plans (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          subject_id INT,
          class_id INT,
          teacher_id INT,
          status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
        )
      `);
      console.log('instruction_plans table created successfully');
    }
    
    // Create instruction_plan_views table if it doesn't exist
    if (!hasInstructionPlanViews) {
      console.log('Creating instruction_plan_views table...');
      await db.query(`
        CREATE TABLE instruction_plan_views (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          student_id INT NOT NULL,
          viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE KEY (plan_id, student_id),
          FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      console.log('instruction_plan_views table created successfully');
    }
    
    // Create instruction_plan_completions table if it doesn't exist
    if (!hasInstructionPlanCompletions) {
      console.log('Creating instruction_plan_completions table...');
      await db.query(`
        CREATE TABLE instruction_plan_completions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          student_id INT NOT NULL,
          completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE KEY (plan_id, student_id),
          FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      console.log('instruction_plan_completions table created successfully');
    }
    
    // Create instruction_plan_resources table if it doesn't exist
    if (!hasInstructionPlanResources) {
      console.log('Creating instruction_plan_resources table...');
      await db.query(`
        CREATE TABLE instruction_plan_resources (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_id INT NOT NULL,
          resource_name VARCHAR(255) NOT NULL,
          resource_type ENUM('file', 'link', 'text') NOT NULL DEFAULT 'file',
          resource_path VARCHAR(255),
          resource_content TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
        )
      `);
      console.log('instruction_plan_resources table created successfully');
    }
    
    console.log('All instruction plan tables have been checked and created if needed.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
checkInstructionPlanTables();
