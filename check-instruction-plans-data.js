const db = require('./config/database');

async function checkInstructionPlansData() {
  try {
    console.log('Checking instruction_plans data...');
    
    // Get all records from instruction_plans table
    const [plans] = await db.query(`
      SELECT * FROM instruction_plans
    `);
    
    console.log(`Found ${plans.length} instruction plans`);
    
    if (plans.length > 0) {
      console.log('\nFirst 5 instruction plans:');
      plans.slice(0, 5).forEach(plan => {
        console.log(`ID: ${plan.id}, Title: ${plan.title}, Subject ID: ${plan.subject_id}, Teacher ID: ${plan.teacher_id}, Status: ${plan.status}`);
      });
    } else {
      console.log('No instruction plans found in the database');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkInstructionPlansData();
