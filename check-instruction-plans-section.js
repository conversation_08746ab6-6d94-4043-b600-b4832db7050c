const db = require('./config/database');

async function checkInstructionPlansSection() {
  try {
    console.log('Checking if instruction_plans has class_id, trade_id, or section_id columns...');
    
    // Get columns from instruction_plans table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM instruction_plans WHERE Field IN ('class_id', 'trade_id', 'section_id')
    `);
    
    if (columns.length > 0) {
      console.log('Found columns:');
      columns.forEach(column => {
        console.log(`- ${column.Field} (${column.Type})`);
      });
    } else {
      console.log('No class_id, trade_id, or section_id columns found in instruction_plans table');
    }
    
    // Check if there's a different version of the instruction_plans table
    console.log('\nChecking for tables with "instruction" in their name:');
    const [tables] = await db.query(`
      SHOW TABLES LIKE '%instruction%'
    `);
    
    tables.forEach(table => {
      console.log(`- ${Object.values(table)[0]}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkInstructionPlansSection();
