const db = require('./config/database');

async function checkInstructionPlansTable() {
  try {
    console.log('Checking instruction_plans table structure...');
    
    // Get columns from instruction_plans table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM instruction_plans
    `);
    
    console.log('instruction_plans table columns:');
    columns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type})`);
    });
    
    // Get a sample row from instruction_plans table
    const [rows] = await db.query(`
      SELECT * FROM instruction_plans LIMIT 1
    `);
    
    if (rows.length > 0) {
      console.log('\nSample row from instruction_plans table:');
      console.log(rows[0]);
    } else {
      console.log('\nNo rows found in instruction_plans table');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkInstructionPlansTable();
