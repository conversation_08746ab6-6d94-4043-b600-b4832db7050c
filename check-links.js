const puppeteer = require('puppeteer');

async function checkLinks() {
  console.log('Starting link checker...');

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });

  const page = await browser.newPage();

  try {
    // Login as admin
    console.log('Logging in as admin...');
    await page.goto('http://localhost:3018/login', { timeout: 60000 });
    await page.type('input[name="email"]', '<EMAIL>');
    await page.type('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');

    try {
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 60000 });
    } catch (error) {
      console.log('Navigation timeout, but continuing...');
    }

    // Check if login was successful
    const url = page.url();
    if (url.includes('/login')) {
      console.error('Login failed! Check credentials.');
      return;
    }

    console.log('Login successful, current URL:', url);

    // Navigate to teacher dashboard
    console.log('Navigating to teacher dashboard...');
    await page.goto('http://localhost:3018/teacher/dashboard', { timeout: 60000, waitUntil: 'networkidle0' });

    // Check if we're on the teacher dashboard
    const dashboardUrl = page.url();
    console.log('Current URL after navigation:', dashboardUrl);

    // Check all teacher pages manually
    const teacherPages = [
      { path: '/teacher/dashboard', name: 'Dashboard' },
      { path: '/teacher/timetable', name: 'Timetable' },
      { path: '/teacher/lectures', name: 'Lectures' },
      { path: '/teacher/syllabus', name: 'Syllabus' },
      { path: '/teacher/practicals', name: 'Practicals' },
      { path: '/teacher/practical-records', name: 'Practical Records' },
      { path: '/teacher/reports', name: 'Reports' }
    ];

    // Visit each page and check for errors
    for (const page_info of teacherPages) {
      console.log(`Checking page: ${page_info.name} (${page_info.path})`);

      try {
        await page.goto(`http://localhost:3018${page_info.path}`, { waitUntil: 'networkidle0', timeout: 60000 });

        // Check for error messages on the page
        const errorElements = await page.$$('div.error, .text-red-500, .bg-red-100');
        if (errorElements.length > 0) {
          console.error(`Found ${errorElements.length} error elements on page: ${page_info.path}`);
        } else {
          console.log(`✅ Page loaded successfully: ${page_info.path}`);
        }

        // Check for buttons on the page
        const buttons = await page.$$eval('button, a.btn, .btn, a.button, .button, a.px-3', btns =>
          btns.map(btn => ({
            text: btn.innerText.trim(),
            type: btn.tagName.toLowerCase(),
            href: btn.getAttribute('href'),
            id: btn.id,
            classes: btn.className
          }))
        );

        console.log(`Found ${buttons.length} buttons/links on page ${page_info.path}:`, buttons);

        // Check for forms on the page
        const forms = await page.$$eval('form', forms =>
          forms.map(form => ({
            id: form.id,
            action: form.getAttribute('action'),
            method: form.getAttribute('method')
          }))
        );

        console.log(`Found ${forms.length} forms on page ${page_info.path}:`, forms);

        // Check for select elements on the page
        const selects = await page.$$eval('select', selects =>
          selects.map(select => ({
            id: select.id,
            name: select.getAttribute('name'),
            options: Array.from(select.options).length
          }))
        );

        console.log(`Found ${selects.length} select elements on page ${page_info.path}:`, selects);
      } catch (error) {
        console.error(`Error navigating to ${page_info.path}:`, error.message);
      }
    }

    console.log('Link check completed!');
  } catch (error) {
    console.error('Error during link check:', error);
  } finally {
    await browser.close();
  }
}

checkLinks().catch(console.error);
