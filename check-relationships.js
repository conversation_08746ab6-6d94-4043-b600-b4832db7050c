const db = require('./config/database');

async function checkRelationships() {
  try {
    console.log('=== Checking Table Relationships ===\n');

    // Tables to check
    const tables = [
      'teacher_classes',
      'teacher_subjects',
      'student_classrooms',
      'student_subjects',
      'class_incharge',
      'classrooms',
      'subject_class_assignment'
    ];

    for (const tableName of tables) {
      try {
        // Check if table exists
        const [exists] = await db.query(`
          SELECT COUNT(*) as table_exists 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ?
        `, [tableName]);
        
        if (exists[0].table_exists > 0) {
          console.log(`\n=== ${tableName} Relationships ===`);
          
          // Get foreign keys
          const [foreignKeys] = await db.query(`
            SELECT 
              COLUMN_NAME, 
              REFERENCED_TABLE_NAME, 
              REFERENCED_COLUMN_NAME
            FROM
              INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE
              TABLE_SCHEMA = DATABASE() AND
              TABLE_NAME = ? AND
              REFERENCED_TABLE_NAME IS NOT NULL
          `, [tableName]);
          
          if (foreignKeys.length > 0) {
            console.log('Foreign Keys:');
            foreignKeys.forEach(fk => {
              console.log(`  ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`);
            });
            
            // Check sample data for each foreign key
            for (const fk of foreignKeys) {
              try {
                const [sampleData] = await db.query(`
                  SELECT t.${fk.COLUMN_NAME}, r.${fk.REFERENCED_COLUMN_NAME}
                  FROM ${tableName} t
                  LEFT JOIN ${fk.REFERENCED_TABLE_NAME} r ON t.${fk.COLUMN_NAME} = r.${fk.REFERENCED_COLUMN_NAME}
                  LIMIT 5
                `);
                
                console.log(`\n  Sample data for ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}:`);
                if (sampleData.length > 0) {
                  sampleData.forEach((row, index) => {
                    const fkValue = row[fk.COLUMN_NAME];
                    const refValue = row[fk.REFERENCED_COLUMN_NAME];
                    const status = refValue ? '✅' : '❌';
                    console.log(`    ${status} ${fk.COLUMN_NAME}=${fkValue} -> ${refValue || 'NULL'}`);
                  });
                } else {
                  console.log('    No data found');
                }
              } catch (error) {
                console.log(`    Error checking sample data: ${error.message}`);
              }
            }
          } else {
            console.log('No foreign keys defined');
          }
          
          // Check if this table is referenced by other tables
          const [referencedBy] = await db.query(`
            SELECT 
              TABLE_NAME, 
              COLUMN_NAME
            FROM
              INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE
              TABLE_SCHEMA = DATABASE() AND
              REFERENCED_TABLE_NAME = ?
          `, [tableName]);
          
          if (referencedBy.length > 0) {
            console.log('\nReferenced by:');
            referencedBy.forEach(ref => {
              console.log(`  ${ref.TABLE_NAME}.${ref.COLUMN_NAME}`);
            });
          } else {
            console.log('\nNot referenced by any table');
          }
        } else {
          console.log(`\n❌ ${tableName} does not exist`);
        }
      } catch (error) {
        console.log(`\n❌ Error checking ${tableName}: ${error.message}`);
      }
    }

    // Check specific queries for teacher and student views
    console.log('\n\n=== Testing Teacher Classes Query ===');
    try {
      const [teacherClasses] = await db.query(`
        SELECT 
          c.id AS class_id,
          c.name AS class_name,
          c.grade,
          c.trade,
          c.section,
          c.academic_year AS session,
          cr.room_number AS classroom_number,
          s.id AS subject_id,
          s.name AS subject_name,
          sca.num_theory_lectures,
          sca.num_practical_lectures,
          (sca.num_theory_lectures + sca.num_practical_lectures) AS total_lectures,
          ci.teacher_id AS incharge_teacher_id,
          u.name AS incharge_teacher_name
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        JOIN subject_class_assignment sca ON c.id = sca.class_id
        JOIN subjects s ON sca.subject_id = s.id
        LEFT JOIN class_incharge ci ON c.id = ci.class_id
        LEFT JOIN users u ON ci.teacher_id = u.id
        WHERE tc.teacher_id = 11
        LIMIT 5
      `);
      
      console.log(`Query returned ${teacherClasses.length} rows`);
      if (teacherClasses.length > 0) {
        console.log('Sample row:');
        console.log(JSON.stringify(teacherClasses[0], null, 2));
      }
    } catch (error) {
      console.log(`Error executing teacher classes query: ${error.message}`);
    }
    
    console.log('\n=== Testing Student Classes Query ===');
    try {
      const [studentClasses] = await db.query(`
        SELECT 
          c.id AS class_id,
          c.name AS class_name,
          c.grade,
          c.trade,
          c.section,
          c.academic_year AS session,
          cr.room_number AS classroom_number,
          s.id AS subject_id,
          s.name AS subject_name
        FROM student_classrooms sc
        JOIN classrooms cr ON sc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        JOIN student_subjects ss ON sc.student_id = ss.student_id
        JOIN subjects s ON ss.subject_id = s.id
        WHERE sc.student_id = 27
        LIMIT 5
      `);
      
      console.log(`Query returned ${studentClasses.length} rows`);
      if (studentClasses.length > 0) {
        console.log('Sample row:');
        console.log(JSON.stringify(studentClasses[0], null, 2));
      }
    } catch (error) {
      console.log(`Error executing student classes query: ${error.message}`);
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking relationships:', error);
    process.exit(1);
  }
}

checkRelationships();
