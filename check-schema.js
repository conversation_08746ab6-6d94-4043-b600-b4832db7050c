const db = require('./config/database');

async function checkSchema() {
  try {
    console.log('=== Database Configuration ===');
    console.log('Host:', process.env.DB_HOST || 'localhost');
    console.log('User:', process.env.DB_USER || 'root');
    console.log('Database:', process.env.DB_NAME || 'exam_prep_platform');
    console.log('Port:', process.env.DB_PORT || '3306');
    console.log('===========================\n');

    // Get all tables
    const [tables] = await db.query('SHOW TABLES');
    console.log('Tables in database:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`- ${tableName}`);
    });
    console.log('\n');

    // Check teacher-related tables
    console.log('=== Teacher-Related Tables ===');
    const teacherTables = [
      'teacher_classes',
      'teacher_subjects',
      'teacher_lectures',
      'teacher_practicals',
      'class_incharge'
    ];

    for (const tableName of teacherTables) {
      try {
        const [exists] = await db.query(`
          SELECT COUNT(*) as table_exists 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ?
        `, [tableName]);
        
        if (exists[0].table_exists > 0) {
          console.log(`✅ ${tableName} exists`);
          
          // Get table structure
          const [columns] = await db.query(`DESCRIBE ${tableName}`);
          console.log(`   Columns: ${columns.map(col => col.Field).join(', ')}`);
          
          // Get row count
          const [count] = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   Rows: ${count[0].count}`);
        } else {
          console.log(`❌ ${tableName} does not exist`);
        }
      } catch (error) {
        console.log(`❌ Error checking ${tableName}: ${error.message}`);
      }
    }
    console.log('\n');

    // Check student-related tables
    console.log('=== Student-Related Tables ===');
    const studentTables = [
      'student_classrooms',
      'student_subjects',
      'student_assignments',
      'student_test_attempts'
    ];

    for (const tableName of studentTables) {
      try {
        const [exists] = await db.query(`
          SELECT COUNT(*) as table_exists 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ?
        `, [tableName]);
        
        if (exists[0].table_exists > 0) {
          console.log(`✅ ${tableName} exists`);
          
          // Get table structure
          const [columns] = await db.query(`DESCRIBE ${tableName}`);
          console.log(`   Columns: ${columns.map(col => col.Field).join(', ')}`);
          
          // Get row count
          const [count] = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   Rows: ${count[0].count}`);
        } else {
          console.log(`❌ ${tableName} does not exist`);
        }
      } catch (error) {
        console.log(`❌ Error checking ${tableName}: ${error.message}`);
      }
    }
    console.log('\n');

    // Check core tables
    console.log('=== Core Tables ===');
    const coreTables = [
      'users',
      'classes',
      'subjects',
      'classrooms',
      'trades'
    ];

    for (const tableName of coreTables) {
      try {
        const [exists] = await db.query(`
          SELECT COUNT(*) as table_exists 
          FROM information_schema.tables 
          WHERE table_schema = DATABASE() 
          AND table_name = ?
        `, [tableName]);
        
        if (exists[0].table_exists > 0) {
          console.log(`✅ ${tableName} exists`);
          
          // Get table structure
          const [columns] = await db.query(`DESCRIBE ${tableName}`);
          console.log(`   Columns: ${columns.map(col => col.Field).join(', ')}`);
          
          // Get row count
          const [count] = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   Rows: ${count[0].count}`);
        } else {
          console.log(`❌ ${tableName} does not exist`);
        }
      } catch (error) {
        console.log(`❌ Error checking ${tableName}: ${error.message}`);
      }
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking schema:', error);
    process.exit(1);
  }
}

checkSchema();
