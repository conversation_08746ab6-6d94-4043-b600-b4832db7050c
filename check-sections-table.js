const db = require('./config/database');

async function checkSectionsTable() {
  try {
    console.log('Checking sections table structure...');
    
    // Get columns from sections table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM sections
    `);
    
    console.log('Sections table columns:');
    columns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type})`);
    });
    
    // Get a sample row from sections table
    const [rows] = await db.query(`
      SELECT * FROM sections LIMIT 1
    `);
    
    if (rows.length > 0) {
      console.log('\nSample row from sections table:');
      console.log(rows[0]);
    } else {
      console.log('\nNo rows found in sections table');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkSectionsTable();
