/**
 * <PERSON><PERSON><PERSON> to check if the server is running
 */

const http = require('http');

// Function to check if a server is running on a specific port
function checkServerStatus(port) {
    return new Promise((resolve, reject) => {
        const req = http.get(`http://localhost:${port}`, (res) => {
            console.log(`Server is running on port ${port}`);
            console.log(`Status code: ${res.statusCode}`);
            resolve(true);
        });
        
        req.on('error', (err) => {
            console.error(`Server is not running on port ${port}: ${err.message}`);
            resolve(false);
        });
        
        req.setTimeout(3000, () => {
            req.abort();
            console.error(`Connection to port ${port} timed out`);
            resolve(false);
        });
    });
}

// Check server status on port 3018
async function main() {
    try {
        const isRunning = await checkServerStatus(3018);
        console.log(`Server status on port 3018: ${isRunning ? 'Running' : 'Not running'}`);
        
        if (!isRunning) {
            console.log('Suggestions:');
            console.log('1. Make sure the server is started with "node index.js"');
            console.log('2. Check server logs for any errors');
            console.log('3. Ensure port 3018 is not being used by another application');
        }
    } catch (error) {
        console.error('Error checking server status:', error);
    }
}

main();
