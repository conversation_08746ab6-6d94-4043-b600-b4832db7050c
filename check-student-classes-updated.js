/**
 * <PERSON><PERSON><PERSON> to check student class assignments with the updated structure
 */

const db = require('./config/database');

async function checkStudentClasses() {
  try {
    console.log('Checking student class assignments with updated structure...');
    
    // Check if student_classrooms table exists
    const [studentClassroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'student_classrooms'
    `);
    
    if (studentClassroomsExists[0].table_exists === 0) {
      console.log('student_classrooms table does not exist');
      return;
    }
    
    // Get classes with trade information
    const [classes] = await db.query(`
      SELECT 
        c.id,
        c.grade,
        c.section,
        c.session,
        t.name AS trade_name,
        t.code AS trade_code,
        r.room_number
      FROM classes c
      LEFT JOIN trades t ON c.trade_id = t.id
      LEFT JOIN rooms r ON c.classroom_id = r.id
      ORDER BY c.grade, t.name, c.section
    `);
    
    console.log(`Found ${classes.length} classes with the updated structure:`);
    classes.forEach(cls => {
      console.log(`- ID: ${cls.id}, Grade: ${cls.grade}, Trade: ${cls.trade_name} (${cls.trade_code}), Section: ${cls.section}, Room: ${cls.room_number || 'Not assigned'}, Session: ${cls.session}`);
    });
    
    // Get student-classroom assignments
    const [assignments] = await db.query(`
      SELECT 
        u.id AS student_id, 
        u.name AS student_name,
        u.email AS student_email,
        c.id AS class_id,
        c.grade,
        t.name AS trade_name,
        c.section,
        c.session,
        r.room_number
      FROM student_classrooms sc
      JOIN users u ON sc.student_id = u.id
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      LEFT JOIN trades t ON c.trade_id = t.id
      LEFT JOIN rooms r ON c.classroom_id = r.id
      WHERE u.role = 'student'
    `);
    
    console.log(`\nFound ${assignments.length} student-classroom assignments`);
    
    if (assignments.length > 0) {
      // Group assignments by class
      const classCounts = {};
      
      assignments.forEach(assignment => {
        const className = `${assignment.grade} ${assignment.trade_name} ${assignment.section}`;
        
        if (!classCounts[className]) {
          classCounts[className] = 0;
        }
        
        classCounts[className]++;
      });
      
      console.log('\nStudents per class:');
      Object.entries(classCounts).forEach(([className, count]) => {
        console.log(`- ${className}: ${count} students`);
      });
      
      // Check if there are any students in "12 NON-MEDICAL A" class
      const nonMedicalAStudents = assignments.filter(a => 
        a.grade === '12' && a.trade_name === 'NON-MEDICAL' && a.section === 'A'
      );
      
      if (nonMedicalAStudents.length > 0) {
        console.log(`\nFound ${nonMedicalAStudents.length} students in 12 NON-MEDICAL A class:`);
        nonMedicalAStudents.slice(0, 5).forEach(student => {
          console.log(`- ID: ${student.student_id}, Name: ${student.student_name}, Email: ${student.student_email}`);
        });
        
        if (nonMedicalAStudents.length > 5) {
          console.log(`  ... and ${nonMedicalAStudents.length - 5} more`);
        }
      } else {
        console.log('\nNo students found in 12 NON-MEDICAL A class');
      }
      
      // Check if the demo student exists
      const [demoStudent] = await db.query(`
        SELECT id FROM users WHERE email = '<EMAIL>'
      `);
      
      if (demoStudent.length > 0) {
        console.log('\nFound demo student: <EMAIL>');
        
        // Check if the demo student is assigned to a class
        const [demoStudentClass] = await db.query(`
          SELECT 
            c.id AS class_id,
            c.grade,
            t.name AS trade_name,
            c.section,
            c.session,
            r.room_number
          FROM student_classrooms sc
          JOIN classrooms cr ON sc.classroom_id = cr.id
          JOIN classes c ON cr.class_id = c.id
          LEFT JOIN trades t ON c.trade_id = t.id
          LEFT JOIN rooms r ON c.classroom_id = r.id
          WHERE sc.student_id = ?
        `, [demoStudent[0].id]);
        
        if (demoStudentClass.length > 0) {
          console.log(`Demo student is assigned to class: ${demoStudentClass[0].grade} ${demoStudentClass[0].trade_name} ${demoStudentClass[0].section} (${demoStudentClass[0].room_number || 'No room'}, Session: ${demoStudentClass[0].session})`);
        } else {
          console.log('Demo student is not assigned to any class');
          
          // Find the 12 NON-MEDICAL A class
          const nonMedicalAClass = classes.find(c => 
            c.grade === '12' && c.trade_name === 'NON-MEDICAL' && c.section === 'A'
          );
          
          if (nonMedicalAClass) {
            console.log(`Found 12 NON-MEDICAL A class (ID: ${nonMedicalAClass.id})`);
            
            // Find the classroom for this class
            const [classroom] = await db.query(`
              SELECT id FROM classrooms WHERE class_id = ?
            `, [nonMedicalAClass.id]);
            
            if (classroom.length > 0) {
              // Assign the demo student to 12 NON-MEDICAL A
              await db.query(`
                INSERT INTO student_classrooms (student_id, classroom_id)
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE classroom_id = VALUES(classroom_id)
              `, [demoStudent[0].id, classroom[0].id]);
              
              console.log(`Assigned demo student to 12 NON-MEDICAL A class (Classroom ID: ${classroom[0].id})`);
            } else {
              console.log('Could not find classroom for 12 NON-MEDICAL A class');
            }
          } else {
            console.log('Could not find 12 NON-MEDICAL A class');
          }
        }
      } else {
        console.log('\nDemo student not found: <EMAIL>');
      }
    } else {
      console.log('No student-classroom assignments found');
    }
    
    console.log('\nCheck completed');
    process.exit(0);
  } catch (error) {
    console.error('Error checking student classes:', error);
    process.exit(1);
  }
}

// Run the check
checkStudentClasses();
