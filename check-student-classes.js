/**
 * <PERSON><PERSON><PERSON> to check student class assignments
 * This script checks if students are assigned to classes like "12 NON-MEDICAL A"
 */

const db = require('./config/database');

async function checkStudentClasses() {
  try {
    console.log('Checking student class assignments...');
    
    // Check if student_classrooms table exists
    const [studentClassroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'student_classrooms'
    `);
    
    if (studentClassroomsExists[0].table_exists === 0) {
      console.log('student_classrooms table does not exist');
      
      // Check if there's another table that might contain student-class assignments
      const [tables] = await db.query(`
        SHOW TABLES LIKE '%student%'
      `);
      
      if (tables.length > 0) {
        console.log('Found tables that might contain student data:');
        tables.forEach(table => {
          console.log(`- ${Object.values(table)[0]}`);
        });
      } else {
        console.log('No tables found that might contain student data');
      }
      
      // Check if users table has a role column to identify students
      const [usersRoleExists] = await db.query(`
        SELECT COUNT(*) as column_exists
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'users'
        AND column_name = 'role'
      `);
      
      if (usersRoleExists[0].column_exists > 0) {
        // Count students in users table
        const [studentCount] = await db.query(`
          SELECT COUNT(*) as count
          FROM users
          WHERE role = 'student'
        `);
        
        console.log(`Found ${studentCount[0].count} students in users table`);
      }
      
      // Check classes table
      const [classesExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'classes'
      `);
      
      if (classesExists[0].table_exists > 0) {
        // Get all classes
        const [classes] = await db.query(`
          SELECT * FROM classes
        `);
        
        console.log(`Found ${classes.length} classes in classes table:`);
        classes.forEach(cls => {
          console.log(`- ID: ${cls.id}, Name: ${cls.name || `${cls.grade} ${cls.trade} ${cls.section}`}`);
        });
        
        // Check if there are any classes like "12 NON-MEDICAL A"
        const nonMedicalClasses = classes.filter(cls => 
          (cls.name && cls.name.includes('NON-MEDICAL')) || 
          (cls.trade && cls.trade.includes('NON-MEDICAL'))
        );
        
        if (nonMedicalClasses.length > 0) {
          console.log(`\nFound ${nonMedicalClasses.length} NON-MEDICAL classes:`);
          nonMedicalClasses.forEach(cls => {
            console.log(`- ID: ${cls.id}, Name: ${cls.name || `${cls.grade} ${cls.trade} ${cls.section}`}`);
          });
        } else {
          console.log('\nNo NON-MEDICAL classes found');
        }
      } else {
        console.log('classes table does not exist');
      }
      
      // Create sample data for student-class assignments
      console.log('\nCreating sample data for student-class assignments...');
      
      // Check if classrooms table exists
      const [classroomsExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'classrooms'
      `);
      
      if (classroomsExists[0].table_exists === 0) {
        console.log('classrooms table does not exist, creating it...');
        
        // Create classrooms table
        await db.query(`
          CREATE TABLE IF NOT EXISTS classrooms (
            id INT PRIMARY KEY AUTO_INCREMENT,
            class_id INT NOT NULL,
            room_number VARCHAR(20),
            capacity INT DEFAULT 40,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
          )
        `);
        
        console.log('classrooms table created successfully');
      }
      
      // Create student_classrooms table if it doesn't exist
      await db.query(`
        CREATE TABLE IF NOT EXISTS student_classrooms (
          id INT PRIMARY KEY AUTO_INCREMENT,
          student_id INT NOT NULL,
          classroom_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
          UNIQUE KEY unique_student_classroom (student_id, classroom_id)
        )
      `);
      
      console.log('student_classrooms table created successfully');
      
      // Create sample classes if none exist
      if (classesExists[0].table_exists === 0 || classes.length === 0) {
        console.log('Creating sample classes...');
        
        // Create classes table if it doesn't exist
        await db.query(`
          CREATE TABLE IF NOT EXISTS classes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100),
            grade VARCHAR(10),
            trade VARCHAR(50),
            section VARCHAR(10),
            academic_year VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          )
        `);
        
        // Insert sample classes
        await db.query(`
          INSERT INTO classes (name, grade, trade, section, academic_year)
          VALUES
            ('12 NON-MEDICAL A', '12', 'NON-MEDICAL', 'A', '2023-2024'),
            ('12 NON-MEDICAL B', '12', 'NON-MEDICAL', 'B', '2023-2024'),
            ('12 NON-MEDICAL E', '12', 'NON-MEDICAL', 'E', '2023-2024'),
            ('12 MEDICAL A', '12', 'MEDICAL', 'A', '2023-2024'),
            ('12 COMMERCE A', '12', 'COMMERCE', 'A', '2023-2024'),
            ('12 COMMERCE B', '12', 'COMMERCE', 'B', '2023-2024')
        `);
        
        console.log('Sample classes created successfully');
      }
      
      // Create sample classrooms if none exist
      const [classroomsCount] = await db.query(`
        SELECT COUNT(*) as count FROM classrooms
      `);
      
      if (classroomsCount[0].count === 0) {
        console.log('Creating sample classrooms...');
        
        // Get class IDs
        const [classes] = await db.query(`
          SELECT id FROM classes
        `);
        
        // Insert sample classrooms
        for (let i = 0; i < classes.length; i++) {
          await db.query(`
            INSERT INTO classrooms (class_id, room_number)
            VALUES (?, ?)
          `, [classes[i].id, `Room ${200 + i}`]);
        }
        
        console.log('Sample classrooms created successfully');
      }
      
      // Create sample student-class assignments
      console.log('Creating sample student-class assignments...');
      
      // Get student IDs
      const [students] = await db.query(`
        SELECT id FROM users WHERE role = 'student' LIMIT 10
      `);
      
      // Get classroom IDs
      const [classrooms] = await db.query(`
        SELECT id, class_id FROM classrooms
      `);
      
      // Assign students to classrooms
      if (students.length > 0 && classrooms.length > 0) {
        for (let i = 0; i < students.length; i++) {
          const classroomIndex = i % classrooms.length;
          
          try {
            await db.query(`
              INSERT INTO student_classrooms (student_id, classroom_id)
              VALUES (?, ?)
              ON DUPLICATE KEY UPDATE classroom_id = VALUES(classroom_id)
            `, [students[i].id, classrooms[classroomIndex].id]);
          } catch (error) {
            console.error(`Error assigning student ${students[i].id} to classroom ${classrooms[classroomIndex].id}:`, error.message);
          }
        }
        
        console.log(`Assigned ${students.length} students to classrooms`);
      } else {
        console.log('No students or classrooms found to create assignments');
      }
    } else {
      // student_classrooms table exists, check assignments
      console.log('student_classrooms table exists, checking assignments...');
      
      // Get student-classroom assignments
      const [assignments] = await db.query(`
        SELECT 
          u.id AS student_id, 
          u.name AS student_name,
          c.id AS class_id,
          c.name AS class_name,
          c.grade,
          c.trade,
          c.section,
          cr.room_number
        FROM student_classrooms sc
        JOIN users u ON sc.student_id = u.id
        JOIN classrooms cr ON sc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        WHERE u.role = 'student'
      `);
      
      console.log(`Found ${assignments.length} student-classroom assignments`);
      
      if (assignments.length > 0) {
        // Group assignments by class
        const classCounts = {};
        
        assignments.forEach(assignment => {
          const className = assignment.class_name || `${assignment.grade} ${assignment.trade} ${assignment.section}`;
          
          if (!classCounts[className]) {
            classCounts[className] = 0;
          }
          
          classCounts[className]++;
        });
        
        console.log('\nStudents per class:');
        Object.entries(classCounts).forEach(([className, count]) => {
          console.log(`- ${className}: ${count} students`);
        });
        
        // Check if there are any students in "12 NON-MEDICAL A" class
        const nonMedicalAStudents = assignments.filter(a => 
          (a.class_name && a.class_name.includes('12 NON-MEDICAL A')) || 
          (a.grade === '12' && a.trade === 'NON-MEDICAL' && a.section === 'A')
        );
        
        if (nonMedicalAStudents.length > 0) {
          console.log(`\nFound ${nonMedicalAStudents.length} students in 12 NON-MEDICAL A class:`);
          nonMedicalAStudents.slice(0, 5).forEach(student => {
            console.log(`- ID: ${student.student_id}, Name: ${student.student_name}`);
          });
          
          if (nonMedicalAStudents.length > 5) {
            console.log(`  ... and ${nonMedicalAStudents.length - 5} more`);
          }
        } else {
          console.log('\nNo students found in 12 NON-MEDICAL A class');
        }
      } else {
        console.log('No student-classroom assignments found');
      }
    }
    
    console.log('\nCheck completed');
    process.exit(0);
  } catch (error) {
    console.error('Error checking student classes:', error);
    process.exit(1);
  }
}

// Run the check
checkStudentClasses();
