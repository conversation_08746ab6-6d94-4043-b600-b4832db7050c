const db = require('./config/database');

async function checkStudentRecords() {
  try {
    // Get the structure of the student_practical_records table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM student_practical_records
    `);
    
    console.log('Student practical records columns:', columns.map(col => col.Field));
    
    // Get some sample data from the student_practical_records table
    const [records] = await db.query(`
      SELECT id, student_id, teacher_id, practical_id, class_name, subject_name, status
      FROM student_practical_records
      LIMIT 5
    `);
    
    console.log('Sample records:', records);
    
    // Get unique class names
    const [classNames] = await db.query(`
      SELECT DISTINCT class_name
      FROM student_practical_records
    `);
    
    console.log('Unique class names:', classNames.map(c => c.class_name));
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking student records:', error);
    process.exit(1);
  }
}

checkStudentRecords();
