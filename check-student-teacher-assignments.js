const db = require('./config/database');

async function checkStudentTeacherAssignments() {
  try {
    console.log('=== Checking Student and Teacher Assignments ===\n');

    // 1. Check student class assignments
    console.log('=== Student Class Assignments ===');
    
    // Check if any student is assigned to multiple classes in the same session
    const [multipleClassStudents] = await db.query(`
      SELECT 
        sc.student_id, 
        u.name as student_name,
        COUNT(DISTINCT cr.class_id) as class_count,
        GROUP_CONCAT(DISTINCT c.name ORDER BY c.name SEPARATOR ', ') as classes,
        cr.session
      FROM student_classrooms sc
      JOIN users u ON sc.student_id = u.id
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      GROUP BY sc.student_id, cr.session
      HAVING class_count > 1
    `);
    
    if (multipleClassStudents.length > 0) {
      console.log('❌ Found students assigned to multiple classes in the same session:');
      multipleClassStudents.forEach(student => {
        console.log(`  Student ${student.student_id} (${student.student_name}) is assigned to ${student.class_count} classes in session ${student.session}: ${student.classes}`);
      });
    } else {
      console.log('✅ No students are assigned to multiple classes in the same session');
    }
    
    // 2. Check student subject assignments
    console.log('\n=== Student Subject Assignments ===');
    
    // Get student subject counts
    const [studentSubjects] = await db.query(`
      SELECT 
        ss.student_id, 
        u.name as student_name,
        COUNT(DISTINCT ss.subject_id) as subject_count,
        GROUP_CONCAT(DISTINCT s.name ORDER BY s.name SEPARATOR ', ') as subjects
      FROM student_subjects ss
      JOIN users u ON ss.student_id = u.id
      JOIN subjects s ON ss.subject_id = s.id
      GROUP BY ss.student_id
      ORDER BY subject_count DESC
      LIMIT 10
    `);
    
    console.log('Student subject assignments (top 10):');
    studentSubjects.forEach(student => {
      console.log(`  Student ${student.student_id} (${student.student_name}) is assigned to ${student.subject_count} subjects: ${student.subjects}`);
    });
    
    // 3. Check teacher class assignments by session
    console.log('\n=== Teacher Class Assignments by Session ===');
    
    // Get teacher class counts by session
    const [teacherClasses] = await db.query(`
      SELECT 
        tc.teacher_id, 
        u.name as teacher_name,
        cr.session,
        COUNT(DISTINCT cr.class_id) as class_count,
        GROUP_CONCAT(DISTINCT c.name ORDER BY c.name SEPARATOR ', ') as classes
      FROM teacher_classes tc
      JOIN users u ON tc.teacher_id = u.id
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      GROUP BY tc.teacher_id, cr.session
      ORDER BY class_count DESC
      LIMIT 20
    `);
    
    console.log('Teacher class assignments by session (top 20):');
    teacherClasses.forEach(teacher => {
      console.log(`  Teacher ${teacher.teacher_id} (${teacher.teacher_name}) is assigned to ${teacher.class_count} classes in session ${teacher.session}: ${teacher.classes}`);
    });
    
    // 4. Check teachers without class assignments
    console.log('\n=== Teachers Without Class Assignments ===');
    
    const [teachersWithoutClasses] = await db.query(`
      SELECT 
        u.id, 
        u.name,
        u.email
      FROM users u
      LEFT JOIN teacher_classes tc ON u.id = tc.teacher_id
      WHERE u.role = 'teacher' AND tc.id IS NULL
      ORDER BY u.id
    `);
    
    if (teachersWithoutClasses.length > 0) {
      console.log(`❌ Found ${teachersWithoutClasses.length} teachers without class assignments:`);
      teachersWithoutClasses.forEach(teacher => {
        console.log(`  Teacher ${teacher.id} (${teacher.name}) - ${teacher.email}`);
      });
    } else {
      console.log('✅ All teachers have class assignments');
    }
    
    // 5. Check class-subject assignments
    console.log('\n=== Class-Subject Assignments ===');
    
    const [classSubjects] = await db.query(`
      SELECT 
        c.id as class_id,
        c.name as class_name,
        COUNT(DISTINCT sca.subject_id) as subject_count,
        GROUP_CONCAT(DISTINCT s.name ORDER BY s.name SEPARATOR ', ') as subjects
      FROM classes c
      LEFT JOIN subject_class_assignment sca ON c.id = sca.class_id
      LEFT JOIN subjects s ON sca.subject_id = s.id
      WHERE c.grade IS NOT NULL
      GROUP BY c.id
      ORDER BY subject_count DESC
      LIMIT 10
    `);
    
    console.log('Class-subject assignments (top 10):');
    classSubjects.forEach(cls => {
      console.log(`  Class ${cls.class_id} (${cls.class_name}) has ${cls.subject_count || 0} subjects: ${cls.subjects || 'None'}`);
    });
    
    // 6. Check demo teacher and student assignments
    console.log('\n=== Demo Teacher and Student Assignments ===');
    
    // Check demo teacher
    const [demoTeacher] = await db.query(`
      SELECT id, username, name FROM users WHERE username = 'csteacher' AND role = 'teacher'
    `);
    
    if (demoTeacher.length > 0) {
      const teacherId = demoTeacher[0].id;
      
      // Get demo teacher classes
      const [demoTeacherClasses] = await db.query(`
        SELECT 
          c.name as class_name,
          c.grade,
          c.trade,
          c.section,
          cr.session,
          s.name as subject_name
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        JOIN subject_class_assignment sca ON c.id = sca.class_id
        JOIN subjects s ON sca.subject_id = s.id
        JOIN teacher_subjects ts ON tc.teacher_id = ts.teacher_id AND sca.subject_id = ts.subject_id
        WHERE tc.teacher_id = ?
        ORDER BY cr.session, c.grade, c.trade, c.section, s.name
      `, [teacherId]);
      
      console.log(`Demo teacher (${demoTeacher[0].name}) has ${demoTeacherClasses.length} class-subject assignments:`);
      demoTeacherClasses.forEach(cls => {
        console.log(`  ${cls.class_name} (${cls.grade} ${cls.trade} ${cls.section}) - ${cls.subject_name} - Session: ${cls.session}`);
      });
    } else {
      console.log('❌ Demo teacher not found');
    }
    
    // Check demo student
    const [demoStudent] = await db.query(`
      SELECT id, username, name FROM users WHERE username = 'csstudent' AND role = 'student'
    `);
    
    if (demoStudent.length > 0) {
      const studentId = demoStudent[0].id;
      
      // Get demo student class
      const [demoStudentClass] = await db.query(`
        SELECT 
          c.name as class_name,
          c.grade,
          c.trade,
          c.section,
          cr.session
        FROM student_classrooms sc
        JOIN classrooms cr ON sc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        WHERE sc.student_id = ?
      `, [studentId]);
      
      console.log(`Demo student (${demoStudent[0].name}) is assigned to ${demoStudentClass.length} classes:`);
      demoStudentClass.forEach(cls => {
        console.log(`  ${cls.class_name} (${cls.grade} ${cls.trade} ${cls.section}) - Session: ${cls.session}`);
      });
      
      // Get demo student subjects
      const [demoStudentSubjects] = await db.query(`
        SELECT 
          s.name as subject_name,
          t.name as trade_name
        FROM student_subjects ss
        JOIN subjects s ON ss.subject_id = s.id
        LEFT JOIN trades t ON ss.trade_id = t.id
        WHERE ss.student_id = ?
        ORDER BY s.name
      `, [studentId]);
      
      console.log(`Demo student (${demoStudent[0].name}) is assigned to ${demoStudentSubjects.length} subjects:`);
      demoStudentSubjects.forEach(subj => {
        console.log(`  ${subj.subject_name}${subj.trade_name ? ` (${subj.trade_name})` : ''}`);
      });
    } else {
      console.log('❌ Demo student not found');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking assignments:', error);
    process.exit(1);
  }
}

checkStudentTeacherAssignments();
