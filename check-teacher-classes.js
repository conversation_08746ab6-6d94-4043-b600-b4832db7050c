const db = require('./config/database');

async function getTeacherClasses(teacherId) {
  try {
    console.log(`Checking classes for teacher ID: ${teacherId}`);
    
    // First, check if the teacher exists
    const [teacher] = await db.query(
      'SELECT id, full_name, role FROM users WHERE id = ? AND role = "teacher"',
      [teacherId]
    );
    
    if (teacher.length === 0) {
      console.log('Teacher not found or user is not a teacher');
      return;
    }
    
    console.log(`Teacher: ${teacher[0].full_name} (ID: ${teacher[0].id})`);
    console.log('----------------------------------------');
    
    // Try to get classes using teacher_classes table
    console.log('Classes assigned through teacher_classes:');
    const [teacherClasses] = await db.query(`
      SELECT tc.id, c.name AS class_name, c.grade, c.trade, c.section, cl.room_number
      FROM teacher_classes tc 
      JOIN classrooms cl ON tc.classroom_id = cl.id
      JOIN classes c ON cl.class_id = c.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);
    
    if (teacherClasses.length > 0) {
      console.table(teacherClasses);
    } else {
      console.log('No classes found in teacher_classes table');
    }
    
    // Check for subject assignments
    console.log('\nSubjects assigned to this teacher:');
    const [teacherSubjects] = await db.query(`
      SELECT ts.id, s.name AS subject_name, s.code AS subject_code
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = ?
    `, [teacherId]);
    
    if (teacherSubjects.length > 0) {
      console.table(teacherSubjects);
    } else {
      console.log('No subjects found in teacher_subjects table');
    }
    
    // Check for classes through practicals
    console.log('\nClasses assigned through practicals:');
    const [practicalClasses] = await db.query(`
      SELECT DISTINCT c.id, c.name AS class_name, c.grade, c.trade, c.section, s.name AS subject_name
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      WHERE p.teacher_id = ?
    `, [teacherId]);
    
    if (practicalClasses.length > 0) {
      console.table(practicalClasses);
    } else {
      console.log('No classes found through practicals');
    }
    
    // Check for lecture schedules
    console.log('\nLecture schedules:');
    const [lectureSchedules] = await db.query(`
      SELECT ls.id, ls.day_of_week, ls.start_time, ls.end_time, ls.classroom,
             c.name AS class_name, c.grade, c.trade, c.section,
             s.name AS subject_name
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = ?
    `, [teacherId]);
    
    if (lectureSchedules.length > 0) {
      console.table(lectureSchedules);
    } else {
      console.log('No lecture schedules found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error fetching teacher classes:', error);
    process.exit(1);
  }
}

// Execute with teacher ID 1
getTeacherClasses(1);
