const db = require('./config/database');

async function checkTeacherLectures() {
  try {
    // Get the structure of the teacher_lectures table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM teacher_lectures
    `);
    
    console.log('Teacher lectures columns:', columns.map(col => col.Field));
    
    // Get some sample data from the teacher_lectures table
    const [lectures] = await db.query(`
      SELECT id, teacher_id, date, class_name, subject_name, topic, status
      FROM teacher_lectures
      LIMIT 5
    `);
    
    console.log('Sample lectures:', lectures);
    
    // Get unique class names
    const [classNames] = await db.query(`
      SELECT DISTINCT class_name
      FROM teacher_lectures
    `);
    
    console.log('Unique class names:', classNames.map(c => c.class_name));
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking teacher lectures:', error);
    process.exit(1);
  }
}

checkTeacherLectures();
