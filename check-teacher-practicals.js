const db = require('./config/database');

async function checkTeacherPracticals() {
  try {
    // Get the structure of the teacher_practicals table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM teacher_practicals
    `);

    console.log('Teacher practicals columns:', columns.map(col => col.Field));

    // Get some sample data from the teacher_practicals table
    const [practicals] = await db.query(`
      SELECT id, teacher_id, date, class_name, subject_name, practical_topic, venue, status
      FROM teacher_practicals
      LIMIT 5
    `);

    console.log('Sample practicals:', practicals);

    // Get unique class names
    const [classNames] = await db.query(`
      SELECT DISTINCT class_name
      FROM teacher_practicals
    `);

    console.log('Unique class names:', classNames.map(c => c.class_name));

    process.exit(0);
  } catch (error) {
    console.error('Error checking teacher practicals:', error);
    process.exit(1);
  }
}

checkTeacherPracticals();
