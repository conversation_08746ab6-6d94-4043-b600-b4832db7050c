const db = require('./config/database');

async function checkTeacherSyllabus() {
  try {
    // Get the structure of the teacher_syllabus table
    const [columns] = await db.query(`
      SHOW COLUMNS FROM teacher_syllabus
    `);

    console.log('Teacher syllabus columns:', columns.map(col => col.Field));

    // Get some sample data from the teacher_syllabus table
    const [syllabus] = await db.query(`
      SELECT id, teacher_id, subject_name, topic, status
      FROM teacher_syllabus
      LIMIT 5
    `);

    console.log('Sample syllabus:', syllabus);

    // Check if syllabus_progress table exists and has class information
    try {
      const [progressColumns] = await db.query(`
        SHOW COLUMNS FROM syllabus_progress
      `);

      console.log('Syllabus progress columns:', progressColumns.map(col => col.Field));

      // Check if there's class information in syllabus_progress
      const [progressData] = await db.query(`
        SELECT * FROM syllabus_progress LIMIT 5
      `);

      console.log('Sample syllabus progress:', progressData);
    } catch (error) {
      console.log('Error checking syllabus progress:', error.message);
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking teacher syllabus:', error);
    process.exit(1);
  }
}

checkTeacherSyllabus();
