#!/bin/bash

# Create backup directory
BACKUP_DIR="/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/backup_files"
mkdir -p "$BACKUP_DIR/js"
mkdir -p "$BACKUP_DIR/css"

# Function to move a file to backup directory
backup_file() {
  local file=$1
  local target_dir=$2
  
  if [ -f "$file" ]; then
    echo "Moving $file to backup"
    # Create directory structure in backup
    local rel_path=${file#/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/}
    local backup_path="$BACKUP_DIR/$rel_path"
    mkdir -p "$(dirname "$backup_path")"
    
    # Move file to backup
    mv "$file" "$backup_path"
    echo "✅ Moved: $file"
  else
    echo "❌ File not found: $file"
  fi
}

# List of unnecessary JavaScript files to remove
JS_FILES=(
  # Debug files
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/chat-debug.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/chat-icon-debug.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/device-testing.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/direct-db-counts.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/fix-javascript.js"
  
  # Duplicate chat files
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/ensure-chat-button.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/guaranteed-chat-icon.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/unified-chat-icon.js"
  
  # Redundant files
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/js/global-fix.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/debug-profile-api.js"
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/fix-profile-api.js"
)

# List of unnecessary CSS files to remove
CSS_FILES=(
  # Duplicate chat CSS files
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/css/unified-chat-icon.css"
  
  # Redundant CSS files
  "/Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/public/css/chosen-custom.css"
)

# Backup JavaScript files
echo "Backing up unnecessary JavaScript files..."
for file in "${JS_FILES[@]}"; do
  backup_file "$file" "$BACKUP_DIR/js"
done

# Backup CSS files
echo "Backing up unnecessary CSS files..."
for file in "${CSS_FILES[@]}"; do
  backup_file "$file" "$BACKUP_DIR/css"
done

echo "Cleanup completed. Files have been moved to $BACKUP_DIR"
