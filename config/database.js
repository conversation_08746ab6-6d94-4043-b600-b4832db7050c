const mysql = require('mysql2/promise');
// Import fs for direct file logging to avoid circular dependencies
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// We'll use this to avoid circular dependencies when importing the query error logger
let queryErrorLogger = null;

// This will be set after the module is fully loaded
setTimeout(() => {
    try {
        queryErrorLogger = require('../utils/query-error-logger');
    } catch (error) {
        console.error('Failed to load query error logger:', error);
    }
}, 1000);

// Print database configuration (excluding sensitive data)
console.log('\n=== Database Configuration ===');
console.log('Host:', process.env.DB_HOST);
console.log('User:', process.env.DB_USER);
console.log('Database:', process.env.DB_NAME);
console.log('Port:', process.env.DB_PORT);
console.log('===========================\n');

const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

const pool = mysql.createPool(dbConfig);

// Simple file-based logging function to avoid circular dependencies
const logToFile = async (type, message, data = null) => {
    try {
        const timestamp = new Date().toISOString();
        const logDir = path.join(__dirname, '../logs');

        // Ensure log directory exists
        try {
            await fs.mkdir(logDir, { recursive: true });
        } catch (mkdirError) {
            console.error('Failed to create log directory:', mkdirError);
        }

        const logFile = path.join(logDir, `${type}.log`);
        const logEntry = `[${timestamp}] ${message}${data ? ' ' + JSON.stringify(data) : ''}\n`;

        await fs.appendFile(logFile, logEntry);
    } catch (error) {
        console.error(`Failed to log to ${type} file:`, error);
    }
};

// Test database connection
const testConnection = async () => {
    try {
        const connection = await pool.getConnection();
        console.log('🟢 Database connection established successfully');
        console.log(`📦 Connected to database: ${process.env.DB_NAME}`);

        // Test query to verify database access
        const [result] = await connection.query('SELECT 1');
        console.log('✅ Database query test successful');

        connection.release();
        return true;
    } catch (error) {
        console.error('\n❌ Database Connection Error ❌');
        console.error('Error Type:', error.code);
        console.error('Error Message:', error.message);

        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('💡 Hint: Check your database username and password in .env file');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.error('💡 Hint: Database does not exist. Create it first.');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('💡 Hint: Make sure MySQL server is running (check XAMPP)');
        }

        console.error('\nCheck your .env file contains:');
        console.error('DB_HOST=localhost');
        console.error('DB_USER=root');
        console.error('DB_PASSWORD=');
        console.error('DB_NAME=exam_prep_platform');
        console.error('DB_PORT=3306\n');

        process.exit(1);
    }
};

// Execute connection test immediately
testConnection().then(success => {
    if (success) {
        console.log('🚀 Database initialization complete\n');
    }
});

// Modified query wrapper
const wrappedPool = {
    query: async (...args) => {
        const start = Date.now();
        try {
            const result = await pool.query(...args);
            const duration = Date.now() - start;

            // Log query to file
            await logToFile('query', `Query executed in ${duration}ms`, {
                query: args[0],
                params: args[1] || [],
                duration: duration
            });

            // Log slow queries with more details
            if (duration > 1000) {
                await logToFile('slow_query', `Slow query executed in ${duration}ms`, {
                    query: args[0],
                    params: args[1] || [],
                    duration: duration,
                    timestamp: new Date().toISOString()
                });
            }

            return result;
        } catch (error) {
            const duration = Date.now() - start;

            // Log query error to file
            await logToFile('query_error', `Query error in ${duration}ms: ${error.message}`, {
                query: args[0],
                params: args[1] || [],
                duration: duration,
                error: {
                    message: error.message,
                    code: error.code,
                    sqlState: error.sqlState,
                    sqlMessage: error.sqlMessage
                },
                timestamp: new Date().toISOString()
            });

            // Log to database if query error logger is available
            if (queryErrorLogger) {
                try {
                    await queryErrorLogger.logQueryError(error, args[0], args[1] || []);
                } catch (logError) {
                    console.error('Failed to log query error to database:', logError);
                }
            }

            throw error;
        }
    },
    getConnection: () => pool.getConnection()
};

module.exports = wrappedPool;
