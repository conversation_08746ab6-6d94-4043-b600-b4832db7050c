-- Practicals Management Tables

-- Classes table
CREATE TABLE IF NOT EXISTS `classes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `trade` varchar(50) DEFAULT NULL,
  `section` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Subjects table
CREATE TABLE IF NOT EXISTS `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(20) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Labs table
CREATE TABLE IF NOT EXISTS `labs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `capacity` int(11) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Topics table
CREATE TABLE IF NOT EXISTS `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `topics_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Practicals table
CREATE TABLE IF NOT EXISTS `practicals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `class_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `lab_id` int(11) NOT NULL,
  `description` text DEFAULT NULL,
  `prerequisites` varchar(255) DEFAULT NULL,
  `materials` text DEFAULT NULL,
  `equipment` text DEFAULT NULL,
  `status` enum('Upcoming', 'Completed', 'Cancelled') NOT NULL DEFAULT 'Upcoming',
  `teacher_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  KEY `subject_id` (`subject_id`),
  KEY `lab_id` (`lab_id`),
  KEY `teacher_id` (`teacher_id`),
  CONSTRAINT `practicals_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`),
  CONSTRAINT `practicals_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`),
  CONSTRAINT `practicals_ibfk_3` FOREIGN KEY (`lab_id`) REFERENCES `labs` (`id`),
  CONSTRAINT `practicals_ibfk_4` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Junction table for practicals and topics (many-to-many)
CREATE TABLE IF NOT EXISTS `practical_topics` (
  `practical_id` int(11) NOT NULL,
  `topic_id` int(11) NOT NULL,
  PRIMARY KEY (`practical_id`, `topic_id`),
  KEY `topic_id` (`topic_id`),
  CONSTRAINT `practical_topics_ibfk_1` FOREIGN KEY (`practical_id`) REFERENCES `practicals` (`id`) ON DELETE CASCADE,
  CONSTRAINT `practical_topics_ibfk_2` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Student records for practicals
CREATE TABLE IF NOT EXISTS `practical_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `practical_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `attendance` enum('Present', 'Absent', 'Late') NOT NULL DEFAULT 'Absent',
  `completion_percentage` tinyint(3) UNSIGNED DEFAULT 0,
  `performance` enum('Excellent', 'Very Good', 'Good', 'Satisfactory', 'Poor', 'N/A') DEFAULT 'N/A',
  `comments` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `practical_student` (`practical_id`, `student_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `practical_records_ibfk_1` FOREIGN KEY (`practical_id`) REFERENCES `practicals` (`id`) ON DELETE CASCADE,
  CONSTRAINT `practical_records_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data for testing

-- Classes
INSERT INTO `classes` (`name`, `trade`, `section`) VALUES
('11', 'Non-Medical', 'A'),
('11', 'Non-Medical', 'B'),
('11', 'Medical', 'A'),
('12', 'Non-Medical', 'A'),
('12', 'Commerce', 'A');

-- Subjects
INSERT INTO `subjects` (`name`, `code`) VALUES
('Computer Science', 'CS'),
('Physics', 'PHY'),
('Chemistry', 'CHEM'),
('Biology', 'BIO');

-- Labs
INSERT INTO `labs` (`name`, `capacity`, `location`) VALUES
('Lab 1', 30, 'First Floor'),
('Lab 2', 30, 'First Floor'),
('Lab 3', 25, 'Second Floor');

-- Topics
INSERT INTO `topics` (`name`, `subject_id`) VALUES
('Python Basics', 1),
('Python Functions and Modules', 1),
('Database Connectivity with Python', 1),
('File Handling in Python', 1),
('Introduction to Programming', 1),
('Basic Network Configuration', 1),
('HTML and CSS Basics', 1),
('JavaScript Fundamentals', 1),
('Object-Oriented Programming', 1),
('Data Structures', 1),
('Electricity and Magnetism', 2),
('Wave Optics', 2),
('Mechanics and Kinematics', 2),
('Organic Chemistry Reactions', 3),
('Chemical Bonding', 3),
('Cell Structure and Function', 4),
('Human Anatomy', 4),
('Genetics and Evolution', 4); 