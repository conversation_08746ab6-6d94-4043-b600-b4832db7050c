const i18n = require('i18n');
const path = require('path');

i18n.configure({
    locales: ['en', 'pa'],
    defaultLocale: 'en',
    directory: path.join(__dirname, '../locales'),
    objectNotation: true,
    cookie: 'lang',
    queryParameter: 'lang',
    autoReload: true,
    updateFiles: false,
    syncFiles: false,
    register: global,
    missingKeyFn: function(locale, value) {
        console.log(`Missing translation key: ${value} for locale: ${locale}`);
        return value;
    }
});

module.exports = i18n;
