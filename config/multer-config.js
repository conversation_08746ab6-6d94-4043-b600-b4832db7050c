/**
 * Multer Configuration
 * Handles file upload configuration for different parts of the application
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Create base uploads directory if it doesn't exist
const baseUploadDir = path.join(__dirname, '../public/uploads');
if (!fs.existsSync(baseUploadDir)) {
  fs.mkdirSync(baseUploadDir, { recursive: true });
  // Set full permissions
  try {
    fs.chmodSync(baseUploadDir, 0o777);
    console.log('Set full permissions on base upload directory');
  } catch (err) {
    console.error('Error setting permissions on base upload directory:', err);
  }
}

// Configure storage for procurement files
const procurementStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(baseUploadDir, 'procurement');
    try {
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
        // Set full permissions
        fs.chmodSync(uploadDir, 0o777);
        console.log('Created procurement upload directory with full permissions');
      } else {
        // Ensure directory has correct permissions
        fs.chmodSync(uploadDir, 0o777);
      }

      // Log the destination directory for debugging
      console.log(`Destination directory for ${file.fieldname}: ${uploadDir}`);

      cb(null, uploadDir);
    } catch (err) {
      console.error('Error creating or setting permissions on procurement directory:', err);
      // Create a fallback directory in /tmp which should be writable
      const fallbackDir = path.join('/tmp', 'procurement_uploads');
      if (!fs.existsSync(fallbackDir)) {
        fs.mkdirSync(fallbackDir, { recursive: true });
      }
      console.log(`Using fallback directory: ${fallbackDir}`);
      cb(null, fallbackDir);
    }
  },
  filename: function (req, file, cb) {
    // Extract file type from field name (e.g., 'request_letter', 'quotation', etc.)
    let fileType = 'document';

    // Check if the field name contains a known file type
    const knownTypes = ['request_letter', 'quotation', 'committee_proceedings', 'bill', 'payment_proof'];
    for (const type of knownTypes) {
      if (file.fieldname.includes(type)) {
        fileType = type;
        break;
      }
    }

    // Generate unique filename with original extension
    const uniqueFilename = `${fileType}-${uuidv4()}${path.extname(file.originalname)}`;

    // Log the file being processed
    console.log(`Processing file upload: ${file.fieldname} -> ${uniqueFilename} (${file.size} bytes, ${file.mimetype})`);

    cb(null, uniqueFilename);
  }
});

// File filter for procurement files
const procurementFileFilter = (req, file, cb) => {
  // Accept images, PDFs, and common document formats
  const allowedMimes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // Word
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // Excel
    'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation' // PowerPoint
  ];

  // Log the file being filtered
  console.log(`Filtering file: ${file.fieldname}, mimetype: ${file.mimetype}`);

  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    console.warn(`Rejected file upload: ${file.fieldname} - Invalid mimetype: ${file.mimetype}`);
    cb(new Error(`Invalid file type. Only images, PDFs, and office documents are allowed. Got: ${file.mimetype}`), false);
  }
};

// Create multer instances for different parts of the application
const procurementUpload = multer({
  storage: procurementStorage,
  fileFilter: procurementFileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB - reduced from 5MB to be even more conservative
    files: 5, // Maximum 5 files per request
    fieldSize: 5 * 1024 * 1024, // 5MB for field size limit
    fieldNameSize: 100, // Field name size limit
    parts: 50 // Reduced to 50 for more stability
  }
});

// Create a step-specific multer instance for save-draft functionality
// This will handle only the files relevant to the current step
const createStepSpecificUpload = (step) => {
  return multer({
    storage: procurementStorage,
    fileFilter: (req, file, cb) => {
      // First apply the standard file filter
      procurementFileFilter(req, file, (err, result) => {
        if (err) return cb(err, false);
        if (!result) return cb(null, false);

        // If the file passes the standard filter, check if it's relevant to the current step
        const currentStep = parseInt(req.body.current_step || step);
        console.log(`Checking file ${file.fieldname} for step ${currentStep}`);

        // Define which file fields are relevant to each step
        const stepFields = {
          1: ['request_letter'],
          2: [], // No file uploads in step 2
          3: ['quotation_file'],
          4: [], // No file uploads in step 4
          5: ['committee_proceedings'],
          6: ['bill', 'payment_proof']
        };

        // Check if the file's field name is relevant to the current step
        const relevantFields = stepFields[currentStep] || [];
        const isRelevant = relevantFields.some(field => file.fieldname.includes(field));

        if (isRelevant) {
          console.log(`Accepting file ${file.fieldname} for step ${currentStep}`);
          return cb(null, true);
        } else {
          console.log(`Skipping file ${file.fieldname} - not relevant to step ${currentStep}`);
          return cb(null, false);
        }
      });
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 10, // Maximum 10 files per step
      fieldSize: 20 * 1024 * 1024, // 20MB for field size limit
      fieldNameSize: 200, // Increased field name size limit
      parts: 200 // Increased parts limit
    }
  });
};

// Export multer instances
module.exports = {
  procurementUpload,
  createStepSpecificUpload
};
