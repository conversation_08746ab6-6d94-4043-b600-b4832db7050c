const session = require('express-session');
// Use memory store for now to avoid database issues
const MemoryStore = session.MemoryStore;

// Create a new memory store
const sessionStore = new MemoryStore();

// Session configuration
const sessionConfig = {
    secret: process.env.SESSION_SECRET || 'secret-key',
    resave: false,
    saveUninitialized: true,
    store: sessionStore,
    cookie: {
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
};

// Create session parser
const sessionParser = session(sessionConfig);

module.exports = {
    sessionConfig,
    sessionParser,
    sessionStore
};
