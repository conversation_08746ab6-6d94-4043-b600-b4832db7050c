/**
 * SQL Queries Repository
 * Centralized location for all SQL queries used across the application
 * This file contains actual SQL query copies used in various pages for direct database querying
 *
 * Usage: Copy these queries directly for database operations
 * Note: Replace ? with actual values or use parameterized queries for security
 */

const SQLQueries = {

  // ==================== PRINCIPAL DASHBOARD QUERIES ====================

  principalDashboard: {
    // Principal dashboard real-time statistics
    dashboardStats: `
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `,

    // School statistics for principal overview
    schoolStats: `
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `,

    // Recent activities for principal dashboard
    recentActivities: `
      SELECT
        'lecture' as type,
        tl.topic as title,
        tl.subject_name as subtitle,
        u.name as teacher_name,
        tl.date,
        tl.status,
        tl.created_at
      FROM teacher_lectures tl
      JOIN users u ON tl.teacher_id = u.id
      ORDER BY tl.created_at DESC
      LIMIT 10
    `,

    // Class-wise performance summary
    classPerformance: `
      SELECT
        class_name,
        COUNT(*) as total_lectures,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_lectures,
        ROUND((SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as completion_rate
      FROM teacher_lectures
      GROUP BY class_name
      ORDER BY completion_rate DESC
      LIMIT 5
    `,

    // Academic progress data
    academicProgress: `
      SELECT
        c.name as class_name,
        s.name as subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage
      FROM classes c
      LEFT JOIN teacher_lectures tl ON c.name = tl.class_name
      LEFT JOIN subjects s ON s.name = tl.subject_name
      GROUP BY c.name, s.name
      ORDER BY c.name, s.name
    `,

    // Teacher performance data
    teacherPerformance: `
      SELECT
        u.id,
        u.name,
        u.email,
        u.full_name,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate,
        u.last_login
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email, u.full_name, u.last_login
      ORDER BY completion_rate DESC
    `
  },

  // ==================== STUDENT DATA QUERIES ====================

  students: {
    // Get all students with pagination (Principal students page)
    getAllWithPagination: `
      SELECT
        s.id, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
        s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
        s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
        s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
        s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
        s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
        s.account_holder_code, s.session, s.created_at, s.updated_at
      FROM students s
      WHERE s.is_active = 1
      ORDER BY s.class, s.section, s.roll_no, s.name
      LIMIT ? OFFSET ?
    `,

    // Count total students with filters
    countWithFilters: `
      SELECT COUNT(*) as total
      FROM students s
      WHERE s.is_active = 1
        AND (? = '' OR s.class = ?)
        AND (? = '' OR s.section = ?)
        AND (? = '' OR s.session = ?)
        AND (? = '' OR s.gender = ?)
        AND (? = '' OR s.stream = ?)
        AND (? = '' OR s.bpl = ?)
        AND (? = '' OR s.disability = ?)
        AND (? = '' OR s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)
    `,

    // Get students with filters (Principal students page)
    getWithFilters: `
      SELECT
        s.id, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
        s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
        s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
        s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
        s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
        s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
        s.account_holder_code, s.session, s.created_at, s.updated_at
      FROM students s
      WHERE s.is_active = 1
        AND (? = '' OR s.class = ?)
        AND (? = '' OR s.section = ?)
        AND (? = '' OR s.session = ?)
        AND (? = '' OR s.gender = ?)
        AND (? = '' OR s.stream = ?)
        AND (? = '' OR s.bpl = ?)
        AND (? = '' OR s.disability = ?)
        AND (? = '' OR s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)
      ORDER BY s.class, s.section, s.roll_no, s.name
      LIMIT ? OFFSET ?
    `,

    // Get single student by ID
    getById: `
      SELECT * FROM students
      WHERE id = ? AND is_active = 1
    `,

    // Get soft deleted students (trash)
    getTrashed: `
      SELECT id, student_id, name, class, section, contact_no, updated_at
      FROM students
      WHERE is_active = 0
      ORDER BY updated_at DESC
      LIMIT 50
    `,

    // Get unique filter values
    getUniqueClasses: `
      SELECT DISTINCT class
      FROM students
      WHERE is_active = 1 AND class IS NOT NULL AND class != ''
      ORDER BY class
    `,

    getUniqueSections: `
      SELECT DISTINCT section
      FROM students
      WHERE is_active = 1 AND section IS NOT NULL AND section != ''
      ORDER BY section
    `,

    getUniqueSessions: `
      SELECT DISTINCT session
      FROM students
      WHERE is_active = 1 AND session IS NOT NULL AND session != ''
      ORDER BY session DESC
    `,

    getUniqueStreams: `
      SELECT DISTINCT stream
      FROM students
      WHERE is_active = 1 AND stream IS NOT NULL AND stream != ''
      ORDER BY stream
    `
  },

  // ==================== INFRASTRUCTURE QUERIES ====================

  infrastructure: {
    // Get classroom details by room ID
    getClassroomDetails: `
      SELECT
        id,
        room_number,
        floor,
        capacity,
        building
      FROM rooms
      WHERE id = ? OR room_number = ? OR room_number = ?
    `,

    // Get students in specific room
    getStudentsInRoom: `
      SELECT
        id,
        student_id,
        name,
        father_name,
        mother_name,
        gender,
        class,
        section,
        session,
        roll_no,
        contact_no,
        admission_no,
        dob,
        height,
        weight,
        caste_category_name,
        religion_name,
        medium_name,
        bpl,
        disability,
        cur_address,
        village_ward,
        pin_code
      FROM students
      WHERE (room_number = ? OR room_number = ?) AND is_active = 1
      ORDER BY class, section, roll_no, name
    `,

    // Check IT inventory room_id foreign key relationship
    checkITInventoryRoomLink: `
      SELECT
        i.id,
        i.name,
        i.type,
        i.serial_number,
        i.location as current_location,
        i.room_id,
        r.room_number as linked_room_number,
        r.building,
        r.floor,
        CASE
          WHEN i.room_id IS NOT NULL AND r.id IS NOT NULL THEN 'LINKED'
          WHEN i.room_id IS NOT NULL AND r.id IS NULL THEN 'BROKEN_LINK'
          ELSE 'UNLINKED'
        END as link_status
      FROM it_inventory i
      LEFT JOIN rooms r ON i.room_id = r.id
      ORDER BY link_status DESC, r.room_number, i.name
    `,

    // Get all rooms with their equipment count using foreign keys
    getRoomsWithEquipmentCount: `
      SELECT
        r.id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.id) as it_equipment_count,
        COUNT(DISTINCT e.id) as electrical_equipment_count,
        GROUP_CONCAT(DISTINCT i.type ORDER BY i.type) as it_equipment_types,
        GROUP_CONCAT(DISTINCT e.item_type ORDER BY e.item_type) as electrical_equipment_types,
        GROUP_CONCAT(DISTINCT i.name ORDER BY i.name) as it_equipment_names
      FROM rooms r
      LEFT JOIN it_inventory i ON r.id = i.room_id
      LEFT JOIN electrical_inventory e ON (
        e.room_number = REGEXP_SUBSTR(r.room_number, '[0-9]+') OR
        e.location = r.room_number
      )
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `,

    // Get IT equipment for specific room using foreign key
    getITEquipmentByRoomId: `
      SELECT
        i.id,
        i.name,
        i.type,
        i.serial_number,
        i.model,
        i.manufacturer,
        i.status,
        i.condition_status,
        i.purchase_date,
        i.warranty_end_date,
        i.notes,
        i.mac_address,
        i.ip_address,
        i.hostname
      FROM it_inventory i
      WHERE i.room_id = ?
      ORDER BY i.type, i.name
    `,

    // Get unlinked IT equipment (no room_id)
    getUnlinkedITEquipment: `
      SELECT
        i.id,
        i.name,
        i.type,
        i.serial_number,
        i.location,
        i.status,
        i.manufacturer,
        i.model
      FROM it_inventory i
      WHERE i.room_id IS NULL
      ORDER BY i.location, i.name
    `,

    // Get equipment summary by room using inventory_items table
    getEquipmentSummaryByRoom: `
      SELECT
        r.id as room_id,
        r.room_number,
        r.building,
        r.floor,
        r.capacity,
        COUNT(DISTINCT i.item_id) as total_it_equipment,
        COUNT(DISTINCT CASE WHEN i.status = 'available' THEN i.item_id END) as available_it_equipment,
        COUNT(DISTINCT CASE WHEN i.status = 'assigned' THEN i.item_id END) as assigned_it_equipment,
        COUNT(DISTINCT CASE WHEN i.status = 'maintenance' THEN i.item_id END) as maintenance_it_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Router%' THEN i.item_id END) as network_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PANEL%' THEN i.item_id END) as interactive_panels,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%CAMERA%' THEN i.item_id END) as cameras
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      GROUP BY r.id, r.room_number, r.building, r.floor, r.capacity
      ORDER BY r.room_number
    `,

    // Get detailed inventory items for specific room
    getInventoryItemsByRoomId: `
      SELECT
        i.item_id,
        i.name as item_name,
        i.description,
        i.serial_number,
        i.model,
        i.manufacturer,
        i.purchase_date,
        i.purchase_cost,
        i.warranty_expiry,
        i.status,
        i.location,
        i.notes,
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
          WHEN i.name LIKE '%UPS%' THEN 'ups'
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
          WHEN i.name LIKE '%Laptop%' THEN 'laptop'
          WHEN i.name LIKE '%PANEL%' THEN 'interactive_panel'
          WHEN i.name LIKE '%CAMERA%' THEN 'camera'
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
          WHEN i.name LIKE '%Router%' THEN 'router'
          ELSE 'other'
        END as equipment_type
      FROM inventory_items i
      WHERE i.room_id = ?
      ORDER BY
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 1
          WHEN i.name LIKE '%UPS%' THEN 2
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 3
          WHEN i.name LIKE '%Laptop%' THEN 4
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 5
          ELSE 6
        END, i.name
    `,

    // Get count of items category-wise and room-wise (comprehensive view)
    getItemCountCategoryWiseRoomWise: `
      SELECT
        r.room_number,
        r.id as room_id,
        r.building,
        r.floor,
        r.capacity,

        -- Inventory Items Categories
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PANEL%' THEN i.item_id END) as interactive_panels,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%CAMERA%' THEN i.item_id END) as cameras,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Router%' THEN i.item_id END) as routers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%microphone%' OR i.name LIKE '%DIGITEK%' THEN i.item_id END) as microphones,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' OR i.name LIKE '%TV%' THEN i.item_id END) as tvs,

        -- IT Inventory Categories
        COUNT(DISTINCT CASE WHEN it.type = 'laptop' THEN it.id END) as it_laptops,
        COUNT(DISTINCT CASE WHEN it.type = 'desktop' THEN it.id END) as it_desktops,
        COUNT(DISTINCT CASE WHEN it.type = 'tablet' THEN it.id END) as it_tablets,
        COUNT(DISTINCT CASE WHEN it.type = 'projector' THEN it.id END) as it_projectors,
        COUNT(DISTINCT CASE WHEN it.type = 'printer' THEN it.id END) as it_printers,
        COUNT(DISTINCT CASE WHEN it.type = 'network' THEN it.id END) as it_network,
        COUNT(DISTINCT CASE WHEN it.type = 'other' THEN it.id END) as it_other,

        -- Electrical Inventory Categories
        COUNT(DISTINCT CASE WHEN e.item_type = 'tubelight' THEN e.id END) as tube_lights,
        COUNT(DISTINCT CASE WHEN e.item_type = 'fan' THEN e.id END) as ceiling_fans,
        COUNT(DISTINCT CASE WHEN e.item_type = 'outlet' THEN e.id END) as power_outlets,
        COUNT(DISTINCT CASE WHEN e.item_type = 'switch' THEN e.id END) as switches,
        COUNT(DISTINCT CASE WHEN e.item_type = 'other' THEN e.id END) as electrical_other,

        -- Totals by Table
        COUNT(DISTINCT i.item_id) as total_inventory_items,
        COUNT(DISTINCT it.id) as total_it_items,
        COUNT(DISTINCT e.id) as total_electrical_items,

        -- Grand Total
        (COUNT(DISTINCT i.item_id) + COUNT(DISTINCT it.id) + COUNT(DISTINCT e.id)) as grand_total_equipment

      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      LEFT JOIN it_inventory it ON r.id = it.room_id
      LEFT JOIN electrical_inventory e ON r.id = e.room_id
      GROUP BY r.id, r.room_number, r.building, r.floor, r.capacity
      HAVING grand_total_equipment > 0
      ORDER BY
        CASE
          WHEN r.room_number LIKE 'Computer Lab%' THEN 1
          WHEN r.room_number LIKE '%Lab' THEN 2
          WHEN r.room_number = 'Library' THEN 3
          WHEN r.room_number LIKE '%Office%' THEN 4
          WHEN r.room_number LIKE 'Room%' THEN 5
          ELSE 6
        END,
        r.room_number
    `,

    // Get category-wise summary across all rooms
    getCategoryWiseSummaryAllRooms: `
      SELECT
        'Projectors' as category,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) +
        COUNT(DISTINCT CASE WHEN it.type = 'projector' THEN it.id END) as total_count,
        'Technology' as category_group
      FROM inventory_items i
      FULL OUTER JOIN it_inventory it ON 1=1
      WHERE (i.room_id IS NOT NULL OR it.room_id IS NOT NULL)

      UNION ALL

      SELECT
        'Desktops' as category,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) +
        COUNT(DISTINCT CASE WHEN it.type = 'desktop' THEN it.id END) as total_count,
        'Technology' as category_group
      FROM inventory_items i
      FULL OUTER JOIN it_inventory it ON 1=1
      WHERE (i.room_id IS NOT NULL OR it.room_id IS NOT NULL)

      UNION ALL

      SELECT
        'Laptops' as category,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) +
        COUNT(DISTINCT CASE WHEN it.type = 'laptop' THEN it.id END) as total_count,
        'Technology' as category_group
      FROM inventory_items i
      FULL OUTER JOIN it_inventory it ON 1=1
      WHERE (i.room_id IS NOT NULL OR it.room_id IS NOT NULL)

      UNION ALL

      SELECT
        'Printers' as category,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) +
        COUNT(DISTINCT CASE WHEN it.type = 'printer' THEN it.id END) as total_count,
        'Technology' as category_group
      FROM inventory_items i
      FULL OUTER JOIN it_inventory it ON 1=1
      WHERE (i.room_id IS NOT NULL OR it.room_id IS NOT NULL)

      UNION ALL

      SELECT
        'UPS Units' as category,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as total_count,
        'Power' as category_group
      FROM inventory_items i
      WHERE i.room_id IS NOT NULL

      UNION ALL

      SELECT
        'Tube Lights' as category,
        COUNT(DISTINCT CASE WHEN e.item_type = 'tubelight' THEN e.id END) as total_count,
        'Electrical' as category_group
      FROM electrical_inventory e
      WHERE e.room_id IS NOT NULL

      UNION ALL

      SELECT
        'Ceiling Fans' as category,
        COUNT(DISTINCT CASE WHEN e.item_type = 'fan' THEN e.id END) as total_count,
        'Electrical' as category_group
      FROM electrical_inventory e
      WHERE e.room_id IS NOT NULL

      ORDER BY category_group, total_count DESC
    `,

    // Get room-wise equipment summary (simplified view)
    getRoomWiseEquipmentSummary: `
      SELECT
        r.room_number,
        r.id as room_id,
        r.building,
        r.floor,
        CASE
          WHEN r.room_number LIKE 'Computer Lab%' THEN 'Computer Lab'
          WHEN r.room_number LIKE '%Lab' THEN 'Science Lab'
          WHEN r.room_number = 'Library' THEN 'Library'
          WHEN r.room_number LIKE '%Office%' THEN 'Office'
          WHEN r.room_number LIKE 'Room%' THEN 'Classroom'
          ELSE 'Other'
        END as room_type,
        COUNT(DISTINCT i.item_id) as inventory_count,
        COUNT(DISTINCT it.id) as it_count,
        COUNT(DISTINCT e.id) as electrical_count,
        (COUNT(DISTINCT i.item_id) + COUNT(DISTINCT it.id) + COUNT(DISTINCT e.id)) as total_equipment
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      LEFT JOIN it_inventory it ON r.id = it.room_id
      LEFT JOIN electrical_inventory e ON r.id = e.room_id
      GROUP BY r.id, r.room_number, r.building, r.floor
      HAVING total_equipment > 0
      ORDER BY room_type, total_equipment DESC, r.room_number
    `,

    // Get students through classroom relationships
    getStudentsThroughClassrooms: `
      SELECT DISTINCT
        u.id,
        u.username as student_id,
        u.name,
        u.full_name as father_name,
        '' as mother_name,
        'Male' as gender,
        cl.grade as class,
        cl.section,
        c.session,
        '' as roll_no,
        '' as contact_no,
        '' as admission_no,
        u.date_of_birth as dob,
        0 as height,
        0 as weight,
        '' as caste_category_name,
        '' as religion_name,
        '' as medium_name,
        'No' as bpl,
        'No' as disability,
        '' as cur_address,
        '' as village_ward,
        '' as pin_code
      FROM student_classrooms sc
      JOIN users u ON sc.student_id = u.id
      JOIN classrooms c ON sc.classroom_id = c.id
      JOIN classes cl ON c.class_id = cl.id
      JOIN rooms r ON c.room_id = r.id
      WHERE r.id = ? AND u.role = 'student' AND sc.status = 'active'
      ORDER BY cl.grade, cl.section, u.name
    `,

    // Fallback: Get students from users table
    getStudentsFromUsers: `
      SELECT DISTINCT
        u.id,
        u.username as student_id,
        u.name,
        u.full_name as father_name,
        '' as mother_name,
        'Unknown' as gender,
        '' as class,
        '' as section,
        '' as session,
        '' as roll_no,
        '' as contact_no,
        '' as admission_no,
        u.date_of_birth as dob,
        0 as height,
        0 as weight,
        '' as caste_category_name,
        '' as religion_name,
        '' as medium_name,
        'No' as bpl,
        'No' as disability,
        '' as cur_address,
        '' as village_ward,
        '' as pin_code
      FROM users u
      WHERE u.role = 'student'
      ORDER BY u.name
      LIMIT 5
    `
  },

  // ==================== STUDENT DASHBOARD QUERIES ====================

  studentDashboard: {
    // Check if test_assignments table exists
    checkTestAssignmentsTable: `
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'test_assignments'
    `,

    // Get test statistics for student
    getTestStats: `
      SELECT
        COUNT(DISTINCT ta.exam_id) as assignedTests,
        COUNT(DISTINCT CASE WHEN ea.status = 'completed' THEN ea.exam_id END) as completedTests,
        IFNULL(ROUND(AVG(CASE WHEN ea.status = 'completed' THEN ea.total_score END), 1), 0) as averageScore
      FROM test_assignments ta
      LEFT JOIN exam_attempts ea ON ta.exam_id = ea.exam_id AND ea.user_id = ?
      WHERE ta.user_id = ?
    `,

    // Get recent activity for student
    getRecentActivity: `
      (SELECT
        'test_completed' as type,
        e.exam_name,
        ea.total_score as score,
        ea.end_time as created_at,
        NULL as plan_name
      FROM exam_attempts ea
      JOIN exams e ON ea.exam_id = e.exam_id
      WHERE ea.user_id = ? AND ea.status = 'completed'
      ORDER BY ea.end_time DESC
      LIMIT 3)

      UNION ALL

      (SELECT
        'test_assigned' as type,
        e.exam_name,
        NULL as score,
        ta.assigned_at as created_at,
        NULL as plan_name
      FROM test_assignments ta
      JOIN exams e ON ta.exam_id = e.exam_id
      WHERE ta.user_id = ?
      ORDER BY ta.assigned_at DESC
      LIMIT 3)
    `,

    // Check class schedule table
    checkClassScheduleTable: `
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_schedule'
    `,

    // Get today's class schedule
    getTodaySchedule: `
      SELECT
        c.start_time,
        c.end_time,
        s.name as subject,
        u.full_name as teacher_name,
        c.room,
        c.type
      FROM class_schedule c
      JOIN subjects s ON c.subject_id = s.id
      JOIN users u ON c.teacher_id = u.id
      JOIN student_classes sc ON c.class_id = sc.class_id
      WHERE sc.student_id = ?
      AND c.day_of_week = WEEKDAY(CURDATE()) + 1
      ORDER BY c.start_time ASC
    `,

    // Get upcoming exams
    getUpcomingExams: `
      SELECT e.exam_id, e.exam_name, e.category_id as subject, e.duration,
             ta.end_datetime as end_date, ta.max_attempts,
             COUNT(ea.attempt_id) as attempts_used,
             ta.max_attempts - COUNT(ea.attempt_id) as remaining_attempts
      FROM test_assignments ta
      JOIN exams e ON ta.exam_id = e.exam_id
      LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id AND ea.user_id = ?
      WHERE ta.user_id = ?
      AND (ta.end_datetime IS NULL OR ta.end_datetime >= CURDATE())
      GROUP BY e.exam_id, e.exam_name, e.category_id, e.duration, ta.end_datetime, ta.max_attempts
      HAVING COUNT(ea.attempt_id) < ta.max_attempts
      ORDER BY ta.end_datetime ASC
    `,

    // Get completed exams
    getCompletedExams: `
      SELECT e.exam_id, e.exam_name, e.category_id as subject,
             MAX(ea.total_score) as highest_score,
             COUNT(ea.attempt_id) as attempts_used,
             MAX(ea.end_time) as completion_date
      FROM exam_attempts ea
      JOIN exams e ON ea.exam_id = e.exam_id
      WHERE ea.user_id = ? AND ea.status = 'completed'
      GROUP BY e.exam_id, e.exam_name, e.category_id
      ORDER BY MAX(ea.end_time) DESC
    `
  },

  // ==================== ADMIN QUERIES ====================

  admin: {
    // Get all classes
    getAllClasses: `
      SELECT * FROM classes
      ORDER BY name ASC
    `,

    // Check if class exists
    checkClassExists: `
      SELECT * FROM classes WHERE name = ?
    `,

    // Insert new class
    insertClass: `
      INSERT INTO classes (name, description, is_active, created_at)
      VALUES (?, ?, ?, NOW())
    `,

    // Get class by ID
    getClassById: `
      SELECT * FROM classes WHERE id = ?
    `,

    // Update class
    updateClass: `
      UPDATE classes
      SET name = ?, description = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `,

    // Check class relations before delete
    checkClassRelations: `
      SELECT COUNT(*) as count FROM sections WHERE class_id = ?
    `,

    // Delete class
    deleteClass: `
      DELETE FROM classes WHERE id = ?
    `,

    // Get exam attempts count
    getExamAttemptsCount: `
      SELECT COUNT(*) as count FROM exam_attempts
    `,

    // Get recent activity for admin
    getAdminRecentActivity: `
      SELECT 'user_registered' as type, username as subject, created_at as timestamp
      FROM users
      ORDER BY created_at DESC
      LIMIT 5
    `,

    // Get active users count
    getActiveUsersCount: `
      SELECT COUNT(*) as count FROM active_sessions WHERE is_active = 1
    `,

    // Get recent user registrations
    getRecentRegistrations: `
      SELECT 'user_registered' as type, username as subject, created_at as timestamp
      FROM users
      ORDER BY created_at DESC
      LIMIT ?
    `,

    // Get recent test creations
    getRecentTestCreations: `
      SELECT 'test_created' as type, exam_name as subject, created_at as timestamp
      FROM exams
      ORDER BY created_at DESC
      LIMIT ?
    `,

    // Get question type statistics
    getQuestionTypeStats: `
      SELECT
        question_type,
        COUNT(*) as count
      FROM questions
      GROUP BY question_type
    `,

    // Get questions with filters
    getQuestionsWithFilters: `
      SELECT
        q.*,
        GROUP_CONCAT(DISTINCT c.name) as category_name,
        GROUP_CONCAT(DISTINCT c.category_id) as category_ids,
        e.exam_name,
        s.section_name
      FROM questions q
      LEFT JOIN sections s ON q.section_id = s.section_id
      LEFT JOIN exams e ON s.exam_id = e.exam_id
      LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
      LEFT JOIN categories c ON qcm.category_id = c.category_id
      GROUP BY q.question_id
      ORDER BY q.created_at DESC
      LIMIT ? OFFSET ?
    `
  },

  // ==================== QUERY LOGS & MONITORING ====================

  queryLogs: {
    // Get slow queries (duration > 1000ms)
    getSlowQueries: `
      SELECT ql.*, u.username as user_username
      FROM query_logs ql
      LEFT JOIN users u ON ql.user_id = u.id
      WHERE ql.duration > 1000
      ORDER BY ql.duration DESC
      LIMIT 10
    `,

    // Get query logs with filters
    getQueryLogsWithFilters: `
      SELECT ql.*, u.username as user_username
      FROM query_logs ql
      LEFT JOIN users u ON ql.user_id = u.id
      ORDER BY ql.created_at DESC
      LIMIT ? OFFSET ?
    `
  },

  // ==================== STUDENT ROUTES QUERIES ====================

  studentRoutes: {
    // Check table existence
    checkTableExists: `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
      AND TABLE_NAME = ?
    `,

    // Get test assignments for student
    getTestAssignments: `
      SELECT ta.*, e.exam_name, e.duration,
            (SELECT COUNT(*) FROM exam_attempts
              WHERE exam_id = e.exam_id AND user_id = ?) as attempts_used
      FROM test_assignments ta
      JOIN exams e ON ta.exam_id = e.exam_id
      WHERE (ta.user_id = ? OR
            ta.group_id IN (SELECT group_id FROM group_members WHERE user_id = ?))
      AND ta.is_active = 1
      AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
    `,

    // Get assignments for student
    getAssignments: `
      SELECT a.*, s.name as subject_name
      FROM assignments a
      LEFT JOIN subjects s ON a.subject_id = s.id
      WHERE (a.student_id = ? OR a.class_id IN (?))
      AND a.due_date >= NOW()
      ORDER BY a.due_date ASC
    `,

    // Get learning plans
    getLearningPlans: `
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      WHERE p.status = 'published'
      AND (p.subject_id IN (?) OR p.class_id IN (?))
      ORDER BY p.created_at DESC
    `
  },

  // ==================== COMMON UTILITY QUERIES ====================

  utilities: {
    // Get current database name
    getCurrentDatabase: `
      SELECT DATABASE() as current_database
    `,

    // Check if column exists in table
    checkColumnExists: `
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = ?
      AND column_name = ?
    `,

    // Get table structure
    getTableStructure: `
      DESCRIBE ?
    `,

    // Get all tables in database
    getAllTables: `
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      ORDER BY table_name
    `
  }
};

/**
 * USAGE EXAMPLES:
 *
 * // Import the queries
 * const SQLQueries = require('./config/sql-queries');
 *
 * // Use in your controller/route
 * const [students] = await db.query(SQLQueries.students.getAllWithPagination, [limit, offset]);
 *
 * // Use with filters
 * const [filteredStudents] = await db.query(SQLQueries.students.getWithFilters, [
 *   filters.class, filters.class,
 *   filters.section, filters.section,
 *   filters.session, filters.session,
 *   filters.gender, filters.gender,
 *   filters.stream, filters.stream,
 *   filters.bpl, filters.bpl,
 *   filters.disability, filters.disability,
 *   searchTerm, searchTerm, searchTerm, searchTerm, searchTerm,
 *   limit, offset
 * ]);
 *
 * // Use dashboard queries
 * const [stats] = await db.query(SQLQueries.principalDashboard.dashboardStats);
 *
 * // Use infrastructure queries
 * const [roomInfo] = await db.query(SQLQueries.infrastructure.getClassroomDetails, [roomId, roomId, `Room ${roomId}`]);
 */

module.exports = SQLQueries;
