// Class Management
exports.getClasses = async (req, res) => {
  try {
    const [classes] = await db.query(`
      SELECT * FROM classes 
      ORDER BY name ASC
    `);
    
    res.render('admin/classes/index', {
      layout: 'layouts/admin',
      classes,
      pageTitle: 'Manage Classes',
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching classes:', error);
    res.render('error', { 
      message: 'Failed to fetch classes', 
      error,
      layout: 'layouts/admin',
      user: req.session.user
    });
  }
};

exports.getAddClass = (req, res) => {
  res.render('admin/classes/add', {
    layout: 'layouts/admin',
    pageTitle: 'Add New Class',
    user: req.session.user
  });
};

exports.postAddClass = async (req, res) => {
  try {
    const { name, description, is_active } = req.body;
    
    // Validate input
    if (!name) {
      return res.status(400).json({ 
        success: false, 
        message: 'Class name is required' 
      });
    }
    
    // Check if class already exists
    const [existingClasses] = await db.query(
      'SELECT * FROM classes WHERE name = ?', 
      [name]
    );
    
    if (existingClasses.length > 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'A class with this name already exists' 
      });
    }
    
    // Insert new class
    const result = await db.query(
      'INSERT INTO classes (name, description, is_active, created_at) VALUES (?, ?, ?, NOW())',
      [name, description || null, is_active]
    );
    
    res.status(201).json({ 
      success: true, 
      message: 'Class created successfully',
      classId: result[0].insertId
    });
  } catch (error) {
    console.error('Error adding class:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create class' 
    });
  }
};

exports.getEditClass = async (req, res) => {
  try {
    const classId = req.params.id;
    
    const [classes] = await db.query(
      'SELECT * FROM classes WHERE id = ?', 
      [classId]
    );
    
    if (classes.length === 0) {
      return res.redirect('/admin/classes');
    }
    
    res.render('admin/classes/edit', {
      layout: 'layouts/admin',
      pageTitle: 'Edit Class',
      classData: classes[0],
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching class:', error);
    res.render('error', { 
      message: 'Failed to fetch class details', 
      error,
      layout: 'layouts/admin',
      user: req.session.user
    });
  }
};

exports.postEditClass = async (req, res) => {
  try {
    const classId = req.params.id;
    const { name, description, is_active } = req.body;
    
    // Validate input
    if (!name) {
      return res.status(400).json({ 
        success: false, 
        message: 'Class name is required' 
      });
    }
    
    // Check if name already exists (excluding current class)
    const [existingClasses] = await db.query(
      'SELECT * FROM classes WHERE name = ? AND id != ?', 
      [name, classId]
    );
    
    if (existingClasses.length > 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'A class with this name already exists' 
      });
    }
    
    // Update class
    await db.query(
      'UPDATE classes SET name = ?, description = ?, is_active = ?, updated_at = NOW() WHERE id = ?',
      [name, description || null, is_active, classId]
    );
    
    res.json({ 
      success: true, 
      message: 'Class updated successfully' 
    });
  } catch (error) {
    console.error('Error updating class:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update class' 
    });
  }
};

exports.deleteClass = async (req, res) => {
  try {
    const classId = req.params.id;
    
    // Check if class is used in any relations
    const [sections] = await db.query(
      'SELECT COUNT(*) as count FROM sections WHERE class_id = ?',
      [classId]
    );
    
    if (sections[0].count > 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Cannot delete this class because it has sections associated with it'
      });
    }
    
    // Delete class
    await db.query('DELETE FROM classes WHERE id = ?', [classId]);
    
    res.json({ 
      success: true, 
      message: 'Class deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting class:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to delete class' 
    });
  }
}; 