const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Set up storage for uploaded files
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../public/uploads/resources');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Create unique filename
    const uniqueFilename = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// Create multer upload instance
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10 MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept documents, images, PDFs, and common file types
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());

    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only documents, images, and PDFs are allowed'));
    }
  }
}).array('resources', 10); // Allow up to 10 files

// Get all instruction plans
exports.getInstructionPlans = async (req, res) => {
  try {
    // Get all instruction plans with related data
    const [plans] = await db.query(`
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      ORDER BY p.created_at DESC
    `);

    res.render('admin/instruction-plans/index', {
      title: 'Instruction Plans',
      layout: 'admin',
      plans,
      currentPage: 'instruction-plans',
      isAdmin: true
    });
  } catch (error) {
    console.error('Error fetching instruction plans:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plans',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Get form to add a new instruction plan
exports.getAddInstructionPlan = async (req, res) => {
  try {
    // Get subjects for dropdown
    const [subjects] = await db.query(`SELECT * FROM subjects ORDER BY name`);
    
    // Get teachers for dropdown
    const [teachers] = await db.query(`
      SELECT id, full_name FROM users
      WHERE role = 'teacher' AND is_active = 1
      ORDER BY full_name
    `);

    res.render('admin/instruction-plans/add', {
      title: 'Add Instruction Plan',
      layout: 'admin',
      subjects,
      teachers,
      currentPage: 'instruction-plans',
      isAdmin: true
    });
  } catch (error) {
    console.error('Error loading form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading form',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Add a new instruction plan
exports.postAddInstructionPlan = async (req, res) => {
  upload(req, res, async function (err) {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    try {
      const { title, description, subject_id, teacher_id, status } = req.body;

      // Validate input
      if (!title || !subject_id || !teacher_id) {
        // Delete uploaded files if validation fails
        if (req.files && req.files.length > 0) {
          req.files.forEach(file => {
            fs.unlinkSync(file.path);
          });
        }

        return res.status(400).json({
          success: false,
          message: 'Title, subject, and teacher are required'
        });
      }

      // Begin transaction
      await db.query('START TRANSACTION');

      // Insert instruction plan
      const [result] = await db.query(`
        INSERT INTO instruction_plans (title, description, subject_id, teacher_id, status)
        VALUES (?, ?, ?, ?, ?)
      `, [
        title,
        description || null,
        subject_id,
        teacher_id,
        status || 'draft'
      ]);

      const planId = result.insertId;

      // Process uploaded files
      if (req.files && req.files.length > 0) {
        const resourceValues = [];
        const resourceParams = [];

        req.files.forEach(file => {
          const resourceName = req.body[`resource_name_${file.originalname}`] || file.originalname;
          const resourcePath = `/uploads/resources/${file.filename}`;

          resourceValues.push('(?, ?, ?, ?)');
          resourceParams.push(
            planId,
            resourceName,
            'file',
            resourcePath
          );
        });

        // Insert resources
        if (resourceValues.length > 0) {
          await db.query(`
            INSERT INTO instruction_plan_resources
            (plan_id, resource_name, resource_type, resource_path)
            VALUES ${resourceValues.join(', ')}
          `, resourceParams);
        }
      }

      // Process text-based resources
      if (req.body.text_resources) {
        let textResources = [];

        try {
          textResources = JSON.parse(req.body.text_resources);
        } catch (e) {
          console.error('Error parsing text resources:', e);
        }

        if (textResources.length > 0) {
          const textResourceValues = [];
          const textResourceParams = [];

          textResources.forEach(resource => {
            textResourceValues.push('(?, ?, ?, ?)');
            textResourceParams.push(
              planId,
              resource.name,
              'text',
              resource.content
            );
          });

          // Insert text resources
          if (textResourceValues.length > 0) {
            await db.query(`
              INSERT INTO instruction_plan_resources
              (plan_id, resource_name, resource_type, resource_content)
              VALUES ${textResourceValues.join(', ')}
            `, textResourceParams);
          }
        }
      }

      // Process link resources
      if (req.body.link_resources) {
        let linkResources = [];

        try {
          linkResources = JSON.parse(req.body.link_resources);
        } catch (e) {
          console.error('Error parsing link resources:', e);
        }

        if (linkResources.length > 0) {
          const linkResourceValues = [];
          const linkResourceParams = [];

          linkResources.forEach(resource => {
            linkResourceValues.push('(?, ?, ?, ?)');
            linkResourceParams.push(
              planId,
              resource.name,
              'link',
              resource.url
            );
          });

          // Insert link resources
          if (linkResourceValues.length > 0) {
            await db.query(`
              INSERT INTO instruction_plan_resources
              (plan_id, resource_name, resource_type, resource_path)
              VALUES ${linkResourceValues.join(', ')}
            `, linkResourceParams);
          }
        }
      }

      // Commit transaction
      await db.query('COMMIT');

      res.status(201).json({
        success: true,
        message: 'Instruction plan added successfully',
        planId
      });
    } catch (error) {
      // Rollback transaction on error
      await db.query('ROLLBACK');

      console.error('Error adding instruction plan:', error);
      res.status(500).json({
        success: false,
        message: 'Error adding instruction plan'
      });
    }
  });
};

// View instruction plan details
exports.getInstructionPlanDetails = async (req, res) => {
  try {
    const planId = req.params.id;

    // Get plan details
    const [plans] = await db.query(`
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      WHERE p.id = ?
    `, [planId]);

    if (plans.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Instruction plan not found',
        error: { status: 404, stack: 'Instruction plan not found' },
        layout: 'admin'
      });
    }

    // Get resources
    const [resources] = await db.query(`
      SELECT * FROM instruction_plan_resources
      WHERE plan_id = ?
      ORDER BY created_at
    `, [planId]);

    res.render('admin/instruction-plans/view', {
      title: 'Instruction Plan Details',
      layout: 'admin',
      plan: plans[0],
      resources,
      currentPage: 'instruction-plans',
      isAdmin: true
    });
  } catch (error) {
    console.error('Error fetching instruction plan:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plan',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Get form to edit an instruction plan
exports.getEditInstructionPlan = async (req, res) => {
  try {
    const planId = req.params.id;

    // Get plan details
    const [plans] = await db.query(`SELECT * FROM instruction_plans WHERE id = ?`, [planId]);

    if (plans.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Instruction plan not found',
        error: { status: 404, stack: 'Instruction plan not found' },
        layout: 'admin'
      });
    }

    // Get resources
    const [resources] = await db.query(`
      SELECT * FROM instruction_plan_resources
      WHERE plan_id = ?
      ORDER BY created_at
    `, [planId]);

    // Get subjects for dropdown
    const [subjects] = await db.query(`SELECT * FROM subjects ORDER BY name`);
    
    // Get teachers for dropdown
    const [teachers] = await db.query(`
      SELECT id, full_name FROM users
      WHERE role = 'teacher' AND is_active = 1
      ORDER BY full_name
    `);

    res.render('admin/instruction-plans/edit', {
      title: 'Edit Instruction Plan',
      layout: 'admin',
      plan: plans[0],
      resources,
      subjects,
      teachers,
      currentPage: 'instruction-plans',
      isAdmin: true
    });
  } catch (error) {
    console.error('Error loading edit form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading edit form',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Update an instruction plan
exports.postEditInstructionPlan = async (req, res) => {
  upload(req, res, async function (err) {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    try {
      const planId = req.params.id;
      const { title, description, subject_id, teacher_id, status } = req.body;

      // Validate input
      if (!title || !subject_id || !teacher_id) {
        // Delete uploaded files if validation fails
        if (req.files && req.files.length > 0) {
          req.files.forEach(file => {
            fs.unlinkSync(file.path);
          });
        }

        return res.status(400).json({
          success: false,
          message: 'Title, subject, and teacher are required'
        });
      }

      // Begin transaction
      await db.query('START TRANSACTION');

      // Update instruction plan
      await db.query(`
        UPDATE instruction_plans
        SET title = ?, description = ?, subject_id = ?, teacher_id = ?, status = ?
        WHERE id = ?
      `, [
        title,
        description || null,
        subject_id,
        teacher_id,
        status || 'draft',
        planId
      ]);

      // Delete resources that should be removed
      if (req.body.delete_resources) {
        const resourcesToDelete = req.body.delete_resources.split(',').map(id => parseInt(id));

        if (resourcesToDelete.length > 0) {
          // Get file paths before deleting
          const [filesToDelete] = await db.query(`
            SELECT resource_path FROM instruction_plan_resources
            WHERE id IN (${resourcesToDelete.join(',')}) AND resource_type = 'file'
          `);

          // Delete from database
          await db.query(`
            DELETE FROM instruction_plan_resources
            WHERE id IN (${resourcesToDelete.join(',')})
          `);

          // Delete files from disk
          filesToDelete.forEach(file => {
            if (file.resource_path) {
              const filePath = path.join(__dirname, '../public', file.resource_path);
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
              }
            }
          });
        }
      }

      // Process uploaded files
      if (req.files && req.files.length > 0) {
        const resourceValues = [];
        const resourceParams = [];

        req.files.forEach(file => {
          const resourceName = req.body[`resource_name_${file.originalname}`] || file.originalname;
          const resourcePath = `/uploads/resources/${file.filename}`;

          resourceValues.push('(?, ?, ?, ?)');
          resourceParams.push(
            planId,
            resourceName,
            'file',
            resourcePath
          );
        });

        // Insert resources
        if (resourceValues.length > 0) {
          await db.query(`
            INSERT INTO instruction_plan_resources
            (plan_id, resource_name, resource_type, resource_path)
            VALUES ${resourceValues.join(', ')}
          `, resourceParams);
        }
      }

      // Process text-based resources
      if (req.body.text_resources) {
        let textResources = [];

        try {
          textResources = JSON.parse(req.body.text_resources);
        } catch (e) {
          console.error('Error parsing text resources:', e);
        }

        if (textResources.length > 0) {
          const textResourceValues = [];
          const textResourceParams = [];

          textResources.forEach(resource => {
            textResourceValues.push('(?, ?, ?, ?)');
            textResourceParams.push(
              planId,
              resource.name,
              'text',
              resource.content
            );
          });

          // Insert text resources
          if (textResourceValues.length > 0) {
            await db.query(`
              INSERT INTO instruction_plan_resources
              (plan_id, resource_name, resource_type, resource_content)
              VALUES ${textResourceValues.join(', ')}
            `, textResourceParams);
          }
        }
      }

      // Process link resources
      if (req.body.link_resources) {
        let linkResources = [];

        try {
          linkResources = JSON.parse(req.body.link_resources);
        } catch (e) {
          console.error('Error parsing link resources:', e);
        }

        if (linkResources.length > 0) {
          const linkResourceValues = [];
          const linkResourceParams = [];

          linkResources.forEach(resource => {
            linkResourceValues.push('(?, ?, ?, ?)');
            linkResourceParams.push(
              planId,
              resource.name,
              'link',
              resource.url
            );
          });

          // Insert link resources
          if (linkResourceValues.length > 0) {
            await db.query(`
              INSERT INTO instruction_plan_resources
              (plan_id, resource_name, resource_type, resource_path)
              VALUES ${linkResourceValues.join(', ')}
            `, linkResourceParams);
          }
        }
      }

      // Commit transaction
      await db.query('COMMIT');

      res.json({
        success: true,
        message: 'Instruction plan updated successfully'
      });
    } catch (error) {
      // Rollback transaction on error
      await db.query('ROLLBACK');

      console.error('Error updating instruction plan:', error);
      res.status(500).json({
        success: false,
        message: 'Error updating instruction plan'
      });
    }
  });
};

// Delete an instruction plan
exports.deleteInstructionPlan = async (req, res) => {
  try {
    const planId = req.params.id;

    // Get file resources
    const [fileResources] = await db.query(`
      SELECT resource_path FROM instruction_plan_resources
      WHERE plan_id = ? AND resource_type = 'file'
    `, [planId]);

    // Begin transaction
    await db.query('START TRANSACTION');

    // Delete resources
    await db.query(`DELETE FROM instruction_plan_resources WHERE plan_id = ?`, [planId]);

    // Delete instruction plan
    await db.query(`DELETE FROM instruction_plans WHERE id = ?`, [planId]);

    // Commit transaction
    await db.query('COMMIT');

    // Delete files from disk
    fileResources.forEach(file => {
      if (file.resource_path) {
        const filePath = path.join(__dirname, '../public', file.resource_path);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
    });

    res.json({
      success: true,
      message: 'Instruction plan deleted successfully'
    });
  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');

    console.error('Error deleting instruction plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting instruction plan'
    });
  }
};
