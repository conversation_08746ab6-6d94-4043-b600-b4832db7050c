/**
 * Admin Notification Controller
 */
const db = require('../config/database');

module.exports = {
    /**
     * Display admin notifications management page
     */
    index: async (req, res) => {
        try {
            // Get all admin notifications
            const [notifications] = await db.query(`
                SELECT an.*, u.username as creator_name
                FROM admin_notifications an
                JOIN users u ON an.created_by = u.id
                ORDER BY an.created_at DESC
            `);

            res.render('admin/notifications/index', {
                title: 'Admin Notifications',
                pageTitle: 'Admin Notifications',
                notifications,
                layout: 'admin',
                currentPage: 'admin-notifications'
            });
        } catch (error) {
            console.error('Error loading admin notifications:', error);
            req.session.flashError = 'Error loading admin notifications';
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Display form to create a new admin notification
     */
    createForm: async (req, res) => {
        try {
            res.render('admin/notifications/create', {
                title: 'Create Admin Notification',
                pageTitle: 'Create Admin Notification',
                layout: 'admin',
                currentPage: 'admin-notifications'
            });
        } catch (error) {
            console.error('Error loading create notification form:', error);
            req.session.flashError = 'Error loading create notification form';
            res.redirect('/admin/notifications');
        }
    },

    /**
     * Create a new admin notification
     */
    create: async (req, res) => {
        try {
            const { title, message, startDate, endDate, isBroadcast } = req.body;
            const createdBy = req.session.userId;
            const showAsBroadcast = isBroadcast === 'on';

            // Validate required fields
            if (!title || !message) {
                req.session.flashError = 'Title and message are required';
                return res.redirect('/admin/notifications/create');
            }

            // Start a transaction
            await db.query('START TRANSACTION');

            try {
                // Create admin notification
                const [result] = await db.query(`
                    INSERT INTO admin_notifications (title, message, created_by, start_date, end_date, is_broadcast)
                    VALUES (?, ?, ?, ?, ?, ?)
                `, [title, message, createdBy, startDate || null, endDate || null, showAsBroadcast]);

                const adminNotificationId = result.insertId;

                // Get all users to create individual notifications for them
                const [users] = await db.query(`
                    SELECT id FROM users
                `);

                // Create individual notifications for each user
                for (const user of users) {
                    await db.query(`
                        INSERT INTO notifications (user_id, title, message, type, link, is_read, is_broadcast, created_at)
                        VALUES (?, ?, ?, 'admin', ?, 0, ?, NOW())
                    `, [user.id, title, message, `/notifications`, showAsBroadcast]);
                }

                // Commit the transaction
                await db.query('COMMIT');

                req.session.flashSuccess = 'Notification created successfully';
                res.redirect('/admin/notifications');
            } catch (error) {
                // Rollback in case of error
                await db.query('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error creating admin notification:', error);
            req.session.flashError = 'Error creating admin notification';
            res.redirect('/admin/notifications/create');
        }
    },

    /**
     * Display form to edit an admin notification
     */
    editForm: async (req, res) => {
        try {
            const notificationId = parseInt(req.params.id);

            // Get notification details
            const [notifications] = await db.query(`
                SELECT * FROM admin_notifications
                WHERE notification_id = ?
            `, [notificationId]);

            if (notifications.length === 0) {
                req.session.flashError = 'Notification not found';
                return res.redirect('/admin/notifications');
            }

            const notification = notifications[0];

            res.render('admin/notifications/edit', {
                title: 'Edit Admin Notification',
                pageTitle: 'Edit Admin Notification',
                notification,
                layout: 'admin',
                currentPage: 'admin-notifications'
            });
        } catch (error) {
            console.error('Error loading edit notification form:', error);
            req.session.flashError = 'Error loading edit notification form';
            res.redirect('/admin/notifications');
        }
    },

    /**
     * Update an admin notification
     */
    update: async (req, res) => {
        try {
            const notificationId = parseInt(req.params.id);
            const { title, message, isActive, startDate, endDate } = req.body;

            // Validate required fields
            if (!title || !message) {
                req.session.flashError = 'Title and message are required';
                return res.redirect(`/admin/notifications/${notificationId}/edit`);
            }

            // Start a transaction
            await db.query('START TRANSACTION');

            try {
                // Get the original notification to check if title or message changed
                const [originalNotification] = await db.query(`
                    SELECT title, message, created_at FROM admin_notifications WHERE notification_id = ?
                `, [notificationId]);

                if (originalNotification.length === 0) {
                    await db.query('ROLLBACK');
                    req.session.flashError = 'Notification not found';
                    return res.redirect('/admin/notifications');
                }

                const original = originalNotification[0];
                const titleChanged = original.title !== title;
                const messageChanged = original.message !== message;

                // Update admin notification
                await db.query(`
                    UPDATE admin_notifications
                    SET title = ?, message = ?, is_active = ?, start_date = ?, end_date = ?
                    WHERE notification_id = ?
                `, [title, message, isActive ? 1 : 0, startDate || null, endDate || null, notificationId]);

                // If title or message changed, update user notifications
                if (titleChanged || messageChanged) {
                    // Update all user notifications with the same timestamp (approximate match)
                    await db.query(`
                        UPDATE notifications
                        SET title = ?, message = ?
                        WHERE type = 'admin' AND DATE(created_at) = DATE(?)
                    `, [title, message, original.created_at]);
                }

                // Commit the transaction
                await db.query('COMMIT');

                req.session.flashSuccess = 'Notification updated successfully';
                res.redirect('/admin/notifications');
            } catch (error) {
                // Rollback in case of error
                await db.query('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error updating admin notification:', error);
            req.session.flashError = 'Error updating admin notification';
            res.redirect(`/admin/notifications/${req.params.id}/edit`);
        }
    },

    /**
     * Delete an admin notification
     */
    delete: async (req, res) => {
        try {
            const notificationId = parseInt(req.params.id);

            // Start a transaction
            await db.query('START TRANSACTION');

            try {
                // Get the notification to find related user notifications
                const [notification] = await db.query(`
                    SELECT created_at FROM admin_notifications WHERE notification_id = ?
                `, [notificationId]);

                if (notification.length > 0) {
                    const createdAt = notification[0].created_at;

                    // Delete related user notifications
                    await db.query(`
                        DELETE FROM notifications
                        WHERE type = 'admin' AND DATE(created_at) = DATE(?)
                    `, [createdAt]);
                }

                // Delete admin notification
                await db.query(`
                    DELETE FROM admin_notifications
                    WHERE notification_id = ?
                `, [notificationId]);

                // Commit the transaction
                await db.query('COMMIT');

                req.session.flashSuccess = 'Notification deleted successfully';
                res.redirect('/admin/notifications');
            } catch (error) {
                // Rollback in case of error
                await db.query('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error deleting admin notification:', error);
            req.session.flashError = 'Error deleting admin notification';
            res.redirect('/admin/notifications');
        }
    },

    /**
     * Toggle notification active status
     */
    toggleActive: async (req, res) => {
        try {
            const notificationId = parseInt(req.params.id);

            // Start a transaction
            await db.query('START TRANSACTION');

            try {
                // Get current status and created_at
                const [notifications] = await db.query(`
                    SELECT is_active, created_at FROM admin_notifications
                    WHERE notification_id = ?
                `, [notificationId]);

                if (notifications.length === 0) {
                    await db.query('ROLLBACK');
                    return res.status(404).json({
                        success: false,
                        message: 'Notification not found'
                    });
                }

                const isActive = notifications[0].is_active;
                const createdAt = notifications[0].created_at;
                const newStatus = isActive ? 0 : 1;

                // Toggle status
                await db.query(`
                    UPDATE admin_notifications
                    SET is_active = ?
                    WHERE notification_id = ?
                `, [newStatus, notificationId]);

                // If activating, make sure user notifications exist
                if (newStatus === 1) {
                    // Check if user notifications exist
                    const [userNotifications] = await db.query(`
                        SELECT COUNT(*) as count FROM notifications
                        WHERE type = 'admin' AND DATE(created_at) = DATE(?)
                    `, [createdAt]);

                    // If no user notifications exist, create them
                    if (userNotifications[0].count === 0) {
                        // Get notification details
                        const [details] = await db.query(`
                            SELECT title, message FROM admin_notifications
                            WHERE notification_id = ?
                        `, [notificationId]);

                        if (details.length > 0) {
                            const { title, message } = details[0];

                            // Get all users
                            const [users] = await db.query(`
                                SELECT id FROM users
                            `);

                            // Create individual notifications for each user
                            for (const user of users) {
                                await db.query(`
                                    INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
                                    VALUES (?, ?, ?, 'admin', ?, 0, ?)
                                `, [user.id, title, message, `/notifications`, createdAt]);
                            }
                        }
                    }
                }

                // Commit the transaction
                await db.query('COMMIT');

                res.json({
                    success: true,
                    isActive: !isActive
                });
            } catch (error) {
                // Rollback in case of error
                await db.query('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error toggling notification status:', error);
            res.status(500).json({
                success: false,
                message: 'Error toggling notification status'
            });
        }
    },

    /**
     * Get active admin notifications for login page
     */
    getActiveNotifications: async (req, res) => {
        try {
            // Get active notifications
            const [notifications] = await db.query(`
                SELECT * FROM admin_notifications
                WHERE is_active = 1
                AND (start_date IS NULL OR start_date <= NOW())
                AND (end_date IS NULL OR end_date >= NOW())
                ORDER BY created_at DESC
            `);

            res.json({
                success: true,
                notifications
            });
        } catch (error) {
            console.error('Error getting active notifications:', error);
            res.status(500).json({
                success: false,
                message: 'Error getting active notifications'
            });
        }
    }
};
