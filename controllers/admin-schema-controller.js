/**
 * Admin Schema Controller
 *
 * This controller handles the database schema diagram view
 * showing entity relationships between different tables
 */

const db = require('../config/database');

/**
 * Get the database schema diagram page
 */
exports.getSchemaPage = async (req, res, next) => {
    try {
        // Get all tables in the database
        const [tables] = await db.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'exam_prep_platform'
            ORDER BY table_name
        `);

        // Get all foreign keys to understand relationships
        const [foreignKeys] = await db.query(`
            SELECT
                table_name,
                column_name,
                referenced_table_name,
                referenced_column_name
            FROM
                information_schema.key_column_usage
            WHERE
                referenced_table_name IS NOT NULL
                AND table_schema = 'exam_prep_platform'
            ORDER BY table_name, column_name
        `);

        // Get table columns for each table
        const tableColumns = {};
        for (const table of tables) {
            const tableName = table.table_name;
            const [columns] = await db.query(`
                SELECT
                    column_name,
                    column_type,
                    is_nullable,
                    column_key,
                    column_default,
                    extra
                FROM
                    information_schema.columns
                WHERE
                    table_schema = 'exam_prep_platform'
                    AND table_name = ?
                ORDER BY ordinal_position
            `, [tableName]);

            tableColumns[tableName] = columns;
        }

        // Group tables by category for better organization
        const tableCategories = {
            'User Management': [
                'users', 'roles', 'groups', 'group_members', 'active_sessions', 'role_permissions',
                'permissions', 'users_old', 'user_notification_settings', 'user_performance'
            ],
            'Academic Structure': [
                'classes', 'subjects', 'trades', 'classrooms', 'student_classes', 'student_subjects', 'class_sections',
                'class_schedule', 'class_weekly_lectures', 'student_classrooms', 'subject_category',
                'subject_class_assignment', 'subject_syllabus', 'subject_trade_combinations',
                'teacher_subject_class_view', 'teacher_subject_eligibility', 'rooms'
            ],
            'Teacher Management': [
                'teacher_classes', 'teacher_subjects', 'class_incharge', 'teacher_specialization',
                'teacher_lectures', 'teacher_practicals', 'teacher_syllabus', 'teacher_weekly_lectures',
                'lecture_instruction_plan', 'lecture_schedule', 'lecture_syllabus_topic', 'lab_incharge'
            ],
            'Instruction Plans': [
                'instruction_plans', 'instruction_plan_resources', 'plan_collaborators',
                'daily_instruction_plan', 'instruction_plan_completions', 'instruction_plan_content',
                'instruction_plan_views', 'student_instruction_plans', 'student_plan_completions',
                'syllabus_progress', 'syllabus_subtopics', 'syllabus_topics'
            ],
            'Exams & Questions': [
                'exams', 'sections', 'questions', 'options', 'exam_attempts', 'user_answers',
                'categories', 'question_category_mappings', 'question_categories_view', 'question_images',
                'exam_assignments', 'group_exam_assignments', 'student_test_attempts', 'test_assignments',
                'essays'
            ],
            'IT Management': [
                'it_inventory', 'it_issues', 'inventory_items', 'inventory_categories', 'inventory_transactions',
                'procurement_items', 'vendor_quotation_items', 'hardware_condition', 'hardware_parts',
                'procurement_committee_members', 'procurement_documents', 'procurement_requests', 'procurement_vendors',
                'repair_history', 'repair_vendors'
            ],
            'System': [
                'logs', 'notifications', 'system_settings', 'holiday_calendar', 'departments',
                'access_requests', 'activity_log', 'admin_notifications', 'calendar_events',
                'email_templates', 'help_articles', 'help_article_categories', 'help_categories',
                'help_feedback', 'issue_attachments', 'issue_comments', 'issue_history',
                'messages', 'message_status', 'query_error_logs', 'query_logs', 'reports',
                'sessions', 'site_settings', 'attempt_statistics', 'chat_messages', 'group_invites', 'jsvalues'
            ],
            'Assignments & Practicals': [
                'assignments', 'assignment_submissions', 'student_assignments',
                'practicals', 'practical_records', 'practical_topics', 'student_practical_records',
                'labs', 'topics'
            ]
        };

        // Create a map of table relationships
        const relationships = {};
        foreignKeys.forEach(fk => {
            if (!relationships[fk.table_name]) {
                relationships[fk.table_name] = [];
            }
            relationships[fk.table_name].push({
                fromTable: fk.table_name,
                fromColumn: fk.column_name,
                toTable: fk.referenced_table_name,
                toColumn: fk.referenced_column_name
            });
        });

        // Render the schema page
        res.render('admin/schema', {
            title: 'Database Schema',
            pageTitle: 'Database Schema & Entity Relationships',
            currentPage: 'schema',
            tables,
            tableColumns,
            relationships,
            tableCategories
        });
    } catch (error) {
        console.error('Error loading database schema:', error);
        next(error);
    }
};
