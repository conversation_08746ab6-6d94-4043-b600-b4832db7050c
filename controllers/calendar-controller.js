const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

/**
 * Get day timetable data for a specific date
 * This function fetches all events for a specific date based on user role
 */
exports.getDayTimetable = async (req, res) => {
  try {
    const { date } = req.query;
    const userId = req.session.userId;
    const userRole = req.session.userRole;

    // Validate input
    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date is required'
      });
    }

    // Initialize response data
    const responseData = {
      success: true,
      date,
      formattedDate: formatDate(new Date(date)),
      lectures: [],
      tests: [],
      assignments: [],
      practicals: [],
      holidays: []
    };

    // Get holidays for this date
    const [holidays] = await db.query(`
      SELECT id, holiday_date, description, holiday_type
      FROM holiday_calendar
      WHERE holiday_date = ? AND is_active = 1
    `, [date]);

    responseData.holidays = holidays;

    // If it's a teacher or admin
    if (userRole === 'teacher' || userRole === 'admin') {
      // Get teacher's lectures for this date
      const [lectures] = await db.query(`
        SELECT
          l.id, l.teacher_id, l.date, l.start_time, l.end_time,
          l.subject_name, l.topic, l.class_name, l.section_display,
          l.grade, l.stream, l.location, l.status,
          u.full_name as teacher_name
        FROM teacher_lectures l
        LEFT JOIN users u ON l.teacher_id = u.id
        WHERE l.date = ? AND (l.teacher_id = ? OR ? = 'admin')
        ORDER BY l.start_time
      `, [date, userId, userRole]);

      responseData.lectures = lectures;

      // Get tests assigned by this teacher for this date
      const [tests] = await db.query(`
        SELECT
          e.exam_id, e.exam_name, ta.end_datetime,
          COUNT(DISTINCT ta.user_id) as assigned_students,
          (SELECT COUNT(*) FROM questions q JOIN sections s ON q.section_id = s.section_id WHERE s.exam_id = e.exam_id) as total_questions,
          e.duration
        FROM test_assignments ta
        JOIN exams e ON ta.exam_id = e.exam_id
        WHERE DATE(ta.end_datetime) = ?
        AND (e.created_by = ? OR ? = 'admin')
        GROUP BY e.exam_id
        ORDER BY ta.end_datetime
      `, [date, userId, userRole]);

      responseData.tests = tests;

      // Get practicals scheduled by this teacher for this date
      try {
        const [practicals] = await db.query(`
          SELECT
            p.id, p.practical_topic, p.date, p.start_time, p.end_time,
            p.subject_id, p.class_id, p.teacher_id, p.status,
            s.name as subject_name, c.name as class_name,
            u.full_name as teacher_name
          FROM practicals p
          LEFT JOIN subjects s ON p.subject_id = s.id
          LEFT JOIN classes c ON p.class_id = c.id
          LEFT JOIN users u ON p.teacher_id = u.id
          WHERE p.date = ? AND (p.teacher_id = ? OR ? = 'admin')
          ORDER BY p.start_time
        `, [date, userId, userRole]);

        responseData.practicals = practicals;
      } catch (err) {
        console.log('Practicals table may not exist yet:', err.message);
      }

      // Get assignments due on this date
      try {
        const [assignments] = await db.query(`
          SELECT
            a.id, a.title, a.description, a.due_date,
            a.class_id, a.subject_id, a.teacher_id,
            c.name as class_name, s.name as subject_name
          FROM assignments a
          LEFT JOIN classes c ON a.class_id = c.id
          LEFT JOIN subjects s ON a.subject_id = s.id
          WHERE DATE(a.due_date) = ? AND (a.teacher_id = ? OR ? = 'admin')
          ORDER BY a.due_date
        `, [date, userId, userRole]);

        responseData.assignments = assignments;
      } catch (err) {
        console.log('Assignments table may not exist yet:', err.message);
      }
    }
    // If it's a student
    else if (userRole === 'student') {
      // Get student's classes
      const [studentClasses] = await db.query(`
        SELECT c.id
        FROM classes c
        JOIN student_classes sc ON c.id = sc.class_id
        WHERE sc.student_id = ?
      `, [userId]);

      const classIds = studentClasses.map(c => c.id);

      if (classIds.length > 0) {
        // Get lectures for student's classes on this date
        let lecturesQuery = `
          SELECT
            l.id, l.teacher_id, l.date, l.start_time, l.end_time,
            l.subject_name, l.topic, l.class_name, l.section_display,
            l.grade, l.stream, l.location, l.status,
            u.full_name as teacher_name
          FROM teacher_lectures l
          LEFT JOIN users u ON l.teacher_id = u.id
          WHERE l.date = ?`;

        // Handle the IN clause differently based on the number of class IDs
        let lectures = [];
        if (classIds.length === 1) {
          lecturesQuery += ` AND l.class_name = ?`;
          lecturesQuery += ` ORDER BY l.start_time`;
          [lectures] = await db.query(lecturesQuery, [date, classIds[0]]);
        } else {
          lecturesQuery += ` AND l.class_name IN (?)`;
          lecturesQuery += ` ORDER BY l.start_time`;
          [lectures] = await db.query(lecturesQuery, [date, classIds]);
        }

        responseData.lectures = lectures;

        // Get tests assigned to this student for this date
        const [tests] = await db.query(`
          SELECT
            e.exam_id, e.exam_name, ta.end_datetime,
            (SELECT COUNT(*) FROM questions q JOIN sections s ON q.section_id = s.section_id WHERE s.exam_id = e.exam_id) as total_questions,
            e.duration,
            ta.max_attempts,
            (SELECT COUNT(*) FROM exam_attempts WHERE exam_id = e.exam_id AND user_id = ?) as attempts_used
          FROM test_assignments ta
          JOIN exams e ON ta.exam_id = e.exam_id
          WHERE DATE(ta.end_datetime) = ?
          AND (ta.user_id = ? OR ta.group_id IN (SELECT group_id FROM group_members WHERE user_id = ?))
          ORDER BY ta.end_datetime
        `, [userId, date, userId, userId]);

        responseData.tests = tests;

        // Get practicals for student's classes on this date
        try {
          let practicals = [];
          if (classIds.length === 1) {
            const [result] = await db.query(`
              SELECT
                p.id, p.practical_topic, p.date, p.start_time, p.end_time,
                p.subject_id, p.class_id, p.teacher_id, p.status,
                s.name as subject_name, c.name as class_name,
                u.full_name as teacher_name
              FROM practicals p
              LEFT JOIN subjects s ON p.subject_id = s.id
              LEFT JOIN classes c ON p.class_id = c.id
              LEFT JOIN users u ON p.teacher_id = u.id
              WHERE p.date = ? AND p.class_id = ?
              ORDER BY p.start_time
            `, [date, classIds[0]]);
            practicals = result;
          } else {
            const [result] = await db.query(`
              SELECT
                p.id, p.practical_topic, p.date, p.start_time, p.end_time,
                p.subject_id, p.class_id, p.teacher_id, p.status,
                s.name as subject_name, c.name as class_name,
                u.full_name as teacher_name
              FROM practicals p
              LEFT JOIN subjects s ON p.subject_id = s.id
              LEFT JOIN classes c ON p.class_id = c.id
              LEFT JOIN users u ON p.teacher_id = u.id
              WHERE p.date = ? AND p.class_id IN (?)
              ORDER BY p.start_time
            `, [date, classIds]);
            practicals = result;
          }

          responseData.practicals = practicals;
        } catch (err) {
          console.log('Practicals table may not exist yet:', err.message);
        }

        // Get assignments due on this date for this student
        try {
          let assignments = [];
          if (classIds.length === 1) {
            const [result] = await db.query(`
              SELECT
                a.id, a.title, a.description, a.due_date,
                a.class_id, a.subject_id, a.teacher_id,
                c.name as class_name, s.name as subject_name,
                u.full_name as teacher_name
              FROM assignments a
              LEFT JOIN classes c ON a.class_id = c.id
              LEFT JOIN subjects s ON a.subject_id = s.id
              LEFT JOIN users u ON a.teacher_id = u.id
              WHERE DATE(a.due_date) = ?
              AND (a.student_id = ? OR a.class_id = ?)
              ORDER BY a.due_date
            `, [date, userId, classIds[0]]);
            assignments = result;
          } else {
            const [result] = await db.query(`
              SELECT
                a.id, a.title, a.description, a.due_date,
                a.class_id, a.subject_id, a.teacher_id,
                c.name as class_name, s.name as subject_name,
                u.full_name as teacher_name
              FROM assignments a
              LEFT JOIN classes c ON a.class_id = c.id
              LEFT JOIN subjects s ON a.subject_id = s.id
              LEFT JOIN users u ON a.teacher_id = u.id
              WHERE DATE(a.due_date) = ?
              AND (a.student_id = ? OR a.class_id IN (?))
              ORDER BY a.due_date
            `, [date, userId, classIds]);
            assignments = result;
          }

          responseData.assignments = assignments;
        } catch (err) {
          console.log('Assignments table may not exist yet:', err.message);
        }
      }
    }

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching day timetable:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching day timetable'
    });
  }
};
