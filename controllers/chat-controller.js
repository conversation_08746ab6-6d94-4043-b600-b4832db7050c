/**
 * Chat Controller
 */
const db = require('../config/database');
const { v4: uuidv4 } = require('uuid');

module.exports = {
    /**
     * Admin chat monitoring
     */
    adminMonitor: async (req, res) => {
        try {
            // Get private chats
            const [privateChats] = await db.query(`
                SELECT
                    LEAST(cm.sender_id, cm.recipient_id) as user1_id,
                    GREATEST(cm.sender_id, cm.recipient_id) as user2_id,
                    u1.username as user1_name,
                    u2.username as user2_name,
                    COUNT(cm.message_id) as message_count,
                    MAX(cm.created_at) as last_activity
                FROM chat_messages cm
                JOIN users u1 ON LEAST(cm.sender_id, cm.recipient_id) = u1.id
                JOIN users u2 ON GREATEST(cm.sender_id, cm.recipient_id) = u2.id
                GROUP BY user1_id, user2_id
                ORDER BY last_activity DESC
            `);

            // Get group chats
            const [groupChats] = await db.query(`
                SELECT
                    g.id as group_id,
                    g.name,
                    COUNT(gm.message_id) as message_count,
                    MAX(gm.created_at) as last_activity
                FROM groups g
                LEFT JOIN group_messages gm ON g.id = gm.group_id
                GROUP BY g.id
                ORDER BY last_activity DESC
            `);

            res.render('admin/chat/monitor', {
                title: 'Chat Monitoring',
                pageTitle: 'Chat Monitoring',
                privateChats,
                groupChats,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error loading chat monitoring:', error);
            req.session.flashError = 'Error loading chat monitoring';
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Admin view private chat
     */
    adminViewPrivateChat: async (req, res) => {
        try {
            const { user1Id, user2Id } = req.params;

            // Get user details
            const [users1] = await db.query(
                'SELECT id as user_id, username, profile_image, role FROM users WHERE id = ?',
                [user1Id]
            );

            const [users2] = await db.query(
                'SELECT id as user_id, username, profile_image, role FROM users WHERE id = ?',
                [user2Id]
            );

            if (users1.length === 0 || users2.length === 0) {
                req.session.flashError = 'User not found';
                return res.redirect('/admin/chat/monitor');
            }

            const user1 = users1[0];
            const user2 = users2[0];

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    m.message_id,
                    m.sender_id,
                    m.message,
                    m.attachment_url,
                    m.attachment_type,
                    m.created_at,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM chat_messages m
                JOIN users u ON m.sender_id = u.id
                WHERE (m.sender_id = ? AND m.recipient_id = ?) OR (m.sender_id = ? AND m.recipient_id = ?)
                ORDER BY m.created_at ASC
            `, [user1Id, user2Id, user2Id, user1Id]);

            res.render('admin/chat/view-private', {
                title: `Chat between ${user1.username} and ${user2.username}`,
                pageTitle: `Chat between ${user1.username} and ${user2.username}`,
                user1,
                user2,
                messages,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error viewing private chat:', error);
            req.session.flashError = 'Error viewing private chat';
            res.redirect('/admin/chat/monitor');
        }
    },

    /**
     * Admin view group chat
     */
    adminViewGroupChat: async (req, res) => {
        try {
            const { groupId } = req.params;

            // Get group details
            const [groups] = await db.query(`
                SELECT
                    g.id as group_id,
                    g.name,
                    g.created_by,
                    u.username as creator_name
                FROM groups g
                LEFT JOIN users u ON g.created_by = u.id
                WHERE g.id = ?
            `, [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/admin/chat/monitor');
            }

            const group = groups[0];

            // Get group members
            const [members] = await db.query(`
                SELECT
                    u.id as user_id,
                    u.username,
                    u.profile_image,
                    u.role,
                    gm.is_admin
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ?
                ORDER BY gm.is_admin DESC, u.username ASC
            `, [groupId]);

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    m.message_id,
                    m.sender_id,
                    m.message,
                    m.attachment_url,
                    m.attachment_type,
                    m.created_at,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM group_messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.group_id = ?
                ORDER BY m.created_at ASC
            `, [groupId]);

            res.render('admin/chat/view-group', {
                title: `Group Chat: ${group.name}`,
                pageTitle: `Group Chat: ${group.name}`,
                group,
                members,
                messages,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error viewing group chat:', error);
            req.session.flashError = 'Error viewing group chat';
            res.redirect('/admin/chat/monitor');
        }
    },
    /**
     * Display chat interface
     */
    index: async (req, res) => {
        try {
            // Get user's private chats
            const [privateChats] = await db.query(`
                SELECT
                    DISTINCT IF(cm.sender_id = ?, cm.recipient_id, cm.sender_id) as user_id,
                    u.username,
                    u.profile_image,
                    (SELECT COUNT(*) FROM message_status ms
                     JOIN chat_messages cm2 ON ms.message_id = cm2.message_id
                     WHERE ms.user_id = ? AND ms.is_read = 0
                     AND ((cm2.sender_id = u.id AND cm2.recipient_id = ?)
                          OR (cm2.recipient_id = u.id AND cm2.sender_id = ?))
                    ) as unread_count,
                    (SELECT cm3.message FROM chat_messages cm3
                     WHERE (cm3.sender_id = ? AND cm3.recipient_id = u.id)
                        OR (cm3.recipient_id = ? AND cm3.sender_id = u.id)
                     ORDER BY cm3.created_at DESC LIMIT 1
                    ) as last_message,
                    (SELECT cm4.created_at FROM chat_messages cm4
                     WHERE (cm4.sender_id = ? AND cm4.recipient_id = u.id)
                        OR (cm4.recipient_id = ? AND cm4.sender_id = u.id)
                     ORDER BY cm4.created_at DESC LIMIT 1
                    ) as last_message_time
                FROM chat_messages cm
                JOIN users u ON u.id = IF(cm.sender_id = ?, cm.recipient_id, cm.sender_id)
                WHERE (cm.sender_id = ? AND cm.recipient_id IS NOT NULL)
                   OR (cm.recipient_id = ? AND cm.sender_id IS NOT NULL)
                ORDER BY last_message_time DESC
            `, [
                req.session.userId, req.session.userId, req.session.userId, req.session.userId,
                req.session.userId, req.session.userId, req.session.userId, req.session.userId,
                req.session.userId, req.session.userId, req.session.userId
            ]);

            // Get user's groups
            const [groups] = await db.query(`
                SELECT
                    g.*,
                    (SELECT COUNT(*) FROM message_status ms
                     JOIN chat_messages cm ON ms.message_id = cm.message_id
                     WHERE ms.user_id = ? AND ms.is_read = 0 AND cm.group_id = g.group_id
                    ) as unread_count,
                    (SELECT cm.message FROM chat_messages cm
                     WHERE cm.group_id = g.group_id
                     ORDER BY cm.created_at DESC LIMIT 1
                    ) as last_message,
                    (SELECT cm.created_at FROM chat_messages cm
                     WHERE cm.group_id = g.group_id
                     ORDER BY cm.created_at DESC LIMIT 1
                    ) as last_message_time,
                    (SELECT u.username FROM chat_messages cm
                     JOIN users u ON cm.sender_id = u.id
                     WHERE cm.group_id = g.group_id
                     ORDER BY cm.created_at DESC LIMIT 1
                    ) as last_sender
                FROM groups g
                JOIN group_members gm ON g.group_id = gm.group_id
                WHERE gm.user_id = ?
                ORDER BY last_message_time DESC
            `, [req.session.userId, req.session.userId]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('chat/index', {
                title: 'Chat',
                pageTitle: 'Chat',
                privateChats,
                groups,
                layout,
                currentPage: 'chat',
                userId: req.session.userId
            });
        } catch (error) {
            console.error('Error loading chat:', error);
            req.session.flashError = 'Error loading chat';
            res.redirect('/');
        }
    },

    /**
     * Display private chat with a user
     */
    privateChat: async (req, res) => {
        try {
            const userId = parseInt(req.params.userId);

            // Get user details
            const [users] = await db.query(`
                SELECT id, username, profile_image, role
                FROM users
                WHERE id = ?
            `, [userId]);

            if (users.length === 0) {
                req.session.flashError = 'User not found';
                return res.redirect('/chat');
            }

            const user = users[0];

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    cm.*,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM chat_messages cm
                JOIN users u ON cm.sender_id = u.id
                WHERE (cm.sender_id = ? AND cm.recipient_id = ?)
                   OR (cm.sender_id = ? AND cm.recipient_id = ?)
                ORDER BY cm.created_at ASC
            `, [req.session.userId, userId, userId, req.session.userId]);

            // Mark messages as read
            await db.query(`
                UPDATE message_status ms
                JOIN chat_messages cm ON ms.message_id = cm.message_id
                SET ms.is_read = 1, ms.read_at = NOW()
                WHERE ms.user_id = ?
                AND ms.is_read = 0
                AND ((cm.sender_id = ? AND cm.recipient_id = ?)
                     OR (cm.sender_id = ? AND cm.recipient_id = ?))
            `, [req.session.userId, userId, req.session.userId, req.session.userId, userId]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('chat/private', {
                title: `Chat with ${user.username}`,
                pageTitle: `Chat with ${user.username}`,
                user,
                messages,
                layout,
                currentPage: 'chat',
                userId: req.session.userId
            });
        } catch (error) {
            console.error('Error loading private chat:', error);
            req.session.flashError = 'Error loading chat';
            res.redirect('/chat');
        }
    },

    /**
     * Display group chat
     */
    groupChat: async (req, res) => {
        try {
            const groupId = parseInt(req.params.groupId);

            // Get group details
            const [groups] = await db.query(`
                SELECT g.*, u.username as creator_name
                FROM groups g
                LEFT JOIN users u ON g.created_by = u.id
                WHERE g.group_id = ?
            `, [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/chat');
            }

            const group = groups[0];

            // Check if user is a member of the group
            const [members] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, req.session.userId]);

            if (members.length === 0) {
                req.session.flashError = 'You are not a member of this group';
                return res.redirect('/chat');
            }

            // Get group members
            const [groupMembers] = await db.query(`
                SELECT gm.*, u.username, u.profile_image, u.role
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ?
                ORDER BY gm.is_admin DESC, u.username ASC
            `, [groupId]);

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    cm.*,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM chat_messages cm
                JOIN users u ON cm.sender_id = u.id
                WHERE cm.group_id = ?
                ORDER BY cm.created_at ASC
            `, [groupId]);

            // Mark messages as read
            await db.query(`
                UPDATE message_status ms
                JOIN chat_messages cm ON ms.message_id = cm.message_id
                SET ms.is_read = 1, ms.read_at = NOW()
                WHERE ms.user_id = ?
                AND ms.is_read = 0
                AND cm.group_id = ?
            `, [req.session.userId, groupId]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('chat/group', {
                title: group.name,
                pageTitle: group.name,
                group,
                members: groupMembers,
                messages,
                layout,
                currentPage: 'chat',
                userId: req.session.userId,
                isAdmin: members[0].is_admin
            });
        } catch (error) {
            console.error('Error loading group chat:', error);
            req.session.flashError = 'Error loading chat';
            res.redirect('/chat');
        }
    },

    /**
     * Generate invite link for a group
     */
    generateInviteLink: async (req, res) => {
        try {
            const groupId = parseInt(req.params.groupId);
            const { expiresIn, maxUses } = req.body;

            // Check if user is a group admin
            const [members] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ? AND is_admin = 1
            `, [groupId, req.session.userId]);

            if (members.length === 0) {
                return res.status(403).json({
                    success: false,
                    message: 'You do not have permission to generate invite links'
                });
            }

            // Generate invite ID
            const inviteId = uuidv4();

            // Calculate expiration date
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + (parseInt(expiresIn) || 24));

            // Create invite
            await db.query(`
                INSERT INTO group_invites (invite_id, group_id, created_by, expires_at, max_uses)
                VALUES (?, ?, ?, ?, ?)
            `, [inviteId, groupId, req.session.userId, expiresAt, maxUses || null]);

            // Update group invite link
            await db.query(`
                UPDATE groups
                SET invite_link = ?
                WHERE group_id = ?
            `, [inviteId, groupId]);

            res.json({
                success: true,
                inviteLink: `${req.protocol}://${req.get('host')}/groups/join/${inviteId}`,
                inviteId
            });
        } catch (error) {
            console.error('Error generating invite link:', error);
            res.status(500).json({
                success: false,
                message: 'Error generating invite link'
            });
        }
    },

    /**
     * Join group via invite link
     */
    joinViaInvite: async (req, res) => {
        try {
            const inviteId = req.params.inviteId;

            // Get invite details
            const [invites] = await db.query(`
                SELECT i.*, g.name as group_name
                FROM group_invites i
                JOIN groups g ON i.group_id = g.group_id
                WHERE i.invite_id = ?
                AND i.expires_at > NOW()
                AND (i.max_uses IS NULL OR i.uses < i.max_uses)
            `, [inviteId]);

            if (invites.length === 0) {
                req.session.flashError = 'Invalid or expired invite link';
                return res.redirect('/groups');
            }

            const invite = invites[0];

            // Check if user is already a member
            const [members] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [invite.group_id, req.session.userId]);

            if (members.length > 0) {
                req.session.flashSuccess = `You are already a member of ${invite.group_name}`;
                return res.redirect(`/groups/${invite.group_id}`);
            }

            // Add user to group
            await db.query(`
                INSERT INTO group_members (group_id, user_id, is_admin)
                VALUES (?, ?, 0)
            `, [invite.group_id, req.session.userId]);

            // Increment invite uses
            await db.query(`
                UPDATE group_invites
                SET uses = uses + 1
                WHERE invite_id = ?
            `, [inviteId]);

            req.session.flashSuccess = `You have joined ${invite.group_name}`;
            res.redirect(`/groups/${invite.group_id}`);
        } catch (error) {
            console.error('Error joining group via invite:', error);
            req.session.flashError = 'Error joining group';
            res.redirect('/groups');
        }
    },

    /**
     * Admin chat monitoring
     */
    adminMonitor: async (req, res) => {
        try {
            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                return res.redirect('/');
            }

            // Get all private chats
            const [privateChats] = await db.query(`
                SELECT
                    DISTINCT CONCAT(
                        LEAST(cm.sender_id, cm.recipient_id),
                        '-',
                        GREATEST(cm.sender_id, cm.recipient_id)
                    ) as chat_id,
                    u1.username as user1_name,
                    u2.username as user2_name,
                    u1.id as user1_id,
                    u2.id as user2_id,
                    (SELECT COUNT(*) FROM chat_messages
                     WHERE (sender_id = u1.id AND recipient_id = u2.id)
                        OR (sender_id = u2.id AND recipient_id = u1.id)
                    ) as message_count,
                    (SELECT created_at FROM chat_messages
                     WHERE (sender_id = u1.id AND recipient_id = u2.id)
                        OR (sender_id = u2.id AND recipient_id = u1.id)
                     ORDER BY created_at DESC LIMIT 1
                    ) as last_activity
                FROM chat_messages cm
                JOIN users u1 ON u1.id = LEAST(cm.sender_id, cm.recipient_id)
                JOIN users u2 ON u2.id = GREATEST(cm.sender_id, cm.recipient_id)
                WHERE cm.recipient_id IS NOT NULL
                GROUP BY chat_id
                ORDER BY last_activity DESC
            `);

            // Get all group chats
            const [groupChats] = await db.query(`
                SELECT
                    g.*,
                    (SELECT COUNT(*) FROM chat_messages WHERE group_id = g.group_id) as message_count,
                    (SELECT created_at FROM chat_messages
                     WHERE group_id = g.group_id
                     ORDER BY created_at DESC LIMIT 1
                    ) as last_activity
                FROM groups g
                WHERE EXISTS (SELECT 1 FROM chat_messages WHERE group_id = g.group_id)
                ORDER BY last_activity DESC
            `);

            res.render('admin/chat/monitor', {
                title: 'Chat Monitoring',
                pageTitle: 'Chat Monitoring',
                privateChats,
                groupChats,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error loading chat monitor:', error);
            req.session.flashError = 'Error loading chat monitor';
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * View private chat for admin
     */
    adminViewPrivateChat: async (req, res) => {
        try {
            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                return res.redirect('/');
            }

            const user1Id = parseInt(req.params.user1Id);
            const user2Id = parseInt(req.params.user2Id);

            // Get user details
            const [users1] = await db.query(`
                SELECT id, username, profile_image, role
                FROM users
                WHERE id = ?
            `, [user1Id]);

            const [users2] = await db.query(`
                SELECT id, username, profile_image, role
                FROM users
                WHERE id = ?
            `, [user2Id]);

            if (users1.length === 0 || users2.length === 0) {
                req.session.flashError = 'Users not found';
                return res.redirect('/admin/chat/monitor');
            }

            const user1 = users1[0];
            const user2 = users2[0];

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    cm.*,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM chat_messages cm
                JOIN users u ON cm.sender_id = u.id
                WHERE (cm.sender_id = ? AND cm.recipient_id = ?)
                   OR (cm.sender_id = ? AND cm.recipient_id = ?)
                ORDER BY cm.created_at ASC
            `, [user1Id, user2Id, user2Id, user1Id]);

            res.render('admin/chat/view-private', {
                title: `Chat between ${user1.username} and ${user2.username}`,
                pageTitle: `Chat between ${user1.username} and ${user2.username}`,
                user1,
                user2,
                messages,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error viewing private chat:', error);
            req.session.flashError = 'Error viewing chat';
            res.redirect('/admin/chat/monitor');
        }
    },

    /**
     * View group chat for admin
     */
    adminViewGroupChat: async (req, res) => {
        try {
            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                return res.redirect('/');
            }

            const groupId = parseInt(req.params.groupId);

            // Get group details
            const [groups] = await db.query(`
                SELECT g.*, u.username as creator_name
                FROM groups g
                LEFT JOIN users u ON g.created_by = u.id
                WHERE g.group_id = ?
            `, [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/admin/chat/monitor');
            }

            const group = groups[0];

            // Get group members
            const [members] = await db.query(`
                SELECT gm.*, u.username, u.profile_image, u.role
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ?
                ORDER BY gm.is_admin DESC, u.username ASC
            `, [groupId]);

            // Get chat messages
            const [messages] = await db.query(`
                SELECT
                    cm.*,
                    u.username as sender_name,
                    u.profile_image as sender_image
                FROM chat_messages cm
                JOIN users u ON cm.sender_id = u.id
                WHERE cm.group_id = ?
                ORDER BY cm.created_at ASC
            `, [groupId]);

            res.render('admin/chat/view-group', {
                title: `Group Chat: ${group.name}`,
                pageTitle: `Group Chat: ${group.name}`,
                group,
                members,
                messages,
                layout: 'admin',
                currentPage: 'chat-monitor'
            });
        } catch (error) {
            console.error('Error viewing group chat:', error);
            req.session.flashError = 'Error viewing chat';
            res.redirect('/admin/chat/monitor');
        }
    }
};
