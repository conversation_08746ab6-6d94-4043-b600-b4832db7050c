const db = require('../config/database');

// Class Management
exports.getClasses = async (req, res) => {
  try {
    const [classes] = await db.query(`
      SELECT * FROM classes
      ORDER BY name
    `);

    res.render('admin/classes/index', {
      title: 'Class Management',
      layout: 'admin',
      classes,
      currentPage: 'classes'
    });
  } catch (error) {
    console.error('Error fetching classes:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading classes',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.getAddClass = (req, res) => {
  res.render('admin/classes/add', {
    title: 'Add Class',
    layout: 'admin',
    currentPage: 'classes'
  });
};

exports.postAddClass = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Validate input
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Class name is required'
      });
    }

    // Insert class
    await db.query(`
      INSERT INTO classes (name, description)
      VALUES (?, ?)
    `, [name, description]);

    res.json({
      success: true,
      message: 'Class added successfully'
    });
  } catch (error) {
    console.error('Error adding class:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding class'
    });
  }
};

exports.getEditClass = async (req, res) => {
  try {
    const classId = req.params.id;

    // Fetch class
    const [classes] = await db.query(`
      SELECT * FROM classes WHERE id = ?
    `, [classId]);

    if (classes.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Class not found',
        error: { status: 404, stack: 'Class not found' },
        layout: 'admin'
      });
    }

    res.render('admin/classes/edit', {
      title: 'Edit Class',
      layout: 'admin',
      class: classes[0],
      currentPage: 'classes'
    });
  } catch (error) {
    console.error('Error fetching class:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading class',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.postEditClass = async (req, res) => {
  try {
    const classId = req.params.id;
    const { name, description, is_active } = req.body;

    // Validate input
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Class name is required'
      });
    }

    // Update class
    await db.query(`
      UPDATE classes
      SET name = ?, description = ?, is_active = ?
      WHERE id = ?
    `, [name, description, is_active ? 1 : 0, classId]);

    res.json({
      success: true,
      message: 'Class updated successfully'
    });
  } catch (error) {
    console.error('Error updating class:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating class'
    });
  }
};

exports.deleteClass = async (req, res) => {
  try {
    const classId = req.params.id;

    // Check if class is in use
    const [sections] = await db.query(`
      SELECT COUNT(*) as count FROM sections WHERE class_id = ?
    `, [classId]);

    if (sections[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete class. It is associated with one or more sections.'
      });
    }

    // Delete class
    await db.query(`DELETE FROM classes WHERE id = ?`, [classId]);

    res.json({
      success: true,
      message: 'Class deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting class:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting class'
    });
  }
};

// Trade Management
exports.getTrades = async (req, res) => {
  try {
    const [trades] = await db.query(`
      SELECT * FROM trades
      ORDER BY name
    `);

    res.render('admin/trades/index', {
      title: 'Trade Management',
      layout: 'admin',
      trades,
      currentPage: 'trades'
    });
  } catch (error) {
    console.error('Error fetching trades:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading trades',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.getAddTrade = (req, res) => {
  res.render('admin/trades/add', {
    title: 'Add Trade',
    layout: 'admin',
    currentPage: 'trades'
  });
};

exports.postAddTrade = async (req, res) => {
  try {
    const { name, description } = req.body;

    // Validate input
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Trade name is required'
      });
    }

    // Insert trade
    await db.query(`
      INSERT INTO trades (name, description)
      VALUES (?, ?)
    `, [name, description]);

    res.json({
      success: true,
      message: 'Trade added successfully'
    });
  } catch (error) {
    console.error('Error adding trade:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding trade'
    });
  }
};

exports.getEditTrade = async (req, res) => {
  try {
    const tradeId = req.params.id;

    // Fetch trade
    const [trades] = await db.query(`
      SELECT * FROM trades WHERE id = ?
    `, [tradeId]);

    if (trades.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Trade not found',
        error: { status: 404, stack: 'Trade not found' },
        layout: 'admin'
      });
    }

    res.render('admin/trades/edit', {
      title: 'Edit Trade',
      layout: 'admin',
      trade: trades[0],
      currentPage: 'trades'
    });
  } catch (error) {
    console.error('Error fetching trade:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading trade',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.postEditTrade = async (req, res) => {
  try {
    const tradeId = req.params.id;
    const { name, description, is_active } = req.body;

    // Validate input
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Trade name is required'
      });
    }

    // Update trade
    await db.query(`
      UPDATE trades
      SET name = ?, description = ?, is_active = ?
      WHERE id = ?
    `, [name, description, is_active ? 1 : 0, tradeId]);

    res.json({
      success: true,
      message: 'Trade updated successfully'
    });
  } catch (error) {
    console.error('Error updating trade:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating trade'
    });
  }
};

exports.deleteTrade = async (req, res) => {
  try {
    const tradeId = req.params.id;

    // Check if trade is in use
    const [sections] = await db.query(`
      SELECT COUNT(*) as count FROM sections WHERE trade_id = ?
    `, [tradeId]);

    if (sections[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete trade. It is associated with one or more sections.'
      });
    }

    // Delete trade
    await db.query(`DELETE FROM trades WHERE id = ?`, [tradeId]);

    res.json({
      success: true,
      message: 'Trade deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting trade:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting trade'
    });
  }
};

// Section Management
exports.getSections = async (req, res) => {
  try {
    const [sections] = await db.query(`
      SELECT s.*, c.name as class_name, t.name as trade_name
      FROM sections s
      JOIN classes c ON s.class_id = c.id
      LEFT JOIN trades t ON s.trade_id = t.id
      ORDER BY c.name, t.name, s.name
    `);

    // Get classes for dropdown
    const [classes] = await db.query(`
      SELECT * FROM classes
      WHERE is_active = 1
      ORDER BY name
    `);

    // Get trades for dropdown
    const [trades] = await db.query(`
      SELECT * FROM trades
      WHERE is_active = 1
      ORDER BY name
    `);

    res.render('admin/sections/index', {
      title: 'Section Management',
      layout: 'admin',
      sections,
      classes,
      trades,
      currentPage: 'sections'
    });
  } catch (error) {
    console.error('Error fetching sections:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading sections',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.getAddSection = async (req, res) => {
  try {
    // Get classes for dropdown
    const [classes] = await db.query(`
      SELECT * FROM classes
      WHERE is_active = 1
      ORDER BY name
    `);

    // Get trades for dropdown
    const [trades] = await db.query(`
      SELECT * FROM trades
      WHERE is_active = 1
      ORDER BY name
    `);

    res.render('admin/sections/add', {
      title: 'Add Section',
      layout: 'admin',
      classes,
      trades,
      currentPage: 'sections'
    });
  } catch (error) {
    console.error('Error loading section form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading section form',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.postAddSection = async (req, res) => {
  try {
    const { class_id, trade_id, name } = req.body;

    // Validate input
    if (!class_id || !name) {
      return res.status(400).json({
        success: false,
        message: 'Class and section name are required'
      });
    }

    // Insert section
    await db.query(`
      INSERT INTO sections (class_id, trade_id, name)
      VALUES (?, ?, ?)
    `, [class_id, trade_id || null, name]);

    res.json({
      success: true,
      message: 'Section added successfully'
    });
  } catch (error) {
    console.error('Error adding section:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding section'
    });
  }
};

exports.getEditSection = async (req, res) => {
  try {
    const sectionId = req.params.id;

    // Fetch section
    const [sections] = await db.query(`
      SELECT * FROM sections WHERE id = ?
    `, [sectionId]);

    if (sections.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Section not found',
        error: { status: 404, stack: 'Section not found' },
        layout: 'admin'
      });
    }

    // Get classes for dropdown
    const [classes] = await db.query(`
      SELECT * FROM classes
      WHERE is_active = 1
      ORDER BY name
    `);

    // Get trades for dropdown
    const [trades] = await db.query(`
      SELECT * FROM trades
      WHERE is_active = 1
      ORDER BY name
    `);

    res.render('admin/sections/edit', {
      title: 'Edit Section',
      layout: 'admin',
      section: sections[0],
      classes,
      trades,
      currentPage: 'sections'
    });
  } catch (error) {
    console.error('Error fetching section:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading section',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

exports.postEditSection = async (req, res) => {
  try {
    const sectionId = req.params.id;
    const { class_id, trade_id, name, is_active } = req.body;

    // Validate input
    if (!class_id || !name) {
      return res.status(400).json({
        success: false,
        message: 'Class and section name are required'
      });
    }

    // Update section
    await db.query(`
      UPDATE sections
      SET class_id = ?, trade_id = ?, name = ?, is_active = ?
      WHERE id = ?
    `, [class_id, trade_id || null, name, is_active ? 1 : 0, sectionId]);

    res.json({
      success: true,
      message: 'Section updated successfully'
    });
  } catch (error) {
    console.error('Error updating section:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating section'
    });
  }
};

exports.deleteSection = async (req, res) => {
  try {
    const sectionId = req.params.id;

    // Check if section is in use in instruction_plans
    const [plans] = await db.query(`
      SELECT COUNT(*) as count FROM instruction_plans WHERE section_id = ?
    `, [sectionId]);

    if (plans[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete section. It is associated with one or more instruction plans.'
      });
    }

    // Delete section
    await db.query(`DELETE FROM sections WHERE id = ?`, [sectionId]);

    res.json({
      success: true,
      message: 'Section deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting section:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting section'
    });
  }
};