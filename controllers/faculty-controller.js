const db = require('../config/database');

/**
 * Faculty Controller
 * Handles faculty-specific operations for admin panel
 */

// Get faculty list with filtering and pagination
exports.getFaculty = async (req, res) => {
  try {
    const { search, status, sort, page = 1 } = req.query;
    const limit = 10;
    const offset = (page - 1) * limit;

    // Build WHERE clause
    let whereConditions = ["role = 'teacher'"];
    let queryParams = [];

    // Search filter
    if (search && search.trim()) {
      whereConditions.push("(username LIKE ? OR name LIKE ? OR email LIKE ? OR subjects LIKE ?)");
      const searchTerm = `%${search.trim()}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Status filter
    if (status) {
      switch (status) {
        case 'online':
          whereConditions.push("is_online = 1");
          break;
        case 'offline':
          whereConditions.push("is_online = 0");
          break;
        case 'active':
          whereConditions.push("is_blocked = 0 AND is_deleted = 0");
          break;
        case 'blocked':
          whereConditions.push("is_blocked = 1");
          break;
      }
    }

    // Build ORDER BY clause
    let orderBy = "username ASC";
    if (sort) {
      switch (sort) {
        case 'name_asc':
          orderBy = "username ASC";
          break;
        case 'name_desc':
          orderBy = "username DESC";
          break;
        case 'created_desc':
          orderBy = "created_at DESC";
          break;
        case 'created_asc':
          orderBy = "created_at ASC";
          break;
      }
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM users 
      ${whereClause}
    `;
    const [countResult] = await db.query(countQuery, queryParams);
    const totalFaculty = countResult[0].total;
    const totalPages = Math.ceil(totalFaculty / limit);

    // Get faculty data
    const facultyQuery = `
      SELECT 
        id, username, name, email, profile_image, subjects, bio,
        date_of_birth, created_at, last_login, is_online, is_blocked, is_deleted
      FROM users 
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ? OFFSET ?
    `;
    
    const [faculty] = await db.query(facultyQuery, [...queryParams, limit, offset]);

    // Render the faculty page
    res.render('admin/faculty/index', {
      title: 'Faculty Operations',
      pageTitle: 'Faculty Operations',
      currentPage: 'faculty',
      faculty,
      query: req.query,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalFaculty,
        limit
      }
    });

  } catch (error) {
    console.error('Error fetching faculty:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading faculty data',
      error: { status: 500 },
      layout: 'admin'
    });
  }
};

// Get individual faculty details (API endpoint)
exports.getFacultyDetails = async (req, res) => {
  try {
    const facultyId = req.params.id;

    // Get faculty basic information
    const [facultyResult] = await db.query(`
      SELECT 
        id, username, name, email, profile_image, subjects, bio,
        date_of_birth, created_at, last_login, is_online, is_blocked, is_deleted
      FROM users 
      WHERE id = ? AND role = 'teacher'
    `, [facultyId]);

    if (facultyResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }

    const faculty = facultyResult[0];

    // Get additional statistics
    try {
      // Get lecture count
      const [lectureCount] = await db.query(`
        SELECT COUNT(*) as count 
        FROM teacher_lectures 
        WHERE teacher_id = ?
      `, [facultyId]);

      // Get practical count
      const [practicalCount] = await db.query(`
        SELECT COUNT(*) as count 
        FROM teacher_practicals 
        WHERE teacher_id = ?
      `, [facultyId]);

      // Get student count (approximate based on class assignments)
      const [studentCount] = await db.query(`
        SELECT COUNT(DISTINCT s.id) as count
        FROM students s
        JOIN teacher_subjects ts ON ts.teacher_id = ?
        WHERE s.class = ts.class_name OR s.class IS NOT NULL
      `, [facultyId]);

      // Add statistics to faculty object
      faculty.lecture_count = lectureCount[0]?.count || 0;
      faculty.practical_count = practicalCount[0]?.count || 0;
      faculty.student_count = studentCount[0]?.count || 0;

    } catch (statsError) {
      console.error('Error fetching faculty statistics:', statsError);
      // Set default values if statistics query fails
      faculty.lecture_count = 0;
      faculty.practical_count = 0;
      faculty.student_count = 0;
    }

    res.json({
      success: true,
      faculty
    });

  } catch (error) {
    console.error('Error fetching faculty details:', error);
    res.status(500).json({
      success: false,
      message: 'Error loading faculty details'
    });
  }
};

// Get faculty subjects and classes
exports.getFacultySubjects = async (req, res) => {
  try {
    const facultyId = req.params.id;

    // Get subjects taught by this faculty
    const [subjects] = await db.query(`
      SELECT 
        s.id, s.name, s.code, s.description,
        ts.class_name, ts.section
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = ?
      ORDER BY s.name, ts.class_name
    `, [facultyId]);

    res.json({
      success: true,
      subjects
    });

  } catch (error) {
    console.error('Error fetching faculty subjects:', error);
    res.status(500).json({
      success: false,
      message: 'Error loading faculty subjects'
    });
  }
};

// Get faculty timetable
exports.getFacultyTimetable = async (req, res) => {
  try {
    const facultyId = req.params.id;

    // Get timetable for this faculty
    const [timetable] = await db.query(`
      SELECT 
        day_of_week, period, subject, class_name, section,
        start_time, end_time, room_number
      FROM teacher_timetable
      WHERE teacher_id = ?
      ORDER BY 
        FIELD(day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'),
        period
    `, [facultyId]);

    res.json({
      success: true,
      timetable
    });

  } catch (error) {
    console.error('Error fetching faculty timetable:', error);
    res.status(500).json({
      success: false,
      message: 'Error loading faculty timetable'
    });
  }
};

// Update faculty status (block/unblock)
exports.updateFacultyStatus = async (req, res) => {
  try {
    const facultyId = req.params.id;
    const { action } = req.body;

    let updateQuery;
    let successMessage;

    switch (action) {
      case 'block':
        updateQuery = "UPDATE users SET is_blocked = 1 WHERE id = ? AND role = 'teacher'";
        successMessage = 'Faculty member blocked successfully';
        break;
      case 'unblock':
        updateQuery = "UPDATE users SET is_blocked = 0 WHERE id = ? AND role = 'teacher'";
        successMessage = 'Faculty member unblocked successfully';
        break;
      case 'activate':
        updateQuery = "UPDATE users SET is_deleted = 0, is_blocked = 0 WHERE id = ? AND role = 'teacher'";
        successMessage = 'Faculty member activated successfully';
        break;
      case 'deactivate':
        updateQuery = "UPDATE users SET is_deleted = 1 WHERE id = ? AND role = 'teacher'";
        successMessage = 'Faculty member deactivated successfully';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    const [result] = await db.query(updateQuery, [facultyId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Faculty member not found'
      });
    }

    res.json({
      success: true,
      message: successMessage
    });

  } catch (error) {
    console.error('Error updating faculty status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating faculty status'
    });
  }
};
