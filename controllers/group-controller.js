/**
 * Group Controller
 * Handles group management and operations
 */
const db = require('../config/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Create or get a role-based group
 * @param {string} role - User role (e.g., 'student', 'admin')
 * @returns {Promise<number>} - Group ID
 */
async function getOrCreateRoleGroup(role) {
    try {
        // Check if role group exists
        const [groups] = await db.query(
            'SELECT group_id FROM groups WHERE is_role_group = 1 AND role_type = ?',
            [role]
        );

        if (groups.length > 0) {
            return groups[0].group_id;
        }

        // Create role group if it doesn't exist
        const groupName = `${role.charAt(0).toUpperCase() + role.slice(1)}s Group`;
        const [result] = await db.query(
            'INSERT INTO groups (name, description, created_by, is_role_group, role_type, created_at) VALUES (?, ?, NULL, 1, ?, NOW())',
            [groupName, `Automatic group for all ${role} users`, role]
        );

        return result.insertId;
    } catch (error) {
        console.error(`Error creating/getting role group for ${role}:`, error);
        throw error;
    }
}

/**
 * Add user to their role group
 * @param {number} userId - User ID
 * @param {string} role - User role
 */
async function addUserToRoleGroup(userId, role) {
    try {
        const groupId = await getOrCreateRoleGroup(role);

        // Check if user is already in the group
        const [members] = await db.query(
            'SELECT * FROM group_members WHERE group_id = ? AND user_id = ?',
            [groupId, userId]
        );

        if (members.length === 0) {
            // Add user to the group
            await db.query(
                'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 0, NOW())',
                [groupId, userId]
            );
        }
    } catch (error) {
        console.error(`Error adding user ${userId} to ${role} group:`, error);
        // Don't throw error to prevent blocking user creation/update
    }
}

const groupController = {
    /**
     * Display groups list
     */
    index: async (req, res) => {
        try {
            // Get all groups with pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Get total count
            const [countResult] = await db.query('SELECT COUNT(*) as total FROM groups');
            const total = countResult[0].total;
            const totalPages = Math.ceil(total / perPage);

            // Get groups with member count
            const [groups] = await db.query(`
                SELECT g.*,
                       u.username as creator_name,
                       COUNT(gm.user_id) as member_count
                FROM groups g
                LEFT JOIN users u ON g.created_by = u.id
                LEFT JOIN group_members gm ON g.group_id = gm.group_id
                GROUP BY g.group_id
                ORDER BY g.name ASC
                LIMIT ? OFFSET ?
            `, [perPage, offset]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('groups/index', {
                title: 'Groups',
                pageTitle: 'Groups',
                groups,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems: total
                },
                layout,
                currentPage: 'groups'
            });
        } catch (error) {
            console.error('Error loading groups:', error);
            req.session.flashError = 'Error loading groups';
            res.redirect('/');
        }
    },

    /**
     * Display form to create a new group
     */
    createForm: async (req, res) => {
        try {
            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('groups/create', {
                title: 'Create Group',
                pageTitle: 'Create Group',
                layout,
                currentPage: 'groups'
            });
        } catch (error) {
            console.error('Error loading create group form:', error);
            req.session.flashError = 'Error loading create group form';
            res.redirect('/groups');
        }
    },

    /**
     * Create a new group
     */
    create: async (req, res) => {
        try {
            const { name, description } = req.body;
            const created_by = req.session.userId;

            // Validate required fields
            if (!name) {
                req.session.flashError = 'Group name is required';
                return res.redirect('/groups/create');
            }

            // Create group
            const [result] = await db.query(`
                INSERT INTO groups (name, description, created_by)
                VALUES (?, ?, ?)
            `, [name, description, created_by]);

            const groupId = result.insertId;

            // Add creator as a member and admin
            await db.query(`
                INSERT INTO group_members (group_id, user_id, is_admin)
                VALUES (?, ?, 1)
            `, [groupId, created_by]);

            req.session.flashSuccess = 'Group created successfully';
            res.redirect('/groups');
        } catch (error) {
            console.error('Error creating group:', error);
            req.session.flashError = 'Error creating group';
            res.redirect('/groups/create');
        }
    },

    /**
     * Display a specific group
     */
    view: async (req, res) => {
        try {
            const groupId = req.params.id;

            // Get group details
            const [groups] = await db.query(`
                SELECT g.*, u.username as creator_name
                FROM groups g
                LEFT JOIN users u ON g.created_by = u.id
                WHERE g.group_id = ?
            `, [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/groups');
            }

            const group = groups[0];

            // Get group members
            const [members] = await db.query(`
                SELECT gm.*, u.username, u.email, u.profile_image
                FROM group_members gm
                JOIN users u ON gm.user_id = u.id
                WHERE gm.group_id = ?
                ORDER BY gm.is_admin DESC, u.username ASC
            `, [groupId]);

            // Get assigned exams
            const [exams] = await db.query(`
                SELECT gea.*, e.exam_name, e.description, u.username as assigned_by_name
                FROM group_exam_assignments gea
                JOIN exams e ON gea.exam_id = e.exam_id
                LEFT JOIN users u ON gea.assigned_by = u.id
                WHERE gea.group_id = ?
                ORDER BY gea.due_date ASC
            `, [groupId]);

            // Check if current user is a member
            const [userMembership] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, req.session.userId]);

            const isMember = userMembership.length > 0;
            const isAdmin = isMember && userMembership[0].is_admin;

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('groups/view', {
                title: group.name,
                pageTitle: group.name,
                group,
                members,
                exams,
                isMember,
                isAdmin,
                layout,
                currentPage: 'groups'
            });
        } catch (error) {
            console.error('Error loading group:', error);
            req.session.flashError = 'Error loading group';
            res.redirect('/groups');
        }
    },

    /**
     * Display form to edit a group
     */
    editForm: async (req, res) => {
        try {
            const groupId = req.params.id;

            // Get group details
            const [groups] = await db.query('SELECT * FROM groups WHERE group_id = ?', [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/groups');
            }

            const group = groups[0];

            // Check if user has permission to edit
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    req.session.flashError = 'You do not have permission to edit this group';
                    return res.redirect(`/groups/${groupId}`);
                }
            }

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('groups/edit', {
                title: `Edit ${group.name}`,
                pageTitle: `Edit ${group.name}`,
                group,
                layout,
                currentPage: 'groups'
            });
        } catch (error) {
            console.error('Error loading edit group form:', error);
            req.session.flashError = 'Error loading edit group form';
            res.redirect('/groups');
        }
    },

    /**
     * Update a group
     */
    update: async (req, res) => {
        try {
            const groupId = req.params.id;
            const { name, description } = req.body;

            // Validate required fields
            if (!name) {
                req.session.flashError = 'Group name is required';
                return res.redirect(`/groups/${groupId}/edit`);
            }

            // Check if user has permission to edit
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    req.session.flashError = 'You do not have permission to edit this group';
                    return res.redirect(`/groups/${groupId}`);
                }
            }

            // Get group to check if it's a system group
            const [groups] = await db.query('SELECT * FROM groups WHERE group_id = ?', [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/groups');
            }

            const group = groups[0];

            // Don't allow editing name of system groups
            if (group.is_system && group.name !== name) {
                req.session.flashError = 'Cannot change the name of a system group';
                return res.redirect(`/groups/${groupId}/edit`);
            }

            // Update group
            await db.query(`
                UPDATE groups
                SET name = ?, description = ?
                WHERE group_id = ?
            `, [name, description, groupId]);

            req.session.flashSuccess = 'Group updated successfully';
            res.redirect(`/groups/${groupId}`);
        } catch (error) {
            console.error('Error updating group:', error);
            req.session.flashError = 'Error updating group';
            res.redirect(`/groups/${req.params.id}/edit`);
        }
    },

    /**
     * Delete a group
     */
    delete: async (req, res) => {
        try {
            const groupId = req.params.id;

            // Check if user has permission to delete
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    req.session.flashError = 'You do not have permission to delete this group';
                    return res.redirect(`/groups/${groupId}`);
                }
            }

            // Get group to check if it's a system group
            const [groups] = await db.query('SELECT * FROM groups WHERE group_id = ?', [groupId]);

            if (groups.length === 0) {
                req.session.flashError = 'Group not found';
                return res.redirect('/groups');
            }

            const group = groups[0];

            // Don't allow deleting system groups
            if (group.is_system) {
                req.session.flashError = 'Cannot delete a system group';
                return res.redirect(`/groups/${groupId}`);
            }

            // Delete group (cascades to members and assignments)
            await db.query('DELETE FROM groups WHERE group_id = ?', [groupId]);

            req.session.flashSuccess = 'Group deleted successfully';
            res.redirect('/groups');
        } catch (error) {
            console.error('Error deleting group:', error);
            req.session.flashError = 'Error deleting group';
            res.redirect('/groups');
        }
    },

    /**
     * Add a member to a group
     */
    addMember: async (req, res) => {
        try {
            const groupId = req.params.id;
            const { user_id, is_admin } = req.body;

            // Validate required fields
            if (!user_id) {
                return res.status(400).json({
                    success: false,
                    message: 'User ID is required'
                });
            }

            // Check if user has permission to add members
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    return res.status(403).json({
                        success: false,
                        message: 'You do not have permission to add members to this group'
                    });
                }
            }

            // Check if user is already a member
            const [existingMember] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, user_id]);

            if (existingMember.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'User is already a member of this group'
                });
            }

            // Add member
            await db.query(`
                INSERT INTO group_members (group_id, user_id, is_admin)
                VALUES (?, ?, ?)
            `, [groupId, user_id, is_admin ? 1 : 0]);

            // Get user details for response
            const [users] = await db.query(`
                SELECT id, username, email, profile_image
                FROM users
                WHERE id = ?
            `, [user_id]);

            res.json({
                success: true,
                message: 'Member added successfully',
                member: {
                    user_id: parseInt(user_id),
                    is_admin: is_admin ? 1 : 0,
                    ...users[0]
                }
            });
        } catch (error) {
            console.error('Error adding member:', error);
            res.status(500).json({
                success: false,
                message: 'Error adding member'
            });
        }
    },

    /**
     * Remove a member from a group
     */
    removeMember: async (req, res) => {
        try {
            const groupId = req.params.id;
            const userId = req.params.userId;

            // Check if user has permission to remove members
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    return res.status(403).json({
                        success: false,
                        message: 'You do not have permission to remove members from this group'
                    });
                }
            }

            // Don't allow removing yourself if you're the only admin
            if (parseInt(userId) === req.session.userId) {
                const [adminCount] = await db.query(`
                    SELECT COUNT(*) as count FROM group_members
                    WHERE group_id = ? AND is_admin = 1
                `, [groupId]);

                if (adminCount[0].count <= 1) {
                    return res.status(400).json({
                        success: false,
                        message: 'Cannot remove yourself as the only admin'
                    });
                }
            }

            // Remove member
            await db.query(`
                DELETE FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, userId]);

            res.json({
                success: true,
                message: 'Member removed successfully'
            });
        } catch (error) {
            console.error('Error removing member:', error);
            res.status(500).json({
                success: false,
                message: 'Error removing member'
            });
        }
    },

    /**
     * Toggle admin status of a group member
     */
    toggleAdmin: async (req, res) => {
        try {
            const groupId = req.params.id;
            const userId = req.params.userId;
            const { is_admin } = req.body;

            // Check if user has permission to change admin status
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    return res.status(403).json({
                        success: false,
                        message: 'You do not have permission to change admin status'
                    });
                }
            }

            // Don't allow removing admin status from yourself if you're the only admin
            if (parseInt(userId) === req.session.userId && !is_admin) {
                const [adminCount] = await db.query(`
                    SELECT COUNT(*) as count FROM group_members
                    WHERE group_id = ? AND is_admin = 1
                `, [groupId]);

                if (adminCount[0].count <= 1) {
                    return res.status(400).json({
                        success: false,
                        message: 'Cannot remove admin status from yourself as the only admin'
                    });
                }
            }

            // Update admin status
            await db.query(`
                UPDATE group_members
                SET is_admin = ?
                WHERE group_id = ? AND user_id = ?
            `, [is_admin ? 1 : 0, groupId, userId]);

            res.json({
                success: true,
                message: 'Admin status updated successfully'
            });
        } catch (error) {
            console.error('Error updating admin status:', error);
            res.status(500).json({
                success: false,
                message: 'Error updating admin status'
            });
        }
    },

    /**
     * Assign an exam to a group
     */
    assignExam: async (req, res) => {
        try {
            const groupId = req.params.id;
            const { exam_id, due_date } = req.body;

            // Validate required fields
            if (!exam_id) {
                return res.status(400).json({
                    success: false,
                    message: 'Exam ID is required'
                });
            }

            // Check if user has permission to assign exams
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    return res.status(403).json({
                        success: false,
                        message: 'You do not have permission to assign exams to this group'
                    });
                }
            }

            // Check if exam is already assigned
            const [existingAssignment] = await db.query(`
                SELECT * FROM group_exam_assignments
                WHERE group_id = ? AND exam_id = ?
            `, [groupId, exam_id]);

            if (existingAssignment.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Exam is already assigned to this group'
                });
            }

            // Assign exam
            await db.query(`
                INSERT INTO group_exam_assignments (group_id, exam_id, assigned_by, due_date)
                VALUES (?, ?, ?, ?)
            `, [groupId, exam_id, req.session.userId, due_date || null]);

            // Get exam details for response
            const [exams] = await db.query(`
                SELECT e.exam_id, e.exam_name, e.description
                FROM exams e
                WHERE e.exam_id = ?
            `, [exam_id]);

            res.json({
                success: true,
                message: 'Exam assigned successfully',
                assignment: {
                    exam_id: parseInt(exam_id),
                    due_date: due_date || null,
                    assigned_by: req.session.userId,
                    ...exams[0]
                }
            });
        } catch (error) {
            console.error('Error assigning exam:', error);
            res.status(500).json({
                success: false,
                message: 'Error assigning exam'
            });
        }
    },

    /**
     * Remove an exam assignment from a group
     */
    removeExam: async (req, res) => {
        try {
            const groupId = req.params.id;
            const examId = req.params.examId;

            // Check if user has permission to remove exam assignments
            if (req.session.userRole !== 'admin') {
                const [userMembership] = await db.query(`
                    SELECT * FROM group_members
                    WHERE group_id = ? AND user_id = ? AND is_admin = 1
                `, [groupId, req.session.userId]);

                if (userMembership.length === 0) {
                    return res.status(403).json({
                        success: false,
                        message: 'You do not have permission to remove exam assignments from this group'
                    });
                }
            }

            // Remove exam assignment
            await db.query(`
                DELETE FROM group_exam_assignments
                WHERE group_id = ? AND exam_id = ?
            `, [groupId, examId]);

            res.json({
                success: true,
                message: 'Exam assignment removed successfully'
            });
        } catch (error) {
            console.error('Error removing exam assignment:', error);
            res.status(500).json({
                success: false,
                message: 'Error removing exam assignment'
            });
        }
    },

    /**
     * Join a group
     */
    join: async (req, res) => {
        try {
            const groupId = req.params.id;
            const userId = req.session.userId;

            // Check if user is already a member
            const [existingMember] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, userId]);

            if (existingMember.length > 0) {
                req.session.flashError = 'You are already a member of this group';
                return res.redirect(`/groups/${groupId}`);
            }

            // Add member
            await db.query(`
                INSERT INTO group_members (group_id, user_id, is_admin)
                VALUES (?, ?, 0)
            `, [groupId, userId]);

            req.session.flashSuccess = 'You have joined the group successfully';
            res.redirect(`/groups/${groupId}`);
        } catch (error) {
            console.error('Error joining group:', error);
            req.session.flashError = 'Error joining group';
            res.redirect(`/groups/${groupId}`);
        }
    },

    /**
     * Leave a group
     */
    leave: async (req, res) => {
        try {
            const groupId = req.params.id;
            const userId = req.session.userId;

            // Check if user is an admin
            const [userMembership] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, userId]);

            if (userMembership.length === 0) {
                req.session.flashError = 'You are not a member of this group';
                return res.redirect(`/groups/${groupId}`);
            }

            // Check if user is the only admin
            if (userMembership[0].is_admin) {
                const [adminCount] = await db.query(`
                    SELECT COUNT(*) as count FROM group_members
                    WHERE group_id = ? AND is_admin = 1
                `, [groupId]);

                if (adminCount[0].count <= 1) {
                    req.session.flashError = 'You cannot leave the group as the only admin';
                    return res.redirect(`/groups/${groupId}`);
                }
            }

            // Remove member
            await db.query(`
                DELETE FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [groupId, userId]);

            req.session.flashSuccess = 'You have left the group successfully';
            res.redirect('/groups');
        } catch (error) {
            console.error('Error leaving group:', error);
            req.session.flashError = 'Error leaving group';
            res.redirect(`/groups/${groupId}`);
        }
    },

    /**
     * Search for users to add to a group
     */
    searchUsers: async (req, res) => {
        try {
            const query = req.query.q || '';
            const groupId = req.params.id;

            if (!query.trim()) {
                return res.json([]);
            }

            // Search for users not in the group
            const [users] = await db.query(`
                SELECT u.id, u.username, u.email, u.profile_image
                FROM users u
                LEFT JOIN group_members gm ON u.id = gm.user_id AND gm.group_id = ?
                WHERE gm.user_id IS NULL
                AND (u.username LIKE ? OR u.email LIKE ?)
                LIMIT 10
            `, [groupId, `%${query}%`, `%${query}%`]);

            res.json(users);
        } catch (error) {
            console.error('Error searching users:', error);
            res.status(500).json({
                success: false,
                message: 'Error searching users'
            });
        }
    },

    /**
     * Search for exams to assign to a group
     */
    searchExams: async (req, res) => {
        try {
            const query = req.query.q || '';
            const groupId = req.params.id;

            if (!query.trim()) {
                return res.json([]);
            }

            // Search for exams not assigned to the group
            const [exams] = await db.query(`
                SELECT e.exam_id, e.exam_name, e.description
                FROM exams e
                LEFT JOIN group_exam_assignments gea ON e.exam_id = gea.exam_id AND gea.group_id = ?
                WHERE gea.exam_id IS NULL
                AND (e.exam_name LIKE ? OR e.description LIKE ?)
                LIMIT 10
            `, [groupId, `%${query}%`, `%${query}%`]);

            res.json(exams);
        } catch (error) {
            console.error('Error searching exams:', error);
            res.status(500).json({
                success: false,
                message: 'Error searching exams'
            });
        }
    },

    /**
     * Create default role-based groups
     * This is called during system initialization or when a new role is added
     */
    createDefaultGroups: async () => {
        try {
            console.log('Creating default role-based groups...');

            // Get all roles
            const [roles] = await db.query(`
                SELECT DISTINCT role FROM users
                WHERE role IS NOT NULL AND role != ''
            `);

            // Create a group for each role if it doesn't exist
            for (const roleObj of roles) {
                const role = roleObj.role;

                // Check if group already exists
                const [existingGroups] = await db.query(`
                    SELECT * FROM groups
                    WHERE role_based = ?
                `, [role]);

                if (existingGroups.length === 0) {
                    // Create group
                    const groupName = `${role.charAt(0).toUpperCase() + role.slice(1)}s`;
                    const description = `Default group for all ${role} users`;

                    await db.query(`
                        INSERT INTO groups (name, description, is_system, role_based)
                        VALUES (?, ?, 1, ?)
                    `, [groupName, description, role]);

                    console.log(`Created default group for ${role} role`);
                }
            }

            // Add all users to their role-based groups
            // Get all users with roles
            const [users] = await db.query(`
                SELECT id, role FROM users
                WHERE role IS NOT NULL AND role != ''
            `);

            // Get all role-based groups
            const [groups] = await db.query(`
                SELECT group_id, role_based FROM groups
                WHERE role_based IS NOT NULL
            `);

            // Create a map of role to group_id
            const roleToGroupMap = {};
            groups.forEach(group => {
                roleToGroupMap[group.role_based] = group.group_id;
            });

            // Add each user to their role-based group
            for (const user of users) {
                if (user.role && roleToGroupMap[user.role]) {
                    try {
                        await db.query(`
                            INSERT IGNORE INTO group_members (group_id, user_id, is_admin)
                            VALUES (?, ?, 0)
                        `, [roleToGroupMap[user.role], user.id]);
                    } catch (err) {
                        console.error(`Error adding user ${user.id} to group: ${err.message}`);
                    }
                }
            }

            console.log('Default role-based groups created successfully');
            return true;
        } catch (error) {
            console.error('Error creating default groups:', error);
            return false;
        }
    },

    /**
     * Add a user to their role-based group
     * This is called when a user is created or their role is changed
     */
    addUserToRoleGroup: async (userId, role) => {
        try {
            if (!role) {
                console.log('No role provided for user ID:', userId);
                return false;
            }

            console.log(`Adding user ${userId} to role group for ${role}`);

            // Check if the groups table exists and has the expected columns
            try {
                const [tableInfo] = await db.query(`
                    SHOW COLUMNS FROM groups
                `);

                const columns = tableInfo.map(col => col.Field);
                console.log('Groups table columns:', columns);

                // Check if the required columns exist
                const requiredColumns = ['role_based', 'is_system'];
                const missingColumns = requiredColumns.filter(col => !columns.includes(col));

                if (missingColumns.length > 0) {
                    console.error(`Missing columns in groups table: ${missingColumns.join(', ')}`);
                    return false;
                }
            } catch (error) {
                console.error('Error checking groups table structure:', error);
                return false;
            }

            // Get the role-based group
            const [groups] = await db.query(`
                SELECT * FROM groups
                WHERE role_based = ?
            `, [role]);

            if (groups.length === 0) {
                // Create the group if it doesn't exist
                const groupName = `${role.charAt(0).toUpperCase() + role.slice(1)}s`;
                const description = `Default group for all ${role} users`;

                console.log(`Creating new role group: ${groupName}`);

                // Check if the column is_system exists, otherwise use is_role_group
                let createQuery;
                try {
                    createQuery = `
                        INSERT INTO groups (name, description, is_system, role_based)
                        VALUES (?, ?, 1, ?)
                    `;

                    const [result] = await db.query(createQuery, [groupName, description, role]);
                    const groupId = result.insertId;
                    console.log(`Created role group with ID: ${groupId}`);

                    // Add user to group
                    await db.query(`
                        INSERT INTO group_members (group_id, user_id, is_admin)
                        VALUES (?, ?, 0)
                    `, [groupId, userId]);

                    console.log(`Added user ${userId} to new group ${groupId}`);
                } catch (error) {
                    // Try alternative column names if the first attempt fails
                    if (error.code === 'ER_BAD_FIELD_ERROR') {
                        console.log('Trying alternative column names for groups table');

                        createQuery = `
                            INSERT INTO groups (name, description, is_role_group, role_type)
                            VALUES (?, ?, 1, ?)
                        `;

                        const [result] = await db.query(createQuery, [groupName, description, role]);
                        const groupId = result.insertId;
                        console.log(`Created role group with ID: ${groupId} using alternative columns`);

                        // Add user to group
                        await db.query(`
                            INSERT INTO group_members (group_id, user_id, is_admin)
                            VALUES (?, ?, 0)
                        `, [groupId, userId]);

                        console.log(`Added user ${userId} to new group ${groupId}`);
                    } else {
                        throw error;
                    }
                }
            } else {
                // Add user to existing group
                const groupId = groups[0].group_id;
                console.log(`Adding user ${userId} to existing group ${groupId}`);

                await db.query(`
                    INSERT IGNORE INTO group_members (group_id, user_id, is_admin)
                    VALUES (?, ?, 0)
                `, [groupId, userId]);

                console.log(`Added user ${userId} to existing group ${groupId}`);
            }

            return true;
        } catch (error) {
            console.error('Error adding user to role group:', error);
            // Don't throw the error to prevent blocking user creation
            return false;
        }
    },

    /**
     * Join a group via invite link
     */
    joinViaInvite: async (req, res) => {
        try {
            const inviteId = req.params.inviteId;
            const userId = req.session.userId;

            // Check if invite exists and is valid
            const [invites] = await db.query(`
                SELECT gi.*, g.name as group_name
                FROM group_invites gi
                JOIN groups g ON gi.group_id = g.group_id
                WHERE gi.invite_id = ?
                AND (gi.expires_at > NOW() OR gi.expires_at IS NULL)
                AND (gi.max_uses IS NULL OR gi.uses < gi.max_uses)
            `, [inviteId]);

            if (invites.length === 0) {
                req.session.flashError = 'Invalid or expired invite link';
                return res.redirect('/groups');
            }

            const invite = invites[0];

            // Check if user is already a member
            const [existingMember] = await db.query(`
                SELECT * FROM group_members
                WHERE group_id = ? AND user_id = ?
            `, [invite.group_id, userId]);

            if (existingMember.length > 0) {
                req.session.flashInfo = 'You are already a member of this group';
                return res.redirect(`/groups/${invite.group_id}`);
            }

            // Add user to group
            await db.query(`
                INSERT INTO group_members (group_id, user_id, is_admin, joined_at)
                VALUES (?, ?, 0, NOW())
            `, [invite.group_id, userId]);

            // Increment invite uses
            await db.query(`
                UPDATE group_invites
                SET uses = uses + 1
                WHERE invite_id = ?
            `, [inviteId]);

            req.session.flashSuccess = `You have joined the group: ${invite.group_name}`;
            res.redirect(`/groups/${invite.group_id}`);
        } catch (error) {
            console.error('Error joining group via invite:', error);
            req.session.flashError = 'Error joining group';
            res.redirect('/groups');
        }
    }
};

// Export controller and utility functions
module.exports = {
    ...groupController,
    getOrCreateRoleGroup,
    addUserToRoleGroup
};
