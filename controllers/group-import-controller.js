/**
 * Group Import Controller
 * Handles importing groups from CSV or Excel files
 */
const db = require('../config/database');
const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * Helper function to parse CSV data
 * @param {string} csvData - CSV data as string
 * @returns {Array} - Array of objects representing rows
 */
async function parseCSV(csvData) {
    return new Promise((resolve, reject) => {
        const results = [];
        const lines = csvData.split('\n');
        
        // Get headers from first line
        const headers = lines[0].split(',').map(header => 
            header.trim().replace(/^\"(.*)\"$/, '$1') // Remove quotes if present
        );
        
        // Process each line
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue; // Skip empty lines
            
            // <PERSON>le quoted fields with commas inside
            const row = {};
            let inQuote = false;
            let currentField = '';
            let fieldIndex = 0;
            
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                
                if (char === '"') {
                    inQuote = !inQuote;
                } else if (char === ',' && !inQuote) {
                    // End of field
                    row[headers[fieldIndex]] = currentField.replace(/^\"(.*)\"$/, '$1'); // Remove quotes if present
                    currentField = '';
                    fieldIndex++;
                } else {
                    currentField += char;
                }
            }
            
            // Add the last field
            if (fieldIndex < headers.length) {
                row[headers[fieldIndex]] = currentField.replace(/^\"(.*)\"$/, '$1'); // Remove quotes if present
            }
            
            results.push(row);
        }
        
        resolve(results);
    });
}

/**
 * Helper function to parse Excel data
 * @param {Buffer} buffer - Excel file buffer
 * @returns {Array} - Array of objects representing rows
 */
async function parseExcel(buffer) {
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    
    // Get the Groups sheet
    const sheetName = workbook.SheetNames.find(name => name.toLowerCase() === 'groups') || workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const data = XLSX.utils.sheet_to_json(worksheet);
    return data;
}

/**
 * Import groups from file
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileType - File type (csv or excel)
 * @param {number} userId - User ID of the importer
 * @returns {Object} - Import results
 */
async function importGroups(fileBuffer, fileType, userId) {
    let groups = [];
    
    // Parse file based on type
    if (fileType === 'csv') {
        const csvData = fileBuffer.toString();
        groups = await parseCSV(csvData);
    } else if (fileType === 'excel') {
        groups = await parseExcel(fileBuffer);
    } else {
        throw new Error('Unsupported file type');
    }
    
    // Validate groups
    if (!Array.isArray(groups) || groups.length === 0) {
        throw new Error('No valid groups found in file');
    }
    
    // Import results
    const results = {
        total: groups.length,
        imported: 0,
        skipped: 0,
        errors: [],
        importedGroups: []
    };
    
    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();
    
    try {
        // Process each group
        for (const group of groups) {
            try {
                // Validate required fields
                if (!group.name) {
                    results.errors.push(`Group without name skipped`);
                    results.skipped++;
                    continue;
                }
                
                // Check if group already exists
                const [existingGroups] = await connection.query(
                    'SELECT * FROM groups WHERE name = ?',
                    [group.name]
                );
                
                if (existingGroups.length > 0) {
                    results.errors.push(`Group "${group.name}" already exists`);
                    results.skipped++;
                    continue;
                }
                
                // Create group
                const [result] = await connection.query(
                    'INSERT INTO groups (name, description, created_by) VALUES (?, ?, ?)',
                    [group.name, group.description || null, userId]
                );
                
                const groupId = result.insertId;
                
                // Add creator as admin
                await connection.query(
                    'INSERT INTO group_members (group_id, user_id, is_admin) VALUES (?, ?, 1)',
                    [groupId, userId]
                );
                
                // Process members if provided
                if (group.members) {
                    const memberEmails = group.members.split(',').map(email => email.trim());
                    
                    // Get user IDs from emails
                    for (const email of memberEmails) {
                        if (!email) continue;
                        
                        const [users] = await connection.query(
                            'SELECT id FROM users WHERE email = ?',
                            [email]
                        );
                        
                        if (users.length > 0) {
                            // Add user to group
                            await connection.query(
                                'INSERT IGNORE INTO group_members (group_id, user_id, is_admin) VALUES (?, ?, 0)',
                                [groupId, users[0].id]
                            );
                        } else {
                            results.errors.push(`User with email "${email}" not found for group "${group.name}"`);
                        }
                    }
                }
                
                results.imported++;
                results.importedGroups.push({
                    name: group.name,
                    id: groupId
                });
            } catch (error) {
                results.errors.push(`Error importing group "${group.name}": ${error.message}`);
                results.skipped++;
            }
        }
        
        // Commit transaction
        await connection.commit();
        return results;
    } catch (error) {
        // Rollback transaction on error
        await connection.rollback();
        throw error;
    } finally {
        connection.release();
    }
}

module.exports = {
    importGroups,
    parseCSV,
    parseExcel
};
