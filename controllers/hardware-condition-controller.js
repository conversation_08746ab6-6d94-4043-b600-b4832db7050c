/**
 * Hardware Condition Controller
 * Handles hardware parts and condition management
 */
const mysql = require('mysql2/promise');
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'exam_prep_platform',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

const db = pool;

/**
 * Update inventory_items table with condition data from hardware_condition table
 * @param {number} itemId - The ID of the inventory item
 * @param {Array} conditions - Array of condition objects with partId, value, and notes
 */
async function updateItemConditionFields(itemId, conditions) {
    try {
        // Get item category to determine hardware type
        const [items] = await db.query(
            'SELECT i.*, c.name AS category_name FROM inventory_items i ' +
            'LEFT JOIN inventory_categories c ON i.category_id = c.category_id ' +
            'WHERE i.item_id = ?',
            [itemId]
        );

        if (items.length === 0) {
            console.error('Item not found for condition update:', itemId);
            return;
        }

        const item = items[0];

        // Get parts information for mapping
        const [parts] = await db.query('SELECT * FROM hardware_parts');
        const partsMap = {};
        parts.forEach(part => {
            partsMap[part.part_id] = part;
        });

        // Map condition values to inventory_items fields
        const conditionFields = {};

        conditions.forEach(condition => {
            const part = partsMap[condition.partId];
            if (!part) return;

            // Map part_name to corresponding field in inventory_items
            switch (part.part_name) {
                case 'physical_damage':
                    conditionFields.physical_damage = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'keyboard':
                    conditionFields.keyboard_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'touchpad':
                    conditionFields.touchpad_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'hdmi_port':
                    conditionFields.hdmi_port_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'ethernet_wifi':
                    conditionFields.ethernet_wifi_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'vga_port':
                    conditionFields.vga_port_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'usb_ports':
                    conditionFields.usb_port_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'speaker_port':
                    conditionFields.speaker_port_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'speakers':
                    conditionFields.speakers_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'display':
                    conditionFields.display_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'cd_drive':
                    conditionFields.cd_drive_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'webcam':
                    conditionFields.webcam_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'charger_port':
                    conditionFields.charger_port_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'os_drivers':
                    conditionFields.os_drivers_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
                case 'laptop_bag':
                    conditionFields.laptop_bag_condition = `${condition.value}${condition.notes ? ': ' + condition.notes : ''}`;
                    break;
            }
        });

        // Only update if we have fields to update
        if (Object.keys(conditionFields).length === 0) {
            console.log('No condition fields to update for item:', itemId);
            return;
        }

        // Build the update query dynamically
        const fields = Object.keys(conditionFields).map(field => `${field} = ?`).join(', ');
        const values = Object.values(conditionFields);
        values.push(itemId); // Add itemId for WHERE clause

        // Update the inventory_items table
        await db.query(
            `UPDATE inventory_items SET ${fields} WHERE item_id = ?`,
            values
        );

        console.log('Updated item condition fields for item:', itemId);
    } catch (error) {
        console.error('Error updating item condition fields:', error);
    }
}

module.exports = {
    /**
     * Get hardware parts by type
     */
    getPartsByType: async (req, res) => {
        try {
            const { hardwareType } = req.params;

            if (!hardwareType) {
                return res.status(400).json({
                    success: false,
                    message: 'Hardware type is required'
                });
            }

            const [parts] = await db.query(
                'SELECT * FROM hardware_parts WHERE hardware_type = ? ORDER BY display_name',
                [hardwareType]
            );

            res.json({
                success: true,
                parts: parts
            });
        } catch (error) {
            console.error('Error fetching hardware parts:', error);
            res.status(500).json({
                success: false,
                message: 'Error fetching hardware parts',
                error: error.message
            });
        }
    },

    /**
     * Get condition data for an item
     */
    getItemCondition: async (req, res) => {
        try {
            const { itemId } = req.params;

            if (!itemId) {
                return res.status(400).json({
                    success: false,
                    message: 'Item ID is required'
                });
            }

            // Get item details
            const [items] = await db.query(
                'SELECT i.*, c.name AS category_name FROM inventory_items i ' +
                'LEFT JOIN inventory_categories c ON i.category_id = c.category_id ' +
                'WHERE i.item_id = ?',
                [itemId]
            );

            if (items.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Item not found'
                });
            }

            const item = items[0];

            // Get condition data
            const [conditions] = await db.query(
                'SELECT hc.*, hp.part_name, hp.display_name, u.username AS checked_by_name ' +
                'FROM hardware_condition hc ' +
                'JOIN hardware_parts hp ON hc.part_id = hp.part_id ' +
                'LEFT JOIN users u ON hc.checked_by = u.id ' +
                'WHERE hc.item_id = ? ' +
                'ORDER BY hp.display_name',
                [itemId]
            );

            res.json({
                success: true,
                item: item,
                conditions: conditions
            });
        } catch (error) {
            console.error('Error fetching item condition:', error);
            res.status(500).json({
                success: false,
                message: 'Error fetching item condition',
                error: error.message
            });
        }
    },

    /**
     * Save condition data for an item
     */
    saveItemCondition: async (req, res) => {
        try {
            const { itemId } = req.params;
            const { conditions, transactionId } = req.body;

            if (!itemId) {
                return res.status(400).json({
                    success: false,
                    message: 'Item ID is required'
                });
            }

            if (!conditions || !Array.isArray(conditions) || conditions.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Condition data is required'
                });
            }

            // Begin transaction
            await db.query('START TRANSACTION');

            // Delete existing condition data for this item if it's a new check
            if (!transactionId) {
                await db.query('DELETE FROM hardware_condition WHERE item_id = ?', [itemId]);
            } else {
                await db.query('DELETE FROM hardware_condition WHERE item_id = ? AND transaction_id = ?', [itemId, transactionId]);
            }

            // Insert new condition data
            for (const condition of conditions) {
                await db.query(
                    'INSERT INTO hardware_condition (item_id, part_id, condition_value, notes, checked_by, transaction_id) VALUES (?, ?, ?, ?, ?, ?)',
                    [
                        itemId,
                        condition.partId,
                        condition.value,
                        condition.notes || null,
                        req.session.userId,
                        transactionId || null
                    ]
                );
            }

            // If this is part of a transaction, mark it as completed
            if (transactionId) {
                await db.query(
                    'UPDATE inventory_transactions SET condition_check_completed = 1 WHERE transaction_id = ?',
                    [transactionId]
                );

                // Update the hardware condition in the inventory_items table
                await updateItemConditionFields(itemId, conditions);
            }

            // Commit transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                message: 'Condition data saved successfully'
            });
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');

            console.error('Error saving item condition:', error);
            res.status(500).json({
                success: false,
                message: 'Error saving item condition',
                error: error.message
            });
        }
    },

    /**
     * Render condition check form
     */
    renderConditionCheckForm: async (req, res) => {
        try {
            const { itemId, transactionId } = req.params;

            // Get item details
            const [items] = await db.query(
                'SELECT i.*, c.name AS category_name FROM inventory_items i ' +
                'LEFT JOIN inventory_categories c ON i.category_id = c.category_id ' +
                'WHERE i.item_id = ?',
                [itemId]
            );

            if (items.length === 0) {
                req.flash('error', 'Item not found');
                return res.redirect('/admin/inventory/items');
            }

            const item = items[0];

            // Determine hardware type from category name
            let hardwareType = 'other';
            if (item.category_name) {
                const categoryLower = item.category_name.toLowerCase();
                if (categoryLower.includes('laptop')) {
                    hardwareType = 'laptop';
                } else if (categoryLower.includes('speaker')) {
                    hardwareType = 'speaker';
                } else if (categoryLower.includes('projector')) {
                    hardwareType = 'projector';
                }
            }

            // Get parts for this hardware type
            const [parts] = await db.query(
                'SELECT * FROM hardware_parts WHERE hardware_type = ? ORDER BY display_name',
                [hardwareType]
            );

            // Get existing condition data if any
            let conditions = [];
            if (transactionId) {
                const [existingConditions] = await db.query(
                    'SELECT hc.*, hp.part_name, hp.display_name ' +
                    'FROM hardware_condition hc ' +
                    'JOIN hardware_parts hp ON hc.part_id = hp.part_id ' +
                    'WHERE hc.item_id = ? AND hc.transaction_id = ? ' +
                    'ORDER BY hp.display_name',
                    [itemId, transactionId]
                );
                conditions = existingConditions;
            } else {
                const [existingConditions] = await db.query(
                    'SELECT hc.*, hp.part_name, hp.display_name ' +
                    'FROM hardware_condition hc ' +
                    'JOIN hardware_parts hp ON hc.part_id = hp.part_id ' +
                    'WHERE hc.item_id = ? ' +
                    'ORDER BY hp.display_name',
                    [itemId]
                );
                conditions = existingConditions;
            }

            // Get transaction details if applicable
            let transaction = null;
            if (transactionId) {
                const [transactions] = await db.query(
                    'SELECT * FROM inventory_transactions WHERE transaction_id = ?',
                    [transactionId]
                );
                if (transactions.length > 0) {
                    transaction = transactions[0];
                }
            }

            res.render('admin/inventory/condition-check', {
                title: 'Hardware Condition Check',
                item: item,
                parts: parts,
                conditions: conditions,
                transaction: transaction,
                hardwareType: hardwareType
            });
        } catch (error) {
            console.error('Error rendering condition check form:', error);
            req.flash('error', 'Error loading condition check form');
            res.redirect('/admin/inventory/items');
        }
    }
};
