/**
 * Help Controller
 * Handles help article management and display
 */
const db = require('../config/database');

const helpController = {
    /**
     * Display help home page with categories
     */
    index: async (req, res) => {
        try {
            // Check if help_categories table exists
            const [tables] = await db.query(`
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
                AND table_name = 'help_categories'
            `);

            if (tables.length === 0) {
                // Tables don't exist, run the migration
                console.log('Help tables not found, running migration...');
                const fs = require('fs');
                const path = require('path');
                const migrationPath = path.join(__dirname, '../database/migrations/create_help_tables_fix.sql');

                if (fs.existsSync(migrationPath)) {
                    const migration = fs.readFileSync(migrationPath, 'utf8');
                    const statements = migration.split(';').filter(stmt => stmt.trim());

                    for (const stmt of statements) {
                        await db.query(stmt);
                    }
                    console.log('Help tables migration completed successfully');
                }
            }

            // Get all help categories
            const [categories] = await db.query(`
                SELECT
                    hc.*,
                    COUNT(ha.article_id) as article_count
                FROM
                    help_categories hc
                LEFT JOIN
                    help_article_categories hac ON hc.category_id = hac.category_id
                LEFT JOIN
                    help_articles ha ON hac.article_id = ha.article_id AND ha.is_published = 1
                GROUP BY
                    hc.category_id
                ORDER BY
                    hc.display_order ASC
            `);

            // Get popular articles
            const [popularArticles] = await db.query(`
                SELECT * FROM help_articles
                WHERE is_published = 1
                ORDER BY view_count DESC
                LIMIT 5
            `);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('help/index', {
                title: 'Help Center',
                pageTitle: 'Help Center',
                categories,
                popularArticles,
                layout,
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading help center:', error);
            req.session.flashError = 'Error loading help center';
            res.redirect('/');
        }
    },

    /**
     * Display articles in a specific category
     */
    category: async (req, res) => {
        try {
            const categorySlug = req.params.category;

            // Get category
            const [categories] = await db.query(`
                SELECT * FROM help_categories
                WHERE name = ? OR category_id = ?
            `, [categorySlug, categorySlug]);

            if (categories.length === 0) {
                req.session.flashError = 'Category not found';
                return res.redirect('/help');
            }

            const category = categories[0];

            // Get articles in this category
            const [articles] = await db.query(`
                SELECT ha.* FROM help_articles ha
                JOIN help_article_categories hac ON ha.article_id = hac.article_id
                WHERE hac.category_id = ? AND ha.is_published = 1
                ORDER BY ha.title ASC
            `, [category.category_id]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('help/category', {
                title: `${category.name} - Help Center`,
                pageTitle: category.name,
                category,
                articles,
                layout,
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading help category:', error);
            req.session.flashError = 'Error loading help category';
            res.redirect('/help');
        }
    },

    /**
     * Display a specific help article
     */
    article: async (req, res) => {
        try {
            const articleSlug = req.params.slug;

            // Get article
            const [articles] = await db.query(`
                SELECT ha.*, u.username as author_name
                FROM help_articles ha
                LEFT JOIN users u ON ha.created_by = u.id
                WHERE ha.slug = ? AND ha.is_published = 1
            `, [articleSlug]);

            if (articles.length === 0) {
                req.session.flashError = 'Article not found';
                return res.redirect('/help');
            }

            const article = articles[0];

            // Get categories for this article
            const [categories] = await db.query(`
                SELECT hc.* FROM help_categories hc
                JOIN help_article_categories hac ON hc.category_id = hac.category_id
                WHERE hac.article_id = ?
            `, [article.article_id]);

            // Get related articles
            const [relatedArticles] = await db.query(`
                SELECT DISTINCT ha.* FROM help_articles ha
                JOIN help_article_categories hac1 ON ha.article_id = hac1.article_id
                JOIN help_article_categories hac2 ON hac1.category_id = hac2.category_id
                WHERE hac2.article_id = ? AND ha.article_id != ? AND ha.is_published = 1
                LIMIT 3
            `, [article.article_id, article.article_id]);

            // Increment view count
            await db.query(`
                UPDATE help_articles
                SET view_count = view_count + 1
                WHERE article_id = ?
            `, [article.article_id]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('help/article', {
                title: `${article.title} - Help Center`,
                pageTitle: article.title,
                article,
                categories,
                relatedArticles,
                layout,
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading help article:', error);
            req.session.flashError = 'Error loading help article';
            res.redirect('/help');
        }
    },

    /**
     * Search help articles
     */
    search: async (req, res) => {
        try {
            const query = req.query.q || '';

            if (!query.trim()) {
                return res.redirect('/help');
            }

            // Search articles
            const [articles] = await db.query(`
                SELECT * FROM help_articles
                WHERE (title LIKE ? OR content LIKE ?) AND is_published = 1
                ORDER BY title ASC
            `, [`%${query}%`, `%${query}%`]);

            // Determine layout based on user role
            const layout = req.session.userRole === 'admin' ? 'admin' : 'user';

            res.render('help/search', {
                title: `Search Results - Help Center`,
                pageTitle: `Search Results for "${query}"`,
                articles,
                query,
                layout,
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error searching help articles:', error);
            req.session.flashError = 'Error searching help articles';
            res.redirect('/help');
        }
    },

    /**
     * Submit feedback for an article
     */
    submitFeedback: async (req, res) => {
        try {
            const { article_id, is_helpful, comment } = req.body;
            const user_id = req.session.userId || null;

            // Insert feedback
            await db.query(`
                INSERT INTO help_feedback (article_id, user_id, is_helpful, comment)
                VALUES (?, ?, ?, ?)
            `, [article_id, user_id, is_helpful === 'true', comment || null]);

            res.json({
                success: true,
                message: 'Feedback submitted successfully'
            });
        } catch (error) {
            console.error('Error submitting feedback:', error);
            res.status(500).json({
                success: false,
                message: 'Error submitting feedback'
            });
        }
    },

    // Admin controllers for managing help content

    /**
     * Display admin help articles management page
     */
    adminIndex: async (req, res) => {
        try {
            // Get all articles with pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Get total count
            const [countResult] = await db.query('SELECT COUNT(*) as total FROM help_articles');
            const total = countResult[0].total;
            const totalPages = Math.ceil(total / perPage);

            // Get articles
            const [articles] = await db.query(`
                SELECT ha.*, u.username as author_name
                FROM help_articles ha
                LEFT JOIN users u ON ha.created_by = u.id
                ORDER BY ha.created_at DESC
                LIMIT ? OFFSET ?
            `, [perPage, offset]);

            res.render('admin/help/index', {
                title: 'Manage Help Articles',
                pageTitle: 'Manage Help Articles',
                articles,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems: total
                },
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading admin help articles:', error);
            req.session.flashError = 'Error loading help articles';
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Display form to create a new help article
     */
    createForm: async (req, res) => {
        try {
            // Get all categories
            const [categories] = await db.query('SELECT * FROM help_categories ORDER BY name ASC');

            res.render('admin/help/create', {
                title: 'Create Help Article',
                pageTitle: 'Create Help Article',
                categories,
                formData: {},
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading create article form:', error);
            req.session.flashError = 'Error loading create article form';
            res.redirect('/admin/help');
        }
    },

    /**
     * Create a new help article
     */
    create: async (req, res) => {
        try {
            const { title, content, category, slug, is_published } = req.body;
            const created_by = req.session.userId;

            // Validate required fields
            if (!title || !content || !slug) {
                req.session.flashError = 'Title, content, and slug are required';
                return res.redirect('/admin/help/create');
            }

            // Create article
            const [result] = await db.query(`
                INSERT INTO help_articles (title, content, slug, is_published, created_by)
                VALUES (?, ?, ?, ?, ?)
            `, [title, content, slug, is_published ? 1 : 0, created_by]);

            const articleId = result.insertId;

            // Add category mappings
            if (category) {
                const categories = Array.isArray(category) ? category : [category];

                for (const categoryId of categories) {
                    await db.query(`
                        INSERT INTO help_article_categories (article_id, category_id)
                        VALUES (?, ?)
                    `, [articleId, categoryId]);
                }
            }

            req.session.flashSuccess = 'Help article created successfully';
            res.redirect('/admin/help');
        } catch (error) {
            console.error('Error creating help article:', error);
            req.session.flashError = 'Error creating help article';
            res.redirect('/admin/help/create');
        }
    },

    /**
     * Display form to edit a help article
     */
    editForm: async (req, res) => {
        try {
            const articleId = req.params.id;

            // Get article
            const [articles] = await db.query('SELECT * FROM help_articles WHERE article_id = ?', [articleId]);

            if (articles.length === 0) {
                req.session.flashError = 'Article not found';
                return res.redirect('/admin/help');
            }

            const article = articles[0];

            // Get all categories
            const [categories] = await db.query('SELECT * FROM help_categories ORDER BY name ASC');

            // Get selected categories
            const [selectedCategories] = await db.query(`
                SELECT category_id FROM help_article_categories
                WHERE article_id = ?
            `, [articleId]);

            const selectedCategoryIds = selectedCategories.map(c => c.category_id);

            res.render('admin/help/edit', {
                title: 'Edit Help Article',
                pageTitle: 'Edit Help Article',
                article,
                categories,
                selectedCategoryIds,
                currentPage: 'help'
            });
        } catch (error) {
            console.error('Error loading edit article form:', error);
            req.session.flashError = 'Error loading edit article form';
            res.redirect('/admin/help');
        }
    },

    /**
     * Update a help article
     */
    update: async (req, res) => {
        try {
            const articleId = req.params.id;
            const { title, content, category, slug, is_published } = req.body;

            // Validate required fields
            if (!title || !content || !slug) {
                req.session.flashError = 'Title, content, and slug are required';
                return res.redirect(`/admin/help/${articleId}/edit`);
            }

            // Update article
            await db.query(`
                UPDATE help_articles
                SET title = ?, content = ?, slug = ?, is_published = ?
                WHERE article_id = ?
            `, [title, content, slug, is_published ? 1 : 0, articleId]);

            // Update category mappings
            await db.query('DELETE FROM help_article_categories WHERE article_id = ?', [articleId]);

            if (category) {
                const categories = Array.isArray(category) ? category : [category];

                for (const categoryId of categories) {
                    await db.query(`
                        INSERT INTO help_article_categories (article_id, category_id)
                        VALUES (?, ?)
                    `, [articleId, categoryId]);
                }
            }

            req.session.flashSuccess = 'Help article updated successfully';
            res.redirect('/admin/help');
        } catch (error) {
            console.error('Error updating help article:', error);
            req.session.flashError = 'Error updating help article';
            res.redirect(`/admin/help/${req.params.id}/edit`);
        }
    },

    /**
     * Delete a help article
     */
    delete: async (req, res) => {
        try {
            const articleId = req.params.id;

            // Delete article (cascades to mappings and feedback)
            await db.query('DELETE FROM help_articles WHERE article_id = ?', [articleId]);

            req.session.flashSuccess = 'Help article deleted successfully';
            res.redirect('/admin/help');
        } catch (error) {
            console.error('Error deleting help article:', error);
            req.session.flashError = 'Error deleting help article';
            res.redirect('/admin/help');
        }
    }
};

module.exports = helpController;
