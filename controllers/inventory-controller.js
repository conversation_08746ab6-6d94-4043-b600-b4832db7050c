/**
 * Inventory Controller
 * Handles IT inventory management functionality
 */
const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const path = require('path');
const fs = require('fs');
const { ensureDirectoryExists, generatePdfFilename, getPdfFilePath } = require('../utils/pdf-utils');

module.exports = {
    /**
     * Display inventory dashboard
     */
    index: async (req, res) => {
        try {
            // Get inventory statistics
            const [itemCount] = await db.query('SELECT COUNT(*) as count FROM inventory_items');
            const [categoryCount] = await db.query('SELECT COUNT(*) as count FROM inventory_categories');
            const [statusCounts] = await db.query(`
                SELECT status, COUNT(*) as count
                FROM inventory_items
                GROUP BY status
            `);

            // Get recent transactions
            const [recentTransactions] = await db.query(`
                SELECT t.*, i.name as item_name,
                       u1.username as issued_to_name,
                       u2.username as issued_by_name,
                       u3.username as received_by_name
                FROM inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.item_id
                LEFT JOIN users u1 ON t.issued_to = u1.id
                LEFT JOIN users u2 ON t.issued_by = u2.id
                LEFT JOIN users u3 ON t.received_by = u3.id
                ORDER BY t.created_at DESC
                LIMIT 10
            `);

            // Format dates for display
            recentTransactions.forEach(transaction => {
                transaction.issued_date = formatDateTime(transaction.issued_date);
                transaction.received_date = transaction.received_date ? formatDateTime(transaction.received_date) : null;
                transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : null;
            });

            res.render('admin/inventory/index', {
                title: 'IT Inventory Management',
                pageTitle: 'IT Inventory Dashboard',
                stats: {
                    itemCount: itemCount[0].count,
                    categoryCount: categoryCount[0].count,
                    statusCounts
                },
                recentTransactions,
                layout: 'admin',
                currentPage: 'inventory'
            });
        } catch (error) {
            console.error('Error loading inventory dashboard:', error);
            req.flash('error', 'Error loading inventory dashboard');
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Display all inventory items
     */
    items: async (req, res) => {
        try {
            // Pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Filtering
            const filters = [];
            const params = [];

            if (req.query.category) {
                filters.push('i.category_id = ?');
                params.push(req.query.category);
            }

            if (req.query.status) {
                filters.push('i.status = ?');
                params.push(req.query.status);
            }

            if (req.query.search) {
                filters.push('(i.name LIKE ? OR i.serial_number LIKE ? OR i.model LIKE ?)');
                params.push(`%${req.query.search}%`, `%${req.query.search}%`, `%${req.query.search}%`);
            }

            const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

            // Get total count
            const [countResult] = await db.query(`
                SELECT COUNT(*) as total
                FROM inventory_items i
                ${whereClause}
            `, params);

            // Get items
            const [items] = await db.query(`
                SELECT i.*, c.name as category_name, u.username as created_by_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                LEFT JOIN users u ON i.created_by = u.id
                ${whereClause}
                ORDER BY i.created_at DESC
                LIMIT ? OFFSET ?
            `, [...params, perPage, offset]);

            // Format dates
            items.forEach(item => {
                item.purchase_date = item.purchase_date ? formatDate(item.purchase_date) : null;
                item.warranty_expiry = item.warranty_expiry ? formatDate(item.warranty_expiry) : null;
                item.created_at = formatDateTime(item.created_at);
            });

            // Get categories for filter
            const [categories] = await db.query('SELECT * FROM inventory_categories ORDER BY name');

            // Get status counts for quick filters
            const [statusCounts] = await db.query(`
                SELECT status, COUNT(*) as count
                FROM inventory_items
                GROUP BY status
            `);

            // Get category counts for quick filters
            const [categoryCounts] = await db.query(`
                SELECT c.category_id, c.name, COUNT(i.item_id) as count
                FROM inventory_categories c
                LEFT JOIN inventory_items i ON c.category_id = i.category_id
                GROUP BY c.category_id
                ORDER BY count DESC
                LIMIT 5
            `);

            const totalItems = countResult[0].total;
            const totalPages = Math.ceil(totalItems / perPage);

            res.render('admin/inventory/items', {
                title: 'Inventory Items',
                pageTitle: 'Inventory Items',
                items,
                categories,
                statusCounts,
                categoryCounts,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems
                },
                query: req.query,
                layout: 'admin',
                currentPage: 'inventory-items'
            });
        } catch (error) {
            console.error('Error fetching inventory items:', error);
            req.flash('error', 'Error loading inventory items');
            res.redirect('/admin/inventory');
        }
    },

    /**
     * Display form to add new inventory item
     */
    addItemForm: async (req, res) => {
        try {
            // Get categories for dropdown
            const [categories] = await db.query('SELECT * FROM inventory_categories ORDER BY name');

            res.render('admin/inventory/add-item', {
                title: 'Add Inventory Item',
                pageTitle: 'Add New Inventory Item',
                categories,
                layout: 'admin',
                currentPage: 'inventory-items'
            });
        } catch (error) {
            console.error('Error loading add item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/inventory/items');
        }
    },

    /**
     * Process add new inventory item
     */
    addItem: async (req, res) => {
        try {
            console.log('Processing inventory item add request');
            console.log('Request body:', req.body);
            console.log('Request file:', req.file);

            const {
                name, description, category_id, serial_number, model,
                manufacturer, purchase_date, purchase_cost, warranty_expiry,
                status, location, notes,
                // Network information fields
                hostname, ip_address, mac_address,
                // Laptop condition fields
                physical_damage, keyboard_condition, touchpad_condition,
                hdmi_port_condition, ethernet_wifi_condition, vga_port_condition,
                usb_port_condition, speaker_port_condition, speakers_condition,
                display_condition, cd_drive_condition, webcam_condition,
                charger_port_condition, os_drivers_condition, laptop_bag_condition
            } = req.body;

            // Get image URL from the uploaded file
            let image = null;
            if (req.file) {
                image = '/uploads/inventory/' + req.file.filename;
                console.log('Image uploaded successfully:', image);
            }

            // Validate required fields
            if (!name) {
                req.flash('error', 'Item name is required');
                return res.redirect('/admin/inventory/items/add');
            }

            // Prepare network information if provided
            let networkInfo = null;
            if (hostname || ip_address || mac_address) {
                networkInfo = JSON.stringify({
                    hostname: hostname || null,
                    ip_address: ip_address || null,
                    mac_address: mac_address || null
                });
            }

            // Prepare hardware condition JSON if any condition fields are provided
            let hardwareCondition = null;
            if (category_id && (physical_damage || keyboard_condition || touchpad_condition ||
                hdmi_port_condition || ethernet_wifi_condition || vga_port_condition ||
                usb_port_condition || speaker_port_condition || speakers_condition ||
                display_condition || cd_drive_condition || webcam_condition ||
                charger_port_condition || os_drivers_condition || laptop_bag_condition)) {
                hardwareCondition = JSON.stringify({
                    physical_damage: physical_damage || 'None',
                    keyboard: keyboard_condition || 'Working',
                    touchpad: touchpad_condition || 'Working',
                    hdmi_port: hdmi_port_condition || 'Working',
                    ethernet_wifi: ethernet_wifi_condition || 'Both Working',
                    vga_port: vga_port_condition || 'Working',
                    usb_ports: usb_port_condition || 'All Working',
                    speaker_port: speaker_port_condition || 'Working',
                    speakers: speakers_condition || 'Working',
                    display: display_condition || 'Perfect',
                    cd_drive: cd_drive_condition || 'Working',
                    webcam: webcam_condition || 'Working',
                    charger_port: charger_port_condition || 'Both Working',
                    os_drivers: os_drivers_condition || 'Fully Functional',
                    laptop_bag: laptop_bag_condition || 'Good Condition'
                });
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Insert new inventory item
                const [result] = await connection.query(`
                    INSERT INTO inventory_items (
                        name, description, category_id, serial_number, model,
                        manufacturer, purchase_date, purchase_cost, warranty_expiry,
                        status, location, notes, image, network_info, hardware_condition,
                        created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                `, [
                    name,
                    description || null,
                    category_id || null,
                    serial_number || null,
                    model || null,
                    manufacturer || null,
                    purchase_date || null,
                    purchase_cost || null,
                    warranty_expiry || null,
                    status || 'available',
                    location || null,
                    notes || null,
                    image,
                    networkInfo,
                    hardwareCondition,
                    req.session.userId
                ]);

                await connection.commit();
                req.flash('success', 'Inventory item added successfully');
                res.redirect('/admin/inventory/items');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error adding inventory item:', error);
            console.error('Error stack:', error.stack);
            if (error.code) {
                console.error('Error code:', error.code);
                console.error('SQL Message:', error.sqlMessage);
            }
            req.flash('error', `Error adding inventory item: ${error.message}`);
            res.redirect('/admin/inventory/items/add');
        }
    },

    /**
     * Display form to edit inventory item
     */
    editItemForm: async (req, res) => {
        try {
            const itemId = req.params.id;

            // Get item details
            const [items] = await db.query(`
                SELECT * FROM inventory_items WHERE item_id = ?
            `, [itemId]);

            if (items.length === 0) {
                req.flash('error', 'Item not found');
                return res.redirect('/admin/inventory/items');
            }

            const item = items[0];

            // Get all images for this item
            const [images] = await db.query(`
                SELECT * FROM inventory_item_images
                WHERE item_id = ?
                ORDER BY is_primary DESC, created_at ASC
            `, [itemId]);

            // Add images to the item object
            item.images = images;

            // Set the primary image for backward compatibility
            if (images.length > 0) {
                const primaryImage = images.find(img => img.is_primary === 1) || images[0];
                item.image = primaryImage.image_url;
            }

            // Get categories for dropdown
            const [categories] = await db.query('SELECT * FROM inventory_categories ORDER BY name');

            res.render('admin/inventory/edit-item', {
                title: 'Edit Inventory Item',
                pageTitle: 'Edit Inventory Item',
                item,
                categories,
                layout: 'admin',
                currentPage: 'inventory-items'
            });
        } catch (error) {
            console.error('Error loading edit item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/inventory/items');
        }
    },

    /**
     * Process edit inventory item
     */
    updateItem: async (req, res) => {
        try {
            console.log('Processing inventory item update request');
            console.log('Request body:', req.body);
            console.log('Request file:', req.file);

            const itemId = req.params.id;
            const {
                name, description, category_id, serial_number, model,
                manufacturer, purchase_date, purchase_cost, warranty_expiry,
                status, location, notes, keep_existing_image,
                // Network information fields
                hostname, ip_address, mac_address,
                // Laptop condition fields
                physical_damage, keyboard_condition, touchpad_condition,
                hdmi_port_condition, ethernet_wifi_condition, vga_port_condition,
                usb_port_condition, speaker_port_condition, speakers_condition,
                display_condition, cd_drive_condition, webcam_condition,
                charger_port_condition, os_drivers_condition, laptop_bag_condition
            } = req.body;

            // Get image URL from the uploaded file or from the hidden field
            let newImage = null;
            if (req.file) {
                newImage = '/uploads/inventory/' + req.file.filename;
                console.log('New image uploaded:', newImage);
            } else if (req.body.image) {
                newImage = req.body.image;
                console.log('Using pre-uploaded image:', newImage);
            }

            // Validate required fields
            if (!name) {
                req.flash('error', 'Item name is required');
                return res.redirect(`/admin/inventory/items/${itemId}/edit`);
            }

            // Prepare network information if provided
            let networkInfo = null;
            if (hostname || ip_address || mac_address) {
                networkInfo = JSON.stringify({
                    hostname: hostname || null,
                    ip_address: ip_address || null,
                    mac_address: mac_address || null
                });
            }

            // Start a transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Update item (without image field)
                await connection.query(`
                    UPDATE inventory_items SET
                        name = ?, description = ?, category_id = ?, serial_number = ?, model = ?,
                        manufacturer = ?, purchase_date = ?, purchase_cost = ?, warranty_expiry = ?,
                        status = ?, location = ?, notes = ?, network_info = ?,
                        physical_damage = ?, keyboard_condition = ?, touchpad_condition = ?,
                        hdmi_port_condition = ?, ethernet_wifi_condition = ?, vga_port_condition = ?,
                        usb_port_condition = ?, speaker_port_condition = ?, speakers_condition = ?,
                        display_condition = ?, cd_drive_condition = ?, webcam_condition = ?,
                        charger_port_condition = ?, os_drivers_condition = ?, laptop_bag_condition = ?
                    WHERE item_id = ?
                `, [
                    name, description, category_id || null, serial_number || null, model || null,
                    manufacturer || null, purchase_date || null, purchase_cost || null, warranty_expiry || null,
                    status || 'available', location || null, notes || null, networkInfo,
                    physical_damage || null, keyboard_condition || null, touchpad_condition || null,
                    hdmi_port_condition || null, ethernet_wifi_condition || null, vga_port_condition || null,
                    usb_port_condition || null, speaker_port_condition || null, speakers_condition || null,
                    display_condition || null, cd_drive_condition || null, webcam_condition || null,
                    charger_port_condition || null, os_drivers_condition || null, laptop_bag_condition || null,
                    itemId
                ]);

                // If we have a new image, add it to the inventory_item_images table
                if (newImage) {
                    // Add the new image as primary and set all others to non-primary
                    await connection.query(`
                        UPDATE inventory_item_images SET is_primary = 0 WHERE item_id = ?
                    `, [itemId]);

                    await connection.query(`
                        INSERT INTO inventory_item_images (item_id, image_url, is_primary, created_by)
                        VALUES (?, ?, 1, ?)
                    `, [itemId, newImage, req.session.userId]);
                }

                // Commit the transaction
                await connection.commit();
            } catch (error) {
                // Rollback the transaction if there's an error
                await connection.rollback();
                throw error;
            } finally {
                // Release the connection
                connection.release();
            }

            req.flash('success', 'Inventory item updated successfully');
            res.redirect('/admin/inventory/items');
        } catch (error) {
            console.error('Error updating inventory item:', error);
            req.flash('error', 'Error updating inventory item');
            res.redirect(`/admin/inventory/items/${req.params.id}/edit`);
        }
    },

    /**
     * Delete inventory item
     */
    deleteItem: async (req, res) => {
        try {
            const itemId = req.params.id;

            // Check if item has transactions
            const [transactions] = await db.query(`
                SELECT COUNT(*) as count FROM inventory_transactions WHERE item_id = ?
            `, [itemId]);

            if (transactions[0].count > 0) {
                req.flash('error', 'Cannot delete item with transaction history');
                return res.redirect('/admin/inventory/items');
            }

            // Delete item
            await db.query('DELETE FROM inventory_items WHERE item_id = ?', [itemId]);

            req.flash('success', 'Inventory item deleted successfully');
            res.redirect('/admin/inventory/items');
        } catch (error) {
            console.error('Error deleting inventory item:', error);
            req.flash('error', 'Error deleting inventory item');
            res.redirect('/admin/inventory/items');
        }
    },

    /**
     * Display categories
     */
    categories: async (req, res) => {
        try {
            // Get all categories
            const [categories] = await db.query(`
                SELECT c.*, COUNT(i.item_id) as item_count
                FROM inventory_categories c
                LEFT JOIN inventory_items i ON c.category_id = i.category_id
                GROUP BY c.category_id
                ORDER BY c.name
            `);

            res.render('admin/inventory/categories', {
                title: 'Inventory Categories',
                pageTitle: 'Inventory Categories',
                categories,
                layout: 'admin',
                currentPage: 'inventory-categories'
            });
        } catch (error) {
            console.error('Error loading inventory categories:', error);
            req.flash('error', 'Error loading inventory categories');
            res.redirect('/admin/inventory');
        }
    },

    /**
     * Process add new category
     */
    addCategory: async (req, res) => {
        try {
            const { name, description } = req.body;

            // Validate required fields
            if (!name) {
                req.flash('error', 'Category name is required');
                return res.redirect('/admin/inventory/categories');
            }

            // Insert new category
            await db.query(`
                INSERT INTO inventory_categories (name, description)
                VALUES (?, ?)
            `, [name, description || null]);

            req.flash('success', 'Category added successfully');
            res.redirect('/admin/inventory/categories');
        } catch (error) {
            console.error('Error adding category:', error);
            req.flash('error', 'Error adding category');
            res.redirect('/admin/inventory/categories');
        }
    },

    /**
     * Process edit category
     */
    updateCategory: async (req, res) => {
        try {
            const categoryId = req.params.id;
            const { name, description } = req.body;

            // Validate required fields
            if (!name) {
                req.flash('error', 'Category name is required');
                return res.redirect('/admin/inventory/categories');
            }

            // Update category
            await db.query(`
                UPDATE inventory_categories
                SET name = ?, description = ?
                WHERE category_id = ?
            `, [name, description || null, categoryId]);

            req.flash('success', 'Category updated successfully');
            res.redirect('/admin/inventory/categories');
        } catch (error) {
            console.error('Error updating category:', error);
            req.flash('error', 'Error updating category');
            res.redirect('/admin/inventory/categories');
        }
    },

    /**
     * Delete category
     */
    deleteCategory: async (req, res) => {
        try {
            const categoryId = req.params.id;

            // Check if category has items
            const [items] = await db.query(`
                SELECT COUNT(*) as count FROM inventory_items WHERE category_id = ?
            `, [categoryId]);

            if (items[0].count > 0) {
                req.flash('error', 'Cannot delete category with items');
                return res.redirect('/admin/inventory/categories');
            }

            // Delete category
            await db.query('DELETE FROM inventory_categories WHERE category_id = ?', [categoryId]);

            req.flash('success', 'Category deleted successfully');
            res.redirect('/admin/inventory/categories');
        } catch (error) {
            console.error('Error deleting category:', error);
            req.flash('error', 'Error deleting category');
            res.redirect('/admin/inventory/categories');
        }
    },

    /**
     * Display transactions
     */
    transactions: async (req, res) => {
        try {
            // Pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Filtering
            const filters = [];
            const params = [];

            if (req.query.type) {
                filters.push('t.transaction_type = ?');
                params.push(req.query.type);
            }

            if (req.query.item) {
                filters.push('t.item_id = ?');
                params.push(req.query.item);
            }

            if (req.query.user) {
                filters.push('(t.issued_to = ? OR t.issued_by = ? OR t.received_by = ?)');
                params.push(req.query.user, req.query.user, req.query.user);
            }

            const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

            // Get total count
            const [countResult] = await db.query(`
                SELECT COUNT(*) as total
                FROM inventory_transactions t
                ${whereClause}
            `, params);

            // Get transactions
            const [transactions] = await db.query(`
                SELECT t.*, i.name as item_name,
                       u1.username as issued_to_name,
                       u2.username as issued_by_name,
                       u3.username as received_by_name
                FROM inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.item_id
                LEFT JOIN users u1 ON t.issued_to = u1.id
                LEFT JOIN users u2 ON t.issued_by = u2.id
                LEFT JOIN users u3 ON t.received_by = u3.id
                ${whereClause}
                ORDER BY t.created_at DESC
                LIMIT ? OFFSET ?
            `, [...params, perPage, offset]);

            // Format dates
            transactions.forEach(transaction => {
                transaction.issued_date = formatDateTime(transaction.issued_date);
                transaction.received_date = transaction.received_date ? formatDateTime(transaction.received_date) : null;
                transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : null;
                transaction.created_at = formatDateTime(transaction.created_at);
            });

            // Get items for filter
            const [items] = await db.query('SELECT item_id, name FROM inventory_items ORDER BY name');

            // Get users for filter
            const [users] = await db.query('SELECT id, username FROM users ORDER BY username');

            const totalItems = countResult[0].total;
            const totalPages = Math.ceil(totalItems / perPage);

            res.render('admin/inventory/transactions', {
                title: 'Inventory Transactions',
                pageTitle: 'Inventory Transactions',
                transactions,
                items,
                users,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems
                },
                query: req.query,
                layout: 'admin',
                currentPage: 'inventory-transactions'
            });
        } catch (error) {
            console.error('Error fetching inventory transactions:', error);
            req.flash('error', 'Error loading inventory transactions');
            res.redirect('/admin/inventory');
        }
    },

    /**
     * Display form to issue item
     */
    issueItemForm: async (req, res) => {
        try {
            // Get available items
            const [items] = await db.query(`
                SELECT item_id, name, serial_number, model
                FROM inventory_items
                WHERE status = 'available'
                ORDER BY name
            `);

            // Get users
            const [users] = await db.query(`
                SELECT id, username, name
                FROM users
                WHERE is_active = 1
                ORDER BY username
            `);

            res.render('admin/inventory/issue-item', {
                title: 'Issue Item',
                pageTitle: 'Issue Inventory Item',
                items,
                users,
                query: req.query,
                layout: 'admin',
                currentPage: 'inventory-transactions'
            });
        } catch (error) {
            console.error('Error loading issue item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/inventory/transactions');
        }
    },

    /**
     * Process issue item
     */
    issueItem: async (req, res) => {
        try {
            const {
                item_id, issued_to, expected_return_date,
                condition_on_issue, notes
            } = req.body;

            // Validate required fields
            if (!item_id || !issued_to) {
                req.flash('error', 'Item and user are required');
                return res.redirect('/admin/inventory/transactions/issue');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Create transaction record
                const [result] = await connection.query(`
                    INSERT INTO inventory_transactions (
                        item_id, transaction_type, issued_to, issued_by,
                        issued_date, expected_return_date, condition_on_issue, notes
                    ) VALUES (?, 'issue', ?, ?, NOW(), ?, ?, ?)
                `, [
                    item_id, issued_to, req.session.userId,
                    expected_return_date || null, condition_on_issue || null, notes || null
                ]);

                // Update item status
                await connection.query(`
                    UPDATE inventory_items
                    SET status = 'assigned'
                    WHERE item_id = ?
                `, [item_id]);

                await connection.commit();

                req.flash('success', 'Item issued successfully');
                res.redirect('/admin/inventory/transactions');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error issuing item:', error);
            req.flash('error', 'Error issuing item');
            res.redirect('/admin/inventory/transactions/issue');
        }
    },

    /**
     * Display form to receive item
     */
    receiveItemForm: async (req, res) => {
        try {
            // Get assigned items
            const [transactions] = await db.query(`
                SELECT t.transaction_id, t.item_id, i.name as item_name,
                       i.serial_number, u.username as issued_to_name,
                       t.issued_date, t.expected_return_date
                FROM inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.item_id
                JOIN users u ON t.issued_to = u.id
                WHERE t.transaction_type = 'issue'
                AND t.received_date IS NULL
                ORDER BY t.issued_date DESC
            `);

            // Format dates
            transactions.forEach(transaction => {
                transaction.issued_date = formatDateTime(transaction.issued_date);
                transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : 'Not specified';
            });

            res.render('admin/inventory/receive-item', {
                title: 'Receive Item',
                pageTitle: 'Receive Inventory Item',
                transactions,
                layout: 'admin',
                currentPage: 'inventory-transactions'
            });
        } catch (error) {
            console.error('Error loading receive item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/inventory/transactions');
        }
    },

    /**
     * Process receive item
     */
    receiveItem: async (req, res) => {
        try {
            const {
                transaction_id, condition_on_return, notes, require_condition_check,
                quick_condition, quick_condition_notes, create_issue_ticket
            } = req.body;

            // Validate required fields
            if (!transaction_id) {
                req.flash('error', 'Transaction is required');
                return res.redirect('/admin/inventory/transactions/receive');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Get item_id from transaction
                const [transactions] = await connection.query(`
                    SELECT t.*, i.name as item_name, i.serial_number, i.category_id, c.name AS category_name,
                           u.username as issued_to_name
                    FROM inventory_transactions t
                    JOIN inventory_items i ON t.item_id = i.item_id
                    LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                    LEFT JOIN users u ON t.issued_to = u.id
                    WHERE t.transaction_id = ?
                `, [transaction_id]);

                if (transactions.length === 0) {
                    throw new Error('Transaction not found');
                }

                const transaction = transactions[0];
                const item_id = transaction.item_id;
                const categoryName = transaction.category_name || '';
                const itemName = transaction.item_name;
                const serialNumber = transaction.serial_number;
                const issuedToName = transaction.issued_to_name;

                // Determine if condition check is required based on category or checkbox
                const needsConditionCheck = require_condition_check === '1' ||
                    categoryName.toLowerCase().includes('laptop') ||
                    categoryName.toLowerCase().includes('speaker') ||
                    categoryName.toLowerCase().includes('projector') ||
                    categoryName.toLowerCase().includes('interactive');

                // Process quick condition assessment
                let conditionIssues = [];
                if (quick_condition && Array.isArray(quick_condition)) {
                    conditionIssues = quick_condition;
                } else if (quick_condition) {
                    conditionIssues = [quick_condition];
                }

                // Update transaction record
                await connection.query(`
                    UPDATE inventory_transactions
                    SET received_date = NOW(), received_by = ?,
                        condition_on_return = ?,
                        notes = CONCAT(IFNULL(notes, ''), '\nReturn notes: ', ?,
                                      IF(? != '', CONCAT('\nCondition issues: ', ?), '')),
                        condition_check_required = ?
                    WHERE transaction_id = ?
                `, [
                    req.session.userId,
                    condition_on_return || null,
                    notes || '',
                    conditionIssues.length > 0 ? conditionIssues.join(', ') : '',
                    conditionIssues.length > 0 ? conditionIssues.join(', ') : '',
                    needsConditionCheck ? 1 : 0,
                    transaction_id
                ]);

                // Update item status
                await connection.query(`
                    UPDATE inventory_items
                    SET status = 'available'
                    WHERE item_id = ?
                `, [item_id]);

                // Create issue ticket if requested
                let issueId = null;
                if (create_issue_ticket === '1' && conditionIssues.length > 0) {
                    // Create issue title based on condition issues
                    const issueTitle = `Hardware issue with ${itemName} (${serialNumber || 'No S/N'})`;

                    // Create issue description
                    let issueDescription = `Issues reported during return from ${issuedToName}:\n\n`;
                    issueDescription += conditionIssues.map(issue => `- ${issue.replace('_', ' ')}`).join('\n');
                    issueDescription += '\n\nAdditional details:\n' + (quick_condition_notes || 'None provided');

                    // Insert issue
                    const [issueResult] = await connection.query(`
                        INSERT INTO it_issues (
                            title, description, item_id, reported_by, issue_type, priority, status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    `, [
                        issueTitle,
                        issueDescription,
                        item_id,
                        req.session.userId,
                        'hardware',
                        condition_on_return === 'Poor' || condition_on_return === 'Damaged' ? 'high' : 'medium',
                        'open'
                    ]);

                    issueId = issueResult.insertId;

                    // Add initial history record
                    await connection.query(`
                        INSERT INTO issue_history (
                            issue_id, changed_by, field_changed, old_value, new_value
                        ) VALUES (?, ?, ?, ?, ?)
                    `, [
                        issueId,
                        req.session.userId,
                        'status',
                        null,
                        'open'
                    ]);
                }

                await connection.commit();

                // If issue was created, show success message
                if (issueId) {
                    req.flash('success', `Item received and issue ticket #${issueId} created.`);

                    // If condition check is also required, redirect to condition check form
                    if (needsConditionCheck) {
                        return res.redirect(`/admin/inventory/condition/check/${item_id}/transaction/${transaction_id}`);
                    }

                    return res.redirect(`/issues/${issueId}`);
                }

                // If condition check is required, redirect to condition check form
                if (needsConditionCheck) {
                    req.flash('success', 'Item received. Please complete the condition check.');
                    return res.redirect(`/admin/inventory/condition/check/${item_id}/transaction/${transaction_id}`);
                }

                req.flash('success', 'Item received successfully');
                res.redirect('/admin/inventory/transactions');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error receiving item:', error);
            req.flash('error', 'Error receiving item');
            res.redirect('/admin/inventory/transactions/receive');
        }
    },

    /**
     * View item details
     */
    viewItem: async (req, res) => {
        try {
            const itemId = req.params.id;

            // Get item details
            const [items] = await db.query(`
                SELECT i.*, c.name as category_name, u.username as created_by_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                LEFT JOIN users u ON i.created_by = u.id
                WHERE i.item_id = ?
            `, [itemId]);

            if (items.length === 0) {
                req.flash('error', 'Item not found');
                return res.redirect('/admin/inventory/items');
            }

            const item = items[0];

            // Format dates
            item.purchase_date = item.purchase_date ? formatDate(item.purchase_date) : null;
            item.warranty_expiry = item.warranty_expiry ? formatDate(item.warranty_expiry) : null;
            item.created_at = formatDateTime(item.created_at);

            // Get all images for this item
            const [images] = await db.query(`
                SELECT * FROM inventory_item_images
                WHERE item_id = ?
                ORDER BY is_primary DESC, created_at ASC
            `, [itemId]);

            // Add images to the item object
            item.images = images;

            // Set the primary image for backward compatibility
            if (images.length > 0) {
                const primaryImage = images.find(img => img.is_primary === 1) || images[0];
                item.image = primaryImage.image_url;
            }

            // Get transaction history
            const [transactions] = await db.query(`
                SELECT t.*,
                       u1.username as issued_to_name,
                       u2.username as issued_by_name,
                       u3.username as received_by_name
                FROM inventory_transactions t
                LEFT JOIN users u1 ON t.issued_to = u1.id
                LEFT JOIN users u2 ON t.issued_by = u2.id
                LEFT JOIN users u3 ON t.received_by = u3.id
                WHERE t.item_id = ?
                ORDER BY t.created_at DESC
            `, [itemId]);

            // Format transaction dates
            transactions.forEach(transaction => {
                transaction.issued_date = formatDateTime(transaction.issued_date);
                transaction.received_date = transaction.received_date ? formatDateTime(transaction.received_date) : null;
                transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : null;
            });

            // Get hardware condition data
            let conditionData = [];
            try {
                const [conditions] = await db.query(`
                    SELECT hc.*, hp.part_name, hp.display_name, u.username AS checked_by_name
                    FROM hardware_condition hc
                    JOIN hardware_parts hp ON hc.part_id = hp.part_id
                    LEFT JOIN users u ON hc.checked_by = u.id
                    WHERE hc.item_id = ?
                    ORDER BY hp.display_name
                `, [itemId]);
                conditionData = conditions;
            } catch (error) {
                console.error('Error fetching condition data:', error);
                // Continue even if condition data fetch fails
            }

            res.render('admin/inventory/view-item', {
                title: 'Item Details',
                pageTitle: `Item Details: ${item.name}`,
                item,
                transactions,
                conditionData,
                layout: 'admin',
                currentPage: 'inventory-items'
            });
        } catch (error) {
            console.error('Error viewing item details:', error);
            req.flash('error', 'Error loading item details');
            res.redirect('/admin/inventory/items');
        }
    },

    /**
     * Display item issue history
     */
    itemIssueHistory: async (req, res) => {
        try {
            const itemId = req.params.id;

            // Get item details
            const [items] = await db.query(`
                SELECT i.*, c.name as category_name, u.username as created_by_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                LEFT JOIN users u ON i.created_by = u.id
                WHERE i.item_id = ?
            `, [itemId]);

            if (items.length === 0) {
                req.flash('error', 'Item not found');
                return res.redirect('/admin/inventory/items');
            }

            const item = items[0];

            // Format dates
            item.purchase_date = item.purchase_date ? formatDate(item.purchase_date) : null;
            item.warranty_expiry = item.warranty_expiry ? formatDate(item.warranty_expiry) : null;
            item.created_at = formatDateTime(item.created_at);

            // Get transaction history
            const [transactions] = await db.query(`
                SELECT t.*, i.name as item_name,
                       u1.username as issued_to_name, u1.name as issued_to_full_name, u1.email as issued_to_email,
                       u2.username as issued_by_name, u2.name as issued_by_full_name,
                       u3.username as received_by_name, u3.name as received_by_full_name
                FROM inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.item_id
                LEFT JOIN users u1 ON t.issued_to = u1.id
                LEFT JOIN users u2 ON t.issued_by = u2.id
                LEFT JOIN users u3 ON t.received_by = u3.id
                WHERE t.item_id = ?
                ORDER BY t.issued_date DESC
            `, [itemId]);

            // Format dates
            transactions.forEach(transaction => {
                transaction.issued_date = formatDateTime(transaction.issued_date);
                transaction.received_date = transaction.received_date ? formatDateTime(transaction.received_date) : null;
                transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : null;
            });

            res.render('admin/inventory/issue-history', {
                title: 'Item Issue History',
                pageTitle: `Issue History: ${item.name}`,
                item,
                transactions,
                layout: 'admin',
                currentPage: 'inventory-items'
            });
        } catch (error) {
            console.error('Error fetching item issue history:', error);
            req.flash('error', 'Error loading item issue history');
            res.redirect('/admin/inventory/items');
        }
    },

    /**
     * Generate loan voucher PDF
     */
    generateLoanVoucher: async (req, res) => {
        try {
            const transactionId = req.params.id;
            const openInNewTab = req.query.newTab === 'true';

            // Get transaction details
            const [transactions] = await db.query(`
                SELECT t.*, i.name as item_name, i.serial_number, i.model, i.manufacturer,
                       u1.username as issued_to_name, u1.name as issued_to_full_name, u1.email as issued_to_email,
                       u2.username as issued_by_name, u2.name as issued_by_full_name,
                       u3.username as received_by_name, u3.name as received_by_full_name
                FROM inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.item_id
                LEFT JOIN users u1 ON t.issued_to = u1.id
                LEFT JOIN users u2 ON t.issued_by = u2.id
                LEFT JOIN users u3 ON t.received_by = u3.id
                WHERE t.transaction_id = ?
            `, [transactionId]);

            if (transactions.length === 0) {
                req.flash('error', 'Transaction not found');
                return res.redirect('/admin/inventory/transactions');
            }

            // Get site settings for logo
            const [siteSettings] = await db.query('SELECT * FROM site_settings');
            const settings = siteSettings.length > 0 ? siteSettings[0] : {};

            const transaction = transactions[0];

            // Format dates for display
            transaction.issued_date_formatted = formatDateTime(transaction.issued_date);
            transaction.expected_return_date_formatted = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : 'Not specified';
            transaction.received_date_formatted = transaction.received_date ? formatDateTime(transaction.received_date) : 'Not returned';

            // Generate a unique filename
            const filename = generatePdfFilename('loan_voucher', transactionId);
            const { outputDir, outputPath } = getPdfFilePath(filename);
            const templatePath = path.join(__dirname, '../views/admin/inventory/loan_voucher_template.ejs');

            // Ensure directory exists
            await ensureDirectoryExists(outputDir);

            // Generate PDF
            const { generatePDF } = require('../utils/pdf-generator');
            await generatePDF({ transaction, siteSettings: settings }, templatePath, outputPath);

            // If open in new tab is requested, serve the file instead of downloading
            if (openInNewTab) {
                // Create a public URL for the file
                const publicPath = `/uploads/vouchers/${path.basename(outputPath)}`;
                return res.json({ success: true, url: publicPath });
            }

            // Send the file as download
            res.download(outputPath, `Loan_Voucher_${transaction.item_name}.pdf`, (err) => {
                if (err) {
                    console.error('Error downloading file:', err);
                    req.flash('error', 'Error generating loan voucher');
                    res.redirect('/admin/inventory/transactions');
                }

                // Delete the file after sending (with a delay to ensure it's fully downloaded)
                setTimeout(() => {
                    fs.unlink(outputPath, (unlinkErr) => {
                        if (unlinkErr) {
                            console.error('Error deleting temporary file:', unlinkErr);
                        }
                    });
                }, 5000); // 5 second delay
            });
        } catch (error) {
            console.error('Error generating loan voucher:', error);
            req.flash('error', 'Error generating loan voucher');
            res.redirect('/admin/inventory/transactions');
        }
    },

    /**
     * Generate inventory reports
     */
    report: async (req, res) => {
        try {
            const reportType = req.query.type || 'items';

            let reportData = [];
            let reportTitle = '';

            if (reportType === 'items') {
                // Items report
                reportTitle = 'Inventory Items Report';

                const [items] = await db.query(`
                    SELECT i.*, c.name as category_name
                    FROM inventory_items i
                    LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                    ORDER BY i.name
                `);

                // Format dates
                items.forEach(item => {
                    item.purchase_date = item.purchase_date ? formatDate(item.purchase_date) : 'N/A';
                    item.warranty_expiry = item.warranty_expiry ? formatDate(item.warranty_expiry) : 'N/A';
                });

                reportData = items;
            } else if (reportType === 'transactions') {
                // Transactions report
                reportTitle = 'Inventory Transactions Report';

                const [transactions] = await db.query(`
                    SELECT t.*, i.name as item_name,
                           u1.username as issued_to_name,
                           u2.username as issued_by_name,
                           u3.username as received_by_name
                    FROM inventory_transactions t
                    JOIN inventory_items i ON t.item_id = i.item_id
                    LEFT JOIN users u1 ON t.issued_to = u1.id
                    LEFT JOIN users u2 ON t.issued_by = u2.id
                    LEFT JOIN users u3 ON t.received_by = u3.id
                    ORDER BY t.created_at DESC
                `);

                // Format dates
                transactions.forEach(transaction => {
                    transaction.issued_date = formatDateTime(transaction.issued_date);
                    transaction.received_date = transaction.received_date ? formatDateTime(transaction.received_date) : 'Not returned';
                    transaction.expected_return_date = transaction.expected_return_date ? formatDate(transaction.expected_return_date) : 'Not specified';
                });

                reportData = transactions;
            } else if (reportType === 'categories') {
                // Categories report
                reportTitle = 'Inventory Categories Report';

                const [categories] = await db.query(`
                    SELECT c.*, COUNT(i.item_id) as item_count
                    FROM inventory_categories c
                    LEFT JOIN inventory_items i ON c.category_id = i.category_id
                    GROUP BY c.category_id
                    ORDER BY c.name
                `);

                reportData = categories;
            }

            res.render('admin/inventory/report', {
                title: reportTitle,
                pageTitle: reportTitle,
                reportType,
                reportData,
                layout: 'admin',
                currentPage: 'inventory-reports'
            });
        } catch (error) {
            console.error('Error generating inventory report:', error);
            req.flash('error', 'Error generating report');
            res.redirect('/admin/inventory');
        }
    }
};
