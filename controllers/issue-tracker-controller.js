/**
 * Issue Tracker Controller
 * Handles IT issue tracking functionality
 */
const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

module.exports = {
    /**
     * Display issue tracker dashboard
     */
    index: async (req, res) => {
        try {
            // Get issue statistics
            const [openIssues] = await db.query(`
                SELECT COUNT(*) as count FROM it_issues WHERE status = 'open'
            `);

            const [inProgressIssues] = await db.query(`
                SELECT COUNT(*) as count FROM it_issues WHERE status = 'in_progress'
            `);

            const [resolvedIssues] = await db.query(`
                SELECT COUNT(*) as count FROM it_issues WHERE status = 'resolved'
            `);

            const [closedIssues] = await db.query(`
                SELECT COUNT(*) as count FROM it_issues WHERE status = 'closed'
            `);

            // Get recent issues
            const [recentIssues] = await db.query(`
                SELECT i.*, u1.username as reported_by_name, u2.username as assigned_to_name,
                       inv.name as item_name
                FROM it_issues i
                LEFT JOIN users u1 ON i.reported_by = u1.id
                LEFT JOIN users u2 ON i.assigned_to = u2.id
                LEFT JOIN inventory_items inv ON i.item_id = inv.item_id
                ORDER BY i.created_at DESC
                LIMIT 10
            `);

            // Format dates
            recentIssues.forEach(issue => {
                issue.created_at = formatDateTime(issue.created_at);
                issue.updated_at = formatDateTime(issue.updated_at);
                issue.resolved_at = issue.resolved_at ? formatDateTime(issue.resolved_at) : null;
            });

            // Check if user is admin
            const isAdmin = req.session.userRole === 'admin';

            res.render('issues/index', {
                title: 'IT Issue Tracker',
                pageTitle: 'IT Issue Tracker Dashboard',
                stats: {
                    openIssues: openIssues[0].count,
                    inProgressIssues: inProgressIssues[0].count,
                    resolvedIssues: resolvedIssues[0].count,
                    closedIssues: closedIssues[0].count
                },
                recentIssues,
                isAdmin,
                currentPage: 'issues',
                user: req.session.user || { username: req.session.username },
                notificationCount: 0
            });
        } catch (error) {
            console.error('Error loading issue tracker dashboard:', error);
            req.flash('error', 'Error loading issue tracker dashboard');
            res.redirect(req.session.userRole === 'admin' ? '/admin/dashboard' : '/dashboard');
        }
    },

    /**
     * Display all issues
     */
    listIssues: async (req, res) => {
        try {
            // Pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Filtering
            const filters = [];
            const params = [];

            if (req.query.status) {
                filters.push('i.status = ?');
                params.push(req.query.status);
            }

            if (req.query.priority) {
                filters.push('i.priority = ?');
                params.push(req.query.priority);
            }

            if (req.query.type) {
                filters.push('i.issue_type = ?');
                params.push(req.query.type);
            }

            if (req.query.item) {
                filters.push('i.item_id = ?');
                params.push(req.query.item);
            }

            if (req.query.search) {
                filters.push('(i.title LIKE ? OR i.description LIKE ?)');
                params.push(`%${req.query.search}%`, `%${req.query.search}%`);
            }

            // If not admin, only show issues reported by or assigned to the user
            if (req.session.userRole !== 'admin') {
                filters.push('(i.reported_by = ? OR i.assigned_to = ?)');
                params.push(req.session.userId, req.session.userId);
            }

            const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

            // Get total count
            const [countResult] = await db.query(`
                SELECT COUNT(*) as total
                FROM it_issues i
                ${whereClause}
            `, params);

            // Get issues
            const [issues] = await db.query(`
                SELECT i.*, u1.username as reported_by_name, u2.username as assigned_to_name,
                       inv.name as item_name
                FROM it_issues i
                LEFT JOIN users u1 ON i.reported_by = u1.id
                LEFT JOIN users u2 ON i.assigned_to = u2.id
                LEFT JOIN inventory_items inv ON i.item_id = inv.item_id
                ${whereClause}
                ORDER BY
                    CASE
                        WHEN i.status = 'open' THEN 1
                        WHEN i.status = 'in_progress' THEN 2
                        WHEN i.status = 'resolved' THEN 3
                        WHEN i.status = 'closed' THEN 4
                    END,
                    CASE
                        WHEN i.priority = 'critical' THEN 1
                        WHEN i.priority = 'high' THEN 2
                        WHEN i.priority = 'medium' THEN 3
                        WHEN i.priority = 'low' THEN 4
                    END,
                    i.created_at DESC
                LIMIT ? OFFSET ?
            `, [...params, perPage, offset]);

            // Format dates
            issues.forEach(issue => {
                issue.created_at = formatDateTime(issue.created_at);
                issue.updated_at = formatDateTime(issue.updated_at);
                issue.resolved_at = issue.resolved_at ? formatDateTime(issue.resolved_at) : null;
            });

            // Get inventory items for filter
            const [items] = await db.query(`
                SELECT item_id, name FROM inventory_items ORDER BY name
            `);

            // Get status counts for quick filters
            let statusCountsQuery = `
                SELECT status, COUNT(*) as count
                FROM it_issues
            `;

            // Apply user permission filter for non-admin users
            if (req.session.userRole !== 'admin') {
                statusCountsQuery += ` WHERE (reported_by = ? OR assigned_to = ?)`;
            }

            statusCountsQuery += ` GROUP BY status`;

            const [statusCounts] = await db.query(
                statusCountsQuery,
                req.session.userRole !== 'admin' ? [req.session.userId, req.session.userId] : []
            );

            // Get priority counts for quick filters
            let priorityCountsQuery = `
                SELECT priority, COUNT(*) as count
                FROM it_issues
            `;

            // Apply user permission filter for non-admin users
            if (req.session.userRole !== 'admin') {
                priorityCountsQuery += ` WHERE (reported_by = ? OR assigned_to = ?)`;
            }

            priorityCountsQuery += ` GROUP BY priority`;

            const [priorityCounts] = await db.query(
                priorityCountsQuery,
                req.session.userRole !== 'admin' ? [req.session.userId, req.session.userId] : []
            );

            // Get type counts for quick filters
            let typeCountsQuery = `
                SELECT issue_type, COUNT(*) as count
                FROM it_issues
            `;

            // Apply user permission filter for non-admin users
            if (req.session.userRole !== 'admin') {
                typeCountsQuery += ` WHERE (reported_by = ? OR assigned_to = ?)`;
            }

            typeCountsQuery += ` GROUP BY issue_type`;

            const [typeCounts] = await db.query(
                typeCountsQuery,
                req.session.userRole !== 'admin' ? [req.session.userId, req.session.userId] : []
            );

            const totalItems = countResult[0].total;
            const totalPages = Math.ceil(totalItems / perPage);

            res.render('issues/list', {
                title: 'IT Issues',
                pageTitle: 'IT Issues',
                issues,
                items,
                statusCounts,
                priorityCounts,
                typeCounts,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems
                },
                query: req.query,
                isAdmin: req.session.userRole === 'admin',
                currentPage: 'issues-list',
                user: req.session.user || { username: req.session.username },
                notificationCount: 0
            });
        } catch (error) {
            console.error('Error fetching issues:', error);
            req.flash('error', 'Error loading issues');
            res.redirect('/issues');
        }
    },

    /**
     * Display form to report a new issue
     */
    reportIssueForm: async (req, res) => {
        try {
            // Get inventory items for dropdown
            const [items] = await db.query(`
                SELECT item_id, name, serial_number FROM inventory_items ORDER BY name
            `);

            res.render('issues/report', {
                title: 'Report IT Issue',
                pageTitle: 'Report IT Issue',
                items,
                currentPage: 'report-issue',
                user: req.session.user || { username: req.session.username },
                notificationCount: 0
            });
        } catch (error) {
            console.error('Error loading report issue form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/issues');
        }
    },

    /**
     * Process new issue report
     */
    reportIssue: async (req, res) => {
        try {
            const {
                title, description, item_id, issue_type, priority,
                condition_issues, condition_notes
            } = req.body;

            // Validate required fields
            if (!title || !description) {
                req.flash('error', 'Title and description are required');
                return res.redirect('/issues/report');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Process hardware condition assessment if provided
                let enhancedDescription = description;

                if (issue_type === 'hardware' && condition_issues && condition_issues.length > 0) {
                    // Get item details if available
                    let itemName = '';
                    let itemSerial = '';

                    if (item_id) {
                        const [items] = await connection.query(
                            'SELECT name, serial_number FROM inventory_items WHERE item_id = ?',
                            [item_id]
                        );

                        if (items.length > 0) {
                            itemName = items[0].name;
                            itemSerial = items[0].serial_number || '';
                        }
                    }

                    // Format condition issues for description
                    const issuesList = Array.isArray(condition_issues)
                        ? condition_issues
                        : [condition_issues];

                    enhancedDescription += '\n\n--- Hardware Condition Assessment ---\n';
                    enhancedDescription += itemName ? `Item: ${itemName} ${itemSerial ? `(${itemSerial})` : ''}\n` : '';
                    enhancedDescription += '\nIssues Reported:\n';

                    issuesList.forEach(issue => {
                        enhancedDescription += `- ${issue.replace('_', ' ')}\n`;
                    });

                    if (condition_notes) {
                        enhancedDescription += `\nAdditional Details:\n${condition_notes}\n`;
                    }

                    enhancedDescription += '\n-----------------------------------';
                }

                // Insert issue
                const [result] = await connection.query(`
                    INSERT INTO it_issues (
                        title, description, item_id, reported_by, issue_type, priority
                    ) VALUES (?, ?, ?, ?, ?, ?)
                `, [
                    title,
                    enhancedDescription,
                    item_id || null,
                    req.session.userId,
                    issue_type || 'hardware',
                    priority || 'medium'
                ]);

                const issueId = result.insertId;

                // Handle file uploads
                if (req.files && req.files.screenshots) {
                    const screenshots = Array.isArray(req.files.screenshots)
                        ? req.files.screenshots
                        : [req.files.screenshots];

                    const uploadDir = path.join(__dirname, '../public/uploads/issues');

                    // Create directory if it doesn't exist
                    if (!fs.existsSync(uploadDir)) {
                        fs.mkdirSync(uploadDir, { recursive: true });
                    }

                    // Define validation constants
                    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
                    const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
                    const uploadedFiles = [];
                    const errors = [];

                    for (const screenshot of screenshots) {
                        // Validate file type
                        if (!validImageTypes.includes(screenshot.mimetype)) {
                            errors.push(`File "${screenshot.name}" is not a valid image type. Allowed types: JPEG, PNG, GIF, WebP, BMP.`);
                            continue;
                        }

                        // Validate file size
                        if (screenshot.size > MAX_FILE_SIZE) {
                            errors.push(`File "${screenshot.name}" exceeds the maximum file size of 5MB.`);
                            continue;
                        }

                        try {
                            // Generate unique filename
                            const uniqueFilename = `${uuidv4()}_${screenshot.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
                            const filePath = path.join(uploadDir, uniqueFilename);

                            // Move file to uploads directory
                            await screenshot.mv(filePath);

                            // Save attachment record
                            await connection.query(`
                                INSERT INTO issue_attachments (
                                    issue_id, file_path, file_name, file_type, file_size, uploaded_by
                                ) VALUES (?, ?, ?, ?, ?, ?)
                            `, [
                                issueId,
                                `/uploads/issues/${uniqueFilename}`,
                                screenshot.name,
                                screenshot.mimetype,
                                screenshot.size,
                                req.session.userId
                            ]);

                            uploadedFiles.push({
                                name: screenshot.name,
                                path: `/uploads/issues/${uniqueFilename}`
                            });
                        } catch (err) {
                            errors.push(`Error uploading "${screenshot.name}": ${err.message}`);
                        }
                    }

                    // If there were errors, add them to the flash message
                    if (errors.length > 0) {
                        req.flash('warning', `Issue reported successfully, but some attachments failed: ${errors.join(', ')}`);
                    }
                }

                // Add initial history record
                await connection.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    req.session.userId,
                    'status',
                    null,
                    'open'
                ]);

                await connection.commit();

                req.flash('success', 'Issue reported successfully');
                res.redirect(`/issues/${issueId}`);
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error reporting issue:', error);
            req.flash('error', 'Error reporting issue');
            res.redirect('/issues/report');
        }
    },

    /**
     * View issue details
     */
    viewIssue: async (req, res) => {
        try {
            const issueId = req.params.id;

            // Get issue details
            const [issues] = await db.query(`
                SELECT i.*, u1.username as reported_by_name, u2.username as assigned_to_name,
                       inv.name as item_name, inv.serial_number as item_serial
                FROM it_issues i
                LEFT JOIN users u1 ON i.reported_by = u1.id
                LEFT JOIN users u2 ON i.assigned_to = u2.id
                LEFT JOIN inventory_items inv ON i.item_id = inv.item_id
                WHERE i.issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                req.flash('error', 'Issue not found');
                return res.redirect('/issues');
            }

            const issue = issues[0];

            // Check if user has access to this issue
            if (req.session.userRole !== 'admin' &&
                issue.reported_by !== req.session.userId &&
                issue.assigned_to !== req.session.userId) {
                req.flash('error', 'You do not have permission to view this issue');
                return res.redirect('/issues');
            }

            // Format dates
            issue.created_at = formatDateTime(issue.created_at);
            issue.updated_at = formatDateTime(issue.updated_at);
            issue.resolved_at = issue.resolved_at ? formatDateTime(issue.resolved_at) : null;

            // Get attachments
            const [attachments] = await db.query(`
                SELECT a.*, u.username as uploaded_by_name
                FROM issue_attachments a
                LEFT JOIN users u ON a.uploaded_by = u.id
                WHERE a.issue_id = ?
                ORDER BY a.uploaded_at DESC
            `, [issueId]);

            // Format attachment dates
            attachments.forEach(attachment => {
                attachment.uploaded_at = formatDateTime(attachment.uploaded_at);
            });

            // Get comments
            const [comments] = await db.query(`
                SELECT c.*, u.username as commented_by_name
                FROM issue_comments c
                LEFT JOIN users u ON c.commented_by = u.id
                WHERE c.issue_id = ?
                ORDER BY c.created_at ASC
            `, [issueId]);

            // Format comment dates
            comments.forEach(comment => {
                comment.created_at = formatDateTime(comment.created_at);
            });

            // Get history
            const [history] = await db.query(`
                SELECT h.*, u.username as changed_by_name
                FROM issue_history h
                LEFT JOIN users u ON h.changed_by = u.id
                WHERE h.issue_id = ?
                ORDER BY h.changed_at DESC
            `, [issueId]);

            // Format history dates
            history.forEach(entry => {
                entry.changed_at = formatDateTime(entry.changed_at);
            });

            // Get admin users for assignment
            const [adminUsers] = await db.query(`
                SELECT id, username, name
                FROM users
                WHERE role = 'admin' AND is_active = 1
                ORDER BY username
            `);

            res.render('issues/view', {
                title: `Issue: ${issue.title}`,
                pageTitle: `Issue #${issue.issue_id}: ${issue.title}`,
                issue,
                attachments,
                comments,
                history,
                adminUsers,
                isAdmin: req.session.userRole === 'admin',
                currentPage: 'issues',
                user: req.session.user || { username: req.session.username },
                notificationCount: 0
            });
        } catch (error) {
            console.error('Error viewing issue:', error);
            req.flash('error', 'Error loading issue details');
            res.redirect('/issues');
        }
    },

    /**
     * Add comment to issue
     */
    addComment: async (req, res) => {
        try {
            const issueId = req.params.id;
            const { comment } = req.body;

            if (!comment) {
                req.flash('error', 'Comment cannot be empty');
                return res.redirect(`/issues/${issueId}`);
            }

            // Insert comment
            await db.query(`
                INSERT INTO issue_comments (
                    issue_id, comment, commented_by
                ) VALUES (?, ?, ?)
            `, [
                issueId,
                comment,
                req.session.userId
            ]);

            req.flash('success', 'Comment added successfully');
            res.redirect(`/issues/${issueId}`);
        } catch (error) {
            console.error('Error adding comment:', error);
            req.flash('error', 'Error adding comment');
            res.redirect(`/issues/${req.params.id}`);
        }
    },

    /**
     * Update issue status
     */
    updateStatus: async (req, res) => {
        try {
            const issueId = req.params.id;
            const { status, resolution_notes } = req.body;

            // Get current issue data
            const [issues] = await db.query(`
                SELECT * FROM it_issues WHERE issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                req.flash('error', 'Issue not found');
                return res.redirect('/issues');
            }

            const issue = issues[0];

            // Check if user has permission to update status
            if (req.session.userRole !== 'admin' &&
                issue.assigned_to !== req.session.userId) {
                req.flash('error', 'You do not have permission to update this issue');
                return res.redirect(`/issues/${issueId}`);
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Update issue status
                let resolved_at = null;
                if (status === 'resolved' && issue.status !== 'resolved') {
                    resolved_at = new Date();
                }

                await connection.query(`
                    UPDATE it_issues
                    SET status = ?,
                        resolution_notes = ?,
                        resolved_at = ?
                    WHERE issue_id = ?
                `, [
                    status,
                    resolution_notes || issue.resolution_notes,
                    resolved_at,
                    issueId
                ]);

                // Add history record
                await connection.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    req.session.userId,
                    'status',
                    issue.status,
                    status
                ]);

                await connection.commit();

                req.flash('success', 'Issue status updated successfully');
                res.redirect(`/issues/${issueId}`);
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error updating issue status:', error);
            req.flash('error', 'Error updating issue status');
            res.redirect(`/issues/${req.params.id}`);
        }
    },

    /**
     * Assign issue to admin
     */
    assignIssue: async (req, res) => {
        try {
            const issueId = req.params.id;
            const { assigned_to } = req.body;

            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                req.flash('error', 'Only administrators can assign issues');
                return res.redirect(`/issues/${issueId}`);
            }

            // Get current issue data
            const [issues] = await db.query(`
                SELECT * FROM it_issues WHERE issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                req.flash('error', 'Issue not found');
                return res.redirect('/issues');
            }

            const issue = issues[0];

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Update issue assignment
                await connection.query(`
                    UPDATE it_issues
                    SET assigned_to = ?
                    WHERE issue_id = ?
                `, [
                    assigned_to || null,
                    issueId
                ]);

                // Add history record
                await connection.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    req.session.userId,
                    'assigned_to',
                    issue.assigned_to,
                    assigned_to || null
                ]);

                // If issue is open and being assigned, change to in_progress
                if (issue.status === 'open' && assigned_to) {
                    await connection.query(`
                        UPDATE it_issues
                        SET status = 'in_progress'
                        WHERE issue_id = ?
                    `, [issueId]);

                    // Add history record for status change
                    await connection.query(`
                        INSERT INTO issue_history (
                            issue_id, changed_by, field_changed, old_value, new_value
                        ) VALUES (?, ?, ?, ?, ?)
                    `, [
                        issueId,
                        req.session.userId,
                        'status',
                        'open',
                        'in_progress'
                    ]);
                }

                await connection.commit();

                req.flash('success', 'Issue assigned successfully');
                res.redirect(`/issues/${issueId}`);
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error assigning issue:', error);
            req.flash('error', 'Error assigning issue');
            res.redirect(`/issues/${req.params.id}`);
        }
    },

    /**
     * Upload additional attachments to an issue
     */
    uploadAttachments: async (req, res) => {
        try {
            const issueId = req.params.id;

            // Check if files were uploaded
            if (!req.files || !req.files.screenshots) {
                req.flash('error', 'No files were uploaded');
                return res.redirect(`/issues/${issueId}`);
            }

            // Get issue details to check permissions
            const [issues] = await db.query(`
                SELECT * FROM it_issues WHERE issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                req.flash('error', 'Issue not found');
                return res.redirect('/issues');
            }

            const issue = issues[0];

            // Check if user has permission to add attachments
            if (req.session.userRole !== 'admin' &&
                issue.reported_by !== req.session.userId &&
                issue.assigned_to !== req.session.userId) {
                req.flash('error', 'You do not have permission to add attachments to this issue');
                return res.redirect(`/issues/${issueId}`);
            }

            // Handle file uploads
            const screenshots = Array.isArray(req.files.screenshots)
                ? req.files.screenshots
                : [req.files.screenshots];

            // Validate number of files
            const MAX_FILES = 5;
            if (screenshots.length > MAX_FILES) {
                req.flash('error', `You can only upload a maximum of ${MAX_FILES} files at once`);
                return res.redirect(`/issues/${issueId}`);
            }

            const uploadDir = path.join(__dirname, '../public/uploads/issues');

            // Create directory if it doesn't exist
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
                const uploadedFiles = [];
                const errors = [];

                for (const screenshot of screenshots) {
                    // Validate file type
                    if (!validImageTypes.includes(screenshot.mimetype)) {
                        errors.push(`File "${screenshot.name}" is not a valid image type. Allowed types: JPEG, PNG, GIF, WebP, BMP.`);
                        continue;
                    }

                    // Validate file size
                    if (screenshot.size > MAX_FILE_SIZE) {
                        errors.push(`File "${screenshot.name}" exceeds the maximum file size of 5MB.`);
                        continue;
                    }

                    try {
                        // Generate unique filename
                        const uniqueFilename = `${uuidv4()}_${screenshot.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
                        const filePath = path.join(uploadDir, uniqueFilename);

                        // Move file to uploads directory
                        await screenshot.mv(filePath);

                        // Save attachment record
                        const [result] = await connection.query(`
                            INSERT INTO issue_attachments (
                                issue_id, file_path, file_name, file_type, file_size, uploaded_by
                            ) VALUES (?, ?, ?, ?, ?, ?)
                        `, [
                            issueId,
                            `/uploads/issues/${uniqueFilename}`,
                            screenshot.name,
                            screenshot.mimetype,
                            screenshot.size,
                            req.session.userId
                        ]);

                        uploadedFiles.push({
                            id: result.insertId,
                            name: screenshot.name,
                            path: `/uploads/issues/${uniqueFilename}`
                        });
                    } catch (err) {
                        errors.push(`Error uploading "${screenshot.name}": ${err.message}`);
                    }
                }

                if (errors.length > 0 && uploadedFiles.length === 0) {
                    // If all files failed, rollback and show error
                    await connection.rollback();
                    req.flash('error', errors.join('<br>'));
                    return res.redirect(`/issues/${issueId}`);
                }

                await connection.commit();

                if (errors.length > 0) {
                    // Some files failed, but some succeeded
                    req.flash('warning', `${uploadedFiles.length} file(s) uploaded successfully. ${errors.length} file(s) failed: ${errors.join(', ')}`);
                } else {
                    // All files succeeded
                    req.flash('success', `${uploadedFiles.length} attachment(s) uploaded successfully`);
                }

                res.redirect(`/issues/${issueId}`);
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error uploading attachments:', error);
            req.flash('error', 'Error uploading attachments: ' + error.message);
            res.redirect(`/issues/${req.params.id}`);
        }
    },

    /**
     * Upload additional attachments to an issue using base64 encoding
     */
    uploadAttachmentsBase64: async (req, res) => {
        try {
            const issueId = req.params.id;
            console.log('Base64 upload request received for issue:', issueId);

            // Validate request body
            if (!req.body || !req.body.image) {
                return res.status(400).json({
                    success: false,
                    message: 'No image data provided'
                });
            }

            // Get image data from request
            const { image, filename, mimetype } = req.body;

            // Validate image data
            if (!image || typeof image !== 'string') {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid image data format'
                });
            }

            // Get issue details to check permissions
            const [issues] = await db.query(`
                SELECT * FROM it_issues WHERE issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Issue not found'
                });
            }

            const issue = issues[0];

            // Check if user has permission to add attachments
            if (req.session.userRole !== 'admin' &&
                issue.reported_by !== req.session.userId &&
                issue.assigned_to !== req.session.userId) {
                return res.status(403).json({
                    success: false,
                    message: 'You do not have permission to add attachments to this issue'
                });
            }

            // Decode base64 string
            let imageBuffer;
            try {
                imageBuffer = Buffer.from(image, 'base64');
            } catch (error) {
                console.error('Error decoding base64 data:', error);
                return res.status(400).json({
                    success: false,
                    message: 'Invalid base64 encoding'
                });
            }

            // Validate decoded data size
            if (imageBuffer.length > 5 * 1024 * 1024) { // 5MB limit
                return res.status(400).json({
                    success: false,
                    message: 'Image is too large. Maximum size is 5MB'
                });
            }

            const uploadDir = path.join(__dirname, '../public/uploads/issues');

            // Create directory if it doesn't exist
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            // Generate a unique filename
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            let ext = '.jpg'; // Default extension

            // Try to get extension from filename or mimetype
            if (filename && filename.includes('.')) {
                ext = filename.substring(filename.lastIndexOf('.'));
            } else if (mimetype) {
                // Map mimetype to extension
                const mimeToExt = {
                    'image/jpeg': '.jpg',
                    'image/png': '.png',
                    'image/gif': '.gif',
                    'image/webp': '.webp',
                    'image/svg+xml': '.svg'
                };
                ext = mimeToExt[mimetype] || '.jpg';
            }

            // Create final filename
            const finalFilename = `issue-${uniqueSuffix}${ext}`;
            const filePath = path.join(uploadDir, finalFilename);

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Write the file
                await new Promise((resolve, reject) => {
                    fs.writeFile(filePath, imageBuffer, (err) => {
                        if (err) {
                            console.error('Error writing file:', err);
                            reject(err);
                        } else {
                            resolve();
                        }
                    });
                });

                // Set file permissions
                try {
                    fs.chmodSync(filePath, 0o666); // Read/write for everyone
                } catch (chmodError) {
                    console.error('Error setting file permissions (non-critical):', chmodError);
                    // Continue anyway, the file is already saved
                }

                // Save attachment record
                await connection.query(`
                    INSERT INTO issue_attachments (
                        issue_id, file_path, file_name, file_type, file_size, uploaded_by
                    ) VALUES (?, ?, ?, ?, ?, ?)
                `, [
                    issueId,
                    `/uploads/issues/${finalFilename}`,
                    filename || finalFilename,
                    mimetype || 'image/jpeg',
                    imageBuffer.length,
                    req.session.userId
                ]);

                await connection.commit();

                console.log('Image uploaded successfully via base64:', `/uploads/issues/${finalFilename}`);

                res.json({
                    success: true,
                    message: 'Attachment uploaded successfully',
                    filePath: `/uploads/issues/${finalFilename}`
                });
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error processing base64 image upload:', error);
            res.status(500).json({
                success: false,
                message: 'Error processing image upload',
                error: error.message
            });
        }
    },

    /**
     * Update issue priority
     */
    updatePriority: async (req, res) => {
        try {
            const issueId = req.params.id;
            const { priority } = req.body;

            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                req.flash('error', 'Only administrators can change priority');
                return res.redirect(`/issues/${issueId}`);
            }

            // Get current issue data
            const [issues] = await db.query(`
                SELECT * FROM it_issues WHERE issue_id = ?
            `, [issueId]);

            if (issues.length === 0) {
                req.flash('error', 'Issue not found');
                return res.redirect('/issues');
            }

            const issue = issues[0];

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Update issue priority
                await connection.query(`
                    UPDATE it_issues
                    SET priority = ?
                    WHERE issue_id = ?
                `, [
                    priority,
                    issueId
                ]);

                // Add history record
                await connection.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    req.session.userId,
                    'priority',
                    issue.priority,
                    priority
                ]);

                await connection.commit();

                req.flash('success', 'Issue priority updated successfully');
                res.redirect(`/issues/${issueId}`);
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error updating issue priority:', error);
            req.flash('error', 'Error updating issue priority');
            res.redirect(`/issues/${req.params.id}`);
        }
    },

    /**
     * Admin dashboard for issue management
     */
    adminDashboard: async (req, res) => {
        try {
            // Check if user is admin
            if (req.session.userRole !== 'admin') {
                req.flash('error', 'Access denied');
                return res.redirect('/issues');
            }

            // Get issue statistics
            const [statusStats] = await db.query(`
                SELECT status, COUNT(*) as count
                FROM it_issues
                GROUP BY status
            `);

            const [priorityStats] = await db.query(`
                SELECT priority, COUNT(*) as count
                FROM it_issues
                GROUP BY priority
            `);

            const [typeStats] = await db.query(`
                SELECT issue_type, COUNT(*) as count
                FROM it_issues
                GROUP BY issue_type
            `);

            // Get unassigned issues
            const [unassignedIssues] = await db.query(`
                SELECT i.*, u.username as reported_by_name,
                       inv.name as item_name
                FROM it_issues i
                LEFT JOIN users u ON i.reported_by = u.id
                LEFT JOIN inventory_items inv ON i.item_id = inv.item_id
                WHERE i.assigned_to IS NULL AND i.status = 'open'
                ORDER BY
                    CASE
                        WHEN i.priority = 'critical' THEN 1
                        WHEN i.priority = 'high' THEN 2
                        WHEN i.priority = 'medium' THEN 3
                        WHEN i.priority = 'low' THEN 4
                    END,
                    i.created_at ASC
                LIMIT 10
            `);

            // Format dates
            unassignedIssues.forEach(issue => {
                issue.created_at = formatDateTime(issue.created_at);
            });

            // Get admin users for assignment
            const [adminUsers] = await db.query(`
                SELECT id, username, name
                FROM users
                WHERE role = 'admin' AND is_active = 1
                ORDER BY username
            `);

            res.render('admin/issues/dashboard', {
                title: 'IT Issue Management',
                pageTitle: 'IT Issue Management Dashboard',
                statusStats,
                priorityStats,
                typeStats,
                unassignedIssues,
                adminUsers,
                layout: 'admin',
                currentPage: 'admin-issues'
            });
        } catch (error) {
            console.error('Error loading admin issue dashboard:', error);
            req.flash('error', 'Error loading issue management dashboard');
            res.redirect('/admin/dashboard');
        }
    }
};
