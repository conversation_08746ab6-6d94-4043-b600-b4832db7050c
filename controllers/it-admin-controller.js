const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// IT Admin Dashboard
exports.getDashboard = async (req, res) => {
  try {
    // Initialize default values
    let inventoryTypeStats = [];
    let inventoryStats = [{ totalDevices: 0, availableDevices: 0, assignedDevices: 0, devicesInRepair: 0, retiredDevices: 0 }];
    let vendorStats = [{ totalVendors: 0 }];
    let repairStats = [{ totalRepairs: 0, sentRepairs: 0, inProgressRepairs: 0, completedRepairs: 0, cancelledRepairs: 0 }];
    let issueStats = [{ totalIssues: 0, openIssues: 0, inProgressIssues: 0, resolvedIssues: 0, closedIssues: 0 }];
    let issueTypeStats = [];
    let recentIssues = [];
    let recentRepairs = [];
    let deviceNameCounts = [{ projectorCount: 0, tabletCount: 0, printerCount: 0, networkCount: 0 }];
    let itInventoryCounts = [{ projectorCount: 0, tabletCount: 0, printerCount: 0, networkCount: 0 }];

    // Check if inventory_items table exists
    const [inventoryItemsTable] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'inventory_items'
    `);

    if (inventoryItemsTable[0].table_exists > 0) {
      try {
        // Get inventory statistics by category
        [inventoryTypeStats] = await db.query(`
          SELECT
            c.name as type,
            c.category_id,
            COUNT(*) as count,
            SUM(CASE WHEN i.status = 'available' THEN 1 ELSE 0 END) as available,
            SUM(CASE WHEN i.status = 'assigned' THEN 1 ELSE 0 END) as assigned,
            SUM(CASE WHEN i.status = 'maintenance' THEN 1 ELSE 0 END) as in_repair,
            SUM(CASE WHEN i.status = 'retired' THEN 1 ELSE 0 END) as retired
          FROM inventory_items i
          LEFT JOIN inventory_categories c ON i.category_id = c.category_id
          GROUP BY c.name, c.category_id
        `);

        // Get overall inventory statistics
        [inventoryStats] = await db.query(`
          SELECT
            COUNT(*) as totalDevices,
            SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as availableDevices,
            SUM(CASE WHEN status = 'assigned' THEN 1 ELSE 0 END) as assignedDevices,
            SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as devicesInRepair,
            SUM(CASE WHEN status = 'retired' THEN 1 ELSE 0 END) as retiredDevices
          FROM inventory_items
        `);

        // Get direct counts for specific device types by searching item names and categories
        const [deviceNameCounts] = await db.query(`
          SELECT
            SUM(CASE
                WHEN name LIKE '%projector%' OR description LIKE '%projector%' OR
                     EXISTS (SELECT 1 FROM inventory_categories c WHERE c.category_id = i.category_id AND c.name LIKE '%projector%')
                THEN 1 ELSE 0 END) as projectorCount,
            SUM(CASE
                WHEN name LIKE '%tablet%' OR description LIKE '%tablet%' OR name LIKE '%ipad%' OR
                     EXISTS (SELECT 1 FROM inventory_categories c WHERE c.category_id = i.category_id AND c.name LIKE '%tablet%')
                THEN 1 ELSE 0 END) as tabletCount,
            SUM(CASE
                WHEN name LIKE '%printer%' OR description LIKE '%printer%' OR
                     EXISTS (SELECT 1 FROM inventory_categories c WHERE c.category_id = i.category_id AND c.name LIKE '%printer%')
                THEN 1 ELSE 0 END) as printerCount,
            SUM(CASE
                WHEN name LIKE '%router%' OR name LIKE '%switch%' OR name LIKE '%network%' OR
                     EXISTS (SELECT 1 FROM inventory_categories c WHERE c.category_id = i.category_id AND c.name LIKE '%network%')
                THEN 1 ELSE 0 END) as networkCount
          FROM inventory_items i
        `);

        // Also check if we have any items in the it_inventory table
        const [itInventoryExists] = await db.query(`
          SELECT COUNT(*) as count FROM information_schema.tables
          WHERE table_schema = DATABASE() AND table_name = 'it_inventory'
        `);

        // If it_inventory table exists, get counts from there too
        let itInventoryCounts = [{ projectorCount: 0, tabletCount: 0, printerCount: 0, networkCount: 0 }];
        if (itInventoryExists[0].count > 0) {
          [itInventoryCounts] = await db.query(`
            SELECT
              SUM(CASE WHEN type = 'projector' THEN 1 ELSE 0 END) as projectorCount,
              SUM(CASE WHEN type = 'tablet' THEN 1 ELSE 0 END) as tabletCount,
              SUM(CASE WHEN type = 'printer' THEN 1 ELSE 0 END) as printerCount,
              SUM(CASE WHEN type = 'network' THEN 1 ELSE 0 END) as networkCount
            FROM it_inventory
          `);
        }
      } catch (error) {
        console.error('Error fetching inventory statistics:', error);
      }
    }

    // Check if repair_vendors table exists
    const [repairVendorsTable] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'repair_vendors'
    `);

    if (repairVendorsTable[0].table_exists > 0) {
      try {
        // Get vendor statistics
        [vendorStats] = await db.query(`
          SELECT COUNT(*) as totalVendors
          FROM repair_vendors
          WHERE is_active = 1
        `);
      } catch (error) {
        console.error('Error fetching vendor statistics:', error);
      }
    }

    // Check if repair_history table exists
    const [repairHistoryTable] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'repair_history'
    `);

    if (repairHistoryTable[0].table_exists > 0) {
      try {
        // Get repair statistics
        [repairStats] = await db.query(`
          SELECT
            COUNT(*) as totalRepairs,
            SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sentRepairs,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inProgressRepairs,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedRepairs,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelledRepairs
          FROM repair_history
        `);

        // Get recent repairs
        [recentRepairs] = await db.query(`
          SELECT r.*, i.name as item_name, v.name as vendor_name
          FROM repair_history r
          LEFT JOIN inventory_items i ON r.item_id = i.item_id
          LEFT JOIN repair_vendors v ON r.vendor_id = v.vendor_id
          ORDER BY r.sent_date DESC
          LIMIT 5
        `);
      } catch (error) {
        console.error('Error fetching repair statistics:', error);
      }
    }

    // Check if it_issues table exists
    const [itIssuesTable] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'it_issues'
    `);

    if (itIssuesTable[0].table_exists > 0) {
      try {
        // Get issue statistics
        [issueStats] = await db.query(`
          SELECT
            COUNT(*) as totalIssues,
            SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as openIssues,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inProgressIssues,
            SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolvedIssues,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closedIssues
          FROM it_issues
        `);

        // Get issue statistics by type
        [issueTypeStats] = await db.query(`
          SELECT
            issue_type,
            COUNT(*) as count
          FROM it_issues
          GROUP BY issue_type
        `);

        // Get recent issues
        [recentIssues] = await db.query(`
          SELECT i.*, ii.name as device_name
          FROM it_issues i
          LEFT JOIN inventory_items ii ON i.item_id = ii.item_id
          ORDER BY i.created_at DESC
          LIMIT 5
        `);
      } catch (error) {
        console.error('Error fetching issue statistics:', error);
      }
    }

    // Get system status (mock data for now)
    const systemStatus = {
      serverLoad: '32%',
      memoryUsage: '45%',
      diskSpace: '28%',
      networkStatus: 'Operational',
      activeUsers: 24,
      lastBackup: '2023-08-15 03:00 AM'
    };

    // Organize inventory type stats into a more usable format
    const deviceTypeStats = {
      laptop: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      desktop: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      tablet: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      projector: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      printer: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      network: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      other: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 }
    };

    // Fill in the actual values if inventoryTypeStats exists
    if (inventoryTypeStats && inventoryTypeStats.length > 0) {
      // Log all categories for debugging
      console.log('Inventory categories found:', inventoryTypeStats.map(stat => ({
        id: stat.category_id,
        name: stat.type,
        count: stat.count
      })));

      inventoryTypeStats.forEach(stat => {
        // Skip null categories
        if (!stat.type) {
          console.log('Skipping null category with count:', stat.count);
          deviceTypeStats.other.count += parseInt(stat.count || 0);
          deviceTypeStats.other.available += parseInt(stat.available || 0);
          deviceTypeStats.other.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.other.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.other.retired += parseInt(stat.retired || 0);
          return;
        }

        const categoryName = stat.type.toLowerCase();

        // Enhanced category detection with more variations
        if (categoryName.includes('laptop') || categoryName.includes('notebook')) {
          deviceTypeStats.laptop.count += parseInt(stat.count || 0);
          deviceTypeStats.laptop.available += parseInt(stat.available || 0);
          deviceTypeStats.laptop.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.laptop.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.laptop.retired += parseInt(stat.retired || 0);
        }
        else if (categoryName.includes('desktop') || categoryName.includes('workstation')) {
          deviceTypeStats.desktop.count += parseInt(stat.count || 0);
          deviceTypeStats.desktop.available += parseInt(stat.available || 0);
          deviceTypeStats.desktop.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.desktop.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.desktop.retired += parseInt(stat.retired || 0);
        }
        else if (categoryName.includes('tablet') || categoryName.includes('ipad')) {
          deviceTypeStats.tablet.count += parseInt(stat.count || 0);
          deviceTypeStats.tablet.available += parseInt(stat.available || 0);
          deviceTypeStats.tablet.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.tablet.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.tablet.retired += parseInt(stat.retired || 0);
        }
        else if (categoryName.includes('projector') ||
                categoryName.includes('interactive') ||
                categoryName.includes('panel') ||
                categoryName.includes('display') ||
                categoryName.includes('audio/video')) {
          console.log('Found projector category:', stat.type, 'with count:', stat.count);
          deviceTypeStats.projector.count += parseInt(stat.count || 0);
          deviceTypeStats.projector.available += parseInt(stat.available || 0);
          deviceTypeStats.projector.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.projector.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.projector.retired += parseInt(stat.retired || 0);
        }
        else if (categoryName.includes('printer') || categoryName.includes('scanner')) {
          deviceTypeStats.printer.count += parseInt(stat.count || 0);
          deviceTypeStats.printer.available += parseInt(stat.available || 0);
          deviceTypeStats.printer.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.printer.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.printer.retired += parseInt(stat.retired || 0);
        }
        else if (categoryName.includes('network') ||
                categoryName.includes('router') ||
                categoryName.includes('switch') ||
                categoryName.includes('access point')) {
          deviceTypeStats.network.count += parseInt(stat.count || 0);
          deviceTypeStats.network.available += parseInt(stat.available || 0);
          deviceTypeStats.network.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.network.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.network.retired += parseInt(stat.retired || 0);
        }
        else {
          console.log('Categorizing as other:', stat.type, 'with count:', stat.count);
          deviceTypeStats.other.count += parseInt(stat.count || 0);
          deviceTypeStats.other.available += parseInt(stat.available || 0);
          deviceTypeStats.other.assigned += parseInt(stat.assigned || 0);
          deviceTypeStats.other.in_repair += parseInt(stat.in_repair || 0);
          deviceTypeStats.other.retired += parseInt(stat.retired || 0);
        }
      });
    }

    // Organize issue type stats
    const issueTypes = {
      hardware: 0,
      software: 0,
      network: 0,
      other: 0
    };

    // Fill in issue type stats if they exist
    if (issueTypeStats && issueTypeStats.length > 0) {
      issueTypeStats.forEach(stat => {
        if (issueTypes.hasOwnProperty(stat.issue_type)) {
          issueTypes[stat.issue_type] = stat.count || 0;
        }
      });
    }

    // Apply direct name-based counts if category-based counts are zero
    if (deviceNameCounts && deviceNameCounts[0]) {
      console.log('Direct device counts from names:', deviceNameCounts[0]);

      // Always use the direct counts for projectors, tablets, printers, and network devices
      // This ensures we count all devices regardless of how they're categorized
      deviceTypeStats.projector.count = parseInt(deviceNameCounts[0].projectorCount || 0);
      deviceTypeStats.tablet.count = parseInt(deviceNameCounts[0].tabletCount || 0);
      deviceTypeStats.printer.count = parseInt(deviceNameCounts[0].printerCount || 0);
      deviceTypeStats.network.count = parseInt(deviceNameCounts[0].networkCount || 0);

      console.log('Updated device counts from inventory_items:', {
        projectors: deviceTypeStats.projector.count,
        tablets: deviceTypeStats.tablet.count,
        printers: deviceTypeStats.printer.count,
        network: deviceTypeStats.network.count
      });
    }

    // Add counts from it_inventory table if it exists
    if (itInventoryCounts && itInventoryCounts[0]) {
      console.log('Device counts from it_inventory:', itInventoryCounts[0]);

      // Add counts from it_inventory to the existing counts
      deviceTypeStats.projector.count += parseInt(itInventoryCounts[0].projectorCount || 0);
      deviceTypeStats.tablet.count += parseInt(itInventoryCounts[0].tabletCount || 0);
      deviceTypeStats.printer.count += parseInt(itInventoryCounts[0].printerCount || 0);
      deviceTypeStats.network.count += parseInt(itInventoryCounts[0].networkCount || 0);

      console.log('Final device counts after combining both tables:', {
        projectors: deviceTypeStats.projector.count,
        tablets: deviceTypeStats.tablet.count,
        printers: deviceTypeStats.printer.count,
        network: deviceTypeStats.network.count
      });
    }

    // Combine all stats with safe defaults
    const stats = {
      totalDevices: inventoryStats[0]?.totalDevices || 0,
      availableDevices: inventoryStats[0]?.availableDevices || 0,
      assignedDevices: inventoryStats[0]?.assignedDevices || 0,
      devicesInRepair: inventoryStats[0]?.devicesInRepair || 0,
      retiredDevices: inventoryStats[0]?.retiredDevices || 0,
      deviceTypeStats,
      totalVendors: vendorStats[0]?.totalVendors || 0,
      totalRepairs: repairStats[0]?.totalRepairs || 0,
      sentRepairs: repairStats[0]?.sentRepairs || 0,
      inProgressRepairs: repairStats[0]?.inProgressRepairs || 0,
      completedRepairs: repairStats[0]?.completedRepairs || 0,
      cancelledRepairs: repairStats[0]?.cancelledRepairs || 0,
      totalIssues: issueStats[0]?.totalIssues || 0,
      openIssues: issueStats[0]?.openIssues || 0,
      inProgressIssues: issueStats[0]?.inProgressIssues || 0,
      resolvedIssues: issueStats[0]?.resolvedIssues || 0,
      closedIssues: issueStats[0]?.closedIssues || 0,
      issueTypes
    };

    res.render('it-admin/dashboard', {
      title: 'IT Admin Dashboard',
      layout: 'layouts/it-admin',
      currentPage: 'dashboard',
      stats,
      recentIssues: recentIssues || [],
      recentRepairs: recentRepairs || [],
      systemStatus,
      formatDate,
      formatDateTime,
      notificationCount: 0 // Default to 0 notifications
    });
  } catch (error) {
    console.error('Error fetching IT admin dashboard data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading dashboard',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin',
      currentPage: 'dashboard',
      notificationCount: 0 // Default to 0 notifications
    });
  }
};

// System Monitoring
exports.getSystemMonitoring = async (req, res) => {
  try {
    // Mock system monitoring data
    const systemData = {
      cpu: {
        usage: '32%',
        temperature: '45°C',
        cores: 4
      },
      memory: {
        total: '16GB',
        used: '7.2GB',
        free: '8.8GB',
        usage: '45%'
      },
      disk: {
        total: '500GB',
        used: '140GB',
        free: '360GB',
        usage: '28%'
      },
      network: {
        status: 'operational',
        upload: '2.5 Mbps',
        download: '45 Mbps',
        activeConnections: 24
      }
    };

    res.render('it-admin/system-monitoring', {
      title: 'System Monitoring',
      layout: 'layouts/it-admin',
      currentPage: 'system-monitoring',
      systemData,
      notificationCount: 0 // Default to 0 notifications
    });
  } catch (error) {
    console.error('Error fetching system monitoring data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading system monitoring',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin',
      currentPage: 'system-monitoring',
      notificationCount: 0 // Default to 0 notifications
    });
  }
};
