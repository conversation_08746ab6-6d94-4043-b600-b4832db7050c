const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Set up storage for uploaded files
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../public/uploads/resources');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Create unique filename
    const uniqueFilename = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// Create multer upload instance
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10 MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept documents, images, PDFs, and common file types
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());

    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only documents, images, and PDFs are allowed'));
    }
  }
}).array('resources', 10); // Allow up to 10 files

// Get all instruction plans
exports.getInstructionPlans = async (req, res) => {
  try {
    // Get all instruction plans with related data
    const [plans] = await db.query(`
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      ORDER BY p.created_at DESC
    `);

    res.render('it-admin/instruction-plans/index', {
      title: 'Instruction Plans',
      layout: 'layouts/it-admin',
      plans,
      currentPage: 'instruction-plans',
      isITAdmin: true
    });
  } catch (error) {
    console.error('Error fetching instruction plans:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plans',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
};

// View instruction plan details
exports.getInstructionPlanDetails = async (req, res) => {
  try {
    const planId = req.params.id;

    // Get plan details
    const [plans] = await db.query(`
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      WHERE p.id = ?
    `, [planId]);

    if (plans.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Instruction plan not found',
        error: { status: 404, stack: 'Instruction plan not found' },
        layout: 'layouts/it-admin'
      });
    }

    // Get resources
    const [resources] = await db.query(`
      SELECT * FROM instruction_plan_resources
      WHERE plan_id = ?
      ORDER BY created_at
    `, [planId]);

    res.render('it-admin/instruction-plans/view', {
      title: 'Instruction Plan Details',
      layout: 'layouts/it-admin',
      plan: plans[0],
      resources,
      currentPage: 'instruction-plans',
      isITAdmin: true
    });
  } catch (error) {
    console.error('Error fetching instruction plan:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plan',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
};

// IT Admin can view but not edit instruction plans
// They can monitor resource usage and system performance
