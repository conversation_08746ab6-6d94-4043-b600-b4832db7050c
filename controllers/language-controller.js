/**
 * Language Controller
 * Handles language switching functionality
 */
const languageController = {
    /**
     * Switch the language and redirect back to the previous page
     */
    switchLanguage: (req, res) => {
        const lang = req.params.lang;
        const referer = req.get('Referer') || '/';
        const isLoginPage = referer.includes('/login');

        console.log(`Switching language to: ${lang}`);
        console.log(`Referer: ${referer}`);
        console.log(`Is login page: ${isLoginPage}`);

        // Validate language
        if (!['en', 'pa'].includes(lang)) {
            console.error(`Invalid language code: ${lang}`);
            return res.status(400).json({
                success: false,
                message: 'Invalid language code'
            });
        }

        // Set the language cookie - make sure it's not httpOnly so JavaScript can read it
        res.cookie('lang', lang, {
            maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
            httpOnly: false, // Allow JavaScript to read this cookie
            path: '/'
        });

        // Set the locale for the current request
        req.setLocale(lang);

        // Log the current locale after setting it
        console.log(`Current locale after setting: ${req.getLocale()}`);

        // Check if this is an AJAX request
        const isAjax = req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1) ||
                      (req.headers['x-requested-with'] === 'XMLHttpRequest');

        // Special handling for login page
        if (isLoginPage) {
            // For login page, redirect to login with the new language parameter
            return res.redirect('/login');
        } else if (isAjax) {
            // For AJAX requests, return JSON response
            return res.json({
                success: true,
                language: lang,
                message: 'Language switched successfully'
            });
        } else {
            // For regular requests, redirect back to the previous page
            res.redirect(referer);
        }
    }
};

module.exports = languageController;
