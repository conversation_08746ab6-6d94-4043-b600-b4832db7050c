/**
 * Procurement Controller
 * Handles all procurement-related functionality
 */

const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const PDFDocument = require('pdfkit');

// Procurement dashboard
exports.index = async (req, res) => {
  try {
    // Get recent procurement requests
    const [procurements] = await db.query(`
      SELECT
        pr.request_id,
        pr.title,
        pr.request_date,
        pr.total_amount,
        pr.status,
        pr.current_step,
        u.username as requested_by_name
      FROM procurement_requests pr
      LEFT JOIN users u ON pr.requested_by = u.id
      ORDER BY pr.created_at DESC
      LIMIT 10
    `);

    // Get procurement statistics
    const [stats] = await db.query(`
      SELECT
        COUNT(*) as total_procurements,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_procurements,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_procurements,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_procurements,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_procurements,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_procurements,
        SUM(total_amount) as total_amount_spent
      FROM procurement_requests
    `);

    res.render('it-admin/procurement/index', {
      title: 'Procurement Management',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      procurements: procurements || [],
      stats: stats[0] || {
        total_procurements: 0,
        draft_procurements: 0,
        in_progress_procurements: 0,
        approved_procurements: 0,
        completed_procurements: 0,
        rejected_procurements: 0,
        total_amount_spent: 0
      },
      formatDate,
      formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 2
        }).format(amount || 0);
      }
    });
  } catch (error) {
    console.error('Error loading procurement dashboard:', error);
    req.flash('error', 'Error loading procurement dashboard');
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading procurement dashboard',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
};

// New procurement form
exports.newProcurement = async (req, res) => {
  try {
    // Get vendors from the repair_vendors table
    const [vendors] = await db.query(`
      SELECT vendor_id, name, contact_person, phone, email, address
      FROM repair_vendors
      WHERE is_active = 1
      ORDER BY name ASC
    `);

    // Check if we're loading a draft
    const draftId = req.query.draft_id;
    let procurement = {
      current_step: 1,
      request_date: new Date().toISOString().split('T')[0]
    };

    let items = [];
    let selectedVendors = [];
    let committeeMembers = [];
    let documents = {};

    // If draft ID is provided, load the draft data
    if (draftId) {
      try {
        // Get procurement request details
        const [procurements] = await db.query(`
          SELECT
            pr.*,
            u.username as requested_by_name
          FROM procurement_requests pr
          LEFT JOIN users u ON pr.requested_by = u.id
          WHERE pr.request_id = ? AND pr.status = 'draft'
        `, [draftId]);

        if (procurements.length === 0) {
          req.flash('error', 'Draft not found or not in draft status');
          return res.redirect('/it-admin/procurement');
        }

        procurement = procurements[0];

        // Get procurement items
        const [itemsResult] = await db.query(`
          SELECT * FROM procurement_items
          WHERE request_id = ?
        `, [draftId]);

        items = itemsResult;

        // Get procurement vendors
        const [vendorsResult] = await db.query(`
          SELECT * FROM procurement_vendors
          WHERE request_id = ?
        `, [draftId]);

        selectedVendors = vendorsResult;

        // Get committee members
        const [committeeMembersResult] = await db.query(`
          SELECT * FROM procurement_committee_members
          WHERE request_id = ?
        `, [draftId]);

        committeeMembers = committeeMembersResult;

        // Get documents
        const [documentsResult] = await db.query(`
          SELECT * FROM procurement_documents
          WHERE request_id = ?
        `, [draftId]);

        // Group documents by type
        documentsResult.forEach(doc => {
          if (!documents[doc.document_type]) {
            documents[doc.document_type] = [];
          }
          documents[doc.document_type].push(doc);
        });

        console.log(`Loaded draft #${draftId} with ${items.length} items, ${selectedVendors.length} vendors, and ${committeeMembers.length} committee members`);
      } catch (error) {
        console.error('Error loading draft data:', error);
        req.flash('error', 'Error loading draft data');
        return res.redirect('/it-admin/procurement');
      }
    }

    res.render('it-admin/procurement/new', {
      title: draftId ? 'Edit Procurement Draft' : 'New Procurement Request',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      procurement,
      vendors: vendors || [],
      items,
      selectedVendors,
      committeeMembers,
      documents,
      formatDate,
      isDraft: !!draftId
    });
  } catch (error) {
    console.error('Error loading procurement form:', error);
    req.flash('error', 'Error loading procurement form');
    res.redirect('/it-admin/procurement');
  }
};

// Save procurement draft
exports.saveDraft = async (req, res) => {
  try {
    // Log the request body to help with debugging
    console.log('Request body keys:', Object.keys(req.body));
    console.log('Files:', req.files ? req.files.length : 0);

    // Extract form data
    const {
      request_id,
      title,
      description,
      request_date,
      department,
      budget_code,
      current_step,
      hq_letter_ref,
      hq_letter_date,
      // Additional fields based on step
      items,
      vendors,
      meeting_date,
      committee_members,
      bill_number,
      bill_date,
      payment_mode,
      payment_date,
      payment_status,
      notes
    } = req.body;

    // Parse JSON strings if they exist
    let parsedItems = [];
    let parsedVendors = [];

    try {
      if (items) {
        parsedItems = typeof items === 'string' ? JSON.parse(items) : items;
        console.log(`Parsed ${Array.isArray(parsedItems) ? parsedItems.length : 0} items from form data`);
      }
    } catch (e) {
      console.error('Error parsing items JSON:', e);
    }

    try {
      if (vendors) {
        parsedVendors = typeof vendors === 'string' ? JSON.parse(vendors) : vendors;
        console.log(`Parsed ${Array.isArray(parsedVendors) ? parsedVendors.length : 0} vendors from form data`);
      }
    } catch (e) {
      console.error('Error parsing vendors JSON:', e);
    }

    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      let procurementId = request_id;

      // If this is a new procurement request
      if (!procurementId) {
        // For new requests, we need to insert with minimal required fields
        const [result] = await connection.query(`
          INSERT INTO procurement_requests (
            title, description, request_date, requested_by, department,
            budget_code, status, current_step
          ) VALUES (?, ?, ?, ?, ?, ?, 'draft', ?)
        `, [
          title || 'New Procurement',
          description || '',
          request_date || new Date().toISOString().split('T')[0],
          req.session.userId,
          department || '',
          budget_code || '',
          current_step || 1
        ]);

        procurementId = result.insertId;
        console.log(`Created new procurement request with ID: ${procurementId}`);
      } else {
        // For existing requests, update only the fields relevant to the current step
        console.log(`Updating procurement request ${procurementId}, current step: ${current_step}`);

        // Base query with fields that are always updated
        let updateQuery = `
          UPDATE procurement_requests SET
            current_step = ?
        `;

        let queryParams = [current_step || 1];

        // Add fields based on the current step
        if (current_step == 1) {
          // Step 1: Request Letter
          updateQuery += `,
            title = ?,
            description = ?,
            request_date = ?,
            department = ?,
            budget_code = ?,
            hq_letter_ref = ?,
            hq_letter_date = ?
          `;

          queryParams.push(
            title || '',
            description || '',
            request_date || null,
            department || '',
            budget_code || '',
            hq_letter_ref || null,
            hq_letter_date || null
          );
        } else if (current_step == 5) {
          // Step 5: Committee
          updateQuery += `,
            meeting_date = ?
          `;

          queryParams.push(meeting_date || null);
        } else if (current_step == 6) {
          // Step 6: Payment
          updateQuery += `,
            bill_number = ?,
            bill_date = ?,
            payment_mode = ?,
            payment_date = ?,
            payment_status = ?
          `;

          queryParams.push(
            bill_number || '',
            bill_date || null,
            payment_mode || '',
            payment_date || null,
            payment_status || 'pending'
          );
        }

        // Add notes field for all steps
        updateQuery += `, notes = ?`;
        queryParams.push(notes || '');

        // Add WHERE clause
        updateQuery += ` WHERE request_id = ?`;
        queryParams.push(procurementId);

        // Execute the update query
        await connection.query(updateQuery, queryParams);

        // Delete existing items and vendors to replace with new ones
        if (parsedItems && parsedItems.length > 0) {
          await connection.query('DELETE FROM procurement_items WHERE request_id = ?', [procurementId]);
        }

        if (parsedVendors && parsedVendors.length > 0) {
          await connection.query('DELETE FROM procurement_vendors WHERE request_id = ?', [procurementId]);
        }

        if (committee_members) {
          await connection.query('DELETE FROM procurement_committee_members WHERE request_id = ?', [procurementId]);
        }
      }

      // Process items if provided and we're in the relevant steps (2-4)
      if (parsedItems && Array.isArray(parsedItems) && (current_step >= 2 && current_step <= 4)) {
        console.log(`Processing ${parsedItems.length} items for step ${current_step}`);
        for (const item of parsedItems) {
          if (!item || !item.name) continue; // Skip invalid items

          await connection.query(`
            INSERT INTO procurement_items (
              request_id, name, description, quantity, estimated_unit_price
            ) VALUES (?, ?, ?, ?, ?)
          `, [
            procurementId,
            item.name || 'Unnamed Item',
            item.description || '',
            item.quantity || 1,
            item.estimated_unit_price || 0
          ]);
        }
      }

      // Process vendors if provided and we're in the relevant steps (2-4)
      if (parsedVendors && Array.isArray(parsedVendors) && (current_step >= 2 && current_step <= 4)) {
        console.log(`Processing ${parsedVendors.length} vendors for step ${current_step}`);
        for (const vendor of parsedVendors) {
          // Get vendor details from repair_vendors table if vendor_id is provided
          let vendorName = vendor.name;
          let contactPerson = vendor.contact_person;
          let phone = vendor.phone;
          let email = vendor.email;
          let address = vendor.address;
          let gstNumber = vendor.gst_number;

          if (vendor.vendor_id) {
            // Get vendor details from repair_vendors table
            const [vendorDetails] = await connection.query(`
              SELECT name, contact_person, phone, email, address
              FROM repair_vendors
              WHERE vendor_id = ?
            `, [vendor.vendor_id]);

            if (vendorDetails.length > 0) {
              vendorName = vendorDetails[0].name;
              contactPerson = vendorDetails[0].contact_person;
              phone = vendorDetails[0].phone;
              email = vendorDetails[0].email;
              address = vendorDetails[0].address;
            }
          }

          const [vendorResult] = await connection.query(`
            INSERT INTO procurement_vendors (
              request_id, name, contact_person, phone, email, address,
              gst_number, quotation_date, quotation_reference, total_amount, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            procurementId, vendorName, contactPerson, phone, email, address,
            gstNumber, vendor.quotation_date, vendor.quotation_reference, vendor.total_amount, vendor.notes
          ]);

          // Process vendor quotation items if provided
          if (vendor.items && Array.isArray(vendor.items)) {
            for (const item of vendor.items) {
              // Calculate total price based on unit price and quantity
              let unitPrice = parseFloat(item.unit_price) || 0;
              let totalPrice = unitPrice;

              // Get item quantity from items array if available
              if (parsedItems && Array.isArray(parsedItems)) {
                const matchingItem = parsedItems.find(i => i.item_id === item.item_id || i.item_id === parseInt(item.item_id));
                if (matchingItem && matchingItem.quantity) {
                  totalPrice = unitPrice * parseInt(matchingItem.quantity);
                }
              }

              await connection.query(`
                INSERT INTO vendor_quotation_items (
                  vendor_id, item_id, unit_price, gst_percentage, gst_amount, total_price, is_lowest
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
              `, [
                vendorResult.insertId, item.item_id, unitPrice, item.gst_percentage || 0,
                item.gst_amount || 0, totalPrice, item.is_lowest || false
              ]);
            }
          }
        }
      }

      // Process committee members if provided and we're in step 5
      if (committee_members && Array.isArray(committee_members) && current_step == 5) {
        console.log(`Processing ${committee_members.length} committee members for step 5`);
        for (const member of committee_members) {
          if (!member || !member.name) continue; // Skip invalid members

          await connection.query(`
            INSERT INTO procurement_committee_members (
              request_id, name, designation
            ) VALUES (?, ?, ?)
          `, [
            procurementId,
            member.name || 'Unnamed Member',
            member.designation || ''
          ]);
        }
      }

      // Handle file uploads with multer
      if (req.files && req.files.length > 0) {
        console.log('Processing files with multer:', req.files.length);

        try {
          // Process each file
          for (const file of req.files) {
            console.log(`Processing file:`, file.fieldname, file.originalname);

            // Determine document type from fieldname
            let documentType = 'document';
            let vendorId = null;
            let shouldProcess = false;

            if (file.fieldname === 'request_letter') {
              documentType = 'request_letter';
              shouldProcess = (current_step == 1); // Only process in step 1
            } else if (file.fieldname.startsWith('quotation_')) {
              documentType = 'quotation';
              shouldProcess = (current_step >= 2 && current_step <= 4); // Process in steps 2-4

              // Extract vendor ID from field name
              // Format: quotation_vendorId
              const fieldNameParts = file.fieldname.split('_');
              if (fieldNameParts.length > 1) {
                const potentialVendorId = fieldNameParts[1];
                if (potentialVendorId && potentialVendorId !== 'undefined' && potentialVendorId !== 'null') {
                  vendorId = potentialVendorId;
                  console.log(`Extracted vendor ID from fieldname: ${vendorId}`);
                }
              }
            } else if (file.fieldname === 'committee_proceedings') {
              documentType = 'committee_proceedings';
              shouldProcess = (current_step == 5); // Only process in step 5
            } else if (file.fieldname === 'bill' || file.fieldname === 'payment_proof') {
              documentType = file.fieldname;
              shouldProcess = (current_step == 6); // Only process in step 6
            }

            // Only process files relevant to the current step
            if (shouldProcess) {
              console.log(`Processing ${documentType} file for step ${current_step}`);

              // Save file information to database
              await connection.query(`
                INSERT INTO procurement_documents (
                  request_id, document_type, vendor_id, file_name, file_path,
                  original_name, mime_type, file_size, uploaded_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                procurementId,
                documentType,
                vendorId,
                file.filename,
                `/uploads/procurement/${file.filename}`,
                file.originalname,
                file.mimetype,
                file.size,
                req.session.userId
              ]);

              console.log(`Saved ${documentType} file to database:`, file.filename);
            } else {
              console.log(`Skipping ${documentType} file as it's not relevant to step ${current_step}`);
            }
          }
        } catch (filesError) {
          console.error('Error processing files:', filesError);
          // Continue with the request even if file processing fails
        }
      } else {
        console.log('No files were uploaded with this request');
      }

      await connection.commit();

      req.flash('success', 'Procurement request saved successfully');
      res.json({
        success: true,
        message: 'Procurement request saved successfully',
        request_id: procurementId
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error saving procurement draft:', error);

    // Handle the "Unexpected end of form" error specifically
    if (error.message && error.message.includes('Unexpected end of form')) {
      console.log('Detected "Unexpected end of form" error - likely a file upload issue');

      // This specific error is usually related to file uploads being interrupted
      return res.status(400).json({
        success: false,
        error: 'File upload error',
        message: 'There was a problem with your file upload. Please try with smaller files or fewer files at once.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    // Handle other common errors with specific messages
    let errorMessage = 'Error saving procurement draft';

    const errorTypes = {
      'ER_NO_SUCH_TABLE': 'Database table not found. Please contact the administrator.',
      'ER_BAD_FIELD_ERROR': 'Invalid field in database query. Please contact the administrator.',
      'ER_DATA_TOO_LONG': 'Data too long for one or more fields. Please shorten your input.',
      'LIMIT_FILE_SIZE': 'File size is too large. Please upload a smaller file.',
      'ENOENT': 'File upload directory not found. Please contact the administrator.'
    };

    // Set error message based on error code if available
    if (error.code && errorTypes[error.code]) {
      errorMessage = errorTypes[error.code];
    }
    // Handle specific error messages
    else if (error.message) {
      if (error.message.includes('Unexpected field')) {
        errorMessage = 'Invalid file upload field. Please contact the administrator.';
      } else {
        // Include the actual error message for better debugging
        errorMessage = `Error saving procurement draft: ${error.message}`;
      }
    }

    // Log the full error for debugging
    console.error('Full error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    // Check if headers have already been sent
    if (res.headersSent) {
      console.error('Headers already sent, cannot send JSON response');
      return;
    }

    // Send a JSON response
    return res.status(500).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get procurement details
exports.getProcurement = async (req, res) => {
  try {
    const requestId = req.params.id;

    // Get procurement request details
    const [procurements] = await db.query(`
      SELECT
        pr.*,
        u.username as requested_by_name
      FROM procurement_requests pr
      LEFT JOIN users u ON pr.requested_by = u.id
      WHERE pr.request_id = ?
    `, [requestId]);

    if (procurements.length === 0) {
      req.flash('error', 'Procurement request not found');
      return res.redirect('/it-admin/procurement');
    }

    const procurement = procurements[0];

    // Get procurement items
    const [items] = await db.query(`
      SELECT * FROM procurement_items
      WHERE request_id = ?
    `, [requestId]);

    // Get procurement vendors
    const [vendors] = await db.query(`
      SELECT * FROM procurement_vendors
      WHERE request_id = ?
    `, [requestId]);

    // Get vendor quotation items
    for (const vendor of vendors) {
      const [quotationItems] = await db.query(`
        SELECT vqi.*, pi.name as item_name
        FROM vendor_quotation_items vqi
        JOIN procurement_items pi ON vqi.item_id = pi.item_id
        WHERE vqi.vendor_id = ?
      `, [vendor.vendor_id]);

      vendor.items = quotationItems;
    }

    // Get committee members
    const [committeeMembers] = await db.query(`
      SELECT * FROM procurement_committee_members
      WHERE request_id = ?
    `, [requestId]);

    // Get documents
    const [documents] = await db.query(`
      SELECT * FROM procurement_documents
      WHERE request_id = ?
    `, [requestId]);

    // Group documents by type
    const documentsByType = {};
    for (const doc of documents) {
      if (!documentsByType[doc.document_type]) {
        documentsByType[doc.document_type] = [];
      }
      documentsByType[doc.document_type].push(doc);
    }

    res.render('it-admin/procurement/edit', {
      title: 'Edit Procurement Request',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      procurement,
      items,
      vendors,
      committeeMembers,
      documents: documentsByType,
      formatDate,
      formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 2
        }).format(amount || 0);
      }
    });
  } catch (error) {
    console.error('Error loading procurement details:', error);
    req.flash('error', 'Error loading procurement details');
    res.redirect('/it-admin/procurement');
  }
};

// List all procurements
exports.listAll = async (req, res) => {
  try {
    // Get filter parameters
    const { status, search, dateFrom, dateTo } = req.query;

    // Build query with filters
    let query = `
      SELECT
        pr.request_id,
        pr.title,
        pr.request_date,
        pr.total_amount,
        pr.status,
        pr.current_step,
        u.username as requested_by_name
      FROM procurement_requests pr
      LEFT JOIN users u ON pr.requested_by = u.id
      WHERE 1=1
    `;

    const queryParams = [];

    // Apply status filter
    if (status) {
      query += ` AND pr.status = ?`;
      queryParams.push(status);
    }

    // Apply search filter
    if (search) {
      query += ` AND (pr.title LIKE ? OR pr.description LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Apply date range filter
    if (dateFrom) {
      query += ` AND pr.request_date >= ?`;
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      query += ` AND pr.request_date <= ?`;
      queryParams.push(dateTo);
    }

    // Add order by
    query += ` ORDER BY pr.created_at DESC`;

    // Execute query
    const [procurements] = await db.query(query, queryParams);

    res.render('it-admin/procurement/list', {
      title: 'All Procurement Requests',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      procurements: procurements || [],
      filters: {
        status: status || '',
        search: search || '',
        dateFrom: dateFrom || '',
        dateTo: dateTo || ''
      },
      formatDate,
      formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 2
        }).format(amount || 0);
      }
    });
  } catch (error) {
    console.error('Error loading procurement list:', error);
    req.flash('error', 'Error loading procurement list');
    res.redirect('/it-admin/procurement');
  }
};

// List draft procurements
exports.listDrafts = async (req, res) => {
  try {
    // Get draft procurements
    const [procurements] = await db.query(`
      SELECT
        pr.request_id,
        pr.title,
        pr.request_date,
        pr.total_amount,
        pr.status,
        pr.current_step,
        u.username as requested_by_name
      FROM procurement_requests pr
      LEFT JOIN users u ON pr.requested_by = u.id
      WHERE pr.status = 'draft'
      ORDER BY pr.updated_at DESC
    `);

    res.render('it-admin/procurement/drafts', {
      title: 'Draft Procurement Requests',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      procurements: procurements || [],
      formatDate,
      formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 2
        }).format(amount || 0);
      }
    });
  } catch (error) {
    console.error('Error loading draft procurements:', error);
    req.flash('error', 'Error loading draft procurements');
    res.redirect('/it-admin/procurement');
  }
};

// Generate PDF report
exports.generatePDF = async (req, res) => {
  try {
    const requestId = req.params.id;

    // Get procurement request details
    const [procurements] = await db.query(`
      SELECT
        pr.*,
        u.username as requested_by_name
      FROM procurement_requests pr
      LEFT JOIN users u ON pr.requested_by = u.id
      WHERE pr.request_id = ?
    `, [requestId]);

    if (procurements.length === 0) {
      req.flash('error', 'Procurement request not found');
      return res.redirect('/it-admin/procurement');
    }

    const procurement = procurements[0];

    // Get procurement items
    const [items] = await db.query(`
      SELECT * FROM procurement_items
      WHERE request_id = ?
    `, [requestId]);

    // Get procurement vendors
    const [vendors] = await db.query(`
      SELECT * FROM procurement_vendors
      WHERE request_id = ?
    `, [requestId]);

    // Get vendor quotation items
    for (const vendor of vendors) {
      const [quotationItems] = await db.query(`
        SELECT vqi.*, pi.name as item_name
        FROM vendor_quotation_items vqi
        JOIN procurement_items pi ON vqi.item_id = pi.item_id
        WHERE vqi.vendor_id = ?
      `, [vendor.vendor_id]);

      vendor.items = quotationItems;
    }

    // Get committee members
    const [committeeMembers] = await db.query(`
      SELECT * FROM procurement_committee_members
      WHERE request_id = ?
    `, [requestId]);

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=procurement-${requestId}.pdf`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(20).text('Procurement Report', { align: 'center' });
    doc.moveDown();

    doc.fontSize(16).text('Request Details');
    doc.moveDown(0.5);
    doc.fontSize(12).text(`Title: ${procurement.title}`);
    doc.fontSize(12).text(`Request Date: ${formatDate(procurement.request_date)}`);
    doc.fontSize(12).text(`Requested By: ${procurement.requested_by_name}`);
    doc.fontSize(12).text(`Department: ${procurement.department || 'N/A'}`);
    doc.fontSize(12).text(`Status: ${procurement.status}`);
    doc.moveDown();

    if (procurement.description) {
      doc.fontSize(12).text(`Description: ${procurement.description}`);
      doc.moveDown();
    }

    // Add items
    if (items.length > 0) {
      doc.fontSize(16).text('Items');
      doc.moveDown(0.5);

      // Create items table
      const itemsTable = {
        headers: ['Name', 'Description', 'Quantity', 'Est. Unit Price'],
        rows: []
      };

      for (const item of items) {
        itemsTable.rows.push([
          item.name,
          item.description || 'N/A',
          item.quantity.toString(),
          `₹${item.estimated_unit_price || 0}`
        ]);
      }

      // Draw items table
      doc.table(itemsTable, {
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10)
      });

      doc.moveDown();
    }

    // Add vendors
    if (vendors.length > 0) {
      doc.fontSize(16).text('Vendors');
      doc.moveDown(0.5);

      for (const vendor of vendors) {
        doc.fontSize(12).text(`Vendor: ${vendor.name}`);
        doc.fontSize(10).text(`Contact: ${vendor.contact_person || 'N/A'}`);
        doc.fontSize(10).text(`Phone: ${vendor.phone || 'N/A'}`);
        doc.fontSize(10).text(`Email: ${vendor.email || 'N/A'}`);
        doc.fontSize(10).text(`Quotation Date: ${formatDate(vendor.quotation_date) || 'N/A'}`);
        doc.fontSize(10).text(`Total Amount: ₹${vendor.total_amount || 0}`);
        doc.moveDown();
      }
    }

    // Add committee members
    if (committeeMembers.length > 0) {
      doc.fontSize(16).text('Committee Members');
      doc.moveDown(0.5);

      for (const member of committeeMembers) {
        doc.fontSize(12).text(`${member.name} - ${member.designation || 'N/A'}`);
      }

      doc.moveDown();
    }

    // Add payment details
    if (procurement.bill_number) {
      doc.fontSize(16).text('Payment Details');
      doc.moveDown(0.5);
      doc.fontSize(12).text(`Bill Number: ${procurement.bill_number}`);
      doc.fontSize(12).text(`Bill Date: ${formatDate(procurement.bill_date) || 'N/A'}`);
      doc.fontSize(12).text(`Payment Mode: ${procurement.payment_mode || 'N/A'}`);
      doc.fontSize(12).text(`Payment Date: ${formatDate(procurement.payment_date) || 'N/A'}`);
      doc.fontSize(12).text(`Payment Status: ${procurement.payment_status}`);
      doc.moveDown();
    }

    // Add notes
    if (procurement.notes) {
      doc.fontSize(16).text('Notes');
      doc.moveDown(0.5);
      doc.fontSize(12).text(procurement.notes);
    }

    // Finalize PDF
    doc.end();
  } catch (error) {
    console.error('Error generating PDF:', error);
    req.flash('error', 'Error generating PDF');
    res.redirect(`/it-admin/procurement/${req.params.id}`);
  }
};

// Delete procurement
exports.deleteProcurement = async (req, res) => {
  try {
    const requestId = req.params.id;

    // Delete procurement request
    await db.query('DELETE FROM procurement_requests WHERE request_id = ?', [requestId]);

    req.flash('success', 'Procurement request deleted successfully');
    res.redirect('/it-admin/procurement');
  } catch (error) {
    console.error('Error deleting procurement:', error);
    req.flash('error', 'Error deleting procurement');
    res.redirect('/it-admin/procurement');
  }
};

// Submit procurement
exports.submitProcurement = async (req, res) => {
  try {
    const requestId = req.params.id;

    // Update procurement status
    await db.query(`
      UPDATE procurement_requests
      SET status = 'in_progress'
      WHERE request_id = ?
    `, [requestId]);

    req.flash('success', 'Procurement request submitted successfully');
    res.redirect(`/it-admin/procurement/${requestId}`);
  } catch (error) {
    console.error('Error submitting procurement:', error);
    req.flash('error', 'Error submitting procurement');
    res.redirect(`/it-admin/procurement/${requestId}`);
  }
};

// Generate comparative statement PDF
exports.generateComparativePdf = async (req, res) => {
  try {
    console.log('Generating comparative statement PDF...');

    // Extract data from request body
    const { items, vendors, vendorTotals, selectedVendor, justification } = req.body;

    console.log('Raw request body:', JSON.stringify(req.body).substring(0, 200) + '...');

    // Ensure we have valid data
    if (!items || !vendors || !vendorTotals) {
      console.error('Missing required data in request');
      return res.status(400).send('Missing required data: items, vendors, or vendorTotals');
    }

    console.log('Data received:');
    console.log('Items:', Array.isArray(items) ? items.length : 'not an array');
    console.log('Vendors:', Array.isArray(vendors) ? vendors.length : 'not an array');
    console.log('Selected vendor:', selectedVendor);

    // Validate required data
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).send('No items provided');
    }

    if (!Array.isArray(vendors) || vendors.length === 0) {
      return res.status(400).send('No vendors provided');
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4', layout: 'landscape' });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=comparative-statement-${Date.now()}.pdf`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(16).text('COMPARATIVE STATEMENT', { align: 'center' });
    doc.moveDown();

    // Add school name and date
    doc.fontSize(12).text('Senior Secondary Residential School for Meritorious Students, Ludhiana', { align: 'center' });
    doc.fontSize(10).text(`Date: ${new Date().toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' })}`, { align: 'right' });
    doc.moveDown();

    // Calculate column widths based on number of vendors
    const pageWidth = doc.page.width - 100; // Subtract margins
    const itemColWidth = pageWidth * 0.25;
    const qtyColWidth = pageWidth * 0.1;
    const vendorColWidth = (pageWidth - itemColWidth - qtyColWidth) / vendors.length;

    // Draw table header
    let y = doc.y;
    let x = 50;

    // Draw header background
    doc.rect(x, y, pageWidth, 20).fill('#f0f0f0');

    // Draw header text
    doc.fillColor('#000000');
    doc.fontSize(10).text('Item', x + 5, y + 5, { width: itemColWidth - 10 });
    x += itemColWidth;

    doc.fontSize(10).text('Qty', x + 5, y + 5, { width: qtyColWidth - 10, align: 'center' });
    x += qtyColWidth;

    vendors.forEach(vendor => {
      doc.fontSize(10).text(vendor.name, x + 5, y + 5, { width: vendorColWidth - 10, align: 'center' });
      x += vendorColWidth;
    });

    // Move down for table content
    y += 20;

    // Draw table rows for each item
    items.forEach((item, index) => {
      const rowHeight = 40; // Increased height for price and total
      x = 50;

      // Alternate row background
      if (index % 2 === 0) {
        doc.rect(x, y, pageWidth, rowHeight).fill('#f9f9f9');
      }

      // Reset fill color
      doc.fillColor('#000000');

      // Item name
      doc.fontSize(9).text(item.name, x + 5, y + 5, { width: itemColWidth - 10 });
      x += itemColWidth;

      // Quantity
      doc.fontSize(9).text(item.quantity, x + 5, y + 5, { width: qtyColWidth - 10, align: 'center' });
      x += qtyColWidth;

      // Vendor prices
      if (item.prices && Array.isArray(item.prices)) {
        item.prices.forEach((price, vendorIndex) => {
          // Find if this is the lowest price
          const isLowest = item.prices.every((otherPrice, otherIndex) => {
            if (vendorIndex === otherIndex) return true;
            const thisPrice = parseFloat(price.unitPrice.replace('₹', '').trim()) || 0;
            const otherPriceValue = parseFloat(otherPrice.unitPrice.replace('₹', '').trim()) || 0;
            return thisPrice <= otherPriceValue || otherPriceValue === 0;
          });

          // Highlight lowest price
          if (isLowest && parseFloat(price.unitPrice.replace('₹', '').trim()) > 0) {
            doc.rect(x, y, vendorColWidth, rowHeight).fill('#e6ffe6');
            doc.fillColor('#000000');
          }

          // Unit price
          doc.fontSize(9).text('Unit: ' + price.unitPrice, x + 5, y + 5, { width: vendorColWidth - 10, align: 'center' });

          // Total price
          doc.fontSize(9).text('Total: ' + price.totalPrice, x + 5, y + 20, { width: vendorColWidth - 10, align: 'center' });

          x += vendorColWidth;
        });
      }

      y += rowHeight;

      // Add a new page if we're near the bottom
      if (y > doc.page.height - 100) {
        doc.addPage({ size: 'A4', layout: 'landscape', margin: 50 });
        y = 50;
      }
    });

    // Draw total row
    x = 50;
    const totalRowHeight = 20;

    // Total row background
    doc.rect(x, y, pageWidth, totalRowHeight).fill('#f0f0f0');
    doc.fillColor('#000000');

    // Total label
    doc.fontSize(10).text('TOTAL', x + 5, y + 5, { width: itemColWidth + qtyColWidth - 10, align: 'right', bold: true });
    x += itemColWidth + qtyColWidth;

    // Vendor totals
    Object.keys(vendorTotals).forEach(vendorIndex => {
      const total = vendorTotals[vendorIndex];
      const formattedTotal = new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        maximumFractionDigits: 2
      }).format(total);

      doc.fontSize(10).text(formattedTotal, x + 5, y + 5, { width: vendorColWidth - 10, align: 'center', bold: true });
      x += vendorColWidth;
    });

    // Add selected vendor and justification
    if (selectedVendor) {
      doc.moveDown(2);
      doc.fontSize(12).text('Selected Vendor: ' + selectedVendor, { bold: true });

      if (justification) {
        doc.moveDown();
        doc.fontSize(11).text('Justification: ' + justification);
      }
    }

    // Add note about lowest prices
    doc.moveDown(2);
    doc.fontSize(9).text('Note: Cells highlighted in green indicate the lowest price for each item.', { italic: true });

    // Add signature lines
    doc.moveDown(2);
    const signatureY = doc.y;
    const signatureWidth = (pageWidth - 100) / 3;

    // First signature
    doc.fontSize(10).text('Prepared By:', 50, signatureY);
    doc.moveTo(50, signatureY + 40).lineTo(50 + signatureWidth - 20, signatureY + 40).stroke();

    // Second signature
    doc.fontSize(10).text('Checked By:', 50 + signatureWidth, signatureY);
    doc.moveTo(50 + signatureWidth, signatureY + 40).lineTo(50 + 2 * signatureWidth - 20, signatureY + 40).stroke();

    // Third signature
    doc.fontSize(10).text('Approved By:', 50 + 2 * signatureWidth, signatureY);
    doc.moveTo(50 + 2 * signatureWidth, signatureY + 40).lineTo(50 + 3 * signatureWidth - 20, signatureY + 40).stroke();

    // Finalize PDF
    doc.end();
  } catch (error) {
    console.error('Error generating comparative PDF:', error);
    console.error('Error stack:', error.stack);

    // Send a more detailed error response
    res.status(500).send({
      error: 'Error generating PDF',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : null
    });
  }
};

// Generate quotation PDF
exports.generateQuotation = async (req, res) => {
  try {
    console.log('Generating quotation PDF...');

    // Extract data directly from request body (now sent as JSON)
    const {
      subject,
      item_ids,
      item_names,
      item_quantities,
      item_specifications,
      vendor_ids,
      vendor_names,
      vendor_addresses,
      terms_conditions,
      buyer_name,
      buyer_designation
    } = req.body;

    // Validate required data
    if (!Array.isArray(item_names) || item_names.length === 0) {
      return res.status(400).send('No items provided');
    }

    if (!Array.isArray(vendor_names) || vendor_names.length === 0) {
      return res.status(400).send('No vendors provided');
    }

    console.log('Data received:');
    console.log('Items:', item_names);
    console.log('Vendors:', vendor_names);

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=quotation-request-${Date.now()}.pdf`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(16).text('REQUEST FOR QUOTATION', { align: 'center' });
    doc.moveDown();

    // Add date
    doc.fontSize(11).text(`Date: ${new Date().toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' })}`, { align: 'left' });
    doc.moveDown();

    // Add vendors
    doc.fontSize(11).text('To:', { align: 'left' });
    if (Array.isArray(vendor_names) && vendor_names.length > 0) {
      vendor_names.forEach((name, index) => {
        const address = vendor_addresses && vendor_addresses[index] ? vendor_addresses[index] : '';
        doc.fontSize(11).text(name, { align: 'left', indent: 20 });
        if (address) {
          doc.fontSize(10).text(address, { align: 'left', indent: 20, continued: false });
        }
        doc.moveDown(0.5);
      });
    } else {
      doc.fontSize(10).text('No vendors specified', { align: 'left', indent: 20 });
    }
    doc.moveDown();

    // Add salutation and subject
    doc.fontSize(11).text('Dear Sir/Madam,', { align: 'left' });
    doc.moveDown(0.5);
    doc.fontSize(11).text(`Subject: ${subject || 'Request for Quotation'}`, { align: 'left' });
    doc.moveDown(0.5);
    doc.fontSize(11).text('We are pleased to invite you to submit a quotation for the following items:', { align: 'left' });
    doc.moveDown();

    // Add items table
    const tableTop = doc.y;
    const tableLeft = 50;
    const colWidths = [30, 150, 150, 60, 60];

    // Table headers
    doc.fontSize(10).text('S.No.', tableLeft, tableTop);
    doc.text('Item Description', tableLeft + colWidths[0]);
    doc.text('Specifications', tableLeft + colWidths[0] + colWidths[1]);
    doc.text('Quantity', tableLeft + colWidths[0] + colWidths[1] + colWidths[2]);
    doc.text('Rate', tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3]);

    // Draw horizontal lines
    doc.moveTo(tableLeft, tableTop - 5)
      .lineTo(tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + colWidths[4], tableTop - 5)
      .stroke();

    doc.moveTo(tableLeft, tableTop + 15)
      .lineTo(tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + colWidths[4], tableTop + 15)
      .stroke();

    // Table rows
    let y = tableTop + 25;
    if (Array.isArray(item_names) && item_names.length > 0) {
      item_names.forEach((name, index) => {
        const quantity = item_quantities && item_quantities[index] ? item_quantities[index] : '1';
        const specs = item_specifications && item_specifications[index] ? item_specifications[index] : '';

        doc.fontSize(10).text((index + 1).toString(), tableLeft, y);
        doc.text(name, tableLeft + colWidths[0], y, { width: colWidths[1] - 10 });
        doc.text(specs, tableLeft + colWidths[0] + colWidths[1], y, { width: colWidths[2] - 10 });
        doc.text(quantity.toString(), tableLeft + colWidths[0] + colWidths[1] + colWidths[2], y);

        // Calculate height needed for this row
        const nameHeight = doc.heightOfString(name, { width: colWidths[1] - 10 });
        const specsHeight = doc.heightOfString(specs, { width: colWidths[2] - 10 });
        const rowHeight = Math.max(nameHeight, specsHeight, 20);

        y += rowHeight + 5;
      });
    } else {
      doc.fontSize(10).text('No items specified', tableLeft, y, { width: colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + colWidths[4], align: 'center' });
      y += 20;
    }

    // Draw bottom line
    doc.moveTo(tableLeft, y)
      .lineTo(tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3] + colWidths[4], y)
      .stroke();

    doc.moveDown(2);

    // Add terms and conditions
    doc.fontSize(11).text('Terms and Conditions:', { align: 'left' });
    doc.moveDown(0.5);

    if (terms_conditions) {
      const terms = terms_conditions.split('\n');
      terms.forEach((term, index) => {
        if (term.trim()) {
          doc.fontSize(10).text(term, { align: 'left', indent: 20 });
        }
      });
    } else {
      // Default terms
      doc.fontSize(10).text('1. All prices should include GST and delivery charges.', { align: 'left', indent: 20 });
      doc.fontSize(10).text('2. Please mention your GSTIN in the quotation.', { align: 'left', indent: 20 });
      doc.fontSize(10).text('3. Quotation should be valid for at least 30 days.', { align: 'left', indent: 20 });
      doc.fontSize(10).text('4. Delivery period should be clearly mentioned.', { align: 'left', indent: 20 });
    }

    doc.moveDown(2);

    // Add signature
    doc.fontSize(11).text('Thanking you,', { align: 'left' });
    doc.moveDown(2);
    doc.fontSize(11).text(buyer_name || 'Principal', { align: 'left' });
    doc.fontSize(11).text(buyer_designation || 'Principal', { align: 'left' });
    doc.fontSize(11).text('Senior Secondary Residential School for Meritorious Students', { align: 'left' });
    doc.fontSize(11).text('Ludhiana', { align: 'left' });

    // Finalize PDF
    doc.end();
  } catch (error) {
    console.error('Error generating quotation PDF:', error);
    res.status(500).send('Error generating PDF: ' + error.message);
  }
};

// Generate reports
exports.generateReports = async (req, res) => {
  try {
    // Get procurement statistics
    const [monthlyStats] = await db.query(`
      SELECT
        DATE_FORMAT(request_date, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(total_amount) as total_amount
      FROM procurement_requests
      WHERE status = 'completed'
      GROUP BY DATE_FORMAT(request_date, '%Y-%m')
      ORDER BY month DESC
      LIMIT 12
    `);

    // Get department statistics
    const [departmentStats] = await db.query(`
      SELECT
        department,
        COUNT(*) as count,
        SUM(total_amount) as total_amount
      FROM procurement_requests
      WHERE status = 'completed' AND department IS NOT NULL
      GROUP BY department
      ORDER BY total_amount DESC
    `);

    // Get vendor statistics
    const [vendorStats] = await db.query(`
      SELECT
        pv.name as vendor_name,
        COUNT(DISTINCT pv.request_id) as request_count,
        SUM(pv.total_amount) as total_amount
      FROM procurement_vendors pv
      JOIN procurement_requests pr ON pv.request_id = pr.request_id
      WHERE pr.status = 'completed'
      GROUP BY pv.name
      ORDER BY total_amount DESC
      LIMIT 10
    `);

    res.render('it-admin/procurement/reports', {
      title: 'Procurement Reports',
      layout: 'layouts/it-admin',
      currentPage: 'procurement',
      monthlyStats: monthlyStats || [],
      departmentStats: departmentStats || [],
      vendorStats: vendorStats || [],
      formatDate,
      formatCurrency: (amount) => {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          maximumFractionDigits: 2
        }).format(amount || 0);
      }
    });
  } catch (error) {
    console.error('Error generating reports:', error);
    req.flash('error', 'Error generating reports');
    res.redirect('/it-admin/procurement');
  }
};
