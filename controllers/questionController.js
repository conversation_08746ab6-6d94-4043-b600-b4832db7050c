exports.getQuestions = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Build the WHERE clause based on filters
        const filters = [];
        const params = [];

        if (req.query.search) {
            filters.push('(q.question_text LIKE ? OR q.solution_text LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.type) {
            filters.push('q.question_type = ?');
            params.push(req.query.type);
        }

        if (req.query.exam) {
            filters.push('e.exam_id = ?');
            params.push(req.query.exam);
        }

        if (req.query.section) {
            filters.push('s.section_id = ?');
            params.push(req.query.section);
        }

        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

        // Get total count
        const [countResult] = await db.query(`
            SELECT COUNT(DISTINCT q.question_id) as total
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            ${whereClause}
        `, params);

        // Get questions
        const [questions] = await db.query(`
            SELECT 
                q.*,
                e.exam_name,
                s.section_name
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            ${whereClause}
            ORDER BY q.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, perPage, offset]);

        // Get all exams and sections for filters
        const [exams] = await db.query('SELECT exam_id, exam_name FROM exams ORDER BY exam_name');
        const [sections] = await db.query('SELECT section_id, section_name FROM sections ORDER BY section_name');

        const totalQuestions = countResult[0].total;
        const totalPages = Math.ceil(totalQuestions / perPage);

        res.render('admin/questions/index', {
            title: 'Question Bank',
            pageTitle: 'Question Bank',
            questions,
            pagination: {
                page,
                totalPages,
                perPage
            },
            query: req.query,
            exams,
            sections,
            totalQuestions,
            perPage
        });
    } catch (error) {
        console.error('Error fetching questions:', error);
        req.flash('error', 'Error loading questions');
        res.redirect('/admin/dashboard');
    }
};