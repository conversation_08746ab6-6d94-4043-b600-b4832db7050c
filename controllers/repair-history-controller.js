/**
 * Repair History Controller
 * Handles repair history management functionality
 */
const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

module.exports = {
    /**
     * Display repair history
     */
    index: async (req, res) => {
        try {
            // Pagination
            const page = parseInt(req.query.page) || 1;
            const perPage = parseInt(req.query.perPage) || 10;
            const offset = (page - 1) * perPage;

            // Filtering
            const filters = [];
            const params = [];

            if (req.query.status) {
                filters.push('r.status = ?');
                params.push(req.query.status);
            }

            if (req.query.vendor) {
                filters.push('r.vendor_id = ?');
                params.push(req.query.vendor);
            }

            if (req.query.item) {
                filters.push('r.item_id = ?');
                params.push(req.query.item);
            }

            const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

            // Get total count
            const [countResult] = await db.query(`
                SELECT COUNT(*) as total
                FROM repair_history r
                ${whereClause}
            `, params);

            // Get repair history
            const [repairs] = await db.query(`
                SELECT r.*, i.name as item_name, i.serial_number,
                       v.name as vendor_name,
                       u1.username as sent_by_name, u2.username as received_by_name
                FROM repair_history r
                JOIN inventory_items i ON r.item_id = i.item_id
                JOIN repair_vendors v ON r.vendor_id = v.vendor_id
                LEFT JOIN users u1 ON r.sent_by = u1.id
                LEFT JOIN users u2 ON r.received_by = u2.id
                ${whereClause}
                ORDER BY
                    CASE
                        WHEN r.status = 'sent' THEN 1
                        WHEN r.status = 'in_progress' THEN 2
                        WHEN r.status = 'completed' THEN 3
                        WHEN r.status = 'cancelled' THEN 4
                    END,
                    r.sent_date DESC
                LIMIT ? OFFSET ?
            `, [...params, perPage, offset]);

            // Format dates
            repairs.forEach(repair => {
                repair.sent_date = formatDate(repair.sent_date);
                repair.expected_return_date = repair.expected_return_date ? formatDate(repair.expected_return_date) : null;
                repair.returned_date = repair.returned_date ? formatDate(repair.returned_date) : null;
                repair.created_at = formatDateTime(repair.created_at);
            });

            // Get vendors for filter
            const [vendors] = await db.query(`
                SELECT vendor_id, name FROM repair_vendors WHERE is_active = 1 ORDER BY name
            `);

            // Get items for filter
            const [items] = await db.query(`
                SELECT item_id, name, serial_number FROM inventory_items ORDER BY name
            `);

            const totalItems = countResult[0].total;
            const totalPages = Math.ceil(totalItems / perPage);

            res.render('admin/repair-history/index', {
                title: 'Repair History',
                pageTitle: 'Repair History',
                repairs,
                vendors,
                items,
                pagination: {
                    currentPage: page,
                    perPage,
                    totalPages,
                    totalItems
                },
                query: req.query,
                layout: 'admin',
                currentPage: 'repair-history'
            });
        } catch (error) {
            console.error('Error loading repair history:', error);
            req.flash('error', 'Error loading repair history');
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Display form to send item for repair
     */
    sendItemForm: async (req, res) => {
        try {
            // Get available items
            const [items] = await db.query(`
                SELECT item_id, name, serial_number, model
                FROM inventory_items
                WHERE status IN ('available', 'maintenance')
                ORDER BY name
            `);

            // Get active vendors
            const [vendors] = await db.query(`
                SELECT vendor_id, name, specialization
                FROM repair_vendors
                WHERE is_active = 1
                ORDER BY name
            `);

            res.render('admin/repair-history/send-item', {
                title: 'Send Item for Repair',
                pageTitle: 'Send Item for Repair',
                items,
                vendors,
                layout: 'admin',
                currentPage: 'repair-history'
            });
        } catch (error) {
            console.error('Error loading send item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/repair-history');
        }
    },

    /**
     * Process send item for repair
     */
    sendItem: async (req, res) => {
        try {
            const {
                item_id, vendor_id, sent_date, expected_return_date,
                issue_description, notes
            } = req.body;

            // Validate required fields
            if (!item_id || !vendor_id || !sent_date || !issue_description) {
                req.flash('error', 'Item, vendor, sent date, and issue description are required');
                return res.redirect('/admin/repair/history/send');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Create repair history record
                const [result] = await connection.query(`
                    INSERT INTO repair_history (
                        item_id, vendor_id, sent_date, expected_return_date,
                        issue_description, sent_by, notes, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'sent')
                `, [
                    item_id, vendor_id, sent_date, expected_return_date || null,
                    issue_description, req.session.userId, notes || null
                ]);

                // Update item status
                await connection.query(`
                    UPDATE inventory_items
                    SET status = 'maintenance'
                    WHERE item_id = ?
                `, [item_id]);

                await connection.commit();

                req.flash('success', 'Item sent for repair successfully');
                res.redirect('/admin/repair/history');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error sending item for repair:', error);
            req.flash('error', 'Error sending item for repair');
            res.redirect('/admin/repair/history/send');
        }
    },

    /**
     * Display form to receive item from repair
     */
    receiveItemForm: async (req, res) => {
        try {
            // Get items sent for repair
            const [repairs] = await db.query(`
                SELECT r.repair_id, r.item_id, i.name as item_name,
                       i.serial_number, v.name as vendor_name,
                       r.sent_date, r.expected_return_date
                FROM repair_history r
                JOIN inventory_items i ON r.item_id = i.item_id
                JOIN repair_vendors v ON r.vendor_id = v.vendor_id
                WHERE r.status IN ('sent', 'in_progress')
                ORDER BY r.sent_date
            `);

            // Format dates
            repairs.forEach(repair => {
                repair.sent_date = formatDate(repair.sent_date);
                repair.expected_return_date = repair.expected_return_date ? formatDate(repair.expected_return_date) : 'Not specified';
            });

            res.render('admin/repair-history/receive-item', {
                title: 'Receive Item from Repair',
                pageTitle: 'Receive Item from Repair',
                repairs,
                layout: 'admin',
                currentPage: 'repair-history'
            });
        } catch (error) {
            console.error('Error loading receive item form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/repair-history');
        }
    },

    /**
     * Process receive item from repair
     */
    receiveItem: async (req, res) => {
        try {
            const {
                repair_id, returned_date, repair_cost, repair_details, notes,
                require_condition_check
            } = req.body;

            // Validate required fields
            if (!repair_id || !returned_date) {
                req.flash('error', 'Repair ID and returned date are required');
                return res.redirect('/admin/repair/history/receive');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Get repair details
                const [repairs] = await connection.query(`
                    SELECT r.*, i.category_id, c.name AS category_name, v.vendor_id, v.name AS vendor_name
                    FROM repair_history r
                    JOIN inventory_items i ON r.item_id = i.item_id
                    LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                    JOIN repair_vendors v ON r.vendor_id = v.vendor_id
                    WHERE r.repair_id = ?
                `, [repair_id]);

                if (repairs.length === 0) {
                    throw new Error('Repair record not found');
                }

                const repair = repairs[0];
                const item_id = repair.item_id;
                const vendor_id = repair.vendor_id;
                const categoryName = repair.category_name || '';

                // Determine if condition check is required based on category or checkbox
                const needsConditionCheck = require_condition_check === 'on' ||
                    categoryName.toLowerCase().includes('laptop') ||
                    categoryName.toLowerCase().includes('speaker') ||
                    categoryName.toLowerCase().includes('projector') ||
                    categoryName.toLowerCase().includes('interactive');

                // Update repair history record
                await connection.query(`
                    UPDATE repair_history
                    SET returned_date = ?, received_by = ?,
                        repair_cost = ?, repair_details = ?,
                        notes = CONCAT(IFNULL(notes, ''), '\nReturn notes: ', ?),
                        status = 'completed'
                    WHERE repair_id = ?
                `, [
                    returned_date,
                    req.session.userId,
                    repair_cost || null,
                    repair_details || null,
                    notes || '',
                    repair_id
                ]);

                // Create transaction record for receiving from repair
                const [transactionResult] = await connection.query(`
                    INSERT INTO inventory_transactions (
                        item_id, transaction_type, issued_to, issued_by,
                        issued_date, received_date, received_by,
                        condition_on_return, notes, condition_check_required,
                        repair_vendor_id, from_repair
                    ) VALUES (?, 'receive', NULL, ?,
                        NOW(), NOW(), ?,
                        'Repaired', ?, ?,
                        ?, 1)
                `, [
                    item_id,
                    req.session.userId,
                    req.session.userId,
                    `Received from repair vendor: ${repair.vendor_name}. ${notes || ''}`,
                    needsConditionCheck ? 1 : 0,
                    vendor_id
                ]);

                const transaction_id = transactionResult.insertId;

                // Update item status
                await connection.query(`
                    UPDATE inventory_items
                    SET status = 'available'
                    WHERE item_id = ?
                `, [item_id]);

                await connection.commit();

                // If condition check is required, redirect to condition check form
                if (needsConditionCheck) {
                    req.flash('success', 'Item received from repair. Please complete the condition check.');
                    return res.redirect(`/admin/inventory/condition/check/${item_id}/transaction/${transaction_id}`);
                }

                req.flash('success', 'Item received from repair successfully');
                res.redirect('/admin/repair/history');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error receiving item from repair:', error);
            req.flash('error', 'Error receiving item from repair');
            res.redirect('/admin/repair/history/receive');
        }
    },

    /**
     * Update repair status
     */
    updateStatus: async (req, res) => {
        try {
            const repairId = req.params.id;
            const { status, notes } = req.body;

            // Validate status
            if (!['sent', 'in_progress', 'completed', 'cancelled'].includes(status)) {
                req.flash('error', 'Invalid status');
                return res.redirect('/admin/repair/history');
            }

            // Start transaction
            const connection = await db.getConnection();
            await connection.beginTransaction();

            try {
                // Get current repair data
                const [repairs] = await connection.query(`
                    SELECT * FROM repair_history WHERE repair_id = ?
                `, [repairId]);

                if (repairs.length === 0) {
                    throw new Error('Repair record not found');
                }

                const repair = repairs[0];
                const item_id = repair.item_id;

                // Update repair status
                await connection.query(`
                    UPDATE repair_history
                    SET status = ?,
                        notes = CONCAT(IFNULL(notes, ''), '\nStatus update: ', ?)
                    WHERE repair_id = ?
                `, [
                    status,
                    notes || `Status changed to ${status}`,
                    repairId
                ]);

                // If cancelled, update item status back to available
                if (status === 'cancelled') {
                    await connection.query(`
                        UPDATE inventory_items
                        SET status = 'available'
                        WHERE item_id = ?
                    `, [item_id]);
                }

                await connection.commit();

                req.flash('success', 'Repair status updated successfully');
                res.redirect('/admin/repair/history');
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            console.error('Error updating repair status:', error);
            req.flash('error', 'Error updating repair status');
            res.redirect('/admin/repair/history');
        }
    },

    /**
     * View repair details
     */
    viewRepair: async (req, res) => {
        try {
            const repairId = req.params.id;

            // Get repair details
            const [repairs] = await db.query(`
                SELECT r.*, i.name as item_name, i.serial_number, i.model,
                       v.name as vendor_name, v.contact_person, v.phone, v.email,
                       u1.username as sent_by_name, u2.username as received_by_name
                FROM repair_history r
                JOIN inventory_items i ON r.item_id = i.item_id
                JOIN repair_vendors v ON r.vendor_id = v.vendor_id
                LEFT JOIN users u1 ON r.sent_by = u1.id
                LEFT JOIN users u2 ON r.received_by = u2.id
                WHERE r.repair_id = ?
            `, [repairId]);

            if (repairs.length === 0) {
                req.flash('error', 'Repair record not found');
                return res.redirect('/admin/repair/history');
            }

            const repair = repairs[0];

            // Format dates
            repair.sent_date = formatDate(repair.sent_date);
            repair.expected_return_date = repair.expected_return_date ? formatDate(repair.expected_return_date) : null;
            repair.returned_date = repair.returned_date ? formatDate(repair.returned_date) : null;
            repair.created_at = formatDateTime(repair.created_at);

            // Get hardware condition data if available
            let conditionData = [];
            try {
                const [conditions] = await db.query(`
                    SELECT hc.*, hp.part_name, hp.display_name, u.username AS checked_by_name
                    FROM hardware_condition hc
                    JOIN hardware_parts hp ON hc.part_id = hp.part_id
                    LEFT JOIN users u ON hc.checked_by = u.id
                    WHERE hc.item_id = ?
                    ORDER BY hc.checked_at DESC, hp.display_name
                `, [repair.item_id]);

                // Group by check date
                const conditionsByDate = {};
                conditions.forEach(condition => {
                    const checkDate = formatDateTime(condition.checked_at);
                    if (!conditionsByDate[checkDate]) {
                        conditionsByDate[checkDate] = [];
                    }
                    conditionsByDate[checkDate].push(condition);
                });

                conditionData = conditionsByDate;
            } catch (error) {
                console.error('Error fetching condition data:', error);
                // Continue even if condition data fetch fails
            }

            res.render('admin/repair-history/view', {
                title: 'Repair Details',
                pageTitle: `Repair Details: ${repair.item_name}`,
                repair,
                conditionData,
                layout: 'admin',
                currentPage: 'repair-history'
            });
        } catch (error) {
            console.error('Error viewing repair details:', error);
            req.flash('error', 'Error loading repair details');
            res.redirect('/admin/repair/history');
        }
    }
};
