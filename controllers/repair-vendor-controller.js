/**
 * Repair Vendor Controller
 * Handles repair vendor management functionality
 */
const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

module.exports = {
    /**
     * Display repair vendors
     */
    index: async (req, res) => {
        try {
            // Get all vendors
            const [vendors] = await db.query(`
                SELECT v.*,
                       (SELECT COUNT(*) FROM repair_history WHERE vendor_id = v.vendor_id) as repair_count
                FROM repair_vendors v
                ORDER BY v.name
            `);

            res.render('admin/repair-vendors/index', {
                title: 'Repair Vendors',
                pageTitle: 'Repair Vendors',
                vendors,
                layout: 'admin',
                currentPage: 'repair-vendors'
            });
        } catch (error) {
            console.error('Error loading repair vendors:', error);
            req.flash('error', 'Error loading repair vendors');
            res.redirect('/admin/dashboard');
        }
    },

    /**
     * Display form to add new repair vendor
     */
    addVendorForm: async (req, res) => {
        try {
            res.render('admin/repair-vendors/add', {
                title: 'Add Repair Vendor',
                pageTitle: 'Add New Repair Vendor',
                layout: 'admin',
                currentPage: 'repair-vendors'
            });
        } catch (error) {
            console.error('Error loading add vendor form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/repair-vendors');
        }
    },

    /**
     * Process add new repair vendor
     */
    addVendor: async (req, res) => {
        try {
            const {
                name, contact_person, phone, email, address, specialization, notes
            } = req.body;

            // Validate required fields
            if (!name) {
                req.flash('error', 'Vendor name is required');
                return res.redirect('/admin/repair/vendors/add');
            }

            // Insert new vendor
            await db.query(`
                INSERT INTO repair_vendors (
                    name, contact_person, phone, email, address, specialization, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
                name, contact_person || null, phone || null, email || null,
                address || null, specialization || null, notes || null
            ]);

            req.flash('success', 'Repair vendor added successfully');
            res.redirect('/admin/repair/vendors');
        } catch (error) {
            console.error('Error adding repair vendor:', error);
            req.flash('error', 'Error adding repair vendor');
            res.redirect('/admin/repair/vendors/add');
        }
    },

    /**
     * Display form to edit repair vendor
     */
    editVendorForm: async (req, res) => {
        try {
            const vendorId = req.params.id;

            // Get vendor details
            const [vendors] = await db.query(`
                SELECT * FROM repair_vendors WHERE vendor_id = ?
            `, [vendorId]);

            if (vendors.length === 0) {
                req.flash('error', 'Vendor not found');
                return res.redirect('/admin/repair/vendors');
            }

            const vendor = vendors[0];

            res.render('admin/repair-vendors/edit', {
                title: 'Edit Repair Vendor',
                pageTitle: 'Edit Repair Vendor',
                vendor,
                layout: 'admin',
                currentPage: 'repair-vendors'
            });
        } catch (error) {
            console.error('Error loading edit vendor form:', error);
            req.flash('error', 'Error loading form');
            res.redirect('/admin/repair/vendors');
        }
    },

    /**
     * Process edit repair vendor
     */
    updateVendor: async (req, res) => {
        try {
            const vendorId = req.params.id;
            const {
                name, contact_person, phone, email, address, specialization, notes, is_active
            } = req.body;

            // Validate required fields
            if (!name) {
                req.flash('error', 'Vendor name is required');
                return res.redirect(`/admin/repair/vendors/${vendorId}/edit`);
            }

            // Update vendor
            await db.query(`
                UPDATE repair_vendors
                SET name = ?, contact_person = ?, phone = ?, email = ?,
                    address = ?, specialization = ?, notes = ?, is_active = ?
                WHERE vendor_id = ?
            `, [
                name,
                contact_person || null,
                phone || null,
                email || null,
                address || null,
                specialization || null,
                notes || null,
                is_active ? 1 : 0,
                vendorId
            ]);

            req.flash('success', 'Repair vendor updated successfully');
            res.redirect('/admin/repair/vendors');
        } catch (error) {
            console.error('Error updating repair vendor:', error);
            req.flash('error', 'Error updating repair vendor');
            res.redirect(`/admin/repair/vendors/${req.params.id}/edit`);
        }
    },

    /**
     * Delete repair vendor
     */
    deleteVendor: async (req, res) => {
        try {
            const vendorId = req.params.id;

            // Check if vendor has repair history
            const [repairs] = await db.query(`
                SELECT COUNT(*) as count FROM repair_history WHERE vendor_id = ?
            `, [vendorId]);

            if (repairs[0].count > 0) {
                req.flash('error', 'Cannot delete vendor with repair history. You can deactivate it instead.');
                return res.redirect('/admin/repair/vendors');
            }

            // Delete vendor
            await db.query('DELETE FROM repair_vendors WHERE vendor_id = ?', [vendorId]);

            req.flash('success', 'Repair vendor deleted successfully');
            res.redirect('/admin/repair/vendors');
        } catch (error) {
            console.error('Error deleting repair vendor:', error);
            req.flash('error', 'Error deleting repair vendor');
            res.redirect('/admin/repair/vendors');
        }
    },

    /**
     * View vendor details
     */
    viewVendor: async (req, res) => {
        try {
            const vendorId = req.params.id;

            // Get vendor details
            const [vendors] = await db.query(`
                SELECT * FROM repair_vendors WHERE vendor_id = ?
            `, [vendorId]);

            if (vendors.length === 0) {
                req.flash('error', 'Vendor not found');
                return res.redirect('/admin/repair/vendors');
            }

            const vendor = vendors[0];

            // Get repair history
            const [repairs] = await db.query(`
                SELECT r.*, i.name as item_name, i.serial_number,
                       u1.username as sent_by_name, u2.username as received_by_name
                FROM repair_history r
                JOIN inventory_items i ON r.item_id = i.item_id
                LEFT JOIN users u1 ON r.sent_by = u1.id
                LEFT JOIN users u2 ON r.received_by = u2.id
                WHERE r.vendor_id = ?
                ORDER BY r.sent_date DESC
            `, [vendorId]);

            // Format dates
            repairs.forEach(repair => {
                repair.sent_date = formatDate(repair.sent_date);
                repair.expected_return_date = repair.expected_return_date ? formatDate(repair.expected_return_date) : null;
                repair.returned_date = repair.returned_date ? formatDate(repair.returned_date) : null;
                repair.created_at = formatDateTime(repair.created_at);
            });

            res.render('admin/repair-vendors/view', {
                title: 'Vendor Details',
                pageTitle: `Vendor Details: ${vendor.name}`,
                vendor,
                repairs,
                layout: 'admin',
                currentPage: 'repair-vendors'
            });
        } catch (error) {
            console.error('Error viewing vendor details:', error);
            req.flash('error', 'Error loading vendor details');
            res.redirect('/admin/repair/vendors');
        }
    }
};
