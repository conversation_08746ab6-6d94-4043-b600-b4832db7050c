const db = require('../config/database');
const { generateReport } = require('../utils/report-generator');
const { sendReportEmail } = require('../utils/email-sender');
const { shareFile } = require('../utils/system-share');
const fs = require('fs').promises;
const path = require('path');

/**
 * Render the reports dashboard page
 */
exports.getReportsDashboard = async (req, res) => {
    try {
        // Get recent reports
        const [recentReports] = await db.query(
            `SELECT id, name, type, format, file_url, generated_at
             FROM reports
             WHERE user_id = ? AND is_deleted = 0
             ORDER BY generated_at DESC
             LIMIT 10`,
            [req.session.userId]
        );

        res.render('admin/reports/dashboard', {
            title: 'Reports Dashboard',
            recentReports
        });
    } catch (error) {
        console.error('Error fetching reports:', error);
        req.flash('error', 'Failed to load reports dashboard');
        res.redirect('/admin/dashboard');
    }
};

/**
 * Render the export reports page
 */
exports.getExportReports = async (req, res) => {
    try {
        // Get users for filter
        const [users] = await db.query(
            `SELECT id, username, email FROM users WHERE is_active = 1 ORDER BY username`
        );

        // Get exams for filter
        const [exams] = await db.query(
            `SELECT exam_id, exam_name FROM exams WHERE is_active = 1 ORDER BY exam_name`
        );

        // Get categories for filter
        const [categories] = await db.query(
            `SELECT category_id, name FROM categories ORDER BY name`
        );

        // Get recent reports
        const [recentReports] = await db.query(
            `SELECT id, name, type, format, file_url, generated_at
             FROM reports
             WHERE user_id = ? AND is_deleted = 0
             ORDER BY generated_at DESC
             LIMIT 10`,
            [req.session.userId]
        );

        res.render('admin/reports/export', {
            title: 'Export Reports',
            users,
            exams,
            categories,
            recentReports
        });
    } catch (error) {
        console.error('Error loading export page:', error);
        req.flash('error', 'Failed to load export page');
        res.redirect('/admin/reports');
    }
};

/**
 * Generate a report based on form submission
 */
exports.generateReport = async (req, res) => {
    try {
        const {
            report_type,
            export_format,
            start_date,
            end_date,
            user_id,
            exam_id,
            category_id,
            delivery_options,
            email_address
        } = req.body;

        // Validate required fields
        if (!report_type || !export_format) {
            req.flash('error', 'Report type and export format are required');
            return res.redirect('/admin/reports/export');
        }

        // Prepare filters
        const filters = {
            start_date: start_date || null,
            end_date: end_date || null,
            user_id: user_id || null,
            exam_id: exam_id || null,
            category_id: category_id || null
        };

        // Generate the report
        const report = await generateReport({
            reportType: report_type,
            format: export_format,
            filters,
            userId: req.session.userId
        });

        // Log the report generation
        await db.query(
            `INSERT INTO logs (user_id, operation, category, details, status, ip_address, request_method, request_uri)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                req.session.userId,
                'generate_report',
                'reports',
                JSON.stringify({ report_type, export_format, filters }),
                'success',
                req.ip,
                req.method,
                req.originalUrl
            ]
        );

        // Handle delivery options
        const deliveryOptions = Array.isArray(delivery_options) ? delivery_options : [delivery_options];

        // Email delivery
        if (deliveryOptions.includes('email') && email_address) {
            try {
                await sendReportEmail({
                    to: email_address,
                    report,
                    userName: req.session.username
                });

                req.flash('success', `Report has been sent to ${email_address}`);
            } catch (emailError) {
                console.error('Error sending email:', emailError);
                req.flash('error', `Failed to send email: ${emailError.message}`);
            }
        }

        // System share
        if (deliveryOptions.includes('share')) {
            try {
                await shareFile(report.filePath);
                req.flash('success', 'Report has been opened with system application');
            } catch (shareError) {
                console.error('Error sharing file:', shareError);
                req.flash('error', `Failed to share file: ${shareError.message}`);
            }
        }

        // Download (default)
        if (deliveryOptions.includes('download') || deliveryOptions.length === 0) {
            return res.redirect(`/admin/reports/download/${report.id}`);
        }

        // Redirect back to reports page
        req.flash('success', 'Report generated successfully');
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error generating report:', error);
        req.flash('error', `Failed to generate report: ${error.message}`);
        res.redirect('/admin/reports/export');
    }
};

/**
 * Download a report
 */
exports.downloadReport = async (req, res) => {
    try {
        const reportId = req.params.id;

        // Get report details
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            req.flash('error', 'Report not found');
            return res.redirect('/admin/reports/export');
        }

        const report = reports[0];

        // Check if file exists
        try {
            await fs.access(report.file_path);
        } catch (error) {
            req.flash('error', 'Report file not found');
            return res.redirect('/admin/reports/export');
        }

        // Set appropriate content type
        const contentTypes = {
            pdf: 'application/pdf',
            excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            csv: 'text/csv',
            json: 'application/json'
        };

        // Set headers for download
        res.setHeader('Content-Type', contentTypes[report.format] || 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${path.basename(report.file_path)}"`);

        // Stream the file
        const fileStream = fs.createReadStream(report.file_path);
        fileStream.pipe(res);
    } catch (error) {
        console.error('Error downloading report:', error);
        req.flash('error', 'Failed to download report');
        res.redirect('/admin/reports/export');
    }
};

/**
 * Email a report
 */
exports.emailReport = async (req, res) => {
    try {
        const reportId = req.params.id;
        const { email } = req.body;

        // Validate email
        if (!email) {
            req.flash('error', 'Email address is required');
            return res.redirect('/admin/reports/export');
        }

        // Get report details
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            req.flash('error', 'Report not found');
            return res.redirect('/admin/reports/export');
        }

        const report = reports[0];

        // Send email
        await sendReportEmail({
            to: email,
            report: {
                name: report.name,
                type: report.type,
                format: report.format,
                filePath: report.file_path
            },
            userName: req.session.username
        });

        req.flash('success', `Report has been sent to ${email}`);
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error emailing report:', error);
        req.flash('error', 'Failed to email report');
        res.redirect('/admin/reports/export');
    }
};

/**
 * Share a report via system apps
 */
exports.shareReport = async (req, res) => {
    try {
        const reportId = req.params.id;

        // Get report details
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            req.flash('error', 'Report not found');
            return res.redirect('/admin/reports/export');
        }

        const report = reports[0];

        // Share file
        await shareFile(report.file_path);

        req.flash('success', 'Report has been opened with system application');
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error sharing report:', error);
        req.flash('error', 'Failed to share report');
        res.redirect('/admin/reports/export');
    }
};

/**
 * Delete a report
 */
exports.deleteReport = async (req, res) => {
    try {
        const reportId = req.params.id;

        // Get report details
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            return res.status(404).json({ success: false, message: 'Report not found' });
        }

        // Mark report as deleted
        await db.query(
            'UPDATE reports SET is_deleted = 1 WHERE id = ?',
            [reportId]
        );

        // Try to delete the file (but don't fail if it doesn't exist)
        try {
            await fs.unlink(reports[0].file_path);
        } catch (error) {
            console.error('Error deleting report file:', error);
            // Continue even if file deletion fails
        }

        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting report:', error);
        res.status(500).json({ success: false, message: 'Failed to delete report' });
    }
};
