const db = require('../config/database');

const roleController = {
    /**
     * Display all roles
     */
    index: async (req, res, next) => {
        try {
            console.log('Role controller index method called');

            // Clear any flash messages
            delete req.session.flashError;
            req.session.flashError = null;

            // Save the session to ensure flash messages are cleared
            await new Promise((resolve) => {
                req.session.save(resolve);
            });

            // Check if roles table exists
            const [tables] = await db.query(`
                SELECT TABLE_NAME
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'roles'
            `);

            let roles = [];
            let permissionsByCategory = {};

            if (tables.length > 0) {
                // Get all roles with user counts
                const [rolesResult] = await db.query(`
                    SELECT r.role_id, r.role_name, r.description, r.is_system,
                           COUNT(u.id) as user_count
                    FROM roles r
                    LEFT JOIN users u ON r.role_name = u.role
                    GROUP BY r.role_id
                    ORDER BY r.role_name
                `);

                roles = rolesResult;
                console.log('Roles found:', roles.length);

                // Get all permissions grouped by category
                const [permissions] = await db.query(`
                    SELECT permission_id, permission_name, description, category
                    FROM permissions
                    ORDER BY category, permission_name
                `);

                // Group permissions by category
                permissionsByCategory = permissions.reduce((acc, permission) => {
                    if (!acc[permission.category]) {
                        acc[permission.category] = [];
                    }
                    acc[permission.category].push(permission);
                    return acc;
                }, {});
            } else {
                // If tables don't exist, get roles from users table
                const [userRoles] = await db.query(`
                    SELECT role as role_name, COUNT(*) as user_count
                    FROM users
                    GROUP BY role
                    ORDER BY role
                `);

                // Format roles to match the expected structure
                roles = userRoles.map(role => ({
                    role_id: 0,
                    role_name: role.role_name,
                    description: role.role_name === 'admin' ? 'Administrator with full system access' :
                                role.role_name === 'teacher' ? 'Teacher with access to create and manage tests' :
                                'Student with access to take tests',
                    is_system: true,
                    user_count: role.user_count
                }));
                console.log('User roles found:', roles.length);
            }

            // Explicitly set error to null in both locals and render options
            res.locals.error = null;
            res.locals.flashError = null;

            console.log('Rendering roles page with:', {
                roles: roles.length,
                permissionCategories: Object.keys(permissionsByCategory).length
            });

            // Check if the roles.ejs file exists
            try {
                const fs = require('fs');
                const path = require('path');
                const rolesViewPath = path.join(__dirname, '../views/admin/users/roles.ejs');

                if (fs.existsSync(rolesViewPath)) {
                    console.log('Roles view file exists at:', rolesViewPath);
                } else {
                    console.error('Roles view file does not exist at:', rolesViewPath);
                    // Create a basic roles view if it doesn't exist
                    const basicRolesView = `<!-- Role Management -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">User Roles</h2>
      <a href="/admin/users/roles/create" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Create New Role
      </a>
    </div>

    <% if (roles && roles.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% roles.forEach(role => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= role.role_name %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500"><%= role.description || 'No description' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= role.user_count %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= role.is_system ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' %>">
                    <%= role.is_system ? 'System' : 'Custom' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <a href="/admin/users/roles/<%= role.role_id %>/users" class="text-blue-600 hover:text-blue-900 mr-3">View Users</a>
                  <% if (!role.is_system) { %>
                    <a href="/admin/users/roles/<%= role.role_id %>/edit" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                    <form method="POST" action="/admin/users/roles/<%= role.role_id %>/delete" class="inline">
                      <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this role?')">Delete</button>
                    </form>
                  <% } %>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <p class="text-gray-500">No roles found. Create your first role.</p>
      </div>
    <% } %>
  </div>
</div>`;

                    const rolesDir = path.join(__dirname, '../views/admin/users');
                    if (!fs.existsSync(rolesDir)) {
                        fs.mkdirSync(rolesDir, { recursive: true });
                    }
                    fs.writeFileSync(rolesViewPath, basicRolesView);
                    console.log('Created basic roles view at:', rolesViewPath);
                }
            } catch (error) {
                console.error('Error checking/creating roles view file:', error);
            }

            // Set the correct layout and current page
            res.locals.layout = 'admin';
            res.locals.currentPage = 'roles';

            return res.render('admin/users/roles', {
                title: 'Role Management',
                pageTitle: 'Manage User Roles',
                currentPage: 'roles',
                roles,
                permissionsByCategory,
                error: null,
                flashError: null
            });
        } catch (error) {
            console.error('Error loading roles:', error);
            next(error);
        }
    },

    /**
     * Display form to create a new role
     */
    createForm: async (req, res, next) => {
        try {
            // Check if permissions table exists
            const [tables] = await db.query(`
                SELECT TABLE_NAME
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'permissions'
            `);

            let permissionsByCategory = {};

            if (tables.length > 0) {
                // Get all permissions grouped by category
                const [permissions] = await db.query(`
                    SELECT permission_id, permission_name, description, category
                    FROM permissions
                    ORDER BY category, permission_name
                `);

                // Group permissions by category
                permissionsByCategory = permissions.reduce((acc, permission) => {
                    if (!acc[permission.category]) {
                        acc[permission.category] = [];
                    }
                    acc[permission.category].push(permission);
                    return acc;
                }, {});
            } else {
                // If permissions table doesn't exist, show a message
                req.session.flashError = 'Role management system is not fully set up. Please run the migration first.';
                return res.redirect('/admin/users/roles');
            }

            res.render('admin/users/create-role', {
                title: 'Create New Role',
                pageTitle: 'Create New Role',
                currentPage: 'roles',
                permissionsByCategory
            });
        } catch (error) {
            console.error('Error loading create role form:', error);
            req.session.flashError = 'Error loading create role form';
            res.redirect('/admin/users/roles');
        }
    },

    /**
     * Create a new role
     */
    create: async (req, res, next) => {
        try {
            const { role_name, description, permissions } = req.body;

            // Validate required fields
            if (!role_name) {
                req.session.flashError = 'Role name is required';
                return res.redirect('/admin/users/roles/create');
            }

            // Check if role already exists
            const [existingRoles] = await db.query(
                'SELECT role_id FROM roles WHERE role_name = ?',
                [role_name]
            );

            if (existingRoles.length > 0) {
                req.session.flashError = 'A role with this name already exists';
                return res.redirect('/admin/users/roles/create');
            }

            // Create role
            const [result] = await db.query(
                'INSERT INTO roles (role_name, description) VALUES (?, ?)',
                [role_name, description || '']
            );

            const roleId = result.insertId;

            // Assign permissions if any were selected
            if (permissions && Array.isArray(permissions)) {
                const values = permissions.map(permissionId => [roleId, permissionId]);

                if (values.length > 0) {
                    await db.query(
                        'INSERT INTO role_permissions (role_id, permission_id) VALUES ?',
                        [values]
                    );
                }
            }

            req.session.flashSuccess = 'Role created successfully';
            res.redirect('/admin/users/roles');
        } catch (error) {
            console.error('Error creating role:', error);
            req.session.flashError = 'Error creating role';
            res.redirect('/admin/users/roles/create');
        }
    },

    /**
     * Display form to edit a role
     */
    editForm: async (req, res, next) => {
        try {
            const roleId = req.params.id;

            // Get role details
            const [roles] = await db.query(
                'SELECT * FROM roles WHERE role_id = ?',
                [roleId]
            );

            if (roles.length === 0) {
                req.session.flashError = 'Role not found';
                return res.redirect('/admin/users/roles');
            }

            const role = roles[0];

            // Get all permissions grouped by category
            const [permissions] = await db.query(`
                SELECT permission_id, permission_name, description, category
                FROM permissions
                ORDER BY category, permission_name
            `);

            // Get role permissions
            const [rolePermissions] = await db.query(
                'SELECT permission_id FROM role_permissions WHERE role_id = ?',
                [roleId]
            );

            const rolePermissionIds = rolePermissions.map(p => p.permission_id);

            // Group permissions by category
            const permissionsByCategory = permissions.reduce((acc, permission) => {
                if (!acc[permission.category]) {
                    acc[permission.category] = [];
                }

                // Add a selected property to each permission
                permission.selected = rolePermissionIds.includes(permission.permission_id);

                acc[permission.category].push(permission);
                return acc;
            }, {});

            res.render('admin/users/edit-role', {
                title: 'Edit Role',
                pageTitle: `Edit Role: ${role.role_name}`,
                currentPage: 'roles',
                role,
                permissionsByCategory
            });
        } catch (error) {
            console.error('Error loading edit role form:', error);
            req.session.flashError = 'Error loading edit role form';
            res.redirect('/admin/users/roles');
        }
    },

    /**
     * Update a role
     */
    update: async (req, res, next) => {
        try {
            const roleId = req.params.id;
            const { role_name, description, permissions } = req.body;

            // Validate required fields
            if (!role_name) {
                req.session.flashError = 'Role name is required';
                return res.redirect(`/admin/users/roles/${roleId}/edit`);
            }

            // Get role details to check if it's a system role
            const [roles] = await db.query(
                'SELECT * FROM roles WHERE role_id = ?',
                [roleId]
            );

            if (roles.length === 0) {
                req.session.flashError = 'Role not found';
                return res.redirect('/admin/users/roles');
            }

            const role = roles[0];

            // Check if trying to rename a system role
            if (role.is_system && role.role_name !== role_name) {
                req.session.flashError = 'System role names cannot be changed';
                return res.redirect(`/admin/users/roles/${roleId}/edit`);
            }

            // Check if new name already exists (for another role)
            if (role.role_name !== role_name) {
                const [existingRoles] = await db.query(
                    'SELECT role_id FROM roles WHERE role_name = ? AND role_id != ?',
                    [role_name, roleId]
                );

                if (existingRoles.length > 0) {
                    req.session.flashError = 'A role with this name already exists';
                    return res.redirect(`/admin/users/roles/${roleId}/edit`);
                }
            }

            // Update role
            await db.query(
                'UPDATE roles SET role_name = ?, description = ? WHERE role_id = ?',
                [role_name, description || '', roleId]
            );

            // Delete existing permissions
            await db.query(
                'DELETE FROM role_permissions WHERE role_id = ?',
                [roleId]
            );

            // Assign new permissions if any were selected
            if (permissions && Array.isArray(permissions)) {
                const values = permissions.map(permissionId => [roleId, permissionId]);

                if (values.length > 0) {
                    await db.query(
                        'INSERT INTO role_permissions (role_id, permission_id) VALUES ?',
                        [values]
                    );
                }
            }

            // If this is a system role, make sure to update the role name in the users table
            if (role.is_system && role.role_name !== role_name) {
                await db.query(
                    'UPDATE users SET role = ? WHERE role = ?',
                    [role_name, role.role_name]
                );
            }

            req.session.flashSuccess = 'Role updated successfully';
            res.redirect('/admin/users/roles');
        } catch (error) {
            console.error('Error updating role:', error);
            req.session.flashError = 'Error updating role';
            res.redirect(`/admin/users/roles/${req.params.id}/edit`);
        }
    },

    /**
     * Delete a role
     */
    delete: async (req, res, next) => {
        try {
            const roleId = req.params.id;

            // Check if role exists and is not a system role
            const [roles] = await db.query(
                'SELECT * FROM roles WHERE role_id = ?',
                [roleId]
            );

            if (roles.length === 0) {
                return res.status(404).json({ success: false, message: 'Role not found' });
            }

            const role = roles[0];

            if (role.is_system) {
                return res.status(403).json({ success: false, message: 'System roles cannot be deleted' });
            }

            // Check if role is assigned to any users
            const [users] = await db.query(
                'SELECT COUNT(*) as count FROM users WHERE role = ?',
                [role.role_name]
            );

            if (users[0].count > 0) {
                return res.status(403).json({
                    success: false,
                    message: `Cannot delete role that is assigned to ${users[0].count} users`
                });
            }

            // Delete role (cascade will delete role_permissions)
            await db.query(
                'DELETE FROM roles WHERE role_id = ?',
                [roleId]
            );

            return res.json({ success: true, message: 'Role deleted successfully' });
        } catch (error) {
            console.error('Error deleting role:', error);
            return res.status(500).json({ success: false, message: 'Error deleting role' });
        }
    },

    /**
     * View users with a specific role
     */
    viewUsers: async (req, res, next) => {
        try {
            const roleId = req.params.id;

            // Get role details
            const [roles] = await db.query(
                'SELECT * FROM roles WHERE role_id = ?',
                [roleId]
            );

            if (roles.length === 0) {
                req.session.flashError = 'Role not found';
                return res.redirect('/admin/users/roles');
            }

            const role = roles[0];

            // Get users with this role
            const [users] = await db.query(
                'SELECT id, username, name, email, profile_image, last_login FROM users WHERE role = ? ORDER BY name',
                [role.role_name]
            );

            res.render('admin/users/role-users', {
                title: `Users with Role: ${role.role_name}`,
                pageTitle: `Users with Role: ${role.role_name}`,
                currentPage: 'roles',
                role,
                users
            });
        } catch (error) {
            console.error('Error loading role users:', error);
            req.session.flashError = 'Error loading role users';
            res.redirect('/admin/users/roles');
        }
    }
};

module.exports = roleController;
