/**
 * Settings Controller
 * Handles site settings management
 */
const db = require('../config/database');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { promisify } = require('util');
const mkdirAsync = promisify(fs.mkdir);

// Configure multer for logo uploads
const logoStorage = multer.diskStorage({
    destination: async function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/site');
        try {
            // Ensure the directory exists
            await mkdirAsync(uploadDir, { recursive: true });
            // Use relative path for storage
            cb(null, 'public/uploads/site');
        } catch (error) {
            console.error('Error creating upload directory:', error);
            cb(error);
        }
    },
    filename: function (req, file, cb) {
        // Get file extension
        const ext = path.extname(file.originalname).toLowerCase();
        // Use 'site-logo' as the filename with the original extension
        cb(null, 'site-logo' + ext);
    }
});

// Create multer upload instance
const logoUpload = multer({
    storage: logoStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: function (req, file, cb) {
        // Accept only image files
        const filetypes = /jpeg|jpg|png|gif|svg/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only image files are allowed!'));
    }
}).single('logo');

// Promisify the multer upload function
const uploadLogoAsync = (req, res) => {
    return new Promise((resolve, reject) => {
        logoUpload(req, res, function (err) {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
};

/**
 * Display the logo upload page
 */
exports.logoUploadPage = async (req, res) => {
    try {
        // Get current logo settings
        const [logoSettings] = await db.query(
            'SELECT * FROM site_settings WHERE setting_key = ?',
            ['site_logo']
        );

        let currentLogo = null;
        if (logoSettings && logoSettings.length > 0) {
            currentLogo = logoSettings[0].setting_value;
        }

        res.render('admin/settings/logo-upload', {
            title: 'Upload Site Logo',
            pageTitle: 'Upload Site Logo',
            currentLogo,
            layout: 'admin',
            currentPage: 'settings'
        });
    } catch (error) {
        console.error('Error loading logo upload page:', error);
        req.flash('error', 'Failed to load logo upload page');
        res.redirect('/admin/settings');
    }
};

/**
 * Handle logo upload
 */
exports.uploadLogo = async (req, res) => {
    try {
        // Process the file upload
        await uploadLogoAsync(req, res);

        if (!req.file) {
            req.flash('error', 'No file selected');
            return res.redirect('/admin/settings/logo');
        }

        // Get the file path relative to the public directory
        const logoPath = '/uploads/site/' + req.file.filename;

        // Check if setting already exists
        const [existingSettings] = await db.query(
            'SELECT * FROM site_settings WHERE setting_key = ?',
            ['site_logo']
        );

        if (existingSettings && existingSettings.length > 0) {
            // Update existing setting
            await db.query(
                'UPDATE site_settings SET setting_value = ? WHERE setting_key = ?',
                [logoPath, 'site_logo']
            );
        } else {
            // Insert new setting
            await db.query(
                'INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)',
                ['site_logo', logoPath]
            );
        }

        req.flash('success', 'Logo uploaded successfully');
        res.redirect('/admin/settings/logo');
    } catch (error) {
        console.error('Error uploading logo:', error);
        req.flash('error', error.message || 'Failed to upload logo');
        res.redirect('/admin/settings/logo');
    }
};

/**
 * Delete the current logo
 */
exports.deleteLogo = async (req, res) => {
    try {
        // Get current logo
        const [logoSettings] = await db.query(
            'SELECT * FROM site_settings WHERE setting_key = ?',
            ['site_logo']
        );

        if (logoSettings && logoSettings.length > 0) {
            const logoPath = logoSettings[0].setting_value;

            // Delete the file if it exists
            if (logoPath) {
                const fullPath = path.join(__dirname, '../public', logoPath);
                if (fs.existsSync(fullPath)) {
                    fs.unlinkSync(fullPath);
                }
            }

            // Update the setting to null
            await db.query(
                'UPDATE site_settings SET setting_value = NULL WHERE setting_key = ?',
                ['site_logo']
            );
        }

        req.flash('success', 'Logo removed successfully');
        res.redirect('/admin/settings/logo');
    } catch (error) {
        console.error('Error deleting logo:', error);
        req.flash('error', 'Failed to delete logo');
        res.redirect('/admin/settings/logo');
    }
};

/**
 * Get site settings
 * Helper function to get site settings
 */
exports.getSiteSettings = async () => {
    try {
        const [settings] = await db.query('SELECT * FROM site_settings');

        // Convert to key-value object
        const settingsObj = {};
        if (settings && settings.length > 0) {
            settings.forEach(setting => {
                settingsObj[setting.setting_key] = setting.setting_value;
            });
        }

        return settingsObj;
    } catch (error) {
        console.error('Error getting site settings:', error);
        return {};
    }
};
