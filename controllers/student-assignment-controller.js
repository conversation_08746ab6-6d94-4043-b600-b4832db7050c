const db = require('../config/database');

// Get all students with their class assignments
exports.getStudents = async (req, res) => {
  try {
    // Get all students
    const [students] = await db.query(`
      SELECT u.id, u.username, u.email, u.role, u.is_active, u.profile_image
      FROM users u
      WHERE u.role = 'student' AND u.is_deleted = 0
      ORDER BY u.username
    `);

    // Get all classes
    const [classes] = await db.query(`
      SELECT id, name, description
      FROM classes
      WHERE is_active = 1
      ORDER BY name
    `);

    res.render('admin/students/index', {
      title: 'Student Management',
      layout: 'admin',
      currentPage: 'students',
      students,
      classes
    });
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch students',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Get student class assignment form
exports.getAssignClass = async (req, res) => {
  try {
    const studentId = req.params.id;
    
    // Get student details
    const [students] = await db.query(
      'SELECT id, username, email FROM users WHERE id = ? AND role = "student"',
      [studentId]
    );
    
    if (students.length === 0) {
      req.session.flashError = 'Student not found';
      return res.redirect('/admin/students');
    }
    
    // Get all classes
    const [classes] = await db.query(`
      SELECT id, name, description
      FROM classes
      WHERE is_active = 1
      ORDER BY name
    `);
    
    // Get student's current class assignments
    const [studentClasses] = await db.query(`
      SELECT sc.class_id, c.name as class_name
      FROM student_classes sc
      JOIN classes c ON sc.class_id = c.id
      WHERE sc.student_id = ?
    `, [studentId]);
    
    res.render('admin/students/assign-class', {
      title: 'Assign Class',
      layout: 'admin',
      currentPage: 'students',
      student: students[0],
      classes,
      studentClasses: studentClasses.map(sc => sc.class_id)
    });
  } catch (error) {
    console.error('Error fetching student class data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch student class data',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Update student class assignments
exports.updateClassAssignments = async (req, res) => {
  try {
    const studentId = req.params.id;
    const { classIds } = req.body;
    
    // Validate input
    if (!Array.isArray(classIds)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid class selection'
      });
    }
    
    // Check if student exists
    const [students] = await db.query(
      'SELECT id FROM users WHERE id = ? AND role = "student"',
      [studentId]
    );
    
    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    // Begin transaction
    await db.query('START TRANSACTION');
    
    try {
      // Remove all existing class assignments for this student
      await db.query(
        'DELETE FROM student_classes WHERE student_id = ?',
        [studentId]
      );
      
      // Add new class assignments
      if (classIds.length > 0) {
        const values = classIds.map(classId => [studentId, classId]);
        await db.query(
          'INSERT INTO student_classes (student_id, class_id) VALUES ?',
          [values]
        );
      }
      
      // Commit transaction
      await db.query('COMMIT');
      
      res.json({
        success: true,
        message: 'Class assignments updated successfully'
      });
    } catch (error) {
      // Rollback transaction on error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Error updating class assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update class assignments'
    });
  }
};

// Get student subject assignment form
exports.getAssignSubjects = async (req, res) => {
  try {
    const studentId = req.params.id;
    
    // Get student details
    const [students] = await db.query(
      'SELECT id, username, email FROM users WHERE id = ? AND role = "student"',
      [studentId]
    );
    
    if (students.length === 0) {
      req.session.flashError = 'Student not found';
      return res.redirect('/admin/students');
    }
    
    // Get all subjects
    const [subjects] = await db.query(`
      SELECT id, name, description
      FROM subjects
      WHERE is_active = 1
      ORDER BY name
    `);
    
    // Get student's current subject assignments
    const [studentSubjects] = await db.query(`
      SELECT ss.subject_id, s.name as subject_name
      FROM student_subjects ss
      JOIN subjects s ON ss.subject_id = s.id
      WHERE ss.student_id = ?
    `, [studentId]);
    
    res.render('admin/students/assign-subjects', {
      title: 'Assign Subjects',
      layout: 'admin',
      currentPage: 'students',
      student: students[0],
      subjects,
      studentSubjects: studentSubjects.map(ss => ss.subject_id)
    });
  } catch (error) {
    console.error('Error fetching student subject data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch student subject data',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Update student subject assignments
exports.updateSubjectAssignments = async (req, res) => {
  try {
    const studentId = req.params.id;
    const { subjectIds } = req.body;
    
    // Validate input
    if (!Array.isArray(subjectIds)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid subject selection'
      });
    }
    
    // Check if student exists
    const [students] = await db.query(
      'SELECT id FROM users WHERE id = ? AND role = "student"',
      [studentId]
    );
    
    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }
    
    // Begin transaction
    await db.query('START TRANSACTION');
    
    try {
      // Remove all existing subject assignments for this student
      await db.query(
        'DELETE FROM student_subjects WHERE student_id = ?',
        [studentId]
      );
      
      // Add new subject assignments
      if (subjectIds.length > 0) {
        const values = subjectIds.map(subjectId => [studentId, subjectId]);
        await db.query(
          'INSERT INTO student_subjects (student_id, subject_id) VALUES ?',
          [values]
        );
      }
      
      // Commit transaction
      await db.query('COMMIT');
      
      res.json({
        success: true,
        message: 'Subject assignments updated successfully'
      });
    } catch (error) {
      // Rollback transaction on error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('Error updating subject assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subject assignments'
    });
  }
};
