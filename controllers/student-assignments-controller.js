const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Get all assignments for the student
exports.getAssignments = async (req, res) => {
  try {
    const studentId = req.session.userId || req.session.user.id;
    
    // Get student's classes
    const [studentClasses] = await db.query(`
      SELECT c.id
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);
    
    // Handle case where studentClasses might be empty
    const classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];
    
    // Get pending assignments
    let pendingAssignments = [];
    let submittedAssignments = [];
    let gradedAssignments = [];
    
    try {
      // Check if assignments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'assignments'
      `);
      
      if (tables.length > 0) {
        // If assignments table exists, fetch pending assignments
        if (classIds.length > 0) {
          [pendingAssignments] = await db.query(`
            SELECT a.*, s.name as subject_name, u.full_name as teacher_name,
                  (SELECT COUNT(*) FROM assignment_submissions 
                   WHERE assignment_id = a.id AND student_id = ?) as has_submission
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.id
            LEFT JOIN users u ON a.teacher_id = u.id
            WHERE (a.student_id = ? OR a.class_id IN (?))
            AND a.due_date >= NOW()
            AND NOT EXISTS (
              SELECT 1 FROM assignment_submissions
              WHERE assignment_id = a.id AND student_id = ?
            )
            ORDER BY a.due_date ASC
          `, [studentId, studentId, classIds, studentId]);
          
          // Fetch submitted but not graded assignments
          [submittedAssignments] = await db.query(`
            SELECT a.*, s.name as subject_name, u.full_name as teacher_name,
                  asub.submission_date, asub.status
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.id
            LEFT JOIN users u ON a.teacher_id = u.id
            JOIN assignment_submissions asub ON a.id = asub.assignment_id
            WHERE asub.student_id = ?
            AND asub.status = 'submitted'
            ORDER BY asub.submission_date DESC
          `, [studentId]);
          
          // Fetch graded assignments
          [gradedAssignments] = await db.query(`
            SELECT a.*, s.name as subject_name, u.full_name as teacher_name,
                  asub.submission_date, asub.status, asub.marks_obtained,
                  asub.feedback, asub.graded_at
            FROM assignments a
            LEFT JOIN subjects s ON a.subject_id = s.id
            LEFT JOIN users u ON a.teacher_id = u.id
            JOIN assignment_submissions asub ON a.id = asub.assignment_id
            WHERE asub.student_id = ?
            AND asub.status = 'graded'
            ORDER BY asub.graded_at DESC
          `, [studentId]);
        }
      }
    } catch (err) {
      console.log('Error fetching assignments:', err.message);
      // Continue with empty assignments
    }
    
    // Get academic plan assessments (another type of assignment)
    let assessments = [];
    try {
      // Check if academic_plan_assessments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'academic_plan_assessments'
      `);
      
      if (tables.length > 0 && classIds.length > 0) {
        // If table exists, fetch assessments
        [assessments] = await db.query(`
          SELECT apa.*, ip.title as plan_title, s.name as subject_name
          FROM academic_plan_assessments apa
          JOIN instruction_plans ip ON apa.plan_id = ip.id
          LEFT JOIN subjects s ON ip.subject_id = s.id
          WHERE ip.class_id IN (?)
          AND apa.assessment_date >= CURDATE()
          ORDER BY apa.assessment_date ASC
        `, [classIds]);
      }
    } catch (err) {
      console.log('Academic plan assessments table may not exist yet:', err.message);
      // Continue without assessments data
    }
    
    res.render('student/assignments/index', {
      title: 'My Assignments',
      layout: 'student',
      currentPage: 'assignments',
      user: res.locals.user,
      notificationCount: 0,
      pendingAssignments,
      submittedAssignments,
      gradedAssignments,
      assessments,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching student assignments:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading assignments',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
};

// Get assignment details
exports.getAssignmentDetails = async (req, res) => {
  try {
    const assignmentId = req.params.id;
    const studentId = req.session.userId || req.session.user.id;
    
    // Get assignment details
    const [assignments] = await db.query(`
      SELECT a.*, s.name as subject_name, u.full_name as teacher_name
      FROM assignments a
      LEFT JOIN subjects s ON a.subject_id = s.id
      LEFT JOIN users u ON a.teacher_id = u.id
      WHERE a.id = ?
      AND (a.student_id = ? OR a.class_id IN (
        SELECT class_id FROM student_classes WHERE student_id = ?
      ))
    `, [assignmentId, studentId, studentId]);
    
    if (assignments.length === 0) {
      req.flash('error', 'Assignment not found');
      return res.redirect('/student/assignments');
    }
    
    const assignment = assignments[0];
    
    // Check if student has submitted for this assignment
    const [submissions] = await db.query(`
      SELECT * FROM assignment_submissions
      WHERE assignment_id = ? AND student_id = ?
    `, [assignmentId, studentId]);
    
    const hasSubmission = submissions.length > 0;
    const submission = hasSubmission ? submissions[0] : null;
    
    res.render('student/assignments/view', {
      title: assignment.title || 'Assignment Details',
      assignment,
      submission,
      hasSubmission,
      user: res.locals.user,
      layout: 'student',
      currentPage: 'assignments',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching assignment details:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch assignment details',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0
    });
  }
};

// Submit assignment
exports.submitAssignment = async (req, res) => {
  try {
    const assignmentId = req.params.id;
    const studentId = req.session.userId || req.session.user.id;
    const { content } = req.body;
    const filePath = req.file ? `/uploads/assignment_submissions/${req.file.filename}` : null;
    
    // Check if assignment exists and is valid for this student
    const [assignments] = await db.query(`
      SELECT a.*
      FROM assignments a
      WHERE a.id = ?
      AND (a.student_id = ? OR a.class_id IN (
        SELECT class_id FROM student_classes WHERE student_id = ?
      ))
    `, [assignmentId, studentId, studentId]);
    
    if (assignments.length === 0) {
      req.flash('error', 'Assignment not found');
      return res.redirect('/student/assignments');
    }
    
    // Check if already submitted
    const [existingSubmissions] = await db.query(`
      SELECT * FROM assignment_submissions
      WHERE assignment_id = ? AND student_id = ?
    `, [assignmentId, studentId]);
    
    if (existingSubmissions.length > 0) {
      // Update existing submission
      await db.query(`
        UPDATE assignment_submissions
        SET submission_text = ?, file_path = ?, submission_date = NOW(), status = 'submitted'
        WHERE assignment_id = ? AND student_id = ?
      `, [content, filePath, assignmentId, studentId]);
      
      req.flash('success', 'Assignment submission updated successfully');
    } else {
      // Create new submission
      await db.query(`
        INSERT INTO assignment_submissions
        (assignment_id, student_id, submission_text, file_path, status)
        VALUES (?, ?, ?, ?, 'submitted')
      `, [assignmentId, studentId, content, filePath]);
      
      req.flash('success', 'Assignment submitted successfully');
    }
    
    res.redirect('/student/assignments');
  } catch (error) {
    console.error('Error submitting assignment:', error);
    req.flash('error', 'Failed to submit assignment');
    res.redirect(`/student/assignments/${req.params.id}`);
  }
};
