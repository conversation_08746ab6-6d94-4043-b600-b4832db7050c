const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Student Dashboard
exports.getDashboard = async (req, res) => {
  try {
    const studentId = req.session.userId;

    // Initialize default values
    let testStats = [{ assignedTests: 0, completedTests: 0, averageScore: 0 }];

    try {
      // Check if test_assignments table exists
      const [testAssignmentsTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'test_assignments'
      `);

      if (testAssignmentsTable[0].table_exists > 0) {
        // Get test statistics
        [testStats] = await db.query(`
          SELECT
            COUNT(DISTINCT ta.exam_id) as assignedTests,
            COUNT(DISTINCT CASE WHEN ea.status = 'completed' THEN ea.exam_id END) as completedTests,
            IFNULL(ROUND(AVG(CASE WHEN ea.status = 'completed' THEN ea.total_score END), 1), 0) as averageScore
          FROM test_assignments ta
          LEFT JOIN exam_attempts ea ON ta.exam_id = ea.exam_id AND ea.user_id = ?
          WHERE ta.user_id = ?
        `, [studentId, studentId]);
      }
    } catch (error) {
      console.error('Error fetching test statistics:', error);
      // Continue with default values
    }

    // Get learning plans count
    let planStats = [{ learningPlans: 0 }];
    try {
      [planStats] = await db.query(`
        SELECT COUNT(*) as learningPlans
        FROM student_instruction_plans
        WHERE student_id = ?
      `, [studentId]);
    } catch (err) {
      console.error('Error fetching learning plans count:', err);
      // Continue with default value if table doesn't exist
    }

    // Get upcoming tests
    let upcomingTests = [];

    try {
      // Check if test_assignments table exists
      const [testAssignmentsTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'test_assignments'
      `);

      if (testAssignmentsTable[0].table_exists > 0) {
        [upcomingTests] = await db.query(`
          SELECT e.exam_id, e.exam_name, e.category_id as subject, ta.end_datetime as end_date, ta.max_attempts,
            COUNT(ea.attempt_id) as attempts_used
          FROM test_assignments ta
          JOIN exams e ON ta.exam_id = e.exam_id
          LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id AND ea.user_id = ?
          WHERE ta.user_id = ?
          AND (ta.end_datetime IS NULL OR ta.end_datetime >= CURDATE())
          GROUP BY e.exam_id, e.exam_name, e.category_id, ta.end_datetime, ta.max_attempts
          HAVING COUNT(ea.attempt_id) < ta.max_attempts
          ORDER BY ta.end_datetime ASC
          LIMIT 5
        `, [studentId, studentId]);
      }
    } catch (error) {
      console.error('Error fetching upcoming tests:', error);
      // Continue with empty array
    }

    // Get recent activity
    let recentActivity = [];
    try {
      [recentActivity] = await db.query(`
        (SELECT
          'test_completed' as type,
          e.exam_name,
          ea.total_score as score,
          ea.end_time as created_at,
          NULL as plan_name
        FROM exam_attempts ea
        JOIN exams e ON ea.exam_id = e.exam_id
        WHERE ea.user_id = ? AND ea.status = 'completed'
        ORDER BY ea.end_time DESC
        LIMIT 3)

        UNION ALL

        (SELECT
          'test_assigned' as type,
          e.exam_name,
          NULL as score,
          ta.assigned_at as created_at,
          NULL as plan_name
        FROM test_assignments ta
        JOIN exams e ON ta.exam_id = e.exam_id
        WHERE ta.user_id = ?
        ORDER BY ta.assigned_at DESC
        LIMIT 3)

        ORDER BY created_at DESC
        LIMIT 5
      `, [studentId, studentId]);
    } catch (err) {
      console.error('Error fetching recent activity:', err);
      // Continue with empty array if query fails
    }

    // Try to get plan completions if the table exists
    try {
      const [planCompletions] = await db.query(`
        SELECT
          'plan_completed' as type,
          NULL as exam_name,
          NULL as score,
          spc.completed_at as created_at,
          ip.title as plan_name
        FROM student_plan_completions spc
        JOIN instruction_plans ip ON spc.plan_id = ip.id
        WHERE spc.student_id = ?
        ORDER BY spc.completed_at DESC
        LIMIT 3
      `, [studentId]);

      // Combine with existing activity and sort by date
      if (planCompletions && planCompletions.length > 0) {
        recentActivity = [...recentActivity, ...planCompletions]
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 5);
      }
    } catch (err) {
      console.error('Error fetching plan completions:', err);
      // Continue with existing activity if table doesn't exist
    }

    // Get today's class schedule
    let todaySchedule = [];

    try {
      // Check if class_schedule table exists
      const [classScheduleTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'class_schedule'
      `);

      if (classScheduleTable[0].table_exists > 0) {
        [todaySchedule] = await db.query(`
          SELECT
            c.start_time,
            c.end_time,
            s.name as subject,
            u.full_name as teacher_name,
            c.room,
            c.type
          FROM class_schedule c
          JOIN subjects s ON c.subject_id = s.id
          JOIN users u ON c.teacher_id = u.id
          JOIN student_classes sc ON c.class_id = sc.class_id
          WHERE sc.student_id = ?
          AND c.day_of_week = WEEKDAY(CURDATE()) + 1
          ORDER BY c.start_time ASC
        `, [studentId]);
      }
    } catch (error) {
      console.error('Error fetching today\'s class schedule:', error);
      // Continue with empty array
    }

    // Get student's classes
    let studentClasses = [];
    let classIds = [];

    try {
      // Check if classes and student_classes tables exist
      const [classesTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'classes'
      `);

      const [studentClassesTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'student_classes'
      `);

      if (classesTable[0].table_exists > 0 && studentClassesTable[0].table_exists > 0) {
        [studentClasses] = await db.query(`
          SELECT c.id
          FROM classes c
          JOIN student_classes sc ON c.id = sc.class_id
          WHERE sc.student_id = ?
        `, [studentId]);

        classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];
      }
    } catch (error) {
      console.error('Error fetching student\'s classes:', error);
      // Continue with empty array
    }

    // Get upcoming practicals
    let upcomingPracticals = [];
    if (classIds.length > 0) {
      [upcomingPracticals] = await db.query(`
        SELECT p.*,
               (SELECT COUNT(*) FROM practical_records
                WHERE practical_id = p.id AND student_id = ?) as has_submission
        FROM practicals p
        WHERE p.class_id IN (?)
        AND p.date >= CURDATE()
        AND p.status != 'Cancelled'
        ORDER BY p.date ASC, p.start_time ASC
        LIMIT 5
      `, [studentId, classIds]);
    }

    // Get all test assignments for calendar
    let calendarTestAssignments = [];

    try {
      // Check if test_assignments table exists
      const [testAssignmentsTable] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'test_assignments'
      `);

      if (testAssignmentsTable[0].table_exists > 0) {
        [calendarTestAssignments] = await db.query(`
          SELECT ta.exam_id, e.exam_name, ta.end_datetime
          FROM test_assignments ta
          JOIN exams e ON ta.exam_id = e.exam_id
          WHERE (ta.user_id = ? OR
                ta.group_id IN (SELECT group_id FROM group_members WHERE user_id = ?))
          AND ta.is_active = 1
          AND ta.end_datetime IS NOT NULL
          AND ta.end_datetime >= CURDATE()
          AND ta.end_datetime <= DATE_ADD(CURDATE(), INTERVAL 60 DAY)
        `, [studentId, studentId]);
      }
    } catch (error) {
      console.error('Error fetching calendar test assignments:', error);
      // Continue with empty array
    }

    // Get all practicals for calendar
    let calendarPracticals = [];
    if (classIds.length > 0) {
      [calendarPracticals] = await db.query(`
        SELECT p.id, p.date, p.description
        FROM practicals p
        WHERE p.class_id IN (?)
        AND p.date >= CURDATE()
        AND p.date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY)
        AND p.status != 'Cancelled'
      `, [classIds]);
    }

    // Try to get assignments for calendar if the table exists
    let calendarAssignments = [];
    try {
      // Check if assignments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'assignments'
      `);

      if (tables.length > 0) {
        // If assignments table exists, fetch assignments
        [calendarAssignments] = await db.query(`
          SELECT id, title, due_date
          FROM assignments
          WHERE (student_id = ? OR class_id IN (?))
          AND due_date >= CURDATE()
          AND due_date <= DATE_ADD(CURDATE(), INTERVAL 60 DAY)
        `, [studentId, classIds]);
      }
    } catch (err) {
      console.log('Assignments table may not exist yet:', err.message);
      // Continue without assignments data
    }

    // Combine all stats
    const stats = {
      assignedTests: testStats[0]?.assignedTests || 0,
      completedTests: testStats[0]?.completedTests || 0,
      averageScore: testStats[0]?.averageScore ? `${testStats[0].averageScore}%` : '0%',
      learningPlans: planStats[0]?.learningPlans || 0,
      upcomingPracticals: upcomingPracticals.length || 0
    };

    // Prepare calendar events data
    const calendarEvents = [
      ...calendarTestAssignments.map(test => ({
        type: 'test',
        title: test.exam_name,
        date: test.end_datetime instanceof Date
          ? test.end_datetime.toISOString().substring(0, 10)
          : (typeof test.end_datetime === 'string'
              ? test.end_datetime.substring(0, 10)
              : new Date().toISOString().substring(0, 10)),
        color: 'indigo'
      })),
      ...calendarPracticals.map(practical => ({
        type: 'practical',
        title: practical.description || 'Practical',
        date: practical.date instanceof Date
          ? practical.date.toISOString().substring(0, 10)
          : (typeof practical.date === 'string'
              ? practical.date.substring(0, 10)
              : new Date().toISOString().substring(0, 10)),
        color: 'blue'
      })),
      ...calendarAssignments.map(assignment => ({
        type: 'assignment',
        title: assignment.title || 'Assignment',
        date: assignment.due_date instanceof Date
          ? assignment.due_date.toISOString().substring(0, 10)
          : (typeof assignment.due_date === 'string'
              ? assignment.due_date.substring(0, 10)
              : new Date().toISOString().substring(0, 10)),
        color: 'orange'
      }))
    ];

    res.render('student/dashboard', {
      title: 'Student Dashboard',
      layout: 'student',
      currentPage: 'dashboard',
      user: res.locals.user,
      notificationCount: 0,
      stats,
      upcomingTests: upcomingTests || [],
      recentActivity: recentActivity || [],
      todaySchedule: todaySchedule || [],
      upcomingPracticals: upcomingPracticals || [],
      calendarEvents: calendarEvents || [],
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching student dashboard data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading dashboard',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
};

// Student Activity
exports.getActivity = async (req, res) => {
  try {
    const studentId = req.session.userId;

    // Get all activity
    let activity = [];
    try {
      [activity] = await db.query(`
        (SELECT
          'test_completed' as type,
          e.exam_name,
          ea.total_score as score,
          ea.end_time as created_at,
          NULL as plan_name
        FROM exam_attempts ea
        JOIN exams e ON ea.exam_id = e.exam_id
        WHERE ea.user_id = ? AND ea.status = 'completed')

        UNION ALL

        (SELECT
          'test_assigned' as type,
          e.exam_name,
          NULL as score,
          ta.assigned_at as created_at,
          NULL as plan_name
        FROM test_assignments ta
        JOIN exams e ON ta.exam_id = e.exam_id
        WHERE ta.user_id = ?)

        ORDER BY created_at DESC
      `, [studentId, studentId]);
    } catch (err) {
      console.error('Error fetching activity:', err);
      // Continue with empty array if query fails
    }

    // Try to get plan completions if the table exists
    try {
      const [planCompletions] = await db.query(`
        SELECT
          'plan_completed' as type,
          NULL as exam_name,
          NULL as score,
          spc.completed_at as created_at,
          ip.title as plan_name
        FROM student_plan_completions spc
        JOIN instruction_plans ip ON spc.plan_id = ip.id
        WHERE spc.student_id = ?
        ORDER BY spc.completed_at DESC
      `, [studentId]);

      // Combine with existing activity and sort by date
      if (planCompletions && planCompletions.length > 0) {
        activity = [...activity, ...planCompletions]
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      }
    } catch (err) {
      console.error('Error fetching plan completions for activity page:', err);
      // Continue with existing activity if table doesn't exist
    }

    res.render('student/activity', {
      title: 'Activity History',
      layout: 'student',
      currentPage: 'activity',
      user: res.locals.user,
      notificationCount: 0,
      activity: activity || [],
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching student activity:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading activity history',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
};
