const db = require('../config/database');
const XLSX = require('xlsx');
const bcrypt = require('bcrypt');
const groupController = require('./group-controller');
const csv = require('csv-parser');
const { Readable } = require('stream');

/**
 * Parse CSV data
 * @param {string} csvData - CSV content as string
 * @returns {Array} - Array of student objects
 */
async function parseCSV(csvData) {
    console.log('Parsing CSV data with proper CSV parser...');
    console.log('CSV data preview:', csvData.substring(0, 300) + '...');
    console.log('CSV data length:', csvData.length);

    // Check if data looks like it has proper line endings
    const lines = csvData.split('\n');
    console.log('Number of lines:', lines.length);
    console.log('First line (header):', lines[0]);
    if (lines.length > 1) {
        console.log('Second line (first data):', lines[1]);
    }

    return new Promise((resolve, reject) => {
        const results = [];
        const stream = Readable.from([csvData]);

        stream
            .pipe(csv({
                skipEmptyLines: true,
                trim: true,
                separator: ',',
                quote: '"',
                escape: '"'
            }))
            .on('headers', (headers) => {
                console.log('CSV headers detected:', headers);
            })
            .on('data', (row) => {
                console.log('Parsed CSV row:', {
                    name: row.name || 'MISSING',
                    student_id: row.student_id || 'MISSING',
                    gender: row.gender || 'MISSING',
                    class: row.class || 'MISSING',
                    allKeys: Object.keys(row),
                    allValues: Object.values(row)
                });
                results.push(row);
            })
            .on('end', () => {
                console.log(`Successfully parsed ${results.length} student records`);
                if (results.length > 0) {
                    console.log('Sample parsed record:', results[0]);
                }
                resolve(results);
            })
            .on('error', (error) => {
                console.error('CSV parsing error:', error);
                reject(new Error(`CSV parsing failed: ${error.message}`));
            });
    });
}

/**
 * Parse Excel data
 * @param {Buffer} buffer - Excel file buffer
 * @returns {Array} - Array of student objects
 */
async function parseExcel(buffer) {
    try {
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: '',
            blankrows: false
        });

        if (jsonData.length < 2) {
            throw new Error('Excel file must contain at least a header row and one data row');
        }

        const headers = jsonData[0];
        const results = [];

        for (let i = 1; i < jsonData.length; i++) {
            const row = {};
            for (let j = 0; j < headers.length; j++) {
                row[headers[j]] = jsonData[i][j] || '';
            }
            results.push(row);
        }

        return results;
    } catch (error) {
        throw new Error(`Error parsing Excel file: ${error.message}`);
    }
}

/**
 * Create user account for student
 * @param {Object} studentData - Student data object
 * @param {Object} connection - Database connection
 * @returns {Object} - User creation result
 */
async function createStudentUser(studentData, connection) {
    try {
        // Generate username from student_id
        const username = studentData.student_id.toLowerCase();

        // Generate email if contact number is available
        let email = null;
        if (studentData.contact_no) {
            email = `${username}@student.school.edu`;
        }

        // Generate default password (student_id + "123")
        const defaultPassword = `${studentData.student_id}123`;
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);

        // Check if user already exists
        const [existingUsers] = await connection.query(
            'SELECT id FROM users WHERE username = ?',
            [username]
        );

        if (existingUsers.length > 0) {
            return {
                success: false,
                message: 'User already exists',
                userId: existingUsers[0].id
            };
        }

        // Create user account
        const [userResult] = await connection.query(
            `INSERT INTO users (
                username, name, email, password, role,
                date_of_birth, bio, is_active, is_approved, is_deleted,
                created_at, last_login
            ) VALUES (?, ?, ?, ?, ?, ?, '', 1, 1, 0, NOW(), NOW())`,
            [
                username,
                studentData.name,
                email,
                hashedPassword,
                'student',
                studentData.dob
            ]
        );

        const userId = userResult.insertId;

        // Add user to student role group
        try {
            await groupController.addUserToRoleGroup(userId, 'student');
        } catch (groupError) {
            console.error('Error adding student to role group:', groupError);
            // Continue even if group assignment fails
        }

        return {
            success: true,
            message: 'User created successfully',
            userId: userId,
            username: username,
            defaultPassword: defaultPassword
        };

    } catch (error) {
        console.error('Error creating student user:', error);
        return {
            success: false,
            message: error.message,
            userId: null
        };
    }
}

/**
 * Validate student data
 * @param {Object} student - Student data object
 * @returns {Object} - Validation result
 */
function validateStudent(student) {
    const errors = [];

    // Required fields
    if (!student.name || student.name.trim() === '') {
        errors.push('Name is required');
    }

    if (!student.student_id || student.student_id.trim() === '') {
        errors.push('Student ID is required');
    }

    if (!student.class || student.class.trim() === '') {
        errors.push('Class is required');
    }

    // Note: Session is now handled at import level, not per student
    // if (!student.session || student.session.trim() === '') {
    //     errors.push('Session is required');
    // }

    if (!student.gender || !['Male', 'Female', 'Other'].includes(student.gender)) {
        errors.push('Gender must be Male, Female, or Other (current value: "' + (student.gender || 'empty') + '")');
    }

    // Validate date format for DOB
    if (student.dob && student.dob.trim() !== '') {
        const dobDate = new Date(student.dob);
        if (isNaN(dobDate.getTime())) {
            errors.push('Invalid date format for DOB (use YYYY-MM-DD)');
        }
    }

    // Validate admission date
    if (student.admission_date && student.admission_date.trim() !== '') {
        const admissionDate = new Date(student.admission_date);
        if (isNaN(admissionDate.getTime())) {
            errors.push('Invalid date format for Admission Date (use YYYY-MM-DD)');
        }
    }

    // Validate numeric fields
    if (student.height && student.height.trim() !== '') {
        const height = parseFloat(student.height);
        if (isNaN(height) || height <= 0 || height > 300) {
            errors.push('Height must be a valid number between 0 and 300 cm');
        }
    }

    if (student.weight && student.weight.trim() !== '') {
        const weight = parseFloat(student.weight);
        if (isNaN(weight) || weight <= 0 || weight > 200) {
            errors.push('Weight must be a valid number between 0 and 200 kg');
        }
    }

    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

/**
 * Import students from file
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileType - File type (csv or excel)
 * @param {number} userId - User ID of the importer
 * @param {string} session - Session to assign to students
 * @returns {Object} - Import results
 */
async function importStudents(fileBuffer, fileType, userId, session) {
    console.log(`Starting import process - File type: ${fileType}, Session: ${session}, User ID: ${userId}`);

    let students = [];

    try {
        // Parse file based on type
        if (fileType === 'csv') {
            const csvData = fileBuffer.toString();
            console.log(`CSV data length: ${csvData.length} characters`);
            students = await parseCSV(csvData);
        } else if (fileType === 'excel') {
            console.log(`Excel buffer size: ${fileBuffer.length} bytes`);
            students = await parseExcel(fileBuffer);
        } else {
            throw new Error('Unsupported file type');
        }

        console.log(`Parsed ${students.length} student records from file`);

        // Validate students
        if (!Array.isArray(students) || students.length === 0) {
            throw new Error('No valid students found in file');
        }

        // Log first student for debugging
        if (students.length > 0) {
            console.log('First student data:', JSON.stringify(students[0], null, 2));
        }
    } catch (parseError) {
        console.error('Error parsing file:', parseError);
        throw new Error(`Error parsing file: ${parseError.message}`);
    }

    const results = {
        imported: 0,
        updated: 0,
        skipped: 0,
        errors: [],
        usersCreated: 0,
        userCreationErrors: []
    };

    const connection = await db.getConnection();

    try {
        await connection.beginTransaction();

        // Process each student
        for (const [index, student] of students.entries()) {
            try {
                // Validate student data
                const validation = validateStudent(student);
                if (!validation.isValid) {
                    results.errors.push(`Row ${index + 2}: ${validation.errors.join(', ')}`);
                    results.skipped++;
                    continue;
                }

                // Check if student already exists
                const [existingStudents] = await connection.query(
                    'SELECT * FROM students WHERE student_id = ?',
                    [student.student_id]
                );

                // Clean and prepare student data
                const cleanValue = (value) => {
                    if (!value) return null;
                    // Remove leading apostrophes and trim
                    return String(value).replace(/^'/, '').trim() || null;
                };

                const parseDate = (dateStr) => {
                    if (!dateStr) return null;
                    const cleaned = cleanValue(dateStr);
                    if (!cleaned) return null;

                    // Handle different date formats
                    try {
                        // Try DD-MMM-YYYY format (01-Oct-2009)
                        if (cleaned.includes('-') && cleaned.length > 8) {
                            const parts = cleaned.split('-');
                            if (parts.length === 3) {
                                const day = parts[0];
                                const month = parts[1];
                                const year = parts[2];

                                // Convert month name to number
                                const monthMap = {
                                    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
                                    'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
                                    'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                                };

                                const monthNum = monthMap[month] || month;
                                const isoDate = `${year}-${monthNum.padStart(2, '0')}-${day.padStart(2, '0')}`;
                                return new Date(isoDate);
                            }
                        }

                        // Try direct parsing
                        return new Date(cleaned);
                    } catch (error) {
                        console.warn('Date parsing error for:', cleaned, error.message);
                        return null;
                    }
                };

                // Prepare student data
                const studentData = {
                    sno: cleanValue(student.sno) || (index + 1),
                    student_id: cleanValue(student.student_id),
                    udise_code: cleanValue(student.udise_code),
                    name: cleanValue(student.name),
                    father_name: cleanValue(student.father_name),
                    mother_name: cleanValue(student.mother_name),
                    dob: parseDate(student.dob),
                    gender: cleanValue(student.gender),
                    class: cleanValue(student.class),
                    section: cleanValue(student.section),
                    session: cleanValue(student.session) || session, // Use session from CSV if provided, otherwise use form session
                    stream: cleanValue(student.stream),
                    trade: cleanValue(student.trade),
                    caste_category_name: cleanValue(student.caste_category_name),
                    bpl: cleanValue(student.bpl) || 'No',
                    disability: cleanValue(student.disability) || 'No',
                    religion_name: cleanValue(student.religion_name),
                    medium_name: cleanValue(student.medium_name) || 'English',
                    height: student.height ? parseFloat(cleanValue(student.height)) : null,
                    weight: student.weight ? parseFloat(cleanValue(student.weight)) : null,
                    admission_no: cleanValue(student.admission_no),
                    admission_date: parseDate(student.admission_date),
                    state_name: cleanValue(student.state_name),
                    district_name: cleanValue(student.district_name),
                    cur_address: cleanValue(student.cur_address),
                    village_ward: cleanValue(student.village_ward),
                    gram_panchayat: cleanValue(student.gram_panchayat),
                    pin_code: cleanValue(student.pin_code),
                    roll_no: cleanValue(student.roll_no),
                    contact_no: cleanValue(student.contact_no),
                    ifsc_code: cleanValue(student.ifsc_code),
                    bank_name: cleanValue(student.bank_name),
                    column1: cleanValue(student.column1),
                    account_holder_code: cleanValue(student.account_holder_code),
                    account_holder: cleanValue(student.account_holder),
                    account_holder_name: cleanValue(student.account_holder_name),
                    is_active: true
                };

                console.log(`Processing student ${index + 1}:`, {
                    student_id: studentData.student_id,
                    name: studentData.name,
                    class: studentData.class,
                    gender: studentData.gender,
                    session: studentData.session,
                    csvSession: student.session,
                    formSession: session
                });

                if (existingStudents.length > 0) {
                    // Update existing student
                    const updateFields = Object.keys(studentData).map(key => `${key} = ?`).join(', ');
                    const updateValues = Object.values(studentData);
                    updateValues.push(student.student_id);

                    await connection.query(
                        `UPDATE students SET ${updateFields} WHERE student_id = ?`,
                        updateValues
                    );

                    results.updated++;
                } else {
                    // Create new student
                    const insertFields = Object.keys(studentData).join(', ');
                    const insertPlaceholders = Object.keys(studentData).map(() => '?').join(', ');
                    const insertValues = Object.values(studentData);

                    await connection.query(
                        `INSERT INTO students (${insertFields}) VALUES (${insertPlaceholders})`,
                        insertValues
                    );

                    results.imported++;
                }

                // Auto-create user account for the student
                try {
                    const userCreationResult = await createStudentUser(studentData, connection);
                    if (userCreationResult.success) {
                        results.usersCreated++;
                        console.log(`Created user account for student: ${studentData.student_id} (Username: ${userCreationResult.username}, Password: ${userCreationResult.defaultPassword})`);
                    } else if (userCreationResult.message !== 'User already exists') {
                        results.userCreationErrors.push(`Row ${index + 2}: Failed to create user - ${userCreationResult.message}`);
                    }
                } catch (userError) {
                    console.error(`Error creating user for student ${studentData.student_id}:`, userError);
                    results.userCreationErrors.push(`Row ${index + 2}: User creation error - ${userError.message}`);
                }

            } catch (error) {
                console.error(`Error processing student at row ${index + 2}:`, error);
                results.errors.push(`Row ${index + 2}: ${error.message}`);
                results.skipped++;
            }
        }

        await connection.commit();

    } catch (error) {
        await connection.rollback();
        throw error;
    } finally {
        connection.release();
    }

    return results;
}

/**
 * Get sample CSV template
 * @returns {string} - CSV template content
 */
function getSampleCSVTemplate() {
    const headers = [
        'sno', 'student_id', 'udise_code', 'name', 'father_name', 'mother_name',
        'dob', 'gender', 'class', 'section', 'session', 'stream', 'trade',
        'caste_category_name', 'bpl', 'disability', 'religion_name', 'medium_name',
        'height', 'weight', 'admission_no', 'admission_date', 'state_name',
        'district_name', 'cur_address', 'village_ward', 'gram_panchayat',
        'pin_code', 'roll_no', 'contact_no', 'ifsc_code', 'bank_name',
        'column1', 'account_holder_code', 'account_holder', 'account_holder_name'
    ];

    const sampleData = [
        '1', 'STU001', 'UD12345', 'John Doe', 'Robert Doe', 'Jane Doe',
        '01-Oct-2009', 'Male', '11th', 'A', '2024-25', 'Science', 'Non-Medical',
        'General', 'No', 'No', 'Hindu', 'English',
        '165.5', '55.2', 'ADM001', '28-Oct-2024', 'Punjab',
        'Ludhiana', '"House No. 123, Model Town"', 'Model Town', 'Model Town Panchayat',
        '141001', 'R001', '**********', 'SBIN0001234', 'State Bank of India',
        'Additional Info', 'ACC001', 'John Doe', 'John Doe'
    ];

    return headers.join(',') + '\n' + sampleData.join(',');
}

module.exports = {
    importStudents,
    parseCSV,
    parseExcel,
    validateStudent,
    getSampleCSVTemplate,
    createStudentUser
};
