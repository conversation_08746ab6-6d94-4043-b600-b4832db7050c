const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Get all instruction plans assigned to the student
exports.getInstructionPlans = async (req, res) => {
  try {
    const studentId = req.session.userId || req.session.user.id;
    console.log('Loading instruction plans for student ID:', studentId);

    // Get student's classes
    const [studentClasses] = await db.query(`
      SELECT c.id
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);

    // Handle case where studentClasses might be empty
    const classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];
    console.log('Student class IDs:', classIds);

    // Get student's subjects
    const [studentSubjects] = await db.query(`
      SELECT ss.subject_id
      FROM student_subjects ss
      WHERE ss.student_id = ?
    `, [studentId]);

    // Handle case where studentSubjects might be empty
    const subjectIds = studentSubjects && studentSubjects.length > 0 ? studentSubjects.map(s => s.subject_id) : [];
    console.log('Student subject IDs:', subjectIds);

    // Get instruction plans for the student's class and subjects
    let plans = [];

    // Build the query based on available data
    try {
      let query = `
        SELECT p.*, s.name as subject_name,
              u.full_name as teacher_name
        FROM instruction_plans p
        LEFT JOIN subjects s ON p.subject_id = s.id
        LEFT JOIN users u ON p.teacher_id = u.id
        WHERE p.status = 'published'
      `;

      const queryParams = [];

      // Only add the filter conditions if we have class or subject IDs
      if (subjectIds.length > 0 || classIds.length > 0) {
        query += ` AND (`;

        const conditions = [];

        if (subjectIds.length > 0) {
          conditions.push(`p.subject_id IN (?)`);
          queryParams.push(subjectIds);
        }

        if (classIds.length > 0) {
          conditions.push(`p.class_id IN (?)`);
          queryParams.push(classIds);
        }

        query += conditions.join(' OR ');
        query += `)`;
      }

      query += ` ORDER BY p.created_at DESC`;

      console.log('Executing query for instruction plans');
      [plans] = await db.query(query, queryParams);
      console.log(`Found ${plans.length} instruction plans`);

      // Now add the is_viewed and is_completed flags
      // We'll do this in JavaScript instead of SQL to avoid potential issues
      for (const plan of plans) {
        // Check if plan is viewed
        try {
          const [views] = await db.query(`
            SELECT * FROM instruction_plan_views
            WHERE plan_id = ? AND student_id = ?
          `, [plan.id, studentId]);

          plan.is_viewed = views.length > 0;
        } catch (err) {
          console.log('Error checking plan views:', err.message);
          plan.is_viewed = false;
        }

        // Check if plan is completed
        try {
          const [completions] = await db.query(`
            SELECT * FROM instruction_plan_completions
            WHERE plan_id = ? AND student_id = ?
          `, [plan.id, studentId]);

          plan.is_completed = completions.length > 0;
        } catch (err) {
          console.log('Error checking plan completions:', err.message);
          plan.is_completed = false;
        }
      }
    } catch (err) {
      console.error('Error querying instruction plans:', err);
      // Continue with empty plans array
    }

    res.render('student/instruction-plans/index', {
      title: 'My Learning Plans',
      layout: 'student',
      plans,
      currentPage: 'instruction-plans',
      user: res.locals.user,
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching instruction plans:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plans',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
};

// View instruction plan details
exports.getInstructionPlanDetails = async (req, res) => {
  try {
    const planId = req.params.id;
    const studentId = req.session.userId || req.session.user.id;
    console.log('Loading instruction plan details for plan ID:', planId, 'and student ID:', studentId);

    // Get student's classes
    const [studentClasses] = await db.query(`
      SELECT c.id
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);

    // Handle case where studentClasses might be empty
    const classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];
    console.log('Student class IDs:', classIds);

    // Get student's subjects
    const [studentSubjects] = await db.query(`
      SELECT ss.subject_id
      FROM student_subjects ss
      WHERE ss.student_id = ?
    `, [studentId]);

    // Handle case where studentSubjects might be empty
    const subjectIds = studentSubjects && studentSubjects.length > 0 ? studentSubjects.map(s => s.subject_id) : [];
    console.log('Student subject IDs:', subjectIds);

    // Simplified query to get the plan details
    let query = `
      SELECT p.*, s.name as subject_name,
            u.full_name as teacher_name
      FROM instruction_plans p
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN users u ON p.teacher_id = u.id
      WHERE p.id = ?
      AND p.status = 'published'
    `;

    const queryParams = [planId];

    // Only add the filter conditions if we have class or subject IDs
    if (subjectIds.length > 0 || classIds.length > 0) {
      query += ` AND (`;

      const conditions = [];

      if (subjectIds.length > 0) {
        conditions.push(`p.subject_id IN (?)`);
        queryParams.push(subjectIds);
      }

      if (classIds.length > 0) {
        conditions.push(`p.class_id IN (?)`);
        queryParams.push(classIds);
      }

      query += conditions.join(' OR ');
      query += `)`;
    }

    console.log('Executing query for instruction plan details');
    const [plans] = await db.query(query, queryParams);
    console.log(`Found ${plans.length} matching instruction plans`);

    if (plans.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Instruction plan not found or not available for your class',
        error: { status: 404, stack: 'Instruction plan not found' },
        layout: 'student',
        user: res.locals.user,
        notificationCount: 0
      });
    }

    // Get resources
    let resources = [];
    try {
      console.log('Fetching resources for plan ID:', planId);
      [resources] = await db.query(`
        SELECT * FROM instruction_plan_resources
        WHERE plan_id = ?
        ORDER BY created_at
      `, [planId]);
      console.log(`Found ${resources.length} resources`);
    } catch (err) {
      console.log('Error fetching instruction plan resources:', err.message);
      // Continue without resources data
    }

    // Track that the student has viewed this plan
    let isCompleted = false;

    try {
      console.log('Tracking plan view for student');
      await db.query(`
        INSERT INTO instruction_plan_views (plan_id, student_id, viewed_at)
        VALUES (?, ?, NOW())
        ON DUPLICATE KEY UPDATE viewed_at = NOW()
      `, [planId, studentId]);
    } catch (err) {
      console.log('Error tracking plan view:', err.message);
      // Continue without tracking views
    }

    // Check if plan is completed
    try {
      console.log('Checking if plan is completed');
      const [completions] = await db.query(`
        SELECT * FROM instruction_plan_completions
        WHERE plan_id = ? AND student_id = ?
      `, [planId, studentId]);

      isCompleted = completions.length > 0;
      console.log('Plan completion status:', isCompleted);
    } catch (err) {
      console.log('Error checking plan completion:', err.message);
      // Continue without completion data
    }

    res.render('student/instruction-plans/view', {
      title: 'Learning Plan Details',
      layout: 'student',
      plan: plans[0],
      resources,
      isCompleted,
      currentPage: 'instruction-plans',
      user: res.locals.user,
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching instruction plan:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading instruction plan',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
};

// Mark a plan as completed
exports.markPlanCompleted = async (req, res) => {
  try {
    const planId = req.params.id;
    const studentId = req.session.userId || req.session.user.id;
    console.log('Marking plan as completed for plan ID:', planId, 'and student ID:', studentId);

    // Simplified query to check if the plan exists and is available to the student
    let query = `
      SELECT p.id
      FROM instruction_plans p
      WHERE p.id = ?
      AND p.status = 'published'
    `;

    const queryParams = [planId];

    console.log('Checking if plan exists and is published');
    const [plans] = await db.query(query, queryParams);

    if (plans.length === 0) {
      console.log('Plan not found or not published');
      return res.status(404).json({
        success: false,
        message: 'Instruction plan not found or not available'
      });
    }

    console.log('Plan found, marking as completed');

    try {
      // Mark the plan as completed by the student
      await db.query(`
        INSERT INTO instruction_plan_completions (plan_id, student_id, completed_at)
        VALUES (?, ?, NOW())
        ON DUPLICATE KEY UPDATE completed_at = NOW()
      `, [planId, studentId]);

      console.log('Plan marked as completed successfully');

      res.json({
        success: true,
        message: 'Learning plan marked as completed'
      });
    } catch (err) {
      console.error('Error inserting completion record:', err);

      // If the error is due to the table not existing, create it and try again
      if (err.code === 'ER_NO_SUCH_TABLE') {
        console.log('Creating instruction_plan_completions table');

        await db.query(`
          CREATE TABLE instruction_plan_completions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            plan_id INT NOT NULL,
            student_id INT NOT NULL,
            completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (plan_id, student_id),
            FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
          )
        `);

        // Try again after creating the table
        await db.query(`
          INSERT INTO instruction_plan_completions (plan_id, student_id, completed_at)
          VALUES (?, ?, NOW())
        `, [planId, studentId]);

        console.log('Plan marked as completed after creating table');

        res.json({
          success: true,
          message: 'Learning plan marked as completed'
        });
      } else {
        throw err; // Re-throw if it's a different error
      }
    }
  } catch (error) {
    console.error('Error marking plan as completed:', error);
    res.status(500).json({
      success: false,
      message: 'Error marking plan as completed'
    });
  }
};
