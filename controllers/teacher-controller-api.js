const db = require('../config/database');
const classUtils = require('../utils/class-utils');

// Get timetable data for a date range
exports.getTimetableData = async (req, res) => {
  try {
    const { start_date, end_date, teacher_id, class_id } = req.query;

    // Validate input
    if (!start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    // Determine which teacher's data to fetch
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    // If admin is requesting a specific teacher's data
    if (isAdmin && teacher_id) {
      teacherId = teacher_id;
      console.log('Admin requesting timetable for teacher ID:', teacherId);
    }

    // Build the base query with additional information about instruction plans and syllabus topics
    let query = `
      SELECT
        l.*,
        u.full_name as teacher_name,
        (SELECT COUNT(*) FROM lecture_instruction_plan WHERE lecture_id = l.id) as has_instruction_plan,
        (SELECT COUNT(*) FROM lecture_syllabus_topic WHERE lecture_id = l.id) as has_syllabus_topic
      FROM teacher_lectures l
      JOIN users u ON l.teacher_id = u.id
      WHERE l.date BETWEEN ? AND ?
    `;

    // Parameters for the query
    let params = [start_date, end_date];

    // Add teacher filter if not admin or if admin has selected a specific teacher
    if (!isAdmin || (isAdmin && teacher_id)) {
      query += ` AND l.teacher_id = ?`;
      params.push(teacherId);
    }

    // Add class filter if provided
    if (class_id) {
      // Parse the class_id which could be in format "grade-stream-section"
      const parts = class_id.split('-');

      if (parts.length >= 1) {
        // Filter by grade
        query += ` AND l.grade = ?`;
        params.push(parts[0]);

        if (parts.length >= 2) {
          // Filter by stream
          query += ` AND l.streamCode = ?`;
          params.push(parts[1]);

          if (parts.length >= 3) {
            // Filter by section
            query += ` AND l.sectionLetter = ?`;
            params.push(parts[2]);
          }
        }
      }
    }

    // Add order by clause
    query += ` ORDER BY l.date ASC, l.start_time ASC`;

    console.log('Executing query:', query);
    console.log('With parameters:', params);

    // Execute the query
    const [lectures] = await db.query(query, params);

    // Get all teachers for admin view
    let teachers = [];
    if (isAdmin) {
      [teachers] = await db.query(
        `SELECT id, full_name, subjects FROM users WHERE role = 'teacher' AND is_active = 1`
      );
    }

    // Get all classes and sections for filtering
    const [classes] = await db.query(`
      SELECT DISTINCT grade, streamCode, sectionLetter
      FROM teacher_lectures
      ORDER BY grade, streamCode, sectionLetter
    `);

    // Get instruction plans for each lecture
    let instructionPlans = [];
    let syllabusTopics = [];

    // Check if we have any lectures before trying to fetch related data
    if (lectures && lectures.length > 0) {
      const lectureIds = lectures.map(lecture => lecture.id);

      try {
        // Get instruction plans if we have lecture IDs
        if (lectureIds.length > 0) {
          [instructionPlans] = await db.query(`
            SELECT lip.lecture_id, ip.id as plan_id, ip.title, s.name as subject_name
            FROM lecture_instruction_plan lip
            JOIN instruction_plans ip ON lip.instruction_plan_id = ip.id
            LEFT JOIN subjects s ON ip.subject_id = s.id
            WHERE lip.lecture_id IN (?)
          `, [lectureIds]);
        }

        // Get syllabus topics if we have lecture IDs
        if (lectureIds.length > 0) {
          [syllabusTopics] = await db.query(`
            SELECT lst.lecture_id, ts.id as topic_id, ts.topic, ts.subject_name
            FROM lecture_syllabus_topic lst
            JOIN teacher_syllabus ts ON lst.topic_id = ts.id
            WHERE lst.lecture_id IN (?)
          `, [lectureIds]);
        }

        // Add instruction plans and syllabus topics to each lecture
        lectures.forEach(lecture => {
          lecture.instruction_plans = instructionPlans.filter(plan => plan.lecture_id === lecture.id) || [];
          lecture.syllabus_topics = syllabusTopics.filter(topic => topic.lecture_id === lecture.id) || [];
        });
      } catch (error) {
        console.error('Error fetching related data for lectures:', error);
        // Continue execution even if this part fails
      }
    } else {
      console.log('No lectures found for the specified criteria');
    }

    res.json({
      success: true,
      lectures,
      teachers: isAdmin ? teachers : [],
      classes,
      currentTeacherId: teacherId
    });
  } catch (error) {
    console.error('Error fetching timetable data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching timetable data'
    });
  }
};

// Get teacher-specific timetable data with optimized lecture distribution
exports.getTeacherTimetableData = async (req, res) => {
  try {
    const { start_date, end_date, teacher_id } = req.query;

    // Validate input
    if (!start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    // Determine which teacher's data to fetch
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    // If admin is requesting a specific teacher's data
    if (isAdmin && teacher_id) {
      teacherId = teacher_id;
      console.log('Admin requesting teacher timetable for teacher ID:', teacherId);
    }

    // Get teacher information
    const [teacherInfo] = await db.query(
      `SELECT id, name as full_name, subjects FROM users WHERE id = ?`,
      [teacherId]
    );

    if (teacherInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherInfo[0];

    // Parse subjects if it's a string
    let teacherSubjects = teacher.subjects;
    if (typeof teacherSubjects === 'string') {
      try {
        teacherSubjects = JSON.parse(teacherSubjects);
      } catch (e) {
        teacherSubjects = teacherSubjects.split(',').map(s => s.trim());
      }
    }

    // If no subjects defined, use a default
    if (!teacherSubjects || !Array.isArray(teacherSubjects) || teacherSubjects.length === 0) {
      teacherSubjects = ['Computer Science'];
    }

    console.log('Teacher subjects:', teacherSubjects);

    // Generate optimized timetable data for this teacher
    // This would normally come from the database, but we'll generate it here
    // based on the requirements

    // Start with an empty lectures array
    let lectures = [];

    // Get the date range
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);

    // For each subject the teacher teaches
    for (const subject of teacherSubjects) {
      // Determine which classes need this subject
      let classesForSubject = [];

      if (subject === 'Physics') {
        // Physics is taught to 6 non-medical classes and 2 medical sections for each of 11 and 12
        classesForSubject = [
          { grade: '11', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '11', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '12', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '12', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 }
        ];
      } else if (subject === 'Chemistry') {
        // Chemistry is taught to 6 non-medical classes and 2 medical sections for each of 11 and 12
        classesForSubject = [
          { grade: '11', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '11', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '12', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '12', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 }
        ];
      } else if (subject === 'Mathematics') {
        // Mathematics is taught to 6 non-medical classes for each of 11 and 12
        classesForSubject = [
          { grade: '11', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 8, practicalLectures: 0 },
          { grade: '12', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 8, practicalLectures: 0 }
        ];
      } else if (subject === 'Biology') {
        // Biology is taught to 2 medical sections for each of 11 and 12
        classesForSubject = [
          { grade: '11', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 },
          { grade: '12', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 3 }
        ];
      } else if (subject === 'Computer Science') {
        // Computer Science is taught to all streams
        classesForSubject = [
          { grade: '11', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 4, practicalLectures: 2 },
          { grade: '11', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 4, practicalLectures: 2 },
          { grade: '11', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 4, practicalLectures: 2 },
          { grade: '12', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 4, practicalLectures: 2 },
          { grade: '12', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 4, practicalLectures: 2 },
          { grade: '12', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 4, practicalLectures: 2 }
        ];
      } else if (subject === 'English' || subject === 'Punjabi') {
        // Languages are taught to all streams
        classesForSubject = [
          { grade: '11', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '11', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '11', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '12', stream: 'Non-Medical', sections: ['A', 'B', 'C', 'D', 'E', 'F'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '12', stream: 'Medical', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '12', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 }
        ];
      } else if (['Business Studies', 'Accountancy', 'Economics'].includes(subject)) {
        // Commerce subjects
        classesForSubject = [
          { grade: '11', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 },
          { grade: '12', stream: 'Commerce', sections: ['A', 'B'], theoryLectures: 6, practicalLectures: 0 }
        ];
      }

      // Define subject lecture counts according to the provided table
      const subjectLectureCounts = {
        'English': { theory: 6, practical: 0, max_weekly: 30 },
        'Punjabi': { theory: 6, practical: 0, max_weekly: 30 },
        'Computer Science': { theory: 2, practical: 2, max_weekly: 27 },
        'Physics': { theory: 6, practical: 3, max_weekly: 14 },
        'Chemistry': { theory: 6, practical: 3, max_weekly: 14 },
        'Biology': { theory: 6, practical: 3, max_weekly: 9 },
        'Mathematics': { theory: 9, practical: 0, max_weekly: 27 },
        'Accountancy': { theory: 9, practical: 0, max_weekly: 6 },
        'Business Studies': { theory: 9, practical: 0, max_weekly: 6 },
        'Economics': { theory: 9, practical: 0, max_weekly: 6 },
        'MOP': { theory: 6, practical: 3, max_weekly: 3 },
        'EBUSINESS': { theory: 6, practical: 3, max_weekly: 3 }
      };

      // Get lecture counts for this subject
      const theoryCount = subjectLectureCounts[subject]?.theory || 6;
      const practicalCount = subjectLectureCounts[subject]?.practical || 0;

      // Limit to max 2 classes per teacher (one for XI and one for XII)
      // For each subject, select at most one class from grade 11 and one from grade 12
      const selectedClasses = [];
      const grade11Classes = classesForSubject.filter(c => c.grade === '11');
      const grade12Classes = classesForSubject.filter(c => c.grade === '12');

      if (grade11Classes.length > 0) {
        // Select one class from grade 11
        selectedClasses.push({...grade11Classes[0], theoryLectures: theoryCount, practicalLectures: practicalCount});
      }

      if (grade12Classes.length > 0) {
        // Select one class from grade 12
        selectedClasses.push({...grade12Classes[0], theoryLectures: theoryCount, practicalLectures: practicalCount});
      }

      // Track teacher's lecture count to ensure we don't exceed 48 per week
      let teacherLectureCount = 0;

      // For each selected class, generate lectures
      for (const classInfo of selectedClasses) {
        // Generate theory lectures
        for (let i = 0; i < classInfo.theoryLectures; i++) {
          // For each section in this class
          for (const section of classInfo.sections) {
            // Get the maximum weekly lectures for this subject
            const maxWeeklyLectures = subjectLectureCounts[subject]?.max_weekly || 48;

            // Check if adding this lecture would exceed the subject-specific maximum
            if (teacherLectureCount >= maxWeeklyLectures) {
              console.log(`Teacher ${teacher.full_name} has reached the maximum of ${maxWeeklyLectures} lectures per week for ${subject}`);
              continue;
            }

            // Create a unique lecture ID
            const lectureId = parseInt(`${classInfo.grade}${classInfo.stream.charCodeAt(0)}${section.charCodeAt(0)}${i}`, 10);

            // Calculate the date for this lecture (distribute across the week)
            const dayOffset = i % 6; // 6 days in a week (Monday to Saturday)
            const lectureDate = new Date(startDate);
            lectureDate.setDate(startDate.getDate() + dayOffset);

            // Skip if the date is outside our range
            if (lectureDate > endDate) continue;

            // Check how many lectures this teacher already has on this day
            const dateString = lectureDate.toISOString().split('T')[0];
            const lecturesOnThisDay = lectures.filter(l =>
              l.teacher_id === teacherId && l.date === dateString
            ).length;

            // Skip if teacher already has 8 lectures on this day (maximum allowed)
            if (lecturesOnThisDay >= 8) {
              console.log(`Teacher ${teacher.full_name} already has 8 lectures on ${dateString}`);
              continue;
            }

            // Calculate time slot (distribute across the day)
            const periodIndex = (i + section.charCodeAt(0)) % 8; // 8 periods per day (max)

            // Create the lecture object
            lectures.push({
              id: lectureId,
              teacher_id: teacherId,
              teacher_name: teacher.full_name,
              class_name: `Class ${classInfo.grade}`,
              section: `${classInfo.stream}-${section}`,
              section_display: `${classInfo.stream} ${section}`,
              subject_name: subject,
              topic: getTopicForSubject(subject, i),
              date: lectureDate.toISOString().split('T')[0],
              start_time: getTimeForPeriod(periodIndex, 'start'),
              end_time: getTimeForPeriod(periodIndex, 'end'),
              location: getLocationForSubject(subject),
              status: getRandomStatus(),
              description: `This lecture will cover ${getTopicForSubject(subject, i)} for ${subject}.`,
              slot_index: periodIndex,
              stream: classInfo.stream,
              streamCode: getStreamCode(classInfo.stream),
              grade: classInfo.grade,
              sectionLetter: section,
              lecture_type: 'Theory'
            });

            // Increment teacher's lecture count
            teacherLectureCount++;
          }
        }

        // Generate practical lectures for subjects that have them
        if (classInfo.practicalLectures > 0) {
          for (let i = 0; i < classInfo.practicalLectures; i++) {
            // For each section in this class
            for (const section of classInfo.sections) {
              // Get the maximum weekly lectures for this subject
              const maxWeeklyLectures = subjectLectureCounts[subject]?.max_weekly || 48;

              // Check if adding this lecture would exceed the subject-specific maximum
              if (teacherLectureCount >= maxWeeklyLectures) {
                console.log(`Teacher ${teacher.full_name} has reached the maximum of ${maxWeeklyLectures} lectures per week for ${subject}`);
                continue;
              }

              // Create a unique lecture ID
              const lectureId = parseInt(`9${classInfo.grade}${classInfo.stream.charCodeAt(0)}${section.charCodeAt(0)}${i}`, 10);

              // Calculate the date for this lecture (distribute across the week)
              const dayOffset = (i + 3) % 6; // 6 days in a week (Monday to Saturday)
              const lectureDate = new Date(startDate);
              lectureDate.setDate(startDate.getDate() + dayOffset);

              // Skip if the date is outside our range
              if (lectureDate > endDate) continue;

              // Check how many lectures this teacher already has on this day
              const dateString = lectureDate.toISOString().split('T')[0];
              const lecturesOnThisDay = lectures.filter(l =>
                l.teacher_id === teacherId && l.date === dateString
              ).length;

              // Skip if teacher already has 8 lectures on this day (maximum allowed)
              if (lecturesOnThisDay >= 8) {
                console.log(`Teacher ${teacher.full_name} already has 8 lectures on ${dateString}`);
                continue;
              }

              // Calculate time slot (distribute across the day)
              const periodIndex = (i + section.charCodeAt(0) + 4) % 8; // 8 periods per day (max)

              // Create the lecture object
              lectures.push({
                id: lectureId,
                teacher_id: teacherId,
                teacher_name: teacher.full_name,
                class_name: `Class ${classInfo.grade}`,
                section: `${classInfo.stream}-${section}`,
                section_display: `${classInfo.stream} ${section}`,
                subject_name: subject,
                topic: `Practical: ${getTopicForSubject(subject, i)}`,
                date: lectureDate.toISOString().split('T')[0],
                start_time: getTimeForPeriod(periodIndex, 'start'),
                end_time: getTimeForPeriod(periodIndex, 'end'),
                location: getLabForSubject(subject),
                status: getRandomStatus(),
                description: `This practical session will cover ${getTopicForSubject(subject, i)} for ${subject}.`,
                slot_index: periodIndex,
                stream: classInfo.stream,
                streamCode: getStreamCode(classInfo.stream),
                grade: classInfo.grade,
                sectionLetter: section,
                lecture_type: 'Practical'
              });

              // Increment teacher's lecture count
              teacherLectureCount++;
            }
          }
        }
      }
    }

    // Sort lectures by date and time
    lectures.sort((a, b) => {
      if (a.date !== b.date) {
        return a.date.localeCompare(b.date);
      }
      return a.start_time.localeCompare(b.start_time);
    });

    // Get all teachers for admin view
    let teachers = [];
    if (isAdmin) {
      [teachers] = await db.query(
        `SELECT id, name as full_name, subjects FROM users WHERE role = 'teacher' AND is_active = 1`
      );
    }

    res.json({
      success: true,
      lectures,
      teachers: isAdmin ? teachers : [],
      currentTeacherId: teacherId
    });
  } catch (error) {
    console.error('Error fetching teacher timetable data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching teacher timetable data'
    });
  }
};

// Helper function to get a topic for a subject
function getTopicForSubject(subject, index) {
  const subjectTopics = {
    'Physics': [
      'Kinematics', 'Newton\'s Laws', 'Work and Energy', 'Rotational Motion',
      'Gravitation', 'Properties of Matter', 'Thermodynamics', 'Waves'
    ],
    'Chemistry': [
      'Atomic Structure', 'Chemical Bonding', 'States of Matter', 'Thermochemistry',
      'Solutions', 'Equilibrium', 'Redox Reactions', 'Organic Chemistry Basics'
    ],
    'Mathematics': [
      'Functions', 'Limits', 'Derivatives', 'Integrals', 'Vectors',
      'Matrices', 'Probability', 'Statistics', 'Complex Numbers'
    ],
    'Biology': [
      'Cell Structure', 'Photosynthesis', 'Human Physiology', 'Genetics',
      'Evolution', 'Ecology', 'Molecular Biology', 'Biotechnology'
    ],
    'Computer Science': [
      'Data Types', 'Control Structures', 'Functions', 'Arrays',
      'Object-Oriented Programming', 'Data Structures', 'Algorithms', 'Web Development'
    ],
    'English': [
      'Reading Comprehension', 'Writing Skills', 'Grammar', 'Literature Analysis',
      'Poetry', 'Drama', 'Prose', 'Communication Skills'
    ],
    'Punjabi': [
      'Grammar', 'Literature', 'Poetry', 'Prose',
      'Writing Skills', 'Reading Comprehension', 'Cultural Studies', 'Communication'
    ],
    'Business Studies': [
      'Business Environment', 'Planning', 'Organizing', 'Staffing',
      'Directing', 'Controlling', 'Financial Management', 'Marketing'
    ],
    'Accountancy': [
      'Basic Accounting', 'Journal Entries', 'Ledger Accounts', 'Trial Balance',
      'Financial Statements', 'Depreciation', 'Company Accounts', 'Analysis of Financial Statements'
    ],
    'Economics': [
      'Microeconomics', 'Macroeconomics', 'National Income', 'Money and Banking',
      'Government Budget', 'Balance of Payments', 'International Trade', 'Development Economics'
    ],
  };

  const topics = subjectTopics[subject] || ['General Topic'];
  return topics[index % topics.length];
}

// Helper function to get a location for a subject
function getLocationForSubject(subject) {
  if (['Physics', 'Chemistry', 'Biology', 'Computer Science'].includes(subject)) {
    return `${subject} Lab`;
  }
  return 'Classroom';
}

// Helper function to get a lab for a subject
function getLabForSubject(subject) {
  return `${subject} Lab`;
}

// Helper function to get a random status
function getRandomStatus() {
  const statuses = ['Pending', 'Delivered', 'Rescheduled'];
  const randomIndex = Math.floor(Math.random() * 10);
  if (randomIndex < 7) return 'Pending';
  if (randomIndex < 9) return 'Delivered';
  return 'Rescheduled';
}

// Helper function to get time for a period
function getTimeForPeriod(periodIndex, type) {
  // Maximum 8 periods per day as per requirements
  const startTimes = [
    '08:00', '08:50', '09:40', '10:30',
    '11:20', '12:10', '13:00', '13:50'
  ];

  const endTimes = [
    '08:40', '09:30', '10:20', '11:10',
    '12:00', '12:50', '13:40', '14:30'
  ];

  // Ensure we don't exceed 8 periods
  const safeIndex = periodIndex % 8;
  return type === 'start' ? startTimes[safeIndex] : endTimes[safeIndex];
}

// Helper function to get stream code
function getStreamCode(streamName) {
  const streamCodes = {
    'Non-Medical': 'NM',
    'Medical': 'M',
    'Commerce': 'C'
  };

  return streamCodes[streamName] || streamName;
}

// Update lecture status
exports.updateLectureStatus = async (req, res) => {
  try {
    const lectureId = req.params.id;
    const { status, instruction_plan_id, topic_id } = req.body;

    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    // Validate status - make case-insensitive
    const validStatuses = ['pending', 'delivered', 'cancelled'];
    const normalizedStatus = status ? status.toLowerCase() : '';

    if (!normalizedStatus || !validStatuses.includes(normalizedStatus)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Use normalized status for database operations
    const statusForDb = normalizedStatus;

    // Begin transaction
    await db.query('START TRANSACTION');

    // Update lecture status
    let result;
    if (isAdmin) {
      // Admins can update any lecture
      [result] = await db.query(
        `UPDATE teacher_lectures
         SET status = ?
         WHERE id = ?`,
        [statusForDb, lectureId]
      );
    } else {
      // Teachers can only update their own lectures
      [result] = await db.query(
        `UPDATE teacher_lectures
         SET status = ?
         WHERE id = ? AND teacher_id = ?`,
        [statusForDb, lectureId, teacherId]
      );
    }

    if (result.affectedRows === 0) {
      await db.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        message: 'Lecture not found or you do not have permission to update it'
      });
    }

    // If status is 'delivered', update related data
    if (statusForDb === 'delivered') {
      // Get lecture details
      const [lectures] = await db.query(
        `SELECT * FROM teacher_lectures WHERE id = ?`,
        [lectureId]
      );

      if (lectures.length > 0) {
        const lecture = lectures[0];

        // If instruction plan ID is provided, link it to the lecture
        if (instruction_plan_id) {
          // Check if the link already exists
          const [existingLinks] = await db.query(
            `SELECT * FROM lecture_instruction_plan
             WHERE lecture_id = ? AND instruction_plan_id = ?`,
            [lectureId, instruction_plan_id]
          );

          if (existingLinks.length === 0) {
            // Create the link
            await db.query(
              `INSERT INTO lecture_instruction_plan (lecture_id, instruction_plan_id)
               VALUES (?, ?)`,
              [lectureId, instruction_plan_id]
            );
          }
        }

        // If topic ID is provided, mark it as completed
        if (topic_id) {
          // Check if the topic exists and belongs to this teacher
          const [topics] = await db.query(
            `SELECT * FROM teacher_syllabus
             WHERE id = ? AND teacher_id = ?`,
            [topic_id, teacherId]
          );

          if (topics.length > 0) {
            // Update topic status to completed
            await db.query(
              `UPDATE teacher_syllabus
               SET status = 'completed', completion_date = CURDATE()
               WHERE id = ?`,
              [topic_id]
            );

            // Link the lecture to the topic
            await db.query(
              `INSERT INTO lecture_syllabus_topic (lecture_id, topic_id, completion_date)
               VALUES (?, ?, CURDATE())
               ON DUPLICATE KEY UPDATE completion_date = CURDATE()`,
              [lectureId, topic_id]
            );

            // Update syllabus progress
            const [syllabusProgress] = await db.query(
              `SELECT * FROM syllabus_progress
               WHERE teacher_id = ? AND subject_name = ?`,
              [teacherId, lecture.subject_name]
            );

            if (syllabusProgress.length > 0) {
              // Increment completed topics
              await db.query(
                `UPDATE syllabus_progress
                 SET completed_topics = completed_topics + 1
                 WHERE teacher_id = ? AND subject_name = ?`,
                [teacherId, lecture.subject_name]
              );
            } else {
              // Create new progress entry
              await db.query(
                `INSERT INTO syllabus_progress (teacher_id, subject_name, total_topics, completed_topics)
                 VALUES (?, ?, 1, 1)`,
                [teacherId, lecture.subject_name]
              );
            }
          }
        }
      }
    }

    // Commit transaction
    await db.query('COMMIT');

    res.json({
      success: true,
      message: `Lecture ${statusForDb === 'delivered' ? 'marked as delivered' : statusForDb === 'cancelled' ? 'cancelled' : 'updated'} successfully`
    });
  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');
    console.error('Error updating lecture status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating lecture status'
    });
  }
};

// Get lecture by ID
exports.getLectureById = async (req, res) => {
  try {
    const lectureId = req.params.id;

    // Get lecture details
    const [lectures] = await db.query(
      `SELECT * FROM teacher_lectures WHERE id = ?`,
      [lectureId]
    );

    if (lectures.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Lecture not found'
      });
    }

    const lecture = lectures[0];

    // Get linked instruction plans
    const [instructionPlans] = await db.query(`
      SELECT ip.*, s.name as subject_name
      FROM lecture_instruction_plan lip
      JOIN instruction_plans ip ON lip.instruction_plan_id = ip.id
      LEFT JOIN subjects s ON ip.subject_id = s.id
      WHERE lip.lecture_id = ?
    `, [lectureId]);

    // Get linked syllabus topics
    const [syllabusTopics] = await db.query(`
      SELECT ts.*, lst.completion_date
      FROM lecture_syllabus_topic lst
      JOIN teacher_syllabus ts ON lst.topic_id = ts.id
      WHERE lst.lecture_id = ?
    `, [lectureId]);

    // Get available instruction plans for this subject
    const [availablePlans] = await db.query(`
      SELECT ip.*, s.name as subject_name
      FROM instruction_plans ip
      LEFT JOIN subjects s ON ip.subject_id = s.id
      WHERE s.name = ? AND ip.status = 'published'
    `, [lecture.subject_name]);

    // Get available syllabus topics for this subject
    const [availableTopics] = await db.query(`
      SELECT * FROM teacher_syllabus
      WHERE subject_name = ? AND teacher_id = ? AND status = 'pending'
    `, [lecture.subject_name, lecture.teacher_id]);

    res.json({
      success: true,
      lecture,
      instructionPlans,
      syllabusTopics,
      availablePlans,
      availableTopics
    });
  } catch (error) {
    console.error('Error fetching lecture:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching lecture'
    });
  }
};

// Get practical by ID
exports.getPracticalById = async (req, res) => {
  try {
    const practicalId = req.params.id;

    // Get practical details
    const [practicals] = await db.query(
      `SELECT * FROM teacher_practicals WHERE id = ?`,
      [practicalId]
    );

    if (practicals.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Practical not found'
      });
    }

    const practical = practicals[0];

    res.json({
      success: true,
      practical
    });
  } catch (error) {
    console.error('Error fetching practical:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching practical'
    });
  }
};

// Update practical status
exports.updatePracticalStatus = async (req, res) => {
  try {
    const practicalId = req.params.id;
    const { status } = req.body;

    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    // Validate status
    if (!status || !['pending', 'conducted', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    // Update practical status
    let result;
    if (isAdmin) {
      // Admins can update any practical
      [result] = await db.query(
        `UPDATE teacher_practicals
         SET status = ?
         WHERE id = ?`,
        [status, practicalId]
      );
    } else {
      // Teachers can only update their own practicals
      [result] = await db.query(
        `UPDATE teacher_practicals
         SET status = ?
         WHERE id = ? AND teacher_id = ?`,
        [status, practicalId, teacherId]
      );
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Practical not found or you do not have permission to update it'
      });
    }

    res.json({
      success: true,
      message: `Practical ${status === 'conducted' ? 'marked as conducted' : status === 'cancelled' ? 'cancelled' : 'updated'} successfully`
    });
  } catch (error) {
    console.error('Error updating practical status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating practical status'
    });
  }
};

// Get class-wise subject lecture data
exports.getClassSubjectData = async (req, res) => {
  try {
    // Get all teacher-class-subject relationships from the database
    // In a real implementation, this would query the actual database tables
    // For now, we'll generate sample data based on the existing structure

    // Get all teachers
    const [teachers] = await db.query(
      `SELECT id, full_name, subjects FROM users WHERE role = 'teacher' AND is_active = 1`
    );

    // Get all classes and sections
    const [classes] = await db.query(`
      SELECT DISTINCT grade, streamCode, sectionLetter
      FROM teacher_lectures
      ORDER BY grade, streamCode, sectionLetter
    `);

    // Get lecture counts by class, subject, and teacher
    // Using a simpler query to avoid potential SQL errors
    // Modified to calculate weekly lecture counts
    const [lectureCounts] = await db.query(`
      SELECT
        CONCAT(l.grade, ' ',
          CASE
            WHEN l.streamCode = 'NM' THEN 'Non-Medical'
            WHEN l.streamCode = 'M' THEN 'Medical'
            WHEN l.streamCode = 'C' THEN 'Commerce'
            ELSE l.streamCode
          END,
          ' ', l.sectionLetter) AS class_name,
        l.subject_name,
        u.full_name AS teacher_name,
        COUNT(CASE WHEN l.topic NOT LIKE 'Practical:%' THEN 1 ELSE NULL END) /
          (DATEDIFF(MAX(l.date), MIN(l.date)) / 7 + 1) AS theory_lectures,
        COUNT(CASE WHEN l.topic LIKE 'Practical:%' THEN 1 ELSE NULL END) /
          (DATEDIFF(MAX(l.date), MIN(l.date)) / 7 + 1) AS practical_sessions
      FROM teacher_lectures l
      JOIN users u ON l.teacher_id = u.id
      GROUP BY l.grade, l.streamCode, l.sectionLetter, l.subject_name, u.full_name
      ORDER BY l.grade, l.streamCode, l.sectionLetter, l.subject_name
    `);

    // If no data is found, generate sample data
    if (lectureCounts.length === 0) {
      const sampleData = [];

      // Define subject lecture counts according to the provided table
      const subjectLectureCounts = {
        'English': { theory: 6, practical: 0, xi_classes: 10, xii_classes: 10, teachers: 4, total_per_class: 6, total_all_classes: 120, max_weekly: 30 },
        'Punjabi': { theory: 6, practical: 0, xi_classes: 10, xii_classes: 10, teachers: 4, total_per_class: 6, total_all_classes: 120, max_weekly: 30 },
        'Computer Science': { theory: 2, practical: 2, xi_classes: 10, xii_classes: 10, teachers: 3, total_per_class: 4, total_all_classes: 80, max_weekly: 27 },
        'Physics': { theory: 6, practical: 3, xi_classes: 6, xii_classes: 6, teachers: 8, total_per_class: 9, total_all_classes: 108, max_weekly: 14 },
        'Chemistry': { theory: 6, practical: 3, xi_classes: 6, xii_classes: 6, teachers: 8, total_per_class: 9, total_all_classes: 108, max_weekly: 14 },
        'Biology': { theory: 6, practical: 3, xi_classes: 2, xii_classes: 2, teachers: 4, total_per_class: 9, total_all_classes: 36, max_weekly: 9 },
        'Mathematics': { theory: 9, practical: 0, xi_classes: 6, xii_classes: 6, teachers: 4, total_per_class: 9, total_all_classes: 108, max_weekly: 27 },
        'Accountancy': { theory: 9, practical: 0, xi_classes: 2, xii_classes: 2, teachers: 6, total_per_class: 9, total_all_classes: 36, max_weekly: 6 },
        'Business Studies': { theory: 9, practical: 0, xi_classes: 2, xii_classes: 2, teachers: 6, total_per_class: 9, total_all_classes: 36, max_weekly: 6 },
        'Economics': { theory: 9, practical: 0, xi_classes: 2, xii_classes: 2, teachers: 6, total_per_class: 9, total_all_classes: 36, max_weekly: 6 },
        'MOP': { theory: 6, practical: 3, xi_classes: 2, xii_classes: 0, teachers: 6, total_per_class: 9, total_all_classes: 18, max_weekly: 3 },
        'EBUSINESS': { theory: 6, practical: 3, xi_classes: 0, xii_classes: 2, teachers: 6, total_per_class: 9, total_all_classes: 18, max_weekly: 3 }
      };

      // Track teacher lecture counts to ensure we don't exceed 48 per week
      const teacherLectureCounts = {};
      teachers.forEach(teacher => {
        teacherLectureCounts[teacher.full_name] = 0;
      });

      // Assign teachers to subjects
      const subjectTeachers = {};
      Object.keys(subjectLectureCounts).forEach(subject => {
        const teacherCount = subjectLectureCounts[subject].teachers;
        subjectTeachers[subject] = [];

        // Get available teachers for this subject
        const availableTeachers = [...teachers].sort(() => Math.random() - 0.5);

        // Assign teachers to this subject
        for (let i = 0; i < Math.min(teacherCount, availableTeachers.length); i++) {
          subjectTeachers[subject].push(availableTeachers[i].full_name);
        }

        // If we don't have enough teachers, repeat some
        while (subjectTeachers[subject].length < teacherCount && teachers.length > 0) {
          subjectTeachers[subject].push(teachers[Math.floor(Math.random() * teachers.length)].full_name);
        }
      });

      // Create class names for XI and XII
      const classNames = {
        'XI': [],
        'XII': []
      };

      // Generate class names based on streams
      ['Non-Medical', 'Medical', 'Commerce'].forEach(stream => {
        const streamCode = stream === 'Non-Medical' ? 'NM' : (stream === 'Medical' ? 'M' : 'C');

        // Determine how many sections to create for each stream
        let sectionCount;
        if (stream === 'Non-Medical') {
          sectionCount = 6; // 6 sections for Non-Medical
        } else if (stream === 'Medical') {
          sectionCount = 2; // 2 sections for Medical
        } else { // Commerce
          sectionCount = 2; // 2 sections for Commerce
        }

        // Create class names
        for (let i = 0; i < sectionCount; i++) {
          const section = String.fromCharCode(65 + i); // A, B, C, etc.
          classNames['XI'].push(`11 ${stream} ${section}`);
          classNames['XII'].push(`12 ${stream} ${section}`);
        }
      });

      // For each subject
      Object.keys(subjectLectureCounts).forEach(subject => {
        const { theory, practical, xi_classes, xii_classes } = subjectLectureCounts[subject];
        const teachers = subjectTeachers[subject];

        // Determine which classes get this subject
        let xi_class_list = [];
        let xii_class_list = [];

        // Assign classes based on subject and stream
        if (subject === 'Physics' || subject === 'Chemistry') {
          // Physics and Chemistry are for Non-Medical and Medical
          xi_class_list = classNames['XI'].filter(c => c.includes('Non-Medical') || c.includes('Medical'));
          xii_class_list = classNames['XII'].filter(c => c.includes('Non-Medical') || c.includes('Medical'));
        } else if (subject === 'Biology') {
          // Biology is only for Medical
          xi_class_list = classNames['XI'].filter(c => c.includes('Medical'));
          xii_class_list = classNames['XII'].filter(c => c.includes('Medical'));
        } else if (subject === 'Mathematics') {
          // Mathematics is for Non-Medical
          xi_class_list = classNames['XI'].filter(c => c.includes('Non-Medical'));
          xii_class_list = classNames['XII'].filter(c => c.includes('Non-Medical'));
        } else if (subject === 'Accountancy' || subject === 'Business Studies' || subject === 'Economics' || subject === 'MOP') {
          // Commerce subjects
          xi_class_list = classNames['XI'].filter(c => c.includes('Commerce'));
          xii_class_list = classNames['XII'].filter(c => c.includes('Commerce'));
        } else {
          // English, Punjabi, Computer Science are for all classes
          xi_class_list = classNames['XI'];
          xii_class_list = classNames['XII'];
        }

        // Limit to the number of classes specified in the table
        xi_class_list = xi_class_list.slice(0, xi_classes);
        xii_class_list = xii_class_list.slice(0, xii_classes);

        // Combine both grade lists
        const all_classes = [...xi_class_list, ...xii_class_list];

        // Distribute classes among teachers
        for (let i = 0; i < all_classes.length; i++) {
          const className = all_classes[i];
          // Select teacher with fewest lectures
          let selectedTeacher = teachers[0];
          let minLectures = Number.MAX_SAFE_INTEGER;

          for (const teacher of teachers) {
            const currentLectures = teacherLectureCounts[teacher] || 0;
            // Use the subject-specific max weekly lectures instead of the fixed 48
            const maxWeeklyLectures = subjectLectureCounts[subject].max_weekly;
            if (currentLectures < minLectures && currentLectures + theory + practical <= maxWeeklyLectures) {
              minLectures = currentLectures;
              selectedTeacher = teacher;
            }
          }

          // If all teachers are at max capacity, just pick one randomly
          if (minLectures === Number.MAX_SAFE_INTEGER) {
            selectedTeacher = teachers[Math.floor(Math.random() * teachers.length)];
          }

          // Add to teacher's lecture count
          teacherLectureCounts[selectedTeacher] = (teacherLectureCounts[selectedTeacher] || 0) + theory + practical;

          // Add the data
          sampleData.push({
            class_name: className,
            subject_name: subject,
            teacher_name: selectedTeacher,
            theory_lectures: theory,
            practical_sessions: practical
          });
        }
      });

      res.json({
        success: true,
        data: sampleData
      });
    } else {
      // Return the actual data from the database
      res.json({
        success: true,
        data: lectureCounts
      });
    }
  } catch (error) {
    console.error('Error fetching class-subject data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching class-subject data'
    });
  }
};

// Get class-teacher-subject relationships for mind map
exports.getClassTeacherMap = async (req, res) => {
  try {
    // Get all teacher-class-subject relationships from the database
    // In a real implementation, this would query the actual database tables
    // For now, we'll generate sample data based on the existing structure

    // Get all teachers
    const [teachers] = await db.query(
      `SELECT id, full_name, subjects FROM users WHERE role = 'teacher' AND is_active = 1`
    );

    // Get all classes and sections
    const [classes] = await db.query(`
      SELECT DISTINCT grade, streamCode, sectionLetter
      FROM teacher_lectures
      ORDER BY grade, streamCode, sectionLetter
    `);

    // Create relationships array
    const relationships = [];

    // Process each teacher
    for (const teacher of teachers) {
      // Parse subjects if it's a string
      let teacherSubjects = teacher.subjects;
      if (typeof teacherSubjects === 'string') {
        try {
          teacherSubjects = JSON.parse(teacherSubjects);
        } catch (e) {
          teacherSubjects = teacherSubjects.split(',').map(s => s.trim());
        }
      }

      // If no subjects defined, use a default
      if (!teacherSubjects || !Array.isArray(teacherSubjects) || teacherSubjects.length === 0) {
        teacherSubjects = ['Computer Science'];
      }

      // For each subject the teacher teaches
      for (const subject of teacherSubjects) {
        // Determine which classes need this subject
        let classesForSubject = [];

        if (subject === 'Physics') {
          // Physics is taught to non-medical and medical classes
          classesForSubject = classes.filter(c =>
            (c.streamCode === 'NM' || c.streamCode === 'M')
          );
        } else if (subject === 'Chemistry') {
          // Chemistry is taught to non-medical and medical classes
          classesForSubject = classes.filter(c =>
            (c.streamCode === 'NM' || c.streamCode === 'M')
          );
        } else if (subject === 'Mathematics') {
          // Mathematics is taught to non-medical classes
          classesForSubject = classes.filter(c => c.streamCode === 'NM');
        } else if (subject === 'Biology') {
          // Biology is taught to medical classes
          classesForSubject = classes.filter(c => c.streamCode === 'M');
        } else if (subject === 'Computer Science') {
          // Computer Science is taught to all classes
          classesForSubject = classes;
        } else if (subject === 'English' || subject === 'Punjabi') {
          // Languages are taught to all classes
          classesForSubject = classes;
        } else if (['Business Studies', 'Accountancy', 'Economics'].includes(subject)) {
          // Commerce subjects are taught to commerce classes
          classesForSubject = classes.filter(c => c.streamCode === 'C');
        }

        // Add relationships for each class
        for (const cls of classesForSubject) {
          // Create a unique class ID
          const classId = `${cls.grade}-${cls.streamCode}-${cls.sectionLetter}`;

          // Create a class name in the format "11 Non-Medical A"
          const streamName = getFullStreamName(cls.streamCode);
          const className = `${cls.grade} ${streamName} ${cls.sectionLetter}`;

          // Add the relationship
          relationships.push({
            teacher_id: teacher.id,
            teacher_name: teacher.full_name,
            subject_name: subject,
            class_id: classId,
            class_name: className,
            grade: cls.grade,
            stream: streamName,
            section: cls.sectionLetter
          });
        }
      }
    }

    res.json({
      success: true,
      relationships
    });
  } catch (error) {
    console.error('Error fetching class-teacher map data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching class-teacher map data'
    });
  }
};

// Helper function to get full stream name from code
function getFullStreamName(streamCode) {
  switch (streamCode) {
    case 'NM': return 'Non-Medical';
    case 'M': return 'Medical';
    case 'C': return 'Commerce';
    default: return streamCode;
  }
}