const db = require('../config/database');
const classUtils = require('../utils/class-utils');

// Add a new lecture with class section support
exports.addLecture = async (req, res) => {
  try {
    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;

    if (req.session.userRole === 'admin') {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    const { date, class_section_id, start_time, end_time, subject_name, topic, notes } = req.body;

    // Validate input
    if (!date || !class_section_id || !start_time || !end_time || !subject_name || !topic) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Get class section details
    const classSection = await classUtils.getClassSectionById(class_section_id);
    if (!classSection) {
      return res.status(400).json({
        success: false,
        message: 'Invalid class section'
      });
    }

    // Format class name as "Class 10-A (Medical)" or "Class 10-A" for general
    const className = classSection.trade_name === 'General'
      ? `Class ${classSection.class_name}-${classSection.section}`
      : `Class ${classSection.class_name}-${classSection.section} (${classSection.trade_name})`;

    // Insert lecture
    const [result] = await db.query(
      `INSERT INTO teacher_lectures
       (teacher_id, date, class_name, class_section_id, start_time, end_time, subject_name, topic, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [teacherId, date, className, class_section_id, start_time, end_time, subject_name, topic, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Lecture added successfully',
      lectureId: result.insertId
    });
  } catch (error) {
    console.error('Error adding lecture:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding lecture'
    });
  }
};

// Get lectures page with class sections
exports.getLectures = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        // No teachers found, render empty lectures with a message
        return res.render('teacher/lectures', {
          title: 'Lecture Management',
          layout: 'teacher',
          pageTitle: 'Lecture Management',
          currentPage: 'lectures',
          lectures: [],
          classSections: [],
          isAdmin: true
        });
      }
    }

    // Extract filter parameters from query
    const { date, class: classFilter, subject, status } = req.query;

    // Build the query with filters
    let query = `SELECT * FROM teacher_lectures WHERE teacher_id = ?`;
    const queryParams = [teacherId];

    if (date) {
      query += ` AND date = ?`;
      queryParams.push(date);
    }

    if (classFilter) {
      query += ` AND class_name LIKE ?`;
      queryParams.push(`%${classFilter}%`);
    }

    if (subject) {
      query += ` AND subject_name LIKE ?`;
      queryParams.push(`%${subject}%`);
    }

    if (status) {
      query += ` AND status = ?`;
      queryParams.push(status);
    }

    query += ` ORDER BY date ASC, start_time ASC`;

    // Get lectures with filters
    const [lectures] = await db.query(query, queryParams);

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/lectures', {
      title: 'Lecture Management',
      layout: 'teacher',
      pageTitle: 'Lecture Management',
      currentPage: 'lectures',
      lectures,
      classSections,
      isAdmin,
      filters: { date, class: classFilter, subject, status } // Pass filters back to the view
    });
  } catch (error) {
    console.error('Error fetching lecture data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading lectures',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Add a new practical with class section support
exports.addPractical = async (req, res) => {
  try {
    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;

    if (req.session.userRole === 'admin') {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    const { date, class_section_id, start_time, end_time, subject_name, practical_topic, venue, notes } = req.body;

    // Validate input
    if (!date || !class_section_id || !start_time || !end_time || !subject_name || !practical_topic || !venue) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Get class section details
    const classSection = await classUtils.getClassSectionById(class_section_id);
    if (!classSection) {
      return res.status(400).json({
        success: false,
        message: 'Invalid class section'
      });
    }

    // Format class name as "Class 10-A (Medical)" or "Class 10-A" for general
    const className = classSection.trade_name === 'General'
      ? `Class ${classSection.class_name}-${classSection.section}`
      : `Class ${classSection.class_name}-${classSection.section} (${classSection.trade_name})`;

    // Insert practical
    const [result] = await db.query(
      `INSERT INTO teacher_practicals
       (teacher_id, date, class_name, class_section_id, start_time, end_time, subject_name, practical_topic, venue, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [teacherId, date, className, class_section_id, start_time, end_time, subject_name, practical_topic, venue, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Practical added successfully',
      practicalId: result.insertId
    });
  } catch (error) {
    console.error('Error adding practical:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding practical'
    });
  }
};

// Get practicals page with class sections
exports.getPracticals = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        // No teachers found, render empty practicals with a message
        return res.render('teacher/practicals', {
          title: 'Practicals Management',
          layout: 'teacher',
          pageTitle: 'Practicals & Lab Sessions',
          currentPage: 'practicals',
          practicals: [],
          classSections: [],
          isAdmin: true
        });
      }
    }

    // Extract filter parameters from query
    const { date_range, date_from, date_to, class: classFilter, status } = req.query;

    // Build the query with filters
    let query = `SELECT * FROM teacher_practicals WHERE teacher_id = ?`;
    const queryParams = [teacherId];

    // Handle date filters
    if (date_range) {
      if (date_range === 'today') {
        query += ` AND date = CURDATE()`;
      } else if (date_range === 'this-week') {
        query += ` AND YEARWEEK(date, 1) = YEARWEEK(CURDATE(), 1)`;
      } else if (date_range === 'this-month') {
        query += ` AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())`;
      } else if (date_range === 'custom' && date_from && date_to) {
        query += ` AND date BETWEEN ? AND ?`;
        queryParams.push(date_from, date_to);
      }
    }

    if (classFilter && classFilter !== 'all') {
      query += ` AND class_name LIKE ?`;
      queryParams.push(`%${classFilter}%`);
    }

    if (status && status !== 'all') {
      query += ` AND status = ?`;
      queryParams.push(status);
    }

    query += ` ORDER BY date ASC, start_time ASC`;

    // Get practicals with filters
    const [practicals] = await db.query(query, queryParams);

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/practicals', {
      title: 'Practicals Management',
      layout: 'teacher',
      pageTitle: 'Practicals & Lab Sessions',
      currentPage: 'practicals',
      practicals,
      classSections,
      isAdmin,
      filters: { date_range, date_from, date_to, class: classFilter, status } // Pass filters back to the view
    });
  } catch (error) {
    console.error('Error fetching practicals data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practicals',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Update syllabus progress
exports.updateSyllabusProgress = async (req, res) => {
  try {
    console.log('Updating syllabus progress with data:', req.body);

    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;

    if (req.session.userRole === 'admin') {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    const { subject_name, total_topics, completed_topics } = req.body;

    // Validate input
    if (!subject_name || !total_topics || completed_topics === undefined) {
      console.error('Missing required fields:', { subject_name, total_topics, completed_topics });
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Convert to integers
    const totalTopicsInt = parseInt(total_topics);
    const completedTopicsInt = parseInt(completed_topics);

    // Validate that completed topics is not greater than total topics
    if (completedTopicsInt > totalTopicsInt) {
      return res.status(400).json({
        success: false,
        message: 'Completed topics cannot exceed total topics'
      });
    }

    // Check if the subject already exists in syllabus_progress
    const [existingProgress] = await db.query(
      `SELECT * FROM syllabus_progress
       WHERE teacher_id = ? AND subject_name = ?`,
      [teacherId, subject_name]
    );

    console.log('Existing progress:', existingProgress);

    let result;
    if (existingProgress.length > 0) {
      // Update existing progress
      console.log('Updating existing progress');
      [result] = await db.query(
        `UPDATE syllabus_progress
         SET total_topics = ?, completed_topics = ?, updated_at = NOW()
         WHERE teacher_id = ? AND subject_name = ?`,
        [totalTopicsInt, completedTopicsInt, teacherId, subject_name]
      );
    } else {
      // Insert new progress
      console.log('Inserting new progress');
      [result] = await db.query(
        `INSERT INTO syllabus_progress
         (teacher_id, subject_name, total_topics, completed_topics)
         VALUES (?, ?, ?, ?)`,
        [teacherId, subject_name, totalTopicsInt, completedTopicsInt]
      );
    }

    console.log('Syllabus progress updated successfully');

    res.status(200).json({
      success: true,
      message: 'Syllabus progress updated successfully',
      data: {
        subject_name,
        total_topics: totalTopicsInt,
        completed_topics: completedTopicsInt,
        progress: Math.round((completedTopicsInt / totalTopicsInt) * 100)
      }
    });
  } catch (error) {
    console.error('Error updating syllabus progress:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating syllabus progress: ' + error.message
    });
  }
};