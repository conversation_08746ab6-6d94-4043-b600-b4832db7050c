const db = require('../config/database');
const classUtils = require('../utils/class-utils');

// Import API functions
const apiController = require('./teacher-controller-api');
const updateController = require('./teacher-controller-update');

// Add API functions to exports
exports.getLectureById = apiController.getLectureById;
exports.getPracticalById = apiController.getPracticalById;
exports.updatePracticalStatus = apiController.updatePracticalStatus;
exports.updateLectureStatus = apiController.updateLectureStatus;

// Add updated functions
exports.addLecture = updateController.addLecture;
exports.addPractical = updateController.addPractical;
exports.updateSyllabusProgress = updateController.updateSyllabusProgress;

// Grade practical record
exports.gradePracticalRecord = async (req, res) => {
  try {
    const recordId = req.params.id;
    const { grade, remarks } = req.body;

    // Validate input
    if (!grade) {
      return res.status(400).json({
        success: false,
        message: 'Grade is required'
      });
    }

    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }

    // Update record
    let result;
    if (isAdmin) {
      // Admins can update any record
      [result] = await db.query(
        `UPDATE student_practical_records
         SET grade = ?, remarks = ?, graded_at = NOW()
         WHERE id = ?`,
        [grade, remarks, recordId]
      );
    } else {
      // Teachers can only update their own records
      [result] = await db.query(
        `UPDATE student_practical_records
         SET grade = ?, remarks = ?, graded_at = NOW()
         WHERE id = ? AND teacher_id = ?`,
        [grade, remarks, recordId, teacherId]
      );
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Record not found or you do not have permission to update it'
      });
    }

    res.json({
      success: true,
      message: 'Practical record graded successfully'
    });
  } catch (error) {
    console.error('Error grading practical record:', error);
    res.status(500).json({
      success: false,
      message: 'Error grading practical record'
    });
  }
};

// Teacher dashboard
exports.getDashboard = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    const hasGlobalAccess = isAdmin;
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !hasGlobalAccess) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (hasGlobalAccess) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log(`${req.session.userRole} using teacher ID:`, teacherId);
      } else {
        // No teachers found, render empty dashboard with a message
        return res.render('teacher/dashboard', {
          title: 'Teacher Dashboard',
          layout: 'teacher',
          pageTitle: 'Teacher Dashboard',
          currentPage: 'dashboard',
          stats: { todayLectures: 0, pendingTopics: 0, upcomingPracticals: 0, syllabusProgress: 0 },
          todayTimetable: [],
          syllabusProgress: [],
          upcomingPracticals: [],
          isAdmin: isAdmin
        });
      }
    }

    // Get today's lectures
    const [todayLectures] = await db.query(
      `SELECT * FROM teacher_lectures
       WHERE teacher_id = ?
       AND date = CURDATE()
       ORDER BY start_time ASC`,
      [teacherId]
    );

    // Get statistics
    const [lectureStats] = await db.query(
      `SELECT
         COUNT(CASE WHEN date = CURDATE() THEN 1 END) as todayLectures,
         COUNT(CASE WHEN status = 'pending' THEN 1 END) as pendingLectures
       FROM teacher_lectures
       WHERE teacher_id = ?`,
      [teacherId]
    );

    const [practicalStats] = await db.query(
      `SELECT
         COUNT(*) as upcomingPracticals
       FROM teacher_practicals
       WHERE teacher_id = ?
       AND date >= CURDATE()
       AND status = 'pending'`,
      [teacherId]
    );

    // Get syllabus progress
    const [syllabusProgress] = await db.query(
      `SELECT
         subject_name as name,
         completed_topics as completed,
         total_topics as total,
         ROUND((completed_topics / total_topics) * 100) as progress
       FROM syllabus_progress
       WHERE teacher_id = ?`,
      [teacherId]
    );

    // Calculate overall syllabus progress percentage
    let overallProgress = 0;
    if (syllabusProgress.length > 0) {
      const totalCompleted = syllabusProgress.reduce((sum, subject) => sum + subject.completed, 0);
      const totalTopics = syllabusProgress.reduce((sum, subject) => sum + subject.total, 0);
      overallProgress = Math.round((totalCompleted / totalTopics) * 100);
    }

    // Combine all stats
    const stats = {
      todayLectures: lectureStats[0].todayLectures || 0,
      pendingTopics: lectureStats[0].pendingLectures || 0,
      upcomingPracticals: practicalStats[0].upcomingPracticals || 0,
      syllabusProgress: overallProgress
    };

    // Get upcoming practicals
    const [upcomingPracticals] = await db.query(
      `SELECT * FROM teacher_practicals
       WHERE teacher_id = ?
       AND date >= CURDATE()
       ORDER BY date ASC, start_time ASC
       LIMIT 5`,
      [teacherId]
    );

    res.render('teacher/dashboard', {
      title: 'Teacher Dashboard',
      layout: 'teacher',
      pageTitle: 'Teacher Dashboard',
      currentPage: 'dashboard',
      stats,
      todayTimetable: todayLectures,
      syllabusProgress,
      upcomingPracticals,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading dashboard',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Teacher timetable
exports.getTimetable = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';

    res.render('teacher/timetable', {
      title: 'Timetable',
      layout: 'teacher',
      pageTitle: 'Class Timetable',
      currentPage: 'timetable',
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching timetable:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load timetable',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Teacher timetable with teacher filter
exports.getTeacherTimetable = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';
    // Check if user is admin with username 'principal' (our workaround)
    const isPrincipal = req.session.userRole === 'admin' && req.session.user && req.session.user.username === 'principal';

    res.render('teacher/teacher-timetable', {
      title: 'Teacher Timetable',
      layout: 'teacher',
      pageTitle: 'Teacher Timetable',
      currentPage: 'teacher-timetable',
      isAdmin,
      isPrincipal
    });
  } catch (error) {
    console.error('Error fetching teacher timetable:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher timetable',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Class-Teacher-Subject mind map
exports.getClassTeacherMap = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';

    res.render('teacher/class-teacher-map', {
      title: 'Class-Subject-Teacher Mind Map',
      layout: 'teacher',
      pageTitle: 'Class-Subject-Teacher Mind Map',
      currentPage: 'class-teacher-map',
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching class-teacher map:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load class-teacher map',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Class-Subject Summary page
exports.getClassSubjectSummary = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';
    // Check if user is admin with username 'principal' (our workaround)
    const isPrincipal = req.session.userRole === 'admin' && req.session.user && req.session.user.username === 'principal';

    res.render('teacher/class-subject-summary', {
      title: 'Class-Subject Summary',
      layout: 'teacher',
      pageTitle: 'Class-Subject Summary',
      currentPage: 'class-subject-summary',
      isAdmin,
      isPrincipal
    });
  } catch (error) {
    console.error('Error fetching class-subject summary:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load class-subject summary',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Teacher's personal timetable
exports.getMyTimetable = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;
    let teacherInfo = null;

    // Get teacher info
    const [teacherData] = await db.query(
      `SELECT id, full_name, subjects FROM users WHERE id = ?`,
      [teacherId]
    );

    if (teacherData.length > 0) {
      teacherInfo = teacherData[0];

      // Handle null subjects by providing a default
      if (!teacherInfo.subjects) {
        console.log('No subjects defined for teacher, using default');
        teacherInfo.subjects = 'Computer Science';
      }

      console.log('Loaded teacher info:', teacherInfo);
    } else {
      console.error('No teacher data found for ID:', teacherId);
      // Create a default teacher info object to prevent template errors
      teacherInfo = {
        id: teacherId,
        full_name: 'Unknown Teacher',
        subjects: 'Computer Science'
      };
    }

    res.render('teacher/my-timetable', {
      title: 'My Teaching Schedule',
      layout: 'teacher',
      pageTitle: 'My Teaching Schedule',
      currentPage: 'my-timetable',
      teacherInfo,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching teacher timetable:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher timetable',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Teacher profile page
exports.getProfile = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      }
    }

    res.render('teacher/profile', {
      title: 'Teacher Profile',
      layout: 'teacher',
      pageTitle: 'My Profile',
      currentPage: 'profile',
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching teacher profile:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher profile',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Teacher classes page
exports.getClasses = async (req, res) => {
  try {
    // Get the teacher's information
    const isAdmin = req.session.userRole === 'admin';
    const isPrincipal = req.session.userRole === 'admin' && req.session.user && req.session.user.username === 'principal';
    let teacherId = req.session.userId;
    let isComputerTeacher = false;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin && !isPrincipal) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin || isPrincipal) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      }
    }

    // Get teacher information
    const [teacherInfo] = await db.query(
      `SELECT id, username, name as full_name, email, profile_image, subjects, bio
       FROM users
       WHERE id = ? AND role = 'teacher'`,
      [teacherId]
    );

    if (teacherInfo.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Teacher not found',
        error: { status: 404 },
        layout: 'teacher'
      });
    }

    console.log(`Loading classes for teacher ID: ${teacherId}`);

    // Check if teacher is a computer teacher
    // First check if teacher_specialization table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_specialization'
    `);

    if (tableExists[0].table_exists > 0) {
      // Check if teacher has Computer Science specialization
      const [specialization] = await db.query(`
        SELECT id, is_primary
        FROM teacher_specialization
        WHERE teacher_id = ? AND specialization = 'Computer Science'
      `, [teacherId]);

      if (specialization.length > 0) {
        isComputerTeacher = true;
        console.log(`Teacher ID ${teacherId} is a computer teacher with primary=${specialization[0].is_primary}`);
      }
    } else {
      // If no specialization table, check if teacher teaches computer subjects
      const [teachesComputerSubjects] = await db.query(`
        SELECT ts.id
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        WHERE ts.teacher_id = ?
        AND (
          s.name LIKE '%computer%'
          OR s.name LIKE '%Computer%'
          OR s.code LIKE '%CS%'
          OR s.code LIKE '%comp%'
        )
      `, [teacherId]);

      if (teachesComputerSubjects.length > 0) {
        isComputerTeacher = true;
        console.log(`Teacher ID ${teacherId} is a computer teacher (based on subjects taught)`);
      }
    }

    // For debugging purposes, log the teacher's assigned classes
    const [debugClasses] = await db.query(`
      SELECT tc.id, r.room_number, CONCAT(c.grade, ' ', t.name, ' ', cr.section) as class_name
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN rooms r ON cr.room_id = r.id
      JOIN classes c ON cr.class_id = c.id
      LEFT JOIN trades t ON cr.trade_id = t.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);

    console.log(`Teacher has ${debugClasses.length} assigned classes in teacher_classes table`);
    debugClasses.forEach(cls => {
      console.log(`- ${cls.id}: Room ${cls.room_number} - ${cls.class_name}`);
    });

    // Debug teacher subjects
    const [debugSubjects] = await db.query(`
      SELECT ts.id, s.name, s.code
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = ?
    `, [teacherId]);

    console.log(`Teacher has ${debugSubjects.length} assigned subjects in teacher_subjects table`);
    debugSubjects.forEach(subj => {
      console.log(`- ${subj.id}: ${subj.name} (${subj.code || 'No code'})`);
    });

    // Get classes assigned to this teacher
    // First check if class_incharge table exists
    const [tableCheck] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_incharge'
    `);

    const classInchargeExists = tableCheck[0].table_exists > 0;
    console.log(`class_incharge table exists: ${classInchargeExists}`);

    // Debug the SQL queries we're about to run
    const classroomsQuery = `
      SELECT DISTINCT
        c.id AS class_id,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) AS class_name,
        c.grade,
        t.name AS trade,
        cr.section,
        cr.session,
        r.room_number AS classroom_number,
        CASE WHEN cr.incharge = ? THEN TRUE ELSE FALSE END AS is_class_incharge,
        cr.incharge AS incharge_teacher_id,
        incharge.name AS incharge_teacher_name,
        incharge.email AS incharge_email,
        incharge.profile_image AS incharge_profile_image
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN rooms r ON cr.room_id = r.id
      JOIN classes c ON cr.class_id = c.id
      LEFT JOIN trades t ON cr.trade_id = t.id
      LEFT JOIN users incharge ON cr.incharge = incharge.id
      WHERE tc.teacher_id = ?
    `;

    console.log('Will run classrooms query:', classroomsQuery.replace(/\s+/g, ' '));

    let teacherClasses = [];

    // Check if subject_category table exists to get primary/secondary subjects
    const [subjectCategoryExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'subject_category'
    `);

    console.log(`subject_category table exists: ${subjectCategoryExists[0].table_exists > 0}`);

    let subjectCategoryJoin = '';
    let subjectCategorySelect = 'FALSE AS is_primary_subject';
    let subjectCategoryWhere = '';

    if (subjectCategoryExists[0].table_exists > 0 && isComputerTeacher) {
      // Check if is_primary column exists in subject_category table
      const [isPrimaryColumnExists] = await db.query(`
        SELECT COUNT(*) as column_exists
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'subject_category'
        AND column_name = 'is_primary'
      `);

      if (isPrimaryColumnExists[0].column_exists > 0) {
        subjectCategoryJoin = 'LEFT JOIN subject_category sc ON s.id = sc.subject_id';
        subjectCategorySelect = 'IFNULL(sc.is_primary, FALSE) AS is_primary_subject';

        // For computer teachers, we want to show all subjects but mark which ones are primary
        console.log('Using subject_category to identify primary/secondary subjects');
      } else {
        // If is_primary column doesn't exist, use a fallback approach
        subjectCategoryJoin = 'LEFT JOIN subject_category sc ON s.id = sc.subject_id';
        subjectCategorySelect = `
          CASE
            WHEN sc.category = 'Computer Science' THEN TRUE
            WHEN s.name LIKE '%computer%' OR s.name LIKE '%Computer%' OR s.name LIKE '%Programming%' OR
                 s.code LIKE '%CS%' OR s.code LIKE '%comp%' OR s.code LIKE '%CP%' THEN TRUE
            ELSE FALSE
          END AS is_primary_subject
        `;
        console.log('Using fallback approach to identify primary/secondary subjects');
      }
    }

    if (classInchargeExists) {
      // First, get all classes assigned to this teacher
      const [teacherClassrooms] = await db.query(`
        SELECT DISTINCT
          c.id AS class_id,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) AS class_name,
          c.grade,
          t.name AS trade,
          cr.section,
          cr.session,
          r.room_number AS classroom_number,
          CASE WHEN cr.incharge = ? THEN TRUE ELSE FALSE END AS is_class_incharge,
          cr.incharge AS incharge_teacher_id,
          incharge.name AS incharge_teacher_name,
          incharge.email AS incharge_email,
          incharge.profile_image AS incharge_profile_image
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN rooms r ON cr.room_id = r.id
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        LEFT JOIN users incharge ON cr.incharge = incharge.id
        WHERE tc.teacher_id = ?
      `, [teacherId, teacherId]);

      console.log(`Found ${teacherClassrooms.length} classrooms for teacher ID ${teacherId}`);

      // Then, get all subjects taught by this teacher
      const [teacherSubjects] = await db.query(`
        SELECT
          s.id AS subject_id,
          s.name AS subject_name,
          s.code AS subject_code,
          ${subjectCategorySelect}
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        ${subjectCategoryJoin}
        WHERE ts.teacher_id = ?
        ${subjectCategoryWhere}
        ORDER BY ${isComputerTeacher ? 'is_primary_subject DESC,' : ''} s.name
      `, [teacherId]);

      console.log(`Found ${teacherSubjects.length} subjects for teacher ID ${teacherId}`);

      // Now combine classes and subjects
      let teacherClasses = [];

      for (const classroom of teacherClassrooms) {
        for (const subject of teacherSubjects) {
          // Get lecture counts for this class-subject combination
          const [lectureInfo] = await db.query(`
            SELECT
              IFNULL(num_theory_lectures, 4) AS num_theory_lectures,
              IFNULL(num_practical_lectures, 2) AS num_practical_lectures,
              IFNULL((num_theory_lectures + num_practical_lectures), 6) AS total_lectures
            FROM subject_class_assignment
            WHERE class_id = ? AND subject_id = ?
          `, [classroom.class_id, subject.subject_id]);

          const lectureData = lectureInfo.length > 0 ? lectureInfo[0] : {
            num_theory_lectures: 4,
            num_practical_lectures: 2,
            total_lectures: 6
          };

          teacherClasses.push({
            ...classroom,
            ...subject,
            ...lectureData
          });
        }
      }
    } else {
      // First, get all classes assigned to this teacher
      const [teacherClassrooms] = await db.query(`
        SELECT DISTINCT
          c.id AS class_id,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) AS class_name,
          c.grade,
          t.name AS trade,
          cr.section,
          cr.session,
          r.room_number AS classroom_number,
          CASE WHEN cr.incharge = ? THEN TRUE ELSE FALSE END AS is_class_incharge,
          cr.incharge AS incharge_teacher_id,
          IFNULL(incharge.name, 'Not Assigned') AS incharge_teacher_name,
          incharge.email AS incharge_email,
          incharge.profile_image AS incharge_profile_image
        FROM teacher_classes tc
        JOIN classrooms cr ON tc.classroom_id = cr.id
        JOIN rooms r ON cr.room_id = r.id
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        LEFT JOIN users incharge ON cr.incharge = incharge.id
        WHERE tc.teacher_id = ?
      `, [teacherId, teacherId]);

      console.log(`Found ${teacherClassrooms.length} classrooms for teacher ID ${teacherId}`);

      // Then, get all subjects taught by this teacher
      const [teacherSubjects] = await db.query(`
        SELECT
          s.id AS subject_id,
          s.name AS subject_name,
          s.code AS subject_code,
          ${subjectCategorySelect}
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        ${subjectCategoryJoin}
        WHERE ts.teacher_id = ?
        ${subjectCategoryWhere}
        ORDER BY ${isComputerTeacher ? 'is_primary_subject DESC,' : ''} s.name
      `, [teacherId]);

      console.log(`Found ${teacherSubjects.length} subjects for teacher ID ${teacherId}`);

      // Now combine classes and subjects
      let teacherClasses = [];

      for (const classroom of teacherClassrooms) {
        for (const subject of teacherSubjects) {
          // Get lecture counts for this class-subject combination
          const [lectureInfo] = await db.query(`
            SELECT
              IFNULL(num_theory_lectures, 4) AS num_theory_lectures,
              IFNULL(num_practical_lectures, 2) AS num_practical_lectures,
              IFNULL((num_theory_lectures + num_practical_lectures), 6) AS total_lectures
            FROM subject_class_assignment
            WHERE class_id = ? AND subject_id = ?
          `, [classroom.class_id, subject.subject_id]);

          const lectureData = lectureInfo.length > 0 ? lectureInfo[0] : {
            num_theory_lectures: 4,
            num_practical_lectures: 2,
            total_lectures: 6
          };

          teacherClasses.push({
            ...classroom,
            ...subject,
            ...lectureData
          });
        }
      }
    }

    // If no classes found, set empty array and we'll show an error message
    let classes = teacherClasses;
    console.log(`Teacher classes array has ${classes.length} items`);

    if (classes.length === 0) {
      console.log(`No classes found for teacher ID: ${teacherId}`);
      // We'll keep classes as an empty array and handle this in the response
    } else {
      console.log('First few classes:');
      classes.slice(0, 3).forEach((cls, index) => {
        console.log(`Class ${index + 1}:`, JSON.stringify(cls));
      });
    }

    // No sample data creation - we'll show an error message if no classes are found

    // Get student counts for each class
    for (const cls of classes) {
      try {
        // Check if student_classrooms table exists
        const [studentTableCheck] = await db.query(`
          SELECT COUNT(*) as table_exists
          FROM information_schema.tables
          WHERE table_schema = DATABASE()
          AND table_name = 'student_classrooms'
        `);

        if (studentTableCheck[0].table_exists > 0) {
          // Check if gender column exists in users table
          const [genderColumnExists] = await db.query(`
            SELECT COUNT(*) as column_exists
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'users'
            AND column_name = 'gender'
          `);

          let studentCounts;

          if (genderColumnExists[0].column_exists > 0) {
            // Get total students, boys and girls
            [studentCounts] = await db.query(`
              SELECT
                COUNT(*) AS total_students,
                SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) AS boys_count,
                SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) AS girls_count
              FROM student_classrooms sc
              JOIN users u ON sc.student_id = u.id
              JOIN classrooms cr ON sc.classroom_id = cr.id
              JOIN classes c ON cr.class_id = c.id
              LEFT JOIN trades t ON cr.trade_id = t.id
              WHERE CONCAT(c.grade, ' ', t.name, ' ', cr.section) = ?
            `, [cls.class_name || `${cls.grade} ${cls.trade} ${cls.section}`]);
          } else {
            // If gender column doesn't exist, just count total students
            [studentCounts] = await db.query(`
              SELECT
                COUNT(*) AS total_students,
                0 AS boys_count,
                0 AS girls_count
              FROM student_classrooms sc
              JOIN classrooms cr ON sc.classroom_id = cr.id
              JOIN classes c ON cr.class_id = c.id
              LEFT JOIN trades t ON cr.trade_id = t.id
              WHERE CONCAT(c.grade, ' ', t.name, ' ', cr.section) = ?
            `, [cls.class_name || `${cls.grade} ${cls.trade} ${cls.section}`]);
          }

          if (studentCounts.length > 0) {
            cls.total_students = studentCounts[0].total_students || 0;
            cls.boys_count = studentCounts[0].boys_count || 0;
            cls.girls_count = studentCounts[0].girls_count || 0;
          } else {
            // If no students found, set counts to 0
            cls.total_students = 0;
            cls.boys_count = 0;
            cls.girls_count = 0;
          }
        } else {
          // If table doesn't exist, set counts to 0
          cls.total_students = 0;
          cls.boys_count = 0;
          cls.girls_count = 0;
        }
      } catch (error) {
        console.error('Error getting student counts:', error);
        // If query fails, set counts to 0
        cls.total_students = 0;
        cls.boys_count = 0;
        cls.girls_count = 0;
      }
    }

    // Group classes by class name for display
    const classGroups = {};

    // Debug output for classes
    console.log(`Grouping ${classes.length} classes:`);
    classes.forEach((cls, index) => {
      if (index < 5) { // Limit output to first 5 classes
        console.log(`- Class ${index+1}: ID=${cls.class_id}, Name=${cls.name || cls.class_name}, Grade=${cls.grade}, Trade=${cls.trade}, Section=${cls.section}`);
      }
    });

    // First, create entries for all classes
    const uniqueClasses = new Set();
    classes.forEach(cls => {
      // Use class_name if available, otherwise construct from grade, trade, section
      if (cls.class_name) {
        uniqueClasses.add(cls.class_name);
      } else if (cls.grade && cls.trade && cls.section) {
        const classKey = `${cls.grade} ${cls.trade} ${cls.section}`;
        uniqueClasses.add(classKey);
      } else if (cls.name) {
        uniqueClasses.add(cls.name);
      }
    });

    console.log(`Found ${uniqueClasses.size} unique classes`);

    // Initialize class groups
    uniqueClasses.forEach(classKey => {
      // Find the class data - first try exact match on class_name
      let classData = classes.find(cls => cls.class_name === classKey);

      // If not found, try constructed key
      if (!classData) {
        classData = classes.find(cls =>
          cls.grade && cls.trade && cls.section &&
          `${cls.grade} ${cls.trade} ${cls.section}` === classKey
        );
      }

      // If still not found, try name
      if (!classData) {
        classData = classes.find(cls => cls.name === classKey);
      }

      // If we have class data, create the group
      if (classData) {
        classGroups[classKey] = {
          class_id: classData.class_id,
          class_name: classKey,
          grade: classData.grade,
          trade: classData.trade,
          section: classData.section,
          session: classData.session,
          classroom_number: classData.classroom_number,
          total_students: classData.total_students || 0,
          boys_count: classData.boys_count || 0,
          girls_count: classData.girls_count || 0,
          is_class_incharge: classData.is_class_incharge || false,
          incharge_teacher_id: classData.incharge_teacher_id,
          incharge_teacher_name: classData.incharge_teacher_name || 'Not Assigned',
          incharge_email: classData.incharge_email,
          incharge_profile_image: classData.incharge_profile_image,
          primary_subjects: [],
          secondary_subjects: []
        };
      }
    });

    // Now add all subjects to their respective classes
    classes.forEach(cls => {
      // Determine the class key
      let classKey;
      if (cls.class_name) {
        classKey = cls.class_name;
      } else if (cls.grade && cls.trade && cls.section) {
        classKey = `${cls.grade} ${cls.trade} ${cls.section}`;
      } else if (cls.name) {
        classKey = cls.name;
      } else {
        console.log(`Skipping class with no identifiable key: ${JSON.stringify(cls)}`);
        return; // Skip this class if we can't identify it
      }

      // Skip if the class group doesn't exist
      if (!classGroups[classKey]) {
        console.log(`Class group not found for key: ${classKey}`);
        return;
      }

      // Skip if this subject is already added to this class
      const subjectExists = (isComputerTeacher && cls.is_primary_subject)
        ? (classGroups[classKey].primary_subjects &&
           classGroups[classKey].primary_subjects.some(s => s.subject_id === cls.subject_id))
        : (classGroups[classKey].secondary_subjects &&
           classGroups[classKey].secondary_subjects.some(s => s.subject_id === cls.subject_id));

      if (subjectExists) {
        return;
      }

      // Add subject to the class, separating primary and secondary subjects
      const subjectData = {
        subject_id: cls.subject_id,
        subject_name: cls.subject_name,
        subject_code: cls.subject_code,
        theory_lectures: cls.num_theory_lectures,
        practical_lectures: cls.num_practical_lectures,
        total_lectures: cls.total_lectures,
        is_primary: cls.is_primary_subject
      };

      if (isComputerTeacher) {
        // For computer teachers, separate primary and secondary subjects
        if (cls.is_primary_subject) {
          classGroups[classKey].primary_subjects.push(subjectData);
        } else {
          classGroups[classKey].secondary_subjects.push(subjectData);
        }
      } else {
        // For non-computer teachers, just add all subjects to primary
        classGroups[classKey].primary_subjects.push(subjectData);
      }
    });

    // Debug output
    const classesArray = Object.values(classGroups);
    console.log(`Rendering ${classesArray.length} classes for teacher:`);
    classesArray.forEach(cls => {
      console.log(`- Class: ${cls.class_name}, Primary subjects: ${cls.primary_subjects.length}, Secondary subjects: ${cls.secondary_subjects.length}`);
    });

    res.render('teacher/classes', {
      title: 'My Classes',
      layout: 'teacher',
      pageTitle: 'My Classes',
      currentPage: 'classes',
      teacher: teacherInfo[0],
      classes: classesArray,
      isAdmin,
      isPrincipal,
      isComputerTeacher
    });
  } catch (error) {
    console.error('Error loading classes:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load classes',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Syllabus management
exports.getSyllabus = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        // No teachers found, render empty syllabus with a message
        return res.render('teacher/syllabus', {
          title: 'Syllabus Management',
          layout: 'teacher',
          pageTitle: 'Syllabus Management',
          currentPage: 'syllabus',
          syllabusProgress: [],
          syllabusTopics: [],
          classSections: [],
          isAdmin: true
        });
      }
    }

    // Get syllabus progress
    const [syllabusProgress] = await db.query(
      `SELECT
         subject_name as name,
         completed_topics as completed,
         total_topics as total,
         ROUND((completed_topics / total_topics) * 100) as progress
       FROM syllabus_progress
       WHERE teacher_id = ?`,
      [teacherId]
    );

    // Get syllabus topics
    const [syllabusTopics] = await db.query(
      `SELECT * FROM teacher_syllabus
       WHERE teacher_id = ?
       ORDER BY subject_name, status DESC, completion_date DESC`,
      [teacherId]
    );

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/syllabus', {
      title: 'Syllabus Management',
      layout: 'teacher',
      pageTitle: 'Syllabus Management',
      currentPage: 'syllabus',
      syllabusProgress,
      syllabusTopics,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching syllabus data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading syllabus',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Syllabus topics management
exports.getSyllabusTopics = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        // No teachers found, render empty syllabus topics with a message
        return res.render('teacher/syllabus-topics', {
          title: 'Syllabus Topics',
          layout: 'teacher',
          pageTitle: 'Syllabus Topics',
          currentPage: 'syllabus',
          topics: [],
          classSections: [],
          isAdmin: true
        });
      }
    }

    // Get syllabus topics
    const [topics] = await db.query(
      `SELECT * FROM teacher_syllabus
       WHERE teacher_id = ?
       ORDER BY subject_name, status DESC, completion_date DESC`,
      [teacherId]
    );

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/syllabus-topics', {
      title: 'Syllabus Topics',
      layout: 'teacher',
      pageTitle: 'Syllabus Topics',
      currentPage: 'syllabus',
      topics,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching syllabus topics:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading syllabus topics',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Syllabus progress management
exports.getSyllabusProgress = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      } else {
        // No teachers found, render empty syllabus progress with a message
        return res.render('teacher/syllabus-progress', {
          title: 'Syllabus Progress',
          layout: 'teacher',
          pageTitle: 'Syllabus Progress',
          currentPage: 'syllabus',
          progress: [],
          subjects: [],
          isAdmin: true
        });
      }
    }

    // Get syllabus progress
    const [progress] = await db.query(
      `SELECT
         subject_name as name,
         completed_topics as completed,
         total_topics as total,
         ROUND((completed_topics / total_topics) * 100) as progress
       FROM syllabus_progress
       WHERE teacher_id = ?`,
      [teacherId]
    );

    // Get subjects for the form
    const [subjects] = await db.query(
      `SELECT DISTINCT subject_name FROM teacher_syllabus WHERE teacher_id = ?`,
      [teacherId]
    );

    res.render('teacher/syllabus-progress', {
      title: 'Syllabus Progress',
      layout: 'teacher',
      pageTitle: 'Syllabus Progress',
      currentPage: 'syllabus',
      progress,
      subjects,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching syllabus progress:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading syllabus progress',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Reports management
exports.getReports = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      }
    }

    // Get quick stats
    const [lectureStats] = await db.query(
      `SELECT
         COUNT(*) as total_lectures,
         COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_lectures,
         COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_lectures
       FROM teacher_lectures
       WHERE teacher_id = ?`,
      [teacherId]
    );

    const [syllabusStats] = await db.query(
      `SELECT
         SUM(completed_topics) as completed_topics,
         SUM(total_topics) as total_topics
       FROM syllabus_progress
       WHERE teacher_id = ?`,
      [teacherId]
    );

    const [practicalStats] = await db.query(
      `SELECT
         COUNT(*) as total_practicals,
         COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_submissions
       FROM teacher_practicals
       WHERE teacher_id = ?`,
      [teacherId]
    );

    const [studentStats] = await db.query(
      `SELECT
         COUNT(DISTINCT student_id) as active_students
       FROM student_practical_records
       WHERE teacher_id = ?`,
      [teacherId]
    );

    res.render('teacher/reports', {
      title: 'Reports',
      layout: 'teacher',
      pageTitle: 'Reports',
      currentPage: 'reports',
      lectureStats: lectureStats[0] || { total_lectures: 0, delivered_lectures: 0, pending_lectures: 0 },
      syllabusStats: syllabusStats[0] || { completed_topics: 0, total_topics: 0 },
      practicalStats: practicalStats[0] || { total_practicals: 0, pending_submissions: 0 },
      studentStats: studentStats[0] || { active_students: 0 },
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching reports data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading reports',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Practicals management
exports.getPracticals = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for practicals:', teacherId);
    }

    // Get upcoming practicals
    const [practicals] = await db.query(
      `SELECT * FROM teacher_practicals
       WHERE teacher_id = ?
       ORDER BY date ASC, start_time ASC`,
      [teacherId]
    );

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/practicals', {
      title: 'Practicals Management',
      layout: 'teacher',
      pageTitle: 'Practicals & Lab Sessions',
      currentPage: 'practicals',
      practicals,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practicals data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practicals',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Practical records management
exports.getPracticalRecords = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for practical records:', teacherId);
    }

    // Get practical records
    const [records] = await db.query(
      `SELECT spr.*, u.name as student_name, tp.practical_topic, tp.subject_name
       FROM student_practical_records spr
       JOIN users u ON spr.student_id = u.id
       JOIN teacher_practicals tp ON spr.practical_id = tp.id
       WHERE spr.teacher_id = ?
       ORDER BY spr.created_at DESC`,
      [teacherId]
    );

    // Get class sections for the filter
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/practical-records', {
      title: 'Practical Records',
      layout: 'teacher',
      pageTitle: 'Student Practical Records',
      currentPage: 'practical-records',
      records,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practical records:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practical records',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Reports page
exports.getReports = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for reports:', teacherId);
    }

    // Extract filter parameters from query
    const { date_range, date_from, date_to, class: classFilter, subject } = req.query;

    // Get class sections for the filter
    const classSections = await classUtils.getAllClassSections();

    // Get filtered reports data
    // In a real app, you would use these filters in your queries
    // For example:

    let reportsQuery = `SELECT * FROM teacher_lectures WHERE teacher_id = ?`;
    const queryParams = [teacherId];

    // Apply filters to query if provided
    if (date_range) {
      if (date_range === 'this-week') {
        reportsQuery += ` AND YEARWEEK(date, 1) = YEARWEEK(CURDATE(), 1)`;
      } else if (date_range === 'this-month') {
        reportsQuery += ` AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())`;
      } else if (date_range === 'custom' && date_from && date_to) {
        reportsQuery += ` AND date BETWEEN ? AND ?`;
        queryParams.push(date_from, date_to);
      }
    }

    if (classFilter) {
      reportsQuery += ` AND class_name LIKE ?`;
      queryParams.push(`%${classFilter}%`);
    }

    if (subject) {
      reportsQuery += ` AND subject_name LIKE ?`;
      queryParams.push(`%${subject}%`);
    }

    // Order and limit
    reportsQuery += ` ORDER BY date DESC LIMIT 50`;

    // Execute the query
    const [reports] = await db.query(reportsQuery, queryParams);

    res.render('teacher/reports', {
      title: 'Reports',
      layout: 'teacher',
      pageTitle: 'Teacher Reports',
      currentPage: 'reports',
      classSections,
      reports,
      isAdmin,
      filters: { date_range, date_from, date_to, class: classFilter, subject } // Pass filters back to view
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading reports',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get practical details
exports.getPracticalDetails = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the first teacher's data
    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID:', teacherId);
      }
    }

    const practicalId = req.params.id;

    // Get practical details
    let practicals;
    if (isAdmin) {
      // Admins can view any practical
      [practicals] = await db.query(
        `SELECT tp.*, CONCAT(c.grade, ' ', t.name, ' ', c.section) as class_name, cs.section, t.name as trade_name
         FROM teacher_practicals tp
         JOIN class_sections cs ON tp.class_section_id = cs.id
         JOIN classes c ON cs.class_id = c.id
         LEFT JOIN trades t ON cs.trade_id = t.id
         WHERE tp.id = ?`,
        [practicalId]
      );
    } else {
      // Teachers can only view their own practicals
      [practicals] = await db.query(
        `SELECT tp.*, CONCAT(c.grade, ' ', t.name, ' ', c.section) as class_name, cs.section, t.name as trade_name
         FROM teacher_practicals tp
         JOIN class_sections cs ON tp.class_section_id = cs.id
         JOIN classes c ON cs.class_id = c.id
         LEFT JOIN trades t ON cs.trade_id = t.id
         WHERE tp.id = ? AND tp.teacher_id = ?`,
        [practicalId, teacherId]
      );
    }

    if (practicals.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Practical not found',
        error: { status: 404, stack: 'Practical not found or you do not have permission to view it.' },
        layout: 'teacher'
      });
    }

    const practical = practicals[0];

    // Get student records for this practical
    const [studentRecords] = await db.query(
      `SELECT spr.*, u.name as student_name
       FROM student_practical_records spr
       JOIN users u ON spr.student_id = u.id
       WHERE spr.practical_id = ?`,
      [practicalId]
    );

    res.render('teacher/practical-details', {
      title: 'Practical Details',
      layout: 'teacher',
      pageTitle: 'Practical Details',
      currentPage: 'practicals',
      practical,
      studentRecords,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practical details:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practical details',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get edit practical page
exports.getEditPractical = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for editing practicals:', teacherId);
    }

    const practicalId = req.params.id;

    // Get practical details
    let practicals;
    if (isAdmin) {
      // Admins can edit any practical
      [practicals] = await db.query(
        `SELECT tp.*, CONCAT(c.grade, ' ', t.name, ' ', c.section) as class_name, cs.section, t.name as trade_name
         FROM teacher_practicals tp
         JOIN class_sections cs ON tp.class_section_id = cs.id
         JOIN classes c ON cs.class_id = c.id
         LEFT JOIN trades t ON cs.trade_id = t.id
         WHERE tp.id = ?`,
        [practicalId]
      );
    } else {
      // Teachers can only edit their own practicals
      [practicals] = await db.query(
        `SELECT tp.*, CONCAT(c.grade, ' ', t.name, ' ', c.section) as class_name, cs.section, t.name as trade_name
         FROM teacher_practicals tp
         JOIN class_sections cs ON tp.class_section_id = cs.id
         JOIN classes c ON cs.class_id = c.id
         LEFT JOIN trades t ON cs.trade_id = t.id
         WHERE tp.id = ? AND tp.teacher_id = ?`,
        [practicalId, teacherId]
      );
    }

    if (practicals.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Practical not found',
        error: { status: 404, stack: 'Practical not found or you do not have permission to edit it.' },
        layout: 'teacher'
      });
    }

    const practical = practicals[0];

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/edit-practical', {
      title: 'Edit Practical',
      layout: 'teacher',
      pageTitle: 'Edit Practical',
      currentPage: 'practicals',
      practical,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practical for editing:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practical for editing',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get practical student records
exports.getPracticalStudentRecords = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    console.log('Session in getPracticalStudentRecords:', req.session);
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      console.log('Not logged in or not a teacher/admin, redirecting to login');
      return res.redirect('/login');
    }

    console.log('User is logged in as:', req.session.userRole, 'User ID:', req.session.userId);

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for practicals:', teacherId);
    }

    const practicalId = req.params.id;

    // Get practical details
    console.log('Getting practical details for ID:', practicalId, 'Teacher ID:', teacherId, 'Is Admin:', isAdmin);
    let practicals;
    if (isAdmin) {
      // Admins can view any practical's student records
      [practicals] = await db.query(
        `SELECT * FROM teacher_practicals WHERE id = ?`,
        [practicalId]
      );
      console.log('Admin query result:', practicals);
    } else {
      // Teachers can only view their own practicals' student records
      [practicals] = await db.query(
        `SELECT * FROM teacher_practicals WHERE id = ? AND teacher_id = ?`,
        [practicalId, teacherId]
      );
      console.log('Teacher query result:', practicals);
    }

    if (practicals.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Practical not found',
        error: { status: 404, stack: 'Practical not found or you do not have permission to view its records.' },
        layout: 'teacher'
      });
    }

    const practical = practicals[0];

    // Get student records for this practical
    console.log('Getting student records for practical ID:', practicalId);
    const [studentRecords] = await db.query(
      `SELECT spr.*, u.username as student_name
       FROM student_practical_records spr
       JOIN users u ON spr.student_id = u.id
       WHERE spr.practical_id = ?`,
      [practicalId]
    );
    console.log('Student records found:', studentRecords.length);

    res.render('teacher/practical-student-records', {
      title: 'Practical Student Records',
      layout: 'teacher',
      pageTitle: 'Practical Student Records',
      currentPage: 'practicals',
      practical,
      studentRecords,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practical student records:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practical student records',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Schedule new practical page
exports.getSchedulePractical = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for scheduling practicals:', teacherId);
    }

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/schedule-practical', {
      title: 'Schedule Practical',
      layout: 'teacher',
      pageTitle: 'Schedule New Practical',
      currentPage: 'practicals',
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error loading schedule practical form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading schedule practical form',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Lecture management
exports.getLectures = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for lectures:', teacherId);
    }

    // Get lectures
    const [lectures] = await db.query(
      `SELECT * FROM teacher_lectures
       WHERE teacher_id = ?
       ORDER BY date ASC, start_time ASC`,
      [teacherId]
    );

    // Get class sections for the form
    const classSections = await classUtils.getAllClassSections();

    res.render('teacher/lectures', {
      title: 'Lecture Management',
      layout: 'teacher',
      pageTitle: 'Lecture Management',
      currentPage: 'lectures',
      lectures,
      classSections,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching lecture data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading lectures',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Student practical records
exports.getPracticalRecords = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for practical records:', teacherId);
    }

    // Get student practical records
    const [studentRecords] = await db.query(
      `SELECT r.*, u.username as student_name
       FROM student_practical_records r
       JOIN users u ON r.student_id = u.id
       WHERE r.teacher_id = ?
       ORDER BY r.submission_date DESC, r.status, u.username`,
      [teacherId]
    );

    res.render('teacher/practical-records', {
      title: 'Student Practical Records',
      layout: 'teacher',
      pageTitle: 'Student Practical Records',
      currentPage: 'practical-records',
      studentRecords,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching practical records:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading practical records',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Lecture reports
exports.getLectureReports = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // If admin, use the admin's ID directly since lectures are assigned to admin
    if (isAdmin) {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for lectures:', teacherId);
    }

    // Get lecture statistics
    const [lecturesBySubject] = await db.query(
      `SELECT
         subject_name,
         COUNT(*) as total_lectures,
         COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
         COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
         COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
       FROM teacher_lectures
       WHERE teacher_id = ?
       GROUP BY subject_name`,
      [teacherId]
    );

    // Get lecture history
    const [lectureHistory] = await db.query(
      `SELECT
         DATE_FORMAT(date, '%Y-%m') as month,
         COUNT(*) as total_lectures
       FROM teacher_lectures
       WHERE teacher_id = ?
       GROUP BY month
       ORDER BY month DESC
       LIMIT 6`,
      [teacherId]
    );

    res.render('teacher/lecture-reports', {
      title: 'Lecture Reports',
      layout: 'teacher',
      pageTitle: 'Lecture Reports',
      currentPage: 'reports',
      lecturesBySubject,
      lectureHistory,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching lecture reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading lecture reports',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Add a new lecture
exports.addLecture = async (req, res) => {
  try {
    // Allow admins to use their own ID for API calls
    let teacherId = req.session.userId;

    if (req.session.userRole === 'admin') {
      // Keep using the admin's ID (teacherId = req.session.userId)
      console.log('Admin using their own ID for adding lectures:', teacherId);
    }
    const { date, class_name, start_time, end_time, subject_name, topic, notes } = req.body;

    // Validate input
    if (!date || !class_name || !start_time || !end_time || !subject_name || !topic) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Insert lecture
    const [result] = await db.query(
      `INSERT INTO teacher_lectures
       (teacher_id, date, class_name, start_time, end_time, subject_name, topic, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [teacherId, date, class_name, start_time, end_time, subject_name, topic, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Lecture added successfully',
      lectureId: result.insertId
    });
  } catch (error) {
    console.error('Error adding lecture:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding lecture'
    });
  }
};

// Update lecture status
exports.updateLectureStatus = async (req, res) => {
  try {
    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }
    const lectureId = req.params.id;
    const { status } = req.body;

    // Validate status
    if (!['pending', 'delivered', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    // Update lecture status
    let result;
    if (isAdmin) {
      // Admins can update any lecture
      [result] = await db.query(
        `UPDATE teacher_lectures
         SET status = ?
         WHERE id = ?`,
        [status, lectureId]
      );
    } else {
      // Teachers can only update their own lectures
      [result] = await db.query(
        `UPDATE teacher_lectures
         SET status = ?
         WHERE id = ? AND teacher_id = ?`,
        [status, lectureId, teacherId]
      );
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Lecture not found or you do not have permission to update it'
      });
    }

    res.json({
      success: true,
      message: `Lecture marked as ${status} successfully`
    });
  } catch (error) {
    console.error('Error updating lecture status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating lecture status'
    });
  }
};

// Grade a student practical record
exports.gradePracticalRecord = async (req, res) => {
  try {
    // Allow admins to use the first teacher's ID for API calls
    let teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';

    if (isAdmin) {
      const [teachers] = await db.query(
        `SELECT id FROM users WHERE role = 'teacher' LIMIT 1`
      );

      if (teachers.length > 0) {
        teacherId = teachers[0].id;
        console.log('Admin using teacher ID for API call:', teacherId);
      }
    }
    const recordId = req.params.id;
    const { grade, feedback } = req.body;

    // Validate input
    if (!grade) {
      return res.status(400).json({
        success: false,
        message: 'Grade is required'
      });
    }

    // Get record details before updating
    const [recordDetails] = await db.query(
      `SELECT spr.*, tp.practical_topic, u.full_name as teacher_name
       FROM student_practical_records spr
       JOIN teacher_practicals tp ON spr.practical_id = tp.id
       JOIN users u ON tp.teacher_id = u.id
       WHERE spr.id = ?`,
      [recordId]
    );

    if (recordDetails.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Record not found'
      });
    }

    const record = recordDetails[0];
    const studentId = record.student_id;
    const practicalTopic = record.practical_topic;

    // Update record
    let result;
    if (isAdmin) {
      // Admins can update any record
      [result] = await db.query(
        `UPDATE student_practical_records
         SET grade = ?, feedback = ?, status = 'graded'
         WHERE id = ?`,
        [grade, feedback, recordId]
      );
    } else {
      // Teachers can only update their own records
      [result] = await db.query(
        `UPDATE student_practical_records
         SET grade = ?, feedback = ?, status = 'graded'
         WHERE id = ? AND teacher_id = ?`,
        [grade, feedback, recordId, teacherId]
      );
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Record not found or you do not have permission to update it'
      });
    }

    // Create notification for the student
    try {
      const teacherName = record.teacher_name || 'Your teacher';
      const notificationTitle = `Practical Graded: ${practicalTopic}`;
      const notificationMessage = `${teacherName} has graded your practical work for "${practicalTopic}". Your grade: ${grade}`;

      await db.query(
        `INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
         VALUES (?, ?, ?, 'practical', ?, 0, NOW())`,
        [studentId, notificationTitle, notificationMessage, `/student/practicals/${record.practical_id}`]
      );

      console.log(`Notification sent to student ${studentId} for graded practical`);
    } catch (notificationError) {
      console.error('Error sending notification:', notificationError);
      // Continue with the response even if notification fails
    }

    res.json({
      success: true,
      message: 'Practical record graded successfully'
    });
  } catch (error) {
    console.error('Error grading practical record:', error);
    res.status(500).json({
      success: false,
      message: 'Error grading practical record'
    });
  }
};

// Mark Lecture page
exports.getMarkLecture = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // Get pending lectures
    const [pendingLectures] = await db.query(
      `SELECT * FROM teacher_lectures
       WHERE teacher_id = ? AND status = 'pending'
       ORDER BY date ASC, start_time ASC`,
      [teacherId]
    );

    res.render('teacher/mark-lecture', {
      title: 'Mark Lecture',
      layout: 'teacher',
      pageTitle: 'Mark Lecture',
      currentPage: 'lectures',
      pendingLectures,
      isAdmin
    });
  } catch (error) {
    console.error('Error loading mark lecture page:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load mark lecture page',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Update Syllabus page
exports.getUpdateSyllabus = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // Get subjects taught by this teacher
    const [subjects] = await db.query(
      `SELECT DISTINCT subject_name FROM teacher_lectures
       WHERE teacher_id = ?
       ORDER BY subject_name`,
      [teacherId]
    );

    res.render('teacher/update-syllabus', {
      title: 'Update Syllabus',
      layout: 'teacher',
      pageTitle: 'Update Syllabus',
      currentPage: 'syllabus',
      subjects,
      isAdmin
    });
  } catch (error) {
    console.error('Error loading update syllabus page:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load update syllabus page',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Export Reports page
exports.getExportReports = async (req, res) => {
  try {
    // Give admin users full access to teacher functionality
    const isAdmin = req.session.userRole === 'admin';
    let teacherId = req.session.userId;

    // If not a teacher or admin, redirect to login
    if (req.session.userRole !== 'teacher' && !isAdmin) {
      return res.redirect('/login');
    }

    // Get report types
    const reportTypes = [
      { id: 'lectures', name: 'Lecture Reports' },
      { id: 'practicals', name: 'Practical Reports' },
      { id: 'syllabus', name: 'Syllabus Progress' },
      { id: 'students', name: 'Student Performance' }
    ];

    res.render('teacher/export-reports', {
      title: 'Export Reports',
      layout: 'teacher',
      pageTitle: 'Export Reports',
      currentPage: 'reports',
      reportTypes,
      isAdmin
    });
  } catch (error) {
    console.error('Error loading export reports page:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load export reports page',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};