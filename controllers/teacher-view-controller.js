/**
 * Controller for teacher view functionality
 * Handles routes related to teacher subject eligibility, class assignments, 
 * lecture schedules, and instruction plans
 */

const db = require('../config/database');
const { formatDate, formatTime } = require('../utils/date-utils');

// Get teacher subject eligibility
exports.getTeacherSubjectEligibility = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';
    
    // Get teacher's subject eligibility
    const [eligibility] = await db.query(`
      SELECT tse.id, tse.teacher_id, u.name AS teacher_name, u.full_name AS teacher_full_name,
             s.id AS subject_id, s.name AS subject_name, s.code AS subject_code,
             s.max_theory_lectures, s.max_practical_lectures, s.total_lectures_per_week,
             tse.is_active
      FROM teacher_subject_eligibility tse
      JOIN users u ON tse.teacher_id = u.id
      JOIN subjects s ON tse.subject_id = s.id
      WHERE tse.teacher_id = ?
      ORDER BY s.name
    `, [teacherId]);
    
    // Get all subjects for the form
    const [subjects] = await db.query(`
      SELECT id, name, code, description, max_theory_lectures, max_practical_lectures, total_lectures_per_week
      FROM subjects
      ORDER BY name
    `);
    
    res.render('teacher/subject-eligibility', {
      title: 'Subject Eligibility',
      layout: 'teacher',
      pageTitle: 'My Subject Eligibility',
      currentPage: 'subject-eligibility',
      eligibility,
      subjects,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching teacher subject eligibility:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load subject eligibility',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get class assignments
exports.getClassAssignments = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';
    
    // Get teacher's class assignments
    const [assignments] = await db.query(`
      SELECT tscv.eligibility_id, tscv.teacher_id, tscv.teacher_name, tscv.teacher_full_name,
             tscv.subject_id, tscv.subject_name, tscv.subject_code,
             tscv.assignment_id, tscv.class_id, tscv.class_name,
             tscv.grade, tscv.trade, tscv.section,
             tscv.num_theory_lectures, tscv.num_practical_lectures, tscv.total_lectures
      FROM teacher_subject_class_view tscv
      WHERE tscv.teacher_id = ?
      ORDER BY tscv.class_name, tscv.subject_name
    `, [teacherId]);
    
    // Get all classes for the form
    const [classes] = await db.query(`
      SELECT id, name, description, grade, trade, section, academic_year, board, max_capacity
      FROM classes
      ORDER BY name
    `);
    
    // Get all subjects for the form
    const [subjects] = await db.query(`
      SELECT id, name, code, description, max_theory_lectures, max_practical_lectures, total_lectures_per_week
      FROM subjects
      ORDER BY name
    `);
    
    res.render('teacher/class-assignments', {
      title: 'Class Assignments',
      layout: 'teacher',
      pageTitle: 'My Class Assignments',
      currentPage: 'class-assignments',
      assignments,
      classes,
      subjects,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching teacher class assignments:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load class assignments',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get lecture schedules
exports.getLectureSchedules = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';
    
    // Get teacher's lecture schedules
    const [schedules] = await db.query(`
      SELECT ls.id, ls.assignment_id, ls.teacher_id, u.name AS teacher_name, u.full_name AS teacher_full_name,
             ls.day_of_week, ls.start_time, ls.end_time, ls.classroom, ls.semester,
             sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section,
             ls.is_active
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      JOIN users u ON ls.teacher_id = u.id
      WHERE ls.teacher_id = ?
      ORDER BY FIELD(ls.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
               ls.start_time
    `, [teacherId]);
    
    // Format times for display
    schedules.forEach(schedule => {
      schedule.start_time_formatted = formatTime(schedule.start_time);
      schedule.end_time_formatted = formatTime(schedule.end_time);
    });
    
    // Get all assignments for the form
    const [assignments] = await db.query(`
      SELECT sca.id, sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section,
             sca.num_theory_lectures, sca.num_practical_lectures
      FROM subject_class_assignment sca
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      JOIN teacher_subject_eligibility tse ON tse.subject_id = sca.subject_id
      WHERE tse.teacher_id = ?
      ORDER BY c.name, s.name
    `, [teacherId]);
    
    res.render('teacher/lecture-schedules', {
      title: 'Lecture Schedules',
      layout: 'teacher',
      pageTitle: 'My Lecture Schedules',
      currentPage: 'lecture-schedules',
      schedules,
      assignments,
      isAdmin,
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    });
  } catch (error) {
    console.error('Error fetching teacher lecture schedules:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load lecture schedules',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get instruction plans
exports.getInstructionPlans = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    const isAdmin = req.session.userRole === 'admin';
    
    // Get teacher's instruction plans
    const [plans] = await db.query(`
      SELECT dip.id, dip.schedule_id, dip.date, dip.topic, dip.objectives, 
             dip.material_support, dip.activities, dip.homework, dip.notes,
             dip.topic_completion_percentage, dip.is_active,
             ls.day_of_week, ls.start_time, ls.end_time, ls.classroom,
             sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section
      FROM daily_instruction_plan dip
      JOIN lecture_schedule ls ON dip.schedule_id = ls.id
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = ?
      ORDER BY dip.date DESC, ls.start_time
    `, [teacherId]);
    
    // Format dates for display
    plans.forEach(plan => {
      plan.date_formatted = formatDate(plan.date);
      plan.start_time_formatted = formatTime(plan.start_time);
      plan.end_time_formatted = formatTime(plan.end_time);
    });
    
    // Get all schedules for the form
    const [schedules] = await db.query(`
      SELECT ls.id, ls.day_of_week, ls.start_time, ls.end_time, ls.classroom,
             sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = ?
      ORDER BY FIELD(ls.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
               ls.start_time
    `, [teacherId]);
    
    // Format times for display
    schedules.forEach(schedule => {
      schedule.start_time_formatted = formatTime(schedule.start_time);
      schedule.end_time_formatted = formatTime(schedule.end_time);
      schedule.display_text = `${schedule.day_of_week} ${schedule.start_time_formatted}-${schedule.end_time_formatted} | ${schedule.subject_name} | ${schedule.class_name}`;
    });
    
    res.render('teacher/instruction-plans', {
      title: 'Instruction Plans',
      layout: 'teacher',
      pageTitle: 'My Instruction Plans',
      currentPage: 'instruction-plans',
      plans,
      schedules,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching teacher instruction plans:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load instruction plans',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// Get holiday calendar
exports.getHolidayCalendar = async (req, res) => {
  try {
    const isAdmin = req.session.userRole === 'admin';
    
    // Get all holidays
    const [holidays] = await db.query(`
      SELECT id, holiday_date, description, holiday_type, is_active
      FROM holiday_calendar
      ORDER BY holiday_date
    `);
    
    // Format dates for display
    holidays.forEach(holiday => {
      holiday.date_formatted = formatDate(holiday.holiday_date);
    });
    
    res.render('teacher/holiday-calendar', {
      title: 'Holiday Calendar',
      layout: 'teacher',
      pageTitle: 'Holiday Calendar',
      currentPage: 'holiday-calendar',
      holidays,
      isAdmin
    });
  } catch (error) {
    console.error('Error fetching holiday calendar:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load holiday calendar',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
};

// API endpoints for teacher view

// Get teacher subject eligibility
exports.getTeacherSubjectEligibilityAPI = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    
    // Get teacher's subject eligibility
    const [eligibility] = await db.query(`
      SELECT tse.id, tse.teacher_id, u.name AS teacher_name, u.full_name AS teacher_full_name,
             s.id AS subject_id, s.name AS subject_name, s.code AS subject_code,
             s.max_theory_lectures, s.max_practical_lectures, s.total_lectures_per_week,
             tse.is_active
      FROM teacher_subject_eligibility tse
      JOIN users u ON tse.teacher_id = u.id
      JOIN subjects s ON tse.subject_id = s.id
      WHERE tse.teacher_id = ?
      ORDER BY s.name
    `, [teacherId]);
    
    res.json({
      success: true,
      data: eligibility
    });
  } catch (error) {
    console.error('Error fetching teacher subject eligibility:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load subject eligibility',
      error: error.message
    });
  }
};

// Add teacher subject eligibility
exports.addTeacherSubjectEligibilityAPI = async (req, res) => {
  try {
    const { subject_id } = req.body;
    const teacher_id = req.session.userId;
    
    // Validate input
    if (!subject_id) {
      return res.status(400).json({
        success: false,
        message: 'Subject ID is required'
      });
    }
    
    // Check if eligibility already exists
    const [existing] = await db.query(`
      SELECT id FROM teacher_subject_eligibility
      WHERE teacher_id = ? AND subject_id = ?
    `, [teacher_id, subject_id]);
    
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Teacher is already eligible for this subject'
      });
    }
    
    // Add eligibility
    await db.query(`
      INSERT INTO teacher_subject_eligibility (teacher_id, subject_id)
      VALUES (?, ?)
    `, [teacher_id, subject_id]);
    
    res.json({
      success: true,
      message: 'Subject eligibility added successfully'
    });
  } catch (error) {
    console.error('Error adding teacher subject eligibility:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add subject eligibility',
      error: error.message
    });
  }
};

// Update teacher subject eligibility
exports.updateTeacherSubjectEligibilityAPI = async (req, res) => {
  try {
    const { id, is_active } = req.body;
    
    // Validate input
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Eligibility ID is required'
      });
    }
    
    // Update eligibility
    await db.query(`
      UPDATE teacher_subject_eligibility
      SET is_active = ?
      WHERE id = ?
    `, [is_active ? 1 : 0, id]);
    
    res.json({
      success: true,
      message: 'Subject eligibility updated successfully'
    });
  } catch (error) {
    console.error('Error updating teacher subject eligibility:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subject eligibility',
      error: error.message
    });
  }
};

// Get lecture schedules API
exports.getLectureSchedulesAPI = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    
    // Get teacher's lecture schedules
    const [schedules] = await db.query(`
      SELECT ls.id, ls.assignment_id, ls.teacher_id, u.name AS teacher_name, u.full_name AS teacher_full_name,
             ls.day_of_week, ls.start_time, ls.end_time, ls.classroom, ls.semester,
             sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section,
             ls.is_active
      FROM lecture_schedule ls
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      JOIN users u ON ls.teacher_id = u.id
      WHERE ls.teacher_id = ?
      ORDER BY FIELD(ls.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
               ls.start_time
    `, [teacherId]);
    
    // Format times for display
    schedules.forEach(schedule => {
      schedule.start_time_formatted = formatTime(schedule.start_time);
      schedule.end_time_formatted = formatTime(schedule.end_time);
    });
    
    res.json({
      success: true,
      data: schedules
    });
  } catch (error) {
    console.error('Error fetching teacher lecture schedules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load lecture schedules',
      error: error.message
    });
  }
};

// Add lecture schedule API
exports.addLectureScheduleAPI = async (req, res) => {
  try {
    const { assignment_id, day_of_week, start_time, end_time, classroom, semester } = req.body;
    const teacher_id = req.session.userId;
    
    // Validate input
    if (!assignment_id || !day_of_week || !start_time || !end_time) {
      return res.status(400).json({
        success: false,
        message: 'Assignment ID, day of week, start time, and end time are required'
      });
    }
    
    // Add schedule
    await db.query(`
      INSERT INTO lecture_schedule (assignment_id, teacher_id, day_of_week, start_time, end_time, classroom, semester)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [assignment_id, teacher_id, day_of_week, start_time, end_time, classroom || 'Classroom 1', semester || 'First']);
    
    res.json({
      success: true,
      message: 'Lecture schedule added successfully'
    });
  } catch (error) {
    console.error('Error adding lecture schedule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add lecture schedule',
      error: error.message
    });
  }
};

// Update lecture schedule API
exports.updateLectureScheduleAPI = async (req, res) => {
  try {
    const { id, day_of_week, start_time, end_time, classroom, semester, is_active } = req.body;
    
    // Validate input
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Schedule ID is required'
      });
    }
    
    // Build update query
    let updateQuery = 'UPDATE lecture_schedule SET ';
    const updateParams = [];
    
    if (day_of_week) {
      updateQuery += 'day_of_week = ?, ';
      updateParams.push(day_of_week);
    }
    
    if (start_time) {
      updateQuery += 'start_time = ?, ';
      updateParams.push(start_time);
    }
    
    if (end_time) {
      updateQuery += 'end_time = ?, ';
      updateParams.push(end_time);
    }
    
    if (classroom) {
      updateQuery += 'classroom = ?, ';
      updateParams.push(classroom);
    }
    
    if (semester) {
      updateQuery += 'semester = ?, ';
      updateParams.push(semester);
    }
    
    if (is_active !== undefined) {
      updateQuery += 'is_active = ?, ';
      updateParams.push(is_active ? 1 : 0);
    }
    
    // Remove trailing comma and space
    updateQuery = updateQuery.slice(0, -2);
    
    // Add WHERE clause
    updateQuery += ' WHERE id = ?';
    updateParams.push(id);
    
    // Update schedule
    await db.query(updateQuery, updateParams);
    
    res.json({
      success: true,
      message: 'Lecture schedule updated successfully'
    });
  } catch (error) {
    console.error('Error updating lecture schedule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update lecture schedule',
      error: error.message
    });
  }
};

// Get instruction plans API
exports.getInstructionPlansAPI = async (req, res) => {
  try {
    const teacherId = req.session.userId;
    
    // Get teacher's instruction plans
    const [plans] = await db.query(`
      SELECT dip.id, dip.schedule_id, dip.date, dip.topic, dip.objectives, 
             dip.material_support, dip.activities, dip.homework, dip.notes,
             dip.topic_completion_percentage, dip.is_active,
             ls.day_of_week, ls.start_time, ls.end_time, ls.classroom,
             sca.subject_id, s.name AS subject_name, s.code AS subject_code,
             sca.class_id, c.name AS class_name, c.grade, c.trade, c.section
      FROM daily_instruction_plan dip
      JOIN lecture_schedule ls ON dip.schedule_id = ls.id
      JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
      JOIN subjects s ON sca.subject_id = s.id
      JOIN classes c ON sca.class_id = c.id
      WHERE ls.teacher_id = ?
      ORDER BY dip.date DESC, ls.start_time
    `, [teacherId]);
    
    // Format dates and times for display
    plans.forEach(plan => {
      plan.date_formatted = formatDate(plan.date);
      plan.start_time_formatted = formatTime(plan.start_time);
      plan.end_time_formatted = formatTime(plan.end_time);
    });
    
    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Error fetching teacher instruction plans:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load instruction plans',
      error: error.message
    });
  }
};

// Add instruction plan API
exports.addInstructionPlanAPI = async (req, res) => {
  try {
    const { schedule_id, date, topic, objectives, material_support, activities, homework, notes } = req.body;
    
    // Validate input
    if (!schedule_id || !date || !topic) {
      return res.status(400).json({
        success: false,
        message: 'Schedule ID, date, and topic are required'
      });
    }
    
    // Add instruction plan
    await db.query(`
      INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [schedule_id, date, topic, objectives || null, material_support || null, activities || null, homework || null, notes || null]);
    
    res.json({
      success: true,
      message: 'Instruction plan added successfully'
    });
  } catch (error) {
    console.error('Error adding instruction plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add instruction plan',
      error: error.message
    });
  }
};

// Update instruction plan API
exports.updateInstructionPlanAPI = async (req, res) => {
  try {
    const { id, topic, objectives, material_support, activities, homework, notes, topic_completion_percentage, is_active } = req.body;
    
    // Validate input
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID is required'
      });
    }
    
    // Build update query
    let updateQuery = 'UPDATE daily_instruction_plan SET ';
    const updateParams = [];
    
    if (topic) {
      updateQuery += 'topic = ?, ';
      updateParams.push(topic);
    }
    
    if (objectives !== undefined) {
      updateQuery += 'objectives = ?, ';
      updateParams.push(objectives);
    }
    
    if (material_support !== undefined) {
      updateQuery += 'material_support = ?, ';
      updateParams.push(material_support);
    }
    
    if (activities !== undefined) {
      updateQuery += 'activities = ?, ';
      updateParams.push(activities);
    }
    
    if (homework !== undefined) {
      updateQuery += 'homework = ?, ';
      updateParams.push(homework);
    }
    
    if (notes !== undefined) {
      updateQuery += 'notes = ?, ';
      updateParams.push(notes);
    }
    
    if (topic_completion_percentage !== undefined) {
      updateQuery += 'topic_completion_percentage = ?, ';
      updateParams.push(topic_completion_percentage);
    }
    
    if (is_active !== undefined) {
      updateQuery += 'is_active = ?, ';
      updateParams.push(is_active ? 1 : 0);
    }
    
    // Remove trailing comma and space
    updateQuery = updateQuery.slice(0, -2);
    
    // Add WHERE clause
    updateQuery += ' WHERE id = ?';
    updateParams.push(id);
    
    // Update instruction plan
    await db.query(updateQuery, updateParams);
    
    res.json({
      success: true,
      message: 'Instruction plan updated successfully'
    });
  } catch (error) {
    console.error('Error updating instruction plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update instruction plan',
      error: error.message
    });
  }
};

// Get holiday calendar API
exports.getHolidayCalendarAPI = async (req, res) => {
  try {
    // Get all holidays
    const [holidays] = await db.query(`
      SELECT id, holiday_date, description, holiday_type, is_active
      FROM holiday_calendar
      ORDER BY holiday_date
    `);
    
    // Format dates for display
    holidays.forEach(holiday => {
      holiday.date_formatted = formatDate(holiday.holiday_date);
    });
    
    res.json({
      success: true,
      data: holidays
    });
  } catch (error) {
    console.error('Error fetching holiday calendar:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load holiday calendar',
      error: error.message
    });
  }
};

// Add holiday API
exports.addHolidayAPI = async (req, res) => {
  try {
    const { holiday_date, description, holiday_type } = req.body;
    
    // Validate input
    if (!holiday_date || !description) {
      return res.status(400).json({
        success: false,
        message: 'Holiday date and description are required'
      });
    }
    
    // Check if holiday already exists
    const [existing] = await db.query(`
      SELECT id FROM holiday_calendar
      WHERE holiday_date = ?
    `, [holiday_date]);
    
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'A holiday already exists on this date'
      });
    }
    
    // Add holiday
    await db.query(`
      INSERT INTO holiday_calendar (holiday_date, description, holiday_type)
      VALUES (?, ?, ?)
    `, [holiday_date, description, holiday_type || 'Public Holiday']);
    
    res.json({
      success: true,
      message: 'Holiday added successfully'
    });
  } catch (error) {
    console.error('Error adding holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add holiday',
      error: error.message
    });
  }
};

// Update holiday API
exports.updateHolidayAPI = async (req, res) => {
  try {
    const { id, description, holiday_type, is_active } = req.body;
    
    // Validate input
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Holiday ID is required'
      });
    }
    
    // Build update query
    let updateQuery = 'UPDATE holiday_calendar SET ';
    const updateParams = [];
    
    if (description) {
      updateQuery += 'description = ?, ';
      updateParams.push(description);
    }
    
    if (holiday_type) {
      updateQuery += 'holiday_type = ?, ';
      updateParams.push(holiday_type);
    }
    
    if (is_active !== undefined) {
      updateQuery += 'is_active = ?, ';
      updateParams.push(is_active ? 1 : 0);
    }
    
    // Remove trailing comma and space
    updateQuery = updateQuery.slice(0, -2);
    
    // Add WHERE clause
    updateQuery += ' WHERE id = ?';
    updateParams.push(id);
    
    // Update holiday
    await db.query(updateQuery, updateParams);
    
    res.json({
      success: true,
      message: 'Holiday updated successfully'
    });
  } catch (error) {
    console.error('Error updating holiday:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update holiday',
      error: error.message
    });
  }
};
