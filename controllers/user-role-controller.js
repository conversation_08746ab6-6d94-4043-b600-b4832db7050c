const db = require('../config/database');

// Get all users with their roles
exports.getUsers = async (req, res) => {
  try {
    // Get query parameters
    const query = {
      search: req.query.search || '',
      role: req.query.role || '',
      status: req.query.status || '',
      sort: req.query.sort || 'created_desc',
      fromDate: req.query.fromDate || '',
      toDate: req.query.toDate || ''
    };

    // Build the WHERE clause
    let whereClause = 'u.is_deleted = 0';
    let queryParams = [];

    if (query.search) {
      whereClause += ' AND (u.username LIKE ? OR u.email LIKE ?)';
      queryParams.push(`%${query.search}%`, `%${query.search}%`);
    }

    if (query.role) {
      whereClause += ' AND u.role = ?';
      queryParams.push(query.role);
    }

    // Status filtering will be handled differently for online/offline status
    // since it requires a subquery
    let statusFilter = '';
    if (query.status === 'blocked') {
      whereClause += ' AND u.is_blocked = 1';
    } else if (query.status === 'new') {
      // New users (registered within 48 hours)
      whereClause += ' AND u.created_at >= DATE_SUB(NOW(), INTERVAL 48 HOUR)';
    }

    if (query.fromDate) {
      whereClause += ' AND u.created_at >= ?';
      queryParams.push(query.fromDate);
    }

    if (query.toDate) {
      whereClause += ' AND u.created_at <= ?';
      queryParams.push(query.toDate + ' 23:59:59');
    }

    // Determine sort order
    let orderBy = 'u.created_at DESC';
    if (query.sort === 'created_asc') {
      orderBy = 'u.created_at ASC';
    } else if (query.sort === 'username_asc') {
      orderBy = 'u.username ASC';
    } else if (query.sort === 'username_desc') {
      orderBy = 'u.username DESC';
    }

    // Get total count for pagination with status filter
    let countQuery;
    if (query.status === 'online' || query.status === 'offline') {
      countQuery = `
        SELECT COUNT(*) as total FROM (
          SELECT u.id,
                 (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
          FROM users u
          WHERE ${whereClause}
          ${query.status === 'online' ? 'HAVING is_online = 1' : ''}
          ${query.status === 'offline' ? 'HAVING is_online = 0' : ''}
        ) as filtered_users
      `;
    } else {
      countQuery = `SELECT COUNT(*) as total FROM users u WHERE ${whereClause}`;
    }

    const [countResult] = await db.query(countQuery, queryParams);
    const totalUsers = countResult[0].total;

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = 10; // Users per page
    const offset = (page - 1) * limit;
    const totalPages = Math.ceil(totalUsers / limit);

    // Get users with pagination and online status
    let mainQuery;
    if (query.status === 'online' || query.status === 'offline') {
      // For online/offline status, we need to use a subquery with HAVING
      mainQuery = `
        SELECT u.id, u.username, u.email, u.role, u.is_active, u.is_blocked,
               u.last_login, u.created_at, u.profile_image,
               (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
        FROM users u
        WHERE ${whereClause}
        HAVING ${query.status === 'online' ? 'is_online = 1' : 'is_online = 0'}
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `;
    } else {
      // For other queries, include the online status but don't filter by it
      mainQuery = `
        SELECT u.id, u.username, u.email, u.role, u.is_active, u.is_blocked,
               u.last_login, u.created_at, u.profile_image,
               (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
        FROM users u
        WHERE ${whereClause}
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `;
    }

    const [users] = await db.query(mainQuery, [...queryParams, limit, offset]);

    // Import date formatter functions
    const { formatDate, formatDateTime } = require('../utils/date-formatter');

    res.render('admin/users/index', {
      title: 'User Management',
      pageTitle: 'Manage Users',
      layout: 'admin',
      currentPage: 'users',
      users,
      query, // Pass the query object
      pagination: {
        currentPage: page,
        totalPages,
        totalUsers,
        limit
      },
      formatDate, // Explicitly pass the date formatter function
      formatDateTime, // Explicitly pass the date-time formatter function
      currentUserId: req.session.userId // Explicitly pass the current user's ID
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch users',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Get user role assignment form
exports.getAssignRole = async (req, res) => {
  try {
    const userId = req.params.id;

    // Get user details
    const [users] = await db.query(
      'SELECT id, username, email, role FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      req.session.flashError = 'User not found';
      return res.redirect('/admin/users');
    }

    // Get available roles
    const [roles] = await db.query(
      'SELECT role_id, role_name, description FROM roles ORDER BY role_name'
    );

    res.render('admin/users/assign-role', {
      title: 'Assign Role',
      layout: 'admin',
      currentPage: 'users',
      user: users[0],
      roles,
      currentRole: users[0].role
    });
  } catch (error) {
    console.error('Error fetching user role data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch user role data',
      error: { status: 500, stack: error.stack },
      layout: 'admin'
    });
  }
};

// Update user role
exports.updateRole = async (req, res) => {
  try {
    const userId = req.params.id;
    const { role } = req.body;

    // Validate input
    if (!role) {
      return res.status(400).json({
        success: false,
        message: 'Role is required'
      });
    }

    // Check if user exists
    const [users] = await db.query(
      'SELECT id FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update user role
    await db.query(
      'UPDATE users SET role = ? WHERE id = ?',
      [role, userId]
    );

    // Log the role change
    await db.query(
      `INSERT INTO activity_log (user_id, action, details, ip_address)
       VALUES (?, 'role_updated', ?, ?)`,
      [
        req.session.userId,
        JSON.stringify({ userId, oldRole: users[0].role, newRole: role }),
        req.ip
      ]
    );

    res.json({
      success: true,
      message: 'User role updated successfully'
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user role'
    });
  }
};
