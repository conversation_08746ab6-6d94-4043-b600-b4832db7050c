const db = require('./config/database');

async function createClassTables() {
  try {
    // Create classes table
    await db.query(`
      CREATE TABLE IF NOT EXISTS classes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(10) NOT NULL,
        description VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (name)
      )
    `);
    console.log('Classes table created or already exists');
    
    // Create trades table
    await db.query(`
      CREATE TABLE IF NOT EXISTS trades (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (name)
      )
    `);
    console.log('Trades table created or already exists');
    
    // Create class_sections table
    await db.query(`
      CREATE TABLE IF NOT EXISTS class_sections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        trade_id INT,
        section VARCHAR(10) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id),
        FOREIGN KEY (trade_id) REFERENCES trades(id),
        UNIQUE KEY (class_id, trade_id, section)
      )
    `);
    console.log('Class sections table created or already exists');
    
    // Insert default classes
    await db.query(`
      INSERT IGNORE INTO classes (name, description) VALUES
      ('9', 'Class 9'),
      ('10', 'Class 10'),
      ('11', 'Class 11'),
      ('12', 'Class 12')
    `);
    console.log('Default classes inserted');
    
    // Insert default trades
    await db.query(`
      INSERT IGNORE INTO trades (name, description) VALUES
      ('Medical', 'Medical stream with Biology, Chemistry, Physics'),
      ('Non-Medical', 'Non-Medical stream with Mathematics, Physics, Chemistry'),
      ('Commerce', 'Commerce stream with Business Studies, Accountancy, Economics'),
      ('General', 'General stream for classes 9 and 10')
    `);
    console.log('Default trades inserted');
    
    // Insert default sections
    const [classes] = await db.query('SELECT id, name FROM classes');
    const [trades] = await db.query('SELECT id, name FROM trades');
    
    // For classes 9 and 10, use General trade
    const generalTrade = trades.find(t => t.name === 'General');
    
    for (const cls of classes) {
      if (cls.name === '9' || cls.name === '10') {
        // For classes 9 and 10, create sections A, B, C with General trade
        await db.query(`
          INSERT IGNORE INTO class_sections (class_id, trade_id, section) VALUES
          (?, ?, 'A'),
          (?, ?, 'B'),
          (?, ?, 'C')
        `, [cls.id, generalTrade.id, cls.id, generalTrade.id, cls.id, generalTrade.id]);
      } else {
        // For classes 11 and 12, create sections for each trade
        for (const trade of trades) {
          if (trade.name !== 'General') {
            // Create sections A and B for each trade
            await db.query(`
              INSERT IGNORE INTO class_sections (class_id, trade_id, section) VALUES
              (?, ?, 'A'),
              (?, ?, 'B')
            `, [cls.id, trade.id, cls.id, trade.id]);
          }
        }
      }
    }
    console.log('Default sections inserted');
    
    // Add class_section_id column to users table if it doesn't exist
    const [userClassColumns] = await db.query(`
      SHOW COLUMNS FROM users WHERE Field = 'class_section_id'
    `);
    
    if (userClassColumns.length === 0) {
      await db.query(`
        ALTER TABLE users
        ADD COLUMN class_section_id INT,
        ADD FOREIGN KEY (class_section_id) REFERENCES class_sections(id)
      `);
      console.log('Added class_section_id column to users table');
    } else {
      console.log('class_section_id column already exists in users table');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating class tables:', error);
    process.exit(1);
  }
}

createClassTables();
