const db = require('./config/database');

async function createFillUpQuestions() {
    try {
        console.log('Creating sample fill-up questions...');
        
        // Get a section ID to use
        const [sections] = await db.query(`
            SELECT section_id FROM sections LIMIT 5
        `);
        
        if (sections.length === 0) {
            console.log('No sections found to create sample questions.');
            process.exit(1);
        }
        
        // Sample fill-up questions
        const questions = [
            {
                question_text: 'The largest planet in our solar system is _______.',
                correct_answer: 'Jupiter',
                solution_text: 'Jupiter is the largest planet in our solar system.'
            },
            {
                question_text: 'Water boils at _______ degrees Celsius at standard atmospheric pressure.',
                correct_answer: '100',
                solution_text: 'Water boils at 100 degrees Celsius (212 degrees Fahrenheit) at standard atmospheric pressure.'
            },
            {
                question_text: 'The chemical symbol for gold is _______.',
                correct_answer: 'Au',
                solution_text: 'The chemical symbol for gold is Au, from the Latin word "aurum".'
            },
            {
                question_text: 'The Great Wall of China is approximately _______ kilometers long.',
                correct_answer: '21196',
                solution_text: 'The Great Wall of China is approximately 21,196 kilometers (13,171 miles) long.'
            },
            {
                question_text: 'The author of "<PERSON> and <PERSON>" is _______.',
                correct_answer: '<PERSON> <PERSON>',
                solution_text: 'William Shakespeare wrote "Romeo and Juliet" around 1594-1596.'
            }
        ];
        
        // Insert each question
        for (let i = 0; i < questions.length; i++) {
            const sectionId = sections[i % sections.length].section_id;
            const question = questions[i];
            
            const [result] = await db.query(`
                INSERT INTO questions (
                    section_id, 
                    question_type, 
                    question_text, 
                    correct_answer, 
                    solution_text, 
                    marks
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                sectionId,
                'fill_up',
                question.question_text,
                question.correct_answer,
                question.solution_text,
                1
            ]);
            
            console.log(`Created fill-up question with ID: ${result.insertId}`);
        }
        
        console.log('Successfully created sample fill-up questions!');
        process.exit(0);
    } catch (error) {
        console.error('Error creating fill-up questions:', error);
        process.exit(1);
    }
}

// Run the function
createFillUpQuestions();
