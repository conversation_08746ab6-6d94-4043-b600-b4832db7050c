const db = require('./config/database');

async function createScheduledTest() {
    try {
        console.log('Creating a scheduled test with future publish date...');
        
        // Set publish date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(12, 0, 0, 0); // Set to noon tomorrow
        
        const publishDate = tomorrow.toISOString().slice(0, 19).replace('T', ' ');
        console.log('Publish date:', publishDate);
        
        // Create the test
        const [result] = await db.query(`
            INSERT INTO exams (
                exam_name,
                duration,
                passing_marks,
                status,
                created_by,
                publish_date,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
            'Scheduled Test Demo',
            60, // 60 minutes duration
            40, // 40 passing marks
            'published', // This might be the issue - setting to published even with future date
            1, // Admin user ID
            publishDate
        ]);
        
        const examId = result.insertId;
        console.log(`Created scheduled test with ID: ${examId}`);
        
        // Create a section for the test
        const [sectionResult] = await db.query(`
            INSERT INTO sections (
                exam_id,
                section_name,
                position,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, NOW(), NOW())
        `, [
            examId,
            'General Knowledge',
            1
        ]);
        
        console.log(`Created section with ID: ${sectionResult.insertId}`);
        
        console.log('Successfully created scheduled test!');
        process.exit(0);
    } catch (error) {
        console.error('Error creating scheduled test:', error);
        process.exit(1);
    }
}

// Run the function
createScheduledTest();
