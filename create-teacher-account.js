/**
 * <PERSON><PERSON><PERSON> to create a teacher account with proper credentials
 */

const db = require('./config/database');
const bcrypt = require('bcrypt');

async function createTeacherAccount() {
  try {
    console.log('Creating teacher account...');
    
    // Teacher credentials
    const teacherData = {
      name: 'Computer Teacher',
      username: 'compteacher',
      email: '<EMAIL>',
      password: 'Merit123#',
      role: 'teacher',
      bio: 'Computer Science Teacher'
    };
    
    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(teacherData.password, saltRounds);
    teacherData.password = hashedPassword;
    
    // Check if teacher already exists
    const [existingTeacher] = await db.query(`
      SELECT id FROM users WHERE email = ? OR username = ?
    `, [teacherData.email, teacherData.username]);
    
    let teacherId;
    
    if (existingTeacher.length > 0) {
      teacherId = existingTeacher[0].id;
      
      // Update existing teacher
      await db.query(`
        UPDATE users
        SET name = ?, username = ?, email = ?, password = ?, role = ?, bio = ?
        WHERE id = ?
      `, [
        teacherData.name,
        teacherData.username,
        teacherData.email,
        teacherData.password,
        teacherData.role,
        teacherData.bio,
        teacherId
      ]);
      
      console.log(`Updated existing teacher account with ID: ${teacherId}`);
    } else {
      // Create new teacher account
      const [result] = await db.query(`
        INSERT INTO users (name, username, email, password, role, bio, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        teacherData.name,
        teacherData.username,
        teacherData.email,
        teacherData.password,
        teacherData.role,
        teacherData.bio
      ]);
      
      teacherId = result.insertId;
      console.log(`Created new teacher account with ID: ${teacherId}`);
    }
    
    // Assign the same classes and subjects as the demo teacher
    const [demoTeacher] = await db.query(`
      SELECT id FROM users WHERE email = '<EMAIL>'
    `);
    
    if (demoTeacher.length > 0) {
      const demoTeacherId = demoTeacher[0].id;
      
      // Get subjects assigned to demo teacher
      const [subjects] = await db.query(`
        SELECT subject_id FROM teacher_subjects WHERE teacher_id = ?
      `, [demoTeacherId]);
      
      // Assign subjects to new teacher
      for (const subject of subjects) {
        await db.query(`
          INSERT INTO teacher_subjects (teacher_id, subject_id, created_at, updated_at)
          VALUES (?, ?, NOW(), NOW())
          ON DUPLICATE KEY UPDATE updated_at = NOW()
        `, [teacherId, subject.subject_id]);
        
        console.log(`Assigned subject ID ${subject.subject_id} to teacher`);
      }
      
      // Get classrooms assigned to demo teacher
      const [classrooms] = await db.query(`
        SELECT classroom_id FROM teacher_classes WHERE teacher_id = ?
      `, [demoTeacherId]);
      
      // Assign classrooms to new teacher
      for (const classroom of classrooms) {
        await db.query(`
          INSERT INTO teacher_classes (teacher_id, classroom_id, created_at, updated_at)
          VALUES (?, ?, NOW(), NOW())
          ON DUPLICATE KEY UPDATE updated_at = NOW()
        `, [teacherId, classroom.classroom_id]);
        
        console.log(`Assigned classroom ID ${classroom.classroom_id} to teacher`);
      }
    }
    
    console.log('\nTeacher account created successfully!');
    console.log('Login credentials:');
    console.log(`Email: ${teacherData.email}`);
    console.log(`Password: Merit123#`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating teacher account:', error);
    process.exit(1);
  }
}

// Run the function
createTeacherAccount();
