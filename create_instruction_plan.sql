-- <PERSON><PERSON> to create instruction plan for computer teacher
-- with lectures in Class 11 Non-Medical A, B and Med-A
-- with daily lectures 3, 4, 5 respectively except Sundays

-- Use the Computer Science Teacher 1 (cs_teacher1)
SET @teacher_id = 19; -- cs_teacher1
SET @subject_id = 5;  -- Computer Science

-- Class IDs
SET @class_11_nm_a = 5;  -- 11-NM-A
SET @class_11_nm_b = 6;  -- 11-NM-B
SET @class_11_m_a = 7;   -- 11-M-A

-- 1. Create student accounts for testing (5 for each class)
-- Create students for Class 11 Non-Medical A
INSERT IGNORE INTO users (username, name, email, password, role, full_name, is_active, last_login, bio)
VALUES
('nm_a_student1', 'nm_a_student1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-A Student 1', 1, NOW(), 'Student in 11th Non-Medical A'),
('nm_a_student2', 'nm_a_student2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-A Student 2', 1, NOW(), 'Student in 11th Non-Medical A'),
('nm_a_student3', 'nm_a_student3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-A Student 3', 1, NOW(), 'Student in 11th Non-Medical A'),
('nm_a_student4', 'nm_a_student4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-A Student 4', 1, NOW(), 'Student in 11th Non-Medical A'),
('nm_a_student5', 'nm_a_student5', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-A Student 5', 1, NOW(), 'Student in 11th Non-Medical A');

-- Create students for Class 11 Non-Medical B
INSERT IGNORE INTO users (username, name, email, password, role, full_name, is_active, last_login, bio)
VALUES
('nm_b_student1', 'nm_b_student1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-B Student 1', 1, NOW(), 'Student in 11th Non-Medical B'),
('nm_b_student2', 'nm_b_student2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-B Student 2', 1, NOW(), 'Student in 11th Non-Medical B'),
('nm_b_student3', 'nm_b_student3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-B Student 3', 1, NOW(), 'Student in 11th Non-Medical B'),
('nm_b_student4', 'nm_b_student4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-B Student 4', 1, NOW(), 'Student in 11th Non-Medical B'),
('nm_b_student5', 'nm_b_student5', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'NM-B Student 5', 1, NOW(), 'Student in 11th Non-Medical B');

-- Create students for Class 11 Medical A
INSERT IGNORE INTO users (username, name, email, password, role, full_name, is_active, last_login, bio)
VALUES
('m_a_student1', 'm_a_student1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'M-A Student 1', 1, NOW(), 'Student in 11th Medical A'),
('m_a_student2', 'm_a_student2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'M-A Student 2', 1, NOW(), 'Student in 11th Medical A'),
('m_a_student3', 'm_a_student3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'M-A Student 3', 1, NOW(), 'Student in 11th Medical A'),
('m_a_student4', 'm_a_student4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'M-A Student 4', 1, NOW(), 'Student in 11th Medical A'),
('m_a_student5', 'm_a_student5', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'student', 'M-A Student 5', 1, NOW(), 'Student in 11th Medical A');

-- Get the IDs of the newly created students
SET @nm_a_student1 = (SELECT id FROM users WHERE username = 'nm_a_student1');
SET @nm_a_student2 = (SELECT id FROM users WHERE username = 'nm_a_student2');
SET @nm_a_student3 = (SELECT id FROM users WHERE username = 'nm_a_student3');
SET @nm_a_student4 = (SELECT id FROM users WHERE username = 'nm_a_student4');
SET @nm_a_student5 = (SELECT id FROM users WHERE username = 'nm_a_student5');
SET @nm_b_student1 = (SELECT id FROM users WHERE username = 'nm_b_student1');
SET @nm_b_student2 = (SELECT id FROM users WHERE username = 'nm_b_student2');
SET @nm_b_student3 = (SELECT id FROM users WHERE username = 'nm_b_student3');
SET @nm_b_student4 = (SELECT id FROM users WHERE username = 'nm_b_student4');
SET @nm_b_student5 = (SELECT id FROM users WHERE username = 'nm_b_student5');
SET @m_a_student1 = (SELECT id FROM users WHERE username = 'm_a_student1');
SET @m_a_student2 = (SELECT id FROM users WHERE username = 'm_a_student2');
SET @m_a_student3 = (SELECT id FROM users WHERE username = 'm_a_student3');
SET @m_a_student4 = (SELECT id FROM users WHERE username = 'm_a_student4');
SET @m_a_student5 = (SELECT id FROM users WHERE username = 'm_a_student5');

-- 2. Assign students to their respective classes
-- Assign Class 11 Non-Medical A students
INSERT IGNORE INTO student_classes (student_id, class_id, created_at)
VALUES
(@nm_a_student1, @class_11_nm_a, NOW()),
(@nm_a_student2, @class_11_nm_a, NOW()),
(@nm_a_student3, @class_11_nm_a, NOW()),
(@nm_a_student4, @class_11_nm_a, NOW()),
(@nm_a_student5, @class_11_nm_a, NOW());

-- Assign Class 11 Non-Medical B students
INSERT IGNORE INTO student_classes (student_id, class_id, created_at)
VALUES
(@nm_b_student1, @class_11_nm_b, NOW()),
(@nm_b_student2, @class_11_nm_b, NOW()),
(@nm_b_student3, @class_11_nm_b, NOW()),
(@nm_b_student4, @class_11_nm_b, NOW()),
(@nm_b_student5, @class_11_nm_b, NOW());

-- Assign Class 11 Medical A students
INSERT IGNORE INTO student_classes (student_id, class_id, created_at)
VALUES
(@m_a_student1, @class_11_m_a, NOW()),
(@m_a_student2, @class_11_m_a, NOW()),
(@m_a_student3, @class_11_m_a, NOW()),
(@m_a_student4, @class_11_m_a, NOW()),
(@m_a_student5, @class_11_m_a, NOW());

-- 3. Create subject-class assignments
-- Create assignment for Class 11 Non-Medical A
INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures, is_active)
VALUES (@subject_id, @class_11_nm_a, 2, 1, 1);
SET @assignment_nm_a = (SELECT id FROM subject_class_assignment WHERE subject_id = @subject_id AND class_id = @class_11_nm_a);

-- Create assignment for Class 11 Non-Medical B
INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures, is_active)
VALUES (@subject_id, @class_11_nm_b, 3, 1, 1);
SET @assignment_nm_b = (SELECT id FROM subject_class_assignment WHERE subject_id = @subject_id AND class_id = @class_11_nm_b);

-- Create assignment for Class 11 Medical A
INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures, is_active)
VALUES (@subject_id, @class_11_m_a, 4, 1, 1);
SET @assignment_nm_c = (SELECT id FROM subject_class_assignment WHERE subject_id = @subject_id AND class_id = @class_11_m_a);

-- 4. Create lecture schedules
-- Class 11 Non-Medical A (3 lectures per week - Monday, Wednesday, Friday)
INSERT INTO lecture_schedule (assignment_id, teacher_id, day_of_week, start_time, end_time, classroom, is_active)
VALUES
(@assignment_nm_a, @teacher_id, 'Monday', '09:00:00', '09:45:00', 'Computer Lab 1', 1),
(@assignment_nm_a, @teacher_id, 'Wednesday', '10:00:00', '10:45:00', 'Computer Lab 1', 1),
(@assignment_nm_a, @teacher_id, 'Friday', '11:00:00', '11:45:00', 'Computer Lab 1', 1);

-- Store the schedule IDs for Class 11 Non-Medical A
SET @schedule_nm_a_1 = LAST_INSERT_ID();
SET @schedule_nm_a_2 = LAST_INSERT_ID() + 1;
SET @schedule_nm_a_3 = LAST_INSERT_ID() + 2;

-- Class 11 Non-Medical B (4 lectures per week - Monday, Tuesday, Thursday, Friday)
INSERT INTO lecture_schedule (assignment_id, teacher_id, day_of_week, start_time, end_time, classroom, is_active)
VALUES
(@assignment_nm_b, @teacher_id, 'Monday', '12:00:00', '12:45:00', 'Computer Lab 2', 1),
(@assignment_nm_b, @teacher_id, 'Tuesday', '09:00:00', '09:45:00', 'Computer Lab 2', 1),
(@assignment_nm_b, @teacher_id, 'Thursday', '10:00:00', '10:45:00', 'Computer Lab 2', 1),
(@assignment_nm_b, @teacher_id, 'Friday', '12:00:00', '12:45:00', 'Computer Lab 2', 1);

-- Store the schedule IDs for Class 11 Non-Medical B
SET @schedule_nm_b_1 = LAST_INSERT_ID();
SET @schedule_nm_b_2 = LAST_INSERT_ID() + 1;
SET @schedule_nm_b_3 = LAST_INSERT_ID() + 2;
SET @schedule_nm_b_4 = LAST_INSERT_ID() + 3;

-- Class 11 Medical A (5 lectures per week - Monday through Friday)
INSERT INTO lecture_schedule (assignment_id, teacher_id, day_of_week, start_time, end_time, classroom, is_active)
VALUES
(@assignment_nm_c, @teacher_id, 'Monday', '14:00:00', '14:45:00', 'Computer Lab 3', 1),
(@assignment_nm_c, @teacher_id, 'Tuesday', '14:00:00', '14:45:00', 'Computer Lab 3', 1),
(@assignment_nm_c, @teacher_id, 'Wednesday', '14:00:00', '14:45:00', 'Computer Lab 3', 1),
(@assignment_nm_c, @teacher_id, 'Thursday', '14:00:00', '14:45:00', 'Computer Lab 3', 1),
(@assignment_nm_c, @teacher_id, 'Friday', '14:00:00', '14:45:00', 'Computer Lab 3', 1);

-- Store the schedule IDs for Class 11 Medical A
SET @schedule_m_a_1 = LAST_INSERT_ID();
SET @schedule_m_a_2 = LAST_INSERT_ID() + 1;
SET @schedule_m_a_3 = LAST_INSERT_ID() + 2;
SET @schedule_m_a_4 = LAST_INSERT_ID() + 3;
SET @schedule_m_a_5 = LAST_INSERT_ID() + 4;

-- 5. Create daily instruction plans for the next 4 weeks
-- Get the current date and calculate dates for the next 4 weeks
SET @current_date = CURDATE();

-- Create instruction plans for Class 11 Non-Medical A
-- Week 1
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_nm_a_1, DATE_ADD(@current_date, INTERVAL 0 DAY), 'Introduction to Programming Concepts', 'Understand basic programming concepts and paradigms', 'Textbook Chapter 1, Presentation slides', 'Group discussion on programming examples', 'Read Chapter 1 and solve exercise 1.1', 'Focus on fundamentals'),
(@schedule_nm_a_2, DATE_ADD(@current_date, INTERVAL 2 DAY), 'Variables and Data Types', 'Learn about different data types and variable declaration', 'Textbook Chapter 2, Code examples', 'Hands-on coding exercises', 'Complete worksheet on data types', 'Emphasize type conversion'),
(@schedule_nm_a_3, DATE_ADD(@current_date, INTERVAL 4 DAY), 'Operators and Expressions', 'Understand arithmetic, relational and logical operators', 'Textbook Chapter 3, Practice problems', 'Solve operator precedence problems', 'Complete the operator challenge problems', 'Cover bitwise operators briefly');

-- Week 2
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_nm_a_1, DATE_ADD(@current_date, INTERVAL 7 DAY), 'Control Structures - Conditional Statements', 'Learn if-else and switch statements', 'Textbook Chapter 4, Flowcharts', 'Create flowcharts and implement in code', 'Implement decision-making programs', 'Focus on nested conditions'),
(@schedule_nm_a_2, DATE_ADD(@current_date, INTERVAL 9 DAY), 'Control Structures - Loops', 'Understand for, while and do-while loops', 'Textbook Chapter 5, Loop examples', 'Trace loop execution and write programs', 'Solve loop-based problems', 'Emphasize loop control statements'),
(@schedule_nm_a_3, DATE_ADD(@current_date, INTERVAL 11 DAY), 'Functions and Methods', 'Learn function declaration, parameters and return values', 'Textbook Chapter 6, Function examples', 'Write and test various functions', 'Create a calculator using functions', 'Cover scope of variables');

-- Create instruction plans for Class 11 Non-Medical B
-- Week 1
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_nm_b_1, DATE_ADD(@current_date, INTERVAL 0 DAY), 'Introduction to Programming Concepts', 'Understand basic programming concepts and paradigms', 'Textbook Chapter 1, Presentation slides', 'Group discussion on programming examples', 'Read Chapter 1 and solve exercise 1.1', 'Focus on fundamentals'),
(@schedule_nm_b_2, DATE_ADD(@current_date, INTERVAL 1 DAY), 'Variables and Data Types', 'Learn about different data types and variable declaration', 'Textbook Chapter 2, Code examples', 'Hands-on coding exercises', 'Complete worksheet on data types', 'Emphasize type conversion'),
(@schedule_nm_b_3, DATE_ADD(@current_date, INTERVAL 3 DAY), 'Operators and Expressions', 'Understand arithmetic, relational and logical operators', 'Textbook Chapter 3, Practice problems', 'Solve operator precedence problems', 'Complete the operator challenge problems', 'Cover bitwise operators briefly'),
(@schedule_nm_b_4, DATE_ADD(@current_date, INTERVAL 4 DAY), 'Control Structures - Conditional Statements', 'Learn if-else and switch statements', 'Textbook Chapter 4, Flowcharts', 'Create flowcharts and implement in code', 'Implement decision-making programs', 'Focus on nested conditions');

-- Week 2
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_nm_b_1, DATE_ADD(@current_date, INTERVAL 7 DAY), 'Control Structures - Loops', 'Understand for, while and do-while loops', 'Textbook Chapter 5, Loop examples', 'Trace loop execution and write programs', 'Solve loop-based problems', 'Emphasize loop control statements'),
(@schedule_nm_b_2, DATE_ADD(@current_date, INTERVAL 8 DAY), 'Functions and Methods', 'Learn function declaration, parameters and return values', 'Textbook Chapter 6, Function examples', 'Write and test various functions', 'Create a calculator using functions', 'Cover scope of variables'),
(@schedule_nm_b_3, DATE_ADD(@current_date, INTERVAL 10 DAY), 'Arrays and Lists', 'Understand array declaration, initialization and operations', 'Textbook Chapter 7, Array examples', 'Implement array manipulation programs', 'Solve array-based problems', 'Cover multi-dimensional arrays'),
(@schedule_nm_b_4, DATE_ADD(@current_date, INTERVAL 11 DAY), 'Searching and Sorting Algorithms', 'Learn linear search, binary search, bubble sort', 'Textbook Chapter 8, Algorithm flowcharts', 'Implement and compare algorithms', 'Analyze algorithm efficiency', 'Focus on time complexity');

-- Create instruction plans for Class 11 Medical A
-- Week 1
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_m_a_1, DATE_ADD(@current_date, INTERVAL 0 DAY), 'Introduction to Programming Concepts', 'Understand basic programming concepts and paradigms', 'Textbook Chapter 1, Presentation slides', 'Group discussion on programming examples', 'Read Chapter 1 and solve exercise 1.1', 'Focus on fundamentals'),
(@schedule_m_a_2, DATE_ADD(@current_date, INTERVAL 1 DAY), 'Variables and Data Types', 'Learn about different data types and variable declaration', 'Textbook Chapter 2, Code examples', 'Hands-on coding exercises', 'Complete worksheet on data types', 'Emphasize type conversion'),
(@schedule_m_a_3, DATE_ADD(@current_date, INTERVAL 2 DAY), 'Operators and Expressions', 'Understand arithmetic, relational and logical operators', 'Textbook Chapter 3, Practice problems', 'Solve operator precedence problems', 'Complete the operator challenge problems', 'Cover bitwise operators briefly'),
(@schedule_m_a_4, DATE_ADD(@current_date, INTERVAL 3 DAY), 'Control Structures - Conditional Statements', 'Learn if-else and switch statements', 'Textbook Chapter 4, Flowcharts', 'Create flowcharts and implement in code', 'Implement decision-making programs', 'Focus on nested conditions'),
(@schedule_m_a_5, DATE_ADD(@current_date, INTERVAL 4 DAY), 'Control Structures - Loops', 'Understand for, while and do-while loops', 'Textbook Chapter 5, Loop examples', 'Trace loop execution and write programs', 'Solve loop-based problems', 'Emphasize loop control statements');

-- Week 2
INSERT INTO daily_instruction_plan (schedule_id, date, topic, objectives, material_support, activities, homework, notes)
VALUES
(@schedule_m_a_1, DATE_ADD(@current_date, INTERVAL 7 DAY), 'Functions and Methods', 'Learn function declaration, parameters and return values', 'Textbook Chapter 6, Function examples', 'Write and test various functions', 'Create a calculator using functions', 'Cover scope of variables'),
(@schedule_m_a_2, DATE_ADD(@current_date, INTERVAL 8 DAY), 'Arrays and Lists', 'Understand array declaration, initialization and operations', 'Textbook Chapter 7, Array examples', 'Implement array manipulation programs', 'Solve array-based problems', 'Cover multi-dimensional arrays'),
(@schedule_m_a_3, DATE_ADD(@current_date, INTERVAL 9 DAY), 'Searching and Sorting Algorithms', 'Learn linear search, binary search, bubble sort', 'Textbook Chapter 8, Algorithm flowcharts', 'Implement and compare algorithms', 'Analyze algorithm efficiency', 'Focus on time complexity'),
(@schedule_m_a_4, DATE_ADD(@current_date, INTERVAL 10 DAY), 'String Manipulation', 'Learn string operations and methods', 'Textbook Chapter 9, String examples', 'Implement string manipulation programs', 'Create a text analyzer', 'Cover regular expressions briefly'),
(@schedule_m_a_5, DATE_ADD(@current_date, INTERVAL 11 DAY), 'Introduction to Object-Oriented Programming', 'Understand OOP concepts and principles', 'Textbook Chapter 10, OOP diagrams', 'Design classes and objects', 'Create a simple class hierarchy', 'Focus on encapsulation');

-- Add a message to confirm completion
SELECT 'Instruction plan for Computer Teacher 1 has been created successfully.' AS Message;
