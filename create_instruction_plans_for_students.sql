-- <PERSON><PERSON>t to create instruction plans for students to view
-- This creates entries in the instruction_plans table which is what students see

-- Use the Computer Science Teacher 1 (cs_teacher1)
SET @teacher_id = 19; -- cs_teacher1
SET @subject_id = 5;  -- Computer Science

-- Class IDs
SET @class_11_nm_a = 5;  -- 11-NM-A
SET @class_11_nm_b = 6;  -- 11-NM-B
SET @class_11_m_a = 7;   -- 11-M-A

-- Create instruction plans for each class
-- Class 11 Non-Medical A
INSERT INTO instruction_plans (title, description, subject_id, teacher_id, class_id, status, created_at)
VALUES (
  'Computer Science Fundamentals - 11 Non-Medical A',
  'This learning plan covers the fundamentals of computer science including programming concepts, data types, control structures, functions, arrays, and basic algorithms. Students will learn through a combination of theory and practical exercises.',
  @subject_id,
  @teacher_id,
  @class_11_nm_a,
  'published',
  NOW()
);

-- Class 11 Non-Medical B
INSERT INTO instruction_plans (title, description, subject_id, teacher_id, class_id, status, created_at)
VALUES (
  'Computer Science Fundamentals - 11 Non-Medical B',
  'This learning plan covers the fundamentals of computer science including programming concepts, data types, control structures, functions, arrays, and basic algorithms. Students will learn through a combination of theory and practical exercises.',
  @subject_id,
  @teacher_id,
  @class_11_nm_b,
  'published',
  NOW()
);

-- Class 11 Medical A
INSERT INTO instruction_plans (title, description, subject_id, teacher_id, class_id, status, created_at)
VALUES (
  'Computer Science Fundamentals - 11 Medical A',
  'This learning plan covers the fundamentals of computer science including programming concepts, data types, control structures, functions, arrays, and basic algorithms. Students will learn through a combination of theory and practical exercises.',
  @subject_id,
  @teacher_id,
  @class_11_m_a,
  'published',
  NOW()
);

-- Create instruction plan resources
-- Get the IDs of the newly created instruction plans
SET @plan_nm_a = (SELECT id FROM instruction_plans WHERE title = 'Computer Science Fundamentals - 11 Non-Medical A' ORDER BY id DESC LIMIT 1);
SET @plan_nm_b = (SELECT id FROM instruction_plans WHERE title = 'Computer Science Fundamentals - 11 Non-Medical B' ORDER BY id DESC LIMIT 1);
SET @plan_m_a = (SELECT id FROM instruction_plans WHERE title = 'Computer Science Fundamentals - 11 Medical A' ORDER BY id DESC LIMIT 1);

-- Check if instruction_plan_resources table exists
SET @table_exists = (
  SELECT COUNT(*)
  FROM information_schema.tables
  WHERE table_schema = 'exam_prep_platform'
  AND table_name = 'instruction_plan_resources'
);

-- Create resources if the table exists
SET @sql = IF(@table_exists > 0,
  'INSERT INTO instruction_plan_resources (plan_id, resource_name, resource_type, resource_path, resource_content, created_at)
   VALUES
   (@plan_nm_a, "Introduction to Programming", "file", "/resources/intro_programming.pdf", "Basic concepts and paradigms", NOW()),
   (@plan_nm_a, "Variables and Data Types", "file", "/resources/data_types.pdf", "Understanding different data types", NOW()),
   (@plan_nm_a, "Control Structures", "file", "/resources/control_structures.pdf", "Conditional statements and loops", NOW()),
   (@plan_nm_b, "Introduction to Programming", "file", "/resources/intro_programming.pdf", "Basic concepts and paradigms", NOW()),
   (@plan_nm_b, "Variables and Data Types", "file", "/resources/data_types.pdf", "Understanding different data types", NOW()),
   (@plan_nm_b, "Control Structures", "file", "/resources/control_structures.pdf", "Conditional statements and loops", NOW()),
   (@plan_m_a, "Introduction to Programming", "file", "/resources/intro_programming.pdf", "Basic concepts and paradigms", NOW()),
   (@plan_m_a, "Variables and Data Types", "file", "/resources/data_types.pdf", "Understanding different data types", NOW()),
   (@plan_m_a, "Control Structures", "file", "/resources/control_structures.pdf", "Conditional statements and loops", NOW())',
  'SELECT "instruction_plan_resources table does not exist" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Skip instruction_plan_details and lecture_instruction_map as they don't exist

-- Add a message to confirm completion
SELECT 'Instruction plans for students have been created successfully.' AS Message;
