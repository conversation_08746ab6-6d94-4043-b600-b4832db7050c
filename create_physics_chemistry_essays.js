const db = require('./config/database');

async function createEssaysAndQuestions() {
    try {
        console.log('Starting creation of Physics and Chemistry essays and questions...');

        // Start transaction
        await db.query('START TRANSACTION');

        // Get admin user ID
        const [adminUsers] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (adminUsers.length === 0) {
            throw new Error('No admin user found. Please create an admin user first.');
        }

        const adminId = adminUsers[0].id;

        // Get the existing test ID
        const [existingTests] = await db.query(
            'SELECT exam_id FROM exams WHERE exam_name = ?',
            ['Class 11 Physics & Chemistry Test']
        );

        if (existingTests.length === 0) {
            throw new Error('Test not found. Please run the create_physics_chemistry_test.js script first.');
        }

        const examId = existingTests[0].exam_id;

        // Get the section IDs
        const [sections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ?',
            [examId]
        );

        if (sections.length < 2) {
            throw new Error('Test sections not found. Please run the create_physics_chemistry_test.js script first.');
        }

        const physicsSectionId = sections.find(s => s.section_name === 'Physics').section_id;
        const chemistrySectionId = sections.find(s => s.section_name === 'Chemistry').section_id;

        // Get the categories
        const [categories] = await db.query(
            'SELECT category_id, name FROM categories WHERE name IN (?, ?, ?, ?, ?, ?)',
            ['Electrostatics', 'Motion', 'Gravitation', 'Structure of Atom', 'Orbitals', 'P-Block Elements']
        );

        const categoryMap = {};
        categories.forEach(cat => {
            categoryMap[cat.name] = cat.category_id;
        });

        // 1. Create Physics Essay - Electrostatics
        const electrostaticsEssayContent = `# Understanding Electrostatics

Electrostatics is the branch of physics that deals with the phenomena and properties of stationary or slow-moving electric charges. It encompasses a wide range of phenomena from the attraction of plastic rubbed with fur to the behavior of lightning during a thunderstorm.

## Fundamental Principles

At the heart of electrostatics is the concept of electric charge. Electric charge is a fundamental property of matter, much like mass. There are two types of electric charges: positive and negative. Like charges repel each other, while unlike charges attract.

The smallest unit of charge is the elementary charge, denoted by e, which is approximately 1.602 × 10^-19 coulombs. This is the magnitude of charge carried by a single electron (negative) or proton (positive).

## Coulomb's Law

The force between two point charges is described by Coulomb's Law, which states that the force is directly proportional to the product of the charges and inversely proportional to the square of the distance between them:

F = k(q₁q₂)/r²

Where:
- F is the electrostatic force between the charges (in newtons)
- k is Coulomb's constant (8.99 × 10^9 N·m²/C²)
- q₁ and q₂ are the magnitudes of the charges (in coulombs)
- r is the distance between the charges (in meters)

## Electric Fields

An electric field is a region of space around a charged particle or object within which a force would be exerted on other charged particles or objects. The electric field strength at a point is defined as the force that would be exerted on a unit positive charge placed at that point:

E = F/q

Where:
- E is the electric field strength (in N/C or V/m)
- F is the force on a charge q (in newtons)
- q is the charge (in coulombs)

Electric field lines are a visual representation of the electric field. They start from positive charges and end at negative charges. The density of these lines indicates the strength of the field.

## Electric Potential

Electric potential is the amount of work needed to move a unit positive charge from a reference point to a specific point against an electric field. The difference in electric potential between two points is called voltage.

V = W/q

Where:
- V is the electric potential (in volts)
- W is the work done (in joules)
- q is the charge (in coulombs)

## Applications of Electrostatics

Electrostatics has numerous practical applications:

1. **Photocopiers and Laser Printers**: Use electrostatic attraction to transfer toner to paper.
2. **Electrostatic Precipitators**: Remove particles from air or other gases in industrial processes.
3. **Paint Spraying**: Electrostatic forces help ensure an even coating of paint on objects.
4. **Van de Graaff Generators**: Demonstrate principles of electrostatics and can generate high voltages.

## Challenges and Safety Considerations

While electrostatics offers many benefits, it also presents challenges:

1. **Static Discharge**: Can damage sensitive electronic components.
2. **Lightning**: A natural electrostatic discharge that can cause significant damage.
3. **Dust Attraction**: Electrostatic forces can cause dust to cling to surfaces, which can be problematic in clean environments.

Understanding the principles of electrostatics is crucial for developing technologies that harness or mitigate these effects, ensuring both efficiency and safety in various applications.`;

        const [electrostaticsEssayResult] = await db.query(
            'INSERT INTO essays (title, content, created_by) VALUES (?, ?, ?)',
            ['Principles of Electrostatics', electrostaticsEssayContent, adminId]
        );

        const electrostaticsEssayId = electrostaticsEssayResult.insertId;
        console.log(`Created Electrostatics essay with ID: ${electrostaticsEssayId}`);

        // 2. Create Chemistry Essay - Atomic Structure
        const atomicStructureEssayContent = `# The Structure of the Atom: From Dalton to Quantum Mechanics

The understanding of atomic structure has evolved dramatically over the past two centuries, from simple models to complex quantum mechanical descriptions. This essay explores this fascinating journey and the current understanding of atomic structure.

## Early Atomic Models

### Dalton's Atomic Theory (1808)
John Dalton proposed that all matter consists of indivisible particles called atoms. He suggested that atoms of the same element are identical, while atoms of different elements have different properties and masses.

### Thomson's Plum Pudding Model (1897)
After discovering the electron, J.J. Thomson proposed the "plum pudding" model, suggesting that atoms were spheres of positive charge with negatively charged electrons embedded throughout, like plums in a pudding.

### Rutherford's Nuclear Model (1911)
Ernest Rutherford's gold foil experiment led to the discovery of the nucleus. He proposed that atoms have a small, dense, positively charged nucleus with electrons orbiting around it, similar to planets orbiting the sun.

## The Bohr Model (1913)

Niels Bohr refined Rutherford's model by incorporating quantum theory. Key features of the Bohr model include:

1. Electrons orbit the nucleus in specific, allowed circular paths called "energy levels" or "shells."
2. Each shell has a fixed energy level.
3. Electrons can jump between energy levels by absorbing or emitting specific amounts of energy (quanta).
4. The energy of an electron is quantized, meaning it can only have certain discrete values.

The Bohr model successfully explained the hydrogen spectrum but failed to explain the spectra of more complex atoms.

## Limitations of the Bohr Model

Despite its success with hydrogen, the Bohr model had several limitations:

1. It couldn't explain the spectra of atoms with more than one electron.
2. It violated the Heisenberg Uncertainty Principle, which states that we cannot simultaneously know both the position and momentum of an electron with perfect accuracy.
3. It couldn't explain the fine structure of spectral lines.
4. It didn't account for the wave-like properties of electrons.

## Modern Quantum Mechanical Model

The current understanding of atomic structure is based on quantum mechanics, developed by scientists like Schrödinger, Heisenberg, and Dirac in the 1920s.

### Key Concepts of the Quantum Mechanical Model:

1. **Wave-Particle Duality**: Electrons exhibit both wave-like and particle-like properties.
2. **Orbitals**: Instead of fixed orbits, electrons exist in probability clouds called orbitals, which represent regions where electrons are likely to be found.
3. **Quantum Numbers**: Four quantum numbers describe the properties of electrons in atoms:
   - Principal Quantum Number (n): Determines the energy level and size of the orbital
   - Angular Momentum Quantum Number (l): Determines the shape of the orbital
   - Magnetic Quantum Number (ml): Determines the orientation of the orbital
   - Spin Quantum Number (ms): Describes the spin of the electron

4. **Pauli Exclusion Principle**: No two electrons in an atom can have the same set of four quantum numbers.
5. **Aufbau Principle**: Electrons fill orbitals starting from the lowest energy level.
6. **Hund's Rule**: When filling orbitals of equal energy, electrons first occupy each orbital singly before pairing up.

## Subatomic Particles

Our current understanding includes three fundamental particles that make up atoms:

1. **Protons**: Positively charged particles found in the nucleus, with a mass of approximately 1.67 × 10^-27 kg.
2. **Neutrons**: Neutral particles found in the nucleus, with a mass slightly greater than that of protons.
3. **Electrons**: Negatively charged particles that orbit the nucleus, with a mass of approximately 9.11 × 10^-31 kg.

## Beyond the Atom

Modern physics has revealed that protons and neutrons are composed of even smaller particles called quarks. The Standard Model of particle physics describes these fundamental particles and their interactions.

## Conclusion

The journey from Dalton's simple atomic theory to the complex quantum mechanical model illustrates the evolution of scientific understanding. Each model built upon the strengths of its predecessors while addressing their limitations. The current quantum mechanical model provides a comprehensive framework for understanding atomic structure, though research continues to refine our understanding of the fundamental building blocks of matter.`;

        const [atomicStructureEssayResult] = await db.query(
            'INSERT INTO essays (title, content, created_by) VALUES (?, ?, ?)',
            ['Evolution of Atomic Structure', atomicStructureEssayContent, adminId]
        );

        const atomicStructureEssayId = atomicStructureEssayResult.insertId;
        console.log(`Created Atomic Structure essay with ID: ${atomicStructureEssayId}`);

        // 3. Create Physics Essay Questions with Follow-up Questions
        const physicsEssayQuestions = [
            {
                question_text: "Based on the essay, explain how Coulomb's Law describes the interaction between charged particles and derive the mathematical expression. Then, calculate the force between two charges of +2.0 μC and -3.0 μC separated by a distance of 0.15 m.",
                question_type: "essay",
                marks: 8,
                solution_text: "Coulomb's Law: F = k(q₁q₂)/r². For the given charges: F = (8.99 × 10^9)(2.0 × 10^-6)(-3.0 × 10^-6))/(0.15)² = -2.4 N (attractive force).",
                essay_id: electrostaticsEssayId,
                categories: ['Electrostatics']
            },
            {
                question_text: "Using the concepts from the essay, describe what electric field lines represent and how they are used to visualize electric fields. Draw and explain the electric field pattern around (a) an isolated positive charge, (b) an isolated negative charge, and (c) a dipole consisting of equal and opposite charges.",
                question_type: "essay",
                marks: 7,
                solution_text: "Electric field lines show field direction and strength. They radiate outward from positive charges and inward to negative charges. For dipoles, lines flow from positive to negative charge.",
                essay_id: electrostaticsEssayId,
                categories: ['Electrostatics']
            },
            {
                question_text: "The essay mentions several practical applications of electrostatics. Choose two applications and explain in detail how the principles of electrostatics are applied in each case. What are the advantages and limitations of using electrostatic principles in these applications?",
                question_type: "essay",
                marks: 6,
                solution_text: "Photocopiers use charged drums to attract toner to specific areas. Precipitators use electric fields to remove particles from gases. Both utilize charge attraction but require careful management of static electricity.",
                essay_id: electrostaticsEssayId,
                categories: ['Electrostatics']
            }
        ];

        // 4. Create Chemistry Essay Questions with Follow-up Questions
        const chemistryEssayQuestions = [
            {
                question_text: "According to the essay, describe the evolution of atomic models from Dalton to the quantum mechanical model. For each model, explain its key features, contributions, and limitations. Which experimental evidence led to the development of each new model?",
                question_type: "essay",
                marks: 8,
                solution_text: "Atomic models evolved: Dalton (indivisible particles), Thomson (plum pudding), Rutherford (nuclear), Bohr (energy levels), and quantum mechanical (probability orbitals).",
                essay_id: atomicStructureEssayId,
                categories: ['Structure of Atom']
            },
            {
                question_text: "Explain the four quantum numbers in detail and how they describe an electron's state in an atom. Then, write the complete set of quantum numbers for all electrons in a carbon atom (Z=6) in its ground state. How does the Pauli Exclusion Principle apply to these electrons?",
                question_type: "essay",
                marks: 7,
                solution_text: "Quantum numbers: n (energy level), l (shape), ml (orientation), ms (spin). Carbon: 1s² electrons (1,0,0,±1/2), 2s² (2,0,0,±1/2), 2p² (2,1,-1,+1/2), (2,1,0,+1/2).",
                essay_id: atomicStructureEssayId,
                categories: ['Orbitals']
            },
            {
                question_text: "The essay discusses the Aufbau Principle and Hund's Rule. Explain these principles and demonstrate how they are applied when determining the electron configuration of phosphorus (Z=15) and iron (Z=26). How do these principles relate to the periodic table's structure?",
                question_type: "essay",
                marks: 6,
                solution_text: "Aufbau: fill lowest energy first. Hund's: single occupancy before pairing. P: 1s²2s²2p⁶3s²3p³ (3 unpaired 3p). Fe: 1s²2s²2p⁶3s²3p⁶4s²3d⁶ (4 unpaired 3d).",
                essay_id: atomicStructureEssayId,
                categories: ['Structure of Atom', 'Orbitals']
            }
        ];

        // Insert Physics Essay Questions
        for (let i = 0; i < physicsEssayQuestions.length; i++) {
            const q = physicsEssayQuestions[i];

            // Insert essay question
            const [qResult] = await db.query(
                'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position, essay_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [physicsSectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 10, q.essay_id]
            );

            const questionId = qResult.insertId;

            // Insert category mappings
            for (const categoryName of q.categories) {
                const categoryId = categoryMap[categoryName];
                if (categoryId) {
                    await db.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
                        [questionId, categoryId]
                    );
                }
            }

            console.log(`Created Physics essay question ${i + 1} with ID: ${questionId}`);
        }

        // Insert Chemistry Essay Questions
        for (let i = 0; i < chemistryEssayQuestions.length; i++) {
            const q = chemistryEssayQuestions[i];

            // Insert essay question
            const [qResult] = await db.query(
                'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position, essay_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [chemistrySectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 10, q.essay_id]
            );

            const questionId = qResult.insertId;

            // Insert category mappings
            for (const categoryName of q.categories) {
                const categoryId = categoryMap[categoryName];
                if (categoryId) {
                    await db.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
                        [questionId, categoryId]
                    );
                }
            }

            console.log(`Created Chemistry essay question ${i + 1} with ID: ${questionId}`);
        }

        // Commit the transaction
        await db.query('COMMIT');
        console.log('Essays and questions created successfully!');

        return {
            success: true,
            message: 'Essays and questions created successfully'
        };
    } catch (error) {
        // Rollback on error
        await db.query('ROLLBACK');
        console.error('Error creating essays and questions:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        process.exit();
    }
}

// Run the function
createEssaysAndQuestions();
