const db = require('./config/database');

async function createTest() {
    try {
        console.log('Starting test creation...');
        
        // Start transaction
        await db.query('START TRANSACTION');
        
        // 1. Create the test
        const [testResult] = await db.query(
            'INSERT INTO exams (exam_name, description, duration, instructions, passing_marks, status, difficulty, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [
                'Class 11 Physics & Chemistry Test',
                'Mixed test covering Physics and Chemistry topics for Class 11 students',
                30, // 30 minutes duration
                'This test covers topics from Physics (Electrostatics, Motion, Gravitation) and Chemistry (Structure of Atom, Orbitals, P-Block Elements). Answer all questions. Essay questions require detailed explanations.',
                60.00, // passing marks
                'published', // status
                'intermediate', // difficulty
                1 // admin user ID
            ]
        );
        
        const examId = testResult.insertId;
        console.log(`Created test with ID: ${examId}`);
        
        // 2. Create sections
        const [physicsSection] = await db.query(
            'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
            [examId, 'Physics', 1]
        );
        
        const [chemistrySection] = await db.query(
            'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
            [examId, 'Chemistry', 2]
        );
        
        const physicsSectionId = physicsSection.insertId;
        const chemistrySectionId = chemistrySection.insertId;
        
        console.log(`Created Physics section with ID: ${physicsSectionId}`);
        console.log(`Created Chemistry section with ID: ${chemistrySectionId}`);
        
        // 3. Create categories if they don't exist
        // Check if categories exist
        const [existingCategories] = await db.query('SELECT category_id, name FROM categories WHERE name IN (?, ?, ?, ?, ?, ?)', 
            ['Electrostatics', 'Motion', 'Gravitation', 'Structure of Atom', 'Orbitals', 'P-Block Elements']);
        
        const categoryMap = {};
        existingCategories.forEach(cat => {
            categoryMap[cat.name] = cat.category_id;
        });
        
        // Create missing categories
        const categoriesToCreate = [
            { name: 'Electrostatics', description: 'Physics - Electrostatics topics', parent_id: 6 },
            { name: 'Motion', description: 'Physics - Motion and mechanics', parent_id: 6 },
            { name: 'Gravitation', description: 'Physics - Gravitation topics', parent_id: 6 },
            { name: 'Structure of Atom', description: 'Chemistry - Atomic structure', parent_id: 7 },
            { name: 'Orbitals', description: 'Chemistry - Orbital theory', parent_id: 7 },
            { name: 'P-Block Elements', description: 'Chemistry - P-Block elements', parent_id: 7 }
        ];
        
        for (const category of categoriesToCreate) {
            if (!categoryMap[category.name]) {
                const [result] = await db.query(
                    'INSERT INTO categories (name, description, parent_id) VALUES (?, ?, ?)',
                    [category.name, category.description, category.parent_id]
                );
                categoryMap[category.name] = result.insertId;
                console.log(`Created category: ${category.name} with ID: ${result.insertId}`);
            }
        }
        
        // 4. Create questions
        // Physics Questions
        const physicsQuestions = [
            {
                question_text: "What is Coulomb's Law and how does it describe the force between two charged particles?",
                question_type: "essay",
                marks: 5,
                solution_text: "Coulomb's Law states that the force between two charged particles is directly proportional to the product of their charges and inversely proportional to the square of the distance between them. The mathematical form is F = k(q₁q₂)/r², where k is Coulomb's constant.",
                categories: ['Electrostatics']
            },
            {
                question_text: "A charged particle with a charge of 3.0 μC experiences a force of 0.15 N when placed in an electric field. What is the magnitude of the electric field?",
                question_type: "multiple_choice",
                marks: 2,
                solution_text: "Using F = qE, we get E = F/q = 0.15 N / (3.0 × 10⁻⁶ C) = 5.0 × 10⁴ N/C",
                options: [
                    { option_text: "5.0 × 10⁴ N/C", is_correct: true },
                    { option_text: "4.5 × 10⁴ N/C", is_correct: false },
                    { option_text: "5.0 × 10⁶ N/C", is_correct: false },
                    { option_text: "0.45 × 10⁴ N/C", is_correct: false }
                ],
                categories: ['Electrostatics']
            },
            {
                question_text: "Newton's Second Law of Motion states that:",
                question_type: "multiple_choice",
                marks: 1,
                solution_text: "Newton's Second Law states that the acceleration of an object is directly proportional to the net force acting on it and inversely proportional to its mass (F = ma).",
                options: [
                    { option_text: "An object at rest stays at rest unless acted upon by an external force", is_correct: false },
                    { option_text: "For every action, there is an equal and opposite reaction", is_correct: false },
                    { option_text: "Force equals mass times acceleration (F = ma)", is_correct: true },
                    { option_text: "Energy can neither be created nor destroyed", is_correct: false }
                ],
                categories: ['Motion']
            },
            {
                question_text: "The acceleration due to gravity on Earth's surface is approximately:",
                question_type: "multiple_choice",
                marks: 1,
                solution_text: "The acceleration due to gravity on Earth's surface is approximately 9.8 m/s².",
                options: [
                    { option_text: "9.8 m/s²", is_correct: true },
                    { option_text: "6.7 m/s²", is_correct: false },
                    { option_text: "3.7 m/s²", is_correct: false },
                    { option_text: "11.2 m/s²", is_correct: false }
                ],
                categories: ['Gravitation']
            },
            {
                question_text: "True or False: The gravitational force between two objects is independent of their masses.",
                question_type: "true_false",
                marks: 1,
                solution_text: "False. According to Newton's Law of Universal Gravitation, the gravitational force between two objects is directly proportional to the product of their masses.",
                correct_answer: "false",
                categories: ['Gravitation']
            }
        ];
        
        // Chemistry Questions
        const chemistryQuestions = [
            {
                question_text: "Describe the Bohr model of the atom and explain its limitations.",
                question_type: "essay",
                marks: 5,
                solution_text: "The Bohr model depicts the atom as a small, positively charged nucleus surrounded by electrons that travel in circular orbits around the nucleus. Limitations include inability to explain the spectra of atoms with more than one electron, inability to explain the fine structure of spectral lines, and violation of the Heisenberg Uncertainty Principle.",
                categories: ['Structure of Atom']
            },
            {
                question_text: "Which quantum number determines the shape of an orbital?",
                question_type: "multiple_choice",
                marks: 1,
                solution_text: "The angular momentum quantum number (l) determines the shape of an orbital.",
                options: [
                    { option_text: "Principal quantum number (n)", is_correct: false },
                    { option_text: "Angular momentum quantum number (l)", is_correct: true },
                    { option_text: "Magnetic quantum number (m)", is_correct: false },
                    { option_text: "Spin quantum number (s)", is_correct: false }
                ],
                categories: ['Orbitals']
            },
            {
                question_text: "The maximum number of electrons that can be accommodated in a p-orbital is:",
                question_type: "multiple_choice",
                marks: 1,
                solution_text: "A p-orbital can accommodate a maximum of 6 electrons (3 p-orbitals with 2 electrons each).",
                options: [
                    { option_text: "2", is_correct: false },
                    { option_text: "6", is_correct: true },
                    { option_text: "10", is_correct: false },
                    { option_text: "14", is_correct: false }
                ],
                categories: ['Orbitals']
            },
            {
                question_text: "Which of the following is a p-block element?",
                question_type: "multiple_choice",
                marks: 1,
                solution_text: "Oxygen (O) is a p-block element located in Group 16 of the periodic table.",
                options: [
                    { option_text: "Sodium (Na)", is_correct: false },
                    { option_text: "Calcium (Ca)", is_correct: false },
                    { option_text: "Oxygen (O)", is_correct: true },
                    { option_text: "Iron (Fe)", is_correct: false }
                ],
                categories: ['P-Block Elements']
            },
            {
                question_text: "True or False: Carbon dioxide (CO₂) is an example of a compound formed by p-block elements.",
                question_type: "true_false",
                marks: 1,
                solution_text: "True. Carbon dioxide is formed from carbon and oxygen, both of which are p-block elements.",
                correct_answer: "true",
                categories: ['P-Block Elements']
            }
        ];
        
        // Insert Physics Questions
        for (let i = 0; i < physicsQuestions.length; i++) {
            const q = physicsQuestions[i];
            let questionId;
            
            if (q.question_type === 'multiple_choice') {
                // Insert question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position) VALUES (?, ?, ?, ?, ?, ?)',
                    [physicsSectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
                
                // Insert options
                for (let j = 0; j < q.options.length; j++) {
                    const option = q.options[j];
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, option.option_text, option.is_correct, j + 1]
                    );
                }
            } else if (q.question_type === 'true_false') {
                // Insert true/false question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, correct_answer, marks, position) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    [physicsSectionId, q.question_type, q.question_text, q.solution_text, q.correct_answer, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
            } else {
                // Insert essay question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position) VALUES (?, ?, ?, ?, ?, ?)',
                    [physicsSectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
            }
            
            // Insert category mappings
            for (const categoryName of q.categories) {
                const categoryId = categoryMap[categoryName];
                if (categoryId) {
                    await db.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
                        [questionId, categoryId]
                    );
                }
            }
            
            console.log(`Created Physics question ${i + 1} with ID: ${questionId}`);
        }
        
        // Insert Chemistry Questions
        for (let i = 0; i < chemistryQuestions.length; i++) {
            const q = chemistryQuestions[i];
            let questionId;
            
            if (q.question_type === 'multiple_choice') {
                // Insert question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position) VALUES (?, ?, ?, ?, ?, ?)',
                    [chemistrySectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
                
                // Insert options
                for (let j = 0; j < q.options.length; j++) {
                    const option = q.options[j];
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, option.option_text, option.is_correct, j + 1]
                    );
                }
            } else if (q.question_type === 'true_false') {
                // Insert true/false question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, correct_answer, marks, position) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    [chemistrySectionId, q.question_type, q.question_text, q.solution_text, q.correct_answer, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
            } else {
                // Insert essay question
                const [qResult] = await db.query(
                    'INSERT INTO questions (section_id, question_type, question_text, solution_text, marks, position) VALUES (?, ?, ?, ?, ?, ?)',
                    [chemistrySectionId, q.question_type, q.question_text, q.solution_text, q.marks, i + 1]
                );
                
                questionId = qResult.insertId;
            }
            
            // Insert category mappings
            for (const categoryName of q.categories) {
                const categoryId = categoryMap[categoryName];
                if (categoryId) {
                    await db.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
                        [questionId, categoryId]
                    );
                }
            }
            
            console.log(`Created Chemistry question ${i + 1} with ID: ${questionId}`);
        }
        
        // Commit the transaction
        await db.query('COMMIT');
        console.log('Test creation completed successfully!');
        
        return {
            success: true,
            examId: examId,
            message: 'Test created successfully'
        };
    } catch (error) {
        // Rollback on error
        await db.query('ROLLBACK');
        console.error('Error creating test:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        process.exit();
    }
}

// Run the function
createTest();
