# Database Column Usage Guide

## user_answers Table

The `user_answers` table stores responses submitted by users during exam attempts. This table has been updated to support multiple ways of storing answers.

### Column Definitions

| Column Name | Type | Description | Usage |
|------------|------|-------------|-------|
| id | int(11) | Primary key identifier | Unique identifier for each answer record |
| attempt_id | int(11) | Foreign key to exam_attempts | Links this answer to a specific test attempt |
| question_id | int(11) | Foreign key to questions | Links this answer to a specific question |
| selected_option_id | int(11) | Foreign key to options | For multiple-choice questions, stores the selected option ID |
| answer_text | text | Text answer | Stores text-based answers (used by newer application routes) |
| essay_answer | text | Text answer | Stores text-based answers (used by older application routes) |
| marks_obtained | decimal(5,2) | Marks awarded | The score awarded for this answer |
| is_bookmarked | tinyint(1) | Bookmark flag | Indicates if the question is bookmarked by the user |
| created_at | timestamp | Creation timestamp | When the answer was first recorded |
| updated_at | timestamp | Update timestamp | When the answer was last updated |

### Important Notes

1. **Dual Text Answer Fields:** The table contains both `answer_text` and `essay_answer` columns:
   - These columns serve the same purpose but may be used by different parts of the application
   - For consistency, new code should populate both columns with the same value
   - When fetching answers, check both columns using `answer.selected_option_id || answer.answer_text || answer.essay_answer || ''`

2. **Database Fix Scripts:** If database inconsistencies are encountered, run the fix script:
   ```
   node scripts/fix-user-answers-table.js
   ```

3. **Recommended Query Templates:**

   For inserting new answers:
   ```sql
   INSERT INTO user_answers (
       attempt_id, 
       question_id, 
       selected_option_id, 
       answer_text, 
       essay_answer, 
       is_bookmarked, 
       created_at
   ) VALUES (?, ?, ?, ?, ?, ?, NOW())
   ```

   For updating existing answers:
   ```sql
   UPDATE user_answers SET 
       selected_option_id = ?, 
       answer_text = ?, 
       essay_answer = ?, 
       is_bookmarked = ?, 
       updated_at = NOW() 
   WHERE attempt_id = ? AND question_id = ?
   ```

4. **Maintaining Compatibility:** To ensure compatibility with all code paths, always:
   - Update both `answer_text` and `essay_answer` with the same value
   - Check both columns when retrieving answers 