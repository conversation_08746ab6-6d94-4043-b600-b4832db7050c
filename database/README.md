# Database Documentation

## Overview
This document describes the database structure for the Meritorious Exam Preparation Platform. The platform uses MySQL/MariaDB as its database management system.

## Database Name
`exam_prep_platform`

## Tables Overview

### Core Tables
1. `users` - User account information
2. `sessions` - Session management
3. `exams` - Exam information
4. `sections` - Exam sections
5. `questions` - Questions in sections
6. `options` - MCQ options
7. `user_exam_attempts` - Exam attempts by users
8. `user_answers` - User answers to questions
9. `notifications` - User notifications

## Table Details

### Users Table
- Primary user information
- Includes authentication and profile data
- Supports password reset functionality
- Role-based access control

### Sessions Table
- Manages user sessions
- Includes expiration handling
- Supports remember-me functionality

### Exams Table
- Core exam information
- Supports draft and published states
- Tracks creation and updates

### Sections Table
- Organizes exam content
- Maintains section order
- Links to parent exam

### Questions Table
- Supports multiple question types
- Includes scoring information
- Links to parent section

### Options Table
- MCQ answer options
- Tracks correct answers
- Maintains option order

### User Exam Attempts Table
- Tracks exam attempts
- Records scores
- Monitors completion status

### User Answers Table
- Records individual answers
- Tracks scoring
- Links to attempts

### Notifications Table
- User notifications
- Read/unread status
- Different notification types

## Maintenance Procedures

### Backup
```bash
# Create a full backup
mysqldump -u root -p exam_prep_platform > backup_$(date +%Y%m%d).sql

# Backup specific tables
mysqldump -u root -p exam_prep_platform users sessions > auth_backup_$(date +%Y%m%d).sql
```

### Restore
```bash
# Restore full backup
mysql -u root -p exam_prep_platform < backup_file.sql

# Restore specific tables
mysql -u root -p exam_prep_platform < auth_backup_file.sql
```

### Database Creation
```bash
# Create database and tables
mysql -u root -p < schema.sql
```

## Security Considerations

1. **Passwords**
   - All passwords are hashed using bcrypt
   - Minimum password length: 8 characters
   - Password reset tokens expire after 1 hour

2. **Sessions**
   - HTTP-only cookies
   - Secure flag in production
   - Session timeout: 24 hours

3. **Access Control**
   - Role-based permissions
   - Admin and user roles
   - Protected routes

## Indexes

The database uses several indexes for performance:
- Email lookup
- Username and DOB for password recovery
- Foreign key relationships
- Order maintenance (positions)

## Best Practices

1. **Backups**
   - Daily full backups
   - Transaction log backups
   - Regular backup testing

2. **Maintenance**
   - Regular index optimization
   - Session cleanup
   - Log rotation

3. **Monitoring**
   - Query performance
   - Storage usage
   - Connection limits

## Schema Updates

When updating the schema:
1. Create a backup
2. Test changes in development
3. Use transactions for updates
4. Document all changes
5. Update application code
6. Test thoroughly

## Support

For database issues or questions:
1. Check error logs
2. Verify connections
3. Test queries
4. Monitor performance

## Version History

- 2024-03-09: Initial schema creation
- Added user authentication
- Added exam management
- Added notification system 