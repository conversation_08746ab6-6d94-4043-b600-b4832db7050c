-- Add new categories to the system
-- This script adds a comprehensive set of categories for different question types

-- Academic Subjects
INSERT INTO categories (name, description) VALUES 
('Chemistry', 'Chemistry concepts, reactions, and problems'),
('Biology', 'Biology concepts, processes, and systems'),
('Physics', 'Physics principles, laws, and problem-solving'),
('Computer Science', 'Computer science concepts, algorithms, and programming'),
('Environmental Science', 'Environmental systems, ecology, and sustainability'),
('Astronomy', 'Celestial objects, space phenomena, and cosmology'),
('Geology', 'Earth structure, processes, and geological formations'),
('Statistics', 'Statistical methods, probability, and data analysis'),
('Calculus', 'Differential and integral calculus concepts and problems'),
('Trigonometry', 'Trigonometric functions, identities, and applications'),
('Algebra', 'Algebraic expressions, equations, and problem-solving'),
('Geometry', 'Geometric shapes, properties, and theorems'),
('World History', 'Global historical events, periods, and figures'),
('Ancient History', 'Ancient civilizations, cultures, and historical periods'),
('Modern History', 'Recent historical events, movements, and developments'),
('Geography', 'Physical and human geography concepts'),
('Political Science', 'Political systems, theories, and international relations'),
('Economics', 'Economic principles, theories, and applications'),
('Sociology', 'Social structures, interactions, and phenomena'),
('Psychology', 'Psychological theories, processes, and behaviors'),
('Philosophy', 'Philosophical concepts, theories, and ethical frameworks'),
('Literature', 'Literary works, analysis, and critical thinking'),
('Grammar', 'Language structure, rules, and usage'),
('Vocabulary', 'Word meanings, usage, and language development');

-- Professional Fields
INSERT INTO categories (name, description) VALUES 
('Business Management', 'Business principles, management, and organizational behavior'),
('Marketing', 'Marketing concepts, strategies, and consumer behavior'),
('Finance', 'Financial principles, markets, and analysis'),
('Accounting', 'Accounting principles, practices, and financial reporting'),
('Law', 'Legal principles, systems, and case analysis'),
('Medicine', 'Medical knowledge, healthcare, and clinical concepts'),
('Engineering', 'Engineering principles, design, and problem-solving'),
('Architecture', 'Architectural design, principles, and history'),
('Agriculture', 'Agricultural systems, practices, and sustainability'),
('Information Technology', 'IT systems, infrastructure, and technologies');

-- Skill-Based Categories
INSERT INTO categories (name, description) VALUES 
('Critical Thinking', 'Analytical reasoning and problem-solving skills'),
('Logical Reasoning', 'Deductive and inductive reasoning skills'),
('Quantitative Reasoning', 'Numerical problem-solving and mathematical reasoning'),
('Verbal Reasoning', 'Language-based reasoning and comprehension'),
('Data Interpretation', 'Analysis and interpretation of data, charts, and graphs'),
('Reading Comprehension', 'Understanding and analyzing written passages'),
('Writing Skills', 'Written expression, composition, and communication'),
('Problem Solving', 'Strategies and approaches to solving complex problems'),
('Decision Making', 'Evaluating options and making informed choices'),
('Creative Thinking', 'Innovative and original thinking approaches');

-- Language Categories
INSERT INTO categories (name, description) VALUES 
('English', 'English language proficiency and skills'),
('Hindi', 'Hindi language proficiency and skills'),
('Punjabi', 'Punjabi language proficiency and skills'),
('French', 'French language proficiency and skills'),
('Spanish', 'Spanish language proficiency and skills'),
('German', 'German language proficiency and skills'),
('Chinese', 'Chinese language proficiency and skills'),
('Japanese', 'Japanese language proficiency and skills'),
('Arabic', 'Arabic language proficiency and skills'),
('Russian', 'Russian language proficiency and skills');

-- Specialized Test Categories
INSERT INTO categories (name, description) VALUES 
('Aptitude Test', 'General aptitude and cognitive ability assessment'),
('IQ Test', 'Intelligence quotient measurement and assessment'),
('Personality Assessment', 'Personality traits and behavioral assessment'),
('Career Assessment', 'Career aptitude and interest assessment'),
('Entrance Exam', 'Academic admission and qualification assessment'),
('Certification Exam', 'Professional certification and qualification assessment'),
('Competitive Exam', 'Competitive selection and ranking assessment'),
('Diagnostic Test', 'Knowledge gaps and learning needs assessment'),
('Practice Test', 'Preparation and practice for other assessments'),
('Self-Assessment', 'Personal knowledge and skill evaluation');

-- Question Format Categories
INSERT INTO categories (name, description) VALUES 
('Multiple Choice', 'Questions with multiple answer options'),
('True/False', 'Binary choice questions with true or false answers'),
('Fill in the Blank', 'Questions requiring completion with specific words'),
('Short Answer', 'Questions requiring brief written responses'),
('Essay', 'Questions requiring extended written responses'),
('Matching', 'Questions requiring matching related items'),
('Ordering', 'Questions requiring arrangement in correct sequence'),
('Diagram Labeling', 'Questions requiring identification of diagram parts'),
('Case Study', 'Questions based on detailed scenarios or cases'),
('Problem Set', 'Series of related problems requiring solutions');

-- Difficulty Level Categories
INSERT INTO categories (name, description) VALUES 
('Beginner', 'Entry-level questions for novice learners'),
('Intermediate', 'Moderate difficulty questions for developing learners'),
('Advanced', 'Challenging questions for proficient learners'),
('Expert', 'Very difficult questions for expert learners');

-- Cognitive Level Categories (Based on Bloom's Taxonomy)
INSERT INTO categories (name, description) VALUES 
('Knowledge', 'Recall of facts, terms, basic concepts, or answers'),
('Comprehension', 'Understanding of facts and ideas by organizing, comparing, translating, interpreting'),
('Application', 'Using acquired knowledge to solve problems in new situations'),
('Analysis', 'Examining and breaking information into parts, identifying motives or causes'),
('Synthesis', 'Compiling information together in a different way by combining elements'),
('Evaluation', 'Presenting and defending opinions by making judgments about information');
