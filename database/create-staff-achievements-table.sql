-- =====================================================
-- STAFF ACHIEVEMENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS staff_achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id INT NOT NULL,
    
    -- Achievement Details
    title VARCHAR(255) NOT NULL,
    category ENUM(
        'software_development',
        'teaching_excellence', 
        'educational_innovation',
        'student_mentoring',
        'research_publication',
        'professional_recognition',
        'community_service',
        'diversity_inclusion',
        'professional_certification',
        'entrepreneurship',
        'intellectual_property',
        'leadership',
        'awards_honors',
        'other'
    ) NOT NULL,
    description TEXT NOT NULL,
    achievement_date DATE,
    
    -- Recognition Level
    recognition_level ENUM(
        'institutional',    -- School/College level
        'district',        -- District level
        'state',          -- State level
        'national',       -- National level
        'international'   -- International level
    ) DEFAULT 'institutional',
    
    -- Impact and Evidence
    impact_description TEXT,
    skills_demonstrated JSON, -- ["Skill1", "Skill2", "Skill3"]
    evidence_url VARCHAR(500), -- Link to certificate, article, project, etc.
    evidence_file VARCHAR(255), -- Local file path if uploaded
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by VARCHAR(255), -- Who verified this achievement
    verification_date DATE,
    verification_notes TEXT,
    
    -- Additional Details
    collaborators TEXT, -- Other people involved
    organization_name VARCHAR(255), -- Organization that recognized the achievement
    award_amount DECIMAL(10,2), -- If monetary award
    currency VARCHAR(10) DEFAULT 'INR',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
    INDEX idx_staff_achievements (staff_id, category),
    INDEX idx_achievement_date (achievement_date),
    INDEX idx_recognition_level (recognition_level),
    INDEX idx_verified (is_verified)
);

-- =====================================================
-- SAMPLE ACHIEVEMENT CATEGORIES REFERENCE
-- =====================================================
/*
SOFTWARE DEVELOPMENT:
- Web/Mobile Applications
- Open Source Contributions
- System Architecture
- Database Design
- API Development
- DevOps/Cloud Solutions

TEACHING EXCELLENCE:
- Best Teacher Awards
- Student Performance Improvements
- Innovative Teaching Methods
- Curriculum Development

EDUCATIONAL INNOVATION:
- New Teaching Technologies
- Learning Management Systems
- Educational Tools/Apps
- Pedagogical Innovations

STUDENT MENTORING:
- Competition Coaching
- Career Guidance
- Skill Development Programs
- Student Success Stories

RESEARCH & PUBLICATION:
- Research Papers
- Conference Presentations
- Book Publications
- Patent Applications

PROFESSIONAL RECOGNITION:
- Industry Certifications
- Speaking Engagements
- Expert Panels
- Advisory Roles

COMMUNITY SERVICE:
- Volunteer Work
- Social Impact Projects
- NGO Contributions
- Public Service

DIVERSITY & INCLUSION:
- Women in Tech Initiatives
- Accessibility Projects
- Inclusive Education Programs
- Minority Support Programs

ENTREPRENEURSHIP:
- Startup Founding
- Business Development
- Product Launches
- Revenue Generation

LEADERSHIP:
- Team Management
- Project Leadership
- Organizational Change
- Strategic Planning
*/

-- =====================================================
-- VIEWS FOR EASY QUERYING
-- =====================================================

-- View for achievement summary by staff
CREATE OR REPLACE VIEW staff_achievement_summary AS
SELECT 
    s.id as staff_id,
    u.name as staff_name,
    u.email as staff_email,
    COUNT(sa.id) as total_achievements,
    COUNT(CASE WHEN sa.is_verified = TRUE THEN 1 END) as verified_achievements,
    COUNT(CASE WHEN sa.recognition_level = 'international' THEN 1 END) as international_achievements,
    COUNT(CASE WHEN sa.recognition_level = 'national' THEN 1 END) as national_achievements,
    COUNT(CASE WHEN sa.recognition_level = 'state' THEN 1 END) as state_achievements,
    COUNT(CASE WHEN sa.category = 'software_development' THEN 1 END) as software_achievements,
    COUNT(CASE WHEN sa.category = 'teaching_excellence' THEN 1 END) as teaching_achievements,
    COUNT(CASE WHEN sa.category = 'research_publication' THEN 1 END) as research_achievements,
    MAX(sa.achievement_date) as latest_achievement_date
FROM staff s
LEFT JOIN users u ON s.user_id = u.id
LEFT JOIN staff_achievements sa ON s.id = sa.staff_id
WHERE u.role = 'teacher'
GROUP BY s.id, u.name, u.email
ORDER BY total_achievements DESC;

-- View for achievement timeline
CREATE OR REPLACE VIEW staff_achievement_timeline AS
SELECT 
    sa.*,
    u.name as staff_name,
    u.email as staff_email,
    YEAR(sa.achievement_date) as achievement_year,
    MONTH(sa.achievement_date) as achievement_month
FROM staff_achievements sa
JOIN staff s ON sa.staff_id = s.id
JOIN users u ON s.user_id = u.id
ORDER BY sa.staff_id, sa.achievement_date DESC;

-- =====================================================
-- SAMPLE QUERIES
-- =====================================================

-- Get all achievements for a specific teacher
-- SELECT * FROM staff_achievements WHERE staff_id = ? ORDER BY achievement_date DESC;

-- Get achievement summary for a teacher
-- SELECT * FROM staff_achievement_summary WHERE staff_id = ?;

-- Get achievements by category
-- SELECT category, COUNT(*) as count FROM staff_achievements WHERE staff_id = ? GROUP BY category;

-- Get recent achievements (last 2 years)
-- SELECT * FROM staff_achievements 
-- WHERE staff_id = ? AND achievement_date >= DATE_SUB(CURDATE(), INTERVAL 2 YEAR)
-- ORDER BY achievement_date DESC;

-- Get top achievements by recognition level
-- SELECT * FROM staff_achievements 
-- WHERE staff_id = ? AND recognition_level IN ('international', 'national')
-- ORDER BY FIELD(recognition_level, 'international', 'national'), achievement_date DESC;
