-- Create the question_category_mappings table if it doesn't exist
CREATE TABLE IF NOT EXISTS `question_category_mappings` (
  `mapping_id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `question_category_unique` (`question_id`, `category_id`),
  KEY `question_id` (`question_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `question_category_mappings_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE,
  CONSTRAINT `question_category_mappings_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create a view to get questions with their categories
CREATE OR REPLACE VIEW `question_categories_view` AS
SELECT 
    q.question_id,
    q.question_text,
    q.question_type,
    GROUP_CONCAT(c.name ORDER BY c.name SEPARATOR ', ') AS category_names
FROM 
    questions q
LEFT JOIN 
    question_category_mappings qcm ON q.question_id = qcm.question_id
LEFT JOIN 
    categories c ON qcm.category_id = c.category_id
WHERE 
    q.is_deleted = 0
GROUP BY 
    q.question_id;

-- Add a trigger to update the category_names field in the questions table when mappings change
DELIMITER //
CREATE TRIGGER IF NOT EXISTS update_question_categories_after_insert
AFTER INSERT ON question_category_mappings
FOR EACH ROW
BEGIN
    -- Get all category names for this question
    SET @category_names = (
        SELECT GROUP_CONCAT(c.name ORDER BY c.name SEPARATOR ', ')
        FROM categories c
        JOIN question_category_mappings qcm ON c.category_id = qcm.category_id
        WHERE qcm.question_id = NEW.question_id
    );
    
    -- Update the question's category_names field
    UPDATE questions
    SET category_names = @category_names
    WHERE question_id = NEW.question_id;
END //

CREATE TRIGGER IF NOT EXISTS update_question_categories_after_delete
AFTER DELETE ON question_category_mappings
FOR EACH ROW
BEGIN
    -- Get all category names for this question
    SET @category_names = (
        SELECT GROUP_CONCAT(c.name ORDER BY c.name SEPARATOR ', ')
        FROM categories c
        JOIN question_category_mappings qcm ON c.category_id = qcm.category_id
        WHERE qcm.question_id = OLD.question_id
    );
    
    -- Update the question's category_names field
    UPDATE questions
    SET category_names = @category_names
    WHERE question_id = OLD.question_id;
END //
DELIMITER ;

-- Add category_names column to questions table if it doesn't exist
ALTER TABLE questions ADD COLUMN IF NOT EXISTS category_names TEXT DEFAULT NULL;
