-- Create essays table
CREATE TABLE IF NOT EXISTS `essays` (
  `essay_id` INT NOT NULL AUTO_INCREMENT,
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `pdf_path` VARCHAR(255) NULL,
  `created_by` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`essay_id`),
  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Alter questions table to add essay_id
ALTER TABLE `questions` 
ADD COLUMN `essay_id` INT NULL,
ADD FOREIGN KEY (`essay_id`) REFERENCES `essays` (`essay_id`) ON DELETE SET NULL;
