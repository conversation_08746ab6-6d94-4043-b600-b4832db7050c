-- Check if is_bookmarked column exists in user_answers table
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_answers' 
AND COLUMN_NAME = 'is_bookmarked';

-- Add the column if it doesn't exist
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE user_answers ADD COLUMN is_bookmarked TINYINT(1) NOT NULL DEFAULT 0 AFTER essay_answer',
    'SELECT "Column already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if answer_text column exists in user_answers table
SELECT COUNT(*) INTO @answer_text_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_answers' 
AND COLUMN_NAME = 'answer_text';

-- Add the column if it doesn't exist
SET @query_answer_text = IF(@answer_text_exists = 0, 
    'ALTER TABLE user_answers ADD COLUMN answer_text TEXT NULL AFTER selected_option_id',
    'SELECT "answer_text column already exists"');

PREPARE stmt FROM @query_answer_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 