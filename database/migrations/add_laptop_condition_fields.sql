-- Add laptop condition fields to inventory_items table
ALTER TABLE `inventory_items` 
ADD COLUMN `physical_damage` VARCHAR(255) NULL AFTER `image`,
ADD COLUMN `keyboard_condition` VARCHAR(255) NULL AFTER `physical_damage`,
ADD COLUMN `touchpad_condition` VARCHAR(255) NULL AFTER `keyboard_condition`,
ADD COLUMN `hdmi_port_condition` VARCHAR(255) NULL AFTER `touchpad_condition`,
ADD COLUMN `ethernet_wifi_condition` VARCHAR(255) NULL AFTER `hdmi_port_condition`,
ADD COLUMN `vga_port_condition` VARCHAR(255) NULL AFTER `ethernet_wifi_condition`,
ADD COLUMN `usb_port_condition` VARCHAR(255) NULL AFTER `vga_port_condition`,
ADD COLUMN `speaker_port_condition` VARCHAR(255) NULL AFTER `usb_port_condition`,
ADD COLUMN `speakers_condition` VARCHAR(255) NULL AFTER `speaker_port_condition`,
ADD COLUMN `display_condition` VARCHAR(255) NULL AFTER `speakers_condition`,
ADD COLUMN `cd_drive_condition` VARCHAR(255) NULL AFTER `display_condition`,
ADD COLUMN `webcam_condition` VARCHAR(255) NULL AFTER `cd_drive_condition`,
ADD COLUMN `charger_port_condition` VARCHAR(255) NULL AFTER `webcam_condition`,
ADD COLUMN `os_drivers_condition` VARCHAR(255) NULL AFTER `charger_port_condition`,
ADD COLUMN `laptop_bag_condition` VARCHAR(255) NULL AFTER `os_drivers_condition`;
