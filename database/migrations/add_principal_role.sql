-- Add Principal Role to Database
-- This script adds the principal role to the users table and creates a sample principal user

-- First, check if the users table has the principal role in the enum
-- If not, we need to modify the enum to include 'principal'

-- Modify the users table to include principal role
ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'teacher', 'student', 'principal') DEFAULT 'student';

-- Insert a sample principal user
-- Password is 'principal123' hashed with bcrypt
INSERT INTO users (
    username,
    name,
    email,
    password,
    role,
    full_name,
    bio,
    is_active,
    created_at,
    updated_at
) VALUES (
    'principal',
    'Dr. <PERSON>',
    '<EMAIL>',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'principal',
    'Dr. <PERSON>',
    'School Principal with executive leadership and strategic oversight responsibilities.',
    1,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    email = VALUES(email),
    role = VALUES(role),
    full_name = VALUES(full_name),
    bio = VALUES(bio),
    is_active = VALUES(is_active),
    updated_at = VALUES(updated_at);

-- Add principal role to roles table if it exists
INSERT IGNORE INTO roles (role_name, description, is_system) VALUES
('principal', 'School Principal with comprehensive oversight access', TRUE);

-- Add principal permissions if permissions table exists
INSERT IGNORE INTO permissions (permission_name, description, category) VALUES
-- Principal-specific permissions
('view_school_overview', 'View comprehensive school statistics and overview', 'principal'),
('view_academic_progress', 'View academic progress across all classes and subjects', 'principal'),
('view_teacher_performance', 'View teacher performance metrics and analytics', 'principal'),
('view_student_analytics', 'View student analytics and performance data', 'principal'),
('view_infrastructure_overview', 'View infrastructure and resource utilization', 'principal'),
('generate_reports', 'Generate comprehensive school reports', 'principal'),
('view_all_timetables', 'View all teacher and class timetables', 'principal'),
('monitor_syllabus_completion', 'Monitor syllabus completion across all subjects', 'principal');

-- Grant principal permissions to principal role if role_permissions table exists
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'principal'
AND p.category = 'principal';

-- Also grant some admin permissions to principal
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r, permissions p
WHERE r.role_name = 'principal'
AND p.permission_name IN (
    'view_users',
    'view_reports',
    'view_analytics',
    'view_system_status'
);

-- Create a view for principal dashboard statistics (optional)
CREATE OR REPLACE VIEW principal_dashboard_stats AS
SELECT
    (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
    (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
    (SELECT COUNT(*) FROM classes) as total_classes,
    (SELECT COUNT(*) FROM subjects) as total_subjects,
    (SELECT COUNT(*) FROM teacher_lectures WHERE date = CURDATE()) as today_lectures,
    (SELECT COUNT(*) FROM teacher_lectures WHERE date = CURDATE() AND status = 'delivered') as delivered_lectures,
    (SELECT ROUND(AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END), 2) FROM teacher_lectures) as avg_syllabus_completion;

-- Update any existing principal user's password (if needed)
-- This is commented out for security - uncomment and modify if needed
-- UPDATE users SET password = '$2b$10$newHashedPasswordHere' WHERE username = 'principal';

-- Verify the changes
SELECT 'Principal role setup completed successfully' as message;

-- Show the principal user that was created/updated
SELECT id, username, name, email, role, is_active, created_at
FROM users
WHERE role = 'principal';

-- Show available roles
SELECT role_name, description FROM roles WHERE role_name IN ('admin', 'teacher', 'student', 'principal');
