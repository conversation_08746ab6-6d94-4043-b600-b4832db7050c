-- Add missing profile fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS institution VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS grade VARCHAR(50) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS field_of_study VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS preferred_subjects VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS target_exams VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS study_goal VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS language_preference VARCHAR(50) DEFAULT 'English',
ADD COLUMN IF NOT EXISTS time_zone VARCHAR(50) DEFAULT 'Asia/Kolkata',
ADD COLUMN IF NOT EXISTS accessibility_needs VARCHAR(50) DEFAULT 'None';
