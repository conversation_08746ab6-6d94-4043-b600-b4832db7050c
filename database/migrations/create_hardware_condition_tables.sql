-- Create hardware_parts table to store different parts for different hardware types
CREATE TABLE IF NOT EXISTS `hardware_parts` (
  `part_id` INT AUTO_INCREMENT PRIMARY KEY,
  `hardware_type` VARCHAR(50) NOT NULL,
  `part_name` VARCHAR(100) NOT NULL,
  `display_name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_part_per_type` (`hardware_type`, `part_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create hardware_condition table to store condition data for each item-part combination
CREATE TABLE IF NOT EXISTS `hardware_condition` (
  `condition_id` INT AUTO_INCREMENT PRIMARY KEY,
  `item_id` INT NOT NULL,
  `part_id` INT NOT NULL,
  `condition_value` VARCHAR(50) NOT NULL,
  `notes` TEXT NULL,
  `checked_by` INT NULL,
  `checked_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `transaction_id` INT NULL,
  <PERSON>OR<PERSON><PERSON>N KEY (`item_id`) REFERENCES `inventory_items` (`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`part_id`) REFERENCES `hardware_parts` (`part_id`) ON DELETE CASCADE,
  FOREIGN KEY (`checked_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`transaction_id`) REFERENCES `inventory_transactions` (`transaction_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add condition_check_required field to inventory_transactions table
ALTER TABLE `inventory_transactions` 
ADD COLUMN `condition_check_required` TINYINT(1) DEFAULT 0 AFTER `notes`,
ADD COLUMN `condition_check_completed` TINYINT(1) DEFAULT 0 AFTER `condition_check_required`;

-- Insert default hardware parts for laptops
INSERT INTO `hardware_parts` (`hardware_type`, `part_name`, `display_name`, `description`) VALUES
('laptop', 'physical_damage', 'Physical Damage', 'Overall physical condition of the laptop'),
('laptop', 'keyboard', 'Keyboard', 'Keyboard functionality'),
('laptop', 'touchpad', 'Touchpad', 'Touchpad/trackpad functionality'),
('laptop', 'hdmi_port', 'HDMI Port', 'HDMI port functionality'),
('laptop', 'ethernet_wifi', 'Ethernet Port & WiFi', 'Network connectivity'),
('laptop', 'vga_port', 'VGA Port', 'VGA port functionality'),
('laptop', 'usb_port', 'USB Ports', 'USB ports functionality'),
('laptop', 'speaker_port', 'Speaker Port', 'Audio output port'),
('laptop', 'speakers', 'Speakers', 'Built-in speakers functionality'),
('laptop', 'display', 'Display', 'Screen condition and functionality'),
('laptop', 'cd_drive', 'CD Drive', 'Optical drive functionality'),
('laptop', 'webcam', 'Web Camera', 'Webcam functionality'),
('laptop', 'charger_port', 'Charger & Charging Port', 'Power adapter and charging port'),
('laptop', 'os_drivers', 'OS & Drivers', 'Operating system and drivers status'),
('laptop', 'laptop_bag', 'Laptop Bag', 'Condition of the laptop bag');

-- Insert default hardware parts for speakers
INSERT INTO `hardware_parts` (`hardware_type`, `part_name`, `display_name`, `description`) VALUES
('speaker', 'physical_damage', 'Physical Damage', 'Overall physical condition of the speaker'),
('speaker', 'power_button', 'Power Button', 'Power button functionality'),
('speaker', 'volume_controls', 'Volume Controls', 'Volume adjustment controls'),
('speaker', 'audio_output', 'Audio Output', 'Sound quality and output'),
('speaker', 'bluetooth', 'Bluetooth Connectivity', 'Bluetooth connection functionality'),
('speaker', 'aux_port', 'AUX Port', 'Auxiliary input port'),
('speaker', 'charging_port', 'Charging Port', 'Power/charging port'),
('speaker', 'battery', 'Battery', 'Battery life and condition');

-- Insert default hardware parts for projectors
INSERT INTO `hardware_parts` (`hardware_type`, `part_name`, `display_name`, `description`) VALUES
('projector', 'physical_damage', 'Physical Damage', 'Overall physical condition of the projector'),
('projector', 'lamp', 'Lamp/Bulb', 'Projector lamp condition and brightness'),
('projector', 'display_quality', 'Display Quality', 'Image quality and resolution'),
('projector', 'hdmi_port', 'HDMI Port', 'HDMI input port'),
('projector', 'vga_port', 'VGA Port', 'VGA input port'),
('projector', 'remote', 'Remote Control', 'Remote control functionality'),
('projector', 'speakers', 'Built-in Speakers', 'Internal speaker functionality'),
('projector', 'cooling_fan', 'Cooling Fan', 'Cooling system functionality'),
('projector', 'power_cable', 'Power Cable', 'Power cable condition');
