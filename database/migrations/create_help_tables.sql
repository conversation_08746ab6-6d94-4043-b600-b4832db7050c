-- Help content table
CREATE TABLE IF NOT EXISTS help_articles (
    article_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('general', 'exams', 'questions', 'profile', 'groups', 'admin') NOT NULL DEFAULT 'general',
    slug VARCHAR(255) NOT NULL UNIQUE,
    is_published BOOLEAN DEFAULT TRUE,
    view_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Help categories for organization
CREATE TABLE IF NOT EXISTS help_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default help categories
INSERT INTO help_categories (name, description, icon, display_order) VALUES
('Getting Started', 'Basic information for new users', 'book-open', 1),
('Account Management', 'Managing your account and profile', 'user', 2),
('Exams', 'Taking and managing exams', 'file-text', 3),
('Groups', 'Working with user groups', 'users', 4),
('For Administrators', 'Help for system administrators', 'shield', 5);

-- Help article to category mapping
CREATE TABLE IF NOT EXISTS help_article_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    article_id INT NOT NULL,
    category_id INT NOT NULL,
    FOREIGN KEY (article_id) REFERENCES help_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES help_categories(category_id) ON DELETE CASCADE,
    UNIQUE KEY unique_article_category (article_id, category_id)
);

-- Help article feedback
CREATE TABLE IF NOT EXISTS help_feedback (
    feedback_id INT AUTO_INCREMENT PRIMARY KEY,
    article_id INT NOT NULL,
    user_id INT,
    is_helpful BOOLEAN NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES help_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
