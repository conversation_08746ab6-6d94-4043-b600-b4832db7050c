-- Check if help_articles table exists, if not create it
CREATE TABLE IF NOT EXISTS help_articles (
    article_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    is_published BOOLEAN DEFAULT TRUE,
    view_count INT DEFAULT 0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Check if help_categories table exists, if not create it
CREATE TABLE IF NOT EXISTS help_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default help categories if they don't exist
INSERT IGNORE INTO help_categories (name, description, icon, display_order) VALUES
('Getting Started', 'Basic information for new users', 'book-open', 1),
('Account Management', 'Managing your account and profile', 'user', 2),
('Exams', 'Taking and managing exams', 'file-text', 3),
('Groups', 'Working with user groups', 'users', 4),
('For Administrators', 'Help for system administrators', 'shield', 5);

-- Check if help_article_categories table exists, if not create it
CREATE TABLE IF NOT EXISTS help_article_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    article_id INT NOT NULL,
    category_id INT NOT NULL,
    FOREIGN KEY (article_id) REFERENCES help_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES help_categories(category_id) ON DELETE CASCADE,
    UNIQUE KEY unique_article_category (article_id, category_id)
);

-- Check if help_feedback table exists, if not create it
CREATE TABLE IF NOT EXISTS help_feedback (
    feedback_id INT AUTO_INCREMENT PRIMARY KEY,
    article_id INT NOT NULL,
    user_id INT,
    is_helpful BOOLEAN NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES help_articles(article_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert a sample help article if none exist
INSERT INTO help_articles (title, content, slug, is_published, created_by)
SELECT 'Welcome to the Help Center', 
       '<h2>Welcome to the Meritorious Exam Preparation Platform Help Center</h2><p>This help center provides information and guidance on how to use the platform effectively. Browse the categories or use the search function to find answers to your questions.</p><p>If you need further assistance, please contact the administrator.</p>', 
       'welcome-to-help-center', 
       1, 
       (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM help_articles LIMIT 1);

-- Link the sample article to the Getting Started category
INSERT IGNORE INTO help_article_categories (article_id, category_id)
SELECT 
    (SELECT article_id FROM help_articles WHERE slug = 'welcome-to-help-center' LIMIT 1),
    (SELECT category_id FROM help_categories WHERE name = 'Getting Started' LIMIT 1)
FROM dual
WHERE EXISTS (SELECT 1 FROM help_articles WHERE slug = 'welcome-to-help-center' LIMIT 1);
