-- Create IT inventory tables

-- Table for inventory categories
CREATE TABLE IF NOT EXISTS `inventory_categories` (
  `category_id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `created_at` TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for inventory items
CREATE TABLE IF NOT EXISTS `inventory_items` (
  `item_id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `category_id` INT NULL,
  `serial_number` VARCHAR(100) NULL,
  `model` VARCHAR(100) NULL,
  `manufacturer` VARCHAR(100) NULL,
  `purchase_date` DATE NULL,
  `purchase_cost` DECIMAL(10,2) NULL,
  `warranty_expiry` DATE NULL,
  `status` ENUM('available', 'assigned', 'maintenance', 'retired') DEFAULT 'available',
  `location` VARCHAR(100) NULL,
  `notes` TEXT NULL,
  `created_by` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`),
  FOREIGN KEY (`category_id`) REFERENCES `inventory_categories` (`category_id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for inventory transactions (issue/receive)
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
  `transaction_id` INT NOT NULL AUTO_INCREMENT,
  `item_id` INT NOT NULL,
  `transaction_type` ENUM('issue', 'receive') NOT NULL,
  `issued_to` INT NULL,
  `issued_by` INT NOT NULL,
  `issued_date` DATETIME NOT NULL,
  `expected_return_date` DATE NULL,
  `received_date` DATETIME NULL,
  `received_by` INT NULL,
  `condition_on_issue` VARCHAR(100) NULL,
  `condition_on_return` VARCHAR(100) NULL,
  `notes` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transaction_id`),
  FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`item_id`),
  FOREIGN KEY (`issued_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`),
  FOREIGN KEY (`received_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some default categories
INSERT INTO `inventory_categories` (`name`, `description`) VALUES 
('Computers', 'Desktop computers, laptops, and servers'),
('Peripherals', 'Keyboards, mice, monitors, and other accessories'),
('Networking', 'Routers, switches, access points, and network cables'),
('Mobile Devices', 'Smartphones, tablets, and other portable devices'),
('Software', 'Software licenses and subscriptions'),
('Other', 'Miscellaneous IT equipment');
