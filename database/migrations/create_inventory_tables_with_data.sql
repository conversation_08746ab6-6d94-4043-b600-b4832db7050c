-- Create IT inventory tables

-- Drop existing tables if they exist
DROP TABLE IF EXISTS `inventory_transactions`;
DROP TABLE IF EXISTS `inventory_items`;
DROP TABLE IF EXISTS `inventory_categories`;

-- Table for inventory categories
CREATE TABLE IF NOT EXISTS `inventory_categories` (
  `category_id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for inventory items
CREATE TABLE IF NOT EXISTS `inventory_items` (
  `item_id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT NULL,
  `category_id` INT NULL,
  `serial_number` VARCHAR(100) NULL,
  `model` VARCHAR(100) NULL,
  `manufacturer` VARCHAR(100) NULL,
  `purchase_date` DATE NULL,
  `purchase_cost` DECIMAL(10,2) NULL,
  `warranty_expiry` DATE NULL,
  `status` ENUM('available', 'assigned', 'maintenance', 'retired') DEFAULT 'available',
  `location` VARCHAR(100) NULL,
  `notes` TEXT NULL,
  `created_by` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`),
  FOREIGN KEY (`category_id`) REFERENCES `inventory_categories` (`category_id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for inventory transactions (issue/receive)
CREATE TABLE IF NOT EXISTS `inventory_transactions` (
  `transaction_id` INT NOT NULL AUTO_INCREMENT,
  `item_id` INT NOT NULL,
  `transaction_type` ENUM('issue', 'receive') NOT NULL,
  `issued_to` INT NULL,
  `issued_by` INT NOT NULL,
  `issued_date` DATETIME NOT NULL,
  `expected_return_date` DATE NULL,
  `received_date` DATETIME NULL,
  `received_by` INT NULL,
  `condition_on_issue` VARCHAR(100) NULL,
  `condition_on_return` VARCHAR(100) NULL,
  `notes` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transaction_id`),
  FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`item_id`),
  FOREIGN KEY (`issued_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`),
  FOREIGN KEY (`received_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample categories
INSERT INTO `inventory_categories` (`name`, `description`) VALUES
('Computers', 'Desktop computers, laptops, and servers'),
('Peripherals', 'Keyboards, mice, monitors, and other accessories'),
('Networking', 'Routers, switches, access points, and network cables'),
('Mobile Devices', 'Smartphones, tablets, and other portable devices'),
('Software', 'Software licenses and subscriptions'),
('Other', 'Miscellaneous IT equipment');

-- Get a valid user ID for created_by
SET @admin_id = (SELECT id FROM users WHERE role = 'admin' LIMIT 1);
SET @admin_id = IFNULL(@admin_id, (SELECT id FROM users LIMIT 1));

-- Insert sample inventory items
INSERT INTO `inventory_items` (`name`, `description`, `category_id`, `serial_number`, `model`, `manufacturer`, `purchase_date`, `purchase_cost`, `warranty_expiry`, `status`, `location`, `notes`, `created_by`) VALUES
('Dell Latitude 5420', 'Business laptop for staff use', 1, 'DL5420-2023-001', 'Latitude 5420', 'Dell', '2023-01-15', 1200.00, '2026-01-15', 'available', 'IT Storage Room', 'New laptop for staff', @admin_id),
('HP ProDesk 400 G7', 'Desktop computer for lab', 1, 'HP400G7-2022-005', 'ProDesk 400 G7', 'HP', '2022-08-10', 850.00, '2025-08-10', 'available', 'Computer Lab 2', 'Standard desktop configuration', @admin_id),
('Logitech MX Master 3', 'Wireless mouse', 2, 'LMX3-2023-012', 'MX Master 3', 'Logitech', '2023-03-20', 99.99, '2024-03-20', 'available', 'IT Storage Room', 'Ergonomic mouse for staff', @admin_id),
('Cisco Catalyst 2960', '24-Port Gigabit Switch', 3, 'CC2960-2021-002', 'Catalyst 2960', 'Cisco', '2021-11-05', 1500.00, '2026-11-05', 'available', 'Server Room', 'Main network switch for admin building', @admin_id),
('iPad Air 4th Gen', 'Tablet for presentations', 4, 'IPAD4-2022-007', 'iPad Air 4', 'Apple', '2022-05-18', 599.00, '2024-05-18', 'assigned', 'Principal Office', 'Used for presentations and meetings', @admin_id),
('Microsoft Office 365', 'Office suite subscription', 5, 'MS365-2023-100', 'Office 365 Education', 'Microsoft', '2023-01-01', 1500.00, '2024-01-01', 'available', 'N/A', '100 user license for staff and faculty', @admin_id),
('Epson EB-X51', 'Projector for classrooms', 6, 'EEB-X51-2022-003', 'EB-X51', 'Epson', '2022-07-12', 450.00, '2025-07-12', 'available', 'IT Storage Room', 'Spare projector for classrooms', @admin_id),
('ThinkPad X1 Carbon', 'Ultrabook for administrative staff', 1, 'TPX1C-2023-002', 'X1 Carbon Gen 10', 'Lenovo', '2023-02-10', 1400.00, '2026-02-10', 'assigned', 'Admin Office', 'Assigned to administrative staff', @admin_id),
('Dell UltraSharp U2720Q', '27-inch 4K Monitor', 2, 'DU2720Q-2022-008', 'UltraSharp U2720Q', 'Dell', '2022-09-15', 450.00, '2025-09-15', 'available', 'IT Storage Room', 'High-resolution monitor for design work', @admin_id),
('TP-Link Archer AX50', 'Wi-Fi 6 Router', 3, 'TPAX50-2023-004', 'Archer AX50', 'TP-Link', '2023-04-05', 150.00, '2025-04-05', 'available', 'Server Room', 'Secondary Wi-Fi router for guest network', @admin_id);

-- Get some user IDs for transactions
SET @user1_id = (SELECT id FROM users WHERE id <> @admin_id LIMIT 1);
SET @user1_id = IFNULL(@user1_id, @admin_id);
SET @user2_id = (SELECT id FROM users WHERE id <> @admin_id AND id <> @user1_id LIMIT 1);
SET @user2_id = IFNULL(@user2_id, @user1_id);

-- Insert sample transactions
INSERT INTO `inventory_transactions` (`item_id`, `transaction_type`, `issued_to`, `issued_by`, `issued_date`, `expected_return_date`, `received_date`, `received_by`, `condition_on_issue`, `condition_on_return`, `notes`) VALUES
(5, 'issue', @user1_id, @admin_id, '2023-05-20 09:30:00', '2023-12-31', NULL, NULL, 'Excellent', NULL, 'Issued for presentations and meetings'),
(8, 'issue', @user2_id, @admin_id, '2023-02-15 10:15:00', '2023-12-31', NULL, NULL, 'Excellent', NULL, 'Issued for administrative work'),
(3, 'issue', @user1_id, @admin_id, '2023-04-10 14:00:00', '2023-05-10', '2023-05-09 16:30:00', @admin_id, 'Good', 'Good', 'Temporarily issued for special project'),
(7, 'issue', @user2_id, @admin_id, '2023-07-15 11:45:00', '2023-07-20', '2023-07-20 15:15:00', @admin_id, 'Excellent', 'Good', 'Used for classroom presentation');
