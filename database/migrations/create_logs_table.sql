-- Create logs table
CREATE TABLE IF NOT EXISTS `logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  `level` enum('info','warning','error','debug') NOT NULL DEFAULT 'info',
  `category` varchar(50) NOT NULL DEFAULT 'system',
  `operation` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_uri` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`),
  <PERSON><PERSON>Y `logs_user_id_foreign` (`user_id`),
  <PERSON><PERSON><PERSON> `logs_level_index` (`level`),
  <PERSON><PERSON>Y `logs_category_index` (`category`),
  <PERSON><PERSON>Y `logs_timestamp_index` (`timestamp`),
  CONSTRAINT `logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
