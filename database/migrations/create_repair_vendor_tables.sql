-- Create repair vendors table
CREATE TABLE IF NOT EXISTS `repair_vendors` (
  `vendor_id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHA<PERSON>(255) NOT NULL,
  `contact_person` VARCHAR(255) NULL,
  `phone` VARCHAR(50) NULL,
  `email` VARCHAR(255) NULL,
  `address` TEXT NULL,
  `specialization` VARCHAR(255) NULL,
  `notes` TEXT NULL,
  `is_active` TINYINT(1) NOT NULL DEFAULT 1,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create repair history table
CREATE TABLE IF NOT EXISTS `repair_history` (
  `repair_id` INT AUTO_INCREMENT PRIMARY KEY,
  `item_id` INT NOT NULL,
  `vendor_id` INT NOT NULL,
  `sent_date` DATE NOT NULL,
  `expected_return_date` DATE NULL,
  `returned_date` DATE NULL,
  `sent_by` INT NOT NULL,
  `received_by` INT NULL,
  `issue_description` TEXT NOT NULL,
  `repair_cost` DECIMAL(10,2) NULL,
  `repair_details` TEXT NULL,
  `status` ENUM('sent', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'sent',
  `notes` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`item_id`) ON DELETE CASCADE,
  FOREIGN KEY (`vendor_id`) REFERENCES `repair_vendors` (`vendor_id`) ON DELETE CASCADE,
  FOREIGN KEY (`sent_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`received_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add repair_vendor_id field to inventory_transactions table
ALTER TABLE `inventory_transactions` 
ADD COLUMN `repair_vendor_id` INT NULL AFTER `condition_check_completed`,
ADD COLUMN `from_repair` TINYINT(1) DEFAULT 0 AFTER `repair_vendor_id`,
ADD FOREIGN KEY (`repair_vendor_id`) REFERENCES `repair_vendors` (`vendor_id`) ON DELETE SET NULL;

-- Insert sample repair vendors
INSERT INTO `repair_vendors` (`name`, `contact_person`, `phone`, `email`, `address`, `specialization`, `notes`) VALUES
('TechFix Solutions', 'John Smith', '************', '<EMAIL>', '123 Repair St, Tech City', 'Laptops, Desktops', 'Preferred vendor for computer repairs'),
('Display Masters', 'Sarah Johnson', '************', '<EMAIL>', '456 Screen Ave, Display Town', 'Monitors, Projectors, Interactive Panels', 'Specializes in display technology repairs'),
('AudioVision Repairs', 'Mike Brown', '************', '<EMAIL>', '789 Sound Blvd, Audio City', 'Speakers, Microphones, AV Equipment', 'Good for audio equipment repairs');
