-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE COMMENT 'True for built-in roles that cannot be deleted',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    permission_id INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL COMMENT 'Category for grouping permissions (e.g., users, tests, questions)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIG<PERSON> KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
);

-- Insert default roles
INSERT INTO roles (role_name, description, is_system) VALUES
('admin', 'Administrator with full system access', TRUE),
('teacher', 'Teacher with access to create and manage tests', TRUE),
('student', 'Student with access to take tests', TRUE);

-- Insert default permissions
INSERT INTO permissions (permission_name, description, category) VALUES
-- User permissions
('view_users', 'View user list', 'users'),
('create_users', 'Create new users', 'users'),
('edit_users', 'Edit user details', 'users'),
('delete_users', 'Delete users', 'users'),
('block_users', 'Block/unblock users', 'users'),
('approve_users', 'Approve new user registrations', 'users'),

-- Role permissions
('view_roles', 'View roles', 'roles'),
('create_roles', 'Create new roles', 'roles'),
('edit_roles', 'Edit role details and permissions', 'roles'),
('delete_roles', 'Delete roles', 'roles'),

-- Group permissions
('view_groups', 'View groups', 'groups'),
('create_groups', 'Create new groups', 'groups'),
('edit_groups', 'Edit group details', 'groups'),
('delete_groups', 'Delete groups', 'groups'),
('manage_group_members', 'Add/remove group members', 'groups'),

-- Test permissions
('view_tests', 'View tests', 'tests'),
('create_tests', 'Create new tests', 'tests'),
('edit_tests', 'Edit test details', 'tests'),
('delete_tests', 'Delete tests', 'tests'),
('publish_tests', 'Publish tests', 'tests'),
('archive_tests', 'Archive tests', 'tests'),
('assign_tests', 'Assign tests to users/groups', 'tests'),

-- Question permissions
('view_questions', 'View questions', 'questions'),
('create_questions', 'Create new questions', 'questions'),
('edit_questions', 'Edit question details', 'questions'),
('delete_questions', 'Delete questions', 'questions'),
('import_questions', 'Import questions', 'questions'),

-- Report permissions
('view_reports', 'View reports', 'reports'),
('export_reports', 'Export reports', 'reports'),

-- Settings permissions
('manage_settings', 'Manage system settings', 'settings'),
('manage_email_templates', 'Manage email templates', 'settings'),
('view_logs', 'View system logs', 'settings');

-- Assign all permissions to admin role
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT role_id FROM roles WHERE role_name = 'admin'),
    permission_id
FROM permissions;

-- Assign teacher permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT role_id FROM roles WHERE role_name = 'teacher'),
    permission_id
FROM permissions
WHERE permission_name IN (
    'view_tests', 'create_tests', 'edit_tests', 'delete_tests', 'publish_tests',
    'view_questions', 'create_questions', 'edit_questions', 'delete_questions',
    'view_groups', 'create_groups', 'edit_groups', 'manage_group_members',
    'view_reports', 'export_reports'
);

-- Assign student permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT role_id FROM roles WHERE role_name = 'student'),
    permission_id
FROM permissions
WHERE permission_name IN (
    'view_tests'
);
