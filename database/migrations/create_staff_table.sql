-- Create staff table with educational qualifications and professional details
-- This table extends user information for staff members (teachers, principals, admin)

CREATE TABLE IF NOT EXISTS `staff` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `employee_id` VARCHAR(50) UNIQUE,
  `designation` VARCHAR(100),
  `department` VARCHAR(100),
  `joining_date` DATE,
  `employment_type` ENUM('permanent', 'temporary', 'contract', 'part_time') DEFAULT 'permanent',
  
  -- Contact Information
  `phone` VARCHAR(15),
  `alternate_phone` VARCHAR(15),
  `emergency_contact` VARCHAR(15),
  `address` TEXT,
  `city` VARCHAR(100),
  `state` VARCHAR(100),
  `pincode` VARCHAR(10),
  
  -- Educational Qualifications
  `class_10_board` VARCHAR(100),
  `class_10_year` YEAR,
  `class_10_percentage` DECIMAL(5,2),
  `class_10_school` VARCHAR(255),
  
  `class_12_board` VARCHAR(100),
  `class_12_year` YEAR,
  `class_12_percentage` DECIMAL(5,2),
  `class_12_school` VARCHAR(255),
  `class_12_stream` VARCHAR(50),
  
  `graduation_degree` VARCHAR(100),
  `graduation_university` VARCHAR(255),
  `graduation_year` YEAR,
  `graduation_percentage` DECIMAL(5,2),
  `graduation_specialization` VARCHAR(100),
  
  `post_graduation_degree` VARCHAR(100),
  `post_graduation_university` VARCHAR(255),
  `post_graduation_year` YEAR,
  `post_graduation_percentage` DECIMAL(5,2),
  `post_graduation_specialization` VARCHAR(100),
  
  `phd_subject` VARCHAR(100),
  `phd_university` VARCHAR(255),
  `phd_year` YEAR,
  `phd_thesis_title` VARCHAR(500),
  
  `other_qualifications` TEXT,
  `professional_certifications` TEXT,
  
  -- Experience Details
  `total_experience_years` INT DEFAULT 0,
  `teaching_experience_years` INT DEFAULT 0,
  `administrative_experience_years` INT DEFAULT 0,
  `previous_organizations` TEXT,
  `current_salary` DECIMAL(10,2),
  `subjects_taught` TEXT,
  `classes_handled` VARCHAR(255),
  
  -- Achievements and Recognition
  `awards_received` TEXT,
  `publications` TEXT,
  `research_papers` TEXT,
  `conferences_attended` TEXT,
  `training_programs` TEXT,
  `special_skills` TEXT,
  `languages_known` VARCHAR(255),
  
  -- Administrative Details
  `office_location` VARCHAR(100),
  `reporting_manager_id` INT,
  `probation_period_months` INT DEFAULT 6,
  `confirmation_date` DATE,
  `last_promotion_date` DATE,
  `performance_rating` ENUM('excellent', 'very_good', 'good', 'satisfactory', 'needs_improvement'),
  
  -- Document References
  `resume_file` VARCHAR(255),
  `photo_file` VARCHAR(255),
  `id_proof_file` VARCHAR(255),
  `address_proof_file` VARCHAR(255),
  `qualification_certificates` TEXT,
  
  -- Status and Metadata
  `is_active` TINYINT(1) DEFAULT 1,
  `is_on_leave` TINYINT(1) DEFAULT 0,
  `leave_start_date` DATE,
  `leave_end_date` DATE,
  `notes` TEXT,
  
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign Key Constraints
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reporting_manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  
  -- Indexes for better performance
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_employee_id` (`employee_id`),
  INDEX `idx_department` (`department`),
  INDEX `idx_designation` (`designation`),
  INDEX `idx_joining_date` (`joining_date`)
);

-- Add some sample data for testing (optional)
-- This can be removed in production
INSERT INTO `staff` (
  `user_id`, `employee_id`, `designation`, `department`, `joining_date`,
  `phone`, `class_10_board`, `class_10_year`, `class_10_percentage`,
  `class_12_board`, `class_12_year`, `class_12_percentage`, `class_12_stream`,
  `graduation_degree`, `graduation_university`, `graduation_year`, `graduation_percentage`,
  `total_experience_years`, `teaching_experience_years`
) 
SELECT 
  u.id, 
  CONCAT('EMP', LPAD(u.id, 4, '0')),
  CASE 
    WHEN u.role = 'principal' THEN 'Principal'
    WHEN u.role = 'admin' THEN 'Administrator'
    WHEN u.role = 'teacher' THEN 'Teacher'
    ELSE 'Staff'
  END,
  CASE 
    WHEN u.role = 'principal' THEN 'Administration'
    WHEN u.role = 'admin' THEN 'Administration'
    WHEN u.role = 'teacher' THEN 'Academic'
    ELSE 'General'
  END,
  DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 1825) DAY), -- Random date within last 5 years
  NULL, -- phone will be filled manually
  'CBSE', 2005, 85.5,
  'CBSE', 2007, 82.3, 'Science',
  'Bachelor of Education', 'University of Education', 2011, 78.9,
  FLOOR(RAND() * 15) + 1, -- 1-15 years experience
  FLOOR(RAND() * 12) + 1   -- 1-12 years teaching experience
FROM users u 
WHERE u.role IN ('principal', 'admin', 'teacher') 
  AND u.is_active = 1
  AND NOT EXISTS (SELECT 1 FROM staff s WHERE s.user_id = u.id);
