-- Create comprehensive students table with all required fields
-- This table will store detailed student information for the school management system

DROP TABLE IF EXISTS `students`;

CREATE TABLE `students` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `sno` INT NOT NULL,
  `student_id` VARCHAR(50) NOT NULL UNIQUE,
  `udise_code` VARCHAR(20) DEFAULT NULL,
  `name` VARCHAR(255) NOT NULL,
  `father_name` VA<PERSON><PERSON><PERSON>(255) DEFAULT NULL,
  `mother_name` VARCHAR(255) DEFAULT NULL,
  `dob` DATE DEFAULT NULL,
  `gender` ENUM('Male', 'Female', 'Other') NOT NULL,
  `class` VARCHAR(20) NOT NULL,
  `section` VARCHAR(10) DEFAULT NULL,
  `session` VARCHAR(20) NOT NULL,
  `stream` VARCHAR(50) DEFAULT NULL,
  `trade` VARCHAR(100) DEFAULT NULL,
  `caste_category_name` VARCHAR(100) DEFAULT NULL,
  `bpl` ENUM('Yes', 'No') DEFAULT 'No',
  `disability` VARCHAR(255) DEFAULT 'No',
  `religion_name` VARCHAR(100) DEFAULT NULL,
  `medium_name` VARCHAR(50) DEFAULT 'English',
  `height` DECIMAL(5,2) DEFAULT NULL COMMENT 'Height in cm',
  `weight` DECIMAL(5,2) DEFAULT NULL COMMENT 'Weight in kg',
  `admission_no` VARCHAR(50) DEFAULT NULL,
  `admission_date` DATE DEFAULT NULL,
  `state_name` VARCHAR(100) DEFAULT NULL,
  `district_name` VARCHAR(100) DEFAULT NULL,
  `cur_address` TEXT DEFAULT NULL,
  `village_ward` VARCHAR(255) DEFAULT NULL,
  `gram_panchayat` VARCHAR(255) DEFAULT NULL,
  `pin_code` VARCHAR(10) DEFAULT NULL,
  `roll_no` VARCHAR(20) DEFAULT NULL,
  `contact_no` VARCHAR(15) DEFAULT NULL,
  `ifsc_code` VARCHAR(15) DEFAULT NULL,
  `bank_name` VARCHAR(255) DEFAULT NULL,
  `account_holder_code` VARCHAR(50) DEFAULT NULL,
  `account_holder` VARCHAR(255) DEFAULT NULL,
  `account_holder_name` VARCHAR(255) DEFAULT NULL,
  `room_number` INT DEFAULT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Indexes for better performance
  INDEX `idx_student_id` (`student_id`),
  INDEX `idx_class_section` (`class`, `section`),
  INDEX `idx_session` (`session`),
  INDEX `idx_room_number` (`room_number`),
  INDEX `idx_name` (`name`),
  INDEX `idx_admission_no` (`admission_no`),
  INDEX `idx_roll_no` (`roll_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO `students` (
  `sno`, `student_id`, `udise_code`, `name`, `father_name`, `mother_name`,
  `dob`, `gender`, `class`, `section`, `session`, `stream`, `caste_category_name`,
  `bpl`, `disability`, `religion_name`, `medium_name`, `height`, `weight`,
  `admission_no`, `admission_date`, `state_name`, `district_name`,
  `cur_address`, `village_ward`, `pin_code`, `roll_no`, `contact_no`,
  `room_number`, `is_active`
) VALUES
(1, 'STU001', 'UD12345', 'Rahul Kumar', 'Suresh Kumar', 'Sunita Devi',
 '2008-05-15', 'Male', '10', 'A', '2023-24', 'Science', 'General',
 'No', 'No', 'Hindu', 'English', 165.5, 55.2,
 'ADM001', '2023-04-01', 'Punjab', 'Ludhiana',
 'House No. 123, Model Town', 'Model Town', '141001', 'R001', '9876543210',
 7, TRUE),

(2, 'STU002', 'UD12346', 'Priya Sharma', 'Rajesh Sharma', 'Meera Sharma',
 '2008-08-22', 'Female', '10', 'A', '2023-24', 'Science', 'General',
 'No', 'No', 'Hindu', 'English', 160.0, 50.5,
 'ADM002', '2023-04-01', 'Punjab', 'Ludhiana',
 'House No. 456, Civil Lines', 'Civil Lines', '141001', 'R002', '9876543211',
 7, TRUE),

(3, 'STU003', 'UD12347', 'Arjun Singh', 'Harpreet Singh', 'Simran Kaur',
 '2008-12-10', 'Male', '10', 'B', '2023-24', 'Commerce', 'OBC',
 'Yes', 'No', 'Sikh', 'Punjabi', 170.2, 60.8,
 'ADM003', '2023-04-02', 'Punjab', 'Ludhiana',
 'Village Khanna, Tehsil Khanna', 'Khanna', '141401', 'R003', '9876543212',
 8, TRUE);

-- Create view for easy student data access
CREATE OR REPLACE VIEW `student_details_view` AS
SELECT
  s.*,
  CONCAT(s.class, '-', s.section) as class_section,
  YEAR(CURDATE()) - YEAR(s.dob) as age
FROM students s
WHERE s.is_active = TRUE;

-- Create summary view for dashboard
CREATE OR REPLACE VIEW `student_summary_view` AS
SELECT
  session,
  class,
  section,
  room_number,
  COUNT(*) as total_students,
  SUM(CASE WHEN gender = 'Male' THEN 1 ELSE 0 END) as boys_count,
  SUM(CASE WHEN gender = 'Female' THEN 1 ELSE 0 END) as girls_count,
  SUM(CASE WHEN bpl = 'Yes' THEN 1 ELSE 0 END) as bpl_students,
  SUM(CASE WHEN disability = 'Yes' THEN 1 ELSE 0 END) as disabled_students
FROM students
WHERE is_active = TRUE
GROUP BY session, class, section, room_number;
