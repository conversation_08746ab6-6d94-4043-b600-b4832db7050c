CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY DEFAULT 1,
    site_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'Meritorious EP',
    site_description TEXT,
    contact_email VARCHAR(255),
    support_phone VARCHAR(20),
    maintenance_mode <PERSON><PERSON><PERSON>EAN DEFAULT FALSE,
    timezone VARCHAR(100) DEFAULT 'UTC',
    date_format VARCHAR(50) DEFAULT 'YYYY-MM-DD',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT single_row CHECK (id = 1)
);

-- Insert default settings if not exists
INSERT IGNORE INTO system_settings (id, site_name) VALUES (1, 'Meritorious EP');