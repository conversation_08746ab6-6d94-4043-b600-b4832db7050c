-- Create test_assignments table for managing test assignments to users and groups
CREATE TABLE IF NOT EXISTS `test_assignments` (
  `assignment_id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `assigned_by` int(11) NOT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `max_attempts` int(11) NOT NULL DEFAULT 1,
  `end_datetime` datetime DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`assignment_id`),
  <PERSON><PERSON>Y `test_assignments_exam_id_foreign` (`exam_id`),
  KEY `test_assignments_user_id_foreign` (`user_id`),
  KEY `test_assignments_group_id_foreign` (`group_id`),
  <PERSON><PERSON>Y `test_assignments_assigned_by_foreign` (`assigned_by`),
  CONSTRAINT `test_assignments_exam_id_foreign` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE,
  CONSTRAINT `test_assignments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `test_assignments_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`group_id`) ON DELETE CASCADE,
  CONSTRAINT `test_assignments_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `check_assignment_target` CHECK ((user_id IS NOT NULL AND group_id IS NULL) OR (user_id IS NULL AND group_id IS NOT NULL))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
