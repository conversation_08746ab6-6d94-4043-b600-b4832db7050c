-- Drop Vice Principal Tables
-- Run this script to remove all vice principal related database tables

-- Drop tables in reverse order of dependencies to avoid foreign key constraints

-- Drop infrastructure maintenance log first
DROP TABLE IF EXISTS `infrastructure_maintenance_log`;

-- Drop syllabus completion tracking
DROP TABLE IF EXISTS `syllabus_completion_tracking`;

-- Drop daily class schedule
DROP TABLE IF EXISTS `daily_class_schedule`;

-- Drop classroom infrastructure
DROP TABLE IF EXISTS `classroom_infrastructure`;

-- Note: We're keeping the classrooms table as it might be used by other parts of the system
-- If you want to remove classrooms table as well, uncomment the line below:
-- DROP TABLE IF EXISTS `classrooms`;

-- Clean up any vice principal user records (optional)
-- Uncomment the lines below if you want to remove the vice principal user account:
-- DELETE FROM users WHERE username = 'vice_principal';
-- DELETE FROM users WHERE role = 'vice_principal';

SELECT 'Vice Principal tables have been dropped successfully.' as message;
