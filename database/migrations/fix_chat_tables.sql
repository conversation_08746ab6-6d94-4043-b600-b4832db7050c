-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    recipient_type <PERSON><PERSON><PERSON>('user', 'group') NOT NULL,
    message TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create message status table
CREATE TABLE IF NOT EXISTS message_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    read_at DATETIME NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON>EY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON>IQ<PERSON> KEY (message_id, user_id)
);
