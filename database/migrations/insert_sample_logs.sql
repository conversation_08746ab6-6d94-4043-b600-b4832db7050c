-- Insert sample log entries
INSERT INTO `logs` (`timestamp`, `user_id`, `level`, `category`, `operation`, `details`, `status`, `error_message`, `ip_address`, `request_method`, `request_uri`, `user_agent`)
VALUES
    (NOW(), 1, 'info', 'auth', 'User Login', 'User logged in successfully', 'success', NULL, '127.0.0.1', 'POST', '/login', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 1, 'info', 'user', 'Profile Update', 'User updated their profile information', 'success', NULL, '127.0.0.1', 'PUT', '/profile', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 2, 'warning', 'auth', 'Failed Login Attempt', 'Multiple failed login attempts detected', 'warning', NULL, '***********', 'POST', '/login', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15'),
    (NOW(), NULL, 'error', 'system', 'Database Connection', 'Failed to connect to database', 'error', 'Connection refused', '127.0.0.1', 'GET', '/admin/dashboard', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 3, 'info', 'test', 'Test Created', 'New test created: Math Test', 'success', NULL, '127.0.0.1', 'POST', '/admin/tests/add', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 3, 'info', 'test', 'Test Updated', 'Test updated: Math Test', 'success', NULL, '127.0.0.1', 'PUT', '/admin/tests/5', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 1, 'info', 'question', 'Question Created', 'New question created: What is 2+2?', 'success', NULL, '127.0.0.1', 'POST', '/admin/questions/add', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 1, 'debug', 'system', 'Cache Cleared', 'Application cache cleared', 'success', NULL, '127.0.0.1', 'POST', '/admin/cache/clear', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 2, 'error', 'test', 'Test Submission', 'Failed to submit test results', 'error', 'Database query error', '***********', 'POST', '/tests/submit/3', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'),
    (NOW(), NULL, 'warning', 'system', 'Disk Space', 'Low disk space warning', 'warning', NULL, '127.0.0.1', 'GET', '/admin/dashboard', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 3, 'info', 'user', 'User Created', 'New user created: <EMAIL>', 'success', NULL, '127.0.0.1', 'POST', '/admin/users/add', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 1, 'info', 'auth', 'Password Reset', 'Password reset requested for user: <EMAIL>', 'success', NULL, '127.0.0.1', 'POST', '/password/reset', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), NULL, 'error', 'email', 'Email Sending', 'Failed to send email notification', 'error', 'SMTP connection error', '127.0.0.1', 'POST', '/admin/email/send', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 2, 'info', 'report', 'Report Generated', 'Monthly activity report generated', 'success', NULL, '127.0.0.1', 'GET', '/admin/reports/generate', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 3, 'debug', 'system', 'Cron Job', 'Daily backup cron job executed', 'success', NULL, '127.0.0.1', 'GET', '/cron/backup', 'Mozilla/5.0 (compatible; CronJob/1.0)'),
    (NOW(), NULL, 'warning', 'security', 'Suspicious Activity', 'Multiple failed API requests from same IP', 'warning', NULL, '***********', 'POST', '/api/login', 'PostmanRuntime/7.28.0'),
    (NOW(), 1, 'info', 'settings', 'Settings Updated', 'Application settings updated', 'success', NULL, '127.0.0.1', 'PUT', '/admin/settings', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 2, 'info', 'question', 'Questions Imported', 'Imported 50 questions from Excel file', 'success', NULL, '127.0.0.1', 'POST', '/admin/questions/import', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 3, 'error', 'file', 'File Upload', 'Failed to upload file: image.jpg', 'error', 'File size exceeds limit', '127.0.0.1', 'POST', '/admin/files/upload', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (NOW(), 1, 'info', 'test', 'Test Published', 'Test published: Science Quiz', 'success', NULL, '127.0.0.1', 'PUT', '/admin/tests/7/publish', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

-- Insert logs with different timestamps for testing date filtering
INSERT INTO `logs` (`timestamp`, `user_id`, `level`, `category`, `operation`, `details`, `status`, `ip_address`, `request_method`, `request_uri`, `user_agent`)
VALUES
    (DATE_SUB(NOW(), INTERVAL 1 DAY), 1, 'info', 'auth', 'User Login', 'User logged in successfully', 'success', '127.0.0.1', 'POST', '/login', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (DATE_SUB(NOW(), INTERVAL 2 DAY), 2, 'warning', 'auth', 'Failed Login Attempt', 'Failed login attempt', 'warning', '***********', 'POST', '/login', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15'),
    (DATE_SUB(NOW(), INTERVAL 3 DAY), 3, 'error', 'system', 'Database Error', 'Failed to execute query', 'error', '127.0.0.1', 'GET', '/admin/dashboard', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (DATE_SUB(NOW(), INTERVAL 4 DAY), 1, 'info', 'test', 'Test Attempt', 'User started test', 'success', '127.0.0.1', 'GET', '/tests/1/start', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (DATE_SUB(NOW(), INTERVAL 5 DAY), 2, 'debug', 'system', 'System Check', 'Routine system check completed', 'success', '127.0.0.1', 'GET', '/admin/system/check', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (DATE_SUB(NOW(), INTERVAL 6 DAY), 3, 'info', 'user', 'Password Changed', 'User changed their password', 'success', '127.0.0.1', 'POST', '/profile/password', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
    (DATE_SUB(NOW(), INTERVAL 7 DAY), 1, 'warning', 'security', 'Multiple Login Attempts', 'Multiple login attempts detected', 'warning', '***********', 'POST', '/login', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
