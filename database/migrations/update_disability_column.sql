-- Migration to update disability column from ENUM to VARCHAR
-- This allows for more flexible disability descriptions

-- First, check if the column exists and is ENUM
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'students' 
    AND COLUMN_NAME = 'disability';

-- Update the disability column to VARCHAR
ALTER TABLE students 
MODIFY COLUMN disability VARCHAR(255) DEFAULT 'No';

-- Update existing data to maintain consistency
-- Convert 'Yes' to 'Yes' and 'No' to 'No' (no change needed for these values)
-- This is safe as VARCHAR can hold the existing ENUM values

-- Verify the change
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'students' 
    AND COLUMN_NAME = 'disability';

-- Show sample data to verify
SELECT 
    student_id, 
    name, 
    disability 
FROM students 
LIMIT 5;
