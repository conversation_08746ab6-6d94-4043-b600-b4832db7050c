-- REDESIGNED STAFF TABLES FOR PROPER DATA STRUCTURE
-- This file contains the new table designs for educational qualifications and professional experience

-- =====================================================
-- 1. <PERSON>DUC<PERSON>IONAL QUALIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS staff_educational_qualifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id INT NOT NULL,
    
    -- Basic Information
    qualification_level ENUM('10th', '12th', 'diploma', 'graduation', 'post_graduation', 'phd', 'other') NOT NULL,
    qualification_name VARCHAR(255) NOT NULL, -- e.g., "B.Tech", "M.Sc", "Class 10th"
    specialization VARCHAR(255), -- e.g., "Computer Science", "Mathematics"
    
    -- Institution Details
    institution_name VARCHAR(255) NOT NULL,
    university_board VARCHAR(255), -- University/Board name
    location VARCHAR(255), -- City, State
    
    -- Academic Performance
    subjects JSON, -- {"Mathematics": {"marks": 85, "total": 100}, "Physics": {"marks": 78, "total": 100}}
    total_marks_obtained DECIMAL(8,2),
    total_marks_maximum DECIMAL(8,2),
    percentage DECIMAL(5,2),
    grade VARCHAR(10), -- A+, A, B+, etc.
    cgpa DECIMAL(4,2),
    
    -- Timeline
    start_year YEAR,
    completion_year YEAR,
    duration_years DECIMAL(3,1), -- 1.5, 2.0, 4.0 etc.
    
    -- Additional Details
    thesis_title VARCHAR(500), -- For PhD/Masters
    guide_supervisor VARCHAR(255), -- Research guide name
    achievements TEXT, -- Scholarships, awards, etc.
    certificate_number VARCHAR(100),
    verification_status ENUM('verified', 'pending', 'not_verified') DEFAULT 'pending',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
    INDEX idx_staff_qualification (staff_id, qualification_level),
    INDEX idx_completion_year (completion_year)
);

-- =====================================================
-- 2. PROFESSIONAL EXPERIENCE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS staff_professional_experience (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id INT NOT NULL,
    
    -- Position Details
    job_title VARCHAR(255) NOT NULL,
    department VARCHAR(255),
    employment_type ENUM('full_time', 'part_time', 'contract', 'internship', 'freelance', 'consultant') NOT NULL,
    job_category ENUM('teaching', 'administrative', 'research', 'industry', 'government', 'private', 'ngo', 'other') NOT NULL,
    
    -- Organization Details
    organization_name VARCHAR(255) NOT NULL,
    organization_type ENUM('school', 'college', 'university', 'company', 'government', 'ngo', 'startup', 'other') NOT NULL,
    organization_location VARCHAR(255), -- City, State, Country
    organization_size ENUM('startup', 'small', 'medium', 'large', 'enterprise') DEFAULT 'medium',
    
    -- Timeline
    start_date DATE NOT NULL,
    end_date DATE NULL, -- NULL for current position
    is_current BOOLEAN DEFAULT FALSE,
    total_duration_months INT, -- Calculated field
    
    -- Responsibilities and Achievements
    job_description TEXT,
    key_responsibilities JSON, -- ["Teaching CS subjects", "Lab management", "Student mentoring"]
    achievements JSON, -- ["Improved student performance by 25%", "Implemented new curriculum"]
    skills_used JSON, -- ["Java", "Python", "Database Management", "Team Leadership"]
    
    -- Compensation (Optional)
    salary_range VARCHAR(50), -- "50000-75000", "Not disclosed"
    currency VARCHAR(10) DEFAULT 'INR',
    
    -- Performance
    performance_rating ENUM('excellent', 'good', 'satisfactory', 'needs_improvement') DEFAULT 'satisfactory',
    reason_for_leaving TEXT,
    
    -- References
    supervisor_name VARCHAR(255),
    supervisor_contact VARCHAR(100),
    reference_available BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
    INDEX idx_staff_experience (staff_id, start_date),
    INDEX idx_current_position (staff_id, is_current),
    INDEX idx_organization (organization_name),
    INDEX idx_job_category (job_category)
);

-- =====================================================
-- 3. STAFF CERTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS staff_certifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id INT NOT NULL,
    
    -- Certification Details
    certification_name VARCHAR(255) NOT NULL,
    certification_type ENUM('professional', 'technical', 'teaching', 'language', 'safety', 'other') NOT NULL,
    issuing_organization VARCHAR(255) NOT NULL,
    
    -- Validity
    issue_date DATE,
    expiry_date DATE NULL, -- NULL for lifetime certifications
    is_lifetime BOOLEAN DEFAULT FALSE,
    
    -- Verification
    certificate_id VARCHAR(100),
    verification_url VARCHAR(500),
    verification_status ENUM('verified', 'pending', 'expired', 'invalid') DEFAULT 'pending',
    
    -- Additional Info
    description TEXT,
    skills_covered JSON, -- ["Python Programming", "Data Analysis", "Machine Learning"]
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
    INDEX idx_staff_certification (staff_id, certification_type),
    INDEX idx_expiry_date (expiry_date)
);

-- =====================================================
-- 4. STAFF SKILLS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS staff_skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id INT NOT NULL,
    
    -- Skill Details
    skill_name VARCHAR(255) NOT NULL,
    skill_category ENUM('technical', 'teaching', 'language', 'soft_skill', 'research', 'administrative', 'other') NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL,
    
    -- Experience with skill
    years_of_experience DECIMAL(3,1),
    last_used_date DATE,
    
    -- Verification
    is_certified BOOLEAN DEFAULT FALSE,
    certification_details TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
    INDEX idx_staff_skill (staff_id, skill_category),
    UNIQUE KEY unique_staff_skill (staff_id, skill_name)
);

-- =====================================================
-- 5. UPDATED MAIN STAFF TABLE (SIMPLIFIED)
-- =====================================================
-- Remove redundant fields that are now in separate tables
ALTER TABLE staff 
DROP COLUMN IF EXISTS class_10_board,
DROP COLUMN IF EXISTS class_10_year,
DROP COLUMN IF EXISTS class_10_percentage,
DROP COLUMN IF EXISTS class_10_school,
DROP COLUMN IF EXISTS class_12_board,
DROP COLUMN IF EXISTS class_12_year,
DROP COLUMN IF EXISTS class_12_percentage,
DROP COLUMN IF EXISTS class_12_school,
DROP COLUMN IF EXISTS class_12_stream,
DROP COLUMN IF EXISTS graduation_degree,
DROP COLUMN IF EXISTS graduation_university,
DROP COLUMN IF EXISTS graduation_year,
DROP COLUMN IF EXISTS graduation_percentage,
DROP COLUMN IF EXISTS graduation_specialization,
DROP COLUMN IF EXISTS post_graduation_degree,
DROP COLUMN IF EXISTS post_graduation_university,
DROP COLUMN IF EXISTS post_graduation_year,
DROP COLUMN IF EXISTS post_graduation_percentage,
DROP COLUMN IF EXISTS post_graduation_specialization,
DROP COLUMN IF EXISTS phd_subject,
DROP COLUMN IF EXISTS phd_university,
DROP COLUMN IF EXISTS phd_year,
DROP COLUMN IF EXISTS phd_thesis_title,
DROP COLUMN IF EXISTS previous_organizations,
DROP COLUMN IF EXISTS professional_certifications,
DROP COLUMN IF EXISTS other_qualifications,
DROP COLUMN IF EXISTS special_skills,
DROP COLUMN IF EXISTS languages_known;

-- =====================================================
-- 6. VIEWS FOR EASY DATA RETRIEVAL
-- =====================================================

-- View for complete staff profile with latest qualification
CREATE OR REPLACE VIEW staff_profile_summary AS
SELECT 
    s.id,
    s.user_id,
    s.employee_id,
    s.designation,
    s.department,
    s.joining_date,
    s.employment_type,
    
    -- Latest/Highest qualification
    (SELECT qualification_name 
     FROM staff_educational_qualifications seq 
     WHERE seq.staff_id = s.id 
     ORDER BY FIELD(qualification_level, 'phd', 'post_graduation', 'graduation', 'diploma', '12th', '10th') 
     LIMIT 1) as highest_qualification,
     
    -- Current position details
    (SELECT job_title 
     FROM staff_professional_experience spe 
     WHERE spe.staff_id = s.id AND spe.is_current = TRUE 
     LIMIT 1) as current_position,
     
    -- Total experience
    (SELECT SUM(total_duration_months)/12 
     FROM staff_professional_experience spe 
     WHERE spe.staff_id = s.id) as total_experience_years,
     
    -- Active certifications count
    (SELECT COUNT(*) 
     FROM staff_certifications sc 
     WHERE sc.staff_id = s.id 
     AND (sc.expiry_date IS NULL OR sc.expiry_date > CURDATE())) as active_certifications_count

FROM staff s;

-- View for educational timeline
CREATE OR REPLACE VIEW staff_education_timeline AS
SELECT 
    seq.*,
    u.name as staff_name,
    u.email as staff_email
FROM staff_educational_qualifications seq
JOIN staff s ON seq.staff_id = s.id
JOIN users u ON s.user_id = u.id
ORDER BY seq.staff_id, seq.completion_year;

-- View for experience timeline
CREATE OR REPLACE VIEW staff_experience_timeline AS
SELECT 
    spe.*,
    u.name as staff_name,
    u.email as staff_email,
    CASE 
        WHEN spe.end_date IS NULL THEN CONCAT(DATE_FORMAT(spe.start_date, '%b %Y'), ' - Present')
        ELSE CONCAT(DATE_FORMAT(spe.start_date, '%b %Y'), ' - ', DATE_FORMAT(spe.end_date, '%b %Y'))
    END as duration_display
FROM staff_professional_experience spe
JOIN staff s ON spe.staff_id = s.id
JOIN users u ON s.user_id = u.id
ORDER BY spe.staff_id, spe.start_date;
