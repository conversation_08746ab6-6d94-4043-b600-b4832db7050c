-- Insert Physics Section
INSERT INTO `sections` (`section_id`, `exam_id`, `section_name`, `description`, `duration`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(300, NULL, 'Physics', 'Physics questions covering fundamental concepts', NULL, 1, 1, NOW(), NOW());

-- Insert Physics Questions
INSERT INTO `questions` (`question_id`, `section_id`, `question_text`, `correct_answer`, `question_type`, `marks`, `solution_text`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(400, 300, 'What is the SI unit of electric current?', 'Ampere', 'multiple_choice', 1.00, 'The SI unit of electric current is the Ampere (A).', 1, 1, NOW(), NOW()),
(401, 300, 'What is Coulomb\'s Law?', 'F = k(q₁q₂/r²)', 'multiple_choice', 1.00, '<PERSON>ulomb\'s Law states that the force between two charged particles is directly proportional to the product of their charges and inversely proportional to the square of the distance between them.', 2, 1, NOW(), NOW()),
(402, 300, 'What is the speed of light in vacuum?', '3 × 10⁸ m/s', 'multiple_choice', 1.00, 'The speed of light in vacuum is approximately 3 × 10⁸ meters per second.', 3, 1, NOW(), NOW());

-- Insert Physics Question Options
INSERT INTO `options` (`question_id`, `option_text`, `is_correct`, `position`, `created_at`, `updated_at`) VALUES
(400, 'Ampere', 1, 1, NOW(), NOW()),
(400, 'Volt', 0, 2, NOW(), NOW()),
(400, 'Watt', 0, 3, NOW(), NOW()),
(400, 'Ohm', 0, 4, NOW(), NOW()),

(401, 'F = k(q₁q₂/r²)', 1, 1, NOW(), NOW()),
(401, 'F = ma', 0, 2, NOW(), NOW()),
(401, 'E = mc²', 0, 3, NOW(), NOW()),
(401, 'V = IR', 0, 4, NOW(), NOW()),

(402, '3 × 10⁸ m/s', 1, 1, NOW(), NOW()),
(402, '2 × 10⁸ m/s', 0, 2, NOW(), NOW()),
(402, '4 × 10⁸ m/s', 0, 3, NOW(), NOW()),
(402, '5 × 10⁸ m/s', 0, 4, NOW(), NOW());

-- Insert Chemistry Section
INSERT INTO `sections` (`section_id`, `exam_id`, `section_name`, `description`, `duration`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(301, NULL, 'Chemistry', 'Chemistry questions covering fundamental concepts', NULL, 2, 1, NOW(), NOW());

-- Insert Chemistry Questions
INSERT INTO `questions` (`question_id`, `section_id`, `question_text`, `correct_answer`, `question_type`, `marks`, `solution_text`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(403, 301, 'What is the atomic number of Carbon?', '6', 'multiple_choice', 1.00, 'Carbon has an atomic number of 6, meaning it has 6 protons in its nucleus.', 1, 1, NOW(), NOW()),
(404, 301, 'Which gas is known as the "Silent Killer"?', 'Carbon Monoxide', 'multiple_choice', 1.00, 'Carbon Monoxide (CO) is known as the "Silent Killer" because it is colorless, odorless, and can be deadly in high concentrations.', 2, 1, NOW(), NOW()),
(405, 301, 'What is the pH of a neutral solution at 25°C?', '7', 'multiple_choice', 1.00, 'A neutral solution has a pH of 7 at 25°C.', 3, 1, NOW(), NOW());

-- Insert Chemistry Question Options
INSERT INTO `options` (`question_id`, `option_text`, `is_correct`, `position`, `created_at`, `updated_at`) VALUES
(403, '6', 1, 1, NOW(), NOW()),
(403, '4', 0, 2, NOW(), NOW()),
(403, '8', 0, 3, NOW(), NOW()),
(403, '12', 0, 4, NOW(), NOW()),

(404, 'Carbon Monoxide', 1, 1, NOW(), NOW()),
(404, 'Carbon Dioxide', 0, 2, NOW(), NOW()),
(404, 'Nitrogen', 0, 3, NOW(), NOW()),
(404, 'Oxygen', 0, 4, NOW(), NOW()),

(405, '7', 1, 1, NOW(), NOW()),
(405, '0', 0, 2, NOW(), NOW()),
(405, '14', 0, 3, NOW(), NOW()),
(405, '1', 0, 4, NOW(), NOW());

-- Insert Mathematics Section
INSERT INTO `sections` (`section_id`, `exam_id`, `section_name`, `description`, `duration`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(302, NULL, 'Mathematics', 'Mathematics questions covering fundamental concepts', NULL, 3, 1, NOW(), NOW());

-- Insert Mathematics Questions
INSERT INTO `questions` (`question_id`, `section_id`, `question_text`, `correct_answer`, `question_type`, `marks`, `solution_text`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(406, 302, 'What is the value of π (pi) to two decimal places?', '3.14', 'multiple_choice', 1.00, 'The value of π (pi) is approximately 3.14159..., which rounds to 3.14 to two decimal places.', 1, 1, NOW(), NOW()),
(407, 302, 'What is the square root of 144?', '12', 'multiple_choice', 1.00, 'The square root of 144 is 12, as 12 × 12 = 144.', 2, 1, NOW(), NOW()),
(408, 302, 'What is the sum of the angles in a triangle?', '180 degrees', 'multiple_choice', 1.00, 'The sum of the angles in any triangle is always 180 degrees.', 3, 1, NOW(), NOW());

-- Insert Mathematics Question Options
INSERT INTO `options` (`question_id`, `option_text`, `is_correct`, `position`, `created_at`, `updated_at`) VALUES
(406, '3.14', 1, 1, NOW(), NOW()),
(406, '3.12', 0, 2, NOW(), NOW()),
(406, '3.16', 0, 3, NOW(), NOW()),
(406, '3.18', 0, 4, NOW(), NOW()),

(407, '12', 1, 1, NOW(), NOW()),
(407, '10', 0, 2, NOW(), NOW()),
(407, '14', 0, 3, NOW(), NOW()),
(407, '16', 0, 4, NOW(), NOW()),

(408, '180 degrees', 1, 1, NOW(), NOW()),
(408, '90 degrees', 0, 2, NOW(), NOW()),
(408, '360 degrees', 0, 3, NOW(), NOW()),
(408, '270 degrees', 0, 4, NOW(), NOW()); 