-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Mar 26, 2025 at 08:44 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `exam_prep_platform`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`category_id`, `name`, `description`, `parent_id`, `created_at`, `updated_at`) VALUES
(1, 'Mathematics', 'Mathematics related exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(2, 'Science', 'Science related exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(3, 'Language', 'Language proficiency exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(4, 'Programming', 'Programming and coding exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(5, 'General Knowledge', 'General knowledge and aptitude exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(6, 'Physics', 'Physics examination topics', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(7, 'Chemistry', 'Chemistry cat', NULL, '2025-03-24 07:52:49', '2025-03-24 07:52:49'),
(8, 'Computer Science', 'Computer Science cat', NULL, '2025-03-24 07:53:07', '2025-03-24 07:53:07');

-- --------------------------------------------------------

--
-- Table structure for table `exams`
--

CREATE TABLE `exams` (
  `exam_id` int(11) NOT NULL,
  `exam_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `duration` int(11) NOT NULL DEFAULT 60 COMMENT 'Duration in minutes',
  `instructions` text DEFAULT NULL,
  `publish_date` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `passing_marks` decimal(5,2) DEFAULT 60.00,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `difficulty` enum('beginner','intermediate','advanced') DEFAULT 'intermediate',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `exams`
--

INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(83, 'computer science', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(85, 'Mega Test2', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(86, 'xc', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(87, '112', NULL, 12, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(89, 'zc', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:13:40', '2025-03-19 17:13:40'),
(91, 'ca', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:19:14', '2025-03-19 17:19:14'),
(93, 'das', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:29:04', '2025-03-19 17:29:04'),
(95, 'dazca', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:37:45', '2025-03-19 17:37:45'),
(96, 'computer sciencew1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 01:33:04', '2025-03-20 01:33:40'),
(98, 'w1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 01:50:06', '2025-03-20 01:50:06'),
(100, '3e', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 02:41:48', '2025-03-20 02:42:26'),
(101, 'qsc', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 02:48:49', '2025-03-20 02:48:49'),
(103, 'dax', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:32:38', '2025-03-20 05:32:38'),
(105, 'dax32', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:36:06', '2025-03-20 05:36:06'),
(107, 'xdf', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:38:45', '2025-03-20 05:38:45'),
(109, 'dare', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 06:58:53', '2025-03-20 06:58:53'),
(111, 'daree', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 06:59:40', '2025-03-20 06:59:40'),
(112, 'xfe', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:38:27', '2025-03-20 07:38:27'),
(114, 'xfes', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:43:13', '2025-03-20 07:43:13'),
(115, 'daee', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:48:01', '2025-03-20 07:48:01'),
(117, 'x3', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 16:46:22', '2025-03-20 16:46:22'),
(118, 'xe3', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 17:07:31', '2025-03-20 17:07:34'),
(120, 'xe4', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 17:12:38', '2025-03-20 17:15:58'),
(121, 'xe5', NULL, 61, NULL, NULL, 1, 1, NULL, 44.00, 'draft', 'intermediate', '2025-03-20 17:17:04', '2025-03-20 17:17:36'),
(122, 'xe44', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 17:20:11', '2025-03-20 17:20:11'),
(123, 'xe35', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 18:13:48', '2025-03-20 18:13:48'),
(124, 'e34', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 18:15:17', '2025-03-20 18:15:17'),
(125, 'xe22', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:26:46', '2025-03-21 01:28:41'),
(126, 'xd21', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:30:25', '2025-03-21 01:30:25'),
(127, 'xe231', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:35:34', '2025-03-21 01:35:34'),
(128, 'dex', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:36:44', '2025-03-21 01:36:44'),
(129, 'fr1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:38:31', '2025-03-21 01:38:31'),
(130, 'x3121', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:42:15', '2025-03-21 01:46:18'),
(131, 'xd32121', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:47:52', '2025-03-21 01:47:52'),
(133, 'xdf1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:51:06', '2025-03-21 01:51:06'),
(134, 'qqdq', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:52:22', '2025-03-21 01:52:22'),
(135, 'fdew', NULL, 334, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:54:16', '2025-03-21 01:54:57'),
(136, 'tx1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:52:19', '2025-03-21 06:52:31'),
(137, 'tx11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:53:56', '2025-03-21 06:54:01'),
(139, 'tx111', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigatiown and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:58:28', '2025-03-21 06:58:33'),
(140, 'r3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:00:00', '2025-03-21 07:00:09'),
(142, 'r311', NULL, 60, 'Online Exam Instructions:\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:00:29', '2025-03-21 07:00:43'),
(143, 'r23', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:05:41', '2025-03-21 07:05:49'),
(144, '12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:29:15', '2025-03-21 07:29:22'),
(145, 'dfg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:35:18', '2025-03-21 07:35:30'),
(146, 'r32', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:47:38', '2025-03-21 07:47:47'),
(147, 'e321', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:50:21', '2025-03-21 07:50:27'),
(148, 'x324', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:54:49', '2025-03-21 07:55:01'),
(149, 'nv1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:00:32', '2025-03-21 08:00:38'),
(150, 'm345', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:07:29', '2025-03-21 08:07:37'),
(151, 'x221', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:10:26', '2025-03-21 08:10:36'),
(152, 'xe3`1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 08:11:39', '2025-03-21 08:11:39'),
(153, 'xc44', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 08:11:54', '2025-03-21 08:11:54'),
(154, 'p3g', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:13:33', '2025-03-21 08:13:42'),
(155, 'cvf', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:15:42', '2025-03-21 08:15:48'),
(156, 'xdr', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 15:10:36', '2025-03-21 15:10:36'),
(157, 'x3132', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:17:03', '2025-03-21 15:17:11'),
(158, 'xft4', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:37:48', '2025-03-21 15:37:56'),
(159, 'redman', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:58:04', '2025-03-21 15:58:15'),
(160, 'gn', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 16:45:04', '2025-03-21 16:45:19'),
(161, 'xe3111', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 17:01:14', '2025-03-21 17:01:23');
INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(162, 'fr', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 18:18:10', '2025-03-21 18:18:17'),
(163, 'he23', NULL, 23, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 18:47:18', '2025-03-21 18:48:43'),
(164, 'rty5', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:28:24', '2025-03-22 02:28:32'),
(165, 'wed', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:35:15', '2025-03-22 02:35:23'),
(166, 'fedv', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:42:26', '2025-03-22 02:42:37'),
(167, 'ehj', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:44:07', '2025-03-22 02:44:13'),
(168, 'jkl', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:51:01', '2025-03-22 02:51:12'),
(169, 'ftg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:57:45', '2025-03-22 02:58:03'),
(170, 'o1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:02:29', '2025-03-22 04:02:41'),
(172, 'p1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:04:48', '2025-03-22 04:04:59'),
(173, 'op1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:13:02', '2025-03-22 04:13:08'),
(174, 'ox1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:14:33', '2025-03-22 04:14:40'),
(175, 'cx1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:16:00', '2025-03-22 04:16:07'),
(176, 'p12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:18:13', '2025-03-22 04:18:20'),
(177, 'pox1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:23:29', '2025-03-22 04:23:36'),
(178, 'po1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:35:05', '2025-03-22 04:35:14'),
(179, 'ind', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-22 05:37:12', '2025-03-22 05:37:12'),
(180, 'ko', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:28:50', '2025-03-22 08:29:19'),
(181, 'o2', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:38:36', '2025-03-22 08:38:44'),
(182, 'l12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:41:14', '2025-03-22 08:41:23'),
(183, 'kiop', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 09:58:11', '2025-03-22 09:58:22'),
(184, 'pl1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:02:14', '2025-03-22 10:02:28'),
(185, 'x-2', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:09:21', '2025-03-22 10:13:08'),
(186, 'f1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:20:32', '2025-03-22 10:20:49'),
(188, 'f11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:21:53', '2025-03-22 10:22:10'),
(189, 'fg12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:27:33', '2025-03-22 10:27:41'),
(190, 'dt1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-22 11:20:43', '2025-03-22 11:20:43'),
(191, 'pol1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 11:42:22', '2025-03-22 11:42:45'),
(193, 'r35t', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 17:47:32', '2025-03-22 17:47:45'),
(194, 'rfg3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 17:51:45', '2025-03-22 17:51:56'),
(195, 'rf11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:05:05', '2025-03-22 18:05:18'),
(196, 'rfg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:06:12', '2025-03-22 18:06:23'),
(198, 'lo1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:13:14', '2025-03-22 18:13:23'),
(199, 'rfg1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:14:56', '2025-03-22 18:15:02');
INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(200, 'xcfr3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:22:09', '2025-03-22 18:22:16'),
(201, 'fgbrr', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:24:06', '2025-03-22 18:24:13'),
(202, 'lpp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:31:20', '2025-03-22 18:31:33'),
(203, 'oo1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:35:27', '2025-03-22 18:35:36'),
(204, 'tgb', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:37:11', '2025-03-22 18:37:19'),
(205, 'lopp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:43:15', '2025-03-22 18:43:25'),
(206, 'p010', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:26:57', '2025-03-23 02:27:05'),
(207, 'bgt', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:32:59', '2025-03-23 02:33:07'),
(208, 'xp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:35:52', '2025-03-23 02:36:01'),
(209, 'PIL', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:54:06', '2025-03-23 02:54:14'),
(210, 'xr12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:02:51', '2025-03-23 03:02:59'),
(211, 'polk1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:08:36', '2025-03-23 03:08:43'),
(212, 'kl1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:11:35', '2025-03-23 03:11:45'),
(213, 'lo', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:14:17', '2025-03-23 03:14:24'),
(215, 'megatron', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:17:44', '2025-03-23 03:17:54'),
(216, 'jil', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:21:05', '2025-03-23 03:21:13'),
(217, 'frg3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:25:04', '2025-03-23 03:25:11'),
(218, 'p101', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:07:14', '2025-03-23 06:07:53'),
(219, 'p11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:12:02', '2025-03-23 06:12:18'),
(222, 'p123', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:18:36', '2025-03-23 06:19:33'),
(223, 'bgt`', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:24:22', '2025-03-23 06:24:50'),
(224, 'grok3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 07:42:44', '2025-03-23 07:43:00'),
(225, 'pt-1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 07:46:11', '2025-03-23 07:46:21'),
(226, 'k12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', '2025-03-28 13:21:00', 1, 1, NULL, 40.00, 'published', 'intermediate', '2025-03-23 07:50:33', '2025-03-23 07:51:56'),
(227, 'gin', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', '2025-03-26 13:30:00', 1, 1, NULL, 40.00, 'published', 'intermediate', '2025-03-23 07:57:19', '2025-03-23 07:58:29'),
(228, 'INDIGO', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-24 06:57:29', '2025-03-24 06:57:48');

-- --------------------------------------------------------

--
-- Table structure for table `exam_attempts`
--

CREATE TABLE `exam_attempts` (
  `attempt_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('in_progress','completed','abandoned') DEFAULT 'in_progress',
  `total_score` decimal(5,2) DEFAULT NULL,
  `attempt_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `is_read`, `created_at`) VALUES
(1, 3, 'Physics Test Result Available', 'Your Physics Fundamentals Test has been graded. Score: 85%', 'result', 0, '2025-03-17 05:34:18');

-- --------------------------------------------------------

--
-- Table structure for table `options`
--

CREATE TABLE `options` (
  `id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `option_text` text NOT NULL,
  `is_correct` tinyint(1) NOT NULL DEFAULT 0,
  `position` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `options`
--

INSERT INTO `options` (`id`, `question_id`, `option_text`, `is_correct`, `position`, `created_at`, `updated_at`) VALUES
(43, 162, '33', 1, 1, '2025-03-26 07:43:32', '2025-03-26 07:43:32'),
(44, 162, '44', 0, 2, '2025-03-26 07:43:32', '2025-03-26 07:43:32');

-- --------------------------------------------------------

--
-- Table structure for table `questions`
--

CREATE TABLE `questions` (
  `question_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  `question_text` text NOT NULL,
  `correct_answer` varchar(255) DEFAULT NULL,
  `question_type` enum('multiple_choice','true_false','essay') NOT NULL,
  `marks` decimal(5,2) DEFAULT 1.00,
  `solution_text` varchar(500) DEFAULT NULL,
  `position` int(11) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `questions`
--

INSERT INTO `questions` (`question_id`, `section_id`, `question_text`, `correct_answer`, `question_type`, `marks`, `solution_text`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(36, 149, 'fw', NULL, 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:44:52', '2025-03-22 02:44:52'),
(37, 149, 'fw', NULL, 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:44:52', '2025-03-22 02:44:52'),
(38, 151, 'rg', '45', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:51:28', '2025-03-22 02:51:28'),
(39, 151, 'rg', '45', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:51:28', '2025-03-22 02:51:28'),
(40, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:30', '2025-03-22 02:58:30'),
(41, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:30', '2025-03-22 02:58:30'),
(42, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:50', '2025-03-22 02:58:50'),
(43, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:50', '2025-03-22 02:58:50'),
(44, 155, 'e', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:02:52', '2025-03-22 04:02:52'),
(45, 155, 'e', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:02:52', '2025-03-22 04:02:52'),
(46, 156, 'qd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:05:20', '2025-03-22 04:05:20'),
(47, 156, 'qd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:05:20', '2025-03-22 04:05:20'),
(48, 157, 'dad', 'eq', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:13:33', '2025-03-22 04:13:33'),
(49, 157, 'dad', 'eq', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:13:33', '2025-03-22 04:13:33'),
(50, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:01', '2025-03-22 04:15:01'),
(51, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:01', '2025-03-22 04:15:01'),
(52, 158, 'what is ', 'true', 'true_false', 1.00, 'of', 1, 1, '2025-03-22 04:15:12', '2025-03-25 07:56:01'),
(53, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:12', '2025-03-22 04:15:12'),
(54, 159, 'qf', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:16:21', '2025-03-22 04:16:21'),
(55, 159, 'qf', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:16:21', '2025-03-22 04:16:21'),
(56, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:18:42', '2025-03-22 04:18:42'),
(57, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:18:42', '2025-03-22 04:18:42'),
(58, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(59, 161, 'dq', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(60, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(61, 161, 'dq', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(62, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(63, 161, '1w11', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(64, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(65, 161, '1w11', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(66, 162, 'opjoi joj', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:24:02', '2025-03-22 04:24:02'),
(67, 162, 'opjoi joj', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:24:02', '2025-03-22 04:24:02'),
(68, 166, 'lmaf q', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:39:08', '2025-03-22 08:39:08'),
(69, 166, 'lmaf q', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:39:08', '2025-03-22 08:39:08'),
(70, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:41:39', '2025-03-22 08:41:39'),
(71, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:41:39', '2025-03-22 08:41:39'),
(72, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:44:44', '2025-03-22 08:44:44'),
(73, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:44:44', '2025-03-22 08:44:44'),
(74, 168, 'd', 'r', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 09:58:45', '2025-03-22 09:58:45'),
(75, 168, 'd', 'r', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 09:58:45', '2025-03-22 09:58:45'),
(76, 169, 'jdja haflha', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:02:49', '2025-03-22 10:02:49'),
(77, 169, 'jdja haflha', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:02:49', '2025-03-22 10:02:49'),
(78, 170, 'H₂O + CO₂ → C₆H₁₂O₆ + O₂', 'H₂O', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:16:53', '2025-03-22 10:16:53'),
(79, 170, 'H₂O + CO₂ → C₆H₁₂O₆ + O₂', 'H₂O', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:16:53', '2025-03-22 10:16:53'),
(80, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:22:29', '2025-03-22 10:22:29'),
(81, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:22:29', '2025-03-22 10:22:29'),
(82, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:24:51', '2025-03-22 10:24:51'),
(83, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:24:51', '2025-03-22 10:24:51'),
(84, 174, 'fwf', 'fw3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:28:17', '2025-03-22 10:28:17'),
(85, 174, 'fwf', 'fw3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:28:17', '2025-03-22 10:28:17'),
(86, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:04', '2025-03-22 11:43:04'),
(87, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:04', '2025-03-22 11:43:04'),
(88, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:16', '2025-03-22 11:43:16'),
(89, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:16', '2025-03-22 11:43:16'),
(90, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:11', '2025-03-22 17:48:11'),
(91, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:11', '2025-03-22 17:48:11'),
(92, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:24', '2025-03-22 17:48:24'),
(93, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:24', '2025-03-22 17:48:24'),
(94, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:23', '2025-03-22 17:52:23'),
(95, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:23', '2025-03-22 17:52:23'),
(96, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:30', '2025-03-22 17:52:30'),
(97, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:30', '2025-03-22 17:52:30'),
(98, 180, '$\\int x^2 dx$', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:06:44', '2025-03-24 17:21:47'),
(99, 180, 'gb`', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:06:44', '2025-03-22 18:06:44'),
(100, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:13:35', '2025-03-22 18:13:35'),
(102, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:14:34', '2025-03-22 18:14:34'),
(103, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:14:34', '2025-03-22 18:14:34'),
(104, 182, 'f3', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:15:13', '2025-03-22 18:15:13'),
(105, 182, 'f3', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:15:13', '2025-03-22 18:15:13'),
(106, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:28', '2025-03-22 18:22:28'),
(107, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:28', '2025-03-22 18:22:28'),
(108, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:34', '2025-03-22 18:22:34'),
(109, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:34', '2025-03-22 18:22:34'),
(110, 184, 'f2', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:24:33', '2025-03-22 18:24:33'),
(111, 184, 'f2', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:24:33', '2025-03-22 18:24:33'),
(112, 186, 'wf', 'fw', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:31:44', '2025-03-22 18:31:44'),
(113, 186, 'wf', 'fw', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:31:44', '2025-03-22 18:31:44'),
(114, 187, 'r2', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:35:51', '2025-03-22 18:35:51'),
(115, 187, 'r2', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:35:51', '2025-03-22 18:35:51'),
(116, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:33', '2025-03-22 18:37:33'),
(117, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:33', '2025-03-22 18:37:33'),
(118, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:39', '2025-03-22 18:37:39'),
(119, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:39', '2025-03-22 18:37:39'),
(120, 189, 'nji', 'p', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:43:46', '2025-03-22 18:43:46'),
(121, 189, 'nji', 'p', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:43:46', '2025-03-22 18:43:46'),
(122, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:22', '2025-03-23 02:27:22'),
(123, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:22', '2025-03-23 02:27:22'),
(124, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:28', '2025-03-23 02:27:28'),
(125, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:28', '2025-03-23 02:27:28'),
(126, 192, 'what is formula of Newton\'s Second Law ?', 'F = ma', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:36:46', '2025-03-23 02:36:46'),
(127, 192, 'what is formula of Newton\'s Second Law ?', 'F = ma', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:36:46', '2025-03-23 02:36:46'),
(128, 193, 'VR', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:54:32', '2025-03-23 02:54:32'),
(129, 193, 'VR', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:54:32', '2025-03-23 02:54:32'),
(130, 194, 'hi', 'io', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:03:19', '2025-03-23 03:03:19'),
(131, 194, 'hi', 'io', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:03:19', '2025-03-23 03:03:19'),
(132, 196, 'f', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:08:54', '2025-03-23 03:08:54'),
(133, 196, 'f', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:08:54', '2025-03-23 03:08:54'),
(134, 198, 'fw', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:11:59', '2025-03-23 03:11:59'),
(135, 198, 'fw', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:11:59', '2025-03-23 03:11:59'),
(136, 199, 'What is the pH value of pure water at 25°C?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:15:39', '2025-03-23 03:15:39'),
(137, 199, 'What is the pH value of pure water at 25°C?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:15:39', '2025-03-23 03:15:39'),
(138, 200, 'what is ph of water at 25 degree celsius ?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:18:29', '2025-03-23 03:18:29'),
(139, 200, 'what is ph of water at 25 degree celsius ?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:18:29', '2025-03-23 03:18:29'),
(142, 203, 'jkl', '9', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:25:28', '2025-03-23 03:25:28'),
(143, 203, 'jkl', '9', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:25:28', '2025-03-23 03:25:28'),
(144, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:08:37', '2025-03-23 06:08:37'),
(145, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:08:37', '2025-03-23 06:08:37'),
(146, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(147, 206, 'Evaluate \\int 5x^2 dx:', '\\frac{5x^3}{3} + C', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(149, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:48', '2025-03-23 06:13:48'),
(150, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:48', '2025-03-23 06:13:48'),
(151, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:57', '2025-03-23 06:13:57'),
(152, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:57', '2025-03-23 06:13:57'),
(153, 208, '2.	Which gas is known as “Laughing Gas”?', 'b) Carbon Dioxide (CO₂)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:19:33', '2025-03-23 06:19:33'),
(155, 210, 'Which gas is known as “Laughing Gas”?', 'b) Carbon Dioxide (CO₂)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:44:21', '2025-03-23 07:44:21'),
(156, 212, '2.	Which gas is known as “Laughing Gas”?', 'c) Nitrous Oxide (N₂O)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:47:05', '2025-03-24 08:39:53'),
(157, 212, '2.	Which gas is known as “Laughing Gas”?, $\\int x^2 dx$)', NULL, 'true_false', 1.00, 'ddd', 1, 1, '2025-03-23 07:47:05', '2025-03-25 08:12:08'),
(158, 213, '1.	Which of the following is an example of a chemical change?', 'b) Rusting of iron', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:51:47', '2025-03-23 07:51:47'),
(159, 213, '1.	Which of the following is an example of a chemical change?', 'b) Rusting of iron', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:51:47', '2025-03-23 07:51:47'),
(160, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:19', '2025-03-23 07:58:19'),
(161, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:19', '2025-03-23 07:58:19'),
(162, 215, '1.	Which of the following is an example of a chemical change?Question Text (Use LaTeX for math: e.g., $\\int x^2 dx$)\r\n', NULL, 'multiple_choice', 1.00, 'Question Text (Use LaTeX for math: e.g., $\\int x^2 dx$)\r\n', 1, 1, '2025-03-23 07:58:28', '2025-03-26 07:43:32'),
(163, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:28', '2025-03-23 07:58:28');

-- --------------------------------------------------------

--
-- Table structure for table `question_category_mappings`
--

CREATE TABLE `question_category_mappings` (
  `mapping_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `question_category_mappings`
--

INSERT INTO `question_category_mappings` (`mapping_id`, `question_id`, `category_id`, `created_at`, `updated_at`) VALUES
(5, 162, 8, '2025-03-26 07:43:32', '2025-03-26 07:43:32'),
(6, 162, 5, '2025-03-26 07:43:32', '2025-03-26 07:43:32');

-- --------------------------------------------------------

--
-- Table structure for table `sections`
--

CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL,
  `exam_id` int(11) DEFAULT NULL,
  `section_name` varchar(300) NOT NULL,
  `description` text DEFAULT NULL,
  `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
  `position` int(11) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sections`
--

INSERT INTO `sections` (`section_id`, `exam_id`, `section_name`, `description`, `duration`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(140, NULL, '', NULL, NULL, 0, 1, '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(141, NULL, '', NULL, NULL, 1, 1, '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(142, 85, '', NULL, NULL, 0, 1, '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(143, 85, '', NULL, NULL, 1, 1, '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(144, 86, '', NULL, NULL, 0, 1, '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(145, 86, '', NULL, NULL, 1, 1, '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(146, 87, '', NULL, NULL, 0, 1, '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(147, 87, '', NULL, NULL, 1, 1, '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(148, 166, 'x23', NULL, NULL, 0, 1, '2025-03-22 02:42:37', '2025-03-22 02:42:56'),
(149, 167, 'rgn', NULL, NULL, 0, 1, '2025-03-22 02:44:13', '2025-03-22 02:44:30'),
(150, 167, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:44:13', '2025-03-22 02:44:13'),
(151, 168, 'l1', NULL, NULL, 0, 1, '2025-03-22 02:51:04', '2025-03-22 02:51:28'),
(152, 168, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:51:04', '2025-03-22 02:51:04'),
(153, 169, 'gb1', NULL, NULL, 0, 1, '2025-03-22 02:58:03', '2025-03-22 02:58:30'),
(154, 169, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:58:03', '2025-03-22 02:58:03'),
(155, 170, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 04:02:41', '2025-03-22 04:02:41'),
(156, 172, 'xdf1', NULL, NULL, 0, 1, '2025-03-22 04:04:59', '2025-03-22 04:05:20'),
(157, 173, 'x123', NULL, NULL, 0, 1, '2025-03-22 04:13:08', '2025-03-22 04:13:33'),
(158, 174, 'r1', NULL, NULL, 0, 1, '2025-03-22 04:14:40', '2025-03-22 04:15:01'),
(159, 175, 'xf1', NULL, NULL, 0, 1, '2025-03-22 04:16:07', '2025-03-22 04:16:21'),
(160, 176, 'q1', NULL, NULL, 0, 1, '2025-03-22 04:18:20', '2025-03-22 04:18:42'),
(161, 176, 'q2', NULL, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(162, 177, 'ox1', NULL, NULL, 0, 1, '2025-03-22 04:23:36', '2025-03-22 04:24:02'),
(163, 178, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 04:35:14', '2025-03-22 04:35:14'),
(164, 180, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 08:28:52', '2025-03-22 08:28:52'),
(165, 180, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 08:28:52', '2025-03-22 08:28:52'),
(166, 181, 'l1', NULL, NULL, 0, 1, '2025-03-22 08:38:38', '2025-03-22 08:39:08'),
(167, 182, 'lad', NULL, NULL, 0, 1, '2025-03-22 08:41:23', '2025-03-22 08:41:39'),
(168, 183, 'm0-1', NULL, NULL, 0, 1, '2025-03-22 09:58:22', '2025-03-22 09:58:45'),
(169, 184, 'p1', NULL, NULL, 0, 1, '2025-03-22 10:02:28', '2025-03-22 10:02:49'),
(170, 185, 'Mathematics', NULL, NULL, 0, 1, '2025-03-22 10:09:22', '2025-03-22 10:16:53'),
(171, 185, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 10:09:22', '2025-03-22 10:09:22'),
(172, 186, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 10:20:49', '2025-03-22 10:20:49'),
(173, 188, 'd1', NULL, NULL, 0, 1, '2025-03-22 10:21:55', '2025-03-22 10:22:29'),
(174, 189, 'g3', NULL, NULL, 0, 1, '2025-03-22 10:27:41', '2025-03-22 10:28:17'),
(175, 191, 'x-1', NULL, NULL, 0, 1, '2025-03-22 11:42:45', '2025-03-22 11:43:04'),
(176, 193, 'xcf1', NULL, NULL, 0, 1, '2025-03-22 17:47:45', '2025-03-22 17:48:11'),
(177, 194, 'fv1', NULL, NULL, 0, 1, '2025-03-22 17:51:56', '2025-03-22 17:52:23'),
(178, 194, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 17:51:56', '2025-03-22 17:51:56'),
(179, 195, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 18:05:18', '2025-03-22 18:05:18'),
(180, 196, 'gb1', NULL, NULL, 0, 1, '2025-03-22 18:06:13', '2025-03-22 18:06:44'),
(181, 198, 'r4', NULL, NULL, 0, 1, '2025-03-22 18:13:23', '2025-03-22 18:13:35'),
(182, 199, 'fr', NULL, NULL, 0, 1, '2025-03-22 18:15:02', '2025-03-22 18:15:13'),
(183, 200, 'gbt', NULL, NULL, 0, 1, '2025-03-22 18:22:16', '2025-03-22 18:22:28'),
(184, 201, 'r1', NULL, NULL, 0, 1, '2025-03-22 18:24:13', '2025-03-22 18:24:33'),
(185, 201, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 18:24:13', '2025-03-22 18:24:13'),
(186, 202, 'fr1', NULL, NULL, 0, 1, '2025-03-22 18:31:23', '2025-03-22 18:31:44'),
(187, 203, 'gr', NULL, NULL, 0, 1, '2025-03-22 18:35:36', '2025-03-22 18:35:51'),
(188, 204, 'free1', NULL, NULL, 0, 1, '2025-03-22 18:37:19', '2025-03-22 18:37:33'),
(189, 205, 'nm1', NULL, NULL, 0, 1, '2025-03-22 18:43:25', '2025-03-22 18:43:46'),
(190, 206, 'q23', NULL, NULL, 0, 1, '2025-03-23 02:27:05', '2025-03-23 02:27:22'),
(191, 207, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 02:33:07', '2025-03-23 02:33:07'),
(192, 208, 'xpc', NULL, NULL, 0, 1, '2025-03-23 02:35:54', '2025-03-23 02:36:46'),
(193, 209, 'RT', NULL, NULL, 0, 1, '2025-03-23 02:54:14', '2025-03-23 02:54:32'),
(194, 210, 'pl1', NULL, NULL, 0, 1, '2025-03-23 03:02:59', '2025-03-23 03:03:19'),
(195, 210, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:02:59', '2025-03-23 03:02:59'),
(196, 211, 'rf1', NULL, NULL, 0, 1, '2025-03-23 03:08:43', '2025-03-23 03:08:54'),
(197, 211, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:08:43', '2025-03-23 03:08:43'),
(198, 212, 'r1', NULL, NULL, 0, 1, '2025-03-23 03:11:45', '2025-03-23 03:11:59'),
(199, 213, 'oimn', NULL, NULL, 0, 1, '2025-03-23 03:14:24', '2025-03-23 03:15:39'),
(200, 215, 'Basic Chemistry', NULL, NULL, 0, 1, '2025-03-23 03:17:54', '2025-03-23 03:18:29'),
(201, 216, 'lio', NULL, NULL, 0, 1, '2025-03-23 03:21:13', '2025-03-23 03:21:29'),
(202, 216, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:21:13', '2025-03-23 03:21:13'),
(203, 217, 'oi1', NULL, NULL, 0, 1, '2025-03-23 03:25:11', '2025-03-23 03:25:28'),
(204, 217, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:25:11', '2025-03-23 03:25:11'),
(205, 218, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 06:07:53', '2025-03-23 06:11:15'),
(206, 218, 'Mathematics', NULL, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(207, 219, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 06:12:18', '2025-03-23 06:13:48'),
(208, 222, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 06:19:33', '2025-03-23 06:19:33'),
(209, 223, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 06:24:50', '2025-03-23 06:24:50'),
(210, 224, 'PR-1', NULL, NULL, 0, 1, '2025-03-23 07:43:00', '2025-03-23 07:44:21'),
(211, 224, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 07:43:00', '2025-03-23 07:43:00'),
(212, 225, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 07:46:21', '2025-03-23 07:47:05'),
(213, 226, 'chemistry', NULL, NULL, 0, 1, '2025-03-23 07:50:43', '2025-03-23 07:51:47'),
(214, 226, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 07:50:43', '2025-03-23 07:50:43'),
(215, 227, 'ce', NULL, NULL, 0, 1, '2025-03-23 07:57:40', '2025-03-23 07:58:19'),
(216, 228, 'Section 1', NULL, NULL, 0, 1, '2025-03-24 06:57:31', '2025-03-24 06:57:31'),
(217, 228, 'Section 1', NULL, NULL, 0, 1, '2025-03-24 06:57:31', '2025-03-24 06:57:31');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `expires` datetime NOT NULL,
  `data` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(300) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','teacher','student','principal') DEFAULT 'student',
  `full_name` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `bio` text NOT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expires` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` datetime NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `name`, `email`, `password`, `role`, `full_name`, `date_of_birth`, `profile_image`, `bio`, `reset_token`, `reset_token_expires`, `is_active`, `last_login`, `is_deleted`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'admin_user', 'CHARANPREET SINGH', '<EMAIL>', '$2b$10$mSKpfUFwzCH/8yaccQHa1e0X0KGfY.PqNxvc2nHNw/Ubu8mic7vTq', 'admin', 'Admin User', '1990-01-01', '/uploads/profiles/profile-1742303751385-38531603.jpeg', 'I am passionate about technology, education, and student welfare. My work involves managing a school LAN-based mail and chat system, ensuring smooth communication among students. I also handle database management, including importing schedule data from CSV files, tracking issue statuses, and automating updates.\r\n\r\nIn addition, I oversee boys’ hostel reports, managing attendance, leave records, and stream-wise distribution. I actively promote digital learning, setting up cameras and computers for YouTube-based online classes, helping students prepare for meritorious school admissions.\r\n\r\nI am also working on enhancing STEM education by setting up an Atal Tinkering Lab (ATL) focused on reading and computing. To improve school infrastructure, I advocate for air conditioning in reading rooms, computer labs, staff rooms, and common areas to create a better learning environment.\r\n\r\nOn the technical side, I manage Windows 8.1 Gold Edition lab PCs, exploring centralized software installations and optimizing network administration to filter online content effectively.\r\n\r\nOutside of work, I enjoy gaming and cycling, which help me maintain a balance between work and personal interests. My dedication to education, technology, and student development drives me to constantly seek innovative solutions for a better learning experience.', NULL, NULL, 1, '2025-03-18 18:26:33', 0, '2025-03-17 05:34:18', '2025-03-18 13:15:51', NULL),
(2, 'teacher_user', '', '<EMAIL>', '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS', 'teacher', 'Teacher User', '1985-05-15', NULL, '', NULL, NULL, 1, '0000-00-00 00:00:00', 0, '2025-03-17 05:34:18', '2025-03-17 05:34:18', NULL),
(3, 'student_user', '', '<EMAIL>', '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS', 'student', 'Student User', '2000-03-20', NULL, '', NULL, NULL, 1, '0000-00-00 00:00:00', 0, '2025-03-17 05:34:18', '2025-03-17 05:34:18', NULL),
(4, 'itadmin485', 'itadmin485', '<EMAIL>', '$2b$10$w1BB/ehIT40Y21fGgSVWSuwsHzUi.wjLFxADRm677KxyTKEu6Bjwm', 'admin', NULL, '1989-12-31', '/uploads/profiles/profile-1742866489104-394726837.jpeg', 'kop', NULL, NULL, 1, '2025-03-24 23:02:09', 0, '2025-03-24 17:32:09', '2025-03-25 01:34:49', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_answers`
--

CREATE TABLE `user_answers` (
  `id` int(11) NOT NULL,
  `attempt_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `selected_option_id` int(11) DEFAULT NULL,
  `essay_answer` text DEFAULT NULL,
  `marks_obtained` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `exams`
--
ALTER TABLE `exams`
  ADD PRIMARY KEY (`exam_id`),
  ADD UNIQUE KEY `exam_name` (`exam_name`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `idx_name` (`exam_name`),
  ADD KEY `idx_publish_date` (`publish_date`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD PRIMARY KEY (`attempt_id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_notification` (`user_id`,`is_read`);

--
-- Indexes for table `options`
--
ALTER TABLE `options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_question_option` (`question_id`,`position`);

--
-- Indexes for table `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`question_id`),
  ADD KEY `idx_section_question` (`section_id`,`position`);

--
-- Indexes for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  ADD PRIMARY KEY (`mapping_id`),
  ADD UNIQUE KEY `unique_question_category` (`question_id`,`category_id`),
  ADD KEY `fk_question_category_question` (`question_id`),
  ADD KEY `fk_question_category_category` (`category_id`);

--
-- Indexes for table `sections`
--
ALTER TABLE `sections`
  ADD PRIMARY KEY (`section_id`),
  ADD KEY `idx_exam_section` (`exam_id`,`position`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_session` (`user_id`),
  ADD KEY `idx_expires` (`expires`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_deleted_at` (`deleted_at`);

--
-- Indexes for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `exams`
--
ALTER TABLE `exams`
  MODIFY `exam_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=229;

--
-- AUTO_INCREMENT for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  MODIFY `attempt_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `options`
--
ALTER TABLE `options`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `questions`
--
ALTER TABLE `questions`
  MODIFY `question_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=164;

--
-- AUTO_INCREMENT for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  MODIFY `mapping_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `sections`
--
ALTER TABLE `sections`
  MODIFY `section_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=218;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `user_answers`
--
ALTER TABLE `user_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

--
-- Constraints for table `exams`
--
ALTER TABLE `exams`
  ADD CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

--
-- Constraints for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD CONSTRAINT `exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `options`
--
ALTER TABLE `options`
  ADD CONSTRAINT `options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE;

--
-- Constraints for table `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `questions_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`) ON DELETE CASCADE;

--
-- Constraints for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  ADD CONSTRAINT `fk_question_category_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_question_category_question` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE;

--
-- Constraints for table `sections`
--
ALTER TABLE `sections`
  ADD CONSTRAINT `sections_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD CONSTRAINT `user_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `exam_attempts` (`attempt_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `options` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
-- phpMyAdmin SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Mar 26, 2025 at 05:12 PM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `exam_prep_platform`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`category_id`, `name`, `description`, `parent_id`, `created_at`, `updated_at`) VALUES
(1, 'Mathematics', 'Mathematics related exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(2, 'Science', 'Science related exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(3, 'Language', 'Language proficiency exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(4, 'Programming', 'Programming and coding exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(5, 'General Knowledge', 'General knowledge and aptitude exams', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(6, 'Physics', 'Physics examination topics', NULL, '2025-03-17 05:34:18', '2025-03-17 05:34:18'),
(7, 'Chemistry', 'Chemistry cat', NULL, '2025-03-24 07:52:49', '2025-03-24 07:52:49'),
(8, 'Computer Science', 'Computer Science cat', NULL, '2025-03-24 07:53:07', '2025-03-24 07:53:07');

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `template_name` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exams`
--

CREATE TABLE `exams` (
  `exam_id` int(11) NOT NULL,
  `exam_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `duration` int(11) NOT NULL DEFAULT 60 COMMENT 'Duration in minutes',
  `instructions` text DEFAULT NULL,
  `publish_date` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `passing_marks` decimal(5,2) DEFAULT 60.00,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `difficulty` enum('beginner','intermediate','advanced') DEFAULT 'intermediate',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `exams`
--

INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(83, 'computer science', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(85, 'Mega Test2', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(86, 'xc', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(87, '112', NULL, 12, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(89, 'zc', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:13:40', '2025-03-19 17:13:40'),
(91, 'ca', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:19:14', '2025-03-19 17:19:14'),
(93, 'das', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:29:04', '2025-03-19 17:29:04'),
(95, 'dazca', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-19 17:37:45', '2025-03-19 17:37:45'),
(96, 'computer sciencew1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 01:33:04', '2025-03-20 01:33:40'),
(98, 'w1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 01:50:06', '2025-03-20 01:50:06'),
(100, '3e', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 02:41:48', '2025-03-20 02:42:26'),
(101, 'qsc', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 02:48:49', '2025-03-20 02:48:49'),
(103, 'dax', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:32:38', '2025-03-20 05:32:38'),
(105, 'dax32', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:36:06', '2025-03-20 05:36:06'),
(107, 'xdf', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 05:38:45', '2025-03-20 05:38:45'),
(109, 'dare', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 06:58:53', '2025-03-20 06:58:53'),
(111, 'daree', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 06:59:40', '2025-03-20 06:59:40'),
(112, 'xfe', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:38:27', '2025-03-20 07:38:27'),
(114, 'xfes', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:43:13', '2025-03-20 07:43:13'),
(115, 'daee', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 07:48:01', '2025-03-20 07:48:01'),
(117, 'x3', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 16:46:22', '2025-03-20 16:46:22'),
(118, 'xe3', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 17:07:31', '2025-03-20 17:07:34'),
(120, 'xe4', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-20 17:12:38', '2025-03-20 17:15:58'),
(121, 'xe5', NULL, 61, NULL, NULL, 1, 1, NULL, 44.00, 'draft', 'intermediate', '2025-03-20 17:17:04', '2025-03-20 17:17:36'),
(122, 'xe44', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 17:20:11', '2025-03-20 17:20:11'),
(123, 'xe35', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 18:13:48', '2025-03-20 18:13:48'),
(124, 'e34', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-20 18:15:17', '2025-03-20 18:15:17'),
(125, 'xe22', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:26:46', '2025-03-21 01:28:41'),
(126, 'xd21', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:30:25', '2025-03-21 01:30:25'),
(127, 'xe231', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:35:34', '2025-03-21 01:35:34'),
(128, 'dex', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:36:44', '2025-03-21 01:36:44'),
(129, 'fr1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:38:31', '2025-03-21 01:38:31'),
(130, 'x3121', NULL, 60, NULL, NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:42:15', '2025-03-21 01:46:18'),
(131, 'xd32121', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:47:52', '2025-03-21 01:47:52'),
(133, 'xdf1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:51:06', '2025-03-21 01:51:06'),
(134, 'qqdq', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 01:52:22', '2025-03-21 01:52:22'),
(135, 'fdew', NULL, 334, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 01:54:16', '2025-03-21 01:54:57'),
(136, 'tx1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:52:19', '2025-03-21 06:52:31'),
(137, 'tx11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:53:56', '2025-03-21 06:54:01'),
(139, 'tx111', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigatiown and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 06:58:28', '2025-03-21 06:58:33'),
(140, 'r3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:00:00', '2025-03-21 07:00:09'),
(142, 'r311', NULL, 60, 'Online Exam Instructions:\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:00:29', '2025-03-21 07:00:43'),
(143, 'r23', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:05:41', '2025-03-21 07:05:49'),
(144, '12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:29:15', '2025-03-21 07:29:22'),
(145, 'dfg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:35:18', '2025-03-21 07:35:30'),
(146, 'r32', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:47:38', '2025-03-21 07:47:47'),
(147, 'e321', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:50:21', '2025-03-21 07:50:27'),
(148, 'x324', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 07:54:49', '2025-03-21 07:55:01'),
(149, 'nv1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:00:32', '2025-03-21 08:00:38'),
(150, 'm345', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:07:29', '2025-03-21 08:07:37'),
(151, 'x221', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:10:26', '2025-03-21 08:10:36'),
(152, 'xe3`1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 08:11:39', '2025-03-21 08:11:39'),
(153, 'xc44', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 08:11:54', '2025-03-21 08:11:54'),
(154, 'p3g', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:13:33', '2025-03-21 08:13:42'),
(155, 'cvf', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 08:15:42', '2025-03-21 08:15:48'),
(156, 'xdr', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-21 15:10:36', '2025-03-21 15:10:36'),
(157, 'x3132', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:17:03', '2025-03-21 15:17:11'),
(158, 'xft4', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:37:48', '2025-03-21 15:37:56'),
(159, 'redman', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 15:58:04', '2025-03-21 15:58:15'),
(160, 'gn', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 16:45:04', '2025-03-21 16:45:19'),
(161, 'xe3111', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 17:01:14', '2025-03-21 17:01:23');
INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(162, 'fr', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 18:18:10', '2025-03-21 18:18:17'),
(163, 'he23', NULL, 23, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-21 18:47:18', '2025-03-21 18:48:43'),
(164, 'rty5', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:28:24', '2025-03-22 02:28:32'),
(165, 'wed', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:35:15', '2025-03-22 02:35:23'),
(166, 'fedv', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:42:26', '2025-03-22 02:42:37'),
(167, 'ehj', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:44:07', '2025-03-22 02:44:13'),
(168, 'jkl', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:51:01', '2025-03-22 02:51:12'),
(169, 'ftg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 02:57:45', '2025-03-22 02:58:03'),
(170, 'o1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:02:29', '2025-03-22 04:02:41'),
(172, 'p1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:04:48', '2025-03-22 04:04:59'),
(173, 'op1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:13:02', '2025-03-22 04:13:08'),
(174, 'ox1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:14:33', '2025-03-22 04:14:40'),
(175, 'cx1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:16:00', '2025-03-22 04:16:07'),
(176, 'p12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:18:13', '2025-03-22 04:18:20'),
(177, 'pox1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:23:29', '2025-03-22 04:23:36'),
(178, 'po1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 04:35:05', '2025-03-22 04:35:14'),
(179, 'ind', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-22 05:37:12', '2025-03-22 05:37:12'),
(180, 'ko', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:28:50', '2025-03-22 08:29:19'),
(181, 'o2', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:38:36', '2025-03-22 08:38:44'),
(182, 'l12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 08:41:14', '2025-03-22 08:41:23'),
(183, 'kiop', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 09:58:11', '2025-03-22 09:58:22'),
(184, 'pl1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:02:14', '2025-03-22 10:02:28'),
(185, 'x-2', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:09:21', '2025-03-22 10:13:08'),
(186, 'f1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:20:32', '2025-03-22 10:20:49'),
(188, 'f11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:21:53', '2025-03-22 10:22:10'),
(189, 'fg12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 10:27:33', '2025-03-22 10:27:41'),
(190, 'dt1', NULL, 60, NULL, NULL, 1, 1, NULL, 60.00, 'draft', 'intermediate', '2025-03-22 11:20:43', '2025-03-22 11:20:43'),
(191, 'pol1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 11:42:22', '2025-03-22 11:42:45'),
(193, 'r35t', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 17:47:32', '2025-03-22 17:47:45'),
(194, 'rfg3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 17:51:45', '2025-03-22 17:51:56'),
(195, 'rf11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:05:05', '2025-03-22 18:05:18'),
(196, 'rfg', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:06:12', '2025-03-22 18:06:23'),
(198, 'lo1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:13:14', '2025-03-22 18:13:23'),
(199, 'rfg1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:14:56', '2025-03-22 18:15:02');
INSERT INTO `exams` (`exam_id`, `exam_name`, `description`, `duration`, `instructions`, `publish_date`, `is_active`, `created_by`, `category_id`, `passing_marks`, `status`, `difficulty`, `created_at`, `updated_at`) VALUES
(200, 'xcfr3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:22:09', '2025-03-22 18:22:16'),
(201, 'fgbrr', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:24:06', '2025-03-22 18:24:13'),
(202, 'lpp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:31:20', '2025-03-22 18:31:33'),
(203, 'oo1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:35:27', '2025-03-22 18:35:36'),
(204, 'tgb', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:37:11', '2025-03-22 18:37:19'),
(205, 'lopp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-22 18:43:15', '2025-03-22 18:43:25'),
(206, 'p010', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:26:57', '2025-03-23 02:27:05'),
(207, 'bgt', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:32:59', '2025-03-23 02:33:07'),
(208, 'xp1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:35:52', '2025-03-23 02:36:01'),
(209, 'PIL', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 02:54:06', '2025-03-23 02:54:14'),
(210, 'xr12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:02:51', '2025-03-23 03:02:59'),
(211, 'polk1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:08:36', '2025-03-23 03:08:43'),
(212, 'kl1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:11:35', '2025-03-23 03:11:45'),
(213, 'lo', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:14:17', '2025-03-23 03:14:24'),
(215, 'megatron', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:17:44', '2025-03-23 03:17:54'),
(216, 'jil', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:21:05', '2025-03-23 03:21:13'),
(217, 'frg3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 03:25:04', '2025-03-23 03:25:11'),
(218, 'p101', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:07:14', '2025-03-23 06:07:53'),
(219, 'p11', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:12:02', '2025-03-23 06:12:18'),
(222, 'p123', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:18:36', '2025-03-23 06:19:33'),
(223, 'bgt`', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 06:24:22', '2025-03-23 06:24:50'),
(224, 'grok3', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 07:42:44', '2025-03-23 07:43:00'),
(225, 'pt-1', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-23 07:46:11', '2025-03-23 07:46:21'),
(226, 'k12', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', '2025-03-28 13:21:00', 1, 1, NULL, 40.00, 'published', 'intermediate', '2025-03-23 07:50:33', '2025-03-23 07:51:56'),
(227, 'gin', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', '2025-03-26 13:30:00', 1, 1, NULL, 40.00, 'published', 'intermediate', '2025-03-23 07:57:19', '2025-03-23 07:58:29'),
(228, 'INDIGO', NULL, 60, 'Online Exam Instructions\n	1.	Technical Requirements: Ensure a stable internet connection, a functioning webcam, and a microphone (if required). Use a compatible browser such as Google Chrome or Mozilla Firefox.\n	2.	Login and Authentication: Log in using your assigned credentials before the exam start time. Some exams may require ID verification via webcam.\n	3.	Exam Environment: Choose a quiet, well-lit room with no background noise. Do not use multiple screens, external devices, or study materials unless permitted.\n	4.	Exam Duration: The timer starts once you begin. Ensure you submit your answers before the time expires, as late submissions may not be accepted.\n	5.	Navigation and Answering: Read instructions carefully before starting. Some questions may be locked after answering, preventing changes. Do not refresh the page or switch tabs, as this may trigger security alerts.\n	6.	Prohibited Actions: Do not use unauthorized materials, communicate with others, or attempt to cheat. Suspicious activities will be flagged, and violations may lead to disqualification.\n	7.	Technical Issues: If you face technical difficulties, immediately inform the exam supervisor or support team.\n	8.	Submission: Click the “Submit” button before the timer ends. Ensure you receive confirmation of submission.\n\nFailure to follow these guidelines may result in penalties, including disqualification. Good luck!', NULL, 1, 1, NULL, 40.00, 'draft', 'intermediate', '2025-03-24 06:57:29', '2025-03-24 06:57:48');

-- --------------------------------------------------------

--
-- Table structure for table `exam_attempts`
--

CREATE TABLE `exam_attempts` (
  `attempt_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('in_progress','completed','abandoned') DEFAULT 'in_progress',
  `total_score` decimal(5,2) DEFAULT NULL,
  `attempt_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `is_read`, `created_at`) VALUES
(1, 3, 'Physics Test Result Available', 'Your Physics Fundamentals Test has been graded. Score: 85%', 'result', 0, '2025-03-17 05:34:18');

-- --------------------------------------------------------

--
-- Table structure for table `options`
--

CREATE TABLE `options` (
  `id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `option_text` text NOT NULL,
  `is_correct` tinyint(1) NOT NULL DEFAULT 0,
  `position` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `options`
--

INSERT INTO `options` (`id`, `question_id`, `option_text`, `is_correct`, `position`, `created_at`, `updated_at`) VALUES
(45, 162, '33', 0, 1, '2025-03-26 08:47:07', '2025-03-26 08:47:07'),
(46, 162, '44', 1, 2, '2025-03-26 08:47:07', '2025-03-26 08:47:07'),
(47, 165, '1', 0, 1, '2025-03-26 13:39:05', '2025-03-26 13:39:05'),
(48, 165, '2', 1, 2, '2025-03-26 13:39:05', '2025-03-26 13:39:05'),
(49, 166, '1', 0, 1, '2025-03-26 13:49:58', '2025-03-26 13:49:58'),
(50, 166, '2', 1, 2, '2025-03-26 13:49:58', '2025-03-26 13:49:58'),
(51, 167, '3', 0, 1, '2025-03-26 13:52:47', '2025-03-26 13:52:47'),
(52, 167, '4', 1, 2, '2025-03-26 13:52:47', '2025-03-26 13:52:47'),
(53, 168, '1', 0, 1, '2025-03-26 13:58:30', '2025-03-26 13:58:30'),
(54, 168, '2', 1, 2, '2025-03-26 13:58:30', '2025-03-26 13:58:30'),
(55, 169, '12', 1, 1, '2025-03-26 14:02:35', '2025-03-26 14:02:35'),
(56, 169, '31', 0, 2, '2025-03-26 14:02:35', '2025-03-26 14:02:35'),
(57, 170, '23', 0, 1, '2025-03-26 14:15:25', '2025-03-26 14:15:25'),
(58, 170, '12', 1, 2, '2025-03-26 14:15:25', '2025-03-26 14:15:25');

-- --------------------------------------------------------

--
-- Table structure for table `questions`
--

CREATE TABLE `questions` (
  `question_id` int(11) NOT NULL,
  `section_id` int(11) DEFAULT NULL,
  `question_text` text NOT NULL,
  `correct_answer` varchar(255) DEFAULT NULL,
  `question_type` enum('multiple_choice','true_false','essay') NOT NULL,
  `marks` decimal(5,2) DEFAULT 1.00,
  `solution_text` varchar(500) DEFAULT NULL,
  `position` int(11) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `questions`
--

INSERT INTO `questions` (`question_id`, `section_id`, `question_text`, `correct_answer`, `question_type`, `marks`, `solution_text`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(36, 149, 'fw', NULL, 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:44:52', '2025-03-22 02:44:52'),
(37, 149, 'fw', NULL, 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:44:52', '2025-03-22 02:44:52'),
(38, 151, 'rg', '45', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:51:28', '2025-03-22 02:51:28'),
(39, 151, 'rg', '45', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:51:28', '2025-03-22 02:51:28'),
(40, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:30', '2025-03-22 02:58:30'),
(41, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:30', '2025-03-22 02:58:30'),
(42, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:50', '2025-03-22 02:58:50'),
(43, 153, 'fg', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 02:58:50', '2025-03-22 02:58:50'),
(44, 155, 'e', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:02:52', '2025-03-22 04:02:52'),
(45, 155, 'e', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:02:52', '2025-03-22 04:02:52'),
(46, 156, 'qd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:05:20', '2025-03-22 04:05:20'),
(47, 156, 'qd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:05:20', '2025-03-22 04:05:20'),
(48, 157, 'dad', 'eq', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:13:33', '2025-03-22 04:13:33'),
(49, 157, 'dad', 'eq', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:13:33', '2025-03-22 04:13:33'),
(50, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:01', '2025-03-22 04:15:01'),
(51, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:01', '2025-03-22 04:15:01'),
(52, 158, 'what is ', 'true', 'true_false', 1.00, 'of', 1, 1, '2025-03-22 04:15:12', '2025-03-25 07:56:01'),
(53, 158, 'fffafa', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:15:12', '2025-03-22 04:15:12'),
(54, 159, 'qf', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:16:21', '2025-03-22 04:16:21'),
(55, 159, 'qf', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:16:21', '2025-03-22 04:16:21'),
(56, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:18:42', '2025-03-22 04:18:42'),
(57, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:18:42', '2025-03-22 04:18:42'),
(58, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(59, 161, 'dq', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(60, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(61, 161, 'dq', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(62, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(63, 161, '1w11', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(64, 160, 'daad', '44', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(65, 161, '1w11', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:21:03', '2025-03-22 04:21:03'),
(66, 162, 'opjoi joj', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:24:02', '2025-03-22 04:24:02'),
(67, 162, 'opjoi joj', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 04:24:02', '2025-03-22 04:24:02'),
(68, 166, 'lmaf q', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:39:08', '2025-03-22 08:39:08'),
(69, 166, 'lmaf q', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:39:08', '2025-03-22 08:39:08'),
(70, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:41:39', '2025-03-22 08:41:39'),
(71, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:41:39', '2025-03-22 08:41:39'),
(72, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:44:44', '2025-03-22 08:44:44'),
(73, 167, 'dd', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 08:44:44', '2025-03-22 08:44:44'),
(74, 168, 'd', 'r', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 09:58:45', '2025-03-22 09:58:45'),
(75, 168, 'd', 'r', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 09:58:45', '2025-03-22 09:58:45'),
(76, 169, 'jdja haflha', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:02:49', '2025-03-22 10:02:49'),
(77, 169, 'jdja haflha', '2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:02:49', '2025-03-22 10:02:49'),
(78, 170, 'H₂O + CO₂ → C₆H₁₂O₆ + O₂', 'H₂O', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:16:53', '2025-03-22 10:16:53'),
(79, 170, 'H₂O + CO₂ → C₆H₁₂O₆ + O₂', 'H₂O', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:16:53', '2025-03-22 10:16:53'),
(80, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:22:29', '2025-03-22 10:22:29'),
(81, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:22:29', '2025-03-22 10:22:29'),
(82, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:24:51', '2025-03-22 10:24:51'),
(83, 173, 'fe', '14', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:24:51', '2025-03-22 10:24:51'),
(84, 174, 'fwf', 'fw3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:28:17', '2025-03-22 10:28:17'),
(85, 174, 'fwf', 'fw3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 10:28:17', '2025-03-22 10:28:17'),
(86, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:04', '2025-03-22 11:43:04'),
(87, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:04', '2025-03-22 11:43:04'),
(88, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:16', '2025-03-22 11:43:16'),
(89, 175, 'c', 'r1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 11:43:16', '2025-03-22 11:43:16'),
(90, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:11', '2025-03-22 17:48:11'),
(91, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:11', '2025-03-22 17:48:11'),
(92, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:24', '2025-03-22 17:48:24'),
(93, 176, 'rgb', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:48:24', '2025-03-22 17:48:24'),
(94, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:23', '2025-03-22 17:52:23'),
(95, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:23', '2025-03-22 17:52:23'),
(96, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:30', '2025-03-22 17:52:30'),
(97, 177, 'fv', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 17:52:30', '2025-03-22 17:52:30'),
(98, 180, '$\\int x^2 dx$', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:06:44', '2025-03-24 17:21:47'),
(99, 180, 'gb`', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:06:44', '2025-03-22 18:06:44'),
(100, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:13:35', '2025-03-22 18:13:35'),
(102, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:14:34', '2025-03-22 18:14:34'),
(103, 181, 'f', 'f', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:14:34', '2025-03-22 18:14:34'),
(104, 182, 'f3', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:15:13', '2025-03-22 18:15:13'),
(105, 182, 'f3', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:15:13', '2025-03-22 18:15:13'),
(106, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:28', '2025-03-22 18:22:28'),
(107, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:28', '2025-03-22 18:22:28'),
(108, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:34', '2025-03-22 18:22:34'),
(109, 183, 'r', 'r3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:22:34', '2025-03-22 18:22:34'),
(110, 184, 'f2', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:24:33', '2025-03-22 18:24:33'),
(111, 184, 'f2', 'v', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:24:33', '2025-03-22 18:24:33'),
(112, 186, 'wf', 'fw', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:31:44', '2025-03-22 18:31:44'),
(113, 186, 'wf', 'fw', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:31:44', '2025-03-22 18:31:44'),
(114, 187, 'r2', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:35:51', '2025-03-22 18:35:51'),
(115, 187, 'r2', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:35:51', '2025-03-22 18:35:51'),
(116, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:33', '2025-03-22 18:37:33'),
(117, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:33', '2025-03-22 18:37:33'),
(118, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:39', '2025-03-22 18:37:39'),
(119, 188, 'dq', 'dq2', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:37:39', '2025-03-22 18:37:39'),
(120, 189, 'nji', 'p', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:43:46', '2025-03-22 18:43:46'),
(121, 189, 'nji', 'p', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-22 18:43:46', '2025-03-22 18:43:46'),
(122, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:22', '2025-03-23 02:27:22'),
(123, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:22', '2025-03-23 02:27:22'),
(124, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:28', '2025-03-23 02:27:28'),
(125, 190, 'vfr', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:27:28', '2025-03-23 02:27:28'),
(126, 192, 'what is formula of Newton\'s Second Law ?', 'F = ma', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:36:46', '2025-03-23 02:36:46'),
(127, 192, 'what is formula of Newton\'s Second Law ?', 'F = ma', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:36:46', '2025-03-23 02:36:46'),
(128, 193, 'VR', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:54:32', '2025-03-23 02:54:32'),
(129, 193, 'VR', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 02:54:32', '2025-03-23 02:54:32'),
(130, 194, 'hi', 'io', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:03:19', '2025-03-23 03:03:19'),
(131, 194, 'hi', 'io', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:03:19', '2025-03-23 03:03:19'),
(132, 196, 'f', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:08:54', '2025-03-23 03:08:54'),
(133, 196, 'f', 'false', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:08:54', '2025-03-23 03:08:54'),
(134, 198, 'fw', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:11:59', '2025-03-23 03:11:59'),
(135, 198, 'fw', '4', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:11:59', '2025-03-23 03:11:59'),
(136, 199, 'What is the pH value of pure water at 25°C?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:15:39', '2025-03-23 03:15:39'),
(137, 199, 'What is the pH value of pure water at 25°C?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:15:39', '2025-03-23 03:15:39'),
(138, 200, 'what is ph of water at 25 degree celsius ?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:18:29', '2025-03-23 03:18:29'),
(139, 200, 'what is ph of water at 25 degree celsius ?', '7', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:18:29', '2025-03-23 03:18:29'),
(142, 203, 'jkl', '9', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:25:28', '2025-03-23 03:25:28'),
(143, 203, 'jkl', '9', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 03:25:28', '2025-03-23 03:25:28'),
(144, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:08:37', '2025-03-23 06:08:37'),
(145, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:08:37', '2025-03-23 06:08:37'),
(146, 205, 'What is the IUPAC name of CH₃-CH₂-CHO?', 'Propanal', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(147, 206, 'Evaluate \\int 5x^2 dx:', '\\frac{5x^3}{3} + C', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(149, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:48', '2025-03-23 06:13:48'),
(150, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:48', '2025-03-23 06:13:48'),
(151, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:57', '2025-03-23 06:13:57'),
(152, 207, 'Find \\int x e^x dx using integration by parts.', '3', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:13:57', '2025-03-23 06:13:57'),
(153, 208, '2.	Which gas is known as “Laughing Gas”?', 'b) Carbon Dioxide (CO₂)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 06:19:33', '2025-03-23 06:19:33'),
(155, 210, 'Which gas is known as “Laughing Gas”?', 'b) Carbon Dioxide (CO₂)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:44:21', '2025-03-23 07:44:21'),
(156, 212, '2.	Which gas is known as “Laughing Gas”?', 'c) Nitrous Oxide (N₂O)', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:47:05', '2025-03-24 08:39:53'),
(157, 212, '2.	Which gas is known as “Laughing Gas”?, $\\int x^2 dx$)', 'true', 'true_false', 1.00, 'ddd', 1, 1, '2025-03-23 07:47:05', '2025-03-26 09:10:23'),
(158, 213, '1.	Which of the following is an example of a chemical change?', 'b) Rusting of iron', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:51:47', '2025-03-23 07:51:47'),
(159, 213, '1.	Which of the following is an example of a chemical change?', 'b) Rusting of iron', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:51:47', '2025-03-23 07:51:47'),
(160, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:19', '2025-03-23 07:58:19'),
(161, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:19', '2025-03-23 07:58:19'),
(162, 215, '1.	Which of the following is an example of a chemical change?Question Text (Use LaTeX for math: e.g., $\\int x^2 dx$)\r\n', NULL, 'multiple_choice', 1.00, 'Question Text (Use LaTeX for math: e.g., $\\int x^2 dx$)\r\n', 1, 1, '2025-03-23 07:58:28', '2025-03-26 08:47:07'),
(163, 215, '1.	Which of the following is an example of a chemical change?', '1', 'multiple_choice', 1.00, NULL, 1, 1, '2025-03-23 07:58:28', '2025-03-23 07:58:28'),
(164, 141, 'color of water', NULL, 'true_false', 1.00, '1', 1, 1, '2025-03-26 13:33:32', '2025-03-26 13:33:32'),
(165, 140, 'e1', NULL, 'multiple_choice', 1.00, '2', 1, 1, '2025-03-26 13:39:05', '2025-03-26 13:39:05'),
(166, NULL, 'c', NULL, 'multiple_choice', 1.00, '2', 1, 1, '2025-03-26 13:49:58', '2025-03-26 13:49:58'),
(167, NULL, '1', NULL, 'multiple_choice', 1.00, '2', 1, 1, '2025-03-26 13:52:47', '2025-03-26 13:52:47'),
(168, NULL, 'qd', NULL, 'multiple_choice', 1.00, '2', 1, 1, '2025-03-26 13:58:30', '2025-03-26 13:58:30'),
(169, NULL, 'q', NULL, 'multiple_choice', 1.00, '1', 1, 1, '2025-03-26 14:02:35', '2025-03-26 14:02:35'),
(170, NULL, '1', NULL, 'multiple_choice', 1.00, '2', 1, 1, '2025-03-26 14:15:25', '2025-03-26 14:15:25');

-- --------------------------------------------------------

--
-- Table structure for table `question_category_mappings`
--

CREATE TABLE `question_category_mappings` (
  `mapping_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `question_category_mappings`
--

INSERT INTO `question_category_mappings` (`mapping_id`, `question_id`, `category_id`, `created_at`, `updated_at`) VALUES
(7, 162, 8, '2025-03-26 08:47:07', '2025-03-26 08:47:07'),
(8, 162, 5, '2025-03-26 08:47:07', '2025-03-26 08:47:07'),
(13, 157, 8, '2025-03-26 09:10:23', '2025-03-26 09:10:23'),
(14, 157, 5, '2025-03-26 09:10:23', '2025-03-26 09:10:23'),
(15, 164, 8, '2025-03-26 13:33:32', '2025-03-26 13:33:32'),
(16, 164, 5, '2025-03-26 13:33:32', '2025-03-26 13:33:32'),
(17, 164, 3, '2025-03-26 13:33:32', '2025-03-26 13:33:32'),
(18, 165, 7, '2025-03-26 13:39:05', '2025-03-26 13:39:05'),
(19, 165, 5, '2025-03-26 13:39:05', '2025-03-26 13:39:05'),
(20, 166, 8, '2025-03-26 13:49:58', '2025-03-26 13:49:58'),
(21, 166, 5, '2025-03-26 13:49:58', '2025-03-26 13:49:58'),
(22, 167, 8, '2025-03-26 13:52:47', '2025-03-26 13:52:47'),
(23, 167, 5, '2025-03-26 13:52:47', '2025-03-26 13:52:47'),
(24, 168, 7, '2025-03-26 13:58:30', '2025-03-26 13:58:30'),
(25, 168, 8, '2025-03-26 13:58:30', '2025-03-26 13:58:30'),
(26, 169, 8, '2025-03-26 14:02:35', '2025-03-26 14:02:35'),
(27, 169, 3, '2025-03-26 14:02:35', '2025-03-26 14:02:35'),
(28, 170, 8, '2025-03-26 14:15:25', '2025-03-26 14:15:25'),
(29, 170, 5, '2025-03-26 14:15:25', '2025-03-26 14:15:25');

-- --------------------------------------------------------

--
-- Table structure for table `sections`
--

CREATE TABLE `sections` (
  `section_id` int(11) NOT NULL,
  `exam_id` int(11) DEFAULT NULL,
  `section_name` varchar(300) NOT NULL,
  `description` text DEFAULT NULL,
  `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
  `position` int(11) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sections`
--

INSERT INTO `sections` (`section_id`, `exam_id`, `section_name`, `description`, `duration`, `position`, `is_active`, `created_at`, `updated_at`) VALUES
(140, NULL, '', NULL, NULL, 0, 1, '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(141, NULL, '', NULL, NULL, 1, 1, '2025-03-19 14:53:57', '2025-03-20 05:36:27'),
(142, 85, '', NULL, NULL, 0, 1, '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(143, 85, '', NULL, NULL, 1, 1, '2025-03-19 17:00:29', '2025-03-19 17:00:29'),
(144, 86, '', NULL, NULL, 0, 1, '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(145, 86, '', NULL, NULL, 1, 1, '2025-03-19 17:03:09', '2025-03-19 17:03:09'),
(146, 87, '', NULL, NULL, 0, 1, '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(147, 87, '', NULL, NULL, 1, 1, '2025-03-19 17:08:21', '2025-03-19 17:08:21'),
(148, 166, 'x23', NULL, NULL, 0, 1, '2025-03-22 02:42:37', '2025-03-22 02:42:56'),
(149, 167, 'rgn', NULL, NULL, 0, 1, '2025-03-22 02:44:13', '2025-03-22 02:44:30'),
(150, 167, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:44:13', '2025-03-22 02:44:13'),
(151, 168, 'l1', NULL, NULL, 0, 1, '2025-03-22 02:51:04', '2025-03-22 02:51:28'),
(152, 168, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:51:04', '2025-03-22 02:51:04'),
(153, 169, 'gb1', NULL, NULL, 0, 1, '2025-03-22 02:58:03', '2025-03-22 02:58:30'),
(154, 169, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 02:58:03', '2025-03-22 02:58:03'),
(155, 170, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 04:02:41', '2025-03-22 04:02:41'),
(156, 172, 'xdf1', NULL, NULL, 0, 1, '2025-03-22 04:04:59', '2025-03-22 04:05:20'),
(157, 173, 'x123', NULL, NULL, 0, 1, '2025-03-22 04:13:08', '2025-03-22 04:13:33'),
(158, 174, 'r1', NULL, NULL, 0, 1, '2025-03-22 04:14:40', '2025-03-22 04:15:01'),
(159, 175, 'xf1', NULL, NULL, 0, 1, '2025-03-22 04:16:07', '2025-03-22 04:16:21'),
(160, 176, 'q1', NULL, NULL, 0, 1, '2025-03-22 04:18:20', '2025-03-22 04:18:42'),
(161, 176, 'q2', NULL, NULL, 1, 1, '2025-03-22 04:20:45', '2025-03-22 04:20:45'),
(162, 177, 'ox1', NULL, NULL, 0, 1, '2025-03-22 04:23:36', '2025-03-22 04:24:02'),
(163, 178, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 04:35:14', '2025-03-22 04:35:14'),
(164, 180, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 08:28:52', '2025-03-22 08:28:52'),
(165, 180, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 08:28:52', '2025-03-22 08:28:52'),
(166, 181, 'l1', NULL, NULL, 0, 1, '2025-03-22 08:38:38', '2025-03-22 08:39:08'),
(167, 182, 'lad', NULL, NULL, 0, 1, '2025-03-22 08:41:23', '2025-03-22 08:41:39'),
(168, 183, 'm0-1', NULL, NULL, 0, 1, '2025-03-22 09:58:22', '2025-03-22 09:58:45'),
(169, 184, 'p1', NULL, NULL, 0, 1, '2025-03-22 10:02:28', '2025-03-22 10:02:49'),
(170, 185, 'Mathematics', NULL, NULL, 0, 1, '2025-03-22 10:09:22', '2025-03-22 10:16:53'),
(171, 185, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 10:09:22', '2025-03-22 10:09:22'),
(172, 186, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 10:20:49', '2025-03-22 10:20:49'),
(173, 188, 'd1', NULL, NULL, 0, 1, '2025-03-22 10:21:55', '2025-03-22 10:22:29'),
(174, 189, 'g3', NULL, NULL, 0, 1, '2025-03-22 10:27:41', '2025-03-22 10:28:17'),
(175, 191, 'x-1', NULL, NULL, 0, 1, '2025-03-22 11:42:45', '2025-03-22 11:43:04'),
(176, 193, 'xcf1', NULL, NULL, 0, 1, '2025-03-22 17:47:45', '2025-03-22 17:48:11'),
(177, 194, 'fv1', NULL, NULL, 0, 1, '2025-03-22 17:51:56', '2025-03-22 17:52:23'),
(178, 194, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 17:51:56', '2025-03-22 17:51:56'),
(179, 195, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 18:05:18', '2025-03-22 18:05:18'),
(180, 196, 'gb1', NULL, NULL, 0, 1, '2025-03-22 18:06:13', '2025-03-22 18:06:44'),
(181, 198, 'r4', NULL, NULL, 0, 1, '2025-03-22 18:13:23', '2025-03-22 18:13:35'),
(182, 199, 'fr', NULL, NULL, 0, 1, '2025-03-22 18:15:02', '2025-03-22 18:15:13'),
(183, 200, 'gbt', NULL, NULL, 0, 1, '2025-03-22 18:22:16', '2025-03-22 18:22:28'),
(184, 201, 'r1', NULL, NULL, 0, 1, '2025-03-22 18:24:13', '2025-03-22 18:24:33'),
(185, 201, 'Section 1', NULL, NULL, 0, 1, '2025-03-22 18:24:13', '2025-03-22 18:24:13'),
(186, 202, 'fr1', NULL, NULL, 0, 1, '2025-03-22 18:31:23', '2025-03-22 18:31:44'),
(187, 203, 'gr', NULL, NULL, 0, 1, '2025-03-22 18:35:36', '2025-03-22 18:35:51'),
(188, 204, 'free1', NULL, NULL, 0, 1, '2025-03-22 18:37:19', '2025-03-22 18:37:33'),
(189, 205, 'nm1', NULL, NULL, 0, 1, '2025-03-22 18:43:25', '2025-03-22 18:43:46'),
(190, 206, 'q23', NULL, NULL, 0, 1, '2025-03-23 02:27:05', '2025-03-23 02:27:22'),
(191, 207, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 02:33:07', '2025-03-23 02:33:07'),
(192, 208, 'xpc', NULL, NULL, 0, 1, '2025-03-23 02:35:54', '2025-03-23 02:36:46'),
(193, 209, 'RT', NULL, NULL, 0, 1, '2025-03-23 02:54:14', '2025-03-23 02:54:32'),
(194, 210, 'pl1', NULL, NULL, 0, 1, '2025-03-23 03:02:59', '2025-03-23 03:03:19'),
(195, 210, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:02:59', '2025-03-23 03:02:59'),
(196, 211, 'rf1', NULL, NULL, 0, 1, '2025-03-23 03:08:43', '2025-03-23 03:08:54'),
(197, 211, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:08:43', '2025-03-23 03:08:43'),
(198, 212, 'r1', NULL, NULL, 0, 1, '2025-03-23 03:11:45', '2025-03-23 03:11:59'),
(199, 213, 'oimn', NULL, NULL, 0, 1, '2025-03-23 03:14:24', '2025-03-23 03:15:39'),
(200, 215, 'Basic Chemistry', NULL, NULL, 0, 1, '2025-03-23 03:17:54', '2025-03-23 03:18:29'),
(201, 216, 'lio', NULL, NULL, 0, 1, '2025-03-23 03:21:13', '2025-03-23 03:21:29'),
(202, 216, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:21:13', '2025-03-23 03:21:13'),
(203, 217, 'oi1', NULL, NULL, 0, 1, '2025-03-23 03:25:11', '2025-03-23 03:25:28'),
(204, 217, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 03:25:11', '2025-03-23 03:25:11'),
(205, 218, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 06:07:53', '2025-03-23 06:11:15'),
(206, 218, 'Mathematics', NULL, NULL, 1, 1, '2025-03-23 06:11:15', '2025-03-23 06:11:15'),
(207, 219, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 06:12:18', '2025-03-23 06:13:48'),
(208, 222, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 06:19:33', '2025-03-23 06:19:33'),
(209, 223, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 06:24:50', '2025-03-23 06:24:50'),
(210, 224, 'PR-1', NULL, NULL, 0, 1, '2025-03-23 07:43:00', '2025-03-23 07:44:21'),
(211, 224, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 07:43:00', '2025-03-23 07:43:00'),
(212, 225, 'Chemistry', NULL, NULL, 0, 1, '2025-03-23 07:46:21', '2025-03-23 07:47:05'),
(213, 226, 'chemistry', NULL, NULL, 0, 1, '2025-03-23 07:50:43', '2025-03-23 07:51:47'),
(214, 226, 'Section 1', NULL, NULL, 0, 1, '2025-03-23 07:50:43', '2025-03-23 07:50:43'),
(215, 227, 'ce', NULL, NULL, 0, 1, '2025-03-23 07:57:40', '2025-03-23 07:58:19'),
(216, 228, 'Section 1', NULL, NULL, 0, 1, '2025-03-24 06:57:31', '2025-03-24 06:57:31'),
(217, 228, 'Section 1', NULL, NULL, 0, 1, '2025-03-24 06:57:31', '2025-03-24 06:57:31');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `expires` datetime NOT NULL,
  `data` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL,
  `setting_name` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(300) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','teacher','student','principal') DEFAULT 'student',
  `full_name` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `bio` text NOT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expires` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` datetime NOT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `name`, `email`, `password`, `role`, `full_name`, `date_of_birth`, `profile_image`, `bio`, `reset_token`, `reset_token_expires`, `is_active`, `last_login`, `is_deleted`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'admin_user', 'CHARANPREET SINGH', '<EMAIL>', '$2b$10$mSKpfUFwzCH/8yaccQHa1e0X0KGfY.PqNxvc2nHNw/Ubu8mic7vTq', 'admin', 'Admin User', '1990-01-01', '/uploads/profiles/profile-1742303751385-38531603.jpeg', 'I am passionate about technology, education, and student welfare. My work involves managing a school LAN-based mail and chat system, ensuring smooth communication among students. I also handle database management, including importing schedule data from CSV files, tracking issue statuses, and automating updates.\r\n\r\nIn addition, I oversee boys’ hostel reports, managing attendance, leave records, and stream-wise distribution. I actively promote digital learning, setting up cameras and computers for YouTube-based online classes, helping students prepare for meritorious school admissions.\r\n\r\nI am also working on enhancing STEM education by setting up an Atal Tinkering Lab (ATL) focused on reading and computing. To improve school infrastructure, I advocate for air conditioning in reading rooms, computer labs, staff rooms, and common areas to create a better learning environment.\r\n\r\nOn the technical side, I manage Windows 8.1 Gold Edition lab PCs, exploring centralized software installations and optimizing network administration to filter online content effectively.\r\n\r\nOutside of work, I enjoy gaming and cycling, which help me maintain a balance between work and personal interests. My dedication to education, technology, and student development drives me to constantly seek innovative solutions for a better learning experience.', NULL, NULL, 1, '2025-03-18 18:26:33', 0, '2025-03-17 05:34:18', '2025-03-18 13:15:51', NULL),
(2, 'teacher_user', '', '<EMAIL>', '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS', 'teacher', 'Teacher User', '1985-05-15', NULL, '', NULL, NULL, 1, '0000-00-00 00:00:00', 0, '2025-03-17 05:34:18', '2025-03-17 05:34:18', NULL),
(3, 'student_user', '', '<EMAIL>', '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS', 'student', 'Student User', '2000-03-20', NULL, '', NULL, NULL, 1, '0000-00-00 00:00:00', 0, '2025-03-17 05:34:18', '2025-03-17 05:34:18', NULL),
(4, 'itadmin485', 'itadmin485', '<EMAIL>', '$2b$10$w1BB/ehIT40Y21fGgSVWSuwsHzUi.wjLFxADRm677KxyTKEu6Bjwm', 'admin', NULL, '1989-12-31', '/uploads/profiles/profile-1742866489104-394726837.jpeg', 'kop', NULL, NULL, 1, '2025-03-24 23:02:09', 0, '2025-03-24 17:32:09', '2025-03-25 01:34:49', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_answers`
--

CREATE TABLE `user_answers` (
  `id` int(11) NOT NULL,
  `attempt_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `selected_option_id` int(11) DEFAULT NULL,
  `essay_answer` text DEFAULT NULL,
  `marks_obtained` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `parent_id` (`parent_id`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `template_name` (`template_name`);

--
-- Indexes for table `exams`
--
ALTER TABLE `exams`
  ADD PRIMARY KEY (`exam_id`),
  ADD UNIQUE KEY `exam_name` (`exam_name`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `idx_name` (`exam_name`),
  ADD KEY `idx_publish_date` (`publish_date`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD PRIMARY KEY (`attempt_id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_notification` (`user_id`,`is_read`);

--
-- Indexes for table `options`
--
ALTER TABLE `options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_question_option` (`question_id`,`position`);

--
-- Indexes for table `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`question_id`),
  ADD KEY `idx_section_question` (`section_id`,`position`);

--
-- Indexes for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  ADD PRIMARY KEY (`mapping_id`),
  ADD UNIQUE KEY `unique_question_category` (`question_id`,`category_id`),
  ADD KEY `fk_question_category_question` (`question_id`),
  ADD KEY `fk_question_category_category` (`category_id`);

--
-- Indexes for table `sections`
--
ALTER TABLE `sections`
  ADD PRIMARY KEY (`section_id`),
  ADD KEY `idx_exam_section` (`exam_id`,`position`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_session` (`user_id`),
  ADD KEY `idx_expires` (`expires`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_deleted_at` (`deleted_at`);

--
-- Indexes for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exams`
--
ALTER TABLE `exams`
  MODIFY `exam_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=229;

--
-- AUTO_INCREMENT for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  MODIFY `attempt_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `options`
--
ALTER TABLE `options`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `questions`
--
ALTER TABLE `questions`
  MODIFY `question_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=171;

--
-- AUTO_INCREMENT for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  MODIFY `mapping_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `sections`
--
ALTER TABLE `sections`
  MODIFY `section_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=218;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `user_answers`
--
ALTER TABLE `user_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

--
-- Constraints for table `exams`
--
ALTER TABLE `exams`
  ADD CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL;

--
-- Constraints for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD CONSTRAINT `exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `options`
--
ALTER TABLE `options`
  ADD CONSTRAINT `options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE;

--
-- Constraints for table `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `questions_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `sections` (`section_id`) ON DELETE CASCADE;

--
-- Constraints for table `question_category_mappings`
--
ALTER TABLE `question_category_mappings`
  ADD CONSTRAINT `fk_question_category_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_question_category_question` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE;

--
-- Constraints for table `sections`
--
ALTER TABLE `sections`
  ADD CONSTRAINT `sections_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE;

--
-- Constraints for table `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD CONSTRAINT `user_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `exam_attempts` (`attempt_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `options` (`id`) ON DELETE SET NULL;
COMMIT;

-- --------------------------------------------------------

--
-- Table structure for table `jsvalues`
--

CREATE TABLE `jsvalues` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `route` varchar(255) DEFAULT NULL,
  `context` varchar(255) DEFAULT NULL,
  `variable_name` varchar(255) NOT NULL,
  `variable_type` varchar(50) DEFAULT NULL,
  `variable_value` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `route` (`route`),
  KEY `variable_name` (`variable_name`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Update logs table to have status column with VARCHAR(255)
--

ALTER TABLE `logs` MODIFY COLUMN `status` VARCHAR(255);

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
