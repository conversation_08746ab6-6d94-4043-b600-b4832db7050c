-- Create a demo Science Exam
INSERT INTO exams (exam_name, description, duration, instructions, passing_marks, status, difficulty)
VALUES (
    'Science Knowledge Test',
    'Test your knowledge in fundamental chemistry and physics concepts',
    60,
    'Read each question carefully and select the best answer. You have 60 minutes to complete the exam.',
    60.00,
    'published',
    'intermediate'
);

-- Get the exam ID
SET @exam_id = LAST_INSERT_ID();

-- Create Chemistry Section
INSERT INTO sections (exam_id, section_name, description, duration, position)
VALUES (
    @exam_id,
    'Chemistry',
    'Basic concepts in chemistry including atomic structure, periodic table, and chemical reactions',
    30,
    1
);

-- Get the chemistry section ID
SET @chemistry_section_id = LAST_INSERT_ID();

-- Chemistry Questions
-- Question 1: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @chemistry_section_id,
    'What is the chemical symbol for gold?',
    'Au',
    'multiple_choice',
    1.00,
    'Au is the chemical symbol for gold, derived from the Latin word "aurum".',
    1
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Au', 1),
(@question_id, 'Ag', 2),
(@question_id, 'Fe', 3),
(@question_id, 'Go', 4);

-- Question 2: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @chemistry_section_id,
    'Which of the following is NOT a noble gas?',
    'Nitrogen',
    'multiple_choice',
    1.00,
    'Noble gases include helium, neon, argon, krypton, xenon, and radon. Nitrogen is not a noble gas.',
    2
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Helium', 1),
(@question_id, 'Neon', 2),
(@question_id, 'Argon', 3),
(@question_id, 'Nitrogen', 4);

-- Question 3: True/False
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @chemistry_section_id,
    'The pH of a neutral solution is exactly 7.',
    'true',
    'true_false',
    1.00,
    'A neutral solution has a pH of 7. Solutions with pH < 7 are acidic, and those with pH > 7 are basic.',
    3
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'true', 1),
(@question_id, 'false', 2);

-- Question 4: Essay
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @chemistry_section_id,
    'Explain the difference between covalent and ionic bonding, and provide an example of each.',
    '',
    'essay',
    2.00,
    'Covalent bonding involves sharing of electrons (e.g., H2O), while ionic bonding involves electron transfer (e.g., NaCl).',
    4
);

-- Question 5: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @chemistry_section_id,
    'What is the main component of natural gas?',
    'Methane',
    'multiple_choice',
    1.00,
    'Natural gas primarily consists of methane (CH4), typically 70-90%.',
    5
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Propane', 1),
(@question_id, 'Methane', 2),
(@question_id, 'Butane', 3),
(@question_id, 'Ethane', 4);

-- Create Physics Section
INSERT INTO sections (exam_id, section_name, description, duration, position)
VALUES (
    @exam_id,
    'Physics',
    'Core physics concepts including mechanics, electricity, and light',
    30,
    2
);

-- Get the physics section ID
SET @physics_section_id = LAST_INSERT_ID();

-- Physics Questions
-- Question 1: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @physics_section_id,
    'What is the SI unit of force?',
    'Newton',
    'multiple_choice',
    1.00,
    'The SI unit of force is the Newton (N), defined as the force needed to accelerate 1 kg of mass at 1 m/s².',
    1
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Joule', 1),
(@question_id, 'Newton', 2),
(@question_id, 'Watt', 3),
(@question_id, 'Pascal', 4);

-- Question 2: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @physics_section_id,
    'Which of the following is an example of a vector quantity?',
    'Velocity',
    'multiple_choice',
    1.00,
    'Vector quantities have both magnitude and direction. Velocity is a vector because it specifies both speed and direction.',
    2
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Time', 1),
(@question_id, 'Temperature', 2),
(@question_id, 'Mass', 3),
(@question_id, 'Velocity', 4);

-- Question 3: True/False
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @physics_section_id,
    'Light travels faster than sound.',
    'true',
    'true_false',
    1.00,
    'Light travels at approximately 3 × 10^8 m/s, while sound travels at approximately 343 m/s in air at room temperature.',
    3
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'true', 1),
(@question_id, 'false', 2);

-- Question 4: Essay
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @physics_section_id,
    'Describe Newton\'s three laws of motion and provide an everyday example of each.',
    '',
    'essay',
    2.00,
    'Newton\'s laws: 1) Object at rest stays at rest unless acted upon by force, 2) F=ma, 3) For every action, there is equal and opposite reaction.',
    4
);

-- Question 5: Multiple Choice
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
VALUES (
    @physics_section_id,
    'What is the formula for calculating power?',
    'Work/Time',
    'multiple_choice',
    1.00,
    'Power is defined as the rate of doing work or energy transfer. The formula is P = W/t, where P is power, W is work, and t is time.',
    5
);

-- Get the question ID
SET @question_id = LAST_INSERT_ID();

-- Add options for this question
INSERT INTO options (question_id, option_text, position) VALUES
(@question_id, 'Force × Distance', 1),
(@question_id, 'Mass × Acceleration', 2),
(@question_id, 'Work/Time', 3),
(@question_id, 'Force × Time', 4); 