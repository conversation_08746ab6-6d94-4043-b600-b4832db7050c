-- First, modify users table to support multiple subjects
ALTER TABLE users
ADD COLUMN IF NOT EXISTS subjects TEXT DEFAULT NULL COMMENT 'Comma-separated list of subjects assigned to teacher';

-- Create table for instruction plans
CREATE TABLE IF NOT EXISTS instruction_plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  subject_id INT,
  class_id INT,
  trade_id INT,
  section_id INT,
  teacher_id INT,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
  FOREI<PERSON><PERSON> KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create table for instruction plan resources
CREATE TABLE IF NOT EXISTS instruction_plan_resources (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  resource_name VARCHAR(255) NOT NULL,
  resource_type VARCHAR(50) NOT NULL COMMENT 'file, link, text',
  resource_path VARCHAR(255) COMMENT 'File path or URL',
  resource_content TEXT COMMENT 'For text-based resources',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
);

-- Ensure classes, trades, and sections tables exist with proper structure
CREATE TABLE IF NOT EXISTS classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  description VARCHAR(255),
  is_active TINYINT(1) DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY (name)
);

CREATE TABLE IF NOT EXISTS trades (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description VARCHAR(255),
  is_active TINYINT(1) DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY (name)
);

CREATE TABLE IF NOT EXISTS sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  class_id INT NOT NULL,
  trade_id INT,
  name VARCHAR(10) NOT NULL,
  is_active TINYINT(1) DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
  UNIQUE KEY (class_id, trade_id, name)
);

-- Insert default values for classes
INSERT IGNORE INTO classes (name, description) VALUES
('9', 'Class 9'),
('10', 'Class 10'),
('11', 'Class 11'),
('12', 'Class 12');

-- Insert default values for trades
INSERT IGNORE INTO trades (name, description) VALUES
('General', 'General stream for classes 9 and 10'),
('Science', 'Science stream with Physics, Chemistry, Mathematics'),
('Medical', 'Medical stream with Biology, Chemistry, Physics'),
('Non-Medical', 'Non-Medical stream with Mathematics, Physics, Chemistry'),
('Commerce', 'Commerce stream with Business Studies, Accountancy, Economics'),
('Arts', 'Arts stream with History, Geography, Political Science, etc.');

-- Insert default subjects
CREATE TABLE IF NOT EXISTS subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20),
  is_active TINYINT(1) DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY (name)
);

-- Insert default subjects
INSERT IGNORE INTO subjects (name, code) VALUES
('Mathematics', 'MATH'),
('Physics', 'PHY'),
('Chemistry', 'CHEM'),
('Biology', 'BIO'),
('Computer Science', 'CS'),
('English', 'ENG'),
('Hindi', 'HIN'),
('Punjabi', 'PUN'),
('Social Science', 'SOC'),
('History', 'HIS'),
('Geography', 'GEO'),
('Political Science', 'POL'),
('Economics', 'ECO'),
('Business Studies', 'BUS'),
('Accountancy', 'ACC'); 