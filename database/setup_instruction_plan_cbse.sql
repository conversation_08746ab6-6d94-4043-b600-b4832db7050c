-- Setup instruction plan with CBSE syllabus topics
-- This script adds instruction plan data for classes 11 and 12

-- First, let's create variables for teacher IDs
SET @physics_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Physics%' AND role = 'teacher' LIMIT 1);
SET @chemistry_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Chemistry%' AND role = 'teacher' LIMIT 1);
SET @math_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Mathematics%' AND role = 'teacher' LIMIT 1);
SET @biology_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Biology%' AND role = 'teacher' LIMIT 1);
SET @cs_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Computer Science%' AND role = 'teacher' LIMIT 1);
SET @english_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%English%' AND role = 'teacher' LIMIT 1);
SET @punjabi_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Punjabi%' AND role = 'teacher' LIMIT 1);
SET @evs_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Environmental Studies%' AND role = 'teacher' LIMIT 1);
SET @business_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Business Studies%' AND role = 'teacher' LIMIT 1);
SET @accountancy_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Accountancy%' AND role = 'teacher' LIMIT 1);
SET @economics_teacher_id = (SELECT id FROM users WHERE subjects LIKE '%Economics%' AND role = 'teacher' LIMIT 1);

-- Clear existing data
TRUNCATE TABLE teacher_syllabus;
TRUNCATE TABLE syllabus_progress;

-- Physics syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@physics_teacher_id, 'Physics', 'Physical World and Measurement (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Kinematics (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Laws of Motion (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Work, Energy and Power (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Motion of System of Particles and Rigid Body (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Gravitation (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Properties of Bulk Matter (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Thermodynamics (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Behaviour of Perfect Gas and Kinetic Theory (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Oscillations and Waves (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Physics syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@physics_teacher_id, 'Physics', 'Electrostatics (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Current Electricity (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Magnetic Effects of Current and Magnetism (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Electromagnetic Induction and Alternating Currents (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Electromagnetic Waves (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Optics (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Dual Nature of Matter and Radiation (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Atoms and Nuclei (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Electronic Devices (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@physics_teacher_id, 'Physics', 'Communication Systems (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Chemistry syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@chemistry_teacher_id, 'Chemistry', 'Some Basic Concepts of Chemistry (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Structure of Atom (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Classification of Elements and Periodicity in Properties (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Chemical Bonding and Molecular Structure (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'States of Matter: Gases and Liquids (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Thermodynamics (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Equilibrium (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Redox Reactions (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Hydrogen (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 's-Block Elements (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'p-Block Elements (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Organic Chemistry: Basic Principles (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Hydrocarbons (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Environmental Chemistry (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Chemistry syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@chemistry_teacher_id, 'Chemistry', 'Solid State (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Solutions (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Electrochemistry (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Chemical Kinetics (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Surface Chemistry (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'd and f Block Elements (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Coordination Compounds (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Haloalkanes and Haloarenes (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Alcohols, Phenols and Ethers (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Aldehydes, Ketones and Carboxylic Acids (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Amines (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Biomolecules (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Polymers (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@chemistry_teacher_id, 'Chemistry', 'Chemistry in Everyday Life (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Mathematics syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@math_teacher_id, 'Mathematics', 'Sets (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Relations and Functions (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Trigonometric Functions (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Principle of Mathematical Induction (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Complex Numbers and Quadratic Equations (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Linear Inequalities (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Permutations and Combinations (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Binomial Theorem (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Sequences and Series (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Straight Lines (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Conic Sections (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Introduction to Three-dimensional Geometry (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Limits and Derivatives (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Mathematical Reasoning (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Statistics (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Probability (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Mathematics syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@math_teacher_id, 'Mathematics', 'Relations and Functions (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Inverse Trigonometric Functions (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Matrices (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Determinants (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Continuity and Differentiability (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Applications of Derivatives (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Integrals (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Applications of Integrals (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Differential Equations (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Vector Algebra (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Three Dimensional Geometry (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Linear Programming (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@math_teacher_id, 'Mathematics', 'Probability (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Computer Science syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@cs_teacher_id, 'Computer Science', 'Computer Fundamentals (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Programming Methodology (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Introduction to Python (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Python Fundamentals (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Flow of Control (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Functions (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Strings (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Lists (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Tuples (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Dictionaries (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'File Handling (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Data Visualization (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Computer Science syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@cs_teacher_id, 'Computer Science', 'Review of Python (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Object Oriented Programming (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Database Concepts (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Introduction to SQL (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Data Structures (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Stacks (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Queues (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Computer Networks (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Network Security Concepts (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Web Application Development (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@cs_teacher_id, 'Computer Science', 'Interface Python with SQL (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- English syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@english_teacher_id, 'English', 'Reading Comprehension (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Note Making and Summarizing (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Sub-titling (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Creative Writing Skills (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Essay Writing (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Letter Writing (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Hornbill (Prose) (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Hornbill (Poetry) (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Snapshots (Supplementary Reader) (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@english_teacher_id, 'English', 'Grammar and Usage (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- English syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@english_teacher_id, 'English', 'Reading Comprehension (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Note Making and Summarizing (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Advanced Writing Skills (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Business Letters (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Official Letters (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Report Writing (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Flamingo (Prose) (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Flamingo (Poetry) (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Vistas (Supplementary Reader) (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@english_teacher_id, 'English', 'Advanced Grammar (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Punjabi syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@punjabi_teacher_id, 'Punjabi', 'Prose and Poetry (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Grammar and Composition (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Punjabi Culture and Heritage (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Punjabi Literature (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Creative Writing (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Translation Skills (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Punjabi syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@punjabi_teacher_id, 'Punjabi', 'Advanced Prose and Poetry (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Advanced Grammar (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Punjabi Drama and Fiction (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Modern Punjabi Literature (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Advanced Creative Writing (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@punjabi_teacher_id, 'Punjabi', 'Advanced Translation (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Environmental Studies syllabus for Class 11 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@evs_teacher_id, 'Environmental Studies', 'Introduction to Environmental Studies (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Ecosystems (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Biodiversity and its Conservation (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Environmental Pollution (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Social Issues and the Environment (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Human Population and Environment (Class 11 Non-Medical)', 'pending', NULL, 'Class 11 Non-Medical');

-- Environmental Studies syllabus for Class 12 (CBSE)
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date, notes) VALUES
(@evs_teacher_id, 'Environmental Studies', 'Natural Resources (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Environmental Management (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Sustainable Development (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Climate Change (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Environmental Laws and Policies (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical'),
(@evs_teacher_id, 'Environmental Studies', 'Field Work and Project (Class 12 Non-Medical)', 'pending', NULL, 'Class 12 Non-Medical');

-- Update syllabus progress for each subject and teacher
INSERT INTO syllabus_progress (teacher_id, subject_name, total_topics, completed_topics) VALUES
(@physics_teacher_id, 'Physics', 20, 0),
(@chemistry_teacher_id, 'Chemistry', 28, 0),
(@math_teacher_id, 'Mathematics', 29, 0),
(@cs_teacher_id, 'Computer Science', 23, 0),
(@english_teacher_id, 'English', 20, 0),
(@punjabi_teacher_id, 'Punjabi', 12, 0),
(@evs_teacher_id, 'Environmental Studies', 12, 0);

-- Print completion message
SELECT 'Instruction plan with CBSE syllabus has been added successfully.' AS Message;
