-- Setup teacher lectures for testing
-- This script adds sample lecture data for teachers

-- Add missing columns to teacher_lectures table if they don't exist
ALTER TABLE `teacher_lectures` ADD COLUMN IF NOT EXISTS `grade` varchar(10) NOT NULL DEFAULT '11' AFTER `topic`;
ALTER TABLE `teacher_lectures` ADD COLUMN IF NOT EXISTS `streamCode` varchar(10) NOT NULL DEFAULT 'NM' AFTER `grade`;
ALTER TABLE `teacher_lectures` ADD COLUMN IF NOT EXISTS `stream` varchar(50) NOT NULL DEFAULT 'Non-Medical' AFTER `streamCode`;
ALTER TABLE `teacher_lectures` ADD COLUMN IF NOT EXISTS `location` varchar(100) NOT NULL DEFAULT 'Classroom 1' AFTER `stream`;
ALTER TABLE `teacher_lectures` ADD COLUMN IF NOT EXISTS `section_display` varchar(50) DEFAULT NULL AFTER `class_name`;

-- Get teacher IDs
SET @physics_teacher_id = (SELECT id FROM users WHERE role = 'teacher' AND subjects LIKE '%Physics%' LIMIT 1);
SET @chemistry_teacher_id = (SELECT id FROM users WHERE role = 'teacher' AND subjects LIKE '%Chemistry%' LIMIT 1);
SET @math_teacher_id = (SELECT id FROM users WHERE role = 'teacher' AND subjects LIKE '%Mathematics%' LIMIT 1);
SET @cs_teacher_id = (SELECT id FROM users WHERE role = 'teacher' AND subjects LIKE '%Computer Science%' LIMIT 1);
SET @english_teacher_id = (SELECT id FROM users WHERE role = 'teacher' AND subjects LIKE '%English%' LIMIT 1);

-- If we don't have enough teachers, use the first teacher for all subjects
SET @default_teacher_id = (SELECT id FROM users WHERE role = 'teacher' LIMIT 1);

-- Use default teacher if specific subject teachers are not found
SET @physics_teacher_id = IFNULL(@physics_teacher_id, @default_teacher_id);
SET @chemistry_teacher_id = IFNULL(@chemistry_teacher_id, @default_teacher_id);
SET @math_teacher_id = IFNULL(@math_teacher_id, @default_teacher_id);
SET @cs_teacher_id = IFNULL(@cs_teacher_id, @default_teacher_id);
SET @english_teacher_id = IFNULL(@english_teacher_id, @default_teacher_id);

-- Clear existing data
TRUNCATE TABLE teacher_lectures;

-- Insert sample lectures for the current week
-- Physics lectures for Class 11 Non-Medical
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '08:00:00', '08:40:00', 0, 'Physics', 'Kinematics', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '09:00:00', '09:40:00', 1, 'Physics', 'Newton\'s Laws', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '10:00:00', '10:40:00', 2, 'Physics', 'Work and Energy', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '11:00:00', '11:40:00', 3, 'Physics', 'Rotational Motion', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'Physics', 'Gravitation', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending');

-- Chemistry lectures for Class 11 Non-Medical
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '09:00:00', '09:40:00', 1, 'Chemistry', 'Atomic Structure', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '10:00:00', '10:40:00', 2, 'Chemistry', 'Chemical Bonding', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '11:00:00', '11:40:00', 3, 'Chemistry', 'States of Matter', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'Chemistry', 'Thermodynamics', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '13:00:00', '13:40:00', 5, 'Chemistry', 'Equilibrium', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending');

-- Mathematics lectures for Class 11 Non-Medical
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '10:00:00', '10:40:00', 2, 'Mathematics', 'Sets and Functions', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '11:00:00', '11:40:00', 3, 'Mathematics', 'Trigonometry', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'Mathematics', 'Coordinate Geometry', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Classroom 3', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '13:00:00', '13:40:00', 5, 'Mathematics', 'Calculus', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Classroom 4', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '14:00:00', '14:40:00', 6, 'Mathematics', 'Statistics', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Classroom 5', 'pending');

-- Computer Science lectures for Class 11 Non-Medical
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '11:00:00', '11:40:00', 3, 'Computer Science', 'Introduction to Programming', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'Computer Science', 'Data Types and Variables', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '13:00:00', '13:40:00', 5, 'Computer Science', 'Control Structures', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '14:00:00', '14:40:00', 6, 'Computer Science', 'Functions', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '15:00:00', '15:40:00', 7, 'Computer Science', 'Arrays', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending');

-- English lectures for Class 11 Non-Medical
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'English', 'Reading Comprehension', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '13:00:00', '13:40:00', 5, 'English', 'Grammar', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '14:00:00', '14:40:00', 6, 'English', 'Writing Skills', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Classroom 3', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '15:00:00', '15:40:00', 7, 'English', 'Literature', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Classroom 4', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '16:00:00', '16:40:00', 8, 'English', 'Speaking Skills', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Classroom 5', 'pending');

-- Mark some lectures as delivered
UPDATE teacher_lectures SET status = 'delivered' WHERE date < CURDATE();

-- Add some lectures for next week
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (8 - DAYOFWEEK(CURDATE())) DAY), '08:00:00', '08:40:00', 0, 'Physics', 'Waves', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (9 - DAYOFWEEK(CURDATE())) DAY), '09:00:00', '09:40:00', 1, 'Chemistry', 'Redox Reactions', '11 Non-Medical', 'B', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (10 - DAYOFWEEK(CURDATE())) DAY), '10:00:00', '10:40:00', 2, 'Mathematics', 'Probability', '11 Non-Medical', 'C', '11', 'NM', 'Non-Medical', 'Classroom 3', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (11 - DAYOFWEEK(CURDATE())) DAY), '11:00:00', '11:40:00', 3, 'Computer Science', 'Strings', '11 Non-Medical', 'D', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (12 - DAYOFWEEK(CURDATE())) DAY), '12:00:00', '12:40:00', 4, 'English', 'Essay Writing', '11 Non-Medical', 'E', '11', 'NM', 'Non-Medical', 'Classroom 5', 'pending');

-- Add some lectures for Class 12
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '13:00:00', '13:40:00', 5, 'Physics', 'Electrostatics', '12 Non-Medical', 'A', '12', 'NM', 'Non-Medical', 'Physics Lab 2', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '14:00:00', '14:40:00', 6, 'Chemistry', 'Organic Chemistry', '12 Non-Medical', 'B', '12', 'NM', 'Non-Medical', 'Chemistry Lab 2', 'pending'),
(@math_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '15:00:00', '15:40:00', 7, 'Mathematics', 'Matrices', '12 Non-Medical', 'C', '12', 'NM', 'Non-Medical', 'Classroom 6', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY), '16:00:00', '16:40:00', 8, 'Computer Science', 'Data Structures', '12 Non-Medical', 'D', '12', 'NM', 'Non-Medical', 'Computer Lab 2', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY), '08:00:00', '08:40:00', 0, 'English', 'Advanced Grammar', '12 Non-Medical', 'E', '12', 'NM', 'Non-Medical', 'Classroom 7', 'pending');

-- Add some lectures for Medical stream
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '14:00:00', '14:40:00', 6, 'Physics', 'Optics', '11 Medical', 'A', '11', 'M', 'Medical', 'Physics Lab 1', 'pending'),
(@chemistry_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '15:00:00', '15:40:00', 7, 'Chemistry', 'Biomolecules', '11 Medical', 'A', '11', 'M', 'Medical', 'Chemistry Lab 1', 'pending'),
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '16:00:00', '16:40:00', 8, 'Computer Science', 'Introduction to Databases', '11 Medical', 'A', '11', 'M', 'Medical', 'Computer Lab 1', 'pending');

-- Add some lectures for Commerce stream
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@cs_teacher_id, DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY), '15:00:00', '15:40:00', 7, 'Computer Science', 'Spreadsheet Basics', '11 Commerce', 'A', '11', 'C', 'Commerce', 'Computer Lab 2', 'pending'),
(@english_teacher_id, DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY), '16:00:00', '16:40:00', 8, 'English', 'Business Communication', '11 Commerce', 'A', '11', 'C', 'Commerce', 'Classroom 8', 'pending');

-- Add some rescheduled lectures
INSERT INTO teacher_lectures
(teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
(@physics_teacher_id, DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY), '16:00:00', '16:40:00', 8, 'Physics', 'Fluid Mechanics', '11 Non-Medical', 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'cancelled');

-- Print completion message
SELECT 'Teacher lectures sample data has been added successfully.' AS Message;
