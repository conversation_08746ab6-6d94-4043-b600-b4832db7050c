-- Setup teachers with appropriate subjects
-- This script adds teachers for different subjects according to the specified count

-- First, let's clear existing teachers (except admin users)
DELETE FROM users WHERE role = 'teacher';

-- Create English teachers (4)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('english_teacher1', 'english_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'English Teacher 1', 'English', 'English Teacher with expertise in language and literature', NOW()),
('english_teacher2', 'english_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'English Teacher 2', 'English', 'English Teacher with expertise in language and literature', NOW()),
('english_teacher3', 'english_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'English Teacher 3', 'English', 'English Teacher with expertise in language and literature', NOW()),
('english_teacher4', 'english_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'English Teacher 4', 'English', 'English Teacher with expertise in language and literature', NOW());

-- Create Punjabi teachers (4)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('punjabi_teacher1', 'punjabi_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Punjabi Teacher 1', 'Punjabi', 'Punjabi Teacher with expertise in language and literature', NOW()),
('punjabi_teacher2', 'punjabi_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Punjabi Teacher 2', 'Punjabi', 'Punjabi Teacher with expertise in language and literature', NOW()),
('punjabi_teacher3', 'punjabi_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Punjabi Teacher 3', 'Punjabi', 'Punjabi Teacher with expertise in language and literature', NOW()),
('punjabi_teacher4', 'punjabi_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Punjabi Teacher 4', 'Punjabi', 'Punjabi Teacher with expertise in language and literature', NOW());

-- Create Computer Science teachers (3)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('cs_teacher1', 'cs_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Computer Science Teacher 1', 'Computer Science', 'Computer Science Teacher with expertise in programming and algorithms', NOW()),
('cs_teacher2', 'cs_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Computer Science Teacher 2', 'Computer Science', 'Computer Science Teacher with expertise in programming and algorithms', NOW()),
('cs_teacher3', 'cs_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Computer Science Teacher 3', 'Computer Science', 'Computer Science Teacher with expertise in programming and algorithms', NOW());

-- Create Physics teachers (8)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('physics_teacher1', 'physics_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 1', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher2', 'physics_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 2', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher3', 'physics_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 3', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher4', 'physics_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 4', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher5', 'physics_teacher5', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 5', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher6', 'physics_teacher6', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 6', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher7', 'physics_teacher7', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 7', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW()),
('physics_teacher8', 'physics_teacher8', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Physics Teacher 8', 'Physics', 'Physics Teacher with expertise in mechanics and thermodynamics', NOW());

-- Create Chemistry teachers (8)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('chemistry_teacher1', 'chemistry_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 1', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher2', 'chemistry_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 2', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher3', 'chemistry_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 3', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher4', 'chemistry_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 4', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher5', 'chemistry_teacher5', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 5', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher6', 'chemistry_teacher6', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 6', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher7', 'chemistry_teacher7', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 7', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW()),
('chemistry_teacher8', 'chemistry_teacher8', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Chemistry Teacher 8', 'Chemistry', 'Chemistry Teacher with expertise in organic and inorganic chemistry', NOW());

-- Create Biology teachers (4)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('biology_teacher1', 'biology_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Biology Teacher 1', 'Biology', 'Biology Teacher with expertise in botany and zoology', NOW()),
('biology_teacher2', 'biology_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Biology Teacher 2', 'Biology', 'Biology Teacher with expertise in botany and zoology', NOW()),
('biology_teacher3', 'biology_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Biology Teacher 3', 'Biology', 'Biology Teacher with expertise in botany and zoology', NOW()),
('biology_teacher4', 'biology_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Biology Teacher 4', 'Biology', 'Biology Teacher with expertise in botany and zoology', NOW());

-- Create Mathematics teachers (4)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('math_teacher1', 'math_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Mathematics Teacher 1', 'Mathematics', 'Mathematics Teacher with expertise in algebra and calculus', NOW()),
('math_teacher2', 'math_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Mathematics Teacher 2', 'Mathematics', 'Mathematics Teacher with expertise in algebra and calculus', NOW()),
('math_teacher3', 'math_teacher3', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Mathematics Teacher 3', 'Mathematics', 'Mathematics Teacher with expertise in algebra and calculus', NOW()),
('math_teacher4', 'math_teacher4', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Mathematics Teacher 4', 'Mathematics', 'Mathematics Teacher with expertise in algebra and calculus', NOW());

-- Create Commerce teachers (6 total for Accounts, Business Studies, and Economics)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('accounts_teacher1', 'accounts_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Accounts Teacher 1', 'Accountancy', 'Accountancy Teacher with expertise in financial accounting', NOW()),
('accounts_teacher2', 'accounts_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Accounts Teacher 2', 'Accountancy', 'Accountancy Teacher with expertise in financial accounting', NOW()),
('business_teacher1', 'business_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Business Studies Teacher 1', 'Business Studies', 'Business Studies Teacher with expertise in management and marketing', NOW()),
('business_teacher2', 'business_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Business Studies Teacher 2', 'Business Studies', 'Business Studies Teacher with expertise in management and marketing', NOW()),
('economics_teacher1', 'economics_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Economics Teacher 1', 'Economics', 'Economics Teacher with expertise in microeconomics and macroeconomics', NOW()),
('economics_teacher2', 'economics_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'Economics Teacher 2', 'Economics', 'Economics Teacher with expertise in microeconomics and macroeconomics', NOW());

-- Create MOP (Methods of Psychology) teachers (2)
INSERT INTO users (name, username, email, password, role, is_active, full_name, subjects, bio, last_login) VALUES
('mop_teacher1', 'mop_teacher1', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'MOP Teacher 1', 'Methods of Psychology', 'Psychology Teacher with expertise in research methods and statistics', NOW()),
('mop_teacher2', 'mop_teacher2', '<EMAIL>', '$2a$10$JsRozZp8xUH5.2SEugXcre5TepY3XMh2mj/lmQJ.IXJVPt1xvf/fy', 'teacher', 1, 'MOP Teacher 2', 'Methods of Psychology', 'Psychology Teacher with expertise in research methods and statistics', NOW());

-- Print completion message
SELECT 'Teachers have been added successfully.' AS Message;
