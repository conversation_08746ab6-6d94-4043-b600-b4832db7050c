-- Setup timetable with CBSE syllabus lecture distribution
-- This script adds timetable data for classes 11 and 12 based on the specified distribution

-- First, let's clear existing timetable data
TRUNCATE TABLE teacher_lectures;

-- Get teacher IDs for each subject
-- English teachers (4)
SET @english_teacher1 = (SELECT id FROM users WHERE name = 'english_teacher1' LIMIT 1);
SET @english_teacher2 = (SELECT id FROM users WHERE name = 'english_teacher2' LIMIT 1);
SET @english_teacher3 = (SELECT id FROM users WHERE name = 'english_teacher3' LIMIT 1);
SET @english_teacher4 = (SELECT id FROM users WHERE name = 'english_teacher4' LIMIT 1);

-- Punjabi teachers (4)
SET @punjabi_teacher1 = (SELECT id FROM users WHERE name = 'punjabi_teacher1' LIMIT 1);
SET @punjabi_teacher2 = (SELECT id FROM users WHERE name = 'punjabi_teacher2' LIMIT 1);
SET @punjabi_teacher3 = (SELECT id FROM users WHERE name = 'punjabi_teacher3' LIMIT 1);
SET @punjabi_teacher4 = (SELECT id FROM users WHERE name = 'punjabi_teacher4' LIMIT 1);

-- Computer Science teachers (3)
SET @cs_teacher1 = (SELECT id FROM users WHERE name = 'cs_teacher1' LIMIT 1);
SET @cs_teacher2 = (SELECT id FROM users WHERE name = 'cs_teacher2' LIMIT 1);
SET @cs_teacher3 = (SELECT id FROM users WHERE name = 'cs_teacher3' LIMIT 1);

-- Physics teachers (8)
SET @physics_teacher1 = (SELECT id FROM users WHERE name = 'physics_teacher1' LIMIT 1);
SET @physics_teacher2 = (SELECT id FROM users WHERE name = 'physics_teacher2' LIMIT 1);
SET @physics_teacher3 = (SELECT id FROM users WHERE name = 'physics_teacher3' LIMIT 1);
SET @physics_teacher4 = (SELECT id FROM users WHERE name = 'physics_teacher4' LIMIT 1);
SET @physics_teacher5 = (SELECT id FROM users WHERE name = 'physics_teacher5' LIMIT 1);
SET @physics_teacher6 = (SELECT id FROM users WHERE name = 'physics_teacher6' LIMIT 1);
SET @physics_teacher7 = (SELECT id FROM users WHERE name = 'physics_teacher7' LIMIT 1);
SET @physics_teacher8 = (SELECT id FROM users WHERE name = 'physics_teacher8' LIMIT 1);

-- Chemistry teachers (8)
SET @chemistry_teacher1 = (SELECT id FROM users WHERE name = 'chemistry_teacher1' LIMIT 1);
SET @chemistry_teacher2 = (SELECT id FROM users WHERE name = 'chemistry_teacher2' LIMIT 1);
SET @chemistry_teacher3 = (SELECT id FROM users WHERE name = 'chemistry_teacher3' LIMIT 1);
SET @chemistry_teacher4 = (SELECT id FROM users WHERE name = 'chemistry_teacher4' LIMIT 1);
SET @chemistry_teacher5 = (SELECT id FROM users WHERE name = 'chemistry_teacher5' LIMIT 1);
SET @chemistry_teacher6 = (SELECT id FROM users WHERE name = 'chemistry_teacher6' LIMIT 1);
SET @chemistry_teacher7 = (SELECT id FROM users WHERE name = 'chemistry_teacher7' LIMIT 1);
SET @chemistry_teacher8 = (SELECT id FROM users WHERE name = 'chemistry_teacher8' LIMIT 1);

-- Biology teachers (4)
SET @biology_teacher1 = (SELECT id FROM users WHERE name = 'biology_teacher1' LIMIT 1);
SET @biology_teacher2 = (SELECT id FROM users WHERE name = 'biology_teacher2' LIMIT 1);
SET @biology_teacher3 = (SELECT id FROM users WHERE name = 'biology_teacher3' LIMIT 1);
SET @biology_teacher4 = (SELECT id FROM users WHERE name = 'biology_teacher4' LIMIT 1);

-- Mathematics teachers (4)
SET @math_teacher1 = (SELECT id FROM users WHERE name = 'math_teacher1' LIMIT 1);
SET @math_teacher2 = (SELECT id FROM users WHERE name = 'math_teacher2' LIMIT 1);
SET @math_teacher3 = (SELECT id FROM users WHERE name = 'math_teacher3' LIMIT 1);
SET @math_teacher4 = (SELECT id FROM users WHERE name = 'math_teacher4' LIMIT 1);

-- Commerce teachers (6)
SET @accounts_teacher1 = (SELECT id FROM users WHERE name = 'accounts_teacher1' LIMIT 1);
SET @accounts_teacher2 = (SELECT id FROM users WHERE name = 'accounts_teacher2' LIMIT 1);
SET @business_teacher1 = (SELECT id FROM users WHERE name = 'business_teacher1' LIMIT 1);
SET @business_teacher2 = (SELECT id FROM users WHERE name = 'business_teacher2' LIMIT 1);
SET @economics_teacher1 = (SELECT id FROM users WHERE name = 'economics_teacher1' LIMIT 1);
SET @economics_teacher2 = (SELECT id FROM users WHERE name = 'economics_teacher2' LIMIT 1);

-- MOP teachers (2)
SET @mop_teacher1 = (SELECT id FROM users WHERE name = 'mop_teacher1' LIMIT 1);
SET @mop_teacher2 = (SELECT id FROM users WHERE name = 'mop_teacher2' LIMIT 1);

-- Define class sections
-- Class 11 Non-Medical (6 sections)
SET @class_11_nm_1 = '11 Non-Medical A';
SET @class_11_nm_2 = '11 Non-Medical B';
SET @class_11_nm_3 = '11 Non-Medical C';
SET @class_11_nm_4 = '11 Non-Medical D';
SET @class_11_nm_5 = '11 Non-Medical E';
SET @class_11_nm_6 = '11 Non-Medical F';

-- Class 11 Medical (2 sections)
SET @class_11_m_1 = '11 Medical A';
SET @class_11_m_2 = '11 Medical B';

-- Class 11 Commerce (2 sections)
SET @class_11_c_1 = '11 Commerce A';
SET @class_11_c_2 = '11 Commerce B';

-- Class 12 Non-Medical (6 sections)
SET @class_12_nm_1 = '12 Non-Medical A';
SET @class_12_nm_2 = '12 Non-Medical B';
SET @class_12_nm_3 = '12 Non-Medical C';
SET @class_12_nm_4 = '12 Non-Medical D';
SET @class_12_nm_5 = '12 Non-Medical E';
SET @class_12_nm_6 = '12 Non-Medical F';

-- Class 12 Medical (2 sections)
SET @class_12_m_1 = '12 Medical A';
SET @class_12_m_2 = '12 Medical B';

-- Class 12 Commerce (2 sections)
SET @class_12_c_1 = '12 Commerce A';
SET @class_12_c_2 = '12 Commerce B';

-- Define time slots
SET @slot_1 = 0;
SET @slot_2 = 1;
SET @slot_3 = 2;
SET @slot_4 = 3;
SET @slot_5 = 4;
SET @slot_6 = 5;
SET @slot_7 = 6;
SET @slot_8 = 7;
SET @slot_9 = 8;

-- Define time ranges
SET @time_8_00 = '08:00:00';
SET @time_8_40 = '08:40:00';
SET @time_8_45 = '08:45:00';
SET @time_9_25 = '09:25:00';
SET @time_9_30 = '09:30:00';
SET @time_10_10 = '10:10:00';
SET @time_10_15 = '10:15:00';
SET @time_10_55 = '10:55:00';
SET @time_11_00 = '11:00:00';
SET @time_11_40 = '11:40:00';
SET @time_11_45 = '11:45:00';
SET @time_12_25 = '12:25:00';
SET @time_12_30 = '12:30:00';
SET @time_13_10 = '13:10:00';
SET @time_13_15 = '13:15:00';
SET @time_13_55 = '13:55:00';
SET @time_14_00 = '14:00:00';
SET @time_14_40 = '14:40:00';
SET @time_14_45 = '14:45:00';
SET @time_15_25 = '15:25:00';

-- Define days of the week (Monday to Saturday)
SET @monday = DATE_ADD(CURDATE(), INTERVAL (1 - DAYOFWEEK(CURDATE())) DAY);
SET @tuesday = DATE_ADD(CURDATE(), INTERVAL (2 - DAYOFWEEK(CURDATE())) DAY);
SET @wednesday = DATE_ADD(CURDATE(), INTERVAL (3 - DAYOFWEEK(CURDATE())) DAY);
SET @thursday = DATE_ADD(CURDATE(), INTERVAL (4 - DAYOFWEEK(CURDATE())) DAY);
SET @friday = DATE_ADD(CURDATE(), INTERVAL (5 - DAYOFWEEK(CURDATE())) DAY);
SET @saturday = DATE_ADD(CURDATE(), INTERVAL (6 - DAYOFWEEK(CURDATE())) DAY);

-- Now let's populate the timetable for Class 11 Non-Medical Section A
-- Monday
INSERT INTO teacher_lectures (teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
-- Monday - Class 11 Non-Medical A
(@physics_teacher1, @monday, @time_8_00, @time_8_40, @slot_1, 'Physics', 'Kinematics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@math_teacher1, @monday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Sets and Relations', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @monday, @time_9_30, @time_10_10, @slot_3, 'Chemistry', 'Atomic Structure', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@english_teacher1, @monday, @time_10_15, @time_10_55, @slot_4, 'English', 'Reading Comprehension', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @monday, @time_11_00, @time_11_40, @slot_5, 'Mathematics', 'Functions', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @monday, @time_11_45, @time_12_25, @slot_6, 'Physics', 'Laws of Motion', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@punjabi_teacher1, @monday, @time_12_30, @time_13_10, @slot_7, 'Punjabi', 'Grammar and Composition', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@cs_teacher1, @monday, @time_13_15, @time_13_55, @slot_8, 'Computer Science', 'Introduction to Programming', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@math_teacher1, @monday, @time_14_00, @time_14_40, @slot_9, 'Mathematics', 'Trigonometric Functions', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),

-- Tuesday - Class 11 Non-Medical A
(@chemistry_teacher1, @tuesday, @time_8_00, @time_8_40, @slot_1, 'Chemistry', 'Chemical Bonding', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@math_teacher1, @tuesday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Complex Numbers', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @tuesday, @time_9_30, @time_10_10, @slot_3, 'Physics', 'Work, Energy and Power', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@punjabi_teacher1, @tuesday, @time_10_15, @time_10_55, @slot_4, 'Punjabi', 'Prose and Poetry', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @tuesday, @time_11_00, @time_11_40, @slot_5, 'Mathematics', 'Quadratic Equations', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @tuesday, @time_11_45, @time_12_25, @slot_6, 'Chemistry', 'States of Matter', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@english_teacher1, @tuesday, @time_12_30, @time_13_10, @slot_7, 'English', 'Writing Skills', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @tuesday, @time_13_15, @time_13_55, @slot_8, 'Physics', 'Practical - Measurements', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@math_teacher1, @tuesday, @time_14_00, @time_14_40, @slot_9, 'Mathematics', 'Permutations and Combinations', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),

-- Wednesday - Class 11 Non-Medical A
(@english_teacher1, @wednesday, @time_8_00, @time_8_40, @slot_1, 'English', 'Grammar', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @wednesday, @time_8_45, @time_9_25, @slot_2, 'Physics', 'Gravitation', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@math_teacher1, @wednesday, @time_9_30, @time_10_10, @slot_3, 'Mathematics', 'Binomial Theorem', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @wednesday, @time_10_15, @time_10_55, @slot_4, 'Chemistry', 'Thermodynamics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@punjabi_teacher1, @wednesday, @time_11_00, @time_11_40, @slot_5, 'Punjabi', 'Creative Writing', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @wednesday, @time_11_45, @time_12_25, @slot_6, 'Mathematics', 'Sequences and Series', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @wednesday, @time_12_30, @time_13_10, @slot_7, 'Chemistry', 'Practical - Chemical Analysis', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@cs_teacher1, @wednesday, @time_13_15, @time_13_55, @slot_8, 'Computer Science', 'Python Fundamentals', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),
(@math_teacher1, @wednesday, @time_14_00, @time_14_40, @slot_9, 'Mathematics', 'Straight Lines', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),

-- Thursday - Class 11 Non-Medical A
(@physics_teacher1, @thursday, @time_8_00, @time_8_40, @slot_1, 'Physics', 'Properties of Bulk Matter', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@math_teacher1, @thursday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Conic Sections', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @thursday, @time_9_30, @time_10_10, @slot_3, 'Chemistry', 'Equilibrium', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@english_teacher1, @thursday, @time_10_15, @time_10_55, @slot_4, 'English', 'Literature', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @thursday, @time_11_00, @time_11_40, @slot_5, 'Mathematics', 'Introduction to 3D Geometry', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @thursday, @time_11_45, @time_12_25, @slot_6, 'Physics', 'Thermodynamics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@punjabi_teacher1, @thursday, @time_12_30, @time_13_10, @slot_7, 'Punjabi', 'Punjabi Literature', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @thursday, @time_13_15, @time_13_55, @slot_8, 'Chemistry', 'Redox Reactions', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@math_teacher1, @thursday, @time_14_00, @time_14_40, @slot_9, 'Mathematics', 'Limits and Derivatives', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),

-- Friday - Class 11 Non-Medical A
(@chemistry_teacher1, @friday, @time_8_00, @time_8_40, @slot_1, 'Chemistry', 'Hydrogen', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@math_teacher1, @friday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Mathematical Reasoning', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @friday, @time_9_30, @time_10_10, @slot_3, 'Physics', 'Oscillations and Waves', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@punjabi_teacher1, @friday, @time_10_15, @time_10_55, @slot_4, 'Punjabi', 'Translation Skills', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @friday, @time_11_00, @time_11_40, @slot_5, 'Mathematics', 'Statistics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @friday, @time_11_45, @time_12_25, @slot_6, 'Chemistry', 's-Block Elements', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@english_teacher1, @friday, @time_12_30, @time_13_10, @slot_7, 'English', 'Speaking Skills', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @friday, @time_13_15, @time_13_55, @slot_8, 'Physics', 'Practical - Mechanics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@cs_teacher1, @friday, @time_14_00, @time_14_40, @slot_9, 'Computer Science', 'Practical - Python Programming', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending'),

-- Saturday - Class 11 Non-Medical A
(@english_teacher1, @saturday, @time_8_00, @time_8_40, @slot_1, 'English', 'Essay Writing', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@math_teacher1, @saturday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Probability', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @saturday, @time_9_30, @time_10_10, @slot_3, 'Chemistry', 'p-Block Elements', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@punjabi_teacher1, @saturday, @time_10_15, @time_10_55, @slot_4, 'Punjabi', 'Punjabi Culture and Heritage', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@physics_teacher1, @saturday, @time_11_00, @time_11_40, @slot_5, 'Physics', 'Kinetic Theory of Gases', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Physics Lab 1', 'pending'),
(@english_teacher1, @saturday, @time_11_45, @time_12_25, @slot_6, 'English', 'Note Making and Summarizing', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@chemistry_teacher1, @saturday, @time_12_30, @time_13_10, @slot_7, 'Chemistry', 'Practical - Volumetric Analysis', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Chemistry Lab 1', 'pending'),
(@math_teacher1, @saturday, @time_13_15, @time_13_55, @slot_8, 'Mathematics', 'Practical - Statistics', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Classroom 1', 'pending'),
(@cs_teacher1, @saturday, @time_14_00, @time_14_40, @slot_9, 'Computer Science', 'Flow of Control', @class_11_nm_1, 'A', '11', 'NM', 'Non-Medical', 'Computer Lab 1', 'pending');

-- Now let's populate the timetable for Class 12 Non-Medical Section A
-- Monday
INSERT INTO teacher_lectures (teacher_id, date, start_time, end_time, slot_index, subject_name, topic, class_name, section_display, grade, streamCode, stream, location, status)
VALUES
-- Monday - Class 12 Non-Medical A
(@physics_teacher2, @monday, @time_8_00, @time_8_40, @slot_1, 'Physics', 'Electrostatics', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Physics Lab 2', 'pending'),
(@math_teacher2, @monday, @time_8_45, @time_9_25, @slot_2, 'Mathematics', 'Relations and Functions', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@chemistry_teacher2, @monday, @time_9_30, @time_10_10, @slot_3, 'Chemistry', 'Solid State', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Chemistry Lab 2', 'pending'),
(@english_teacher2, @monday, @time_10_15, @time_10_55, @slot_4, 'English', 'Reading Comprehension', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@math_teacher2, @monday, @time_11_00, @time_11_40, @slot_5, 'Mathematics', 'Inverse Trigonometric Functions', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@physics_teacher2, @monday, @time_11_45, @time_12_25, @slot_6, 'Physics', 'Current Electricity', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Physics Lab 2', 'pending'),
(@punjabi_teacher2, @monday, @time_12_30, @time_13_10, @slot_7, 'Punjabi', 'Advanced Grammar', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Classroom 2', 'pending'),
(@cs_teacher2, @monday, @time_13_15, @time_13_55, @slot_8, 'Computer Science', 'Object Oriented Programming', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Computer Lab 2', 'pending'),
(@math_teacher2, @monday, @time_14_00, @time_14_40, @slot_9, 'Mathematics', 'Matrices', @class_12_nm_1, 'A', '12', 'NM', 'Non-Medical', 'Classroom 2', 'pending');

-- Add more timetable entries for other classes and sections as needed

-- Print completion message
SELECT 'Timetable with CBSE syllabus has been added successfully.' AS Message;
