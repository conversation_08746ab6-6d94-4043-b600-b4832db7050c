-- Current teacher fields in the users table
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100), -- sometimes referred to as full_name
  full_name VA<PERSON>HAR(100), -- in some queries
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
  profile_image VARCHAR(255),
  subjects TEXT, -- comma-separated list of subjects
  bio TEXT,
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE
);

-- Extended teacher fields in the staff table (from create_staff_table.sql)
-- This table contains comprehensive teacher/staff information including:

-- PERSONAL INFORMATION:
-- - user_id (links to users table)
-- - employee_id (unique staff identifier)
-- - designation (Teacher, Principal, etc.)
-- - department (Academic, Administration, etc.)
-- - joining_date
-- - employment_type (permanent, temporary, contract, part_time)

-- CONTACT INFORMATION:
-- - phone, alternate_phone, emergency_contact
-- - address, city, state, pincode

-- EDUCATIONAL QUALIFICATIONS (Timeline Data):
-- - class_10_board, class_10_year, class_10_percentage, class_10_school
-- - class_12_board, class_12_year, class_12_percentage, class_12_school, class_12_stream
-- - graduation_degree, graduation_university, graduation_year, graduation_percentage, graduation_specialization
-- - post_graduation_degree, post_graduation_university, post_graduation_year, post_graduation_percentage, post_graduation_specialization
-- - phd_subject, phd_university, phd_year, phd_thesis_title
-- - other_qualifications, professional_certifications

-- EXPERIENCE DETAILS (Timeline Data):
-- - total_experience_years, teaching_experience_years, administrative_experience_years
-- - previous_organizations, current_salary
-- - subjects_taught, classes_handled

-- ACHIEVEMENTS AND RECOGNITION:
-- - awards_received, publications, research_papers
-- - conferences_attended, training_programs
-- - special_skills, languages_known

-- ADMINISTRATIVE DETAILS:
-- - office_location, reporting_manager_id
-- - probation_period_months, confirmation_date
-- - last_promotion_date, performance_rating

-- DOCUMENT REFERENCES:
-- - resume_file, photo_file, id_proof_file, address_proof_file
-- - qualification_certificates

-- STATUS AND METADATA:
-- - is_active, is_on_leave, leave_start_date, leave_end_date
-- - notes, created_at, updated_at

-- SAMPLE QUERY TO GET ALL TEACHER FIELDS:
/*
SELECT
  u.id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
  u.subjects, u.bio, u.date_of_birth, u.gender, u.created_at, u.last_login, u.is_active,
  s.employee_id, s.designation, s.department, s.joining_date, s.employment_type,
  s.phone, s.alternate_phone, s.emergency_contact, s.address, s.city, s.state, s.pincode,
  s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
  s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
  s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
  s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
  s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
  s.other_qualifications, s.professional_certifications,
  s.total_experience_years, s.teaching_experience_years, s.administrative_experience_years,
  s.previous_organizations, s.current_salary, s.subjects_taught, s.classes_handled,
  s.awards_received, s.publications, s.research_papers, s.conferences_attended, s.training_programs,
  s.special_skills, s.languages_known,
  s.office_location, s.reporting_manager_id, s.probation_period_months, s.confirmation_date,
  s.last_promotion_date, s.performance_rating,
  s.resume_file, s.photo_file, s.id_proof_file, s.address_proof_file, s.qualification_certificates,
  s.is_on_leave, s.leave_start_date, s.leave_end_date, s.notes
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.role = 'teacher' AND u.is_active = 1;
*/