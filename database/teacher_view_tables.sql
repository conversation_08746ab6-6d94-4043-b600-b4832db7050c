-- <PERSON><PERSON><PERSON> to create missing tables for teacher view functionality
-- Based on the database tables specified in the developer guide

-- Add missing columns to subjects table
ALTER TABLE `subjects` 
ADD COLUMN IF NOT EXISTS `max_theory_lectures` INT DEFAULT 0 AFTER `description`,
ADD COLUMN IF NOT EXISTS `max_practical_lectures` INT DEFAULT 0 AFTER `max_theory_lectures`,
ADD COLUMN IF NOT EXISTS `total_lectures_per_week` INT DEFAULT 0 AFTER `max_practical_lectures`;

-- Add missing columns to classes table
ALTER TABLE `classes` 
ADD COLUMN IF NOT EXISTS `grade` VARCHAR(10) AFTER `description`,
ADD COLUMN IF NOT EXISTS `trade` VARCHAR(50) AFTER `grade`,
ADD COLUMN IF NOT EXISTS `section` VARCHAR(10) AFTER `trade`,
ADD COLUMN IF NOT EXISTS `academic_year` VARCHAR(20) AFTER `section`,
ADD COLUMN IF NOT EXISTS `board` VARCHAR(50) DEFAULT 'CBSE' AFTER `academic_year`,
ADD COLUMN IF NOT EXISTS `max_capacity` INT DEFAULT 40 AFTER `board`;

-- Create TeacherSubjectEligibility table if it doesn't exist
CREATE TABLE IF NOT EXISTS `teacher_subject_eligibility` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `teacher_id` INT NOT NULL,
  `subject_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`subject_id`) REFERENCES `subjects`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_teacher_subject` (`teacher_id`, `subject_id`)
);

-- Create SubjectClassAssignment table if it doesn't exist
CREATE TABLE IF NOT EXISTS `subject_class_assignment` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `subject_id` INT NOT NULL,
  `class_id` INT NOT NULL,
  `num_theory_lectures` INT DEFAULT 0,
  `num_practical_lectures` INT DEFAULT 0,
  `class_subject_teacher_ratio` VARCHAR(50) DEFAULT '1:1',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (`subject_id`) REFERENCES `subjects`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`class_id`) REFERENCES `classes`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_subject_class` (`subject_id`, `class_id`)
);

-- Create LectureSchedule table if it doesn't exist
CREATE TABLE IF NOT EXISTS `lecture_schedule` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `assignment_id` INT NOT NULL,
  `teacher_id` INT NOT NULL,
  `day_of_week` VARCHAR(10) NOT NULL,
  `start_time` TIME NOT NULL,
  `end_time` TIME NOT NULL,
  `classroom` VARCHAR(50) DEFAULT 'Classroom 1',
  `semester` VARCHAR(20) DEFAULT 'First',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (`assignment_id`) REFERENCES `subject_class_assignment`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`teacher_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);

-- Create DailyInstructionPlan table if it doesn't exist
CREATE TABLE IF NOT EXISTS `daily_instruction_plan` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `schedule_id` INT NOT NULL,
  `date` DATE NOT NULL,
  `topic` VARCHAR(255) NOT NULL,
  `objectives` TEXT,
  `material_support` TEXT,
  `activities` TEXT,
  `homework` TEXT,
  `notes` TEXT,
  `topic_completion_percentage` DECIMAL(5,2) DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (`schedule_id`) REFERENCES `lecture_schedule`(`id`) ON DELETE CASCADE
);

-- Create HolidayCalendar table if it doesn't exist
CREATE TABLE IF NOT EXISTS `holiday_calendar` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `holiday_date` DATE NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `holiday_type` VARCHAR(50) DEFAULT 'Public Holiday',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  UNIQUE KEY `unique_holiday_date` (`holiday_date`)
);

-- Create a view to show teacher-subject-class assignments
CREATE OR REPLACE VIEW `teacher_subject_class_view` AS
SELECT 
  tse.id AS eligibility_id,
  tse.teacher_id,
  u.name AS teacher_name,
  u.full_name AS teacher_full_name,
  tse.subject_id,
  s.name AS subject_name,
  s.code AS subject_code,
  sca.id AS assignment_id,
  sca.class_id,
  c.name AS class_name,
  c.grade,
  c.trade,
  c.section,
  sca.num_theory_lectures,
  sca.num_practical_lectures,
  (sca.num_theory_lectures + sca.num_practical_lectures) AS total_lectures,
  ls.id AS schedule_id,
  ls.day_of_week,
  ls.start_time,
  ls.end_time,
  ls.classroom
FROM 
  teacher_subject_eligibility tse
JOIN 
  users u ON tse.teacher_id = u.id
JOIN 
  subjects s ON tse.subject_id = s.id
LEFT JOIN 
  subject_class_assignment sca ON tse.subject_id = sca.subject_id
LEFT JOIN 
  classes c ON sca.class_id = c.id
LEFT JOIN 
  lecture_schedule ls ON sca.id = ls.assignment_id AND tse.teacher_id = ls.teacher_id
WHERE 
  tse.is_active = TRUE
  AND (sca.is_active IS NULL OR sca.is_active = TRUE)
  AND (ls.is_active IS NULL OR ls.is_active = TRUE);

-- Create a view to show teacher weekly lecture count
CREATE OR REPLACE VIEW `teacher_weekly_lectures` AS
SELECT 
  u.id AS teacher_id,
  u.name AS teacher_name,
  u.full_name AS teacher_full_name,
  s.id AS subject_id,
  s.name AS subject_name,
  COUNT(ls.id) AS weekly_lectures
FROM 
  users u
JOIN 
  teacher_subject_eligibility tse ON u.id = tse.teacher_id
JOIN 
  subjects s ON tse.subject_id = s.id
LEFT JOIN 
  subject_class_assignment sca ON tse.subject_id = sca.subject_id
LEFT JOIN 
  lecture_schedule ls ON sca.id = ls.assignment_id AND tse.teacher_id = ls.teacher_id
WHERE 
  u.role = 'teacher'
  AND tse.is_active = TRUE
  AND (sca.is_active IS NULL OR sca.is_active = TRUE)
  AND (ls.is_active IS NULL OR ls.is_active = TRUE)
GROUP BY 
  u.id, s.id;

-- Create a view to show class weekly lecture count
CREATE OR REPLACE VIEW `class_weekly_lectures` AS
SELECT 
  c.id AS class_id,
  c.name AS class_name,
  c.grade,
  c.trade,
  c.section,
  s.id AS subject_id,
  s.name AS subject_name,
  COUNT(ls.id) AS weekly_lectures
FROM 
  classes c
JOIN 
  subject_class_assignment sca ON c.id = sca.class_id
JOIN 
  subjects s ON sca.subject_id = s.id
LEFT JOIN 
  lecture_schedule ls ON sca.id = ls.assignment_id
WHERE 
  sca.is_active = TRUE
  AND (ls.is_active IS NULL OR ls.is_active = TRUE)
GROUP BY 
  c.id, s.id;

-- Insert sample data for subjects if table is empty
INSERT INTO subjects (name, code, description, max_theory_lectures, max_practical_lectures, total_lectures_per_week)
SELECT * FROM (
  SELECT 'Physics', 'PHY', 'Physics for classes 11-12', 6, 3, 9 UNION
  SELECT 'Chemistry', 'CHE', 'Chemistry for classes 11-12', 6, 3, 9 UNION
  SELECT 'Mathematics', 'MAT', 'Mathematics for classes 11-12', 8, 0, 8 UNION
  SELECT 'Biology', 'BIO', 'Biology for classes 11-12', 5, 3, 8 UNION
  SELECT 'Computer Science', 'CS', 'Computer Science for classes 11-12', 4, 4, 8 UNION
  SELECT 'English', 'ENG', 'English for classes 11-12', 6, 0, 6 UNION
  SELECT 'Physical Education', 'PE', 'Physical Education for classes 11-12', 2, 2, 4
) AS tmp
WHERE NOT EXISTS (
  SELECT name FROM subjects WHERE name IN ('Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science', 'English', 'Physical Education')
) LIMIT 7;

-- Insert sample data for classes if table is empty
INSERT INTO classes (name, description, grade, trade, section, academic_year, board, max_capacity)
SELECT * FROM (
  SELECT '11-NM-A', '11th Non-Medical Section A', '11', 'Non-Medical', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '11-NM-B', '11th Non-Medical Section B', '11', 'Non-Medical', 'B', '2023-2024', 'CBSE', 40 UNION
  SELECT '11-M-A', '11th Medical Section A', '11', 'Medical', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '11-M-B', '11th Medical Section B', '11', 'Medical', 'B', '2023-2024', 'CBSE', 40 UNION
  SELECT '11-C-A', '11th Commerce Section A', '11', 'Commerce', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '11-C-B', '11th Commerce Section B', '11', 'Commerce', 'B', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-NM-A', '12th Non-Medical Section A', '12', 'Non-Medical', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-NM-B', '12th Non-Medical Section B', '12', 'Non-Medical', 'B', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-M-A', '12th Medical Section A', '12', 'Medical', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-M-B', '12th Medical Section B', '12', 'Medical', 'B', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-C-A', '12th Commerce Section A', '12', 'Commerce', 'A', '2023-2024', 'CBSE', 40 UNION
  SELECT '12-C-B', '12th Commerce Section B', '12', 'Commerce', 'B', '2023-2024', 'CBSE', 40
) AS tmp
WHERE NOT EXISTS (
  SELECT name FROM classes WHERE name IN ('11-NM-A', '11-NM-B', '11-M-A', '11-M-B', '11-C-A', '11-C-B', '12-NM-A', '12-NM-B', '12-M-A', '12-M-B', '12-C-A', '12-C-B')
) LIMIT 12;

-- Create a procedure to initialize teacher subject eligibility
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS initialize_teacher_subject_eligibility()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE teacher_id INT;
  DECLARE subject_id INT;
  DECLARE subject_name VARCHAR(100);
  DECLARE teacher_subjects VARCHAR(255);
  
  -- Cursor for teachers
  DECLARE teacher_cursor CURSOR FOR 
    SELECT id, subjects FROM users WHERE role = 'teacher' AND subjects IS NOT NULL;
  
  -- Continue handler
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
  
  -- Open cursor
  OPEN teacher_cursor;
  
  -- Start reading
  read_loop: LOOP
    FETCH teacher_cursor INTO teacher_id, teacher_subjects;
    
    IF done THEN
      LEAVE read_loop;
    END IF;
    
    -- For each subject in the teacher's subjects
    SET @subjects = teacher_subjects;
    
    -- Split subjects by comma and process each
    WHILE LOCATE(',', @subjects) > 0 DO
      SET @subject = TRIM(SUBSTRING_INDEX(@subjects, ',', 1));
      SET @subjects = SUBSTRING(@subjects, LOCATE(',', @subjects) + 1);
      
      -- Find subject ID
      SELECT id INTO subject_id FROM subjects WHERE name = @subject LIMIT 1;
      
      -- If subject exists, create eligibility record
      IF subject_id IS NOT NULL THEN
        INSERT IGNORE INTO teacher_subject_eligibility (teacher_id, subject_id)
        VALUES (teacher_id, subject_id);
      END IF;
    END WHILE;
    
    -- Process the last subject
    SET @subject = TRIM(@subjects);
    SELECT id INTO subject_id FROM subjects WHERE name = @subject LIMIT 1;
    
    IF subject_id IS NOT NULL THEN
      INSERT IGNORE INTO teacher_subject_eligibility (teacher_id, subject_id)
      VALUES (teacher_id, subject_id);
    END IF;
  END LOOP;
  
  -- Close cursor
  CLOSE teacher_cursor;
END //
DELIMITER ;

-- Call the procedure to initialize teacher subject eligibility
CALL initialize_teacher_subject_eligibility();

-- Create a procedure to initialize subject class assignments
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS initialize_subject_class_assignments()
BEGIN
  -- Non-Medical classes (Physics, Chemistry, Mathematics, English, Physical Education)
  INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures)
  SELECT s.id, c.id, 
    CASE 
      WHEN s.name = 'Physics' THEN 6
      WHEN s.name = 'Chemistry' THEN 6
      WHEN s.name = 'Mathematics' THEN 8
      WHEN s.name = 'English' THEN 6
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END,
    CASE 
      WHEN s.name = 'Physics' THEN 3
      WHEN s.name = 'Chemistry' THEN 3
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END
  FROM subjects s
  CROSS JOIN classes c
  WHERE s.name IN ('Physics', 'Chemistry', 'Mathematics', 'English', 'Physical Education')
  AND c.trade = 'Non-Medical';
  
  -- Medical classes (Physics, Chemistry, Biology, English, Physical Education)
  INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures)
  SELECT s.id, c.id, 
    CASE 
      WHEN s.name = 'Physics' THEN 6
      WHEN s.name = 'Chemistry' THEN 6
      WHEN s.name = 'Biology' THEN 5
      WHEN s.name = 'English' THEN 6
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END,
    CASE 
      WHEN s.name = 'Physics' THEN 3
      WHEN s.name = 'Chemistry' THEN 3
      WHEN s.name = 'Biology' THEN 3
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END
  FROM subjects s
  CROSS JOIN classes c
  WHERE s.name IN ('Physics', 'Chemistry', 'Biology', 'English', 'Physical Education')
  AND c.trade = 'Medical';
  
  -- Commerce classes (English, Mathematics, Computer Science, Physical Education)
  INSERT IGNORE INTO subject_class_assignment (subject_id, class_id, num_theory_lectures, num_practical_lectures)
  SELECT s.id, c.id, 
    CASE 
      WHEN s.name = 'English' THEN 6
      WHEN s.name = 'Mathematics' THEN 8
      WHEN s.name = 'Computer Science' THEN 4
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END,
    CASE 
      WHEN s.name = 'Computer Science' THEN 4
      WHEN s.name = 'Physical Education' THEN 2
      ELSE 0
    END
  FROM subjects s
  CROSS JOIN classes c
  WHERE s.name IN ('English', 'Mathematics', 'Computer Science', 'Physical Education')
  AND c.trade = 'Commerce';
END //
DELIMITER ;

-- Call the procedure to initialize subject class assignments
CALL initialize_subject_class_assignments();

-- Insert sample holidays
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2023-08-15', 'Independence Day', 'National Holiday'),
('2023-10-02', 'Gandhi Jayanti', 'National Holiday'),
('2023-10-24', 'Dussehra', 'Festival'),
('2023-11-12', 'Diwali', 'Festival'),
('2023-12-25', 'Christmas', 'National Holiday'),
('2024-01-26', 'Republic Day', 'National Holiday'),
('2024-03-25', 'Holi', 'Festival'),
('2024-04-05', 'Good Friday', 'Festival');
