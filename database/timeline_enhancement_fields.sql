-- Additional fields for enhanced timeline functionality
-- These fields can be added to the staff table if more detailed timeline tracking is needed

-- Timeline-specific fields for better experience tracking
ALTER TABLE staff 
ADD COLUMN IF NOT EXISTS experience_timeline JSON COMMENT 'JSON array of detailed work experience with dates',
ADD COLUMN IF NOT EXISTS education_timeline JSON COMMENT 'JSON array of educational milestones with dates',
ADD COLUMN IF NOT EXISTS achievement_timeline JSON COMMENT 'JSON array of achievements with dates',
ADD COLUMN IF NOT EXISTS certification_timeline JSON COMMENT 'JSON array of certifications with dates';

-- Additional experience tracking fields
ALTER TABLE staff 
ADD COLUMN IF NOT EXISTS first_teaching_date DATE COMMENT 'Date when started teaching career',
ADD COLUMN IF NOT EXISTS promotion_history TEXT COMMENT 'History of promotions and position changes',
ADD COLUMN IF NOT EXISTS sabbatical_periods TEXT COMMENT 'Any sabbatical or break periods',
ADD COLUMN IF NOT EXISTS international_experience TEXT COMMENT 'Any international teaching or work experience';

-- Enhanced educational tracking
ALTER TABLE staff 
ADD COLUMN IF NOT EXISTS additional_courses TEXT COMMENT 'Additional courses, workshops, online certifications',
ADD COLUMN IF NOT EXISTS research_interests TEXT COMMENT 'Current research interests and areas of expertise',
ADD COLUMN IF NOT EXISTS thesis_advisor VARCHAR(255) COMMENT 'PhD thesis advisor name',
ADD COLUMN IF NOT EXISTS academic_honors TEXT COMMENT 'Academic honors, scholarships, distinctions';

-- Professional development tracking
ALTER TABLE staff 
ADD COLUMN IF NOT EXISTS mentorship_roles TEXT COMMENT 'Mentorship roles and responsibilities',
ADD COLUMN IF NOT EXISTS committee_memberships TEXT COMMENT 'Academic and professional committee memberships',
ADD COLUMN IF NOT EXISTS editorial_roles TEXT COMMENT 'Editorial board memberships, review roles',
ADD COLUMN IF NOT EXISTS grant_funding TEXT COMMENT 'Research grants and funding received';

-- Sample data for timeline enhancement
-- This shows how the JSON timeline fields can be structured

-- Example experience_timeline JSON structure:
/*
[
  {
    "position": "Computer Instructor",
    "organization": "Private Computer Institute, Ludhiana",
    "start_date": "2008-06-01",
    "end_date": "2012-05-31",
    "responsibilities": "Teaching basic computer skills and programming",
    "achievements": "Developed new curriculum for programming courses"
  },
  {
    "position": "Computer Science Teacher",
    "organization": "Government Senior Secondary School, Mohali",
    "start_date": "2012-06-01",
    "end_date": "2020-06-30",
    "responsibilities": "Teaching 9th-12th grade computer science",
    "achievements": "Improved student performance by 25%, introduced coding clubs"
  },
  {
    "position": "Senior Computer Science Teacher",
    "organization": "Current School",
    "start_date": "2020-07-01",
    "end_date": null,
    "responsibilities": "Senior teaching position with administrative duties",
    "achievements": "Leading digital transformation initiatives",
    "is_current": true
  }
]
*/

-- Example education_timeline JSON structure:
/*
[
  {
    "level": "Class 10",
    "institution": "Government Senior Secondary School, Ludhiana",
    "board": "PSEB",
    "year": 2001,
    "percentage": 88.5,
    "subjects": "All subjects",
    "achievements": "School topper in Mathematics"
  },
  {
    "level": "Class 12",
    "institution": "Government Senior Secondary School, Ludhiana",
    "board": "PSEB",
    "stream": "Science (PCM)",
    "year": 2003,
    "percentage": 85.2,
    "achievements": "District level science exhibition winner"
  },
  {
    "level": "Graduation",
    "degree": "Bachelor of Computer Applications (BCA)",
    "institution": "Panjab University, Chandigarh",
    "year": 2006,
    "percentage": 82.1,
    "specialization": "Computer Science & Programming",
    "final_project": "Library Management System using Java"
  },
  {
    "level": "Post Graduation",
    "degree": "Master of Computer Applications (MCA)",
    "institution": "Panjab University, Chandigarh",
    "year": 2008,
    "percentage": 85.7,
    "specialization": "Software Engineering & Database Systems",
    "thesis": "Web-based Student Information System"
  },
  {
    "level": "Doctorate",
    "degree": "PhD in Computer Science",
    "institution": "Punjab Technical University",
    "year": 2015,
    "thesis_title": "Machine Learning Applications in Educational Technology",
    "advisor": "Dr. Priya Sharma",
    "duration_years": 4
  }
]
*/

-- Example achievement_timeline JSON structure:
/*
[
  {
    "achievement": "Best Teacher Award",
    "organization": "Current School",
    "date": "2022-09-05",
    "category": "Teaching Excellence",
    "description": "Recognized for outstanding contribution to computer science education"
  },
  {
    "achievement": "Excellence in Computer Education",
    "organization": "State Education Board",
    "date": "2021-03-15",
    "category": "Subject Excellence",
    "description": "State-level recognition for innovative teaching methods"
  },
  {
    "achievement": "Innovation in Teaching Award",
    "organization": "District Education Office",
    "date": "2020-12-10",
    "category": "Innovation",
    "description": "For implementing digital learning solutions during pandemic"
  }
]
*/

-- Example certification_timeline JSON structure:
/*
[
  {
    "certification": "Bachelor of Education (B.Ed.)",
    "institution": "Punjab University",
    "date": "2009-05-20",
    "validity": "Lifetime",
    "certificate_number": "BED/2009/12345"
  },
  {
    "certification": "Microsoft Certified Educator",
    "institution": "Microsoft",
    "date": "2018-08-15",
    "validity": "2 years",
    "renewal_date": "2020-08-15",
    "certificate_number": "MCE-2018-5678"
  },
  {
    "certification": "Google for Education Certified Trainer",
    "institution": "Google",
    "date": "2020-11-30",
    "validity": "1 year",
    "renewal_date": "2021-11-30",
    "certificate_number": "GECT-2020-9012"
  }
]
*/

-- Query to insert sample timeline data for a teacher
-- This is an example of how to populate the timeline fields

/*
UPDATE staff 
SET 
  experience_timeline = JSON_ARRAY(
    JSON_OBJECT(
      'position', 'Computer Instructor',
      'organization', 'Private Computer Institute, Ludhiana',
      'start_date', '2008-06-01',
      'end_date', '2012-05-31',
      'responsibilities', 'Teaching basic computer skills and programming',
      'achievements', 'Developed new curriculum for programming courses'
    ),
    JSON_OBJECT(
      'position', 'Computer Science Teacher',
      'organization', 'Government Senior Secondary School, Mohali',
      'start_date', '2012-06-01',
      'end_date', '2020-06-30',
      'responsibilities', 'Teaching 9th-12th grade computer science',
      'achievements', 'Improved student performance by 25%, introduced coding clubs'
    ),
    JSON_OBJECT(
      'position', 'Senior Computer Science Teacher',
      'organization', 'Current School',
      'start_date', '2020-07-01',
      'end_date', NULL,
      'responsibilities', 'Senior teaching position with administrative duties',
      'achievements', 'Leading digital transformation initiatives',
      'is_current', true
    )
  ),
  education_timeline = JSON_ARRAY(
    JSON_OBJECT(
      'level', 'Class 10',
      'institution', 'Government Senior Secondary School, Ludhiana',
      'board', 'PSEB',
      'year', 2001,
      'percentage', 88.5,
      'achievements', 'School topper in Mathematics'
    ),
    JSON_OBJECT(
      'level', 'Class 12',
      'institution', 'Government Senior Secondary School, Ludhiana',
      'board', 'PSEB',
      'stream', 'Science (PCM)',
      'year', 2003,
      'percentage', 85.2,
      'achievements', 'District level science exhibition winner'
    ),
    JSON_OBJECT(
      'level', 'Graduation',
      'degree', 'Bachelor of Computer Applications (BCA)',
      'institution', 'Panjab University, Chandigarh',
      'year', 2006,
      'percentage', 82.1,
      'specialization', 'Computer Science & Programming'
    ),
    JSON_OBJECT(
      'level', 'Post Graduation',
      'degree', 'Master of Computer Applications (MCA)',
      'institution', 'Panjab University, Chandigarh',
      'year', 2008,
      'percentage', 85.7,
      'specialization', 'Software Engineering & Database Systems'
    ),
    JSON_OBJECT(
      'level', 'Doctorate',
      'degree', 'PhD in Computer Science',
      'institution', 'Punjab Technical University',
      'year', 2015,
      'thesis_title', 'Machine Learning Applications in Educational Technology'
    )
  )
WHERE user_id = 1; -- Replace with actual teacher user ID
*/

-- Indexes for better performance on timeline queries
CREATE INDEX IF NOT EXISTS idx_staff_timeline_fields ON staff(user_id, joining_date, first_teaching_date);

-- View to get comprehensive teacher timeline data
CREATE OR REPLACE VIEW teacher_timeline_view AS
SELECT 
  u.id,
  u.username,
  u.name,
  u.full_name,
  u.email,
  s.employee_id,
  s.designation,
  s.department,
  s.joining_date,
  s.first_teaching_date,
  s.total_experience_years,
  s.teaching_experience_years,
  s.experience_timeline,
  s.education_timeline,
  s.achievement_timeline,
  s.certification_timeline,
  s.class_10_year,
  s.class_12_year,
  s.graduation_year,
  s.post_graduation_year,
  s.phd_year
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.role = 'teacher' AND u.is_active = 1;
