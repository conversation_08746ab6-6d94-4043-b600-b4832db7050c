<?php
// Database connection details
require_once './config/database.php';

// Get database connection
$db = getDb();

// Check table structure
$result = $db->query("DESCRIBE users");
echo "<h2>Users Table Structure</h2>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
    echo "<tr>";
    foreach ($row as $key => $value) {
        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
    }
    echo "</tr>";
}
echo "</table>";

// Check sample teacher data
$result = $db->query("SELECT id, full_name, subjects, role FROM users WHERE role = 'teacher' LIMIT 5");
echo "<h2>Sample Teacher Data</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Full Name</th><th>Subjects</th><th>Role</th></tr>";
while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['id']) . "</td>";
    echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
    echo "<td>" . htmlspecialchars($row['subjects'] ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($row['role']) . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p>Done!</p>";
?> 