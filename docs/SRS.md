## Test Creation Workflow

### Step Navigation System

The test creation process is divided into 5 steps with visual progress tracking:

1. **Progress Bar**
   - Shows overall completion percentage
   - Updates dynamically as steps are completed
   - Located at the top of the form

2. **Step Indicators**
   - Visual tabs for each step
   - Color-coded status:
     - Green: Step completed successfully
     - Red: Step has missing or invalid data
     - Gray: Future step not yet accessible

3. **Validation System**
   - Real-time validation of each step
   - Error messages displayed as bullet points under incomplete steps
   - Required fields clearly indicated

4. **Step Requirements**

   a. Basic Info (Step 1)
   - Test name (required)
   - Duration (required)
   - Passing score (required)

   b. Instructions (Step 2)
   - Test instructions (required)
   - Word count between 180-250 words

   c. Questions (Step 3)
   - At least one section required
   - Each section must contain at least one question
   - Questions must be properly configured

   d. Preview (Step 4)
   - Review of all test components
   - No specific validation requirements

   e. Publish (Step 5)
   - Final review and publication options
   - No specific validation requirements

5. **Navigation Features**
   - "Proceed to Next Step" indicator appears when current step is complete
   - Next step name is dynamically displayed
   - Previous steps remain accessible for editing
   - Invalid steps prevent progression until errors are resolved

6. **Progress Tracking**
   - Percentage-based completion tracking
   - Visual progress bar
   - Step-by-step validation status