# Admin Navbar Design

## Overview

This document provides the design and implementation details for the admin navigation bar. The admin navbar should provide quick access to all administrative functions while maintaining a clean and intuitive interface.

## Desktop Navbar Design

```
+----------------------------------------------------------------------+
| Meritorious EP                                        Admin: <PERSON> |
+----------------------------------------------------------------------+
| Dashboard | Users | Tests | Questions | Reports | Settings | Logout   |
+----------------------------------------------------------------------+
```

### HTML Structure

```html
<nav class="bg-indigo-800 text-white shadow-lg">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center py-3">
      <!-- Logo and Brand -->
      <div class="flex items-center space-x-4">
        <a href="/" class="text-xl font-bold">Meritorious EP</a>
        
        <!-- Main Navigation -->
        <div class="hidden md:flex space-x-4 ml-10">
          <a href="/admin/dashboard" class="px-3 py-2 rounded hover:bg-indigo-700 transition">Dashboard</a>
          
          <!-- Users Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded hover:bg-indigo-700 transition flex items-center">
              Users
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <a href="/admin/users" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">All Users</a>
              <a href="/admin/users/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Add New User</a>
              <a href="/admin/users/roles" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Manage Roles</a>
            </div>
          </div>
          
          <!-- Tests Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded hover:bg-indigo-700 transition flex items-center">
              Tests
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <a href="/admin/tests" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">All Tests</a>
              <a href="/admin/tests/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Create New Test</a>
              <a href="/admin/tests/categories" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Test Categories</a>
            </div>
          </div>
          
          <!-- Questions Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded hover:bg-indigo-700 transition flex items-center">
              Questions
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <a href="/admin/questions" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Question Bank</a>
              <a href="/admin/questions/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Add Question</a>
              <a href="/admin/questions/import" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Import Questions</a>
              <a href="/admin/questions/categories" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Categories</a>
            </div>
          </div>
          
          <!-- Reports Link -->
          <a href="/admin/reports" class="px-3 py-2 rounded hover:bg-indigo-700 transition">Reports</a>
          
          <!-- Settings Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded hover:bg-indigo-700 transition flex items-center">
              Settings
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <a href="/admin/settings/general" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">General Settings</a>
              <a href="/admin/settings/email" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Email Templates</a>
              <a href="/admin/settings/security" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Security</a>
              <a href="/admin/settings/backup" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Backup & Restore</a>
            </div>
          </div>
        </div>
      </div>
      
      <!-- User Menu -->
      <div class="flex items-center space-x-4">
        <span class="hidden md:inline-block">Admin: <span class="font-semibold">John Doe</span></span>
        <div class="relative group">
          <button class="w-10 h-10 rounded-full bg-indigo-600 flex items-center justify-center">
            <span class="text-lg font-bold">JD</span>
          </button>
          <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
            <a href="/profile" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">My Profile</a>
            <a href="/admin/dashboard" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Admin Dashboard</a>
            <div class="border-t border-gray-200"></div>
            <a href="/logout" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Logout</a>
          </div>
        </div>
      </div>
      
      <!-- Mobile Menu Button -->
      <div class="md:hidden">
        <button id="mobile-menu-button" class="text-white focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Mobile Menu -->
  <div id="mobile-menu" class="hidden md:hidden bg-indigo-900 pb-4 px-4">
    <a href="/admin/dashboard" class="block py-2 text-white">Dashboard</a>
    <a href="/admin/users" class="block py-2 text-white">Users</a>
    <a href="/admin/tests" class="block py-2 text-white">Tests</a>
    <a href="/admin/questions" class="block py-2 text-white">Questions</a>
    <a href="/admin/reports" class="block py-2 text-white">Reports</a>
    <a href="/admin/settings" class="block py-2 text-white">Settings</a>
    <div class="border-t border-indigo-800 my-2"></div>
    <a href="/profile" class="block py-2 text-white">My Profile</a>
    <a href="/logout" class="block py-2 text-white">Logout</a>
  </div>
</nav>
```

## Mobile Navbar Design

On mobile devices, the navbar collapses into a hamburger menu:

```
+----------------------------------------------------------------------+
| Meritorious EP                                                 ☰     |
+----------------------------------------------------------------------+
```

When expanded:

```
+----------------------------------------------------------------------+
| Meritorious EP                                                 ☰     |
+----------------------------------------------------------------------+
| Dashboard                                                             |
| Users                                                                 |
| Tests                                                                 |
| Questions                                                             |
| Reports                                                               |
| Settings                                                              |
| -------------------------------------------------------------------- |
| My Profile                                                            |
| Logout                                                                |
+----------------------------------------------------------------------+
```

## JavaScript for Mobile Menu Toggle

```javascript
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  mobileMenuButton.addEventListener('click', function() {
    mobileMenu.classList.toggle('hidden');
  });
});
```

## Active Link Highlighting

To highlight the current page in the navbar, add the following class to the active link:

```html
<a href="/admin/dashboard" class="px-3 py-2 rounded bg-indigo-700 transition">Dashboard</a>
```

## Implementation in EJS

In your layout file, you can determine the active page using:

```ejs
<a href="/admin/dashboard" class="px-3 py-2 rounded <%= currentPage === 'dashboard' ? 'bg-indigo-700' : 'hover:bg-indigo-700' %> transition">Dashboard</a>
```

## Breadcrumbs

For better navigation, include breadcrumbs below the navbar:

```html
<div class="bg-gray-100 py-2 px-4">
  <div class="container mx-auto">
    <div class="flex items-center text-sm text-gray-600">
      <a href="/admin/dashboard" class="hover:text-indigo-600">Dashboard</a>
      <svg class="w-3 h-3 mx-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <a href="/admin/users" class="hover:text-indigo-600">Users</a>
      <svg class="w-3 h-3 mx-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span class="text-gray-800">Edit User</span>
    </div>
  </div>
</div>
```

## Notifications Indicator

Add a notifications indicator to the navbar:

```html
<div class="relative">
  <a href="/admin/notifications" class="text-white">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
    </svg>
    <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
  </a>
</div>
```

## Quick Actions Menu

Add a quick actions button to the navbar:

```html
<div class="relative group">
  <button class="px-3 py-2 rounded hover:bg-indigo-700 transition flex items-center">
    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Quick Actions
  </button>
  <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
    <a href="/admin/tests/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Create Test</a>
    <a href="/admin/users/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Add User</a>
    <a href="/admin/questions/add" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Add Question</a>
    <a href="/admin/reports/generate" class="block px-4 py-2 text-gray-800 hover:bg-indigo-100">Generate Report</a>
  </div>
</div>
```

## Admin Layout Template

Create a reusable admin layout template (`views/layouts/admin.ejs`):

```ejs
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - Admin Dashboard</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <!-- Admin Navbar -->
  <%- include('../partials/admin-navbar', { currentPage }) %>
  
  <!-- Breadcrumbs -->
  <% if (breadcrumbs) { %>
    <%- include('../partials/breadcrumbs', { breadcrumbs }) %>
  <% } %>
  
  <!-- Main Content -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <%- body %>
  </main>
  
  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>
  
  <!-- Mobile Menu Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      
      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });
    });
  </script>
</body>
</html>
```

## Usage Example

To use this navbar in your admin pages:

```javascript
// In your route handler
router.get('/admin/dashboard', checkAdmin, (req, res) => {
  res.render('admin/dashboard', {
    title: 'Admin Dashboard',
    currentPage: 'dashboard',
    breadcrumbs: [
      { text: 'Dashboard', url: '/admin/dashboard', active: true }
    ],
    user: req.user
  });
});
``` 