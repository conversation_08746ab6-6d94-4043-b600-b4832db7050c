# Category-wise Room-wise Equipment Count Queries

This document describes the comprehensive SQL queries for getting equipment counts categorized by type and organized by room.

## Overview

The system now includes three main queries for analyzing equipment distribution:

1. **Comprehensive Category-wise Room-wise Count** - Detailed breakdown by equipment categories
2. **Category-wise Summary Across All Rooms** - Total counts by equipment category
3. **Room-wise Equipment Summary** - Simplified view grouped by room type

## Database Structure

### Tables Involved
- `rooms` - Room information (room_number, building, floor, capacity)
- `inventory_items` - Main equipment inventory (158 items)
- `it_inventory` - IT-specific equipment (9 items)
- `electrical_inventory` - Electrical equipment (422 items)

### Foreign Key Relationships
- `inventory_items.room_id → rooms.id`
- `it_inventory.room_id → rooms.id`
- `electrical_inventory.room_id → rooms.id`

## Query 1: Comprehensive Category-wise Room-wise Count

**Query Name:** `getItemCountCategoryWiseRoomWise`

**Purpose:** Get detailed equipment counts by category for each room

**Usage:**
```javascript
const SQLQueries = require('./config/sql-queries');
const [results] = await db.query(SQLQueries.infrastructure.getItemCountCategoryWiseRoomWise);
```

**Returns:**
- Room information (room_number, room_id, building, floor, capacity)
- **Inventory Items Categories:**
  - `projectors` - Projectors from inventory_items
  - `ups_units` - UPS units
  - `desktops` - Desktop computers
  - `laptops` - Laptop computers
  - `interactive_panels` - Interactive flat panel displays
  - `cameras` - Cameras and webcams
  - `printers` - Printers and MFPs
  - `routers` - Network routers
  - `microphones` - Microphones and audio equipment

- **IT Inventory Categories:**
  - `it_laptops` - Laptops from it_inventory
  - `it_desktops` - Desktops from it_inventory
  - `it_tablets` - Tablets
  - `it_projectors` - Projectors from it_inventory
  - `it_printers` - Printers from it_inventory
  - `it_network` - Network equipment
  - `it_other` - Other IT equipment

- **Electrical Inventory Categories:**
  - `tube_lights` - Tube lights/fluorescent lights
  - `ceiling_fans` - Ceiling fans
  - `power_outlets` - Power outlets and sockets
  - `switches` - Light switches and controls
  - `electrical_other` - Other electrical equipment

- **Totals:**
  - `total_inventory_items` - Total from inventory_items table
  - `total_it_items` - Total from it_inventory table
  - `total_electrical_items` - Total from electrical_inventory table
  - `grand_total_equipment` - Sum of all equipment

**Sample Output:**
```
Computer Lab 1 (ID: 39)
Building: Main Building | Floor: 1 | Capacity: 50
Technology: 18 Desktops, 1 Printers, 1 UPS Units
Totals: 20 inventory + 0 IT + 0 electrical = 20 total
```

## Query 2: Category-wise Summary Across All Rooms

**Query Name:** `getCategoryWiseSummaryAllRooms`

**Purpose:** Get total counts by equipment category across the entire institution

**Usage:**
```javascript
const [categories] = await db.query(SQLQueries.infrastructure.getCategoryWiseSummaryAllRooms);
```

**Returns:**
- `category` - Equipment category name
- `total_count` - Total count across all rooms
- `category_group` - Group (Technology, Power, Electrical)

**Categories Included:**
- **Technology:** Projectors, Desktops, Laptops, Printers
- **Power:** UPS Units
- **Electrical:** Tube Lights, Ceiling Fans

## Query 3: Room-wise Equipment Summary

**Query Name:** `getRoomWiseEquipmentSummary`

**Purpose:** Get simplified equipment summary grouped by room type

**Usage:**
```javascript
const [summary] = await db.query(SQLQueries.infrastructure.getRoomWiseEquipmentSummary);
```

**Returns:**
- Room information and type classification
- Equipment counts by table (inventory, IT, electrical)
- Total equipment count per room

**Room Types:**
- `Computer Lab` - Computer Lab 1, Computer Lab 2
- `Science Lab` - Biology Lab, Chemistry Lab, Physics Lab
- `Library` - Library room
- `Office` - Principal Office, Admin Office, etc.
- `Classroom` - Room 1, Room 2, etc.
- `Other` - Miscellaneous rooms

## Current Statistics (as of latest update)

### Overall Totals
- **Total Rooms with Equipment:** 31
- **Total Equipment Items:** 586
  - Inventory Items: 155
  - IT Items: 9
  - Electrical Items: 422

### Technology Equipment
- **Projectors:** 22 (HITACHI, BENQ models)
- **Desktop Computers:** 61 (ACER VERITON, Intel OPS)
- **Laptops:** 24 (Lenovo, Apple, Dell)
- **Printers:** 8 (HP LaserJet, MFP)
- **UPS Units:** 30 (MICROTEK, UNILINE)
- **Interactive Panels:** 2 (ACER 75" IFPD)
- **Cameras:** 2 (LOGITECH BRIO 4K)

### Electrical Equipment
- **Tube Lights:** 100 (Philips TL-D 36W/40W)
- **Ceiling Fans:** 80 (Bajaj Maxima 1200mm)
- **Power Outlets:** 160 (Legrand Myrius 6A)
- **Switches:** 80 (Fan regulators, light controls)

## Room Distribution Examples

### Computer Labs
- **Computer Lab 1:** 20 items (18 desktops, 1 printer, 1 UPS)
- **Computer Lab 2:** 22 items (19 desktops, 1 printer, 1 UPS)

### Science Labs
- **Biology Lab:** 3 items (1 projector, 1 desktop, 1 UPS)
- **Chemistry Lab:** 3 items (1 projector, 1 desktop, 1 UPS)
- **Physics Lab:** 3 items (1 projector, 1 desktop, 1 UPS)

### Offices
- **Principal Office:** 4 items (desktop, tablet, printer, UPS)
- **Admin Office:** 4 items (desktop, printer, UPS, other)

### Library
- **Library:** 3 items (PC, HP printer, UPS)

## Implementation Notes

1. **Equipment Classification:** Uses LIKE patterns to categorize equipment based on names
2. **Distinct Counting:** Prevents double-counting of equipment across tables
3. **Room Ordering:** Orders results by room type priority (Labs → Library → Offices → Classrooms)
4. **Performance:** Uses indexed foreign key relationships for optimal query performance
5. **Data Integrity:** Foreign key constraints ensure referential integrity

## Usage in Applications

These queries are ideal for:
- **Infrastructure dashboards** showing equipment distribution
- **Inventory management** reports
- **Resource planning** and allocation
- **Maintenance scheduling** by equipment type
- **Budget planning** based on equipment categories
- **Compliance reporting** for educational standards

## Test Script

Run the test script to see all queries in action:
```bash
node test-category-wise-queries.js
```

This will demonstrate all three queries with sample output and usage examples.
