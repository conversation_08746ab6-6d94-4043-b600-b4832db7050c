# Computer Teacher Rules

This document outlines the rules and database structure for computer teachers in the system.

## Rules

1. **Computer teachers can only teach computer-related subjects**
   - Computer Science
   - Computer Applications
   - Programming
   - Other computer-related subjects

2. **Computer teachers cannot be class incharge**
   - They can only be lab incharge
   - This prevents computer teachers from being assigned as class incharge

3. **All inchargeships are session-wise**
   - Class incharge assignments include a session column
   - Lab incharge assignments include a session column
   - This allows for different assignments in different academic sessions

## Database Structure

### Teacher Specialization

```sql
CREATE TABLE teacher_specialization (
  id INT PRIMARY KEY AUTO_INCREMENT,
  teacher_id INT NOT NULL,
  specialization ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 
                     'English', 'Hindi', 'Social Science', 'Economics', 'Commerce', 
                     'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_teacher_specialization (teacher_id, specialization)
)
```

### Subject Category

```sql
CREATE TABLE subject_category (
  id INT PRIMARY KEY AUTO_INCREMENT,
  subject_id INT NOT NULL,
  category ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 
               'English', 'Hindi', 'Social Science', 'Economics', 'Commerce', 
               'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  UNIQUE KEY unique_subject_category (subject_id, category)
)
```

### Class Incharge

```sql
CREATE TABLE class_incharge (
  id INT PRIMARY KEY AUTO_INCREMENT,
  class_id INT NOT NULL,
  teacher_id INT NOT NULL,
  session VARCHAR(20) NOT NULL DEFAULT '2024-2025',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_class_incharge_session (class_id, session)
)
```

### Lab Incharge

```sql
CREATE TABLE lab_incharge (
  id INT PRIMARY KEY AUTO_INCREMENT,
  lab_id INT NOT NULL,
  teacher_id INT NOT NULL,
  session VARCHAR(20) NOT NULL DEFAULT '2024-2025',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (lab_id) REFERENCES labs(id) ON DELETE CASCADE,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_lab_incharge_session (lab_id, session)
)
```

## Implementation

The implementation includes:

1. **Database Migrations**
   - `update-class-incharge-table.js`: Adds session column to class_incharge table
   - `create-lab-incharge-table.js`: Creates lab_incharge table with session column
   - `create-teacher-specialization-table.js`: Creates teacher_specialization and subject_category tables

2. **Validation Middleware**
   - `teacher-subject-validator.js`: Ensures computer teachers can only teach computer-related subjects
   - `class-incharge-validator.js`: Prevents computer teachers from being assigned as class incharge

3. **Fix Script**
   - `fix-computer-teacher-assignments.js`: Updates existing assignments to comply with the new rules

4. **Main Migration Script**
   - `update-computer-teacher-rules.js`: Runs all the migrations and fixes in the correct order

## How to Run

To implement these changes, run the main migration script:

```bash
node migrations/update-computer-teacher-rules.js
```

This will:
1. Update the class_incharge table to add a session column
2. Create the lab_incharge table with a session column
3. Create the teacher_specialization and subject_category tables
4. Fix existing computer teacher assignments to comply with the new rules

## Middleware Integration

To enforce these rules in your application, add the validation middleware to your routes:

```javascript
// In your routes file where teacher-subject assignments are handled:
const validateTeacherSubjectAssignment = require('../middleware/teacher-subject-validator');

// Add the middleware to the route
router.post('/assign-subject', validateTeacherSubjectAssignment, yourControllerFunction);

// In your routes file where class incharge assignments are handled:
const validateClassInchargeAssignment = require('../middleware/class-incharge-validator');

// Add the middleware to the route
router.post('/assign-class-incharge', validateClassInchargeAssignment, yourControllerFunction);
```

## Verification

After running the migration, you can verify that:

1. Computer teachers are only assigned to computer-related subjects
2. Computer teachers are not assigned as class incharge
3. Computer teachers are assigned as lab incharge for computer labs
4. All inchargeships have a session column
