# Computer Teacher View Restrictions

This document outlines the restrictions implemented for computer teachers in the system.

## Overview

Computer teachers are restricted to only view their own data, not data from other teachers or global data. This is implemented through middleware that:

1. Identifies if a user is a computer teacher
2. Restricts computer teachers to only see their own data
3. Prevents computer teachers from accessing global data views

## Implementation

### Middleware

Two main middleware functions have been implemented:

1. **isComputerTeacher**: Identifies if a user is a computer teacher based on:
   - Having a specialization of 'Computer Science' in the teacher_specialization table
   - Teaching computer-related subjects

2. **restrictComputerTeacher**: Ensures computer teachers can only see their own data by:
   - Checking if the user is a computer teacher
   - Ensuring any teacher_id parameter matches the logged-in user's ID
   - Forcing teacher_id to be the logged-in user's ID in queries

### Routes Modified

The following routes have been modified to use the new middleware:

1. **Teacher Dashboard Routes**:
   - `/teacher/dashboard`
   - `/teacher/profile`
   - `/teacher/classes`
   - `/teacher/timetable`
   - `/teacher/my-timetable`
   - `/teacher/syllabus/*`
   - `/teacher/practicals/*`
   - `/teacher/lectures/*`
   - `/teacher/reports/*`

2. **Teacher API Routes**:
   - `/api/teacher/timetable`
   - `/api/teacher/teacher-timetable`
   - `/api/teacher/class-teacher-map`
   - `/api/teacher/class-subject-data`
   - `/api/teacher/lectures/*`
   - `/api/teacher/practicals/*`
   - `/api/teacher/syllabus/*`
   - `/api/teacher/subject-eligibility`
   - `/api/teacher/lecture-schedules`
   - `/api/teacher/instruction-plans`
   - `/api/teacher/holiday-calendar`

3. **Practicals Routes**:
   - `/teacher/practicals/*`

### Global Data Access

Computer teachers are prevented from accessing global data views that show information about other teachers. This is implemented through the `checkGlobalAccess` middleware, which now checks if a user is a computer teacher and denies access if they are.

Global data views that are restricted include:
- `/teacher/teacher-timetable`
- `/teacher/class-teacher-map`
- `/teacher/class-subject-summary`
- `/teacher/subject-eligibility`
- `/teacher/class-assignments`
- `/teacher/lecture-schedules`

## How It Works

1. When a computer teacher logs in, the `isComputerTeacher` middleware identifies them as a computer teacher.
2. When they access any teacher view, the `restrictComputerTeacher` middleware ensures they can only see their own data.
3. If they try to access a global data view, the `checkGlobalAccess` middleware denies access.

## Example

For example, when a computer teacher accesses the `/teacher/classes` route:

1. The `isComputerTeacher` middleware identifies them as a computer teacher.
2. The `restrictComputerTeacher` middleware ensures the query only returns classes assigned to the logged-in teacher.
3. The teacher only sees their own classes, not classes assigned to other teachers.

## Testing

To test these restrictions:

1. Log in as a computer teacher (one who teaches computer-related subjects).
2. Navigate to various teacher views and verify that only data related to the logged-in teacher is displayed.
3. Try to access global data views and verify that access is denied.
4. Log in as a non-computer teacher and verify that they can access global data views.

## Conclusion

These restrictions ensure that computer teachers can only view their own data, not data from other teachers or global data. This provides better security and privacy for teacher data in the system.
