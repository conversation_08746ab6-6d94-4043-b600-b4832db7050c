# Developer Quick Reference Guide

## Project Structure

```
meritorious_ep/
├── config/                 # Configuration files
│   └── database.js         # Database connection
├── middleware/             # Express middleware
│   └── auth.js             # Authentication middleware
├── public/                 # Static assets
│   ├── css/                # Stylesheets
│   ├── js/                 # Client-side JavaScript
│   └── uploads/            # User uploads
│       └── profiles/       # Profile images
├── routes/                 # Express routes
│   ├── auth-routes.js      # Authentication routes
│   ├── test-routes.js      # Test management routes
│   ├── profile-routes.js   # Profile management routes
│   └── notification-routes.js # Notification routes
├── scripts/                # Utility scripts
│   ├── check-table-structure.js # Database check script
│   └── fix-database.js     # Database repair script
├── views/                  # EJS templates
│   ├── auth/               # Authentication templates
│   │   ├── login.ejs       # Login page
│   │   ├── register.ejs    # Registration page
│   │   ├── forgot-password.ejs # Password recovery
│   │   └── reset-password.ejs # Password reset
│   ├── tests/              # Test templates
│   │   ├── index.ejs       # Tests list
│   │   ├── add-test.ejs    # Test creation/editing
│   │   └── take-test.ejs   # Test taking interface
│   ├── profile/            # Profile templates
│   │   └── index.ejs       # Profile page
│   ├── admin/              # Admin templates
│   │   └── dashboard.ejs   # Admin dashboard
│   └── error.ejs           # Error page
├── database/               # Database files
│   └── schema.sql          # Database schema
├── docs/                   # Documentation
│   ├── website-design.md   # Design reference
│   └── sitemap.md          # Visual sitemap
├── index.js                # Application entry point
└── package.json            # Dependencies and scripts
```

## Key Files and Their Purpose

| File | Purpose |
|------|---------|
| `index.js` | Main application entry point, sets up Express, middleware, and routes |
| `config/database.js` | Database connection configuration |
| `middleware/auth.js` | Authentication middleware for protected routes |
| `routes/auth-routes.js` | Authentication routes (login, register, etc.) |
| `routes/test-routes.js` | Test management routes |
| `database/schema.sql` | Complete database schema |

## Common Tasks

### Starting the Application

```bash
# Start the server
node index.js

# Start with nodemon for development
nodemon index.js
```

### Database Management

```bash
# Check database structure
node scripts/check-table-structure.js

# Fix database issues
node scripts/fix-database.js

# Initialize database with schema
mysql -u root -p < database/schema.sql
```

### Adding a New Route

1. Create a new route file in the `routes/` directory
2. Define your routes using Express Router
3. Export the router
4. Import and register the router in `index.js`

Example:
```javascript
// routes/example-routes.js
const express = require('express');
const router = express.Router();

router.get('/', (req, res) => {
    res.render('example/index', { title: 'Example' });
});

module.exports = router;

// In index.js
const exampleRoutes = require('./routes/example-routes');
app.use('/example', checkAuthenticated, exampleRoutes);
```

### Adding a New View

1. Create a new EJS file in the appropriate directory under `views/`
2. Use the standard layout structure
3. Reference it from your route handler

Example:
```javascript
// Route handler
router.get('/example', (req, res) => {
    res.render('example/index', { 
        title: 'Example Page',
        data: someData
    });
});
```

### Authentication

The application uses session-based authentication with the following middleware:

```javascript
// Protect routes that require authentication
const checkAuthenticated = (req, res, next) => {
    if (!req.session.userId) {
        return res.redirect('/login');
    }
    next();
};

// Protect admin routes
const checkAdmin = (req, res, next) => {
    if (!req.session.userId || req.session.userRole !== 'admin') {
        return res.redirect('/');
    }
    next();
};
```

### Database Queries

The application uses mysql2/promise for database operations:

```javascript
// Example query
const [users] = await db.query(
    'SELECT * FROM users WHERE email = ?',
    [email]
);

// Example transaction
try {
    await db.query('START TRANSACTION');
    
    // Multiple queries...
    
    await db.query('COMMIT');
} catch (error) {
    await db.query('ROLLBACK');
    throw error;
}
```

## API Response Format

All API endpoints should follow this response format:

### Success Response
```json
{
    "message": "Operation successful",
    "data": {
        // Response data here
    }
}
```

### Error Response
```json
{
    "error": "Error message"
}
```

## Common Middleware

| Middleware | Purpose |
|------------|---------|
| `checkAuthenticated` | Ensures user is logged in |
| `checkAdmin` | Ensures user is an admin |
| `isApiRequest` | Detects API/AJAX requests |

## Environment Variables

| Variable | Purpose | Default |
|----------|---------|---------|
| `PORT` | Server port | 3001 |
| `SESSION_SECRET` | Session encryption key | "your-secret-key" |
| `NODE_ENV` | Environment (development/production) | development |

## Coding Standards

1. Use camelCase for variables and functions
2. Use PascalCase for classes
3. Use snake_case for database columns
4. Use meaningful variable and function names
5. Add comments for complex logic
6. Handle errors properly
7. Validate user input
8. Use async/await for asynchronous operations
9. Use parameterized queries for database operations

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check if MySQL is running
   - Verify database credentials in `config/database.js`
   - Ensure database exists

2. **Session Issues**
   - Check session configuration in `index.js`
   - Verify cookie settings
   - Check for proper session handling in routes

3. **Route Not Found**
   - Check route registration in `index.js`
   - Verify route path and method
   - Check for typos in route paths

4. **Template Rendering Errors**
   - Ensure template exists in the correct location
   - Check for missing variables in render calls
   - Verify EJS syntax

## Useful Commands

```bash
# Install dependencies
npm install

# Create database backup
mysqldump -u root -p exam_prep_platform > backup.sql

# Restore database from backup
mysql -u root -p exam_prep_platform < backup.sql

# Check for errors in logs
grep -i error logs/app.log

# Run linter
npm run lint

# Run tests
npm test
``` 

# Database tables for teacher view
Table Teacher {
  TeacherID int [pk]
  FirstName varchar
  LastName varchar
  Email varchar
  Phone varchar
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table Class {
  ClassID int [pk]
  ClassName varchar
  Grade varchar
  Trade varchar
  Section varchar
  AcademicYear varchar
  Board varchar
  MaxCapacity int
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table Subject {
  SubjectID int [pk]
  SubjectCode varchar
  SubjectName varchar
  Description text
  MaxTheoryLectures int
  MaxPracticalLectures int
  TotalLecturesPerWeek int
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table TeacherSubjectEligibility {
  EligibilityID int [pk]
  TeacherID int [ref: > Teacher.TeacherID]
  SubjectID int [ref: > Subject.SubjectID]
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table SubjectClassAssignment {
  AssignmentID int [pk]
  SubjectID int [ref: > Subject.SubjectID]
  ClassID int [ref: > Class.ClassID]
  NumTheoryLectures int
  NumPracticalLectures int
  ClassSubjectTeacherRatio varchar
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table LectureSchedule {
  ScheduleID int [pk]
  AssignmentID int [ref: > SubjectClassAssignment.AssignmentID]
  TeacherID int [ref: > Teacher.TeacherID]
  DayOfWeek varchar
  StartTime time
  EndTime time
  Classroom varchar
  Semester varchar
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table DailyInstructionPlan {
  PlanID int [pk]
  ScheduleID int [ref: > LectureSchedule.ScheduleID]
  Date date
  Topic varchar
  Objectives text
  MaterialSupport text
  Activities text
  Homework text
  Notes text
  TopicCompletionPercentage decimal
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}

Table HolidayCalendar {
  HolidayID int [pk]
  HolidayDate date
  Description varchar
  HolidayType varchar
  CreatedAt datetime
  UpdatedAt datetime
  IsActive boolean
}