# Responsive Design Guide

This guide outlines the standards and best practices for maintaining UI consistency across different devices (desktop, laptop, tablet, and mobile) in our application.

## Breakpoints

We follow Tailwind CSS's standard breakpoints:

| Breakpoint | Class Prefix | Minimum Width |
|------------|--------------|---------------|
| Mobile     | (default)    | 0px           |
| Small      | sm:          | 640px         |
| Medium     | md:          | 768px         |
| Large      | lg:          | 1024px        |
| Extra Large| xl:          | 1280px        |
| 2X Large   | 2xl:         | 1536px        |

## Responsive Components

### Navigation

- Mobile navigation uses a hamburger menu that appears below `md:` (768px)
- Desktop navigation shows full menu items at `md:` and above
- Dropdowns in mobile view should be tap-friendly with larger touch targets

```html
<!-- Example of responsive navigation -->
<div class="hidden md:flex">
  <!-- Desktop navigation items -->
</div>
<div class="md:hidden">
  <!-- Mobile hamburger menu -->
</div>
```

### Grid Layouts

Use consistent grid layouts with appropriate column counts for different screen sizes:

```html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  <!-- Content -->
</div>
```

### Tables

Use the responsive table component for all tables:

```html
<div class="responsive-table-wrapper">
  <table class="responsive-table">
    <!-- Table content -->
  </table>
</div>
```

For mobile-friendly tables that transform into cards on small screens:

```html
<div class="responsive-table-wrapper">
  <table class="responsive-table card-table-mobile">
    <!-- Table content with data-label attributes -->
    <td data-label="Name">John Doe</td>
  </table>
</div>
```

### Forms

Use the standardized form components for consistent form styling:

```html
<div class="form-group">
  <label for="email" class="form-label required">Email</label>
  <input type="email" id="email" name="email" class="form-input" required>
  <div id="email-feedback" class="form-feedback"></div>
</div>
```

For responsive form layouts:

```html
<div class="form-row">
  <div class="form-col">
    <!-- First column -->
  </div>
  <div class="form-col">
    <!-- Second column -->
  </div>
</div>
```

### Images

Use the responsive image components:

```html
<div class="responsive-image-container">
  <img src="..." alt="..." class="responsive-image">
</div>
```

For fixed aspect ratio images:

```html
<div class="aspect-ratio-container aspect-ratio-16-9">
  <img src="..." alt="...">
</div>
```

### Modals

Use the standardized modal component:

```html
<div id="example-modal" class="modal hidden">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Modal Title</h3>
      <button class="modal-close" data-close-modal="example-modal">×</button>
    </div>
    <div class="modal-body">
      <!-- Modal content -->
    </div>
    <div class="modal-footer">
      <button class="modal-btn modal-btn-secondary" data-close-modal="example-modal">Cancel</button>
      <button class="modal-btn modal-btn-primary">Save</button>
    </div>
  </div>
</div>
```

## Spacing System

Use the standardized spacing classes for consistent spacing:

```html
<div class="section-spacing">
  <!-- Section content -->
</div>

<div class="card-spacing">
  <!-- Card content -->
</div>

<div class="form-group-spacing">
  <!-- Form group content -->
</div>
```

## Testing Responsive Design

### Device Testing Tool

Add `?dev_mode=true` to any URL to enable the device testing tool, which allows you to quickly test different device sizes.

### Testing Checklist

For each new feature or page, test on:

1. **Desktop** (1920×1080)
   - Check that layouts use space efficiently
   - Ensure tables display all columns
   - Verify that hover states work correctly

2. **Laptop** (1366×768)
   - Check that layouts adjust appropriately
   - Ensure no horizontal scrolling
   - Verify that all content is visible without zooming

3. **Tablet** (768×1024)
   - Check that navigation switches to mobile view
   - Ensure touch targets are large enough
   - Verify that forms are usable

4. **Mobile** (375×667)
   - Check that content stacks properly
   - Ensure text is readable without zooming
   - Verify that all functionality is accessible

## Common Issues and Solutions

### Horizontal Scrolling

If you encounter horizontal scrolling on mobile:

```css
/* Add this to the container */
.overflow-x-hidden
```

### Touch Targets Too Small

For small buttons on mobile:

```html
<button class="py-1 px-2 md:py-2 md:px-4">
  Button Text
</button>
```

### Text Too Small

For responsive text sizing:

```html
<h1 class="text-xl md:text-2xl lg:text-3xl">
  Heading
</h1>
```

### Tables Overflowing

Use the responsive table component:

```html
<div class="responsive-table-wrapper">
  <table class="responsive-table">
    <!-- Table content -->
  </table>
</div>
```

## Best Practices

1. **Mobile-First Approach**: Start with mobile design and add complexity for larger screens
2. **Consistent Breakpoints**: Use the standard breakpoints throughout the application
3. **Test Frequently**: Test on multiple device sizes during development
4. **Use Standard Components**: Leverage the standardized components for consistency
5. **Avoid Fixed Widths**: Use relative units (%, rem) instead of fixed pixels
6. **Optimize Images**: Use responsive images with appropriate sizes
7. **Touch-Friendly UI**: Ensure touch targets are at least 44×44px on mobile
8. **Keyboard Accessibility**: Ensure all interactive elements are keyboard accessible

## Resources

- [Tailwind CSS Responsive Design](https://tailwindcss.com/docs/responsive-design)
- [MDN Responsive Design](https://developer.mozilla.org/en-US/docs/Learn/CSS/CSS_layout/Responsive_Design)
- [Google's Responsive Web Design Basics](https://developers.google.com/web/fundamentals/design-and-ux/responsive)
