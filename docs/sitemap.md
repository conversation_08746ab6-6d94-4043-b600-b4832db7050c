# Meritorious Exam Preparation Platform - Sitemap

## Visual Sitemap

```
                                  +----------------+
                                  |  Landing Page  |
                                  +-------+--------+
                                          |
                    +---------------------+---------------------+
                    |                     |                     |
            +-------v-------+    +--------v-------+    +-------v-------+
            |    Register   |    |     Login      |    | Forgot Password|
            +-------+-------+    +--------+-------+    +-------+-------+
                    |                     |                     |
                    |                     |                     |
                    +----------+----------+                     |
                               |                                |
                     +---------v---------+                      |
                     |                   |                      |
                     |  User Dashboard   <----------------------+
                     |                   |
                     +---------+---------+
                               |
        +--------------------+-+--+-------------------+
        |                    |    |                   |
+-------v------+    +--------v---------+    +---------v--------+
|              |    |                  |    |                  |
|  Tests List  |    | Profile Settings |    |  Notifications   |
|              |    |                  |    |                  |
+-------+------+    +------------------+    +------------------+
        |
        |
+-------v------+    +------------------+
|              |    |                  |
| Take Test    |    | Test Results     |
|              |    |                  |
+--------------+    +------------------+


                     +-------------------+
                     |                   |
                     | Admin Dashboard   |
                     |                   |
                     +--------+----------+
                              |
     +----------+-------------+------------+-----------+
     |          |             |            |           |
+----v---+ +----v----+ +------v-----+ +----v----+ +----v----+
|        | |         | |            | |         | |         |
| Users  | | Tests   | | Questions  | | Reports | | Settings|
|        | |         | |            | |         | |         |
+----+---+ +----+----+ +------+-----+ +---------+ +---------+
     |          |             |
     |          |             |
+----v---+ +----v----+ +------v-----+
|        | |         | |            |
| Create | | Create  | |  Create    |
| Edit   | | Edit    | |  Edit      |
| Delete | | Delete  | |  Delete    |
+--------+ +---------+ +------------+
```

## Navigation Structure

### Main Navigation Bar

#### Public User Navigation
- **Logo** - Links to home/landing page
- **Login** - Access login page
- **Register** - Access registration page

#### User Navigation (After Login)
- **Logo** - Links to user dashboard
- **Tests** - View available tests
- **Profile** - User profile and settings
- **Notifications** - View notifications
- **Logout** - End session

#### Admin Navigation (After Login as Admin)
- **Logo** - Links to admin dashboard
- **Dashboard** - Admin overview
- **Users** - User management (dropdown)
  - All Users
  - Add New User
  - Manage Roles
- **Tests** - Test management (dropdown)
  - All Tests
  - Create New Test
  - Test Categories
- **Questions** - Question bank (dropdown)
  - Question Bank
  - Add Question
  - Import Questions
  - Categories
- **Reports** - Analytics and reporting
- **Settings** - System configuration (dropdown)
  - General Settings
  - Email Templates
  - Security
  - Backup & Restore
- **Quick Actions** - Common tasks (dropdown)
  - Create Test
  - Add User
  - Add Question
  - Generate Report
- **User Menu** - User-specific options (dropdown)
  - My Profile
  - Admin Dashboard
  - Logout

## Navigation Paths

### Public User Flow
1. Landing Page → Register → User Dashboard → Tests List → Take Test → Test Results
2. Landing Page → Login → User Dashboard → Profile Settings
3. Landing Page → Forgot Password → Login → User Dashboard

### Admin User Flow
1. Landing Page → Login → Admin Dashboard → Users → All Users/Add User/Manage Roles
2. Landing Page → Login → Admin Dashboard → Tests → All Tests/Create Test/Categories
3. Landing Page → Login → Admin Dashboard → Questions → Question Bank/Add Question/Import/Categories
4. Landing Page → Login → Admin Dashboard → Reports → View Reports/Generate Reports
5. Landing Page → Login → Admin Dashboard → Settings → General/Email/Security/Backup

## Access Control

### Public Pages (No Authentication Required)
- Landing Page
- Login
- Register
- Forgot Password
- Reset Password

### User Pages (Authentication Required)
- User Dashboard
- Tests List
- Take Test
- Test Results
- Profile Settings
- Notifications

### Admin Pages (Admin Authentication Required)
- Admin Dashboard
- Users Management
  - User List
  - Add/Edit User
  - Role Management
- Tests Management
  - Test List
  - Create/Edit Test
  - Test Categories
- Questions Management
  - Question Bank
  - Add/Edit Question
  - Import Questions
  - Question Categories
- Reports
  - User Performance
  - Test Statistics
  - System Usage
- Settings
  - General Settings
  - Email Templates
  - Security Settings
  - Backup & Restore

## Mobile Navigation

### Mobile User Navigation
- Bottom navigation bar with:
  - Home/Dashboard
  - Tests
  - Profile
  - Notifications

### Mobile Admin Navigation
- Hamburger menu that includes:
  - Dashboard
  - Users
  - Tests
  - Questions
  - Reports
  - Settings
  - Profile
  - Logout

## URL Structure

### Authentication URLs
- `/login` - Login page
- `/register` - Registration page
- `/forgot-password` - Password recovery
- `/reset-password/:token` - Password reset
- `/logout` - Logout

### User URLs
- `/` - User dashboard (when logged in)
- `/tests` - Tests list
- `/tests/take/:id` - Take a test
- `/tests/results/:id` - View test results
- `/profile` - User profile
- `/notifications` - User notifications

### Admin URLs
- `/admin/dashboard` - Admin dashboard
- `/admin/users` - User management
- `/admin/users/add` - Add new user
- `/admin/users/:id/edit` - Edit user
- `/admin/users/roles` - Role management
- `/admin/tests` - Test management
- `/admin/tests/add` - Create new test
- `/admin/tests/:id/edit` - Edit test
- `/admin/tests/categories` - Test categories
- `/admin/questions` - Question bank
- `/admin/questions/add` - Add question
- `/admin/questions/:id/edit` - Edit question
- `/admin/questions/import` - Import questions
- `/admin/questions/categories` - Question categories
- `/admin/reports` - Reports dashboard
- `/admin/reports/users` - User performance reports
- `/admin/reports/tests` - Test statistics
- `/admin/reports/system` - System usage
- `/admin/settings` - Settings dashboard
- `/admin/settings/general` - General settings
- `/admin/settings/email` - Email templates
- `/admin/settings/security` - Security settings
- `/admin/settings/backup` - Backup & restore

## Implementation Notes

1. The admin navbar should be implemented as a reusable component in `views/partials/admin-navbar.ejs`
2. Use the admin layout template for all admin pages in `views/layouts/admin.ejs`
3. Implement proper access control using the `checkAdmin` middleware
4. Use breadcrumbs for better navigation in admin pages
5. Highlight the active page in the navigation
6. Ensure responsive design for all navigation elements 