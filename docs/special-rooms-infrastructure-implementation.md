# Special Rooms Infrastructure Implementation

This document describes the implementation of separate equipment tracking and display for library, labs, staff rooms, and offices in the principal infrastructure view.

## Overview

The principal infrastructure view now displays specialized facilities with their specific equipment in dedicated sections, providing a comprehensive overview of all institutional resources.

## Implementation Summary

### 🏗️ **Database Structure Enhanced**

1. **Room Mappings Created:**
   - Computer Lab 1 (ID: 39) - 20 equipment items
   - Computer Lab 2 (ID: 40) - 22 equipment items
   - Biology Lab (ID: 41) - 3 equipment items
   - Chemistry Lab (ID: 42) - 3 equipment items
   - Physics Lab (ID: 43) - 3 equipment items
   - Library (ID: 44) - 3 equipment items
   - Principal Office (ID: 45) - 4 equipment items
   - Vice Principal Office (ID: 46) - 1 equipment item
   - Admin Office (ID: 47) - 4 equipment items
   - Computer Office (ID: 48) - 3 equipment items
   - Warden Office (ID: 49) - 2 equipment items

2. **Foreign Key Relationships:**
   - `inventory_items.room_id → rooms.id`
   - `it_inventory.room_id → rooms.id`
   - `electrical_inventory.room_id → rooms.id`

### 🎨 **Principal Infrastructure View Sections**

#### **1. Computer Labs Section**
- **Color Scheme:** Blue gradient (blue-600 to indigo-600)
- **Icon:** Desktop computer
- **Equipment Displayed:**
  - Desktop computers (ACER VERITON)
  - Printers (HP LaserJet)
  - UPS units (MICROTEK, UNILINE)
  - Projectors (HITACHI, BENQ)

**Computer Lab 1:**
- 18 Desktop computers
- 1 Printer
- 1 UPS unit
- Capacity: 30 students

**Computer Lab 2:**
- 19 Desktop computers
- 1 Printer
- 1 UPS unit
- Capacity: 30 students

#### **2. Science Labs Section**
- **Color Scheme:** Green gradient (green-600 to emerald-600)
- **Icon:** Flask
- **Equipment Displayed:**
  - Projectors for presentations
  - Desktop computers for data analysis
  - UPS units for power backup
  - Lab-specific equipment

**Each Science Lab (Biology, Chemistry, Physics):**
- 1 HITACHI Projector
- 1 Desktop computer
- 1 UPS unit
- Capacity: 25 students each

#### **3. Library Section**
- **Color Scheme:** Purple gradient (purple-600 to violet-600)
- **Icon:** Book
- **Equipment Displayed:**
  - Desktop computer for catalog management
  - HP LaserJet printer for document services
  - UPS unit for power backup
  - Capacity: 50 students

#### **4. Administrative Offices Section**
- **Color Scheme:** Amber gradient (amber-600 to orange-600)
- **Icon:** Building/User-tie
- **Equipment Displayed:**
  - Desktop computers
  - Laptops (where applicable)
  - Printers
  - Tablets (Principal Office)

**Office Equipment Distribution:**
- **Principal Office:** 1 Desktop, 1 Printer, 1 Tablet
- **Vice Principal Office:** 1 Desktop
- **Admin Office:** 1 Desktop, 2 Printers
- **Computer Office:** 3 Desktops
- **Warden Office:** 2 Desktops

### 🔧 **Technical Implementation**

#### **Controller Updates (principal-controller.js):**
```javascript
// Computer Labs Query
const [computerLabs] = await db.query(`
  SELECT r.id, r.room_number, r.capacity,
         COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' THEN i.item_id END) as desktops,
         COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' THEN i.item_id END) as printers,
         COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
  FROM rooms r LEFT JOIN inventory_items i ON r.id = i.room_id
  WHERE r.room_number LIKE 'Computer Lab%'
  GROUP BY r.id ORDER BY r.room_number
`);

// Similar queries for Science Labs, Library, and Offices
```

#### **View Updates (infrastructure.ejs):**
- Added 4 new sections with responsive grid layout
- Color-coded cards with appropriate icons
- Equipment type indicators with counts
- Hover effects and click handlers for detailed modals
- Mobile-responsive design

### 🎯 **Features Implemented**

1. **Separate Facility Sections:**
   - Computer Labs (2 labs)
   - Science Labs (3 labs)
   - Library (1 facility)
   - Administrative Offices (5 offices)

2. **Equipment Type Indicators:**
   - Desktop computers with PC icon
   - Printers with print icon
   - UPS units with battery icon
   - Projectors with video icon
   - Tablets with tablet icon
   - Laptops with laptop icon

3. **Interactive Elements:**
   - Clickable cards for detailed equipment view
   - Hover effects with shadow transitions
   - Equipment count badges
   - Status indicators

4. **Responsive Design:**
   - Grid layout adapts to screen size
   - Mobile-friendly card design
   - Proper spacing and typography
   - Touch-friendly interface

### 📊 **Equipment Summary**

| Facility Type | Count | Total Equipment | Key Equipment |
|---------------|-------|-----------------|---------------|
| Computer Labs | 2 | 42 items | 37 Desktops, 2 Printers, 2 UPS |
| Science Labs | 3 | 9 items | 3 Projectors, 3 Desktops, 3 UPS |
| Library | 1 | 3 items | 1 Desktop, 1 Printer, 1 UPS |
| Offices | 5 | 14 items | 8 Desktops, 3 Printers, 1 Tablet |
| **Total** | **11** | **68 items** | **Specialized Equipment** |

### 🔗 **Modal Integration**

All facility cards are clickable and integrate with the existing classroom modal system:
- Same modal structure and styling
- Equipment details with specifications
- Serial numbers and manufacturer information
- Status and condition tracking
- Electrical equipment breakdown

### 🎨 **Visual Design**

1. **Color Coding:**
   - Blue: Computer Labs (technology focus)
   - Green: Science Labs (research focus)
   - Purple: Library (knowledge focus)
   - Amber: Offices (administrative focus)

2. **Icons:**
   - Desktop: Computer Labs
   - Flask: Science Labs
   - Book: Library
   - Building/User-tie: Offices

3. **Layout:**
   - 2-column grid on desktop
   - Single column on mobile
   - Consistent card design
   - Professional spacing

### 🚀 **Benefits**

1. **Comprehensive Overview:** Principal can see all facility types at a glance
2. **Equipment Tracking:** Detailed equipment counts and types for each facility
3. **Resource Planning:** Easy identification of equipment distribution and needs
4. **Maintenance Management:** Quick access to equipment details for maintenance scheduling
5. **Budget Planning:** Clear visibility of equipment inventory for budget allocation

### 📱 **User Experience**

- **Intuitive Navigation:** Clear sections with appropriate icons and colors
- **Quick Access:** Equipment counts visible without clicking
- **Detailed View:** Click any facility for complete equipment breakdown
- **Mobile Friendly:** Responsive design works on all devices
- **Professional Look:** Consistent with principal dashboard theme

## Conclusion

The infrastructure view now provides a comprehensive overview of all institutional facilities with their specific equipment, enabling effective resource management and strategic planning for the principal.
