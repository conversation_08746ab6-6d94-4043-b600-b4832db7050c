const db = require('./config/database');

async function findComputerTeacher() {
  try {
    console.log('Searching for computer science teachers...');
    
    // First, find teachers who teach computer science subjects
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.username, u.full_name, u.email, u.role
      FROM users u
      JOIN teacher_subjects ts ON u.id = ts.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      WHERE u.role = 'teacher' 
      AND (
        s.name LIKE '%computer%' 
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `);
    
    if (computerTeachers.length === 0) {
      console.log('No computer science teachers found through subject assignments');
      
      // Try to find any teacher with "computer" in their details
      const [potentialTeachers] = await db.query(`
        SELECT id, username, full_name, email, role
        FROM users
        WHERE role = 'teacher'
        AND (
          full_name LIKE '%computer%' 
          OR username LIKE '%comp%'
          OR email LIKE '%comp%'
        )
      `);
      
      if (potentialTeachers.length === 0) {
        console.log('No potential computer science teachers found');
        
        // List all teachers as a fallback
        console.log('\nListing all teachers:');
        const [allTeachers] = await db.query(`
          SELECT id, username, full_name, email
          FROM users
          WHERE role = 'teacher'
          LIMIT 10
        `);
        
        if (allTeachers.length === 0) {
          console.log('No teachers found in the database');
        } else {
          console.table(allTeachers);
        }
      } else {
        console.log('Potential computer science teachers found:');
        console.table(potentialTeachers);
      }
    } else {
      console.log('Computer science teachers found:');
      console.table(computerTeachers);
      
      // For each computer teacher, show their subjects
      for (const teacher of computerTeachers) {
        console.log(`\nSubjects taught by ${teacher.full_name} (ID: ${teacher.id}):`);
        
        const [subjects] = await db.query(`
          SELECT s.id, s.name, s.code
          FROM teacher_subjects ts
          JOIN subjects s ON ts.subject_id = s.id
          WHERE ts.teacher_id = ?
        `, [teacher.id]);
        
        if (subjects.length > 0) {
          console.table(subjects);
        } else {
          console.log('No subjects found');
        }
        
        // Show classes taught
        console.log(`\nClasses taught by ${teacher.full_name} (ID: ${teacher.id}):`);
        
        const [classes] = await db.query(`
          SELECT c.id, c.name, c.grade, c.trade, c.section, cl.room_number
          FROM teacher_classes tc
          JOIN classrooms cl ON tc.classroom_id = cl.id
          JOIN classes c ON cl.class_id = c.id
          WHERE tc.teacher_id = ?
        `, [teacher.id]);
        
        if (classes.length > 0) {
          console.table(classes);
        } else {
          console.log('No classes found through teacher_classes table');
        }
      }
    }
    
    // Also check for computer science subjects in the database
    console.log('\nComputer Science subjects in the database:');
    const [csSubjects] = await db.query(`
      SELECT id, name, code, description
      FROM subjects
      WHERE name LIKE '%computer%' 
      OR name LIKE '%Computer%'
      OR code LIKE '%CS%'
      OR code LIKE '%comp%'
    `);
    
    if (csSubjects.length === 0) {
      console.log('No computer science subjects found');
    } else {
      console.table(csSubjects);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
findComputerTeacher();
