/**
 * <PERSON><PERSON>t to find references to the c.name column in the codebase
 */

const fs = require('fs');
const path = require('path');
const util = require('util');

const readdir = util.promisify(fs.readdir);
const readFile = util.promisify(fs.readFile);
const stat = util.promisify(fs.stat);

async function findReferences(dir, pattern) {
  const files = await readdir(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);
    
    if (stats.isDirectory()) {
      // Skip node_modules and .git directories
      if (file !== 'node_modules' && file !== '.git') {
        await findReferences(filePath, pattern);
      }
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ejs'))) {
      try {
        const content = await readFile(filePath, 'utf8');
        
        if (content.includes(pattern)) {
          console.log(`Found reference in ${filePath}:`);
          
          // Extract lines containing the pattern
          const lines = content.split('\n');
          lines.forEach((line, index) => {
            if (line.includes(pattern)) {
              console.log(`  Line ${index + 1}: ${line.trim()}`);
            }
          });
          
          console.log('');
        }
      } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
      }
    }
  }
}

async function main() {
  try {
    console.log('Searching for references to c.name in the codebase...');
    
    // Search for c.name in SQL queries
    await findReferences('.', 'c.name');
    
    console.log('Search completed');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
