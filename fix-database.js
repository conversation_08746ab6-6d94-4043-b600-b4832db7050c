const db = require('./config/database');

async function fixDatabase() {
  try {
    console.log('=== Fixing Database Issues ===\n');

    // 1. Fix demo teacher assignments
    console.log('=== Fixing Demo Teacher Assignments ===');
    
    // Get demo teacher
    const [demoTeacher] = await db.query(`
      SELECT id, username, name, email 
      FROM users 
      WHERE role = 'teacher' AND (email = '<EMAIL>' OR username = 'csteacher')
      LIMIT 1
    `);
    
    if (demoTeacher.length > 0) {
      const teacherId = demoTeacher[0].id;
      console.log(`Found demo teacher: ${teacherId} - ${demoTeacher[0].username} (${demoTeacher[0].name})`);
      
      // Get required classes (12 NON-MEDICAL B/E, 12 COMMERCE A, and 12 MEDICAL A)
      const [requiredClasses] = await db.query(`
        SELECT c.id, c.name, c.grade, c.trade, c.section, cr.id as classroom_id
        FROM classes c
        JOIN classrooms cr ON c.id = cr.class_id
        WHERE 
          (c.grade = '12' AND c.trade = 'Non-Medical' AND (c.section = 'A' OR c.section = 'B')) OR
          (c.grade = '12' AND c.trade = 'Commerce' AND c.section = 'A') OR
          (c.grade = '12' AND c.trade = 'Medical' AND c.section = 'A')
        LIMIT 10
      `);
      
      console.log(`Found ${requiredClasses.length} required classes for demo teacher`);
      
      // Assign teacher to these classes
      for (const cls of requiredClasses) {
        // Check if assignment already exists
        const [existingAssignment] = await db.query(`
          SELECT id FROM teacher_classes 
          WHERE teacher_id = ? AND classroom_id = ?
        `, [teacherId, cls.classroom_id]);
        
        if (existingAssignment.length === 0) {
          // Create new assignment
          await db.query(`
            INSERT INTO teacher_classes (teacher_id, classroom_id, created_at, updated_at)
            VALUES (?, ?, NOW(), NOW())
          `, [teacherId, cls.classroom_id]);
          
          console.log(`✅ Assigned teacher to class: ${cls.name} (${cls.grade} ${cls.trade} ${cls.section})`);
        } else {
          console.log(`⚠️ Teacher already assigned to class: ${cls.name} (${cls.grade} ${cls.trade} ${cls.section})`);
        }
      }
      
      // Assign teacher to subjects (Computer Science)
      const [csSubjects] = await db.query(`
        SELECT id, name FROM subjects WHERE name LIKE '%Computer Science%' OR name LIKE '%CS%'
      `);
      
      console.log(`Found ${csSubjects.length} Computer Science subjects`);
      
      for (const subject of csSubjects) {
        // Check if assignment already exists
        const [existingAssignment] = await db.query(`
          SELECT id FROM teacher_subjects 
          WHERE teacher_id = ? AND subject_id = ?
        `, [teacherId, subject.id]);
        
        if (existingAssignment.length === 0) {
          // Create new assignment
          await db.query(`
            INSERT INTO teacher_subjects (teacher_id, subject_id, created_at, updated_at)
            VALUES (?, ?, NOW(), NOW())
          `, [teacherId, subject.id]);
          
          console.log(`✅ Assigned teacher to subject: ${subject.name}`);
        } else {
          console.log(`⚠️ Teacher already assigned to subject: ${subject.name}`);
        }
      }
      
      // Make teacher a class incharge for one of the classes
      if (requiredClasses.length > 0) {
        const classId = requiredClasses[0].id;
        
        // Check if class already has an incharge
        const [existingIncharge] = await db.query(`
          SELECT id, teacher_id FROM class_incharge WHERE class_id = ?
        `, [classId]);
        
        if (existingIncharge.length === 0) {
          // Assign as class incharge
          await db.query(`
            INSERT INTO class_incharge (class_id, teacher_id, created_at, updated_at)
            VALUES (?, ?, NOW(), NOW())
          `, [classId, teacherId]);
          
          console.log(`✅ Made teacher the incharge for class ID ${classId}`);
        } else {
          // Update existing incharge
          await db.query(`
            UPDATE class_incharge SET teacher_id = ? WHERE class_id = ?
          `, [teacherId, classId]);
          
          console.log(`✅ Updated class incharge for class ID ${classId} from teacher ${existingIncharge[0].teacher_id} to ${teacherId}`);
        }
      }
    } else {
      console.log('❌ Demo teacher not found');
    }
    
    // 2. Fix subject-class assignments
    console.log('\n=== Fixing Subject-Class Assignments ===');
    
    // Get all classes
    const [classes] = await db.query(`
      SELECT id, name, grade, trade, section FROM classes
      WHERE grade IS NOT NULL AND trade IS NOT NULL
    `);
    
    console.log(`Found ${classes.length} classes to check for subject assignments`);
    
    // Get Computer Science subjects
    const [csSubjects] = await db.query(`
      SELECT id, name FROM subjects WHERE name LIKE '%Computer Science%' OR name LIKE '%CS%'
    `);
    
    if (csSubjects.length > 0) {
      const csSubjectId = csSubjects[0].id;
      console.log(`Using Computer Science subject ID: ${csSubjectId}`);
      
      // Assign CS subject to all classes
      for (const cls of classes) {
        // Check if assignment already exists
        const [existingAssignment] = await db.query(`
          SELECT id FROM subject_class_assignment 
          WHERE class_id = ? AND subject_id = ?
        `, [cls.id, csSubjectId]);
        
        if (existingAssignment.length === 0) {
          // Create new assignment with lecture counts
          await db.query(`
            INSERT INTO subject_class_assignment 
            (class_id, subject_id, num_theory_lectures, num_practical_lectures, created_at, updated_at)
            VALUES (?, ?, 6, 3, NOW(), NOW())
          `, [cls.id, csSubjectId]);
          
          console.log(`✅ Assigned CS subject to class: ${cls.name} (${cls.grade} ${cls.trade} ${cls.section})`);
        } else {
          // Update existing assignment
          await db.query(`
            UPDATE subject_class_assignment 
            SET num_theory_lectures = 6, num_practical_lectures = 3, updated_at = NOW()
            WHERE id = ?
          `, [existingAssignment[0].id]);
          
          console.log(`⚠️ Updated CS subject assignment for class: ${cls.name}`);
        }
      }
    } else {
      console.log('❌ No Computer Science subjects found');
    }
    
    // 3. Create missing tables if needed
    console.log('\n=== Creating Missing Tables ===');
    
    // Check if student_assignments table exists
    const [studentAssignmentsExists] = await db.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'student_assignments'
    `);
    
    if (studentAssignmentsExists[0].table_exists === 0) {
      console.log('Creating student_assignments table...');
      
      await db.query(`
        CREATE TABLE student_assignments (
          id INT PRIMARY KEY AUTO_INCREMENT,
          student_id INT NOT NULL,
          assignment_id INT NOT NULL,
          status ENUM('pending', 'submitted', 'graded') DEFAULT 'pending',
          submission_date DATETIME,
          grade DECIMAL(5,2),
          feedback TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ student_assignments table created');
    } else {
      console.log('⚠️ student_assignments table already exists');
    }
    
    // Check if student_test_attempts table exists
    const [studentTestAttemptsExists] = await db.query(`
      SELECT COUNT(*) as table_exists 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name = 'student_test_attempts'
    `);
    
    if (studentTestAttemptsExists[0].table_exists === 0) {
      console.log('Creating student_test_attempts table...');
      
      await db.query(`
        CREATE TABLE student_test_attempts (
          id INT PRIMARY KEY AUTO_INCREMENT,
          student_id INT NOT NULL,
          test_id INT NOT NULL,
          attempt_number INT DEFAULT 1,
          start_time DATETIME,
          end_time DATETIME,
          score DECIMAL(5,2),
          max_score DECIMAL(5,2),
          status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('✅ student_test_attempts table created');
    } else {
      console.log('⚠️ student_test_attempts table already exists');
    }
    
    console.log('\n=== Database Fixes Completed ===');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing database:', error);
    process.exit(1);
  }
}

fixDatabase();
