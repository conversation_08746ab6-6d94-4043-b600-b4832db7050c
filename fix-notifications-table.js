/**
 * <PERSON><PERSON><PERSON> to fix the notifications table by adding the missing is_broadcast column
 */

const db = require('./config/database');

async function fixNotificationsTable() {
    try {
        console.log('Checking notifications table structure...');
        
        // Check if the is_broadcast column exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'notifications' 
            AND COLUMN_NAME = 'is_broadcast'
        `);
        
        if (columns.length === 0) {
            console.log('Adding is_broadcast column to notifications table...');
            
            // Add the is_broadcast column
            await db.query(`
                ALTER TABLE notifications 
                ADD COLUMN is_broadcast BOOLEAN DEFAULT FALSE
            `);
            
            console.log('✅ Successfully added is_broadcast column to notifications table');
        } else {
            console.log('✅ is_broadcast column already exists in notifications table');
        }

        // Check if the link column exists
        const [linkColumns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'notifications' 
            AND COLUMN_NAME = 'link'
        `);
        
        if (linkColumns.length === 0) {
            console.log('Adding link column to notifications table...');
            
            // Add the link column
            await db.query(`
                ALTER TABLE notifications 
                ADD COLUMN link VARCHAR(255) DEFAULT NULL
            `);
            
            console.log('✅ Successfully added link column to notifications table');
        } else {
            console.log('✅ link column already exists in notifications table');
        }

        // Check if the title column exists
        const [titleColumns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'notifications' 
            AND COLUMN_NAME = 'title'
        `);
        
        if (titleColumns.length === 0) {
            console.log('Adding title column to notifications table...');
            
            // Add the title column
            await db.query(`
                ALTER TABLE notifications 
                ADD COLUMN title VARCHAR(255) DEFAULT NULL
            `);
            
            console.log('✅ Successfully added title column to notifications table');
        } else {
            console.log('✅ title column already exists in notifications table');
        }

        console.log('Notifications table structure has been fixed!');
        
    } catch (error) {
        console.error('Error fixing notifications table:', error);
    } finally {
        process.exit(0);
    }
}

// Run the function
fixNotificationsTable();
