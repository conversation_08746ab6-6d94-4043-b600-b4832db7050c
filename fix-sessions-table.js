/**
 * <PERSON><PERSON><PERSON> to fix the active_sessions table
 */

const db = require('./config/database');

async function fixSessionsTable() {
  try {
    console.log('Fixing active_sessions table...');
    
    // Check if active_sessions table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
    `);
    
    if (tableExists[0].table_exists > 0) {
      console.log('active_sessions table exists, dropping it...');
      
      // Drop the table
      await db.query(`
        DROP TABLE active_sessions
      `);
      
      console.log('active_sessions table dropped');
    }
    
    // Create the table with the correct structure
    console.log('Creating active_sessions table with correct structure...');
    
    await db.query(`
      CREATE TABLE active_sessions (
        session_id VARCHAR(128) COLLATE utf8mb4_bin NOT NULL,
        expires INT(11) UNSIGNED NOT NULL,
        data MEDIUMTEXT COLLATE utf8mb4_bin,
        PRIMARY KEY (session_id)
      )
    `);
    
    console.log('active_sessions table created successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Error fixing sessions table:', error);
    process.exit(1);
  }
}

// Run the function
fixSessionsTable();
