const db = require('./config/database');

async function fixStudentTeacherAssignments() {
  try {
    console.log('=== Fixing Student and Teacher Assignments ===\n');

    // 1. Fix teacher class assignments
    console.log('=== Fixing Teacher Class Assignments ===');
    
    // Get all teachers without class assignments
    const [teachersWithoutClasses] = await db.query(`
      SELECT 
        u.id, 
        u.name,
        u.email,
        SUBSTRING_INDEX(u.name, '_', 1) as subject_area
      FROM users u
      LEFT JOIN teacher_classes tc ON u.id = tc.teacher_id
      WHERE u.role = 'teacher' AND tc.id IS NULL
      ORDER BY u.id
    `);
    
    console.log(`Found ${teachersWithoutClasses.length} teachers without class assignments`);
    
    // Get all classrooms
    const [classrooms] = await db.query(`
      SELECT 
        cr.id as classroom_id,
        c.id as class_id,
        c.name as class_name,
        c.grade,
        c.trade,
        c.section,
        cr.session
      FROM classrooms cr
      JOIN classes c ON cr.class_id = c.id
      ORDER BY cr.session, c.grade, c.trade, c.section
    `);
    
    console.log(`Found ${classrooms.length} classrooms to assign teachers to`);
    
    // Get all subjects
    const [subjects] = await db.query(`
      SELECT id, name, code FROM subjects ORDER BY name
    `);
    
    console.log(`Found ${subjects.length} subjects`);
    
    // Assign teachers to classes based on their subject area
    let assignmentCount = 0;
    
    for (const teacher of teachersWithoutClasses) {
      // Find matching subjects for this teacher
      const matchingSubjects = subjects.filter(subject => {
        const subjectName = subject.name.toLowerCase();
        const teacherSubject = teacher.subject_area.toLowerCase();
        return subjectName.includes(teacherSubject) || 
               (teacherSubject === 'cs' && subjectName.includes('computer')) ||
               (teacherSubject === 'math' && subjectName.includes('mathematics'));
      });
      
      if (matchingSubjects.length > 0) {
        console.log(`Teacher ${teacher.id} (${teacher.name}) matches subjects: ${matchingSubjects.map(s => s.name).join(', ')}`);
        
        // Find classrooms to assign this teacher to
        const classroomsToAssign = [];
        
        // Assign each teacher to 2-3 classrooms
        const maxAssignments = 2 + Math.floor(Math.random() * 2); // 2 or 3
        
        for (let i = 0; i < classrooms.length && classroomsToAssign.length < maxAssignments; i++) {
          const classroom = classrooms[i];
          
          // Check if this classroom has the subject assigned
          const [subjectAssigned] = await db.query(`
            SELECT COUNT(*) as count
            FROM subject_class_assignment
            WHERE class_id = ? AND subject_id IN (?)
          `, [classroom.class_id, matchingSubjects.map(s => s.id)]);
          
          if (subjectAssigned[0].count > 0) {
            classroomsToAssign.push(classroom);
          }
        }
        
        // Assign teacher to these classrooms
        for (const classroom of classroomsToAssign) {
          // Check if assignment already exists
          const [existingAssignment] = await db.query(`
            SELECT id FROM teacher_classes 
            WHERE teacher_id = ? AND classroom_id = ?
          `, [teacher.id, classroom.classroom_id]);
          
          if (existingAssignment.length === 0) {
            // Create new assignment
            await db.query(`
              INSERT INTO teacher_classes (teacher_id, classroom_id, created_at, updated_at)
              VALUES (?, ?, NOW(), NOW())
            `, [teacher.id, classroom.classroom_id]);
            
            console.log(`✅ Assigned teacher ${teacher.id} (${teacher.name}) to class: ${classroom.class_name} (${classroom.grade} ${classroom.trade} ${classroom.section}) - Session: ${classroom.session}`);
            assignmentCount++;
            
            // Also assign teacher to the subject
            for (const subject of matchingSubjects) {
              const [existingSubjectAssignment] = await db.query(`
                SELECT id FROM teacher_subjects 
                WHERE teacher_id = ? AND subject_id = ?
              `, [teacher.id, subject.id]);
              
              if (existingSubjectAssignment.length === 0) {
                await db.query(`
                  INSERT INTO teacher_subjects (teacher_id, subject_id, created_at, updated_at)
                  VALUES (?, ?, NOW(), NOW())
                `, [teacher.id, subject.id]);
                
                console.log(`✅ Assigned teacher ${teacher.id} (${teacher.name}) to subject: ${subject.name}`);
              }
            }
          }
        }
      } else {
        console.log(`⚠️ No matching subjects found for teacher ${teacher.id} (${teacher.name})`);
      }
    }
    
    console.log(`Created ${assignmentCount} new teacher-class assignments`);
    
    // 2. Fix demo teacher's duplicate assignments
    console.log('\n=== Fixing Demo Teacher Duplicate Assignments ===');
    
    // Get demo teacher
    const [demoTeacher] = await db.query(`
      SELECT id, username, name FROM users WHERE username = 'csteacher' AND role = 'teacher'
    `);
    
    if (demoTeacher.length > 0) {
      const teacherId = demoTeacher[0].id;
      
      // Remove duplicate teacher_classes entries
      await db.query(`
        DELETE t1 FROM teacher_classes t1
        INNER JOIN teacher_classes t2 
        WHERE 
          t1.id > t2.id AND 
          t1.teacher_id = t2.teacher_id AND 
          t1.classroom_id = t2.classroom_id AND
          t1.teacher_id = ?
      `, [teacherId]);
      
      console.log(`✅ Removed duplicate class assignments for demo teacher`);
      
      // Ensure demo teacher has the required classes
      const requiredClasses = [
        { grade: '12', trade: 'Non-Medical', section: 'A' },
        { grade: '12', trade: 'Non-Medical', section: 'B' },
        { grade: '12', trade: 'Commerce', section: 'A' },
        { grade: '12', trade: 'Medical', section: 'A' }
      ];
      
      for (const reqClass of requiredClasses) {
        // Find matching classroom
        const [classroom] = await db.query(`
          SELECT cr.id as classroom_id
          FROM classrooms cr
          JOIN classes c ON cr.class_id = c.id
          WHERE c.grade = ? AND c.trade = ? AND c.section = ?
          LIMIT 1
        `, [reqClass.grade, reqClass.trade, reqClass.section]);
        
        if (classroom.length > 0) {
          // Check if assignment already exists
          const [existingAssignment] = await db.query(`
            SELECT id FROM teacher_classes 
            WHERE teacher_id = ? AND classroom_id = ?
          `, [teacherId, classroom[0].classroom_id]);
          
          if (existingAssignment.length === 0) {
            // Create new assignment
            await db.query(`
              INSERT INTO teacher_classes (teacher_id, classroom_id, created_at, updated_at)
              VALUES (?, ?, NOW(), NOW())
            `, [teacherId, classroom[0].classroom_id]);
            
            console.log(`✅ Assigned demo teacher to required class: ${reqClass.grade} ${reqClass.trade} ${reqClass.section}`);
          }
        } else {
          console.log(`⚠️ Could not find classroom for ${reqClass.grade} ${reqClass.trade} ${reqClass.section}`);
        }
      }
    } else {
      console.log('❌ Demo teacher not found');
    }
    
    // 3. Fix demo student's subject assignments
    console.log('\n=== Fixing Demo Student Subject Assignments ===');
    
    // Get demo student
    const [demoStudent] = await db.query(`
      SELECT id, username, name FROM users WHERE username = 'csstudent' AND role = 'student'
    `);
    
    if (demoStudent.length > 0) {
      const studentId = demoStudent[0].id;
      
      // Get student's class
      const [studentClass] = await db.query(`
        SELECT 
          c.id as class_id,
          c.name as class_name,
          c.grade,
          c.trade,
          c.section
        FROM student_classrooms sc
        JOIN classrooms cr ON sc.classroom_id = cr.id
        JOIN classes c ON cr.class_id = c.id
        WHERE sc.student_id = ?
        LIMIT 1
      `, [studentId]);
      
      if (studentClass.length > 0) {
        const classId = studentClass[0].class_id;
        const className = studentClass[0].class_name;
        const classTrade = studentClass[0].trade;
        
        console.log(`Demo student is in class: ${className} (${classTrade})`);
        
        // Get appropriate subjects for this class
        const [classSubjects] = await db.query(`
          SELECT 
            s.id as subject_id,
            s.name as subject_name
          FROM subject_class_assignment sca
          JOIN subjects s ON sca.subject_id = s.id
          WHERE sca.class_id = ?
        `, [classId]);
        
        console.log(`Found ${classSubjects.length} subjects for class ${className}`);
        
        // Clear existing subject assignments
        await db.query(`
          DELETE FROM student_subjects WHERE student_id = ?
        `, [studentId]);
        
        console.log(`✅ Cleared existing subject assignments for demo student`);
        
        // Get trade ID for Non-Medical
        const [tradeInfo] = await db.query(`
          SELECT id FROM trades WHERE name = 'NON MEDICAL' OR name = 'Non-Medical'
          LIMIT 1
        `);
        
        const tradeId = tradeInfo.length > 0 ? tradeInfo[0].id : null;
        
        // Assign student to these subjects
        for (const subject of classSubjects) {
          await db.query(`
            INSERT INTO student_subjects (student_id, subject_id, trade_id, created_at, updated_at)
            VALUES (?, ?, ?, NOW(), NOW())
          `, [studentId, subject.subject_id, tradeId]);
          
          console.log(`✅ Assigned demo student to subject: ${subject.subject_name}`);
        }
        
        // Add Computer Science subject if not already assigned
        const [csSubject] = await db.query(`
          SELECT id FROM subjects WHERE name LIKE '%Computer Science%' LIMIT 1
        `);
        
        if (csSubject.length > 0) {
          const [existingAssignment] = await db.query(`
            SELECT id FROM student_subjects 
            WHERE student_id = ? AND subject_id = ?
          `, [studentId, csSubject[0].id]);
          
          if (existingAssignment.length === 0) {
            await db.query(`
              INSERT INTO student_subjects (student_id, subject_id, trade_id, created_at, updated_at)
              VALUES (?, ?, ?, NOW(), NOW())
            `, [studentId, csSubject[0].id, tradeId]);
            
            console.log(`✅ Added Computer Science subject to demo student`);
          }
        }
      } else {
        console.log('❌ Demo student not assigned to any class');
      }
    } else {
      console.log('❌ Demo student not found');
    }
    
    console.log('\n=== Student and Teacher Assignment Fixes Completed ===');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing assignments:', error);
    process.exit(1);
  }
}

fixStudentTeacherAssignments();
