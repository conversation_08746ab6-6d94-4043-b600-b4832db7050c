<h2>Admin Dashboard Guide</h2>

<p>This guide explains how to use the Admin Dashboard in the Meritorious Exam Preparation Platform to manage tests, users, and system settings.</p>

<h3>Accessing the Admin Dashboard</h3>

<p>After logging in with admin credentials, you'll be automatically directed to the admin dashboard. You can also access it at any time by clicking on "Dashboard" in the main navigation menu.</p>

<h3>Dashboard Overview</h3>

<p>The Admin Dashboard provides a central hub for managing all aspects of the platform:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <h3 style="margin-top:0;">Admin Dashboard</h3>
    
    <div style="display:grid;grid-template-columns:repeat(4, 1fr);gap:15px;margin-top:15px;">
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Total Users</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">256</p>
        </div>
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Active Tests</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">18</p>
        </div>
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Test Attempts Today</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">42</p>
        </div>
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Pending Approvals</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">7</p>
        </div>
    </div>
</div>

<h3>Quick Actions</h3>

<p>The Quick Actions section provides easy access to common administrative tasks:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Quick Actions</h4>
    
    <div style="display:grid;grid-template-columns:repeat(4, 1fr);gap:15px;margin-top:15px;">
        <a href="#" style="text-decoration:none;color:inherit;">
            <div style="background-color:#e0e7ff;padding:15px;border-radius:6px;text-align:center;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:24px;height:24px;margin:0 auto 10px;color:#4338ca;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <p style="margin:0;font-weight:500;color:#4338ca;">Create Test</p>
            </div>
        </a>
        
        <a href="#" style="text-decoration:none;color:inherit;">
            <div style="background-color:#f0fff4;padding:15px;border-radius:6px;text-align:center;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:24px;height:24px;margin:0 auto 10px;color:#047857;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <p style="margin:0;font-weight:500;color:#047857;">Manage Users</p>
            </div>
        </a>
        
        <a href="#" style="text-decoration:none;color:inherit;">
            <div style="background-color:#fef3c7;padding:15px;border-radius:6px;text-align:center;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:24px;height:24px;margin:0 auto 10px;color:#b45309;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <p style="margin:0;font-weight:500;color:#b45309;">View Reports</p>
            </div>
        </a>
        
        <a href="#" style="text-decoration:none;color:inherit;">
            <div style="background-color:#fee2e2;padding:15px;border-radius:6px;text-align:center;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:24px;height:24px;margin:0 auto 10px;color:#b91c1c;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <p style="margin:0;font-weight:500;color:#b91c1c;">System Settings</p>
            </div>
        </a>
    </div>
</div>

<h3>Pending Approvals</h3>

<p>The Pending Approvals section shows items that require admin approval:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <div style="display:flex;justify-content:space-between;align-items:center;">
        <h4 style="margin:0;">Pending Approvals</h4>
        <a href="#" style="color:#4338ca;text-decoration:none;font-size:14px;">View All</a>
    </div>
    
    <div style="margin-top:15px;">
        <div style="border:1px solid #e5e7eb;border-radius:6px;overflow:hidden;">
            <table style="width:100%;border-collapse:collapse;">
                <thead>
                    <tr style="background-color:#f9fafb;text-align:left;">
                        <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Type</th>
                        <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Details</th>
                        <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Requested By</th>
                        <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Date</th>
                        <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <span style="background-color:#e0e7ff;color:#4338ca;padding:4px 8px;border-radius:4px;font-size:12px;">New User</span>
                        </td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">John Smith (<EMAIL>)</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Self</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 15, 2025</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <div style="display:flex;gap:5px;">
                                <button style="background-color:#d1fae5;color:#047857;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Approve</button>
                                <button style="background-color:#fee2e2;color:#b91c1c;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Reject</button>
                            </div>
                        </td>
                    </tr>
                    <tr style="background-color:#f9fafb;">
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <span style="background-color:#fef3c7;color:#b45309;padding:4px 8px;border-radius:4px;font-size:12px;">Test Access</span>
                        </td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Biology Mock Test (Additional Attempt)</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Sarah Johnson</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 14, 2025</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <div style="display:flex;gap:5px;">
                                <button style="background-color:#d1fae5;color:#047857;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Approve</button>
                                <button style="background-color:#fee2e2;color:#b91c1c;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Reject</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <span style="background-color:#fee2e2;color:#b91c1c;padding:4px 8px;border-radius:4px;font-size:12px;">Group Creation</span>
                        </td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Physics Study Group</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Michael Brown</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 13, 2025</td>
                        <td style="padding:10px;border-bottom:1px solid #e5e7eb;">
                            <div style="display:flex;gap:5px;">
                                <button style="background-color:#d1fae5;color:#047857;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Approve</button>
                                <button style="background-color:#fee2e2;color:#b91c1c;border:none;padding:4px 8px;border-radius:4px;font-size:12px;">Reject</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<h3>Recent Activity</h3>

<p>The Recent Activity section shows the latest actions on the platform:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <div style="display:flex;justify-content:space-between;align-items:center;">
        <h4 style="margin:0;">Recent Activity</h4>
        <a href="#" style="color:#4338ca;text-decoration:none;font-size:14px;">View All</a>
    </div>
    
    <div style="margin-top:15px;">
        <div style="display:flex;align-items:center;padding:10px;border-bottom:1px solid #e5e7eb;">
            <div style="background-color:#e0e7ff;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:15px;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:20px;height:20px;color:#4338ca;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
            </div>
            <div>
                <p style="margin:0;font-weight:500;">New test created: Physics Final Exam</p>
                <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Created by Admin (you) - April 15, 2025 - 10:45 AM</p>
            </div>
        </div>
        
        <div style="display:flex;align-items:center;padding:10px;border-bottom:1px solid #e5e7eb;">
            <div style="background-color:#f0fff4;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:15px;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:20px;height:20px;color:#047857;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div>
                <p style="margin:0;font-weight:500;">User approved: John Smith</p>
                <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Approved by Admin (you) - April 15, 2025 - 9:30 AM</p>
            </div>
        </div>
        
        <div style="display:flex;align-items:center;padding:10px;">
            <div style="background-color:#fef3c7;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:15px;">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="width:20px;height:20px;color:#b45309;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
            </div>
            <div>
                <p style="margin:0;font-weight:500;">Test assigned: Chemistry Practice Quiz</p>
                <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Assigned to 25 users by Admin (you) - April 14, 2025 - 2:15 PM</p>
            </div>
        </div>
    </div>
</div>

<h3>System Overview</h3>

<p>The System Overview section provides information about the platform's performance and usage:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">System Overview</h4>
    
    <div style="display:grid;grid-template-columns:repeat(2, 1fr);gap:20px;margin-top:15px;">
        <div>
            <h5 style="margin-top:0;">User Statistics</h5>
            <div style="display:grid;grid-template-columns:repeat(2, 1fr);gap:10px;">
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Total Users</p>
                    <p style="margin:5px 0 0;font-weight:500;">256</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Active Users</p>
                    <p style="margin:5px 0 0;font-weight:500;">187</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">New Users (30d)</p>
                    <p style="margin:5px 0 0;font-weight:500;">42</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Groups</p>
                    <p style="margin:5px 0 0;font-weight:500;">15</p>
                </div>
            </div>
        </div>
        
        <div>
            <h5 style="margin-top:0;">Test Statistics</h5>
            <div style="display:grid;grid-template-columns:repeat(2, 1fr);gap:10px;">
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Total Tests</p>
                    <p style="margin:5px 0 0;font-weight:500;">35</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Active Tests</p>
                    <p style="margin:5px 0 0;font-weight:500;">18</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Test Attempts</p>
                    <p style="margin:5px 0 0;font-weight:500;">1,245</p>
                </div>
                <div style="background-color:#f3f4f6;padding:10px;border-radius:6px;">
                    <p style="margin:0;color:#6b7280;font-size:12px;">Avg. Score</p>
                    <p style="margin:5px 0 0;font-weight:500;">72.5%</p>
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin-top:20px;">
        <h5 style="margin-top:0;">System Health</h5>
        <div style="display:grid;grid-template-columns:repeat(3, 1fr);gap:10px;">
            <div style="background-color:#d1fae5;padding:10px;border-radius:6px;">
                <p style="margin:0;color:#047857;font-size:12px;">Server Status</p>
                <p style="margin:5px 0 0;font-weight:500;color:#047857;">Operational</p>
            </div>
            <div style="background-color:#d1fae5;padding:10px;border-radius:6px;">
                <p style="margin:0;color:#047857;font-size:12px;">Database Status</p>
                <p style="margin:5px 0 0;font-weight:500;color:#047857;">Operational</p>
            </div>
            <div style="background-color:#d1fae5;padding:10px;border-radius:6px;">
                <p style="margin:0;color:#047857;font-size:12px;">Storage Usage</p>
                <p style="margin:5px 0 0;font-weight:500;color:#047857;">45% (450GB/1TB)</p>
            </div>
        </div>
    </div>
</div>

<h3>Admin Features</h3>

<h4>Test Management</h4>

<p>As an admin, you can manage all aspects of tests on the platform:</p>

<ul>
    <li><strong>Create Tests</strong>: Create new tests with multiple sections and question types</li>
    <li><strong>Edit Tests</strong>: Modify existing tests, including adding or removing questions</li>
    <li><strong>Publish Tests</strong>: Make tests available to users</li>
    <li><strong>Assign Tests</strong>: Assign tests to specific users or groups</li>
    <li><strong>View Results</strong>: Access detailed results for all test attempts</li>
</ul>

<h4>User Management</h4>

<p>The user management features allow you to:</p>

<ul>
    <li><strong>Add Users</strong>: Create new user accounts</li>
    <li><strong>Edit Users</strong>: Modify user information and permissions</li>
    <li><strong>Block/Unblock Users</strong>: Control user access to the platform</li>
    <li><strong>Manage Groups</strong>: Create and manage user groups</li>
    <li><strong>Assign Roles</strong>: Control what features users can access</li>
</ul>

<h4>Reporting</h4>

<p>The reporting features provide insights into platform usage and performance:</p>

<ul>
    <li><strong>User Reports</strong>: View detailed information about user activity</li>
    <li><strong>Test Reports</strong>: Analyze test performance across users and groups</li>
    <li><strong>System Reports</strong>: Monitor platform usage and performance</li>
    <li><strong>Export Reports</strong>: Download reports in various formats (PDF, CSV, Excel)</li>
</ul>

<h4>System Settings</h4>

<p>The system settings allow you to configure various aspects of the platform:</p>

<ul>
    <li><strong>General Settings</strong>: Configure basic platform settings</li>
    <li><strong>User Settings</strong>: Set default user preferences</li>
    <li><strong>Test Settings</strong>: Configure default test parameters</li>
    <li><strong>Security Settings</strong>: Manage security features like session timeout</li>
    <li><strong>Notification Settings</strong>: Configure system notifications</li>
</ul>

<h3>Admin Best Practices</h3>

<h4>Test Creation</h4>

<ul>
    <li>Use the multi-step form to create comprehensive tests</li>
    <li>Include clear instructions for each test</li>
    <li>Use a variety of question types to assess different skills</li>
    <li>Set appropriate time limits and passing marks</li>
    <li>Preview tests before publishing to ensure quality</li>
</ul>

<h4>User Management</h4>

<ul>
    <li>Regularly review pending user approvals</li>
    <li>Organize users into logical groups for easier management</li>
    <li>Assign appropriate roles based on user responsibilities</li>
    <li>Monitor user activity for unusual behavior</li>
    <li>Respond promptly to user access requests</li>
</ul>

<h4>System Maintenance</h4>

<ul>
    <li>Regularly check system logs for errors</li>
    <li>Monitor storage usage and performance metrics</li>
    <li>Back up important data regularly</li>
    <li>Update system settings as needed</li>
    <li>Test new features before making them available to all users</li>
</ul>

<h3>Getting Help</h3>

<p>If you need assistance with admin functions, you can:</p>

<ul>
    <li>Refer to the admin documentation</li>
    <li>Contact technical support</li>
    <li>Check the system logs for error messages</li>
    <li>Visit the admin forum for community support</li>
</ul>
