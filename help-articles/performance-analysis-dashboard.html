<h2>Using the Performance Analysis Dashboard</h2>

<p>This guide explains how to use the Performance Analysis Dashboard in the Meritorious Exam Preparation Platform to track your progress and identify areas for improvement.</p>

<h3>Accessing the Performance Dashboard</h3>

<p>You can access the Performance Analysis Dashboard by clicking on "Performance" in the main navigation menu. This dashboard provides a comprehensive overview of your test performance across all tests you've taken.</p>

<h3>Dashboard Overview</h3>

<p>The Performance Analysis Dashboard is divided into several sections, each providing different insights into your test performance:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <h3 style="margin-top:0;">Performance Analysis</h3>
    
    <div style="display:grid;grid-template-columns:repeat(3, 1fr);gap:15px;margin-top:15px;">
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Tests Taken</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">12</p>
        </div>
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Average Score</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">78.5%</p>
        </div>
        <div style="background-color:#f3f4f6;padding:15px;border-radius:6px;">
            <p style="margin:0;color:#6b7280;font-size:14px;">Time Spent</p>
            <p style="margin:5px 0 0;font-weight:500;font-size:24px;">8h 45m</p>
        </div>
    </div>
</div>

<h3>Performance Metrics</h3>

<p>The Performance Metrics section provides detailed information about your accuracy and precision across all tests:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Overall Performance Metrics</h4>
    <div style="display:grid;grid-template-columns:repeat(2, 1fr);gap:20px;margin-top:15px;">
        <div style="background-color:#f0f4ff;padding:15px;border-radius:6px;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;">
                <h5 style="margin:0;color:#4338ca;">Average Accuracy</h5>
                <span style="font-size:18px;font-weight:bold;color:#4338ca;">76.8%</span>
            </div>
            <p style="margin:0 0 10px;font-size:12px;color:#6b7280;">Percentage of all questions answered correctly across all tests</p>
            <div style="background-color:#e0e7ff;height:8px;border-radius:4px;overflow:hidden;">
                <div style="background-color:#4338ca;width:76.8%;height:100%;"></div>
            </div>
        </div>
        
        <div style="background-color:#f0fff4;padding:15px;border-radius:6px;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;">
                <h5 style="margin:0;color:#047857;">Average Precision</h5>
                <span style="font-size:18px;font-weight:bold;color:#047857;">85.2%</span>
            </div>
            <p style="margin:0 0 10px;font-size:12px;color:#6b7280;">Percentage of attempted questions answered correctly across all tests</p>
            <div style="background-color:#d1fae5;height:8px;border-radius:4px;overflow:hidden;">
                <div style="background-color:#047857;width:85.2%;height:100%;"></div>
            </div>
        </div>
    </div>
</div>

<h3>Performance Trends</h3>

<p>The Performance Trends section shows how your performance has changed over time:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Performance Trends</h4>
    
    <div style="height:200px;background-color:#f9fafb;border-radius:6px;padding:15px;position:relative;margin-top:15px;">
        <!-- This is a simplified representation of a chart -->
        <div style="position:absolute;bottom:15px;left:15px;right:15px;height:150px;">
            <!-- Chart lines -->
            <div style="position:absolute;bottom:0;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            <div style="position:absolute;bottom:50px;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            <div style="position:absolute;bottom:100px;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            <div style="position:absolute;bottom:150px;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            
            <!-- Y-axis labels -->
            <div style="position:absolute;bottom:-10px;left:-25px;font-size:10px;color:#6b7280;">0%</div>
            <div style="position:absolute;bottom:45px;left:-25px;font-size:10px;color:#6b7280;">50%</div>
            <div style="position:absolute;bottom:95px;left:-25px;font-size:10px;color:#6b7280;">100%</div>
            
            <!-- Accuracy line (blue) -->
            <svg style="position:absolute;bottom:0;left:0;width:100%;height:100%;overflow:visible;">
                <polyline points="0,50 50,60 100,55 150,70 200,65 250,75 300,80" 
                          style="fill:none;stroke:#4338ca;stroke-width:2;" />
                <circle cx="0" cy="50" r="3" style="fill:#4338ca;" />
                <circle cx="50" cy="60" r="3" style="fill:#4338ca;" />
                <circle cx="100" cy="55" r="3" style="fill:#4338ca;" />
                <circle cx="150" cy="70" r="3" style="fill:#4338ca;" />
                <circle cx="200" cy="65" r="3" style="fill:#4338ca;" />
                <circle cx="250" cy="75" r="3" style="fill:#4338ca;" />
                <circle cx="300" cy="80" r="3" style="fill:#4338ca;" />
            </svg>
            
            <!-- Precision line (green) -->
            <svg style="position:absolute;bottom:0;left:0;width:100%;height:100%;overflow:visible;">
                <polyline points="0,60 50,65 100,70 150,75 200,80 250,85 300,90" 
                          style="fill:none;stroke:#047857;stroke-width:2;" />
                <circle cx="0" cy="60" r="3" style="fill:#047857;" />
                <circle cx="50" cy="65" r="3" style="fill:#047857;" />
                <circle cx="100" cy="70" r="3" style="fill:#047857;" />
                <circle cx="150" cy="75" r="3" style="fill:#047857;" />
                <circle cx="200" cy="80" r="3" style="fill:#047857;" />
                <circle cx="250" cy="85" r="3" style="fill:#047857;" />
                <circle cx="300" cy="90" r="3" style="fill:#047857;" />
            </svg>
        </div>
        
        <!-- Legend -->
        <div style="position:absolute;top:15px;right:15px;display:flex;gap:15px;">
            <div style="display:flex;align-items:center;">
                <div style="width:12px;height:12px;background-color:#4338ca;border-radius:2px;margin-right:5px;"></div>
                <span style="font-size:12px;">Accuracy</span>
            </div>
            <div style="display:flex;align-items:center;">
                <div style="width:12px;height:12px;background-color:#047857;border-radius:2px;margin-right:5px;"></div>
                <span style="font-size:12px;">Precision</span>
            </div>
        </div>
    </div>
    
    <p style="font-size:14px;color:#6b7280;margin-top:10px;text-align:center;">Performance trends over the last 7 tests</p>
</div>

<h3>Test Comparison</h3>

<p>The Test Comparison section allows you to compare your performance across different tests:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Test Comparison</h4>
    
    <div style="height:200px;background-color:#f9fafb;border-radius:6px;padding:15px;position:relative;margin-top:15px;">
        <!-- This is a simplified representation of a bar chart -->
        <div style="position:absolute;bottom:15px;left:15px;right:15px;height:150px;">
            <!-- Chart grid lines -->
            <div style="position:absolute;bottom:0;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            <div style="position:absolute;bottom:50px;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            <div style="position:absolute;bottom:100px;left:0;width:100%;height:1px;background-color:#e5e7eb;"></div>
            
            <!-- Y-axis labels -->
            <div style="position:absolute;bottom:-10px;left:-25px;font-size:10px;color:#6b7280;">0%</div>
            <div style="position:absolute;bottom:45px;left:-25px;font-size:10px;color:#6b7280;">50%</div>
            <div style="position:absolute;bottom:95px;left:-25px;font-size:10px;color:#6b7280;">100%</div>
            
            <!-- Bars -->
            <div style="position:absolute;bottom:0;left:20px;width:30px;height:65px;background-color:#4338ca;"></div>
            <div style="position:absolute;bottom:0;left:60px;width:30px;height:75px;background-color:#4338ca;"></div>
            <div style="position:absolute;bottom:0;left:100px;width:30px;height:85px;background-color:#4338ca;"></div>
            <div style="position:absolute;bottom:0;left:140px;width:30px;height:70px;background-color:#4338ca;"></div>
            <div style="position:absolute;bottom:0;left:180px;width:30px;height:90px;background-color:#4338ca;"></div>
            
            <!-- X-axis labels -->
            <div style="position:absolute;bottom:-25px;left:20px;font-size:10px;color:#6b7280;width:30px;text-align:center;">Test 1</div>
            <div style="position:absolute;bottom:-25px;left:60px;font-size:10px;color:#6b7280;width:30px;text-align:center;">Test 2</div>
            <div style="position:absolute;bottom:-25px;left:100px;font-size:10px;color:#6b7280;width:30px;text-align:center;">Test 3</div>
            <div style="position:absolute;bottom:-25px;left:140px;font-size:10px;color:#6b7280;width:30px;text-align:center;">Test 4</div>
            <div style="position:absolute;bottom:-25px;left:180px;font-size:10px;color:#6b7280;width:30px;text-align:center;">Test 5</div>
        </div>
    </div>
    
    <p style="font-size:14px;color:#6b7280;margin-top:30px;text-align:center;">Score comparison across different tests</p>
</div>

<h3>Subject Area Analysis</h3>

<p>The Subject Area Analysis section helps you identify your strengths and weaknesses by subject area:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Subject Area Analysis</h4>
    
    <div style="margin-top:15px;">
        <div style="margin-bottom:15px;padding-bottom:15px;border-bottom:1px solid #e5e7eb;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:5px;">
                <h5 style="margin:0;">Algebra</h5>
                <span style="font-weight:500;">85%</span>
            </div>
            <div style="background-color:#e0e7ff;height:8px;border-radius:4px;overflow:hidden;">
                <div style="background-color:#4338ca;width:85%;height:100%;"></div>
            </div>
            <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Strong performance - Continue practicing to maintain proficiency</p>
        </div>
        
        <div style="margin-bottom:15px;padding-bottom:15px;border-bottom:1px solid #e5e7eb;">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:5px;">
                <h5 style="margin:0;">Geometry</h5>
                <span style="font-weight:500;">65%</span>
            </div>
            <div style="background-color:#e0e7ff;height:8px;border-radius:4px;overflow:hidden;">
                <div style="background-color:#4338ca;width:65%;height:100%;"></div>
            </div>
            <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Average performance - Focus on improving specific concepts</p>
        </div>
        
        <div>
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:5px;">
                <h5 style="margin:0;">Calculus</h5>
                <span style="font-weight:500;">45%</span>
            </div>
            <div style="background-color:#e0e7ff;height:8px;border-radius:4px;overflow:hidden;">
                <div style="background-color:#4338ca;width:45%;height:100%;"></div>
            </div>
            <p style="margin:5px 0 0;font-size:12px;color:#6b7280;">Needs improvement - Consider additional study resources</p>
        </div>
    </div>
</div>

<h3>Performance Categories</h3>

<p>The Performance Categories section groups your performance into three categories:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Performance Categories</h4>
    
    <div style="display:grid;grid-template-columns:repeat(3, 1fr);gap:15px;margin-top:15px;">
        <div style="background-color:#d1fae5;padding:15px;border-radius:6px;">
            <h5 style="margin-top:0;color:#047857;">Strong Areas</h5>
            <ul style="margin:0;padding-left:20px;color:#047857;">
                <li>Algebra</li>
                <li>Statistics</li>
                <li>Probability</li>
            </ul>
            <p style="margin:10px 0 0;font-size:12px;color:#047857;">Performance > 80%</p>
        </div>
        
        <div style="background-color:#fef3c7;padding:15px;border-radius:6px;">
            <h5 style="margin-top:0;color:#b45309;">Medium Areas</h5>
            <ul style="margin:0;padding-left:20px;color:#b45309;">
                <li>Geometry</li>
                <li>Trigonometry</li>
            </ul>
            <p style="margin:10px 0 0;font-size:12px;color:#b45309;">Performance 60-80%</p>
        </div>
        
        <div style="background-color:#fee2e2;padding:15px;border-radius:6px;">
            <h5 style="margin-top:0;color:#b91c1c;">Weak Areas</h5>
            <ul style="margin:0;padding-left:20px;color:#b91c1c;">
                <li>Calculus</li>
                <li>Vectors</li>
            </ul>
            <p style="margin:10px 0 0;font-size:12px;color:#b91c1c;">Performance < 60%</p>
        </div>
    </div>
</div>

<h3>Recent Test Attempts</h3>

<p>The Recent Test Attempts section shows your most recent test attempts with key metrics:</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#ffffff;">
    <h4 style="margin-top:0;">Recent Test Attempts</h4>
    
    <div style="margin-top:15px;border:1px solid #e5e7eb;border-radius:6px;overflow:hidden;">
        <table style="width:100%;border-collapse:collapse;">
            <thead>
                <tr style="background-color:#f9fafb;text-align:left;">
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Test Name</th>
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Date</th>
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Score</th>
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Accuracy</th>
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Precision</th>
                    <th style="padding:10px;border-bottom:1px solid #e5e7eb;">Result</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Calculus Final</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 15, 2025</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">85%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">83.33%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">90.91%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;"><span style="background-color:#d1fae5;color:#047857;padding:2px 6px;border-radius:4px;font-size:12px;">Passed</span></td>
                </tr>
                <tr style="background-color:#f9fafb;">
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Geometry Quiz</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 10, 2025</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">75%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">75.00%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">81.82%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;"><span style="background-color:#d1fae5;color:#047857;padding:2px 6px;border-radius:4px;font-size:12px;">Passed</span></td>
                </tr>
                <tr>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Algebra Test</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">Apr 5, 2025</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">90%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">90.00%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;">94.74%</td>
                    <td style="padding:10px;border-bottom:1px solid #e5e7eb;"><span style="background-color:#d1fae5;color:#047857;padding:2px 6px;border-radius:4px;font-size:12px;">Passed</span></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<h3>Using Performance Insights</h3>

<p>The Performance Analysis Dashboard provides valuable insights that can help you improve your test performance. Here's how to use these insights effectively:</p>

<h4>Identify Strengths and Weaknesses</h4>

<p>Use the Subject Area Analysis and Performance Categories sections to identify your strengths and weaknesses. Focus your study efforts on improving your weak areas while maintaining your strong areas.</p>

<h4>Track Progress Over Time</h4>

<p>Use the Performance Trends section to track your progress over time. Look for patterns and trends in your performance to identify areas where you're improving and areas where you may be struggling.</p>

<h4>Compare Test Performance</h4>

<p>Use the Test Comparison section to compare your performance across different tests. This can help you identify which types of tests or subjects you perform better in and which ones you need to focus more on.</p>

<h4>Analyze Accuracy and Precision</h4>

<p>Pay attention to your accuracy and precision metrics. If your precision is significantly higher than your accuracy, it may indicate that you're only answering questions you're confident about and leaving too many unanswered. Try to attempt more questions to improve your overall accuracy.</p>

<h4>Set Performance Goals</h4>

<p>Use the insights from the dashboard to set specific, measurable performance goals. For example, you might set a goal to improve your calculus performance from 45% to 60% within the next month.</p>

<h3>Exporting Performance Data</h3>

<p>You can export your performance data for further analysis or record-keeping. To export your data:</p>

<ol>
    <li>Click on the "Export" button in the top-right corner of the dashboard</li>
    <li>Select the format you want to export to (PDF, CSV, or Excel)</li>
    <li>Choose the date range for the data you want to export</li>
    <li>Click "Export" to download the file</li>
</ol>

<h3>Sharing Performance Reports</h3>

<p>You can share your performance reports with teachers, tutors, or study partners. To share a report:</p>

<ol>
    <li>Click on the "Share" button in the top-right corner of the dashboard</li>
    <li>Select the specific report or metrics you want to share</li>
    <li>Choose the sharing method (email, link, or print)</li>
    <li>Follow the prompts to complete the sharing process</li>
</ol>

<p>Remember that sharing your performance data can help you get valuable feedback and guidance from others, which can further improve your test performance.</p>
