<h2>Using the Test-Taking Interface</h2>

<p>This guide explains how to navigate and use the test-taking interface in the Meritorious Exam Preparation Platform.</p>

<h3>Getting Started with a Test</h3>

<p>When you access a test, you'll first see the test details page with information about the test, including:</p>

<ul>
    <li>Test name and description</li>
    <li>Duration</li>
    <li>Number of questions</li>
    <li>Number of attempts used and remaining</li>
    <li>A "Take Test" button</li>
</ul>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <div style="display:flex;align-items:center;margin-bottom:15px;">
        <div style="flex-grow:1;">
            <h3 style="margin:0;font-size:18px;">Mathematics Practice Test</h3>
            <div style="display:flex;gap:8px;margin-top:8px;">
                <span style="background-color:#f3f4f6;color:#374151;padding:4px 8px;border-radius:4px;font-size:12px;">60 minutes</span>
                <span style="background-color:#e0e7ff;color:#4338ca;padding:4px 8px;border-radius:4px;font-size:12px;">25 questions</span>
                <span style="background-color:#f0fff4;color:#047857;padding:4px 8px;border-radius:4px;font-size:12px;">1/3 attempts used</span>
            </div>
        </div>
        <button style="background-color:#4338ca;color:white;border:none;padding:8px 16px;border-radius:6px;font-weight:500;">Take Test</button>
    </div>
    <p style="margin:0;color:#6b7280;">This test covers basic and advanced mathematical concepts including algebra, geometry, and calculus.</p>
</div>

<h3>Test Instructions</h3>

<p>Before starting the test, you'll see an instructions overlay with important information:</p>

<ul>
    <li>Test rules and guidelines</li>
    <li>Time limit</li>
    <li>Scoring method</li>
    <li>Navigation instructions</li>
    <li>Continuation chances (if applicable)</li>
</ul>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <h3 style="margin-top:0;">Test Instructions</h3>
    <ul style="margin-bottom:20px;">
        <li>You have 60 minutes to complete this test</li>
        <li>There are 25 questions across 3 sections</li>
        <li>You can navigate between questions using the question palette</li>
        <li>Answered questions will appear green in the palette</li>
        <li>You can bookmark difficult questions to review later</li>
        <li>You have 2 continuation chances if you need to pause the test</li>
    </ul>
    <div style="display:flex;justify-content:space-between;">
        <button style="background-color:#f3f4f6;color:#374151;border:none;padding:8px 16px;border-radius:6px;font-weight:500;">Exit</button>
        <button style="background-color:#4338ca;color:white;border:none;padding:8px 16px;border-radius:6px;font-weight:500;">Start Test</button>
    </div>
</div>

<h3>Test-Taking Screen</h3>

<p>The test-taking screen is divided into several key areas:</p>

<ol>
    <li><strong>Header</strong>: Contains the test name, timer, and navigation controls</li>
    <li><strong>Question Area</strong>: Displays the current question and answer options</li>
    <li><strong>Question Palette</strong>: Shows all questions with their status (answered, unanswered, bookmarked)</li>
    <li><strong>Action Buttons</strong>: Includes options to clear response, bookmark, and navigate between questions</li>
</ol>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;padding-bottom:10px;border-bottom:1px solid #e5e7eb;">
        <h4 style="margin:0;">Mathematics Practice Test</h4>
        <div style="display:flex;align-items:center;gap:15px;">
            <div style="background-color:#fee2e2;color:#b91c1c;padding:4px 12px;border-radius:4px;font-weight:500;">Time: 45:32</div>
            <button style="background-color:#4338ca;color:white;border:none;padding:6px 12px;border-radius:4px;font-size:14px;">Submit Test</button>
        </div>
    </div>
    
    <div style="display:flex;gap:20px;">
        <!-- Question Area -->
        <div style="flex-grow:1;">
            <div style="margin-bottom:10px;">
                <span style="background-color:#e0e7ff;color:#4338ca;padding:2px 8px;border-radius:4px;font-size:12px;">Question 7 of 25</span>
                <span style="background-color:#f3f4f6;color:#374151;padding:2px 8px;border-radius:4px;font-size:12px;">2 marks</span>
            </div>
            
            <p style="font-weight:500;">Solve for x: 3x² + 6x - 9 = 0</p>
            
            <div style="display:flex;flex-direction:column;gap:10px;margin-top:15px;">
                <label style="display:flex;align-items:center;padding:10px;border:1px solid #e5e7eb;border-radius:6px;">
                    <input type="radio" name="q7" style="margin-right:10px;">
                    <span>x = 1, x = -3</span>
                </label>
                <label style="display:flex;align-items:center;padding:10px;border:1px solid #e5e7eb;border-radius:6px;background-color:#d1fae5;">
                    <input type="radio" name="q7" checked style="margin-right:10px;">
                    <span>x = 1, x = -3</span>
                </label>
                <label style="display:flex;align-items:center;padding:10px;border:1px solid #e5e7eb;border-radius:6px;">
                    <input type="radio" name="q7" style="margin-right:10px;">
                    <span>x = 3, x = -1</span>
                </label>
                <label style="display:flex;align-items:center;padding:10px;border:1px solid #e5e7eb;border-radius:6px;">
                    <input type="radio" name="q7" style="margin-right:10px;">
                    <span>x = -1, x = 3</span>
                </label>
            </div>
            
            <div style="display:flex;gap:10px;margin-top:20px;">
                <button style="background-color:#f3f4f6;color:#374151;border:none;padding:6px 12px;border-radius:4px;font-size:14px;">Clear Answer</button>
                <button style="background-color:#f3f4f6;color:#374151;border:none;padding:6px 12px;border-radius:4px;font-size:14px;">Bookmark</button>
            </div>
            
            <div style="display:flex;justify-content:space-between;margin-top:30px;">
                <button style="background-color:#4338ca;color:white;border:none;padding:6px 12px;border-radius:4px;font-size:14px;">Previous</button>
                <button style="background-color:#4338ca;color:white;border:none;padding:6px 12px;border-radius:4px;font-size:14px;">Next</button>
            </div>
        </div>
        
        <!-- Question Palette -->
        <div style="width:200px;border-left:1px solid #e5e7eb;padding-left:15px;">
            <h5 style="margin-top:0;margin-bottom:10px;">Question Palette</h5>
            <div style="display:grid;grid-template-columns:repeat(5, 1fr);gap:5px;">
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">1</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">2</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">3</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">4</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">5</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;">6</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#d1fae5;color:#047857;border-radius:4px;font-size:12px;border:2px solid #047857;">7</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#f3f4f6;color:#374151;border-radius:4px;font-size:12px;">8</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#f3f4f6;color:#374151;border-radius:4px;font-size:12px;">9</div>
                <div style="width:30px;height:30px;display:flex;align-items:center;justify-content:center;background-color:#f3f4f6;color:#374151;border-radius:4px;font-size:12px;">10</div>
                <!-- More question numbers would go here -->
            </div>
            
            <div style="margin-top:20px;">
                <div style="display:flex;align-items:center;gap:5px;margin-bottom:5px;font-size:12px;">
                    <div style="width:12px;height:12px;background-color:#d1fae5;border-radius:2px;"></div>
                    <span>Answered</span>
                </div>
                <div style="display:flex;align-items:center;gap:5px;margin-bottom:5px;font-size:12px;">
                    <div style="width:12px;height:12px;background-color:#f3f4f6;border-radius:2px;"></div>
                    <span>Not Answered</span>
                </div>
                <div style="display:flex;align-items:center;gap:5px;font-size:12px;">
                    <div style="width:12px;height:12px;background-color:#fef3c7;border-radius:2px;"></div>
                    <span>Bookmarked</span>
                </div>
            </div>
        </div>
    </div>
</div>

<h3>Timer and Progress</h3>

<p>The timer shows the remaining time for the test. It changes color as time runs out:</p>

<ul>
    <li><strong>Green</strong>: More than 50% of time remaining</li>
    <li><strong>Yellow</strong>: Between 25% and 50% of time remaining</li>
    <li><strong>Red</strong>: Less than 25% of time remaining</li>
</ul>

<p>When the timer expires, the test is automatically submitted.</p>

<h3>Question Navigation</h3>

<p>You can navigate between questions in several ways:</p>

<ul>
    <li>Using the "Previous" and "Next" buttons</li>
    <li>Clicking on question numbers in the question palette</li>
    <li>Using keyboard shortcuts (if enabled)</li>
</ul>

<h3>Answering Questions</h3>

<p>Different question types have different answer methods:</p>

<ul>
    <li><strong>Multiple Choice</strong>: Select one option by clicking the radio button</li>
    <li><strong>True/False</strong>: Select either "True" or "False"</li>
    <li><strong>Essay</strong>: Type your answer in the text box provided</li>
</ul>

<p>After selecting an answer, it will be automatically saved. You can change your answer at any time before submitting the test.</p>

<h3>Bookmarking Questions</h3>

<p>If you're unsure about a question and want to come back to it later, you can bookmark it by clicking the "Bookmark" button. Bookmarked questions appear with a yellow background in the question palette.</p>

<h3>Submitting the Test</h3>

<p>When you're ready to submit your test, click the "Submit Test" button in the header. You'll see a confirmation dialog asking if you're sure you want to submit. Once submitted, you cannot return to the test.</p>

<div style="border:1px solid #e5e7eb;border-radius:8px;padding:20px;margin:15px 0;background-color:#f9fafb;">
    <div style="text-align:center;margin-bottom:15px;">
        <h4 style="margin-top:0;">Confirm Submission</h4>
        <p style="color:#6b7280;">Are you sure you want to submit your test?</p>
        <p style="font-size:14px;color:#b91c1c;">This action cannot be undone.</p>
    </div>
    <div style="display:flex;justify-content:center;gap:15px;">
        <button style="background-color:#f3f4f6;color:#374151;border:none;padding:8px 16px;border-radius:6px;font-weight:500;">Cancel</button>
        <button style="background-color:#4338ca;color:white;border:none;padding:8px 16px;border-radius:6px;font-weight:500;">Submit Test</button>
    </div>
</div>

<h3>Test Continuation</h3>

<p>If you need to pause a test, you can use the continuation feature (if enabled for the test). You have a maximum of 2 continuation chances per test. To continue a test:</p>

<ol>
    <li>Return to the test details page</li>
    <li>Click the "Continue Test" button</li>
    <li>The test will resume from where you left off</li>
</ol>

<p>After using all continuation chances, the test will be automatically submitted if you try to leave it again.</p>

<h3>Tips for Test Taking</h3>

<ul>
    <li>Read all instructions carefully before starting the test</li>
    <li>Manage your time wisely - don't spend too long on any single question</li>
    <li>Use the bookmark feature for difficult questions</li>
    <li>Review all your answers before submitting if time permits</li>
    <li>If you're unsure about an answer, make an educated guess rather than leaving it blank</li>
</ul>
