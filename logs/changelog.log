# Changelog
# Format: [TIMES<PERSON>MP] PAGE: MESSAGE

CREATE TABLE IF NOT EXISTS query_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    user_id INT UNSIGNED,
    query TEXT NOT NULL,
    params TEXT,
    duration INT UNSIGNED NOT NULL COMMENT 'Query duration in milliseconds',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
