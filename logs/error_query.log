[2025-04-23T03:59:41.311Z] (157ms) User: 1
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"SELECT role FROM users WHERE id = ?","[1]",7,null,"success",null,"users","SELECT",null,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T03:59:41.311Z] (157ms) User: system
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"SELECT role FROM users WHERE id = ?","[1]",7,null,"success",null,"users","SELECT",null,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T03:59:41.311Z] (157ms) User: 1
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"SELECT role FROM users WHERE id = ?","[1]",7,null,"success",null,"users","SELECT",null,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T03:59:44.273Z] (2739ms) User: 1
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T03:59:44.273Z] (2739ms) User: system
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T03:59:44.275Z] (2739ms) User: 1
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T04:00:13.647Z] (7010ms) User: 1
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"\n                INSERT INTO query_logs (\n                    user_id, query, params, duration, result, status,\n                    error_message, table_name, query_type, affected_rows,\n                    ip_address, route\n                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n            ","[1,\"\\n            SELECT 1 FROM information_schema.tables\\n            WHERE table_schema = ? AND table_name = 'query_logs'\\n        \",\"[\\\"exam_prep_platform\\\"]\",2815,\"[{\\\"1\\\":1}]\",\"slow\",null,\"information_schema\",\"SELECT\",null,\"::1\",\"/admin/dashboard\"]",23,null,"success",null,"query_logs","INSERT",1,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T04:00:13.647Z] (7010ms) User: system
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"\n                INSERT INTO query_logs (\n                    user_id, query, params, duration, result, status,\n                    error_message, table_name, query_type, affected_rows,\n                    ip_address, route\n                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n            ","[1,\"\\n            SELECT 1 FROM information_schema.tables\\n            WHERE table_schema = ? AND table_name = 'query_logs'\\n        \",\"[\\\"exam_prep_platform\\\"]\",2815,\"[{\\\"1\\\":1}]\",\"slow\",null,\"information_schema\",\"SELECT\",null,\"::1\",\"/admin/dashboard\"]",23,null,"success",null,"query_logs","INSERT",1,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T04:00:13.648Z] (7010ms) User: 1
Query: 
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            
Params: [null,"\n                INSERT INTO query_logs (\n                    user_id, query, params, duration, result, status,\n                    error_message, table_name, query_type, affected_rows,\n                    ip_address, route\n                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n            ","[1,\"\\n            SELECT 1 FROM information_schema.tables\\n            WHERE table_schema = ? AND table_name = 'query_logs'\\n        \",\"[\\\"exam_prep_platform\\\"]\",2815,\"[{\\\"1\\\":1}]\",\"slow\",null,\"information_schema\",\"SELECT\",null,\"::1\",\"/admin/dashboard\"]",23,null,"success",null,"query_logs","INSERT",1,"::1","/api/users/check-email?email=admin%40example.com"]
Error: write EPIPE

[2025-04-23T04:00:43.998Z] (1095ms) User: system
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T04:00:43.998Z] (1095ms) User: system
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T04:00:43.999Z] (1095ms) User: 1
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

[2025-04-23T04:00:44.000Z] (1095ms) User: 1
Query: 
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        
Params: ["exam_prep_platform"]
Error: write EPIPE

