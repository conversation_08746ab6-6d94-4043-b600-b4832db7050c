const db = require('../config/database');

// Cache for user data to reduce database queries
const userCache = new Map();

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Middleware to check if user is authenticated
const checkAuthenticated = (req, res, next) => {
    // Check if this is an API request
    req.isApiRequest = req.path.startsWith('/api/');

    // Skip detailed logging to improve performance
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    // Check if we have cached user data that's still valid
    const cachedUser = userCache.get(req.session.userId);
    const now = Date.now();

    if (cachedUser && (now - cachedUser.timestamp < CACHE_EXPIRATION)) {
        // Use cached user data
        const user = cachedUser.data;

        // Check if user is blocked
        if (user.is_blocked) {
            req.session.destroy();
            if (req.isApiRequest) {
                return res.status(403).json({ error: 'Your account has been blocked. Please contact an administrator.' });
            }
            req.session.flashError = 'Your account has been blocked. Please contact an administrator.';
            return res.redirect('/login');
        }

        // Set user data in response locals
        res.locals.user = {
            id: user.id,
            username: user.username || 'User',
            role: user.role || 'user',
            profile_image: user.profile_image,
            name: user.name,
            email: user.email,
            is_blocked: user.is_blocked || 0
        };

        // Continue to next middleware
        return next();
    }

    // No valid cache, fetch from database
    // Use a timeout to prevent hanging
    const dbTimeout = setTimeout(() => {
        console.error('Database query timeout in checkAuthenticated');
        // Use session data as fallback
        res.locals.user = {
            id: req.session.userId,
            username: 'User',
            role: req.session.userRole || 'user'
        };
        next();
    }, 2000); // 2 second timeout

    // Fetch user data from database
    db.query('SELECT * FROM users WHERE id = ?', [req.session.userId])
        .then(async ([users]) => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);

            if (users.length > 0) {
                const user = users[0];

                // Check if user is blocked
                if (user.is_blocked) {
                    req.session.destroy();
                    if (req.isApiRequest) {
                        return res.status(403).json({ error: 'Your account has been blocked. Please contact an administrator.' });
                    }
                    req.session.flashError = 'Your account has been blocked. Please contact an administrator.';
                    return res.redirect('/login');
                }

                // Store user role in session
                req.session.userRole = user.role || 'user';

                // Cache the user data
                userCache.set(req.session.userId, {
                    data: user,
                    timestamp: Date.now()
                });

                // Load permissions in background (don't block the request)
                if (!req.session.permissions) {
                    loadUserPermissions(req, user).catch(err => {
                        console.error('Error loading permissions:', err);
                    });
                }

                res.locals.user = {
                    id: user.id,
                    username: user.username || 'User',
                    role: user.role || 'user',
                    profile_image: user.profile_image,
                    name: user.name,
                    email: user.email,
                    is_blocked: user.is_blocked || 0
                };
                next();
            } else {
                if (req.isApiRequest) {
                    return res.status(401).json({ error: 'User not found' });
                }
                // Clear the session and redirect to login
                req.session.destroy((err) => {
                    if (err) {
                        console.error('Session destruction error:', err);
                    }
                    return res.redirect('/login');
                });
                return; // Important to prevent further execution
            }
        })
        .catch(error => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);
            console.error('Error fetching user data:', error);

            // Use session data as fallback
            res.locals.user = {
                id: req.session.userId,
                username: 'User',
                role: req.session.userRole || 'user'
            };
            next();
        });
};

// Helper function to load user permissions in the background
async function loadUserPermissions(req, user) {
    try {
        // Admin users have all permissions
        if (user.role === 'admin') {
            const [allPermissions] = await db.query('SELECT permission_name FROM permissions');
            req.session.permissions = allPermissions.map(p => p.permission_name);
        } else {
            // Get role ID
            const [roles] = await db.query('SELECT role_id FROM roles WHERE role_name = ?', [user.role]);

            if (roles.length > 0) {
                const roleId = roles[0].role_id;

                // Get all permissions for this role
                const [permissions] = await db.query(`
                    SELECT p.permission_name
                    FROM role_permissions rp
                    JOIN permissions p ON rp.permission_id = p.permission_id
                    WHERE rp.role_id = ?
                `, [roleId]);

                req.session.permissions = permissions.map(p => p.permission_name);
            } else {
                req.session.permissions = [];
            }
        }
    } catch (error) {
        console.error('Error loading user permissions:', error);
        req.session.permissions = [];
    }
}

// Middleware to check if user is an admin
const checkAdmin = (req, res, next) => {
    // Skip detailed logging to improve performance
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    if (req.session.userRole !== 'admin') {
        if (req.isApiRequest) {
            return res.status(403).json({ error: 'Admin access required' });
        }

        // Flash message about access denied
        req.session.flashError = 'Admin access required';
        return res.redirect('/');
    }

    // Check if we have cached user data that's still valid
    const cachedUser = userCache.get(req.session.userId);
    const now = Date.now();

    if (cachedUser && (now - cachedUser.timestamp < CACHE_EXPIRATION)) {
        // Use cached user data
        const user = cachedUser.data;

        res.locals.user = {
            id: user.id,
            username: user.username || 'Admin',
            role: 'admin',
            profile_image: user.profile_image,
            name: user.name,
            email: user.email
        };

        // Clear any existing flash messages
        delete req.session.flashError;

        return next();
    }

    // Use a timeout to prevent hanging
    const dbTimeout = setTimeout(() => {
        console.error('Database query timeout in checkAdmin');
        // Use session data as fallback
        res.locals.user = {
            id: req.session.userId,
            username: 'Admin',
            role: 'admin'
        };
        next();
    }, 2000); // 2 second timeout

    // Fetch admin data from database
    db.query('SELECT * FROM users WHERE id = ?', [req.session.userId])
        .then(([users]) => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);

            if (users.length > 0) {
                const user = users[0];

                // Cache the user data
                userCache.set(req.session.userId, {
                    data: user,
                    timestamp: Date.now()
                });

                res.locals.user = {
                    id: user.id,
                    username: user.username || 'Admin',
                    role: 'admin',
                    profile_image: user.profile_image,
                    name: user.name,
                    email: user.email
                };

                // Clear any existing flash messages
                delete req.session.flashError;

                next();
            } else {
                if (req.isApiRequest) {
                    return res.status(401).json({ error: 'User not found' });
                }

                // Create a new session without flash messages
                req.session.regenerate((err) => {
                    if (err) {
                        console.error('Session regeneration error:', err);
                    }
                    return res.redirect('/login');
                });
                return; // Important to prevent further execution
            }
        })
        .catch(error => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);
            console.error('Error fetching admin data:', error);

            // Use session data as fallback
            res.locals.user = {
                id: req.session.userId,
                username: 'Admin',
                role: 'admin'
            };
            next();
        });
};

// Middleware to check if user is an admin (without redirecting)
const isAdmin = (req, res, next) => {
    try {
        if (req.session && req.session.userId && req.session.userRole === 'admin') {
            res.locals.isAdmin = true;
        } else {
            res.locals.isAdmin = false;
        }
    } catch (error) {
        console.error('Error in isAdmin middleware:', error);
        res.locals.isAdmin = false;
    }
    next();
};

// Cache for role permissions to reduce database queries
const permissionCache = new Map();

// Middleware to check if user has a specific permission
const checkPermission = (permissionName) => {
    return async (req, res, next) => {
        if (!req.session.userId) {
            if (req.isApiRequest) {
                return res.status(401).json({ error: 'Authentication required' });
            }
            return res.redirect('/login');
        }

        try {
            // Admin users have all permissions
            if (req.session.userRole === 'admin') {
                return next();
            }

            // Check if permissions are already loaded in session
            if (req.session.permissions && Array.isArray(req.session.permissions)) {
                if (req.session.permissions.includes(permissionName)) {
                    return next();
                } else {
                    if (req.isApiRequest) {
                        return res.status(403).json({ error: 'Permission denied' });
                    }
                    req.session.flashError = 'You do not have permission to access this resource';
                    return res.redirect('/');
                }
            }

            // Get user's role from session
            const userRole = req.session.userRole;
            if (!userRole) {
                if (req.isApiRequest) {
                    return res.status(401).json({ error: 'User role not found' });
                }
                return res.redirect('/login');
            }

            // Check permission cache for this role
            const cacheKey = `${userRole}:${permissionName}`;
            const cachedPermission = permissionCache.get(cacheKey);

            if (cachedPermission !== undefined) {
                if (cachedPermission) {
                    return next();
                } else {
                    if (req.isApiRequest) {
                        return res.status(403).json({ error: 'Permission denied' });
                    }
                    req.session.flashError = 'You do not have permission to access this resource';
                    return res.redirect('/');
                }
            }

            // Get role ID with a timeout to prevent hanging
            const rolePromise = db.query('SELECT role_id FROM roles WHERE role_name = ?', [userRole]);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Database query timeout')), 2000);
            });

            let roleId;
            try {
                const [roles] = await Promise.race([rolePromise, timeoutPromise]);
                if (roles.length === 0) {
                    // Cache the negative result
                    permissionCache.set(cacheKey, false);

                    if (req.isApiRequest) {
                        return res.status(403).json({ error: 'Permission denied' });
                    }
                    req.session.flashError = 'You do not have permission to access this resource';
                    return res.redirect('/');
                }
                roleId = roles[0].role_id;
            } catch (error) {
                console.error('Error or timeout getting role ID:', error);
                // Fallback to deny permission on error
                if (req.isApiRequest) {
                    return res.status(403).json({ error: 'Permission denied' });
                }
                req.session.flashError = 'You do not have permission to access this resource';
                return res.redirect('/');
            }

            // Check if role has the required permission with a timeout
            const permissionPromise = db.query(`
                SELECT 1
                FROM role_permissions rp
                JOIN permissions p ON rp.permission_id = p.permission_id
                WHERE rp.role_id = ? AND p.permission_name = ?
                LIMIT 1
            `, [roleId, permissionName]);

            try {
                const [permissions] = await Promise.race([permissionPromise, new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Permission query timeout')), 2000);
                })]);

                const hasPermission = permissions.length > 0;

                // Cache the result
                permissionCache.set(cacheKey, hasPermission);

                if (hasPermission) {
                    return next();
                } else {
                    if (req.isApiRequest) {
                        return res.status(403).json({ error: 'Permission denied' });
                    }
                    req.session.flashError = 'You do not have permission to access this resource';
                    return res.redirect('/');
                }
            } catch (error) {
                console.error('Error or timeout checking permission:', error);
                // Fallback to deny permission on error
                if (req.isApiRequest) {
                    return res.status(403).json({ error: 'Permission denied' });
                }
                req.session.flashError = 'You do not have permission to access this resource';
                return res.redirect('/');
            }
        } catch (error) {
            console.error('Error checking permission:', error);
            if (req.isApiRequest) {
                return res.status(500).json({ error: 'Internal server error' });
            }
            // Continue to next middleware on error
            next();
        }
    };
};

// Middleware to check if user is an IT admin
const checkITAdmin = (req, res, next) => {
    // Skip detailed logging to improve performance
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    if (req.session.userRole !== 'it_admin') {
        if (req.isApiRequest) {
            return res.status(403).json({ error: 'IT Admin access required' });
        }

        // Flash message about access denied
        req.session.flashError = 'IT Admin access required';
        return res.redirect('/');
    }

    // Check if we have cached user data that's still valid
    const cachedUser = userCache.get(req.session.userId);
    const now = Date.now();

    if (cachedUser && (now - cachedUser.timestamp < CACHE_EXPIRATION)) {
        // Use cached user data
        const user = cachedUser.data;

        res.locals.user = {
            id: user.id,
            username: user.username || 'IT Admin',
            role: 'it_admin',
            profile_image: user.profile_image,
            name: user.name,
            email: user.email
        };

        // Clear any existing flash messages
        delete req.session.flashError;

        return next();
    }

    // Use a timeout to prevent hanging
    const dbTimeout = setTimeout(() => {
        console.error('Database query timeout in checkITAdmin');
        // Use session data as fallback
        res.locals.user = {
            id: req.session.userId,
            username: 'IT Admin',
            role: 'it_admin'
        };
        next();
    }, 2000); // 2 second timeout

    // Fetch IT admin data from database
    db.query('SELECT * FROM users WHERE id = ?', [req.session.userId])
        .then(([users]) => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);

            if (users.length > 0) {
                const user = users[0];

                // Cache the user data
                userCache.set(req.session.userId, {
                    data: user,
                    timestamp: Date.now()
                });

                res.locals.user = {
                    id: user.id,
                    username: user.username || 'IT Admin',
                    role: 'it_admin',
                    profile_image: user.profile_image,
                    name: user.name,
                    email: user.email
                };

                // Clear any existing flash messages
                delete req.session.flashError;

                next();
            } else {
                if (req.isApiRequest) {
                    return res.status(401).json({ error: 'User not found' });
                }

                // Create a new session without flash messages
                req.session.regenerate((err) => {
                    if (err) {
                        console.error('Session regeneration error:', err);
                    }
                    return res.redirect('/login');
                });
                return; // Important to prevent further execution
            }
        })
        .catch(error => {
            // Clear the timeout since we got a response
            clearTimeout(dbTimeout);
            console.error('Error fetching IT admin data:', error);

            // Use session data as fallback
            res.locals.user = {
                id: req.session.userId,
                username: 'IT Admin',
                role: 'it_admin'
            };
            next();
        });
};

// Middleware to check if user is an IT admin (without redirecting)
const isITAdmin = (req, res, next) => {
    try {
        if (req.session && req.session.userId && req.session.userRole === 'it_admin') {
            res.locals.isITAdmin = true;
        } else {
            res.locals.isITAdmin = false;
        }
    } catch (error) {
        console.error('Error in isITAdmin middleware:', error);
        res.locals.isITAdmin = false;
    }
    next();
};

// Middleware to check if user is a student
const isStudent = (req, res, next) => {
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    if (req.session.userRole !== 'student') {
        if (req.isApiRequest) {
            return res.status(403).json({ error: 'Student access required' });
        }
        req.session.flashError = 'Student access required';
        return res.redirect('/');
    }

    next();
};

// Middleware to check if user is a teacher
const isTeacher = (req, res, next) => {
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    if (req.session.userRole !== 'teacher' && req.session.userRole !== 'admin' && req.session.userRole !== 'principal') {
        if (req.isApiRequest) {
            return res.status(403).json({ error: 'Teacher access required' });
        }
        req.session.flashError = 'Teacher access required';
        return res.redirect('/');
    }

    next();
};

// Middleware to check if user is a principal
const isPrincipal = (req, res, next) => {
    if (!req.session.userId) {
        if (req.isApiRequest) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        return res.redirect('/login');
    }

    if (req.session.userRole !== 'principal' && req.session.userRole !== 'admin') {
        if (req.isApiRequest) {
            return res.status(403).json({ error: 'Principal access required' });
        }
        req.session.flashError = 'Principal access required';
        return res.redirect('/');
    }

    next();
};



module.exports = {
    checkAuthenticated,
    checkAdmin,
    isAdmin,
    checkITAdmin,
    isITAdmin,
    checkPermission,
    isStudent,
    isTeacher,
    isPrincipal
};