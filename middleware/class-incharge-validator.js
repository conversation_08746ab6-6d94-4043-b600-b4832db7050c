/**
 * Middleware to validate class incharge assignments
 * Ensures computer teachers cannot be assigned as class incharge
 */

const db = require('../config/database');

/**
 * Middleware to validate if a teacher can be assigned as class incharge
 * Computer teachers cannot be class incharge, only lab incharge
 */
const validateClassInchargeAssignment = async (req, res, next) => {
  try {
    const { teacher_id, class_id, session } = req.body;
    
    // If either teacher_id or class_id is missing, skip validation
    if (!teacher_id || !class_id) {
      return next();
    }
    
    // Check if teacher exists and is active
    const [teacher] = await db.query(`
      SELECT id, full_name, role 
      FROM users 
      WHERE id = ? AND role = 'teacher' AND is_active = 1
    `, [teacher_id]);
    
    if (teacher.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Teacher not found or inactive'
      });
    }
    
    // Check if class exists
    const [classExists] = await db.query(`
      SELECT id, name, grade, trade, section 
      FROM classes 
      WHERE id = ?
    `, [class_id]);
    
    if (classExists.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Class not found'
      });
    }
    
    // Check if teacher_specialization table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_specialization'
    `);
    
    if (tableExists[0].table_exists > 0) {
      // Check if teacher is a computer teacher (has Computer Science specialization)
      const [isComputerTeacher] = await db.query(`
        SELECT id 
        FROM teacher_specialization 
        WHERE teacher_id = ? AND specialization = 'Computer Science'
      `, [teacher_id]);
      
      if (isComputerTeacher.length > 0) {
        return res.status(403).json({
          success: false,
          message: 'Computer teachers cannot be assigned as class incharge, only as lab incharge'
        });
      }
    } else {
      // If teacher_specialization table doesn't exist, check if teacher teaches computer subjects
      const [teachesComputerSubjects] = await db.query(`
        SELECT ts.id
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        WHERE ts.teacher_id = ?
        AND (
          s.name LIKE '%computer%' 
          OR s.name LIKE '%Computer%'
          OR s.code LIKE '%CS%'
          OR s.code LIKE '%comp%'
        )
      `, [teacher_id]);
      
      // Check if this is the only subject type they teach
      if (teachesComputerSubjects.length > 0) {
        const [totalSubjects] = await db.query(`
          SELECT COUNT(*) as total
          FROM teacher_subjects
          WHERE teacher_id = ?
        `, [teacher_id]);
        
        // If all subjects taught are computer subjects, they are a computer teacher
        if (teachesComputerSubjects.length === totalSubjects[0].total) {
          return res.status(403).json({
            success: false,
            message: 'Computer teachers cannot be assigned as class incharge, only as lab incharge'
          });
        }
      }
    }
    
    // If all validations pass, proceed to the next middleware
    next();
  } catch (error) {
    console.error('Error in class-incharge validator:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during validation',
      error: error.message
    });
  }
};

module.exports = validateClassInchargeAssignment;
