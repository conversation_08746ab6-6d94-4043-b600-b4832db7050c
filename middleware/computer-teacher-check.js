/**
 * Middleware to check if a user is a computer teacher
 * This is used to restrict computer teachers to only see their own data
 */

const db = require('../config/database');

/**
 * Check if the logged-in user is a computer teacher
 * Computer teachers are identified by:
 * 1. Having a specialization of 'Computer Science' in the teacher_specialization table
 * 2. Teaching computer-related subjects
 */
const isComputerTeacher = async (req, res, next) => {
  try {
    // Skip check if not a teacher
    if (req.session.userRole !== 'teacher') {
      req.isComputerTeacher = false;
      return next();
    }

    const teacherId = req.session.userId;
    
    // Check if teacher_specialization table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_specialization'
    `);
    
    if (tableExists[0].table_exists > 0) {
      // Check if teacher has Computer Science specialization
      const [specialization] = await db.query(`
        SELECT id 
        FROM teacher_specialization 
        WHERE teacher_id = ? AND specialization = 'Computer Science'
      `, [teacherId]);
      
      if (specialization.length > 0) {
        req.isComputerTeacher = true;
        return next();
      }
    }
    
    // If no specialization found, check if teacher teaches computer subjects
    const [teachesComputerSubjects] = await db.query(`
      SELECT ts.id
      FROM teacher_subjects ts
      JOIN subjects s ON ts.subject_id = s.id
      WHERE ts.teacher_id = ?
      AND (
        s.name LIKE '%computer%' 
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `, [teacherId]);
    
    // Check if all subjects taught are computer subjects
    if (teachesComputerSubjects.length > 0) {
      const [totalSubjects] = await db.query(`
        SELECT COUNT(*) as total
        FROM teacher_subjects
        WHERE teacher_id = ?
      `, [teacherId]);
      
      // If all subjects taught are computer subjects, they are a computer teacher
      if (teachesComputerSubjects.length === totalSubjects[0].total) {
        req.isComputerTeacher = true;
        return next();
      }
    }
    
    // If we get here, the teacher is not a computer teacher
    req.isComputerTeacher = false;
    next();
  } catch (error) {
    console.error('Error checking if teacher is a computer teacher:', error);
    req.isComputerTeacher = false;
    next();
  }
};

/**
 * Middleware to restrict computer teachers to only see their own data
 * This middleware should be used on routes that might show data from other teachers
 */
const restrictComputerTeacher = async (req, res, next) => {
  try {
    // Skip check if not a teacher
    if (req.session.userRole !== 'teacher') {
      return next();
    }
    
    // Check if user is a computer teacher
    if (req.isComputerTeacher === undefined) {
      await isComputerTeacher(req, res, () => {});
    }
    
    // If user is a computer teacher, ensure they can only see their own data
    if (req.isComputerTeacher) {
      // If the route includes a teacher_id parameter that doesn't match the logged-in user
      if (req.query.teacher_id && req.query.teacher_id != req.session.userId) {
        return res.status(403).render('error', {
          title: 'Access Denied',
          message: 'Computer teachers can only view their own data.',
          error: { status: 403 },
          layout: 'teacher'
        });
      }
      
      // Force teacher_id to be the logged-in user's ID
      req.query.teacher_id = req.session.userId;
      
      // If there's a body parameter for teacher_id, ensure it's the logged-in user
      if (req.body.teacher_id) {
        req.body.teacher_id = req.session.userId;
      }
    }
    
    next();
  } catch (error) {
    console.error('Error in restrictComputerTeacher middleware:', error);
    next();
  }
};

module.exports = {
  isComputerTeacher,
  restrictComputerTeacher
};
