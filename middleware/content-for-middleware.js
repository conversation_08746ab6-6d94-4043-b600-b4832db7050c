/**
 * Content For Middleware
 *
 * This middleware ensures that the contentFor function is available in all EJS templates.
 * It adds the contentFor function to res.locals so it can be used in all views.
 */

// Define the contentPattern (must match the one in express-ejs-layouts)
const contentPattern = '&&<>&&';

// Define the contentFor function
const contentFor = function(contentName) {
  return contentPattern + contentName + contentPattern;
};

const contentForMiddleware = (req, res, next) => {
  // Add contentFor function to res.locals
  res.locals.contentFor = contentFor;
  
  next();
};

module.exports = contentForMiddleware;
