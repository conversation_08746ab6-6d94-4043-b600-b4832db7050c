/**
 * JavaScript Logger Middleware
 *
 * This middleware makes the logjsvalues function available in the request object
 * and also overrides console.log to optionally save logs to the database.
 */

const { logjsvalues } = require('../utils/js-logger');

const jsLoggerMiddleware = (req, res, next) => {
    // Attach the logjsvalues function to the request object
    req.logjsvalues = (variableName, variableValue, context = 'middleware') => {
        return logjsvalues(variableName, variableValue, {
            route: req.originalUrl,
            context,
            user: req.session?.user,
            req
        });
    };

    // Make logjsvalues available in res.locals for use in templates
    res.locals.logjsvalues = (variableName, variableValue, context = 'view') => {
        return logjsvalues(variableName, variableValue, {
            route: req.originalUrl,
            context,
            user: req.session?.user,
            req
        });
    };

    // Store the original render method
    const originalRender = res.render;

    // Override the render method to make logjsvalues available in templates
    res.render = function(view, options, callback) {
        // Add logjsvalues to the options
        const newOptions = options || {};
        newOptions.logjsvalues = (variableName, variableValue, context = view) => {
            return logjsvalues(variableName, variableValue, {
                route: req.originalUrl,
                context,
                user: req.session?.userId ? { id: parseInt(req.session.userId) } : null,
                req
            });
        };

        // Call the original render method with the new options
        originalRender.call(this, view, newOptions, callback);
    };

    next();
};

module.exports = jsLoggerMiddleware;
