/**
 * JavaScript Variable Logger Middleware
 *
 * This middleware intercepts variables being sent to the client via res.locals
 * and logs them for audit purposes.
 */

// Use a simple console log instead of the enhanced logger
const logJavaScriptVariable = (varName, varValue, context, user, level, route) => {
    // Skip logging objects and arrays to avoid console clutter
    if (typeof varValue === 'object' && varValue !== null) {
        console.log(`[JS-VAR] ${route || 'unknown'} - ${context || 'unknown'} - ${varName} - [Object/Array]`);
    } else {
        console.log(`[JS-VAR] ${route || 'unknown'} - ${context || 'unknown'} - ${varName} - ${varValue}`);
    }
};

const jsVariableLoggerMiddleware = (req, res, next) => {
    // Store the original res.render method
    const originalRender = res.render;

    // Override the render method to log variables
    res.render = function(view, options, callback) {
        // Log variables being sent to the client
        if (options) {
            try {
                // Don't log certain views to avoid excessive logging
                const skipViews = ['error', 'partials/footer', 'partials/header', 'partials/admin-navbar'];
                if (!skipViews.includes(view)) {
                    // Get user info from session
                    const user = req.session && req.session.userId ? {
                        id: parseInt(req.session.userId),
                        username: req.session.username,
                        role: req.session.userRole
                    } : null;

                    // Log each variable
                    Object.keys(options).forEach(key => {
                        // Skip certain variables to avoid excessive logging
                        const skipVars = ['title', 'pageTitle', 'navbar', 'currentPage', 'error', 'message', 'csrfToken'];
                        if (!skipVars.includes(key)) {
                            // Log the variable
                            try {
                                logJavaScriptVariable(
                                    key,
                                    options[key],
                                    `View: ${view}, URL: ${req.originalUrl}`,
                                    user,
                                    'info',
                                    req.originalUrl
                                );
                            } catch (error) {
                                console.error(`Failed to log JavaScript variable ${key}:`, error);
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error in JS variable logger middleware:', error);
            }
        }

        // Call the original render method
        return originalRender.call(this, view, options, callback);
    };

    // Continue with the request
    next();
};

module.exports = jsVariableLoggerMiddleware;
