/**
 * Middleware to debug language selection
 */
const languageMiddleware = (req, res, next) => {
    // Log the current language
    console.log('Current language:', req.getLocale ? req.getLocale() : 'Not available');
    console.log('Language from cookie:', req.cookies.lang);
    console.log('Language from query:', req.query.lang);
    console.log('Available languages:', req.app.locals.availableLanguages);
    
    // If language is specified in query parameter, set it as cookie
    if (req.query.lang) {
        res.cookie('lang', req.query.lang, { 
            maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
            httpOnly: true,
            path: '/'
        });
        console.log('Setting language cookie:', req.query.lang);
    }
    
    next();
};

module.exports = languageMiddleware;
