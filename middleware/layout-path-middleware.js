/**
 * Layout Path Middleware
 *
 * This middleware ensures that layout paths are correctly resolved to the layouts directory.
 * It modifies the layout path to include the 'layouts/' prefix if it doesn't already have it.
 */

const layoutPathMiddleware = (req, res, next) => {
  // Store the original render method
  const originalRender = res.render;

  // Override the render method
  res.render = function(view, options, callback) {
    // Initialize options if not provided
    options = options || {};

    // If options is a function, it's the callback
    if (typeof options === 'function') {
      callback = options;
      options = {};
    }

    // Check if layout is specified and not false
    if (options.layout !== false && options.layout !== undefined) {
      // If layout doesn't start with 'layouts/', add it
      if (typeof options.layout === 'string' && !options.layout.startsWith('layouts/')) {
        console.log(`Before modification - View: ${view}, Layout: ${options.layout}`);
        options.layout = 'layouts/' + options.layout;
        console.log(`After modification - Layout path modified to: ${options.layout}`);
      }
    }

    // Check if layout is specified in res.locals and not false
    if (res.locals && res.locals.layout !== false && res.locals.layout !== undefined) {
      // If layout doesn't start with 'layouts/', add it
      if (typeof res.locals.layout === 'string' && !res.locals.layout.startsWith('layouts/')) {
        console.log(`Before modification - res.locals.layout: ${res.locals.layout}`);
        res.locals.layout = 'layouts/' + res.locals.layout;
        console.log(`After modification - res.locals.layout modified to: ${res.locals.layout}`);
      }
    }

    // Call the original render method with the modified options
    return originalRender.call(this, view, options, callback);
  };

  next();
};

module.exports = layoutPathMiddleware;
