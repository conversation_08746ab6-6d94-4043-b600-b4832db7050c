const { logEvent } = require('../utils/logger');

// Middleware to log user actions
const logUserAction = async (req, res, next) => {
    // Store the original end method
    const originalEnd = res.end;

    // Override the end method
    res.end = function(chunk, encoding) {
        // Call the original end method
        originalEnd.call(this, chunk, encoding);

        // Don't log requests to static files or the logs page itself
        if (
            req.path.startsWith('/css') ||
            req.path.startsWith('/js') ||
            req.path.startsWith('/images') ||
            req.path.startsWith('/uploads') ||
            req.path.startsWith('/admin/logs') ||
            req.path.startsWith('/admin/api/logs')
        ) {
            return;
        }

        // Don't log OPTIONS requests
        if (req.method === 'OPTIONS') {
            return;
        }

        // Determine the action based on the request method and path
        let action = req.method;
        let category = 'navigation';
        let level = 'info';
        let operation = `${req.method} ${req.path}`;
        let details = '';

        // Determine category based on path
        if (req.path.includes('/admin')) {
            category = 'admin';
        } else if (req.path.includes('/api')) {
            category = 'api';
        } else if (req.path.includes('/auth') || req.path.includes('/login') || req.path.includes('/register')) {
            category = 'auth';
        } else if (req.path.includes('/tests')) {
            category = 'test';
        } else if (req.path.includes('/questions')) {
            category = 'question';
        } else if (req.path.includes('/users')) {
            category = 'user';
        }

        // Determine operation based on method and path
        if (req.method === 'POST' || req.method === 'PUT' || req.method === 'DELETE') {
            // For data modification requests, provide more specific operation names
            if (req.path.includes('/login')) {
                operation = 'User Login';
                details = `User attempted to login with email: ${req.body.email || 'unknown'}`;
            } else if (req.path.includes('/register')) {
                operation = 'User Registration';
                details = `New user registration with email: ${req.body.email || 'unknown'}`;
            } else if (req.path.includes('/tests/add') || req.path.includes('/tests/create')) {
                operation = 'Test Creation';
                details = `Test created with title: ${req.body.title || 'unknown'}`;
            } else if (req.path.includes('/tests') && req.path.includes('/edit')) {
                operation = 'Test Update';
                details = `Test updated with ID: ${req.params.id || 'unknown'}`;
            } else if (req.path.includes('/questions/add') || req.path.includes('/questions/create')) {
                operation = 'Question Creation';
                details = `Question created with type: ${req.body.question_type || 'unknown'}`;
            } else if (req.path.includes('/questions') && req.path.includes('/edit')) {
                operation = 'Question Update';
                details = `Question updated with ID: ${req.params.id || 'unknown'}`;
            }
        } else {
            // For GET requests, just log the page view
            operation = 'Page View';
            details = `User viewed page: ${req.path}`;
        }

        // Determine status based on response status code
        let status = 'success';
        if (res.statusCode >= 400) {
            status = 'error';
            level = 'error';
        } else if (res.statusCode >= 300) {
            status = 'redirect';
        }

        // Log the action using both loggers for backward compatibility
        try {
            // Use the logger
            logEvent(
                req,
                level,
                category,
                operation,
                details,
                status
            ).catch(error => {
                console.error('Failed to log user action:', error);
            });
        } catch (error) {
            console.error('Error in logger middleware:', error);
        }
    };

    next();
};

module.exports = logUserAction;
