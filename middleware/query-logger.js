/**
 * Query Logger Middleware
 *
 * This middleware logs database queries to both a database table and log files.
 * It tracks query execution time, parameters, and results.
 */

const fs = require('fs').promises;
const path = require('path');
const db = require('../config/database');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
fs.mkdir(logsDir, { recursive: true }).catch(err => {
    console.error('Error creating logs directory:', err);
});

// Log file paths
const queryLogPath = path.join(logsDir, 'query.log');
const slowQueryLogPath = path.join(logsDir, 'slow_query.log');
const errorQueryLogPath = path.join(logsDir, 'error_query.log');

/**
 * Configuration for selective query logging
 */
const loggingConfig = {
    // Log queries by type (true = log, false = don't log)
    queryTypes: {
        SELECT: false,     // Don't log regular SELECT queries
        INSERT: true,      // Log INSERT queries
        UPDATE: true,      // Log UPDATE queries
        DELETE: true,      // Log DELETE queries
        CREATE: true,      // Log CREATE queries
        ALTER: true,       // Log ALTER queries
        DROP: true,        // Log DROP queries
        TRUNCATE: true     // Log TRUNCATE queries
    },

    // Always log these regardless of type
    alwaysLog: {
        slow: true,        // Log slow queries (> 1000ms)
        error: true,       // Log queries that result in errors
        sensitive: true    // Log queries to sensitive tables
    },

    // Tables considered sensitive (always log queries to these)
    sensitiveTables: [
        'users',
        'roles',
        'permissions',
        'role_permissions',
        'active_sessions',
        'user_permissions'
    ],

    // Routes to exclude from logging
    excludeRoutes: [
        '/login',          // Don't log login route queries
        '/api/auth',       // Don't log auth API queries
        '/extend-session'  // Don't log session extension queries
    ],

    // Minimum duration in ms to consider a query "slow"
    slowQueryThreshold: 1000
};

/**
 * Determines if a query should be logged based on configuration
 * @param {Object} queryInfo - Information about the query
 * @returns {boolean} - Whether the query should be logged
 */
function shouldLogQuery(queryInfo) {
    const {
        query,
        duration,
        error,
        tableName,
        queryType,
        route
    } = queryInfo;

    // Always log errors
    if (error && loggingConfig.alwaysLog.error) {
        return true;
    }

    // Always log slow queries
    if (duration > loggingConfig.slowQueryThreshold && loggingConfig.alwaysLog.slow) {
        return true;
    }

    // Skip logging for excluded routes
    if (route && loggingConfig.excludeRoutes.some(excludeRoute => route.startsWith(excludeRoute))) {
        return false;
    }

    // Always log queries to sensitive tables
    if (tableName && loggingConfig.sensitiveTables.includes(tableName) && loggingConfig.alwaysLog.sensitive) {
        return true;
    }

    // Log based on query type
    if (queryType && loggingConfig.queryTypes[queryType]) {
        return true;
    }

    // Default: don't log
    return false;
}

/**
 * Log a query to the database and log files
 * @param {Object} queryInfo - Information about the query
 */
async function logQuery(queryInfo) {
    // Check if this query should be logged
    if (!shouldLogQuery(queryInfo)) {
        return;
    }

    const {
        query,
        params,
        duration,
        result,
        error,
        userId,
        tableName,
        queryType,
        affectedRows,
        ipAddress,
        route
    } = queryInfo;

    // Determine query status
    const status = error ? 'error' : (duration > loggingConfig.slowQueryThreshold ? 'slow' : 'success');

    // Log to database if query_logs table exists
    try {
        // Check if query_logs table exists
        const [tableExists] = await db.query(`
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        `, [process.env.DB_NAME]);

        if (tableExists.length > 0) {
            // Insert query log into database
            await db.query(`
                INSERT INTO query_logs (
                    user_id, query, params, duration, result, status,
                    error_message, table_name, query_type, affected_rows,
                    ip_address, route
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId || null,
                query,
                JSON.stringify(params),
                duration,
                result ? JSON.stringify(result) : null,
                status,
                error ? error.message : null,
                tableName || null,
                queryType || null,
                affectedRows || null,
                ipAddress || null,
                route || null
            ]);
        }
    } catch (dbError) {
        console.error('Error logging query to database:', dbError);
        // Continue with file logging
    }

    // Format log entry
    const timestamp = new Date().toISOString();
    const userInfo = userId ? `User: ${userId}` : 'User: system';
    const logEntry = `[${timestamp}] (${duration}ms) ${userInfo}\nQuery: ${query}\nParams: ${JSON.stringify(params)}\n`;

    // Log to appropriate file based on status
    try {
        if (status === 'error') {
            await fs.appendFile(errorQueryLogPath, `${logEntry}Error: ${error.message}\n\n`);
        } else if (status === 'slow') {
            await fs.appendFile(slowQueryLogPath, `${logEntry}\n`);
        }

        // Always log to main query log
        await fs.appendFile(queryLogPath, `${logEntry}\n`);
    } catch (fileError) {
        console.error('Error writing to query log file:', fileError);
    }
}

/**
 * Middleware to log database queries
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function queryLoggerMiddleware(req, res, next) {
    // Store original query method
    const originalQuery = db.query;

    // Override query method to log queries
    db.query = async function(sql, params) {
        const startTime = Date.now();
        let result, error;

        try {
            // Execute the original query
            result = await originalQuery.call(this, sql, params);
            return result;
        } catch (err) {
            error = err;
            throw err;
        } finally {
            const duration = Date.now() - startTime;

            // Extract query type and table name
            const queryType = sql.trim().split(' ')[0].toUpperCase();

            // Quick check to see if we should even bother with further processing
            // This avoids regex and other expensive operations for queries we won't log
            const shouldProcess =
                error ||
                duration > loggingConfig.slowQueryThreshold ||
                loggingConfig.queryTypes[queryType] ||
                loggingConfig.alwaysLog.sensitive;

            if (!shouldProcess) {
                return;
            }

            // Only extract table name if needed
            let tableName = null;
            if (shouldProcess) {
                const tableNameMatch = sql.match(/(?:FROM|INTO|UPDATE|JOIN)\s+`?(\w+)`?/i);
                tableName = tableNameMatch ? tableNameMatch[1] : null;
            }

            // Extract affected rows if available
            const affectedRows = result && result[0] && result[0].affectedRows ? result[0].affectedRows : null;

            // Log the query asynchronously (don't await)
            logQuery({
                query: sql,
                params,
                duration,
                // Only include result if it's an error or slow query to reduce memory usage
                result: (error || duration > loggingConfig.slowQueryThreshold) ? (result ? result[0] : null) : null,
                error,
                userId: req.session && req.session.userId ? req.session.userId : null,
                tableName,
                queryType,
                affectedRows,
                ipAddress: req.ip || req.connection.remoteAddress,
                route: req.originalUrl
            }).catch(logError => {
                console.error('Error in query logging:', logError);
            });
        }
    };

    next();
}

module.exports = {
    queryLoggerMiddleware,
    logQuery
};
