/**
 * Request Context Middleware
 * 
 * This middleware stores the current request in a global variable
 * so it can be accessed by other parts of the application,
 * particularly for logging purposes.
 */

const requestContextMiddleware = (req, res, next) => {
    // Store the request in a global variable
    global.currentRequest = req;
    
    // Continue with the request
    next();
};

module.exports = requestContextMiddleware;
