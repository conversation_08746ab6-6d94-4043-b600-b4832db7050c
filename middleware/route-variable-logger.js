/**
 * Route Variable Logger Middleware
 *
 * This middleware logs variables in routes for audit purposes.
 */

// Simple logging function to replace the enhanced logger
const logJavaScriptVariable = (varName, varValue, options) => {
    const context = options.context || 'unknown';
    const route = options.route || 'unknown';
    const level = options.level || 'info';

    // Skip logging objects and arrays to avoid console clutter
    if (typeof varValue === 'object' && varValue !== null) {
        console.log(`[${level.toUpperCase()}] [ROUTE-VAR] ${route} - ${context} - ${varName} - [Object/Array]`);
    } else {
        console.log(`[${level.toUpperCase()}] [ROUTE-VAR] ${route} - ${context} - ${varName} - ${varValue}`);
    }
};

/**
 * Create a middleware to log route variables
 * @param {string} routeName - Name of the route
 * @returns {Function} - Express middleware function
 */
const routeVariableLogger = (routeName) => {
    return (req, res, next) => {
        // Create a wrapper for req.body to log variables
        const originalBody = { ...req.body };

        // Log request body
        if (Object.keys(originalBody).length > 0) {
            try {
                logJavaScriptVariable(
                    'request.body',
                    originalBody,
                    {
                        context: `${routeName} - Request Body`,
                        user: req.session ? {
                            id: req.session.userId,
                            username: req.session.username,
                            role: req.session.userRole
                        } : null,
                        level: 'info',
                        route: req.originalUrl
                    }
                );
            } catch (error) {
                console.error(`Failed to log request body for ${routeName}:`, error);
            }
        }

        // Log request parameters
        if (Object.keys(req.params).length > 0) {
            try {
                logJavaScriptVariable(
                    'request.params',
                    req.params,
                    {
                        context: `${routeName} - Request Parameters`,
                        user: req.session ? {
                            id: req.session.userId,
                            username: req.session.username,
                            role: req.session.userRole
                        } : null,
                        level: 'info',
                        route: req.originalUrl
                    }
                );
            } catch (error) {
                console.error(`Failed to log request parameters for ${routeName}:`, error);
            }
        }

        // Log query parameters
        if (Object.keys(req.query).length > 0) {
            try {
                logJavaScriptVariable(
                    'request.query',
                    req.query,
                    {
                        context: `${routeName} - Query Parameters`,
                        user: req.session ? {
                            id: req.session.userId,
                            username: req.session.username,
                            role: req.session.userRole
                        } : null,
                        level: 'info',
                        route: req.originalUrl
                    }
                );
            } catch (error) {
                console.error(`Failed to log query parameters for ${routeName}:`, error);
            }
        }

        // Create a wrapper for res.render to log variables
        const originalRender = res.render;
        res.render = function(view, options, callback) {
            // Log variables being sent to the view
            if (options) {
                try {
                    // Don't log certain views to avoid excessive logging
                    const skipViews = ['error', 'partials/footer', 'partials/header', 'partials/admin-navbar'];
                    if (!skipViews.includes(view)) {
                        // Log each variable
                        Object.keys(options).forEach(key => {
                            // Skip certain variables to avoid excessive logging
                            const skipVars = ['title', 'pageTitle', 'navbar', 'currentPage', 'error', 'message', 'csrfToken'];
                            if (!skipVars.includes(key)) {
                                // Log the variable
                                try {
                                    logJavaScriptVariable(
                                        key,
                                        options[key],
                                        {
                                            context: `${routeName} - View: ${view}`,
                                            user: req.session ? {
                                                id: req.session.userId,
                                                username: req.session.username,
                                                role: req.session.userRole
                                            } : null,
                                            level: 'info',
                                            route: req.originalUrl
                                        }
                                    );
                                } catch (error) {
                                    console.error(`Failed to log variable ${key} for ${routeName}:`, error);
                                }
                            }
                        });
                    }
                } catch (error) {
                    console.error(`Error in route variable logger middleware for ${routeName}:`, error);
                }
            }

            // Call the original render method
            return originalRender.call(this, view, options, callback);
        };

        // Create a wrapper for res.json to log variables
        const originalJson = res.json;
        res.json = function(obj) {
            // Log the JSON response
            try {
                logJavaScriptVariable(
                    'response.json',
                    obj,
                    {
                        context: `${routeName} - JSON Response`,
                        user: req.session ? {
                            id: req.session.userId,
                            username: req.session.username,
                            role: req.session.userRole
                        } : null,
                        level: 'info',
                        route: req.originalUrl
                    }
                );
            } catch (error) {
                console.error(`Error in route variable logger middleware for ${routeName}:`, error);
            }

            // Call the original json method
            return originalJson.call(this, obj);
        };

        // Continue with the request
        next();
    };
};

module.exports = routeVariableLogger;
