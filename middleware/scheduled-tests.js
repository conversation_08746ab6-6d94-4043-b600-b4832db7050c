/**
 * Middleware to check for scheduled tests and update their status
 * This middleware will run on every request to ensure scheduled tests are published when their time comes
 */

const db = require('../config/database');

const checkScheduledTests = async (req, res, next) => {
    try {
        // Only run this check occasionally to avoid excessive database queries
        // Use a random chance (1 in 10) to run the check
        if (Math.random() < 0.1) {
            console.log('Checking for scheduled tests that need to be published...');
            
            // Find scheduled tests where publish_date has passed
            const [scheduledTests] = await db.query(`
                SELECT exam_id, exam_name, publish_date 
                FROM exams 
                WHERE status = 'scheduled' 
                AND publish_date <= NOW()
            `);
            
            if (scheduledTests.length > 0) {
                console.log(`Found ${scheduledTests.length} scheduled tests to publish:`, 
                    scheduledTests.map(t => `${t.exam_id}: ${t.exam_name} (${t.publish_date})`));
                
                // Update the status of these tests to 'published'
                const examIds = scheduledTests.map(test => test.exam_id);
                
                await db.query(`
                    UPDATE exams 
                    SET status = 'published', 
                        updated_at = NOW() 
                    WHERE exam_id IN (?)
                `, [examIds]);
                
                console.log(`Updated ${scheduledTests.length} tests to published status`);
            }
        }
    } catch (error) {
        console.error('Error checking scheduled tests:', error);
        // Don't throw the error, just log it and continue
    }
    
    next();
};

module.exports = checkScheduledTests;
