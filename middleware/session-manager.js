const db = require('../config/database');

/**
 * Middleware to manage user sessions and prevent multi-device login
 * except for admin users
 */
const sessionManager = async (req, res, next) => {
    // Skip for non-authenticated users or for API requests
    if (!req.session.userId || req.isApiRequest) {
        return next();
    }

    try {
        // Get user role from session instead of querying the database
        const userRole = req.session.userRole;

        // If no role in session, proceed without session management
        if (!userRole) {
            console.log('No user role in session, skipping session management');
            return next();
        }

        // Admin users can have multiple sessions
        if (userRole === 'admin') {
            // For admin users, record session in background but don't wait
            recordSession(req).catch(err => console.error('Error recording admin session:', err));
            return next();
        }

        // For non-admin users, check if they already have an active session
        // but do it asynchronously to avoid blocking the request
        const sessionCheckPromise = db.query(
            'SELECT * FROM active_sessions WHERE user_id = ? AND is_active = 1 AND session_id != ?',
            [req.session.userId, req.sessionID]
        );

        // Set a timeout to ensure we don't wait too long
        const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => resolve([[]]), 500); // 500ms timeout
        });

        // Race the database query against the timeout
        const [activeSessions] = await Promise.race([sessionCheckPromise, timeoutPromise]);

        if (activeSessions && activeSessions.length > 0) {
            // User already has an active session on another device
            // Store the message and user ID before destroying the session
            const errorMessage = 'You are already logged in on another device. Please log out from other devices first.';
            const userId = req.session.userId;

            // Invalidate the current session and wait for it to complete before redirecting
            return new Promise((resolve) => {
                req.session.destroy((err) => {
                    if (err) {
                        console.error('Error destroying session:', err);
                    }

                    // Redirect to login with error message
                    res.render('auth/login', {
                        title: 'Login',
                        currentLanguage: req.getLocale ? req.getLocale() : 'en',
                        availableLanguages: req.app.get('availableLanguages') || ['en', 'pa'],
                        layout: 'layouts/auth',
                        canForceLogout: true,
                        userId: userId,
                        error: errorMessage
                    });
                    resolve();
                });
            });
        }

        // Record the current session in background but don't wait
        recordSession(req).catch(err => console.error('Error recording session:', err));

        next();
    } catch (error) {
        console.error('Session management error:', error);
        // Don't let session errors block the request
        next();
    }
};

/**
 * Record the current session in the active_sessions table
 * This is now optimized to be more efficient and non-blocking
 *
 * TEMPORARILY DISABLED due to database schema issues
 */
async function recordSession(req) {
    try {
        // Temporarily disabled to avoid database errors
        console.log('Session recording temporarily disabled');
        return true;
    } catch (error) {
        console.error('Error recording session:', error);
        return false;
    }
}

/**
 * Clean up session when user logs out
 *
 * TEMPORARILY DISABLED active_sessions table updates due to database schema issues
 */
const cleanupSession = async (req) => {
    try {
        // Temporarily disabled active_sessions table updates
        console.log('Session cleanup (active_sessions) temporarily disabled');

        // If WebSocket server is available, force logout the user
        if (req.session && req.session.userId) {
            try {
                const { forceLogout } = require('../websocket-server');
                forceLogout(req.session.userId);
            } catch (wsError) {
                console.error('Error forcing logout via WebSocket:', wsError);
            }
        }
    } catch (error) {
        console.error('Error cleaning up session:', error);
    }
};

module.exports = {
    sessionManager,
    cleanupSession
};
