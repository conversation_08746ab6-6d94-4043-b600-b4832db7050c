/**
 * Middleware to handle session timeout after inactivity
 * Logs out users after 15 minutes of inactivity
 * Disabled during tests
 */
const { getUsersInTest } = require('./test-mode-middleware');

// Session timeout in milliseconds (15 minutes)
const SESSION_TIMEOUT = 15 * 60 * 1000;

module.exports = {
    /**
     * Check if session has timed out
     */
    checkSessionTimeout: (req, res, next) => {
        try {
            // Skip if not authenticated
            if (!req.session.userId) {
                return next();
            }

            // Skip if user is in test mode
            const usersInTest = getUsersInTest();
            if (usersInTest.includes(req.session.userId)) {
                // Reset last activity time to keep session active during test
                req.session.lastActivity = Date.now();
                return next();
            }

            // Check if last activity time exists
            if (!req.session.lastActivity) {
                // Initialize last activity time
                req.session.lastActivity = Date.now();
                return next();
            }

            // Check if session has timed out
            const currentTime = Date.now();
            const timeSinceLastActivity = currentTime - req.session.lastActivity;

            if (timeSinceLastActivity > SESSION_TIMEOUT) {
                // Session has timed out, destroy session and redirect to login
                req.session.destroy((err) => {
                    if (err) {
                        console.error('Error destroying session:', err);
                    }
                    return res.redirect('/login?timeout=true');
                });
            } else {
                // Update last activity time
                req.session.lastActivity = currentTime;
                next();
            }
        } catch (error) {
            console.error('Error checking session timeout:', error);
            next();
        }
    },

    /**
     * Update last activity time
     */
    updateLastActivity: (req, res, next) => {
        if (req.session.userId) {
            req.session.lastActivity = Date.now();
        }
        next();
    }
};
