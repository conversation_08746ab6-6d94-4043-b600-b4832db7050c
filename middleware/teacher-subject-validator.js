/**
 * Middleware to validate teacher-subject assignments
 * Ensures computer teachers can only teach computer-related subjects
 */

const db = require('../config/database');

/**
 * Middleware to validate if a teacher can be assigned to a subject
 * Computer teachers can only teach computer-related subjects
 */
const validateTeacherSubjectAssignment = async (req, res, next) => {
  try {
    const { teacher_id, subject_id } = req.body;
    
    // If either teacher_id or subject_id is missing, skip validation
    if (!teacher_id || !subject_id) {
      return next();
    }
    
    // Check if teacher exists and is active
    const [teacher] = await db.query(`
      SELECT id, full_name, role 
      FROM users 
      WHERE id = ? AND role = 'teacher' AND is_active = 1
    `, [teacher_id]);
    
    if (teacher.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Teacher not found or inactive'
      });
    }
    
    // Check if subject exists and is active
    const [subject] = await db.query(`
      SELECT id, name, code 
      FROM subjects 
      WHERE id = ? AND is_active = 1
    `, [subject_id]);
    
    if (subject.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Subject not found or inactive'
      });
    }
    
    // Check if teacher_specialization table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_specialization'
    `);
    
    if (tableExists[0].table_exists > 0) {
      // Check if teacher is a computer teacher (has Computer Science specialization)
      const [isComputerTeacher] = await db.query(`
        SELECT id 
        FROM teacher_specialization 
        WHERE teacher_id = ? AND specialization = 'Computer Science'
      `, [teacher_id]);
      
      if (isComputerTeacher.length > 0) {
        // Check if subject_category table exists
        const [categoryTableExists] = await db.query(`
          SELECT COUNT(*) as table_exists
          FROM information_schema.tables
          WHERE table_schema = DATABASE()
          AND table_name = 'subject_category'
        `);
        
        if (categoryTableExists[0].table_exists > 0) {
          // Check if subject is a computer subject
          const [isComputerSubject] = await db.query(`
            SELECT id 
            FROM subject_category 
            WHERE subject_id = ? AND category = 'Computer Science'
          `, [subject_id]);
          
          // If no category is found, check the subject name/code
          if (isComputerSubject.length === 0) {
            // Check if subject name/code indicates it's a computer subject
            const isComputerByName = 
              subject[0].name.toLowerCase().includes('computer') || 
              subject[0].name.toLowerCase().includes('programming') ||
              subject[0].code.includes('CS') ||
              subject[0].code.toLowerCase().includes('comp');
            
            if (!isComputerByName) {
              return res.status(403).json({
                success: false,
                message: 'Computer teachers can only teach computer-related subjects'
              });
            }
          }
        } else {
          // If subject_category table doesn't exist, check subject name/code
          const isComputerByName = 
            subject[0].name.toLowerCase().includes('computer') || 
            subject[0].name.toLowerCase().includes('programming') ||
            subject[0].code.includes('CS') ||
            subject[0].code.toLowerCase().includes('comp');
          
          if (!isComputerByName) {
            return res.status(403).json({
              success: false,
              message: 'Computer teachers can only teach computer-related subjects'
            });
          }
        }
      }
    } else {
      // If teacher_specialization table doesn't exist, check if teacher teaches computer subjects
      const [teachesComputerSubjects] = await db.query(`
        SELECT ts.id
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        WHERE ts.teacher_id = ?
        AND (
          s.name LIKE '%computer%' 
          OR s.name LIKE '%Computer%'
          OR s.code LIKE '%CS%'
          OR s.code LIKE '%comp%'
        )
      `, [teacher_id]);
      
      if (teachesComputerSubjects.length > 0) {
        // Teacher teaches computer subjects, check if the new subject is a computer subject
        const isComputerSubject = 
          subject[0].name.toLowerCase().includes('computer') || 
          subject[0].name.toLowerCase().includes('programming') ||
          subject[0].code.includes('CS') ||
          subject[0].code.toLowerCase().includes('comp');
        
        if (!isComputerSubject) {
          return res.status(403).json({
            success: false,
            message: 'Computer teachers can only teach computer-related subjects'
          });
        }
      }
    }
    
    // If all validations pass, proceed to the next middleware
    next();
  } catch (error) {
    console.error('Error in teacher-subject validator:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during validation',
      error: error.message
    });
  }
};

module.exports = validateTeacherSubjectAssignment;
