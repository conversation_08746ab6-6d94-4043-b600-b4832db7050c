/**
 * Middleware to handle test mode
 * Disables chat functionality during tests
 */
const db = require('../config/database');

// Map to store users in test mode
const usersInTest = new Map();

module.exports = {
    /**
     * Check if user is in test mode
     */
    checkTestMode: async (req, res, next) => {
        try {
            // Skip if not authenticated or session is undefined
            if (!req.session || !req.session.userId) {
                res.locals.isInTestMode = false;
                res.locals.inTestMode = false;
                return next();
            }

            // Check if user is in test mode
            const isInTest = usersInTest.has(req.session.userId);

            // If user is in test mode and trying to access chat
            if (isInTest && req.originalUrl.startsWith('/chat')) {
                if (req.session) {
                    req.session.flashError = 'Chat is disabled during tests';
                }
                return res.redirect('/tests');
            }

            // Add test mode flag to locals and body class
            res.locals.isInTestMode = isInTest;

            // This will be used in the layout to add the in-test-mode class to the body
            res.locals.inTestMode = isInTest;

            next();
        } catch (error) {
            console.error('Error checking test mode:', error);
            res.locals.isInTestMode = false;
            res.locals.inTestMode = false;
            next();
        }
    },

    /**
     * Enter test mode
     */
    enterTestMode: (userId) => {
        usersInTest.set(userId, true);
    },

    /**
     * Exit test mode
     */
    exitTestMode: (userId) => {
        usersInTest.delete(userId);
    },

    /**
     * Get users in test mode
     */
    getUsersInTest: () => {
        return Array.from(usersInTest.keys());
    }
};
