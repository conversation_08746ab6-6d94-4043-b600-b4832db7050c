/**
 * Voucher Middleware
 * Handles common functionality for voucher generation
 */

const fs = require('fs');
const path = require('path');
const PDFDocument = require('pdfkit');

/**
 * Ensure the voucher uploads directory exists
 */
function ensureVoucherDirectoryExists() {
  const uploadsDir = path.join(__dirname, '../public/uploads/vouchers');
  if (!fs.existsSync(uploadsDir)) {
    console.log('Creating voucher uploads directory:', uploadsDir);
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  return uploadsDir;
}

/**
 * Middleware to prepare the response for voucher generation
 */
function prepareVoucherResponse(req, res, next) {
  console.log('Voucher middleware: Preparing response');

  // Set proper content type for AJAX requests
  const isAjaxRequest = req.xhr || req.query.newTab === 'true' ||
                      (req.headers.accept && req.headers.accept.includes('application/json')) ||
                      req.headers['x-requested-with'] === 'XMLHttpRequest';

  if (isAjaxRequest) {
    res.setHeader('Content-Type', 'application/json');
  }

  // Ensure the voucher directory exists
  ensureVoucherDirectoryExists();

  next();
}

/**
 * Generate a PDF voucher and return it as a response
 * @param {Object} options - Options for generating the voucher
 * @param {Object} res - Express response object
 * @param {boolean} isNewTab - Whether to return a URL for a new tab or stream directly
 */
async function generateVoucher(options, res, isNewTab) {
  // Debug: Log the options being passed to the voucher generator
  console.log('Voucher options:', JSON.stringify(options, null, 2));

  const {
    transactionId,
    voucherType,
    title,
    itemName,
    serialNumber,
    issuedTo,
    issuedBy,
    issuedDate,
    expectedReturnDate,
    returnDate,
    returnedBy,
    condition
  } = options;

  try {
    // Create a unique filename
    const timestamp = Date.now();
    const filename = `${voucherType}_voucher_${transactionId}_${timestamp}.pdf`;
    const outputPath = path.join(ensureVoucherDirectoryExists(), filename);

    if (isNewTab) {
      // For new tab, create the PDF and save it to disk
      const doc = new PDFDocument({ margin: 50 });
      const writeStream = fs.createWriteStream(outputPath);
      doc.pipe(writeStream);

      // Generate the PDF content
      generatePDFContent(doc, options);

      // Finalize the PDF
      doc.end();

      // Wait for the PDF to be written to disk
      await new Promise((resolve, reject) => {
        writeStream.on('finish', () => {
          console.log('PDF write stream finished');
          resolve();
        });
        writeStream.on('error', (err) => {
          console.error('PDF write stream error:', err);
          reject(err);
        });
      });

      // Create a public URL for the file
      const publicPath = `/uploads/vouchers/${path.basename(outputPath)}`;
      console.log('Generated public path:', publicPath);

      // Return the URL as JSON
      console.log('Returning JSON response with URL');
      return res.json({ success: true, url: publicPath });
    } else {
      // For direct download, create the PDF and stream it to the response
      const doc = new PDFDocument({ margin: 50 });

      // Set response headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // Pipe the PDF directly to the response
      doc.pipe(res);

      // Generate the PDF content
      generatePDFContent(doc, options);

      // Finalize the PDF
      doc.end();
    }
  } catch (error) {
    console.error(`Error generating ${voucherType} voucher:`, error);
    if (isNewTab) {
      return res.status(500).json({
        success: false,
        message: `Error generating ${voucherType} voucher: ${error.message}`
      });
    } else {
      res.status(500).send(`Error generating ${voucherType} voucher: ${error.message}`);
    }
  }
}

/**
 * Generate the content for the PDF voucher
 * @param {PDFDocument} doc - PDFKit document
 * @param {Object} options - Options for generating the voucher
 */
function generatePDFContent(doc, options) {
  const {
    transactionId,
    voucherType,
    title,
    itemName,
    serialNumber,
    issuedTo,
    issuedBy,
    issuedDate,
    expectedReturnDate,
    returnDate,
    returnedBy,
    condition
  } = options;

  // Set document metadata
  doc.info.Title = title || `${voucherType.toUpperCase()} VOUCHER`;
  doc.info.Author = 'Meritorious EP IT Inventory System';
  doc.info.Subject = `${voucherType.toUpperCase()} Voucher for ${itemName}`;

  // Add a border to the page
  doc.rect(20, 20, doc.page.width - 40, doc.page.height - 40)
     .lineWidth(1)
     .stroke('#3366cc');

  // Add school name at the top
  doc.fontSize(18)
     .font('Helvetica-Bold')
     .fillColor('#000066')
     .text('MERITORIOUS EP SCHOOL', 0, 40, {
       align: 'center',
       width: doc.page.width
     });

  // Add a smaller subtitle
  doc.fontSize(10)
     .font('Helvetica')
     .fillColor('#333333')
     .text('IT INVENTORY MANAGEMENT SYSTEM', 0, 65, {
       align: 'center',
       width: doc.page.width
     });

  // Add a horizontal line
  doc.moveTo(50, 85)
     .lineTo(550, 85)
     .lineWidth(1)
     .stroke('#3366cc');

  // Add the title with a background - smaller font
  doc.rect(150, 95, 300, 35)
     .fillAndStroke('#f0f4f8', '#3366cc');

  doc.fillColor('#000066')
     .fontSize(16) // Reduced from 22
     .font('Helvetica-Bold')
     .text(title || `${voucherType.toUpperCase()} VOUCHER`, 150, 105, {
       align: 'center',
       width: 300
     });

  // Add voucher ID
  doc.fillColor('#333333')
     .fontSize(10)
     .font('Helvetica')
     .text(`Voucher #: ${voucherType.charAt(0).toUpperCase()}V-${transactionId || '000'}`, 400, 140, {
       align: 'right'
     });

  // Add horizontal line
  doc.moveTo(50, 160)
     .lineTo(550, 160)
     .lineWidth(1)
     .stroke('#cccccc');

  // Start main content with more prominent header
  doc.fontSize(14)
     .font('Helvetica-Bold')
     .fillColor('#000066')
     .text('EQUIPMENT DETAILS', 50, 180);

  // Add a light background for the equipment section with a border
  doc.rect(50, 200, 500, 80)
     .fillAndStroke('#f0f4f8', '#3366cc');

  // Equipment details
  doc.fontSize(11)
     .font('Helvetica')
     .fillColor('#333333')
     .text('Item:', 70, 210)
     .font('Helvetica-Bold')
     .text(itemName || 'N/A', 200, 210);

  doc.font('Helvetica')
     .text('Serial Number:', 70, 230)
     .font('Helvetica-Bold')
     .text(serialNumber || 'N/A', 200, 230);

  // Transaction details section - with a more prominent header
  doc.font('Helvetica-Bold')
     .fillColor('#000066')
     .fontSize(14)
     .text('TRANSACTION DETAILS', 50, 300);

  // Add a light background for the transaction section with a border
  doc.rect(50, 320, 500, voucherType.toLowerCase() === 'loan' ? 120 : 140)
     .fillAndStroke('#f0f4f8', '#3366cc');

  if (voucherType.toLowerCase() === 'loan') {
    // Loan voucher specific fields
    doc.fontSize(11)
       .font('Helvetica')
       .fillColor('#333333')
       .text('Recipient Name:', 70, 330)
       .font('Helvetica-Bold')
       .text(issuedTo || 'N/A', 200, 330);

    doc.font('Helvetica')
       .text('Recipient Position:', 70, 350)
       .font('Helvetica-Bold')
       .text('Staff/Faculty', 200, 350);

    doc.font('Helvetica')
       .text('Issuing Officer:', 70, 370)
       .font('Helvetica-Bold')
       .text(issuedBy || 'N/A', 200, 370);

    doc.font('Helvetica')
       .text('Issue Date:', 70, 390)
       .font('Helvetica-Bold')
       .text(issuedDate || new Date().toLocaleDateString(), 200, 390);

    doc.font('Helvetica')
       .text('Expected Return Date:', 70, 410)
       .font('Helvetica-Bold')
       .text(expectedReturnDate || 'Not specified', 200, 410);

    doc.font('Helvetica')
       .text('Department:', 70, 430)
       .font('Helvetica-Bold')
       .text('IT Department', 200, 430);
  } else {
    // Return voucher specific fields
    doc.fontSize(11)
       .font('Helvetica')
       .fillColor('#333333')
       .text('Recipient Name:', 70, 330)
       .font('Helvetica-Bold')
       .text(issuedTo || 'N/A', 200, 330);

    doc.font('Helvetica')
       .text('Recipient Position:', 70, 350)
       .font('Helvetica-Bold')
       .text('Staff/Faculty', 200, 350);

    doc.font('Helvetica')
       .text('Returned By:', 70, 370)
       .font('Helvetica-Bold')
       .text(returnedBy || issuedTo || 'N/A', 200, 370);

    doc.font('Helvetica')
       .text('Receiving Officer:', 70, 390)
       .font('Helvetica-Bold')
       .text(issuedBy || 'IT Staff', 200, 390);

    doc.font('Helvetica')
       .text('Issue Date:', 70, 410)
       .font('Helvetica-Bold')
       .text(issuedDate || new Date().toLocaleDateString(), 200, 410);

    doc.font('Helvetica')
       .text('Return Date:', 70, 430)
       .font('Helvetica-Bold')
       .text(returnDate || new Date().toLocaleDateString(), 200, 430);

    doc.font('Helvetica')
       .text('Condition on Return:', 70, 450)
       .font('Helvetica-Bold')
       .text(condition || 'Good', 200, 450);
  }

  // Terms and conditions section with more prominent header
  doc.font('Helvetica-Bold')
     .fillColor('#000066')
     .fontSize(14)
     .text(voucherType.toLowerCase() === 'loan' ? 'TERMS AND CONDITIONS' : 'RETURN ACKNOWLEDGMENT', 50, voucherType.toLowerCase() === 'loan' ? 460 : 480);

  // Add a light background for the terms section with a border
  doc.rect(50, voucherType.toLowerCase() === 'loan' ? 480 : 500, 500, voucherType.toLowerCase() === 'loan' ? 120 : 80)
     .fillAndStroke(voucherType.toLowerCase() === 'loan' ? '#f0f4f8' : '#e6f0ff', '#3366cc');

  if (voucherType.toLowerCase() === 'loan') {
    // Loan terms and conditions
    doc.fontSize(10)
       .font('Helvetica')
       .fillColor('#333333')
       .text('1. The borrower is responsible for the proper care and use of the equipment.', 70, 490);
    doc.text('2. Any damage or loss must be reported immediately.', 70, 510);
    doc.text('3. The equipment must be returned in the same condition as when it was issued.', 70, 530);
    doc.text('4. Late returns may result in penalties as per school policy.', 70, 550);
    doc.text('5. This document must be presented when returning the equipment.', 70, 570);
  } else {
    // Return acknowledgment
    doc.fontSize(10)
       .font('Helvetica')
       .fillColor('#333333')
       .text('1. This document acknowledges that the above item has been returned to the IT department.', 70, 510);
    doc.text('2. The condition of the item has been assessed upon return.', 70, 530);
    doc.text('3. This voucher serves as proof of equipment return for both parties.', 70, 550);
  }

  // Signature section with a header
  const signatureY = voucherType.toLowerCase() === 'loan' ? 620 : 580;

  // Add a header for the signature section
  doc.font('Helvetica-Bold')
     .fillColor('#000066')
     .fontSize(14)
     .text('SIGNATURES', 50, signatureY - 20);

  // Add signature boxes with light backgrounds and borders
  doc.rect(50, signatureY, 230, 80)
     .fillAndStroke('#f0f4f8', '#3366cc');

  doc.rect(320, signatureY, 230, 80)
     .fillAndStroke('#f0f4f8', '#3366cc');

  // First signature line
  doc.fontSize(11)
     .font('Helvetica-Bold')
     .fillColor('#000066')
     .text(voucherType.toLowerCase() === 'loan' ? 'RECIPIENT SIGNATURE' : 'RETURNER SIGNATURE', 50, signatureY + 10, {
       width: 230,
       align: 'center'
     });

  doc.fontSize(10)
     .font('Helvetica')
     .fillColor('#333333')
     .text('Name: ' + (issuedTo || 'N/A'), 70, signatureY + 30);

  doc.moveTo(70, signatureY + 60)
     .lineTo(260, signatureY + 60)
     .lineWidth(1)
     .stroke('#000066');

  doc.fontSize(9)
     .text('Signature', 50, signatureY + 65, {
       width: 230,
       align: 'center'
     });

  // Second signature line
  doc.fontSize(11)
     .font('Helvetica-Bold')
     .fillColor('#000066')
     .text('IT DEPARTMENT SIGNATURE', 320, signatureY + 10, {
       width: 230,
       align: 'center'
     });

  doc.fontSize(10)
     .font('Helvetica')
     .fillColor('#333333')
     .text('Name: ' + (issuedBy || 'IT Staff'), 340, signatureY + 30);

  doc.moveTo(340, signatureY + 60)
     .lineTo(530, signatureY + 60)
     .lineWidth(1)
     .stroke('#000066');

  doc.fontSize(9)
     .text('Signature', 320, signatureY + 65, {
       width: 230,
       align: 'center'
     });

  // Date section
  doc.fontSize(10)
     .font('Helvetica-Bold')
     .text('VOUCHER DATE: ' + (voucherType.toLowerCase() === 'loan' ? issuedDate : returnDate || new Date().toLocaleDateString()),
       50, signatureY + 100, {
         align: 'center',
         width: 500
       });

  // Add footer with a light background
  doc.rect(20, doc.page.height - 50, doc.page.width - 40, 30)
     .fillAndStroke('#f0f4f8', '#cccccc');

  doc.fontSize(8)
     .font('Helvetica')
     .fillColor('#333333')
     .text(
       `This document was generated by the Meritorious EP IT Inventory System on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}.`,
       50, doc.page.height - 40, {
         align: 'center',
         width: doc.page.width - 100
       }
     );

  // Reset text color for future operations
  doc.fillColor('#000000');
}

module.exports = {
  prepareVoucherResponse,
  generateVoucher,
  ensureVoucherDirectoryExists
};
