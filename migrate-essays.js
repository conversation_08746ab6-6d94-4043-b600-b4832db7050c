const db = require('./config/database');
const fs = require('fs');
const path = require('path');

async function runMigration() {
    try {
        console.log('Starting essays migration...');
        
        // Read the migration SQL file
        const migrationSQL = fs.readFileSync(
            path.join(__dirname, 'database/essay-migration.sql'), 
            'utf8'
        );
        
        // Split the SQL into individual statements
        const statements = migrationSQL
            .split(';')
            .filter(statement => statement.trim() !== '');
        
        // Execute each statement
        for (const statement of statements) {
            console.log(`Executing: ${statement.substring(0, 50)}...`);
            await db.query(statement);
        }
        
        console.log('Essays migration completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error running essays migration:', error);
        process.exit(1);
    }
}

// Run the migration
runMigration();
