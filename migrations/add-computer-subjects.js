/**
 * Migration script to add computer-related subjects to the database
 */

const db = require('../config/database');

async function addComputerSubjects() {
  try {
    console.log('Starting migration: Adding computer-related subjects');
    
    // Check if subjects table exists
    const [subjectsTableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'subjects'
    `);
    
    if (subjectsTableExists[0].table_exists === 0) {
      console.log('Subjects table does not exist, creating it...');
      
      // Create subjects table
      await db.query(`
        CREATE TABLE IF NOT EXISTS subjects (
          id INT PRIMARY KEY AUTO_INCREMENT,
          name VARCHAR(100) NOT NULL,
          code VARCHAR(20),
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_subject_name (name)
        )
      `);
      
      console.log('Subjects table created successfully');
    }
    
    // Define the computer-related subjects
    const computerSubjects = [
      { name: 'Computer Science', code: 'CS', description: 'Study of computers and computational systems' },
      { name: 'Computer Applications', code: 'CA', description: 'Practical applications of computer technology' },
      { name: 'Robotics', code: 'ROB', description: 'Design, construction, and use of robots' }
    ];
    
    // Add each subject to the database
    for (const subject of computerSubjects) {
      // Check if subject already exists
      const [existingSubject] = await db.query(`
        SELECT id FROM subjects WHERE name = ? OR code = ?
      `, [subject.name, subject.code]);
      
      if (existingSubject.length === 0) {
        // Subject doesn't exist, add it
        await db.query(`
          INSERT INTO subjects (name, code, description)
          VALUES (?, ?, ?)
        `, [subject.name, subject.code, subject.description]);
        
        console.log(`Added subject: ${subject.name} (${subject.code})`);
      } else {
        // Subject exists, update it
        await db.query(`
          UPDATE subjects
          SET code = ?, description = ?
          WHERE name = ?
        `, [subject.code, subject.description, subject.name]);
        
        console.log(`Updated subject: ${subject.name} (${subject.code})`);
      }
    }
    
    // Check if subject_category table exists
    const [subjectCategoryExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'subject_category'
    `);
    
    if (subjectCategoryExists[0].table_exists === 0) {
      console.log('subject_category table does not exist, creating it...');
      
      // Create subject_category table
      await db.query(`
        CREATE TABLE IF NOT EXISTS subject_category (
          id INT PRIMARY KEY AUTO_INCREMENT,
          subject_id INT NOT NULL,
          category ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 
                       'English', 'Hindi', 'Social Science', 'Economics', 'Commerce', 
                       'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
          is_primary BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_subject_category (subject_id, category)
        )
      `);
      
      console.log('subject_category table created successfully');
    } else {
      // Check if is_primary column exists in subject_category table
      const [isPrimaryColumnExists] = await db.query(`
        SELECT COUNT(*) as column_exists
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'subject_category'
        AND column_name = 'is_primary'
      `);
      
      if (isPrimaryColumnExists[0].column_exists === 0) {
        console.log('Adding is_primary column to subject_category table...');
        
        // Add is_primary column to subject_category table
        await db.query(`
          ALTER TABLE subject_category
          ADD COLUMN is_primary BOOLEAN DEFAULT TRUE AFTER category
        `);
        
        console.log('is_primary column added to subject_category table');
      }
    }
    
    // Get the IDs of the computer subjects
    const [computerScienceSubject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Science'
    `);
    
    const [computerApplicationsSubject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Applications'
    `);
    
    const [roboticsSubject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Robotics'
    `);
    
    // Add categories for the subjects
    if (computerScienceSubject.length > 0) {
      // Add Computer Science as a primary subject
      await db.query(`
        INSERT INTO subject_category (subject_id, category, is_primary)
        VALUES (?, 'Computer Science', TRUE)
        ON DUPLICATE KEY UPDATE is_primary = TRUE
      `, [computerScienceSubject[0].id]);
      
      console.log('Set Computer Science as a primary subject');
    }
    
    if (computerApplicationsSubject.length > 0) {
      // Add Computer Applications as a secondary subject
      await db.query(`
        INSERT INTO subject_category (subject_id, category, is_primary)
        VALUES (?, 'Computer Science', FALSE)
        ON DUPLICATE KEY UPDATE is_primary = FALSE
      `, [computerApplicationsSubject[0].id]);
      
      console.log('Set Computer Applications as a secondary subject');
    }
    
    if (roboticsSubject.length > 0) {
      // Add Robotics as a secondary subject
      await db.query(`
        INSERT INTO subject_category (subject_id, category, is_primary)
        VALUES (?, 'Computer Science', FALSE)
        ON DUPLICATE KEY UPDATE is_primary = FALSE
      `, [roboticsSubject[0].id]);
      
      console.log('Set Robotics as a secondary subject');
    }
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
addComputerSubjects();
