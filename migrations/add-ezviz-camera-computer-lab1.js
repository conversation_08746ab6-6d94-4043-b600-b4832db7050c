/**
 * Migration to add EZVIZ Smart Home Camera to Computer Lab 1
 *
 * This migration:
 * 1. Adds the EZVIZ CS-CP1-LITE camera to inventory_items
 * 2. Links it to Computer Lab 1 room
 * 3. Sets proper equipment details and specifications
 * 4. Updates equipment counts for infrastructure view
 */

const db = require('../config/database');

async function addEzvizCameraToComputerLab1() {
    console.log('🔄 Adding EZVIZ Smart Home Camera to Computer Lab 1...\n');

    try {
        // 1. Get Computer Lab 1 room ID
        console.log('1. Finding Computer Lab 1...');

        const [computerLab1] = await db.query('SELECT id, room_number FROM rooms WHERE room_number = ?', ['Computer Lab 1']);

        if (computerLab1.length === 0) {
            throw new Error('Computer Lab 1 not found');
        }

        const roomId = computerLab1[0].id;
        console.log(`   ✅ Found Computer Lab 1 (ID: ${roomId})`);

        // 2. Check if camera already exists
        console.log('\n2. Checking for existing EZVIZ camera...');

        const [existingCamera] = await db.query(`
            SELECT item_id, name, serial_number
            FROM inventory_items
            WHERE serial_number = ? OR (name LIKE '%EZVIZ%' AND room_id = ?)
        `, ['BD6528592', roomId]);

        if (existingCamera.length > 0) {
            console.log(`   ⚠️  Camera already exists: ${existingCamera[0].name} (${existingCamera[0].serial_number})`);
            console.log('   Skipping insertion to avoid duplicates.');
            return { success: true, action: 'skipped', reason: 'already_exists' };
        }

        console.log('   ✅ No existing EZVIZ camera found, proceeding with insertion');

        // 3. Insert EZVIZ camera into inventory
        console.log('\n3. Adding EZVIZ Smart Home Camera to inventory...');

        const cameraData = {
            name: 'EZVIZ CS-CP1-LITE Smart Camera',
            description: 'EZVIZ Smart Home Camera for Computer Lab 1 security monitoring and surveillance. Model: CS-CP1-LITE, Power: 5V DC 1A 5W Max, Version: R101-1G2WF-LITE, Made in China, Date: 08/2024, Verification: RLPENM',
            manufacturer: 'EZVIZ',
            model: 'CS-CP1-LITE',
            serial_number: 'BD6528592',
            location: 'Computer Lab 1',
            room_id: roomId,
            status: 'available',
            purchase_date: '2024-08-01', // Based on 08/2024 date
            notes: 'Smart security camera for Computer Lab 1 monitoring. Specifications: Input Power 5V DC 1A 5W Max, Verification Code RLPENM, Version R101-1G2WF-LITE, Made in China, Manufacturing Date 08/2024, Certifications: CE Mark, WEEE',
            created_by: 1 // Admin user
        };

        const [insertResult] = await db.query(`
            INSERT INTO inventory_items (
                name, description, manufacturer, model, serial_number, location, room_id,
                status, purchase_date, notes, created_by, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
            cameraData.name,
            cameraData.description,
            cameraData.manufacturer,
            cameraData.model,
            cameraData.serial_number,
            cameraData.location,
            cameraData.room_id,
            cameraData.status,
            cameraData.purchase_date,
            cameraData.notes,
            cameraData.created_by
        ]);

        const newItemId = insertResult.insertId;
        console.log(`   ✅ Added EZVIZ camera with Item ID: ${newItemId}`);

        // 4. Verify the insertion
        console.log('\n4. Verifying camera insertion...');

        const [verifyCamera] = await db.query(`
            SELECT item_id, name, manufacturer, model, serial_number, location, status
            FROM inventory_items
            WHERE item_id = ?
        `, [newItemId]);

        if (verifyCamera.length > 0) {
            const camera = verifyCamera[0];
            console.log('   ✅ Camera successfully added:');
            console.log(`      • Item ID: ${camera.item_id}`);
            console.log(`      • Name: ${camera.name}`);
            console.log(`      • Manufacturer: ${camera.manufacturer}`);
            console.log(`      • Model: ${camera.model}`);
            console.log(`      • Serial Number: ${camera.serial_number}`);
            console.log(`      • Location: ${camera.location}`);
            console.log(`      • Status: ${camera.status}`);
        }

        // 5. Check updated Computer Lab 1 equipment count
        console.log('\n5. Checking updated Computer Lab 1 equipment...');

        const [labEquipment] = await db.query(`
            SELECT
                COUNT(DISTINCT i.item_id) as total_equipment,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%EZVIZ%' OR i.name LIKE '%Camera%' OR i.model LIKE '%Camera%' THEN i.item_id END) as cameras
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.id = ?
            GROUP BY r.id
        `, [roomId]);

        if (labEquipment.length > 0) {
            const equipment = labEquipment[0];
            console.log('   📊 Updated Computer Lab 1 equipment:');
            console.log(`      • Total Equipment: ${equipment.total_equipment} items`);
            console.log(`      • Desktop PCs: ${equipment.desktops}`);
            console.log(`      • Printers: ${equipment.printers}`);
            console.log(`      • UPS Units: ${equipment.ups_units}`);
            console.log(`      • Projectors: ${equipment.projectors}`);
            console.log(`      • Security Cameras: ${equipment.cameras} (NEW!)`);
        }

        // 6. Show detailed equipment list
        console.log('\n6. Complete Computer Lab 1 equipment list...');

        const [allEquipment] = await db.query(`
            SELECT name, manufacturer, model, serial_number, status
            FROM inventory_items
            WHERE room_id = ?
            ORDER BY name
        `, [roomId]);

        console.log(`   📋 All equipment in Computer Lab 1 (${allEquipment.length} items):`);
        allEquipment.forEach((item, index) => {
            console.log(`      ${index + 1}. ${item.name}`);
            console.log(`         • ${item.manufacturer} ${item.model} (${item.serial_number})`);
            console.log(`         • Status: ${item.status}`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('\n🎯 EZVIZ Smart Home Camera Details:');
        console.log('   • Brand: EZVIZ');
        console.log('   • Model: CS-CP1-LITE');
        console.log('   • Serial: BD6528592');
        console.log('   • Power: 5V DC, 1A, 5W Max');
        console.log('   • Version: R101-1G2WF-LITE');
        console.log('   • Origin: Made in China');
        console.log('   • Date: 08/2024');
        console.log('   • Verification: RLPENM');
        console.log('   • Location: Computer Lab 1');
        console.log('   • Purpose: Security monitoring and surveillance');

        console.log('\n📊 Infrastructure Impact:');
        console.log('   • Computer Lab 1 now has security camera monitoring');
        console.log('   • Equipment count increased by 1 item');
        console.log('   • New category: Security Camera added to lab equipment');
        console.log('   • Principal infrastructure view will show updated counts');

        return {
            success: true,
            action: 'inserted',
            itemId: newItemId,
            equipmentCount: labEquipment[0]?.total_equipment || 0
        };

    } catch (error) {
        console.error('❌ Error adding EZVIZ camera:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    addEzvizCameraToComputerLab1()
        .then((results) => {
            console.log('\n🎯 EZVIZ camera added to Computer Lab 1 successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { addEzvizCameraToComputerLab1 };
