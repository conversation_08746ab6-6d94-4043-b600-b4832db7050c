/**
 * Migration to add hostel warden rooms and map hostel equipment
 * 
 * This migration:
 * 1. Adds Boys Hostel Warden Room and Girls Hostel Warden Room to rooms table
 * 2. Maps existing hostel equipment to proper rooms
 * 3. Updates foreign key relationships
 * 4. Preserves original location data
 */

const db = require('../config/database');

async function addHostelWardenRooms() {
    console.log('🔄 Adding Hostel Warden Rooms and Mapping Equipment...\n');

    try {
        // 1. Add hostel warden rooms
        console.log('1. Adding hostel warden rooms to rooms table...');
        
        const hostelRooms = [
            {
                room_number: 'Boys Hostel Warden Room',
                building: 'Boys Hostel',
                floor: 1,
                capacity: 5
            },
            {
                room_number: 'Girls Hostel Warden Room', 
                building: 'Girls Hostel',
                floor: 1,
                capacity: 5
            }
        ];

        const addedRooms = [];
        
        for (const room of hostelRooms) {
            // Check if room already exists
            const [existing] = await db.query('SELECT id FROM rooms WHERE room_number = ?', [room.room_number]);
            
            if (existing.length === 0) {
                const [result] = await db.query(`
                    INSERT INTO rooms (room_number, building, floor, capacity, created_at, updated_at)
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                `, [room.room_number, room.building, room.floor, room.capacity]);
                
                addedRooms.push({ ...room, id: result.insertId });
                console.log(`   ✅ Added: ${room.room_number} (ID: ${result.insertId})`);
            } else {
                console.log(`   ⚠️  Already exists: ${room.room_number} (ID: ${existing[0].id})`);
                addedRooms.push({ ...room, id: existing[0].id });
            }
        }

        // 2. Get all rooms for mapping
        console.log('\n2. Getting room mappings...');
        const [allRooms] = await db.query('SELECT id, room_number FROM rooms ORDER BY room_number');
        
        const roomMap = new Map();
        allRooms.forEach(room => {
            roomMap.set(room.room_number, room.id);
        });
        
        console.log(`   ✅ Found ${allRooms.length} rooms total`);

        // 3. Create hostel location mappings
        console.log('\n3. Creating hostel location mappings...');
        
        const hostelLocationMappings = new Map([
            ['Boys Hostel', 'Boys Hostel Warden Room'],
            ['Girls Hostel', 'Girls Hostel Warden Room'],
            ['Warden Office', 'Warden Office'] // Keep existing mapping
        ]);

        // 4. Update inventory_items for hostel locations
        console.log('\n4. Updating inventory_items for hostel locations...');
        
        const [hostelInventoryItems] = await db.query(`
            SELECT item_id, location 
            FROM inventory_items 
            WHERE location IN ('Boys Hostel', 'Girls Hostel', 'Warden Office')
        `);
        
        let inventoryUpdated = 0;
        
        for (const item of hostelInventoryItems) {
            const location = item.location.trim();
            
            if (hostelLocationMappings.has(location)) {
                const mappedRoom = hostelLocationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE inventory_items SET room_id = ? WHERE item_id = ?', [roomId, item.item_id]);
                    console.log(`   ✅ Item ${item.item_id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    inventoryUpdated++;
                }
            }
        }

        // 5. Update it_inventory for hostel locations (if any)
        console.log('\n5. Checking it_inventory for hostel locations...');
        
        const [hostelITItems] = await db.query(`
            SELECT id, location 
            FROM it_inventory 
            WHERE location IN ('Boys Hostel', 'Girls Hostel', 'Warden Office')
        `);
        
        let itUpdated = 0;
        
        for (const item of hostelITItems) {
            const location = item.location.trim();
            
            if (hostelLocationMappings.has(location)) {
                const mappedRoom = hostelLocationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE it_inventory SET room_id = ? WHERE id = ?', [roomId, item.id]);
                    console.log(`   ✅ IT Item ${item.id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    itUpdated++;
                }
            }
        }

        // 6. Update electrical_inventory for hostel locations (if any)
        console.log('\n6. Checking electrical_inventory for hostel locations...');
        
        const [hostelElectricalItems] = await db.query(`
            SELECT id, location 
            FROM electrical_inventory 
            WHERE location IN ('Boys Hostel', 'Girls Hostel', 'Warden Office')
        `);
        
        let electricalUpdated = 0;
        
        for (const item of hostelElectricalItems) {
            if (!item.location) continue;
            
            const location = item.location.trim();
            
            if (hostelLocationMappings.has(location)) {
                const mappedRoom = hostelLocationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE electrical_inventory SET room_id = ? WHERE id = ?', [roomId, item.id]);
                    console.log(`   ✅ Electrical Item ${item.id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    electricalUpdated++;
                }
            }
        }

        // 7. Verify hostel equipment mapping
        console.log('\n7. Verifying hostel equipment mapping...');
        
        const [hostelEquipmentSummary] = await db.query(`
            SELECT 
                r.room_number,
                r.id as room_id,
                r.building,
                COUNT(DISTINCT i.item_id) as inventory_items,
                COUNT(DISTINCT it.id) as it_items,
                COUNT(DISTINCT e.id) as electrical_items,
                (COUNT(DISTINCT i.item_id) + COUNT(DISTINCT it.id) + COUNT(DISTINCT e.id)) as total_equipment
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            LEFT JOIN it_inventory it ON r.id = it.room_id
            LEFT JOIN electrical_inventory e ON r.id = e.room_id
            WHERE r.room_number LIKE '%Hostel%' OR r.room_number = 'Warden Office'
            GROUP BY r.id, r.room_number, r.building
            ORDER BY r.room_number
        `);

        console.log(`   📋 Hostel equipment distribution:`);
        hostelEquipmentSummary.forEach(room => {
            console.log(`      • ${room.room_number} (${room.building}): ${room.total_equipment} items`);
            console.log(`        Inventory: ${room.inventory_items}, IT: ${room.it_items}, Electrical: ${room.electrical_items}`);
        });

        // 8. Show detailed equipment for each hostel room
        console.log('\n8. Detailed hostel equipment breakdown...');
        
        for (const room of hostelEquipmentSummary) {
            if (room.total_equipment > 0) {
                console.log(`\n   📍 ${room.room_number}:`);
                
                // Get inventory items
                const [items] = await db.query(`
                    SELECT name, manufacturer, model, serial_number, status
                    FROM inventory_items 
                    WHERE room_id = ?
                    ORDER BY name
                `, [room.room_id]);
                
                items.forEach((item, index) => {
                    console.log(`      ${index + 1}. ${item.name}`);
                    console.log(`         • Manufacturer: ${item.manufacturer || 'N/A'}`);
                    console.log(`         • Model: ${item.model || 'N/A'}`);
                    console.log(`         • Serial: ${item.serial_number || 'N/A'}`);
                    console.log(`         • Status: ${item.status || 'N/A'}`);
                });
            }
        }

        console.log('\n✅ Migration completed successfully!');
        console.log(`📊 Summary:`);
        console.log(`   • Hostel rooms added: ${addedRooms.length}`);
        console.log(`   • Inventory items updated: ${inventoryUpdated}`);
        console.log(`   • IT items updated: ${itUpdated}`);
        console.log(`   • Electrical items updated: ${electricalUpdated}`);
        console.log(`   • Hostel warden rooms now properly configured with equipment`);

        return {
            success: true,
            roomsAdded: addedRooms.length,
            inventoryUpdated,
            itUpdated,
            electricalUpdated
        };

    } catch (error) {
        console.error('❌ Error adding hostel warden rooms:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    addHostelWardenRooms()
        .then((results) => {
            console.log('\n🎯 Hostel warden rooms migration completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { addHostelWardenRooms };
