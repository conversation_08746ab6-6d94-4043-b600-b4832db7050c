/**
 * Migration to add room_id foreign key to electrical_inventory table
 * 
 * This migration:
 * 1. Adds room_id column to electrical_inventory table
 * 2. Creates foreign key constraint to rooms table
 * 3. Maps existing room_number data to room_id
 * 4. Keeps original room_number and location data intact
 * 5. Provides proper database-level relationship
 */

const db = require('../config/database');

async function addRoomIdToElectricalInventory() {
    console.log('🔄 Adding room_id foreign key to electrical_inventory table...\n');

    try {
        // 1. Check if room_id column already exists
        console.log('1. Checking if room_id column already exists...');
        
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'electrical_inventory' AND COLUMN_NAME = 'room_id'
        `, [process.env.DB_NAME || 'exam_prep_platform']);

        if (columns.length > 0) {
            console.log('   ✅ room_id column already exists');
        } else {
            // 2. Add room_id column
            console.log('\n2. Adding room_id column to electrical_inventory table...');
            
            await db.query(`
                ALTER TABLE electrical_inventory 
                ADD COLUMN room_id INT NULL AFTER location,
                ADD INDEX idx_electrical_room_id (room_id)
            `);
            
            console.log('   ✅ room_id column added successfully');
        }

        // 3. Get all rooms for mapping
        console.log('\n3. Getting room mappings...');
        const [rooms] = await db.query(`
            SELECT id, room_number 
            FROM rooms 
            ORDER BY room_number
        `);

        // Create room lookup maps
        const roomByNumber = new Map();
        const roomByNumericId = new Map();
        
        rooms.forEach(room => {
            roomByNumber.set(room.room_number, room.id);
            
            // Extract numeric part for pattern matching
            const numericMatch = room.room_number.match(/(\d+)/);
            if (numericMatch) {
                roomByNumericId.set(numericMatch[1], room.id);
            }
        });

        console.log(`   ✅ Found ${rooms.length} rooms for mapping`);

        // 4. Get all electrical inventory items
        console.log('\n4. Analyzing electrical inventory items...');
        
        const [electricalItems] = await db.query(`
            SELECT id, item_name, room_number, location 
            FROM electrical_inventory 
            ORDER BY id
        `);

        console.log(`   📋 Found ${electricalItems.length} electrical inventory items`);

        // 5. Apply mappings and populate room_id
        console.log('\n5. Populating room_id based on room_number and location...');
        
        let updatedCount = 0;
        let unmatchedCount = 0;
        const unmatchedItems = [];

        for (const item of electricalItems) {
            let roomId = null;
            let matchReason = '';

            // Try room_number first (most reliable)
            if (item.room_number !== null && item.room_number !== undefined) {
                const roomNumberStr = item.room_number.toString();
                
                // Handle room_number = 0 (common areas)
                if (roomNumberStr === '0') {
                    // Skip room 0 items (common areas like electrical room, corridors)
                    console.log(`   ⚠️  Item ${item.id}: Skipping common area (room_number: 0) - ${item.item_name}`);
                    unmatchedCount++;
                    continue;
                }
                
                // Try to match with room numbers
                roomId = roomByNumericId.get(roomNumberStr);
                if (roomId) {
                    matchReason = `Room number match: ${roomNumberStr} → Room ${roomNumberStr}`;
                }
            }

            // Try location-based matching if room_number didn't work
            if (!roomId && item.location) {
                const location = item.location.trim();
                
                // Pattern matching for locations
                const locationPatterns = [
                    { regex: /Room\s+(\d+)/i, format: (match) => match[1] },
                    { regex: /Classroom\s+(\d+)/i, format: (match) => match[1] },
                    { regex: /R\.\s*No\s*-?\s*(\d+)/i, format: (match) => match[1] }
                ];

                for (const pattern of locationPatterns) {
                    const match = location.match(pattern.regex);
                    if (match) {
                        const roomNum = pattern.format(match);
                        roomId = roomByNumericId.get(roomNum);
                        if (roomId) {
                            matchReason = `Location pattern match: "${location}" → Room ${roomNum}`;
                            break;
                        }
                    }
                }

                // Special location mappings
                if (!roomId) {
                    const specialMappings = {
                        'principal office': '20',
                        'admin office': '20',
                        'staff room': '18',
                        'library': '7',
                        'computer lab 1': '1',
                        'computer lab 2': '2',
                        'science lab': '4',
                        'physics lab': '6',
                        'chemistry lab': '5',
                        'biology lab': '4'
                    };

                    const lowerLocation = location.toLowerCase();
                    for (const [key, value] of Object.entries(specialMappings)) {
                        if (lowerLocation.includes(key)) {
                            roomId = roomByNumericId.get(value);
                            if (roomId) {
                                matchReason = `Special location mapping: "${location}" → Room ${value}`;
                                break;
                            }
                        }
                    }
                }
            }

            // Update database if room found
            if (roomId) {
                await db.query(`
                    UPDATE electrical_inventory 
                    SET room_id = ? 
                    WHERE id = ?
                `, [roomId, item.id]);
                
                console.log(`   ✅ Item ${item.id}: ${matchReason}`);
                updatedCount++;
            } else {
                console.log(`   ⚠️  Item ${item.id}: No room match - ${item.item_name} (Room: ${item.room_number}, Location: ${item.location || 'N/A'})`);
                unmatchedItems.push(item);
                unmatchedCount++;
            }
        }

        // 6. Add foreign key constraint if not exists
        console.log('\n6. Adding foreign key constraint...');
        
        try {
            await db.query(`
                ALTER TABLE electrical_inventory 
                ADD CONSTRAINT fk_electrical_inventory_room_id 
                FOREIGN KEY (room_id) REFERENCES rooms(id) 
                ON DELETE SET NULL 
                ON UPDATE CASCADE
            `);
            console.log('   ✅ Foreign key constraint added successfully');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('   ✅ Foreign key constraint already exists');
            } else {
                throw error;
            }
        }

        // 7. Verify results
        console.log('\n7. Verifying results...');
        
        const [verifyResults] = await db.query(`
            SELECT 
                COUNT(*) as total_items,
                COUNT(room_id) as items_with_room_id,
                COUNT(DISTINCT room_id) as unique_rooms_used
            FROM electrical_inventory
        `);

        console.log(`   📊 Verification Results:`);
        console.log(`      • Total electrical items: ${verifyResults[0].total_items}`);
        console.log(`      • Items with room_id: ${verifyResults[0].items_with_room_id}`);
        console.log(`      • Unique rooms used: ${verifyResults[0].unique_rooms_used}`);
        console.log(`      • Successfully mapped: ${updatedCount}`);
        console.log(`      • Unmatched: ${unmatchedCount}`);

        // 8. Show room distribution
        console.log('\n8. Electrical equipment distribution by room...');
        
        const [roomDistribution] = await db.query(`
            SELECT 
                r.room_number,
                r.id as room_id,
                COUNT(e.id) as item_count,
                GROUP_CONCAT(DISTINCT e.item_type ORDER BY e.item_type) as equipment_types
            FROM rooms r
            LEFT JOIN electrical_inventory e ON r.id = e.room_id
            WHERE e.id IS NOT NULL
            GROUP BY r.id, r.room_number
            ORDER BY r.room_number
        `);

        console.log(`   📋 Electrical equipment distribution by room:`);
        roomDistribution.forEach(room => {
            console.log(`      • ${room.room_number} (ID: ${room.room_id}): ${room.item_count} items`);
            console.log(`        Types: ${room.equipment_types || 'None'}`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('📝 Note: Original room_number and location data preserved');
        console.log('🔗 electrical_inventory table now properly linked to rooms via foreign key');

        return {
            success: true,
            totalItems: verifyResults[0].total_items,
            itemsWithRoomId: verifyResults[0].items_with_room_id,
            uniqueRoomsUsed: verifyResults[0].unique_rooms_used,
            updatedCount,
            unmatchedCount
        };

    } catch (error) {
        console.error('❌ Error adding room_id to electrical_inventory:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    addRoomIdToElectricalInventory()
        .then((results) => {
            console.log('\n🎯 Migration completed successfully!');
            console.log(`📈 Success Rate: ${(results.itemsWithRoomId / results.totalItems * 100).toFixed(1)}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { addRoomIdToElectricalInventory };
