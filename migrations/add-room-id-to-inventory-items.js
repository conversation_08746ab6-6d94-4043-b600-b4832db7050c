/**
 * Migration to add room_id foreign key to inventory_items table
 * 
 * This migration:
 * 1. Adds room_id column to inventory_items table
 * 2. Creates foreign key constraint to rooms table
 * 3. Maps existing location data to room numbers
 * 4. Populates room_id based on location patterns
 * 5. Keeps original location data intact
 */

const db = require('../config/database');

async function addRoomIdToInventoryItems() {
    console.log('🔄 Adding room_id foreign key to inventory_items table...\n');

    try {
        // 1. Check if room_id column already exists
        console.log('1. Checking if room_id column already exists...');
        
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory_items' AND COLUMN_NAME = 'room_id'
        `, [process.env.DB_NAME || 'exam_prep_platform']);

        if (columns.length > 0) {
            console.log('   ✅ room_id column already exists');
        } else {
            // 2. Add room_id column
            console.log('\n2. Adding room_id column to inventory_items table...');
            
            await db.query(`
                ALTER TABLE inventory_items 
                ADD COLUMN room_id INT NULL AFTER location,
                ADD INDEX idx_inventory_room_id (room_id)
            `);
            
            console.log('   ✅ room_id column added successfully');
        }

        // 3. Get all rooms for mapping
        console.log('\n3. Getting room mappings...');
        const [rooms] = await db.query(`
            SELECT id, room_number 
            FROM rooms 
            ORDER BY room_number
        `);

        // Create room lookup maps
        const roomByNumber = new Map();
        const roomByNumericId = new Map();
        
        rooms.forEach(room => {
            roomByNumber.set(room.room_number, room.id);
            
            // Extract numeric part for pattern matching
            const numericMatch = room.room_number.match(/(\d+)/);
            if (numericMatch) {
                roomByNumericId.set(numericMatch[1], room.id);
            }
        });

        console.log(`   ✅ Found ${rooms.length} rooms for mapping`);

        // 4. Get all inventory items with locations
        console.log('\n4. Analyzing inventory items locations...');
        
        const [inventoryItems] = await db.query(`
            SELECT item_id, location 
            FROM inventory_items 
            WHERE location IS NOT NULL AND location != '' AND location != 'null'
            ORDER BY item_id
        `);

        console.log(`   📋 Found ${inventoryItems.length} items with location data`);

        // 5. Create location to room mapping rules
        const locationMappings = new Map([
            // Direct room mappings
            ['Room 3', 'Room 3'],
            ['Room 5', 'Room 5'],
            
            // Lab mappings
            ['Computer Lab 1', 'Room 1'],
            ['Computer Lab 2', 'Room 2'],
            ['Computer Lab-1', 'Room 1'],
            ['Computer Lab-2', 'Room 2'],
            ['Computer Lab - 1', 'Room 1'],
            ['Computer Lab - 2', 'Room 2'],
            ['Computer LAB-1', 'Room 1'],
            ['Computer LAB-2', 'Room 2'],
            ['Biology Lab', 'Room 4'],
            ['Chemistry Lab', 'Room 5'],
            ['Physics Lab', 'Room 6'],
            ['Science LAB Room -1 (BIOLOGY)', 'Room 4'],
            ['Science LAB Room -2 (PHYSICS)', 'Room 6'],
            ['Science LAB Room -3 (CHEMSTRY)', 'Room 5'],
            
            // Office mappings
            ['Principal Office', 'Room 20'],
            ['Principal Room', 'Room 20'],
            ['Vice Principal Office', 'Room 19'],
            ['Admin Office', 'Room 20'],
            ['Office Room', 'Room 20'],
            ['Computer Office', 'Room 20'],
            ['Staff Room', 'Room 18'],
            ['Warden Office', 'Room 17'],
            
            // Storage and technical rooms
            ['IT Storage Room', 'Room 16'],
            ['IT Department', 'Room 16'],
            ['Server Room', 'Room 16'],
            ['Library', 'Room 7'],
            ['Library Room', 'Room 7'],
            
            // Hostel mappings (using available rooms)
            ['Boys Hostel', 'Room 15'],
            ['Girls Hostel', 'Room 14'],
            ['Coaching Department', 'Room 13']
        ]);

        // 6. Apply mappings and populate room_id
        console.log('\n5. Populating room_id based on location patterns...');
        
        let updatedCount = 0;
        let unmatchedCount = 0;
        const unmatchedLocations = new Set();

        for (const item of inventoryItems) {
            const location = item.location.trim();
            let roomId = null;
            let matchReason = '';

            // Try direct mapping first
            if (locationMappings.has(location)) {
                const mappedRoom = locationMappings.get(location);
                roomId = roomByNumber.get(mappedRoom);
                matchReason = `Direct mapping: ${location} → ${mappedRoom}`;
            }
            
            // Try room number pattern matching
            if (!roomId) {
                // Pattern: "R. No-X" or "R. No -X" or "Room No-X"
                const roomPatterns = [
                    /R\.\s*No\s*-?\s*(\d+)/i,
                    /Room\s*No\s*-?\s*(\d+)/i,
                    /Room\s+(\d+)/i
                ];

                for (const pattern of roomPatterns) {
                    const match = location.match(pattern);
                    if (match) {
                        const roomNum = match[1];
                        roomId = roomByNumericId.get(roomNum);
                        if (roomId) {
                            matchReason = `Pattern match: ${location} → Room ${roomNum}`;
                            break;
                        }
                    }
                }
            }

            // Update database if room found
            if (roomId) {
                await db.query(`
                    UPDATE inventory_items 
                    SET room_id = ? 
                    WHERE item_id = ?
                `, [roomId, item.item_id]);
                
                console.log(`   ✅ Item ${item.item_id}: ${matchReason}`);
                updatedCount++;
            } else {
                console.log(`   ⚠️  Item ${item.item_id}: No room match for "${location}"`);
                unmatchedLocations.add(location);
                unmatchedCount++;
            }
        }

        // 7. Add foreign key constraint if not exists
        console.log('\n6. Adding foreign key constraint...');
        
        try {
            await db.query(`
                ALTER TABLE inventory_items 
                ADD CONSTRAINT fk_inventory_items_room_id 
                FOREIGN KEY (room_id) REFERENCES rooms(id) 
                ON DELETE SET NULL 
                ON UPDATE CASCADE
            `);
            console.log('   ✅ Foreign key constraint added successfully');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('   ✅ Foreign key constraint already exists');
            } else {
                throw error;
            }
        }

        // 8. Verify results
        console.log('\n7. Verifying results...');
        
        const [verifyResults] = await db.query(`
            SELECT 
                COUNT(*) as total_items,
                COUNT(room_id) as items_with_room_id,
                COUNT(DISTINCT room_id) as unique_rooms_used
            FROM inventory_items
            WHERE location IS NOT NULL AND location != '' AND location != 'null'
        `);

        console.log(`   📊 Verification Results:`);
        console.log(`      • Total items with location: ${verifyResults[0].total_items}`);
        console.log(`      • Items with room_id: ${verifyResults[0].items_with_room_id}`);
        console.log(`      • Unique rooms used: ${verifyResults[0].unique_rooms_used}`);
        console.log(`      • Successfully mapped: ${updatedCount}`);
        console.log(`      • Unmatched: ${unmatchedCount}`);

        // Show unmatched locations
        if (unmatchedLocations.size > 0) {
            console.log(`\n   ⚠️  Unmatched locations (${unmatchedLocations.size}):`);
            unmatchedLocations.forEach(location => {
                console.log(`      • "${location}"`);
            });
        }

        // 9. Show room distribution
        console.log('\n8. Equipment distribution by room...');
        
        const [roomDistribution] = await db.query(`
            SELECT 
                r.room_number,
                r.id as room_id,
                COUNT(i.item_id) as item_count,
                GROUP_CONCAT(DISTINCT 
                    CASE 
                        WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'Projector'
                        WHEN i.name LIKE '%UPS%' THEN 'UPS'
                        WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'Desktop'
                        WHEN i.name LIKE '%Laptop%' THEN 'Laptop'
                        WHEN i.name LIKE '%PANEL%' THEN 'Interactive Panel'
                        WHEN i.name LIKE '%CAMERA%' THEN 'Camera'
                        WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'Printer'
                        WHEN i.name LIKE '%Router%' THEN 'Router'
                        ELSE 'Other'
                    END
                ) as equipment_types
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE i.item_id IS NOT NULL
            GROUP BY r.id, r.room_number
            ORDER BY r.room_number
        `);

        console.log(`   📋 Equipment distribution by room:`);
        roomDistribution.forEach(room => {
            console.log(`      • ${room.room_number} (ID: ${room.room_id}): ${room.item_count} items`);
            console.log(`        Types: ${room.equipment_types || 'None'}`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('📝 Note: Original location data preserved in location column');
        console.log('🔗 inventory_items table now properly linked to rooms via foreign key');

        return {
            success: true,
            totalItems: verifyResults[0].total_items,
            itemsWithRoomId: verifyResults[0].items_with_room_id,
            uniqueRoomsUsed: verifyResults[0].unique_rooms_used,
            updatedCount,
            unmatchedCount
        };

    } catch (error) {
        console.error('❌ Error adding room_id to inventory_items:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    addRoomIdToInventoryItems()
        .then((results) => {
            console.log('\n🎯 Migration completed successfully!');
            console.log(`📈 Success Rate: ${(results.itemsWithRoomId / results.totalItems * 100).toFixed(1)}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { addRoomIdToInventoryItems };
