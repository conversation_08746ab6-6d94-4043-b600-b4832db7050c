/**
 * Migration to add room_id foreign key to it_inventory table
 * 
 * This migration:
 * 1. Adds room_id column to it_inventory table
 * 2. Populates room_id based on existing location data
 * 3. Creates foreign key constraint to rooms table
 * 4. Keeps location column for backward compatibility
 * 5. Provides rollback functionality
 */

const db = require('../config/database');

async function addRoomIdToITInventory() {
    console.log('🔄 Adding room_id foreign key to it_inventory table...\n');

    try {
        // 1. Check if room_id column already exists
        console.log('1. Checking if room_id column already exists...');
        
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'it_inventory' AND COLUMN_NAME = 'room_id'
        `, [process.env.DB_NAME || 'exam_prep_platform']);

        if (columns.length > 0) {
            console.log('   ✅ room_id column already exists');
            return;
        }

        // 2. Add room_id column
        console.log('\n2. Adding room_id column to it_inventory table...');
        
        await db.query(`
            ALTER TABLE it_inventory 
            ADD COLUMN room_id INT NULL AFTER location,
            ADD INDEX idx_room_id (room_id)
        `);
        
        console.log('   ✅ room_id column added successfully');

        // 3. Populate room_id based on existing location data
        console.log('\n3. Populating room_id based on existing location data...');
        
        // Get all IT inventory items with locations
        const [itItems] = await db.query(`
            SELECT id, location 
            FROM it_inventory 
            WHERE location IS NOT NULL AND location != ''
        `);

        // Get all rooms for mapping
        const [rooms] = await db.query(`
            SELECT id, room_number 
            FROM rooms 
            ORDER BY room_number
        `);

        // Create room lookup map
        const roomMap = new Map();
        rooms.forEach(room => {
            roomMap.set(room.room_number, room.id);
        });

        let updatedCount = 0;
        let unmatchedCount = 0;

        for (const item of itItems) {
            const location = item.location.trim();
            let roomId = null;

            // Direct match
            if (roomMap.has(location)) {
                roomId = roomMap.get(location);
            } else {
                // Try pattern matching for non-standard formats
                for (const [roomNumber, id] of roomMap) {
                    const numericPart = roomNumber.match(/(\d+)/)?.[1];
                    
                    if (numericPart && (
                        location.toLowerCase().includes(`room ${numericPart}`) ||
                        location.toLowerCase().includes(`classroom ${numericPart}`) ||
                        location === numericPart
                    )) {
                        roomId = id;
                        break;
                    }
                }
            }

            if (roomId) {
                await db.query(`
                    UPDATE it_inventory 
                    SET room_id = ? 
                    WHERE id = ?
                `, [roomId, item.id]);
                
                console.log(`   ✅ Updated item ${item.id}: "${location}" → Room ID ${roomId}`);
                updatedCount++;
            } else {
                console.log(`   ⚠️  No room match found for item ${item.id}: "${location}"`);
                unmatchedCount++;
            }
        }

        console.log(`\n   📊 Population Results:`);
        console.log(`      ✅ Successfully mapped: ${updatedCount} items`);
        console.log(`      ⚠️  Unmatched: ${unmatchedCount} items`);

        // 4. Add foreign key constraint
        console.log('\n4. Adding foreign key constraint...');
        
        await db.query(`
            ALTER TABLE it_inventory 
            ADD CONSTRAINT fk_it_inventory_room_id 
            FOREIGN KEY (room_id) REFERENCES rooms(id) 
            ON DELETE SET NULL 
            ON UPDATE CASCADE
        `);
        
        console.log('   ✅ Foreign key constraint added successfully');

        // 5. Verify the changes
        console.log('\n5. Verifying the changes...');
        
        const [verifyResults] = await db.query(`
            SELECT 
                COUNT(*) as total_items,
                COUNT(room_id) as items_with_room_id,
                COUNT(DISTINCT room_id) as unique_rooms_used
            FROM it_inventory
        `);

        const [constraintCheck] = await db.query(`
            SELECT 
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = ? 
            AND TABLE_NAME = 'it_inventory' 
            AND COLUMN_NAME = 'room_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        `, [process.env.DB_NAME || 'exam_prep_platform']);

        console.log(`   📊 Verification Results:`);
        console.log(`      • Total IT items: ${verifyResults[0].total_items}`);
        console.log(`      • Items with room_id: ${verifyResults[0].items_with_room_id}`);
        console.log(`      • Unique rooms used: ${verifyResults[0].unique_rooms_used}`);
        console.log(`      • Foreign key constraint: ${constraintCheck.length > 0 ? '✅ Active' : '❌ Missing'}`);

        // 6. Show room distribution
        console.log('\n6. Room distribution summary...');
        
        const [roomDistribution] = await db.query(`
            SELECT 
                r.room_number,
                r.id as room_id,
                COUNT(i.id) as item_count,
                GROUP_CONCAT(DISTINCT i.type) as equipment_types
            FROM rooms r
            LEFT JOIN it_inventory i ON r.id = i.room_id
            GROUP BY r.id, r.room_number
            HAVING item_count > 0
            ORDER BY r.room_number
        `);

        console.log(`   📋 Equipment distribution by room:`);
        roomDistribution.forEach(room => {
            console.log(`      • ${room.room_number} (ID: ${room.room_id}): ${room.item_count} items (${room.equipment_types || 'none'})`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('📝 Note: The location column is kept for backward compatibility');
        console.log('🔗 IT inventory is now properly linked to rooms via foreign key');

        return {
            success: true,
            totalItems: verifyResults[0].total_items,
            itemsWithRoomId: verifyResults[0].items_with_room_id,
            uniqueRoomsUsed: verifyResults[0].unique_rooms_used,
            updatedCount,
            unmatchedCount
        };

    } catch (error) {
        console.error('❌ Error adding room_id to it_inventory:', error);
        throw error;
    }
}

// Rollback function
async function rollbackRoomIdFromITInventory() {
    console.log('🔄 Rolling back room_id changes from it_inventory table...\n');

    try {
        // Remove foreign key constraint
        console.log('1. Removing foreign key constraint...');
        
        try {
            await db.query(`
                ALTER TABLE it_inventory 
                DROP FOREIGN KEY fk_it_inventory_room_id
            `);
            console.log('   ✅ Foreign key constraint removed');
        } catch (error) {
            console.log('   ⚠️  Foreign key constraint not found or already removed');
        }

        // Remove room_id column
        console.log('\n2. Removing room_id column...');
        
        try {
            await db.query(`
                ALTER TABLE it_inventory 
                DROP COLUMN room_id
            `);
            console.log('   ✅ room_id column removed');
        } catch (error) {
            console.log('   ⚠️  room_id column not found or already removed');
        }

        console.log('\n✅ Rollback completed successfully!');

    } catch (error) {
        console.error('❌ Error during rollback:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    const action = process.argv[2];
    
    if (action === 'rollback') {
        rollbackRoomIdFromITInventory()
            .then(() => {
                console.log('\n🎯 Rollback completed successfully!');
                process.exit(0);
            })
            .catch((error) => {
                console.error('❌ Rollback failed:', error);
                process.exit(1);
            });
    } else {
        addRoomIdToITInventory()
            .then((results) => {
                console.log('\n🎯 Migration completed successfully!');
                console.log(`📈 Success Rate: ${(results.itemsWithRoomId / results.totalItems * 100).toFixed(1)}%`);
                process.exit(0);
            })
            .catch((error) => {
                console.error('❌ Migration failed:', error);
                process.exit(1);
            });
    }
}

module.exports = { 
    addRoomIdToITInventory, 
    rollbackRoomIdFromITInventory 
};
