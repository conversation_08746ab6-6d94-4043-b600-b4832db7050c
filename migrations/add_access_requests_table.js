/**
 * Migration to add access_requests table
 */

const db = require('../config/database');

async function addAccessRequestsTable() {
    try {
        console.log('Starting migration: Adding access_requests table');
        
        // Check if the table already exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'access_requests'
        `);
        
        if (tables.length === 0) {
            // Create the access_requests table
            await db.query(`
                CREATE TABLE access_requests (
                    id INT NOT NULL AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    exam_id INT NOT NULL,
                    message TEXT,
                    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    requested_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP NULL DEFAULT NULL,
                    processed_by INT NULL DEFAULT NULL,
                    additional_attempts INT DEFAULT 1,
                    PRIMARY KEY (id),
                    KEY idx_user_exam (user_id, exam_id),
                    KEY idx_status (status),
                    CONSTRAINT fk_access_requests_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    CONSTRAINT fk_access_requests_exam FOREIGN KEY (exam_id) REFERENCES exams (exam_id) ON DELETE CASCADE,
                    CONSTRAINT fk_access_requests_admin FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('✅ Successfully created access_requests table');
        } else {
            console.log('✅ access_requests table already exists');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addAccessRequestsTable()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
