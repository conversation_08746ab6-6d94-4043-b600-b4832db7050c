/**
 * Migration to add continuation_count column to exam_attempts table
 */

const db = require('../config/database');

async function addContinuationCountColumn() {
    try {
        console.log('Starting migration: Adding continuation_count column to exam_attempts table');
        
        // Check if the column already exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'exam_attempts' 
            AND COLUMN_NAME = 'continuation_count'
        `);
        
        if (columns.length === 0) {
            // Add the continuation_count column
            await db.query(`
                ALTER TABLE exam_attempts 
                ADD COLUMN continuation_count INT DEFAULT 0
            `);
            console.log('✅ Successfully added continuation_count column to exam_attempts table');
        } else {
            console.log('✅ continuation_count column already exists in exam_attempts table');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addContinuationCountColumn()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
