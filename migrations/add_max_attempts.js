/**
 * Migration to add max_attempts column to exams table
 */

const db = require('../config/database');

async function addMaxAttemptsColumn() {
    try {
        console.log('Starting migration: Adding max_attempts column to exams table');
        
        // Check if the column already exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'exams' 
            AND COLUMN_NAME = 'max_attempts'
        `);
        
        if (columns.length === 0) {
            // Add the max_attempts column with default value of 1
            await db.query(`
                ALTER TABLE exams 
                ADD COLUMN max_attempts INT DEFAULT 1 COMMENT 'Maximum number of attempts allowed per user'
            `);
            console.log('✅ Successfully added max_attempts column to exams table');
        } else {
            console.log('✅ max_attempts column already exists in exams table');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addMaxAttemptsColumn()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
