/**
 * Migration to add passing_marks column to sections table
 */

const db = require('../config/database');
const fs = require('fs');
const path = require('path');

async function addPassingMarksToSections() {
    try {
        console.log('Starting migration: Adding passing_marks column to sections table');
        
        // Check if the column already exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'sections' 
            AND COLUMN_NAME = 'passing_marks'
        `);
        
        if (columns.length === 0) {
            // Read the SQL file
            const sqlPath = path.join(__dirname, '..', 'database', 'migrations', 'add_passing_marks_to_sections.sql');
            const sql = fs.readFileSync(sqlPath, 'utf8');
            
            // Execute the SQL
            await db.query(sql);
            console.log('✅ Successfully added passing_marks column to sections table');
        } else {
            console.log('✅ passing_marks column already exists in sections table');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addPassingMarksToSections()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
