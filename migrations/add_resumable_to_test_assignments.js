/**
 * Migration to add is_resumable column to test_assignments table
 */

const db = require('../config/database');

async function addResumableToTestAssignments() {
    try {
        console.log('Starting migration: Adding is_resumable column to test_assignments table');

        // Check if the column already exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'exam_prep_platform'
            AND TABLE_NAME = 'test_assignments'
            AND COLUMN_NAME = 'is_resumable'
        `);

        if (columns.length === 0) {
            // Add the is_resumable column
            await db.query(`
                ALTER TABLE test_assignments
                ADD COLUMN is_resumable TINYINT(1) NOT NULL DEFAULT 0
                AFTER max_attempts
            `);
            console.log('✅ Successfully added is_resumable column to test_assignments table');
        } else {
            console.log('✅ is_resumable column already exists in test_assignments table');
        }

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addResumableToTestAssignments()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
