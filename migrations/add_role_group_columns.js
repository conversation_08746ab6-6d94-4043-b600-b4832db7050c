/**
 * Migration to add role group columns to groups table
 */
const db = require('../config/database');

async function runMigration() {
    try {
        console.log('Starting migration: Adding role group columns to groups table');
        
        // Check if columns already exist
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'groups' 
            AND COLUMN_NAME IN ('is_role_group', 'role_type')
        `);
        
        if (columns.length === 2) {
            console.log('Columns already exist, skipping migration');
            return;
        }
        
        // Add columns if they don't exist
        if (!columns.find(col => col.COLUMN_NAME === 'is_role_group')) {
            await db.query(`
                ALTER TABLE groups 
                ADD COLUMN is_role_group TINYINT(1) DEFAULT 0
            `);
            console.log('Added is_role_group column');
        }
        
        if (!columns.find(col => col.COLUMN_NAME === 'role_type')) {
            await db.query(`
                ALTER TABLE groups 
                ADD COLUMN role_type VARCHAR(50) NULL
            `);
            console.log('Added role_type column');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Migration failed:', error);
    }
}

// Run the migration
runMigration().then(() => {
    console.log('Migration script finished');
    process.exit(0);
}).catch(err => {
    console.error('Migration script failed:', err);
    process.exit(1);
});
