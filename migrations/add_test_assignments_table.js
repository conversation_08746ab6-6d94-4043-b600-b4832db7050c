/**
 * Migration to add test_assignments table
 */

const db = require('../config/database');

async function addTestAssignmentsTable() {
    try {
        console.log('Starting migration: Adding test_assignments table');

        // Check if the table already exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = 'exam_prep_platform'
            AND TABLE_NAME = 'test_assignments'
        `);

        if (tables.length === 0) {
            // Create the test_assignments table
            // Create the table without foreign keys first
            await db.query(`
                CREATE TABLE test_assignments (
                    assignment_id INT NOT NULL AUTO_INCREMENT,
                    exam_id INT NOT NULL,
                    user_id INT NULL DEFAULT NULL,
                    group_id INT NULL DEFAULT NULL,
                    assigned_by INT NULL DEFAULT NULL,
                    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    max_attempts INT NOT NULL DEFAULT 1,
                    end_datetime DATETIME NULL DEFAULT NULL,
                    is_active TINYINT(1) NOT NULL DEFAULT 1,
                    PRIMARY KEY (assignment_id),
                    KEY idx_exam_id (exam_id),
                    KEY idx_user_id (user_id),
                    KEY idx_group_id (group_id),
                    KEY idx_assigned_by (assigned_by)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);

            // Add foreign keys one by one
            try {
                await db.query(`
                    ALTER TABLE test_assignments
                    ADD CONSTRAINT fk_test_assignments_exam
                    FOREIGN KEY (exam_id) REFERENCES exams (exam_id) ON DELETE CASCADE
                `);
                console.log('✅ Added foreign key for exam_id');
            } catch (error) {
                console.error('❌ Error adding foreign key for exam_id:', error.message);
            }

            try {
                await db.query(`
                    ALTER TABLE test_assignments
                    ADD CONSTRAINT fk_test_assignments_user
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                `);
                console.log('✅ Added foreign key for user_id');
            } catch (error) {
                console.error('❌ Error adding foreign key for user_id:', error.message);
            }

            try {
                await db.query(`
                    ALTER TABLE test_assignments
                    ADD CONSTRAINT fk_test_assignments_group
                    FOREIGN KEY (group_id) REFERENCES groups (group_id) ON DELETE CASCADE
                `);
                console.log('✅ Added foreign key for group_id');
            } catch (error) {
                console.error('❌ Error adding foreign key for group_id:', error.message);
            }

            try {
                await db.query(`
                    ALTER TABLE test_assignments
                    ADD CONSTRAINT fk_test_assignments_assigned_by
                    FOREIGN KEY (assigned_by) REFERENCES users (id) ON DELETE SET NULL
                `);
                console.log('✅ Added foreign key for assigned_by');
            } catch (error) {
                console.error('❌ Error adding foreign key for assigned_by:', error.message);
            }
            console.log('✅ Successfully created test_assignments table');
        } else {
            console.log('✅ test_assignments table already exists');
        }

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addTestAssignmentsTable()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
