/**
 * Migration to add is_blocked column to users table
 */

const db = require('../config/database');

async function addUserBlockColumn() {
    try {
        console.log('Starting migration: Adding is_blocked column to users table');
        
        // Check if the column already exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'users' 
            AND COLUMN_NAME = 'is_blocked'
        `);
        
        if (columns.length === 0) {
            // Add the is_blocked column with default value of 0 (not blocked)
            await db.query(`
                ALTER TABLE users 
                ADD COLUMN is_blocked TINYINT(1) DEFAULT 0 COMMENT 'Whether the user is blocked (1) or not (0)'
            `);
            console.log('✅ Successfully added is_blocked column to users table');
        } else {
            console.log('✅ is_blocked column already exists in users table');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
addUserBlockColumn()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
