/**
 * Migration to assign students to classrooms and subjects
 */

const db = require('../config/database');

async function assignStudentsToClassrooms() {
  try {
    console.log('Assigning students to classrooms and subjects...');
    
    // Get all students
    const [students] = await db.query(`
      SELECT u.id, u.username, u.email
      FROM users u
      WHERE u.role = 'student'
    `);
    
    if (students.length === 0) {
      console.log('No students found');
      return true;
    }
    
    console.log(`Found ${students.length} students`);
    
    // Get all classrooms
    const [classrooms] = await db.query(`
      SELECT 
        cr.id, cr.room_number, cr.session, cr.section,
        c.id as class_id, c.name as class_name, c.grade,
        t.id as trade_id, t.name as trade_name
      FROM classrooms cr
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
    `);
    
    if (classrooms.length === 0) {
      console.log('No classrooms found. Creating a default classroom...');
      
      // Get a class
      const [classes] = await db.query('SELECT id, name, grade FROM classes WHERE grade IS NOT NULL LIMIT 1');
      if (classes.length === 0) {
        console.log('No classes found with grade. Creating a default class...');
        const [result] = await db.query(`
          INSERT INTO classes (name, description, grade, section, academic_year)
          VALUES ('11', 'Class 11', '11', 'A', '2024-2025')
        `);
        var classId = result.insertId;
        var className = '11';
        var grade = '11';
      } else {
        var classId = classes[0].id;
        var className = classes[0].name;
        var grade = classes[0].grade || className;
      }
      
      // Get a trade
      const [trades] = await db.query('SELECT id, name FROM trades LIMIT 1');
      if (trades.length === 0) {
        console.log('No trades found. Creating a default trade...');
        const [result] = await db.query(`
          INSERT INTO trades (name, description)
          VALUES ('NON MEDICAL', 'Non-Medical stream with Mathematics, Physics, Chemistry')
        `);
        var tradeId = result.insertId;
        var tradeName = 'NON MEDICAL';
      } else {
        var tradeId = trades[0].id;
        var tradeName = trades[0].name;
      }
      
      // Create a default classroom
      const [result] = await db.query(`
        INSERT INTO classrooms (room_number, session, class_id, section, trade_id)
        VALUES (?, ?, ?, ?, ?)
      `, [`${grade}-1`, '2024-2025', classId, 'A', tradeId]);
      
      const [newClassrooms] = await db.query(`
        SELECT 
          cr.id, cr.room_number, cr.session, cr.section,
          c.id as class_id, c.name as class_name, c.grade,
          t.id as trade_id, t.name as trade_name
        FROM classrooms cr
        JOIN classes c ON cr.class_id = c.id
        JOIN trades t ON cr.trade_id = t.id
        WHERE cr.id = ?
      `, [result.insertId]);
      
      if (newClassrooms.length === 0) {
        console.error('Failed to create default classroom');
        return false;
      }
      
      console.log(`Created default classroom: Room ${grade}-1, Class ${className}, Section A, Trade ${tradeName}`);
      var availableClassrooms = newClassrooms;
    } else {
      console.log(`Found ${classrooms.length} classrooms`);
      var availableClassrooms = classrooms;
    }
    
    // Assign each student to a classroom
    for (const student of students) {
      // Check if student is already assigned to a classroom
      const [existingAssignment] = await db.query(`
        SELECT id FROM student_classrooms
        WHERE student_id = ?
      `, [student.id]);
      
      if (existingAssignment.length > 0) {
        console.log(`Student ${student.username} (ID: ${student.id}) already assigned to a classroom`);
        continue;
      }
      
      // Assign to a random classroom
      const classroom = availableClassrooms[Math.floor(Math.random() * availableClassrooms.length)];
      
      await db.query(`
        INSERT INTO student_classrooms (student_id, classroom_id, status)
        VALUES (?, ?, 'active')
      `, [student.id, classroom.id]);
      
      console.log(`Assigned student ${student.username} (ID: ${student.id}) to classroom ${classroom.room_number} (${classroom.class_name} ${classroom.section}, ${classroom.trade_name})`);
      
      // Get subjects for this trade
      const [subjects] = await db.query(`
        SELECT stc.subject_id
        FROM subject_trade_combinations stc
        WHERE stc.trade_id = ? AND stc.is_compulsory = 1
      `, [classroom.trade_id]);
      
      if (subjects.length > 0) {
        // Check existing subject assignments
        for (const subject of subjects) {
          const [existingSubject] = await db.query(`
            SELECT id FROM student_subjects
            WHERE student_id = ? AND subject_id = ?
          `, [student.id, subject.subject_id]);
          
          if (existingSubject.length === 0) {
            await db.query(`
              INSERT INTO student_subjects (student_id, subject_id, trade_id)
              VALUES (?, ?, ?)
            `, [student.id, subject.subject_id, classroom.trade_id]);
            
            console.log(`Assigned subject ID ${subject.subject_id} to student ${student.username}`);
          }
        }
      }
    }
    
    console.log('Migration completed successfully');
    return true;
  } catch (error) {
    console.error('Error in migration:', error);
    return false;
  }
}

// Execute the migration
assignStudentsToClassrooms()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
