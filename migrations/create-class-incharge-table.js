/**
 * Migration to create class_incharge table
 * This table tracks which teacher is the incharge for each class
 */

const db = require('../config/database');

async function createClassInchargeTable() {
  try {
    console.log('Creating class_incharge table...');

    // Create class_incharge table
    await db.query(`
      CREATE TABLE IF NOT EXISTS class_incharge (
        id INT PRIMARY KEY AUTO_INCREMENT,
        class_id INT NOT NULL,
        teacher_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_class_incharge (class_id)
      )
    `);

    console.log('class_incharge table created successfully');

    // Check if is_active column exists in classes table
    const [columnsCheck] = await db.query(`
      SHOW COLUMNS FROM classes LIKE 'is_active'
    `);

    // Construct query based on whether is_active column exists
    let query = `SELECT id FROM classes`;
    if (columnsCheck.length > 0) {
      query += ` WHERE is_active = 1`;
    }

    // Get all classes
    const [classes] = await db.query(query);

    if (classes.length > 0) {
      console.log(`Found ${classes.length} classes, assigning incharge teachers...`);

      // Check if is_active column exists in users table
      const [userColumnsCheck] = await db.query(`
        SHOW COLUMNS FROM users LIKE 'is_active'
      `);

      // Construct query based on whether is_active column exists
      let teacherQuery = `SELECT id FROM users WHERE role = 'teacher'`;
      if (userColumnsCheck.length > 0) {
        teacherQuery += ` AND is_active = 1`;
      }

      // Get teachers
      const [teachers] = await db.query(teacherQuery);

      if (teachers.length > 0) {
        // Assign teachers as incharge to classes
        for (let i = 0; i < classes.length; i++) {
          const classId = classes[i].id;
          const teacherId = teachers[i % teachers.length].id; // Cycle through teachers

          // Check if class already has an incharge
          const [existing] = await db.query(`
            SELECT id FROM class_incharge WHERE class_id = ?
          `, [classId]);

          if (existing.length === 0) {
            await db.query(`
              INSERT INTO class_incharge (class_id, teacher_id)
              VALUES (?, ?)
            `, [classId, teacherId]);

            console.log(`Assigned teacher ${teacherId} as incharge for class ${classId}`);
          } else {
            console.log(`Class ${classId} already has an incharge`);
          }
        }
      } else {
        console.log('No teachers found to assign as incharge');
      }
    } else {
      console.log('No classes found to assign incharge teachers');
    }

    console.log('Class incharge setup completed');

  } catch (error) {
    console.error('Error creating class_incharge table:', error);
    throw error;
  }
}

// Run the migration
createClassInchargeTable()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
