/**
 * Migration to create classrooms table and update related tables
 */

const db = require('../config/database');

async function createClassroomsTable() {
  try {
    console.log('Creating classrooms table...');
    
    // Create classrooms table
    await db.query(`
      CREATE TABLE IF NOT EXISTS classrooms (
        id INT PRIMARY KEY AUTO_INCREMENT,
        room_number VARCHAR(20) NOT NULL,
        session VARCHAR(20) NOT NULL DEFAULT '2024-2025',
        class_id INT NOT NULL,
        section VARCHAR(10) NOT NULL,
        trade_id INT NOT NULL,
        max_capacity INT DEFAULT 40,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
        UNIQUE KEY unique_classroom (room_number, session)
      )
    `);
    
    console.log('Classrooms table created successfully');
    
    // Ensure all required trades exist
    console.log('Ensuring all required trades exist...');
    const requiredTrades = [
      { name: 'COMMERCE', description: 'Commerce stream with Business Studies, Accountancy, Economics' },
      { name: 'NON MEDICAL', description: 'Non-Medical stream with Mathematics, Physics, Chemistry' },
      { name: 'MEDICAL', description: 'Medical stream with Biology, Chemistry, Physics' },
      { name: 'ARTS', description: 'Arts stream with History, Political Science, Geography, etc.' }
    ];
    
    for (const trade of requiredTrades) {
      const [existingTrade] = await db.query('SELECT id FROM trades WHERE name = ?', [trade.name]);
      
      if (existingTrade.length === 0) {
        await db.query('INSERT INTO trades (name, description) VALUES (?, ?)', [trade.name, trade.description]);
        console.log(`Trade ${trade.name} created`);
      } else {
        console.log(`Trade ${trade.name} already exists`);
      }
    }
    
    // Create sample classrooms
    console.log('Creating sample classrooms...');
    const [classes] = await db.query('SELECT id, name, grade FROM classes WHERE grade IS NOT NULL LIMIT 10');
    const [trades] = await db.query('SELECT id, name FROM trades LIMIT 10');
    
    if (classes.length > 0 && trades.length > 0) {
      for (let i = 0; i < Math.min(classes.length, 10); i++) {
        const classObj = classes[i];
        const trade = trades[i % trades.length];
        const roomNumber = `${classObj.grade}-${i + 1}`;
        const section = String.fromCharCode(65 + (i % 3)); // A, B, C
        
        const [existingClassroom] = await db.query(
          'SELECT id FROM classrooms WHERE room_number = ? AND session = ?', 
          [roomNumber, '2024-2025']
        );
        
        if (existingClassroom.length === 0) {
          await db.query(`
            INSERT INTO classrooms (room_number, session, class_id, section, trade_id)
            VALUES (?, ?, ?, ?, ?)
          `, [roomNumber, '2024-2025', classObj.id, section, trade.id]);
          
          console.log(`Created classroom: Room ${roomNumber}, Class ${classObj.name}, Section ${section}, Trade ${trade.name}`);
        } else {
          console.log(`Classroom with room ${roomNumber} for session 2024-2025 already exists`);
        }
      }
    } else {
      console.log('No classes or trades found to create sample classrooms');
    }
    
    console.log('Migration completed successfully');
    return true;
  } catch (error) {
    console.error('Error in migration:', error);
    return false;
  }
}

// Execute the migration
createClassroomsTable()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
