/**
 * Migration to create lab_incharge table
 * This table tracks which teacher is the incharge for each lab for a specific session
 */

const db = require('../config/database');

async function createLabInchargeTable() {
  try {
    console.log('Creating lab_incharge table...');

    // Create lab_incharge table with session column
    await db.query(`
      CREATE TABLE IF NOT EXISTS lab_incharge (
        id INT PRIMARY KEY AUTO_INCREMENT,
        lab_id INT NOT NULL,
        teacher_id INT NOT NULL,
        session VARCHAR(20) NOT NULL DEFAULT '2024-2025',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (lab_id) REFERENCES labs(id) ON DELETE CASCADE,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_lab_incharge_session (lab_id, session)
      )
    `);

    console.log('lab_incharge table created successfully');

    // Check if labs table exists and has data
    const [labs] = await db.query(`
      SELECT * FROM labs
    `);

    if (labs.length === 0) {
      console.log('No labs found, creating sample labs...');
      
      // Create sample labs
      await db.query(`
        INSERT INTO labs (name, capacity, location, is_active)
        VALUES 
        ('Computer Lab 1', 30, 'Block A, First Floor', 1),
        ('Computer Lab 2', 30, 'Block A, Second Floor', 1),
        ('Physics Lab', 25, 'Block B, Ground Floor', 1),
        ('Chemistry Lab', 25, 'Block B, First Floor', 1),
        ('Biology Lab', 20, 'Block B, Second Floor', 1)
      `);
      
      console.log('Sample labs created');
      
      // Get the labs again
      [labs] = await db.query(`
        SELECT * FROM labs
      `);
    }

    // Get computer science teachers
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_subjects ts ON u.id = ts.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      WHERE u.role = 'teacher' 
      AND (
        s.name LIKE '%computer%' 
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `);

    if (computerTeachers.length > 0) {
      console.log(`Found ${computerTeachers.length} computer teachers, assigning as lab incharges...`);

      // Get computer labs
      const computerLabs = labs.filter(lab => 
        lab.name.toLowerCase().includes('computer') || 
        lab.name.toLowerCase().includes('cs')
      );

      if (computerLabs.length > 0) {
        // Assign computer teachers to computer labs
        for (let i = 0; i < computerLabs.length; i++) {
          const labId = computerLabs[i].id;
          const teacherId = computerTeachers[i % computerTeachers.length].id; // Cycle through teachers

          // Check if lab already has an incharge for the current session
          const [existing] = await db.query(`
            SELECT id FROM lab_incharge 
            WHERE lab_id = ? AND session = '2024-2025'
          `, [labId]);

          if (existing.length === 0) {
            await db.query(`
              INSERT INTO lab_incharge (lab_id, teacher_id, session)
              VALUES (?, ?, '2024-2025')
            `, [labId, teacherId]);

            console.log(`Assigned teacher ${teacherId} as incharge for lab ${labId} for session 2024-2025`);
          } else {
            console.log(`Lab ${labId} already has an incharge for session 2024-2025`);
          }
        }
      } else {
        console.log('No computer labs found');
      }
    } else {
      console.log('No computer teachers found');
    }

    console.log('Lab incharge setup completed');
    return true;
  } catch (error) {
    console.error('Error creating lab_incharge table:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  createLabInchargeTable()
    .then(() => {
      console.log('Lab incharge migration completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Lab incharge migration failed:', err);
      process.exit(1);
    });
}

module.exports = createLabInchargeTable;
