/**
 * Migration script to create permissions tables
 * 
 * This script creates the necessary tables for role-based permissions:
 * - permissions: Stores available permissions
 * - roles: Stores user roles
 * - role_permissions: Maps roles to permissions
 */

const db = require('../config/database');

async function createPermissionsTables() {
    try {
        console.log('Starting permissions tables migration...');

        // Check if tables already exist
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('permissions', 'roles', 'role_permissions')
        `);

        const existingTables = tables.map(t => t.TABLE_NAME);
        console.log('Existing tables:', existingTables);

        // Create roles table if it doesn't exist
        if (!existingTables.includes('roles')) {
            console.log('Creating roles table...');
            await db.query(`
                CREATE TABLE roles (
                    role_id INT AUTO_INCREMENT PRIMARY KEY,
                    role_name VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    is_system BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);
            console.log('Roles table created successfully');
        }

        // Create permissions table if it doesn't exist
        if (!existingTables.includes('permissions')) {
            console.log('Creating permissions table...');
            await db.query(`
                CREATE TABLE permissions (
                    permission_id INT AUTO_INCREMENT PRIMARY KEY,
                    permission_name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    category VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
            console.log('Permissions table created successfully');
        }

        // Create role_permissions table if it doesn't exist
        if (!existingTables.includes('role_permissions')) {
            console.log('Creating role_permissions table...');
            await db.query(`
                CREATE TABLE role_permissions (
                    role_permission_id INT AUTO_INCREMENT PRIMARY KEY,
                    role_id INT NOT NULL,
                    permission_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY role_permission_unique (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
                )
            `);
            console.log('Role_permissions table created successfully');
        }

        // Insert default roles if roles table was just created
        if (!existingTables.includes('roles')) {
            console.log('Inserting default roles...');
            await db.query(`
                INSERT INTO roles (role_name, description, is_system)
                VALUES
                ('admin', 'Administrator with full system access', TRUE),
                ('teacher', 'Teacher with access to create and manage tests', TRUE),
                ('student', 'Student with access to take tests', TRUE)
            `);
            console.log('Default roles inserted successfully');
        }

        // Insert default permissions if permissions table was just created
        if (!existingTables.includes('permissions')) {
            console.log('Inserting default permissions...');
            await db.query(`
                INSERT INTO permissions (permission_name, description, category)
                VALUES
                ('view_dashboard', 'Access to view the dashboard', 'System'),
                ('manage_users', 'Access to manage users', 'System'),
                ('manage_roles', 'Access to manage roles', 'System'),
                ('manage_settings', 'Access to manage system settings', 'System'),
                ('view_logs', 'Access to view system logs', 'System'),
                
                ('create_tests', 'Access to create tests', 'Tests'),
                ('edit_tests', 'Access to edit tests', 'Tests'),
                ('delete_tests', 'Access to delete tests', 'Tests'),
                ('view_tests', 'Access to view tests', 'Tests'),
                ('assign_tests', 'Access to assign tests to users', 'Tests'),
                ('publish_tests', 'Access to publish tests', 'Tests'),
                
                ('create_questions', 'Access to create questions', 'Questions'),
                ('edit_questions', 'Access to edit questions', 'Questions'),
                ('delete_questions', 'Access to delete questions', 'Questions'),
                ('view_questions', 'Access to view questions', 'Questions'),
                ('import_questions', 'Access to import questions', 'Questions'),
                
                ('create_groups', 'Access to create groups', 'Groups'),
                ('edit_groups', 'Access to edit groups', 'Groups'),
                ('delete_groups', 'Access to delete groups', 'Groups'),
                ('view_groups', 'Access to view groups', 'Groups'),
                ('manage_group_members', 'Access to manage group members', 'Groups'),
                
                ('view_reports', 'Access to view reports', 'Reports'),
                ('generate_reports', 'Access to generate reports', 'Reports'),
                ('export_reports', 'Access to export reports', 'Reports'),
                
                ('take_tests', 'Access to take tests', 'Student'),
                ('view_results', 'Access to view test results', 'Student'),
                ('request_access', 'Access to request test access', 'Student')
            `);
            console.log('Default permissions inserted successfully');
        }

        // Assign all permissions to admin role
        const [adminRoles] = await db.query(`
            SELECT role_id FROM roles WHERE role_name = 'admin'
        `);

        if (adminRoles.length > 0) {
            const adminRoleId = adminRoles[0].role_id;
            
            // Get all permissions
            const [permissions] = await db.query(`
                SELECT permission_id FROM permissions
            `);
            
            // Check if admin already has permissions
            const [existingPermissions] = await db.query(`
                SELECT COUNT(*) as count FROM role_permissions WHERE role_id = ?
            `, [adminRoleId]);
            
            if (existingPermissions[0].count === 0) {
                console.log('Assigning all permissions to admin role...');
                
                // Create values for bulk insert
                const values = permissions.map(p => [adminRoleId, p.permission_id]);
                
                if (values.length > 0) {
                    await db.query(`
                        INSERT INTO role_permissions (role_id, permission_id)
                        VALUES ?
                    `, [values]);
                }
                
                console.log('Admin permissions assigned successfully');
            }
        }

        // Assign teacher permissions
        const [teacherRoles] = await db.query(`
            SELECT role_id FROM roles WHERE role_name = 'teacher'
        `);

        if (teacherRoles.length > 0) {
            const teacherRoleId = teacherRoles[0].role_id;
            
            // Check if teacher already has permissions
            const [existingPermissions] = await db.query(`
                SELECT COUNT(*) as count FROM role_permissions WHERE role_id = ?
            `, [teacherRoleId]);
            
            if (existingPermissions[0].count === 0) {
                console.log('Assigning permissions to teacher role...');
                
                // Get teacher permissions
                const [teacherPermissions] = await db.query(`
                    SELECT permission_id FROM permissions
                    WHERE permission_name IN (
                        'view_dashboard', 'view_tests', 'create_tests', 'edit_tests',
                        'view_questions', 'create_questions', 'edit_questions',
                        'view_groups', 'create_groups', 'edit_groups', 'manage_group_members',
                        'view_reports', 'generate_reports', 'export_reports'
                    )
                `);
                
                // Create values for bulk insert
                const values = teacherPermissions.map(p => [teacherRoleId, p.permission_id]);
                
                if (values.length > 0) {
                    await db.query(`
                        INSERT INTO role_permissions (role_id, permission_id)
                        VALUES ?
                    `, [values]);
                }
                
                console.log('Teacher permissions assigned successfully');
            }
        }

        // Assign student permissions
        const [studentRoles] = await db.query(`
            SELECT role_id FROM roles WHERE role_name = 'student'
        `);

        if (studentRoles.length > 0) {
            const studentRoleId = studentRoles[0].role_id;
            
            // Check if student already has permissions
            const [existingPermissions] = await db.query(`
                SELECT COUNT(*) as count FROM role_permissions WHERE role_id = ?
            `, [studentRoleId]);
            
            if (existingPermissions[0].count === 0) {
                console.log('Assigning permissions to student role...');
                
                // Get student permissions
                const [studentPermissions] = await db.query(`
                    SELECT permission_id FROM permissions
                    WHERE permission_name IN (
                        'view_dashboard', 'take_tests', 'view_results', 'request_access'
                    )
                `);
                
                // Create values for bulk insert
                const values = studentPermissions.map(p => [studentRoleId, p.permission_id]);
                
                if (values.length > 0) {
                    await db.query(`
                        INSERT INTO role_permissions (role_id, permission_id)
                        VALUES ?
                    `, [values]);
                }
                
                console.log('Student permissions assigned successfully');
            }
        }

        console.log('Permissions migration completed successfully');
    } catch (error) {
        console.error('Error in permissions migration:', error);
        throw error;
    }
}

// Run the migration
createPermissionsTables()
    .then(() => {
        console.log('Migration completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
