/**
 * Migration to create separate Boys Warden Office and Girls Warden Office
 * 
 * This migration:
 * 1. <PERSON><PERSON><PERSON> "Warden Office" to "Boys Warden Office"
 * 2. Creates new "Girls Warden Office"
 * 3. Redistributes PCs between the two warden offices
 * 4. Keeps hostel warden rooms unchanged (1 PC + 1 TV + 1 UPS each)
 * 5. Updates infrastructure view to show both warden offices
 */

const db = require('../config/database');

async function createSeparateWardenOffices() {
    console.log('🔄 Creating Separate Boys and Girls Warden Offices...\n');

    try {
        // 1. Check current setup
        console.log('1. Checking current warden office setup...');
        
        const [currentWardenOffice] = await db.query('SELECT id, room_number FROM rooms WHERE room_number = ?', ['Warden Office']);
        
        if (currentWardenOffice.length === 0) {
            throw new Error('Current Warden Office not found');
        }
        
        const currentWardenOfficeId = currentWardenOffice[0].id;
        console.log(`   Found current Warden Office (ID: ${currentWardenOfficeId})`);
        
        // Get current equipment
        const [currentEquipment] = await db.query(`
            SELECT item_id, name, manufacturer, model, serial_number, location
            FROM inventory_items 
            WHERE room_id = ?
            ORDER BY name
        `, [currentWardenOfficeId]);
        
        console.log(`   Current equipment: ${currentEquipment.length} items`);
        currentEquipment.forEach(item => {
            console.log(`      • ${item.name} (${item.serial_number})`);
        });

        // 2. Rename current "Warden Office" to "Boys Warden Office"
        console.log('\n2. Renaming current Warden Office to Boys Warden Office...');
        
        await db.query(`
            UPDATE rooms 
            SET room_number = ?, updated_at = NOW()
            WHERE id = ?
        `, ['Boys Warden Office', currentWardenOfficeId]);
        
        console.log('   ✅ Renamed "Warden Office" to "Boys Warden Office"');

        // 3. Create new "Girls Warden Office"
        console.log('\n3. Creating new Girls Warden Office...');
        
        const [result] = await db.query(`
            INSERT INTO rooms (room_number, building, floor, capacity, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        `, ['Girls Warden Office', 'Main Building', 2, 5]);
        
        const girlsWardenOfficeId = result.insertId;
        console.log(`   ✅ Created Girls Warden Office (ID: ${girlsWardenOfficeId})`);

        // 4. Redistribute equipment between Boys and Girls Warden Offices
        console.log('\n4. Redistributing equipment between warden offices...');
        
        // Keep 2 PCs in Boys Warden Office, move 1 PC to Girls Warden Office
        if (currentEquipment.length >= 3) {
            // Move the PC that was originally from Girls Hostel to Girls Warden Office
            const pcToMove = currentEquipment.find(item => item.location === 'Girls Hostel');
            
            if (pcToMove) {
                await db.query(`
                    UPDATE inventory_items 
                    SET room_id = ? 
                    WHERE item_id = ?
                `, [girlsWardenOfficeId, pcToMove.item_id]);
                
                console.log(`   ✅ Moved ${pcToMove.name} to Girls Warden Office`);
            } else {
                // If no Girls Hostel PC found, move the last PC
                const pcToMove = currentEquipment[currentEquipment.length - 1];
                await db.query(`
                    UPDATE inventory_items 
                    SET room_id = ? 
                    WHERE item_id = ?
                `, [girlsWardenOfficeId, pcToMove.item_id]);
                
                console.log(`   ✅ Moved ${pcToMove.name} to Girls Warden Office`);
            }
        }

        // 5. Verify the new setup
        console.log('\n5. Verifying new warden office setup...');
        
        const [boysWardenOffice] = await db.query(`
            SELECT 
                r.id, r.room_number, r.building, r.floor,
                COUNT(i.item_id) as equipment_count
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number = 'Boys Warden Office'
            GROUP BY r.id
        `);
        
        const [girlsWardenOffice] = await db.query(`
            SELECT 
                r.id, r.room_number, r.building, r.floor,
                COUNT(i.item_id) as equipment_count
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number = 'Girls Warden Office'
            GROUP BY r.id
        `);

        console.log(`\n   📍 Boys Warden Office:`);
        console.log(`      • ID: ${boysWardenOffice[0].id}`);
        console.log(`      • Building: ${boysWardenOffice[0].building}`);
        console.log(`      • Floor: ${boysWardenOffice[0].floor}`);
        console.log(`      • Equipment: ${boysWardenOffice[0].equipment_count} items`);
        
        console.log(`\n   📍 Girls Warden Office:`);
        console.log(`      • ID: ${girlsWardenOffice[0].id}`);
        console.log(`      • Building: ${girlsWardenOffice[0].building}`);
        console.log(`      • Floor: ${girlsWardenOffice[0].floor}`);
        console.log(`      • Equipment: ${girlsWardenOffice[0].equipment_count} items`);

        // 6. Get detailed equipment for each office
        console.log('\n6. Detailed equipment breakdown...');
        
        const [boysEquipment] = await db.query(`
            SELECT name, manufacturer, model, serial_number
            FROM inventory_items 
            WHERE room_id = ?
            ORDER BY name
        `, [boysWardenOffice[0].id]);
        
        const [girlsEquipment] = await db.query(`
            SELECT name, manufacturer, model, serial_number
            FROM inventory_items 
            WHERE room_id = ?
            ORDER BY name
        `, [girlsWardenOffice[0].id]);

        console.log(`\n   📍 Boys Warden Office Equipment:`);
        boysEquipment.forEach((item, index) => {
            console.log(`      ${index + 1}. ${item.name}`);
            console.log(`         • ${item.manufacturer} ${item.model} (${item.serial_number})`);
        });
        
        console.log(`\n   📍 Girls Warden Office Equipment:`);
        girlsEquipment.forEach((item, index) => {
            console.log(`      ${index + 1}. ${item.name}`);
            console.log(`         • ${item.manufacturer} ${item.model} (${item.serial_number})`);
        });

        // 7. Test infrastructure view query
        console.log('\n7. Testing updated infrastructure view query...');
        
        const [allWardenFacilities] = await db.query(`
            SELECT 
                r.id as room_id,
                r.room_number,
                r.building,
                r.floor,
                COUNT(DISTINCT i.item_id) as total_equipment,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' THEN i.item_id END) as tvs,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number LIKE '%Warden%'
            GROUP BY r.id, r.room_number, r.building, r.floor
            ORDER BY r.room_number
        `);

        console.log(`\n   📊 All Warden Facilities:`);
        allWardenFacilities.forEach(facility => {
            console.log(`      • ${facility.room_number} (${facility.building}): ${facility.total_equipment} items`);
            console.log(`        - ${facility.desktops} PCs, ${facility.tvs} TVs, ${facility.ups_units} UPS`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('\n🎯 FINAL WARDEN OFFICE STRUCTURE:');
        console.log('   • Boys Warden Office: Administrative office with PCs');
        console.log('   • Girls Warden Office: Administrative office with PCs');
        console.log('   • Boys Hostel Warden Room: Monitoring room with TV + PC + UPS');
        console.log('   • Girls Hostel Warden Room: Monitoring room with TV + PC + UPS');
        console.log('\n📋 Infrastructure view will now show 4 separate warden facilities!');

        return {
            success: true,
            boysWardenOfficeId: boysWardenOffice[0].id,
            girlsWardenOfficeId: girlsWardenOffice[0].id,
            boysEquipmentCount: boysEquipment.length,
            girlsEquipmentCount: girlsEquipment.length
        };

    } catch (error) {
        console.error('❌ Error creating separate warden offices:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    createSeparateWardenOffices()
        .then((results) => {
            console.log('\n🎯 Separate warden offices created successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { createSeparateWardenOffices };
