/**
 * Migration to create timetable for the student
 */

const db = require('../config/database');

async function createStudentTimetable() {
  try {
    console.log('Creating timetable for the student...');

    // Get the demo student (csstudent)
    const [student] = await db.query(`
      SELECT u.id, u.username, sc.classroom_id, cr.class_id, c.name as class_name, c.grade,
             cr.section, cr.room_number, cr.trade_id, t.name as trade_name
      FROM users u
      JOIN student_classrooms sc ON u.id = sc.student_id
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE u.username = 'csstudent'
    `);

    if (student.length === 0) {
      console.error('Demo student (csstudent) not found');
      return false;
    }

    const studentId = student[0].id;
    const classroomId = student[0].classroom_id;
    const classId = student[0].class_id;
    const className = `${student[0].grade} ${student[0].trade_name} ${student[0].section}`;
    const roomNumber = student[0].room_number;

    console.log(`Found student: ${student[0].username}, Class: ${className}, Room: ${roomNumber}`);

    // Get student's subjects
    const [studentSubjects] = await db.query(`
      SELECT ss.subject_id, s.name as subject_name, s.code as subject_code
      FROM student_subjects ss
      JOIN subjects s ON ss.subject_id = s.id
      WHERE ss.student_id = ?
    `, [studentId]);

    if (studentSubjects.length === 0) {
      console.error('No subjects found for the student');
      return false;
    }

    console.log(`Found ${studentSubjects.length} subjects for the student`);

    // Get teachers
    const [teachers] = await db.query(`
      SELECT id, username, full_name
      FROM users
      WHERE role = 'teacher'
    `);

    if (teachers.length === 0) {
      console.error('No teachers found');
      return false;
    }

    // Create class_weekly_lectures table if it doesn't exist
    await db.query(`
      CREATE TABLE IF NOT EXISTS class_weekly_lectures (
        id INT PRIMARY KEY AUTO_INCREMENT,
        class_id INT NOT NULL,
        day_of_week INT NOT NULL COMMENT '0=Sunday, 1=Monday, ..., 6=Saturday',
        period INT NOT NULL,
        subject_id INT NOT NULL,
        teacher_id INT NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        room VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_class_schedule (class_id, day_of_week, period)
      )
    `);

    // Create teacher_weekly_lectures table if it doesn't exist
    await db.query(`
      CREATE TABLE IF NOT EXISTS teacher_weekly_lectures (
        id INT PRIMARY KEY AUTO_INCREMENT,
        teacher_id INT NOT NULL,
        day_of_week INT NOT NULL COMMENT '0=Sunday, 1=Monday, ..., 6=Saturday',
        period INT NOT NULL,
        class_id INT NOT NULL,
        subject_id INT NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        room VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_teacher_schedule (teacher_id, day_of_week, period)
      )
    `);

    // Define lecture schedule
    const lectureSchedule = [
      { period: 1, start_time: '08:00:00', end_time: '08:40:00' },
      { period: 2, start_time: '08:45:00', end_time: '09:25:00' },
      { period: 3, start_time: '09:30:00', end_time: '10:10:00' },
      { period: 4, start_time: '10:15:00', end_time: '10:55:00' },
      { period: 5, start_time: '11:00:00', end_time: '11:40:00' },
      { period: 6, start_time: '11:45:00', end_time: '12:25:00' },
      { period: 7, start_time: '12:30:00', end_time: '13:10:00' },
      { period: 8, start_time: '13:15:00', end_time: '13:55:00' }
    ];

    // Check if class_weekly_lectures table exists
    const [cwlTable] = await db.query(`
      SHOW TABLES LIKE 'class_weekly_lectures'
    `);

    if (cwlTable.length === 0) {
      console.log('class_weekly_lectures table does not exist yet, will create it');
    } else {
      console.log('class_weekly_lectures table already exists');

      // Try to clear existing entries for this class
      try {
        await db.query(`
          DELETE FROM class_weekly_lectures
          WHERE class_id = ?
        `, [classId]);
        console.log('Cleared existing timetable for the class');
      } catch (err) {
        console.log('Could not clear existing timetable, will proceed with creating new entries');
      }
    }

    // Create timetable for Monday to Friday (1-5)
    for (let day = 1; day <= 5; day++) {
      // Assign 6 periods per day
      for (let period = 1; period <= 6; period++) {
        // Select a random subject
        const subject = studentSubjects[Math.floor(Math.random() * studentSubjects.length)];

        // Select a random teacher
        const teacher = teachers[Math.floor(Math.random() * teachers.length)];

        // Get period times from our predefined schedule
        const periodInfo = lectureSchedule[period - 1];
        if (!periodInfo) {
          console.error(`No schedule found for period ${period}`);
          continue;
        }

        // Insert into class_weekly_lectures
        await db.query(`
          INSERT INTO class_weekly_lectures (
            class_id, day_of_week, period, subject_id, teacher_id,
            start_time, end_time, room
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          classId, day, period, subject.subject_id, teacher.id,
          periodInfo.start_time, periodInfo.end_time, roomNumber
        ]);

        console.log(`Added lecture for day ${day}, period ${period}, subject ${subject.subject_name}`);

        // Insert into teacher_weekly_lectures
        await db.query(`
          INSERT INTO teacher_weekly_lectures (
            teacher_id, day_of_week, period, class_id, subject_id,
            start_time, end_time, room
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacher.id, day, period, classId, subject.subject_id,
          periodInfo.start_time, periodInfo.end_time, roomNumber
        ]);
      }
    }

    // Create teacher_lectures table if it doesn't exist
    const [teacherLecturesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (teacherLecturesTable.length === 0) {
      await db.query(`
        CREATE TABLE teacher_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          slot_index INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          section_display VARCHAR(10),
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255),
          grade VARCHAR(10),
          streamCode VARCHAR(10),
          sectionLetter VARCHAR(5),
          stream VARCHAR(50),
          location VARCHAR(100),
          status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          class_section_id INT,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('Created teacher_lectures table');
    }

    // Generate some upcoming lectures
    console.log('Generating upcoming lectures...');

    // Get next Monday's date
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);

    // Generate lectures for the next 5 days
    for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
      const lectureDate = new Date(nextMonday);
      lectureDate.setDate(nextMonday.getDate() + dayOffset);

      // Format date as YYYY-MM-DD
      const formattedDate = lectureDate.toISOString().split('T')[0];

      // Get timetable for this day
      const [timetable] = await db.query(`
        SELECT cwl.*, s.name as subject_name, u.id as teacher_id, u.full_name as teacher_name
        FROM class_weekly_lectures cwl
        JOIN subjects s ON cwl.subject_id = s.id
        JOIN users u ON cwl.teacher_id = u.id
        WHERE cwl.class_id = ? AND cwl.day_of_week = ?
        ORDER BY cwl.period
      `, [classId, lectureDate.getDay()]);

      // Create teacher_lectures entries
      for (const lecture of timetable) {
        // Generate a topic based on subject
        const topics = [
          'Introduction to the subject',
          'Key concepts and principles',
          'Practical applications',
          'Advanced techniques',
          'Review and assessment'
        ];

        const topic = topics[Math.floor(Math.random() * topics.length)];

        // Check if lecture already exists
        const [existingLecture] = await db.query(`
          SELECT id FROM teacher_lectures
          WHERE teacher_id = ? AND date = ? AND slot_index = ?
        `, [lecture.teacher_id, formattedDate, lecture.period - 1]);

        if (existingLecture.length === 0) {
          await db.query(`
            INSERT INTO teacher_lectures (
              teacher_id, date, start_time, end_time, slot_index,
              class_name, section_display, subject_name, topic,
              grade, stream, location, status
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
          `, [
            lecture.teacher_id, formattedDate, lecture.start_time, lecture.end_time, lecture.period - 1,
            className, student[0].section, lecture.subject_name, topic,
            student[0].grade, student[0].trade_name, lecture.room,
          ]);

          console.log(`Created lecture for ${formattedDate}, period ${lecture.period}, subject ${lecture.subject_name}`);
        }
      }
    }

    console.log('Student timetable created successfully');
    return true;
  } catch (error) {
    console.error('Error creating student timetable:', error);
    return false;
  }
}

// Execute the migration
createStudentTimetable()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
