/**
 * Migration to create teacher_specialization table
 * This table defines teacher specializations (e.g., Computer Science, Physics, etc.)
 * Computer teachers can only teach computer-related subjects
 */

const db = require('../config/database');

async function createTeacherSpecializationTable() {
  try {
    console.log('Creating teacher_specialization table...');

    // Create teacher_specialization table
    await db.query(`
      CREATE TABLE IF NOT EXISTS teacher_specialization (
        id INT PRIMARY KEY AUTO_INCREMENT,
        teacher_id INT NOT NULL,
        specialization ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 
                           'English', 'Hindi', 'Social Science', 'Economics', 'Commerce', 
                           'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
        is_primary BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_teacher_specialization (teacher_id, specialization)
      )
    `);

    console.log('teacher_specialization table created successfully');

    // Create subject_category table to categorize subjects
    await db.query(`
      CREATE TABLE IF NOT EXISTS subject_category (
        id INT PRIMARY KEY AUTO_INCREMENT,
        subject_id INT NOT NULL,
        category ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics', 
                     'English', 'Hindi', 'Social Science', 'Economics', 'Commerce', 
                     'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_subject_category (subject_id, category)
      )
    `);

    console.log('subject_category table created successfully');

    // Find computer science subjects
    const [computerSubjects] = await db.query(`
      SELECT id, name, code
      FROM subjects
      WHERE name LIKE '%computer%' 
      OR name LIKE '%Computer%'
      OR code LIKE '%CS%'
      OR code LIKE '%comp%'
    `);

    if (computerSubjects.length > 0) {
      console.log(`Found ${computerSubjects.length} computer science subjects, categorizing them...`);
      
      // Categorize computer science subjects
      for (const subject of computerSubjects) {
        // Check if subject already has a category
        const [existingCategory] = await db.query(`
          SELECT id FROM subject_category WHERE subject_id = ?
        `, [subject.id]);

        if (existingCategory.length === 0) {
          await db.query(`
            INSERT INTO subject_category (subject_id, category)
            VALUES (?, 'Computer Science')
          `, [subject.id]);

          console.log(`Categorized subject ${subject.name} (ID: ${subject.id}) as Computer Science`);
        } else {
          console.log(`Subject ${subject.name} (ID: ${subject.id}) already has a category`);
        }
      }
    } else {
      console.log('No computer science subjects found');
    }

    // Find computer science teachers
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_subjects ts ON u.id = ts.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      WHERE u.role = 'teacher' 
      AND (
        s.name LIKE '%computer%' 
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `);

    if (computerTeachers.length > 0) {
      console.log(`Found ${computerTeachers.length} computer teachers, setting their specialization...`);
      
      // Set computer science specialization for computer teachers
      for (const teacher of computerTeachers) {
        // Check if teacher already has a specialization
        const [existingSpecialization] = await db.query(`
          SELECT id FROM teacher_specialization WHERE teacher_id = ?
        `, [teacher.id]);

        if (existingSpecialization.length === 0) {
          await db.query(`
            INSERT INTO teacher_specialization (teacher_id, specialization, is_primary)
            VALUES (?, 'Computer Science', TRUE)
          `, [teacher.id]);

          console.log(`Set specialization for teacher ${teacher.full_name} (ID: ${teacher.id}) as Computer Science`);
        } else {
          console.log(`Teacher ${teacher.full_name} (ID: ${teacher.id}) already has a specialization`);
        }
      }
    } else {
      console.log('No computer teachers found');
    }

    console.log('Teacher specialization setup completed');
    return true;
  } catch (error) {
    console.error('Error creating teacher_specialization table:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  createTeacherSpecializationTable()
    .then(() => {
      console.log('Teacher specialization migration completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Teacher specialization migration failed:', err);
      process.exit(1);
    });
}

module.exports = createTeacherSpecializationTable;
