const db = require('../config/database');

async function createAccessRequestsTable() {
    try {
        // Check if table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'access_requests'
        `);

        if (tables.length === 0) {
            console.log('Creating access_requests table...');
            
            // Create the table
            await db.query(`
                CREATE TABLE access_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    exam_id INT NOT NULL,
                    user_id INT NOT NULL,
                    reason TEXT,
                    requested_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                    approved_by INT,
                    approved_at DATETIME,
                    additional_attempts INT,
                    rejected_by INT,
                    rejected_at DATETIME,
                    rejection_reason TEXT,
                    FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
                    FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX (status),
                    INDEX (user_id),
                    INDEX (exam_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
            
            console.log('access_requests table created successfully');
        } else {
            console.log('access_requests table already exists');
        }
    } catch (error) {
        console.error('Error creating access_requests table:', error);
        throw error;
    }
}

module.exports = createAccessRequestsTable;
