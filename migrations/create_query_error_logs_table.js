/**
 * Migration to create query_error_logs table
 */

const db = require('../config/database');

async function createQueryErrorLogsTable() {
    try {
        console.log('Starting migration: Creating query_error_logs table');
        
        // Check if the table already exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'query_error_logs'
        `);
        
        if (tables.length === 0) {
            // Create the query_error_logs table
            await db.query(`
                CREATE TABLE query_error_logs (
                    id INT NOT NULL AUTO_INCREMENT,
                    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    error_code VARCHAR(50),
                    error_message TEXT,
                    query TEXT,
                    parameters TEXT,
                    route VARCHAR(255),
                    user_id INT,
                    ip_address VARCHAR(50),
                    user_agent TEXT,
                    stack_trace TEXT,
                    PRIMARY KEY (id),
                    KEY idx_timestamp (timestamp),
                    KEY idx_error_code (error_code),
                    KEY idx_user_id (user_id),
                    CONSTRAINT fk_query_error_logs_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('✅ Successfully created query_error_logs table');
        } else {
            console.log('✅ query_error_logs table already exists');
        }
        
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('❌ Error during migration:', error);
    }
}

// Run the migration
createQueryErrorLogsTable()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('Migration process failed:', err);
        process.exit(1);
    });
