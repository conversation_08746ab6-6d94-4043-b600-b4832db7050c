/**
 * Migration to create the query_logs table
 */

const db = require('../config/database');

async function up() {
    try {
        console.log('Creating query_logs table...');
        
        // Check if table already exists
        const [tableExists] = await db.query(`
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        `, [process.env.DB_NAME]);
        
        if (tableExists.length > 0) {
            console.log('query_logs table already exists, skipping creation');
            return;
        }
        
        // Create query_logs table
        await db.query(`
            CREATE TABLE query_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id INT NULL,
                query TEXT NOT NULL,
                params TEXT NULL,
                duration INT NOT NULL,
                result TEXT NULL,
                status VARCHAR(20) DEFAULT 'success',
                error_message TEXT NULL,
                table_name VARCHAR(100) NULL,
                query_type VARCHAR(20) NULL,
                affected_rows INT NULL,
                ip_address VARCHAR(45) NULL,
                route VARCHAR(255) NULL,
                INDEX idx_timestamp (timestamp),
                INDEX idx_user_id (user_id),
                INDEX idx_status (status),
                INDEX idx_query_type (query_type),
                INDEX idx_duration (duration)
            )
        `);
        
        console.log('query_logs table created successfully');
    } catch (error) {
        console.error('Error creating query_logs table:', error);
        throw error;
    }
}

async function down() {
    try {
        console.log('Dropping query_logs table...');
        
        // Drop query_logs table
        await db.query(`
            DROP TABLE IF EXISTS query_logs
        `);
        
        console.log('query_logs table dropped successfully');
    } catch (error) {
        console.error('Error dropping query_logs table:', error);
        throw error;
    }
}

module.exports = {
    up,
    down
};
