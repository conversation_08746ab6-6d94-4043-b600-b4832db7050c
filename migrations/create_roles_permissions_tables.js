const fs = require('fs');
const path = require('path');
const db = require('../config/database');

async function up() {
    try {
        console.log('Starting roles and permissions tables migration...');
        
        // Check if roles table already exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'roles'
        `);
        
        if (tables.length > 0) {
            console.log('Roles table already exists. Skipping migration.');
            return;
        }
        
        // Read the SQL file
        const sqlPath = path.join(__dirname, '..', 'database', 'migrations', 'create_roles_permissions_tables.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        // Split SQL statements by semicolon
        const statements = sql.split(';').filter(statement => statement.trim() !== '');
        
        // Execute each statement
        for (const statement of statements) {
            await db.query(statement);
        }
        
        console.log('Roles and permissions tables created successfully!');
    } catch (error) {
        console.error('Error creating roles and permissions tables:', error);
        throw error;
    }
}

async function down() {
    try {
        console.log('Reverting roles and permissions tables migration...');
        
        // Drop tables in reverse order to avoid foreign key constraints
        await db.query('DROP TABLE IF EXISTS role_permissions');
        await db.query('DROP TABLE IF EXISTS permissions');
        await db.query('DROP TABLE IF EXISTS roles');
        
        console.log('Roles and permissions tables dropped successfully!');
    } catch (error) {
        console.error('Error dropping roles and permissions tables:', error);
        throw error;
    }
}

module.exports = { up, down };
