/**
 * Migration script to fix the active_sessions table structure
 * 
 * This script checks if the active_sessions table has the expected structure
 * and adds missing columns if needed.
 */

const db = require('../config/database');

async function fixActiveSessionsTable() {
  try {
    console.log('Starting migration: Fixing active_sessions table structure');

    // Check if active_sessions table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
    `);

    if (tableExists[0].table_exists === 0) {
      console.log('active_sessions table does not exist, creating it...');
      
      // Create the active_sessions table with the correct structure
      await db.query(`
        CREATE TABLE IF NOT EXISTS active_sessions (
          id int(11) NOT NULL AUTO_INCREMENT,
          user_id int(11) NOT NULL,
          session_id varchar(128) NOT NULL,
          ip_address varchar(45) NOT NULL,
          user_agent text DEFAULT NULL,
          login_time timestamp NOT NULL DEFAULT current_timestamp(),
          last_activity timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          is_active tinyint(1) NOT NULL DEFAULT 1,
          PRIMARY KEY (id),
          KEY idx_user_id (user_id),
          KEY idx_session_id (session_id),
          KEY idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);
      
      console.log('active_sessions table created successfully');
      return;
    }

    // Check if user_id column exists
    const [userIdExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'user_id'
    `);

    if (userIdExists[0].column_exists === 0) {
      console.log('Adding user_id column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN user_id int(11) NOT NULL AFTER session_id
      `);
      console.log('user_id column added successfully');
    }

    // Check if is_active column exists
    const [isActiveExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'is_active'
    `);

    if (isActiveExists[0].column_exists === 0) {
      console.log('Adding is_active column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN is_active tinyint(1) NOT NULL DEFAULT 1
      `);
      console.log('is_active column added successfully');
    }

    // Check if ip_address column exists
    const [ipAddressExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'ip_address'
    `);

    if (ipAddressExists[0].column_exists === 0) {
      console.log('Adding ip_address column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN ip_address varchar(45) NOT NULL DEFAULT '127.0.0.1'
      `);
      console.log('ip_address column added successfully');
    }

    // Check if user_agent column exists
    const [userAgentExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'user_agent'
    `);

    if (userAgentExists[0].column_exists === 0) {
      console.log('Adding user_agent column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN user_agent text DEFAULT NULL
      `);
      console.log('user_agent column added successfully');
    }

    // Check if login_time column exists
    const [loginTimeExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'login_time'
    `);

    if (loginTimeExists[0].column_exists === 0) {
      console.log('Adding login_time column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN login_time timestamp NOT NULL DEFAULT current_timestamp()
      `);
      console.log('login_time column added successfully');
    }

    // Check if last_activity column exists
    const [lastActivityExists] = await db.query(`
      SELECT COUNT(*) as column_exists
      FROM information_schema.columns
      WHERE table_schema = DATABASE()
      AND table_name = 'active_sessions'
      AND column_name = 'last_activity'
    `);

    if (lastActivityExists[0].column_exists === 0) {
      console.log('Adding last_activity column to active_sessions table...');
      await db.query(`
        ALTER TABLE active_sessions
        ADD COLUMN last_activity timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
      `);
      console.log('last_activity column added successfully');
    }

    // Add indexes if they don't exist
    await db.query(`
      ALTER TABLE active_sessions
      ADD INDEX IF NOT EXISTS idx_user_id (user_id),
      ADD INDEX IF NOT EXISTS idx_session_id (session_id),
      ADD INDEX IF NOT EXISTS idx_is_active (is_active)
    `);

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error fixing active_sessions table:', error);
    throw error;
  }
}

// Run the migration
fixActiveSessionsTable()
  .then(() => {
    console.log('Active sessions table migration completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
