/**
 * Migration to fix foreign key constraints after the principal role migration
 */

const db = require('../config/database');

async function run() {
  try {
    console.log('Starting migration: Fix foreign key constraints');

    // Check if users_old table exists
    const [oldUserTables] = await db.query(`
      SHOW TABLES LIKE 'users_old'
    `);

    if (oldUserTables.length > 0) {
      console.log('Found users_old table, checking foreign key constraints');

      // Get all tables that reference users_old
      const [referencingTables] = await db.query(`
        SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_NAME = 'users_old'
        AND REFERENCED_TABLE_SCHEMA = 'exam_prep_platform'
      `);

      console.log(`Found ${referencingTables.length} tables referencing users_old`);

      // For each table, drop the foreign key constraint and recreate it to point to users
      for (const table of referencingTables) {
        console.log(`Fixing foreign key constraint in table ${table.TABLE_NAME}`);
        
        // Drop the foreign key constraint
        await db.query(`
          ALTER TABLE ${table.TABLE_NAME}
          DROP FOREIGN KEY ${table.CONSTRAINT_NAME}
        `);
        
        console.log(`Dropped foreign key ${table.CONSTRAINT_NAME} from ${table.TABLE_NAME}`);
        
        // Recreate the foreign key constraint to point to users
        await db.query(`
          ALTER TABLE ${table.TABLE_NAME}
          ADD CONSTRAINT ${table.CONSTRAINT_NAME}
          FOREIGN KEY (${table.COLUMN_NAME})
          REFERENCES users(id)
          ON DELETE CASCADE
        `);
        
        console.log(`Recreated foreign key ${table.CONSTRAINT_NAME} in ${table.TABLE_NAME} to reference users`);
      }
      
      console.log('All foreign key constraints fixed');
    } else {
      console.log('No users_old table found, checking for other issues');
      
      // Check if there are any foreign key constraints pointing to non-existent tables
      const [brokenConstraints] = await db.query(`
        SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_SCHEMA = 'exam_prep_platform'
        AND REFERENCED_TABLE_NAME NOT IN (
          SELECT TABLE_NAME 
          FROM information_schema.TABLES 
          WHERE TABLE_SCHEMA = 'exam_prep_platform'
        )
      `);
      
      if (brokenConstraints.length > 0) {
        console.log(`Found ${brokenConstraints.length} broken foreign key constraints`);
        
        for (const constraint of brokenConstraints) {
          console.log(`Fixing broken constraint ${constraint.CONSTRAINT_NAME} in table ${constraint.TABLE_NAME}`);
          
          // Drop the foreign key constraint
          await db.query(`
            ALTER TABLE ${constraint.TABLE_NAME}
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `);
          
          console.log(`Dropped foreign key ${constraint.CONSTRAINT_NAME} from ${constraint.TABLE_NAME}`);
          
          // Recreate the foreign key constraint to point to users
          await db.query(`
            ALTER TABLE ${constraint.TABLE_NAME}
            ADD CONSTRAINT ${constraint.CONSTRAINT_NAME}
            FOREIGN KEY (${constraint.COLUMN_NAME})
            REFERENCES users(id)
            ON DELETE CASCADE
          `);
          
          console.log(`Recreated foreign key ${constraint.CONSTRAINT_NAME} in ${constraint.TABLE_NAME} to reference users`);
        }
      } else {
        console.log('No broken foreign key constraints found');
      }
    }

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

run();
