/**
 * Migration to fix hostel equipment distribution
 * 
 * This migration:
 * 1. Ensures each hostel warden room has exactly 1 PC, 1 TV, 1 UPS
 * 2. Moves extra PC from Girls Hostel to Main Warden Office
 * 3. Updates equipment distribution correctly
 * 4. Preserves all equipment records
 */

const db = require('../config/database');

async function fixHostelEquipmentDistribution() {
    console.log('🔄 Fixing Hostel Equipment Distribution...\n');

    try {
        // 1. Check current distribution
        console.log('1. Checking current hostel equipment distribution...');
        
        const [boysHostelEquipment] = await db.query(`
            SELECT item_id, name, serial_number
            FROM inventory_items 
            WHERE room_id = (SELECT id FROM rooms WHERE room_number = 'Boys Hostel Warden Room')
            ORDER BY name
        `);
        
        const [girlsHostelEquipment] = await db.query(`
            SELECT item_id, name, serial_number
            FROM inventory_items 
            WHERE room_id = (SELECT id FROM rooms WHERE room_number = 'Girls Hostel Warden Room')
            ORDER BY name
        `);
        
        console.log(`   Boys Hostel Warden Room: ${boysHostelEquipment.length} items`);
        boysHostelEquipment.forEach(item => {
            console.log(`      • ${item.name} (${item.serial_number})`);
        });
        
        console.log(`   Girls Hostel Warden Room: ${girlsHostelEquipment.length} items`);
        girlsHostelEquipment.forEach(item => {
            console.log(`      • ${item.name} (${item.serial_number})`);
        });

        // 2. Identify the extra PC in Girls Hostel Warden Room
        console.log('\n2. Identifying extra equipment...');
        
        const girlsHostelPCs = girlsHostelEquipment.filter(item => 
            item.name.includes('VERITON') || item.name.includes('PC') || item.name.includes('Desktop')
        );
        
        if (girlsHostelPCs.length > 1) {
            console.log(`   Found ${girlsHostelPCs.length} PCs in Girls Hostel Warden Room (should be 1)`);
            
            // Keep the first PC, move the second one
            const pcToMove = girlsHostelPCs[1];
            console.log(`   Will move: ${pcToMove.name} (${pcToMove.serial_number})`);
            
            // 3. Get Main Warden Office room ID
            const [wardenOffice] = await db.query('SELECT id FROM rooms WHERE room_number = ?', ['Warden Office']);
            
            if (wardenOffice.length === 0) {
                throw new Error('Warden Office room not found');
            }
            
            const wardenOfficeId = wardenOffice[0].id;
            
            // 4. Move the extra PC to Main Warden Office
            console.log('\n3. Moving extra PC to Main Warden Office...');
            
            await db.query(`
                UPDATE inventory_items 
                SET room_id = ? 
                WHERE item_id = ?
            `, [wardenOfficeId, pcToMove.item_id]);
            
            console.log(`   ✅ Moved ${pcToMove.name} to Main Warden Office`);
            
        } else {
            console.log('   ✅ Girls Hostel Warden Room already has correct number of PCs');
        }

        // 5. Verify the corrected distribution
        console.log('\n4. Verifying corrected distribution...');
        
        const [correctedBoys] = await db.query(`
            SELECT item_id, name, serial_number,
                   CASE 
                     WHEN name LIKE '%BDL%' OR name LIKE '%PHILIPS%' THEN 'TV'
                     WHEN name LIKE '%VERITON%' OR name LIKE '%PC%' THEN 'PC'
                     WHEN name LIKE '%UPS%' THEN 'UPS'
                     ELSE 'Other'
                   END as equipment_type
            FROM inventory_items 
            WHERE room_id = (SELECT id FROM rooms WHERE room_number = 'Boys Hostel Warden Room')
            ORDER BY equipment_type, name
        `);
        
        const [correctedGirls] = await db.query(`
            SELECT item_id, name, serial_number,
                   CASE 
                     WHEN name LIKE '%BDL%' OR name LIKE '%PHILIPS%' THEN 'TV'
                     WHEN name LIKE '%VERITON%' OR name LIKE '%PC%' THEN 'PC'
                     WHEN name LIKE '%UPS%' THEN 'UPS'
                     ELSE 'Other'
                   END as equipment_type
            FROM inventory_items 
            WHERE room_id = (SELECT id FROM rooms WHERE room_number = 'Girls Hostel Warden Room')
            ORDER BY equipment_type, name
        `);
        
        const [wardenOfficeEquipment] = await db.query(`
            SELECT item_id, name, serial_number,
                   CASE 
                     WHEN name LIKE '%VERITON%' OR name LIKE '%PC%' THEN 'PC'
                     WHEN name LIKE '%UPS%' THEN 'UPS'
                     ELSE 'Other'
                   END as equipment_type
            FROM inventory_items 
            WHERE room_id = (SELECT id FROM rooms WHERE room_number = 'Warden Office')
            ORDER BY equipment_type, name
        `);

        console.log(`\n   ✅ CORRECTED DISTRIBUTION:`);
        
        console.log(`\n   📍 Boys Hostel Warden Room (${correctedBoys.length} items):`);
        correctedBoys.forEach(item => {
            console.log(`      • ${item.equipment_type}: ${item.name} (${item.serial_number})`);
        });
        
        console.log(`\n   📍 Girls Hostel Warden Room (${correctedGirls.length} items):`);
        correctedGirls.forEach(item => {
            console.log(`      • ${item.equipment_type}: ${item.name} (${item.serial_number})`);
        });
        
        console.log(`\n   📍 Main Warden Office (${wardenOfficeEquipment.length} items):`);
        wardenOfficeEquipment.forEach(item => {
            console.log(`      • ${item.equipment_type}: ${item.name} (${item.serial_number})`);
        });

        // 6. Update the infrastructure view data
        console.log('\n5. Testing updated infrastructure queries...');
        
        const [hostelRoomsData] = await db.query(`
            SELECT 
                r.room_number,
                r.id as room_id,
                COUNT(DISTINCT i.item_id) as total_equipment,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' THEN i.item_id END) as tvs,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number LIKE '%Hostel%' OR r.room_number = 'Warden Office'
            GROUP BY r.id, r.room_number
            ORDER BY r.room_number
        `);
        
        console.log(`\n   📊 Infrastructure View Data:`);
        hostelRoomsData.forEach(room => {
            console.log(`      • ${room.room_number}: ${room.total_equipment} items`);
            console.log(`        - ${room.desktops} PCs, ${room.tvs} TVs, ${room.ups_units} UPS`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('\n🎯 FINAL CORRECT DISTRIBUTION:');
        console.log('   • Boys Hostel Warden Room: 1 TV + 1 PC + 1 UPS = 3 items');
        console.log('   • Girls Hostel Warden Room: 1 TV + 1 PC + 1 UPS = 3 items');
        console.log('   • Main Warden Office: 2 PCs = 2 items');
        console.log('   • Total Hostel Equipment: 8 items (2 TVs, 4 PCs, 2 UPS)');

        return {
            success: true,
            boysHostelItems: correctedBoys.length,
            girlsHostelItems: correctedGirls.length,
            wardenOfficeItems: wardenOfficeEquipment.length
        };

    } catch (error) {
        console.error('❌ Error fixing hostel equipment distribution:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    fixHostelEquipmentDistribution()
        .then((results) => {
            console.log('\n🎯 Hostel equipment distribution fixed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { fixHostelEquipmentDistribution };
