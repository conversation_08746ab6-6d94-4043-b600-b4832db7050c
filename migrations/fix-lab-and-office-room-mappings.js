/**
 * Migration to fix lab and office room mappings
 * 
 * This migration:
 * 1. Adds missing rooms for labs and offices
 * 2. Corrects room mappings for Computer Lab 1, Computer Lab 2, Science Labs, Library, etc.
 * 3. Updates all inventory tables with proper room assignments
 * 4. Preserves original location data
 */

const db = require('../config/database');

async function fixLabAndOfficeRoomMappings() {
    console.log('🔄 Fixing lab and office room mappings...\n');

    try {
        // 1. Add missing rooms for labs and offices
        console.log('1. Adding missing rooms for labs and offices...');
        
        const missingRooms = [
            { room_number: 'Computer Lab 1', building: 'Main Building', floor: 1, capacity: 30 },
            { room_number: 'Computer Lab 2', building: 'Main Building', floor: 1, capacity: 30 },
            { room_number: 'Biology Lab', building: 'Main Building', floor: 2, capacity: 25 },
            { room_number: 'Chemistry Lab', building: 'Main Building', floor: 2, capacity: 25 },
            { room_number: 'Physics Lab', building: 'Main Building', floor: 2, capacity: 25 },
            { room_number: 'Library', building: 'Main Building', floor: 1, capacity: 50 },
            { room_number: 'Principal Office', building: 'Main Building', floor: 3, capacity: 10 },
            { room_number: 'Vice Principal Office', building: 'Main Building', floor: 3, capacity: 8 },
            { room_number: 'Admin Office', building: 'Main Building', floor: 3, capacity: 15 },
            { room_number: 'Computer Office', building: 'Main Building', floor: 1, capacity: 10 },
            { room_number: 'Warden Office', building: 'Main Building', floor: 2, capacity: 5 }
        ];

        const addedRooms = [];
        
        for (const room of missingRooms) {
            // Check if room already exists
            const [existing] = await db.query('SELECT id FROM rooms WHERE room_number = ?', [room.room_number]);
            
            if (existing.length === 0) {
                const [result] = await db.query(`
                    INSERT INTO rooms (room_number, building, floor, capacity, created_at, updated_at)
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                `, [room.room_number, room.building, room.floor, room.capacity]);
                
                addedRooms.push({ ...room, id: result.insertId });
                console.log(`   ✅ Added: ${room.room_number} (ID: ${result.insertId})`);
            } else {
                console.log(`   ⚠️  Already exists: ${room.room_number} (ID: ${existing[0].id})`);
                addedRooms.push({ ...room, id: existing[0].id });
            }
        }

        // 2. Get all rooms for mapping
        console.log('\n2. Getting updated room mappings...');
        const [allRooms] = await db.query('SELECT id, room_number FROM rooms ORDER BY room_number');
        
        const roomMap = new Map();
        allRooms.forEach(room => {
            roomMap.set(room.room_number, room.id);
        });
        
        console.log(`   ✅ Found ${allRooms.length} rooms total`);

        // 3. Create comprehensive location mappings
        console.log('\n3. Creating comprehensive location mappings...');
        
        const locationMappings = new Map([
            // Computer Labs
            ['Computer Lab 1', 'Computer Lab 1'],
            ['Computer Lab-1', 'Computer Lab 1'],
            ['Computer LAB-1', 'Computer Lab 1'],
            ['Computer Lab - 1', 'Computer Lab 1'],
            
            ['Computer Lab 2', 'Computer Lab 2'],
            ['Computer Lab-2', 'Computer Lab 2'],
            ['Computer LAB-2', 'Computer Lab 2'],
            ['Computer Lab - 2', 'Computer Lab 2'],
            
            // Science Labs
            ['Biology Lab', 'Biology Lab'],
            ['Science LAB Room -1 (BIOLOGY)', 'Biology Lab'],
            
            ['Chemistry Lab', 'Chemistry Lab'],
            ['Science LAB Room -3 (CHEMSTRY)', 'Chemistry Lab'],
            
            ['Physics Lab', 'Physics Lab'],
            ['Science LAB Room -2 (PHYSICS)', 'Physics Lab'],
            
            // Offices
            ['Principal Office', 'Principal Office'],
            ['Principal Room', 'Principal Office'],
            
            ['Vice Principal Office', 'Vice Principal Office'],
            
            ['Admin Office', 'Admin Office'],
            ['Office Room', 'Admin Office'],
            
            ['Computer Office', 'Computer Office'],
            
            ['Warden Office', 'Warden Office'],
            
            // Library
            ['Library', 'Library'],
            ['Library Room', 'Library'],
            
            // Keep existing room mappings
            ['Room 1', 'Room 1'],
            ['Room 2', 'Room 2'],
            ['Room 3', 'Room 3'],
            ['Room 4', 'Room 4'],
            ['Room 5', 'Room 5'],
            ['Room 6', 'Room 6'],
            ['Room 7', 'Room 7'],
            ['Room 8', 'Room 8'],
            ['Room 9', 'Room 9'],
            ['Room 10', 'Room 10'],
            ['Room 11', 'Room 11'],
            ['Room 12', 'Room 12'],
            ['Room 13', 'Room 13'],
            ['Room 14', 'Room 14'],
            ['Room 15', 'Room 15'],
            ['Room 16', 'Room 16'],
            ['Room 17', 'Room 17'],
            ['Room 18', 'Room 18'],
            ['Room 19', 'Room 19'],
            ['Room 20', 'Room 20']
        ]);

        // 4. Update inventory_items table
        console.log('\n4. Updating inventory_items table mappings...');
        
        const [inventoryItems] = await db.query('SELECT item_id, location FROM inventory_items WHERE location IS NOT NULL');
        let inventoryUpdated = 0;
        
        for (const item of inventoryItems) {
            const location = item.location.trim();
            
            if (locationMappings.has(location)) {
                const mappedRoom = locationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE inventory_items SET room_id = ? WHERE item_id = ?', [roomId, item.item_id]);
                    console.log(`   ✅ Item ${item.item_id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    inventoryUpdated++;
                }
            }
        }

        // 5. Update it_inventory table
        console.log('\n5. Updating it_inventory table mappings...');
        
        const [itItems] = await db.query('SELECT id, location FROM it_inventory WHERE location IS NOT NULL');
        let itUpdated = 0;
        
        for (const item of itItems) {
            const location = item.location.trim();
            
            if (locationMappings.has(location)) {
                const mappedRoom = locationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE it_inventory SET room_id = ? WHERE id = ?', [roomId, item.id]);
                    console.log(`   ✅ IT Item ${item.id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    itUpdated++;
                }
            }
        }

        // 6. Update electrical_inventory table (for labs and offices)
        console.log('\n6. Updating electrical_inventory table for labs and offices...');
        
        const [electricalItems] = await db.query('SELECT id, location FROM electrical_inventory WHERE location LIKE "%Lab%" OR location LIKE "%Office%" OR location LIKE "%Library%"');
        let electricalUpdated = 0;
        
        for (const item of electricalItems) {
            if (!item.location) continue;
            
            const location = item.location.trim();
            
            if (locationMappings.has(location)) {
                const mappedRoom = locationMappings.get(location);
                const roomId = roomMap.get(mappedRoom);
                
                if (roomId) {
                    await db.query('UPDATE electrical_inventory SET room_id = ? WHERE id = ?', [roomId, item.id]);
                    console.log(`   ✅ Electrical Item ${item.id}: "${location}" → ${mappedRoom} (ID: ${roomId})`);
                    electricalUpdated++;
                }
            }
        }

        // 7. Verify results
        console.log('\n7. Verifying updated mappings...');
        
        const [verifyResults] = await db.query(`
            SELECT 
                r.room_number,
                COUNT(DISTINCT i.item_id) as inventory_items,
                COUNT(DISTINCT it.id) as it_items,
                COUNT(DISTINCT e.id) as electrical_items
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            LEFT JOIN it_inventory it ON r.id = it.room_id
            LEFT JOIN electrical_inventory e ON r.id = e.room_id
            WHERE r.room_number LIKE '%Lab%' OR r.room_number LIKE '%Office%' OR r.room_number = 'Library'
            GROUP BY r.id, r.room_number
            HAVING (inventory_items > 0 OR it_items > 0 OR electrical_items > 0)
            ORDER BY r.room_number
        `);

        console.log('\n   📋 Lab and Office equipment distribution:');
        verifyResults.forEach(room => {
            console.log(`      • ${room.room_number}:`);
            console.log(`        Inventory: ${room.inventory_items}, IT: ${room.it_items}, Electrical: ${room.electrical_items}`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log(`📊 Summary:`);
        console.log(`   • Rooms added: ${addedRooms.filter(r => r.id).length}`);
        console.log(`   • Inventory items updated: ${inventoryUpdated}`);
        console.log(`   • IT items updated: ${itUpdated}`);
        console.log(`   • Electrical items updated: ${electricalUpdated}`);
        console.log(`   • Labs and offices now properly mapped to distinct rooms`);

        return {
            success: true,
            roomsAdded: addedRooms.length,
            inventoryUpdated,
            itUpdated,
            electricalUpdated
        };

    } catch (error) {
        console.error('❌ Error fixing lab and office mappings:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    fixLabAndOfficeRoomMappings()
        .then((results) => {
            console.log('\n🎯 Lab and office room mappings fixed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { fixLabAndOfficeRoomMappings };
