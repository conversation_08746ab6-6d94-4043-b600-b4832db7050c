/**
 * Migration to fix teacher profile issues
 * This script fixes the following issues:
 * 1. Updates the teacher profile API to use lecture_schedule data
 * 2. Fixes the timetable timings to match the seasonal requirements
 * 3. Ensures all classes are shown in the timetable
 */

const db = require('../config/database');

// Helper function to format time (HH:MM:SS)
function formatTime(hours, minutes, seconds = 0) {
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

async function fixTeacherProfileIssues() {
  try {
    console.log('Fixing teacher profile issues...');
    
    // Get the demo teacher
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);
    
    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }
    
    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);
    
    // Fix 1: Update lecture_schedule timings to match seasonal requirements
    
    // Clear existing lecture schedule for the teacher
    await db.query(`
      DELETE FROM lecture_schedule
      WHERE teacher_id = ?
    `, [teacherId]);
    
    console.log('Cleared existing lecture schedule for the teacher');
    
    // Get subject-class assignments
    const [assignments] = await db.query(`
      SELECT id, class_id, subject_id
      FROM subject_class_assignment
      LIMIT 1
    `);
    
    if (assignments.length === 0) {
      console.error('No subject-class assignments found');
      
      // Create a new assignment
      const [result] = await db.query(`
        INSERT INTO subject_class_assignment (class_id, subject_id)
        VALUES (1, 1)
      `);
      
      assignments.push({
        id: result.insertId,
        class_id: 1,
        subject_id: 1
      });
      
      console.log('Created new assignment with ID:', result.insertId);
    }
    
    const assignmentId = assignments[0].id;
    console.log('Using assignment ID:', assignmentId);
    
    // Get teacher's assigned classes
    const [teacherClasses] = await db.query(`
      SELECT 
        tc.id, 
        tc.classroom_id,
        c.id as class_id,
        c.grade,
        cr.section,
        t.name as trade_name,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);
    
    if (teacherClasses.length === 0) {
      console.error('No classes assigned to teacher');
      return false;
    }
    
    console.log(`Found ${teacherClasses.length} classes assigned to teacher`);
    
    // Define the schedule for each class
    // 5 classes per week (2 theory, 2 practical, 1 lab/project)
    const scheduleTemplate = [
      { day: 'Monday', period: 1, duration: 1, is_practical: false, topic: 'Theory' },
      { day: 'Tuesday', period: 3, duration: 2, is_practical: true, topic: 'Practical' },
      { day: 'Wednesday', period: 2, duration: 1, is_practical: false, topic: 'Theory' },
      { day: 'Thursday', period: 5, duration: 2, is_practical: true, topic: 'Practical' },
      { day: 'Friday', period: 7, duration: 2, is_practical: true, topic: 'Lab/Project' }
    ];
    
    // Define seasonal timings
    const seasonalTimings = {
      summer: { // April to September
        start: '08:00:00',
        end: '14:00:00',
        periods: [
          { start: formatTime(8, 0), end: formatTime(8, 40) },
          { start: formatTime(8, 45), end: formatTime(9, 25) },
          { start: formatTime(9, 30), end: formatTime(10, 10) },
          { start: formatTime(10, 15), end: formatTime(10, 55) },
          { start: formatTime(11, 0), end: formatTime(11, 40) },
          { start: formatTime(11, 45), end: formatTime(12, 25) },
          { start: formatTime(12, 30), end: formatTime(13, 10) },
          { start: formatTime(13, 15), end: formatTime(13, 55) }
        ]
      },
      october: { // October
        start: '08:30:00',
        end: '14:50:00',
        periods: [
          { start: formatTime(8, 30), end: formatTime(9, 10) },
          { start: formatTime(9, 15), end: formatTime(9, 55) },
          { start: formatTime(10, 0), end: formatTime(10, 40) },
          { start: formatTime(10, 45), end: formatTime(11, 25) },
          { start: formatTime(11, 30), end: formatTime(12, 10) },
          { start: formatTime(12, 15), end: formatTime(12, 55) },
          { start: formatTime(13, 0), end: formatTime(13, 40) },
          { start: formatTime(13, 45), end: formatTime(14, 25) }
        ]
      },
      winter: { // November to March
        start: '09:00:00',
        end: '15:20:00',
        periods: [
          { start: formatTime(9, 0), end: formatTime(9, 40) },
          { start: formatTime(9, 45), end: formatTime(10, 25) },
          { start: formatTime(10, 30), end: formatTime(11, 10) },
          { start: formatTime(11, 15), end: formatTime(11, 55) },
          { start: formatTime(12, 0), end: formatTime(12, 40) },
          { start: formatTime(12, 45), end: formatTime(13, 25) },
          { start: formatTime(13, 30), end: formatTime(14, 10) },
          { start: formatTime(14, 15), end: formatTime(14, 55) }
        ]
      }
    };
    
    // Create lecture schedule for each class
    for (const cls of teacherClasses) {
      console.log(`Creating lecture schedule for class ${cls.full_class_name}`);
      
      for (const slot of scheduleTemplate) {
        // Get period timings for each season
        const summerPeriod = seasonalTimings.summer.periods[slot.period - 1];
        const summerEndPeriod = seasonalTimings.summer.periods[slot.period + slot.duration - 2];
        
        const octoberPeriod = seasonalTimings.october.periods[slot.period - 1];
        const octoberEndPeriod = seasonalTimings.october.periods[slot.period + slot.duration - 2];
        
        const winterPeriod = seasonalTimings.winter.periods[slot.period - 1];
        const winterEndPeriod = seasonalTimings.winter.periods[slot.period + slot.duration - 2];
        
        // Insert summer schedule (April to September)
        await db.query(`
          INSERT INTO lecture_schedule (
            assignment_id, teacher_id, day_of_week, start_time, end_time, 
            classroom, semester, is_active
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          assignmentId,
          teacherId,
          slot.day,
          summerPeriod.start,
          summerEndPeriod.end,
          `Room ${cls.full_class_name}`,
          'Summer',
          slot.day === 'Monday' || slot.day === 'Wednesday' ? 1 : 0 // Only active on Monday and Wednesday
        ]);
        
        // Insert october schedule
        await db.query(`
          INSERT INTO lecture_schedule (
            assignment_id, teacher_id, day_of_week, start_time, end_time, 
            classroom, semester, is_active
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          assignmentId,
          teacherId,
          slot.day,
          octoberPeriod.start,
          octoberEndPeriod.end,
          `Room ${cls.full_class_name}`,
          'Fall',
          slot.day === 'Tuesday' ? 1 : 0 // Only active on Tuesday
        ]);
        
        // Insert winter schedule (November to March)
        await db.query(`
          INSERT INTO lecture_schedule (
            assignment_id, teacher_id, day_of_week, start_time, end_time, 
            classroom, semester, is_active
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          assignmentId,
          teacherId,
          slot.day,
          winterPeriod.start,
          winterEndPeriod.end,
          `Room ${cls.full_class_name}`,
          'Winter',
          slot.day === 'Thursday' || slot.day === 'Friday' ? 1 : 0 // Only active on Thursday and Friday
        ]);
        
        console.log(`Created lecture schedule for ${cls.full_class_name} on ${slot.day}`);
      }
    }
    
    // Fix 2: Update teacher_lectures to include all classes
    await db.query(`
      DELETE FROM teacher_lectures
      WHERE teacher_id = ?
    `, [teacherId]);
    
    console.log('Cleared existing teacher lectures');
    
    // Get next Monday's date
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
    
    // Generate lectures for each class for the next 5 days
    for (const cls of teacherClasses) {
      for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
        const lectureDate = new Date(nextMonday);
        lectureDate.setDate(nextMonday.getDate() + dayOffset);
        
        // Skip weekends
        if (lectureDate.getDay() === 0 || lectureDate.getDay() === 6) {
          continue;
        }
        
        // Format date as YYYY-MM-DD
        const formattedDate = lectureDate.toISOString().split('T')[0];
        
        // Get day of week
        const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayOfWeek = daysOfWeek[lectureDate.getDay()];
        
        // Get the schedule for this day
        const daySchedule = scheduleTemplate.find(s => s.day === dayOfWeek);
        if (!daySchedule) continue;
        
        // Get the appropriate timing based on the month
        const month = lectureDate.getMonth() + 1; // JavaScript months are 0-indexed
        let timing;
        
        if (month === 10) {
          timing = seasonalTimings.october;
        } else if (month >= 4 && month <= 9) {
          timing = seasonalTimings.summer;
        } else {
          timing = seasonalTimings.winter;
        }
        
        // Get period timing
        const periodStart = timing.periods[daySchedule.period - 1].start;
        const periodEnd = timing.periods[daySchedule.period + daySchedule.duration - 2].end;
        
        // Generate a topic
        const topics = [
          'Introduction to Programming',
          'Data Types and Variables',
          'Control Structures',
          'Functions and Methods',
          'Object-Oriented Programming',
          'Data Structures',
          'Algorithms',
          'File Handling',
          'Database Connectivity',
          'Web Development'
        ];
        
        const topic = topics[Math.floor(Math.random() * topics.length)];
        
        // Create teacher lecture
        await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId,
          formattedDate,
          periodStart,
          periodEnd,
          daySchedule.period - 1,
          `Class ${cls.grade}`,
          cls.section,
          'Computer Science',
          topic,
          cls.grade,
          cls.trade_name,
          `Room ${cls.grade}${cls.section}`,
          Math.random() > 0.7 ? 'delivered' : 'pending'
        ]);
        
        console.log(`Created lecture for ${cls.full_class_name} on ${formattedDate}`);
      }
    }
    
    console.log('Teacher profile issues fixed successfully');
    return true;
  } catch (error) {
    console.error('Error fixing teacher profile issues:', error);
    return false;
  }
}

// Execute the migration
fixTeacherProfileIssues()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
