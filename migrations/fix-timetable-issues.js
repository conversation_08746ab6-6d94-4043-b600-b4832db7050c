/**
 * Migration to fix timetable issues
 * This script fixes the following issues:
 * 1. Ensures all classes are mapped onto the timetable
 * 2. Corrects the timings to 8AM to 2PM until September 30, 2025
 */

const db = require('../config/database');

// Helper function to format time (HH:MM:SS)
function formatTime(hours, minutes, seconds = 0) {
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

async function fixTimetableIssues() {
  try {
    console.log('Fixing timetable issues...');

    // Get the demo teacher
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Get Computer Science subject
    const [subject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Science'
    `);

    if (subject.length === 0) {
      console.error('Computer Science subject not found');
      return false;
    }

    const subjectId = subject[0].id;
    console.log(`Found Computer Science subject, ID: ${subjectId}`);

    // Get teacher's assigned classes
    const [teacherClasses] = await db.query(`
      SELECT
        tc.id,
        tc.classroom_id,
        c.id as class_id,
        c.grade,
        cr.section,
        t.id as trade_id,
        t.name as trade_name,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);

    if (teacherClasses.length === 0) {
      console.error('No classes assigned to teacher');
      return false;
    }

    console.log(`Found ${teacherClasses.length} classes assigned to teacher`);

    // Clear existing lecture schedule for the teacher
    await db.query(`
      DELETE FROM lecture_schedule
      WHERE teacher_id = ?
    `, [teacherId]);

    console.log('Cleared existing lecture schedule for the teacher');

    // Get subject-class assignments
    const [assignments] = await db.query(`
      SELECT id, class_id, subject_id
      FROM subject_class_assignment
      LIMIT 1
    `);

    let assignmentId;
    if (assignments.length === 0) {
      // Create a new assignment
      const [result] = await db.query(`
        INSERT INTO subject_class_assignment (class_id, subject_id)
        VALUES (1, ?)
      `, [subjectId]);

      assignmentId = result.insertId;
      console.log('Created new assignment with ID:', assignmentId);
    } else {
      assignmentId = assignments[0].id;
      console.log('Using assignment ID:', assignmentId);
    }

    // Define the schedule for each class
    // 5 classes per week (2 theory, 2 practical, 1 lab/project)
    const scheduleTemplate = [
      { day: 'Monday', period: 1, duration: 1, is_practical: false, topic: 'Theory' },
      { day: 'Tuesday', period: 3, duration: 2, is_practical: true, topic: 'Practical' },
      { day: 'Wednesday', period: 2, duration: 1, is_practical: false, topic: 'Theory' },
      { day: 'Thursday', period: 5, duration: 2, is_practical: true, topic: 'Practical' },
      { day: 'Friday', period: 7, duration: 2, is_practical: true, topic: 'Lab/Project' }
    ];

    // Define summer timings (8AM to 2PM)
    const summerTimings = {
      start: '08:00:00',
      end: '14:00:00',
      periods: [
        { start: formatTime(8, 0), end: formatTime(8, 40) },
        { start: formatTime(8, 45), end: formatTime(9, 25) },
        { start: formatTime(9, 30), end: formatTime(10, 10) },
        { start: formatTime(10, 15), end: formatTime(10, 55) },
        { start: formatTime(11, 0), end: formatTime(11, 40) },
        { start: formatTime(11, 45), end: formatTime(12, 25) },
        { start: formatTime(12, 30), end: formatTime(13, 10) },
        { start: formatTime(13, 15), end: formatTime(13, 55) }
      ]
    };

    // Create lecture schedule for each class
    for (const cls of teacherClasses) {
      console.log(`Creating lecture schedule for class ${cls.full_class_name}`);

      for (const slot of scheduleTemplate) {
        // Get period timings
        const periodStart = summerTimings.periods[slot.period - 1];
        const periodEnd = summerTimings.periods[slot.period + slot.duration - 2];

        // Insert into lecture_schedule
        await db.query(`
          INSERT INTO lecture_schedule (
            assignment_id, teacher_id, day_of_week, start_time, end_time,
            classroom, semester, is_active
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          assignmentId,
          teacherId,
          slot.day,
          periodStart.start,
          periodEnd.end,
          cls.full_class_name,
          'Summer',
          1 // All active
        ]);

        console.log(`Created lecture schedule for ${cls.full_class_name} on ${slot.day}`);
      }
    }

    // Skip teacher_weekly_lectures as it's not updatable
    console.log('Skipping teacher_weekly_lectures table as it may not be updatable');

    // Skip class_weekly_lectures as it may also not be updatable
    console.log('Skipping class_weekly_lectures table as it may not be updatable');

    // Update teacher_lectures to include all classes with correct timings
    await db.query(`
      DELETE FROM teacher_lectures
      WHERE teacher_id = ?
    `, [teacherId]);

    console.log('Cleared existing teacher lectures');

    // Get next Monday's date
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);

    // Generate lectures for each class for the next 5 days
    for (const cls of teacherClasses) {
      for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
        const lectureDate = new Date(nextMonday);
        lectureDate.setDate(nextMonday.getDate() + dayOffset);

        // Skip weekends
        if (lectureDate.getDay() === 0 || lectureDate.getDay() === 6) {
          continue;
        }

        // Format date as YYYY-MM-DD
        const formattedDate = lectureDate.toISOString().split('T')[0];

        // Get day of week
        const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayOfWeek = daysOfWeek[lectureDate.getDay()];

        // Get the schedule for this day
        const daySchedule = scheduleTemplate.find(s => s.day === dayOfWeek);
        if (!daySchedule) continue;

        // Get period timing
        const periodStart = summerTimings.periods[daySchedule.period - 1].start;
        const periodEnd = summerTimings.periods[daySchedule.period + daySchedule.duration - 2].end;

        // Generate a topic
        const topics = [
          'Introduction to Programming',
          'Data Types and Variables',
          'Control Structures',
          'Functions and Methods',
          'Object-Oriented Programming',
          'Data Structures',
          'Algorithms',
          'File Handling',
          'Database Connectivity',
          'Web Development'
        ];

        const topic = topics[Math.floor(Math.random() * topics.length)];

        // Create teacher lecture
        await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId,
          formattedDate,
          periodStart,
          periodEnd,
          daySchedule.period - 1,
          `Class ${cls.grade}`,
          cls.section,
          'Computer Science',
          topic,
          cls.grade,
          cls.trade_name,
          `Room ${cls.grade}${cls.section}`,
          Math.random() > 0.7 ? 'delivered' : 'pending'
        ]);

        console.log(`Created lecture for ${cls.full_class_name} on ${formattedDate}`);
      }
    }

    console.log('Timetable issues fixed successfully');
    return true;
  } catch (error) {
    console.error('Error fixing timetable issues:', error);
    return false;
  }
}

// Execute the migration
fixTimetableIssues()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
