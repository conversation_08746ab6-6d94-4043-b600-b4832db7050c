/**
 * Migration script to remove backup tables created during previous migrations
 */

const db = require('../config/database');

async function removeBackupTables() {
  try {
    console.log('Starting migration: Removing backup tables');
    
    // Get a list of all tables in the database
    const [tables] = await db.query(`
      SHOW TABLES
    `);
    
    // Extract table names
    const tableNames = tables.map(table => Object.values(table)[0]);
    
    // Find backup tables (tables with _backup suffix)
    const backupTables = tableNames.filter(name => name.endsWith('_backup'));
    
    console.log(`Found ${backupTables.length} backup tables:`);
    backupTables.forEach(table => {
      console.log(`- ${table}`);
    });
    
    // Drop each backup table
    for (const table of backupTables) {
      try {
        await db.query(`
          DROP TABLE IF EXISTS ${table}
        `);
        
        console.log(`Dropped table: ${table}`);
      } catch (error) {
        console.error(`Error dropping table ${table}:`, error.message);
      }
    }
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
removeBackupTables();
