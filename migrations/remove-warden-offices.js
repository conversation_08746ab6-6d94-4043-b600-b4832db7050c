/**
 * Migration to remove Boys Warden Office and Girls Warden Office
 * 
 * This migration:
 * 1. Moves equipment from warden offices to appropriate locations
 * 2. Removes Boys Warden Office and Girls Warden Office rooms
 * 3. Keeps hostel warden rooms (monitoring rooms with TVs)
 * 4. Updates infrastructure view to show only hostel warden rooms
 * 5. Preserves all equipment records
 */

const db = require('../config/database');

async function removeWardenOffices() {
    console.log('🔄 Removing Boys Warden Office and Girls Warden Office...\n');

    try {
        // 1. Check current warden facilities
        console.log('1. Checking current warden facilities...');
        
        const [currentWardenFacilities] = await db.query(`
            SELECT id, room_number, building, floor
            FROM rooms 
            WHERE room_number LIKE '%Warden%'
            ORDER BY room_number
        `);
        
        console.log(`Found ${currentWardenFacilities.length} warden facilities:`);
        currentWardenFacilities.forEach(room => {
            console.log(`   • ${room.room_number} (ID: ${room.id}) - ${room.building}, Floor ${room.floor}`);
        });

        // 2. Get equipment from warden offices to be removed
        console.log('\n2. Checking equipment in warden offices to be removed...');
        
        const [boysWardenOffice] = await db.query('SELECT id FROM rooms WHERE room_number = ?', ['Boys Warden Office']);
        const [girlsWardenOffice] = await db.query('SELECT id FROM rooms WHERE room_number = ?', ['Girls Warden Office']);
        
        let boysOfficeEquipment = [];
        let girlsOfficeEquipment = [];
        
        if (boysWardenOffice.length > 0) {
            const [equipment] = await db.query(`
                SELECT item_id, name, manufacturer, model, serial_number, location
                FROM inventory_items 
                WHERE room_id = ?
                ORDER BY name
            `, [boysWardenOffice[0].id]);
            
            boysOfficeEquipment = equipment;
            console.log(`   Boys Warden Office equipment (${equipment.length} items):`);
            equipment.forEach(item => {
                console.log(`      • ${item.name} (${item.serial_number})`);
            });
        }
        
        if (girlsWardenOffice.length > 0) {
            const [equipment] = await db.query(`
                SELECT item_id, name, manufacturer, model, serial_number, location
                FROM inventory_items 
                WHERE room_id = ?
                ORDER BY name
            `, [girlsWardenOffice[0].id]);
            
            girlsOfficeEquipment = equipment;
            console.log(`   Girls Warden Office equipment (${equipment.length} items):`);
            equipment.forEach(item => {
                console.log(`      • ${item.name} (${item.serial_number})`);
            });
        }

        // 3. Move equipment to appropriate hostel warden rooms
        console.log('\n3. Moving equipment to hostel warden rooms...');
        
        const [boysHostelRoom] = await db.query('SELECT id FROM rooms WHERE room_number = ?', ['Boys Hostel Warden Room']);
        const [girlsHostelRoom] = await db.query('SELECT id FROM rooms WHERE room_number = ?', ['Girls Hostel Warden Room']);
        
        if (boysHostelRoom.length === 0 || girlsHostelRoom.length === 0) {
            throw new Error('Hostel warden rooms not found');
        }
        
        const boysHostelRoomId = boysHostelRoom[0].id;
        const girlsHostelRoomId = girlsHostelRoom[0].id;
        
        // Move Boys Warden Office equipment to Boys Hostel Warden Room
        let equipmentMoved = 0;
        
        for (const item of boysOfficeEquipment) {
            await db.query(`
                UPDATE inventory_items 
                SET room_id = ?, location = 'Boys Hostel Warden Room', updated_at = NOW()
                WHERE item_id = ?
            `, [boysHostelRoomId, item.item_id]);
            
            console.log(`   ✅ Moved ${item.name} to Boys Hostel Warden Room`);
            equipmentMoved++;
        }
        
        // Move Girls Warden Office equipment to Girls Hostel Warden Room
        for (const item of girlsOfficeEquipment) {
            await db.query(`
                UPDATE inventory_items 
                SET room_id = ?, location = 'Girls Hostel Warden Room', updated_at = NOW()
                WHERE item_id = ?
            `, [girlsHostelRoomId, item.item_id]);
            
            console.log(`   ✅ Moved ${item.name} to Girls Hostel Warden Room`);
            equipmentMoved++;
        }

        // 4. Remove the warden office rooms
        console.log('\n4. Removing warden office rooms...');
        
        let roomsRemoved = 0;
        
        if (boysWardenOffice.length > 0) {
            await db.query('DELETE FROM rooms WHERE id = ?', [boysWardenOffice[0].id]);
            console.log('   ✅ Removed Boys Warden Office');
            roomsRemoved++;
        }
        
        if (girlsWardenOffice.length > 0) {
            await db.query('DELETE FROM rooms WHERE id = ?', [girlsWardenOffice[0].id]);
            console.log('   ✅ Removed Girls Warden Office');
            roomsRemoved++;
        }

        // 5. Verify the remaining warden facilities
        console.log('\n5. Verifying remaining warden facilities...');
        
        const [remainingWardenFacilities] = await db.query(`
            SELECT 
                r.id,
                r.room_number,
                r.building,
                r.floor,
                COUNT(DISTINCT i.item_id) as total_equipment,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' THEN i.item_id END) as tvs,
                COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number LIKE '%Warden%'
            GROUP BY r.id, r.room_number, r.building, r.floor
            ORDER BY r.room_number
        `);
        
        console.log(`\n   📊 Remaining warden facilities (${remainingWardenFacilities.length}):`);
        remainingWardenFacilities.forEach(room => {
            console.log(`\n      🏠 ${room.room_number} (ID: ${room.id})`);
            console.log(`         Building: ${room.building}`);
            console.log(`         Floor: ${room.floor}`);
            console.log(`         Total Equipment: ${room.total_equipment} items`);
            console.log(`         Equipment: ${room.desktops} PCs, ${room.tvs} TVs, ${room.ups_units} UPS`);
        });

        // 6. Get detailed equipment for remaining facilities
        console.log('\n6. Detailed equipment in remaining warden facilities...');
        
        for (const room of remainingWardenFacilities) {
            if (room.total_equipment > 0) {
                const [equipment] = await db.query(`
                    SELECT name, manufacturer, model, serial_number, status
                    FROM inventory_items 
                    WHERE room_id = ?
                    ORDER BY name
                `, [room.id]);
                
                console.log(`\n      📍 ${room.room_number} equipment:`);
                equipment.forEach((item, index) => {
                    console.log(`         ${index + 1}. ${item.name}`);
                    console.log(`            • ${item.manufacturer} ${item.model} (${item.serial_number})`);
                    console.log(`            • Status: ${item.status}`);
                });
            }
        }

        console.log('\n✅ Migration completed successfully!');
        console.log('\n🎯 WARDEN FACILITIES STRUCTURE UPDATED:');
        console.log('   ❌ Removed: Boys Warden Office (administrative)');
        console.log('   ❌ Removed: Girls Warden Office (administrative)');
        console.log('   ✅ Kept: Boys Hostel Warden Room (monitoring with TV + PC + UPS)');
        console.log('   ✅ Kept: Girls Hostel Warden Room (monitoring with TV + PC + UPS)');
        
        console.log('\n📊 Summary:');
        console.log(`   • Rooms removed: ${roomsRemoved}`);
        console.log(`   • Equipment items moved: ${equipmentMoved}`);
        console.log(`   • Remaining warden facilities: ${remainingWardenFacilities.length}`);
        
        console.log('\n📋 Infrastructure Impact:');
        console.log('   • Hostel Facilities section will show only 2 warden rooms');
        console.log('   • Administrative offices section updated');
        console.log('   • Equipment consolidated in hostel monitoring rooms');
        console.log('   • Simplified warden facility structure');

        return {
            success: true,
            roomsRemoved,
            equipmentMoved,
            remainingFacilities: remainingWardenFacilities.length
        };

    } catch (error) {
        console.error('❌ Error removing warden offices:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    removeWardenOffices()
        .then((results) => {
            console.log('\n🎯 Warden offices removed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { removeWardenOffices };
