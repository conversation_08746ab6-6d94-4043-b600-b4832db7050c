/**
 * Migration to set up demo teacher data
 * This script sets up the demo teacher as a computer science teacher
 * with all related data (subjects, classes, timetable, etc.)
 */

const db = require('../config/database');

async function setupDemoTeacherData() {
  try {
    console.log('Setting up demo teacher data...');

    // Get the demo teacher (<EMAIL>)
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Update teacher profile with computer science specialization
    await db.query(`
      UPDATE users
      SET
        subjects = 'Computer Science, Mathematics, Physics',
        bio = 'Experienced computer science teacher with 10+ years of experience teaching programming, data structures, and algorithms. Passionate about helping students develop problem-solving skills and computational thinking.',
        field_of_study = 'M.Tech in Computer Science, B.Tech in Information Technology, Certified Educator'
      WHERE id = ?
    `, [teacherId]);

    console.log('Updated teacher profile');

    // Get computer science and related subjects
    const [subjects] = await db.query(`
      SELECT id, name, code
      FROM subjects
      WHERE name IN ('Computer Science', 'Mathematics', 'Physics', 'Information Practices', 'Web Technology')
      OR name LIKE '%Computer%' OR name LIKE '%Programming%' OR name LIKE '%IT%'
    `);

    if (subjects.length === 0) {
      console.error('No computer science related subjects found');

      // Create computer science subjects if they don't exist
      console.log('Creating computer science subjects...');

      const csSubjects = [
        { name: 'Computer Science', code: 'CS', description: 'Study of computers and computational systems', subject_group: 12 },
        { name: 'Information Practices', code: 'IP', description: 'Database management and web applications', subject_group: 12 },
        { name: 'Web Technology', code: 'WT', description: 'Web development and design', subject_group: 12 },
        { name: 'Programming in Python', code: 'PY', description: 'Python programming language', subject_group: 12 },
        { name: 'Data Structures', code: 'DS', description: 'Data structures and algorithms', subject_group: 12 }
      ];

      for (const subject of csSubjects) {
        const [result] = await db.query(`
          INSERT INTO subjects (name, code, description, subject_group)
          VALUES (?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            code = VALUES(code),
            description = VALUES(description),
            subject_group = VALUES(subject_group)
        `, [subject.name, subject.code, subject.description, subject.subject_group]);

        console.log(`Created/Updated subject: ${subject.name}`);
      }

      // Get the newly created subjects
      const [newSubjects] = await db.query(`
        SELECT id, name, code
        FROM subjects
        WHERE name IN ('Computer Science', 'Information Practices', 'Web Technology', 'Programming in Python', 'Data Structures')
      `);

      if (newSubjects.length === 0) {
        console.error('Failed to create computer science subjects');
        return false;
      }

      // Use the newly created subjects
      subjects.push(...newSubjects);
    }

    console.log(`Found ${subjects.length} computer science related subjects`);

    // Check if teacher_subjects table exists
    const [teacherSubjectsTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_subjects'
    `);

    if (teacherSubjectsTable.length === 0) {
      // Create teacher_subjects table
      await db.query(`
        CREATE TABLE teacher_subjects (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          subject_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_subject (teacher_id, subject_id)
        )
      `);

      console.log('Created teacher_subjects table');
    } else {
      // Clear existing teacher subjects
      await db.query(`
        DELETE FROM teacher_subjects
        WHERE teacher_id = ?
      `, [teacherId]);
    }

    // Assign subjects to teacher
    for (const subject of subjects) {
      await db.query(`
        INSERT INTO teacher_subjects (teacher_id, subject_id)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE subject_id = VALUES(subject_id)
      `, [teacherId, subject.id]);

      console.log(`Assigned subject ${subject.name} to teacher`);
    }

    // Get classes (11th and 12th grade)
    const [classes] = await db.query(`
      SELECT c.id as class_id, c.name as class_name, c.grade,
             cr.id as classroom_id, cr.section, cr.room_number,
             t.id as trade_id, t.name as trade_name
      FROM classes c
      JOIN classrooms cr ON c.id = cr.class_id
      JOIN trades t ON cr.trade_id = t.id
      WHERE c.grade IN ('11', '12')
      AND (t.name LIKE '%Non-Medical%' OR t.name LIKE '%Science%')
    `);

    if (classes.length === 0) {
      console.error('No suitable classes found');

      // Create classes if they don't exist
      console.log('Creating classes and classrooms...');

      // Check if trades exist
      const [trades] = await db.query(`
        SELECT id, name FROM trades
        WHERE name IN ('NON-MEDICAL', 'MEDICAL', 'COMMERCE', 'ARTS STREAM')
      `);

      let nonMedicalTradeId = null;

      if (trades.length === 0) {
        // Create trades
        const [tradeResult] = await db.query(`
          INSERT INTO trades (name) VALUES ('NON-MEDICAL')
        `);

        nonMedicalTradeId = tradeResult.insertId;
        console.log(`Created NON-MEDICAL trade with ID: ${nonMedicalTradeId}`);
      } else {
        // Find NON-MEDICAL trade
        const nonMedicalTrade = trades.find(t => t.name === 'NON-MEDICAL');
        if (nonMedicalTrade) {
          nonMedicalTradeId = nonMedicalTrade.id;
        } else {
          // Create NON-MEDICAL trade
          const [tradeResult] = await db.query(`
            INSERT INTO trades (name) VALUES ('NON-MEDICAL')
          `);

          nonMedicalTradeId = tradeResult.insertId;
          console.log(`Created NON-MEDICAL trade with ID: ${nonMedicalTradeId}`);
        }
      }

      // Create classes
      const classGrades = ['11', '12'];
      const classSections = ['A', 'B'];

      for (const grade of classGrades) {
        // Create class
        const [classResult] = await db.query(`
          INSERT INTO classes (name, grade)
          VALUES (?, ?)
          ON DUPLICATE KEY UPDATE grade = VALUES(grade)
        `, [`Class ${grade}`, grade]);

        const classId = classResult.insertId;
        console.log(`Created/Updated class ${grade} with ID: ${classId}`);

        for (const section of classSections) {
          // Create classroom
          const [classroomResult] = await db.query(`
            INSERT INTO classrooms (class_id, section, room_number, trade_id)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
              section = VALUES(section),
              room_number = VALUES(room_number),
              trade_id = VALUES(trade_id)
          `, [classId, section, `${grade}${section}`, nonMedicalTradeId]);

          console.log(`Created/Updated classroom ${grade}${section}`);
        }
      }

      // Get the newly created classes
      const [newClasses] = await db.query(`
        SELECT c.id as class_id, c.name as class_name, c.grade,
               cr.id as classroom_id, cr.section, cr.room_number,
               t.id as trade_id, t.name as trade_name
        FROM classes c
        JOIN classrooms cr ON c.id = cr.class_id
        JOIN trades t ON cr.trade_id = t.id
        WHERE c.grade IN ('11', '12')
      `);

      if (newClasses.length === 0) {
        console.error('Failed to create classes');
        return false;
      }

      // Use the newly created classes
      classes.push(...newClasses);
    }

    console.log(`Found ${classes.length} classes`);

    // Check if teacher_classes table exists
    const [teacherClassesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_classes'
    `);

    if (teacherClassesTable.length === 0) {
      // Create teacher_classes table
      await db.query(`
        CREATE TABLE teacher_classes (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          classroom_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_classroom (teacher_id, classroom_id)
        )
      `);

      console.log('Created teacher_classes table');
    } else {
      // Clear existing teacher classes
      await db.query(`
        DELETE FROM teacher_classes
        WHERE teacher_id = ?
      `, [teacherId]);
    }

    // Assign classes to teacher
    for (const cls of classes) {
      await db.query(`
        INSERT INTO teacher_classes (teacher_id, classroom_id)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE classroom_id = VALUES(classroom_id)
      `, [teacherId, cls.classroom_id]);

      console.log(`Assigned class ${cls.grade}${cls.section} to teacher`);
    }

    // Create weekly timetable for the teacher
    console.log('Creating weekly timetable...');

    // Define lecture schedule
    const lectureSchedule = [
      { period: 1, start_time: '08:00:00', end_time: '08:40:00' },
      { period: 2, start_time: '08:45:00', end_time: '09:25:00' },
      { period: 3, start_time: '09:30:00', end_time: '10:10:00' },
      { period: 4, start_time: '10:15:00', end_time: '10:55:00' },
      { period: 5, start_time: '11:00:00', end_time: '11:40:00' },
      { period: 6, start_time: '11:45:00', end_time: '12:25:00' },
      { period: 7, start_time: '12:30:00', end_time: '13:10:00' },
      { period: 8, start_time: '13:15:00', end_time: '13:55:00' }
    ];

    // Check if class_weekly_lectures table exists
    const [cwlTable] = await db.query(`
      SHOW TABLES LIKE 'class_weekly_lectures'
    `);

    if (cwlTable.length === 0) {
      // Create class_weekly_lectures table
      await db.query(`
        CREATE TABLE IF NOT EXISTS class_weekly_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          class_id INT NOT NULL,
          day_of_week INT NOT NULL COMMENT '0=Sunday, 1=Monday, ..., 6=Saturday',
          period INT NOT NULL,
          subject_id INT NOT NULL,
          teacher_id INT NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          room VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE KEY unique_class_schedule (class_id, day_of_week, period)
        )
      `);

      console.log('Created class_weekly_lectures table');
    }

    // Check if teacher_weekly_lectures table exists
    const [twlTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_weekly_lectures'
    `);

    if (twlTable.length === 0) {
      // Create teacher_weekly_lectures table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_weekly_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          day_of_week INT NOT NULL COMMENT '0=Sunday, 1=Monday, ..., 6=Saturday',
          period INT NOT NULL,
          class_id INT NOT NULL,
          subject_id INT NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          room VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_schedule (teacher_id, day_of_week, period)
        )
      `);

      console.log('Created teacher_weekly_lectures table');
    }

    // Drop existing tables if they have issues
    try {
      await db.query(`DROP TABLE IF EXISTS teacher_weekly_lectures`);
      await db.query(`DROP TABLE IF EXISTS class_weekly_lectures`);

      console.log('Dropped existing timetable tables');
    } catch (error) {
      console.log('Error dropping tables:', error.message);
    }

    // Create timetable for Monday to Friday (1-5)
    for (let day = 1; day <= 5; day++) {
      // Assign 6 periods per day
      for (let period = 1; period <= 6; period++) {
        // Select a random subject
        const subject = subjects[Math.floor(Math.random() * subjects.length)];

        // Select a random class
        const cls = classes[Math.floor(Math.random() * classes.length)];

        // Get period times
        const periodInfo = lectureSchedule[period - 1];
        if (!periodInfo) {
          console.error(`No schedule found for period ${period}`);
          continue;
        }

        // Insert into class_weekly_lectures
        await db.query(`
          INSERT INTO class_weekly_lectures (
            class_id, day_of_week, period, subject_id, teacher_id,
            start_time, end_time, room
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            subject_id = VALUES(subject_id),
            teacher_id = VALUES(teacher_id),
            start_time = VALUES(start_time),
            end_time = VALUES(end_time),
            room = VALUES(room)
        `, [
          cls.class_id, day, period, subject.id, teacherId,
          periodInfo.start_time, periodInfo.end_time, cls.room_number
        ]);

        console.log(`Added lecture for day ${day}, period ${period}, subject ${subject.name}, class ${cls.grade}${cls.section}`);

        // Insert into teacher_weekly_lectures
        await db.query(`
          INSERT INTO teacher_weekly_lectures (
            teacher_id, day_of_week, period, class_id, subject_id,
            start_time, end_time, room
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            class_id = VALUES(class_id),
            subject_id = VALUES(subject_id),
            start_time = VALUES(start_time),
            end_time = VALUES(end_time),
            room = VALUES(room)
        `, [
          teacherId, day, period, cls.class_id, subject.id,
          periodInfo.start_time, periodInfo.end_time, cls.room_number
        ]);
      }
    }

    // Create teacher_lectures table if it doesn't exist
    const [teacherLecturesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (teacherLecturesTable.length === 0) {
      await db.query(`
        CREATE TABLE teacher_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          slot_index INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          section_display VARCHAR(10),
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255),
          grade VARCHAR(10),
          streamCode VARCHAR(10),
          sectionLetter VARCHAR(5),
          stream VARCHAR(50),
          location VARCHAR(100),
          status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          class_section_id INT,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('Created teacher_lectures table');
    }

    // Generate some upcoming lectures
    console.log('Generating upcoming lectures...');

    // Get next Monday's date
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);

    // Clear existing upcoming lectures
    await db.query(`
      DELETE FROM teacher_lectures
      WHERE teacher_id = ? AND date >= CURDATE()
    `, [teacherId]);

    // Generate lectures for the next 5 days
    for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
      const lectureDate = new Date(nextMonday);
      lectureDate.setDate(nextMonday.getDate() + dayOffset);

      // Format date as YYYY-MM-DD
      const formattedDate = lectureDate.toISOString().split('T')[0];

      // Get timetable for this day
      const [timetable] = await db.query(`
        SELECT twl.*, s.name as subject_name, c.grade, cr.section
        FROM teacher_weekly_lectures twl
        JOIN subjects s ON twl.subject_id = s.id
        JOIN classes c ON twl.class_id = c.id
        JOIN classrooms cr ON c.id = cr.class_id
        WHERE twl.teacher_id = ? AND twl.day_of_week = ?
        ORDER BY twl.period
      `, [teacherId, lectureDate.getDay()]);

      // Create teacher_lectures entries
      for (const lecture of timetable) {
        // Generate a topic based on subject
        const csTopics = [
          'Introduction to Programming',
          'Data Types and Variables',
          'Control Structures',
          'Functions and Methods',
          'Object-Oriented Programming',
          'Data Structures',
          'Algorithms',
          'File Handling',
          'Database Connectivity',
          'Web Development Basics',
          'Network Programming',
          'GUI Development'
        ];

        const topic = csTopics[Math.floor(Math.random() * csTopics.length)];

        await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId, formattedDate, lecture.start_time, lecture.end_time, lecture.period - 1,
          `Class ${lecture.grade}`, lecture.section, lecture.subject_name, topic,
          lecture.grade, 'NON-MEDICAL', lecture.room,
          Math.random() > 0.7 ? 'completed' : 'pending'
        ]);

        console.log(`Created lecture for ${formattedDate}, period ${lecture.period}, subject ${lecture.subject_name}`);
      }
    }

    console.log('Demo teacher data setup successfully');
    return true;
  } catch (error) {
    console.error('Error setting up demo teacher data:', error);
    return false;
  }
}

// Execute the migration
setupDemoTeacherData()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
