/**
 * Migration to set up instruction plans for the teacher
 * This script creates instruction plans for each class and topic until Feb 10, 2026
 */

const db = require('../config/database');

// Helper function to check if a date is a holiday
function isHoliday(date) {
  const day = date.getDay();
  const dayOfMonth = date.getDate();
  const month = date.getMonth() + 1; // JavaScript months are 0-indexed

  // Sunday (0)
  if (day === 0) return true;

  // Second Saturday
  if (day === 6 && Math.floor((dayOfMonth - 1) / 7) === 1) return true;

  // Indian holidays (simplified list)
  const holidays = [
    { day: 26, month: 1 }, // Republic Day
    { day: 15, month: 8 }, // Independence Day
    { day: 2, month: 10 }, // <PERSON>
    { day: 25, month: 12 }, // Christmas
    { day: 1, month: 5 }, // Labor Day
    { day: 15, month: 1 }, // Makar Sankranti
    { day: 26, month: 11 }, // Constitution Day
    { day: 14, month: 4 }, // <PERSON><PERSON><PERSON>
    { day: 5, month: 9 }, // Teachers' Day
    { day: 2, month: 4 }, // <PERSON>
    { day: 19, month: 10 }, // Dussehra
    { day: 12, month: 11 }, // Diwali
    { day: 18, month: 3 }, // Holi
    { day: 21, month: 8 }, // Raksha Bandhan
    { day: 30, month: 8 }, // Janmashtami
  ];

  return holidays.some(holiday => holiday.day === dayOfMonth && holiday.month === month);
}

// Helper function to format date as YYYY-MM-DD
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

async function setupInstructionPlans() {
  try {
    console.log('Setting up instruction plans...');

    // Get the demo teacher
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Get Computer Science subject
    const [subject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Science'
    `);

    if (subject.length === 0) {
      console.error('Computer Science subject not found');
      return false;
    }

    const subjectId = subject[0].id;
    console.log(`Found Computer Science subject, ID: ${subjectId}`);

    // Get teacher's assigned classes
    const [teacherClasses] = await db.query(`
      SELECT
        tc.id,
        tc.classroom_id,
        c.id as class_id,
        c.grade,
        cr.section,
        t.name as trade_name,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);

    if (teacherClasses.length === 0) {
      console.error('No classes assigned to teacher');
      return false;
    }

    console.log(`Found ${teacherClasses.length} classes assigned to teacher`);

    // Get syllabus for Computer Science
    const [syllabus] = await db.query(`
      SELECT
        ss.id as syllabus_id,
        ss.unit_number,
        ss.unit_title,
        ss.description as unit_description,
        ss.is_practical,
        st.id as topic_id,
        st.topic_number,
        st.topic_title,
        st.description as topic_description,
        st.hours,
        ssub.id as subtopic_id,
        ssub.subtopic_number,
        ssub.subtopic_title,
        ssub.description as subtopic_description,
        ssub.hours as subtopic_hours
      FROM subject_syllabus ss
      JOIN syllabus_topics st ON ss.id = st.syllabus_id
      JOIN syllabus_subtopics ssub ON st.id = ssub.topic_id
      WHERE ss.subject_id = ?
      ORDER BY ss.order_index, st.order_index, ssub.order_index
    `, [subjectId]);

    if (syllabus.length === 0) {
      console.error('No syllabus found for Computer Science');
      return false;
    }

    console.log(`Found ${syllabus.length} syllabus items for Computer Science`);

    // Check if instruction_plans table exists
    const [instructionPlansTable] = await db.query(`
      SHOW TABLES LIKE 'instruction_plans'
    `);

    if (instructionPlansTable.length > 0) {
      // Clear existing instruction plans for the teacher
      await db.query(`
        DELETE FROM instruction_plans
        WHERE teacher_id = ?
      `, [teacherId]);

      console.log('Cleared existing instruction plans for the teacher');
    } else {
      console.error('instruction_plans table does not exist');
      return false;
    }

    // Check if teacher_lectures table exists
    const [teacherLecturesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (teacherLecturesTable.length === 0) {
      await db.query(`
        CREATE TABLE teacher_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          slot_index INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          section_display VARCHAR(10),
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255),
          grade VARCHAR(10),
          streamCode VARCHAR(10),
          sectionLetter VARCHAR(5),
          stream VARCHAR(50),
          location VARCHAR(100),
          status ENUM('pending', 'delivered', 'cancelled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          class_section_id INT,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('Created teacher_lectures table');
    } else {
      // Clear existing teacher lectures for the teacher
      await db.query(`
        DELETE FROM teacher_lectures
        WHERE teacher_id = ?
      `, [teacherId]);

      console.log('Cleared existing teacher lectures for the teacher');
    }

    // Check if class_schedule table exists
    const [classScheduleTable] = await db.query(`
      SHOW TABLES LIKE 'class_schedule'
    `);

    if (classScheduleTable.length === 0) {
      console.error('class_schedule table does not exist');
      return false;
    }

    // Start date (today)
    const startDate = new Date();

    // End date (Feb 10, 2026)
    const endDate = new Date('2026-02-10');

    // Create instruction plans for each class
    for (const cls of teacherClasses) {
      console.log(`Creating instruction plans for class ${cls.full_class_name}`);

      // Current date pointer
      const currentDate = new Date(startDate);

      // Current syllabus index
      let syllabusIndex = 0;

      // Create instruction plans until end date or until we've covered all syllabus items
      while (currentDate <= endDate && syllabusIndex < syllabus.length) {
        // Skip holidays
        if (isHoliday(currentDate)) {
          currentDate.setDate(currentDate.getDate() + 1);
          continue;
        }

        // Get current syllabus item
        const item = syllabus[syllabusIndex];

        // Create main instruction plan
        const [instructionPlanResult] = await db.query(`
          INSERT INTO instruction_plans (
            teacher_id, class_id, subject_id, title, description, status
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          teacherId,
          cls.class_id,
          subjectId,
          `${item.topic_number} ${item.topic_title}`,
          item.topic_description,
          'published'
        ]);

        // Get the schedule ID for this class
        const [scheduleResult] = await db.query(`
          SELECT id FROM class_schedule
          WHERE class_id = ? AND subject_id = ?
          LIMIT 1
        `, [cls.class_id, subjectId]);

        let scheduleId;
        if (scheduleResult.length > 0) {
          scheduleId = scheduleResult[0].id;
        } else {
          // Create a new schedule entry
          const [newScheduleResult] = await db.query(`
            INSERT INTO class_schedule (class_id, subject_id, teacher_id, day_of_week, start_time, end_time, room, type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [cls.class_id, subjectId, teacherId, currentDate.getDay(), '09:00:00', '10:00:00', `Room ${cls.grade}${cls.section}`, 'lecture']);

          scheduleId = newScheduleResult.insertId;
        }

        // Create teacher lecture
        const [lectureResult] = await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId,
          formatDate(currentDate),
          '09:00:00',
          '10:00:00',
          0,
          `Class ${cls.grade}`,
          cls.section,
          'Computer Science',
          `${item.subtopic_number} ${item.subtopic_title}`,
          cls.grade,
          cls.trade_name,
          `Room ${cls.grade}${cls.section}`,
          Math.random() > 0.8 && currentDate < new Date() ? 'delivered' : 'pending'
        ]);

        // Create daily instruction plan
        await db.query(`
          INSERT INTO daily_instruction_plan (
            schedule_id, date, topic, objectives, material_support,
            activities, homework, notes, topic_completion_percentage
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          scheduleId,
          formatDate(currentDate),
          `${item.subtopic_number} ${item.subtopic_title}`,
          `After this lesson, students will be able to understand ${item.subtopic_title} and apply the concepts in practical scenarios.`,
          'Textbook, PowerPoint presentation, Code examples',
          `1. Introduction to ${item.subtopic_title} (15 min)\n2. Demonstration and examples (20 min)\n3. Practice exercises (15 min)`,
          `Complete the exercises on ${item.subtopic_title} from the textbook.`,
          `${item.subtopic_description}`,
          Math.random() > 0.8 && currentDate < new Date() ? 100 : 0
        ]);

        console.log(`Created instruction plan for ${cls.full_class_name} on ${formatDate(currentDate)}: ${item.subtopic_title}`);

        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);

        // Move to next syllabus item
        syllabusIndex++;

        // If we've reached the end of the syllabus, start over
        if (syllabusIndex >= syllabus.length) {
          syllabusIndex = 0;
          console.log(`Restarting syllabus for ${cls.full_class_name}`);
        }
      }
    }

    console.log('Instruction plans set up successfully');
    return true;
  } catch (error) {
    console.error('Error setting up instruction plans:', error);
    return false;
  }
}

// Execute the migration
setupInstructionPlans()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
