/**
 * Migration to set up teacher classes and CBSE Computer Science syllabus
 * This script sets up the demo teacher with specific classes and creates the CBSE Computer Science syllabus
 */

const db = require('../config/database');

async function setupTeacherClassesAndSyllabus() {
  try {
    console.log('Setting up teacher classes and CBSE Computer Science syllabus...');

    // Get the demo teacher (<EMAIL>)
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Create trades if they don't exist
    const trades = [
      { name: 'NON-MEDICAL' },
      { name: 'MEDICAL' },
      { name: 'COMMERCE' },
      { name: 'ARTS STREAM' }
    ];

    const tradeIds = {};

    for (const trade of trades) {
      const [existingTrade] = await db.query(`
        SELECT id FROM trades WHERE name = ?
      `, [trade.name]);

      if (existingTrade.length > 0) {
        tradeIds[trade.name] = existingTrade[0].id;
        console.log(`Found existing trade: ${trade.name}, ID: ${existingTrade[0].id}`);
      } else {
        const [result] = await db.query(`
          INSERT INTO trades (name) VALUES (?)
        `, [trade.name]);

        tradeIds[trade.name] = result.insertId;
        console.log(`Created trade: ${trade.name}, ID: ${result.insertId}`);
      }
    }

    // Create classes if they don't exist
    const [existingClass12] = await db.query(`
      SELECT id FROM classes WHERE grade = '12'
    `);

    let class12Id;
    if (existingClass12.length > 0) {
      class12Id = existingClass12[0].id;
      console.log(`Found existing class: 12, ID: ${class12Id}`);
    } else {
      const [result] = await db.query(`
        INSERT INTO classes (name, grade) VALUES ('Class 12', '12')
      `);

      class12Id = result.insertId;
      console.log(`Created class: 12, ID: ${class12Id}`);
    }

    // Create classrooms if they don't exist
    const classrooms = [
      { class_id: class12Id, section: 'B', room_number: '12B', trade_id: tradeIds['NON-MEDICAL'] },
      { class_id: class12Id, section: 'E', room_number: '12E', trade_id: tradeIds['NON-MEDICAL'] },
      { class_id: class12Id, section: 'A', room_number: '12A-COM', trade_id: tradeIds['COMMERCE'] },
      { class_id: class12Id, section: 'A', room_number: '12A-MED', trade_id: tradeIds['MEDICAL'] }
    ];

    const classroomIds = [];

    for (const classroom of classrooms) {
      // Check if classroom exists with this class_id, section, and trade_id
      const [existingClassroom] = await db.query(`
        SELECT id FROM classrooms
        WHERE class_id = ? AND section = ? AND trade_id = ?
      `, [classroom.class_id, classroom.section, classroom.trade_id]);

      if (existingClassroom.length > 0) {
        classroomIds.push({
          id: existingClassroom[0].id,
          class_id: classroom.class_id,
          section: classroom.section,
          room_number: classroom.room_number,
          trade_id: classroom.trade_id,
          trade_name: Object.keys(tradeIds).find(key => tradeIds[key] === classroom.trade_id)
        });

        console.log(`Found existing classroom: 12 ${classroom.section} ${Object.keys(tradeIds).find(key => tradeIds[key] === classroom.trade_id)}, ID: ${existingClassroom[0].id}`);
      } else {
        try {
          const [result] = await db.query(`
            INSERT INTO classrooms (class_id, section, room_number, trade_id)
            VALUES (?, ?, ?, ?)
          `, [classroom.class_id, classroom.section, classroom.room_number, classroom.trade_id]);

          classroomIds.push({
            id: result.insertId,
            class_id: classroom.class_id,
            section: classroom.section,
            room_number: classroom.room_number,
            trade_id: classroom.trade_id,
            trade_name: Object.keys(tradeIds).find(key => tradeIds[key] === classroom.trade_id)
          });

          console.log(`Created classroom: 12 ${classroom.section} ${Object.keys(tradeIds).find(key => tradeIds[key] === classroom.trade_id)}, ID: ${result.insertId}`);
        } catch (error) {
          console.error(`Error creating classroom: ${error.message}`);

          // Try to find the classroom by room_number as a fallback
          const [fallbackClassroom] = await db.query(`
            SELECT id FROM classrooms
            WHERE room_number = ?
          `, [classroom.room_number]);

          if (fallbackClassroom.length > 0) {
            classroomIds.push({
              id: fallbackClassroom[0].id,
              class_id: classroom.class_id,
              section: classroom.section,
              room_number: classroom.room_number,
              trade_id: classroom.trade_id,
              trade_name: Object.keys(tradeIds).find(key => tradeIds[key] === classroom.trade_id)
            });

            console.log(`Found fallback classroom by room number: ${classroom.room_number}, ID: ${fallbackClassroom[0].id}`);
          }
        }
      }
    }

    // Create Computer Science subject if it doesn't exist
    const [existingSubject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Science'
    `);

    let csSubjectId;
    if (existingSubject.length > 0) {
      csSubjectId = existingSubject[0].id;
      console.log(`Found existing subject: Computer Science, ID: ${csSubjectId}`);
    } else {
      const [result] = await db.query(`
        INSERT INTO subjects (name, code, description, subject_group)
        VALUES ('Computer Science', 'CS', 'Computer Science for senior secondary students', 12)
      `);

      csSubjectId = result.insertId;
      console.log(`Created subject: Computer Science, ID: ${csSubjectId}`);
    }

    // Check if teacher_subjects table exists
    const [teacherSubjectsTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_subjects'
    `);

    if (teacherSubjectsTable.length === 0) {
      // Create teacher_subjects table
      await db.query(`
        CREATE TABLE teacher_subjects (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          subject_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_subject (teacher_id, subject_id)
        )
      `);

      console.log('Created teacher_subjects table');
    }

    // Assign Computer Science subject to teacher
    await db.query(`
      INSERT IGNORE INTO teacher_subjects (teacher_id, subject_id)
      VALUES (?, ?)
    `, [teacherId, csSubjectId]);

    console.log(`Assigned subject Computer Science to teacher`);

    // Check if teacher_classes table exists
    const [teacherClassesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_classes'
    `);

    if (teacherClassesTable.length === 0) {
      // Create teacher_classes table
      await db.query(`
        CREATE TABLE teacher_classes (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          classroom_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_classroom (teacher_id, classroom_id)
        )
      `);

      console.log('Created teacher_classes table');
    } else {
      // Clear existing teacher classes
      await db.query(`
        DELETE FROM teacher_classes
        WHERE teacher_id = ?
      `, [teacherId]);

      console.log('Cleared existing teacher classes');
    }

    // Assign classes to teacher
    for (const classroom of classroomIds) {
      await db.query(`
        INSERT INTO teacher_classes (teacher_id, classroom_id)
        VALUES (?, ?)
      `, [teacherId, classroom.id]);

      console.log(`Assigned class 12 ${classroom.section} ${classroom.trade_name} to teacher`);
    }

    // Create syllabus table if it doesn't exist
    const [syllabusTable] = await db.query(`
      SHOW TABLES LIKE 'subject_syllabus'
    `);

    if (syllabusTable.length === 0) {
      await db.query(`
        CREATE TABLE subject_syllabus (
          id INT PRIMARY KEY AUTO_INCREMENT,
          subject_id INT NOT NULL,
          unit_number INT NOT NULL,
          unit_title VARCHAR(255) NOT NULL,
          description TEXT,
          is_practical BOOLEAN DEFAULT FALSE,
          order_index INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_subject_unit (subject_id, unit_number)
        )
      `);

      console.log('Created subject_syllabus table');
    }

    // Create syllabus topics table if it doesn't exist
    const [syllabusTopicsTable] = await db.query(`
      SHOW TABLES LIKE 'syllabus_topics'
    `);

    if (syllabusTopicsTable.length === 0) {
      await db.query(`
        CREATE TABLE syllabus_topics (
          id INT PRIMARY KEY AUTO_INCREMENT,
          syllabus_id INT NOT NULL,
          topic_number VARCHAR(10) NOT NULL,
          topic_title VARCHAR(255) NOT NULL,
          description TEXT,
          hours INT DEFAULT 0,
          order_index INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (syllabus_id) REFERENCES subject_syllabus(id) ON DELETE CASCADE
        )
      `);

      console.log('Created syllabus_topics table');
    }

    // Create syllabus subtopics table if it doesn't exist
    const [syllabusSubtopicsTable] = await db.query(`
      SHOW TABLES LIKE 'syllabus_subtopics'
    `);

    if (syllabusSubtopicsTable.length === 0) {
      await db.query(`
        CREATE TABLE syllabus_subtopics (
          id INT PRIMARY KEY AUTO_INCREMENT,
          topic_id INT NOT NULL,
          subtopic_number VARCHAR(10) NOT NULL,
          subtopic_title VARCHAR(255) NOT NULL,
          description TEXT,
          hours INT DEFAULT 0,
          order_index INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (topic_id) REFERENCES syllabus_topics(id) ON DELETE CASCADE
        )
      `);

      console.log('Created syllabus_subtopics table');
    }

    // Clear existing syllabus for Computer Science
    await db.query(`
      DELETE ss FROM syllabus_subtopics ss
      JOIN syllabus_topics st ON ss.topic_id = st.id
      JOIN subject_syllabus su ON st.syllabus_id = su.id
      WHERE su.subject_id = ?
    `, [csSubjectId]);

    await db.query(`
      DELETE st FROM syllabus_topics st
      JOIN subject_syllabus su ON st.syllabus_id = su.id
      WHERE su.subject_id = ?
    `, [csSubjectId]);

    await db.query(`
      DELETE FROM subject_syllabus
      WHERE subject_id = ?
    `, [csSubjectId]);

    console.log('Cleared existing syllabus for Computer Science');

    // Create CBSE Computer Science syllabus
    const syllabus = [
      {
        unit_number: 1,
        unit_title: 'Programming and Computational Thinking',
        description: 'Review of Python, Functions, Recursion, Data Structures',
        is_practical: false,
        order_index: 1,
        topics: [
          {
            topic_number: '1.1',
            topic_title: 'Review of Python',
            description: 'Review of Python basics covered in Class XI',
            hours: 4,
            order_index: 1,
            subtopics: [
              { subtopic_number: '1.1.1', subtopic_title: 'Basic Python Syntax', description: 'Variables, operators, expressions', hours: 1, order_index: 1 },
              { subtopic_number: '1.1.2', subtopic_title: 'Control Structures', description: 'Conditional statements, loops', hours: 1, order_index: 2 },
              { subtopic_number: '1.1.3', subtopic_title: 'Strings and Lists', description: 'String operations, list operations', hours: 1, order_index: 3 },
              { subtopic_number: '1.1.4', subtopic_title: 'Dictionaries and Tuples', description: 'Dictionary operations, tuple operations', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '1.2',
            topic_title: 'Functions',
            description: 'Functions in Python',
            hours: 5,
            order_index: 2,
            subtopics: [
              { subtopic_number: '1.2.1', subtopic_title: 'Function Definition', description: 'Defining and calling functions', hours: 1, order_index: 1 },
              { subtopic_number: '1.2.2', subtopic_title: 'Parameters and Arguments', description: 'Positional, keyword, default parameters', hours: 1, order_index: 2 },
              { subtopic_number: '1.2.3', subtopic_title: 'Return Values', description: 'Returning values from functions', hours: 1, order_index: 3 },
              { subtopic_number: '1.2.4', subtopic_title: 'Scope of Variables', description: 'Local and global variables', hours: 1, order_index: 4 },
              { subtopic_number: '1.2.5', subtopic_title: 'Lambda Functions', description: 'Anonymous functions', hours: 1, order_index: 5 }
            ]
          },
          {
            topic_number: '1.3',
            topic_title: 'Recursion',
            description: 'Recursive functions and algorithms',
            hours: 4,
            order_index: 3,
            subtopics: [
              { subtopic_number: '1.3.1', subtopic_title: 'Introduction to Recursion', description: 'Concept of recursion, base case, recursive case', hours: 1, order_index: 1 },
              { subtopic_number: '1.3.2', subtopic_title: 'Recursive Functions', description: 'Writing recursive functions', hours: 1, order_index: 2 },
              { subtopic_number: '1.3.3', subtopic_title: 'Recursive vs Iterative Solutions', description: 'Comparing recursive and iterative approaches', hours: 1, order_index: 3 },
              { subtopic_number: '1.3.4', subtopic_title: 'Applications of Recursion', description: 'Factorial, Fibonacci, Tower of Hanoi', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '1.4',
            topic_title: 'Data Structures',
            description: 'Lists, stacks, queues',
            hours: 7,
            order_index: 4,
            subtopics: [
              { subtopic_number: '1.4.1', subtopic_title: 'Lists as Data Structure', description: 'List operations and methods', hours: 1, order_index: 1 },
              { subtopic_number: '1.4.2', subtopic_title: 'Stacks', description: 'Stack operations: push, pop, peek', hours: 2, order_index: 2 },
              { subtopic_number: '1.4.3', subtopic_title: 'Queues', description: 'Queue operations: enqueue, dequeue', hours: 2, order_index: 3 },
              { subtopic_number: '1.4.4', subtopic_title: 'Implementation', description: 'Implementing stacks and queues using lists', hours: 2, order_index: 4 }
            ]
          }
        ]
      },
      {
        unit_number: 2,
        unit_title: 'Object-Oriented Programming',
        description: 'Classes, objects, inheritance, polymorphism',
        is_practical: false,
        order_index: 2,
        topics: [
          {
            topic_number: '2.1',
            topic_title: 'Object-Oriented Concepts',
            description: 'Basic concepts of OOP',
            hours: 4,
            order_index: 1,
            subtopics: [
              { subtopic_number: '2.1.1', subtopic_title: 'Introduction to OOP', description: 'Principles of OOP', hours: 1, order_index: 1 },
              { subtopic_number: '2.1.2', subtopic_title: 'Classes and Objects', description: 'Defining classes, creating objects', hours: 1, order_index: 2 },
              { subtopic_number: '2.1.3', subtopic_title: 'Attributes and Methods', description: 'Class attributes, instance attributes, methods', hours: 1, order_index: 3 },
              { subtopic_number: '2.1.4', subtopic_title: 'Constructors', description: 'The __init__ method', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '2.2',
            topic_title: 'Inheritance',
            description: 'Inheritance in Python',
            hours: 4,
            order_index: 2,
            subtopics: [
              { subtopic_number: '2.2.1', subtopic_title: 'Single Inheritance', description: 'Creating derived classes', hours: 1, order_index: 1 },
              { subtopic_number: '2.2.2', subtopic_title: 'Multiple Inheritance', description: 'Inheriting from multiple base classes', hours: 1, order_index: 2 },
              { subtopic_number: '2.2.3', subtopic_title: 'Method Overriding', description: 'Overriding methods in derived classes', hours: 1, order_index: 3 },
              { subtopic_number: '2.2.4', subtopic_title: 'super() Function', description: 'Using super() to call parent class methods', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '2.3',
            topic_title: 'Polymorphism',
            description: 'Polymorphism in Python',
            hours: 2,
            order_index: 3,
            subtopics: [
              { subtopic_number: '2.3.1', subtopic_title: 'Method Polymorphism', description: 'Same method name, different implementations', hours: 1, order_index: 1 },
              { subtopic_number: '2.3.2', subtopic_title: 'Operator Polymorphism', description: 'Operator overloading', hours: 1, order_index: 2 }
            ]
          }
        ]
      },
      {
        unit_number: 3,
        unit_title: 'Database Management',
        description: 'SQL, database concepts, connectivity',
        is_practical: false,
        order_index: 3,
        topics: [
          {
            topic_number: '3.1',
            topic_title: 'Database Concepts',
            description: 'Introduction to databases',
            hours: 4,
            order_index: 1,
            subtopics: [
              { subtopic_number: '3.1.1', subtopic_title: 'Introduction to DBMS', description: 'Database management systems', hours: 1, order_index: 1 },
              { subtopic_number: '3.1.2', subtopic_title: 'Relational Model', description: 'Tables, records, fields', hours: 1, order_index: 2 },
              { subtopic_number: '3.1.3', subtopic_title: 'Keys', description: 'Primary key, foreign key, candidate key', hours: 1, order_index: 3 },
              { subtopic_number: '3.1.4', subtopic_title: 'Relationships', description: 'One-to-one, one-to-many, many-to-many', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '3.2',
            topic_title: 'SQL',
            description: 'Structured Query Language',
            hours: 8,
            order_index: 2,
            subtopics: [
              { subtopic_number: '3.2.1', subtopic_title: 'Data Definition Language', description: 'CREATE, ALTER, DROP', hours: 2, order_index: 1 },
              { subtopic_number: '3.2.2', subtopic_title: 'Data Manipulation Language', description: 'INSERT, UPDATE, DELETE', hours: 2, order_index: 2 },
              { subtopic_number: '3.2.3', subtopic_title: 'Data Query Language', description: 'SELECT, WHERE, ORDER BY, GROUP BY', hours: 2, order_index: 3 },
              { subtopic_number: '3.2.4', subtopic_title: 'Joins', description: 'INNER JOIN, LEFT JOIN, RIGHT JOIN', hours: 2, order_index: 4 }
            ]
          },
          {
            topic_number: '3.3',
            topic_title: 'Database Connectivity',
            description: 'Connecting Python with MySQL',
            hours: 3,
            order_index: 3,
            subtopics: [
              { subtopic_number: '3.3.1', subtopic_title: 'MySQL Connector', description: 'Installing and configuring MySQL connector', hours: 1, order_index: 1 },
              { subtopic_number: '3.3.2', subtopic_title: 'Connection and Cursor', description: 'Creating connection and cursor objects', hours: 1, order_index: 2 },
              { subtopic_number: '3.3.3', subtopic_title: 'Executing Queries', description: 'Executing SQL queries from Python', hours: 1, order_index: 3 }
            ]
          }
        ]
      },
      {
        unit_number: 4,
        unit_title: 'Computer Networks',
        description: 'Network concepts, protocols, security',
        is_practical: false,
        order_index: 4,
        topics: [
          {
            topic_number: '4.1',
            topic_title: 'Network Fundamentals',
            description: 'Basic networking concepts',
            hours: 4,
            order_index: 1,
            subtopics: [
              { subtopic_number: '4.1.1', subtopic_title: 'Network Types', description: 'LAN, MAN, WAN', hours: 1, order_index: 1 },
              { subtopic_number: '4.1.2', subtopic_title: 'Network Topologies', description: 'Bus, star, ring, mesh', hours: 1, order_index: 2 },
              { subtopic_number: '4.1.3', subtopic_title: 'Network Devices', description: 'Router, switch, hub, modem', hours: 1, order_index: 3 },
              { subtopic_number: '4.1.4', subtopic_title: 'Transmission Media', description: 'Wired and wireless media', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '4.2',
            topic_title: 'Network Protocols',
            description: 'Communication protocols',
            hours: 4,
            order_index: 2,
            subtopics: [
              { subtopic_number: '4.2.1', subtopic_title: 'TCP/IP Model', description: 'Layers of TCP/IP model', hours: 1, order_index: 1 },
              { subtopic_number: '4.2.2', subtopic_title: 'IP Addressing', description: 'IPv4, IPv6, subnetting', hours: 1, order_index: 2 },
              { subtopic_number: '4.2.3', subtopic_title: 'Domain Name System', description: 'DNS resolution', hours: 1, order_index: 3 },
              { subtopic_number: '4.2.4', subtopic_title: 'Application Layer Protocols', description: 'HTTP, FTP, SMTP', hours: 1, order_index: 4 }
            ]
          },
          {
            topic_number: '4.3',
            topic_title: 'Network Security',
            description: 'Security concepts and measures',
            hours: 2,
            order_index: 3,
            subtopics: [
              { subtopic_number: '4.3.1', subtopic_title: 'Threats and Attacks', description: 'Types of network attacks', hours: 1, order_index: 1 },
              { subtopic_number: '4.3.2', subtopic_title: 'Security Measures', description: 'Firewall, encryption, authentication', hours: 1, order_index: 2 }
            ]
          }
        ]
      },
      {
        unit_number: 5,
        unit_title: 'Practical Work',
        description: 'Programming in Python, SQL, and project work',
        is_practical: true,
        order_index: 5,
        topics: [
          {
            topic_number: '5.1',
            topic_title: 'Python Programming',
            description: 'Practical programming in Python',
            hours: 10,
            order_index: 1,
            subtopics: [
              { subtopic_number: '5.1.1', subtopic_title: 'Functions and Recursion', description: 'Implementing functions and recursive algorithms', hours: 2, order_index: 1 },
              { subtopic_number: '5.1.2', subtopic_title: 'Data Structures', description: 'Implementing stacks and queues', hours: 3, order_index: 2 },
              { subtopic_number: '5.1.3', subtopic_title: 'Object-Oriented Programming', description: 'Creating classes and objects', hours: 3, order_index: 3 },
              { subtopic_number: '5.1.4', subtopic_title: 'File Handling', description: 'Reading from and writing to files', hours: 2, order_index: 4 }
            ]
          },
          {
            topic_number: '5.2',
            topic_title: 'SQL Programming',
            description: 'Practical SQL queries',
            hours: 8,
            order_index: 2,
            subtopics: [
              { subtopic_number: '5.2.1', subtopic_title: 'Database Creation', description: 'Creating databases and tables', hours: 2, order_index: 1 },
              { subtopic_number: '5.2.2', subtopic_title: 'Data Manipulation', description: 'Inserting, updating, and deleting data', hours: 2, order_index: 2 },
              { subtopic_number: '5.2.3', subtopic_title: 'Queries', description: 'Writing SELECT queries with various clauses', hours: 2, order_index: 3 },
              { subtopic_number: '5.2.4', subtopic_title: 'Python-MySQL Connectivity', description: 'Connecting Python with MySQL database', hours: 2, order_index: 4 }
            ]
          },
          {
            topic_number: '5.3',
            topic_title: 'Project Work',
            description: 'Developing a complete project',
            hours: 12,
            order_index: 3,
            subtopics: [
              { subtopic_number: '5.3.1', subtopic_title: 'Project Planning', description: 'Planning and designing the project', hours: 2, order_index: 1 },
              { subtopic_number: '5.3.2', subtopic_title: 'Database Design', description: 'Designing database for the project', hours: 2, order_index: 2 },
              { subtopic_number: '5.3.3', subtopic_title: 'Implementation', description: 'Implementing the project', hours: 6, order_index: 3 },
              { subtopic_number: '5.3.4', subtopic_title: 'Testing and Documentation', description: 'Testing and documenting the project', hours: 2, order_index: 4 }
            ]
          }
        ]
      }
    ];

    // Insert syllabus data
    for (const unit of syllabus) {
      const [unitResult] = await db.query(`
        INSERT INTO subject_syllabus (subject_id, unit_number, unit_title, description, is_practical, order_index)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [csSubjectId, unit.unit_number, unit.unit_title, unit.description, unit.is_practical, unit.order_index]);

      const syllabusId = unitResult.insertId;
      console.log(`Created syllabus unit: ${unit.unit_title}, ID: ${syllabusId}`);

      for (const topic of unit.topics) {
        const [topicResult] = await db.query(`
          INSERT INTO syllabus_topics (syllabus_id, topic_number, topic_title, description, hours, order_index)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [syllabusId, topic.topic_number, topic.topic_title, topic.description, topic.hours, topic.order_index]);

        const topicId = topicResult.insertId;
        console.log(`Created syllabus topic: ${topic.topic_title}, ID: ${topicId}`);

        for (const subtopic of topic.subtopics) {
          await db.query(`
            INSERT INTO syllabus_subtopics (topic_id, subtopic_number, subtopic_title, description, hours, order_index)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [topicId, subtopic.subtopic_number, subtopic.subtopic_title, subtopic.description, subtopic.hours, subtopic.order_index]);

          console.log(`Created syllabus subtopic: ${subtopic.subtopic_title}`);
        }
      }
    }

    console.log('Teacher classes and CBSE Computer Science syllabus set up successfully');
    return true;
  } catch (error) {
    console.error('Error setting up teacher classes and syllabus:', error);
    return false;
  }
}

// Execute the migration
setupTeacherClassesAndSyllabus()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
