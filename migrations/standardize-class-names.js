/**
 * Migration script to standardize class names and ensure required classes exist
 */

const db = require('../config/database');

async function standardizeClassNames() {
  try {
    console.log('Starting migration: Standardizing class names');

    // Check if classes table exists
    const [classesExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'classes'
    `);

    if (classesExists[0].table_exists === 0) {
      console.log('classes table does not exist, creating it...');

      // Create classes table
      await db.query(`
        CREATE TABLE IF NOT EXISTS classes (
          id INT PRIMARY KEY AUTO_INCREMENT,
          name VARCHAR(100),
          grade VARCHAR(10),
          trade VARCHAR(50),
          section VARCHAR(10),
          academic_year VARCHAR(20),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log('classes table created successfully');
    } else {
      // Check if name column is large enough
      const [nameColumnInfo] = await db.query(`
        SELECT CHARACTER_MAXIMUM_LENGTH
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'classes'
        AND column_name = 'name'
      `);

      if (nameColumnInfo.length > 0 && nameColumnInfo[0].CHARACTER_MAXIMUM_LENGTH < 100) {
        console.log(`Increasing size of name column from ${nameColumnInfo[0].CHARACTER_MAXIMUM_LENGTH} to 100 characters`);

        // Increase the size of the name column
        await db.query(`
          ALTER TABLE classes
          MODIFY COLUMN name VARCHAR(100)
        `);

        console.log('Name column size increased successfully');
      }
    }

    // Get existing classes
    const [existingClasses] = await db.query(`
      SELECT id, name, grade, trade, section, academic_year FROM classes
    `);

    console.log(`Found ${existingClasses.length} existing classes`);

    // Define the required classes
    const requiredClasses = [
      { name: '12 NON-MEDICAL A', grade: '12', trade: 'NON-MEDICAL', section: 'A', academic_year: '2023-2024' },
      { name: '12 NON-MEDICAL B', grade: '12', trade: 'NON-MEDICAL', section: 'B', academic_year: '2023-2024' },
      { name: '12 NON-MEDICAL E', grade: '12', trade: 'NON-MEDICAL', section: 'E', academic_year: '2023-2024' },
      { name: '12 MEDICAL A', grade: '12', trade: 'MEDICAL', section: 'A', academic_year: '2023-2024' },
      { name: '12 COMMERCE A', grade: '12', trade: 'COMMERCE', section: 'A', academic_year: '2023-2024' },
      { name: '12 COMMERCE B', grade: '12', trade: 'COMMERCE', section: 'B', academic_year: '2023-2024' }
    ];

    // Update existing classes to standardize names
    for (const cls of existingClasses) {
      // Skip classes that already have standardized names
      if (requiredClasses.some(rc => rc.name === cls.name)) {
        continue;
      }

      // Construct standardized name
      let standardizedName = '';

      if (cls.grade && cls.trade && cls.section) {
        // Convert abbreviated trade names to full names
        let fullTrade = cls.trade;
        if (cls.trade === 'NM' || cls.trade === 'Non-Medical' || cls.trade === 'non-medical') {
          fullTrade = 'NON-MEDICAL';
        } else if (cls.trade === 'M' || cls.trade === 'Medical' || cls.trade === 'medical') {
          fullTrade = 'MEDICAL';
        } else if (cls.trade === 'C' || cls.trade === 'Commerce' || cls.trade === 'commerce') {
          fullTrade = 'COMMERCE';
        }

        standardizedName = `${cls.grade} ${fullTrade} ${cls.section}`;
      } else if (cls.name) {
        // Parse existing name to extract grade, trade, and section
        const nameParts = cls.name.split(/[-\s]+/);

        if (nameParts.length >= 3) {
          let grade = nameParts[0];
          let trade = nameParts[1].toUpperCase();
          let section = nameParts[2].toUpperCase();

          // Convert abbreviated trade names to full names
          if (trade === 'NM') {
            trade = 'NON-MEDICAL';
          } else if (trade === 'M') {
            trade = 'MEDICAL';
          } else if (trade === 'C') {
            trade = 'COMMERCE';
          }

          standardizedName = `${grade} ${trade} ${section}`;

          // Update grade, trade, and section fields
          await db.query(`
            UPDATE classes
            SET grade = ?, trade = ?, section = ?
            WHERE id = ?
          `, [grade, trade, section, cls.id]);
        } else {
          console.log(`Skipping class with unparseable name: ${cls.name}`);
          continue;
        }
      } else {
        console.log(`Skipping class with no name or grade/trade/section: ${JSON.stringify(cls)}`);
        continue;
      }

      // Update the class name
      await db.query(`
        UPDATE classes
        SET name = ?
        WHERE id = ?
      `, [standardizedName, cls.id]);

      console.log(`Updated class name: ${cls.name || `${cls.grade} ${cls.trade} ${cls.section}`} -> ${standardizedName}`);
    }

    // Get updated classes after renaming
    const [updatedClasses] = await db.query(`
      SELECT id, name, grade, trade, section, academic_year FROM classes
    `);

    console.log(`Found ${updatedClasses.length} classes after renaming`);

    // Add required classes that don't exist
    for (const requiredClass of requiredClasses) {
      // Check if class already exists (using both name and grade/trade/section)
      const classExists = updatedClasses.some(ec =>
        (ec.name && ec.name.toUpperCase() === requiredClass.name.toUpperCase()) ||
        (ec.grade === requiredClass.grade &&
         ec.trade === requiredClass.trade &&
         ec.section === requiredClass.section)
      );

      if (!classExists) {
        // Add the required class
        await db.query(`
          INSERT INTO classes (name, grade, trade, section, academic_year)
          VALUES (?, ?, ?, ?, ?)
        `, [
          requiredClass.name,
          requiredClass.grade,
          requiredClass.trade,
          requiredClass.section,
          requiredClass.academic_year
        ]);

        console.log(`Added required class: ${requiredClass.name}`);
      } else {
        console.log(`Required class already exists: ${requiredClass.name}`);
      }
    }

    // Check if classrooms table exists
    const [classroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'classrooms'
    `);

    if (classroomsExists[0].table_exists === 0) {
      console.log('classrooms table does not exist, creating it...');

      // Create classrooms table
      await db.query(`
        CREATE TABLE IF NOT EXISTS classrooms (
          id INT PRIMARY KEY AUTO_INCREMENT,
          class_id INT NOT NULL,
          room_number VARCHAR(20),
          capacity INT DEFAULT 40,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
        )
      `);

      console.log('classrooms table created successfully');
    } else {
      // Check the structure of the classrooms table
      const [classroomsColumns] = await db.query(`
        SHOW COLUMNS FROM classrooms
      `);

      console.log('Classrooms table structure:');
      classroomsColumns.forEach(col => {
        console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
      });

      // Check if section column exists
      const sectionColumn = classroomsColumns.find(col => col.Field === 'section');

      if (sectionColumn) {
        // If section column exists but doesn't have a default value, add one
        if (sectionColumn.Null === 'NO' && !sectionColumn.Default) {
          console.log('Adding default value to section column');

          await db.query(`
            ALTER TABLE classrooms
            MODIFY COLUMN section VARCHAR(10) NOT NULL DEFAULT 'A'
          `);

          console.log('Default value added to section column');
        }
      }
    }

    // Get all classes
    const [allClasses] = await db.query(`
      SELECT id, name FROM classes
    `);

    // Get existing classrooms
    const [existingClassrooms] = await db.query(`
      SELECT id, class_id, room_number FROM classrooms
    `);

    console.log(`Found ${existingClassrooms.length} existing classrooms`);

    // Create classrooms for classes that don't have one
    for (const cls of allClasses) {
      const hasClassroom = existingClassrooms.some(ec => ec.class_id === cls.id);

      if (!hasClassroom) {
        // Generate a room number
        const roomNumber = `Room ${200 + cls.id}`;

        // Create a classroom for this class
        await db.query(`
          INSERT INTO classrooms (class_id, room_number)
          VALUES (?, ?)
        `, [cls.id, roomNumber]);

        console.log(`Created classroom ${roomNumber} for class ${cls.name}`);
      }
    }

    // Check if student_classrooms table exists
    const [studentClassroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'student_classrooms'
    `);

    if (studentClassroomsExists[0].table_exists === 0) {
      console.log('student_classrooms table does not exist, creating it...');

      // Create student_classrooms table
      await db.query(`
        CREATE TABLE IF NOT EXISTS student_classrooms (
          id INT PRIMARY KEY AUTO_INCREMENT,
          student_id INT NOT NULL,
          classroom_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
          UNIQUE KEY unique_student_classroom (student_id, classroom_id)
        )
      `);

      console.log('student_classrooms table created successfully');
    }

    // Get students
    const [students] = await db.query(`
      SELECT id, name, email FROM users WHERE role = 'student'
    `);

    console.log(`Found ${students.length} students`);

    // Get classrooms for the required classes
    const [requiredClassrooms] = await db.query(`
      SELECT cr.id, cr.room_number, c.name AS class_name
      FROM classrooms cr
      JOIN classes c ON cr.class_id = c.id
      WHERE c.name IN (?, ?, ?, ?, ?, ?)
    `, requiredClasses.map(rc => rc.name));

    console.log(`Found ${requiredClassrooms.length} classrooms for required classes`);

    // Check if the demo student exists
    const [demoStudent] = await db.query(`
      SELECT id FROM users WHERE email = '<EMAIL>'
    `);

    if (demoStudent.length > 0) {
      console.log('Found demo student: <EMAIL>');

      // Find the 12 NON-MEDICAL A classroom
      const nonMedicalAClassroom = requiredClassrooms.find(rc => rc.class_name === '12 NON-MEDICAL A');

      if (nonMedicalAClassroom) {
        // Assign the demo student to 12 NON-MEDICAL A
        await db.query(`
          INSERT INTO student_classrooms (student_id, classroom_id)
          VALUES (?, ?)
          ON DUPLICATE KEY UPDATE classroom_id = VALUES(classroom_id)
        `, [demoStudent[0].id, nonMedicalAClassroom.id]);

        console.log(`Assigned demo student to ${nonMedicalAClassroom.class_name} (${nonMedicalAClassroom.room_number})`);
      } else {
        console.log('Could not find 12 NON-MEDICAL A classroom');
      }
    } else {
      console.log('Demo student not found: <EMAIL>');
    }

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
standardizeClassNames();
