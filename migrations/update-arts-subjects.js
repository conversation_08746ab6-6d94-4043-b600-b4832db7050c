/**
 * Migration to update Arts stream subjects and combinations
 */

const db = require('../config/database');

async function updateArtsSubjects() {
  try {
    console.log('Updating Arts stream subjects and combinations...');
    
    // Define all Arts subjects
    const artsSubjects = [
      // Language subjects (Group 1)
      { name: 'PUNJABI', code: 'PUN', description: 'Punjabi language for senior secondary', group: 1, is_elective: true },
      { name: 'HINDI', code: 'HIN', description: 'Hindi language for senior secondary', group: 1, is_elective: true },
      { name: 'ENGLISH', code: 'ENG', description: 'English language for senior secondary', group: 1, is_elective: true },
      { name: 'URDU', code: 'URD', description: 'Urdu language for senior secondary', group: 1, is_elective: true },
      
      // Classical/Foreign Language subjects (Group 2)
      { name: 'SANSKRIT', code: 'SAN', description: 'Sanskrit language for senior secondary', group: 2, is_elective: true },
      { name: '<PERSON>RE<PERSON><PERSON>', code: 'FRE', description: 'French language for senior secondary', group: 2, is_elective: true },
      { name: 'GERMAN', code: 'GER', description: 'German language for senior secondary', group: 2, is_elective: true },
      
      // Core subjects (Groups 3-11)
      { name: 'HISTORY', code: 'HIS', description: 'History for senior secondary', group: 3, is_elective: false },
      { name: 'ECONOMICS', code: 'ECO', description: 'Economics for senior secondary', group: 4, is_elective: true },
      { name: 'BUSINESS STUDIES', code: 'BUS', description: 'Business Studies for senior secondary', group: 4, is_elective: true },
      { name: 'ACCOUNTANCY', code: 'ACC', description: 'Accountancy for senior secondary', group: 4, is_elective: true },
      { name: 'MATHEMATICS', code: 'MAT', description: 'Mathematics for senior secondary', group: 5, is_elective: true },
      { name: 'POLITICAL SCIENCE', code: 'POL', description: 'Political Science for senior secondary', group: 6, is_elective: false },
      { name: 'SOCIOLOGY', code: 'SOC', description: 'Sociology for senior secondary', group: 7, is_elective: true },
      { name: 'PUBLIC ADMINISTRATION', code: 'PUB', description: 'Public Administration for senior secondary', group: 8, is_elective: true },
      { name: 'PHILOSOPHY', code: 'PHI', description: 'Philosophy for senior secondary', group: 9, is_elective: true },
      { name: 'RELIGION', code: 'REL', description: 'Religion for senior secondary', group: 10, is_elective: true },
      { name: 'GEOGRAPHY', code: 'GEO', description: 'Geography for senior secondary', group: 11, is_elective: true },
      
      // Additional subjects
      { name: 'COMPUTER SCIENCE', code: 'CS', description: 'Computer Science for senior secondary', group: 12, is_elective: true },
      { name: 'WELCOME LIFE', code: 'WL', description: 'Welcome Life for senior secondary', group: 13, is_elective: true }
    ];
    
    // Get the ARTS trade ID
    const [artsTrade] = await db.query('SELECT id FROM trades WHERE name = ?', ['ARTS']);
    
    if (artsTrade.length === 0) {
      console.error('ARTS trade not found');
      return false;
    }
    
    const artsTradeId = artsTrade[0].id;
    
    // Add subject group column to subjects table if it doesn't exist
    const [columns] = await db.query('SHOW COLUMNS FROM subjects LIKE "subject_group"');
    if (columns.length === 0) {
      console.log('Adding subject_group column to subjects table...');
      await db.query(`
        ALTER TABLE subjects
        ADD COLUMN subject_group INT DEFAULT NULL
      `);
      console.log('Added subject_group column to subjects table');
    }
    
    // Add or update each subject
    for (const subject of artsSubjects) {
      // Check if subject exists
      const [existingSubject] = await db.query('SELECT id FROM subjects WHERE name = ?', [subject.name]);
      
      let subjectId;
      if (existingSubject.length === 0) {
        // Create new subject
        const [result] = await db.query(`
          INSERT INTO subjects (name, code, description, subject_group)
          VALUES (?, ?, ?, ?)
        `, [subject.name, subject.code, subject.description, subject.group]);
        
        subjectId = result.insertId;
        console.log(`Created subject: ${subject.name}`);
      } else {
        // Update existing subject
        subjectId = existingSubject[0].id;
        await db.query(`
          UPDATE subjects
          SET code = ?, description = ?, subject_group = ?
          WHERE id = ?
        `, [subject.code, subject.description, subject.group, subjectId]);
        
        console.log(`Updated subject: ${subject.name}`);
      }
      
      // Check if subject-trade combination exists
      const [existingCombo] = await db.query(`
        SELECT id FROM subject_trade_combinations
        WHERE trade_id = ? AND subject_id = ?
      `, [artsTradeId, subjectId]);
      
      if (existingCombo.length === 0) {
        // Create new combination
        await db.query(`
          INSERT INTO subject_trade_combinations (trade_id, subject_id, is_compulsory, is_elective)
          VALUES (?, ?, ?, ?)
        `, [artsTradeId, subjectId, subject.is_elective ? 0 : 1, subject.is_elective ? 1 : 0]);
        
        console.log(`Added ${subject.name} to ARTS trade`);
      } else {
        // Update existing combination
        await db.query(`
          UPDATE subject_trade_combinations
          SET is_compulsory = ?, is_elective = ?
          WHERE id = ?
        `, [subject.is_elective ? 0 : 1, subject.is_elective ? 1 : 0, existingCombo[0].id]);
        
        console.log(`Updated ${subject.name} in ARTS trade`);
      }
    }
    
    console.log('Arts subjects and combinations updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating Arts subjects:', error);
    return false;
  }
}

// Execute the migration
updateArtsSubjects()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
