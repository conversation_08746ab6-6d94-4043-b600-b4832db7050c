/**
 * Migration to update class_incharge table to add session column
 * This ensures all inchargeships are session-wise
 */

const db = require('../config/database');

async function updateClassInchargeTable() {
  try {
    console.log('Updating class_incharge table to add session column...');

    // Check if class_incharge table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_incharge'
    `);

    if (tableExists[0].table_exists === 0) {
      console.log('class_incharge table does not exist, creating it with session column...');
      
      // Create class_incharge table with session column
      await db.query(`
        CREATE TABLE IF NOT EXISTS class_incharge (
          id INT PRIMARY KEY AUTO_INCREMENT,
          class_id INT NOT NULL,
          teacher_id INT NOT NULL,
          session VARCHAR(20) NOT NULL DEFAULT '2024-2025',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE KEY unique_class_incharge_session (class_id, session)
        )
      `);
      
      console.log('class_incharge table created with session column');
    } else {
      // Check if session column already exists
      const [sessionColumnExists] = await db.query(`
        SELECT COUNT(*) as column_exists
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'class_incharge'
        AND column_name = 'session'
      `);

      if (sessionColumnExists[0].column_exists === 0) {
        console.log('Adding session column to class_incharge table...');
        
        // Add session column to class_incharge table
        await db.query(`
          ALTER TABLE class_incharge
          ADD COLUMN session VARCHAR(20) NOT NULL DEFAULT '2024-2025' AFTER teacher_id
        `);
        
        // Drop the old unique key
        await db.query(`
          ALTER TABLE class_incharge
          DROP INDEX unique_class_incharge
        `);
        
        // Add new unique key that includes session
        await db.query(`
          ALTER TABLE class_incharge
          ADD CONSTRAINT unique_class_incharge_session UNIQUE (class_id, session)
        `);
        
        console.log('Session column added to class_incharge table');
      } else {
        console.log('Session column already exists in class_incharge table');
      }
    }

    console.log('Class incharge table update completed');
    return true;
  } catch (error) {
    console.error('Error updating class_incharge table:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  updateClassInchargeTable()
    .then(() => {
      console.log('Class incharge table update completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Class incharge table update failed:', err);
      process.exit(1);
    });
}

module.exports = updateClassInchargeTable;
