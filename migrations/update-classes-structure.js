/**
 * Migration script to update the classes table structure
 *
 * Changes:
 * 1. The `class` column should contain the class trade ID from the trade table
 * 2. The `section` column should contain values like A, B, C, D, E, F
 * 3. The `session` column should be set to "2025-26"
 * 4. The `classroom` column should contain the ID from the rooms table
 * 5. Other columns should be removed
 */

const db = require('../config/database');

async function updateClassesStructure() {
  try {
    console.log('Starting migration: Updating classes table structure');

    // Check if classes table exists
    const [classesExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'classes'
    `);

    if (classesExists[0].table_exists === 0) {
      console.log('classes table does not exist, creating it with the new structure...');

      // Create classes table with the new structure
      await db.query(`
        CREATE TABLE IF NOT EXISTS classes (
          id INT PRIMARY KEY AUTO_INCREMENT,
          grade VARCHAR(10) NOT NULL,
          trade_id INT NOT NULL,
          section VARCHAR(10) NOT NULL,
          session VARCHAR(20) NOT NULL DEFAULT '2025-26',
          classroom_id INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log('classes table created successfully');
    } else {
      console.log('classes table exists, checking if trade table exists...');

      // Check if trade table exists
      const [tradeExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'trades'
      `);

      if (tradeExists[0].table_exists === 0) {
        console.log('trades table does not exist, creating it...');

        // Create trades table
        await db.query(`
          CREATE TABLE IF NOT EXISTS trades (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(50) NOT NULL,
            code VARCHAR(20) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_trade_name (name),
            UNIQUE KEY unique_trade_code (code)
          )
        `);

        console.log('trades table created successfully');

        // Insert default trades
        await db.query(`
          INSERT INTO trades (name, code, description)
          VALUES
            ('NON-MEDICAL', 'NM', 'Non-Medical stream with Physics, Chemistry, Mathematics'),
            ('MEDICAL', 'M', 'Medical stream with Physics, Chemistry, Biology'),
            ('COMMERCE', 'C', 'Commerce stream with Business Studies, Accountancy, Economics'),
            ('ARTS', 'A', 'Arts stream with History, Geography, Political Science')
        `);

        console.log('Default trades inserted successfully');
      }

      // Check if rooms table exists
      const [roomsExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'rooms'
      `);

      if (roomsExists[0].table_exists === 0) {
        console.log('rooms table does not exist, creating it...');

        // Create rooms table
        await db.query(`
          CREATE TABLE IF NOT EXISTS rooms (
            id INT PRIMARY KEY AUTO_INCREMENT,
            room_number VARCHAR(20) NOT NULL,
            capacity INT DEFAULT 40,
            building VARCHAR(50),
            floor INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_room_number (room_number)
          )
        `);

        console.log('rooms table created successfully');

        // Insert default rooms
        await db.query(`
          INSERT INTO rooms (room_number, capacity, building, floor)
          VALUES
            ('Room 201', 40, 'Main Building', 2),
            ('Room 202', 40, 'Main Building', 2),
            ('Room 203', 40, 'Main Building', 2),
            ('Room 204', 40, 'Main Building', 2),
            ('Room 205', 40, 'Main Building', 2),
            ('Room 206', 40, 'Main Building', 2)
        `);

        console.log('Default rooms inserted successfully');
      }

      // Get the current structure of the classes table
      const [classesColumns] = await db.query(`
        SHOW COLUMNS FROM classes
      `);

      console.log('Current classes table structure:');
      classesColumns.forEach(col => {
        console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
      });

      // Check if the table has the old structure
      const hasOldStructure = classesColumns.some(col => col.Field === 'trade');

      if (hasOldStructure) {
        console.log('Classes table has the old structure, backing up data...');

        // Create a backup of the classes table
        await db.query(`
          CREATE TABLE IF NOT EXISTS classes_backup AS
          SELECT * FROM classes
        `);

        console.log('Classes table backed up to classes_backup');

        // Get existing classes data
        const [existingClasses] = await db.query(`
          SELECT id, name, grade, trade, section, academic_year FROM classes
        `);

        console.log(`Found ${existingClasses.length} existing classes`);

        // Check if trades table has code column
        const [tradesColumns] = await db.query(`
          SHOW COLUMNS FROM trades
        `);

        const hasCodeColumn = tradesColumns.some(col => col.Field === 'code');

        if (!hasCodeColumn) {
          console.log('Adding code column to trades table');

          await db.query(`
            ALTER TABLE trades
            ADD COLUMN code VARCHAR(20)
          `);

          // Update the code column based on the name
          await db.query(`
            UPDATE trades
            SET code = CASE
              WHEN name = 'NON-MEDICAL' OR name LIKE '%Non-Medical%' OR name LIKE '%non-medical%' THEN 'NM'
              WHEN name = 'MEDICAL' OR name LIKE '%Medical%' OR name LIKE '%medical%' THEN 'M'
              WHEN name = 'COMMERCE' OR name LIKE '%Commerce%' OR name LIKE '%commerce%' THEN 'C'
              WHEN name = 'ARTS' OR name LIKE '%Arts%' OR name LIKE '%arts%' THEN 'A'
              ELSE LEFT(name, 2)
            END
          `);

          console.log('Code column added and updated');
        }

        // Get trades data
        const [trades] = await db.query(`
          SELECT id, name, ${hasCodeColumn ? 'code' : 'LEFT(name, 2) as code'} FROM trades
        `);

        console.log(`Found ${trades.length} trades`);

        // Get rooms data
        const [rooms] = await db.query(`
          SELECT id, room_number FROM rooms
        `);

        console.log(`Found ${rooms.length} rooms`);

        // Check if classrooms table exists
        const [classroomsExists] = await db.query(`
          SELECT COUNT(*) as table_exists
          FROM information_schema.tables
          WHERE table_schema = DATABASE()
          AND table_name = 'classrooms'
        `);

        let classroomsData = [];

        if (classroomsExists[0].table_exists > 0) {
          // Get classrooms data
          [classroomsData] = await db.query(`
            SELECT id, class_id, room_number FROM classrooms
          `);

          console.log(`Found ${classroomsData.length} classrooms`);
        }

        // Add trade_id, classroom_id, and session columns if they don't exist
        const hasTrade_id = classesColumns.some(col => col.Field === 'trade_id');
        const hasClassroom_id = classesColumns.some(col => col.Field === 'classroom_id');
        const hasSession = classesColumns.some(col => col.Field === 'session');

        if (!hasTrade_id) {
          console.log('Adding trade_id column to classes table');

          await db.query(`
            ALTER TABLE classes
            ADD COLUMN trade_id INT
          `);
        }

        if (!hasClassroom_id) {
          console.log('Adding classroom_id column to classes table');

          await db.query(`
            ALTER TABLE classes
            ADD COLUMN classroom_id INT
          `);
        }

        if (!hasSession) {
          console.log('Adding session column to classes table');

          await db.query(`
            ALTER TABLE classes
            ADD COLUMN session VARCHAR(20) DEFAULT '2025-26'
          `);
        }

        // Update the trade_id based on the trade name
        for (const cls of existingClasses) {
          // Find the trade ID
          let tradeId = null;

          if (cls.trade) {
            const trade = trades.find(t =>
              t.name === cls.trade ||
              t.code === cls.trade ||
              t.name === `${cls.trade.toUpperCase()}` ||
              (cls.trade === 'NM' && t.code === 'NM') ||
              (cls.trade === 'M' && t.code === 'M') ||
              (cls.trade === 'C' && t.code === 'C') ||
              (cls.trade === 'A' && t.code === 'A') ||
              (cls.trade === 'NON-MEDICAL' && t.code === 'NM') ||
              (cls.trade === 'MEDICAL' && t.code === 'M') ||
              (cls.trade === 'COMMERCE' && t.code === 'C') ||
              (cls.trade === 'ARTS' && t.code === 'A')
            );

            if (trade) {
              tradeId = trade.id;
            }
          }

          // Find the classroom ID
          let classroomId = null;

          if (classroomsData.length > 0) {
            const classroom = classroomsData.find(cr => cr.class_id === cls.id);

            if (classroom) {
              // Find the room ID
              const room = rooms.find(r => r.room_number === classroom.room_number);

              if (room) {
                classroomId = room.id;
              }
            }
          }

          // Update the class with trade_id and classroom_id
          if (tradeId) {
            await db.query(`
              UPDATE classes
              SET trade_id = ?, classroom_id = ?, session = '2025-26'
              WHERE id = ?
            `, [tradeId, classroomId, cls.id]);

            console.log(`Updated class ${cls.id} (${cls.name || `${cls.grade} ${cls.trade} ${cls.section}`}) with trade_id=${tradeId}, classroom_id=${classroomId}`);
          } else {
            console.log(`Could not find trade for class ${cls.id} (${cls.name || `${cls.grade} ${cls.trade} ${cls.section}`})`);
          }
        }

        // Now we can drop the old columns
        console.log('Dropping old columns from classes table');

        // Check if the columns exist before dropping them
        const hasName = classesColumns.some(col => col.Field === 'name');
        const hasTrade = classesColumns.some(col => col.Field === 'trade');
        const hasAcademicYear = classesColumns.some(col => col.Field === 'academic_year');

        if (hasName) {
          await db.query(`
            ALTER TABLE classes
            DROP COLUMN name
          `);
        }

        if (hasTrade) {
          await db.query(`
            ALTER TABLE classes
            DROP COLUMN trade
          `);
        }

        if (hasAcademicYear) {
          await db.query(`
            ALTER TABLE classes
            DROP COLUMN academic_year
          `);
        }

        console.log('Old columns dropped successfully');

        // Delete rows with NULL trade_id
        await db.query(`
          DELETE FROM classes
          WHERE trade_id IS NULL
        `);

        console.log('Deleted classes with NULL trade_id');

        // Make sure trade_id is NOT NULL
        await db.query(`
          ALTER TABLE classes
          MODIFY COLUMN trade_id INT NOT NULL
        `);

        // Make sure session is NOT NULL and has a default value
        await db.query(`
          ALTER TABLE classes
          MODIFY COLUMN session VARCHAR(20) NOT NULL DEFAULT '2025-26'
        `);

        console.log('Classes table structure updated successfully');
      } else {
        console.log('Classes table already has the new structure');
      }
    }

    // Get the final structure of the classes table
    const [finalClassesColumns] = await db.query(`
      SHOW COLUMNS FROM classes
    `);

    console.log('Final classes table structure:');
    finalClassesColumns.forEach(col => {
      console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
updateClassesStructure();
