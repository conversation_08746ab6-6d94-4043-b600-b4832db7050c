/**
 * Migration script to update the classrooms table structure
 *
 * Changes:
 * 1. Update classrooms table to have: id, session, room_id, incharge, class_id, trade_id, section, is_active
 * 2. Remove other columns from the classrooms table
 * 3. Add 20 rooms numbered 1 to 20
 * 4. Allow classes to be changed in a new session
 */

const db = require('../config/database');

async function updateClassroomsStructure() {
  try {
    console.log('Starting migration: Updating classrooms table structure');

    // Check if rooms table exists
    const [roomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'rooms'
    `);

    if (roomsExists[0].table_exists === 0) {
      console.log('rooms table does not exist, creating it...');

      // Create rooms table
      await db.query(`
        CREATE TABLE IF NOT EXISTS rooms (
          id INT PRIMARY KEY AUTO_INCREMENT,
          room_number VARCHAR(20) NOT NULL,
          capacity INT DEFAULT 40,
          building VARCHAR(50) DEFAULT 'Main Building',
          floor INT DEFAULT 1,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_room_number (room_number)
        )
      `);

      console.log('rooms table created successfully');

      // Insert 20 rooms
      for (let i = 1; i <= 20; i++) {
        await db.query(`
          INSERT INTO rooms (room_number, capacity, floor)
          VALUES (?, 40, ?)
        `, [`Room ${i}`, Math.ceil(i / 5)]);
      }

      console.log('20 rooms inserted successfully');
    } else {
      // Check if we already have 20 rooms
      const [roomCount] = await db.query(`
        SELECT COUNT(*) as count FROM rooms
      `);

      if (roomCount[0].count < 20) {
        console.log(`Only ${roomCount[0].count} rooms found, adding more to reach 20...`);

        // Get existing room numbers
        const [existingRooms] = await db.query(`
          SELECT room_number FROM rooms
        `);

        const existingRoomNumbers = existingRooms.map(r => r.room_number);

        // Add rooms until we have 20
        for (let i = 1; i <= 20; i++) {
          const roomNumber = `Room ${i}`;

          if (!existingRoomNumbers.includes(roomNumber)) {
            await db.query(`
              INSERT INTO rooms (room_number, capacity, floor)
              VALUES (?, 40, ?)
            `, [roomNumber, Math.ceil(i / 5)]);

            console.log(`Added room: ${roomNumber}`);
          }
        }
      }
    }

    // Check if classrooms table exists
    const [classroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'classrooms'
    `);

    if (classroomsExists[0].table_exists === 0) {
      console.log('classrooms table does not exist, creating it with the new structure...');

      // Create classrooms table with the new structure
      await db.query(`
        CREATE TABLE IF NOT EXISTS classrooms (
          id INT PRIMARY KEY AUTO_INCREMENT,
          session VARCHAR(20) NOT NULL DEFAULT '2025-26',
          room_id INT NOT NULL,
          incharge INT,
          class_id INT,
          trade_id INT,
          section VARCHAR(10),
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
          FOREIGN KEY (incharge) REFERENCES users(id) ON DELETE SET NULL,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
          FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
          UNIQUE KEY unique_classroom_session_room (session, room_id)
        )
      `);

      console.log('classrooms table created successfully');
    } else {
      console.log('classrooms table exists, backing up data...');

      // Create a backup of the classrooms table
      await db.query(`
        CREATE TABLE IF NOT EXISTS classrooms_backup AS
        SELECT * FROM classrooms
      `);

      console.log('classrooms table backed up to classrooms_backup');

      // Get the current structure of the classrooms table
      const [classroomsColumns] = await db.query(`
        SHOW COLUMNS FROM classrooms
      `);

      console.log('Current classrooms table structure:');
      classroomsColumns.forEach(col => {
        console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
      });

      // Check if the table has the old structure
      const hasOldStructure = !classroomsColumns.some(col => col.Field === 'session') ||
                             !classroomsColumns.some(col => col.Field === 'room_id') ||
                             !classroomsColumns.some(col => col.Field === 'is_active');

      if (hasOldStructure) {
        console.log('classrooms table has the old structure, updating it...');

        // Get existing classrooms data
        const [existingClassrooms] = await db.query(`
          SELECT * FROM classrooms
        `);

        console.log(`Found ${existingClassrooms.length} existing classrooms`);

        // Find tables that reference classrooms
        const [referencingTables] = await db.query(`
          SELECT TABLE_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
          AND REFERENCED_TABLE_NAME = 'classrooms'
          AND REFERENCED_COLUMN_NAME = 'id'
        `);

        console.log('Tables referencing classrooms:');
        referencingTables.forEach(table => {
          console.log(`- ${table.TABLE_NAME}`);
        });

        // Create backups of referencing tables and clear them
        for (const table of referencingTables) {
          const tableName = table.TABLE_NAME;

          // Create backup
          await db.query(`
            CREATE TABLE IF NOT EXISTS ${tableName}_backup AS
            SELECT * FROM ${tableName}
          `);

          console.log(`${tableName} table backed up to ${tableName}_backup`);

          // Clear the table
          await db.query(`
            DELETE FROM ${tableName}
          `);

          console.log(`Cleared ${tableName} table`);
        }

        // Temporarily disable foreign key checks
        await db.query(`SET FOREIGN_KEY_CHECKS = 0`);
        console.log('Disabled foreign key checks');

        // Now we can drop the classrooms table
        await db.query(`
          DROP TABLE classrooms
        `);

        console.log('Dropped classrooms table');

        // Re-enable foreign key checks after creating the new table

        // Create classrooms table with the new structure
        await db.query(`
          CREATE TABLE classrooms (
            id INT PRIMARY KEY AUTO_INCREMENT,
            session VARCHAR(20) NOT NULL DEFAULT '2025-26',
            room_id INT NOT NULL,
            incharge INT,
            class_id INT,
            trade_id INT,
            section VARCHAR(10),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
            FOREIGN KEY (incharge) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
            FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
            UNIQUE KEY unique_classroom_session_room (session, room_id)
          )
        `);

        console.log('Recreated classrooms table with the new structure');

        // Re-enable foreign key checks
        await db.query(`SET FOREIGN_KEY_CHECKS = 1`);
        console.log('Re-enabled foreign key checks');

        // Get rooms data
        const [rooms] = await db.query(`
          SELECT id, room_number FROM rooms
        `);

        console.log(`Found ${rooms.length} rooms`);

        // Get classes data
        const [classes] = await db.query(`
          SELECT c.id, c.grade, c.section, c.trade_id, t.name as trade_name
          FROM classes c
          LEFT JOIN trades t ON c.trade_id = t.id
        `);

        console.log(`Found ${classes.length} classes`);

        // Get teachers data
        const [teachers] = await db.query(`
          SELECT id, name FROM users WHERE role = 'teacher'
        `);

        console.log(`Found ${teachers.length} teachers`);

        // Migrate data from old structure to new structure
        const newClassroomIds = {}; // Map old classroom IDs to new classroom IDs

        for (const classroom of existingClassrooms) {
          // Find the room ID
          let roomId = null;
          if (classroom.room_number) {
            const room = rooms.find(r => r.room_number === classroom.room_number);
            if (room) {
              roomId = room.id;
            }
          }

          // If no room ID found, assign a random available room
          if (!roomId && rooms.length > 0) {
            const randomRoom = rooms[Math.floor(Math.random() * rooms.length)];
            roomId = randomRoom.id;
          }

          // Find the class details
          let classId = null;
          let tradeId = null;
          let section = null;

          if (classroom.class_id) {
            const classInfo = classes.find(c => c.id === classroom.class_id);
            if (classInfo) {
              classId = classInfo.id;
              tradeId = classInfo.trade_id || classroom.trade_id;
              section = classInfo.section || classroom.section;
            }
          } else {
            // Use values directly from the classroom record
            classId = classroom.class_id;
            tradeId = classroom.trade_id;
            section = classroom.section;
          }

          // Find the incharge teacher
          let inchargeId = null;
          if (teachers.length > 0) {
            // Assign a random teacher as incharge
            const randomTeacher = teachers[Math.floor(Math.random() * teachers.length)];
            inchargeId = randomTeacher.id;
          }

          // Insert into new classrooms table
          if (roomId) {
            try {
              // Check if a classroom with this session and room_id already exists
              const [existingRoom] = await db.query(`
                SELECT id FROM classrooms WHERE session = ? AND room_id = ?
              `, [classroom.session || '2025-26', roomId]);

              let newClassroomId;

              if (existingRoom.length > 0) {
                // Update the existing classroom
                await db.query(`
                  UPDATE classrooms
                  SET incharge = ?, class_id = ?, trade_id = ?, section = ?, is_active = ?
                  WHERE id = ?
                `, [
                  inchargeId,
                  classId,
                  tradeId,
                  section,
                  classroom.is_active !== undefined ? classroom.is_active : true,
                  existingRoom[0].id
                ]);

                newClassroomId = existingRoom[0].id;
                console.log(`Updated existing classroom: ID ${newClassroomId}, Room ID ${roomId}, Class ID ${classId || 'NULL'}, Trade ID ${tradeId || 'NULL'}, Section ${section || 'NULL'}`);
              } else {
                // Insert a new classroom
                const [result] = await db.query(`
                  INSERT INTO classrooms (session, room_id, incharge, class_id, trade_id, section, is_active)
                  VALUES (?, ?, ?, ?, ?, ?, ?)
                `, [
                  classroom.session || '2025-26',
                  roomId,
                  inchargeId,
                  classId,
                  tradeId,
                  section,
                  classroom.is_active !== undefined ? classroom.is_active : true
                ]);

                newClassroomId = result.insertId;
                console.log(`Migrated classroom: Room ID ${roomId}, Class ID ${classId || 'NULL'}, Trade ID ${tradeId || 'NULL'}, Section ${section || 'NULL'}`);
              }

              // Store the mapping from old ID to new ID
              newClassroomIds[classroom.id] = newClassroomId;
            } catch (error) {
              console.error(`[QUERY ERROR] ${error.code}: ${error.sqlMessage} - Query: ${error.sql}`);

              // Try with a different room
              const usedRoomIds = Object.values(newClassroomIds).map(id => id);
              const availableRooms = rooms.filter(r => !usedRoomIds.includes(r.id));

              if (availableRooms.length > 0) {
                const newRoom = availableRooms[0];
                console.log(`Retrying with a different room: ${newRoom.room_number} (ID: ${newRoom.id})`);

                const [result] = await db.query(`
                  INSERT INTO classrooms (session, room_id, incharge, class_id, trade_id, section, is_active)
                  VALUES (?, ?, ?, ?, ?, ?, ?)
                `, [
                  classroom.session || '2025-26',
                  newRoom.id,
                  inchargeId,
                  classId,
                  tradeId,
                  section,
                  classroom.is_active !== undefined ? classroom.is_active : true
                ]);

                // Store the mapping from old ID to new ID
                newClassroomIds[classroom.id] = result.insertId;

                console.log(`Migrated classroom with alternative room: Room ID ${newRoom.id}, Class ID ${classId || 'NULL'}, Trade ID ${tradeId || 'NULL'}, Section ${section || 'NULL'}`);
              } else {
                console.error('No available rooms left for migration');
              }
            }
          }
        }

        // Restore data in referencing tables
        for (const table of referencingTables) {
          const tableName = table.TABLE_NAME;

          if (tableName === 'student_classrooms') {
            // Get backup data
            const [backupData] = await db.query(`
              SELECT * FROM ${tableName}_backup
            `);

            console.log(`Restoring ${backupData.length} records to ${tableName}...`);

            // Restore each record with the new classroom ID
            for (const record of backupData) {
              const newClassroomId = newClassroomIds[record.classroom_id];

              if (newClassroomId) {
                await db.query(`
                  INSERT INTO ${tableName} (student_id, classroom_id)
                  VALUES (?, ?)
                `, [record.student_id, newClassroomId]);
              } else {
                console.log(`Warning: Could not find new ID for classroom ${record.classroom_id}`);
              }
            }

            console.log(`Restored ${tableName} data`);
          }
        }
      }
    }

    // Update student_classrooms table to reference the new classrooms structure
    const [studentClassroomsExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'student_classrooms'
    `);

    if (studentClassroomsExists[0].table_exists > 0) {
      console.log('Updating student_classrooms table references...');

      // Check if there are any student-classroom assignments
      const [studentAssignments] = await db.query(`
        SELECT COUNT(*) as count FROM student_classrooms
      `);

      if (studentAssignments[0].count > 0) {
        console.log(`Found ${studentAssignments[0].count} student-classroom assignments, updating them...`);

        // Create a backup of the student_classrooms table
        await db.query(`
          CREATE TABLE IF NOT EXISTS student_classrooms_backup AS
          SELECT * FROM student_classrooms
        `);

        console.log('student_classrooms table backed up to student_classrooms_backup');
      }
    }

    // Get the final structure of the classrooms table
    const [finalClassroomsColumns] = await db.query(`
      SHOW COLUMNS FROM classrooms
    `);

    console.log('Final classrooms table structure:');
    finalClassroomsColumns.forEach(col => {
      console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
updateClassroomsStructure();
