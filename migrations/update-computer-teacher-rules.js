/**
 * Main migration script to implement the new rules for computer teachers:
 * 1. Computer teachers can only teach computer-related subjects
 * 2. Computer teachers cannot be class incharge, only lab incharge
 * 3. All inchargeships are session-wise
 */

const db = require('../config/database');
const updateClassInchargeTable = require('./update-class-incharge-table');
const createLabInchargeTable = require('./create-lab-incharge-table');
const createTeacherSpecializationTable = require('./create-teacher-specialization-table');
const fixComputerTeacherAssignments = require('../scripts/fix-computer-teacher-assignments');

async function updateComputerTeacherRules() {
  try {
    console.log('Starting migration to update computer teacher rules...');
    
    // 1. Update class_incharge table to add session column
    console.log('\n=== Step 1: Update class_incharge table ===');
    const classInchargeUpdated = await updateClassInchargeTable();
    if (!classInchargeUpdated) {
      console.error('Failed to update class_incharge table');
      return false;
    }
    
    // 2. Create lab_incharge table with session column
    console.log('\n=== Step 2: Create lab_incharge table ===');
    const labInchargeCreated = await createLabInchargeTable();
    if (!labInchargeCreated) {
      console.error('Failed to create lab_incharge table');
      return false;
    }
    
    // 3. Create teacher_specialization table
    console.log('\n=== Step 3: Create teacher_specialization table ===');
    const specializationCreated = await createTeacherSpecializationTable();
    if (!specializationCreated) {
      console.error('Failed to create teacher_specialization table');
      return false;
    }
    
    // 4. Fix existing computer teacher assignments
    console.log('\n=== Step 4: Fix existing computer teacher assignments ===');
    const assignmentsFixed = await fixComputerTeacherAssignments();
    if (!assignmentsFixed) {
      console.error('Failed to fix computer teacher assignments');
      return false;
    }
    
    // 5. Register the middleware in the application
    console.log('\n=== Step 5: Middleware registration ===');
    console.log('To complete the setup, add the following middleware to your routes:');
    console.log(`
    // In your routes file where teacher-subject assignments are handled:
    const validateTeacherSubjectAssignment = require('../middleware/teacher-subject-validator');
    
    // Add the middleware to the route
    router.post('/assign-subject', validateTeacherSubjectAssignment, yourControllerFunction);
    
    // In your routes file where class incharge assignments are handled:
    const validateClassInchargeAssignment = require('../middleware/class-incharge-validator');
    
    // Add the middleware to the route
    router.post('/assign-class-incharge', validateClassInchargeAssignment, yourControllerFunction);
    `);
    
    console.log('\nMigration completed successfully!');
    return true;
  } catch (error) {
    console.error('Error during migration:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  updateComputerTeacherRules()
    .then(success => {
      if (success) {
        console.log('Computer teacher rules update completed successfully');
      } else {
        console.error('Computer teacher rules update failed');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(err => {
      console.error('Computer teacher rules update failed with error:', err);
      process.exit(1);
    });
}

module.exports = updateComputerTeacherRules;
