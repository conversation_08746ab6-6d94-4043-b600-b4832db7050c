/**
 * Migration script to update the demo student's class assignment
 */

const db = require('../config/database');

async function updateDemoStudent() {
  try {
    console.log('Starting migration: Updating demo student class assignment');
    
    // Check if the demo student exists
    const [demoStudent] = await db.query(`
      SELECT id, name, email FROM users WHERE email = '<EMAIL>'
    `);
    
    if (demoStudent.length === 0) {
      console.log('Demo student not found, creating it...');
      
      // Create the demo student
      await db.query(`
        INSERT INTO users (name, email, password, role)
        VALUES ('CS Student', '<EMAIL>', '$2a$10$XJKRdYw0Qz2fPFKjxAw9W.1QKV0UXA.H8l1cGEDYIuW/9JQB5.7Aq', 'student')
      `);
      
      // Get the demo student ID
      const [newDemoStudent] = await db.query(`
        SELECT id FROM users WHERE email = '<EMAIL>'
      `);
      
      if (newDemoStudent.length === 0) {
        throw new Error('Failed to create demo student');
      }
      
      console.log(`Demo student created with ID: ${newDemoStudent[0].id}`);
      
      // Use the new demo student ID
      demoStudent[0] = newDemoStudent[0];
    } else {
      console.log(`Found demo student: ${demoStudent[0].name} (${demoStudent[0].email}), ID: ${demoStudent[0].id}`);
    }
    
    // Find the 12 NON-MEDICAL A class
    const [nonMedicalAClass] = await db.query(`
      SELECT c.id, c.grade, t.name AS trade_name, c.section
      FROM classes c
      JOIN trades t ON c.trade_id = t.id
      WHERE c.grade = '12' AND t.name = 'Non-Medical' AND c.section = 'A'
    `);
    
    if (nonMedicalAClass.length === 0) {
      console.log('12 NON-MEDICAL A class not found, creating it...');
      
      // Get the Non-Medical trade ID
      const [nonMedicalTrade] = await db.query(`
        SELECT id FROM trades WHERE name = 'Non-Medical' OR code = 'NM'
      `);
      
      if (nonMedicalTrade.length === 0) {
        throw new Error('Non-Medical trade not found');
      }
      
      // Create the 12 NON-MEDICAL A class
      await db.query(`
        INSERT INTO classes (grade, trade_id, section, session)
        VALUES ('12', ?, 'A', '2025-26')
      `, [nonMedicalTrade[0].id]);
      
      // Get the new class ID
      const [newClass] = await db.query(`
        SELECT id FROM classes
        WHERE grade = '12' AND trade_id = ? AND section = 'A'
      `, [nonMedicalTrade[0].id]);
      
      if (newClass.length === 0) {
        throw new Error('Failed to create 12 NON-MEDICAL A class');
      }
      
      console.log(`12 NON-MEDICAL A class created with ID: ${newClass[0].id}`);
      
      // Use the new class ID
      nonMedicalAClass[0] = { id: newClass[0].id, grade: '12', trade_name: 'Non-Medical', section: 'A' };
    } else {
      console.log(`Found 12 NON-MEDICAL A class with ID: ${nonMedicalAClass[0].id}`);
    }
    
    // Find or create a classroom for this class
    const [classroom] = await db.query(`
      SELECT id FROM classrooms WHERE class_id = ?
    `, [nonMedicalAClass[0].id]);
    
    let classroomId;
    
    if (classroom.length === 0) {
      console.log('Classroom for 12 NON-MEDICAL A not found, creating it...');
      
      // Create a classroom for this class
      await db.query(`
        INSERT INTO classrooms (class_id, room_number)
        VALUES (?, 'Room 201')
      `, [nonMedicalAClass[0].id]);
      
      // Get the new classroom ID
      const [newClassroom] = await db.query(`
        SELECT id FROM classrooms WHERE class_id = ?
      `, [nonMedicalAClass[0].id]);
      
      if (newClassroom.length === 0) {
        throw new Error('Failed to create classroom for 12 NON-MEDICAL A');
      }
      
      console.log(`Classroom created for 12 NON-MEDICAL A with ID: ${newClassroom[0].id}`);
      
      classroomId = newClassroom[0].id;
    } else {
      console.log(`Found classroom for 12 NON-MEDICAL A with ID: ${classroom[0].id}`);
      classroomId = classroom[0].id;
    }
    
    // Check if the demo student is already assigned to 12 NON-MEDICAL A
    const [currentAssignment] = await db.query(`
      SELECT sc.id, c.grade, t.name AS trade_name, c.section
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON c.trade_id = t.id
      WHERE sc.student_id = ?
    `, [demoStudent[0].id]);
    
    if (currentAssignment.length > 0) {
      console.log(`Demo student is currently assigned to: ${currentAssignment[0].grade} ${currentAssignment[0].trade_name} ${currentAssignment[0].section}`);
      
      if (currentAssignment[0].grade === '12' && 
          currentAssignment[0].trade_name === 'Non-Medical' && 
          currentAssignment[0].section === 'A') {
        console.log('Demo student is already assigned to 12 NON-MEDICAL A, no changes needed');
      } else {
        console.log('Updating demo student assignment to 12 NON-MEDICAL A');
        
        // Update the student's classroom assignment
        await db.query(`
          UPDATE student_classrooms
          SET classroom_id = ?
          WHERE student_id = ?
        `, [classroomId, demoStudent[0].id]);
        
        console.log('Demo student assignment updated successfully');
      }
    } else {
      console.log('Demo student is not assigned to any class, assigning to 12 NON-MEDICAL A');
      
      // Assign the demo student to 12 NON-MEDICAL A
      await db.query(`
        INSERT INTO student_classrooms (student_id, classroom_id)
        VALUES (?, ?)
      `, [demoStudent[0].id, classroomId]);
      
      console.log('Demo student assigned to 12 NON-MEDICAL A successfully');
    }
    
    // Verify the assignment
    const [verifyAssignment] = await db.query(`
      SELECT c.grade, t.name AS trade_name, c.section
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON c.trade_id = t.id
      WHERE sc.student_id = ?
    `, [demoStudent[0].id]);
    
    if (verifyAssignment.length > 0) {
      console.log(`Verified: Demo student is now assigned to ${verifyAssignment[0].grade} ${verifyAssignment[0].trade_name} ${verifyAssignment[0].section}`);
    } else {
      console.log('Error: Could not verify demo student assignment');
    }
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
updateDemoStudent();
