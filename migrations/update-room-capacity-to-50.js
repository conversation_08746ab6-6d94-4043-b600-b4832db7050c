const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'exam_prep_platform'
};

async function updateRoomCapacity() {
  let connection;

  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);

    console.log('✅ Connected to database successfully');

    // Check current room capacities
    console.log('\n📊 Checking current room capacities...');
    const [currentCapacities] = await connection.query(`
      SELECT id, room_number, capacity
      FROM rooms
      ORDER BY id
    `);

    console.log(`Found ${currentCapacities.length} rooms:`);
    currentCapacities.forEach(room => {
      console.log(`  - ${room.room_number}: ${room.capacity} students`);
    });

    // Update all room capacities to 50
    console.log('\n🔄 Updating room capacities to 50 students...');
    const [updateResult] = await connection.query(`
      UPDATE rooms
      SET capacity = 50
      WHERE capacity != 50
    `);

    console.log(`✅ Updated ${updateResult.affectedRows} rooms to capacity of 50 students`);

    // Verify the update
    console.log('\n✅ Verifying updates...');
    const [updatedCapacities] = await connection.query(`
      SELECT id, room_number, capacity
      FROM rooms
      ORDER BY id
    `);

    console.log('Updated room capacities:');
    updatedCapacities.forEach(room => {
      console.log(`  - ${room.room_number}: ${room.capacity} students`);
    });

    // Update any classroom-related tables that might have capacity references
    console.log('\n🔄 Checking for other capacity references...');

    // Check if classrooms table has max_capacity column
    try {
      const [classroomCapacities] = await connection.query(`
        SELECT COUNT(*) as count
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'classrooms'
        AND COLUMN_NAME = 'max_capacity'
      `);

      if (classroomCapacities[0].count > 0) {
        console.log('📝 Found max_capacity column in classrooms table, updating...');
        const [classroomUpdate] = await connection.query(`
          UPDATE classrooms
          SET max_capacity = 50
          WHERE max_capacity != 50
        `);
        console.log(`✅ Updated ${classroomUpdate.affectedRows} classroom capacity records`);
      }
    } catch (error) {
      console.log('ℹ️  No max_capacity column found in classrooms table (this is normal)');
    }

    console.log('\n🎉 Room capacity update completed successfully!');
    console.log('📋 Summary:');
    console.log(`   - Total rooms: ${updatedCapacities.length}`);
    console.log(`   - Standard capacity: 50 students per room`);
    console.log(`   - Total school capacity: ${updatedCapacities.length * 50} students`);

  } catch (error) {
    console.error('❌ Error updating room capacities:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the migration
if (require.main === module) {
  updateRoomCapacity()
    .then(() => {
      console.log('\n✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = updateRoomCapacity;
