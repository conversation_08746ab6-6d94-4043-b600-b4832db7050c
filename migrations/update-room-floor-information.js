/**
 * Migration to update room floor information
 * 
 * This migration:
 * 1. Updates rooms 1-6 to ground floor (0)
 * 2. Updates rooms 7-20 to first floor (1)
 * 3. Updates labs and offices floor information
 * 4. Ensures consistent floor numbering across the system
 */

const db = require('../config/database');

async function updateRoomFloorInformation() {
    console.log('🔄 Updating Room Floor Information...\n');

    try {
        // 1. Check current room floor information
        console.log('1. Checking current room floor information...');
        
        const [currentRooms] = await db.query(`
            SELECT id, room_number, building, floor 
            FROM rooms 
            WHERE room_number LIKE 'Room %' OR room_number LIKE '%Lab%' OR room_number LIKE '%Office%'
            ORDER BY id
        `);
        
        console.log(`Found ${currentRooms.length} rooms to update:`);
        currentRooms.forEach(room => {
            console.log(`   • ${room.room_number} (ID: ${room.id}) - Current Floor: ${room.floor || 'NULL'}`);
        });

        // 2. Update rooms 1-6 to ground floor (0)
        console.log('\n2. Updating rooms 1-6 to ground floor (0)...');
        
        const groundFloorRooms = ['Room 1', 'Room 2', 'Room 3', 'Room 4', 'Room 5', 'Room 6'];
        let groundFloorUpdated = 0;
        
        for (const roomName of groundFloorRooms) {
            const [result] = await db.query(`
                UPDATE rooms 
                SET floor = 0, updated_at = NOW()
                WHERE room_number = ?
            `, [roomName]);
            
            if (result.affectedRows > 0) {
                console.log(`   ✅ Updated ${roomName} to ground floor`);
                groundFloorUpdated++;
            } else {
                console.log(`   ⚠️  ${roomName} not found`);
            }
        }

        // 3. Update rooms 7-20 to first floor (1)
        console.log('\n3. Updating rooms 7-20 to first floor (1)...');
        
        const firstFloorRooms = [];
        for (let i = 7; i <= 20; i++) {
            firstFloorRooms.push(`Room ${i}`);
        }
        
        let firstFloorUpdated = 0;
        
        for (const roomName of firstFloorRooms) {
            const [result] = await db.query(`
                UPDATE rooms 
                SET floor = 1, updated_at = NOW()
                WHERE room_number = ?
            `, [roomName]);
            
            if (result.affectedRows > 0) {
                console.log(`   ✅ Updated ${roomName} to first floor`);
                firstFloorUpdated++;
            } else {
                console.log(`   ⚠️  ${roomName} not found`);
            }
        }

        // 4. Update labs and offices floor information
        console.log('\n4. Updating labs and offices floor information...');
        
        const specialRoomsFloorMapping = {
            // Ground floor facilities (0)
            'Computer Lab 1': 0,
            'Computer Lab 2': 0,
            'Biology Lab': 0,
            'Chemistry Lab': 0,
            'Physics Lab': 0,
            'Library': 0,
            
            // First floor facilities (1)
            'Principal Office': 1,
            'Vice Principal Office': 1,
            'Admin Office': 1,
            'Computer Office': 1,
            
            // Second floor facilities (2)
            'Boys Warden Office': 2,
            'Girls Warden Office': 2,
            
            // Hostel facilities (1)
            'Boys Hostel Warden Room': 1,
            'Girls Hostel Warden Room': 1
        };
        
        let specialRoomsUpdated = 0;
        
        for (const [roomName, floorNumber] of Object.entries(specialRoomsFloorMapping)) {
            const [result] = await db.query(`
                UPDATE rooms 
                SET floor = ?, updated_at = NOW()
                WHERE room_number = ?
            `, [floorNumber, roomName]);
            
            if (result.affectedRows > 0) {
                console.log(`   ✅ Updated ${roomName} to floor ${floorNumber}`);
                specialRoomsUpdated++;
            } else {
                console.log(`   ⚠️  ${roomName} not found`);
            }
        }

        // 5. Verify the updates
        console.log('\n5. Verifying floor updates...');
        
        const [updatedRooms] = await db.query(`
            SELECT room_number, building, floor,
                   CASE 
                     WHEN floor = 0 THEN 'Ground Floor'
                     WHEN floor = 1 THEN 'First Floor'
                     WHEN floor = 2 THEN 'Second Floor'
                     ELSE 'Unknown Floor'
                   END as floor_name
            FROM rooms 
            WHERE room_number LIKE 'Room %' OR room_number LIKE '%Lab%' OR room_number LIKE '%Office%'
            ORDER BY floor, room_number
        `);
        
        console.log('\n   📊 Updated room floor distribution:');
        
        const floorGroups = {};
        updatedRooms.forEach(room => {
            const floorName = room.floor_name;
            if (!floorGroups[floorName]) {
                floorGroups[floorName] = [];
            }
            floorGroups[floorName].push(room);
        });
        
        Object.entries(floorGroups).forEach(([floorName, rooms]) => {
            console.log(`\n   🏢 ${floorName} (${rooms.length} rooms):`);
            rooms.forEach(room => {
                console.log(`      • ${room.room_number} (${room.building || 'Main Building'})`);
            });
        });

        // 6. Update infrastructure view data with floor information
        console.log('\n6. Testing infrastructure view with updated floor data...');
        
        const [infrastructureData] = await db.query(`
            SELECT 
                r.room_number,
                r.building,
                r.floor,
                CASE 
                  WHEN r.floor = 0 THEN 'Ground Floor'
                  WHEN r.floor = 1 THEN 'First Floor'
                  WHEN r.floor = 2 THEN 'Second Floor'
                  ELSE 'Unknown Floor'
                END as floor_display,
                COUNT(DISTINCT i.item_id) as total_equipment
            FROM rooms r
            LEFT JOIN inventory_items i ON r.id = i.room_id
            WHERE r.room_number LIKE 'Computer Lab%' OR r.room_number LIKE '%Lab%' OR r.room_number LIKE '%Office%'
            GROUP BY r.id, r.room_number, r.building, r.floor
            ORDER BY r.floor, r.room_number
        `);
        
        console.log('\n   📋 Infrastructure view floor display:');
        infrastructureData.forEach(room => {
            console.log(`      • ${room.room_number}: ${room.floor_display} (${room.total_equipment} items)`);
        });

        console.log('\n✅ Migration completed successfully!');
        console.log('\n🎯 FLOOR DISTRIBUTION SUMMARY:');
        console.log(`   • Ground Floor (0): Rooms 1-6, Computer Labs, Science Labs, Library`);
        console.log(`   • First Floor (1): Rooms 7-20, Hostel Warden Rooms`);
        console.log(`   • Second Floor (2): Administrative Offices, Warden Offices`);
        console.log(`   • Total rooms updated: ${groundFloorUpdated + firstFloorUpdated + specialRoomsUpdated}`);
        
        console.log('\n📊 Infrastructure Impact:');
        console.log('   • Principal infrastructure view will show correct floor information');
        console.log('   • Room cards will display accurate floor numbers');
        console.log('   • Equipment modals will show proper floor details');
        console.log('   • Floor-based filtering and organization enabled');

        return {
            success: true,
            groundFloorUpdated,
            firstFloorUpdated,
            specialRoomsUpdated,
            totalUpdated: groundFloorUpdated + firstFloorUpdated + specialRoomsUpdated
        };

    } catch (error) {
        console.error('❌ Error updating room floor information:', error);
        throw error;
    }
}

// Run migration if this script is executed directly
if (require.main === module) {
    updateRoomFloorInformation()
        .then((results) => {
            console.log('\n🎯 Room floor information updated successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

module.exports = { updateRoomFloorInformation };
