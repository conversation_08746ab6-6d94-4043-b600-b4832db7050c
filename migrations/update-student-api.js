/**
 * Migration to update the student API to use the new classroom relationship
 */

const fs = require('fs');
const path = require('path');

async function updateStudentApi() {
  try {
    console.log('Updating student API to use the new classroom relationship...');
    
    const studentApiPath = path.join(__dirname, '..', 'routes', 'api', 'student-api.js');
    
    // Check if the file exists
    if (!fs.existsSync(studentApiPath)) {
      console.error('Student API file not found at:', studentApiPath);
      return false;
    }
    
    // Read the current file
    let studentApiContent = fs.readFileSync(studentApiPath, 'utf8');
    
    // Update the class-info endpoint
    const oldClassInfoQuery = `
    // Get student's class information with grade and section
    const [classInfo] = await db.query(\`
      SELECT
        c.id, c.name, c.grade, c.trade, c.section, c.academic_year,
        CONCAT(c.grade, ' ', c.name, ' ', c.section) as full_class_name
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
      LIMIT 1
    \`, [studentId]);`;
    
    const newClassInfoQuery = `
    // Get student's classroom information with grade, section, and trade
    const [classInfo] = await db.query(\`
      SELECT
        cr.id as classroom_id, c.id as class_id, c.name, c.grade, 
        cr.section, t.name as trade_name, cr.session as academic_year,
        cr.room_number,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    \`, [studentId]);
    
    // If no classroom found, try the old way with student_classes
    if (classInfo.length === 0) {
      const [oldClassInfo] = await db.query(\`
        SELECT
          c.id, c.name, c.grade, c.trade, c.section, c.academic_year,
          CONCAT(c.grade, ' ', c.name, ' ', c.section) as full_class_name
        FROM classes c
        JOIN student_classes sc ON c.id = sc.class_id
        WHERE sc.student_id = ?
        LIMIT 1
      \`, [studentId]);
      
      if (oldClassInfo.length > 0) {
        classInfo.push(oldClassInfo[0]);
      }
    }`;
    
    // Replace the old query with the new one
    studentApiContent = studentApiContent.replace(oldClassInfoQuery, newClassInfoQuery);
    
    // Add new endpoint for student subjects by trade
    const newEndpoint = `
// Get student subjects by trade
router.get('/subjects-by-trade', async (req, res) => {
  try {
    const studentId = req.session.userId;
    
    // Get student's trade from classroom
    const [studentTrade] = await db.query(\`
      SELECT
        t.id as trade_id, t.name as trade_name
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    \`, [studentId]);
    
    if (studentTrade.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student trade not found'
      });
    }
    
    const tradeId = studentTrade[0].trade_id;
    
    // Get subjects for this trade
    const [subjects] = await db.query(\`
      SELECT
        s.id, s.name, s.code, s.description,
        stc.is_compulsory, stc.is_elective,
        CASE WHEN ss.id IS NOT NULL THEN 1 ELSE 0 END as is_enrolled
      FROM subject_trade_combinations stc
      JOIN subjects s ON stc.subject_id = s.id
      LEFT JOIN student_subjects ss ON ss.subject_id = s.id AND ss.student_id = ?
      WHERE stc.trade_id = ?
      ORDER BY stc.is_compulsory DESC, s.name ASC
    \`, [studentId, tradeId]);
    
    res.json({
      success: true,
      trade: studentTrade[0],
      subjects
    });
  } catch (error) {
    console.error('Error fetching student subjects by trade:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student subjects by trade'
    });
  }
});`;
    
    // Add the new endpoint before the module.exports line
    const moduleExportsLine = 'module.exports = router;';
    studentApiContent = studentApiContent.replace(
      moduleExportsLine,
      `${newEndpoint}\n\n${moduleExportsLine}`
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(studentApiPath, studentApiContent, 'utf8');
    
    console.log('Student API updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating student API:', error);
    return false;
  }
}

// Execute the migration
updateStudentApi()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
