/**
 * Migration to update student-classroom relationships
 * - Creates student_classrooms table for one-to-one relationship
 * - Updates student_subjects to link with trades
 */

const db = require('../config/database');

async function updateStudentClassroomRelationships() {
  try {
    console.log('Creating student_classrooms table for one-to-one relationship...');
    
    // Create student_classrooms table
    await db.query(`
      CREATE TABLE IF NOT EXISTS student_classrooms (
        id INT PRIMARY KEY AUTO_INCREMENT,
        student_id INT NOT NULL,
        classroom_id INT NOT NULL,
        enrollment_date DATE DEFAULT (CURRENT_DATE),
        status ENUM('active', 'inactive', 'transferred', 'graduated') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
        UNIQUE KEY unique_student_classroom (student_id, classroom_id)
      )
    `);
    
    console.log('Student_classrooms table created successfully');
    
    // Add trade_id to student_subjects table if it doesn't exist
    const [columns] = await db.query('SHOW COLUMNS FROM student_subjects LIKE "trade_id"');
    if (columns.length === 0) {
      console.log('Adding trade_id to student_subjects table...');
      await db.query(`
        ALTER TABLE student_subjects
        ADD COLUMN trade_id INT,
        ADD FOREIGN KEY (trade_id) REFERENCES trades(id)
      `);
      console.log('Added trade_id to student_subjects table');
    } else {
      console.log('trade_id column already exists in student_subjects table');
    }
    
    // Create subject_trade_combinations table
    console.log('Creating subject_trade_combinations table...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS subject_trade_combinations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        trade_id INT NOT NULL,
        subject_id INT NOT NULL,
        is_compulsory TINYINT(1) DEFAULT 1,
        is_elective TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_trade_subject (trade_id, subject_id)
      )
    `);
    
    console.log('Subject_trade_combinations table created successfully');
    
    // Add default subject-trade combinations
    console.log('Adding default subject-trade combinations...');
    
    // Get trade IDs
    const [trades] = await db.query('SELECT id, name FROM trades');
    const tradeMap = {};
    trades.forEach(trade => {
      tradeMap[trade.name.toUpperCase()] = trade.id;
    });
    
    // Get subject IDs
    const [subjects] = await db.query('SELECT id, name FROM subjects');
    const subjectMap = {};
    subjects.forEach(subject => {
      subjectMap[subject.name.toUpperCase()] = subject.id;
    });
    
    // Define trade-subject combinations
    const combinations = [
      // NON-MEDICAL
      { trade: 'NON MEDICAL', subject: 'PHYSICS', compulsory: 1 },
      { trade: 'NON MEDICAL', subject: 'CHEMISTRY', compulsory: 1 },
      { trade: 'NON MEDICAL', subject: 'MATHEMATICS', compulsory: 1 },
      { trade: 'NON MEDICAL', subject: 'ENGLISH', compulsory: 1 },
      { trade: 'NON MEDICAL', subject: 'COMPUTER SCIENCE', compulsory: 0, elective: 1 },
      
      // MEDICAL
      { trade: 'MEDICAL', subject: 'PHYSICS', compulsory: 1 },
      { trade: 'MEDICAL', subject: 'CHEMISTRY', compulsory: 1 },
      { trade: 'MEDICAL', subject: 'BIOLOGY', compulsory: 1 },
      { trade: 'MEDICAL', subject: 'ENGLISH', compulsory: 1 },
      
      // COMMERCE
      { trade: 'COMMERCE', subject: 'ACCOUNTANCY', compulsory: 1 },
      { trade: 'COMMERCE', subject: 'BUSINESS STUDIES', compulsory: 1 },
      { trade: 'COMMERCE', subject: 'ECONOMICS', compulsory: 1 },
      { trade: 'COMMERCE', subject: 'ENGLISH', compulsory: 1 },
      { trade: 'COMMERCE', subject: 'MATHEMATICS', compulsory: 0, elective: 1 },
      
      // ARTS
      { trade: 'ARTS', subject: 'HISTORY', compulsory: 1 },
      { trade: 'ARTS', subject: 'POLITICAL SCIENCE', compulsory: 1 },
      { trade: 'ARTS', subject: 'GEOGRAPHY', compulsory: 0, elective: 1 },
      { trade: 'ARTS', subject: 'ENGLISH', compulsory: 1 },
      { trade: 'ARTS', subject: 'ECONOMICS', compulsory: 0, elective: 1 }
    ];
    
    // Insert missing subjects
    for (const combo of combinations) {
      if (!subjectMap[combo.subject]) {
        console.log(`Creating missing subject: ${combo.subject}`);
        const [result] = await db.query(
          'INSERT INTO subjects (name, code, description) VALUES (?, ?, ?)',
          [combo.subject, combo.subject.substring(0, 3), `${combo.subject} for senior secondary`]
        );
        subjectMap[combo.subject] = result.insertId;
      }
    }
    
    // Insert combinations
    for (const combo of combinations) {
      const tradeId = tradeMap[combo.trade];
      const subjectId = subjectMap[combo.subject];
      
      if (tradeId && subjectId) {
        const [existing] = await db.query(
          'SELECT id FROM subject_trade_combinations WHERE trade_id = ? AND subject_id = ?',
          [tradeId, subjectId]
        );
        
        if (existing.length === 0) {
          await db.query(`
            INSERT INTO subject_trade_combinations 
            (trade_id, subject_id, is_compulsory, is_elective) 
            VALUES (?, ?, ?, ?)
          `, [tradeId, subjectId, combo.compulsory || 1, combo.elective || 0]);
          
          console.log(`Added ${combo.subject} to ${combo.trade} trade`);
        } else {
          console.log(`Combination of ${combo.subject} and ${combo.trade} already exists`);
        }
      } else {
        console.log(`Could not find trade ${combo.trade} or subject ${combo.subject}`);
      }
    }
    
    console.log('Migration completed successfully');
    return true;
  } catch (error) {
    console.error('Error in migration:', error);
    return false;
  }
}

// Execute the migration
updateStudentClassroomRelationships()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
