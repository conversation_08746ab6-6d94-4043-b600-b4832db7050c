/**
 * Migration to update student subjects and create instruction plans
 */

const db = require('../config/database');

async function updateStudentSubjectsAndPlans() {
  try {
    console.log('Updating student subjects and creating instruction plans...');

    // Get the demo student (csstudent)
    const [student] = await db.query(`
      SELECT u.id, u.username, sc.classroom_id, cr.class_id, c.name as class_name,
             cr.trade_id, t.name as trade_name
      FROM users u
      JOIN student_classrooms sc ON u.id = sc.student_id
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE u.username = 'csstudent'
    `);

    if (student.length === 0) {
      console.error('Demo student (csstudent) not found');
      return false;
    }

    const studentId = student[0].id;
    const classroomId = student[0].classroom_id;
    const classId = student[0].class_id;
    const tradeId = student[0].trade_id;
    const tradeName = student[0].trade_name;

    console.log(`Found student: ${student[0].username}, Trade: ${tradeName}, Class: ${student[0].class_name}`);

    // Clear existing student subjects that don't have a trade_id
    await db.query(`
      DELETE FROM student_subjects
      WHERE student_id = ? AND (trade_id IS NULL OR trade_id != ?)
    `, [studentId, tradeId]);

    console.log('Cleared outdated student subjects');

    // Get all subjects for the ARTS trade
    const [subjects] = await db.query(`
      SELECT s.id, s.name, s.code, s.description, s.subject_group,
             stc.is_compulsory, stc.is_elective
      FROM subjects s
      JOIN subject_trade_combinations stc ON s.id = stc.subject_id
      WHERE stc.trade_id = ?
      ORDER BY s.subject_group, s.name
    `, [tradeId]);

    console.log(`Found ${subjects.length} subjects for ${tradeName} trade`);

    // Group subjects by subject_group
    const subjectGroups = {};
    subjects.forEach(subject => {
      if (!subjectGroups[subject.subject_group]) {
        subjectGroups[subject.subject_group] = [];
      }
      subjectGroups[subject.subject_group].push(subject);
    });

    // Select subjects for the student based on the Arts curriculum
    const selectedSubjects = [];

    // Group 1: Select one language (English)
    const group1 = subjectGroups[1] || [];
    const englishSubject = group1.find(s => s.name === 'ENGLISH');
    if (englishSubject) {
      selectedSubjects.push(englishSubject);
    }

    // Group 2: Select one classical/foreign language (Sanskrit)
    const group2 = subjectGroups[2] || [];
    const sanskritSubject = group2.find(s => s.name === 'SANSKRIT');
    if (sanskritSubject) {
      selectedSubjects.push(sanskritSubject);
    }

    // Group 3: History (compulsory)
    const group3 = subjectGroups[3] || [];
    if (group3.length > 0) {
      selectedSubjects.push(group3[0]);
    }

    // Group 4: Economics
    const group4 = subjectGroups[4] || [];
    const economicsSubject = group4.find(s => s.name === 'ECONOMICS');
    if (economicsSubject) {
      selectedSubjects.push(economicsSubject);
    }

    // Group 6: Political Science (compulsory)
    const group6 = subjectGroups[6] || [];
    if (group6.length > 0) {
      selectedSubjects.push(group6[0]);
    }

    // Group 11: Geography
    const group11 = subjectGroups[11] || [];
    if (group11.length > 0) {
      selectedSubjects.push(group11[0]);
    }

    // Additional subjects: Computer Science and Welcome Life
    const group12 = subjectGroups[12] || [];
    const csSubject = group12.find(s => s.name === 'COMPUTER SCIENCE');
    if (csSubject) {
      selectedSubjects.push(csSubject);
    }

    const group13 = subjectGroups[13] || [];
    const wlSubject = group13.find(s => s.name === 'WELCOME LIFE');
    if (wlSubject) {
      selectedSubjects.push(wlSubject);
    }

    console.log(`Selected ${selectedSubjects.length} subjects for the student`);

    // Assign selected subjects to the student
    for (const subject of selectedSubjects) {
      // Check if student already has this subject
      const [existingSubject] = await db.query(`
        SELECT id FROM student_subjects
        WHERE student_id = ? AND subject_id = ?
      `, [studentId, subject.id]);

      if (existingSubject.length === 0) {
        // Assign new subject
        await db.query(`
          INSERT INTO student_subjects (student_id, subject_id, trade_id)
          VALUES (?, ?, ?)
        `, [studentId, subject.id, tradeId]);

        console.log(`Assigned subject ${subject.name} to student`);
      } else {
        // Update existing subject
        await db.query(`
          UPDATE student_subjects
          SET trade_id = ?
          WHERE id = ?
        `, [tradeId, existingSubject[0].id]);

        console.log(`Updated subject ${subject.name} for student`);
      }
    }

    // Create instruction plans for each subject
    console.log('Creating instruction plans for each subject...');

    // Get teachers
    const [teachers] = await db.query(`
      SELECT id, username, full_name
      FROM users
      WHERE role = 'teacher'
      LIMIT 10
    `);

    if (teachers.length === 0) {
      console.log('No teachers found, creating sample teachers...');

      // Create sample teachers
      for (let i = 1; i <= 5; i++) {
        await db.query(`
          INSERT INTO users (username, email, password, role, full_name)
          VALUES (?, ?, ?, 'teacher', ?)
        `, [`arts_teacher${i}`, `arts_teacher${i}@example.com`, '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQaCWXD8k.2JNRrQ0FBGzNLDLUNPke', `Arts Teacher ${i}`]);
      }

      const [newTeachers] = await db.query(`
        SELECT id, username, full_name
        FROM users
        WHERE role = 'teacher' AND username LIKE 'arts_teacher%'
        LIMIT 5
      `);

      if (newTeachers.length > 0) {
        console.log(`Created ${newTeachers.length} sample teachers`);
        var availableTeachers = newTeachers;
      } else {
        console.error('Failed to create sample teachers');
        return false;
      }
    } else {
      var availableTeachers = teachers;
    }

    // Create instruction plans for each subject
    for (const subject of selectedSubjects) {
      // Assign a random teacher
      const teacher = availableTeachers[Math.floor(Math.random() * availableTeachers.length)];

      // Create 2 instruction plans per subject
      for (let i = 1; i <= 2; i++) {
        const title = `${subject.name} - Topic ${i}`;
        const description = `Instruction plan for ${subject.name} covering Topic ${i}`;

        // Check if plan already exists
        const [existingPlan] = await db.query(`
          SELECT id FROM instruction_plans
          WHERE title = ? AND subject_id = ? AND teacher_id = ?
        `, [title, subject.id, teacher.id]);

        if (existingPlan.length === 0) {
          // Create new plan
          const [result] = await db.query(`
            INSERT INTO instruction_plans (title, description, subject_id, class_id, teacher_id, status)
            VALUES (?, ?, ?, ?, ?, 'published')
          `, [title, description, subject.id, classId, teacher.id]);

          console.log(`Created instruction plan: ${title}`);

          // Create plan content
          await db.query(`
            INSERT INTO instruction_plan_content (plan_id, title, content_type, content, order_index)
            VALUES (?, ?, 'text', ?, 1)
          `, [result.insertId, title, `# ${title}\n\n${description}\n\n## Learning Objectives\n\n- Understand key concepts\n- Apply knowledge to practical scenarios\n- Analyze and evaluate information`]);

          console.log(`Added content to instruction plan: ${title}`);
        } else {
          console.log(`Instruction plan already exists: ${title}`);
        }
      }
    }

    console.log('Student subjects and instruction plans updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating student subjects and plans:', error);
    return false;
  }
}

// Execute the migration
updateStudentSubjectsAndPlans()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
