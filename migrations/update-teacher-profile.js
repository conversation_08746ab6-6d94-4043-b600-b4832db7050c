/**
 * Migration to update the teacher profile
 * This script updates the demo teacher profile with computer science specialization
 */

const db = require('../config/database');

async function updateTeacherProfile() {
  try {
    console.log('Updating teacher profile...');

    // Get the demo teacher (<EMAIL>)
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Update teacher profile with computer science specialization
    await db.query(`
      UPDATE users
      SET
        subjects = 'Computer Science, Mathematics, Physics',
        bio = 'Experienced computer science teacher with 10+ years of experience teaching programming, data structures, and algorithms. Passionate about helping students develop problem-solving skills and computational thinking.',
        field_of_study = 'M.Tech in Computer Science, B.Tech in Information Technology, Certified Educator',
        full_name = '<PERSON><PERSON>'
      WHERE id = ?
    `, [teacherId]);

    console.log('Updated teacher profile');

    // Create teacher_lectures table if it doesn't exist
    const [teacherLecturesTable] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (teacherLecturesTable.length === 0) {
      await db.query(`
        CREATE TABLE teacher_lectures (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          slot_index INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          section_display VARCHAR(10),
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255),
          grade VARCHAR(10),
          streamCode VARCHAR(10),
          sectionLetter VARCHAR(5),
          stream VARCHAR(50),
          location VARCHAR(100),
          status ENUM('pending', 'delivered', 'cancelled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          class_section_id INT,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('Created teacher_lectures table');
    }

    // Generate some upcoming lectures
    console.log('Generating upcoming lectures...');

    // Clear existing upcoming lectures
    await db.query(`
      DELETE FROM teacher_lectures
      WHERE teacher_id = ? AND date >= CURDATE()
    `, [teacherId]);

    // Get next Monday's date
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);

    // Generate lectures for the next 5 days
    for (let dayOffset = 0; dayOffset < 5; dayOffset++) {
      const lectureDate = new Date(nextMonday);
      lectureDate.setDate(nextMonday.getDate() + dayOffset);

      // Format date as YYYY-MM-DD
      const formattedDate = lectureDate.toISOString().split('T')[0];

      // Generate 6 lectures per day
      for (let period = 1; period <= 6; period++) {
        // Define lecture times
        const lectureSchedule = [
          { start_time: '08:00:00', end_time: '08:40:00' },
          { start_time: '08:45:00', end_time: '09:25:00' },
          { start_time: '09:30:00', end_time: '10:10:00' },
          { start_time: '10:15:00', end_time: '10:55:00' },
          { start_time: '11:00:00', end_time: '11:40:00' },
          { start_time: '11:45:00', end_time: '12:25:00' }
        ];

        // Define subjects
        const subjects = [
          'Computer Science',
          'Mathematics',
          'Physics',
          'Information Practices',
          'Web Technology',
          'Programming in Python'
        ];

        // Define classes
        const classes = [
          { grade: '11', section: 'A' },
          { grade: '11', section: 'B' },
          { grade: '12', section: 'A' },
          { grade: '12', section: 'B' }
        ];

        // Generate a topic based on subject
        const csTopics = [
          'Introduction to Programming',
          'Data Types and Variables',
          'Control Structures',
          'Functions and Methods',
          'Object-Oriented Programming',
          'Data Structures',
          'Algorithms',
          'File Handling',
          'Database Connectivity',
          'Web Development Basics',
          'Network Programming',
          'GUI Development'
        ];

        // Select random subject, class, and topic
        const subject = subjects[Math.floor(Math.random() * subjects.length)];
        const cls = classes[Math.floor(Math.random() * classes.length)];
        const topic = csTopics[Math.floor(Math.random() * csTopics.length)];
        const periodInfo = lectureSchedule[period - 1];

        await db.query(`
          INSERT INTO teacher_lectures (
            teacher_id, date, start_time, end_time, slot_index,
            class_name, section_display, subject_name, topic,
            grade, stream, location, status
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId, formattedDate, periodInfo.start_time, periodInfo.end_time, period - 1,
          `Class ${cls.grade}`, cls.section, subject, topic,
          cls.grade, 'NON-MEDICAL', `Room ${cls.grade}${cls.section}`,
          Math.random() > 0.7 ? 'delivered' : 'pending'
        ]);

        console.log(`Created lecture for ${formattedDate}, period ${period}, subject ${subject}`);
      }
    }

    console.log('Teacher profile updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating teacher profile:', error);
    return false;
  }
}

// Execute the migration
updateTeacherProfile()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
