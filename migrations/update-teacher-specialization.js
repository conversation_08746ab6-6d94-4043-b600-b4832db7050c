/**
 * Migration to update teacher_specialization table to better handle primary and secondary subjects
 */

const db = require('../config/database');

async function updateTeacherSpecialization() {
  try {
    console.log('Updating teacher_specialization table...');

    // Check if teacher_specialization table exists
    const [tableExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'teacher_specialization'
    `);

    if (tableExists[0].table_exists === 0) {
      console.log('teacher_specialization table does not exist, creating it...');

      // Create teacher_specialization table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_specialization (
          id INT PRIMARY KEY AUTO_INCREMENT,
          teacher_id INT NOT NULL,
          specialization ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics',
                             'English', 'Hindi', 'Social Science', 'Economics', 'Commerce',
                             'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
          is_primary BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE KEY unique_teacher_specialization (teacher_id, specialization)
        )
      `);

      console.log('teacher_specialization table created successfully');
    }

    // Check if subject_category table exists
    const [subjectCategoryExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'subject_category'
    `);

    if (subjectCategoryExists[0].table_exists === 0) {
      console.log('subject_category table does not exist, creating it...');

      // Create subject_category table
      await db.query(`
        CREATE TABLE IF NOT EXISTS subject_category (
          id INT PRIMARY KEY AUTO_INCREMENT,
          subject_id INT NOT NULL,
          category ENUM('Computer Science', 'Physics', 'Chemistry', 'Biology', 'Mathematics',
                       'English', 'Hindi', 'Social Science', 'Economics', 'Commerce',
                       'Physical Education', 'Arts', 'Music', 'Other') NOT NULL,
          is_primary BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          UNIQUE KEY unique_subject_category (subject_id, category)
        )
      `);

      console.log('subject_category table created successfully');
    } else {
      // Check if is_primary column exists in subject_category table
      const [isPrimaryColumnExists] = await db.query(`
        SELECT COUNT(*) as column_exists
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'subject_category'
        AND column_name = 'is_primary'
      `);

      if (isPrimaryColumnExists[0].column_exists === 0) {
        console.log('Adding is_primary column to subject_category table...');

        // Add is_primary column to subject_category table
        await db.query(`
          ALTER TABLE subject_category
          ADD COLUMN is_primary BOOLEAN DEFAULT TRUE AFTER category
        `);

        console.log('is_primary column added to subject_category table');
      }
    }

    // Find computer science teachers
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_subjects ts ON u.id = ts.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      WHERE u.role = 'teacher'
      AND (
        s.name LIKE '%computer%'
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `);

    if (computerTeachers.length > 0) {
      console.log(`Found ${computerTeachers.length} computer teachers, updating their specializations...`);

      // Find computer science subjects
      const [computerSubjects] = await db.query(`
        SELECT id, name, code
        FROM subjects
        WHERE name LIKE '%computer%'
        OR name LIKE '%Computer%'
        OR code LIKE '%CS%'
        OR code LIKE '%comp%'
      `);

      if (computerSubjects.length > 0) {
        console.log(`Found ${computerSubjects.length} computer science subjects`);

        // Categorize computer science subjects
        for (const subject of computerSubjects) {
          // Check if subject already has a category
          const [existingCategory] = await db.query(`
            SELECT id FROM subject_category WHERE subject_id = ?
          `, [subject.id]);

          if (existingCategory.length === 0) {
            await db.query(`
              INSERT INTO subject_category (subject_id, category, is_primary)
              VALUES (?, 'Computer Science', TRUE)
            `, [subject.id]);

            console.log(`Categorized subject ${subject.name} (ID: ${subject.id}) as Computer Science (primary)`);
          }
        }

        // Set computer science specialization for computer teachers
        for (const teacher of computerTeachers) {
          // Check if teacher already has a specialization
          const [existingSpecialization] = await db.query(`
            SELECT id FROM teacher_specialization WHERE teacher_id = ? AND specialization = 'Computer Science'
          `, [teacher.id]);

          if (existingSpecialization.length === 0) {
            await db.query(`
              INSERT INTO teacher_specialization (teacher_id, specialization, is_primary)
              VALUES (?, 'Computer Science', TRUE)
            `, [teacher.id]);

            console.log(`Set primary specialization for teacher ${teacher.full_name} (ID: ${teacher.id}) as Computer Science`);
          } else {
            // Update existing specialization to ensure it's marked as primary
            await db.query(`
              UPDATE teacher_specialization
              SET is_primary = TRUE
              WHERE teacher_id = ? AND specialization = 'Computer Science'
            `, [teacher.id]);

            console.log(`Updated specialization for teacher ${teacher.full_name} (ID: ${teacher.id}) to primary`);
          }

          // Get all subjects taught by this teacher
          const [teacherSubjects] = await db.query(`
            SELECT ts.id, s.id AS subject_id, s.name, s.code
            FROM teacher_subjects ts
            JOIN subjects s ON ts.subject_id = s.id
            WHERE ts.teacher_id = ?
          `, [teacher.id]);

          // Add secondary specializations for non-computer subjects
          for (const subject of teacherSubjects) {
            const isComputerSubject =
              subject.name.toLowerCase().includes('computer') ||
              subject.name.toLowerCase().includes('programming') ||
              subject.code.includes('CS') ||
              subject.code.toLowerCase().includes('comp');

            if (!isComputerSubject) {
              // Determine the category based on subject name
              let category = 'Other';
              if (subject.name.toLowerCase().includes('physics')) category = 'Physics';
              else if (subject.name.toLowerCase().includes('chemistry')) category = 'Chemistry';
              else if (subject.name.toLowerCase().includes('biology')) category = 'Biology';
              else if (subject.name.toLowerCase().includes('math')) category = 'Mathematics';
              else if (subject.name.toLowerCase().includes('english')) category = 'English';
              else if (subject.name.toLowerCase().includes('hindi')) category = 'Hindi';
              else if (subject.name.toLowerCase().includes('social')) category = 'Social Science';
              else if (subject.name.toLowerCase().includes('economics')) category = 'Economics';
              else if (subject.name.toLowerCase().includes('commerce')) category = 'Commerce';
              else if (subject.name.toLowerCase().includes('physical')) category = 'Physical Education';
              else if (subject.name.toLowerCase().includes('art')) category = 'Arts';
              else if (subject.name.toLowerCase().includes('music')) category = 'Music';

              // Check if subject already has a category
              const [existingSubjectCategory] = await db.query(`
                SELECT id FROM subject_category WHERE subject_id = ?
              `, [subject.subject_id]);

              if (existingSubjectCategory.length === 0) {
                await db.query(`
                  INSERT INTO subject_category (subject_id, category, is_primary)
                  VALUES (?, ?, FALSE)
                `, [subject.subject_id, category]);

                console.log(`Categorized subject ${subject.name} (ID: ${subject.subject_id}) as ${category} (secondary)`);
              }

              // Check if teacher already has this specialization
              const [existingSpecialization] = await db.query(`
                SELECT id FROM teacher_specialization WHERE teacher_id = ? AND specialization = ?
              `, [teacher.id, category]);

              if (existingSpecialization.length === 0) {
                await db.query(`
                  INSERT INTO teacher_specialization (teacher_id, specialization, is_primary)
                  VALUES (?, ?, FALSE)
                `, [teacher.id, category]);

                console.log(`Set secondary specialization for teacher ${teacher.full_name} (ID: ${teacher.id}) as ${category}`);
              }
            }
          }
        }
      }
    }

    console.log('Teacher specialization update completed');
    return true;
  } catch (error) {
    console.error('Error updating teacher specialization:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  updateTeacherSpecialization()
    .then(() => {
      console.log('Teacher specialization update completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Teacher specialization update failed:', err);
      process.exit(1);
    });
}

module.exports = updateTeacherSpecialization;
