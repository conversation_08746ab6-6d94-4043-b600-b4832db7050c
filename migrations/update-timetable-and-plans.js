/**
 * Migration to update the timetable and instruction plans
 * This script updates the timetable to include 5 classes per week (2 theory, 2 practical)
 * and implements seasonal timing changes and summer break
 */

const db = require('../config/database');

// Helper function to check if a date is in summer break (June 1-30)
function isSummerBreak(date) {
  const month = date.getMonth() + 1; // JavaScript months are 0-indexed
  const day = date.getDate();

  return month === 6; // June
}

// Helper function to check if a date is a holiday
function isHoliday(date) {
  const day = date.getDay();
  const dayOfMonth = date.getDate();
  const month = date.getMonth() + 1; // JavaScript months are 0-indexed

  // Sunday (0)
  if (day === 0) return true;

  // Second Saturday
  if (day === 6 && Math.floor((dayOfMonth - 1) / 7) === 1) return true;

  // Summer break
  if (isSummerBreak(date)) return true;

  // Indian holidays (simplified list)
  const holidays = [
    { day: 26, month: 1 }, // Republic Day
    { day: 15, month: 8 }, // Independence Day
    { day: 2, month: 10 }, // <PERSON>
    { day: 25, month: 12 }, // Christmas
    { day: 1, month: 5 }, // Labor Day
    { day: 15, month: 1 }, // Makar Sankranti
    { day: 26, month: 11 }, // Constitution Day
    { day: 14, month: 4 }, // Ambedkar Jayanti
    { day: 5, month: 9 }, // Teachers' Day
    { day: 2, month: 4 }, // Ram Navami
    { day: 19, month: 10 }, // Dussehra
    { day: 12, month: 11 }, // Diwali
    { day: 18, month: 3 }, // Holi
    { day: 21, month: 8 }, // Raksha Bandhan
    { day: 30, month: 8 }, // Janmashtami
  ];

  return holidays.some(holiday => holiday.day === dayOfMonth && holiday.month === month);
}

// Helper function to get the school timings based on the date
function getSchoolTimings(date) {
  const month = date.getMonth() + 1; // JavaScript months are 0-indexed

  // Summer timings (April 1 to September 30)
  if (month >= 4 && month <= 9) {
    return {
      start: '08:00:00',
      end: '14:00:00'
    };
  }

  // October timings
  if (month === 10) {
    return {
      start: '08:30:00',
      end: '14:50:00'
    };
  }

  // Winter timings (November 1 to March 31)
  return {
    start: '09:00:00',
    end: '15:20:00'
  };
}

// Helper function to get period timings based on school timings
function getPeriodTimings(schoolTimings) {
  const startHour = parseInt(schoolTimings.start.split(':')[0]);
  const startMinute = parseInt(schoolTimings.start.split(':')[1]);

  // Create 8 periods with 40 minutes each and 5-minute breaks
  const periods = [];
  let currentHour = startHour;
  let currentMinute = startMinute;

  for (let i = 0; i < 8; i++) {
    const periodStart = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}:00`;

    // Add 40 minutes for the period
    currentMinute += 40;
    if (currentMinute >= 60) {
      currentHour += Math.floor(currentMinute / 60);
      currentMinute %= 60;
    }

    const periodEnd = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}:00`;

    periods.push({
      start: periodStart,
      end: periodEnd
    });

    // Add 5 minutes for the break
    currentMinute += 5;
    if (currentMinute >= 60) {
      currentHour += Math.floor(currentMinute / 60);
      currentMinute %= 60;
    }
  }

  return periods;
}

// Helper function to format date as YYYY-MM-DD
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

async function updateTimetableAndPlans() {
  try {
    console.log('Updating timetable and instruction plans...');

    // Get the demo teacher
    const [teacher] = await db.query(`
      SELECT id, username, full_name, email
      FROM users
      WHERE username = 'teacher' OR email = '<EMAIL>'
      LIMIT 1
    `);

    if (teacher.length === 0) {
      console.error('Demo teacher not found');
      return false;
    }

    const teacherId = teacher[0].id;
    console.log(`Found demo teacher: ${teacher[0].username}, ID: ${teacherId}`);

    // Get Computer Science subject
    const [subject] = await db.query(`
      SELECT id FROM subjects WHERE name = 'Computer Science'
    `);

    if (subject.length === 0) {
      console.error('Computer Science subject not found');
      return false;
    }

    const subjectId = subject[0].id;
    console.log(`Found Computer Science subject, ID: ${subjectId}`);

    // Get teacher's assigned classes
    const [teacherClasses] = await db.query(`
      SELECT
        tc.id,
        tc.classroom_id,
        c.id as class_id,
        c.grade,
        cr.section,
        t.name as trade_name,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM teacher_classes tc
      JOIN classrooms cr ON tc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE tc.teacher_id = ?
    `, [teacherId]);

    if (teacherClasses.length === 0) {
      console.error('No classes assigned to teacher');
      return false;
    }

    console.log(`Found ${teacherClasses.length} classes assigned to teacher`);

    // Get or create subject-class assignments
    const assignments = [];

    for (const cls of teacherClasses) {
      // Check if assignment exists
      const [existingAssignment] = await db.query(`
        SELECT id FROM subject_class_assignment
        WHERE class_id = ? AND subject_id = ?
      `, [cls.class_id, subjectId]);

      let assignmentId;

      if (existingAssignment.length > 0) {
        assignmentId = existingAssignment[0].id;
        console.log(`Found existing assignment for ${cls.full_class_name}, ID: ${assignmentId}`);
      } else {
        // Create new assignment
        const [result] = await db.query(`
          INSERT INTO subject_class_assignment (class_id, subject_id)
          VALUES (?, ?)
        `, [cls.class_id, subjectId]);

        assignmentId = result.insertId;
        console.log(`Created new assignment for ${cls.full_class_name}, ID: ${assignmentId}`);
      }

      assignments.push({
        id: assignmentId,
        class_id: cls.class_id,
        subject_id: subjectId,
        class_name: cls.full_class_name
      });
    }

    // Clear existing lecture schedule for the teacher
    await db.query(`
      DELETE FROM lecture_schedule
      WHERE teacher_id = ?
    `, [teacherId]);

    console.log('Cleared existing lecture schedule for the teacher');

    // Create weekly timetable for each class
    for (const assignment of assignments) {
      console.log(`Creating timetable for class ${assignment.class_name}`);

      // Assign 2 theory and 2 practical classes per week for each class
      // Monday - Theory (Period 1)
      // Tuesday - Practical (Period 3-4)
      // Wednesday - Theory (Period 2)
      // Thursday - Practical (Period 5-6)
      // Friday - Lab work/Project (Period 7-8)

      const schedule = [
        { day: 'Monday', period: 1, duration: 1, is_practical: false, topic: 'Theory' },
        { day: 'Tuesday', period: 3, duration: 2, is_practical: true, topic: 'Practical' },
        { day: 'Wednesday', period: 2, duration: 1, is_practical: false, topic: 'Theory' },
        { day: 'Thursday', period: 5, duration: 2, is_practical: true, topic: 'Practical' },
        { day: 'Friday', period: 7, duration: 2, is_practical: true, topic: 'Lab/Project' }
      ];

      for (const slot of schedule) {
        // Get period timings for a sample date in each season
        const summerDate = new Date('2025-05-15');
        const octoberDate = new Date('2025-10-15');
        const winterDate = new Date('2025-11-15');

        const summerTimings = getSchoolTimings(summerDate);
        const octoberTimings = getSchoolTimings(octoberDate);
        const winterTimings = getSchoolTimings(winterDate);

        const summerPeriods = getPeriodTimings(summerTimings);
        const octoberPeriods = getPeriodTimings(octoberTimings);
        const winterPeriods = getPeriodTimings(winterTimings);

        // Determine which period timings to use based on the current season
        let periodTimings;
        let semester;

        // April to September (Summer)
        if (slot.day === 'Monday' || slot.day === 'Wednesday') {
          periodTimings = summerPeriods;
          semester = 'Summer';
        }
        // October
        else if (slot.day === 'Tuesday') {
          periodTimings = octoberPeriods;
          semester = 'Fall';
        }
        // November to March (Winter)
        else {
          periodTimings = winterPeriods;
          semester = 'Winter';
        }

        // Insert into lecture_schedule
        await db.query(`
          INSERT INTO lecture_schedule (
            assignment_id, teacher_id, day_of_week, start_time, end_time,
            classroom, semester, is_active
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          assignment.id,
          teacherId,
          slot.day,
          periodTimings[slot.period - 1].start,
          periodTimings[slot.period + slot.duration - 2].end,
          `Room ${assignment.class_name}`,
          semester,
          1
        ]);

        console.log(`Created lecture schedule for ${assignment.class_name} on ${slot.day}, period ${slot.period}`);
      }
    }

    // Update instruction plans to mark summer break
    try {
      // Get all instruction plans
      const [instructionPlans] = await db.query(`
        SELECT id, description FROM instruction_plans
      `);

      // Update each instruction plan with summer break note
      for (const plan of instructionPlans) {
        await db.query(`
          UPDATE instruction_plans
          SET status = 'archived', description = CONCAT(IFNULL(description, ''), ' (Summer Break)')
          WHERE id = ?
        `, [plan.id]);
      }

      console.log(`Updated ${instructionPlans.length} instruction plans for summer break`);
    } catch (error) {
      console.error('Error updating instruction plans:', error.message);
    }

    // Update daily_instruction_plan to mark summer break
    try {
      await db.query(`
        UPDATE daily_instruction_plan
        SET is_active = 0, notes = CONCAT(IFNULL(notes, ''), ' (Summer Break)')
        WHERE DATE_FORMAT(date, '%m-%d') BETWEEN '06-01' AND '06-30'
      `);

      console.log('Updated daily instruction plans for summer break');
    } catch (error) {
      console.error('Error updating daily instruction plans:', error.message);
    }

    // Update teacher_lectures to mark summer break
    try {
      await db.query(`
        UPDATE teacher_lectures
        SET status = 'cancelled', notes = CONCAT(IFNULL(notes, ''), ' (Summer Break)')
        WHERE DATE_FORMAT(date, '%m-%d') BETWEEN '06-01' AND '06-30'
      `);

      console.log('Updated teacher lectures for summer break');
    } catch (error) {
      console.error('Error updating teacher lectures:', error.message);
    }

    // Update lecture_schedule to mark summer break
    try {
      await db.query(`
        UPDATE lecture_schedule
        SET is_active = 0
        WHERE semester = 'Summer'
      `);

      console.log('Updated lecture schedule for summer break');
    } catch (error) {
      console.error('Error updating lecture schedule:', error.message);
    }

    console.log('Timetable and instruction plans updated successfully');
    return true;
  } catch (error) {
    console.error('Error updating timetable and plans:', error);
    return false;
  }
}

// Execute the migration
updateTimetableAndPlans()
  .then(success => {
    if (success) {
      console.log('Migration completed successfully');
      process.exit(0);
    } else {
      console.error('Migration failed');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unhandled error in migration:', err);
    process.exit(1);
  });
