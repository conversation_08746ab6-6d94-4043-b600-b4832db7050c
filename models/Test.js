const mongoose = require('mongoose');

const testSchema = new mongoose.Schema({
    exam_name: { type: String, required: true },
    instructions: { type: String, required: true },
    duration: { type: Number, required: true },
    passing_score: { type: Number, required: true },
    sections: [{
        name: { type: String, required: true },
        questions: [{
            type: { type: String, enum: ['mcq', 'true_false', 'short_answer'], required: true },
            text: { type: String, required: true },
            solution: String,
            options: [{
                text: String,
                is_correct: Boolean
            }],
            correct_answer: String
        }]
    }],
    isDraft: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Test', testSchema);
