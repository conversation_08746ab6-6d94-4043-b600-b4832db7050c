{"name": "exam_prep_platform", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "npm run build:css && node index.js", "dev": "npm run build:css && nodemon index.js", "build:css": "npx tailwindcss -i ./public/tailwind.css -o ./public/styles.css", "create-admin": "node scripts/create-admin.js", "fix-db": "node scripts/fix-missing-tables.js", "create-notifications": "node scripts/create-notifications-table.js", "setup-admin": "node scripts/setup-admin-tables.js", "add-profile-image": "node scripts/add-profile-image-column.js", "fix-sections": "node scripts/fix-sections-table.js", "fix-users": "node scripts/fix-users-table.js", "fix-duplicate-sections": "node scripts/fix-duplicate-sections.js", "create-test-assignments": "node scripts/create-test-assignments-table.js", "add-is-approved": "node scripts/add-is-approved-column.js", "create-chat-tables": "node scripts/create-chat-tables.js", "fix-chat-tables": "node scripts/fix-chat-tables.js", "add-laptop-data": "node scripts/add-laptop-data.js", "update-laptop-conditions": "node scripts/update-laptop-conditions.js", "add-desktop-data": "node scripts/add-desktop-data.js", "add-ifpd-data": "node scripts/add-ifpd-inventory.js", "create-repair-vendor-tables": "node scripts/create-repair-vendor-tables.js", "add-repair-demo-data": "node scripts/add-repair-demo-data.js", "create-plan-collaborators": "node scripts/create-plan-collaborators-table.js", "check-tables": "node scripts/check-required-tables.js", "create-all-tables": "node scripts/create-all-missing-tables.js", "add-admin-collaborators": "node scripts/add-admin-collaborators.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"autoprefixer": "^10.4.16", "bcrypt": "^5.1.1", "chart.js": "^4.4.8", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.21.2", "express-ejs-extend": "^0.0.1", "express-ejs-layouts": "^2.5.1", "express-fileupload": "^1.5.1", "express-mysql-session": "^3.0.3", "express-session": "^1.18.1", "express-validator": "^7.2.1", "formidable": "^3.5.3", "html-pdf-node": "^1.0.8", "i18n": "^0.15.1", "joi": "^17.13.3", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "mysql2": "^3.13.0", "nodemailer": "^6.10.1", "pdfkit": "^0.16.0", "postcss": "^8.4.31", "puppeteer": "^24.7.1", "sharp": "^0.34.1", "socket.io": "^4.8.1", "tailwindcss": "^3.3.3", "uuid": "^11.1.0", "ws": "^8.18.1", "xlsx": "^0.18.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^6.3.3"}}