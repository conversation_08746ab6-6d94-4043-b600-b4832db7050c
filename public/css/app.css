/**
 * Consolidated Application CSS
 * 
 * This file combines common styles from multiple smaller CSS files
 * to reduce HTTP requests and improve performance.
 */

/* Session Timer Styles */
.session-timer-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.session-timer-container.warning {
  background-color: #FEF3C7;
  box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

.session-timer-container.danger {
  background-color: #FEE2E2;
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
}

.session-timer-icon {
  margin-right: 10px;
  color: #4B5563;
}

.session-timer-container.warning .session-timer-icon {
  color: #D97706;
}

.session-timer-container.danger .session-timer-icon {
  color: #DC2626;
}

.session-timer-text {
  font-weight: 500;
}

.session-timer-value {
  font-weight: 600;
  margin-left: 5px;
}

.session-timer-progress {
  height: 4px;
  background-color: #E5E7EB;
  border-radius: 2px;
  margin-top: 5px;
  overflow: hidden;
}

.session-timer-progress-bar {
  height: 100%;
  background-color: #10B981;
  border-radius: 2px;
  transition: width 1s linear;
}

.session-timer-container.warning .session-timer-progress-bar {
  background-color: #F59E0B;
}

.session-timer-container.danger .session-timer-progress-bar {
  background-color: #EF4444;
}

/* Toast Notification Styles */
.toastify {
  padding: 12px 20px;
  color: #ffffff;
  display: inline-block;
  box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #73a5ff, #5477f5);
  position: fixed;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 8px;
  cursor: pointer;
  text-decoration: none;
  max-width: calc(50% - 20px);
  z-index: 2147483647;
}

.toastify.on {
  opacity: 1;
}

.toast-close {
  opacity: 0.4;
  padding: 0 5px;
  margin-left: 10px;
  font-size: 16px;
}

.toastify-right {
  right: 15px;
}

.toastify-left {
  left: 15px;
}

.toastify-top {
  top: 15px;
}

.toastify-bottom {
  bottom: 15px;
}

.toastify-success {
  background: linear-gradient(135deg, #10B981, #059669);
}

.toastify-error {
  background: linear-gradient(135deg, #EF4444, #DC2626);
}

.toastify-warning {
  background: linear-gradient(135deg, #F59E0B, #D97706);
}

.toastify-info {
  background: linear-gradient(135deg, #3B82F6, #2563EB);
}

@media only screen and (max-width: 360px) {
  .toastify-right, .toastify-left {
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    max-width: fit-content;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  max-width: 90%;
  max-height: 90vh;
  width: 500px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
}

.modal-close {
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: #718096;
}

.modal-body {
  padding: 1rem;
  overflow-y: auto;
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  gap: 0.5rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #4a5568;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #4a5568;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #3b82f6;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.form-control.is-invalid {
  border-color: #ef4444;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #ef4444;
}

/* Responsive Tables */
.responsive-table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsive-table {
  width: 100%;
  border-collapse: collapse;
}

.responsive-table th,
.responsive-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #e2e8f0;
}

.responsive-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #e2e8f0;
  background-color: #f8fafc;
  font-weight: 600;
  color: #4a5568;
}

.responsive-table tbody tr:hover {
  background-color: #f1f5f9;
}

@media screen and (max-width: 640px) {
  .responsive-table-container {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .responsive-table {
    width: 100%;
    margin-bottom: 1rem;
  }
}
