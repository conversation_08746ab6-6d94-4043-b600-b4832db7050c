/* Standardized Form Styles */

/* Form container */
.form-container {
  width: 100%;
}

/* Form group */
.form-group {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .form-group {
    margin-bottom: 1.5rem;
  }
}

/* Form labels */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* Required indicator */
.form-label.required::after {
  content: "*";
  color: #ef4444;
  margin-left: 0.25rem;
}

/* Form inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: #93c5fd;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* Validation states */
.form-input.is-invalid,
.form-select.is-invalid,
.form-textarea.is-invalid {
  border-color: #ef4444;
}

.form-input.is-valid,
.form-select.is-valid,
.form-textarea.is-valid {
  border-color: #10b981;
}

.form-input.is-invalid:focus,
.form-select.is-invalid:focus,
.form-textarea.is-invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.25);
}

.form-input.is-valid:focus,
.form-select.is-valid:focus,
.form-textarea.is-valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.25);
}

/* Feedback messages */
.form-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  min-height: 1rem;
}

.invalid-feedback {
  color: #ef4444;
}

.valid-feedback {
  color: #10b981;
}

/* Checkboxes and radios */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: #3b82f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-label {
  font-size: 0.875rem;
  color: #374151;
}

/* Form actions */
.form-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

/* Responsive form layout */
@media (min-width: 768px) {
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.5rem;
    margin-left: -0.5rem;
  }

  .form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .form-col-full {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
}

/* Multi-step form */
.form-step {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.form-step.active {
  display: block;
  opacity: 1;
}

.form-step.hidden {
  display: none;
  opacity: 0;
}

/* Form step indicators */
.form-steps-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.form-step-item {
  flex: 1;
  text-align: center;
  position: relative;
  z-index: 1;
}

.form-step-number {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #6b7280;
  font-weight: 500;
  margin: 0 auto 0.5rem;
  border: 2px solid #e5e7eb;
  transition: all 0.2s;
}

.form-step-title {
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.2s;
}

@media (min-width: 640px) {
  .form-step-title {
    font-size: 0.875rem;
  }
}

.form-step-item.active .form-step-number {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.form-step-item.active .form-step-title {
  color: #1f2937;
  font-weight: 500;
}

.form-step-item.completed .form-step-number {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

/* Progress line between steps */
.form-steps-indicator::before {
  content: "";
  position: absolute;
  top: 1rem;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e5e7eb;
  z-index: 0;
}

/* Form help text */
.form-help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Password strength meter */
.password-strength-meter {
  height: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 0.125rem;
  margin-top: 0.5rem;
  overflow: hidden;
}

.password-strength-meter-bar {
  height: 100%;
  border-radius: 0.125rem;
  transition: width 0.3s ease;
}

.password-strength-weak {
  background-color: #ef4444;
  width: 25%;
}

.password-strength-fair {
  background-color: #f59e0b;
  width: 50%;
}

.password-strength-good {
  background-color: #10b981;
  width: 75%;
}

.password-strength-strong {
  background-color: #047857;
  width: 100%;
}

.password-strength-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Form field with icon */
.form-field-with-icon {
  position: relative;
}

.form-field-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

.form-field-icon-left {
  left: 0.75rem;
}

.form-field-icon-right {
  right: 0.75rem;
}

.form-input-with-icon-left {
  padding-left: 2.5rem;
}

.form-input-with-icon-right {
  padding-right: 2.5rem;
}

/* Form field with button */
.form-field-with-button {
  position: relative;
}

.form-field-button {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
}

.form-field-button:hover {
  color: #4b5563;
}

.form-input-with-button {
  padding-right: 2.5rem;
}
