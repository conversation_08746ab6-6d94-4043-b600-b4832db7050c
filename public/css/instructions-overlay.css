/* Instructions Overlay Styles */
.instructions-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.instructions-overlay.active {
    opacity: 1;
    visibility: visible;
}

.instructions-content {
    background-color: white;
    border-radius: 0.5rem;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(20px);
    transition: transform 0.3s;
}

.instructions-overlay.active .instructions-content {
    transform: translateY(0);
}

.instructions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.instructions-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.instructions-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.instructions-close:hover {
    background-color: #f3f4f6;
    color: #ef4444;
}

.instructions-body {
    padding: 1.5rem;
}

.instructions-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
}

.instructions-footer button {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.instructions-footer .btn-primary {
    background-color: #4f46e5;
    color: white;
}

.instructions-footer .btn-primary:hover {
    background-color: #4338ca;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .instructions-content {
        width: 95%;
    }
    
    .instructions-header {
        padding: 0.75rem 1rem;
    }
    
    .instructions-body {
        padding: 1rem;
    }
    
    .instructions-footer {
        padding: 0.75rem 1rem;
    }
}
