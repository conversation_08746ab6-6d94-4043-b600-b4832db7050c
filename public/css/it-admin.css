/* IT Admin View Custom Styles */

:root {
  --it-admin-primary: #2c5530;    /* <PERSON> */
  --it-admin-secondary: #1e3d23;  /* <PERSON><PERSON> Green */
  --it-admin-light: #e6ede7;      /* <PERSON> Hunter Green */
  --it-admin-accent: #4a7c50;     /* Light<PERSON> Green */
  --it-admin-hover: #3a6040;      /* Medium Hunter Green */
}

/* Background Colors */
.bg-it-admin-primary {
  background-color: var(--it-admin-primary);
}

.bg-it-admin-secondary {
  background-color: var(--it-admin-secondary);
}

.bg-it-admin-light {
  background-color: var(--it-admin-light);
}

.bg-it-admin-accent {
  background-color: var(--it-admin-accent);
}

/* Text Colors */
.text-it-admin-primary {
  color: var(--it-admin-primary);
}

.text-it-admin-secondary {
  color: var(--it-admin-secondary);
}

.text-it-admin-light {
  color: var(--it-admin-light);
}

.text-it-admin-accent {
  color: var(--it-admin-accent);
}

/* Border Colors */
.border-it-admin-primary {
  border-color: var(--it-admin-primary);
}

.border-it-admin-secondary {
  border-color: var(--it-admin-secondary);
}

.border-it-admin-light {
  border-color: var(--it-admin-light);
}

/* Hover States */
.hover\:bg-it-admin-primary:hover {
  background-color: var(--it-admin-primary);
}

.hover\:bg-it-admin-secondary:hover {
  background-color: var(--it-admin-secondary);
}

.hover\:bg-it-admin-hover:hover {
  background-color: var(--it-admin-hover);
}

.hover\:text-it-admin-light:hover {
  color: var(--it-admin-light);
}

/* Button Styles */
.btn-it-admin {
  background-color: var(--it-admin-primary);
  color: white;
  transition: background-color 0.2s ease;
}

.btn-it-admin:hover {
  background-color: var(--it-admin-hover);
}

.btn-it-admin-outline {
  border: 1px solid var(--it-admin-primary);
  color: var(--it-admin-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-it-admin-outline:hover {
  background-color: var(--it-admin-primary);
  color: white;
}

/* Custom scrollbar for IT Admin */
.it-admin-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.it-admin-scrollbar::-webkit-scrollbar-track {
  background: #e6ede7;
}

.it-admin-scrollbar::-webkit-scrollbar-thumb {
  background: var(--it-admin-accent);
  border-radius: 4px;
}

.it-admin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--it-admin-primary);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .inventory-container {
    overflow-x: auto;
  }
}

/* Select2 Custom Styling for IT Admin */
.it-admin-select .select2-container--default .select2-selection--single,
.it-admin-select .select2-container--default .select2-selection--multiple {
  border-color: #d1d5db;
  border-radius: 0.375rem;
}

.it-admin-select .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: var(--it-admin-light);
  border-color: var(--it-admin-accent);
  color: var(--it-admin-primary);
}

.it-admin-select .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--it-admin-primary);
}
