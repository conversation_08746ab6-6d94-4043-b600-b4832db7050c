/* Standardized Modal Styles */

/* Modal backdrop */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

/* Modal content container */
.modal-content {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 28rem;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Modal sizes */
.modal-sm .modal-content {
  max-width: 24rem;
}

.modal-md .modal-content {
  max-width: 32rem;
}

.modal-lg .modal-content {
  max-width: 48rem;
}

.modal-xl .modal-content {
  max-width: 64rem;
}

.modal-full .modal-content {
  max-width: 90vw;
  height: 90vh;
}

/* Modal header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.modal-close {
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
}

.modal-close:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

/* Modal body */
.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex-grow: 1;
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal {
    padding: 0.5rem;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-lg .modal-content,
  .modal-xl .modal-content {
    max-width: 100%;
  }
}

/* Animation */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  animation: modalFadeIn 0.3s ease-out;
}

/* Utility classes for modal buttons */
.modal-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.modal-btn-primary {
  background-color: #4f46e5;
  color: white;
  border: 1px solid transparent;
}

.modal-btn-primary:hover {
  background-color: #4338ca;
}

.modal-btn-secondary {
  background-color: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.modal-btn-secondary:hover {
  background-color: #f9fafb;
  color: #1f2937;
}

.modal-btn-danger {
  background-color: #ef4444;
  color: white;
  border: 1px solid transparent;
}

.modal-btn-danger:hover {
  background-color: #dc2626;
}
