/* Principal View Custom Styles - Executive Leadership Theme */

:root {
  --principal-primary: #1e3a8a;    /* Deep Navy Blue */
  --principal-secondary: #1e40af;  /* Royal Blue */
  --principal-accent: #f59e0b;     /* Golden Yellow */
  --principal-light: #eff6ff;      /* Light Blue */
  --principal-dark: #0f172a;       /* Very Dark Navy */
  --principal-hover: #3b82f6;      /* Medium Blue */
  --principal-gold: #d97706;       /* Deep Gold */
  --principal-silver: #64748b;     /* Silver Gray */
}

/* Background Colors */
.bg-principal-primary {
  background-color: var(--principal-primary);
}

.bg-principal-secondary {
  background-color: var(--principal-secondary);
}

.bg-principal-light {
  background-color: var(--principal-light);
}

.bg-principal-accent {
  background-color: var(--principal-accent);
}

.bg-principal-dark {
  background-color: var(--principal-dark);
}

.bg-principal-gold {
  background-color: var(--principal-gold);
}

.bg-principal-silver {
  background-color: var(--principal-silver);
}

/* Text Colors */
.text-principal-primary {
  color: var(--principal-primary);
}

.text-principal-secondary {
  color: var(--principal-secondary);
}

.text-principal-light {
  color: var(--principal-light);
}

.text-principal-accent {
  color: var(--principal-accent);
}

.text-principal-dark {
  color: var(--principal-dark);
}

.text-principal-gold {
  color: var(--principal-gold);
}

.text-principal-silver {
  color: var(--principal-silver);
}

/* Border Colors */
.border-principal-primary {
  border-color: var(--principal-primary);
}

.border-principal-secondary {
  border-color: var(--principal-secondary);
}

.border-principal-light {
  border-color: var(--principal-light);
}

.border-principal-accent {
  border-color: var(--principal-accent);
}

/* Hover States */
.hover\:bg-principal-primary:hover {
  background-color: var(--principal-primary);
}

.hover\:bg-principal-secondary:hover {
  background-color: var(--principal-secondary);
}

.hover\:bg-principal-hover:hover {
  background-color: var(--principal-hover);
}

.hover\:text-principal-light:hover {
  color: var(--principal-light);
}

.hover\:text-principal-primary:hover {
  color: var(--principal-primary);
}

/* Button Styles */
.btn-principal {
  background-color: var(--principal-primary);
  color: white;
  transition: background-color 0.2s ease;
}

.btn-principal:hover {
  background-color: var(--principal-hover);
}

.btn-principal-outline {
  border: 1px solid var(--principal-primary);
  color: var(--principal-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-principal-outline:hover {
  background-color: var(--principal-primary);
  color: white;
}

/* Card Hover Effects */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(153, 27, 27, 0.15);
}

/* Gradient Backgrounds */
.bg-gradient-principal {
  background: linear-gradient(135deg, var(--principal-primary) 0%, var(--principal-secondary) 100%);
}

.bg-gradient-principal-light {
  background: linear-gradient(135deg, var(--principal-light) 0%, #ffffff 100%);
}

/* Progress Bars */
.progress-bar-principal {
  background-color: var(--principal-light);
}

.progress-bar-principal .progress-fill {
  background-color: var(--principal-primary);
  transition: width 0.3s ease;
}

/* Status Indicators */
.status-excellent {
  background-color: #10b981;
  color: white;
}

.status-good {
  background-color: #3b82f6;
  color: white;
}

.status-average {
  background-color: #f59e0b;
  color: white;
}

.status-poor {
  background-color: var(--principal-accent);
  color: white;
}

/* Custom Scrollbar for Principal */
.principal-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.principal-scrollbar::-webkit-scrollbar-track {
  background: var(--principal-light);
}

.principal-scrollbar::-webkit-scrollbar-thumb {
  background: var(--principal-accent);
  border-radius: 4px;
}

.principal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--principal-primary);
}

/* Sidebar Styles */
.sidebar-link {
  transition: all 0.3s ease;
}

.sidebar-link:hover {
  background-color: rgba(153, 27, 27, 0.1);
  border-left: 4px solid var(--principal-accent);
}

.sidebar-link.active {
  background-color: rgba(153, 27, 27, 0.2);
  border-left: 4px solid var(--principal-primary);
  color: var(--principal-primary);
}

/* Chart Colors */
.chart-principal-1 { background-color: var(--principal-primary); }
.chart-principal-2 { background-color: var(--principal-accent); }
.chart-principal-3 { background-color: var(--principal-hover); }
.chart-principal-4 { background-color: var(--principal-secondary); }

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(1, 1fr);
  }

  .principal-container {
    overflow-x: auto;
  }

  .sidebar-link {
    padding: 0.75rem 1rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Table Styles */
.table-principal {
  border-collapse: collapse;
}

.table-principal th {
  background-color: var(--principal-light);
  color: var(--principal-primary);
  font-weight: 600;
}

.table-principal tr:hover {
  background-color: rgba(153, 27, 27, 0.05);
}

/* Badge Styles */
.badge-principal {
  background-color: var(--principal-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-principal-outline {
  border: 1px solid var(--principal-primary);
  color: var(--principal-primary);
  background-color: transparent;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Loading Spinner */
.spinner-principal {
  border: 3px solid var(--principal-light);
  border-top: 3px solid var(--principal-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
