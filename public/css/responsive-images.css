/* Responsive Images CSS */

/* Basic responsive image container */
.responsive-image-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Make images responsive by default */
.responsive-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Aspect ratio containers */
.aspect-ratio-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.aspect-ratio-container::before {
  content: "";
  display: block;
  padding-top: 56.25%; /* 16:9 Aspect Ratio by default */
}

.aspect-ratio-container > img,
.aspect-ratio-container > .content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Common aspect ratios */
.aspect-ratio-1-1::before {
  padding-top: 100%; /* 1:1 Aspect Ratio (Square) */
}

.aspect-ratio-4-3::before {
  padding-top: 75%; /* 4:3 Aspect Ratio */
}

.aspect-ratio-16-9::before {
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.aspect-ratio-21-9::before {
  padding-top: 42.85%; /* 21:9 Aspect Ratio */
}

/* Profile image styles */
.profile-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 50%;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Hover overlay for clickable images */
.image-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.image-hover-overlay:hover {
  opacity: 1;
}

.image-hover-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0.25rem;
}

/* Image lightbox/overlay */
.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
}

.image-overlay-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.image-overlay-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.image-overlay-close {
  position: absolute;
  top: -2rem;
  right: -2rem;
  background-color: white;
  color: black;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

/* Responsive sizes */
@media (max-width: 640px) {
  .image-overlay-close {
    top: -1.5rem;
    right: -0.5rem;
  }
}

/* Image loading placeholder */
.image-placeholder {
  background-color: #f3f4f6;
  position: relative;
  overflow: hidden;
}

.image-placeholder::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
