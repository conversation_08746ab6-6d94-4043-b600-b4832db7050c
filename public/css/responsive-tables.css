/* Responsive Tables CSS */

/* Standard table wrapper */
.responsive-table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin: 0 -1rem;
}

@media (min-width: 640px) {
  .responsive-table-wrapper {
    margin: 0;
  }
}

/* Table styles */
.responsive-table {
  min-width: 100%;
  border-collapse: collapse;
}

/* Table header */
.responsive-table thead {
  background-color: #f9fafb;
}

.responsive-table th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

/* Table body */
.responsive-table td {
  padding: 0.75rem 1rem;
  vertical-align: top;
  border-bottom: 1px solid #e5e7eb;
}

/* Responsive styles for small screens */
@media (max-width: 639px) {
  /* Card-style tables for mobile */
  .card-table-mobile thead {
    display: none;
  }
  
  .card-table-mobile tr {
    display: block;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .card-table-mobile td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: right;
    border-bottom: 1px solid #f3f4f6;
    padding: 0.75rem 1rem;
  }
  
  .card-table-mobile td:last-child {
    border-bottom: none;
  }
  
  .card-table-mobile td::before {
    content: attr(data-label);
    font-weight: 500;
    color: #4b5563;
    text-align: left;
    padding-right: 1rem;
  }
  
  /* Action buttons in mobile card tables */
  .card-table-mobile td.table-actions {
    justify-content: flex-end;
  }
  
  .card-table-mobile td.table-actions::before {
    display: none;
  }
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-success {
  background-color: #d1fae5;
  color: #065f46;
}

.status-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-error {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-info {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-neutral {
  background-color: #f3f4f6;
  color: #4b5563;
}

/* Action buttons */
.table-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

@media (max-width: 639px) {
  .table-action-buttons {
    justify-content: flex-end;
  }
}
