/* Search Select Component Styles */

.search-select-container {
    position: relative;
    width: 100%;
}

.search-select-input {
    width: 100%;
    padding-right: 2.5rem;
}

.search-select-dropdown {
    max-height: 200px;
    overflow-y: auto;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.search-select-option {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-select-option:hover {
    background-color: rgba(243, 244, 246, 1);
}

.search-select-option.highlighted {
    background-color: rgba(219, 234, 254, 1);
}

.search-select-no-results {
    padding: 0.5rem 0.75rem;
    color: rgba(107, 114, 128, 1);
    font-style: italic;
}

.search-select-clear {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(156, 163, 175, 1);
    font-size: 1.25rem;
    line-height: 1;
    cursor: pointer;
    transition: color 0.2s;
}

.search-select-clear:hover {
    color: rgba(107, 114, 128, 1);
}
