/* Custom styles for Select2 multi-select fields */

/* Reduce font size and improve UI for multi-selected values */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e5e7eb;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    padding: 0.1rem 0.5rem;
    margin-right: 0.25rem;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
}

/* Style for the remove button */
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #6b7280;
    margin-right: 0.25rem;
    border-right: 1px solid #d1d5db;
    padding-right: 0.25rem;
    font-weight: bold;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #ef4444;
    background-color: transparent;
}

/* Style for the dropdown */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #4f46e5;
    color: white;
}

/* Style for the container */
.select2-container--default .select2-selection--multiple {
    border-color: #d1d5db;
    min-height: 38px;
}

/* Style for the search box */
.select2-container--default .select2-search--inline .select2-search__field {
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

/* Style for the dropdown container */
.select2-container--open .select2-dropdown {
    border-color: #4f46e5;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Style for selected option in dropdown */
.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #e5e7eb;
}

/* Color-coded categories */
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
}

/* Different colors for different categories (will cycle through these) */
.select2-container--default .select2-selection--multiple .select2-selection__choice:nth-child(3n+1) {
    background-color: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice:nth-child(3n+2) {
    background-color: #dcfce7;
    border-color: #86efac;
    color: #166534;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice:nth-child(3n+3) {
    background-color: #fef3c7;
    border-color: #fcd34d;
    color: #92400e;
}
