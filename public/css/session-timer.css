/* Session Timer Overlay Styles */
#session-info {
    transition: opacity 0.5s ease-in-out, transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

#footer-session-timer {
    font-family: monospace;
    transition: color 0.3s ease-in-out, font-weight 0.3s ease-in-out;
    font-size: 1.1em;
}

#footer-extend-btn {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    padding: 2px 8px;
    border-radius: 4px;
}

#footer-extend-btn:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

/* Warning state styles */
.session-warning {
    animation: pulse 2s infinite;
    border-color: rgba(220, 38, 38, 0.3);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
    }
}

#session-timer-container.border-red-500 {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

#session-timer {
    font-family: monospace;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

#extend-session-btn, #logout-now-btn {
    transition: all 0.2s ease;
}

#extend-session-btn:hover, #logout-now-btn:hover {
    transform: translateY(-1px);
}

#extend-session-btn:active, #logout-now-btn:active {
    transform: translateY(1px);
}

/* Session Timer Indicator */
#session-timer-indicator {
    transition: all 0.3s ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 9998; /* High z-index but below chat button */
}

#session-timer-indicator:hover {
    transform: scale(1.1);
}
