/* Student View Custom Styles */

:root {
  --student-primary: #3b82f6;    /* Bright Blue */
  --student-secondary: #2563eb;  /* Darker Blue */
  --student-light: #eff6ff;      /* Light Blue */
  --student-accent: #60a5fa;     /* Medium Blue */
  --student-hover: #1d4ed8;      /* Deep Blue */
}

/* Background Colors */
.bg-student-primary {
  background-color: var(--student-primary);
}

.bg-student-secondary {
  background-color: var(--student-secondary);
}

.bg-student-light {
  background-color: var(--student-light);
}

.bg-student-accent {
  background-color: var(--student-accent);
}

/* Text Colors */
.text-student-primary {
  color: var(--student-primary);
}

.text-student-secondary {
  color: var(--student-secondary);
}

.text-student-light {
  color: var(--student-light);
}

.text-student-accent {
  color: var(--student-accent);
}

/* Border Colors */
.border-student-primary {
  border-color: var(--student-primary);
}

.border-student-secondary {
  border-color: var(--student-secondary);
}

/* Hover States */
.hover\:bg-student-primary:hover {
  background-color: var(--student-primary);
}

.hover\:bg-student-secondary:hover {
  background-color: var(--student-secondary);
}

.hover\:text-student-primary:hover {
  color: var(--student-primary);
}

.hover\:border-student-primary:hover {
  border-color: var(--student-primary);
}

/* Button Styles */
.btn-student-primary {
  background-color: var(--student-primary);
  color: white;
  transition: background-color 0.2s ease;
}

.btn-student-primary:hover {
  background-color: var(--student-secondary);
}

.btn-student-outline {
  border: 1px solid var(--student-primary);
  color: var(--student-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-student-outline:hover {
  background-color: var(--student-primary);
  color: white;
}

/* Card Hover Effect */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Progress Bars */
.progress-container {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Form Styles */
.form-input:focus, .form-select:focus, .form-textarea:focus {
  border-color: var(--student-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: var(--student-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--student-primary);
}
