/* Teacher View Custom Styles */

:root {
  --teacher-primary: #22c55e;    /* <PERSON> Green */
  --teacher-secondary: #16a34a;  /* Darker Green */
  --teacher-dark: #166534;       /* Deep Green */
  --teacher-light: #dcfce7;      /* Light Green */
}

.bg-teacher-primary {
  background-color: var(--teacher-primary);
}

.bg-teacher-secondary {
  background-color: var(--teacher-secondary);
}

.bg-teacher-dark {
  background-color: var(--teacher-dark);
}

.bg-teacher-light {
  background-color: var(--teacher-light);
}

.text-teacher-primary {
  color: var(--teacher-primary);
}

.text-teacher-secondary {
  color: var(--teacher-secondary);
}

.border-teacher-primary {
  border-color: var(--teacher-primary);
}

.hover\:bg-teacher-primary:hover {
  background-color: var(--teacher-primary);
}

.hover\:bg-teacher-secondary:hover {
  background-color: var(--teacher-secondary);
}

.hover\:text-teacher-primary:hover {
  color: var(--teacher-primary);
}

.hover\:text-teacher-secondary:hover {
  color: var(--teacher-secondary);
}

.hover\:border-teacher-primary:hover {
  border-color: var(--teacher-primary);
}

/* Toast Notifications */
.toast {
  position: relative;
  margin-bottom: 1rem;
  width: 350px;
}

.animate-fade-in-down {
  animation: fadeInDown 0.5s ease-out;
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard Cards */
.dashboard-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Progress Bars */
.progress-bar {
  height: 8px;
  border-radius: 4px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Timetable Styles */
.timetable-cell {
  min-height: 100px;
  transition: transform 0.2s ease;
}

.timetable-cell:hover {
  transform: scale(1.02);
}

/* Form Styles */
.form-input:focus, .form-select:focus, .form-textarea:focus {
  border-color: var(--teacher-primary);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

/* Button Styles */
.btn-teacher-primary {
  background-color: var(--teacher-primary);
  color: white;
  transition: background-color 0.2s ease;
}

.btn-teacher-primary:hover {
  background-color: var(--teacher-secondary);
}

.btn-teacher-outline {
  border: 1px solid var(--teacher-primary);
  color: var(--teacher-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.btn-teacher-outline:hover {
  background-color: var(--teacher-primary);
  color: white;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(1, 1fr);
  }

  .timetable-container {
    overflow-x: auto;
  }
}

/* Select2 Custom Styling */
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border-color: #d1d5db;
  border-radius: 0.375rem;
}

.select2-container--default .select2-selection--single:focus,
.select2-container--default .select2-selection--multiple:focus {
  border-color: var(--teacher-primary);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--teacher-primary);
}

/* Chart.js Custom Styling */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
