/* Toast Notifications CSS */

/* Toast container positioning is now handled by JavaScript for responsive design */
#toast-container {
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none; /* Allow clicks to pass through container but not toasts */
}

/* Individual toasts should capture clicks */
#toast-container > div {
  pointer-events: auto;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Toast colors */
.bg-success, .toast-success {
  background-color: #10b981 !important; /* Green */
}

.bg-error, .toast-error {
  background-color: #ef4444 !important; /* Red */
}

.bg-warning, .toast-warning {
  background-color: #f59e0b !important; /* Yellow */
}

.bg-info, .toast-info {
  background-color: #3b82f6 !important; /* Blue */
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  #toast-container > div {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 0.5rem;
    transform: translateY(10px); /* Start slightly below final position */
  }

  /* Ensure toasts are more visible on mobile */
  #toast-container > div {
    padding: 1rem;
  }

  /* Larger text for mobile */
  .toast-content {
    font-size: 1rem;
  }
}

/* Desktop-specific styles */
@media (min-width: 768px) {
  #toast-container > div {
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
  }
}

/* Accessibility improvements */
#toast-container > div:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

/* Add a subtle hover effect */
#toast-container > div:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
