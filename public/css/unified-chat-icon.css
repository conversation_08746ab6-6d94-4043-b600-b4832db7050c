/**
 * Unified Chat Icon Styles
 * Provides consistent styling for the chat icon across all pages
 */

#unified-chat-icon {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 1000;
    cursor: pointer;
    transform: scale(1);
    transition: all 0.3s ease;
}

#unified-chat-icon:hover {
    transform: scale(1.1);
}

#unified-chat-icon .chat-icon-button {
    background-color: #2563eb;
    color: white;
    border-radius: 50%;
    padding: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: background-color 0.3s ease;
}

#unified-chat-icon .chat-icon-button:hover {
    background-color: #1d4ed8;
}

#unified-chat-icon svg {
    width: 1.5rem;
    height: 1.5rem;
}

#chat-notification-badge {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background-color: #ef4444;
    color: white;
    font-size: 0.75rem;
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

#chat-notification-badge.hidden {
    display: none !important;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #unified-chat-icon {
        bottom: 1rem;
        right: 1rem;
    }
    
    #unified-chat-icon .chat-icon-button {
        padding: 0.75rem;
    }
    
    #unified-chat-icon svg {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Chat popup styles (if used) */
.chat-popup {
    position: fixed;
    bottom: 6rem;
    right: 1.5rem;
    width: 20rem;
    max-width: calc(100vw - 2rem);
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 999;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease;
}

.chat-popup.scale-100 {
    transform: scale(1);
}

.chat-popup.opacity-100 {
    opacity: 1;
}

.chat-popup.hidden {
    display: none;
}

@media (max-width: 768px) {
    .chat-popup {
        bottom: 5rem;
        right: 1rem;
        width: calc(100vw - 2rem);
    }
}

/* Animation classes */
.chat-icon-bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Accessibility improvements */
#unified-chat-icon:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

#unified-chat-icon[aria-pressed="true"] {
    background-color: #1d4ed8;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #unified-chat-icon .chat-icon-button {
        border: 2px solid currentColor;
    }
    
    #chat-notification-badge {
        border: 1px solid white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #unified-chat-icon,
    #unified-chat-icon:hover,
    .chat-popup,
    #chat-notification-badge {
        transition: none;
        animation: none;
    }
}
