/* WhatsApp-style Chat Component Styles */

/* Floating chat button */
#floatingChatButton {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 99999 !important;
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #7c3aed !important; /* Purple 600 */
}

@media (min-width: 640px) {
    #floatingChatButton {
        width: 56px !important;
        height: 56px !important;
    }
}

/* Fallback chat button */
#fallbackChatButton {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 99999 !important;
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #7c3aed !important; /* Purple 600 */
}

@media (min-width: 640px) {
    #fallbackChatButton {
        width: 56px !important;
        height: 56px !important;
    }
}

/* Chat popup animation */
#chatPopup {
    transition: all 0.3s ease-in-out;
    position: fixed !important;
    bottom: 90px !important;
    right: 20px !important;
    z-index: 99980 !important;
    max-width: 95vw !important; /* Limit width on small screens */
    max-height: 80vh !important; /* Limit height on small screens */
    width: 350px !important; /* Smaller default width */
    height: 450px !important; /* Smaller default height */

    /* Responsive adjustments */
    @media (min-width: 640px) {
        width: 450px !important;
        height: 500px !important;
    }

    @media (min-width: 768px) {
        width: 500px !important;
    }
}

/* Message bubbles */
.message-bubble {
    position: relative;
    max-width: 70%;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 8px;
}

.message-bubble.sent {
    background-color: #7c3aed; /* Purple 600 */
    color: white;
    margin-left: auto;
    border-top-right-radius: 0;
}

.message-bubble.received {
    background-color: white;
    color: #1f2937; /* Gray 800 */
    margin-right: auto;
    border-top-left-radius: 0;
}

/* Time display */
.message-time {
    font-size: 0.7rem;
    text-align: right;
    margin-top: 2px;
}

.message-bubble.sent .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.message-bubble.received .message-time {
    color: #6b7280; /* Gray 500 */
}

/* Sender name */
.sender-name {
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.message-bubble.received .sender-name {
    color: #4b5563; /* Gray 600 */
}

/* Unread badge */
.unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ef4444; /* Red 500 */
    color: white;
    font-size: 0.7rem;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chat input */
#messageInput {
    border: none;
    outline: none;
    padding: 10px 15px;
    border-radius: 20px;
    background-color: #f3f4f6; /* Gray 100 */
    transition: all 0.2s ease;
}

#messageInput:focus {
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3);
}

/* Send button */
#sendMessageBtn {
    background-color: #7c3aed; /* Purple 600 */
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

#sendMessageBtn:hover {
    background-color: #6d28d9; /* Purple 700 */
}

/* Chat contact list items */
.chat-contact-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f3f4f6; /* Gray 100 */
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.chat-contact-item:hover {
    background-color: #f9fafb; /* Gray 50 */
}

.chat-contact-item.active {
    background-color: #f3f4f6; /* Gray 100 */
}

/* Avatar */
.chat-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    position: relative;
}

.chat-avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.chat-avatar-initial {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #ede9fe; /* Purple 100 */
    color: #7c3aed; /* Purple 600 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Scrollbar styles */
#messagesContainer::-webkit-scrollbar,
#peopleList::-webkit-scrollbar,
#groupsList::-webkit-scrollbar {
    width: 6px;
}

#messagesContainer::-webkit-scrollbar-track,
#peopleList::-webkit-scrollbar-track,
#groupsList::-webkit-scrollbar-track {
    background: transparent;
}

#messagesContainer::-webkit-scrollbar-thumb,
#peopleList::-webkit-scrollbar-thumb,
#groupsList::-webkit-scrollbar-thumb {
    background-color: #d1d5db; /* Gray 300 */
    border-radius: 3px;
}

#messagesContainer::-webkit-scrollbar-thumb:hover,
#peopleList::-webkit-scrollbar-thumb:hover,
#groupsList::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af; /* Gray 400 */
}
