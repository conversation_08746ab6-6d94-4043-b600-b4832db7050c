let sectionCounter = 0;
const sections = new Map();

// Add functions for edit mode
let isEditMode = false;
let testId = null;

// Initialize the page - check if we're in edit mode
document.addEventListener('DOMContentLoaded', async () => {
    // Check if we're in edit mode by looking at both URL formats
    const urlParams = new URLSearchParams(window.location.search);
    let editId = urlParams.get('id');

    // Also check for path parameter format
    if (!editId) {
        const pathParts = window.location.pathname.split('/');
        if (pathParts[1] === 'edit-test' && pathParts[2]) {
            editId = pathParts[2];
        }
    }

    if (editId) {
        isEditMode = true;
        testId = editId;
        await loadTestData(testId);

        // Update form title and button
        document.querySelector('h1').textContent = 'Edit Test';
        document.querySelector('button[type="submit"]').textContent = 'Update Test';
    }
});

document.getElementById('addSectionBtn').addEventListener('click', () => {
    sectionCounter++;
    const sectionId = 'section_' + sectionCounter;
    const sectionName = 'Section ' + sectionCounter;

    // Add section tab
    const tab = document.createElement('div');
    tab.className = 'section-tab cursor-pointer px-4 py-2 rounded-t-lg';
    tab.dataset.sectionId = sectionId;
    tab.innerHTML = `
        <span class="section-name">${sectionName}</span>
        <input type="text" class="section-name-input hidden border px-2" value="${sectionName}">
    `;
    document.getElementById('sectionTabs').appendChild(tab);

    // Add section content
    const content = document.createElement('div');
    content.className = 'section-content hidden';
    content.dataset.sectionId = sectionId;
    content.innerHTML = `
        <div class="mb-4">
            <button type="button" class="add-question-btn bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add Question
            </button>
        </div>
        <div class="questions-container">
            <!-- Questions will be added here -->
        </div>
    `;
    document.getElementById('sectionContent').appendChild(content);

    // Store section data
    sections.set(sectionId, {
        name: sectionName,
        questions: []
    });

    // Show the new section
    showSection(sectionId);

    // Add click handlers
    setupSectionHandlers(tab, content);
});

function setupSectionHandlers(tab, content) {
    // Double click to rename section
    tab.addEventListener('dblclick', (e) => {
        const nameSpan = tab.querySelector('.section-name');
        const nameInput = tab.querySelector('.section-name-input');
        nameSpan.classList.add('hidden');
        nameInput.classList.remove('hidden');
        nameInput.focus();
    });

    // Handle rename input
    const nameInput = tab.querySelector('.section-name-input');
    nameInput.addEventListener('blur', (e) => {
        const nameSpan = tab.querySelector('.section-name');
        nameSpan.textContent = nameInput.value;
        nameSpan.classList.remove('hidden');
        nameInput.classList.add('hidden');

        // Update section data
        const sectionId = tab.dataset.sectionId;
        const sectionData = sections.get(sectionId);
        sectionData.name = nameInput.value;
    });

    // Add question button handler
    content.querySelector('.add-question-btn').addEventListener('click', () => {
        addQuestion(content.dataset.sectionId);
    });
}

function showSection(sectionId) {
    // Hide all sections and deactivate all tabs
    document.querySelectorAll('.section-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.section-content').forEach(content => content.classList.add('hidden'));

    // Show selected section and activate its tab
    const selectedTab = document.querySelector(`.section-tab[data-section-id="${sectionId}"]`);
    const selectedContent = document.querySelector(`.section-content[data-section-id="${sectionId}"]`);
    selectedTab.classList.add('active');
    selectedContent.classList.remove('hidden');
}

function addQuestion(sectionId) {
    const questionId = 'question_' + Date.now();
    const questionsContainer = document.querySelector(`.section-content[data-section-id="${sectionId}"] .questions-container`);

    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-container border rounded p-4 mb-4';
    questionDiv.dataset.questionId = questionId;
    questionDiv.innerHTML = `
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Question Type</label>
            <select class="question-type shadow border rounded w-full py-2 px-3 text-gray-700">
                <option value="mcq">Multiple Choice</option>
                <option value="true_false">True/False</option>
                <option value="fill_in">Fill in the Blank</option>
            </select>
        </div>
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Question Text</label>
            <textarea class="question-text shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" rows="3"></textarea>
        </div>
        <div class="options-container mb-4">
            <!-- Options will be added here for MCQ -->
        </div>
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Solution/Explanation</label>
            <textarea class="solution-text shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" rows="2"></textarea>
        </div>
        <button type="button" class="delete-question bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
            Delete Question
        </button>
    `;
    questionsContainer.appendChild(questionDiv);

    // Setup question type change handler
    const typeSelect = questionDiv.querySelector('.question-type');
    typeSelect.addEventListener('change', () => updateQuestionOptions(questionDiv, typeSelect.value));

    // Initial options setup for MCQ
    updateQuestionOptions(questionDiv, 'mcq');

    // Delete question handler
    questionDiv.querySelector('.delete-question').addEventListener('click', () => {
        questionDiv.remove();
        // Update section data
        const sectionData = sections.get(sectionId);
        sectionData.questions = sectionData.questions.filter(q => q.id !== questionId);
    });
}

function updateQuestionOptions(questionDiv, type) {
    const optionsContainer = questionDiv.querySelector('.options-container');
    optionsContainer.innerHTML = '';

    if (type === 'mcq') {
        optionsContainer.innerHTML = `
            <div class="mb-2">
                <label class="block text-gray-700 text-sm font-bold mb-2">Options</label>
                <div class="options-list space-y-2">
                    <div class="option-item flex items-center">
                        <input type="radio" name="correct_${questionDiv.dataset.questionId}" class="mr-2">
                        <input type="text" class="option-text shadow appearance-none border rounded w-full py-1 px-2 text-gray-700" placeholder="Option 1">
                    </div>
                    <div class="option-item flex items-center">
                        <input type="radio" name="correct_${questionDiv.dataset.questionId}" class="mr-2">
                        <input type="text" class="option-text shadow appearance-none border rounded w-full py-1 px-2 text-gray-700" placeholder="Option 2">
                    </div>
                </div>
                <button type="button" class="add-option mt-2 text-blue-500 hover:text-blue-700 text-sm font-bold">
                    + Add Option
                </button>
            </div>
        `;

        // Add option button handler
        optionsContainer.querySelector('.add-option').addEventListener('click', () => {
            const optionsList = optionsContainer.querySelector('.options-list');
            const optionCount = optionsList.children.length + 1;
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item flex items-center';
            optionDiv.innerHTML = `
                <input type="radio" name="correct_${questionDiv.dataset.questionId}" class="mr-2">
                <input type="text" class="option-text shadow appearance-none border rounded w-full py-1 px-2 text-gray-700" placeholder="Option ${optionCount}">
            `;
            optionsList.appendChild(optionDiv);
        });
    } else if (type === 'true_false') {
        optionsContainer.innerHTML = `
            <div class="mb-2">
                <label class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" name="correct_${questionDiv.dataset.questionId}" value="true" class="mr-2">
                        <span>True</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="correct_${questionDiv.dataset.questionId}" value="false" class="mr-2">
                        <span>False</span>
                    </label>
                </div>
            </div>
        `;
    } else if (type === 'fill_in') {
        optionsContainer.innerHTML = `
            <div class="mb-2">
                <label class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <input type="text" class="correct-answer shadow appearance-none border rounded w-full py-1 px-2 text-gray-700" placeholder="Enter correct answer">
            </div>
        `;
    }
}

// Function to load test data for editing
async function loadTestData(id) {
    try {
        const response = await fetch(`/api/tests/${id}`);

        if (!response.ok) {
            throw new Error(`Failed to load test data: ${response.statusText}`);
        }

        const testData = await response.json();

        // Populate basic form fields
        document.getElementById('exam_name').value = testData.exam_name;
        document.getElementById('duration').value = testData.duration;
        document.getElementById('instructions').value = testData.instructions;

        // Set publish option
        const publishOption = testData.publish_date ? 'later' : 'now';
        document.querySelector(`input[name="publish_option"][value="${publishOption}"]`).checked = true;

        if (testData.publish_date) {
            document.getElementById('publishDateContainer').classList.remove('hidden');
            document.querySelector('input[name="publish_date"]').value = new Date(testData.publish_date)
                .toISOString().split('T')[0]; // Format as YYYY-MM-DD
        }

        // Load sections and questions
        for (const section of testData.sections) {
            // Create section first
            sectionCounter++;
            const sectionId = 'section_' + sectionCounter;

            // Add section tab
            const tab = document.createElement('div');
            tab.className = 'section-tab cursor-pointer px-4 py-2 rounded-t-lg';
            tab.dataset.sectionId = sectionId;
            tab.innerHTML = `
                <span class="section-name">${section.name}</span>
                <input type="text" class="section-name-input hidden border px-2" value="${section.name}">
            `;
            document.getElementById('sectionTabs').appendChild(tab);

            // Add section content
            const content = document.createElement('div');
            content.className = 'section-content hidden';
            content.dataset.sectionId = sectionId;
            content.innerHTML = `
                <div class="mb-4">
                    <button type="button" class="add-question-btn bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Question
                    </button>
                </div>
                <div class="questions-container">
                    <!-- Questions will be added here -->
                </div>
            `;
            document.getElementById('sectionContent').appendChild(content);

            // Store section data
            sections.set(sectionId, {
                name: section.name,
                questions: []
            });

            // Add click handlers
            setupSectionHandlers(tab, content);

            // Add questions to this section
            if (section.questions && section.questions.length > 0) {
                for (const question of section.questions) {
                    addQuestionWithData(sectionId, question);
                }
            }
        }

        // Show the first section if exists
        if (testData.sections.length > 0) {
            const firstSectionId = 'section_1';
            showSection(firstSectionId);
        }

    } catch (error) {
        console.error('Error loading test data:', error);
        alert('Error loading test data: ' + error.message);
    }
}

// Function to add a question with existing data
function addQuestionWithData(sectionId, questionData) {
    const questionId = 'question_' + Date.now() + Math.floor(Math.random() * 1000);
    const questionsContainer = document.querySelector(`.section-content[data-section-id="${sectionId}"] .questions-container`);

    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-container border rounded p-4 mb-4';
    questionDiv.dataset.questionId = questionId;
    questionDiv.innerHTML = `
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Question Type</label>
            <select class="question-type shadow border rounded w-full py-2 px-3 text-gray-700">
                <option value="mcq">Multiple Choice</option>
                <option value="true_false">True/False</option>
                <option value="fill_in">Fill in the Blank</option>
            </select>
        </div>
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Question Text</label>
            <textarea class="question-text shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" rows="3"></textarea>
        </div>
        <div class="options-container mb-4">
            <!-- Options will be added here for MCQ -->
        </div>
        <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">Solution/Explanation</label>
            <textarea class="solution-text shadow appearance-none border rounded w-full py-2 px-3 text-gray-700" rows="2"></textarea>
        </div>
        <button type="button" class="delete-question bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
            Delete Question
        </button>
    `;
    questionsContainer.appendChild(questionDiv);

    // Set question text and solution
    questionDiv.querySelector('.question-text').value = questionData.question_text;
    questionDiv.querySelector('.solution-text').value = questionData.solution_text || '';

    // Set question type
    const typeSelect = questionDiv.querySelector('.question-type');
    typeSelect.value = questionData.type;

    // Setup question type change handler
    typeSelect.addEventListener('change', () => updateQuestionOptions(questionDiv, typeSelect.value));

    // Initial options setup based on question type
    updateQuestionOptions(questionDiv, questionData.type);

    // Set correct answers based on question type
    setTimeout(() => {
        if (questionData.type === 'mcq' && questionData.options) {
            const optionItems = questionDiv.querySelectorAll('.option-item');
            // Create additional options if needed
            while (optionItems.length < questionData.options.length) {
                questionDiv.querySelector('.add-option').click();
            }

            // Set option text and correct option
            const updatedOptionItems = questionDiv.querySelectorAll('.option-item');
            questionData.options.forEach((option, index) => {
                if (index < updatedOptionItems.length) {
                    updatedOptionItems[index].querySelector('.option-text').value = option;
                    if (questionData.correct_option === index) {
                        updatedOptionItems[index].querySelector('input[type="radio"]').checked = true;
                    }
                }
            });
        } else if (questionData.type === 'true_false') {
            const value = questionData.correct_option === 1 ? 'true' : 'false';
            questionDiv.querySelector(`input[value="${value}"]`).checked = true;
        } else if (questionData.type === 'fill_in') {
            questionDiv.querySelector('.correct-answer').value = questionData.correct_option || '';
        }
    }, 0);

    // Delete question handler
    questionDiv.querySelector('.delete-question').addEventListener('click', () => {
        questionDiv.remove();
        // Update section data
        const sectionData = sections.get(sectionId);
        sectionData.questions = sectionData.questions.filter(q => q.id !== questionId);
    });
}

// Function to navigate to edit test page
async function navigateToEditTest(testId) {
    try {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/edit-test';

        const hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.name = 'id';
        hiddenField.value = testId;

        form.appendChild(hiddenField);
        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error navigating to edit test:', error);
        console.error('Error accessing edit test page:', error.message);
    }
}

// Function to gather form data for submission
function gatherFormData() {
    const examName = document.getElementById('testNameField').value;
    const duration = document.getElementById('duration').value;
    const passingScore = document.getElementById('passing_score').value;
    const description = document.getElementById('description').value || '';
    const instructions = document.getElementById('instructions').value || '';

    // Gather sections and questions data
    const sectionsArray = [];

    sections.forEach((sectionData, sectionId) => {
        // Extract questions from the DOM
        const questionsArray = [];
        const questionElements = document.querySelectorAll(`.section-content[data-section-id="${sectionId}"] .question-container`);

        questionElements.forEach(questionElement => {
            const questionType = questionElement.querySelector('.question-type').value;
            const questionText = questionElement.querySelector('.question-text').value;
            const solutionText = questionElement.querySelector('.solution-text').value;

            const questionData = {
                type: questionType,
                text: questionText,
                solution: solutionText
            };

            if (questionType === 'mcq') {
                const options = [];
                const optionElements = questionElement.querySelectorAll('.option-text');
                let correctAnswer = null;

                optionElements.forEach((optionElement, index) => {
                    options.push(optionElement.value);
                    const radioButton = optionElement.closest('.option-item').querySelector('input[type="radio"]');
                    if (radioButton.checked) {
                        correctAnswer = index;
                    }
                });

                questionData.options = options;
                questionData.correct_answer = correctAnswer !== null ? correctAnswer.toString() : '0';
            }

            questionsArray.push(questionData);
        });

        sectionsArray.push({
            name: sectionData.name,
            questions: questionsArray
        });
    });

    return {
        exam_name: examName,
        description: description,
        instructions: instructions,
        duration: duration,
        passing_marks: passingScore,
        sections: sectionsArray,
        isDraft: document.getElementById('isDraft').value
    };
}

// Event handler for form submission
document.getElementById('addTestForm').addEventListener('submit', async function(event) {
    event.preventDefault();

    // Display loading state
    document.getElementById('createTestBtn').disabled = true;
    document.getElementById('createTestBtn').innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
    `;

    try {
        // Gather form data
        const formData = gatherFormData();

        console.log('Submitting form data:', formData);

        // Validate form data
        if (!formData.exam_name) {
            throw new Error('Test name is required');
        }

        if (!formData.sections || formData.sections.length === 0) {
            throw new Error('At least one section is required');
        }

        for (const section of formData.sections) {
            if (!section.questions || section.questions.length === 0) {
                throw new Error(`Section "${section.name}" must have at least one question`);
            }
        }

        // Send data to server
        const response = await fetch('/admin/tests/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to create test');
        }

        // Handle success
        window.location.href = '/admin/tests?success=Test created successfully';
    } catch (error) {
        // Handle error
        console.error('Error submitting form:', error);
        alert(`Error: ${error.message}`);

        // Reset button state
        document.getElementById('createTestBtn').disabled = false;
        document.getElementById('createTestBtn').innerHTML = 'Create Test';
    }
});

// Fix the save draft button functionality
document.getElementById('saveDraftBtn').addEventListener('click', async function() {
    try {
        document.getElementById('isDraft').value = 'true';

        // Gather form data
        const formData = gatherFormData();

        // Validate minimum form data for draft
        if (!formData.exam_name) {
            throw new Error('Test name is required even for drafts');
        }

        // Show saving indicator
        this.disabled = true;
        this.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
        `;

        // Send draft to server
        const response = await fetch('/admin/save-draft', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to save draft');
        }

        const result = await response.json();

        // Show success message
        const saveFeedback = document.createElement('div');
        saveFeedback.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg';
        saveFeedback.innerHTML = 'Draft saved successfully!';
        document.body.appendChild(saveFeedback);

        // Reset button
        this.disabled = false;
        this.innerHTML = 'Save Draft';

        // Remove feedback after 3 seconds
        setTimeout(() => {
            saveFeedback.remove();
        }, 3000);

    } catch (error) {
        console.error('Error saving draft:', error);
        alert(`Error: ${error.message}`);

        // Reset button
        this.disabled = false;
        this.innerHTML = 'Save Draft';
    }
});

// Publish option handler
document.querySelectorAll('input[name="publish_option"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
        const publishDateContainer = document.getElementById('publishDateContainer');
        publishDateContainer.classList.toggle('hidden', e.target.value !== 'later');
    });
});