/**
 * Admin Notifications
 * Handles displaying admin notifications on login
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only run on login page
    if (!window.location.pathname.includes('/login')) return;
    
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('adminNotificationContainer');
    
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'adminNotificationContainer';
        notificationContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
        document.body.appendChild(notificationContainer);
    }
    
    // Function to fetch and display admin notifications
    async function fetchAdminNotifications() {
        try {
            const response = await fetch('/api/admin-notifications/active');
            const data = await response.json();
            
            if (data.success && data.notifications.length > 0) {
                // Create notification content
                let notificationContent = `
                    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
                        <button id="closeAdminNotification" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">${data.notifications[0].title}</h3>
                        <div class="prose mb-4">
                            ${data.notifications[0].message}
                        </div>
                        <div class="text-right">
                            <button id="dismissAdminNotification" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                                Dismiss
                            </button>
                        </div>
                    </div>
                `;
                
                // Set content and show notification
                notificationContainer.innerHTML = notificationContent;
                notificationContainer.classList.remove('hidden');
                
                // Add event listeners
                document.getElementById('closeAdminNotification').addEventListener('click', function() {
                    notificationContainer.classList.add('hidden');
                });
                
                document.getElementById('dismissAdminNotification').addEventListener('click', function() {
                    notificationContainer.classList.add('hidden');
                });
            }
        } catch (error) {
            console.error('Error fetching admin notifications:', error);
        }
    }
    
    // Fetch notifications after a short delay
    setTimeout(fetchAdminNotifications, 1000);
});
