// Add Question Modal JavaScript

// Function to open the add question modal
function openAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.remove('hidden');
}

// Function to close the add question modal
function closeAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.add('hidden');
    document.getElementById('addQuestionForm').reset();

    // Reset preview containers
    const questionPreviewContainer = document.getElementById('question_image_preview_container');
    if (questionPreviewContainer) {
        questionPreviewContainer.classList.add('hidden');
    }

    const solutionPreviewContainer = document.getElementById('solution_image_preview_container');
    if (solutionPreviewContainer) {
        solutionPreviewContainer.classList.add('hidden');
    }

    // Reset option image containers
    document.querySelectorAll('[id^="option_image_container_"]').forEach(container => {
        container.classList.add('hidden');
    });

    document.querySelectorAll('[id^="option_image_preview_container_"]').forEach(container => {
        container.classList.add('hidden');
    });

    // Reset math preview
    const mathPreview = document.getElementById('math_preview');
    if (mathPreview) {
        mathPreview.innerHTML = '';
    }

    // Reset error messages
    const errorMessages = document.getElementById('errorMessages');
    if (errorMessages) {
        errorMessages.classList.add('hidden');
        errorMessages.innerHTML = '';
    }
}

// Function to handle form submission
function submitAddQuestionForm(event) {
    event.preventDefault();

    // Get form data
    const form = document.getElementById('addQuestionForm');
    const formData = new FormData(form);

    // Get section ID and exam ID if they exist
    const sectionIdField = document.getElementById('section_id');
    const examIdField = document.getElementById('exam_id');

    // Get question type and validate required fields
    const questionType = document.getElementById('question_type').value;
    const questionText = document.getElementById('question_text').value;

    if (!questionType) {
        showError('Please select a question type');
        return false;
    }

    if (!questionText) {
        showError('Please enter question text');
        return false;
    }

    // Validate options for multiple choice questions
    if (questionType === 'multiple_choice') {
        const options = document.querySelectorAll('input[name="option_text[]"]');
        if (options.length < 2) {
            showError('Multiple choice questions must have at least 2 options');
            return false;
        }

        const selectedOption = document.querySelector('input[name="correct_option"]:checked');
        if (!selectedOption) {
            showError('Please select a correct option');
            return false;
        }
    }

    // Validate true/false questions
    if (questionType === 'true_false') {
        const selectedAnswer = document.querySelector('input[name="true_false_answer"]:checked');
        if (!selectedAnswer) {
            showError('Please select true or false');
            return false;
        }
    }

    // Validate fill in the blank questions
    if (questionType === 'fill_up') {
        const correctAnswer = document.getElementById('correct_answer').value;
        if (!correctAnswer) {
            showError('Please enter the correct answer');
            return false;
        }
    }

    // Show loading toast
    ToastNotifications.info('Adding question...');

    // Determine the endpoint based on whether we're adding to a section
    let endpoint = form.action;
    if (sectionIdField && sectionIdField.value && examIdField && examIdField.value) {
        endpoint = `/tests/admin/${examIdField.value}/sections/${sectionIdField.value}/questions/add`;
    }

    // Submit form data
    fetch(endpoint, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success toast
            ToastNotifications.success(data.message || 'Question added successfully!');

            // Close modal
            closeAddQuestionModal();

            // Reload page after a short delay
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            // Show error message
            showError(data.message || 'Failed to add question. Please try again.');

            // Show error toast
            ToastNotifications.error(data.message || 'Failed to add question');
        }
    })
    .catch(error => {
        // Show error message
        showError('An error occurred. Please try again.');

        // Show error toast
        ToastNotifications.error('An error occurred. Please try again.');

        console.error('Error adding question:', error);
    });

    return false;
}

// Function to show error message
function showError(message) {
    const errorMessages = document.getElementById('errorMessages');
    if (errorMessages) {
        errorMessages.innerHTML = message;
        errorMessages.classList.remove('hidden');
    } else {
        // Fallback to console.error if error messages element doesn't exist
        console.error(message);
    }
}

// Function to update option values when options are added or removed
function updateOptionValues() {
    const optionRows = document.querySelectorAll('.option-row');
    optionRows.forEach((row, index) => {
        const radio = row.querySelector('input[type="radio"]');
        radio.value = index;
    });
}

// Function to handle question type change
function handleQuestionTypeChange() {
    const questionType = document.getElementById('question_type').value;
    const optionsContainer = document.getElementById('optionsContainer');
    const trueFalseOptions = document.getElementById('true_false_options');
    const fillUpOptions = document.getElementById('fill_up_options');
    const essayOptions = document.getElementById('essay_options');
    const essaySelectionContainer = document.getElementById('essaySelectionContainer');

    // Hide all option containers
    if (optionsContainer) optionsContainer.classList.add('hidden');
    if (trueFalseOptions) trueFalseOptions.classList.add('hidden');
    if (fillUpOptions) fillUpOptions.classList.add('hidden');
    if (essayOptions) essayOptions.classList.add('hidden');

    // Show the appropriate container based on question type
    if (questionType === 'multiple_choice') {
        if (optionsContainer) optionsContainer.classList.remove('hidden');
    } else if (questionType === 'true_false') {
        if (trueFalseOptions) trueFalseOptions.classList.remove('hidden');
    } else if (questionType === 'fill_up') {
        if (fillUpOptions) fillUpOptions.classList.remove('hidden');
    } else if (questionType === 'essay') {
        if (essayOptions) essayOptions.classList.remove('hidden');
        // Hide essay selection for essay questions
        if (essaySelectionContainer) essaySelectionContainer.classList.add('hidden');
    }

    // Show essay selection for non-essay questions
    if (questionType && questionType !== 'essay' && essaySelectionContainer) {
        essaySelectionContainer.classList.remove('hidden');
    }
}

// Function to handle math preview
function updateMathPreview() {
    const questionText = document.getElementById('question_text').value;
    const mathPreview = document.getElementById('math_preview');

    // Display the question text in the preview
    mathPreview.innerHTML = questionText;

    // Render math expressions if MathJax is available
    if (window.MathJax) {
        MathJax.typesetPromise([mathPreview]).catch(err => console.error('Error typesetting math:', err));
    }
}

// Add event listeners when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add option button functionality
    const addOptionBtn = document.getElementById('addOptionBtn');
    if (addOptionBtn) {
        addOptionBtn.addEventListener('click', function() {
            const optionsList = document.getElementById('optionsList');
            if (!optionsList) return;

            const optionCount = optionsList.querySelectorAll('.option-row').length;

            const newOption = document.createElement('div');
            newOption.className = 'option-row mb-4';
            newOption.setAttribute('data-index', optionCount);
            newOption.innerHTML = `
                <div class="flex items-center">
                    <input type="radio" name="correct_option" value="${optionCount}" required class="mr-2">
                    <input type="text" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" name="option_text[]" placeholder="Option text..." required>
                    <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="toggleOptionImageUpload(${optionCount})">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <button type="button" class="ml-2 text-red-600 hover:text-red-800 remove-option">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <!-- Option Image Upload (hidden by default) -->
                <div id="option_image_container_${optionCount}" class="mt-2 hidden">
                    <div class="flex items-start space-x-4">
                        <div class="flex-1">
                            <div class="flex justify-center px-4 py-3 border border-gray-300 border-dashed rounded-md option-drop-zone" data-index="${optionCount}">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-xs text-gray-600">
                                        <label for="option_image_${optionCount}" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500">
                                            <span>Upload image</span>
                                            <input id="option_image_${optionCount}" type="file" class="sr-only" accept="image/*" onchange="previewOptionImage(this, ${optionCount})">
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="option_image_id[]" id="option_image_id_${optionCount}" value="">
                        </div>
                        <div id="option_image_preview_container_${optionCount}" class="w-20 h-20 border border-gray-300 rounded-md overflow-hidden hidden">
                            <img id="option_image_preview_${optionCount}" src="" alt="Option image preview" class="w-full h-full object-contain">
                            <button type="button" onclick="removeOptionImage(${optionCount})" class="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 focus:outline-none">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            optionsList.appendChild(newOption);

            // Setup drag and drop for the new option image
            const dropZone = newOption.querySelector('.option-drop-zone');
            if (dropZone && typeof setupDragAndDrop === 'function') {
                setupDragAndDrop(dropZone, 'option', optionCount);
            }

            // Add event listener to the new remove button
            newOption.querySelector('.remove-option').addEventListener('click', function() {
                optionsList.removeChild(newOption);
                // Update the option indexes
                updateOptionValues();
            });
        });
    }

    // Question type change event
    const questionTypeSelect = document.getElementById('question_type');
    if (questionTypeSelect) {
        questionTypeSelect.addEventListener('change', handleQuestionTypeChange);
        // Trigger the change event to set the initial state
        handleQuestionTypeChange();
    }

    // Math preview update
    const questionText = document.getElementById('question_text');
    if (questionText) {
        questionText.addEventListener('input', updateMathPreview);
    }

    // Exam selection change event
    const examIdSelect = document.getElementById('exam_id');
    if (examIdSelect) {
        examIdSelect.addEventListener('change', function() {
            const examId = this.value;
            const sectionSelect = document.getElementById('section_id');

            if (!sectionSelect) return;

            // Clear existing options
            sectionSelect.innerHTML = '<option value="">Select Section</option>';

            if (examId) {
                // Fetch sections for the selected exam
                fetch(`/admin/questions/get-sections/${examId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.sections) {
                            // Add section options
                            data.sections.forEach(section => {
                                const option = document.createElement('option');
                                option.value = section.section_id;
                                option.textContent = section.section_name;
                                sectionSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching sections:', error);
                        ToastNotifications.error('Error fetching sections');
                    });
            }
        });
    }

    // Initialize chosen2 for categories select
    if (typeof $ !== 'undefined' && $.fn && $.fn.chosen) {
        $('#category_ids').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_multiple: 'Select categories...'
        });
    }

    // Add event listeners to remove option buttons
    document.querySelectorAll('.remove-option').forEach(button => {
        button.addEventListener('click', function() {
            const optionsList = document.getElementById('optionsList');
            if (!optionsList) return;

            const optionRow = this.closest('.option-row');
            if (optionRow && optionsList.querySelectorAll('.option-row').length > 1) {
                optionsList.removeChild(optionRow);
                updateOptionValues();
            }
        });
    });
});
