// Image handling functions for question forms

// Question image functions
function previewQuestionImage(input) {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.classList.remove('hidden');

            // Upload the image to the server
            uploadImage(input.files[0], 'question');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeQuestionImage() {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');
    const imageIdInput = document.getElementById('question_image_id');

    preview.src = '';
    container.classList.add('hidden');
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('question_image');
    if (fileInput) fileInput.value = '';
}

// Solution image functions
function previewSolutionImage(input) {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.classList.remove('hidden');

            // Upload the image to the server
            uploadImage(input.files[0], 'solution');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeSolutionImage() {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');
    const imageIdInput = document.getElementById('solution_image_id');

    preview.src = '';
    container.classList.add('hidden');
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('solution_image');
    if (fileInput) fileInput.value = '';
}

// Option image functions
function previewOptionImage(input, index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.classList.remove('hidden');

            // Upload the image to the server
            uploadImage(input.files[0], 'option', index);
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeOptionImage(index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);
    const imageIdInput = document.getElementById(`option_image_id_${index}`);

    preview.src = '';
    container.classList.add('hidden');
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById(`option_image_${index}`);
    if (fileInput) fileInput.value = '';
}

function toggleOptionImageUpload(index) {
    const container = document.getElementById(`option_image_container_${index}`);
    if (container) {
        container.classList.toggle('hidden');
    }
}

// General image upload function
function uploadImage(file, type, index = null) {
    const formData = new FormData();
    formData.append('image', file);

    // Show loading indicator
    const loadingToast = document.createElement('div');
    loadingToast.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded shadow-lg z-50';
    loadingToast.innerHTML = `Uploading ${type} image...`;
    document.body.appendChild(loadingToast);

    fetch('/admin/images/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading indicator
        document.body.removeChild(loadingToast);

        if (data.success) {
            // Show success toast
            const successToast = document.createElement('div');
            successToast.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-4 py-2 rounded shadow-lg z-50';
            successToast.innerHTML = `${type.charAt(0).toUpperCase() + type.slice(1)} image uploaded successfully!`;
            document.body.appendChild(successToast);
            
            // Remove success toast after 3 seconds
            setTimeout(() => {
                document.body.removeChild(successToast);
            }, 3000);

            // Set the image ID in the appropriate hidden input
            if (type === 'question') {
                document.getElementById('question_image_id').value = data.image.id;
            } else if (type === 'solution') {
                document.getElementById('solution_image_id').value = data.image.id;
            } else if (type === 'option' && index !== null) {
                document.getElementById(`option_image_id_${index}`).value = data.image.id;
            }
        } else {
            // Show error toast
            const errorToast = document.createElement('div');
            errorToast.className = 'fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded shadow-lg z-50';
            errorToast.innerHTML = `Failed to upload image: ${data.message}`;
            document.body.appendChild(errorToast);
            
            // Remove error toast after 5 seconds
            setTimeout(() => {
                document.body.removeChild(errorToast);
            }, 5000);
        }
    })
    .catch(error => {
        // Remove loading indicator
        document.body.removeChild(loadingToast);

        // Show error toast
        const errorToast = document.createElement('div');
        errorToast.className = 'fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded shadow-lg z-50';
        errorToast.innerHTML = 'Error uploading image. Please try again.';
        document.body.appendChild(errorToast);
        
        // Remove error toast after 5 seconds
        setTimeout(() => {
            document.body.removeChild(errorToast);
        }, 5000);

        console.error('Error uploading image:', error);
    });
}

// Handle pasted images
function handlePastedImage(event, type, index = null) {
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    
    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
            event.preventDefault();
            
            const file = items[i].getAsFile();
            
            if (type === 'question') {
                // Display the pasted image in the question image preview
                const preview = document.getElementById('question_image_preview');
                const container = document.getElementById('question_image_preview_container');

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    container.classList.remove('hidden');

                    // Upload the image to the server
                    uploadImage(file, 'question');
                };
                reader.readAsDataURL(file);
            } else if (type === 'solution') {
                // Display the pasted image in the solution image preview
                const preview = document.getElementById('solution_image_preview');
                const container = document.getElementById('solution_image_preview_container');

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    container.classList.remove('hidden');

                    // Upload the image to the server
                    uploadImage(file, 'solution');
                };
                reader.readAsDataURL(file);
            } else if (type === 'option' && index !== null) {
                // Make sure the option image container is visible
                const container = document.getElementById(`option_image_container_${index}`);
                if (container) {
                    container.classList.remove('hidden');
                }

                // Display the pasted image in the option image preview
                const preview = document.getElementById(`option_image_preview_${index}`);
                const previewContainer = document.getElementById(`option_image_preview_container_${index}`);

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    previewContainer.classList.remove('hidden');

                    // Upload the image to the server
                    uploadImage(file, 'option', index);
                };
                reader.readAsDataURL(file);
            }

            break;
        }
    }
}

// Setup paste event listeners for image upload
document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('paste', function(e) {
        // Determine which section is currently active/focused
        const activeElement = document.activeElement;

        if (activeElement && activeElement.closest('.question-drop-zone')) {
            handlePastedImage(e, 'question');
        } else if (activeElement && activeElement.closest('.solution-drop-zone')) {
            handlePastedImage(e, 'solution');
        } else {
            // Check if any option container is active
            const optionContainer = activeElement && activeElement.closest('.option-drop-zone');
            if (optionContainer) {
                const index = optionContainer.dataset.index;
                if (index) {
                    handlePastedImage(e, 'option', parseInt(index));
                }
            }
        }
    });

    // Setup drag and drop for question image
    const questionDropZone = document.querySelector('.question-drop-zone');
    if (questionDropZone) {
        setupDragAndDrop(questionDropZone, 'question');
    }

    // Setup drag and drop for solution image
    const solutionDropZone = document.querySelector('.solution-drop-zone');
    if (solutionDropZone) {
        setupDragAndDrop(solutionDropZone, 'solution');
    }

    // Setup drag and drop for option images
    document.querySelectorAll('.option-drop-zone').forEach(dropZone => {
        const index = dropZone.dataset.index;
        if (index) {
            setupDragAndDrop(dropZone, 'option', parseInt(index));
        }
    });
});

// Setup drag and drop functionality
function setupDragAndDrop(dropZone, type, index = null) {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        dropZone.classList.add('border-indigo-500', 'bg-indigo-50');
    }

    function unhighlight() {
        dropZone.classList.remove('border-indigo-500', 'bg-indigo-50');
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];
            
            if (file.type.startsWith('image/')) {
                if (type === 'question') {
                    previewQuestionImage({ files: [file] });
                } else if (type === 'solution') {
                    previewSolutionImage({ files: [file] });
                } else if (type === 'option' && index !== null) {
                    previewOptionImage({ files: [file] }, index);
                }
            }
        }
    }
}

// Function to update option indexes when adding or removing options
function updateOptionIndexes() {
    const optionRows = document.querySelectorAll('.option-row');
    
    optionRows.forEach((row, index) => {
        // Update data-index attribute
        row.setAttribute('data-index', index);
        
        // Update radio button value
        const radioButton = row.querySelector('input[type="radio"]');
        if (radioButton) {
            radioButton.value = index;
        }
        
        // Update option image container id
        const imageContainer = row.querySelector('[id^="option_image_container_"]');
        if (imageContainer) {
            imageContainer.id = `option_image_container_${index}`;
        }
        
        // Update option drop zone data-index
        const dropZone = row.querySelector('.option-drop-zone');
        if (dropZone) {
            dropZone.setAttribute('data-index', index);
        }
        
        // Update option image input id
        const imageInput = row.querySelector('[id^="option_image_"]');
        if (imageInput && !imageInput.id.includes('_id_')) {
            imageInput.id = `option_image_${index}`;
            imageInput.setAttribute('onchange', `previewOptionImage(this, ${index})`);
        }
        
        // Update option image id input
        const imageIdInput = row.querySelector('[id^="option_image_id_"]');
        if (imageIdInput) {
            imageIdInput.id = `option_image_id_${index}`;
        }
        
        // Update preview container id
        const previewContainer = row.querySelector('[id^="option_image_preview_container_"]');
        if (previewContainer) {
            previewContainer.id = `option_image_preview_container_${index}`;
        }
        
        // Update preview image id
        const previewImage = row.querySelector('[id^="option_image_preview_"]');
        if (previewImage) {
            previewImage.id = `option_image_preview_${index}`;
        }
        
        // Update remove button onclick
        const removeButton = row.querySelector('button[onclick^="removeOptionImage"]');
        if (removeButton) {
            removeButton.setAttribute('onclick', `removeOptionImage(${index})`);
        }
        
        // Update toggle button onclick
        const toggleButton = row.querySelector('button[onclick^="toggleOptionImageUpload"]');
        if (toggleButton) {
            toggleButton.setAttribute('onclick', `toggleOptionImageUpload(${index})`);
        }
    });
}
