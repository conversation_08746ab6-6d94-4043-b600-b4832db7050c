// Section Questions JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Add Question Button
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    if (addQuestionBtn) {
        addQuestionBtn.addEventListener('click', function() {
            if (typeof openAddQuestionModal === 'function') {
                openAddQuestionModal();
            }
        });
    }

    // Edit question button event handlers
    document.querySelectorAll('.edit-question-btn').forEach(button => {
        button.addEventListener('click', function() {
            const questionId = this.getAttribute('data-question-id');
            const sectionId = document.querySelector('input[name="section_id"]').value;
            window.location.href = `/admin/questions/${questionId}/edit?returnUrl=/tests/admin/sections/${sectionId}/questions`;
        });
    });

    // Delete question button event handlers
    document.querySelectorAll('.delete-question-btn').forEach(button => {
        button.addEventListener('click', function() {
            const questionId = this.getAttribute('data-question-id');
            if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
                // Show loading toast
                if (typeof ToastNotifications !== 'undefined') {
                    ToastNotifications.info('Deleting question...');
                }

                fetch(`/admin/questions/${questionId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success toast
                        if (typeof ToastNotifications !== 'undefined') {
                            ToastNotifications.success('Question deleted successfully!');
                        }

                        // Remove the question from the UI
                        const questionElement = document.querySelector(`.question-item[data-question-id="${questionId}"]`);
                        if (questionElement) {
                            questionElement.remove();

                            // Update the question count
                            const questionsContainer = document.getElementById('questionsContainer');
                            const remainingQuestions = questionsContainer.querySelectorAll('.question-item');

                            // Update the count in the heading
                            const headingElement = document.querySelector('h3.text-lg');
                            if (headingElement) {
                                headingElement.textContent = `Questions (${remainingQuestions.length})`;
                            }

                            // Renumber the questions
                            remainingQuestions.forEach((question, index) => {
                                const numberBadge = question.querySelector('.bg-purple-100');
                                if (numberBadge) {
                                    numberBadge.textContent = index + 1;
                                }
                            });
                        }
                    } else {
                        // Show error toast
                        if (typeof ToastNotifications !== 'undefined') {
                            ToastNotifications.error(`Error deleting question: ${data.message || 'Unknown error'}`);
                        } else {
                            console.error(`Error deleting question: ${data.message || 'Unknown error'}`);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error deleting question:', error);
                    if (typeof ToastNotifications !== 'undefined') {
                        ToastNotifications.error('Error deleting question. Please try again.');
                    } else {
                        console.error('Error deleting question. Please try again.');
                    }
                });
            }
        });
    });
});
