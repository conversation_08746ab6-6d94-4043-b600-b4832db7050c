/**
 * Test Creator JavaScript
 * Handles form navigation, validation, and dynamic content for the test creation interface
 */

$(document).ready(function() {
    // Initialize global variables
    let currentStep = 1;
    let totalSteps = 5;
    let sectionCounter = 0;
    let activeSection = null;

    // Check if confirmation dialog is available
    console.log('Confirmation dialog available:', !!window.confirmationDialog);

    // Create confirmation dialog if it doesn't exist
    if (!window.confirmationDialog && typeof ConfirmationDialog === 'function') {
        window.confirmationDialog = new ConfirmationDialog();
        console.log('Confirmation dialog created in test-creator.js');
    }

    // Initialize components
    initializeForm();
    initializeStepNavigation();
    initializeEventListeners();

    /**
     * Initialize the form and its components
     */
    function initializeForm() {
        // Check if Chosen is available
        if (typeof $.fn.chosen !== 'function') {
            console.error('Chosen library not loaded. Falling back to native select.');
            return;
        }

        // Initialize Chosen for test categories
        try {
            $('#category').chosen({
                allow_single_deselect: true,
                width: '100%',
                search_contains: true
            });

            // Handle new category creation
            $('#category').on('change', function(_, params) {
                if (params && params.selected && !params.selected.match(/^\d+$/)) {
                    // This is a new category (not a numeric ID)
                    const newCategoryName = params.selected;

                    // Create the new category on the server
                    $.ajax({
                        url: '/admin/categories',
                        method: 'POST',
                        data: { name: newCategoryName },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Replace the text value with the new ID
                                const select = $('#category');
                                const options = select.val() || [];

                                // Replace the text value with the new ID
                                const index = options.indexOf(newCategoryName);
                                if (index !== -1) {
                                    options[index] = response.category_id.toString();

                                    // Update the select with the new options
                                    select.val(options);

                                    // Add the proper option
                                    if (select.find(`option[value="${response.category_id}"]`).length === 0) {
                                        select.append(new Option(newCategoryName, response.category_id, true, true));
                                    }

                                    // Update chosen
                                    select.trigger('chosen:updated');
                                }
                            }
                        },
                        error: function(error) {
                            console.error('Error creating new category:', error);
                        }
                    });
                }
            });

            // Load categories from server
            $.ajax({
                url: '/admin/categories/search',
                dataType: 'json',
                success: function(data) {
                    // Add options to the select
                    const select = $('#category');
                    data.forEach(function(cat) {
                        select.append(new Option(cat.name, cat.category_id));
                    });
                    // Update chosen
                    select.trigger('chosen:updated');
                },
                error: function(error) {
                    console.error('Error loading categories:', error);
                }
            });
        } catch (e) {
            console.error('Error initializing Chosen:', e);
        }

        // Initialize word counters
        $("#instructions").on('input', updateWordCount);

        // Update passing score display
        updatePassingScore();

        // Initialize passing score change event
        $("#passing_score").on('change', updatePassingScore);

        // Validate initial form state
        validateCurrentStep();

        // Add hidden field for form action type
        if ($("#formActionType").length === 0) {
            $("#testForm").append('<input type="hidden" id="formActionType" name="action_type" value="publish">');
        }
    }

    /**
     * Initialize step navigation
     */
    function initializeStepNavigation() {
        // Show first step
        showStep(1);

        // Next button click
        $("#nextStepBtn").on('click', function() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    showStep(currentStep + 1);

                    // If moving to preview step, generate preview
                    if (currentStep === 4) {
                        generatePreview();
                    }

                    // If moving to publish step, update summary
                    if (currentStep === 5) {
                        updateSummary();
                    }
                }
            } else {
                showValidationToast();
            }
        });

        // Previous button click
        $("#prevStepBtn").on('click', function() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        });

        // Direct step navigation
        $(".step-indicator").on('click', function() {
            const targetStep = parseInt($(this).data('step'));

            // Only allow moving to previous steps or validated steps
            if (targetStep < currentStep || validateCurrentStep()) {
                showStep(targetStep);
            } else {
                showValidationToast();
            }
        });
    }

    /**
     * Initialize other event listeners
     */
    function initializeEventListeners() {
        // Instructions overlay
        $("#viewInstructionsBtn").on('click', showInstructionsOverlay);
        $("#viewPreviewInstructionsBtn").on('click', showInstructionsOverlay);
        $("#closeInstructionsBtn, #confirmInstructionsBtn").on('click', hideInstructionsOverlay);
        // Add section button
        $("#addSectionBtn").on('click', addNewSection);

        // Save draft button
        $("#saveDraftBtn").on('click', saveDraft);

        // Submit button
        $("#testForm").on('submit', submitForm);

        // PDF download buttons
        $("#downloadPreviewPdfBtn").on('click', function() {
            generatePDF();
        });

        $("#downloadPublishPdfBtn").on('click', function() {
            generatePDF();
        });

        // Global event delegation for remove section buttons
        $(document).off('click', '.remove-section').on('click', '.remove-section', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation(); // Ensure no other handlers run
            console.log('Remove section button clicked (global handler)');

            // Get section ID from the parent section tab
            const parentSectionId = $(this).closest('.section-tab').data('section');
            console.log('Section ID from parent (global handler):', parentSectionId);

            if (parentSectionId) {
                removeSection(parentSectionId);
            } else {
                // Fallback to data attribute on the button itself
                const sectionId = $(this).attr('data-section');
                console.log('Section ID from data attribute (global handler):', sectionId);
                if (sectionId) {
                    removeSection(sectionId);
                } else {
                    showToast("Could not determine which section to remove", "error");
                }
            }

            return false; // Ensure the event doesn't bubble up
        });

        // Toggle fields visibility based on checkboxes
        $("#publishNow").on('change', function() {
            $("#schedulePublish").toggleClass('hidden', $(this).is(':checked'));
        });

        $("#requirePasscode").on('change', function() {
            $("#passcodeField").toggleClass('hidden', !$(this).is(':checked'));
        });

        $("#limitAttempts").on('change', function() {
            $("#attemptsField").toggleClass('hidden', !$(this).is(':checked'));
        });

        // Import questions file selection
        $("#importQuestionsFile").on('change', function() {
            const fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $("#selectedFileName").text(fileName).removeClass('hidden');
                $("#importQuestionsBtn").prop('disabled', false);
            } else {
                $("#selectedFileName").addClass('hidden');
                $("#importQuestionsBtn").prop('disabled', true);
            }
        });

        // Import questions button
        $("#importQuestionsBtn").on('click', importQuestions);
    }

    /**
     * Show the specified step and update UI
     */
    function showStep(step) {
        // Hide all steps and show the target step
        $(".form-step").addClass('hidden');
        $(`#step${step}`).removeClass('hidden');

        // Update step indicators
        $(".step-indicator").removeClass('active completed');
        $(".step-arrow").removeClass('active completed');

        for (let i = 1; i <= totalSteps; i++) {
            if (i < step) {
                $(`.step-indicator[data-step="${i}"]`).addClass('completed');
            } else if (i === step) {
                $(`.step-indicator[data-step="${i}"]`).addClass('active');
            }
        }

        // Update progress bar
        const progressPercentage = ((step - 1) / (totalSteps - 1)) * 100;
        $("#progress-bar").css('width', `${progressPercentage}%`);

        // Update current step
        currentStep = step;

        // Update navigation buttons
        $("#prevStepBtn").toggleClass('hidden', currentStep === 1);

        if (currentStep === totalSteps) {
            $("#nextStepBtn").addClass('hidden');
            $("#submitBtn").removeClass('hidden');
        } else {
            $("#nextStepBtn").removeClass('hidden');
            $("#submitBtn").addClass('hidden');
        }

        // Update next button text
        $("#nextButtonText").text(currentStep === totalSteps - 1 ? "Preview & Publish" : "Next Step");
    }

    /**
     * Validate the current step
     */
    function validateCurrentStep() {
        let isValid = true;

        // Remove any previous validation styling
        $(".field-error").removeClass('field-error');

        // Step-specific validation
        switch(currentStep) {
            case 1: // Basic Info
                const requiredFields = ["#testNameField", "#category", "#duration", "#passing_score", "#description"];

                // Check each required field
                requiredFields.forEach(field => {
                    if (!$(field).val()) {
                        $(field).addClass('field-error');
                        isValid = false;
                    }
                });

                // Additional validations
                if (parseInt($("#duration").val()) < 1) {
                    $("#duration").addClass('field-error');
                    isValid = false;
                }

                const passingScore = parseInt($("#passing_score").val());
                if (isNaN(passingScore) || passingScore < 0 || passingScore > 100) {
                    $("#passing_score").addClass('field-error');
                    isValid = false;
                }

                break;

            case 2: // Instructions
                if (!$("#instructions").val().trim()) {
                    $("#instructions").addClass('field-error');
                    isValid = false;
                }
                break;

            case 3: // Sections
                // Check if at least one section exists
                if (sectionCounter === 0) {
                    showToast("Please add at least one section", "error");
                    isValid = false;
                    break;
                }

                // Validate each section
                let allSectionsValid = true;

                // Check section names, question counts, and passing marks
                $(".section-content").each(function() {
                    const sectionTitle = $(this).find('.section-title').val();
                    const questionCount = parseInt($(this).find('.question-count').text());
                    const sectionMarks = parseInt($(this).find('.section-marks').text()) || 0;
                    const passingMarks = parseInt($(this).find('.section-passing-marks').val()) || 0;

                    if (!sectionTitle) {
                        $(this).find('.section-title').addClass('field-error');
                        allSectionsValid = false;
                    }

                    if (questionCount === 0) {
                        showToast(`Section "${sectionTitle || 'Untitled'}" must have at least one question`, "error");
                        allSectionsValid = false;
                    }

                    // Validate that passing marks are not greater than total marks
                    if (passingMarks > sectionMarks) {
                        $(this).find('.section-passing-marks').addClass('field-error');
                        showToast(`Section "${sectionTitle || 'Untitled'}" passing marks cannot be greater than total marks (${sectionMarks})`, "error");
                        allSectionsValid = false;
                    }
                });

                isValid = allSectionsValid;
                break;

            case 4: // Preview
                // No validation needed for preview
                break;

            case 5: // Publish
                // Validate scheduled date if publishing is scheduled
                if (!$("#publishNow").is(':checked') && !$("#publish_date").val()) {
                    $("#publish_date").addClass('field-error');
                    isValid = false;
                }

                // Validate passcode if required
                if ($("#requirePasscode").is(':checked') && !$("#test_passcode").val()) {
                    $("#test_passcode").addClass('field-error');
                    isValid = false;
                }

                break;
        }

        return isValid;
    }

    /**
     * Show validation error toast
     */
    function showValidationToast() {
        showToast("Please fill in all required fields correctly", "error");
    }

    /**
     * Show toast notification
     */
    function showToast(message, type = "info") {
        const bgColor = type === "error" ? "#ef4444" : (type === "success" ? "#10b981" : "#3b82f6");

        Toastify({
            text: message,
            duration: 3000,
            close: true,
            gravity: "top",
            position: "right",
            style: {
                background: bgColor
            },
            className: "toast-message",
            stopOnFocus: true
        }).showToast();
    }

    /**
     * Show instructions overlay
     */
    function showInstructionsOverlay() {
        // Get the instructions content
        const instructions = $("#instructions").val();

        if (!instructions.trim()) {
            showToast("Please enter instructions first", "error");
            return;
        }

        // Format the instructions with proper HTML
        const formattedInstructions = instructions
            .split('\n\n')
            .map(paragraph => `<p class="mb-3">${paragraph}</p>`)
            .join('');

        // Set the content
        $("#instructionsContent").html(formattedInstructions);

        // Show the overlay
        $("#instructionsOverlay").addClass('active');

        // Prevent body scrolling
        $("body").css('overflow', 'hidden');
    }

    /**
     * Hide instructions overlay
     */
    function hideInstructionsOverlay() {
        // Hide the overlay
        $("#instructionsOverlay").removeClass('active');

        // Re-enable body scrolling
        $("body").css('overflow', '');
    }

    /**
     * Update word count for instructions
     */
    function updateWordCount() {
        const text = $(this).val().trim();
        const wordCount = text ? text.split(/\s+/).length : 0;

        if ($(this).hasClass('section-instructions')) {
            // Section instructions
            $(this).closest('.section-content').find('.instruction-word-count').text(`${wordCount} words`);
        } else {
            // Main instructions
            $("#wordCount").text(`${wordCount} words (aim for 180-250)`);

            // Visual feedback based on word count
            if (wordCount > 0 && wordCount < 180) {
                $("#wordCount").css('color', '#f59e0b'); // Amber
            } else if (wordCount >= 180 && wordCount <= 250) {
                $("#wordCount").css('color', '#10b981'); // Green
            } else if (wordCount > 250) {
                $("#wordCount").css('color', '#ef4444'); // Red
            } else {
                $("#wordCount").css('color', ''); // Default
            }
        }
    }

    /**
     * Update the passing score display
     */
    function updatePassingScore() {
        const passingScore = $("#passing_score").val() || 0;
        $("#testPassingScore").text(`${passingScore}%`);
    }

    /**
     * Add a new section to the test
     */
    function addNewSection() {
        sectionCounter++;

        // Create new section tab
        const sectionTab = $($("#sectionTabTemplate").html());
        sectionTab.attr('data-section', sectionCounter);
        sectionTab.data('section', sectionCounter);
        sectionTab.find('.section-number').text(`Section ${sectionCounter}`);

        // Create new section content
        const sectionContent = $($("#sectionContentTemplate").html());
        sectionContent.attr('data-section', sectionCounter);

        // Add to DOM
        $("#sectionTabs").append(sectionTab);
        $("#sectionsContainer").append(sectionContent);

        // Add event listeners for the new section
        initializeSectionEvents(sectionTab, sectionContent);

        // Activate the new section
        activateSection(sectionCounter);

        // Update section statistics
        updateSectionStats();
    }

    /**
     * Initialize events for a new section
     */
    function initializeSectionEvents(sectionTab, sectionContent) {
        // Tab click to activate section
        sectionTab.on('click', function(e) {
            if (!$(e.target).hasClass('remove-section') && !$(e.target).closest('.remove-section').length) {
                activateSection($(this).data('section'));
            }
        });

        // We'll rely on the global event delegation for the remove section button
        // This ensures we don't have duplicate handlers that might conflict

        // Add question button
        sectionContent.find('.add-question-btn').on('click', function() {
            addNewQuestion($(this).closest('.section-content').data('section'));
        });

        // Word count for section instructions
        sectionContent.find('.section-instructions').on('input', updateWordCount);

        // Update section tab when name changes
        sectionContent.find('.section-title').on('input', function() {
            const sectionId = $(this).closest('.section-content').data('section');
            const sectionName = $(this).val() || `Section ${sectionId}`;
            $(`.section-tab[data-section="${sectionId}"]`).find('.section-number').text(sectionName);
        });

        // Make section tabs sortable
        if (!$("#sectionTabs").data('sortable')) {
            const sortable = new Sortable($("#sectionTabs")[0], {
                animation: 150,
                handle: '.drag-handle',
                onEnd: updateSectionOrder
            });

            $("#sectionTabs").data('sortable', sortable);
        }
    }

    /**
     * Activate a section (show its content)
     */
    function activateSection(sectionId) {
        // Deactivate all sections
        $(".section-tab").removeClass('text-indigo-600 border-indigo-500').addClass('text-gray-700 border-transparent');
        $(".section-content").addClass('hidden');

        // Activate target section
        $(`.section-tab[data-section="${sectionId}"]`).addClass('text-indigo-600 border-indigo-500').removeClass('text-gray-700 border-transparent');
        $(`.section-content[data-section="${sectionId}"]`).removeClass('hidden');

        activeSection = sectionId;
    }

    /**
     * Remove a section
     */
    function removeSection(sectionId) {
        console.log('removeSection called with sectionId:', sectionId);

        // Validate section ID
        if (!sectionId) {
            console.error('Invalid section ID:', sectionId);
            showToast("Error: Invalid section ID", "error");
            return;
        }

        // Get section elements
        const sectionTab = $(`.section-tab[data-section="${sectionId}"]`);
        const sectionContent = $(`.section-content[data-section="${sectionId}"]`);

        // Validate that elements exist
        if (sectionTab.length === 0 || sectionContent.length === 0) {
            console.error('Section elements not found:', { sectionId, tabExists: sectionTab.length > 0, contentExists: sectionContent.length > 0 });
            showToast("Error: Section not found", "error");
            return;
        }

        // Get section name for the confirmation message
        const sectionName = sectionTab.find('.section-number').text();
        const questionCount = sectionContent.find('.question-container').length;

        console.log('Section details:', { sectionName, questionCount });

        // Create confirmation dialog if it doesn't exist
        if (!window.confirmationDialog) {
            console.log('Creating new confirmation dialog');
            window.confirmationDialog = new ConfirmationDialog();
        }

        // Show confirmation dialog
        window.confirmationDialog.show({
            title: 'Remove Section',
            message: `Are you sure you want to remove "${sectionName}"? This will delete all ${questionCount} question(s) in this section. This action cannot be undone.`,
            confirmText: 'Remove Section',
            cancelText: 'Cancel',
            type: 'danger',
            onConfirm: () => {
                console.log('Confirmation dialog confirmed, removing section:', sectionId);

                // Remove section tab and content
                // First detach all event handlers to prevent memory leaks
                sectionTab.find('.remove-section').off('click');
                sectionTab.off('click');

                // Then remove the elements from the DOM
                sectionTab.remove();
                sectionContent.remove();

                // Remove any X icons that might be orphaned
                $('.remove-section-wrapper').each(function() {
                    if ($(this).closest('.section-tab').length === 0) {
                        $(this).remove();
                    }
                });

                console.log('Section elements removed');

                // Update section numbers for remaining sections
                updateSectionOrder();

                // Update section statistics
                updateSectionStats();

                // Activate another section if available
                if ($(".section-tab").length > 0) {
                    const firstSectionId = $(".section-tab").first().data('section');
                    console.log('Activating first available section:', firstSectionId);
                    activateSection(firstSectionId);
                } else {
                    console.log('No sections left, setting activeSection to null');
                    activeSection = null;

                    // Add a new section if none left
                    addNewSection();
                }

                // Show success notification
                showToast("Section deleted successfully", "success");

                // Prevent event bubbling
                return false;
            }
        });
    }

    /**
     * Update section ordering after drag and drop or removal
     */
    function updateSectionOrder() {
        // Update section numbers
        $(".section-tab").each(function(index) {
            $(this).find('.section-number').text(`Section ${index + 1}`);
        });

        // Update question numbers to be contiguous across sections
        let questionNumber = 1;
        $(".section-content").each(function() {
            $(this).find('.question-container').each(function() {
                $(this).find('.question-number').text(questionNumber);
                questionNumber++;
            });
        });
    }

    /**
     * Add a new question to a section
     */
    function addNewQuestion(sectionId) {
        const sectionContent = $(`.section-content[data-section="${sectionId}"]`);
        const questionsContainer = sectionContent.find('.questions-container');

        // Hide empty section message
        sectionContent.find('.empty-section-message').addClass('hidden');

        // Create new question from template
        const questionElement = $($("#questionTemplate").html());

        // Set question number (will be updated by updateSectionOrder)
        questionElement.find('.question-number').text('New');

        // Add to DOM
        questionsContainer.append(questionElement);

        // Initialize question events
        initializeQuestionEvents(questionElement);

        // Set initial question type
        setQuestionType(questionElement, 'mcq');

        // Update question statistics
        updateQuestionStats(sectionId);

        // Update question numbers to be contiguous
        updateSectionOrder();
    }

    /**
     * Initialize events for a new question
     */
    function initializeQuestionEvents(questionElement) {
        // Question type change
        questionElement.find('.question-type').on('change', function() {
            const questionType = $(this).val();
            setQuestionType($(this).closest('.question-container'), questionType);
        });

        // Remove question button
        questionElement.find('.remove-question').on('click', function() {
            removeQuestion($(this).closest('.question-container'));
        });

        // Update question stats when marks change
        questionElement.find('.question-marks').on('change', function() {
            updateQuestionStats($(this).closest('.section-content').data('section'));
        });

        // Question image upload functionality
        questionElement.find('.upload-question-image').on('click', function() {
            const container = $(this).closest('.question-container');
            const imageUrlInput = container.find('.question-image-url');
            const imagePreview = container.find('.question-image-preview');
            const removeBtn = container.find('.remove-question-image');

            // Create a file input element
            const fileInput = $('<input type="file" accept="image/*" style="display:none">');
            $('body').append(fileInput);

            fileInput.on('change', function() {
                const file = this.files[0];
                if (!file) return;

                // Check file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showToast("Image size exceeds 2MB limit", "error");
                    return;
                }

                // Create form data
                const formData = new FormData();
                formData.append('image', file);

                // Show loading toast
                showToast("Uploading image...", "info");

                // Upload the image
                $.ajax({
                    url: '/upload/question-image',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // Update the image URL input
                            imageUrlInput.val(response.imageUrl);

                            // Update the preview
                            imagePreview.find('img').attr('src', response.imageUrl);
                            imagePreview.removeClass('hidden');
                            removeBtn.removeClass('hidden');

                            showToast("Image uploaded successfully", "success");
                        } else {
                            showToast(response.message || "Error uploading image", "error");
                        }
                    },
                    error: function(error) {
                        console.error('Error uploading image:', error);
                        showToast("Error uploading image", "error");
                    },
                    complete: function() {
                        // Remove the temporary file input
                        fileInput.remove();
                    }
                });
            });

            // Trigger the file input click
            fileInput.click();
        });

        questionElement.find('.remove-question-image').on('click', function() {
            const container = $(this).closest('.question-container');
            container.find('.question-image-url').val('');
            container.find('.question-image-preview').addClass('hidden');
            $(this).addClass('hidden');
        });

        // Solution image upload functionality
        questionElement.find('.upload-solution-image').on('click', function() {
            const container = $(this).closest('.question-container');
            const imageUrlInput = container.find('.solution-image-url');
            const imagePreview = container.find('.solution-image-preview');
            const removeBtn = container.find('.remove-solution-image');

            // Create a file input element
            const fileInput = $('<input type="file" accept="image/*" style="display:none">');
            $('body').append(fileInput);

            fileInput.on('change', function() {
                const file = this.files[0];
                if (!file) return;

                // Check file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showToast("Image size exceeds 2MB limit", "error");
                    return;
                }

                // Create form data
                const formData = new FormData();
                formData.append('image', file);

                // Show loading toast
                showToast("Uploading image...", "info");

                // Upload the image
                $.ajax({
                    url: '/upload/question-image',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // Update the image URL input
                            imageUrlInput.val(response.imageUrl);

                            // Update the preview
                            imagePreview.find('img').attr('src', response.imageUrl);
                            imagePreview.removeClass('hidden');
                            removeBtn.removeClass('hidden');

                            showToast("Image uploaded successfully", "success");
                        } else {
                            showToast(response.message || "Error uploading image", "error");
                        }
                    },
                    error: function(error) {
                        console.error('Error uploading image:', error);
                        showToast("Error uploading image", "error");
                    },
                    complete: function() {
                        // Remove the temporary file input
                        fileInput.remove();
                    }
                });
            });

            // Trigger the file input click
            fileInput.click();
        });

        questionElement.find('.remove-solution-image').on('click', function() {
            const container = $(this).closest('.question-container');
            container.find('.solution-image-url').val('');
            container.find('.solution-image-preview').addClass('hidden');
            $(this).addClass('hidden');
        });

        // Initialize Chosen for categories if available
        if (typeof $.fn.chosen === 'function') {
            try {
                const questionCategory = questionElement.find('.chosen-category');

                questionCategory.chosen({
                    allow_single_deselect: true,
                    width: '100%',
                    search_contains: true
                });

                // Handle new category creation
                questionCategory.on('change', function(_, params) {
                    if (params && params.selected && !params.selected.match(/^\d+$/)) {
                        // This is a new category (not a numeric ID)
                        const newCategoryName = params.selected;
                        const select = $(this);

                        // Create the new category on the server
                        $.ajax({
                            url: '/admin/categories',
                            method: 'POST',
                            data: { name: newCategoryName },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    // Replace the text value with the new ID
                                    const options = select.val() || [];

                                    // Replace the text value with the new ID
                                    const index = options.indexOf(newCategoryName);
                                    if (index !== -1) {
                                        options[index] = response.category_id.toString();

                                        // Update the select with the new options
                                        select.val(options);

                                        // Add the proper option
                                        if (select.find(`option[value="${response.category_id}"]`).length === 0) {
                                            select.append(new Option(newCategoryName, response.category_id, true, true));
                                        }

                                        // Update chosen
                                        select.trigger('chosen:updated');
                                    }
                                }
                            },
                            error: function(error) {
                                console.error('Error creating new category:', error);
                            }
                        });
                    }
                });

                // Load categories from server
                $.ajax({
                    url: '/admin/categories/search',
                    dataType: 'json',
                    success: function(data) {
                        // Add options to the select
                        const select = questionElement.find('.chosen-category');
                        data.forEach(function(cat) {
                            select.append(new Option(cat.name, cat.category_id));
                        });
                        // Update chosen
                        select.trigger('chosen:updated');
                    },
                    error: function(error) {
                        console.error('Error loading categories:', error);
                    }
                });
            } catch (e) {
                console.error('Error initializing Chosen for question categories:', e);
            }
        } else {
            console.warn('Chosen library not available for question categories');
        }
    }

    /**
     * Set the question type and update the answer options accordingly
     */
    function setQuestionType(questionElement, questionType) {
        // Update badge
        const typeBadge = questionElement.find('.question-type-badge');
        typeBadge.text(getQuestionTypeLabel(questionType));

        // Update badge color
        typeBadge.removeClass('bg-blue-100 text-blue-800 bg-green-100 text-green-800 bg-purple-100 text-purple-800 bg-yellow-100 text-yellow-800');

        switch(questionType) {
            case 'mcq':
                typeBadge.addClass('bg-blue-100 text-blue-800');
                break;
            case 'true_false':
                typeBadge.addClass('bg-green-100 text-green-800');
                break;
            case 'short_answer':
                typeBadge.addClass('bg-purple-100 text-purple-800');
                break;
            case 'essay':
                typeBadge.addClass('bg-yellow-100 text-yellow-800');
                break;
        }

        // Clear answer container
        const answerContainer = questionElement.find('.answer-container');
        answerContainer.empty();

        // Add appropriate answer options
        switch(questionType) {
            case 'mcq':
                const mcqTemplate = $($("#mcqOptionsTemplate").html());
                answerContainer.append(mcqTemplate);

                // Add 4 default options
                for (let i = 0; i < 4; i++) {
                    addMCQOption(mcqTemplate.find('.options-grid'));
                }

                // Add option button event
                mcqTemplate.find('.add-option-btn').on('click', function() {
                    addMCQOption($(this).closest('.mb-4').find('.options-grid'));
                });
                break;

            case 'true_false':
                const tfTemplate = $($("#truefalseTemplate").html());
                answerContainer.append(tfTemplate);
                break;

            case 'short_answer':
                const saTemplate = $($("#shortAnswerTemplate").html());
                answerContainer.append(saTemplate);
                break;

            case 'essay':
                const essayTemplate = $($("#essayTemplate").html());
                answerContainer.append(essayTemplate);
                break;
        }

        // Add essay linking component to all question types
        addEssayLinkingComponent(questionElement);

        // Update section question type counts
        const sectionId = questionElement.closest('.section-content').data('section');
        updateQuestionStats(sectionId);
    }

    /**
     * Add essay linking component to a question
     */
    function addEssayLinkingComponent(questionElement) {
        // Save current essay ID if it exists
        const currentEssayId = questionElement.find('.question-essay-id').val();
        console.log('Adding essay linking component, current essay ID:', currentEssayId);

        // Clear the essay linking wrapper
        const essayLinkingWrapper = questionElement.find('.essay-linking-wrapper');
        essayLinkingWrapper.empty();

        // Add the essay linking component
        const essayLinkingTemplate = $($("#essayLinkingTemplate").html());
        essayLinkingWrapper.append(essayLinkingTemplate);

        const linkedEssaySelect = essayLinkingTemplate.find('.linked-essay');

        // Add event listener for the fetch essays button
        essayLinkingTemplate.find('.fetch-essays-btn').on('click', function() {
            fetchEssays(linkedEssaySelect);
        });

        // Add event listener for the clear essay button
        essayLinkingTemplate.find('.clear-essay-btn').on('click', function() {
            console.log('Clearing essay link');
            // Clear the select element
            linkedEssaySelect.val('');

            // Clear the hidden field
            questionElement.find('.question-essay-id').val('');

            // Show success message
            showToast("Essay link cleared", "success");
        });

        // If there was a previously selected essay, restore it
        if (currentEssayId) {
            // Check if the select already has options
            if (linkedEssaySelect.find('option').length <= 1) {
                // Fetch essays and then set the value
                fetchEssays(linkedEssaySelect, currentEssayId);
            } else {
                // Just set the value if options already exist
                linkedEssaySelect.val(currentEssayId);
            }
        } else {
            // If no essay is linked, fetch essays anyway to populate the dropdown
            if (linkedEssaySelect.find('option').length <= 1) {
                fetchEssays(linkedEssaySelect);
            }
        }

        // Add change event to update the hidden field
        linkedEssaySelect.on('change', function() {
            const selectedEssayId = $(this).val();
            console.log('Essay selection changed to:', selectedEssayId);
            questionElement.find('.question-essay-id').val(selectedEssayId);
        });
    }

    /**
     * Add an MCQ option to the options grid
     */
    function addMCQOption(optionsGrid) {
        const optionTemplate = $($("#mcqOptionItemTemplate").html());

        // Add to grid
        optionsGrid.append(optionTemplate);

        // Set up events
        optionTemplate.find('.correct-option').on('change', function() {
            $(this).closest('.option-wrapper').toggleClass('selected', $(this).is(':checked'));
        });
    }

    /**
     * Remove a question
     */
    function removeQuestion(questionElement) {
        // Get question details for the confirmation message
        const questionNumber = questionElement.find('.question-number').text();
        const questionText = questionElement.find('.question-text').val() || 'Untitled Question';
        const questionPreview = questionText.length > 50 ? questionText.substring(0, 50) + '...' : questionText;
        const sectionContent = questionElement.closest('.section-content');
        const sectionId = sectionContent.data('section');
        const sectionName = $(`.section-tab[data-section="${sectionId}"]`).find('.section-number').text();

        // Create confirmation dialog if it doesn't exist
        if (!window.confirmationDialog) {
            window.confirmationDialog = new ConfirmationDialog();
        }

        // Show confirmation dialog
        window.confirmationDialog.show({
            title: 'Remove Question',
            message: `Are you sure you want to remove Question ${questionNumber} from ${sectionName}?\n\n"${questionPreview}"\n\nThis action cannot be undone.`,
            confirmText: 'Remove Question',
            cancelText: 'Cancel',
            type: 'danger',
            onConfirm: () => {
                // Remove the question
                questionElement.remove();

                // Update all question numbers to be contiguous
                updateSectionOrder();

                // Show empty message if no questions remain
                if (sectionContent.find('.question-container').length === 0) {
                    sectionContent.find('.empty-section-message').removeClass('hidden');
                }

                // Update section statistics
                updateQuestionStats(sectionId);

                // Show success notification
                showToast("Question deleted successfully", "success");
            }
        });
    }

    /**
     * Update statistics for a section's questions
     */
    function updateQuestionStats(sectionId) {
        const sectionContent = $(`.section-content[data-section="${sectionId}"]`);
        const questionsContainer = sectionContent.find('.questions-container');

        // Count questions
        const totalQuestions = questionsContainer.find('.question-container').length;

        // Count by type
        const mcqCount = questionsContainer.find('.question-type-badge:contains("Multiple Choice")').length;
        const tfCount = questionsContainer.find('.question-type-badge:contains("True/False")').length;
        const saCount = questionsContainer.find('.question-type-badge:contains("Short Answer")').length;
        const essayCount = questionsContainer.find('.question-type-badge:contains("Essay")').length;

        // Calculate total marks
        let totalMarks = 0;
        questionsContainer.find('.question-marks').each(function() {
            totalMarks += parseInt($(this).val()) || 0;
        });

        // Update counts
        sectionContent.find('.question-count').text(totalQuestions);
        $(`.section-tab[data-section="${sectionId}"]`).find('.question-count').text(totalQuestions);
        sectionContent.find('.section-marks').text(totalMarks);

        sectionContent.find('.mcq-count').text(mcqCount);
        sectionContent.find('.tf-count').text(tfCount);
        sectionContent.find('.sa-count').text(saCount);
        sectionContent.find('.essay-count').text(essayCount);

        // Update overall statistics
        updateSectionStats();
    }

    /**
     * Update overall section statistics
     */
    function updateSectionStats() {
        // Count sections
        const totalSections = $(".section-content").length;

        // Count questions across all sections
        let totalQuestions = 0;
        $(".section-content .question-count").each(function() {
            totalQuestions += parseInt($(this).text()) || 0;
        });

        // Calculate total marks
        let totalMarks = 0;
        $(".section-content .section-marks").each(function() {
            totalMarks += parseInt($(this).text()) || 0;
        });

        // Update statistics display
        $("#totalSectionsCount").text(totalSections);
        $("#totalQuestionsCount").text(totalQuestions);
        $("#totalMarksCount").text(totalMarks);
    }

    /**
     * Get readable label for question type
     */
    function getQuestionTypeLabel(questionType) {
        switch(questionType) {
            case 'mcq': return 'Multiple Choice';
            case 'true_false': return 'True/False';
            case 'short_answer': return 'Short Answer';
            case 'essay': return 'Essay';
            default: return 'Unknown Type';
        }
    }

    /**
     * Generate test preview
     */
    function generatePreview() {
        const previewContainer = $("#testPreview");
        previewContainer.empty();

        // Get test details
        const testName = $("#testNameField").val();
        const duration = $("#duration").val();

        // Create preview header
        const previewHeaderHTML = `
            <div class="p-4 border border-gray-200 rounded-lg mb-4">
                <h2 class="text-xl font-bold text-center mb-4">${testName}</h2>
                <div class="flex justify-between items-center border-b pb-2 mb-4">
                    <span class="text-sm"><strong>Duration:</strong> ${duration} minutes</span>
                    <span class="text-sm"><strong>Passing Score:</strong> ${$("#passing_score").val()}%</span>
                </div>

                <!-- Instructions are shown in the overlay -->
                <div class="mb-2 flex justify-end">
                    <button type="button" id="previewInstructionsBtn" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Instructions
                    </button>
                </div>
            </div>
        `;

        // Create section tabs for preview
        const tabsHTML = `
            <div class="border-b border-gray-200 mb-4">
                <ul class="flex flex-wrap -mb-px" id="preview-tabs" role="tablist">
                    ${generatePreviewTabs()}
                </ul>
            </div>
            <div id="preview-tab-content">
                ${generatePreviewTabContent()}
            </div>
        `;

        previewContainer.html(previewHeaderHTML + tabsHTML);

        // Add event listeners for tabs
        $("#preview-tabs .preview-tab").on('click', function() {
            const target = $(this).data('target');

            // Update active tab
            $("#preview-tabs .preview-tab").removeClass('border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
            $(this).removeClass('border-transparent text-gray-500').addClass('border-blue-500 text-blue-600');

            // Show active content
            $("#preview-tab-content .tab-pane").addClass('hidden');
            $(`#${target}`).removeClass('hidden');
        });

        // Activate first tab
        $("#preview-tabs .preview-tab").first().click();

        // Add event listener for the preview instructions button
        $("#previewInstructionsBtn").on('click', showInstructionsOverlay);
    }

    /**
     * Generate preview tabs for sections
     */
    function generatePreviewTabs() {
        let tabsHTML = '';

        $(".section-content").each(function(index) {
            const sectionName = $(this).find('.section-title').val() || `Section ${index + 1}`;
            const isActive = index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500';

            tabsHTML += `
                <li class="mr-2" role="presentation">
                    <button class="preview-tab inline-block p-4 border-b-2 rounded-t-lg ${isActive} hover:text-gray-600 hover:border-gray-300"
                            data-target="preview-section-${index + 1}" type="button" role="tab">
                        ${sectionName}
                    </button>
                </li>
            `;
        });

        return tabsHTML;
    }

    /**
     * Generate preview tab content for sections
     */
    function generatePreviewTabContent() {
        let contentHTML = '';

        $(".section-content").each(function(index) {
            const sectionName = $(this).find('.section-title').val() || `Section ${index + 1}`;
            const sectionInstructions = $(this).find('.section-instructions').val();
            const sectionQuestions = generateQuestionsPreview($(this));
            const isActive = index === 0 ? '' : 'hidden';

            contentHTML += `
                <div id="preview-section-${index + 1}" class="tab-pane ${isActive}" role="tabpanel">
                    <div class="border border-gray-200 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-medium mb-2">${sectionName}</h3>
                        ${sectionInstructions ? `<div class="text-sm italic mb-4">${sectionInstructions.replace(/\n/g, '<br>')}</div>` : ''}

                        <div class="space-y-4 mt-4">
                            ${sectionQuestions}
                        </div>
                    </div>
                </div>
            `;
        });

        return contentHTML;
    }

    /**
     * Generate preview for questions in a section
     */
    function generateQuestionsPreview(sectionElement) {
        let questionsHTML = '';

        sectionElement.find('.question-container').each(function(index) {
            const questionText = $(this).find('.question-text').val() || 'Untitled Question';
            const questionType = $(this).find('.question-type').val();
            const marks = $(this).find('.question-marks').val() || '0';
            const solution = $(this).find('.question-solution').val();
            const imageUrl = $(this).find('.question-image-url').val();
            const solutionImageUrl = $(this).find('.solution-image-url').val();

            // Get categories
            const categories = [];
            $(this).find('.chosen-category option:selected').each(function() {
                categories.push($(this).text());
            });

            const categoryDisplay = categories.length > 0 ?
                `<span class="ml-2 text-xs bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full">${categories.join(', ')}</span>` : '';

            let answerHTML = '';
            let correctAnswerHTML = '';

            // Generate different answer previews based on question type
            switch(questionType) {
                case 'mcq':
                    answerHTML = '<div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">';
                    const correctOptions = [];

                    $(this).find('.option-wrapper').each(function(optionIndex) {
                        const optionText = $(this).find('.option-text').val() || `Option ${optionIndex + 1}`;
                        const isCorrect = $(this).find('.correct-option').is(':checked');

                        if (isCorrect) {
                            correctOptions.push(optionText);
                        }

                        answerHTML += `
                            <div class="flex items-center border border-gray-200 rounded-md p-2 ${isCorrect ? 'bg-green-50 border-green-200' : ''}">
                                <input type="checkbox" disabled ${isCorrect ? 'checked' : ''} class="h-4 w-4 mr-2 ${isCorrect ? 'text-green-600' : ''}">
                                <span>${optionText}</span>
                            </div>
                        `;
                    });

                    answerHTML += '</div>';

                    if (correctOptions.length > 0) {
                        correctAnswerHTML = `
                            <div class="mt-2 text-sm text-green-700">
                                <span class="font-medium">Correct Answer(s):</span> ${correctOptions.join(', ')}
                            </div>
                        `;
                    }
                    break;

                case 'true_false':
                    const tfCorrectAnswer = $(this).find('input[name="correct_option"]:checked').val() || '';

                    answerHTML = `
                        <div class="grid grid-cols-2 gap-3 mt-2">
                            <div class="flex items-center border border-gray-200 rounded-md p-2 ${tfCorrectAnswer === 'true' ? 'bg-green-50 border-green-200' : ''}">
                                <input type="radio" disabled ${tfCorrectAnswer === 'true' ? 'checked' : ''} class="h-4 w-4 mr-2">
                                <span>True</span>
                            </div>
                            <div class="flex items-center border border-gray-200 rounded-md p-2 ${tfCorrectAnswer === 'false' ? 'bg-green-50 border-green-200' : ''}">
                                <input type="radio" disabled ${tfCorrectAnswer === 'false' ? 'checked' : ''} class="h-4 w-4 mr-2">
                                <span>False</span>
                            </div>
                        </div>
                    `;

                    if (tfCorrectAnswer) {
                        correctAnswerHTML = `
                            <div class="mt-2 text-sm text-green-700">
                                <span class="font-medium">Correct Answer:</span> ${tfCorrectAnswer.charAt(0).toUpperCase() + tfCorrectAnswer.slice(1)}
                            </div>
                        `;
                    }
                    break;

                case 'short_answer':
                    const saCorrectAnswer = $(this).find('.correct-answer').val() || '';

                    answerHTML = `
                        <div class="mt-2">
                            <input type="text" disabled class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Your answer">
                        </div>
                    `;

                    if (saCorrectAnswer) {
                        correctAnswerHTML = `
                            <div class="mt-2 text-sm text-green-700">
                                <span class="font-medium">Correct Answer:</span> ${saCorrectAnswer}
                            </div>
                        `;
                    }
                    break;

                case 'essay':
                    const minWordCount = $(this).find('.min-word-count').val();
                    const maxWordCount = $(this).find('.max-word-count').val();
                    let wordCountText = '';

                    if (minWordCount && maxWordCount) {
                        wordCountText = `(${minWordCount}-${maxWordCount} words)`;
                    } else if (minWordCount) {
                        wordCountText = `(min. ${minWordCount} words)`;
                    } else if (maxWordCount) {
                        wordCountText = `(max. ${maxWordCount} words)`;
                    }

                    answerHTML = `
                        <div class="mt-2">
                            <textarea disabled rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                placeholder="Your answer ${wordCountText}"></textarea>
                        </div>
                    `;
                    break;
            }

            // Solution/Explanation HTML
            const solutionHTML = solution ? `
                <div class="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="text-xs font-medium text-blue-700 mb-1">Solution/Explanation:</div>
                    <div class="text-sm text-blue-800">${solution}</div>
                </div>
            ` : '';

            questionsHTML += `
                <div class="border border-gray-200 rounded-lg p-4 bg-white">
                    <div class="flex justify-between items-center mb-2">
                        <div class="flex items-center">
                            <span class="font-medium">Q${index + 1}.</span>
                            ${categoryDisplay}
                        </div>
                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">${marks} marks</span>
                    </div>
                    <div class="mb-3">${questionText}</div>
                    ${imageUrl ? `<div class="mb-3"><img src="${imageUrl}" alt="Question image" class="max-h-40 border border-gray-300 rounded-md"></div>` : ''}
                    ${answerHTML}
                    ${correctAnswerHTML}
                    ${solutionHTML}
                    ${solutionImageUrl ? `<div class="mt-3"><img src="${solutionImageUrl}" alt="Solution image" class="max-h-40 border border-gray-300 rounded-md"></div>` : ''}
                </div>
            `;
        });

        return questionsHTML;
    }

    /**
     * Update test summary on the publish step
     */
    function updateSummary() {
        // Update summary fields
        $("#summary-name").text($("#testNameField").val() || '-');
        $("#summary-duration").text($("#duration").val() || '-');
        $("#summary-passing").text($("#passing_score").val() || '-');
        $("#summary-sections").text($("#totalSectionsCount").text() || '-');
        $("#summary-questions").text($("#totalQuestionsCount").text() || '-');
        $("#summary-marks").text($("#totalMarksCount").text() || '-');

        // Initialize publish toggle state
        const publishNowChecked = $("#publishNow").is(':checked');
        $("#schedulePublish").toggleClass('hidden', publishNowChecked);

        // If publish toggle is not checked, make sure schedule publish is visible
        if (!publishNowChecked) {
            $("#schedulePublish").removeClass('hidden');
        }
    }

    /**
     * Save draft
     */
    function saveDraft() {
        // Validate at least the exam name before saving
        const testName = $("#testNameField").val();
        if (!testName) {
            showToast("Test name is required", "error");
            $("#testNameField").addClass('field-error').focus();
            return;
        }

        // Validate other required fields based on current step
        if (currentStep >= 2) {
            // Validate instructions
            const instructions = $("#instructions").val();
            if (!instructions || instructions.trim() === '') {
                showToast("Instructions are required", "error");
                $("#instructions").addClass('field-error').focus();
                return;
            }

            // Validate duration
            const duration = $("#duration").val();
            if (!duration || isNaN(duration) || parseInt(duration) <= 0) {
                showToast("Duration must be a positive number", "error");
                $("#duration").addClass('field-error').focus();
                return;
            }

            // Validate passing score
            const passingScore = $("#passing_score").val();
            if (!passingScore || isNaN(passingScore) || parseInt(passingScore) < 0 || parseInt(passingScore) > 100) {
                showToast("Passing score must be between 0 and 100", "error");
                $("#passing_score").addClass('field-error').focus();
                return;
            }
        }

        // Validate sections and questions if on step 3
        if (currentStep >= 3) {
            // Check if there are any sections
            if ($(".section-content").length === 0) {
                showToast("At least one section is required", "error");
                return;
            }

            // Check each section
            let hasValidSection = false;
            $(".section-content").each(function() {
                const sectionTitle = $(this).find('.section-title').val();
                if (sectionTitle && sectionTitle.trim() !== '') {
                    hasValidSection = true;
                }
            });

            if (!hasValidSection) {
                showToast("At least one section must have a title", "error");
                return;
            }
        }

        showToast("Saving draft...", "info");

        // Disable the save draft button to prevent multiple submissions
        $("#saveDraftBtn").prop('disabled', true);

        // Collect basic form data
        const jsonData = {
            exam_name: testName,
            is_draft: '1',
            action_type: 'save_draft',
            description: $("#description").val() || '',
            duration: $("#duration").val() || '60',
            passing_score: $("#passing_score").val() || '40',
            instructions: $("#instructions").val() || '',
            category: $('#category').val() || [] // Get selected categories (works with both native select and Chosen)
        };

        // Get existing exam_id if available
        const examId = $("#exam_id").val() || '';

        // Add exam_id if it exists
        if (examId) {
            jsonData.exam_id = examId;
        }

        // Collect sections data if we're on step 3 or beyond
        if (currentStep >= 3) {
            jsonData.sections = [];

            // Iterate through each section
            $(".section-content").each(function() {
                const sectionId = $(this).data('section');
                const sectionTitle = $(this).find('.section-title').val() || `Section ${sectionId}`;
                const sectionInstructions = $(this).find('.section-instructions').val() || '';

                const sectionPassingMarks = $(this).find('.section-passing-marks').val() || null;

                const sectionData = {
                    name: sectionTitle,
                    section_title: sectionTitle,
                    section_instructions: sectionInstructions,
                    instructions: sectionInstructions, // Add this line to match the server-side property name
                    passing_marks: sectionPassingMarks,
                    questions: []
                };

                // Collect questions for this section
                $(this).find('.question-container').each(function() {
                    const questionType = $(this).find('.question-type').val();
                    const questionText = $(this).find('.question-text').val();
                    const questionMarks = $(this).find('.question-marks').val() || 1;
                    const questionSolution = $(this).find('.question-solution').val() || '';

                    // Skip if question text is empty
                    if (!questionText || questionText.trim() === '') {
                        return true; // continue to next question
                    }

                    // Get categories for this question (works with both native select and Chosen)
                    const categories = $(this).find('.chosen-category').val() || [];

                    const questionData = {
                        question_text: questionText,
                        question_type: questionType,
                        marks: questionMarks,
                        solution_text: questionSolution,
                        category: categories,
                        options: [],
                        image_url: $(this).find('.question-image-url').val() || null,
                        solution_image_url: $(this).find('.solution-image-url').val() || null
                    };

                    // Handle different question types
                    if (questionType === 'mcq') {
                        // Collect MCQ options
                        $(this).find('.option-wrapper').each(function() {
                            const optionText = $(this).find('.option-text').val();
                            const isCorrect = $(this).find('.correct-option').prop('checked');

                            // Skip if option text is empty
                            if (!optionText || optionText.trim() === '') {
                                return true; // continue to next option
                            }

                            questionData.options.push({
                                option_text: optionText,
                                is_correct: isCorrect
                            });
                        });

                        // Set correct answer based on selected options
                        const correctOptions = questionData.options.filter(opt => opt.is_correct);
                        if (correctOptions.length > 0) {
                            // If there's only one correct option, save it as a string
                            // If there are multiple correct options, save them as a JSON string array
                            if (correctOptions.length === 1) {
                                questionData.correct_answer = correctOptions[0].option_text;
                            } else {
                                questionData.correct_answer = JSON.stringify(correctOptions.map(opt => opt.option_text));
                            }
                        }
                    } else if (questionType === 'true_false') {
                        // Handle true/false questions
                        const correctOption = $(this).find('input[name="correct_option"]:checked').val();
                        questionData.correct_answer = correctOption || 'false';

                        // Add true/false options
                        questionData.options.push({
                            option_text: 'True',
                            is_correct: correctOption === 'true'
                        });
                        questionData.options.push({
                            option_text: 'False',
                            is_correct: correctOption === 'false'
                        });
                    } else if (questionType === 'short_answer') {
                        // Handle short answer questions
                        questionData.correct_answer = $(this).find('.correct-answer').val() || '';
                    } else if (questionType === 'essay') {
                        // Handle essay questions
                        questionData.min_word_count = $(this).find('.min-word-count').val() || 0;
                        questionData.max_word_count = $(this).find('.max-word-count').val() || 0;
                    }

                    // Add essay ID for all question types
                    const essayId = $(this).find('.question-essay-id').val() || $(this).find('.linked-essay').val() || null;
                    if (essayId) {
                        questionData.essay_id = essayId;
                    }

                    // Add question to section data
                    sectionData.questions.push(questionData);
                });

                // Add section to the data
                jsonData.sections.push(sectionData);
            });
        }

        // Use AJAX to submit the form to the current server
        $.ajax({
            url: '/tests/save-draft',
            type: 'POST',
            data: JSON.stringify(jsonData),
            contentType: 'application/json',
            success: function(response) {
                // Re-enable the save draft button
                $("#saveDraftBtn").prop('disabled', false);

                if (response.success) {
                    // If we got an exam_id back and don't have one yet, store it
                    const examId = response.exam_id;
                    console.log('Response received with exam_id:', examId);

                    if (examId) {
                        if (!$("#exam_id").length) {
                            $("#testForm").append(`<input type="hidden" id="exam_id" name="exam_id" value="${examId}">`);
                            console.log('Created new hidden field for exam_id:', examId);
                        } else {
                            $("#exam_id").val(examId);
                            console.log('Updated existing exam_id field with:', examId);
                        }
                    }

                    // Show success message with test ID
                    const testIdMessage = examId ? ` (ID: ${examId})` : '';
                    showToast(`Draft saved successfully${testIdMessage}`, "success");
                } else {
                    showToast(response.message || "Error saving draft", "error");
                }
            },
            error: function(xhr, status, error) {
                // Re-enable the save draft button
                $("#saveDraftBtn").prop('disabled', false);

                // Show error message
                let errorMessage = "Error saving draft. Please try again.";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        const errorObj = JSON.parse(xhr.responseText);
                        errorMessage = errorObj.message || errorObj.error || errorMessage;
                    } catch (e) {
                        errorMessage = xhr.responseText;
                    }
                } else if (error) {
                    errorMessage = error;
                }

                showToast(errorMessage, "error");

                console.error('Error saving draft:', {
                    status: status,
                    error: error,
                    response: xhr.responseText || 'No response text',
                    url: '/tests/save-draft'
                });
            }
        });

        return false;
    }

    /**
     * Import questions from file
     */
    function importQuestions() {
        // Check if a section is active
        if (!activeSection) {
            showToast("Please create or select a section first", "error");
            return;
        }

        // Create form data with the file
        const formData = new FormData();
        formData.append('importFile', $('#importQuestionsFile')[0].files[0]);

        showToast("Importing questions...", "info");

        // AJAX post to import
        $.ajax({
            url: '/admin/tests/import-questions',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Add imported questions to active section
                    for (const question of response.questions) {
                        addImportedQuestion(activeSection, question);
                    }

                    showToast(`Successfully imported ${response.questions.length} questions`, "success");

                    // Reset file input
                    $('#importQuestionsFile').val('');
                    $("#selectedFileName").addClass('hidden');
                    $("#importQuestionsBtn").prop('disabled', true);
                } else {
                    showToast(response.message || "Error importing questions", "error");
                }
            },
            error: function() {
                showToast("Error importing questions", "error");
            }
        });
    }

    /**
     * Add an imported question to a section
     */
    function addImportedQuestion(sectionId, questionData) {
        const sectionContent = $(`.section-content[data-section="${sectionId}"]`);
        const questionsContainer = sectionContent.find('.questions-container');

        // Hide empty section message
        sectionContent.find('.empty-section-message').addClass('hidden');

        // Get current question count for this section
        const currentQuestionCount = questionsContainer.find('.question-container').length + 1;

        // Create new question from template
        const questionElement = $($("#questionTemplate").html());
        questionElement.find('.question-number').text(currentQuestionCount);

        // Fill in question data
        questionElement.find('.question-text').val(questionData.text);
        questionElement.find('.question-marks').val(questionData.marks || 1);
        questionElement.find('.negative-marks').val(questionData.negative_marks || 0);

        if (questionData.solution) {
            questionElement.find('.question-solution').val(questionData.solution);
        }

        // Add to DOM
        questionsContainer.append(questionElement);

        // Initialize question events
        initializeQuestionEvents(questionElement);

        // Set question type
        questionElement.find('.question-type').val(questionData.type);
        setQuestionType(questionElement, questionData.type);

        // Set question options based on type
        if (questionData.type === 'mcq' && questionData.options && questionData.options.length > 0) {
            // Clear default options
            const optionsGrid = questionElement.find('.options-grid').empty();

            // Add options from data
            questionData.options.forEach((option, index) => {
                addMCQOption(optionsGrid);
                const optionElement = optionsGrid.find('.option-wrapper').last();
                optionElement.find('.option-text').val(option);

                // Check if this is a correct option
                if (questionData.correct_answers && questionData.correct_answers.includes(index)) {
                    optionElement.find('.correct-option').prop('checked', true).trigger('change');
                }
            });
        } else if (questionData.type === 'true_false' && questionData.correct_answer) {
            const correctValue = String(questionData.correct_answer).toLowerCase() === 'true' ? 'true' : 'false';
            questionElement.find(`input[name="correct_option"][value="${correctValue}"]`).prop('checked', true);
        } else if (questionData.type === 'short_answer' && questionData.correct_answer) {
            questionElement.find('.correct-answer').val(questionData.correct_answer);
        } else if (questionData.type === 'essay') {
            if (questionData.min_word_count) {
                questionElement.find('.min-word-count').val(questionData.min_word_count);
            }
            if (questionData.max_word_count) {
                questionElement.find('.max-word-count').val(questionData.max_word_count);
            }
            if (questionData.essay_id) {
                // If there's an essay ID, fetch essays and select the right one
                const linkedEssaySelect = questionElement.find('.linked-essay');
                fetchEssays(linkedEssaySelect);

                // Set a timeout to select the essay after fetching
                setTimeout(() => {
                    linkedEssaySelect.val(questionData.essay_id);
                }, 1000);
            }
        }

        // Update question statistics
        updateQuestionStats(sectionId);
    }

    /**
     * Fetch essays from the server for linking to essay questions
     * @param {jQuery} selectElement - The select element to populate with essays
     * @param {string} [selectedEssayId] - Optional essay ID to select after loading
     */
    function fetchEssays(selectElement, selectedEssayId) {
        // Show loading state
        const originalHtml = selectElement.html();
        selectElement.html('<option>Loading essays...</option>');
        selectElement.prop('disabled', true);

        // Fetch essays from the server
        $.ajax({
            url: '/admin/essays/api/list',
            type: 'GET',
            success: function(response) {
                // Clear the select element
                selectElement.empty();

                // Add the default option
                selectElement.append('<option value="">Select an essay to link (optional)</option>');

                // Add essays to the select element
                if (response.success && response.essays && response.essays.length > 0) {
                    response.essays.forEach(function(essay) {
                        selectElement.append(`<option value="${essay.essay_id}">${essay.title}</option>`);
                    });
                    selectElement.prop('disabled', false);

                    // Set the selected essay if provided
                    if (selectedEssayId) {
                        selectElement.val(selectedEssayId);

                        // Also update the hidden field
                        const questionContainer = selectElement.closest('.question-container');
                        if (questionContainer.length) {
                            questionContainer.find('.question-essay-id').val(selectedEssayId);
                        }
                    }
                } else {
                    selectElement.append('<option value="" disabled>No essays available</option>');
                }

                // Show success message
                showToast("Essays loaded successfully", "success");
            },
            error: function() {
                // Restore original content on error
                selectElement.html(originalHtml);
                selectElement.prop('disabled', false);

                // Show error message
                showToast("Failed to load essays. Please try again.", "error");
            }
        });
    }

    /**
     * Submit the test form
     */
    function submitForm(e) {
        e.preventDefault();

        if (!validateCurrentStep()) {
            showValidationToast();
            return false;
        }

        // If we're on the publish step, handle it differently
        if (currentStep === 5) {
            publishTest();
            return false;
        }

        showToast("Creating test...", "info");

        // Add a hidden field to indicate the action
        if (!$("#action_type").length) {
            $("#testForm").append('<input type="hidden" id="action_type" name="action_type" value="create">');
        } else {
            $("#action_type").val("create");
        }

        // Make sure we're not in draft mode
        if ($("#is_draft").length) {
            $("#is_draft").val("0");
        }

        // Submit the form directly to the current URL
        document.getElementById('testForm').submit();

        return false;
    }

    /**
     * Generate PDF for the test
     */
    function generatePDF() {
        // Get the exam ID
        const examId = $("#exam_id").val();
        if (!examId) {
            showToast("Please save the test as a draft first before generating a PDF", "error");
            return;
        }

        // Show loading indicator
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
                <h2 class="text-xl font-bold mb-2">Generating PDF</h2>
                <p class="text-gray-700">This may take a few moments...</p>
            </div>
        `;
        document.body.appendChild(loadingOverlay);

        // Call the API to generate the PDF
        fetch(`/tests/generate-pdf/${examId}`)
            .then(response => response.json())
            .then(data => {
                // Remove loading indicator
                document.body.removeChild(loadingOverlay);

                if (data.success && data.pdfUrl) {
                    // Open the PDF in a new tab
                    window.open(data.pdfUrl, '_blank');
                    showToast("PDF generated successfully", "success");
                } else {
                    showToast(data.message || "Error generating PDF", "error");
                }
            })
            .catch(error => {
                // Remove loading indicator
                if (document.getElementById('loading-overlay')) {
                    document.body.removeChild(loadingOverlay);
                }

                console.error('Error generating PDF:', error);
                showToast("Error generating PDF. Please try again.", "error");
            });
    }

    /**
     * Publish the test via AJAX
     */
    function publishTest() {
        // Disable the submit button to prevent multiple submissions
        $("#submitBtn").prop('disabled', true).html('<span>Publishing...</span><svg class="animate-spin ml-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>');

        // Get the exam ID
        const examId = $("#exam_id").val();
        if (!examId) {
            showToast("Error: No exam ID found. Please save as draft first.", "error");
            $("#submitBtn").prop('disabled', false).html('<span>Publish Test</span><svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>');
            return;
        }

        // Get publish options
        const publishNow = $("#publishNow").is(':checked');
        const publishDate = $("#publish_date").val();
        const requirePasscode = $("#requirePasscode").is(':checked');
        const testPasscode = $("#test_passcode").val();
        const limitAttempts = $("#limitAttempts").is(':checked');
        const maxAttempts = $("#max_attempts").val();

        // Validate
        if (!publishNow && !publishDate) {
            showToast("Please select a publish date", "error");
            $("#publish_date").addClass('field-error').focus();
            $("#submitBtn").prop('disabled', false).html('<span>Publish Test</span><svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>');
            return;
        }

        if (requirePasscode && !testPasscode) {
            showToast("Please enter a passcode", "error");
            $("#test_passcode").addClass('field-error').focus();
            $("#submitBtn").prop('disabled', false).html('<span>Publish Test</span><svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>');
            return;
        }

        // Prepare data
        const publishData = {
            publish_option: publishNow ? 'now' : 'later',
            publish_date: publishDate,
            require_passcode: requirePasscode,
            test_passcode: testPasscode,
            limit_attempts: limitAttempts,
            max_attempts: maxAttempts
        };

        // Format datetime for display
        let displayDateTime;
        if (publishNow) {
            const now = new Date();
            displayDateTime = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            const scheduledDate = new Date(publishDate);
            displayDateTime = scheduledDate.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Send AJAX request
        $.ajax({
            url: `/tests/admin/${examId}/publish`,
            type: 'POST',
            data: JSON.stringify(publishData),
            contentType: 'application/json',
            success: function(response) {
                // Re-enable the button
                $("#submitBtn").prop('disabled', false).html('<span>Publish Test</span><svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>');

                if (response.success) {
                    if (publishNow) {
                        showToast(`Test published successfully at ${displayDateTime}`, "success");
                    } else {
                        showToast(`Test scheduled for publication on ${displayDateTime}`, "success");
                    }
                } else {
                    showToast(response.message || "Error publishing test", "error");
                }
            },
            error: function(error) {
                // Re-enable the button
                $("#submitBtn").prop('disabled', false).html('<span>Publish Test</span><svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>');

                console.error('Error publishing test:', error);
                showToast("Error publishing test. Please try again.", "error");
            }
        });
    }
});