/**
 * Consolidated Application JavaScript
 * 
 * This file combines common functionality from multiple smaller scripts
 * to reduce HTTP requests and improve performance.
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize all components
  initToastNotifications();
  initSessionTimeout();
  initConfirmationDialogs();
  initFormValidation();
  initLanguageSwitcher();
  
  // Log page view for analytics
  logPageView();
});

/**
 * Toast Notifications System
 */
function initToastNotifications() {
  // Check if Toastify is available
  if (typeof Toastify !== 'function') {
    console.warn('Toastify library not loaded');
    return;
  }
  
  // Create a global showToast function
  window.showToast = function(type, title, message) {
    let bgColor = '#4a5568'; // Default gray
    
    // Set color based on type
    if (type === 'success') bgColor = '#10B981';
    if (type === 'error') bgColor = '#EF4444';
    if (type === 'warning') bgColor = '#F59E0B';
    if (type === 'info') bgColor = '#3B82F6';
    
    // Show the toast
    Toastify({
      text: message,
      duration: 5000,
      close: true,
      gravity: "top",
      position: "right",
      backgroundColor: bgColor,
      stopOnFocus: true
    }).showToast();
  };
}

/**
 * Session Timeout Management
 */
function initSessionTimeout() {
  // Elements
  const modal = document.getElementById('session-timeout-modal');
  const countdown = document.getElementById('timeout-countdown');
  const logoutBtn = document.getElementById('logout-btn');
  const continueBtn = document.getElementById('continue-btn');
  
  // If elements don't exist, return
  if (!modal || !countdown || !logoutBtn || !continueBtn) {
    return;
  }
  
  // Variables
  let timeoutWarning = 14 * 60 * 1000; // 14 minutes
  let timeoutNow = 15 * 60 * 1000; // 15 minutes
  let logoutUrl = '/logout';
  let warningTimer;
  let timeoutTimer;
  let countdownTimer;
  let countdownSeconds = 15;
  
  // Start timers
  resetTimers();
  
  // Event listeners
  document.addEventListener('mousemove', resetTimers);
  document.addEventListener('keypress', resetTimers);
  document.addEventListener('click', resetTimers);
  document.addEventListener('scroll', resetTimers);
  
  // Continue session button
  continueBtn.addEventListener('click', function() {
    resetTimers();
    modal.classList.add('hidden');
    clearInterval(countdownTimer);
  });
  
  // Logout button
  logoutBtn.addEventListener('click', function() {
    window.location.href = logoutUrl;
  });
  
  // Reset timers function
  function resetTimers() {
    clearTimeout(warningTimer);
    clearTimeout(timeoutTimer);
    
    // Set warning timer
    warningTimer = setTimeout(function() {
      // Show warning modal
      modal.classList.remove('hidden');
      
      // Start countdown
      countdownSeconds = 15;
      countdown.textContent = countdownSeconds;
      
      countdownTimer = setInterval(function() {
        countdownSeconds--;
        countdown.textContent = countdownSeconds;
        
        if (countdownSeconds <= 0) {
          clearInterval(countdownTimer);
          window.location.href = logoutUrl;
        }
      }, 1000);
    }, timeoutWarning);
    
    // Set logout timer
    timeoutTimer = setTimeout(function() {
      window.location.href = logoutUrl;
    }, timeoutNow);
  }
}

/**
 * Confirmation Dialog System
 */
function initConfirmationDialogs() {
  // Get the confirmation dialog elements
  const dialog = document.getElementById('confirmation-dialog');
  const title = document.getElementById('confirmation-title');
  const message = document.getElementById('confirmation-message');
  const confirmBtn = document.getElementById('confirm-action');
  const cancelBtn = document.getElementById('cancel-action');
  
  if (!dialog || !title || !message || !confirmBtn || !cancelBtn) {
    return;
  }
  
  // Close dialog function
  const closeDialog = () => {
    dialog.classList.add('hidden');
  };
  
  // Add event listeners to close buttons
  cancelBtn.addEventListener('click', closeDialog);
  
  // Create global confirm function
  window.showConfirmDialog = function(titleText, messageText, callback) {
    title.textContent = titleText || 'Confirm Action';
    message.textContent = messageText || 'Are you sure you want to proceed?';
    
    // Show dialog
    dialog.classList.remove('hidden');
    
    // Remove previous event listener
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    
    // Get the new button
    const newConfirmBtn = document.getElementById('confirm-action');
    
    // Add new event listener
    newConfirmBtn.addEventListener('click', function() {
      closeDialog();
      if (typeof callback === 'function') {
        callback();
      }
    });
  };
}

/**
 * Form Validation
 */
function initFormValidation() {
  // Get all forms with validation
  const forms = document.querySelectorAll('form[data-validate="true"]');
  
  forms.forEach(form => {
    form.addEventListener('submit', function(event) {
      // Check if form is valid
      if (!validateForm(form)) {
        event.preventDefault();
      }
    });
  });
  
  // Validate form function
  function validateForm(form) {
    let isValid = true;
    
    // Get all required inputs
    const requiredInputs = form.querySelectorAll('[required]');
    
    requiredInputs.forEach(input => {
      if (!input.value.trim()) {
        isValid = false;
        showInputError(input, 'This field is required');
      } else {
        clearInputError(input);
      }
    });
    
    return isValid;
  }
  
  // Show input error
  function showInputError(input, message) {
    // Add error class
    input.classList.add('border-red-500');
    
    // Create error message
    const errorElement = document.createElement('p');
    errorElement.className = 'text-red-500 text-xs mt-1';
    errorElement.textContent = message;
    
    // Add error message after input
    const parent = input.parentElement;
    const existingError = parent.querySelector('.text-red-500');
    
    if (!existingError) {
      parent.appendChild(errorElement);
    }
  }
  
  // Clear input error
  function clearInputError(input) {
    // Remove error class
    input.classList.remove('border-red-500');
    
    // Remove error message
    const parent = input.parentElement;
    const existingError = parent.querySelector('.text-red-500');
    
    if (existingError) {
      parent.removeChild(existingError);
    }
  }
}

/**
 * Language Switcher
 */
function initLanguageSwitcher() {
  // Get language switcher elements
  const languageMenuButton = document.getElementById('language-menu-button') || 
                             document.getElementById('language-menu-button-auth');
  const languageDropdown = document.querySelector('.language-dropdown') || 
                           document.querySelector('.language-dropdown-auth');
  
  if (!languageMenuButton || !languageDropdown) {
    return;
  }
  
  // Toggle dropdown
  languageMenuButton.addEventListener('click', function(e) {
    e.preventDefault();
    languageDropdown.classList.toggle('hidden');
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function(event) {
    if (!languageMenuButton.contains(event.target) && !languageDropdown.contains(event.target)) {
      languageDropdown.classList.add('hidden');
    }
  });
}

/**
 * Page View Logging
 */
function logPageView() {
  // Only log if audit logger is available
  if (typeof logAuditEvent !== 'function') {
    return;
  }
  
  // Log page view
  logAuditEvent('navigation', 'Page View', {
    url: window.location.pathname,
    referrer: document.referrer
  });
}
