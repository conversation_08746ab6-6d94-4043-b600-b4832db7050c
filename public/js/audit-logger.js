/**
 * Client-side <PERSON>t Logger
 *
 * This script logs client-side JavaScript variables and actions for audit purposes.
 */

// Create the audit logger namespace
const AuditLogger = {
    // Log a variable
    logVariable: function(variableName, variableValue, context = 'client-side') {
        try {
            // Don't log sensitive data
            if (
                variableName.toLowerCase().includes('password') ||
                variableName.toLowerCase().includes('token') ||
                variableName.toLowerCase().includes('secret')
            ) {
                variableValue = '********';
            }

            // Prepare the log data
            const logData = {
                timestamp: new Date().toISOString(),
                type: 'variable',
                name: variableName,
                value: typeof variableValue === 'object' ? JSON.stringify(variableValue) : String(variableValue),
                valueType: typeof variableValue,
                context: context,
                url: window.location.href,
                userAgent: navigator.userAgent
            };

            // Send the log data to the server
            this._sendLogToServer(logData);

            // Also log to console in development
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('[AUDIT]', logData);
            }
        } catch (error) {
            console.error('Failed to log variable:', error);
        }
    },

    // Log a user action
    logAction: function(action, details = {}, context = 'client-side') {
        try {
            // Prepare the log data
            const logData = {
                timestamp: new Date().toISOString(),
                type: 'action',
                action: action,
                details: details,
                context: context,
                url: window.location.href,
                userAgent: navigator.userAgent
            };

            // Send the log data to the server
            this._sendLogToServer(logData);

            // Also log to console in development
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('[AUDIT]', logData);
            }
        } catch (error) {
            console.error('Failed to log action:', error);
        }
    },

    // Send the log data to the server
    _sendLogToServer: function(logData) {
        try {
            console.log('AuditLogger: Sending log data to server:', logData);

            // Add a timestamp to ensure the log is unique
            logData.clientTimestamp = new Date().toISOString();

            // Use fetch API for better debugging
            fetch('/api/audit-log', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('AuditLogger: Server response:', data);
            })
            .catch(error => {
                console.error('AuditLogger: Failed to send log to server:', error);

                // Fall back to Beacon API if fetch fails
                if (navigator.sendBeacon) {
                    console.log('AuditLogger: Falling back to Beacon API');
                    navigator.sendBeacon('/api/audit-log', JSON.stringify(logData));
                }
            });
        } catch (error) {
            console.error('AuditLogger: Failed to send log to server:', error);
        }
    }
};

// Override console.log to capture logs
const originalConsoleLog = console.log;
console.log = function() {
    // Call the original console.log
    originalConsoleLog.apply(console, arguments);

    // Log the arguments if they look like variables
    if (arguments.length === 2 && typeof arguments[0] === 'string' && arguments[0].includes('=')) {
        const variableName = arguments[0].split('=')[0].trim();
        AuditLogger.logVariable(variableName, arguments[1], 'console.log');
    }
};

// Add event listeners to log form submissions
document.addEventListener('DOMContentLoaded', function() {
    // Log form submissions
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(event) {
            // Get form data (excluding sensitive fields)
            const formData = {};
            const formElements = Array.from(form.elements);

            formElements.forEach(element => {
                if (element.name && element.value && !element.name.toLowerCase().includes('password')) {
                    formData[element.name] = element.value;
                }
            });

            // Log the form submission
            AuditLogger.logAction('Form Submission', {
                formId: form.id || 'unknown',
                formAction: form.action,
                formMethod: form.method,
                formData: formData
            });
        });
    });

    // Log button clicks
    document.querySelectorAll('button, a.btn, input[type="button"], input[type="submit"]').forEach(button => {
        button.addEventListener('click', function(event) {
            // Log the button click
            AuditLogger.logAction('Button Click', {
                buttonId: button.id || 'unknown',
                buttonText: button.innerText || button.value || 'unknown',
                buttonType: button.type || 'unknown'
            });
        });
    });
});
