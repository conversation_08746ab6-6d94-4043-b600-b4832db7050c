/**
 * Broadcast Notifications
 * Handles displaying broadcast notifications as overlays
 */
document.addEventListener('DOMContentLoaded', function() {
    // Create notification container if it doesn't exist
    let broadcastContainer = document.getElementById('broadcastNotificationContainer');
    
    if (!broadcastContainer) {
        broadcastContainer = document.createElement('div');
        broadcastContainer.id = 'broadcastNotificationContainer';
        broadcastContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
        document.body.appendChild(broadcastContainer);
    }
    
    // Function to fetch and display broadcast notifications
    async function fetchBroadcastNotifications() {
        try {
            const response = await fetch('/api/notifications/broadcast');
            const data = await response.json();
            
            if (data.success && data.notifications.length > 0) {
                // Create notification content
                let notificationContent = `
                    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
                        <button id="closeBroadcastNotification" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">${data.notifications[0].title}</h3>
                        <div class="prose mb-4">
                            ${data.notifications[0].message}
                        </div>
                        <div class="text-right">
                            <button id="dismissBroadcastNotification" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                                Dismiss
                            </button>
                        </div>
                    </div>
                `;
                
                // Set content and show notification
                broadcastContainer.innerHTML = notificationContent;
                broadcastContainer.classList.remove('hidden');
                
                // Add event listeners
                document.getElementById('closeBroadcastNotification').addEventListener('click', function() {
                    broadcastContainer.classList.add('hidden');
                    markNotificationAsRead(data.notifications[0].id);
                });
                
                document.getElementById('dismissBroadcastNotification').addEventListener('click', function() {
                    broadcastContainer.classList.add('hidden');
                    markNotificationAsRead(data.notifications[0].id);
                });
            }
        } catch (error) {
            console.error('Error fetching broadcast notifications:', error);
        }
    }
    
    // Function to mark notification as read
    async function markNotificationAsRead(notificationId) {
        try {
            await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    // Fetch notifications after a short delay
    setTimeout(fetchBroadcastNotifications, 2000);
    
    // Check for new broadcast notifications every minute
    setInterval(fetchBroadcastNotifications, 60000);
});
