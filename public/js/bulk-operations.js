/**
 * Bulk Operations for Test Management
 * Handles selection, bulk archive, and bulk delete functionality
 */

// Track selected items
let selectedItems = [];

// Initialize bulk operations
function initBulkOperations() {
    // Add event listener to the "Select All" checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            const checkboxes = document.querySelectorAll('.item-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                updateSelectedItem(checkbox.value, isChecked);
            });

            updateBulkActionButtons();
        });
    }

    // Add event listeners to individual checkboxes
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedItem(this.value, this.checked);
            updateBulkActionButtons();

            // Update "Select All" checkbox state
            const selectAllCheckbox = document.getElementById('select-all');
            const allCheckboxes = document.querySelectorAll('.item-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && selectedItems.length > 0;
            }
        });
    });

    // Initialize bulk action buttons
    updateBulkActionButtons();
}

// Update the selectedItems array when a checkbox is checked/unchecked
function updateSelectedItem(itemId, isSelected) {
    if (isSelected) {
        if (!selectedItems.includes(itemId)) {
            selectedItems.push(itemId);
        }
    } else {
        selectedItems = selectedItems.filter(id => id !== itemId);
    }
}

// Update the state of bulk action buttons based on selection
function updateBulkActionButtons() {
    const bulkArchiveBtn = document.getElementById('bulk-archive-btn');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');

    if (bulkArchiveBtn) {
        bulkArchiveBtn.disabled = selectedItems.length === 0;
        bulkArchiveBtn.classList.toggle('opacity-50', selectedItems.length === 0);
        bulkArchiveBtn.classList.toggle('cursor-not-allowed', selectedItems.length === 0);
    }

    if (bulkDeleteBtn) {
        bulkDeleteBtn.disabled = selectedItems.length === 0;
        bulkDeleteBtn.classList.toggle('opacity-50', selectedItems.length === 0);
        bulkDeleteBtn.classList.toggle('cursor-not-allowed', selectedItems.length === 0);
    }

    // Update the count of selected items
    const selectedCount = document.getElementById('selected-count');
    if (selectedCount) {
        selectedCount.textContent = selectedItems.length;
    }
}

// Open the bulk archive confirmation dialog
function confirmBulkArchive() {
    if (selectedItems.length === 0) return;

    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
        title: 'Archive Tests',
        message: `Are you sure you want to archive ${selectedItems.length} selected tests?`,
        confirmText: 'Archive',
        cancelText: 'Cancel',
        type: 'warning',
        onConfirm: function() {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/tests/bulk-archive';

            // Add hidden input for selected items
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'examIds';
            input.value = JSON.stringify(selectedItems);
            form.appendChild(input);

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    });
    } else {
        // Fallback to standard confirm dialog
        if (confirm(`Are you sure you want to archive ${selectedItems.length} selected tests?`)) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/tests/bulk-archive';

            // Add hidden input for selected items
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'examIds';
            input.value = JSON.stringify(selectedItems);
            form.appendChild(input);

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
}

// Close the bulk archive confirmation modal
function closeBulkArchiveModal() {
    const modal = document.getElementById('bulkArchiveModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Open the bulk delete confirmation dialog
function confirmBulkDelete() {
    if (selectedItems.length === 0) return;

    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
        title: 'Delete Tests',
        message: `Are you sure you want to delete ${selectedItems.length} selected tests? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        onConfirm: function() {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/tests/bulk-delete';

            // Add hidden input for selected items
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'examIds';
            input.value = JSON.stringify(selectedItems);
            form.appendChild(input);

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    });
    } else {
        // Fallback to standard confirm dialog
        if (confirm(`Are you sure you want to delete ${selectedItems.length} selected tests? This action cannot be undone.`)) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/tests/bulk-delete';

            // Add hidden input for selected items
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'examIds';
            input.value = JSON.stringify(selectedItems);
            form.appendChild(input);

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
}

// Close the bulk delete confirmation modal
function closeBulkDeleteModal() {
    const modal = document.getElementById('bulkDeleteModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initBulkOperations);
