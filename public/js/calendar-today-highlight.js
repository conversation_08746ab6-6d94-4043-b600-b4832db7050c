/**
 * Enhanced calendar today highlighting
 * This script provides a consistent way to highlight today's date in all calendar views
 */

/**
 * Creates a day element with enhanced highlighting for today's date
 * @param {number} day - The day number
 * @param {string} extraClasses - Additional CSS classes to apply
 * @param {number} month - The month (0-11)
 * @param {number} year - The year
 * @param {Function} createEventsContainer - Function to create events for this day
 * @returns {HTMLElement} The day element
 */
function createEnhancedDayElement(day, extraClasses, month, year, createEventsContainer) {
  const today = new Date();
  const isToday = day === today.getDate() && month === today.getMonth() && year === today.getFullYear();

  const dayEl = document.createElement('div');

  // Add special styling for today's date and make it relative for badge positioning
  if (isToday) {
    dayEl.className = `min-h-[100px] border-2 border-blue-500 p-1 bg-blue-50 relative ${extraClasses}`;
  } else {
    dayEl.className = `min-h-[100px] border p-1 relative ${extraClasses}`;
  }

  // Apply special styling for today's date
  if (isToday) {
    // Create a container for the today indicator
    const todayContainer = document.createElement('div');
    todayContainer.className = 'flex justify-between items-center mb-1';

    // Add "TODAY" label
    const todayLabel = document.createElement('span');
    todayLabel.className = 'text-xs font-semibold text-blue-700 bg-blue-100 px-2 py-0.5 rounded-sm';
    todayLabel.textContent = 'TODAY';
    todayContainer.appendChild(todayLabel);

    // Style the day number
    const dayNumber = document.createElement('div');
    dayNumber.className = 'font-bold text-xl text-blue-700 rounded-full bg-white w-8 h-8 flex items-center justify-center shadow-sm';
    dayNumber.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)';
    dayNumber.textContent = day;

    todayContainer.appendChild(dayNumber);
    dayEl.appendChild(todayContainer);

    // Create events container if provided
    if (typeof createEventsContainer === 'function') {
      const eventsContainer = createEventsContainer(day, month, year);
      if (eventsContainer) {
        dayEl.appendChild(eventsContainer);
      }
    }

    return dayEl;
  } else {
    // Regular day styling
    const dayNumber = document.createElement('div');
    dayNumber.className = 'text-right text-sm';
    dayNumber.textContent = day;
    dayEl.appendChild(dayNumber);

    // Create events container if provided
    if (typeof createEventsContainer === 'function') {
      const eventsContainer = createEventsContainer(day, month, year);
      if (eventsContainer) {
        dayEl.appendChild(eventsContainer);
      }
    }

    return dayEl;
  }
}
