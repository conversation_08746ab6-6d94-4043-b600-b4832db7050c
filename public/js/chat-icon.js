/**
 * Chat Icon and WhatsApp-like Chat Window
 * This script creates a chat icon in the bottom right corner that opens a WhatsApp-like chat window
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (!document.body.classList.contains('logged-in')) return;

    // Create the chat icon
    const chatIcon = document.createElement('div');
    chatIcon.id = 'chatIcon';
    chatIcon.className = 'fixed bottom-20 right-6 bg-purple-600 text-white rounded-full w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center shadow-lg cursor-pointer z-[99999] hover:bg-purple-700 transition-all';
    chatIcon.style.cssText = 'position: fixed !important; bottom: 80px !important; right: 20px !important; z-index: 99999 !important;';

    // Use a simple chat icon with inline SVG for maximum compatibility
    chatIcon.innerHTML = `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 12C21 16.418 16.97 20 12 20C10.5 20 9.062 19.684 7.745 19.1L3 20L4.395 16.28C3.512 15.042 3 13.574 3 12C3 7.582 7.03 4 12 4C16.97 4 21 7.582 21 12Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 12H8.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 12H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M16 12H16.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `;
    document.body.appendChild(chatIcon);

    // Create the chat window
    const chatWindow = document.createElement('div');
    chatWindow.id = 'chatWindow';
    chatWindow.className = 'fixed bottom-40 right-6 bg-white rounded-lg shadow-xl z-[99998] hidden transition-all transform scale-95 opacity-0 flex flex-col';
    chatWindow.style.cssText = 'position: fixed !important; bottom: 150px !important; right: 20px !important; z-index: 99998 !important;';
    chatWindow.innerHTML = `
        <div class="flex h-full w-full">
            <!-- Left sidebar with contacts -->
            <div class="w-1/3 md:w-2/5 border-r border-gray-200 flex flex-col">
                <!-- Header with tabs -->
                <div class="bg-purple-600 text-white p-2 sm:p-3 flex items-center justify-between rounded-tl-lg">
                    <h3 class="font-semibold text-sm sm:text-base">Chats</h3>
                    <div class="flex space-x-1 sm:space-x-2">
                        <button id="peopleTabBtn" class="p-1 bg-purple-700 rounded" title="People">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </button>
                        <button id="groupsTabBtn" class="p-1 hover:bg-purple-700 rounded" title="Groups">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Search bar -->
                <div class="p-2 sm:p-3 border-b border-gray-200">
                    <div class="relative">
                        <input type="text" id="chatSearchInput" placeholder="Search..." class="w-full pl-8 sm:pl-10 pr-2 sm:pr-4 py-1 sm:py-2 text-sm sm:text-base rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <div class="absolute left-2 sm:left-3 top-1.5 sm:top-2.5 text-gray-400">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- People list -->
                <div id="peopleList" class="flex-1 overflow-y-auto">
                    <div class="p-4 text-center text-gray-500">Loading contacts...</div>
                </div>

                <!-- Groups list (hidden by default) -->
                <div id="groupsList" class="flex-1 overflow-y-auto hidden">
                    <div class="p-4 text-center text-gray-500">Loading groups...</div>
                </div>
            </div>

            <!-- Right side with messages -->
            <div class="w-2/3 md:w-3/5 flex flex-col">
                <!-- Chat header -->
                <div class="bg-purple-600 text-white p-2 sm:p-3 flex items-center justify-between rounded-tr-lg">
                    <div id="chatRecipientInfo" class="flex items-center hidden">
                        <div class="bg-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center mr-1 sm:mr-2">
                            <span id="chatRecipientInitial" class="text-purple-600 font-bold text-xs sm:text-base"></span>
                        </div>
                        <h3 id="chatRecipientName" class="font-semibold text-sm sm:text-base truncate max-w-[120px] sm:max-w-[200px]"></h3>
                    </div>
                    <div class="flex space-x-1 sm:space-x-2 ml-auto">
                        <button id="closeChatBtn" class="p-1 hover:bg-purple-700 rounded" title="Close Chat">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Messages container -->
                <div id="messagesContainer" class="flex-1 overflow-y-auto p-2 sm:p-4 bg-gray-100">
                    <div id="noChatSelected" class="h-full flex items-center justify-center">
                        <div class="text-center text-gray-500 px-2">
                            <svg class="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-2 sm:mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <p class="text-base sm:text-lg font-medium">Select a chat</p>
                            <p class="mt-1 sm:mt-2 text-sm sm:text-base">Choose a contact or group</p>
                        </div>
                    </div>
                </div>

                <!-- Message input -->
                <div id="messageInputContainer" class="p-2 sm:p-3 bg-white border-t border-gray-200 hidden">
                    <div class="flex items-center">
                        <input type="text" id="messageInput" placeholder="Message..." class="flex-1 border border-gray-300 rounded-full py-1 sm:py-2 px-3 sm:px-4 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <button id="sendMessageBtn" class="ml-1 sm:ml-2 bg-purple-600 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center hover:bg-purple-700 transition">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(chatWindow);

    // Variables to track current chat
    let currentChatId = null;
    let currentChatType = null; // 'user' or 'group'

    // Event listeners
    chatIcon.addEventListener('click', toggleChatWindow);
    document.getElementById('peopleTabBtn').addEventListener('click', showPeopleList);
    document.getElementById('groupsTabBtn').addEventListener('click', showGroupsList);
    document.getElementById('closeChatBtn').addEventListener('click', function() {
        toggleChatWindow(); // Close the chat window
        closeCurrentChat(); // Reset the chat state
    });
    document.getElementById('sendMessageBtn').addEventListener('click', sendMessage);
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') sendMessage();
    });
    document.getElementById('chatSearchInput').addEventListener('input', handleSearch);

    // Load initial data
    loadPeopleList();
    loadGroupsList();

    // Functions
    function toggleChatWindow() {
        const window = document.getElementById('chatWindow');
        if (window.classList.contains('hidden')) {
            // Show window
            window.classList.remove('hidden');
            setTimeout(() => {
                window.classList.remove('scale-95', 'opacity-0');
                window.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            // Hide window
            window.classList.remove('scale-100', 'opacity-100');
            window.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                window.classList.add('hidden');
            }, 300);
        }
    }

    function showPeopleList() {
        document.getElementById('peopleTabBtn').classList.add('bg-purple-700');
        document.getElementById('groupsTabBtn').classList.remove('bg-purple-700');
        document.getElementById('peopleList').classList.remove('hidden');
        document.getElementById('groupsList').classList.add('hidden');
    }

    function showGroupsList() {
        document.getElementById('peopleTabBtn').classList.remove('bg-purple-700');
        document.getElementById('groupsTabBtn').classList.add('bg-purple-700');
        document.getElementById('peopleList').classList.add('hidden');
        document.getElementById('groupsList').classList.remove('hidden');
    }

    function closeCurrentChat() {
        currentChatId = null;
        currentChatType = null;
        document.getElementById('noChatSelected').classList.remove('hidden');
        document.getElementById('chatRecipientInfo').classList.add('hidden');
        document.getElementById('messageInputContainer').classList.add('hidden');
        document.getElementById('messagesContainer').innerHTML = `
            <div id="noChatSelected" class="h-full flex items-center justify-center">
                <div class="text-center text-gray-500">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <p class="text-lg font-medium">Select a chat to start messaging</p>
                    <p class="mt-2">Choose a contact or group from the left sidebar</p>
                </div>
            </div>
        `;
    }

    async function loadPeopleList() {
        const peopleList = document.getElementById('peopleList');
        peopleList.innerHTML = '<div class="p-4 text-center text-gray-500">Loading contacts...</div>';

        try {
            const response = await fetch('/api/chat/users');

            // Handle unauthorized errors
            if (response.status === 401) {
                peopleList.innerHTML = '<div class="p-4 text-center text-gray-500">Please log in to view contacts</div>';
                return;
            }

            const data = await response.json();

            if (data.success && data.users && data.users.length > 0) {
                peopleList.innerHTML = '';

                data.users.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer flex items-center';
                    userItem.innerHTML = `
                        <div class="bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                            <span class="text-purple-600 font-bold">${user.username.charAt(0).toUpperCase()}</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${user.username}</h4>
                            <p class="text-sm text-gray-500">${user.role || 'User'}</p>
                        </div>
                    `;

                    userItem.addEventListener('click', () => loadUserChat(user.id, user.username));
                    peopleList.appendChild(userItem);
                });
            } else {
                peopleList.innerHTML = '<div class="p-4 text-center text-gray-500">No contacts found</div>';
            }
        } catch (error) {
            console.error('Error loading people list:', error);
            peopleList.innerHTML = '<div class="p-4 text-center text-gray-500">Error loading contacts</div>';
        }
    }

    async function loadGroupsList() {
        const groupsList = document.getElementById('groupsList');
        groupsList.innerHTML = '<div class="p-4 text-center text-gray-500">Loading groups...</div>';

        try {
            const response = await fetch('/api/chat/groups');

            // Handle unauthorized errors
            if (response.status === 401) {
                groupsList.innerHTML = '<div class="p-4 text-center text-gray-500">Please log in to view groups</div>';
                return;
            }

            const data = await response.json();

            if (data.success && data.groups && data.groups.length > 0) {
                groupsList.innerHTML = '';

                data.groups.forEach(group => {
                    const groupItem = document.createElement('div');
                    groupItem.className = 'p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer flex items-center';
                    groupItem.innerHTML = `
                        <div class="bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                            <span class="text-purple-600 font-bold">${group.name.charAt(0).toUpperCase()}</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${group.name}</h4>
                            <p class="text-sm text-gray-500">${group.member_count || 0} members</p>
                        </div>
                    `;

                    groupItem.addEventListener('click', () => loadGroupChat(group.group_id, group.name));
                    groupsList.appendChild(groupItem);
                });
            } else {
                groupsList.innerHTML = '<div class="p-4 text-center text-gray-500">No groups found</div>';
            }
        } catch (error) {
            console.error('Error loading groups list:', error);
            groupsList.innerHTML = '<div class="p-4 text-center text-gray-500">Error loading groups</div>';
        }
    }

    async function loadUserChat(userId, username) {
        currentChatId = userId;
        currentChatType = 'user';

        // Update UI - with null checks
        const noChatSelected = document.getElementById('noChatSelected');
        const chatRecipientInfo = document.getElementById('chatRecipientInfo');
        const messageInputContainer = document.getElementById('messageInputContainer');
        const chatRecipientName = document.getElementById('chatRecipientName');
        const chatRecipientInitial = document.getElementById('chatRecipientInitial');

        // Only proceed if all elements exist
        if (noChatSelected) noChatSelected.classList.add('hidden');
        if (chatRecipientInfo) chatRecipientInfo.classList.remove('hidden');
        if (messageInputContainer) messageInputContainer.classList.remove('hidden');

        // Set recipient info
        if (chatRecipientName) chatRecipientName.textContent = username;
        if (chatRecipientInitial) chatRecipientInitial.textContent = username.charAt(0).toUpperCase();

        // Clear messages container
        const messagesContainer = document.getElementById('messagesContainer');
        if (messagesContainer) {
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Loading messages...</div>';
        }

        try {
            const response = await fetch(`/api/chat/messages/user/${userId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view messages</div>';
                }
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/user/${userId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));
            } else {
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
                }
            }
        } catch (error) {
            console.error('Error loading user chat messages:', error);
            if (messagesContainer) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
            }
        }
    }

    async function loadGroupChat(groupId, groupName) {
        currentChatId = groupId;
        currentChatType = 'group';

        // Update UI - with null checks
        const noChatSelected = document.getElementById('noChatSelected');
        const chatRecipientInfo = document.getElementById('chatRecipientInfo');
        const messageInputContainer = document.getElementById('messageInputContainer');
        const chatRecipientName = document.getElementById('chatRecipientName');
        const chatRecipientInitial = document.getElementById('chatRecipientInitial');

        // Only proceed if all elements exist
        if (noChatSelected) noChatSelected.classList.add('hidden');
        if (chatRecipientInfo) chatRecipientInfo.classList.remove('hidden');
        if (messageInputContainer) messageInputContainer.classList.remove('hidden');

        // Set recipient info
        if (chatRecipientName) chatRecipientName.textContent = groupName;
        if (chatRecipientInitial) chatRecipientInitial.textContent = groupName.charAt(0).toUpperCase();

        // Clear messages container
        const messagesContainer = document.getElementById('messagesContainer');
        if (messagesContainer) {
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Loading messages...</div>';
        }

        try {
            const response = await fetch(`/api/chat/messages/group/${groupId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view messages</div>';
                }
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/group/${groupId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));
            } else {
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
                }
            }
        } catch (error) {
            console.error('Error loading group chat messages:', error);
            if (messagesContainer) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
            }
        }
    }

    function displayMessages(messages) {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) {
            console.error('Messages container not found');
            return;
        }

        messagesContainer.innerHTML = '';

        if (!messages || messages.length === 0) {
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">No messages yet</div>';
            return;
        }

        // Group messages by date
        const messagesByDate = {};
        messages.forEach(message => {
            const date = new Date(message.created_at).toLocaleDateString();
            if (!messagesByDate[date]) {
                messagesByDate[date] = [];
            }
            messagesByDate[date].push(message);
        });

        // Display messages grouped by date
        Object.keys(messagesByDate).forEach(date => {
            // Add date separator
            const dateDiv = document.createElement('div');
            dateDiv.className = 'text-center my-3';
            dateDiv.innerHTML = `<span class="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">${date}</span>`;
            messagesContainer.appendChild(dateDiv);

            // Add messages for this date
            messagesByDate[date].forEach(message => {
                const isCurrentUser = message.sender_id === parseInt(document.body.dataset.userId);
                const messageDiv = document.createElement('div');
                messageDiv.className = `flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`;

                messageDiv.innerHTML = `
                    <div class="${isCurrentUser ? 'bg-purple-600 text-white' : 'bg-white text-gray-800'} rounded-lg py-2 px-3 max-w-[70%] shadow-sm">
                        ${!isCurrentUser ? `<div class="text-xs text-gray-600 font-medium mb-1">${message.sender_name || 'User'}</div>` : ''}
                        <div>${message.message}</div>
                        <div class="text-xs ${isCurrentUser ? 'text-purple-200' : 'text-gray-500'} text-right mt-1">
                            ${new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                    </div>
                `;

                messagesContainer.appendChild(messageDiv);
            });
        });

        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message || !currentChatId || !currentChatType) return;

        messageInput.value = '';

        try {
            const endpoint = currentChatType === 'user'
                ? `/api/chat/messages/user/${currentChatId}/send`
                : `/api/chat/messages/group/${currentChatId}/send`;

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            // Handle unauthorized errors
            if (response.status === 401) {
                const messagesContainer = document.getElementById('messagesContainer');
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to send messages</div>';
                }
                return;
            }

            const data = await response.json();

            if (data.success) {
                // Reload messages
                const chatRecipientName = document.getElementById('chatRecipientName');
                const recipientName = chatRecipientName ? chatRecipientName.textContent : 'User';

                if (currentChatType === 'user') {
                    loadUserChat(currentChatId, recipientName);
                } else {
                    loadGroupChat(currentChatId, recipientName);
                }
            }
        } catch (error) {
            console.error('Error sending message:', error);
        }
    }

    function handleSearch() {
        const searchTerm = document.getElementById('chatSearchInput').value.toLowerCase();
        const peopleList = document.getElementById('peopleList');
        const groupsList = document.getElementById('groupsList');

        // Search in people list
        Array.from(peopleList.children).forEach(item => {
            if (item.textContent.toLowerCase().includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });

        // Search in groups list
        Array.from(groupsList.children).forEach(item => {
            if (item.textContent.toLowerCase().includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }
});
