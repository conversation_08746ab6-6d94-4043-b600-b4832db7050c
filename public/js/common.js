/**
 * Common JavaScript functions used across the application
 */

// Function to format date to YYYY-MM-DD
function formatDate(date) {
  const d = new Date(date);
  let month = '' + (d.getMonth() + 1);
  let day = '' + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = '0' + month;
  if (day.length < 2) day = '0' + day;

  return [year, month, day].join('-');
}

// Function to format date and time
function formatDateTime(date) {
  const d = new Date(date);
  return d.toLocaleString();
}

// Show toast notification
function showToast(message, type = 'info') {
  if (typeof Toastify === 'function') {
    Toastify({
      text: message,
      duration: 3000,
      close: true,
      gravity: "bottom",
      position: "right",
      backgroundColor: type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6',
      stopOnFocus: true
    }).showToast();
  } else {
    console.log(`Toast (${type}): ${message}`);
  }
}

// Initialize chosen select elements
function initChosen() {
  if (typeof jQuery !== 'undefined' && typeof jQuery.fn.chosen !== 'undefined') {
    jQuery(".chosen-select").chosen({
      width: "100%",
      no_results_text: "No results match",
      allow_single_deselect: true
    });
  }
}

// Function to make API calls
async function apiCall(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    console.log(`Making API call to ${endpoint} with method ${method}`, data ? { data } : '');
    const response = await fetch(endpoint, options);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API call failed with status ${response.status}:`, errorText);
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.message || `API call failed with status ${response.status}`);
      } catch (e) {
        throw new Error(`API call failed with status ${response.status}: ${errorText}`);
      }
    }

    const result = await response.json();
    console.log(`API call to ${endpoint} succeeded:`, result);
    return result;
  } catch (error) {
    console.error('API call error:', error);
    showToast(error.message || 'An error occurred while processing your request', 'error');
    throw error;
  }
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
  // Initialize chosen selects if available
  initChosen();

  // Add any other initialization code here
});

// Make functions available globally
window.showToast = showToast;
window.apiCall = apiCall;
