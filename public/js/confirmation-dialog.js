/**
 * Confirmation Dialog Component
 * Provides a reusable confirmation dialog that replaces JavaScript alerts
 */

class ConfirmationDialog {
    constructor() {
        this.createDialogElement();
        this.setupEventListeners();
    }

    createDialogElement() {
        // Create the dialog element if it doesn't exist
        if (document.getElementById('confirmation-dialog')) {
            return;
        }

        // Check if document.body exists
        if (!document.body) {
            console.warn('Document body not available yet, deferring dialog creation');
            return;
        }

        const dialog = document.createElement('div');
        dialog.id = 'confirmation-dialog';
        dialog.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[9999] hidden';
        dialog.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div id="confirmation-dialog-header" class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
                    <h3 id="confirmation-dialog-title" class="text-xl font-semibold">Confirm Action</h3>
                    <button id="confirmation-dialog-close" class="text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <p id="confirmation-dialog-message" class="mb-6">Are you sure you want to perform this action?</p>
                <div class="flex justify-end space-x-3">
                    <button id="confirmation-dialog-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button id="confirmation-dialog-confirm" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Confirm
                    </button>
                </div>
            </div>
        `;

        try {
            document.body.appendChild(dialog);
        } catch (error) {
            console.error('Error appending dialog to body:', error);
        }
    }

    setupEventListeners() {
        // Close dialog when clicking the close button
        document.getElementById('confirmation-dialog-close').addEventListener('click', () => {
            this.hide();
        });

        // Close dialog when clicking the cancel button
        document.getElementById('confirmation-dialog-cancel').addEventListener('click', () => {
            this.hide();
        });

        // Close dialog when clicking outside
        document.getElementById('confirmation-dialog').addEventListener('click', (e) => {
            if (e.target === document.getElementById('confirmation-dialog')) {
                this.hide();
            }
        });

        // Close dialog on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('confirmation-dialog').classList.contains('hidden')) {
                this.hide();
            }
        });
    }

    /**
     * Show the confirmation dialog
     * @param {Object} options - Configuration options
     * @param {string} options.title - Dialog title
     * @param {string} options.message - Dialog message
     * @param {string} options.confirmText - Text for confirm button
     * @param {string} options.cancelText - Text for cancel button
     * @param {string} options.type - Dialog type (info, warning, danger)
     * @param {Function} options.onConfirm - Callback function when confirmed
     * @param {Function} options.onCancel - Callback function when canceled
     */
    show(options = {}) {
        const dialog = document.getElementById('confirmation-dialog');
        const title = document.getElementById('confirmation-dialog-title');
        const message = document.getElementById('confirmation-dialog-message');
        const confirmBtn = document.getElementById('confirmation-dialog-confirm');
        const cancelBtn = document.getElementById('confirmation-dialog-cancel');
        const header = document.getElementById('confirmation-dialog-header');

        // Set dialog content
        title.textContent = options.title || 'Confirm Action';
        message.textContent = options.message || 'Are you sure you want to perform this action?';
        confirmBtn.textContent = options.confirmText || 'Confirm';
        cancelBtn.textContent = options.cancelText || 'Cancel';

        // Set dialog type (changes header color)
        header.className = 'text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4';

        // Add appropriate color based on type
        if (options.type === 'danger') {
            header.classList.add('bg-red-600');
            confirmBtn.className = 'px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500';
        } else if (options.type === 'warning') {
            header.classList.add('bg-yellow-600');
            confirmBtn.className = 'px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500';
        } else {
            header.classList.add('bg-blue-600');
            confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
        }

        // Set up confirm button action
        confirmBtn.onclick = () => {
            if (typeof options.onConfirm === 'function') {
                options.onConfirm();
            }
            this.hide();
        };

        // Set up cancel button action
        cancelBtn.onclick = () => {
            if (typeof options.onCancel === 'function') {
                options.onCancel();
            }
            this.hide();
        };

        // Show the dialog
        dialog.classList.remove('hidden');
    }

    hide() {
        document.getElementById('confirmation-dialog').classList.add('hidden');
    }
}

// Safe initialization function
function initializeConfirmationDialog() {
    if (!window.confirmationDialog && document.body) {
        try {
            window.confirmationDialog = new ConfirmationDialog();
            console.log('Confirmation dialog initialized');
        } catch (error) {
            console.error('Error initializing confirmation dialog:', error);
        }
    }
}

// Create a global instance after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeConfirmationDialog();
});

// Fallback initialization for pages that might load the script after DOMContentLoaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initializeConfirmationDialog();
}

// Delayed initialization as final fallback
setTimeout(function() {
    if (!window.confirmationDialog) {
        initializeConfirmationDialog();
    }
}, 100);
