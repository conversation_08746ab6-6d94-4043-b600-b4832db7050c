// Enhanced CV Generator with Animation
// Handles CV PDF generation with loading animations and progress feedback

class CVGenerator {
    constructor() {
        this.isGenerating = false;
        this.animationDuration = 2000; // 2 seconds for animation
        this.init();
    }

    init() {
        console.log('🔄 CV Generator initialized');
        this.attachEventListeners();
    }

    attachEventListeners() {
        // Attach event listeners to CV generation buttons
        $(document).on('click', '.generate-cv-btn', (e) => {
            e.preventDefault();
            const teacherId = $(e.currentTarget).data('teacher-id');
            console.log('🔄 CV generation requested for teacher:', teacherId);
            this.generateCV(teacherId);
        });
    }

    async generateCV(teacherId) {
        if (this.isGenerating) {
            console.log('⚠️ CV generation already in progress');
            return;
        }

        try {
            this.isGenerating = true;
            console.log('🔄 Starting CV generation for teacher:', teacherId);

            // Show loading animation
            this.showLoadingAnimation();

            // Fetch teacher data first
            const teacherData = await this.fetchTeacherData(teacherId);
            
            if (!teacherData) {
                throw new Error('Failed to fetch teacher data');
            }

            // Update loading message
            this.updateLoadingMessage('Generating PDF document...');

            // Generate PDF using the enhanced API
            const pdfResult = await this.callEnhancedPDFAPI(teacherId, teacherData);

            if (pdfResult.success) {
                // Show success animation
                this.showSuccessAnimation();
                
                // Wait for animation to complete, then open PDF
                setTimeout(() => {
                    window.open(pdfResult.url, '_blank');
                    this.hideLoadingOverlay();
                    this.showSuccessToast('CV PDF generated successfully!');
                }, 1000);
            } else {
                throw new Error(pdfResult.message || 'PDF generation failed');
            }

        } catch (error) {
            console.error('❌ CV generation error:', error);
            this.showErrorAnimation();
            
            setTimeout(() => {
                this.hideLoadingOverlay();
                this.showErrorToast('Failed to generate CV: ' + error.message);
            }, 1000);
        } finally {
            this.isGenerating = false;
        }
    }

    async fetchTeacherData(teacherId) {
        try {
            console.log('🔄 Fetching teacher data for ID:', teacherId);

            // Use the correct enhanced teacher profile API endpoint
            const response = await fetch(`/api/teacher/profile-enhanced?teacher_id=${teacherId}`);
            const result = await response.json();

            if (result.success) {
                console.log('✅ Teacher data fetched successfully');
                return result.teacher;
            } else {
                throw new Error(result.message || 'Failed to fetch teacher data');
            }
        } catch (error) {
            console.error('❌ Error fetching teacher data:', error);
            throw error;
        }
    }

    async callEnhancedPDFAPI(teacherId, teacherData) {
        try {
            console.log('🔄 Calling enhanced PDF API...');
            
            const response = await fetch('/api/generate-enhanced-cv-pdf', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    teacherId: teacherId,
                    teacherData: teacherData
                })
            });

            const result = await response.json();
            console.log('📄 PDF API response:', result);
            
            return result;
        } catch (error) {
            console.error('❌ Error calling PDF API:', error);
            throw error;
        }
    }

    showLoadingAnimation() {
        // Create loading overlay with animation
        const overlay = $(`
            <div id="cv-loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
                    <div class="text-center">
                        <!-- Animated CV Icon -->
                        <div class="cv-icon-container mb-6">
                            <div class="cv-icon">
                                <i class="fas fa-file-pdf text-6xl text-red-500"></i>
                                <div class="cv-animation-ring"></div>
                                <div class="cv-animation-ring cv-animation-ring-2"></div>
                            </div>
                        </div>
                        
                        <!-- Loading Text -->
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Generating CV</h3>
                        <p id="cv-loading-message" class="text-gray-600 mb-4">Fetching teacher information...</p>
                        
                        <!-- Progress Bar -->
                        <div class="cv-progress-container">
                            <div class="cv-progress-bar">
                                <div class="cv-progress-fill"></div>
                            </div>
                        </div>
                        
                        <!-- Loading Dots -->
                        <div class="cv-loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // Add CSS for animations
        if (!$('#cv-animation-styles').length) {
            $('head').append(`
                <style id="cv-animation-styles">
                    .cv-icon-container {
                        position: relative;
                        display: inline-block;
                    }
                    
                    .cv-icon {
                        position: relative;
                        z-index: 2;
                    }
                    
                    .cv-animation-ring {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 120px;
                        height: 120px;
                        border: 3px solid rgba(239, 68, 68, 0.3);
                        border-radius: 50%;
                        animation: cv-pulse 2s infinite;
                    }
                    
                    .cv-animation-ring-2 {
                        animation-delay: 1s;
                        width: 140px;
                        height: 140px;
                    }
                    
                    @keyframes cv-pulse {
                        0% {
                            transform: translate(-50%, -50%) scale(0.8);
                            opacity: 1;
                        }
                        100% {
                            transform: translate(-50%, -50%) scale(1.2);
                            opacity: 0;
                        }
                    }
                    
                    .cv-progress-container {
                        width: 100%;
                        height: 6px;
                        background: #e5e7eb;
                        border-radius: 3px;
                        overflow: hidden;
                        margin-bottom: 20px;
                    }
                    
                    .cv-progress-bar {
                        width: 100%;
                        height: 100%;
                        position: relative;
                    }
                    
                    .cv-progress-fill {
                        height: 100%;
                        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                        border-radius: 3px;
                        animation: cv-progress 3s ease-in-out infinite;
                    }
                    
                    @keyframes cv-progress {
                        0% { width: 0%; }
                        50% { width: 70%; }
                        100% { width: 100%; }
                    }
                    
                    .cv-loading-dots {
                        display: flex;
                        justify-content: center;
                        gap: 4px;
                    }
                    
                    .cv-loading-dots span {
                        width: 8px;
                        height: 8px;
                        background: #3b82f6;
                        border-radius: 50%;
                        animation: cv-bounce 1.4s ease-in-out infinite both;
                    }
                    
                    .cv-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
                    .cv-loading-dots span:nth-child(2) { animation-delay: -0.16s; }
                    
                    @keyframes cv-bounce {
                        0%, 80%, 100% {
                            transform: scale(0);
                        }
                        40% {
                            transform: scale(1);
                        }
                    }
                    
                    .cv-success-icon {
                        color: #10b981 !important;
                        animation: cv-success-bounce 0.6s ease-out;
                    }
                    
                    @keyframes cv-success-bounce {
                        0% { transform: scale(0.3); }
                        50% { transform: scale(1.1); }
                        100% { transform: scale(1); }
                    }
                    
                    .cv-error-icon {
                        color: #ef4444 !important;
                        animation: cv-error-shake 0.6s ease-out;
                    }
                    
                    @keyframes cv-error-shake {
                        0%, 100% { transform: translateX(0); }
                        25% { transform: translateX(-5px); }
                        75% { transform: translateX(5px); }
                    }
                </style>
            `);
        }

        $('body').append(overlay);
        
        // Animate in
        setTimeout(() => {
            overlay.addClass('opacity-100');
        }, 10);
    }

    updateLoadingMessage(message) {
        $('#cv-loading-message').text(message);
    }

    showSuccessAnimation() {
        // Update icon to success
        $('.cv-icon i').removeClass('fa-file-pdf text-red-500').addClass('fa-check-circle text-green-500 cv-success-icon');
        $('.cv-animation-ring').hide();
        
        // Update text
        $('#cv-loading-message').text('CV generated successfully!');
        
        // Hide progress bar and dots
        $('.cv-progress-container, .cv-loading-dots').hide();
    }

    showErrorAnimation() {
        // Update icon to error
        $('.cv-icon i').removeClass('fa-file-pdf text-red-500').addClass('fa-times-circle text-red-500 cv-error-icon');
        $('.cv-animation-ring').hide();
        
        // Update text
        $('#cv-loading-message').text('Failed to generate CV');
        
        // Hide progress bar and dots
        $('.cv-progress-container, .cv-loading-dots').hide();
    }

    hideLoadingOverlay() {
        $('#cv-loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    showSuccessToast(message) {
        this.showToast(message, 'success');
    }

    showErrorToast(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle';
        
        const toast = $(`
            <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            </div>
        `);
        
        $('body').append(toast);
        
        // Animate in
        setTimeout(() => {
            toast.removeClass('translate-x-full');
        }, 10);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            toast.addClass('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// Initialize CV Generator when document is ready
$(document).ready(function() {
    window.cvGenerator = new CVGenerator();
    console.log('✅ CV Generator ready');
});
