/**
 * Device Testing JavaScript
 * Provides device compatibility testing and feature detection
 */

class DeviceTesting {
    constructor() {
        this.results = {
            browser: {},
            device: {},
            features: {},
            performance: {}
        };
        this.init();
    }

    init() {
        try {
            this.detectBrowser();
            this.detectDevice();
            this.testFeatures();
            this.testPerformance();
            console.log('Device testing completed:', this.results);
        } catch (error) {
            console.error('Error in device testing:', error);
        }
    }

    detectBrowser() {
        const userAgent = navigator.userAgent;
        
        this.results.browser = {
            userAgent: userAgent,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            platform: navigator.platform,
            vendor: navigator.vendor
        };

        // Detect specific browsers
        if (userAgent.includes('Chrome')) {
            this.results.browser.name = 'Chrome';
        } else if (userAgent.includes('Firefox')) {
            this.results.browser.name = 'Firefox';
        } else if (userAgent.includes('Safari')) {
            this.results.browser.name = 'Safari';
        } else if (userAgent.includes('Edge')) {
            this.results.browser.name = 'Edge';
        } else {
            this.results.browser.name = 'Unknown';
        }
    }

    detectDevice() {
        this.results.device = {
            screenWidth: screen.width,
            screenHeight: screen.height,
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio || 1,
            touchSupport: 'ontouchstart' in window,
            orientation: screen.orientation ? screen.orientation.type : 'unknown'
        };

        // Detect device type
        const userAgent = navigator.userAgent;
        if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
            this.results.device.type = 'mobile';
        } else if (/iPad/i.test(userAgent)) {
            this.results.device.type = 'tablet';
        } else {
            this.results.device.type = 'desktop';
        }
    }

    testFeatures() {
        this.results.features = {
            localStorage: this.testLocalStorage(),
            sessionStorage: this.testSessionStorage(),
            webGL: this.testWebGL(),
            canvas: this.testCanvas(),
            webWorkers: this.testWebWorkers(),
            geolocation: this.testGeolocation(),
            notifications: this.testNotifications(),
            fileAPI: this.testFileAPI(),
            dragAndDrop: this.testDragAndDrop(),
            websockets: this.testWebSockets()
        };
    }

    testLocalStorage() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    testSessionStorage() {
        try {
            const test = 'test';
            sessionStorage.setItem(test, test);
            sessionStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    testWebGL() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch (e) {
            return false;
        }
    }

    testCanvas() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext && canvas.getContext('2d'));
        } catch (e) {
            return false;
        }
    }

    testWebWorkers() {
        return typeof Worker !== 'undefined';
    }

    testGeolocation() {
        return 'geolocation' in navigator;
    }

    testNotifications() {
        return 'Notification' in window;
    }

    testFileAPI() {
        return window.File && window.FileReader && window.FileList && window.Blob;
    }

    testDragAndDrop() {
        return 'draggable' in document.createElement('div');
    }

    testWebSockets() {
        return 'WebSocket' in window;
    }

    testPerformance() {
        const start = performance.now();
        
        // Simple performance test
        let sum = 0;
        for (let i = 0; i < 100000; i++) {
            sum += Math.random();
        }
        
        const end = performance.now();
        
        this.results.performance = {
            testDuration: end - start,
            memoryUsage: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null,
            timing: performance.timing ? {
                loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
            } : null
        };
    }

    getResults() {
        return this.results;
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            ...this.results
        };
        
        return JSON.stringify(report, null, 2);
    }

    displayResults() {
        console.group('Device Testing Results');
        console.log('Browser:', this.results.browser);
        console.log('Device:', this.results.device);
        console.log('Features:', this.results.features);
        console.log('Performance:', this.results.performance);
        console.groupEnd();
    }

    // Check if device meets minimum requirements
    checkCompatibility() {
        const requirements = {
            localStorage: true,
            canvas: true,
            fileAPI: true
        };

        const issues = [];
        
        Object.keys(requirements).forEach(feature => {
            if (requirements[feature] && !this.results.features[feature]) {
                issues.push(feature);
            }
        });

        return {
            compatible: issues.length === 0,
            issues: issues
        };
    }
}

// Initialize device testing
function initDeviceTesting() {
    if (!window.deviceTesting) {
        window.deviceTesting = new DeviceTesting();
        
        // Display compatibility warnings if needed
        const compatibility = window.deviceTesting.checkCompatibility();
        if (!compatibility.compatible) {
            console.warn('Device compatibility issues detected:', compatibility.issues);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initDeviceTesting);

// Fallback initialization
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initDeviceTesting();
}

console.log('Device testing script loaded');
