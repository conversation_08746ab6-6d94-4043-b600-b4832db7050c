// Enhanced Teacher Modal Utility Functions
// This file contains utility functions for the enhanced teacher modal

// Display notes in modal (enhanced with user bio integration)
function displayModalNotesAndOrganizations(teacher) {
    // Notes - combine user bio and staff notes
    const notesContainer = $('#modal-notes');
    let notesContent = '';

    // Add user bio if available (using userBio field from API)
    if (teacher.userBio && teacher.userBio.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📝 Profile Bio:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.userBio}</p>
        </div>`;
    }

    // Add staff notes if available
    if (teacher.notes && teacher.notes.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📋 Additional Notes:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.notes}</p>
        </div>`;
    }

    // Fallback to bio field if userBio is not available
    if (!notesContent && teacher.bio && teacher.bio.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📝 Profile Information:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.bio}</p>
        </div>`;
    }

    // If no content, show default message
    if (!notesContent) {
        notesContent = '<p class="text-gray-500 text-sm">No additional notes or bio information available</p>';
    }

    notesContainer.html(notesContent);
}

// Display skills and languages in modal
function displayModalSkillsAndLanguages(skills, languages) {
    // Skills
    const skillsContainer = $('#modal-skills-list');
    if (skills) {
        const skillsArray = skills.split(',');
        const skillsHtml = skillsArray.map(skill =>
            `<span class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${skill.trim()}</span>`
        ).join('');
        skillsContainer.html(skillsHtml);
    } else {
        skillsContainer.html('<p class="text-gray-500 text-xs">No skills specified</p>');
    }

    // Languages
    const languagesContainer = $('#modal-languages-list');
    if (languages) {
        const languagesArray = languages.split(',');
        const languagesHtml = languagesArray.map(language =>
            `<span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${language.trim()}</span>`
        ).join('');
        languagesContainer.html(languagesHtml);
    } else {
        languagesContainer.html('<p class="text-gray-500 text-xs">No languages specified</p>');
    }
}

// Display achievements in modal
function displayModalAchievements(awards, training) {
    // Awards
    const awardsContainer = $('#modal-awards-list');
    if (awards) {
        const awardsArray = awards.split(',');
        const awardsHtml = awardsArray.map(award =>
            `<div class="flex items-start space-x-2">
                <i class="fas fa-trophy text-yellow-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${award.trim()}</span>
            </div>`
        ).join('');
        awardsContainer.html(awardsHtml);
    } else {
        awardsContainer.html('<p class="text-gray-500 text-xs">No awards specified</p>');
    }

    // Training
    const trainingContainer = $('#modal-training-list');
    if (training) {
        const trainingArray = training.split(',');
        const trainingHtml = trainingArray.map(program =>
            `<div class="flex items-start space-x-2">
                <i class="fas fa-certificate text-green-600 mt-1 text-xs"></i>
                <span class="text-gray-700 text-xs">${program.trim()}</span>
            </div>`
        ).join('');
        trainingContainer.html(trainingHtml);
    } else {
        trainingContainer.html('<p class="text-gray-500 text-xs">No training programs specified</p>');
    }
}

// Modal utility functions
function getModalInitials(teacher) {
    const name = teacher.fullName || teacher.name || teacher.username || 'T';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
}

function formatModalDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function capitalizeModalFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function showModalError(message) {
    const loadingDiv = document.getElementById('modal-loading');
    loadingDiv.innerHTML = `
        <div class="text-center py-12">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <p class="text-gray-600">${message}</p>
            <button onclick="closeEnhancedTeacherModal()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                Close
            </button>
        </div>
    `;
}

// Search and filter functionality for teacher management page
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('search-teachers');
    const performanceFilter = document.getElementById('filter-performance');
    const teacherRows = document.querySelectorAll('.teacher-row');

    if (searchInput && performanceFilter && teacherRows.length > 0) {
        function filterTeachers() {
            const searchTerm = searchInput.value.toLowerCase();
            const performanceValue = performanceFilter.value;

            teacherRows.forEach(row => {
                const name = row.dataset.name || '';
                const email = row.dataset.email || '';
                const performance = row.dataset.performance || '';

                const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                const matchesPerformance = !performanceValue || performance === performanceValue;

                if (matchesSearch && matchesPerformance) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        searchInput.addEventListener('input', filterTeachers);
        performanceFilter.addEventListener('change', filterTeachers);
    }
});

// Refresh teacher data functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    window.location.reload();
}

// Auto-refresh teacher performance data
function refreshPageData() {
    fetch('/principal/api/teacher-performance')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the table without full page reload if needed
                console.log('Teacher performance data refreshed');
            }
        })
        .catch(error => {
            console.error('Error refreshing teacher performance:', error);
        });
}

// Start auto-refresh every 5 minutes when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (typeof startAutoRefresh === 'function') {
        startAutoRefresh(300000); // 5 minutes
    }
});

console.log('Enhanced teacher modal utility functions loaded');
