/**
 * Floating <PERSON><PERSON> Icon
 * Provides a floating chat button with user search and group management
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (!document.body.classList.contains('logged-in')) return;

    // Create floating chat button
    const chatButton = document.createElement('div');
    chatButton.id = 'floatingChatButton';
    chatButton.className = 'fixed bottom-6 right-6 bg-purple-600 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg cursor-pointer z-50 hover:bg-purple-700 transition-all';
    chatButton.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <span id="chatNotificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
    `;
    document.body.appendChild(chatButton);

    // Create chat popup
    const chatPopup = document.createElement('div');
    chatPopup.id = 'chatPopup';
    chatPopup.className = 'fixed bottom-24 right-6 bg-white rounded-lg shadow-xl w-96 h-[500px] z-50 hidden transition-all transform scale-95 opacity-0 flex flex-col';
    chatPopup.innerHTML = `
        <div class="bg-purple-600 text-white p-3 rounded-t-lg flex justify-between items-center">
            <h3 class="font-semibold">Chat</h3>
            <div class="flex space-x-2">
                <button id="createGroupBtn" class="p-1 hover:bg-purple-700 rounded" title="Create Group">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                </button>
                <button id="closeChatPopupBtn" class="p-1 hover:bg-purple-700 rounded">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex flex-1 overflow-hidden">
            <!-- Left sidebar - Contacts/Groups -->
            <div class="w-1/3 border-r flex flex-col">
                <div class="p-2">
                    <input type="text" id="chatSearchInput" placeholder="Search users..." class="w-full border rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>

                <div class="flex border-b">
                    <button id="recentTabBtn" class="flex-1 py-2 px-3 text-sm font-medium text-center border-b-2 border-purple-600 text-purple-600">
                        Recent
                    </button>
                    <button id="groupsTabBtn" class="flex-1 py-2 px-3 text-sm font-medium text-center border-b-2 border-transparent hover:text-purple-600">
                        Groups
                    </button>
                </div>

                <div id="recentChatsContainer" class="flex-1 overflow-y-auto">
                    <div class="text-center text-gray-500 py-4 text-sm" id="recentChatsLoading">
                        Loading recent chats...
                    </div>
                    <div id="recentChatsList" class="divide-y"></div>
                </div>

                <div id="groupsContainer" class="flex-1 overflow-y-auto hidden">
                    <div class="text-center text-gray-500 py-4 text-sm" id="groupsLoading">
                        Loading groups...
                    </div>
                    <div id="groupsList" class="divide-y"></div>
                </div>

                <div id="searchResultsContainer" class="flex-1 overflow-y-auto hidden">
                    <div id="searchResults" class="divide-y"></div>
                </div>
            </div>

            <!-- Right side - Chat messages -->
            <div class="w-2/3 flex flex-col" id="chatMessagesArea">
                <div class="p-3 border-b">
                    <h3 class="font-medium text-gray-800" id="chatRecipientName">Select a conversation</h3>
                </div>

                <div class="flex-1 overflow-y-auto p-3" id="chatMessagesContainer">
                    <div class="flex items-center justify-center h-full text-gray-500 text-sm">
                        Select a conversation to start chatting
                    </div>
                </div>

                <div class="p-2 border-t" id="chatInputContainer">
                    <div class="flex">
                        <input type="text" id="chatMessageInput" placeholder="Type a message..." class="flex-1 border rounded-l-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600" disabled>
                        <button id="sendMessageBtn" class="bg-purple-600 text-white px-4 rounded-r-md hover:bg-purple-700 disabled:opacity-50" disabled>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-3 border-t">
            <a href="/chat" class="block w-full py-2 px-4 bg-purple-600 text-white rounded-md text-center hover:bg-purple-700 transition">
                Open Full Chat
            </a>
        </div>
    `;
    document.body.appendChild(chatPopup);

    // Create group modal
    const createGroupModal = document.createElement('div');
    createGroupModal.id = 'createGroupModal';
    createGroupModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
    createGroupModal.innerHTML = `
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-900">Create Group</h3>
                <button id="closeGroupModalBtn" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="createGroupForm">
                <div class="mb-4">
                    <label for="groupName" class="block text-gray-700 text-sm font-medium mb-2">Group Name</label>
                    <input type="text" id="groupName" name="groupName" required class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2">Add Members</label>
                    <div class="flex items-center">
                        <input type="text" id="memberSearchInput" placeholder="Search users..." class="flex-1 border rounded-l-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                        <button type="button" id="searchMembersBtn" class="bg-purple-600 text-white px-4 py-2 rounded-r-md hover:bg-purple-700">
                            Search
                        </button>
                    </div>
                </div>

                <div id="memberSearchResults" class="mb-4 max-h-40 overflow-y-auto hidden border rounded-md p-2"></div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2">Selected Members</label>
                    <div id="selectedMembers" class="border rounded-md p-2 min-h-[60px]">
                        <div class="text-gray-500 text-sm">No members selected</div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                        Create Group
                    </button>
                </div>
            </form>
        </div>
    `;
    document.body.appendChild(createGroupModal);

    // Event listeners
    chatButton.addEventListener('click', toggleChatPopup);
    document.getElementById('closeChatPopupBtn').addEventListener('click', closeChatPopup);
    document.getElementById('createGroupBtn').addEventListener('click', openCreateGroupModal);
    document.getElementById('closeGroupModalBtn').addEventListener('click', closeCreateGroupModal);
    document.getElementById('recentTabBtn').addEventListener('click', showRecentChats);
    document.getElementById('groupsTabBtn').addEventListener('click', showGroups);
    document.getElementById('chatSearchInput').addEventListener('input', handleSearch);
    document.getElementById('searchMembersBtn').addEventListener('click', searchMembers);
    document.getElementById('createGroupForm').addEventListener('submit', createGroup);

    // Load initial data
    loadRecentChats();
    loadGroups();
    updateUnreadCount();

    // Set interval to update unread count
    setInterval(updateUnreadCount, 30000);

    // Functions
    function toggleChatPopup() {
        const popup = document.getElementById('chatPopup');
        if (popup.classList.contains('hidden')) {
            // Show popup
            popup.classList.remove('hidden');
            setTimeout(() => {
                popup.classList.remove('scale-95', 'opacity-0');
                popup.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            // Hide popup
            popup.classList.remove('scale-100', 'opacity-100');
            popup.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                popup.classList.add('hidden');
            }, 300);
        }
    }

    function closeChatPopup() {
        const popup = document.getElementById('chatPopup');
        popup.classList.remove('scale-100', 'opacity-100');
        popup.classList.add('scale-95', 'opacity-0');
        setTimeout(() => {
            popup.classList.add('hidden');
        }, 300);
    }

    function openCreateGroupModal() {
        document.getElementById('createGroupModal').classList.remove('hidden');
        closeChatPopup();
    }

    function closeCreateGroupModal() {
        document.getElementById('createGroupModal').classList.add('hidden');
    }

    function showRecentChats() {
        document.getElementById('recentTabBtn').classList.add('border-purple-600', 'text-purple-600');
        document.getElementById('groupsTabBtn').classList.remove('border-purple-600', 'text-purple-600');
        document.getElementById('recentChatsContainer').classList.remove('hidden');
        document.getElementById('groupsContainer').classList.add('hidden');
        document.getElementById('searchResultsContainer').classList.add('hidden');
    }

    function showGroups() {
        document.getElementById('groupsTabBtn').classList.add('border-purple-600', 'text-purple-600');
        document.getElementById('recentTabBtn').classList.remove('border-purple-600', 'text-purple-600');
        document.getElementById('groupsContainer').classList.remove('hidden');
        document.getElementById('recentChatsContainer').classList.add('hidden');
        document.getElementById('searchResultsContainer').classList.add('hidden');
    }

    async function loadRecentChats() {
        try {
            const response = await fetch('/api/chat/recent');

            // Handle unauthorized errors
            if (response.status === 401) {
                document.getElementById('recentChatsLoading').textContent = 'Please log in to view chats';
                return;
            }

            const data = await response.json();

            if (data.success) {
                const recentChatsList = document.getElementById('recentChatsList');
                document.getElementById('recentChatsLoading').classList.add('hidden');

                if (!data.chats || data.chats.length === 0) {
                    recentChatsList.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            No recent chats
                        </div>
                    `;
                    return;
                }

                recentChatsList.innerHTML = '';
                data.chats.forEach(chat => {
                    const chatItem = document.createElement('div');
                    chatItem.className = 'block p-3 hover:bg-gray-50 transition cursor-pointer';
                    chatItem.dataset.userId = chat.user_id;
                    chatItem.dataset.username = chat.username;
                    chatItem.addEventListener('click', () => loadUserChat(chat.user_id, chat.username));

                    chatItem.innerHTML = `
                        <div class="flex items-center">
                            <div class="relative flex-shrink-0">
                                ${chat.profile_image ? `<img src="${chat.profile_image}" alt="${chat.username}" class="w-10 h-10 rounded-full object-cover">` : `<div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-xl font-bold text-white">${chat.username.charAt(0).toUpperCase()}</span>
                            </div>`}
                                ${chat.unread_count > 0 ? `
                                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        ${chat.unread_count}
                                    </span>
                                ` : ''}
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-medium text-gray-900">${chat.username}</h4>
                                    ${chat.last_message_time ? `
                                        <span class="text-xs text-gray-500">
                                            ${new Date(chat.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                                        </span>
                                    ` : ''}
                                </div>
                                <p class="text-sm text-gray-500 truncate">${chat.last_message || 'No messages yet'}</p>
                            </div>
                        </div>
                    `;

                    recentChatsList.appendChild(chatItem);
                });
            } else {
                document.getElementById('recentChatsLoading').textContent = 'Error loading recent chats';
            }
        } catch (error) {
            console.error('Error loading recent chats:', error);
            document.getElementById('recentChatsLoading').textContent = 'Error loading recent chats';
        }
    }

    async function loadGroups() {
        try {
            const response = await fetch('/api/chat/groups');

            // Handle unauthorized errors
            if (response.status === 401) {
                document.getElementById('groupsLoading').textContent = 'Please log in to view groups';
                return;
            }

            const data = await response.json();

            if (data.success) {
                const groupsList = document.getElementById('groupsList');
                document.getElementById('groupsLoading').classList.add('hidden');

                if (!data.groups || data.groups.length === 0) {
                    groupsList.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            No groups found
                        </div>
                    `;
                    return;
                }

                groupsList.innerHTML = '';
                data.groups.forEach(group => {
                    const groupItem = document.createElement('div');
                    groupItem.className = 'block p-3 hover:bg-gray-50 transition cursor-pointer';
                    groupItem.dataset.groupId = group.group_id;
                    groupItem.dataset.groupName = group.name;
                    groupItem.addEventListener('click', () => loadGroupChat(group.group_id, group.name));

                    groupItem.innerHTML = `
                        <div class="flex items-center">
                            <div class="relative bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center">
                                <span class="text-purple-600 font-bold text-lg">${group.name.charAt(0).toUpperCase()}</span>
                                ${group.unread_count > 0 ? `
                                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        ${group.unread_count}
                                    </span>
                                ` : ''}
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-medium text-gray-900">${group.name}</h4>
                                    ${group.last_message_time ? `
                                        <span class="text-xs text-gray-500">
                                            ${new Date(group.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                                        </span>
                                    ` : ''}
                                </div>
                                ${group.last_message ? `
                                    <p class="text-sm text-gray-500 truncate">
                                        ${group.last_sender ? `<span class="font-medium">${group.last_sender}:</span> ` : ''}
                                        ${group.last_message}
                                    </p>
                                ` : `
                                    <p class="text-sm text-gray-500">No messages yet</p>
                                `}
                            </div>
                        </div>
                    `;

                    groupsList.appendChild(groupItem);
                });
            } else {
                document.getElementById('groupsLoading').textContent = 'Error loading groups';
            }
        } catch (error) {
            console.error('Error loading groups:', error);
            document.getElementById('groupsLoading').textContent = 'Error loading groups';
        }
    }

    async function handleSearch() {
        const searchInput = document.getElementById('chatSearchInput');
        const searchTerm = searchInput.value.trim();

        if (searchTerm.length < 2) {
            // If search term is too short, show recent chats
            if (document.getElementById('recentTabBtn').classList.contains('border-purple-600')) {
                showRecentChats();
            } else {
                showGroups();
            }
            return;
        }

        try {
            const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                const searchResults = document.getElementById('searchResults');
                const searchResultsContainer = document.getElementById('searchResultsContainer');

                // Show search results container
                document.getElementById('recentChatsContainer').classList.add('hidden');
                document.getElementById('groupsContainer').classList.add('hidden');
                searchResultsContainer.classList.remove('hidden');

                searchResults.innerHTML = `
                    <div class="text-center text-gray-500 py-4">
                        Please log in to search users
                    </div>
                `;
                return;
            }

            const data = await response.json();

            if (data.success) {
                const searchResults = document.getElementById('searchResults');
                const searchResultsContainer = document.getElementById('searchResultsContainer');

                // Show search results container
                document.getElementById('recentChatsContainer').classList.add('hidden');
                document.getElementById('groupsContainer').classList.add('hidden');
                searchResultsContainer.classList.remove('hidden');

                if (!data.users || data.users.length === 0) {
                    searchResults.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            No users found
                        </div>
                    `;
                    return;
                }

                searchResults.innerHTML = '';
                data.users.forEach(user => {
                    const userItem = document.createElement('a');
                    userItem.href = `/chat/user/${user.id}`;
                    userItem.className = 'block p-3 hover:bg-gray-50 transition';

                    userItem.innerHTML = `
                        <div class="flex items-center">
                            ${user.profile_image ? `<img src="${user.profile_image}" alt="${user.username}" class="w-10 h-10 rounded-full object-cover">` : `<div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-xl font-bold text-white">${user.username.charAt(0).toUpperCase()}</span>
                            </div>`}
                            <div class="ml-3">
                                <h4 class="font-medium text-gray-900">${user.username}</h4>
                                <p class="text-sm text-gray-500">${user.role}</p>
                            </div>
                        </div>
                    `;

                    searchResults.appendChild(userItem);
                });
            }
        } catch (error) {
            console.error('Error searching users:', error);
        }
    }

    async function searchMembers() {
        const searchInput = document.getElementById('memberSearchInput');
        const searchTerm = searchInput.value.trim();

        if (searchTerm.length < 2) {
            return;
        }

        try {
            const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                const searchResults = document.getElementById('memberSearchResults');
                searchResults.classList.remove('hidden');
                searchResults.innerHTML = `
                    <div class="text-center text-gray-500 py-2">
                        Please log in to search users
                    </div>
                `;
                return;
            }

            const data = await response.json();

            if (data.success) {
                const searchResults = document.getElementById('memberSearchResults');
                searchResults.classList.remove('hidden');

                if (!data.users || data.users.length === 0) {
                    searchResults.innerHTML = `
                        <div class="text-center text-gray-500 py-2">
                            No users found
                        </div>
                    `;
                    return;
                }

                searchResults.innerHTML = '';
                data.users.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'flex items-center justify-between p-2 hover:bg-gray-50 cursor-pointer';
                    userItem.dataset.userId = user.id;
                    userItem.dataset.username = user.username;
                    userItem.dataset.profileImage = user.profile_image || '';

                    userItem.innerHTML = `
                        <div class="flex items-center">
                            ${user.profile_image ? `<img src="${user.profile_image}" alt="${user.username}" class="w-8 h-8 rounded-full object-cover">` : `<div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-sm font-bold text-white">${user.username.charAt(0).toUpperCase()}</span>
                            </div>`}
                            <div class="ml-2">
                                <h4 class="font-medium text-gray-900">${user.username}</h4>
                                <p class="text-xs text-gray-500">${user.role}</p>
                            </div>
                        </div>
                        <button type="button" class="add-member-btn text-purple-600 hover:text-purple-800">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    `;

                    userItem.querySelector('.add-member-btn').addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        addMemberToGroup(user.id, user.username, user.profile_image);
                    });

                    searchResults.appendChild(userItem);
                });
            }
        } catch (error) {
            console.error('Error searching users:', error);
        }
    }

    function addMemberToGroup(userId, username, profileImage) {
        const selectedMembers = document.getElementById('selectedMembers');

        // Check if user is already added
        if (selectedMembers.querySelector(`[data-user-id="${userId}"]`)) {
            return;
        }

        // Remove "No members selected" message if present
        const noMembersMsg = selectedMembers.querySelector('.text-gray-500');
        if (noMembersMsg) {
            noMembersMsg.remove();
        }

        const memberItem = document.createElement('div');
        memberItem.className = 'inline-flex items-center bg-purple-100 rounded-full px-3 py-1 m-1';
        memberItem.dataset.userId = userId;

        memberItem.innerHTML = `
            ${profileImage ? `<img src="${profileImage}" alt="${username}" class="w-5 h-5 rounded-full object-cover mr-1">` : `<div class="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center mr-1">
                <span class="text-xs font-bold text-white">${username.charAt(0).toUpperCase()}</span>
            </div>`}
            <span class="text-sm text-purple-800">${username}</span>
            <button type="button" class="ml-1 text-purple-600 hover:text-purple-800">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <input type="hidden" name="members[]" value="${userId}">
        `;

        memberItem.querySelector('button').addEventListener('click', function() {
            memberItem.remove();

            // Add "No members selected" message if no members left
            if (selectedMembers.querySelectorAll('[data-user-id]').length === 0) {
                selectedMembers.innerHTML = `<div class="text-gray-500 text-sm">No members selected</div>`;
            }
        });

        selectedMembers.appendChild(memberItem);
    }

    async function createGroup(e) {
        e.preventDefault();

        const groupName = document.getElementById('groupName').value.trim();
        const memberInputs = document.querySelectorAll('input[name="members[]"]');
        const members = Array.from(memberInputs).map(input => input.value);

        if (!groupName) {
            alert('Please enter a group name');
            return;
        }

        if (members.length === 0) {
            alert('Please add at least one member to the group');
            return;
        }

        try {
            const response = await fetch('/api/chat/groups/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: groupName,
                    members
                })
            });

            // Handle unauthorized errors
            if (response.status === 401) {
                alert('Please log in to create groups');
                return;
            }

            const data = await response.json();

            if (data.success) {
                // Close modal and reload groups
                closeCreateGroupModal();
                loadGroups();

                // Reset form
                document.getElementById('createGroupForm').reset();
                document.getElementById('selectedMembers').innerHTML = `<div class="text-gray-500 text-sm">No members selected</div>`;
                document.getElementById('memberSearchResults').classList.add('hidden');

                // Show success message
                alert('Group created successfully');
            } else {
                alert(data.message || 'Failed to create group');
            }
        } catch (error) {
            console.error('Error creating group:', error);
            alert('Failed to create group');
        }
    }

    async function updateUnreadCount() {
        try {
            const response = await fetch('/api/chat/unread');

            // Handle unauthorized errors silently
            if (response.status === 401) {
                return;
            }

            const data = await response.json();

            if (data.success) {
                const badge = document.getElementById('chatNotificationBadge');

                if (data.count > 0) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                    badge.classList.remove('hidden');
                } else {
                    badge.classList.add('hidden');
                }
            }
        } catch (error) {
            console.error('Error fetching unread count:', error);
        }
    }

    // Chat message functions
    let currentChatType = null; // 'user' or 'group'
    let currentChatId = null;

    async function loadUserChat(userId, username) {
        currentChatType = 'user';
        currentChatId = userId;

        // Update UI
        document.getElementById('chatRecipientName').textContent = username;
        document.getElementById('chatMessageInput').disabled = false;
        document.getElementById('sendMessageBtn').disabled = false;

        // Load messages
        const messagesContainer = document.getElementById('chatMessagesContainer');
        messagesContainer.innerHTML = '<div class="text-center py-4">Loading messages...</div>';

        try {
            const response = await fetch(`/api/chat/messages/user/${userId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Please log in to view messages</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/user/${userId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));
            } else {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Error loading messages</div>';
            }
        } catch (error) {
            console.error('Error loading user chat messages:', error);
            messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Error loading messages</div>';
        }
    }

    async function loadGroupChat(groupId, groupName) {
        currentChatType = 'group';
        currentChatId = groupId;

        // Update UI
        document.getElementById('chatRecipientName').textContent = groupName;
        document.getElementById('chatMessageInput').disabled = false;
        document.getElementById('sendMessageBtn').disabled = false;

        // Load messages
        const messagesContainer = document.getElementById('chatMessagesContainer');
        messagesContainer.innerHTML = '<div class="text-center py-4">Loading messages...</div>';

        try {
            const response = await fetch(`/api/chat/messages/group/${groupId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Please log in to view messages</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/group/${groupId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));
            } else {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Error loading messages</div>';
            }
        } catch (error) {
            console.error('Error loading group chat messages:', error);
            messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Error loading messages</div>';
        }
    }

    function displayMessages(messages) {
        const messagesContainer = document.getElementById('chatMessagesContainer');

        if (!messages || messages.length === 0) {
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">No messages yet</div>';
            return;
        }

        messagesContainer.innerHTML = '';

        messages.forEach(message => {
            const isCurrentUser = message.sender_id === parseInt(document.body.dataset.userId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `flex mb-2 ${isCurrentUser ? 'justify-end' : 'justify-start'}`;

            messageDiv.innerHTML = `
                <div class="${isCurrentUser ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'} rounded-lg py-2 px-3 max-w-[70%]">
                    ${!isCurrentUser ? `<div class="text-xs text-gray-600 font-medium mb-1">${message.sender_name || 'User'}</div>` : ''}
                    <div>${message.message}</div>
                    <div class="text-xs ${isCurrentUser ? 'text-purple-200' : 'text-gray-500'} text-right mt-1">
                        ${new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </div>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
        });

        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    async function sendMessage() {
        const messageInput = document.getElementById('chatMessageInput');
        const message = messageInput.value.trim();

        if (!message || !currentChatType || !currentChatId) return;

        messageInput.value = '';

        try {
            const endpoint = currentChatType === 'user'
                ? `/api/chat/messages/user/${currentChatId}/send`
                : `/api/chat/messages/group/${currentChatId}/send`;

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            // Handle unauthorized errors
            if (response.status === 401) {
                const messagesContainer = document.getElementById('chatMessagesContainer');
                if (messagesContainer) {
                    messagesContainer.innerHTML = '<div class="text-center py-4 text-red-500">Please log in to send messages</div>';
                }
                return;
            }

            const data = await response.json();

            if (data.success) {
                // Reload messages
                if (currentChatType === 'user') {
                    loadUserChat(currentChatId, document.getElementById('chatRecipientName').textContent);
                } else {
                    loadGroupChat(currentChatId, document.getElementById('chatRecipientName').textContent);
                }
            }
        } catch (error) {
            console.error('Error sending message:', error);
        }
    }

    // Add event listeners for chat functionality
    document.getElementById('sendMessageBtn').addEventListener('click', sendMessage);

    // Handle enter key in message input
    document.getElementById('chatMessageInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            sendMessage();
        }
    });

    // Add event listeners for chat popup
    chatButton.addEventListener('click', toggleChatPopup);
    document.getElementById('closeChatPopupBtn').addEventListener('click', closeChatPopup);
    document.getElementById('createGroupBtn').addEventListener('click', openCreateGroupModal);
    document.getElementById('closeGroupModalBtn').addEventListener('click', closeCreateGroupModal);
    document.getElementById('recentTabBtn').addEventListener('click', showRecentChats);
    document.getElementById('groupsTabBtn').addEventListener('click', showGroups);

    // Update unread count on page load
    updateUnreadCount();

    // Update unread count every 30 seconds
    setInterval(updateUnreadCount, 30000);
});
