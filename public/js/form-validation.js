/**
 * Form Validation
 *
 * This script handles all validation for forms, including:
 * - Login form validation
 * - Registration form validation
 * - Password reset form validation
 * - Forgot password form validation
 */

console.log('Form validation script loaded');

// Execute when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing form validation');

    // Flag to indicate that validation is loaded
    window.formValidationLoaded = true;

    // Initialize the appropriate validation based on the form present on the page
    initLoginValidation();
    initRegisterValidation();
    initPasswordResetValidation();
    initForgotPasswordValidation();

    // Fix password toggle for all forms
    initPasswordToggle();
});

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Validate date of birth in DD-MMM-YYYY format
function validateDateOfBirth(dateStr, feedbackElement) {
    // Validate DD-MMM-YYYY format
    const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4}$/;

    if (!dateRegex.test(dateStr)) {
        feedbackElement.textContent = 'Please enter date in DD-MMM-YYYY format (e.g., 15-Jan-2000)';
        feedbackElement.style.color = 'red';
        return false;
    }

    // Parse the date
    const parts = dateStr.split('-');
    const day = parseInt(parts[0], 10);
    const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(parts[1]);
    const year = parseInt(parts[2], 10);

    if (month === -1) {
        feedbackElement.textContent = 'Invalid month abbreviation';
        feedbackElement.style.color = 'red';
        return false;
    }

    const birthDate = new Date(year, month, day);
    const today = new Date();

    // Check if date is valid
    if (birthDate.getDate() !== day || birthDate.getMonth() !== month || birthDate.getFullYear() !== year) {
        feedbackElement.textContent = 'Invalid date';
        feedbackElement.style.color = 'red';
        return false;
    }

    // Check if date is in the future
    if (birthDate > today) {
        feedbackElement.textContent = 'Date of birth cannot be in the future';
        feedbackElement.style.color = 'red';
        return false;
    }

    // Calculate age
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    if (age < 13) {
        feedbackElement.textContent = 'You must be at least 13 years old to register';
        feedbackElement.style.color = 'red';
        return false;
    }

    // Valid date
    feedbackElement.textContent = 'Valid date';
    feedbackElement.style.color = 'green';
    return true;
}

// Calculate password strength
function calculatePasswordStrength(password) {
    if (!password) return 0;

    let score = 0;

    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;

    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1; // Has uppercase
    if (/[a-z]/.test(password)) score += 1; // Has lowercase
    if (/[0-9]/.test(password)) score += 1; // Has number
    if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

    // Normalize score to 0-4 range
    return Math.min(4, Math.floor(score / 1.5));
}

// Update password strength UI
function updatePasswordStrengthUI(strength, strengthBars, strengthText) {
    // Reset all bars
    strengthBars.forEach(bar => {
        if (bar) bar.className = 'h-1 w-1/4 bg-gray-200 rounded-sm';
    });

    // Set text based on strength
    const strengthLabels = ['Very Weak', 'Weak', 'Medium', 'Strong', 'Very Strong'];
    if (strengthText) strengthText.textContent = strengthLabels[strength];

    // Set colors based on strength
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];

    // Update bars based on strength
    for (let i = 0; i < strength; i++) {
        if (strengthBars[i]) {
            strengthBars[i].className = `h-1 w-1/4 ${colors[Math.min(i, colors.length - 1)]} rounded-sm`;
        }
    }
}

// Initialize password toggle functionality
function initPasswordToggle() {
    console.log('Initializing password toggle');

    // Find all toggle password buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');

    if (toggleButtons.length === 0) {
        console.log('No toggle password buttons found');
        return;
    }

    console.log(`Found ${toggleButtons.length} toggle password buttons`);

    // Add event listeners to toggle buttons
    toggleButtons.forEach(button => {
        // Remove existing event listeners to avoid duplicates
        const newButton = button.cloneNode(true);
        if (button.parentNode) {
            button.parentNode.replaceChild(newButton, button);
        }

        // Add new event listener
        newButton.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target') || 'password';
            const passwordField = document.getElementById(targetId);
            const eyeIcon = this.querySelector('.eye-icon');
            const eyeSlashIcon = this.querySelector('.eye-slash-icon');

            if (passwordField) {
                // Toggle password visibility
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    if (eyeIcon) eyeIcon.classList.add('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.remove('hidden');
                } else {
                    passwordField.type = 'password';
                    if (eyeIcon) eyeIcon.classList.remove('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.add('hidden');
                }
            }
        });
    });
}

// Initialize login form validation
function initLoginValidation() {
    console.log('Initializing login validation');

    // Get form elements
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) {
        console.log('Login form not found, skipping login validation');
        return;
    }

    console.log('Login form found, setting up validation');

    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginButton = document.getElementById('loginButton');

    // Create email feedback element if it doesn't exist
    if (!document.getElementById('email-feedback') && emailInput) {
        const emailContainer = emailInput.parentNode;
        const feedbackDiv = document.createElement('div');
        feedbackDiv.id = 'email-feedback';
        feedbackDiv.className = 'mt-1 text-sm text-gray-600';
        feedbackDiv.style.minHeight = '20px';
        feedbackDiv.style.display = 'block';
        emailContainer.appendChild(feedbackDiv);
        console.log('Created email-feedback element for login form');
    }

    // Get the feedback element
    const emailFeedback = document.getElementById('email-feedback');

    // Email validation
    if (emailInput && emailFeedback) {
        emailInput.addEventListener('input', function() {
            const email = this.value.trim();
            console.log('Email input:', email);

            // Simple email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email) {
                emailFeedback.textContent = '';
                return;
            }

            if (!emailRegex.test(email)) {
                emailFeedback.textContent = 'Please enter a valid email address';
                emailFeedback.style.color = 'red';
                return;
            }

            // Valid email format
            emailFeedback.textContent = 'Valid email format';
            emailFeedback.style.color = 'green';

            // Optional: Check if email exists in the system
            fetch(`/api/users/check-email?email=${encodeURIComponent(email)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Email check response:', data);
                    if (data.available) {
                        // Email not found in the system
                        emailFeedback.textContent = 'Email not registered. Do you need to create an account?';
                        emailFeedback.style.color = 'orange';
                    } else {
                        // Email exists
                        emailFeedback.textContent = 'Email recognized';
                        emailFeedback.style.color = 'green';
                    }
                })
                .catch(error => {
                    console.error('Error checking email:', error);
                    // Don't show error to user, just keep the "Valid email format" message
                });
        });
    }

    // Form submission
    loginForm.addEventListener('submit', function(e) {
        if (!emailInput || !passwordInput) return;

        const email = emailInput.value.trim();
        const password = passwordInput.value;
        let hasErrors = false;

        // Get or create feedback elements
        const emailFeedback = document.getElementById('email-feedback');

        let passwordFeedback = document.getElementById('password-feedback');
        if (!passwordFeedback && passwordInput) {
            passwordFeedback = document.createElement('div');
            passwordFeedback.id = 'password-feedback';
            passwordFeedback.className = 'mt-1 text-sm';
            passwordFeedback.style.minHeight = '20px';
            passwordInput.parentNode.appendChild(passwordFeedback);
        }

        // Reset feedback
        if (emailFeedback) emailFeedback.textContent = '';
        if (passwordFeedback) passwordFeedback.textContent = '';

        // Basic validation
        if (!email) {
            e.preventDefault();
            hasErrors = true;
            if (emailFeedback) {
                emailFeedback.textContent = 'Email is required';
                emailFeedback.style.color = 'red';
            }
        }

        if (!password) {
            e.preventDefault();
            hasErrors = true;
            if (passwordFeedback) {
                passwordFeedback.textContent = 'Password is required';
                passwordFeedback.style.color = 'red';
            }
        }

        // Email format validation
        if (email && emailFeedback) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                hasErrors = true;
                emailFeedback.textContent = 'Please enter a valid email address';
                emailFeedback.style.color = 'red';
            }
        }

        if (!hasErrors && loginButton) {
            // Disable button to prevent double submission
            loginButton.disabled = true;
            loginButton.textContent = 'Signing in...';
        }
    });
}

// Initialize registration form validation
function initRegisterValidation() {
    console.log('Initializing registration validation');

    // Form elements
    const registerForm = document.getElementById('registerForm');
    if (!registerForm) {
        console.log('Registration form not found, skipping registration validation');
        return;
    }

    console.log('Registration form found, setting up validation');

    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const dateOfBirthInput = document.getElementById('date_of_birth');
    const registerButton = document.getElementById('registerButton');

    // Feedback elements
    const usernameFeedback = document.getElementById('username-feedback');
    const emailFeedback = document.getElementById('email-feedback');
    const passwordMatchFeedback = document.getElementById('password-match-feedback');

    // Password strength elements
    const strengthBars = [
        document.getElementById('strength-bar-1'),
        document.getElementById('strength-bar-2'),
        document.getElementById('strength-bar-3'),
        document.getElementById('strength-bar-4')
    ];
    const strengthText = document.getElementById('password-strength-text');

    // State variables
    let usernameAvailable = false;
    let emailAvailable = false;
    let passwordsMatch = false;
    let passwordStrong = false;

    // Username validation
    if (usernameInput && usernameFeedback) {
        const checkUsernameDebounced = debounce(function(username) {
            // Check username availability via API
            fetch(`/api/users/check-username?username=${encodeURIComponent(username)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Username check response:', data);
                    usernameAvailable = data.available;
                    usernameFeedback.textContent = data.message;
                    usernameFeedback.style.color = data.available ? 'green' : 'red';
                })
                .catch(error => {
                    console.error('Error checking username:', error);
                    usernameFeedback.textContent = 'Error checking availability';
                    usernameFeedback.style.color = 'red';
                    usernameAvailable = false;
                });
        }, 300);

        usernameInput.addEventListener('input', function() {
            const username = this.value.trim();

            if (username.length < 3) {
                usernameFeedback.textContent = 'Username must be at least 3 characters';
                usernameFeedback.style.color = 'red';
                usernameAvailable = false;
                return;
            }

            usernameFeedback.textContent = 'Checking availability...';
            usernameFeedback.style.color = 'gray';
            checkUsernameDebounced(username);
        });

        // Trigger validation on page load if username has a value
        if (usernameInput.value.trim().length >= 3) {
            usernameInput.dispatchEvent(new Event('input'));
        }
    }

    // Email validation
    if (emailInput && emailFeedback) {
        const checkEmailDebounced = debounce(function(email) {
            // Check email availability via API
            fetch(`/api/users/check-email?email=${encodeURIComponent(email)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Email check response:', data);
                    emailAvailable = data.available;
                    emailFeedback.textContent = data.message;
                    emailFeedback.style.color = data.available ? 'green' : 'red';
                })
                .catch(error => {
                    console.error('Error checking email:', error);
                    emailFeedback.textContent = 'Error checking availability';
                    emailFeedback.style.color = 'red';
                    emailAvailable = false;
                });
        }, 300);

        emailInput.addEventListener('input', function() {
            const email = this.value.trim();

            // Simple email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email) {
                emailFeedback.textContent = '';
                emailAvailable = false;
                return;
            }

            if (!emailRegex.test(email)) {
                emailFeedback.textContent = 'Please enter a valid email address';
                emailFeedback.style.color = 'red';
                emailAvailable = false;
                return;
            }

            emailFeedback.textContent = 'Checking availability...';
            emailFeedback.style.color = 'gray';
            checkEmailDebounced(email);
        });

        // Trigger validation on page load if email has a value
        if (emailInput.value.trim()) {
            emailInput.dispatchEvent(new Event('input'));
        }
    }

    // Password strength validation
    if (passwordInput && strengthBars.every(bar => bar) && strengthText) {
        console.log('Setting up password strength validation');
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            console.log('Password input changed, updating strength indicator');

            // Calculate password strength
            const strength = calculatePasswordStrength(password);
            console.log('Password strength:', strength);
            updatePasswordStrengthUI(strength, strengthBars, strengthText);

            // Update password match if confirm password has a value
            if (confirmPasswordInput && confirmPasswordInput.value) {
                checkPasswordMatch();
            }

            // Update password strength state
            passwordStrong = strength >= 2; // Medium or stronger
        });

        // Trigger the input event to initialize the strength indicator
        if (passwordInput.value) {
            console.log('Triggering initial password strength calculation');
            const event = new Event('input');
            passwordInput.dispatchEvent(event);
        }
    }

    // Password match validation
    if (confirmPasswordInput && passwordMatchFeedback) {
        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    }

    // Check if passwords match
    function checkPasswordMatch() {
        if (!passwordInput || !confirmPasswordInput || !passwordMatchFeedback) return;

        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (!confirmPassword) {
            passwordMatchFeedback.textContent = '';
            passwordsMatch = false;
            return;
        }

        if (password === confirmPassword) {
            passwordMatchFeedback.textContent = 'Passwords match';
            passwordMatchFeedback.style.color = 'green';
            passwordsMatch = true;
        } else {
            passwordMatchFeedback.textContent = 'Passwords do not match';
            passwordMatchFeedback.style.color = 'red';
            passwordsMatch = false;
        }
    }

    // Form submission validation
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            if (!usernameInput || !emailInput || !passwordInput || !confirmPasswordInput || !dateOfBirthInput) return;

            const username = usernameInput.value.trim();
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const dateOfBirth = dateOfBirthInput.value;
            let hasErrors = false;

            // Create date of birth feedback if it doesn't exist
            let dobFeedback = document.getElementById('dob-feedback');
            if (!dobFeedback) {
                dobFeedback = document.createElement('div');
                dobFeedback.id = 'dob-feedback';
                dobFeedback.className = 'mt-1 text-sm';
                dobFeedback.style.minHeight = '20px';
                dateOfBirthInput.parentNode.appendChild(dobFeedback);
            }

            // Basic validation
            if (!username || !email || !password || !confirmPassword || !dateOfBirth) {
                e.preventDefault();
                hasErrors = true;

                if (!username && usernameFeedback) {
                    usernameFeedback.textContent = 'Username is required';
                    usernameFeedback.style.color = 'red';
                }

                if (!email && emailFeedback) {
                    emailFeedback.textContent = 'Email is required';
                    emailFeedback.style.color = 'red';
                }

                if (!password && strengthText) {
                    strengthText.textContent = 'Password is required';
                    strengthText.style.color = 'red';
                }

                if (!confirmPassword && passwordMatchFeedback) {
                    passwordMatchFeedback.textContent = 'Please confirm your password';
                    passwordMatchFeedback.style.color = 'red';
                }

                if (!dateOfBirth && dobFeedback) {
                    dobFeedback.textContent = 'Date of birth is required';
                    dobFeedback.style.color = 'red';
                }
            }

            // Username validation
            if (username && username.length < 3 && usernameFeedback) {
                e.preventDefault();
                hasErrors = true;
                usernameFeedback.textContent = 'Username must be at least 3 characters long';
                usernameFeedback.style.color = 'red';
            }

            // Email validation
            if (email && emailFeedback) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                }
            }

            // Password validation
            if (password && password.length < 8 && strengthText) {
                e.preventDefault();
                hasErrors = true;
                strengthText.textContent = 'Password must be at least 8 characters long';
                strengthText.style.color = 'red';
            }

            // Password match validation
            if (password && confirmPassword && password !== confirmPassword && passwordMatchFeedback) {
                e.preventDefault();
                hasErrors = true;
                passwordMatchFeedback.textContent = 'Passwords do not match';
                passwordMatchFeedback.style.color = 'red';
            }

            // Date of birth validation
            if (dateOfBirth && dobFeedback) {
                // Validate DD-MMM-YYYY format
                const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4}$/;

                if (!dateRegex.test(dateOfBirth)) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'Please enter date in DD-MMM-YYYY format (e.g., 15-Jan-2000)';
                    dobFeedback.style.color = 'red';
                    return;
                }

                // Parse the date
                const parts = dateOfBirth.split('-');
                const day = parseInt(parts[0], 10);
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(parts[1]);
                const year = parseInt(parts[2], 10);

                if (month === -1) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'Invalid month abbreviation';
                    dobFeedback.style.color = 'red';
                    return;
                }

                const birthDate = new Date(year, month, day);
                const today = new Date();

                // Check if date is valid
                if (birthDate.getDate() !== day || birthDate.getMonth() !== month || birthDate.getFullYear() !== year) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'Invalid date';
                    dobFeedback.style.color = 'red';
                    return;
                }

                // Check if date is in the future
                if (birthDate > today) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'Date of birth cannot be in the future';
                    dobFeedback.style.color = 'red';
                    return;
                }

                // Calculate age
                const age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                if (age < 13) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'You must be at least 13 years old to register';
                    dobFeedback.style.color = 'red';
                }
            }

            // If no errors, allow the form to submit
            if (!hasErrors && registerButton) {
                // Disable button to prevent double submission
                registerButton.disabled = true;
                registerButton.textContent = 'Registering...';
            }
        });
    }

    // Initialize date picker for date of birth input
    if (dateOfBirthInput) {
        // Calculate max date (13 years ago from today)
        const today = new Date();
        const maxDate = new Date(today.getFullYear() - 13, today.getMonth(), today.getDate());

        // Check if flatpickr is available
        if (typeof flatpickr === 'function') {
            console.log('Initializing flatpickr date picker');
            flatpickr(dateOfBirthInput, {
                dateFormat: "d-M-Y", // DD-MMM-YYYY format
                maxDate: maxDate,     // Limit to 13 years ago
                disableMobile: true,  // Disable mobile-specific input
                allowInput: true,     // Allow manual input
                onClose: function(selectedDates, dateStr) {
                    // Validate the date when the picker closes
                    if (dateStr && dobFeedback) {
                        validateDateOfBirth(dateStr, dobFeedback);
                    }
                }
            });
        } else {
            console.warn('Flatpickr not available, using native date input');
            // Fallback to native date input
            const today = new Date().toISOString().split('T')[0];
            dateOfBirthInput.setAttribute('max', today);
        }
    }
}

// Initialize password reset form validation
function initPasswordResetValidation() {
    console.log('Initializing password reset validation');

    // Get form elements
    const resetForm = document.getElementById('resetPasswordForm');
    if (!resetForm) {
        console.log('Password reset form not found, skipping validation');
        return;
    }

    console.log('Password reset form found, setting up validation');

    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const resetButton = document.getElementById('resetButton');

    // Create feedback elements
    if (passwordInput) {
        // Create password feedback element if it doesn't exist
        let passwordFeedback = document.getElementById('password-feedback');
        if (!passwordFeedback) {
            passwordFeedback = document.createElement('div');
            passwordFeedback.id = 'password-feedback';
            passwordFeedback.className = 'mt-1 text-sm';
            passwordFeedback.style.minHeight = '20px';
            passwordInput.parentNode.appendChild(passwordFeedback);
        }

        // Password strength validation on input
        passwordInput.addEventListener('input', function() {
            const password = this.value;

            if (!password) {
                passwordFeedback.textContent = '';
                return;
            }

            if (password.length < 8) {
                passwordFeedback.textContent = 'Password must be at least 8 characters long';
                passwordFeedback.style.color = 'red';
            } else {
                passwordFeedback.textContent = 'Password length is good';
                passwordFeedback.style.color = 'green';
            }
        });
    }

    if (confirmPasswordInput && passwordInput) {
        // Create password match feedback element if it doesn't exist
        let passwordMatchFeedback = document.getElementById('password-match-feedback');
        if (!passwordMatchFeedback) {
            passwordMatchFeedback = document.createElement('div');
            passwordMatchFeedback.id = 'password-match-feedback';
            passwordMatchFeedback.className = 'mt-1 text-sm';
            passwordMatchFeedback.style.minHeight = '20px';
            confirmPasswordInput.parentNode.appendChild(passwordMatchFeedback);
        }

        // Password match validation on input
        confirmPasswordInput.addEventListener('input', function() {
            const password = passwordInput.value;
            const confirmPassword = this.value;

            if (!confirmPassword) {
                passwordMatchFeedback.textContent = '';
                return;
            }

            if (password === confirmPassword) {
                passwordMatchFeedback.textContent = 'Passwords match';
                passwordMatchFeedback.style.color = 'green';
            } else {
                passwordMatchFeedback.textContent = 'Passwords do not match';
                passwordMatchFeedback.style.color = 'red';
            }
        });
    }

    // Form submission validation
    resetForm.addEventListener('submit', function(e) {
        if (!passwordInput || !confirmPasswordInput) return;

        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        let hasErrors = false;

        // Get feedback elements
        const passwordFeedback = document.getElementById('password-feedback');
        const passwordMatchFeedback = document.getElementById('password-match-feedback');

        // Reset feedback
        if (passwordFeedback) passwordFeedback.textContent = '';
        if (passwordMatchFeedback) passwordMatchFeedback.textContent = '';

        // Password validation
        if (!password) {
            e.preventDefault();
            hasErrors = true;
            if (passwordFeedback) {
                passwordFeedback.textContent = 'Password is required';
                passwordFeedback.style.color = 'red';
            }
        } else if (password.length < 8) {
            e.preventDefault();
            hasErrors = true;
            if (passwordFeedback) {
                passwordFeedback.textContent = 'Password must be at least 8 characters long';
                passwordFeedback.style.color = 'red';
            }
        }

        // Password match validation
        if (!confirmPassword) {
            e.preventDefault();
            hasErrors = true;
            if (passwordMatchFeedback) {
                passwordMatchFeedback.textContent = 'Please confirm your password';
                passwordMatchFeedback.style.color = 'red';
            }
        } else if (password !== confirmPassword) {
            e.preventDefault();
            hasErrors = true;
            if (passwordMatchFeedback) {
                passwordMatchFeedback.textContent = 'Passwords do not match';
                passwordMatchFeedback.style.color = 'red';
            }
        }

        if (!hasErrors && resetButton) {
            // Disable button to prevent double submission
            resetButton.disabled = true;
            resetButton.textContent = 'Resetting...';
        }
    });
}

// Initialize forgot password form validation
function initForgotPasswordValidation() {
    console.log('Initializing forgot password validation');

    // Get form elements
    const forgotForm = document.getElementById('forgotPasswordForm');
    if (!forgotForm) {
        console.log('Forgot password form not found, skipping validation');
        return;
    }

    console.log('Forgot password form found, setting up validation');

    const usernameInput = document.getElementById('username');
    const dobInput = document.getElementById('date_of_birth');
    const recoverButton = document.getElementById('recoverButton');

    // Create feedback elements
    if (usernameInput) {
        // Create username feedback element if it doesn't exist
        let usernameFeedback = document.getElementById('username-feedback');
        if (!usernameFeedback) {
            usernameFeedback = document.createElement('div');
            usernameFeedback.id = 'username-feedback';
            usernameFeedback.className = 'mt-1 text-sm';
            usernameFeedback.style.minHeight = '20px';
            usernameInput.parentNode.appendChild(usernameFeedback);
        }
    }

    if (dobInput) {
        // Create date of birth feedback element if it doesn't exist
        let dobFeedback = document.getElementById('dob-feedback');
        if (!dobFeedback) {
            dobFeedback = document.createElement('div');
            dobFeedback.id = 'dob-feedback';
            dobFeedback.className = 'mt-1 text-sm';
            dobFeedback.style.minHeight = '20px';
            dobInput.parentNode.appendChild(dobFeedback);
        }
    }

    // Form submission validation
    forgotForm.addEventListener('submit', function(e) {
        if (!usernameInput || !dobInput) return;

        const username = usernameInput.value.trim();
        const dob = dobInput.value;
        let hasErrors = false;

        // Get feedback elements
        const usernameFeedback = document.getElementById('username-feedback');
        const dobFeedback = document.getElementById('dob-feedback');

        // Reset feedback
        if (usernameFeedback) usernameFeedback.textContent = '';
        if (dobFeedback) dobFeedback.textContent = '';

        // Validate username
        if (!username) {
            e.preventDefault();
            hasErrors = true;
            if (usernameFeedback) {
                usernameFeedback.textContent = 'Username is required';
                usernameFeedback.style.color = 'red';
            }
        }

        // Validate date of birth
        if (!dob) {
            e.preventDefault();
            hasErrors = true;
            if (dobFeedback) {
                dobFeedback.textContent = 'Date of birth is required';
                dobFeedback.style.color = 'red';
            }
        }

        if (!hasErrors && recoverButton) {
            // Disable button to prevent double submission
            recoverButton.disabled = true;
            recoverButton.textContent = 'Verifying...';
        }
    });
}
