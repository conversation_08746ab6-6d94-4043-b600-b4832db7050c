/**
 * Form Validator - Standardized form validation
 * 
 * This script provides a consistent way to handle form validation across the application.
 * It ensures validation messages are displayed consistently across all devices.
 */

const FormValidator = {
  /**
   * Initialize form validation for a specific form
   * @param {string} formId - The ID of the form to validate
   * @param {Object} rules - Validation rules for form fields
   * @param {Object} messages - Custom error messages
   * @param {Function} submitCallback - Function to call on successful validation
   */
  init: function(formId, rules, messages = {}, submitCallback = null) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    // Store validation rules
    form.validationRules = rules;
    form.validationMessages = messages;
    
    // Add submit event listener
    form.addEventListener('submit', function(event) {
      // Prevent default form submission
      event.preventDefault();
      
      // Validate the form
      const isValid = FormValidator.validateForm(form);
      
      // If valid and callback provided, call it
      if (isValid && typeof submitCallback === 'function') {
        submitCallback(form);
      } else if (isValid) {
        // Otherwise submit the form normally
        form.submit();
      }
    });
    
    // Add input event listeners for real-time validation
    Object.keys(rules).forEach(fieldName => {
      const field = form.querySelector(`[name="${fieldName}"]`);
      if (field) {
        field.addEventListener('blur', function() {
          FormValidator.validateField(form, field);
        });
        
        // For select fields, also validate on change
        if (field.tagName === 'SELECT') {
          field.addEventListener('change', function() {
            FormValidator.validateField(form, field);
          });
        }
      }
    });
  },
  
  /**
   * Validate an entire form
   * @param {HTMLFormElement} form - The form to validate
   * @returns {boolean} - Whether the form is valid
   */
  validateForm: function(form) {
    if (!form || !form.validationRules) return true;
    
    let isValid = true;
    const rules = form.validationRules;
    
    // Clear all previous error messages
    this.clearAllErrors(form);
    
    // Validate each field with rules
    Object.keys(rules).forEach(fieldName => {
      const field = form.querySelector(`[name="${fieldName}"]`);
      if (field) {
        const fieldIsValid = this.validateField(form, field);
        if (!fieldIsValid) {
          isValid = false;
        }
      }
    });
    
    return isValid;
  },
  
  /**
   * Validate a single field
   * @param {HTMLFormElement} form - The form containing the field
   * @param {HTMLElement} field - The field to validate
   * @returns {boolean} - Whether the field is valid
   */
  validateField: function(form, field) {
    if (!form || !form.validationRules || !field) return true;
    
    const fieldName = field.name;
    const rules = form.validationRules[fieldName];
    
    if (!rules) return true;
    
    // Clear previous errors for this field
    this.clearFieldError(field);
    
    // Get field value
    let value = field.value;
    
    // Check each rule
    for (const rule in rules) {
      let isValid = true;
      let errorMessage = '';
      
      switch (rule) {
        case 'required':
          if (rules.required && !value.trim()) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.required || 'This field is required';
          }
          break;
          
        case 'email':
          if (rules.email && value.trim() && !this._isValidEmail(value)) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.email || 'Please enter a valid email address';
          }
          break;
          
        case 'minLength':
          if (value.trim() && value.length < rules.minLength) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.minLength || 
              `Please enter at least ${rules.minLength} characters`;
          }
          break;
          
        case 'maxLength':
          if (value.length > rules.maxLength) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.maxLength || 
              `Please enter no more than ${rules.maxLength} characters`;
          }
          break;
          
        case 'pattern':
          if (value.trim() && !new RegExp(rules.pattern).test(value)) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.pattern || 'Please enter a valid value';
          }
          break;
          
        case 'match':
          const matchField = form.querySelector(`[name="${rules.match}"]`);
          if (matchField && value !== matchField.value) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.match || 'Fields do not match';
          }
          break;
          
        case 'min':
          if (value.trim() && parseFloat(value) < rules.min) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.min || 
              `Please enter a value greater than or equal to ${rules.min}`;
          }
          break;
          
        case 'max':
          if (value.trim() && parseFloat(value) > rules.max) {
            isValid = false;
            errorMessage = form.validationMessages[fieldName]?.max || 
              `Please enter a value less than or equal to ${rules.max}`;
          }
          break;
          
        case 'custom':
          if (typeof rules.custom === 'function') {
            const customResult = rules.custom(value, form);
            if (customResult !== true) {
              isValid = false;
              errorMessage = customResult || 'Invalid value';
            }
          }
          break;
      }
      
      // If any rule fails, show error and return false
      if (!isValid) {
        this.showError(field, errorMessage);
        return false;
      }
    }
    
    // If we get here, the field is valid
    this.showSuccess(field);
    return true;
  },
  
  /**
   * Show an error message for a field
   * @param {HTMLElement} field - The field with the error
   * @param {string} message - The error message to display
   */
  showError: function(field, message) {
    // Add error class to the field
    field.classList.add('border-red-500');
    field.classList.remove('border-green-500');
    
    // Find or create feedback element
    let feedbackElement = this._getFeedbackElement(field);
    
    // Set error message and styling
    feedbackElement.textContent = message;
    feedbackElement.classList.add('text-red-500');
    feedbackElement.classList.remove('text-green-500', 'text-gray-500');
  },
  
  /**
   * Show success state for a field
   * @param {HTMLElement} field - The valid field
   */
  showSuccess: function(field) {
    // Add success class to the field
    field.classList.add('border-green-500');
    field.classList.remove('border-red-500');
    
    // Find or create feedback element
    let feedbackElement = this._getFeedbackElement(field);
    
    // Clear message and add success styling
    feedbackElement.textContent = '';
    feedbackElement.classList.add('text-green-500');
    feedbackElement.classList.remove('text-red-500', 'text-gray-500');
  },
  
  /**
   * Clear error for a specific field
   * @param {HTMLElement} field - The field to clear errors for
   */
  clearFieldError: function(field) {
    // Remove validation classes
    field.classList.remove('border-red-500', 'border-green-500');
    
    // Find feedback element
    let feedbackElement = this._getFeedbackElement(field);
    
    // Reset feedback element
    if (feedbackElement) {
      feedbackElement.textContent = '';
      feedbackElement.classList.remove('text-red-500', 'text-green-500');
      feedbackElement.classList.add('text-gray-500');
    }
  },
  
  /**
   * Clear all errors in a form
   * @param {HTMLFormElement} form - The form to clear errors for
   */
  clearAllErrors: function(form) {
    // Remove validation classes from all inputs
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      this.clearFieldError(input);
    });
  },
  
  /**
   * Get or create feedback element for a field
   * @param {HTMLElement} field - The field to get feedback element for
   * @returns {HTMLElement} - The feedback element
   * @private
   */
  _getFeedbackElement: function(field) {
    // Try to find existing feedback element
    const fieldId = field.id || field.name;
    let feedbackElement = document.getElementById(`${fieldId}-feedback`);
    
    // If not found, create one
    if (!feedbackElement) {
      feedbackElement = document.createElement('div');
      feedbackElement.id = `${fieldId}-feedback`;
      feedbackElement.className = 'text-sm mt-1 min-h-[20px]';
      
      // Insert after the field or its parent container
      const fieldContainer = field.closest('.form-group') || field.parentNode;
      fieldContainer.appendChild(feedbackElement);
    }
    
    return feedbackElement;
  },
  
  /**
   * Check if an email is valid
   * @param {string} email - The email to validate
   * @returns {boolean} - Whether the email is valid
   * @private
   */
  _isValidEmail: function(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Look for forms with data-validate attribute
  const forms = document.querySelectorAll('form[data-validate]');
  
  forms.forEach(form => {
    // Try to parse validation rules from data attribute
    try {
      const rulesAttr = form.getAttribute('data-validation-rules');
      const messagesAttr = form.getAttribute('data-validation-messages');
      
      if (rulesAttr) {
        const rules = JSON.parse(rulesAttr);
        const messages = messagesAttr ? JSON.parse(messagesAttr) : {};
        
        // Initialize validation for this form
        FormValidator.init(form.id, rules, messages);
      }
    } catch (error) {
      console.error('Error initializing form validation:', error);
    }
  });
});
