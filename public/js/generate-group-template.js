/**
 * Generate a sample Excel file for group import
 * This script uses SheetJS (xlsx) library to create a downloadable Excel file
 */

function generateGroupImportTemplate() {
    // Check if the XLSX library is loaded
    if (typeof XLSX === 'undefined') {
        console.error('XLSX library not loaded. Please include xlsx.full.min.js');
        console.error('XLSX library not loaded. Please refresh the page and try again.');
        return;
    }

    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Create the instructions sheet (first sheet)
    const instructionsData = [
        {
            field: "Instructions",
            value: "This template is for importing groups into the system. Fill out the Groups sheet with your group information."
        },
        {
            field: "name",
            value: "Required. The name of the group. Must be unique."
        },
        {
            field: "description",
            value: "Optional. A description of the group."
        },
        {
            field: "members",
            value: "Optional. A comma-separated list of email addresses of users to add to the group."
        },
        {
            field: "Note",
            value: "Users must already exist in the system to be added to groups."
        }
    ];

    const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
    XLSX.utils.book_append_sheet(wb, instructionsSheet, "Instructions");

    // Create the Groups sheet (second sheet)
    const groupsData = [
        {
            name: "Class 10A",
            description: "Students of Class 10 Section A",
            members: "<EMAIL>,<EMAIL>,<EMAIL>"
        },
        {
            name: "Science Club",
            description: "Students interested in science activities",
            members: "<EMAIL>,<EMAIL>,<EMAIL>"
        },
        {
            name: "Math Olympiad Team",
            description: "Students participating in Math Olympiad",
            members: "<EMAIL>,<EMAIL>,<EMAIL>"
        }
    ];

    const groupsSheet = XLSX.utils.json_to_sheet(groupsData);
    XLSX.utils.book_append_sheet(wb, groupsSheet, "Groups");

    // Generate the Excel file
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    // Create a Blob from the buffer
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Create a download link and trigger the download
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'group_import_template.xlsx';
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 0);

    return true;
}
