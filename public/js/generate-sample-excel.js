/**
 * Generate a sample Excel file for question import
 * This script uses SheetJS (xlsx) library to create a downloadable Excel file
 */

function generateSampleExcel() {
    // Check if the XLSX library is loaded
    if (typeof XLSX === 'undefined') {
        console.error('XLSX library not loaded. Please include xlsx.full.min.js');
        console.error('XLSX library not loaded. Please refresh the page and try again.');
        return;
    }

    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Create the instructions sheet (first sheet)
    const instructionsData = [
        { title: "test introduction", introduction: "This is a sample test with multiple sections. Follow the instructions carefully. You have 60 minutes to complete all sections." },
        { title: "Mathematics", introduction: "This section contains basic mathematics questions. Each question carries 2 marks." },
        { title: "General Knowledge", introduction: "This section tests your general knowledge. Each question carries 1 mark." }
    ];

    const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
    XLSX.utils.book_append_sheet(wb, instructionsSheet, "Instructions");

    // Create the Mathematics section (second sheet)
    const mathQuestions = [
        {
            question_text: "What is the value of π (pi) to two decimal places?",
            question_type: "multiple_choice",
            options: "3.14,3.16,3.12,3.18",
            correct_answer: "0",
            solution_text: "The value of π is approximately 3.14159, which rounds to 3.14 to two decimal places.",
            marks: 2
        },
        {
            question_text: "Solve for x: 2x + 5 = 15",
            question_type: "multiple_choice",
            options: "4,5,6,7",
            correct_answer: "1",
            solution_text: "2x + 5 = 15\n2x = 10\nx = 5",
            marks: 2
        },
        {
            question_text: "What is the square root of 144?",
            question_type: "multiple_choice",
            options: "10,12,14,16",
            correct_answer: "1",
            solution_text: "√144 = 12, because 12² = 144",
            marks: 2
        },
        {
            question_text: "If a triangle has angles of 30°, 60°, and 90°, what type of triangle is it?",
            question_type: "multiple_choice",
            options: "Equilateral,Isosceles,Scalene,Right-angled",
            correct_answer: "3",
            solution_text: "A triangle with angles 30°, 60°, and 90° is a right-angled triangle because it has one angle of 90°. It is also scalene because all sides have different lengths.",
            marks: 2
        },
        {
            question_text: "What is the area of a circle with radius 5 cm?",
            question_type: "multiple_choice",
            options: "25π cm²,10π cm²,15π cm²,20π cm²",
            correct_answer: "0",
            solution_text: "Area of a circle = πr². With r = 5, Area = π × 5² = 25π cm²",
            marks: 2
        },
        {
            question_text: "The value of log₁₀(100) is:",
            question_type: "fill_in_the_blank",
            correct_answer: "2",
            solution_text: "log₁₀(100) = log₁₀(10²) = 2",
            marks: 2
        },
        {
            question_text: "Simplify: (3x² + 2x - 1) - (2x² - 3x + 4)",
            question_type: "fill_in_the_blank",
            correct_answer: "x² + 5x - 5",
            solution_text: "(3x² + 2x - 1) - (2x² - 3x + 4) = 3x² + 2x - 1 - 2x² + 3x - 4 = x² + 5x - 5",
            marks: 2
        }
    ];

    const mathSheet = XLSX.utils.json_to_sheet(mathQuestions);
    XLSX.utils.book_append_sheet(wb, mathSheet, "Mathematics");

    // Create the General Knowledge section (third sheet)
    const gkQuestions = [
        {
            question_text: "Which planet is known as the Red Planet?",
            question_type: "multiple_choice",
            options: "Venus,Mars,Jupiter,Saturn",
            correct_answer: "1",
            solution_text: "Mars is known as the Red Planet due to its reddish appearance, which is caused by iron oxide (rust) on its surface.",
            marks: 1
        },
        {
            question_text: "Who wrote 'Romeo and Juliet'?",
            question_type: "multiple_choice",
            options: "Charles Dickens,William Shakespeare,Jane Austen,Mark Twain",
            correct_answer: "1",
            solution_text: "Romeo and Juliet was written by William Shakespeare, believed to have been written between 1591 and 1595.",
            marks: 1
        },
        {
            question_text: "What is the capital of Japan?",
            question_type: "multiple_choice",
            options: "Beijing,Seoul,Tokyo,Bangkok",
            correct_answer: "2",
            solution_text: "Tokyo is the capital and largest city of Japan.",
            marks: 1
        },
        {
            question_text: "Which element has the chemical symbol 'O'?",
            question_type: "multiple_choice",
            options: "Gold,Oxygen,Osmium,Oganesson",
            correct_answer: "1",
            solution_text: "Oxygen has the chemical symbol 'O' on the periodic table.",
            marks: 1
        },
        {
            question_text: "The Great Barrier Reef is located in which country?",
            question_type: "multiple_choice",
            options: "Australia,Brazil,India,Mexico",
            correct_answer: "0",
            solution_text: "The Great Barrier Reef is located off the coast of Queensland, Australia.",
            marks: 1
        },
        {
            question_text: "Water boils at 100 degrees Celsius at sea level.",
            question_type: "true_false",
            correct_answer: "true",
            solution_text: "Water boils at 100°C (212°F) at standard atmospheric pressure (at sea level).",
            marks: 1
        },
        {
            question_text: "The moon is larger than the Earth.",
            question_type: "true_false",
            correct_answer: "false",
            solution_text: "The moon is much smaller than Earth. The moon's diameter is about 3,474 km, while Earth's diameter is about 12,742 km.",
            marks: 1
        },
        {
            question_text: "Explain the process of photosynthesis and its importance for life on Earth.",
            question_type: "essay",
            solution_text: "Photosynthesis is the process by which green plants, algae, and some bacteria convert light energy, usually from the sun, into chemical energy in the form of glucose or other sugars. This process involves capturing carbon dioxide from the air and water from the soil, and releasing oxygen as a byproduct. The importance of photosynthesis for life on Earth cannot be overstated as it produces oxygen necessary for respiration, provides energy-rich organic compounds for all living organisms, and removes carbon dioxide from the atmosphere.",
            marks: 3
        }
    ];

    const gkSheet = XLSX.utils.json_to_sheet(gkQuestions);
    XLSX.utils.book_append_sheet(wb, gkSheet, "General Knowledge");

    // Generate the Excel file
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    // Create a Blob from the buffer
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Create a download link and trigger the download
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample_questions_import.xlsx';
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 0);

    return true;
}
