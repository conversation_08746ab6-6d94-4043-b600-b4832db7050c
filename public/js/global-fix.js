/**
 * Global Fix JavaScript
 * Contains fixes and utilities that need to be available globally
 */

// Global error handler
window.addEventListener('error', function(event) {
    console.error('Global error caught:', event.error);
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});

// Utility functions
window.GlobalFix = {
    // Safe DOM ready function
    ready: function(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    },

    // Safe element selector
    safeSelect: function(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.warn('Invalid selector:', selector, error);
            return null;
        }
    },

    // Safe element creation
    createElement: function(tag, attributes = {}, content = '') {
        try {
            const element = document.createElement(tag);
            
            Object.keys(attributes).forEach(key => {
                if (key === 'className') {
                    element.className = attributes[key];
                } else if (key === 'innerHTML') {
                    element.innerHTML = attributes[key];
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            });
            
            if (content) {
                element.textContent = content;
            }
            
            return element;
        } catch (error) {
            console.error('Error creating element:', error);
            return null;
        }
    },

    // Safe event listener
    addListener: function(element, event, callback) {
        if (element && typeof callback === 'function') {
            try {
                element.addEventListener(event, callback);
                return true;
            } catch (error) {
                console.error('Error adding event listener:', error);
                return false;
            }
        }
        return false;
    },

    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Initialize global fixes when DOM is ready
GlobalFix.ready(function() {
    console.log('Global fixes initialized');
    
    // Fix for common issues
    try {
        // Ensure all forms have proper encoding
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (!form.getAttribute('enctype') && form.querySelector('input[type="file"]')) {
                form.setAttribute('enctype', 'multipart/form-data');
            }
        });
        
        // Add loading states to buttons with forms
        const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
        submitButtons.forEach(button => {
            GlobalFix.addListener(button, 'click', function() {
                const form = button.closest('form');
                if (form && form.checkValidity()) {
                    setTimeout(() => {
                        if (button.tagName === 'BUTTON') {
                            button.disabled = true;
                            const originalText = button.innerHTML;
                            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                            
                            // Re-enable after 10 seconds as fallback
                            setTimeout(() => {
                                button.disabled = false;
                                button.innerHTML = originalText;
                            }, 10000);
                        }
                    }, 100);
                }
            });
        });
        
    } catch (error) {
        console.error('Error in global fixes:', error);
    }
});

console.log('Global fix script loaded');
