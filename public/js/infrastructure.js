/**
 * Infrastructure Page JavaScript
 */

console.log('🚀 Infrastructure script starting...');
console.log('🚀 jQuery available:', typeof $);



$(document).ready(function() {
    console.log('🎯 Infrastructure Command Center initialized with jQuery');
    console.log('🎯 Document ready fired');

    // Function to fetch classroom data from API
    async function fetchClassroomData(roomId) {
        try {
            console.log('🔍 Fetching classroom data for room:', roomId);
            console.log('🔍 API URL:', '/principal/api/classroom/' + roomId);

            const response = await fetch('/principal/api/classroom/' + roomId, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            console.log('🔍 Response status:', response.status);
            console.log('🔍 Response ok:', response.ok);

            if (!response.ok) {
                console.error('❌ HTTP error:', response.status, response.statusText);
                const errorText = await response.text();
                console.error('❌ Error response:', errorText);
                return null;
            }

            const data = await response.json();
            console.log('🔍 Raw API response:', data);

            if (data.success) {
                console.log('✅ Classroom data fetched successfully:', data.data);
                return data.data;
            } else {
                console.error('❌ API returned error:', data.error);
                return null;
            }
        } catch (error) {
            console.error('❌ Error fetching classroom data:', error);
            console.error('❌ Error stack:', error.stack);
            return null;
        }
    }

    // Function to generate classroom details HTML
    function generateClassroomDetailsHTML(classroomData) {
        const { classroom, students, equipment } = classroomData;

        console.log('🔍 Generating HTML for classroom:', classroom);
        console.log('🔍 Students data:', students);
        console.log('🔍 Equipment data:', equipment);

        // Detect screen size and adjust layout accordingly
        const isMobile = window.innerWidth <= 767;
        const isTablet = window.innerWidth > 767 && window.innerWidth <= 1023;
        const isDesktop = window.innerWidth > 1023;

        // Responsive HTML based on screen size - More compact design without duplicate header
        let html = `
        <div style="padding: ${isMobile ? '12px' : '16px'}; background-color: white; color: black; font-family: Arial, sans-serif; min-height: 100%; width: 100%; box-sizing: border-box;">
            <!-- Quick Info Bar -->
            <div style="background: linear-gradient(135deg, #f8fafc, #e2e8f0); padding: ${isMobile ? '12px' : '16px'}; border-radius: 8px; margin-bottom: ${isMobile ? '16px' : '20px'}; border-left: 4px solid #2563eb;">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                    <div style="flex: 1;">
                        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                            <div style="background: #2563eb; color: white; padding: ${isMobile ? '8px 12px' : '10px 16px'}; border-radius: 6px; font-size: ${isMobile ? '12px' : '14px'}; font-weight: 600;">
                                Floor ${classroom.floor || 'N/A'}
                            </div>
                            <div style="background: #059669; color: white; padding: ${isMobile ? '8px 12px' : '10px 16px'}; border-radius: 6px; font-size: ${isMobile ? '12px' : '14px'}; font-weight: 600;">
                                Cap: 20👦 + 30👧 = 50
                            </div>
                            <div style="background: ${students.total > 0 ? '#dc2626' : '#6b7280'}; color: white; padding: ${isMobile ? '8px 12px' : '10px 16px'}; border-radius: 6px; font-size: ${isMobile ? '12px' : '14px'}; font-weight: 600;">
                                ${students.total}/50 Occupied ${students.total > 0 ? `(${Math.round((students.total / 50) * 100)}%)` : ''}
                            </div>
                        </div>
                        ${classroom.incharge_name ? `<p style="margin: 8px 0 0 0; color: #6b7280; font-size: ${isMobile ? '13px' : '14px'};">👨‍🏫 Class Teacher: ${classroom.incharge_name}</p>` : ''}
                    </div>
                </div>
            </div>

            <!-- Main Content Grid - Compact Layout -->
            <div style="display: grid; grid-template-columns: ${isMobile ? '1fr' : '1fr 1fr'}; gap: ${isMobile ? '12px' : '16px'}; margin-bottom: ${isMobile ? '12px' : '16px'};">

                <!-- Left Column: Student Information -->
                <div style="background-color: #f8fafc; border-radius: 8px; overflow: hidden; border: 1px solid #e2e8f0;">
                    <div style="background: linear-gradient(135deg, #059669, #047857); color: white; padding: ${isMobile ? '14px' : '16px'};">
                        <h3 style="margin: 0; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; display: flex; align-items: center;">
                            <span style="margin-right: 6px;">👥</span> Student Information
                        </h3>
                    </div>
                    <div style="padding: ${isMobile ? '16px' : '20px'};">
                        <div style="display: grid; grid-template-columns: repeat(${isMobile ? '1' : '3'}, 1fr); gap: ${isMobile ? '12px' : '14px'}; margin-bottom: ${isMobile ? '16px' : '18px'};">
                            <div style="text-align: center; padding: ${isMobile ? '14px' : '16px'}; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border-radius: 8px; border: 1px solid #93c5fd;">
                                <div style="font-size: ${isMobile ? '24px' : '28px'}; margin-bottom: 6px;">👥</div>
                                <div style="font-size: ${isMobile ? '20px' : '24px'}; font-weight: bold; color: #1e40af;">${students.total}</div>
                                <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #3b82f6; font-weight: 500;">Total</div>
                            </div>
                            <div style="text-align: center; padding: ${isMobile ? '14px' : '16px'}; background: linear-gradient(135deg, #dcfce7, #bbf7d0); border-radius: 8px; border: 1px solid #86efac;">
                                <div style="font-size: ${isMobile ? '24px' : '28px'}; margin-bottom: 6px;">👦</div>
                                <div style="font-size: ${isMobile ? '20px' : '24px'}; font-weight: bold; color: #16a34a;">${students.boys}</div>
                                <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #22c55e; font-weight: 500;">Boys</div>
                            </div>
                            <div style="text-align: center; padding: ${isMobile ? '14px' : '16px'}; background: linear-gradient(135deg, #fdf2f8, #fce7f3); border-radius: 8px; border: 1px solid #f9a8d4;">
                                <div style="font-size: ${isMobile ? '24px' : '28px'}; margin-bottom: 6px;">👧</div>
                                <div style="font-size: ${isMobile ? '20px' : '24px'}; font-weight: bold; color: #db2777;">${students.girls}</div>
                                <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #ec4899; font-weight: 500;">Girls</div>
                            </div>
                        </div>
                        ${students.total > 0 ? `
                        <div style="background: white; padding: 16px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <h4 style="margin: 0 0 12px 0; color: #374151; font-size: 14px; font-weight: 600;">Class Distribution</h4>
                            <div style="display: flex; justify-content: space-between; font-size: 13px; color: #6b7280;">
                                <span>Attendance Rate: <strong style="color: #059669;">95%</strong></span>
                                <span>Active: <strong style="color: #2563eb;">${students.total}</strong></span>
                            </div>
                        </div>
                        ` : `
                        <div style="text-align: center; padding: 20px; background: white; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <div style="font-size: 48px; margin-bottom: 12px; opacity: 0.5;">📚</div>
                            <h4 style="color: #6b7280; margin: 0; font-size: 16px;">No Students Enrolled</h4>
                            <p style="color: #9ca3af; margin: 8px 0 0 0; font-size: 14px;">This classroom is currently unassigned</p>
                        </div>
                        `}
                    </div>
                </div>

                <!-- Right Column: Equipment Overview -->
                <div style="background-color: #f8fafc; border-radius: 8px; overflow: hidden; border: 1px solid #e2e8f0;">
                    <div style="background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: ${isMobile ? '14px' : '16px'};">
                        <h3 style="margin: 0; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; display: flex; align-items: center;">
                            <span style="margin-right: 6px;">🔧</span> Equipment Overview
                        </h3>
                    </div>
                    <div style="padding: ${isMobile ? '16px' : '20px'};">
                        <div style="display: grid; grid-template-columns: ${isMobile ? '1fr' : '1fr 1fr'}; gap: ${isMobile ? '12px' : '14px'}; margin-bottom: ${isMobile ? '16px' : '18px'};">
                            <div style="padding: ${isMobile ? '14px' : '16px'}; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border-radius: 8px; border-left: 4px solid #2563eb;">
                                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                    <span style="font-size: ${isMobile ? '18px' : '20px'}; margin-right: 6px;">💻</span>
                                    <span style="font-weight: bold; color: #1e40af; font-size: ${isMobile ? '14px' : '15px'};">IT Equipment</span>
                                </div>
                                <div style="font-size: ${isMobile ? '20px' : '24px'}; font-weight: bold; color: #1d4ed8;">${equipment.it.length}</div>
                                <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #3b82f6;">Devices</div>
                            </div>
                            <div style="padding: ${isMobile ? '14px' : '16px'}; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 8px; border-left: 4px solid #f59e0b;">
                                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                    <span style="font-size: ${isMobile ? '18px' : '20px'}; margin-right: 6px;">⚡</span>
                                    <span style="font-weight: bold; color: #d97706; font-size: ${isMobile ? '14px' : '15px'};">Electrical</span>
                                </div>
                                <div style="font-size: ${isMobile ? '20px' : '24px'}; font-weight: bold; color: #b45309;">${equipment.electrical.length}</div>
                                <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #92400e;">Fixtures</div>
                            </div>
                        </div>

                        <!-- Equipment Status Summary -->
                        <div style="background: white; padding: ${isMobile ? '14px' : '16px'}; border-radius: 6px; border: 1px solid #e2e8f0;">
                            <h4 style="margin: 0 0 ${isMobile ? '12px' : '14px'} 0; color: #374151; font-size: ${isMobile ? '14px' : '15px'}; font-weight: 600;">Equipment Status</h4>
                            <div style="display: grid; grid-template-columns: repeat(${isMobile ? '1' : '3'}, 1fr); gap: ${isMobile ? '8px' : '10px'}; font-size: ${isMobile ? '12px' : '13px'};">
                                <div style="text-align: center; padding: ${isMobile ? '10px' : '12px'}; background: #dcfce7; border-radius: 6px;">
                                    <div style="color: #16a34a; font-weight: bold; font-size: ${isMobile ? '11px' : '12px'};">Working</div>
                                    <div style="color: #15803d; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; margin-top: 4px;">${equipment.it.filter(item => item.status === 'working').length + equipment.electrical.filter(item => item.status === 'working').length}</div>
                                </div>
                                <div style="text-align: center; padding: ${isMobile ? '10px' : '12px'}; background: #fef3c7; border-radius: 6px;">
                                    <div style="color: #d97706; font-weight: bold; font-size: ${isMobile ? '11px' : '12px'};">Maintenance</div>
                                    <div style="color: #b45309; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; margin-top: 4px;">${equipment.it.filter(item => item.status === 'maintenance').length + equipment.electrical.filter(item => item.status === 'maintenance').length}</div>
                                </div>
                                <div style="text-align: center; padding: ${isMobile ? '10px' : '12px'}; background: #fecaca; border-radius: 6px;">
                                    <div style="color: #dc2626; font-weight: bold; font-size: ${isMobile ? '11px' : '12px'};">Faulty</div>
                                    <div style="color: #b91c1c; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; margin-top: 4px;">${equipment.it.filter(item => item.status === 'faulty').length + equipment.electrical.filter(item => item.status === 'faulty').length}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Equipment Breakdown -->
            <div style="display: grid; grid-template-columns: ${isMobile ? '1fr' : '1fr 1fr'}; gap: ${isMobile ? '12px' : '16px'};">

                <!-- IT Equipment Details -->
                <div style="background-color: #f8fafc; border-radius: 8px; overflow: hidden; border: 1px solid #e2e8f0;">
                    <div style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: ${isMobile ? '14px' : '16px'};">
                        <h3 style="margin: 0; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; display: flex; align-items: center;">
                            <span style="margin-right: 6px;">💻</span> IT Equipment Details
                        </h3>
                    </div>
                    <div style="padding: ${isMobile ? '16px' : '20px'}; max-height: ${isMobile ? '400px' : '500px'}; overflow-y: auto;">
                        ${equipment.it.length > 0 ? equipment.it.map((item, index) => {
                            const statusColor = item.status === 'available' ? '#16a34a' : item.status === 'in_repair' ? '#dc2626' : item.status === 'assigned' ? '#2563eb' : '#d97706';
                            const statusBg = item.status === 'available' ? '#dcfce7' : item.status === 'in_repair' ? '#fecaca' : item.status === 'assigned' ? '#dbeafe' : '#fef3c7';

                            // Get equipment type icon - Comprehensive mapping
                            const typeIcons = {
                                'projector': '📽️',
                                'laptop': '💻',
                                'desktop': '🖥️',
                                'computer': '🖥️',
                                'pc': '🖥️',
                                'printer': '🖨️',
                                'network': '🌐',
                                'router': '📡',
                                'switch': '🔀',
                                'tablet': '📱',
                                'ipad': '📱',
                                'monitor': '🖥️',
                                'display': '📺',
                                'tv': '📺',
                                'television': '📺',
                                'camera': '📹',
                                'webcam': '📷',
                                'speaker': '🔊',
                                'soundbar': '🔊',
                                'microphone': '🎤',
                                'mic': '🎤',
                                'keyboard': '⌨️',
                                'mouse': '🖱️',
                                'ups': '🔋',
                                'battery': '🔋',
                                'cable': '🔌',
                                'wire': '🔗',
                                'adapter': '🔌',
                                'charger': '🔌',
                                'headphone': '🎧',
                                'headset': '🎧',
                                'scanner': '📠',
                                'fax': '📠',
                                'phone': '📞',
                                'telephone': '☎️',
                                'mobile': '📱',
                                'smartphone': '📱',
                                'server': '🖥️',
                                'hard_drive': '💾',
                                'hdd': '💾',
                                'ssd': '💿',
                                'cd': '💿',
                                'dvd': '📀',
                                'usb': '💾',
                                'pendrive': '💾',
                                'other': '⚙️'
                            };

                            return `
                            <div style="background: white; padding: ${isMobile ? '12px' : '16px'}; border-radius: 8px; margin-bottom: ${isMobile ? '12px' : '14px'}; border: 1px solid #e2e8f0; border-left: 3px solid ${statusColor}; box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: ${isMobile ? '12px' : '14px'};">
                                    <div style="flex: 1;">
                                        <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                            <span style="font-size: ${isMobile ? '18px' : '20px'}; margin-right: ${isMobile ? '8px' : '10px'};">${typeIcons[item.item_type?.toLowerCase()] || typeIcons[item.item_name?.toLowerCase()?.split(' ')[0]] || '⚙️'}</span>
                                            <div>
                                                <h4 style="margin: 0; font-size: ${isMobile ? '14px' : '16px'}; font-weight: 600; color: #374151;">${item.item_name}</h4>
                                                <p style="margin: 2px 0 0 0; font-size: ${isMobile ? '12px' : '13px'}; color: #6b7280; text-transform: capitalize;">${item.item_type || 'Equipment'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="background: ${statusBg}; color: ${statusColor}; padding: ${isMobile ? '6px 10px' : '6px 12px'}; border-radius: 8px; font-size: ${isMobile ? '10px' : '11px'}; font-weight: 600; text-transform: uppercase; white-space: nowrap;">
                                        ${item.status || 'Unknown'}
                                    </div>
                                </div>

                                <!-- Equipment Details Grid -->
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(${isMobile ? '140px' : '160px'}, 1fr)); gap: ${isMobile ? '8px' : '10px'}; margin-bottom: ${isMobile ? '12px' : '14px'};">
                                    <div style="background: #f8fafc; padding: ${isMobile ? '8px' : '10px'}; border-radius: 6px;">
                                        <div style="font-size: ${isMobile ? '10px' : '11px'}; color: #6b7280; font-weight: 500; margin-bottom: 3px;">SERIAL</div>
                                        <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #374151; font-weight: 600; font-family: monospace;">${item.serial_number || 'N/A'}</div>
                                    </div>
                                    ${item.manufacturer ? `
                                    <div style="background: #f8fafc; padding: ${isMobile ? '8px' : '10px'}; border-radius: 6px;">
                                        <div style="font-size: ${isMobile ? '10px' : '11px'}; color: #6b7280; font-weight: 500; margin-bottom: 3px;">BRAND</div>
                                        <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #374151; font-weight: 600;">${item.manufacturer}</div>
                                    </div>
                                    ` : ''}
                                    ${item.model ? `
                                    <div style="background: #f8fafc; padding: ${isMobile ? '8px' : '10px'}; border-radius: 6px;">
                                        <div style="font-size: ${isMobile ? '10px' : '11px'}; color: #6b7280; font-weight: 500; margin-bottom: 3px;">MODEL</div>
                                        <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #374151; font-weight: 600;">${item.model}</div>
                                    </div>
                                    ` : ''}
                                    ${item.condition_status ? `
                                    <div style="background: #f8fafc; padding: ${isMobile ? '8px' : '10px'}; border-radius: 6px;">
                                        <div style="font-size: ${isMobile ? '10px' : '11px'}; color: #6b7280; font-weight: 500; margin-bottom: 3px;">CONDITION</div>
                                        <div style="font-size: ${isMobile ? '12px' : '13px'}; color: #374151; font-weight: 600; text-transform: capitalize;">${item.condition_status}</div>
                                    </div>
                                    ` : ''}
                                </div>

                                <!-- Additional Technical Details -->
                                ${item.mac_address || item.ip_address || item.hostname ? `
                                <div style="background: #f1f5f9; padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                                    <div style="font-size: 12px; color: #475569; font-weight: 600; margin-bottom: 8px;">NETWORK DETAILS</div>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px; font-size: 13px;">
                                        ${item.mac_address ? `<div><span style="color: #64748b;">MAC:</span> <span style="font-family: monospace; color: #334155;">${item.mac_address}</span></div>` : ''}
                                        ${item.ip_address ? `<div><span style="color: #64748b;">IP:</span> <span style="font-family: monospace; color: #334155;">${item.ip_address}</span></div>` : ''}
                                        ${item.hostname ? `<div><span style="color: #64748b;">Host:</span> <span style="font-family: monospace; color: #334155;">${item.hostname}</span></div>` : ''}
                                    </div>
                                </div>
                                ` : ''}

                                <!-- Warranty and Purchase Info -->
                                ${item.purchase_date || item.warranty_expiry ? `
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                                    ${item.purchase_date ? `
                                    <div style="background: #ecfdf5; padding: 12px; border-radius: 8px; border-left: 3px solid #10b981;">
                                        <div style="font-size: 12px; color: #047857; font-weight: 500; margin-bottom: 4px;">PURCHASE DATE</div>
                                        <div style="font-size: 14px; color: #065f46; font-weight: 600;">${new Date(item.purchase_date).toLocaleDateString('en-IN')}</div>
                                    </div>
                                    ` : ''}
                                    ${item.warranty_expiry ? `
                                    <div style="background: ${new Date(item.warranty_expiry) > new Date() ? '#fef3c7' : '#fecaca'}; padding: 12px; border-radius: 8px; border-left: 3px solid ${new Date(item.warranty_expiry) > new Date() ? '#f59e0b' : '#ef4444'};">
                                        <div style="font-size: 12px; color: ${new Date(item.warranty_expiry) > new Date() ? '#92400e' : '#991b1b'}; font-weight: 500; margin-bottom: 4px;">WARRANTY ${new Date(item.warranty_expiry) > new Date() ? 'EXPIRES' : 'EXPIRED'}</div>
                                        <div style="font-size: 14px; color: ${new Date(item.warranty_expiry) > new Date() ? '#78350f' : '#7f1d1d'}; font-weight: 600;">${new Date(item.warranty_expiry).toLocaleDateString('en-IN')}</div>
                                    </div>
                                    ` : ''}
                                </div>
                                ` : ''}

                                ${item.notes ? `
                                <div style="background: #fffbeb; padding: 12px; border-radius: 8px; border-left: 3px solid #f59e0b;">
                                    <div style="font-size: 12px; color: #92400e; font-weight: 500; margin-bottom: 4px;">NOTES</div>
                                    <p style="margin: 0; font-size: 13px; color: #78350f; line-height: 1.4;">${item.notes}</p>
                                </div>
                                ` : ''}
                            </div>
                            `;
                        }).join('') : `
                        <div style="text-align: center; padding: 40px; color: #6b7280;">
                            <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;">💻</div>
                            <h4 style="margin: 0; font-size: 16px;">No IT Equipment</h4>
                            <p style="margin: 8px 0 0 0; font-size: 14px;">No IT devices assigned to this classroom</p>
                        </div>
                        `}
                    </div>
                </div>

                <!-- Electrical Equipment Details -->
                <div style="background-color: #f8fafc; border-radius: 8px; overflow: hidden; border: 1px solid #e2e8f0;">
                    <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; padding: ${isMobile ? '14px' : '16px'};">
                        <h3 style="margin: 0; font-size: ${isMobile ? '16px' : '18px'}; font-weight: bold; display: flex; align-items: center;">
                            <span style="margin-right: 6px;">⚡</span> Electrical Equipment Details
                        </h3>
                    </div>
                    <div style="padding: ${isMobile ? '16px' : '20px'}; max-height: ${isMobile ? '400px' : '500px'}; overflow-y: auto;">
                        ${equipment.electrical.length > 0 ? (() => {
                            // Group electrical equipment by type
                            const grouped = equipment.electrical.reduce((acc, item) => {
                                const type = item.item_type || 'other';
                                if (!acc[type]) acc[type] = [];
                                acc[type].push(item);
                                return acc;
                            }, {});

                            const typeIcons = {
                                'tubelight': '💡',
                                'tube_light': '💡',
                                'light': '💡',
                                'bulb': '💡',
                                'led': '💡',
                                'fan': '🌀',
                                'ceiling_fan': '🌀',
                                'exhaust_fan': '🌪️',
                                'outlet': '🔌',
                                'socket': '🔌',
                                'plug': '🔌',
                                'switch': '🔘',
                                'electrical_switch': '🔘',
                                'wire': '🔗',
                                'cable': '🔗',
                                'wiring': '🔗',
                                'ups': '🔋',
                                'battery': '🔋',
                                'power_backup': '🔋',
                                'soundbar': '🔊',
                                'speaker': '🔊',
                                'audio': '🔊',
                                'amplifier': '📢',
                                'microphone': '🎤',
                                'mic': '🎤',
                                'projector': '📽️',
                                'display': '📺',
                                'monitor': '🖥️',
                                'tv': '📺',
                                'television': '📺',
                                'camera': '📹',
                                'cctv': '📹',
                                'security_camera': '📹',
                                'webcam': '📷',
                                'stabilizer': '⚡',
                                'voltage_stabilizer': '⚡',
                                'transformer': '⚡',
                                'inverter': '🔋',
                                'generator': '⚡',
                                'extension': '🔌',
                                'extension_cord': '🔌',
                                'adapter': '🔌',
                                'charger': '🔌',
                                'other': '⚡'
                            };

                            return Object.entries(grouped).map(([type, items]) => `
                            <div style="margin-bottom: 32px;">
                                <h4 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #374151; display: flex; align-items: center; padding-bottom: 8px; border-bottom: 2px solid #e2e8f0;">
                                    <span style="margin-right: 12px; font-size: 24px;">${typeIcons[type?.toLowerCase()] || '⚡'}</span>
                                    ${type.charAt(0).toUpperCase() + type.slice(1)}s (${items.length} units)
                                </h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px;">
                                    ${items.map((item, index) => {
                                        const statusColor = item.status === 'working' ? '#16a34a' : item.status === 'faulty' ? '#dc2626' : '#d97706';
                                        const statusBg = item.status === 'working' ? '#dcfce7' : item.status === 'faulty' ? '#fecaca' : '#fef3c7';
                                        return `
                                        <div style="background: white; padding: 20px; border-radius: 12px; border: 1px solid #e2e8f0; border-left: 4px solid ${statusColor}; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 16px;">
                                                <div style="flex: 1;">
                                                    <h5 style="margin: 0; font-size: 16px; font-weight: 600; color: #374151;">${item.item_name || type.charAt(0).toUpperCase() + type.slice(1)} #${index + 1}</h5>
                                                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #6b7280; text-transform: capitalize;">${type} Equipment</p>
                                                </div>
                                                <div style="background: ${statusBg}; color: ${statusColor}; padding: 6px 12px; border-radius: 8px; font-size: 11px; font-weight: 600; text-transform: uppercase;">
                                                    ${item.status || 'Unknown'}
                                                </div>
                                            </div>

                                            <!-- Equipment Details -->
                                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                                                <div style="background: #f8fafc; padding: 10px; border-radius: 6px;">
                                                    <div style="font-size: 11px; color: #6b7280; font-weight: 500; margin-bottom: 4px;">SERIAL NUMBER</div>
                                                    <div style="font-size: 13px; color: #374151; font-weight: 600; font-family: monospace;">${item.serial_number || 'Not Available'}</div>
                                                </div>
                                                ${item.wattage ? `
                                                <div style="background: #f8fafc; padding: 10px; border-radius: 6px;">
                                                    <div style="font-size: 11px; color: #6b7280; font-weight: 500; margin-bottom: 4px;">POWER RATING</div>
                                                    <div style="font-size: 13px; color: #374151; font-weight: 600;">${item.wattage}W</div>
                                                </div>
                                                ` : ''}
                                            </div>

                                            ${item.manufacturer || item.model ? `
                                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                                                ${item.manufacturer ? `
                                                <div style="background: #f1f5f9; padding: 10px; border-radius: 6px;">
                                                    <div style="font-size: 11px; color: #475569; font-weight: 500; margin-bottom: 4px;">MANUFACTURER</div>
                                                    <div style="font-size: 13px; color: #334155; font-weight: 600;">${item.manufacturer}</div>
                                                </div>
                                                ` : ''}
                                                ${item.model ? `
                                                <div style="background: #f1f5f9; padding: 10px; border-radius: 6px;">
                                                    <div style="font-size: 11px; color: #475569; font-weight: 500; margin-bottom: 4px;">MODEL</div>
                                                    <div style="font-size: 13px; color: #334155; font-weight: 600;">${item.model}</div>
                                                </div>
                                                ` : ''}
                                            </div>
                                            ` : ''}

                                            ${item.installation_date ? `
                                            <div style="background: #ecfdf5; padding: 10px; border-radius: 6px; border-left: 3px solid #10b981; margin-bottom: 12px;">
                                                <div style="font-size: 11px; color: #047857; font-weight: 500; margin-bottom: 4px;">INSTALLATION DATE</div>
                                                <div style="font-size: 13px; color: #065f46; font-weight: 600;">${new Date(item.installation_date).toLocaleDateString('en-IN')}</div>
                                            </div>
                                            ` : ''}

                                            ${item.notes ? `
                                            <div style="background: #fffbeb; padding: 10px; border-radius: 6px; border-left: 3px solid #f59e0b;">
                                                <div style="font-size: 11px; color: #92400e; font-weight: 500; margin-bottom: 4px;">NOTES</div>
                                                <p style="margin: 0; font-size: 12px; color: #78350f; line-height: 1.4;">${item.notes}</p>
                                            </div>
                                            ` : ''}
                                        </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                            `).join('');
                        })() : `
                        <div style="text-align: center; padding: 40px; color: #6b7280;">
                            <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;">⚡</div>
                            <h4 style="margin: 0; font-size: 16px;">No Electrical Equipment</h4>
                            <p style="margin: 8px 0 0 0; font-size: 14px;">No electrical fixtures assigned to this classroom</p>
                        </div>
                        `}
                    </div>
                </div>
            </div>
        </div>
        `;

        return html;
    }



    // Function to show classroom details
    async function showClassroomDetails(roomNumber) {
        console.log('🔍 showClassroomDetails called for room:', roomNumber);

        // Show loading state
        const $modal = $('#classroomModal');
        const $modalTitle = $('#modalTitle');
        const $modalSubtitle = $('#modalSubtitle');
        const $modalContent = $('#modalContent');

        if ($modal.length === 0) {
            console.error('❌ Modal element not found!');
            return;
        }

        // Show modal with loading state
        $modalTitle.text('Loading Classroom...');
        $modalSubtitle.text('Fetching detailed information');
        $modalContent.html(`
            <div style="padding: 48px; background-color: white; text-align: center;">
                <div style="width: 64px; height: 64px; background-color: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                    <i class="fas fa-spinner fa-spin" style="color: white; font-size: 24px;"></i>
                </div>
                <h3 style="font-size: 20px; font-weight: 600; color: #374151; margin-bottom: 8px;">Loading Classroom Data</h3>
                <p style="color: #6b7280;">Please wait while we fetch the latest information...</p>
            </div>
        `);

        $modal.removeClass('hidden');
        $modal.css('display', 'flex');
        $('body').css('overflow', 'hidden');

        // Fetch real data from API
        const classroomData = await fetchClassroomData(roomNumber);
        if (!classroomData) {
            $modalContent.html(`
                <div style="padding: 48px; background-color: white; text-align: center;">
                    <div style="width: 64px; height: 64px; background-color: #fecaca; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                        <i class="fas fa-exclamation-triangle" style="color: #ef4444; font-size: 24px;"></i>
                    </div>
                    <h3 style="font-size: 20px; font-weight: 600; color: #374151; margin-bottom: 8px;">Failed to Load Data</h3>
                    <p style="color: #6b7280; margin-bottom: 24px;">Unable to fetch classroom information. Please try again.</p>
                    <button onclick="$('#classroomModal').addClass('hidden').css('display', 'none'); $('body').css('overflow', 'auto');"
                            style="background-color: #ef4444; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer;">
                        Close
                    </button>
                </div>
            `);
            return;
        }

        console.log('✅ Classroom data fetched:', classroomData);

        // Update modal with real data
        $modalTitle.text(classroomData.classroom.room_number || 'Classroom Details');
        $modalSubtitle.text((classroomData.classroom.full_name || 'Unassigned') + ' - Complete Details');

        console.log('🔍 Generating HTML content...');
        const htmlContent = generateClassroomDetailsHTML(classroomData);
        console.log('✅ HTML content generated, length:', htmlContent.length);
        console.log('🔍 First 200 chars of HTML:', htmlContent.substring(0, 200));

        $modalContent.html(htmlContent);
        console.log('✅ Modal updated with real data');

        // Add a small delay to ensure content is rendered
        setTimeout(() => {
            const contentHeight = $modalContent.height();
            console.log('📏 Modal content height:', contentHeight);
            if (contentHeight === 0) {
                console.error('❌ Modal content has zero height - possible CSS issue');
            }
        }, 100);
    }

    // Bind click events to each classroom using the classroom-card class
    $(document).on('click', '.classroom-card', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const roomId = $(this).data('room');
        console.log('🎯 Classroom ' + roomId + ' clicked via class binding');
        console.log('🎯 Element:', this);
        console.log('🎯 Room ID type:', typeof roomId);
        showClassroomDetails(roomId);
    });

    // Close modal button
    $(document).on('click', '#closeModalBtn', function() {
        console.log('Close button clicked');
        const $modal = $('#classroomModal');
        $modal.addClass('hidden');
        $modal.css('display', 'none');
        $('body').css('overflow', 'auto');
    });

    // Close modal when clicking outside
    $(document).on('click', '#classroomModal', function(e) {
        if (e.target === this) {
            console.log('Modal background clicked');
            const $modal = $('#classroomModal');
            $modal.addClass('hidden');
            $modal.css('display', 'none');
            $('body').css('overflow', 'auto');
        }
    });

    // Verify all classroom elements exist
    console.log('jQuery event handlers attached');
    console.log('Found classroom cards:', $('.classroom-card').length);

    // Verify each classroom element exists
    $('.classroom-card').each(function() {
        const roomId = $(this).data('room');
        const roomNumber = $(this).find('.text-sm.font-bold').text();
        console.log('✅ ' + roomNumber + ' (ID: ' + roomId + ') element found and bound');
    });

    // Test modal elements exist
    console.log('🔍 Testing modal elements...');
    console.log('  Modal element:', $('#classroomModal').length);
    console.log('  Modal title:', $('#modalTitle').length);
    console.log('  Modal subtitle:', $('#modalSubtitle').length);
    console.log('  Modal content:', $('#modalContent').length);
    console.log('  Close button:', $('#closeModalBtn').length);


});

function refreshPageData() {
    console.log('Refreshing infrastructure data...');
}
