/**
 * Loan Voucher Generator
 * Handles the PDF generation animation and process
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all loan voucher generation buttons
    const loanVoucherButtons = document.querySelectorAll('.generate-loan-voucher-btn');

    // Add click event listener to each button
    loanVoucherButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the transaction ID from the button's data attribute
            const transactionId = this.dataset.transactionId;
            if (!transactionId) {
                showToast("Transaction ID not found", "error");
                return;
            }

            // Show options modal
            showLoanVoucherOptions(transactionId);
        });
    });

    /**
     * Show loan voucher options modal
     * @param {string} transactionId - The transaction ID
     */
    function showLoanVoucherOptions(transactionId) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.id = 'loan-voucher-options-modal';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Loan Voucher Options</h2>
                    <button id="close-loan-voucher-modal" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <p class="mb-4 text-gray-600">Choose how you would like to view the loan voucher:</p>
                <div class="flex flex-col space-y-3">
                    <button id="download-loan-voucher-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Download PDF
                    </button>
                    <button id="open-tab-loan-voucher-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Open in New Tab
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        document.getElementById('close-loan-voucher-modal').addEventListener('click', () => {
            document.getElementById('loan-voucher-options-modal').remove();
        });

        document.getElementById('download-loan-voucher-btn').addEventListener('click', () => {
            document.getElementById('loan-voucher-options-modal').remove();
            generateLoanVoucher(transactionId, false);
        });

        document.getElementById('open-tab-loan-voucher-btn').addEventListener('click', () => {
            document.getElementById('loan-voucher-options-modal').remove();
            generateLoanVoucher(transactionId, true);
        });
    }

    /**
     * Generate a loan voucher PDF for the transaction
     * @param {string} transactionId - The transaction ID
     * @param {boolean} openInNewTab - Whether to open in a new tab
     */
    function generateLoanVoucher(transactionId, openInNewTab = false) {
        // Show loading animation
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
                <div class="flex flex-col items-center">
                    <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <svg class="w-16 h-16 text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <h2 class="text-xl font-bold mb-2">Generating Loan Voucher</h2>
                    <p class="text-gray-700">Please wait while we prepare your document...</p>
                </div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);

        if (openInNewTab) {
            // Make AJAX request to get the PDF URL
            console.log(`Requesting loan voucher for transaction ${transactionId}`);
            fetch(`/it-admin/inventory/transactions/${transactionId}/loan-voucher?newTab=true&t=${Date.now()}`, {
                method: 'GET',
                credentials: 'same-origin', // Include cookies in the request
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
                }
            })
                .then(response => {
                    // Check if we got redirected to the login page
                    if (response.redirected && response.url.includes('/login')) {
                        window.location.href = '/login';
                        throw new Error('Authentication required. Please log in.');
                    }

                    if (!response.ok) {
                        // Handle authentication errors specifically
                        if (response.status === 401) {
                            // Redirect to login page for authentication errors
                            window.location.href = '/login';
                            throw new Error('Authentication required. Please log in.');
                        }
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }

                    // Check content type to make sure we're getting JSON
                    const contentType = response.headers.get('content-type');
                    console.log('Response content type:', contentType);

                    if (!contentType || !contentType.includes('application/json')) {
                        console.error('Received non-JSON response from server:', contentType);
                        throw new Error('Received non-JSON response from server');
                    }

                    // Parse JSON with better error handling
                    return response.json().catch(error => {
                        console.error('Error parsing JSON response:', error);
                        console.error('Response text:', response.text());
                        throw new Error('Invalid JSON response from server');
                    });
                })
                .then(data => {
                    if (data.success && data.url) {
                        // Open the PDF in a new tab
                        window.open(data.url, '_blank');
                    } else {
                        showToast(data.message || "Error generating loan voucher", "error");
                    }
                    // Remove loading overlay
                    document.getElementById('loading-overlay').remove();
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast("Error generating loan voucher: " + error.message, "error");
                    document.getElementById('loading-overlay').remove();

                    // Fallback to direct download if AJAX fails
                    showToast("Trying direct download instead...", "info");
                    setTimeout(() => {
                        window.location.href = `/it-admin/inventory/transactions/${transactionId}/loan-voucher`;
                    }, 1500);
                });
        } else {
            // Redirect to download the voucher
            const downloadFrame = document.createElement('iframe');
            downloadFrame.style.display = 'none';
            downloadFrame.src = `/it-admin/inventory/transactions/${transactionId}/loan-voucher`;
            document.body.appendChild(downloadFrame);

            // Remove loading overlay after a delay
            setTimeout(() => {
                document.getElementById('loading-overlay').remove();
                // Remove the iframe after a delay to ensure download starts
                setTimeout(() => {
                    downloadFrame.remove();
                }, 2000);
            }, 1500);
        }
    }

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, warning, info)
     */
    function showToast(message, type = 'info') {
        // Check if the toast container exists
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            // Create the toast container
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed bottom-4 right-4 z-50';
            document.body.appendChild(toastContainer);
        }

        // Create the toast
        const toast = document.createElement('div');
        toast.className = `flex items-center p-4 mb-3 rounded-lg shadow-lg ${getToastColorClass(type)}`;
        toast.innerHTML = `
            ${getToastIcon(type)}
            <div class="ml-3 text-sm font-medium">${message}</div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 text-gray-500 hover:text-white hover:bg-gray-600" aria-label="Close">
                <span class="sr-only">Close</span>
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;

        // Add the toast to the container
        toastContainer.appendChild(toast);

        // Add click event to close button
        const closeButton = toast.querySelector('button');
        closeButton.addEventListener('click', () => {
            toast.remove();
        });

        // Auto-remove the toast after 5 seconds
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    /**
     * Get the color class for the toast based on type
     * @param {string} type - The type of toast
     * @returns {string} - The color class
     */
    function getToastColorClass(type) {
        switch (type) {
            case 'success':
                return 'bg-green-100 text-green-800';
            case 'error':
                return 'bg-red-100 text-red-800';
            case 'warning':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-blue-100 text-blue-800';
        }
    }

    /**
     * Get the icon for the toast based on type
     * @param {string} type - The type of toast
     * @returns {string} - The icon HTML
     */
    function getToastIcon(type) {
        switch (type) {
            case 'success':
                return '<svg class="w-5 h-5 text-green-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
            case 'error':
                return '<svg class="w-5 h-5 text-red-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
            case 'warning':
                return '<svg class="w-5 h-5 text-yellow-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
            default:
                return '<svg class="w-5 h-5 text-blue-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
        }
    }
});
