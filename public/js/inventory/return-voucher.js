/**
 * Return Voucher Generator
 * Handles the PDF generation animation and process
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all return voucher generation buttons
    const returnVoucherButtons = document.querySelectorAll('.generate-return-voucher-btn');

    // Add click event listener to each button
    returnVoucherButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the transaction ID from the button's data attribute
            const transactionId = this.dataset.transactionId;
            if (!transactionId) {
                showToast("Transaction ID not found", "error");
                return;
            }

            // Show options modal
            showReturnVoucherOptions(transactionId);
        });
    });

    /**
     * Show return voucher options modal
     * @param {string} transactionId - The transaction ID
     */
    function showReturnVoucherOptions(transactionId) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.id = 'return-voucher-options-modal';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Return Voucher Options</h2>
                    <button id="close-return-voucher-modal" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <p class="mb-4 text-gray-600">Choose how you would like to view the return voucher:</p>
                <div class="flex flex-col space-y-3">
                    <button id="download-return-voucher-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Download PDF
                    </button>
                    <button id="open-tab-return-voucher-btn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Open in New Tab
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        document.getElementById('close-return-voucher-modal').addEventListener('click', () => {
            document.getElementById('return-voucher-options-modal').remove();
        });

        document.getElementById('download-return-voucher-btn').addEventListener('click', () => {
            document.getElementById('return-voucher-options-modal').remove();
            generateReturnVoucher(transactionId, false);
        });

        document.getElementById('open-tab-return-voucher-btn').addEventListener('click', () => {
            document.getElementById('return-voucher-options-modal').remove();
            generateReturnVoucher(transactionId, true);
        });
    }

    /**
     * Generate a return voucher PDF for the transaction
     * @param {string} transactionId - The transaction ID
     * @param {boolean} openInNewTab - Whether to open in a new tab
     */
    function generateReturnVoucher(transactionId, openInNewTab = false) {
        // Show loading animation
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
                <div class="flex flex-col items-center">
                    <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-green-500 mb-4"></div>
                    <svg class="w-16 h-16 text-green-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <h2 class="text-xl font-bold mb-2">Generating Return Voucher</h2>
                    <p class="text-gray-700">Please wait while we prepare your document...</p>
                </div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);

        if (openInNewTab) {
            // Make AJAX request to get the PDF URL
            console.log(`Requesting return voucher for transaction ${transactionId}`);
            fetch(`/it-admin/inventory/transactions/${transactionId}/return-voucher?newTab=true&t=${Date.now()}`, {
                method: 'GET',
                credentials: 'same-origin', // Include cookies in the request
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
                }
            })
                .then(response => {
                    // Check if we got redirected to the login page
                    if (response.redirected && response.url.includes('/login')) {
                        window.location.href = '/login';
                        throw new Error('Authentication required. Please log in.');
                    }

                    if (!response.ok) {
                        // Handle authentication errors specifically
                        if (response.status === 401) {
                            // Redirect to login page for authentication errors
                            window.location.href = '/login';
                            throw new Error('Authentication required. Please log in.');
                        }
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }

                    // Check content type to make sure we're getting JSON
                    const contentType = response.headers.get('content-type');
                    console.log('Response content type:', contentType);

                    if (!contentType || !contentType.includes('application/json')) {
                        console.error('Received non-JSON response from server:', contentType);
                        throw new Error('Received non-JSON response from server');
                    }

                    // Parse JSON with better error handling
                    return response.json().catch(error => {
                        console.error('Error parsing JSON response:', error);
                        console.error('Response text:', response.text());
                        throw new Error('Invalid JSON response from server');
                    });
                })
                .then(data => {
                    if (data.success && data.url) {
                        // Open the PDF in a new tab
                        window.open(data.url, '_blank');
                    } else {
                        showToast(data.message || "Error generating return voucher", "error");
                    }
                    // Remove loading overlay
                    document.getElementById('loading-overlay').remove();
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast("Error generating return voucher: " + error.message, "error");
                    document.getElementById('loading-overlay').remove();

                    // Fallback to direct download if AJAX fails
                    showToast("Trying direct download instead...", "info");
                    setTimeout(() => {
                        window.location.href = `/it-admin/inventory/transactions/${transactionId}/return-voucher`;
                    }, 1500);
                });
        } else {
            // Redirect to download the voucher using an iframe to avoid page navigation
            const downloadFrame = document.createElement('iframe');
            downloadFrame.style.display = 'none';
            downloadFrame.src = `/it-admin/inventory/transactions/${transactionId}/return-voucher`;
            document.body.appendChild(downloadFrame);

            // Remove loading overlay after a delay
            setTimeout(() => {
                document.getElementById('loading-overlay').remove();
                // Remove the iframe after a delay to ensure download starts
                setTimeout(() => {
                    downloadFrame.remove();
                }, 2000);
            }, 1500);
        }
    }

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, warning, info)
     */
    function showToast(message, type = 'info') {
        // Check if toast container exists, if not create it
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-4';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'flex items-center p-4 rounded-lg shadow-lg max-w-xs transform transition-all duration-300 ease-in-out';

        // Set background color based on type
        switch (type) {
            case 'success':
                toast.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
                break;
            case 'error':
                toast.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
                break;
            case 'warning':
                toast.classList.add('bg-yellow-100', 'text-yellow-800', 'border-l-4', 'border-yellow-500');
                break;
            default:
                toast.classList.add('bg-blue-100', 'text-blue-800', 'border-l-4', 'border-blue-500');
        }

        // Set toast content
        toast.innerHTML = `
            <div class="mr-3 flex-1">${message}</div>
            <button class="text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;

        // Add the toast to the container
        toastContainer.appendChild(toast);

        // Add click event to close button
        const closeButton = toast.querySelector('button');
        closeButton.addEventListener('click', () => {
            toast.remove();
        });

        // Auto-remove the toast after 5 seconds
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
});
