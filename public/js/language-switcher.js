/**
 * Language Switcher JavaScript
 * Handles language switching functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get language switcher elements
    const languageMenuButton = document.getElementById('language-menu-button');
    const languageDropdown = document.querySelector('.language-dropdown');
    const languageForms = document.querySelectorAll('.language-dropdown form');
    
    // Toggle dropdown when clicking the button
    if (languageMenuButton) {
        languageMenuButton.addEventListener('click', function(e) {
            e.preventDefault();
            languageDropdown.classList.toggle('hidden');
        });
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (languageMenuButton && languageDropdown && 
            !languageMenuButton.contains(event.target) && 
            !languageDropdown.contains(event.target)) {
            languageDropdown.classList.add('hidden');
        }
    });
    
    // Add submit event to language forms
    languageForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            console.log('Language form submitted:', this.action);
            // Let the form submit normally
        });
    });
});
