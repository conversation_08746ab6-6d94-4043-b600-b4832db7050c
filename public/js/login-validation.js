// Login form validation
console.log('Login validation script loaded');

// Execute when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing login validation');

    // Get form elements
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const loginButton = document.getElementById('loginButton');

    // Create email feedback element if it doesn't exist
    if (!document.getElementById('email-feedback')) {
        const emailContainer = emailInput.parentNode;
        const feedbackDiv = document.createElement('div');
        feedbackDiv.id = 'email-feedback';
        feedbackDiv.className = 'mt-1 text-sm text-gray-600';
        feedbackDiv.style.minHeight = '20px';
        feedbackDiv.style.display = 'block';
        emailContainer.appendChild(feedbackDiv);
        console.log('Created email-feedback element for login form');
    }

    // Get the feedback element
    const emailFeedback = document.getElementById('email-feedback');

    // Email validation
    emailInput.addEventListener('input', function() {
        const email = this.value.trim();
        console.log('Email input:', email);

        // Simple email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!email) {
            emailFeedback.textContent = '';
            return;
        }

        if (!emailRegex.test(email)) {
            emailFeedback.textContent = 'Please enter a valid email address';
            emailFeedback.style.color = 'red';
            return;
        }

        // Valid email format
        emailFeedback.textContent = 'Valid email format';
        emailFeedback.style.color = 'green';

        // Optional: Check if email exists in the system
        fetch(`/api/users/check-email?email=${encodeURIComponent(email)}`)
            .then(response => response.json())
            .then(data => {
                console.log('Email check response:', data);
                if (data.available) {
                    // Email not found in the system
                    emailFeedback.textContent = 'Email not registered. Do you need to create an account?';
                    emailFeedback.style.color = 'orange';
                } else {
                    // Email exists
                    emailFeedback.textContent = 'Email recognized';
                    emailFeedback.style.color = 'green';
                }
            })
            .catch(error => {
                console.error('Error checking email:', error);
                // Don't show error to user, just keep the "Valid email format" message
            });
    });

    // Toggle password visibility
    const togglePassword = document.querySelector('.toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            // Get the password input element directly to ensure it's the current reference
            const passwordField = document.getElementById('password');
            const eyeIcon = this.querySelector('.eye-icon');
            const eyeSlashIcon = this.querySelector('.eye-slash-icon');

            if (passwordField && passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.add('hidden');
                eyeSlashIcon.classList.remove('hidden');
            } else if (passwordField) {
                passwordField.type = 'password';
                eyeIcon.classList.remove('hidden');
                eyeSlashIcon.classList.add('hidden');
            }
        });
    }

    // Form submission
    loginForm.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        let hasErrors = false;

        // Get or create feedback elements
        const emailFeedback = document.getElementById('email-feedback');

        let passwordFeedback = document.getElementById('password-feedback');
        if (!passwordFeedback) {
            passwordFeedback = document.createElement('div');
            passwordFeedback.id = 'password-feedback';
            passwordFeedback.className = 'mt-1 text-sm';
            passwordFeedback.style.minHeight = '20px';
            passwordInput.parentNode.appendChild(passwordFeedback);
        }

        // Reset feedback
        emailFeedback.textContent = '';
        passwordFeedback.textContent = '';

        // Basic validation
        if (!email) {
            e.preventDefault();
            hasErrors = true;
            emailFeedback.textContent = 'Email is required';
            emailFeedback.style.color = 'red';
        }

        if (!password) {
            e.preventDefault();
            hasErrors = true;
            passwordFeedback.textContent = 'Password is required';
            passwordFeedback.style.color = 'red';
        }

        // Email format validation
        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                hasErrors = true;
                emailFeedback.textContent = 'Please enter a valid email address';
                emailFeedback.style.color = 'red';
            }
        }

        if (!hasErrors) {
            // Disable button to prevent double submission
            loginButton.disabled = true;
            loginButton.textContent = 'Signing in...';
        }
    });
});
