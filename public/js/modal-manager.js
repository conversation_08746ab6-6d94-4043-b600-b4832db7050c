/**
 * Modal Manager - Standardized modal implementation
 * 
 * This script provides a consistent way to handle modals across the application.
 * It ensures modals behave consistently across all devices.
 */

const ModalManager = {
  /**
   * Opens a modal
   * @param {string} modalId - The ID of the modal to open
   */
  openModal: function(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    // Show the modal
    modal.classList.remove('hidden');
    
    // Prevent body scrolling
    document.body.style.overflow = 'hidden';
    
    // Add event listeners
    this._addModalEventListeners(modal, modalId);
    
    // Trigger open event
    const event = new CustomEvent('modal:opened', { detail: { modalId } });
    document.dispatchEvent(event);
  },
  
  /**
   * Closes a modal
   * @param {string} modalId - The ID of the modal to close
   */
  closeModal: function(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    // Hide the modal
    modal.classList.add('hidden');
    
    // Restore body scrolling
    document.body.style.overflow = '';
    
    // Remove event listeners
    this._removeModalEventListeners(modal);
    
    // Trigger close event
    const event = new CustomEvent('modal:closed', { detail: { modalId } });
    document.dispatchEvent(event);
  },
  
  /**
   * Adds event listeners to the modal
   * @param {HTMLElement} modal - The modal element
   * @param {string} modalId - The ID of the modal
   * @private
   */
  _addModalEventListeners: function(modal, modalId) {
    // Close when clicking outside the modal content
    modal.addEventListener('click', this._handleOutsideClick);
    
    // Close when pressing Escape key
    document.addEventListener('keydown', this._handleEscapeKey);
    
    // Find and attach close button listeners
    const closeButtons = modal.querySelectorAll('[data-close-modal]');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => this.closeModal(modalId));
    });
  },
  
  /**
   * Removes event listeners from the modal
   * @param {HTMLElement} modal - The modal element
   * @private
   */
  _removeModalEventListeners: function(modal) {
    modal.removeEventListener('click', this._handleOutsideClick);
    document.removeEventListener('keydown', this._handleEscapeKey);
  },
  
  /**
   * Handles clicks outside the modal content
   * @param {Event} event - The click event
   * @private
   */
  _handleOutsideClick: function(event) {
    // Check if the click was directly on the modal backdrop (not its children)
    if (event.target === this) {
      const modalId = this.id;
      ModalManager.closeModal(modalId);
    }
  },
  
  /**
   * Handles Escape key press
   * @param {KeyboardEvent} event - The keyboard event
   * @private
   */
  _handleEscapeKey: function(event) {
    if (event.key === 'Escape') {
      // Find the topmost visible modal
      const visibleModals = document.querySelectorAll('.modal:not(.hidden)');
      if (visibleModals.length > 0) {
        const topmostModal = visibleModals[visibleModals.length - 1];
        ModalManager.closeModal(topmostModal.id);
      }
    }
  },
  
  /**
   * Initializes modal triggers
   * Should be called when the DOM is loaded
   */
  init: function() {
    // Find all modal trigger buttons
    const modalTriggers = document.querySelectorAll('[data-open-modal]');
    
    // Add click event listeners to each trigger
    modalTriggers.forEach(trigger => {
      const modalId = trigger.getAttribute('data-open-modal');
      trigger.addEventListener('click', () => this.openModal(modalId));
    });
    
    // Find all modal close buttons
    const modalCloseButtons = document.querySelectorAll('[data-close-modal]');
    
    // Add click event listeners to each close button
    modalCloseButtons.forEach(button => {
      const modalId = button.getAttribute('data-close-modal');
      button.addEventListener('click', () => this.closeModal(modalId));
    });
  }
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  ModalManager.init();
});
