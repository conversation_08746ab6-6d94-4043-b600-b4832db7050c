/**
 * Multi-step Form Handler
 *
 * This script handles the functionality for multi-step forms, including:
 * - Navigation between steps
 * - Validation of each step
 * - Updating progress indicators
 * - Saving form data between steps
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Multi-step form script loaded');

    // Initialize the multi-step form if it exists
    initMultiStepForm();

    // Initialize datepicker for date fields
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            dateFormat: 'd-M-Y',
            allowInput: true,
            altInput: true,
            altFormat: 'd-M-Y',
            maxDate: new Date().fp_incr(-10 * 365) // Minimum age of 10 years
        });
    }
});

function initMultiStepForm() {
    // Get form elements
    const form = document.getElementById('registerForm');
    if (!form) return;

    console.log('Initializing multi-step form');

    // Get step elements
    const steps = document.querySelectorAll('.form-step');
    const stepIndicators = document.querySelectorAll('.step-indicator');
    const progressBar = document.getElementById('progress-bar');
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');

    // Set initial state
    let currentStep = 1;
    updateStepVisibility(currentStep);
    updateProgressBar(currentStep, steps.length);

    // Add event listeners to next buttons
    nextButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Validate current step
            if (validateStep(currentStep)) {
                // Move to next step
                currentStep++;
                updateStepVisibility(currentStep);
                updateProgressBar(currentStep, steps.length);
                window.scrollTo(0, 0);
            }
        });
    });

    // Add event listeners to previous buttons
    prevButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Move to previous step
            currentStep--;
            updateStepVisibility(currentStep);
            updateProgressBar(currentStep, steps.length);
            window.scrollTo(0, 0);
        });
    });

    // Add event listeners to step indicators
    stepIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', function() {
            const stepNumber = index + 1;

            // Only allow clicking on completed steps or the next available step
            if (stepNumber <= currentStep) {
                // Validate current step if moving backward
                if (stepNumber < currentStep || validateStep(currentStep)) {
                    currentStep = stepNumber;
                    updateStepVisibility(currentStep);
                    updateProgressBar(currentStep, steps.length);
                    window.scrollTo(0, 0);
                }
            } else if (stepNumber === currentStep + 1) {
                // Allow clicking on the next step if current step is valid
                if (validateStep(currentStep)) {
                    currentStep = stepNumber;
                    updateStepVisibility(currentStep);
                    updateProgressBar(currentStep, steps.length);
                    window.scrollTo(0, 0);
                }
            }
        });
    });
}

function updateStepVisibility(stepNumber) {
    console.log('Updating step visibility to step', stepNumber);

    // Hide all steps
    const steps = document.querySelectorAll('.form-step');
    steps.forEach(step => {
        step.classList.add('hidden');
        step.classList.remove('active');
    });

    // Show current step
    const currentStep = document.getElementById(`step${stepNumber}`);
    if (currentStep) {
        currentStep.classList.remove('hidden');
        currentStep.classList.add('active');

        // If this is the confirmation step (step 4), trigger a custom event
        if (stepNumber === 4) {
            console.log('Reached confirmation step, triggering preview update');
            // Create and dispatch a custom event
            const event = new CustomEvent('confirmationStepReached');
            document.dispatchEvent(event);
        }
    }

    // Update step indicators
    const stepIndicators = document.querySelectorAll('.step-indicator');
    stepIndicators.forEach((indicator, index) => {
        const indicatorStep = index + 1;

        // Reset all indicators
        indicator.classList.remove('active', 'completed');

        // Set the indicator for the current step as active
        if (indicatorStep === stepNumber) {
            indicator.classList.add('active');
            // Update the circle background
            const circle = indicator.querySelector('.w-8');
            if (circle) {
                circle.classList.remove('bg-gray-300', 'text-gray-600');
                circle.classList.add('bg-indigo-600', 'text-white');
            }
        } else {
            // Reset the circle background for non-active steps
            const circle = indicator.querySelector('.w-8');
            if (circle) {
                if (indicatorStep < stepNumber) {
                    // Completed step
                    circle.classList.remove('bg-gray-300', 'text-gray-600', 'bg-indigo-600');
                    circle.classList.add('bg-green-500', 'text-white');
                } else {
                    // Future step
                    circle.classList.remove('bg-indigo-600', 'text-white', 'bg-green-500');
                    circle.classList.add('bg-gray-300', 'text-gray-600');
                }
            }
        }

        // Set indicators for completed steps
        if (indicatorStep < stepNumber) {
            indicator.classList.add('completed');
        }

        // Update the step number display
        const stepNumberElement = indicator.querySelector('.w-8');
        if (stepNumberElement && indicatorStep < stepNumber) {
            // Show checkmark for completed steps
            stepNumberElement.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
        }
    });
}

function updateProgressBar(currentStep, totalSteps) {
    const progressBar = document.getElementById('progress-bar');
    if (progressBar) {
        const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;
        progressBar.style.width = `${progressPercentage}%`;
    }
}

function validateStep(stepNumber) {
    console.log('Validating step', stepNumber);

    // Get all required fields in the current step
    const currentStep = document.getElementById(`step${stepNumber}`);
    if (!currentStep) return true;

    const requiredFields = currentStep.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    // Check each required field
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = field.nextElementSibling;
            if (!errorElement || !errorElement.classList.contains('error-message')) {
                errorElement = document.createElement('p');
                errorElement.classList.add('error-message', 'text-red-500', 'text-xs', 'mt-1');
                field.parentNode.insertBefore(errorElement, field.nextSibling);
            }
            errorElement.textContent = 'This field is required';
        } else {
            field.classList.remove('border-red-500');

            // Remove error message if it exists
            const errorElement = field.nextElementSibling;
            if (errorElement && errorElement.classList.contains('error-message')) {
                errorElement.textContent = '';
            }
        }
    });

    // Additional validation for specific fields
    if (stepNumber === 1) {
        // Validate email format
        const emailInput = currentStep.querySelector('#email');
        if (emailInput && emailInput.value.trim()) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailInput.value.trim())) {
                isValid = false;
                emailInput.classList.add('border-red-500');

                // Add error message
                let errorElement = document.getElementById('email-feedback');
                if (errorElement) {
                    errorElement.textContent = 'Please enter a valid email address';
                    errorElement.style.color = 'red';
                }
            }
        }

        // Validate password match
        const passwordInput = currentStep.querySelector('#password');
        const confirmPasswordInput = currentStep.querySelector('#confirm_password');
        if (passwordInput && confirmPasswordInput &&
            passwordInput.value.trim() && confirmPasswordInput.value.trim()) {
            if (passwordInput.value !== confirmPasswordInput.value) {
                isValid = false;
                confirmPasswordInput.classList.add('border-red-500');

                // Add error message
                let errorElement = document.getElementById('password-match-feedback');
                if (errorElement) {
                    errorElement.textContent = 'Passwords do not match';
                    errorElement.style.color = 'red';
                }
            }
        }
    }

    // Show general error message if validation fails
    if (!isValid) {
        // Create or update general error message
        let generalError = currentStep.querySelector('.step-error-message');
        if (!generalError) {
            generalError = document.createElement('div');
            generalError.classList.add('step-error-message', 'bg-red-100', 'border', 'border-red-400', 'text-red-700', 'px-4', 'py-3', 'rounded', 'relative', 'mb-4');
            currentStep.insertBefore(generalError, currentStep.firstChild);
        }
        generalError.innerHTML = '<strong>Please correct the errors before proceeding.</strong>';

        // Scroll to the top of the form to show the error message
        generalError.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        // Remove general error message if it exists
        const generalError = currentStep.querySelector('.step-error-message');
        if (generalError) {
            generalError.remove();
        }
    }

    return isValid;
}
