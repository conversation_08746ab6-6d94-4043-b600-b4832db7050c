/**
 * Notification Counter
 * Handles fetching and displaying notification counts
 */
document.addEventListener('DOMContentLoaded', function() {
    // Select all notification count elements
    const notificationCountElements = document.querySelectorAll('.notification-count, #notificationCount, .mobile-notification-count, #adminNotificationCount, .mobile-admin-notification-count');

    if (notificationCountElements.length === 0) return;

    // Function to update notification count
    async function updateNotificationCount() {
        try {
            // Fetch unread notifications count
            const response = await fetch('/api/notifications/unread');

            // Check if response is OK
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }

            const data = await response.json();

            if (data.success) {
                // Update count on all elements
                notificationCountElements.forEach(element => {
                    if (data.count > 0) {
                        element.textContent = data.count > 99 ? '99+' : data.count;
                        element.classList.remove('hidden');
                    } else {
                        element.classList.add('hidden');
                    }
                });
            }
        } catch (error) {
            console.error('Error fetching notification count:', error);
            // Hide notification count on error
            notificationCountElements.forEach(element => {
                element.classList.add('hidden');
            });
        }
    }

    // Update count on page load
    updateNotificationCount();

    // Update count every 30 seconds
    setInterval(updateNotificationCount, 30000);

    // Also fetch unread chat messages
    async function updateChatCount() {
        try {
            // Fetch unread chat messages count
            const response = await fetch('/api/chat/unread');

            // Check if response is OK
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }

            const data = await response.json();

            if (data.success) {
                // Update chat icon if needed
                const chatIcons = document.querySelectorAll('a[href="/chat"] svg, #floatingChatButton svg');
                const chatBadge = document.getElementById('chatNotificationBadge');

                if (data.count > 0) {
                    // Add red color to chat icons
                    chatIcons.forEach(icon => {
                        icon.classList.add('text-red-500');
                    });

                    // Update floating chat badge if it exists
                    if (chatBadge) {
                        chatBadge.textContent = data.count > 99 ? '99+' : data.count;
                        chatBadge.classList.remove('hidden');
                    }
                } else {
                    // Remove red color from chat icons
                    chatIcons.forEach(icon => {
                        icon.classList.remove('text-red-500');
                    });

                    // Hide chat badge if it exists
                    if (chatBadge) {
                        chatBadge.classList.add('hidden');
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching chat count:', error);
            // Reset chat icons and badge on error
            const chatIcons = document.querySelectorAll('a[href="/chat"] svg, #floatingChatButton svg');
            const chatBadge = document.getElementById('chatNotificationBadge');

            chatIcons.forEach(icon => {
                icon.classList.remove('text-red-500');
            });

            if (chatBadge) {
                chatBadge.classList.add('hidden');
            }
        }
    }

    // Update chat count on page load
    updateChatCount();

    // Update chat count every 30 seconds
    setInterval(updateChatCount, 30000);
});
