/**
 * Notification Events
 * This script adds toast notifications to important events in the application
 */

document.addEventListener('DOMContentLoaded', function() {
  // Listen for form submissions
  document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(event) {
      // Don't show notifications for certain forms
      if (this.classList.contains('no-notification') || 
          this.getAttribute('data-no-notification') === 'true') {
        return;
      }
      
      // Get form action and method
      const action = this.getAttribute('action');
      const method = this.getAttribute('method') || 'GET';
      
      // Show notifications for specific actions
      if (action && method.toUpperCase() === 'POST') {
        // Login form
        if (action === '/login' || action.includes('/auth/login')) {
          // Don't show notification for login - will be handled by server response
          return;
        }
        
        // Registration form
        if (action === '/register' || action.includes('/auth/register')) {
          // Don't show notification for registration - will be handled by server response
          return;
        }
        
        // Test submission
        if (action.includes('/tests/submit') || action.includes('/exam/submit')) {
          const message = 'Submitting your test...';
          ToastNotifications.info(message);
        }
        
        // Profile update
        if (action.includes('/profile/update')) {
          const message = 'Updating your profile...';
          ToastNotifications.info(message);
        }
      }
    });
  });
  
  // Listen for AJAX requests
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Only intercept POST, PUT, PATCH, DELETE requests
    if (options.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(options.method.toUpperCase())) {
      // Show notifications for specific API endpoints
      if (url.includes('/api/notifications')) {
        // Don't show notifications for notification API calls to avoid duplication
        return originalFetch(url, options);
      }
      
      // Test results
      if (url.includes('/api/tests/results') || url.includes('/api/exams/results')) {
        ToastNotifications.info('Fetching test results...');
      }
      
      // User actions
      if (url.includes('/api/users') && options.method === 'PUT') {
        ToastNotifications.info('Updating user information...');
      }
    }
    
    return originalFetch(url, options)
      .then(response => {
        // Clone the response to avoid consuming it
        const clone = response.clone();
        
        // Try to parse the response as JSON
        clone.json().catch(() => {}).then(data => {
          if (data && data.message) {
            // Show success or error notification based on response status
            if (response.ok) {
              ToastNotifications.success(data.message);
            } else {
              ToastNotifications.error(data.message);
            }
          }
        });
        
        return response;
      })
      .catch(error => {
        // Show error notification for network errors
        ToastNotifications.error('Network error: ' + error.message);
        throw error;
      });
  };
  
  // Listen for page load events
  if (window.location.search.includes('success=')) {
    const successMessage = new URLSearchParams(window.location.search).get('success');
    if (successMessage) {
      ToastNotifications.success(decodeURIComponent(successMessage));
    }
  }
  
  if (window.location.search.includes('error=')) {
    const errorMessage = new URLSearchParams(window.location.search).get('error');
    if (errorMessage) {
      ToastNotifications.error(decodeURIComponent(errorMessage));
    }
  }
  
  if (window.location.search.includes('info=')) {
    const infoMessage = new URLSearchParams(window.location.search).get('info');
    if (infoMessage) {
      ToastNotifications.info(decodeURIComponent(infoMessage));
    }
  }
  
  if (window.location.search.includes('warning=')) {
    const warningMessage = new URLSearchParams(window.location.search).get('warning');
    if (warningMessage) {
      ToastNotifications.warning(decodeURIComponent(warningMessage));
    }
  }
});
