/**
 * Procurement Step 3: Quotation Upload
 * Handles the quotation upload functionality for the procurement system
 */

// Add helper functions at the top of the file
function safeGetElementValue(elementId) {
    const element = document.getElementById(elementId);
    return element ? element.value : '';
}

function safeGetElement(elementId) {
    try {
        return document.getElementById(elementId);
    } catch (error) {
        console.error('Error getting element by ID:', elementId, error);
        return null;
    }
}

// Create a safe DOM utility to prevent null reference errors
window.safeDOM = window.safeDOM || {
    getElementById: function(id) {
        try {
            return document.getElementById(id);
        } catch (error) {
            console.error('Error getting element by ID:', id, error);
            return null;
        }
    },
    querySelector: function(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.error('Error querying selector:', selector, error);
            return null;
        }
    },
    querySelectorAll: function(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            console.error('Error querying all selector:', selector, error);
            return [];
        }
    },
    getValue: function(id) {
        try {
            const element = document.getElementById(id);
            return element ? element.value : '';
        } catch (error) {
            console.error('Error getting value for element:', id, error);
            return '';
        }
    },
    setValue: function(id, value) {
        try {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting value for element:', id, error);
            return false;
        }
    }
};

// DOM Elements - using safe getter to avoid null references
const vendorQuotationsBody = safeGetElement('vendor-quotations-body');
const quotationDetailsModal = safeGetElement('quotation-details-modal');
const closeQuotationModal = safeGetElement('close-quotation-modal');
const modalVendorName = safeGetElement('modal-vendor-name');
const modalQuotationDate = safeGetElement('modal-quotation-date');
const modalQuotationReference = safeGetElement('modal-quotation-reference');
const modalQuotationFile = safeGetElement('modal-quotation-file');
const modalQuotationPreview = safeGetElement('modal-quotation-preview');
const modalQuotationFilename = safeGetElement('modal-quotation-filename');
const modalRemoveQuotation = safeGetElement('modal-remove-quotation');
const modalItemPrices = safeGetElement('modal-item-prices');
const saveQuotationDetailsBtn = safeGetElement('save-quotation-details');

// Store quotation data
let quotationData = {};
let currentVendorId = null;

/**
 * Initialize Step 3
 */
function initStep3() {
    try {
        console.log('Initializing Step 3: Quotation Upload');

        // Ensure global arrays are initialized
        if (!window.selectedVendors || !Array.isArray(window.selectedVendors)) {
            console.warn('window.selectedVendors is not properly initialized, creating empty array');
            window.selectedVendors = [];
        }

        if (!window.selectedItems || !Array.isArray(window.selectedItems)) {
            console.warn('window.selectedItems is not properly initialized, creating empty array');
            window.selectedItems = [];
        }

        console.log('Current window.selectedVendors:', window.selectedVendors);
        console.log('Current window.selectedItems:', window.selectedItems);

        // Check if required elements exist
        if (!vendorQuotationsBody) {
            console.error('Vendor quotations table body not found. Step 3 initialization aborted.');
            // Try to create the element if it doesn't exist
            const tableContainer = safeGetElement('step3');
            if (tableContainer) {
                console.log('Attempting to create vendor quotations table body');
                // This is a fallback to prevent errors, but the UI won't be fully functional
            }
            return;
        }

        // Populate vendor quotations table
        populateVendorQuotationsTable();

        // Add event listeners with careful error handling
        if (closeQuotationModal) {
            try {
                closeQuotationModal.addEventListener('click', closeModal);
            } catch (error) {
                console.error('Error adding event listener to close modal button:', error);
            }
        } else {
            console.warn('Close quotation modal button not found');
        }

        if (modalQuotationFile) {
            try {
                modalQuotationFile.addEventListener('change', function() {
                    try {
                        if (this.files && this.files.length > 0) {
                            const file = this.files[0];

                            // Show file preview
                            if (modalQuotationPreview) {
                                modalQuotationPreview.classList.remove('hidden');
                            }

                            // Set filename
                            if (modalQuotationFilename) {
                                modalQuotationFilename.textContent = file.name;
                            }
                        }
                    } catch (error) {
                        console.error('Error in file change handler:', error);
                    }
                });
            } catch (error) {
                console.error('Error adding event listener to quotation file input:', error);
            }
        } else {
            console.warn('Modal quotation file input not found');
        }

        if (modalRemoveQuotation) {
            try {
                modalRemoveQuotation.addEventListener('click', removeQuotationFile);
            } catch (error) {
                console.error('Error adding event listener to remove quotation button:', error);
            }
        } else {
            console.warn('Remove quotation button not found');
        }

        if (saveQuotationDetailsBtn) {
            try {
                saveQuotationDetailsBtn.addEventListener('click', saveQuotationDetails);
            } catch (error) {
                console.error('Error adding event listener to save quotation button:', error);
            }
        } else {
            console.warn('Save quotation details button not found');
        }

        // Close modal when clicking outside
        if (quotationDetailsModal) {
            try {
                quotationDetailsModal.addEventListener('click', function(e) {
                    try {
                        if (e.target === quotationDetailsModal) {
                            closeModal();
                        }
                    } catch (error) {
                        console.error('Error in modal click handler:', error);
                    }
                });
            } catch (error) {
                console.error('Error adding event listener to quotation modal:', error);
            }
        } else {
            console.warn('Quotation details modal not found');
        }

        console.log('Step 3 initialization completed successfully');
    } catch (error) {
        console.error('Fatal error during Step 3 initialization:', error);
    }
}

/**
 * Populate vendor quotations table from selected vendors
 */
function populateVendorQuotationsTable() {
    try {
        // Check if table body exists
        if (!vendorQuotationsBody) {
            console.error('Vendor quotations table body not found');
            return;
        }

        // Clear existing rows
        try {
            vendorQuotationsBody.innerHTML = '';
        } catch (error) {
            console.error('Error clearing vendor quotations table:', error);
            return;
        }

        // Debug: Log the current state of selectedVendors
        console.log('Current selectedVendors in step 3:', window.selectedVendors);

        // Make sure we're using the global selectedVendors
        let vendorsToUse = [];

        // Check if window.selectedVendors is defined and has items
        if (typeof window.selectedVendors !== 'undefined' && Array.isArray(window.selectedVendors)) {
            // Filter out any null or undefined entries
            vendorsToUse = window.selectedVendors.filter(vendor => vendor != null);
            console.log('Using window.selectedVendors with', vendorsToUse.length, 'vendors after filtering');
        }
        // Check if local selectedVendors is defined and has items
        else if (typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors)) {
            // Filter out any null or undefined entries
            vendorsToUse = selectedVendors.filter(vendor => vendor != null);
            console.log('Using local selectedVendors with', vendorsToUse.length, 'vendors after filtering');
        }

        // No vendors found - try to add a sample vendor for testing
        if (vendorsToUse.length === 0) {
            console.warn('No valid vendors found in either window.selectedVendors or selectedVendors');

            // Try to add a sample vendor if the addVendor function exists
            if (typeof window.addVendor === 'function') {
                try {
                    console.log('Attempting to add a sample vendor using addVendor function');
                    window.addVendor({
                        id: 'sample_vendor_1',
                        name: 'Sample Vendor',
                        address: 'Sample Address'
                    });

                    // Check if the vendor was added successfully and update vendorsToUse
                    if (window.selectedVendors && Array.isArray(window.selectedVendors)) {
                        vendorsToUse = window.selectedVendors.filter(vendor => vendor != null);
                        console.log('Successfully added a sample vendor, now have', vendorsToUse.length, 'vendors');
                    }
                } catch (error) {
                    console.error('Error adding sample vendor:', error);
                }
            }

            // If still no vendors, show a message
            if (vendorsToUse.length === 0) {
                // Create a message row
                try {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.className = 'px-4 py-3 text-center text-gray-500 italic';
                    cell.colSpan = 3;
                    cell.textContent = 'Vendor data not available. Please go back to Step 2 and select vendors.';
                    row.appendChild(cell);
                    vendorQuotationsBody.appendChild(row);
                } catch (error) {
                    console.error('Error creating message row:', error);
                }
                return;
            }
        }

        // Check if selectedItems is defined and filter out null/undefined entries
        if (typeof window.selectedItems !== 'undefined' && Array.isArray(window.selectedItems)) {
            // Filter out any null or undefined entries
            window.selectedItems = window.selectedItems.filter(item => item != null);
            console.log('Using window.selectedItems with', window.selectedItems.length, 'items after filtering');
        } else if (typeof selectedItems !== 'undefined' && Array.isArray(selectedItems)) {
            // Filter out any null or undefined entries
            selectedItems = selectedItems.filter(item => item != null);
            window.selectedItems = selectedItems;
            console.log('Copied local selectedItems to window.selectedItems, now have', window.selectedItems.length, 'items');
        } else {
            console.warn('selectedItems is not defined');
            window.selectedItems = []; // Initialize with empty array to prevent errors
        }

        // If no items found, try to add a sample item
        if (window.selectedItems.length === 0) {
            // Try to add a sample item if the addItem function exists
            if (typeof window.addItem === 'function') {
                try {
                    console.log('Attempting to add a sample item using addItem function');
                    window.addItem({
                        id: 'sample_item_1',
                        name: 'Sample Item',
                        quantity: 1
                    });

                    // Check if the item was added successfully
                    if (window.selectedItems && window.selectedItems.length > 0) {
                        console.log('Successfully added a sample item, now have', window.selectedItems.length, 'items');
                    }
                } catch (error) {
                    console.error('Error adding sample item:', error);
                }
            }
        }

        // Check if we have vendors to display
        if (vendorsToUse.length > 0) {
            vendorsToUse.forEach(vendor => {
                try {
                    if (!vendor) return; // Skip if vendor is null or undefined

                    console.log('Processing vendor:', vendor);

                    // Create row for each vendor
                    const row = document.createElement('tr');

                    // Vendor cell
                    const vendorCell = document.createElement('td');
                    vendorCell.className = 'px-4 py-3 border-r';
                    vendorCell.innerHTML = `
                        <div class="font-medium">${vendor.name || 'Unknown Vendor'}</div>
                        <div class="text-sm text-gray-500">${vendor.address || ''}</div>
                    `;

                    // Quotation cell
                    const quotationCell = document.createElement('td');
                    quotationCell.className = 'px-4 py-3 border-r';

                    // Check if we have quotation data for this vendor
                    if (quotationData[vendor.id]) {
                        const data = quotationData[vendor.id];
                        quotationCell.innerHTML = `
                            <div class="text-sm">Date: ${data.date || 'Not set'}</div>
                            <div class="text-sm">Ref: ${data.reference || 'N/A'}</div>
                            <div class="text-sm mt-1">
                                ${data.filename ?
                                    `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3" />
                                        </svg>
                                        File uploaded
                                    </span>` :
                                    `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                        <svg class="mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3" />
                                        </svg>
                                        No file
                                    </span>`
                                }
                            </div>
                        `;
                    } else {
                        quotationCell.innerHTML = `
                            <div class="text-sm text-gray-500 italic">No quotation details</div>
                        `;
                    }

                    // Items cell with action buttons
                    const itemsCell = document.createElement('td');
                    itemsCell.className = 'px-4 py-3 text-center';

                    // Check if we have item prices for this vendor
                    let itemsStatus = 'Not entered';
                    let statusClass = 'bg-red-100 text-red-800';

                    if (quotationData[vendor.id] && quotationData[vendor.id].items) {
                        try {
                            const itemsWithPrices = Object.values(quotationData[vendor.id].items).filter(item => item && item.unitPrice > 0);
                            if (itemsWithPrices.length > 0) {
                                if (Array.isArray(window.selectedItems) && itemsWithPrices.length === window.selectedItems.length) {
                                    itemsStatus = 'Complete';
                                    statusClass = 'bg-green-100 text-green-800';
                                } else {
                                    itemsStatus = `${itemsWithPrices.length}/${Array.isArray(window.selectedItems) ? window.selectedItems.length : '?'} items`;
                                    statusClass = 'bg-yellow-100 text-yellow-800';
                                }
                            }
                        } catch (error) {
                            console.error('Error calculating item prices:', error);
                        }
                    }

                    itemsCell.innerHTML = `
                        <div class="mb-2">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${statusClass}">
                                ${itemsStatus}
                            </span>
                        </div>
                        <button type="button" class="edit-quotation-btn px-3 py-1 bg-it-admin-primary text-white text-sm rounded hover:bg-it-admin-secondary" data-vendor-id="${vendor.id}">
                            Edit Quotation
                        </button>
                    `;

                    // Add cells to row
                    row.appendChild(vendorCell);
                    row.appendChild(quotationCell);
                    row.appendChild(itemsCell);

                    // Add row to table
                    vendorQuotationsBody.appendChild(row);
                } catch (error) {
                    console.error('Error creating vendor row:', error);
                }
            });
        } else {
            // No vendors selected
            try {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.className = 'px-4 py-3 text-center text-gray-500 italic';
                cell.colSpan = 3;
                cell.textContent = 'No vendors selected in Step 2. Please go back and select vendors.';
                row.appendChild(cell);
                vendorQuotationsBody.appendChild(row);
            } catch (error) {
                console.error('Error creating no vendors message:', error);
            }
        }

        // Add event listeners to edit buttons
        try {
            const editButtons = document.querySelectorAll('.edit-quotation-btn');
            if (editButtons && editButtons.length > 0) {
                editButtons.forEach(button => {
                    if (button) {
                        try {
                            button.addEventListener('click', function() {
                                try {
                                    const vendorId = this.getAttribute('data-vendor-id');
                                    if (vendorId) {
                                        openQuotationModal(vendorId);
                                    } else {
                                        console.warn('Vendor ID not found on button');
                                    }
                                } catch (error) {
                                    console.error('Error in edit button click handler:', error);
                                }
                            });
                        } catch (error) {
                            console.error('Error adding event listener to edit button:', error);
                        }
                    }
                });
            } else {
                console.warn('No edit buttons found');
            }
        } catch (error) {
            console.error('Error setting up edit button event listeners:', error);
        }

        console.log('Vendor quotations table populated successfully');
    } catch (error) {
        console.error('Fatal error populating vendor quotations table:', error);
    }
}

/**
 * Open quotation modal for a vendor
 * @param {string} vendorId - The vendor ID
 */
function openQuotationModal(vendorId) {
    // Store current vendor ID
    currentVendorId = vendorId;

    // Find vendor data - first try window.selectedVendors, then local selectedVendors
    let vendor;

    if (typeof window.selectedVendors !== 'undefined' && Array.isArray(window.selectedVendors)) {
        vendor = window.selectedVendors.find(v => v.id === vendorId);
        console.log('Found vendor in window.selectedVendors:', vendor);
    }

    if (!vendor && typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors)) {
        vendor = selectedVendors.find(v => v.id === vendorId);
        console.log('Found vendor in local selectedVendors:', vendor);
    }

    if (!vendor) {
        console.error('Vendor not found with ID:', vendorId);
        return;
    }

    // Set vendor name in modal
    if (modalVendorName) {
        modalVendorName.textContent = `${vendor.name} - Quotation Details`;
    }

    // Clear modal fields
    if (modalQuotationDate) modalQuotationDate.value = '';
    if (modalQuotationReference) modalQuotationReference.value = '';
    if (modalQuotationFile) modalQuotationFile.value = '';
    if (modalQuotationPreview) modalQuotationPreview.classList.add('hidden');
    if (modalItemPrices) modalItemPrices.innerHTML = '';

    // Load existing data if available
    if (quotationData[vendorId]) {
        const data = quotationData[vendorId];
        if (modalQuotationDate) modalQuotationDate.value = data.date || '';
        if (modalQuotationReference) modalQuotationReference.value = data.reference || '';

        // Show file preview if available
        if (data.filename && modalQuotationPreview) {
            modalQuotationPreview.classList.remove('hidden');
            if (modalQuotationFilename) modalQuotationFilename.textContent = data.filename;
        }
    }

    // Populate item prices
    populateModalItemPrices(vendorId);

    // Show modal
    if (quotationDetailsModal) {
        quotationDetailsModal.classList.remove('hidden');
    }
}

/**
 * Populate item prices in modal
 * @param {string} vendorId - The vendor ID
 */
function populateModalItemPrices(vendorId) {
    // Get items from window.selectedItems or local selectedItems
    let itemsToUse = [];

    if (typeof window.selectedItems !== 'undefined' && Array.isArray(window.selectedItems) && window.selectedItems.length > 0) {
        itemsToUse = window.selectedItems;
        console.log('Using window.selectedItems in modal with', itemsToUse.length, 'items');
    } else if (typeof selectedItems !== 'undefined' && Array.isArray(selectedItems) && selectedItems.length > 0) {
        itemsToUse = selectedItems;
        console.log('Using local selectedItems in modal with', itemsToUse.length, 'items');
    } else {
        console.warn('No items found to populate modal');
        return;
    }

    if (!modalItemPrices) {
        console.error('Modal item prices container not found');
        return;
    }

    // Clear existing items
    modalItemPrices.innerHTML = '';

    // Get item price template
    const template = document.getElementById('modal-item-price-template');
    if (!template) {
        console.error('Modal item price template not found');
        return;
    }

    // Add each item
    itemsToUse.forEach(item => {
        console.log('Adding item to modal:', item);
        const itemElement = document.importNode(template.content, true);

        // Set item name and ID
        const nameDisplay = itemElement.querySelector('.item-name-display');
        const idInput = itemElement.querySelector('.item-id');
        if (nameDisplay) nameDisplay.value = item.name;
        if (idInput) idInput.value = item.id;

        // Set unit price if available
        const unitPriceInput = itemElement.querySelector('.item-unit-price');
        const totalPriceInput = itemElement.querySelector('.item-total-price');

        if (quotationData[vendorId] && quotationData[vendorId].items && quotationData[vendorId].items[item.id]) {
            const itemData = quotationData[vendorId].items[item.id];
            if (unitPriceInput) unitPriceInput.value = itemData.unitPrice || '';
            if (totalPriceInput) totalPriceInput.value = itemData.totalPrice || '';
        }

        // Add event listener to calculate total price
        if (unitPriceInput) {
            unitPriceInput.addEventListener('input', function() {
                const unitPrice = parseFloat(this.value) || 0;
                const quantity = item.quantity || 1;
                const totalPrice = unitPrice * quantity;

                if (totalPriceInput) {
                    totalPriceInput.value = totalPrice.toFixed(2);
                }
            });
        }

        // Add to container
        modalItemPrices.appendChild(itemElement);
    });
}

/**
 * Save quotation details
 */
function saveQuotationDetails() {
    if (!currentVendorId) return;

    // Initialize data object if not exists
    if (!quotationData[currentVendorId]) {
        quotationData[currentVendorId] = {
            items: {}
        };
    }

    // Get form values safely
    const date = safeGetElementValue('modal-quotation-date');
    const reference = safeGetElementValue('modal-quotation-reference');

    // Validate required fields
    if (!date) {
        if (typeof showToast === 'function') {
            showToast('error', 'Please enter quotation date');
        } else {
            console.error('Please enter quotation date');
            alert('Please enter quotation date');
        }
        return;
    }

    // Get file details
    let filename = '';
    if (quotationData[currentVendorId].filename) {
        filename = quotationData[currentVendorId].filename;
    } else if (modalQuotationFile && modalQuotationFile.files.length > 0) {
        filename = modalQuotationFile.files[0].name;
    } else {
        if (typeof showToast === 'function') {
            showToast('error', 'Please upload quotation file');
        } else {
            console.error('Please upload quotation file');
            alert('Please upload quotation file');
        }
        return;
    }

    // Save basic details
    quotationData[currentVendorId].date = date;
    quotationData[currentVendorId].reference = reference;
    quotationData[currentVendorId].filename = filename;

    // Save item prices
    const itemPriceElements = modalItemPrices.querySelectorAll('.item-price-entry');
    let hasItemsWithPrices = false;

    itemPriceElements.forEach(element => {
        const itemId = element.querySelector('.item-id').value;
        const unitPrice = parseFloat(element.querySelector('.item-unit-price').value) || 0;
        const totalPrice = parseFloat(element.querySelector('.item-total-price').value) || 0;

        // Initialize item data if not exists
        if (!quotationData[currentVendorId].items[itemId]) {
            quotationData[currentVendorId].items[itemId] = {};
        }

        // Save item data
        quotationData[currentVendorId].items[itemId].unitPrice = unitPrice;
        quotationData[currentVendorId].items[itemId].totalPrice = totalPrice;

        if (unitPrice > 0) {
            hasItemsWithPrices = true;
        }

        console.log(`Saved price data for item ${itemId} from vendor ${currentVendorId}: unitPrice=${unitPrice}, totalPrice=${totalPrice}`);
    });

    // Make quotation data available globally
    window.quotationData = quotationData;

    // Log the current state of quotation data
    console.log('Current quotation data after save:', JSON.parse(JSON.stringify(quotationData)));

    // Dispatch an event to notify that quotation data has been updated
    const event = new CustomEvent('quotationDataUpdated', {
        detail: {
            vendorId: currentVendorId,
            hasItemsWithPrices: hasItemsWithPrices
        }
    });
    document.dispatchEvent(event);

    // Close modal
    closeModal();

    // Update table
    populateVendorQuotationsTable();

    // Show success message
    if (typeof showToast === 'function') {
        showToast('success', 'Quotation details saved successfully');
    } else {
        console.log('Quotation details saved successfully');
    }
}

// handleFileUpload function has been inlined in the event listener

/**
 * Remove quotation file
 */
function removeQuotationFile() {
    if (modalQuotationFile) modalQuotationFile.value = '';
    if (modalQuotationPreview) modalQuotationPreview.classList.add('hidden');

    // Remove filename from data
    if (currentVendorId && quotationData[currentVendorId]) {
        delete quotationData[currentVendorId].filename;
    }
}

/**
 * Close quotation modal
 */
function closeModal() {
    if (quotationDetailsModal) {
        quotationDetailsModal.classList.add('hidden');
    }
}

// Implement showToast if it doesn't exist
if (typeof window.showToast !== 'function') {
    window.showToast = function(type, message) {
        console.log(`[${type}] ${message}`);
        // Simple toast implementation
        const toast = document.createElement('div');
        toast.style.position = 'fixed';
        toast.style.bottom = '20px';
        toast.style.right = '20px';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '4px';
        toast.style.color = 'white';
        toast.style.zIndex = '9999';
        toast.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';

        // Set background color based on type
        if (type === 'success') {
            toast.style.backgroundColor = '#4CAF50';
        } else if (type === 'error') {
            toast.style.backgroundColor = '#F44336';
        } else if (type === 'warning') {
            toast.style.backgroundColor = '#FF9800';
        } else {
            toast.style.backgroundColor = '#2196F3';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // Remove after 3 seconds
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    };
}

/**
 * Get quotation data for comparative statement
 * This function is called from step 4 to get the quotation data
 */
function getQuotationDataForComparative() {
    console.log('Getting quotation data for comparative statement');

    // Use global quotation data if available
    const dataToUse = window.quotationData || quotationData;
    console.log('Using quotation data source:', dataToUse === window.quotationData ? 'global window.quotationData' : 'local quotationData');

    // Log the raw data for debugging
    console.log('Raw quotation data:', JSON.parse(JSON.stringify(dataToUse)));

    // Create a structured format for the comparative table
    const result = {
        vendors: [],
        items: [],
        prices: {}, // Format: prices[itemId][vendorId] = { unitPrice, totalPrice }
        rawData: dataToUse // Include the raw quotation data for debugging and direct access
    };

    // Get vendors
    if (typeof window.selectedVendors !== 'undefined' && Array.isArray(window.selectedVendors)) {
        result.vendors = window.selectedVendors.filter(v => v !== null);
    } else if (typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors)) {
        result.vendors = selectedVendors.filter(v => v !== null);
    }

    console.log('Vendors for comparative statement:', result.vendors);

    // Get items
    if (typeof window.selectedItems !== 'undefined' && Array.isArray(window.selectedItems)) {
        result.items = window.selectedItems.filter(i => i !== null);
    } else if (typeof selectedItems !== 'undefined' && Array.isArray(selectedItems)) {
        result.items = selectedItems.filter(i => i !== null);
    }

    console.log('Items for comparative statement:', result.items);

    // Initialize prices object
    result.prices = {};
    result.items.forEach(item => {
        result.prices[item.id] = {};
    });

    // Fill in prices from quotationData
    result.vendors.forEach(vendor => {
        console.log(`Processing vendor ${vendor.id} (${vendor.name}) for comparative data`);

        if (quotationData[vendor.id] && quotationData[vendor.id].items) {
            console.log(`Found quotation data for vendor ${vendor.id} with ${Object.keys(quotationData[vendor.id].items).length} items`);

            result.items.forEach(item => {
                if (quotationData[vendor.id].items[item.id]) {
                    const unitPrice = quotationData[vendor.id].items[item.id].unitPrice || 0;
                    const totalPrice = quotationData[vendor.id].items[item.id].totalPrice || 0;

                    console.log(`Item ${item.id} (${item.name}) from vendor ${vendor.id}: Unit Price = ${unitPrice}, Total Price = ${totalPrice}`);

                    result.prices[item.id][vendor.id] = {
                        unitPrice: unitPrice,
                        totalPrice: totalPrice
                    };
                } else {
                    // No price data for this item from this vendor
                    console.log(`No price data found for item ${item.id} (${item.name}) from vendor ${vendor.id}`);
                    result.prices[item.id][vendor.id] = {
                        unitPrice: 0,
                        totalPrice: 0
                    };
                }
            });
        } else {
            // No price data for this vendor
            console.log(`No quotation data found for vendor ${vendor.id}`);
            result.items.forEach(item => {
                result.prices[item.id][vendor.id] = {
                    unitPrice: 0,
                    totalPrice: 0
                };
            });
        }
    });

    // Make quotation data available globally for debugging
    window.quotationDataForComparative = result;

    console.log('Quotation data for comparative statement:', result);
    return result;
}

// Define fallback functions for adding items and vendors if they don't exist
if (typeof window.addItem !== 'function') {
    window.addItem = function(item) {
        console.log('Using custom addItem implementation in procurement-step3.js');
        if (!window.selectedItems) {
            window.selectedItems = [];
        }

        // Check if item already exists
        const existingIndex = window.selectedItems.findIndex(i => i && i.id === item.id);
        if (existingIndex >= 0) {
            // Update existing item
            window.selectedItems[existingIndex] = {
                ...window.selectedItems[existingIndex],
                ...item
            };
            return window.selectedItems[existingIndex];
        } else {
            // Add new item
            window.selectedItems.push(item);
            return item;
        }
    };
}

if (typeof window.addVendor !== 'function') {
    window.addVendor = function(vendor) {
        console.log('Using custom addVendor implementation in procurement-step3.js');
        if (!window.selectedVendors) {
            window.selectedVendors = [];
        }

        // Check if vendor already exists
        const existingIndex = window.selectedVendors.findIndex(v => v && v.id === vendor.id);
        if (existingIndex >= 0) {
            // Update existing vendor
            window.selectedVendors[existingIndex] = {
                ...window.selectedVendors[existingIndex],
                ...vendor
            };
            return window.selectedVendors[existingIndex];
        } else {
            // Add new vendor
            window.selectedVendors.push(vendor);
            return vendor;
        }
    };
}

// Export functions
window.initStep3 = initStep3;
window.populateVendorQuotationsTable = populateVendorQuotationsTable;
window.getQuotationDataForComparative = getQuotationDataForComparative;

// Add debugging for when the script loads
console.log('procurement-step3.js loaded');
console.log('Initial window.selectedVendors:', window.selectedVendors);
console.log('Initial window.selectedItems:', window.selectedItems);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('DOMContentLoaded event fired in procurement-step3.js');

        // Ensure global arrays are initialized
        if (!window.selectedVendors || !Array.isArray(window.selectedVendors)) {
            console.warn('window.selectedVendors is not properly initialized in DOMContentLoaded, creating empty array');
            window.selectedVendors = [];
        }

        if (!window.selectedItems || !Array.isArray(window.selectedItems)) {
            console.warn('window.selectedItems is not properly initialized in DOMContentLoaded, creating empty array');
            window.selectedItems = [];
        }

        // Get DOM elements using the safe getter
        const vendorQuotationsBody = safeGetElement('vendor-quotations-body');
        const quotationDetailsModal = safeGetElement('quotation-details-modal');
        const closeQuotationModal = safeGetElement('close-quotation-modal');
        const modalVendorName = safeGetElement('modal-vendor-name');
        const modalQuotationDate = safeGetElement('modal-quotation-date');
        const modalQuotationReference = safeGetElement('modal-quotation-reference');
        const modalQuotationFile = safeGetElement('modal-quotation-file');
        const modalQuotationPreview = safeGetElement('modal-quotation-preview');
        const modalQuotationFilename = safeGetElement('modal-quotation-filename');
        const modalItemPrices = safeGetElement('modal-item-prices');
        const saveQuotationDetailsBtn = safeGetElement('save-quotation-details');
        const removeQuotationBtn = safeGetElement('modal-remove-quotation');

        // Make elements available globally
        window.vendorQuotationsBody = vendorQuotationsBody;
        window.quotationDetailsModal = quotationDetailsModal;
        window.closeQuotationModal = closeQuotationModal;
        window.modalVendorName = modalVendorName;
        window.modalQuotationDate = modalQuotationDate;
        window.modalQuotationReference = modalQuotationReference;
        window.modalQuotationFile = modalQuotationFile;
        window.modalQuotationPreview = modalQuotationPreview;
        window.modalQuotationFilename = modalQuotationFilename;
        window.modalItemPrices = modalItemPrices;
        window.saveQuotationDetailsBtn = saveQuotationDetailsBtn;
        window.removeQuotationBtn = removeQuotationBtn;

        // Initialize file upload handling
        if (modalQuotationFile) {
            try {
                modalQuotationFile.addEventListener('change', function() {
                    try {
                        if (this.files && this.files.length > 0) {
                            const file = this.files[0];

                            // Check file type
                            const validTypes = ['application/pdf', 'image/jpeg', 'image/png'];
                            if (!validTypes.includes(file.type)) {
                                if (typeof showToast === 'function') {
                                    showToast('error', 'Invalid file type. Please upload PDF, JPEG, or PNG files only.');
                                } else {
                                    console.error('Invalid file type. Please upload PDF, JPEG, or PNG files only.');
                                    alert('Invalid file type. Please upload PDF, JPEG, or PNG files only.');
                                }
                                this.value = '';
                                return;
                            }

                            // Check file size (max 5MB)
                            const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                            if (file.size > maxSize) {
                                if (typeof showToast === 'function') {
                                    showToast('error', 'File size exceeds 5MB limit.');
                                } else {
                                    console.error('File size exceeds 5MB limit.');
                                    alert('File size exceeds 5MB limit.');
                                }
                                this.value = '';
                                return;
                            }

                            // Show file preview
                            if (modalQuotationPreview && modalQuotationFilename) {
                                modalQuotationPreview.classList.remove('hidden');
                                modalQuotationFilename.textContent = file.name;

                                // Store filename in quotation data
                                if (currentVendorId) {
                                    if (!quotationData[currentVendorId]) {
                                        quotationData[currentVendorId] = { items: {} };
                                    }
                                    quotationData[currentVendorId].filename = file.name;
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Error handling file upload:', error);
                    }
                });
            } catch (error) {
                console.error('Error adding event listener to file input:', error);
            }
        } else {
            console.warn('Modal quotation file input not found');
        }

        // Initialize step 3
        initStep3();

        console.log('Procurement Step 3 script loaded successfully');
    } catch (error) {
        console.error('Error initializing procurement step 3:', error);
    }
});

// Export functions for use in other modules
window.openQuotationModal = openQuotationModal;
window.saveQuotationDetails = saveQuotationDetails;
window.closeQuotationModal = closeModal;
window.getQuotationDataForComparative = getQuotationDataForComparative;
