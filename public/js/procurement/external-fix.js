/**
 * Procurement Form External Fix
 * Fixes issues with external scripts
 */

// Apply immediate fix for classList error
(function() {
    // Patch Element.prototype.classList to prevent errors
    const originalClassListGetter = Object.getOwnPropertyDescriptor(Element.prototype, 'classList').get;
    Object.defineProperty(Element.prototype, 'classList', {
        get: function() {
            try {
                return originalClassListGetter.call(this);
            } catch (e) {
                console.warn('Error accessing classList, returning mock implementation');
                // Return a mock implementation that doesn't throw errors
                return {
                    add: function() {},
                    remove: function() {},
                    contains: function() { return false; },
                    toggle: function() { return false; }
                };
            }
        },
        configurable: true
    });

    // Also patch document.getElementById to prevent null errors
    const originalGetElementById = document.getElementById;
    document.getElementById = function(id) {
        const element = originalGetElementById.call(document, id);
        if (!element) {
            console.warn(`getElementById('${id}') returned null, creating placeholder element`);
            // Create a placeholder element that won't throw errors
            const placeholder = document.createElement('div');
            placeholder.id = id;
            placeholder.style.display = 'none';
            document.body.appendChild(placeholder);
            return placeholder;
        }
        return element;
    };
})();

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('External fix script loaded');

    // Apply fixes immediately
    applyFixes();

    // Also apply fixes after a delay to catch any late-loading scripts
    setTimeout(applyFixes, 500);
    setTimeout(applyFixes, 1000);

    // Set up a MutationObserver to watch for new scripts
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.tagName === 'SCRIPT') {
                        console.log('New script detected, applying fixes');
                        setTimeout(applyFixes, 100);
                    }
                });
            }
        });
    });

    // Start observing the document
    observer.observe(document, {
        childList: true,
        subtree: true
    });
});

// Function to apply all fixes
function applyFixes() {
    try {
        console.log('Applying fixes...');

        // Fix for the "Cannot read properties of null" error
        fixUpdateUIFunction();

        // Fix for "Invalid item data: undefined" and "Invalid vendor data: undefined" errors
        fixAddItemAndVendorFunctions();

        // Fix for missing containers and templates
        fixMissingElements();

        // Fix for external script errors
        fixExternalScriptErrors();

        console.log('All fixes applied successfully');
    } catch (error) {
        console.error('Error applying fixes:', error);
    }
}

// Fix for the updateUI function
function fixUpdateUIFunction() {
    try {
        // Always create a safe updateUI function, regardless of whether we find the original
        console.log('Creating safe updateUI function');

        // Create a new safe updateUI function that will replace the problematic one
        window.safeUpdateUI = function(currentStep) {
            try {
                console.log('Safe updateUI called for step', currentStep);

                // If currentStep is not provided, get it from the input
                if (!currentStep) {
                    const currentStepInput = document.getElementById('current_step');
                    currentStep = currentStepInput ? parseInt(currentStepInput.value) || 1 : 1;
                }

                // Get all step elements
                const steps = document.querySelectorAll('.form-step, .procurement-section, [id^="step"], [id^="section"]');

                // Hide all steps
                steps.forEach(step => {
                    if (step && typeof step.classList !== 'undefined') {
                        try {
                            step.classList.add('hidden');
                        } catch (e) {
                            console.warn('Error hiding step:', e);
                        }
                    }
                });

                // Show current step
                const currentStepElement = document.getElementById(`step${currentStep}`) ||
                                          document.getElementById(`section${currentStep}`);

                if (currentStepElement && typeof currentStepElement.classList !== 'undefined') {
                    try {
                        currentStepElement.classList.remove('hidden');
                    } catch (e) {
                        console.warn('Error showing current step:', e);
                    }
                } else {
                    console.warn(`Step element for step ${currentStep} not found`);

                    // Show the first step if current step not found
                    if (steps.length > 0 && steps[0] && typeof steps[0].classList !== 'undefined') {
                        try {
                            steps[0].classList.remove('hidden');
                        } catch (e) {
                            console.warn('Error showing first step:', e);
                        }
                    }
                }

                // Update step indicators
                const stepIndicators = document.querySelectorAll('.step-indicator, .procurement-section-number, [data-step]');
                stepIndicators.forEach((indicator, index) => {
                    if (!indicator || typeof indicator.classList === 'undefined') return;

                    const step = index + 1;

                    try {
                        if (step < currentStep) {
                            // Completed step
                            indicator.classList.add('completed');
                            indicator.classList.remove('active');
                        } else if (step === currentStep) {
                            // Current step
                            indicator.classList.add('active');
                            indicator.classList.remove('completed');
                        } else {
                            // Future step
                            indicator.classList.remove('active', 'completed');
                        }
                    } catch (e) {
                        console.warn(`Error updating step indicator ${index}:`, e);
                    }
                });

                // Update current step input
                const currentStepInput = document.getElementById('current_step');
                if (currentStepInput) {
                    currentStepInput.value = currentStep;
                }

                // Initialize Step 3 if we're on that step
                if (currentStep === 3 && typeof window.initStep3 === 'function') {
                    window.initStep3();
                }
            } catch (error) {
                console.error('Error in safeUpdateUI:', error);
            }
        };

        // Override updateUI in all possible contexts
        try {
            // Define updateUI in the global scope
            window.updateUI = window.safeUpdateUI;

            // Also try to define it in the global scope without window
            try {
                updateUI = window.safeUpdateUI;
            } catch (e) {
                // This might fail in strict mode, which is fine
                console.warn('Could not define updateUI in global scope without window:', e);
            }

            // Override any existing updateUI function
            if (typeof window.originalUpdateUI !== 'function') {
                // Find all scripts in the document
                const scripts = document.querySelectorAll('script');

                // Look for inline scripts that might contain updateUI
                scripts.forEach(script => {
                    if (script.textContent && script.textContent.includes('updateUI')) {
                        console.log('Found updateUI in inline script, applying fix');

                        // Try to extract the original function
                        try {
                            const match = script.textContent.match(/function\s+updateUI\s*\([^)]*\)\s*\{[\s\S]*?\}/);
                            if (match) {
                                console.log('Extracted original updateUI function');
                                window.originalUpdateUI = new Function('return ' + match[0])();
                            }
                        } catch (e) {
                            console.warn('Error extracting original updateUI function:', e);
                        }
                    }
                });
            }
        } catch (e) {
            console.warn('Error overriding updateUI function:', e);
        }

        // Patch any existing updateUI calls
        try {
            // Find all elements that might trigger updateUI
            const buttons = document.querySelectorAll('button, .btn, [type="button"]');
            buttons.forEach(button => {
                if (button.onclick && button.onclick.toString().includes('updateUI')) {
                    console.log('Found button with updateUI in onclick, patching');
                    const originalOnclick = button.onclick;
                    button.onclick = function(event) {
                        try {
                            return originalOnclick.call(this, event);
                        } catch (e) {
                            console.warn('Error in patched onclick:', e);
                            // Try to call safeUpdateUI as a fallback
                            window.safeUpdateUI();
                            return false;
                        }
                    };
                }
            });
        } catch (e) {
            console.warn('Error patching updateUI calls:', e);
        }

        // Also try to intercept any future calls to updateUI
        try {
            // Create a proxy for window to intercept property access
            const windowProxy = new Proxy(window, {
                get: function(target, prop) {
                    if (prop === 'updateUI') {
                        return window.safeUpdateUI;
                    }
                    return target[prop];
                },
                set: function(target, prop, value) {
                    if (prop === 'updateUI') {
                        console.log('Intercepted attempt to set updateUI');
                        window.originalUpdateUI = value;
                        return true;
                    }
                    target[prop] = value;
                    return true;
                }
            });

            // Try to replace window with the proxy
            // This is a bit hacky and might not work in all browsers
            try {
                Object.defineProperty(window, 'window', {
                    get: function() {
                        return windowProxy;
                    }
                });
            } catch (e) {
                console.warn('Could not replace window with proxy:', e);
            }
        } catch (e) {
            console.warn('Error creating window proxy:', e);
        }
    } catch (error) {
        console.error('Error fixing updateUI function:', error);
    }
}

// Fix for addItem and addVendor functions
function fixAddItemAndVendorFunctions() {
    try {
        // Fix for "Invalid item data: undefined" error
        if (typeof window.addItem === 'function') {
            const originalAddItem = window.addItem;
            window.addItem = function(item) {
                if (!item) {
                    console.warn('Invalid item data provided to addItem, using default item');
                    item = {
                        id: 'default_item_' + Date.now(),
                        name: 'Default Item',
                        quantity: 1
                    };
                }
                return originalAddItem(item);
            };
        }

        // Fix for "Invalid vendor data: undefined" error
        if (typeof window.addVendor === 'function') {
            const originalAddVendor = window.addVendor;
            window.addVendor = function(vendor) {
                if (!vendor) {
                    console.warn('Invalid vendor data provided to addVendor, using default vendor');
                    vendor = {
                        id: 'default_vendor_' + Date.now(),
                        name: 'Default Vendor',
                        address: 'Default Address'
                    };
                }
                return originalAddVendor(vendor);
            };
        }
    } catch (error) {
        console.error('Error fixing addItem and addVendor functions:', error);
    }
}

// Fix for missing containers and templates
function fixMissingElements() {
    try {
        // Create missing containers if needed
        if (typeof window.createMissingContainers === 'function') {
            window.createMissingContainers();
        } else {
            console.warn('createMissingContainers function not found, creating basic containers');

            // Check for items container
            if (!document.getElementById('selected_items_list')) {
                console.log('Creating missing items container');

                // Create container
                const container = document.createElement('div');
                container.id = 'selected_items_container';
                container.className = 'mb-4';
                container.innerHTML = `
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Items</label>
                    <div id="selected_items_list" class="space-y-3">
                        <p id="no_items_message" class="text-gray-500 italic">No items selected yet</p>
                    </div>
                `;

                // Find a good place to add it
                const form = document.getElementById('procurementForm');
                if (form) {
                    form.appendChild(container);
                } else {
                    document.body.appendChild(container);
                }
            }

            // Check for vendors container
            if (!document.getElementById('selected_vendors_list')) {
                console.log('Creating missing vendors container');

                // Create container
                const container = document.createElement('div');
                container.id = 'selected_vendors_container';
                container.className = 'mb-4';
                container.innerHTML = `
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Vendors</label>
                    <div id="selected_vendors_list" class="space-y-2">
                        <p id="no_vendors_message" class="text-gray-500 italic">No vendors selected yet</p>
                    </div>
                `;

                // Find a good place to add it
                const form = document.getElementById('procurementForm');
                if (form) {
                    form.appendChild(container);
                } else {
                    document.body.appendChild(container);
                }
            }
        }
    } catch (error) {
        console.error('Error fixing missing elements:', error);
    }
}

// Fix for external script errors
function fixExternalScriptErrors() {
    try {
        // Fix for procurement-step3.js errors
        if (typeof window.initStep3 === 'function') {
            const originalInitStep3 = window.initStep3;
            window.initStep3 = function() {
                try {
                    return originalInitStep3();
                } catch (error) {
                    console.error('Error in initStep3:', error);
                    // Create a basic implementation as fallback
                    console.log('Using fallback implementation for initStep3');

                    // Create basic UI elements if needed
                    const step3 = document.getElementById('step3');
                    if (step3 && !step3.querySelector('.quotation-table')) {
                        step3.innerHTML = `
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Quotation Upload</h3>
                                <p class="text-sm text-gray-600">Upload quotations from vendors for the selected items.</p>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 quotation-table">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200" id="vendor-quotations-body">
                                        <tr>
                                            <td colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">No vendors selected yet</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        `;
                    }
                }
            };
        }
    } catch (error) {
        console.error('Error fixing external script errors:', error);
    }
}
