/**
 * Procurement Form Main JavaScript
 * Handles form navigation, validation, and submission
 */

// Initialize global variables with proper defaults
window.selectedItems = window.selectedItems || [];
window.selectedVendors = window.selectedVendors || [];
window.itemsData = window.itemsData || [];

// Create a comprehensive safe DOM utility to prevent null reference errors
window.safeDOM = {
    getElementById: function(id) {
        try {
            return document.getElementById(id);
        } catch (error) {
            console.error('Error getting element by ID:', id, error);
            return null;
        }
    },
    querySelector: function(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.error('Error querying selector:', selector, error);
            return null;
        }
    },
    querySelectorAll: function(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            console.error('Error querying all selector:', selector, error);
            return [];
        }
    },
    getValue: function(id) {
        try {
            const element = document.getElementById(id);
            return element ? element.value : '';
        } catch (error) {
            console.error('Error getting value for element:', id, error);
            return '';
        }
    },
    setValue: function(id, value) {
        try {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting value for element:', id, error);
            return false;
        }
    }
};

// For backward compatibility
window.safeGetElementById = window.safeDOM.getElementById;

// Define fallback functions for addItem and addVendor
if (typeof window.addItem !== 'function') {
    window.addItem = function(item) {
        console.log('Global fallback addItem function called');
        if (!item) return null;
        window.selectedItems.push(item);
        return item;
    };
}

if (typeof window.addVendor !== 'function') {
    window.addVendor = function(vendor) {
        console.log('Global fallback addVendor function called');
        if (!vendor) return null;
        window.selectedVendors.push(vendor);
        return vendor;
    };
}

// Add global error handler with line number information
window.addEventListener('error', function(event) {
    // Extract line number and file information if available
    let lineInfo = '';
    if (event.error && event.error.stack) {
        const stackLines = event.error.stack.split('\n');
        for (const line of stackLines) {
            if (line.includes('.js') && line.includes(':')) {
                const match = line.match(/\.js:(\d+):(\d+)/);
                if (match && match[1]) {
                    lineInfo = ` at line ${match[1]}`;
                }
                break;
            }
        }
    }

    console.error('Global error caught:', event.error || event.message, lineInfo);
    console.error('Error details:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });

    // Prevent the error from bubbling up
    event.preventDefault();
    return true;
});

// Function to safely add initial items and vendors if needed
function ensureInitialData() {
    // Make sure global arrays are initialized
    if (!window.selectedItems || !Array.isArray(window.selectedItems)) {
        window.selectedItems = [];
    }

    if (!window.selectedVendors || !Array.isArray(window.selectedVendors)) {
        window.selectedVendors = [];
    }

    // Add initial item if needed
    console.log('Adding initial item...');
    if (window.selectedItems.length === 0) {
        const initialItem = {
            id: 'initial_item_1',
            name: 'Sample Item',
            quantity: 1
        };

        try {
            if (typeof window.addItem === 'function') {
                window.addItem(initialItem);
            } else {
                window.selectedItems.push(initialItem);
                console.log('Added initial item directly to selectedItems array');
            }
        } catch (error) {
            console.log('Error adding initial item:', error);
            window.selectedItems.push(initialItem);
        }
    }

    // Add initial vendor if needed
    console.log('Adding initial vendor...');
    if (window.selectedVendors.length === 0) {
        const initialVendor = {
            id: 'initial_vendor_1',
            name: 'Sample Vendor',
            address: 'Sample Address'
        };

        try {
            if (typeof window.addVendor === 'function') {
                window.addVendor(initialVendor);
            } else {
                window.selectedVendors.push(initialVendor);
                console.log('Added initial vendor directly to selectedVendors array');
            }
        } catch (error) {
            console.log('Error adding initial vendor:', error);
            window.selectedVendors.push(initialVendor);
        }
    }
}

// Main initialization function
function initProcurementForm() {
    try {
        console.log('Procurement form initialization started');

        // Get form elements safely
        const form = window.safeDOM.getElementById('procurementForm');

        // Look for .form-step elements only to avoid confusion
        const steps = window.safeDOM.querySelectorAll('.form-step') || [];
        console.log(`Found ${steps.length} step elements`);

        // Look for both .step-indicator and .procurement-section-number elements
        // Also look for any elements with data-step attribute
        const stepIndicators = window.safeDOM.querySelectorAll('.step-indicator, .procurement-section-number, [data-step]') || [];
        console.log(`Found ${stepIndicators.length} step indicator elements`);

        // If no step indicators found, create virtual ones for internal tracking
        let virtualStepIndicators = [];
        if (stepIndicators.length === 0) {
            console.warn('No step indicators found, creating virtual ones for internal tracking');
            // Create virtual step indicators for the number of steps we have
            const numSteps = steps.length > 0 ? steps.length : 6;
            for (let i = 0; i < numSteps; i++) {
                virtualStepIndicators.push({
                    step: i + 1,
                    classList: {
                        add: function() {},
                        remove: function() {},
                        contains: function() { return false; }
                    }
                });
            }
        }

        const progressBar = window.safeDOM.getElementById('progress-bar');
        const currentStepInput = window.safeDOM.getElementById('current_step');

        // Get current step value safely
        let currentStep = 1;
        if (currentStepInput && currentStepInput.value) {
            currentStep = parseInt(currentStepInput.value) || 1;
        }

        // Get navigation buttons safely - try multiple possible IDs and classes
        const prevBtn = window.safeDOM.getElementById('prevBtn') ||
                        window.safeDOM.getElementById('prev-step') ||
                        window.safeDOM.getElementById('prev-btn') ||
                        window.safeDOM.querySelector('.prev-step') ||
                        window.safeDOM.querySelector('.prev-btn');

        const nextBtn = window.safeDOM.getElementById('nextBtn') ||
                        window.safeDOM.getElementById('next-step') ||
                        window.safeDOM.getElementById('next-btn') ||
                        window.safeDOM.querySelector('.next-step') ||
                        window.safeDOM.querySelector('.next-btn');

        const saveBtn = window.safeDOM.getElementById('saveBtn') ||
                        window.safeDOM.getElementById('save-draft-btn') ||
                        window.safeDOM.getElementById('topSaveBtn') ||
                        window.safeDOM.querySelector('.save-btn') ||
                        window.safeDOM.querySelector('.save-draft-btn');

        const submitBtn = window.safeDOM.getElementById('submitBtn') ||
                          window.safeDOM.getElementById('submit-btn') ||
                          window.safeDOM.querySelector('.submit-btn');

        // Update UI based on current step
        function updateUI() {
            try {
                // Try different step element patterns - prioritize step1 for first step
                let currentStepElement = null;

                if (currentStep === 1) {
                    // For step 1, explicitly look for step1 first
                    currentStepElement = window.safeDOM.getElementById('step1');
                    console.log('Looking for step1 element:', currentStepElement ? 'found' : 'not found');
                }

                // If not found and not step 1, or if step 1 but element not found, try the normal pattern
                if (!currentStepElement) {
                    currentStepElement = window.safeDOM.getElementById(`step${currentStep}`);
                    console.log(`Looking for step${currentStep} element:`, currentStepElement ? 'found' : 'not found');
                }

                // If still not found, try section pattern as fallback
                if (!currentStepElement) {
                    currentStepElement = window.safeDOM.getElementById(`section${currentStep}`);
                    console.log(`Looking for section${currentStep} element:`, currentStepElement ? 'found' : 'not found');
                }

                // If we found a specific step element, show it and hide others
                if (currentStepElement) {
                    // Hide all steps
                    if (steps && steps.length > 0) {
                        steps.forEach(step => {
                            if (step) step.classList.add('hidden');
                        });
                    }

                    // Show current step
                    currentStepElement.classList.remove('hidden');
                    console.log(`Showing step ${currentStep}: ${currentStepElement.id}`);
                } else {
                    console.warn(`Step element for step ${currentStep} not found`);

                    // If we're on step 1 and can't find any specific element, show the first form-step
                    if (currentStep === 1 && steps && steps.length > 0) {
                        const firstStep = steps[0];
                        if (firstStep) {
                            console.log('Showing first available step as fallback:', firstStep.id);
                            steps.forEach(step => {
                                if (step) step.classList.add('hidden');
                            });
                            firstStep.classList.remove('hidden');
                            return;
                        }
                    }

                    // Try to show the first step if no specific step is found
                    if (steps && steps.length > 0 && steps[0]) {
                        steps.forEach((step, index) => {
                            if (step) {
                                if (index === 0) {
                                    step.classList.remove('hidden');
                                    console.log(`Showing first available step: ${step.id}`);
                                } else {
                                    step.classList.add('hidden');
                                }
                            }
                        });
                    }
                }

                // Update step indicators if they exist
                if (stepIndicators && stepIndicators.length > 0) {
                    console.log(`Updating ${stepIndicators.length} step indicators for current step ${currentStep}`);
                    stepIndicators.forEach((indicator, index) => {
                        if (!indicator) return;

                        const step = index + 1;

                        try {
                            if (step < currentStep) {
                                // Completed step
                                indicator.classList.add('completed');
                                indicator.classList.remove('active');
                            } else if (step === currentStep) {
                                // Current step
                                indicator.classList.add('active');
                                indicator.classList.remove('completed');
                            } else {
                                // Future step
                                indicator.classList.remove('active', 'completed');
                            }
                        } catch (e) {
                            console.warn(`Error updating step indicator ${index}:`, e);
                        }
                    });
                } else if (virtualStepIndicators && virtualStepIndicators.length > 0) {
                    // Use virtual step indicators
                    console.log(`Using ${virtualStepIndicators.length} virtual step indicators for current step ${currentStep}`);
                } else {
                    console.warn('No step indicators found to update');
                }

                // Update progress bar if it exists
                if (progressBar) {
                    try {
                        const totalSteps = 6; // Fixed number of steps
                        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
                        console.log(`Setting progress bar width to ${progress}%`);
                        progressBar.style.width = `${progress}%`;
                    } catch (e) {
                        console.warn('Error updating progress bar:', e);
                    }
                } else {
                    console.warn('Progress bar element not found');
                }

                // Update buttons if they exist
                if (prevBtn) {
                    try {
                        if (currentStep === 1) {
                            prevBtn.classList.add('hidden');
                        } else {
                            prevBtn.classList.remove('hidden');
                        }
                    } catch (e) {
                        console.warn('Error updating prev button:', e);
                    }
                }

                if (nextBtn) {
                    try {
                        const totalSteps = steps.length || 6;
                        if (currentStep === totalSteps && submitBtn) {
                            nextBtn.classList.add('hidden');
                            submitBtn.classList.remove('hidden');
                        } else {
                            nextBtn.classList.remove('hidden');
                            if (submitBtn) submitBtn.classList.add('hidden');
                        }
                    } catch (e) {
                        console.warn('Error updating next/submit buttons:', e);
                    }
                }

                // Update current step input
                if (currentStepInput) {
                    currentStepInput.value = currentStep;
                }

                // Initialize Step 3 if we're on that step
                if (currentStep === 3 && typeof window.initStep3 === 'function') {
                    console.log('Current step is 3, calling window.initStep3()...');
                    window.initStep3();
                }
            } catch (error) {
                console.error('Error updating UI:', error);
            }
        }

        // Initialize UI
        updateUI();

        // Add event listeners to buttons if they exist
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                if (typeof validateStep === 'function' && validateStep()) {
                    // Special handling for step transitions
                    if (currentStep === 3) {
                        // When moving from Step 3 to Step 4, explicitly capture the quotation data
                        if (typeof window.captureQuotationPricesForComparative === 'function') {
                            window.captureQuotationPricesForComparative();
                        }
                    }

                    currentStep++;
                    updateUI();
                    window.scrollTo(0, 0);
                }
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                // Make sure we don't go below step 1
                if (currentStep > 1) {
                    currentStep--;
                    updateUI();
                    window.scrollTo(0, 0);
                    console.log(`Navigated to previous step: ${currentStep}`);
                } else {
                    console.warn('Already at first step, cannot go back further');
                }
            });
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                if (typeof validateAndSaveDraft === 'function') {
                    validateAndSaveDraft();
                } else if (typeof saveDraft === 'function') {
                    saveDraft();
                } else {
                    console.error('No save draft function available');
                }
            });
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', function() {
                if (typeof validateStep === 'function' && validateStep()) {
                    if (typeof submitForm === 'function') {
                        submitForm();
                    } else {
                        console.error('submitForm function is not defined');
                    }
                }
            });
        }

        console.log('Procurement form initialized successfully');
    } catch (error) {
        console.error('Error in procurement form initialization:', error);
    }
}

// Initialize when document is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure initial data is available
        ensureInitialData();
        // Initialize the form
        initProcurementForm();
    });
} else {
    // Document already loaded
    ensureInitialData();
    initProcurementForm();
}
