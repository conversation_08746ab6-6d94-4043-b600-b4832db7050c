/**
 * Procurement Form Quotation Functions
 * Handles quotation data capture and comparative analysis
 */

// Initialize quotation data structure
window.quotationData = window.quotationData || {};

// Capture quotation prices for comparative analysis
window.captureQuotationPricesForComparative = function() {
    console.log('Capturing quotation prices for comparative analysis...');

    // Data structure to hold vendor, item, and price information
    const capturedData = {
        vendors: [...window.selectedVendors],
        items: [...window.selectedItems],
        prices: {}
    };

    // Initialize prices structure
    capturedData.items.forEach(item => {
        if (item && item.id) {
            capturedData.prices[item.id] = {};
        }
    });

    // Capture price data from quotationData
    if (window.quotationData) {
        for (const vendorId in window.quotationData) {
            if (window.quotationData[vendorId] && window.quotationData[vendorId].items) {
                for (const itemId in window.quotationData[vendorId].items) {
                    const priceData = window.quotationData[vendorId].items[itemId];
                    if (!priceData) continue;

                    if (!capturedData.prices[itemId]) {
                        capturedData.prices[itemId] = {};
                    }

                    capturedData.prices[itemId][vendorId] = {
                        unitPrice: priceData.unitPrice || 0,
                        totalPrice: priceData.totalPrice || 0
                    };
                }
            }
        }
    }

    // Store globally
    window.capturedQuotationData = capturedData;

    // Try to store in localStorage as a backup
    try {
        if (typeof localStorage !== 'undefined' && localStorage !== null) {
            localStorage.setItem('STEP3_PRICE_DATA', JSON.stringify(capturedData));
        }
    } catch (e) {
        console.warn('Error storing quotation data in localStorage:', e);
    }

    // Dispatch event to notify other components
    try {
        const event = new CustomEvent('quotationDataUpdated', { detail: capturedData });
        document.dispatchEvent(event);
    } catch (e) {
        console.warn('Error dispatching quotationDataUpdated event:', e);
    }

    return capturedData;
};

// Get quotation data for comparative analysis
window.getQuotationDataForComparative = function() {
    // Try to get from memory first
    if (window.capturedQuotationData) {
        return window.capturedQuotationData;
    }

    // Try to get from localStorage
    try {
        if (typeof localStorage !== 'undefined' && localStorage !== null) {
            const storedData = localStorage.getItem('STEP3_PRICE_DATA');
            if (storedData) {
                window.capturedQuotationData = JSON.parse(storedData);
                return window.capturedQuotationData;
            }
        }
    } catch (e) {
        console.warn('Error retrieving quotation data from localStorage:', e);
    }

    // Capture fresh data if nothing is available
    return window.captureQuotationPricesForComparative();
};

// Update quotation data for a vendor and item
window.updateQuotationData = function(vendorId, itemId, unitPrice, totalPrice) {
    if (!vendorId || !itemId) {
        console.error('Invalid vendor or item ID for quotation data update');
        return false;
    }

    // Initialize vendor data if not exists
    if (!window.quotationData[vendorId]) {
        window.quotationData[vendorId] = {
            items: {}
        };
    }

    // Update item price data
    window.quotationData[vendorId].items[itemId] = {
        unitPrice: parseFloat(unitPrice) || 0,
        totalPrice: parseFloat(totalPrice) || 0
    };

    // Update captured data
    window.captureQuotationPricesForComparative();

    return true;
};

// Initialize quotation details modal
function setupQuotationDetailsModal() {
    try {
        console.log('Setting up quotation details modal...');

        // Get modal elements
        const modal = window.safeDOM.getElementById('quotation-details-modal');
        const closeBtn = window.safeDOM.getElementById('close-quotation-modal');
        const saveBtn = window.safeDOM.getElementById('save-quotation-details');
        const vendorNameElement = window.safeDOM.getElementById('modal-vendor-name');
        const quotationDateInput = window.safeDOM.getElementById('modal-quotation-date');
        const quotationRefInput = window.safeDOM.getElementById('modal-quotation-reference');
        const fileInput = window.safeDOM.getElementById('modal-quotation-file');
        const filePreview = window.safeDOM.getElementById('modal-quotation-preview');
        const fileNameElement = window.safeDOM.getElementById('modal-quotation-filename');
        const removeFileBtn = window.safeDOM.getElementById('modal-remove-quotation');
        const itemPricesContainer = window.safeDOM.getElementById('modal-item-prices');

        // Current vendor being edited
        let currentVendorId = null;

        // Close modal function
        function closeModal() {
            if (modal) modal.classList.add('hidden');
            currentVendorId = null;
        }

        // Add event listeners
        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }

        // File upload handling
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    
                    // Update preview
                    if (fileNameElement) fileNameElement.textContent = file.name;
                    if (filePreview) filePreview.classList.remove('hidden');
                }
            });
        }

        // Remove file button
        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', function() {
                if (fileInput) fileInput.value = '';
                if (filePreview) filePreview.classList.add('hidden');
            });
        }

        // Save button
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                if (!currentVendorId) {
                    console.error('No vendor ID set for quotation details');
                    return;
                }

                // Get form values
                const quotationDate = quotationDateInput ? quotationDateInput.value : '';
                const quotationRef = quotationRefInput ? quotationRefInput.value : '';
                
                // Validate required fields
                if (!quotationDate) {
                    alert('Please enter a quotation date');
                    return;
                }

                if (fileInput && !fileInput.files[0] && !window.quotationData[currentVendorId]?.fileUploaded) {
                    alert('Please upload a quotation file');
                    return;
                }

                // Update quotation data
                if (!window.quotationData[currentVendorId]) {
                    window.quotationData[currentVendorId] = {
                        items: {}
                    };
                }

                window.quotationData[currentVendorId].date = quotationDate;
                window.quotationData[currentVendorId].reference = quotationRef;
                
                // Mark as uploaded if file is selected
                if (fileInput && fileInput.files[0]) {
                    window.quotationData[currentVendorId].fileUploaded = true;
                }

                // Get item prices
                const itemPriceInputs = document.querySelectorAll('.item-price-input');
                if (itemPriceInputs && itemPriceInputs.length > 0) {
                    itemPriceInputs.forEach(input => {
                        const itemId = input.getAttribute('data-item-id');
                        if (!itemId) return;

                        const unitPrice = parseFloat(input.value) || 0;
                        const quantity = parseInt(input.getAttribute('data-quantity')) || 1;
                        const totalPrice = unitPrice * quantity;

                        // Update quotation data
                        window.updateQuotationData(currentVendorId, itemId, unitPrice, totalPrice);
                    });
                }

                // Update UI
                updateVendorQuotationsTable();

                // Close modal
                closeModal();
            });
        }

        // Expose function to open modal for a vendor
        window.openQuotationDetailsModal = function(vendorId) {
            if (!vendorId) {
                console.error('No vendor ID provided for quotation details modal');
                return;
            }

            // Set current vendor
            currentVendorId = vendorId;

            // Find vendor data
            const vendor = window.selectedVendors.find(v => v.id === vendorId);
            if (!vendor) {
                console.error('Vendor not found:', vendorId);
                return;
            }

            // Update modal title
            if (vendorNameElement) {
                vendorNameElement.textContent = `Quotation Details: ${vendor.name}`;
            }

            // Set existing values if available
            const vendorData = window.quotationData[vendorId] || {};
            
            if (quotationDateInput) {
                quotationDateInput.value = vendorData.date || '';
            }
            
            if (quotationRefInput) {
                quotationRefInput.value = vendorData.reference || '';
            }

            // Reset file input
            if (fileInput) fileInput.value = '';
            if (filePreview) filePreview.classList.add('hidden');

            // Show file preview if already uploaded
            if (vendorData.fileUploaded && filePreview) {
                filePreview.classList.remove('hidden');
                if (fileNameElement) fileNameElement.textContent = 'Quotation file uploaded';
            }

            // Populate item prices
            if (itemPricesContainer) {
                itemPricesContainer.innerHTML = '';

                // Add each item
                window.selectedItems.forEach(item => {
                    if (!item || !item.id) return;

                    const itemPrice = vendorData.items && vendorData.items[item.id] ? 
                        vendorData.items[item.id].unitPrice : '';

                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'bg-gray-50 p-3 rounded-md';
                    itemDiv.innerHTML = `
                        <div class="font-medium text-gray-800 mb-1">${item.name}</div>
                        <div class="flex items-center">
                            <label class="block text-sm font-medium text-gray-700 mr-2">Unit Price (₹):</label>
                            <input type="number" class="item-price-input w-32 rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" 
                                data-item-id="${item.id}" 
                                data-quantity="${item.quantity || 1}" 
                                value="${itemPrice}" 
                                min="0" 
                                step="0.01">
                        </div>
                    `;

                    itemPricesContainer.appendChild(itemDiv);
                });
            }

            // Show modal
            if (modal) modal.classList.remove('hidden');
        };
    } catch (error) {
        console.error('Error setting up quotation details modal:', error);
    }
}
