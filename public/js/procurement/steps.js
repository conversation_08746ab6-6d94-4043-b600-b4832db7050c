/**
 * Procurement Form Step-Specific Functions
 * Handles functionality for each step of the procurement form
 */

// Step 1: Request Letter
function initStep1() {
    try {
        console.log('Initializing Step 1...');

        // File upload handling
        const requestLetterInput = window.safeDOM.getElementById('request_letter');
        const requestLetterPreview = window.safeDOM.getElementById('request_letter_preview');
        const requestLetterName = window.safeDOM.getElementById('request_letter_name');
        const removeRequestLetter = window.safeDOM.getElementById('remove_request_letter');

        if (requestLetterInput) {
            requestLetterInput.addEventListener('change', function(e) {
                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    // Update preview
                    if (requestLetterName) requestLetterName.textContent = file.name;
                    if (requestLetterPreview) requestLetterPreview.classList.remove('hidden');
                }
            });
        }

        if (removeRequestLetter) {
            removeRequestLetter.addEventListener('click', function() {
                if (requestLetterInput) requestLetterInput.value = '';
                if (requestLetterPreview) requestLetterPreview.classList.add('hidden');
            });
        }
    } catch (error) {
        console.error('Error initializing Step 1:', error);
    }
}

// Create missing containers if needed
function createMissingContainers() {
    console.log('Checking for missing containers...');

    // Check for items container
    if (!document.getElementById('selected_items_list')) {
        console.log('Creating missing items container');

        // Create container
        const container = document.createElement('div');
        container.id = 'selected_items_container';
        container.className = 'mb-4';
        container.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-2">Selected Items</label>
            <div id="selected_items_list" class="space-y-3">
                <p id="no_items_message" class="text-gray-500 italic">No items selected yet</p>
            </div>
        `;

        // Find a good place to add it
        const form = document.getElementById('procurementForm');
        if (form) {
            form.appendChild(container);
        } else {
            document.body.appendChild(container);
        }
    }

    // Check for vendors container
    if (!document.getElementById('selected_vendors_list')) {
        console.log('Creating missing vendors container');

        // Create container
        const container = document.createElement('div');
        container.id = 'selected_vendors_container';
        container.className = 'mb-4';
        container.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-2">Selected Vendors</label>
            <div id="selected_vendors_list" class="space-y-2">
                <p id="no_vendors_message" class="text-gray-500 italic">No vendors selected yet</p>
            </div>
        `;

        // Find a good place to add it
        const form = document.getElementById('procurementForm');
        if (form) {
            form.appendChild(container);
        } else {
            document.body.appendChild(container);
        }
    }

    // Check for item template
    if (!document.getElementById('selected-item-template')) {
        console.log('Creating missing item template');

        // Create template
        const template = document.createElement('template');
        template.id = 'selected-item-template';
        template.innerHTML = `
            <div class="selected-item bg-white p-3 rounded-md border border-gray-200">
                <div class="flex justify-between items-center mb-2">
                    <div class="font-medium text-gray-800 item-name"></div>
                    <button type="button" class="remove-item text-red-500 hover:text-red-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Quantity <span class="text-red-500">*</span></label>
                        <input type="number" name="item_quantities[]" class="item-quantity w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" min="1" value="1" required>
                        <input type="hidden" name="item_ids[]" class="item-id">
                        <input type="hidden" name="item_names[]" class="item-name-input">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Specifications</label>
                        <textarea name="item_specifications[]" rows="2" class="item-specifications w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Enter specifications for this item"></textarea>
                    </div>
                </div>
            </div>
        `;

        // Add to document
        document.body.appendChild(template);
    }

    // Check for vendor template
    if (!document.getElementById('selected-vendor-template')) {
        console.log('Creating missing vendor template');

        // Create template
        const template = document.createElement('template');
        template.id = 'selected-vendor-template';
        template.innerHTML = `
            <div class="selected-vendor bg-white p-3 rounded-md border border-gray-200 flex justify-between items-start">
                <div>
                    <div class="font-medium text-gray-800 vendor-name"></div>
                    <div class="text-sm text-gray-500 vendor-address"></div>
                    <input type="hidden" name="selected_vendors[]" class="vendor-id">
                    <input type="hidden" name="vendor_names[]" class="vendor-name-input">
                    <input type="hidden" name="vendor_addresses[]" class="vendor-address-input">
                </div>
                <button type="button" class="remove-vendor text-red-500 hover:text-red-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to document
        document.body.appendChild(template);
    }
}

// Step 2: Call Quotations
function initStep2() {
    try {
        console.log('Initializing Step 2...');

        // Create missing containers if needed
        createMissingContainers();

        // Item search and selection
        const itemSearch = window.safeDOM.getElementById('item_search');
        const itemSearchResults = window.safeDOM.getElementById('item_search_results');
        const addItemBtn = window.safeDOM.getElementById('add_item_btn');
        const selectedItemsList = window.safeDOM.getElementById('selected_items_list');
        const noItemsMessage = window.safeDOM.getElementById('no_items_message');

        // Vendor search and selection
        const vendorSearch = window.safeDOM.getElementById('vendor_search');
        const vendorSearchResults = window.safeDOM.getElementById('vendor_search_results');
        const addVendorBtn = window.safeDOM.getElementById('add_vendor_btn');
        const selectedVendorsList = window.safeDOM.getElementById('selected_vendors_list');
        const noVendorsMessage = window.safeDOM.getElementById('no_vendors_message');

        // Item search functionality
        if (itemSearch) {
            itemSearch.addEventListener('focus', function() {
                if (itemSearchResults) itemSearchResults.classList.remove('hidden');
            });

            itemSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                // Filter item results
                const itemResults = document.querySelectorAll('.item-result');
                if (itemResults && itemResults.length > 0) {
                    itemResults.forEach(result => {
                        const itemName = result.textContent.toLowerCase();
                        if (itemName.includes(searchTerm)) {
                            result.classList.remove('hidden');
                        } else {
                            result.classList.add('hidden');
                        }
                    });
                }
            });

            // Handle clicking outside search results
            document.addEventListener('click', function(e) {
                if (e.target !== itemSearch && e.target !== addItemBtn && !itemSearchResults.contains(e.target)) {
                    itemSearchResults.classList.add('hidden');
                }
            });
        }

        // Item selection
        const itemResults = document.querySelectorAll('.item-result');
        if (itemResults && itemResults.length > 0) {
            itemResults.forEach(result => {
                result.addEventListener('click', function() {
                    const itemId = this.getAttribute('data-item-id');
                    const itemName = this.getAttribute('data-item-name');

                    addItem({
                        id: itemId,
                        name: itemName
                    });

                    // Hide search results
                    if (itemSearchResults) itemSearchResults.classList.add('hidden');
                    if (itemSearch) itemSearch.value = '';
                });
            });
        }

        // Add item button
        if (addItemBtn) {
            addItemBtn.addEventListener('click', function() {
                const searchTerm = itemSearch ? itemSearch.value.trim() : '';
                if (searchTerm) {
                    addItem({
                        id: 'custom_' + Date.now(),
                        name: searchTerm
                    });

                    // Clear search
                    if (itemSearch) itemSearch.value = '';
                }
            });
        }

        // Vendor search functionality
        if (vendorSearch) {
            vendorSearch.addEventListener('focus', function() {
                if (vendorSearchResults) vendorSearchResults.classList.remove('hidden');
            });

            vendorSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                // Filter vendor results
                const vendorResults = document.querySelectorAll('.vendor-result');
                if (vendorResults && vendorResults.length > 0) {
                    vendorResults.forEach(result => {
                        const vendorName = result.textContent.toLowerCase();
                        if (vendorName.includes(searchTerm)) {
                            result.classList.remove('hidden');
                        } else {
                            result.classList.add('hidden');
                        }
                    });
                }
            });

            // Handle clicking outside search results
            document.addEventListener('click', function(e) {
                if (e.target !== vendorSearch && e.target !== addVendorBtn && !vendorSearchResults.contains(e.target)) {
                    vendorSearchResults.classList.add('hidden');
                }
            });
        }

        // Vendor selection
        const vendorResults = document.querySelectorAll('.vendor-result');
        if (vendorResults && vendorResults.length > 0) {
            vendorResults.forEach(result => {
                result.addEventListener('click', function() {
                    const vendorId = this.getAttribute('data-vendor-id');
                    const vendorName = this.getAttribute('data-vendor-name');
                    const vendorAddress = this.getAttribute('data-vendor-address');

                    addVendor({
                        id: vendorId,
                        name: vendorName,
                        address: vendorAddress
                    });

                    // Hide search results
                    if (vendorSearchResults) vendorSearchResults.classList.add('hidden');
                    if (vendorSearch) vendorSearch.value = '';
                });
            });
        }

        // Add vendor button
        if (addVendorBtn) {
            addVendorBtn.addEventListener('click', function() {
                const searchTerm = vendorSearch ? vendorSearch.value.trim() : '';
                if (searchTerm) {
                    addVendor({
                        id: 'custom_' + Date.now(),
                        name: searchTerm,
                        address: ''
                    });

                    // Clear search
                    if (vendorSearch) vendorSearch.value = '';
                }
            });
        }

        // Update quotation preview
        updateQuotationPreview();

        // Terms and conditions preview
        const termsInput = window.safeDOM.getElementById('terms_conditions');
        if (termsInput) {
            termsInput.addEventListener('input', updateTermsPreview);
            // Initial update
            updateTermsPreview();
        }

        // Buyer information preview
        const buyerNameInput = window.safeDOM.getElementById('buyer_name');
        const buyerDesignationInput = window.safeDOM.getElementById('buyer_designation');

        if (buyerNameInput) {
            buyerNameInput.addEventListener('input', function() {
                const buyerNamePreview = window.safeDOM.getElementById('buyer-name-preview');
                if (buyerNamePreview) buyerNamePreview.textContent = this.value || 'Principal';
            });
        }

        if (buyerDesignationInput) {
            buyerDesignationInput.addEventListener('input', function() {
                const buyerDesignationPreview = window.safeDOM.getElementById('buyer-designation-preview');
                if (buyerDesignationPreview) buyerDesignationPreview.textContent = this.value || 'Principal';
            });
        }

        // Generate PDF button
        const generatePdfBtn = window.safeDOM.getElementById('generate-quotation-pdf');
        if (generatePdfBtn) {
            generatePdfBtn.addEventListener('click', generateQuotationPdf);
        }

        // PDF modal buttons
        const downloadPdfBtn = window.safeDOM.getElementById('download-pdf-btn');
        const openPdfBtn = window.safeDOM.getElementById('open-pdf-btn');
        const closePdfModalBtn = window.safeDOM.getElementById('close-pdf-modal-btn');
        const pdfOptionsModal = window.safeDOM.getElementById('pdf-options-modal');

        if (downloadPdfBtn) {
            downloadPdfBtn.addEventListener('click', function() {
                // Download logic
                if (pdfOptionsModal) pdfOptionsModal.classList.add('hidden');
            });
        }

        if (openPdfBtn) {
            openPdfBtn.addEventListener('click', function() {
                // Open in browser logic
                if (pdfOptionsModal) pdfOptionsModal.classList.add('hidden');
            });
        }

        if (closePdfModalBtn) {
            closePdfModalBtn.addEventListener('click', function() {
                if (pdfOptionsModal) pdfOptionsModal.classList.add('hidden');
            });
        }
    } catch (error) {
        console.error('Error initializing Step 2:', error);
    }
}

// Step 3: Quotation Upload
function initStep3() {
    try {
        console.log('Initializing Step 3...');

        // Populate vendor quotations table
        populateVendorQuotationsTable();

        // Set up quotation details modal
        setupQuotationDetailsModal();
    } catch (error) {
        console.error('Error initializing Step 3:', error);
    }
}

// Helper functions
function addItem(item) {
    try {
        if (!item || !item.id || !item.name) {
            console.error('Invalid item data:', item);
            return;
        }

        // Check if item already exists
        const existingItemIndex = window.selectedItems.findIndex(i => i.id === item.id);
        if (existingItemIndex >= 0) {
            console.log('Item already exists:', item);
            return;
        }

        // Add to global array
        window.selectedItems.push(item);

        // Update UI
        updateSelectedItemsList();
        if (typeof updateQuotationPreview === 'function') {
            updateQuotationPreview();
        }

        console.log('Item added:', item);
    } catch (error) {
        console.error('Error adding item:', error);
    }
}

function addVendor(vendor) {
    try {
        if (!vendor || !vendor.id || !vendor.name) {
            console.error('Invalid vendor data:', vendor);
            return;
        }

        // Check if vendor already exists
        const existingVendorIndex = window.selectedVendors.findIndex(v => v.id === vendor.id);
        if (existingVendorIndex >= 0) {
            console.log('Vendor already exists:', vendor);
            return;
        }

        // Add to global array
        window.selectedVendors.push(vendor);

        // Update UI
        updateSelectedVendorsList();
        if (typeof updateQuotationPreview === 'function') {
            updateQuotationPreview();
        }

        console.log('Vendor added:', vendor);
    } catch (error) {
        console.error('Error adding vendor:', error);
    }
}

// Function to update the selected items list in the UI
function updateSelectedItemsList() {
    try {
        const selectedItemsList = window.safeDOM.getElementById('selected_items_list');
        const noItemsMessage = window.safeDOM.getElementById('no_items_message');

        if (!selectedItemsList) {
            console.warn('Selected items list container not found');
            return;
        }

        // Clear existing items except the no items message
        const existingItems = selectedItemsList.querySelectorAll('.selected-item');
        existingItems.forEach(item => item.remove());

        // Show/hide no items message
        if (window.selectedItems.length === 0) {
            if (noItemsMessage) {
                noItemsMessage.textContent = 'No items selected yet';
                noItemsMessage.classList.remove('hidden');
                noItemsMessage.classList.remove('text-red-500');
                noItemsMessage.classList.add('text-gray-500', 'italic');
            }
            return;
        } else if (noItemsMessage) {
            noItemsMessage.classList.add('hidden');
        }

        // Get the template
        const template = document.getElementById('selected-item-template');
        if (!template) {
            console.error('Selected item template not found');
            return;
        }

        // Add each item
        window.selectedItems.forEach(item => {
            // Clone the template
            const clone = document.importNode(template.content, true);

            // Set item data
            const nameElement = clone.querySelector('.item-name');
            if (nameElement) nameElement.textContent = item.name;

            const idInput = clone.querySelector('.item-id');
            if (idInput) idInput.value = item.id;

            const nameInput = clone.querySelector('.item-name-input');
            if (nameInput) nameInput.value = item.name;

            const quantityInput = clone.querySelector('.item-quantity');
            if (quantityInput && item.quantity) quantityInput.value = item.quantity;

            const specificationsInput = clone.querySelector('.item-specifications');
            if (specificationsInput && item.specifications) specificationsInput.value = item.specifications;

            // Add remove button event listener
            const removeBtn = clone.querySelector('.remove-item');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    // Remove from array
                    const index = window.selectedItems.findIndex(i => i.id === item.id);
                    if (index >= 0) {
                        window.selectedItems.splice(index, 1);

                        // Update UI
                        updateSelectedItemsList();
                        if (typeof updateQuotationPreview === 'function') {
                            updateQuotationPreview();
                        }
                    }
                });
            }

            // Add to the list
            selectedItemsList.appendChild(clone);
        });
    } catch (error) {
        console.error('Error updating selected items list:', error);
    }
}

// Function to update the selected vendors list in the UI
function updateSelectedVendorsList() {
    try {
        const selectedVendorsList = window.safeDOM.getElementById('selected_vendors_list');
        const noVendorsMessage = window.safeDOM.getElementById('no_vendors_message');

        if (!selectedVendorsList) {
            console.warn('Selected vendors list container not found');
            return;
        }

        // Clear existing vendors except the no vendors message
        const existingVendors = selectedVendorsList.querySelectorAll('.selected-vendor');
        existingVendors.forEach(vendor => vendor.remove());

        // Show/hide no vendors message
        if (window.selectedVendors.length === 0) {
            if (noVendorsMessage) {
                noVendorsMessage.textContent = 'No vendors selected yet';
                noVendorsMessage.classList.remove('hidden');
                noVendorsMessage.classList.remove('text-red-500');
                noVendorsMessage.classList.add('text-gray-500', 'italic');
            }
            return;
        } else if (noVendorsMessage) {
            noVendorsMessage.classList.add('hidden');
        }

        // Get the template
        const template = document.getElementById('selected-vendor-template');
        if (!template) {
            console.error('Selected vendor template not found');
            return;
        }

        // Add each vendor
        window.selectedVendors.forEach(vendor => {
            // Clone the template
            const clone = document.importNode(template.content, true);

            // Set vendor data
            const nameElement = clone.querySelector('.vendor-name');
            if (nameElement) nameElement.textContent = vendor.name;

            const addressElement = clone.querySelector('.vendor-address');
            if (addressElement) addressElement.textContent = vendor.address || '';

            const idInput = clone.querySelector('.vendor-id');
            if (idInput) idInput.value = vendor.id;

            const nameInput = clone.querySelector('.vendor-name-input');
            if (nameInput) nameInput.value = vendor.name;

            const addressInput = clone.querySelector('.vendor-address-input');
            if (addressInput) addressInput.value = vendor.address || '';

            // Add remove button event listener
            const removeBtn = clone.querySelector('.remove-vendor');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    // Remove from array
                    const index = window.selectedVendors.findIndex(v => v.id === vendor.id);
                    if (index >= 0) {
                        window.selectedVendors.splice(index, 1);

                        // Update UI
                        updateSelectedVendorsList();
                        if (typeof updateQuotationPreview === 'function') {
                            updateQuotationPreview();
                        }
                    }
                });
            }

            // Add to the list
            selectedVendorsList.appendChild(clone);
        });
    } catch (error) {
        console.error('Error updating selected vendors list:', error);
    }
}

// Function to update the quotation preview
function updateQuotationPreview() {
    try {
        console.log('Updating quotation preview...');

        // Update subject
        const subjectInput = window.safeDOM.getElementById('quotation_subject');
        const subjectPreview = window.safeDOM.getElementById('quotation-subject-preview');

        if (subjectInput && subjectPreview) {
            subjectPreview.textContent = `Subject: ${subjectInput.value || 'Request for Quotation'}`;
        }

        // Update vendors list
        const vendorsPreviewList = window.safeDOM.getElementById('vendors-preview-list');
        if (vendorsPreviewList) {
            if (window.selectedVendors.length === 0) {
                vendorsPreviewList.innerHTML = '<p class="text-gray-600 italic">No vendors selected yet</p>';
            } else {
                vendorsPreviewList.innerHTML = '';
                window.selectedVendors.forEach(vendor => {
                    const vendorDiv = document.createElement('div');
                    vendorDiv.className = 'mb-2';
                    vendorDiv.innerHTML = `
                        <p class="font-medium">${vendor.name}</p>
                        <p class="text-sm text-gray-600">${vendor.address || ''}</p>
                    `;
                    vendorsPreviewList.appendChild(vendorDiv);
                });
            }
        }

        // Update items table
        const itemsPreview = window.safeDOM.getElementById('quotation-items-preview');
        if (itemsPreview) {
            if (window.selectedItems.length === 0) {
                itemsPreview.innerHTML = '<tr><td colspan="5" class="px-4 py-3 text-center text-gray-500 italic">Select items to see preview</td></tr>';
            } else {
                itemsPreview.innerHTML = '';
                window.selectedItems.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-4 py-3 border-r">${index + 1}</td>
                        <td class="px-4 py-3 border-r">${item.name}</td>
                        <td class="px-4 py-3 border-r">${item.specifications || ''}</td>
                        <td class="px-4 py-3 border-r">${item.quantity || 1}</td>
                        <td class="px-4 py-3"></td>
                    `;
                    itemsPreview.appendChild(row);
                });
            }
        }

        // Update terms and conditions
        updateTermsPreview();

        // Update buyer information
        const buyerNameInput = window.safeDOM.getElementById('buyer_name');
        const buyerNamePreview = window.safeDOM.getElementById('buyer-name-preview');

        if (buyerNameInput && buyerNamePreview) {
            buyerNamePreview.textContent = buyerNameInput.value || 'Principal';
        }

        const buyerDesignationInput = window.safeDOM.getElementById('buyer_designation');
        const buyerDesignationPreview = window.safeDOM.getElementById('buyer-designation-preview');

        if (buyerDesignationInput && buyerDesignationPreview) {
            buyerDesignationPreview.textContent = buyerDesignationInput.value || 'Principal';
        }
    } catch (error) {
        console.error('Error updating quotation preview:', error);
    }
}

// Function to update terms preview
function updateTermsPreview() {
    try {
        const termsInput = window.safeDOM.getElementById('terms_conditions');
        const termsPreview = window.safeDOM.getElementById('terms-preview');

        if (!termsInput || !termsPreview) {
            console.warn('Terms input or preview element not found');
            return;
        }

        // Split terms by newline and create list items
        const terms = termsInput.value.split('\n').filter(term => term.trim());

        if (terms.length === 0) {
            termsPreview.innerHTML = '<p class="text-gray-500 italic">No terms and conditions specified</p>';
            return;
        }

        termsPreview.innerHTML = '';
        terms.forEach(term => {
            const p = document.createElement('p');
            p.className = 'text-sm';
            p.textContent = term;
            termsPreview.appendChild(p);
        });
    } catch (error) {
        console.error('Error updating terms preview:', error);
    }
}

// Function to generate quotation PDF
function generateQuotationPdf() {
    try {
        console.log('Generating quotation PDF...');

        // Show loading spinner
        const loadingSpinner = window.safeDOM.getElementById('pdf-loading-spinner');
        if (loadingSpinner) loadingSpinner.classList.remove('hidden');

        // Simulate PDF generation (replace with actual implementation)
        setTimeout(() => {
            // Hide loading spinner
            if (loadingSpinner) loadingSpinner.classList.add('hidden');

            // Show PDF options modal
            const pdfOptionsModal = window.safeDOM.getElementById('pdf-options-modal');
            if (pdfOptionsModal) pdfOptionsModal.classList.remove('hidden');
        }, 2000);
    } catch (error) {
        console.error('Error generating quotation PDF:', error);

        // Hide loading spinner
        const loadingSpinner = window.safeDOM.getElementById('pdf-loading-spinner');
        if (loadingSpinner) loadingSpinner.classList.add('hidden');

        // Log error message
        console.error('Error generating PDF. Please try again.');
    }
}

// Initialize all steps when document is ready
function initializeSteps() {
    try {
        // Create missing containers first
        createMissingContainers();

        // Initialize step 1
        initStep1();

        // Initialize other steps when navigating to them
        const currentStep = parseInt(window.safeDOM.getValue('current_step')) || 1;
        if (currentStep === 2) initStep2();
        if (currentStep === 3) initStep3();

        console.log('All steps initialized successfully');
    } catch (error) {
        console.error('Error initializing steps:', error);
    }
}

// Call initialization when document is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSteps);
} else {
    // Document already loaded
    initializeSteps();
}
