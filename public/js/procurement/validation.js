/**
 * Procurement Form Validation Functions
 * Handles form validation and submission
 */

// Function to validate the current step
function validateStep() {
    try {
        // Get the current step content
        const currentStepElement = window.safeDOM.getElementById(`step${window.safeDOM.getValue('current_step')}`);
        if (!currentStepElement) return true;

        // Get all required fields in the current step
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        if (!requiredFields || requiredFields.length === 0) return true;

        // Check each required field
        let isValid = true;
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');

                // Find the label for this field
                const fieldId = field.id;
                const label = document.querySelector(`label[for="${fieldId}"]`);

                // Show error message
                let errorMsg = window.safeDOM.getElementById(`${fieldId}-error`);
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.id = `${fieldId}-error`;
                    errorMsg.className = 'text-red-500 text-xs mt-1';
                    field.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = `${label ? label.textContent.replace(' *', '') : 'This field'} is required`;
            } else {
                field.classList.remove('border-red-500');
                const errorMsg = window.safeDOM.getElementById(`${field.id}-error`);
                if (errorMsg) errorMsg.remove();
            }
        });

        // Step-specific validation
        const currentStep = parseInt(window.safeDOM.getValue('current_step')) || 1;

        if (currentStep === 2) {
            // Validate items and vendors are selected
            if (window.selectedItems.length === 0) {
                const noItemsMsg = window.safeDOM.getElementById('no_items_message');
                if (noItemsMsg) {
                    noItemsMsg.textContent = 'Please select at least one item';
                    noItemsMsg.classList.add('text-red-500');
                    noItemsMsg.classList.remove('text-gray-500');
                }
                isValid = false;
            }

            if (window.selectedVendors.length === 0) {
                const noVendorsMsg = window.safeDOM.getElementById('no_vendors_message');
                if (noVendorsMsg) {
                    noVendorsMsg.textContent = 'Please select at least one vendor';
                    noVendorsMsg.classList.add('text-red-500');
                    noVendorsMsg.classList.remove('text-gray-500');
                }
                isValid = false;
            }
        }

        if (currentStep === 3) {
            // Validate quotation uploads
            const vendorRows = window.safeDOM.querySelectorAll('.vendor-quotation-row');
            if (vendorRows && vendorRows.length > 0) {
                vendorRows.forEach(row => {
                    const vendorId = row.getAttribute('data-vendor-id');
                    if (vendorId) {
                        const uploadStatus = row.querySelector('.upload-status');
                        if (uploadStatus && uploadStatus.textContent.includes('Not uploaded')) {
                            uploadStatus.classList.add('text-red-500');
                            isValid = false;
                        }
                    }
                });
            }
        }

        return isValid;
    } catch (error) {
        console.error('Error validating step:', error);
        return false;
    }
}

// Function to save draft
function saveDraft() {
    try {
        console.log('Saving draft...');

        // Get the form
        const form = window.safeDOM.getElementById('procurementForm');
        if (!form) {
            console.error('Form not found');
            return;
        }

        // Create FormData object
        const formData = new FormData(form);

        // Add current step
        formData.append('current_step', window.safeDOM.getValue('current_step') || '1');

        // Add selected items and vendors
        if (window.selectedItems && window.selectedItems.length > 0) {
            formData.append('selected_items_json', JSON.stringify(window.selectedItems));
        }

        if (window.selectedVendors && window.selectedVendors.length > 0) {
            formData.append('selected_vendors_json', JSON.stringify(window.selectedVendors));
        }

        // Show loading indicator
        const saveBtn = window.safeDOM.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Saving...';
        }

        // Send AJAX request
        fetch('/it-admin/procurement/save-draft', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Draft saved:', data);

            // Update request ID if provided
            if (data.request_id) {
                window.safeDOM.setValue('request_id', data.request_id);
            }

            // Log success message
            console.log('Draft saved successfully');
        })
        .catch(error => {
            console.error('Error saving draft:', error);
            console.error('Error saving draft. Please try again.');
        })
        .finally(() => {
            // Reset save button
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="fas fa-save mr-2"></i> Save Draft';
            }
        });
    } catch (error) {
        console.error('Error in saveDraft function:', error);
        alert('An error occurred while saving the draft');
    }
}

// Function to submit the form
function submitForm() {
    try {
        console.log('Submitting form...');

        // Validate all steps first
        let isValid = true;
        const currentStepValue = window.safeDOM.getValue('current_step');
        const currentStep = parseInt(currentStepValue) || 1;

        // Temporarily set each step as current and validate
        for (let i = 1; i <= 6; i++) {
            window.safeDOM.setValue('current_step', i.toString());
            if (!validateStep()) {
                isValid = false;
                break;
            }
        }

        // Restore original current step
        window.safeDOM.setValue('current_step', currentStepValue);

        if (!isValid) {
            alert('Please complete all required fields before submitting');
            return;
        }

        // Get the form
        const form = window.safeDOM.getElementById('procurementForm');
        if (!form) {
            console.error('Form not found');
            return;
        }

        // Create FormData object
        const formData = new FormData(form);

        // Add status as submitted
        formData.append('status', 'submitted');

        // Show loading indicator
        const submitBtn = window.safeDOM.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
        }

        // Send AJAX request
        fetch('/it-admin/procurement/submit', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Form submitted:', data);

            if (data.success) {
                // Redirect to success page
                window.location.href = `/it-admin/procurement/view/${data.request_id}`;
            } else {
                console.error(data.message || 'Error submitting form. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            console.error('Error submitting form. Please try again.');
        })
        .finally(() => {
            // Reset submit button
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Submit';
            }
        });
    } catch (error) {
        console.error('Error in submitForm function:', error);
        console.error('An error occurred while submitting the form');
    }
}
