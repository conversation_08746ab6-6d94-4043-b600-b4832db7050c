/**
 * Procurement Form Vendor Modal Functions
 * Handles vendor modal functionality
 */

// Create vendor modal if it doesn't exist
function createVendorModal() {
    // Check if modal already exists
    if (document.getElementById('vendor-modal')) {
        return document.getElementById('vendor-modal');
    }

    console.log('Creating vendor modal...');

    // Create modal element
    const modal = document.createElement('div');
    modal.id = 'vendor-modal';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden';

    // Create modal content
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add New Vendor</h3>
                <button type="button" id="close-vendor-modal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <label for="vendor-name-input" class="block text-sm font-medium text-gray-700 mb-1">Vendor Name <span class="text-red-500">*</span></label>
                <input type="text" id="vendor-name-input" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
            </div>

            <div class="mb-4">
                <label for="vendor-address-input" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <textarea id="vendor-address-input" rows="2" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="vendor-email-input" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="email" id="vendor-email-input" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="vendor-phone-input" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <input type="tel" id="vendor-phone-input" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
            </div>

            <div class="flex justify-end">
                <button type="button" id="save-vendor-btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                    Save Vendor
                </button>
            </div>
        </div>
    `;

    // Add to document
    document.body.appendChild(modal);

    return modal;
}

// Initialize vendor modal
function initVendorModal() {
    try {
        console.log('Initializing vendor modal...');

        // Create modal if it doesn't exist
        const modal = createVendorModal();

        // Get modal elements
        const openModalBtn = window.safeDOM.getElementById('add-vendor-btn') ||
                             window.safeDOM.querySelector('.add-vendor-btn');
        const closeModalBtn = window.safeDOM.getElementById('close-vendor-modal');
        const saveVendorBtn = window.safeDOM.getElementById('save-vendor-btn');
        const vendorNameInput = window.safeDOM.getElementById('vendor-name-input');
        const vendorAddressInput = window.safeDOM.getElementById('vendor-address-input');
        const vendorEmailInput = window.safeDOM.getElementById('vendor-email-input');
        const vendorPhoneInput = window.safeDOM.getElementById('vendor-phone-input');

        // Open modal function
        function openModal() {
            modal.classList.remove('hidden');

            // Clear inputs
            if (vendorNameInput) vendorNameInput.value = '';
            if (vendorAddressInput) vendorAddressInput.value = '';
            if (vendorEmailInput) vendorEmailInput.value = '';
            if (vendorPhoneInput) vendorPhoneInput.value = '';

            // Focus on name input
            if (vendorNameInput) vendorNameInput.focus();
        }

        // Close modal function
        function closeModal() {
            modal.classList.add('hidden');
        }

        // Save vendor function
        function saveVendor() {
            // Get values
            const name = vendorNameInput ? vendorNameInput.value.trim() : '';
            const address = vendorAddressInput ? vendorAddressInput.value.trim() : '';
            const email = vendorEmailInput ? vendorEmailInput.value.trim() : '';
            const phone = vendorPhoneInput ? vendorPhoneInput.value.trim() : '';

            // Validate
            if (!name) {
                alert('Please enter a vendor name');
                return;
            }

            // Create vendor object
            const vendor = {
                id: 'custom_' + Date.now(),
                name: name,
                address: address,
                email: email,
                phone: phone
            };

            // Add to global array
            if (typeof window.addVendor === 'function') {
                window.addVendor(vendor);
            } else {
                console.error('addVendor function not found');

                // Fallback
                if (!window.selectedVendors) window.selectedVendors = [];
                window.selectedVendors.push(vendor);

                // Update UI if possible
                if (typeof updateSelectedVendorsList === 'function') {
                    updateSelectedVendorsList();
                }
            }

            // Close modal
            closeModal();
        }

        // Add event listeners
        if (openModalBtn) {
            openModalBtn.addEventListener('click', openModal);
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', closeModal);
        }

        if (saveVendorBtn) {
            saveVendorBtn.addEventListener('click', saveVendor);
        }

        // Handle Enter key in inputs
        const inputs = [vendorNameInput, vendorAddressInput, vendorEmailInput, vendorPhoneInput];
        inputs.forEach(input => {
            if (input) {
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        saveVendor();
                    }
                });
            }
        });

        // Handle click outside modal to close
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        console.log('Vendor modal initialized successfully');
    } catch (error) {
        console.error('Error initializing vendor modal:', error);
    }
}

// Initialize when document is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initVendorModal);
} else {
    // Document already loaded
    initVendorModal();
}
