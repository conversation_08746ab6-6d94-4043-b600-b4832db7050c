/**
 * Registration Form Validation
 *
 * This script handles all validation for the registration form, including:
 * - Username availability checking
 * - Email validation and availability checking
 * - Password strength indicators
 * - Password matching validation
 * - Toggle password visibility
 * - Form submission validation
 */

// Flag to indicate that register-validation.js is loaded
window.registerValidationLoaded = true;

// Execute when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing registration validation');

    // Form elements
    const registerForm = document.getElementById('registerForm');
    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const dateOfBirthInput = document.getElementById('date_of_birth');
    const registerButton = document.getElementById('registerButton');

    // Feedback elements
    const usernameFeedback = document.getElementById('username-feedback');
    const emailFeedback = document.getElementById('email-feedback');
    const passwordMatchFeedback = document.getElementById('password-match-feedback');

    // Password strength elements
    const strengthBars = [
        document.getElementById('strength-bar-1'),
        document.getElementById('strength-bar-2'),
        document.getElementById('strength-bar-3'),
        document.getElementById('strength-bar-4')
    ];
    const strengthText = document.getElementById('password-strength-text');

    // State variables
    let usernameAvailable = false;
    let emailAvailable = false;
    let passwordsMatch = false;
    let passwordStrong = false;

    // Debounce function to limit API calls
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // Username validation
    if (usernameInput && usernameFeedback) {
        const checkUsernameDebounced = debounce(function(username) {
            // Check username availability via API
            fetch(`/api/users/check-username?username=${encodeURIComponent(username)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Username check response:', data);
                    usernameAvailable = data.available;
                    usernameFeedback.textContent = data.message;
                    usernameFeedback.style.color = data.available ? 'green' : 'red';
                })
                .catch(error => {
                    console.error('Error checking username:', error);
                    usernameFeedback.textContent = 'Error checking availability';
                    usernameFeedback.style.color = 'red';
                    usernameAvailable = false;
                });
        }, 300);

        usernameInput.addEventListener('input', function() {
            const username = this.value.trim();

            if (username.length < 3) {
                usernameFeedback.textContent = 'Username must be at least 3 characters';
                usernameFeedback.style.color = 'red';
                usernameAvailable = false;
                return;
            }

            usernameFeedback.textContent = 'Checking availability...';
            usernameFeedback.style.color = 'gray';
            checkUsernameDebounced(username);
        });
    }

    // Email validation
    if (emailInput && emailFeedback) {
        const checkEmailDebounced = debounce(function(email) {
            // Check email availability via API
            fetch(`/api/users/check-email?email=${encodeURIComponent(email)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Email check response:', data);
                    emailAvailable = data.available;
                    emailFeedback.textContent = data.message;
                    emailFeedback.style.color = data.available ? 'green' : 'red';
                })
                .catch(error => {
                    console.error('Error checking email:', error);
                    emailFeedback.textContent = 'Error checking availability';
                    emailFeedback.style.color = 'red';
                    emailAvailable = false;
                });
        }, 300);

        emailInput.addEventListener('input', function() {
            const email = this.value.trim();

            // Simple email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email) {
                emailFeedback.textContent = '';
                emailAvailable = false;
                return;
            }

            if (!emailRegex.test(email)) {
                emailFeedback.textContent = 'Please enter a valid email address';
                emailFeedback.style.color = 'red';
                emailAvailable = false;
                return;
            }

            emailFeedback.textContent = 'Checking availability...';
            emailFeedback.style.color = 'gray';
            checkEmailDebounced(email);
        });
    }

    // Password strength validation
    if (passwordInput && strengthBars.every(bar => bar) && strengthText) {
        console.log('Setting up password strength validation');
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            console.log('Password input changed, updating strength indicator');

            // Calculate password strength
            const strength = calculatePasswordStrength(password);
            console.log('Password strength:', strength);
            updatePasswordStrengthUI(strength);

            // Update password match if confirm password has a value
            if (confirmPasswordInput && confirmPasswordInput.value) {
                checkPasswordMatch();
            }

            // Update password strength state
            passwordStrong = strength >= 2; // Medium or stronger
        });

        // Trigger the input event to initialize the strength indicator
        if (passwordInput.value) {
            console.log('Triggering initial password strength calculation');
            const event = new Event('input');
            passwordInput.dispatchEvent(event);
        }
    } else {
        console.warn('Password strength elements not found:', {
            passwordInput: !!passwordInput,
            allStrengthBars: strengthBars.every(bar => bar),
            strengthText: !!strengthText
        });
    }

    // Password match validation
    if (confirmPasswordInput && passwordMatchFeedback) {
        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    }

    // Password strength calculation
    function calculatePasswordStrength(password) {
        if (!password) return 0;

        let score = 0;

        // Length check
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;

        // Complexity checks
        if (/[A-Z]/.test(password)) score += 1; // Has uppercase
        if (/[a-z]/.test(password)) score += 1; // Has lowercase
        if (/[0-9]/.test(password)) score += 1; // Has number
        if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char

        // Normalize score to 0-4 range
        return Math.min(4, Math.floor(score / 1.5));
    }

    // Update password strength UI
    function updatePasswordStrengthUI(strength) {
        // Reset all bars
        strengthBars.forEach(bar => {
            bar.className = 'h-1 w-1/4 bg-gray-200 rounded-sm';
        });

        // Set text based on strength
        const strengthLabels = ['Very Weak', 'Weak', 'Medium', 'Strong', 'Very Strong'];
        strengthText.textContent = strengthLabels[strength];

        // Set colors based on strength
        const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];

        // Update bars based on strength
        for (let i = 0; i < strength; i++) {
            strengthBars[i].className = `h-1 w-1/4 ${colors[Math.min(i, colors.length - 1)]} rounded-sm`;
        }
    }

    // Check if passwords match
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (!confirmPassword) {
            passwordMatchFeedback.textContent = '';
            passwordsMatch = false;
            return;
        }

        if (password === confirmPassword) {
            passwordMatchFeedback.textContent = 'Passwords match';
            passwordMatchFeedback.style.color = 'green';
            passwordsMatch = true;
        } else {
            passwordMatchFeedback.textContent = 'Passwords do not match';
            passwordMatchFeedback.style.color = 'red';
            passwordsMatch = false;
        }
    }

    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordField = document.getElementById(targetId);
            const eyeIcon = this.querySelector('.eye-icon');
            const eyeSlashIcon = this.querySelector('.eye-slash-icon');

            if (passwordField) {
                // Toggle password visibility
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    if (eyeIcon) eyeIcon.classList.add('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.remove('hidden');
                } else {
                    passwordField.type = 'password';
                    if (eyeIcon) eyeIcon.classList.remove('hidden');
                    if (eyeSlashIcon) eyeSlashIcon.classList.add('hidden');
                }
            }
        });
    });

    // Form submission validation
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            const username = usernameInput.value.trim();
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            const dateOfBirth = dateOfBirthInput.value;
            let hasErrors = false;

            // Create general error message container if it doesn't exist
            let generalError = document.getElementById('general-error');
            if (!generalError) {
                generalError = document.createElement('div');
                generalError.id = 'general-error';
                generalError.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                registerForm.insertBefore(generalError, registerForm.firstChild);
                generalError.style.display = 'none';
            }

            // Create date of birth feedback if it doesn't exist
            let dobFeedback = document.getElementById('dob-feedback');
            if (!dobFeedback) {
                dobFeedback = document.createElement('div');
                dobFeedback.id = 'dob-feedback';
                dobFeedback.className = 'mt-1 text-sm';
                dobFeedback.style.minHeight = '20px';
                dateOfBirthInput.parentNode.appendChild(dobFeedback);
            }

            // Reset all feedback elements
            generalError.style.display = 'none';
            generalError.textContent = '';
            dobFeedback.textContent = '';

            // Basic validation
            if (!username || !email || !password || !confirmPassword || !dateOfBirth) {
                e.preventDefault();
                hasErrors = true;
                generalError.textContent = 'Please fill in all fields';
                generalError.style.display = 'block';
            }

            // Username validation
            if (username && username.length < 3) {
                e.preventDefault();
                hasErrors = true;
                usernameFeedback.textContent = 'Username must be at least 3 characters long';
                usernameFeedback.style.color = 'red';
            }

            if (username && !usernameAvailable) {
                e.preventDefault();
                hasErrors = true;
                usernameFeedback.textContent = 'Please choose a different username';
                usernameFeedback.style.color = 'red';
            }

            // Email validation
            if (email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                }

                if (!emailAvailable) {
                    e.preventDefault();
                    hasErrors = true;
                    emailFeedback.textContent = 'Please choose a different email address';
                    emailFeedback.style.color = 'red';
                }
            }

            // Password validation
            if (password && password.length < 8) {
                e.preventDefault();
                hasErrors = true;
                strengthText.textContent = 'Password must be at least 8 characters long';
                strengthText.style.color = 'red';
            }

            if (password && !passwordStrong) {
                e.preventDefault();
                hasErrors = true;
                strengthText.textContent = 'Please choose a stronger password';
                strengthText.style.color = 'red';
            }

            // Password match validation
            if (password && confirmPassword && password !== confirmPassword) {
                e.preventDefault();
                hasErrors = true;
                passwordMatchFeedback.textContent = 'Passwords do not match';
                passwordMatchFeedback.style.color = 'red';
            }

            // Date of birth validation
            if (dateOfBirth) {
                const today = new Date();
                const birthDate = new Date(dateOfBirth);
                const age = today.getFullYear() - birthDate.getFullYear();

                if (age < 13) {
                    e.preventDefault();
                    hasErrors = true;
                    dobFeedback.textContent = 'You must be at least 13 years old to register';
                    dobFeedback.style.color = 'red';
                }
            }

            if (!hasErrors) {
                // Disable button to prevent double submission
                registerButton.disabled = true;
                registerButton.textContent = 'Registering...';
            }
        });
    }

    // Set max date to today for date of birth input
    if (dateOfBirthInput) {
        const today = new Date().toISOString().split('T')[0];
        dateOfBirthInput.setAttribute('max', today);
    }
});
