/**
 * Search and Select Component
 * 
 * This script provides a search and select functionality for dropdown menus
 * with a large number of options. It replaces standard select elements with
 * a custom search input and dropdown list.
 */

class SearchSelect {
    /**
     * Initialize a new SearchSelect instance
     * @param {HTMLSelectElement} selectElement - The original select element to enhance
     * @param {Object} options - Configuration options
     */
    constructor(selectElement, options = {}) {
        this.selectElement = selectElement;
        this.options = Object.assign({
            placeholder: 'Search...',
            noResultsText: 'No results found',
            minSearchLength: 1,
            maxResults: 10,
            width: '100%',
            zIndex: 1000,
            onSelect: null
        }, options);

        this.isOpen = false;
        this.selectedOption = null;
        this.searchResults = [];
        this.allOptions = [];

        // Store all options from the original select
        Array.from(this.selectElement.options).forEach(option => {
            if (option.value) {
                this.allOptions.push({
                    value: option.value,
                    text: option.text,
                    element: option
                });
            }
        });

        this.init();
    }

    /**
     * Initialize the component
     */
    init() {
        // Create container
        this.container = document.createElement('div');
        this.container.className = 'search-select-container relative';
        this.container.style.width = this.options.width;
        
        // Create search input
        this.searchInput = document.createElement('input');
        this.searchInput.type = 'text';
        this.searchInput.className = 'search-select-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 pr-10';
        this.searchInput.placeholder = this.options.placeholder;
        
        // Create dropdown
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'search-select-dropdown absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg overflow-y-auto z-50 hidden';
        this.dropdown.style.maxHeight = '200px';
        this.dropdown.style.zIndex = this.options.zIndex;
        
        // Create clear button
        this.clearButton = document.createElement('button');
        this.clearButton.type = 'button';
        this.clearButton.className = 'search-select-clear absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden';
        this.clearButton.innerHTML = '&times;';
        this.clearButton.style.fontSize = '18px';
        
        // Create hidden input to store the value
        this.hiddenInput = document.createElement('input');
        this.hiddenInput.type = 'hidden';
        this.hiddenInput.name = this.selectElement.name;
        
        // Add elements to container
        this.container.appendChild(this.searchInput);
        this.container.appendChild(this.clearButton);
        this.container.appendChild(this.dropdown);
        this.container.appendChild(this.hiddenInput);
        
        // Insert container after select element
        this.selectElement.parentNode.insertBefore(this.container, this.selectElement.nextSibling);
        
        // Hide original select
        this.selectElement.style.display = 'none';
        
        // Set initial value if select has a selected option
        const selectedOption = this.selectElement.options[this.selectElement.selectedIndex];
        if (selectedOption && selectedOption.value) {
            this.setValue(selectedOption.value, selectedOption.text);
        }
        
        // Attach event listeners
        this.attachEventListeners();
    }

    /**
     * Attach event listeners to the component
     */
    attachEventListeners() {
        // Search input events
        this.searchInput.addEventListener('focus', () => this.openDropdown());
        this.searchInput.addEventListener('input', () => this.handleSearch());
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDropdown();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.highlightNextOption();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.highlightPreviousOption();
            } else if (e.key === 'Enter') {
                e.preventDefault();
                this.selectHighlightedOption();
            }
        });
        
        // Clear button event
        this.clearButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.clearSelection();
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }

    /**
     * Handle search input
     */
    handleSearch() {
        const searchTerm = this.searchInput.value.toLowerCase().trim();
        
        if (searchTerm.length < this.options.minSearchLength) {
            this.searchResults = this.allOptions.slice(0, this.options.maxResults);
        } else {
            this.searchResults = this.allOptions.filter(option => 
                option.text.toLowerCase().includes(searchTerm)
            ).slice(0, this.options.maxResults);
        }
        
        this.renderDropdown();
    }

    /**
     * Render the dropdown with search results
     */
    renderDropdown() {
        this.dropdown.innerHTML = '';
        
        if (this.searchResults.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'search-select-no-results p-2 text-gray-500 text-sm';
            noResults.textContent = this.options.noResultsText;
            this.dropdown.appendChild(noResults);
            return;
        }
        
        const ul = document.createElement('ul');
        ul.className = 'search-select-results';
        
        this.searchResults.forEach((option, index) => {
            const li = document.createElement('li');
            li.className = 'search-select-option p-2 hover:bg-gray-100 cursor-pointer';
            li.dataset.value = option.value;
            li.dataset.index = index;
            li.textContent = option.text;
            
            li.addEventListener('click', () => {
                this.setValue(option.value, option.text);
                this.closeDropdown();
            });
            
            ul.appendChild(li);
        });
        
        this.dropdown.appendChild(ul);
    }

    /**
     * Open the dropdown
     */
    openDropdown() {
        if (!this.isOpen) {
            this.isOpen = true;
            this.dropdown.classList.remove('hidden');
            this.handleSearch();
        }
    }

    /**
     * Close the dropdown
     */
    closeDropdown() {
        if (this.isOpen) {
            this.isOpen = false;
            this.dropdown.classList.add('hidden');
            
            // If no option is selected, clear the search input
            if (!this.selectedOption && this.searchInput.value) {
                this.searchInput.value = '';
            }
        }
    }

    /**
     * Set the selected value
     * @param {string} value - The value to set
     * @param {string} text - The display text
     */
    setValue(value, text) {
        this.selectedOption = { value, text };
        this.hiddenInput.value = value;
        this.searchInput.value = text;
        this.selectElement.value = value;
        
        // Show clear button
        this.clearButton.classList.remove('hidden');
        
        // Trigger change event on the original select
        const event = new Event('change', { bubbles: true });
        this.selectElement.dispatchEvent(event);
        
        // Call onSelect callback if provided
        if (typeof this.options.onSelect === 'function') {
            this.options.onSelect(value, text);
        }
    }

    /**
     * Clear the current selection
     */
    clearSelection() {
        this.selectedOption = null;
        this.hiddenInput.value = '';
        this.searchInput.value = '';
        this.selectElement.value = '';
        
        // Hide clear button
        this.clearButton.classList.add('hidden');
        
        // Trigger change event on the original select
        const event = new Event('change', { bubbles: true });
        this.selectElement.dispatchEvent(event);
        
        // Focus the search input
        this.searchInput.focus();
    }

    /**
     * Highlight the next option in the dropdown
     */
    highlightNextOption() {
        if (!this.isOpen) return;
        
        const options = this.dropdown.querySelectorAll('.search-select-option');
        if (options.length === 0) return;
        
        const highlighted = this.dropdown.querySelector('.search-select-option.highlighted');
        let nextIndex = 0;
        
        if (highlighted) {
            const currentIndex = parseInt(highlighted.dataset.index);
            nextIndex = (currentIndex + 1) % options.length;
            highlighted.classList.remove('highlighted', 'bg-blue-100');
        }
        
        options[nextIndex].classList.add('highlighted', 'bg-blue-100');
        options[nextIndex].scrollIntoView({ block: 'nearest' });
    }

    /**
     * Highlight the previous option in the dropdown
     */
    highlightPreviousOption() {
        if (!this.isOpen) return;
        
        const options = this.dropdown.querySelectorAll('.search-select-option');
        if (options.length === 0) return;
        
        const highlighted = this.dropdown.querySelector('.search-select-option.highlighted');
        let prevIndex = options.length - 1;
        
        if (highlighted) {
            const currentIndex = parseInt(highlighted.dataset.index);
            prevIndex = (currentIndex - 1 + options.length) % options.length;
            highlighted.classList.remove('highlighted', 'bg-blue-100');
        }
        
        options[prevIndex].classList.add('highlighted', 'bg-blue-100');
        options[prevIndex].scrollIntoView({ block: 'nearest' });
    }

    /**
     * Select the currently highlighted option
     */
    selectHighlightedOption() {
        if (!this.isOpen) return;
        
        const highlighted = this.dropdown.querySelector('.search-select-option.highlighted');
        if (highlighted) {
            const value = highlighted.dataset.value;
            const text = highlighted.textContent;
            this.setValue(value, text);
            this.closeDropdown();
        }
    }
}

// Initialize all search-select elements on the page
document.addEventListener('DOMContentLoaded', function() {
    // Add a global function to initialize search-select on any element
    window.initSearchSelect = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            new SearchSelect(element, options);
        });
    };
});
