/**
 * Session Timer
 * Displays a countdown timer in the bottom-right corner after 3 seconds of inactivity
 * Counts down from 15 minutes (session timeout duration)
 */

// Check if the session timer has already been initialized
if (typeof window.sessionTimerInitialized === 'undefined') {
    // Set the flag to prevent multiple initializations
    window.sessionTimerInitialized = true;

    // Configuration
    window.INACTIVITY_THRESHOLD = 5000; // 5 seconds of inactivity before showing timer
    window.SESSION_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds
    window.WARNING_THRESHOLD = 2 * 60 * 1000; // 2 minutes in milliseconds (when to show warning)

    // Variables
    window.lastActivityTime = Date.now();
    window.timerInterval = null;
    window.timerVisible = false;
    window.timerElement = null;
    window.timerContainer = null;

    console.log('Session timer initialized for the first time');
} else {
    console.log('Session timer already initialized, skipping duplicate initialization');
}

// Initialize the session timer
function initSessionTimer() {
    // Skip if already initialized
    if (document.getElementById('session-info')) {
        console.log('Session timer DOM elements already exist, skipping initialization');
        return;
    }
    // Create session info element as an overlay below navbar
    // Create session info display as an overlay (initially hidden)
    const sessionInfo = document.createElement('div');
    sessionInfo.id = 'session-info';
    sessionInfo.className = 'hidden fixed top-16 left-1/2 transform -translate-x-1/2 bg-white shadow-md rounded-md px-4 py-2 z-50 opacity-0';
    sessionInfo.innerHTML = `
        <div class="flex items-center justify-center">
            <span class="text-gray-700">Session expires in: </span>
            <span id="footer-session-timer" class="font-medium ml-1 text-gray-900">15:00</span>
            <button id="footer-extend-btn" class="ml-3 text-indigo-600 hover:text-indigo-800 underline text-sm">Extend</button>
        </div>
    `;

    // Add to the body
    document.body.appendChild(sessionInfo);

    // Get timer element
    window.timerElement = document.getElementById('footer-session-timer');

    // Add event listeners for user activity
    document.addEventListener('mousemove', resetActivityTimer);
    document.addEventListener('keypress', resetActivityTimer);
    document.addEventListener('click', resetActivityTimer);
    document.addEventListener('scroll', resetActivityTimer);

    // Add event listeners for buttons
    document.getElementById('footer-extend-btn').addEventListener('click', extendSession);

    // Start checking for inactivity
    startInactivityCheck();

    // Initialize the footer timer with the full session time
    updateFooterTimer(window.SESSION_TIMEOUT);
}

// Start checking for inactivity
function startInactivityCheck() {
    setInterval(() => {
        const currentTime = Date.now();
        const inactiveTime = currentTime - window.lastActivityTime;

        // If user has been inactive for the threshold time and timer is not visible
        if (inactiveTime >= window.INACTIVITY_THRESHOLD && !window.timerVisible) {
            showTimer();
        }
    }, 1000);
}

// Reset activity timer
function resetActivityTimer() {
    window.lastActivityTime = Date.now();

    // If timer is visible, hide it
    if (window.timerVisible) {
        hideTimer();
    }
}

// Show timer with fade-in effect
function showTimer() {
    window.timerVisible = true;

    // Show the session info overlay with fade-in effect
    const sessionInfo = document.getElementById('session-info');
    if (sessionInfo) {
        // First make it visible but with opacity 0
        sessionInfo.classList.remove('hidden');

        // Force a reflow to ensure the transition works
        void sessionInfo.offsetWidth;

        // Then animate the opacity to 1
        sessionInfo.style.transition = 'opacity 0.5s ease-in-out';
        sessionInfo.style.opacity = '1';
    }

    // Start countdown
    startCountdown();
}

// Hide timer with fade-out effect
function hideTimer() {
    // Only proceed with hiding if the timer is currently visible
    if (!window.timerVisible) return;

    window.timerVisible = false;

    // Hide the session info overlay with fade-out effect
    const sessionInfo = document.getElementById('session-info');
    if (sessionInfo) {
        // Animate opacity to 0
        sessionInfo.style.opacity = '0';

        // After the animation completes, hide the element
        setTimeout(() => {
            sessionInfo.classList.add('hidden');
        }, 500); // Match this with the transition duration
    }

    // Stop countdown
    if (window.timerInterval) {
        clearInterval(window.timerInterval);
        window.timerInterval = null;
    }
}

// Start countdown
function startCountdown() {
    // Calculate remaining time
    const currentTime = Date.now();
    const sessionEndTime = window.lastActivityTime + window.SESSION_TIMEOUT;
    let remainingTime = sessionEndTime - currentTime;

    // Update timer immediately
    updateTimerDisplay(remainingTime);

    // Clear existing interval if any
    if (window.timerInterval) {
        clearInterval(window.timerInterval);
    }

    // Start interval to update timer every second
    window.timerInterval = setInterval(() => {
        // Calculate remaining time
        const currentTime = Date.now();
        const sessionEndTime = window.lastActivityTime + window.SESSION_TIMEOUT;
        let remainingTime = sessionEndTime - currentTime;

        // Update timer display
        updateTimerDisplay(remainingTime);

        // If session has expired
        if (remainingTime <= 0) {
            clearInterval(window.timerInterval);
            window.location.href = '/login?timeout=true';
        }

        // If remaining time is less than warning threshold, show warning
        if (remainingTime <= window.WARNING_THRESHOLD) {
            window.timerElement.classList.add('text-red-600');
            window.timerContainer.classList.add('border-red-500');
        }
    }, 1000);
}

// Update timer display
function updateTimerDisplay(remainingTime) {
    // Just update the footer timer since we've removed the overlay
    updateFooterTimer(remainingTime);
}

// Update timer display in the overlay
function updateFooterTimer(remainingTime) {
    const timerElement = document.getElementById('footer-session-timer');
    if (!timerElement) return;

    // Store reference to timer element
    window.timerElement = timerElement;

    const minutes = Math.floor(remainingTime / 60000);
    const seconds = Math.floor((remainingTime % 60000) / 1000);
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    timerElement.textContent = timeString;

    // Get the session info container
    const sessionInfo = document.getElementById('session-info');

    // Store reference to session info container
    window.timerContainer = sessionInfo;

    // Add warning color if time is running low
    if (remainingTime <= window.WARNING_THRESHOLD) {
        timerElement.classList.add('text-red-600');
        timerElement.classList.add('font-bold');

        // Add a subtle pulsing effect to the container when time is running low
        if (sessionInfo) {
            sessionInfo.classList.add('session-warning');
        }
    } else {
        timerElement.classList.remove('text-red-600');
        timerElement.classList.remove('font-bold');

        if (sessionInfo) {
            sessionInfo.classList.remove('session-warning');
        }
    }
}

// Extend session
function extendSession() {
    // Make an AJAX request to extend the session
    fetch('/extend-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reset activity timer
            resetActivityTimer();

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
            successMessage.innerHTML = `
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">Your session has been extended.</span>
            `;
            document.body.appendChild(successMessage);

            // Remove success message after 3 seconds
            setTimeout(() => {
                document.body.removeChild(successMessage);
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error extending session:', error);
    });
}

// Logout now
function logoutNow() {
    window.location.href = '/logout';
}

// Initialize when DOM is loaded, but not on test pages
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a test-taking page
    const isTestPage = window.location.pathname.includes('/tests/take/');

    // Only initialize the session timer if we're not on a test page
    if (!isTestPage) {
        initSessionTimer();
    } else {
        console.log('Session timer disabled on test page');
    }
});
