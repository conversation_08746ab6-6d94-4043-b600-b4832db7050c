// Centralized Students Page JavaScript with AJAX
var StudentsPage = {
  // Initialize the page
  init: function() {
    if (this.initialized) {
      console.log('StudentsPage already initialized, skipping...');
      return;
    }

    console.log('StudentsPage initialized with AJAX');
    this.bindEvents();
    this.currentFilters = this.getUrlParams();
    this.initialized = true;
  },

  // Get URL parameters
  getUrlParams: function() {
    var params = {};
    var urlParams = new URLSearchParams(window.location.search);
    for (var pair of urlParams.entries()) {
      params[pair[0]] = pair[1];
    }
    return params;
  },

  // Show loading indicator
  showLoading: function() {
    if ($('#loadingIndicator').length === 0) {
      $('body').append('<div id="loadingIndicator" style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:20px;border-radius:5px;z-index:9999;">Loading...</div>');
    }
  },

  // Hide loading indicator
  hideLoading: function() {
    $('#loadingIndicator').remove();
  },

  // Bind all events
  bindEvents: function() {
    var self = this;

    // Toggle filters
    $(document).on('click', '#toggleFilters', function(e) {
      e.preventDefault();
      console.log('Toggle filters clicked');
      self.toggleAdvancedFilters();
    });

    // Quick filters with AJAX
    $(document).on('click', '.quick-filter', function(e) {
      e.preventDefault();
      var filterType = $(this).data('filter');
      console.log('Quick filter clicked:', filterType);
      self.applyQuickFilterAjax(filterType);
    });

    // Clear filters with AJAX
    $(document).on('click', '#clearFilters', function(e) {
      e.preventDefault();
      console.log('Clear filters clicked');
      self.clearAllFiltersAjax();
    });

    // View details button (expandable rows)
    $(document).on('click', '.view-details-btn', function(e) {
      e.preventDefault();
      e.stopPropagation();
      var studentId = $(this).data('student-id');
      console.log('View details button clicked:', studentId);
      self.toggleStudentDetails(studentId);
    });

    // View student button (modal) - handle the actual button class used in HTML
    $(document).on('click', '.view-student-btn', function(e) {
      e.preventDefault();
      e.stopPropagation();
      var studentId = $(this).data('student-id');
      console.log('View student button clicked:', studentId);
      self.openStudentModal(studentId, 'view');
    });

    // Modal close buttons
    $(document).on('click', '#closeStudentModalBtn, #closeStudentModalFooterBtn', function(e) {
      e.preventDefault();
      self.closeStudentModal();
    });

    // Select all checkbox functionality
    $(document).on('change', '#selectAll', function() {
      var isChecked = $(this).is(':checked');
      $('.student-checkbox').prop('checked', isChecked);
      self.updateSelectedCount();
      self.updateExportButtons();
    });

    // Individual checkbox functionality
    $(document).on('change', '.student-checkbox', function() {
      self.updateSelectAllState();
      self.updateSelectedCount();
      self.updateExportButtons();
    });

    // Records per page change
    $(document).on('change', '#recordsPerPage', function() {
      var newLimit = $(this).val();
      var currentUrl = new URL(window.location);
      currentUrl.searchParams.set('limit', newLimit);
      currentUrl.searchParams.set('page', '1'); // Reset to first page
      window.location.href = currentUrl.toString();
    });

    // Jump to page functionality
    $(document).on('click', '#jumpToPageBtn', function() {
      var pageNumber = $('#jumpToPage').val();
      var maxPages = $('#jumpToPage').attr('max');

      if (pageNumber && pageNumber >= 1 && pageNumber <= maxPages) {
        var currentUrl = new URL(window.location);
        currentUrl.searchParams.set('page', pageNumber);
        window.location.href = currentUrl.toString();
      }
    });

    // Jump to page on Enter key
    $(document).on('keypress', '#jumpToPage', function(e) {
      if (e.which === 13) { // Enter key
        $('#jumpToPageBtn').click();
      }
    });

    // Export selected students
    $(document).on('click', '#exportSelectedBtn', function() {
      self.exportSelectedStudents();
    });

    // Export all students
    $(document).on('click', '#exportAllBtn', function() {
      self.exportAllStudents();
    });
  },

  // Toggle advanced filters
  toggleAdvancedFilters: function() {
    var $advancedFilters = $('#advancedFilters');
    var $filterToggleIcon = $('#filterToggleIcon');

    if ($advancedFilters.hasClass('hidden')) {
      $advancedFilters.removeClass('hidden');
      $filterToggleIcon.addClass('rotated');
      console.log('Filters expanded');
    } else {
      $advancedFilters.addClass('hidden');
      $filterToggleIcon.removeClass('rotated');
      console.log('Filters collapsed');
    }
  },

  // Apply quick filter with AJAX
  applyQuickFilterAjax: function(filterType) {
    console.log('Applying quick filter:', filterType);
    // For now, just redirect to test
    var url = '/principal/students?';
    switch(filterType) {
      case 'male':
        url += 'gender=Male';
        break;
      case 'female':
        url += 'gender=Female';
        break;
      case 'bpl':
        url += 'bpl=Yes';
        break;
      case 'disability':
        url += 'disability=Yes';
        break;
    }
    window.location.href = url;
  },

  // Clear all filters with AJAX
  clearAllFiltersAjax: function() {
    window.location.href = '/principal/students';
  },

  // Toggle student details
  toggleStudentDetails: function(studentId) {
    var $detailsRow = $('#details-' + studentId);

    if ($detailsRow.length) {
      if ($detailsRow.hasClass('hidden')) {
        // Hide all other open details first
        $('[id^="details-"]').addClass('hidden');
        // Show this one
        $detailsRow.removeClass('hidden');
        console.log('Student details expanded for:', studentId);
      } else {
        $detailsRow.addClass('hidden');
        console.log('Student details collapsed for:', studentId);
      }
    }
  },

  // Modal functions
  openStudentModal: function(studentId, mode) {
    var modal = document.getElementById('studentModal');
    var modalTitle = document.getElementById('studentModalTitle');
    var modalContent = document.getElementById('studentModalContent');
    var modalFooter = document.getElementById('studentModalFooter');

    if (!modal || !modalTitle || !modalContent || !modalFooter) {
      console.error('Modal elements not found');
      return;
    }

    // Set modal title
    modalTitle.innerHTML = '<i class="fas fa-eye mr-2"></i>View Student Details';

    // Show loading
    modalContent.innerHTML = '<div class="flex justify-center items-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-principal-primary"></i></div>';

    // Show modal
    modal.classList.remove('hidden');

    // Fetch student data
    fetch(`/principal/students/${studentId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          var student = data.student;
          modalContent.innerHTML = this.generateViewContent(student);
          modalFooter.innerHTML = `
            <button id="closeStudentModalFooterBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
              <i class="fas fa-times mr-2"></i>Close
            </button>
          `;
        } else {
          modalContent.innerHTML = '<div class="text-red-600 text-center py-4">Error loading student data</div>';
        }
      })
      .catch(error => {
        modalContent.innerHTML = '<div class="text-red-600 text-center py-4">Error loading student data</div>';
        console.error('Error:', error);
      });
  },

  closeStudentModal: function() {
    var modal = document.getElementById('studentModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  },

  generateViewContent: function(student) {
    return `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
              <i class="fas fa-user mr-2 text-principal-primary"></i>Personal Information
            </h4>
            <div class="space-y-2 text-sm">
              <div><strong>Student ID:</strong> ${student.student_id || '-'}</div>
              <div><strong>Name:</strong> ${student.name || '-'}</div>
              <div><strong>Father's Name:</strong> ${student.father_name || '-'}</div>
              <div><strong>Mother's Name:</strong> ${student.mother_name || '-'}</div>
              <div><strong>DOB:</strong> ${student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '-'}</div>
              <div><strong>Gender:</strong> ${student.gender || '-'}</div>
              <div><strong>Religion:</strong> ${student.religion_name || '-'}</div>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
              <i class="fas fa-graduation-cap mr-2 text-principal-primary"></i>Academic Information
            </h4>
            <div class="space-y-2 text-sm">
              <div><strong>Class:</strong> ${student.class || '-'}</div>
              <div><strong>Section:</strong> ${student.section || '-'}</div>
              <div><strong>Stream:</strong> ${student.stream || '-'}</div>
              <div><strong>Roll No:</strong> ${student.roll_no || '-'}</div>
              <div><strong>Session:</strong> ${student.session || '-'}</div>
              <div><strong>Medium:</strong> ${student.medium_name || '-'}</div>
            </div>
          </div>
        </div>

        <div class="space-y-4">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
              <i class="fas fa-map-marker-alt mr-2 text-principal-primary"></i>Contact & Address
            </h4>
            <div class="space-y-2 text-sm">
              <div><strong>Contact:</strong> ${student.contact_no || '-'}</div>
              <div><strong>Address:</strong> ${student.cur_address || '-'}</div>
              <div><strong>Village/Ward:</strong> ${student.village_ward || '-'}</div>
              <div><strong>Pin Code:</strong> ${student.pin_code || '-'}</div>
              <div><strong>District:</strong> ${student.district_name || '-'}</div>
              <div><strong>State:</strong> ${student.state_name || '-'}</div>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-principal-dark mb-3 flex items-center">
              <i class="fas fa-info-circle mr-2 text-principal-primary"></i>Additional Information
            </h4>
            <div class="space-y-2 text-sm">
              <div><strong>BPL Status:</strong>
                <span class="px-2 py-1 text-xs rounded-full ${student.bpl === 'Yes' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}">
                  ${student.bpl || 'No'}
                </span>
              </div>
              <div><strong>Disability:</strong>
                <span class="px-2 py-1 text-xs rounded-full ${student.disability === 'Yes' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">
                  ${student.disability || 'No'}
                </span>
              </div>
              <div><strong>Height:</strong> ${student.height || '-'}</div>
              <div><strong>Weight:</strong> ${student.weight || '-'}</div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Update select all checkbox state
  updateSelectAllState: function() {
    var totalCheckboxes = $('.student-checkbox').length;
    var checkedCheckboxes = $('.student-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
      $('#selectAll').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
      $('#selectAll').prop('indeterminate', false).prop('checked', true);
    } else {
      $('#selectAll').prop('indeterminate', true).prop('checked', false);
    }
  },

  // Update selected count display
  updateSelectedCount: function() {
    var selectedCount = $('.student-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);
  },

  // Update export buttons visibility
  updateExportButtons: function() {
    var selectedCount = $('.student-checkbox:checked').length;

    if (selectedCount > 0) {
      $('#exportSelectedBtn').removeClass('hidden');
    } else {
      $('#exportSelectedBtn').addClass('hidden');
    }
  },

  // Export selected students
  exportSelectedStudents: function() {
    var selectedStudents = [];

    $('.student-checkbox:checked').each(function() {
      try {
        var studentData = JSON.parse($(this).attr('data-student-data'));
        selectedStudents.push(studentData);
      } catch (e) {
        console.error('Error parsing student data:', e);
      }
    });

    if (selectedStudents.length === 0) {
      alert('Please select students to export.');
      return;
    }

    this.downloadCSV(selectedStudents, 'selected_students_export.csv');
  },

  // Export all students (with current filters)
  exportAllStudents: function() {
    // Get current URL parameters to maintain filters
    var currentUrl = new URL(window.location);
    var exportUrl = '/principal/students/export?' + currentUrl.searchParams.toString();

    // Create a temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'all_students_export.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  // Download CSV helper function
  downloadCSV: function(data, filename) {
    if (data.length === 0) {
      alert('No data to export.');
      return;
    }

    // Define CSV headers
    var headers = [
      'S.No', 'Student ID', 'UdiseCode', 'Name', 'Father Name', 'Mother Name', 'DOB', 'Gender',
      'Class', 'Section', 'Stream', 'Trade', 'Caste Category', 'BPL', 'Disability',
      'Religion', 'Medium', 'Height', 'Weight', 'Admission No', 'Admission Date',
      'State', 'District', 'Address', 'Village/Ward', 'Gram Panchayat', 'Pin Code',
      'Roll No', 'Contact No', 'IFSC Code', 'Bank Name', 'Account Holder',
      'Account Holder Name', 'Account Holder Code', 'Session'
    ];

    // Convert data to CSV format
    var csvContent = headers.join(',') + '\n';

    data.forEach(function(student, index) {
      var row = [
        index + 1,
        student.student_id || '',
        student.udise_code || '',
        student.name || '',
        student.father_name || '',
        student.mother_name || '',
        student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
        student.gender || '',
        student.class || '',
        student.section || '',
        student.stream || '',
        student.trade || '',
        student.caste_category_name || '',
        student.bpl || '',
        student.disability || '',
        student.religion_name || '',
        student.medium_name || '',
        student.height || '',
        student.weight || '',
        student.admission_no || '',
        student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
        student.state_name || '',
        student.district_name || '',
        student.cur_address || '',
        student.village_ward || '',
        student.gram_panchayat || '',
        student.pin_code || '',
        student.roll_no || '',
        student.contact_no || '',
        student.ifsc_code || '',
        student.bank_name || '',
        student.account_holder || '',
        student.account_holder_name || '',
        student.account_holder_code || '',
        student.session || ''
      ];

      // Escape commas and quotes in data
      var escapedRow = row.map(function(field) {
        var fieldStr = String(field);
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return '"' + fieldStr.replace(/"/g, '""') + '"';
        }
        return fieldStr;
      });

      csvContent += escapedRow.join(',') + '\n';
    });

    // Create and download the file
    var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    var link = document.createElement('a');

    if (link.download !== undefined) {
      var url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};

// Initialize when document is ready
$(document).ready(function() {
  console.log('External JS: Document ready - initializing StudentsPage');
  console.log('External JS: jQuery available:', typeof $ !== 'undefined');
  console.log('External JS: Elements found:');
  console.log('- Toggle button:', $('#toggleFilters').length);
  console.log('- Quick filters:', $('.quick-filter').length);
  console.log('- Student rows:', $('.student-row').length);
  console.log('- View details buttons:', $('.view-details-btn').length);
  console.log('- View student buttons:', $('.view-student-btn').length);
  console.log('- Student modal:', $('#studentModal').length);

  StudentsPage.init();
});
