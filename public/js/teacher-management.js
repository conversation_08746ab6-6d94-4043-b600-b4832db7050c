// Teacher Management Page JavaScript
// Faculty Operations Modal Implementation

// Event delegation for teacher action buttons using jQuery
$(document).ready(function() {
  // Check if enhanced modal is loaded, if so, don't initialize this fallback
  if (window.enhancedTeacherModalLoaded) {
    console.log('Enhanced teacher modal detected, skipping fallback initialization');
    return;
  }

  // View teacher details button (class-based)
  $(document).on('click', '.view-teacher-btn', function(e) {
    e.preventDefault();
    const teacherId = $(this).data('teacher-id');

    if (!teacherId) {
      alert('Error: No teacher ID found');
      return;
    }

    openTeacherModal(teacherId);
  });

  // View teacher details button (ID-based for viewTeacherBtn-#id)
  $(document).on('click', '[id^="viewTeacherBtn-"]', function(e) {
    e.preventDefault();
    const teacherId = $(this).data('teacher-id');

    if (!teacherId) {
      // Extract teacher ID from button ID if data attribute is missing
      const buttonId = $(this).attr('id');
      const extractedId = buttonId.replace('viewTeacherBtn-', '');
      if (extractedId) {
        openTeacherModal(extractedId);
        return;
      }
      alert('Error: No teacher ID found');
      return;
    }

    openTeacherModal(teacherId);
  });

  // Close modal button
  $(document).on('click', '#closeTeacherModalBtn', function() {
    closeTeacherModal();
  });

  // Close modal when clicking outside
  $(document).on('click', '#teacherModal', function(e) {
    if (e.target === this) {
      closeTeacherModal();
    }
  });

  // Initialize all existing teacher view buttons
  initializeTeacherButtons();
});

// Function to initialize all teacher view buttons
function initializeTeacherButtons() {
  // Check if enhanced modal is loaded
  if (window.enhancedTeacherModalLoaded) {
    console.log('Enhanced teacher modal detected, skipping button initialization');
    return;
  }

  // Find all buttons with IDs starting with 'viewTeacherBtn-'
  const buttons = $('[id^="viewTeacherBtn-"]');

  buttons.each(function() {
    const $button = $(this);
    const buttonId = $button.attr('id');
    const teacherId = buttonId.replace('viewTeacherBtn-', '');

    // Remove any existing click handlers to avoid duplicates
    $button.off('click.teacherModal');

    // Add click handler
    $button.on('click.teacherModal', function(e) {
      e.preventDefault();
      e.stopPropagation();

      if (teacherId) {
        openTeacherModal(teacherId);
      } else {
        alert('Error: Invalid teacher ID');
      }
    });
  });
}

// Vanilla JavaScript fallback for teacher buttons
function initializeTeacherButtonsVanilla() {
  // Check if enhanced modal is loaded
  if (window.enhancedTeacherModalLoaded) {
    console.log('Enhanced teacher modal detected, skipping vanilla initialization');
    return;
  }

  const buttons = document.querySelectorAll('[id^="viewTeacherBtn-"]');

  buttons.forEach(function(button) {
    const buttonId = button.id;
    const teacherId = buttonId.replace('viewTeacherBtn-', '');

    // Remove existing event listeners
    button.removeEventListener('click', handleTeacherButtonClick);

    // Add new event listener
    button.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      if (teacherId) {
        openTeacherModal(teacherId);
      } else {
        alert('Error: Invalid teacher ID');
      }
    });
  });
}

// Generic click handler function
function handleTeacherButtonClick(e) {
  e.preventDefault();
  e.stopPropagation();
  
  const buttonId = e.target.closest('button').id;
  const teacherId = buttonId.replace('viewTeacherBtn-', '');
  
  if (teacherId) {
    openTeacherModal(teacherId);
  } else {
    alert('Error: Invalid teacher ID');
  }
}

// Teacher modal functions - Updated to use enhanced profile
function openTeacherModal(teacherId) {
  console.log('openTeacherModal called with teacherId:', teacherId);

  // Check if enhanced modal is loaded via flag
  if (window.enhancedTeacherModalLoaded) {
    console.log('Enhanced modal flag detected, checking for function...');

    // Try to use the enhanced teacher modal functionality
    if (typeof window.openEnhancedTeacherModal === 'function') {
      console.log('Using enhanced teacher modal function');
      window.openEnhancedTeacherModal(teacherId);
      return;
    } else if (typeof openEnhancedTeacherModal === 'function') {
      console.log('Using global enhanced teacher modal function');
      openEnhancedTeacherModal(teacherId);
      return;
    } else {
      console.log('Enhanced modal flag set but function not available, waiting...');
      // Wait a bit for the function to be defined
      setTimeout(function() {
        if (typeof window.openEnhancedTeacherModal === 'function') {
          console.log('Enhanced modal function now available');
          window.openEnhancedTeacherModal(teacherId);
        } else {
          console.log('Enhanced modal function still not available, using fallback');
          openBasicTeacherModal(teacherId);
        }
      }, 200);
      return;
    }
  }

  console.log('Enhanced modal not available, using fallback');
  openBasicTeacherModal(teacherId);
}

// Separate function for basic modal functionality
function openBasicTeacherModal(teacherId) {
  console.log('Using basic teacher modal for teacher ID:', teacherId);

  // Fallback to basic modal if enhanced version is not available
  const modal = document.getElementById('teacherModal');
  const modalTitle = document.getElementById('teacherModalTitle');
  const modalContent = document.getElementById('teacherModalContent');
  const modalFooter = document.getElementById('teacherModalFooter');

  if (!modal || !modalTitle || !modalContent || !modalFooter) {
    console.error('Modal elements not found');
    alert('Error: Modal elements not found');
    return;
  }

  // Set modal title
  modalTitle.innerHTML = '<i class="fas fa-user-tie mr-2"></i>Teacher Details';

  // Show loading
  modalContent.innerHTML = '<div class="flex justify-center items-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-principal-primary"></i><span class="ml-2">Loading teacher details...</span></div>';

  // Show modal
  modal.classList.remove('hidden');

  // Fetch teacher data
  fetch(`/principal/teacher/${teacherId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        const teacher = result.teacher;
        modalContent.innerHTML = generateTeacherViewContent(teacher);
        modalFooter.innerHTML = `
          <button id="closeTeacherModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
          <button onclick="sendMessage(${teacher.id})" class="px-4 py-2 bg-principal-primary text-white rounded-md hover:bg-principal-secondary">
            Send Message
          </button>
        `;
      } else {
        modalContent.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <p class="text-gray-600">${result.message || 'Error loading teacher details'}</p>
          </div>
        `;
        modalFooter.innerHTML = `
          <button id="closeTeacherModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
        `;
      }
    })
    .catch(error => {
      modalContent.innerHTML = `
        <div class="text-center py-8">
          <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
          <p class="text-gray-600">Error loading teacher details. Please try again.</p>
        </div>
      `;
      modalFooter.innerHTML = `
        <button id="closeTeacherModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
          Close
        </button>
      `;
    });
}

function closeTeacherModal() {
  document.getElementById('teacherModal').classList.add('hidden');
}

// Legacy function - now handled by modal
function viewTeacherDetails(teacherId) {
  openTeacherModal(teacherId);
}

// Send message functionality
function sendMessage(teacherId) {
  alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeTeacherModal();
  }
});

// Generate teacher view content for modal
function generateTeacherViewContent(teacher) {
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  return `
    <div class="space-y-6">
      <!-- Teacher Profile Header -->
      <div class="flex items-center space-x-4 p-4 bg-gradient-to-r from-principal-light to-red-50 rounded-lg">
        <div class="flex-shrink-0">
          ${teacher.profile_image ?
            `<img class="h-20 w-20 rounded-full object-cover border-4 border-white shadow-lg" src="${teacher.profile_image}" alt="${teacher.username}">` :
            `<div class="h-20 w-20 rounded-full bg-principal-light flex items-center justify-center border-4 border-white shadow-lg">
              <span class="text-principal-primary text-2xl font-semibold">${teacher.username.substring(0, 2).toUpperCase()}</span>
            </div>`
          }
        </div>
        <div class="flex-1">
          <h3 class="text-2xl font-bold text-principal-dark">${teacher.username}</h3>
          <p class="text-lg text-principal-silver">${teacher.name || teacher.username}</p>
          <div class="flex items-center space-x-4 mt-2">
            ${teacher.last_login ?
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>Recently Active</span>' :
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><span class="w-2 h-2 mr-1 bg-gray-500 rounded-full"></span>No Recent Activity</span>'
            }
            ${teacher.is_active ?
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-principal-light text-principal-primary">Active</span>' :
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>'
            }
          </div>
        </div>
      </div>

      <!-- Teacher Information Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Personal Information -->
        <div class="bg-white border border-principal-light rounded-lg p-4">
          <h4 class="text-lg font-semibold text-principal-dark mb-4 flex items-center">
            <i class="fas fa-user text-principal-primary mr-2"></i>
            Personal Information
          </h4>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-principal-silver">Full Name</dt>
              <dd class="text-sm text-principal-dark">${teacher.name || teacher.username}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Email Address</dt>
              <dd class="text-sm text-principal-dark">${teacher.email}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Date of Birth</dt>
              <dd class="text-sm text-principal-dark">${formatDate(teacher.date_of_birth)}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Bio</dt>
              <dd class="text-sm text-principal-dark">${teacher.bio || 'No bio provided'}</dd>
            </div>
          </dl>
        </div>

        <!-- Professional Information -->
        <div class="bg-white border border-principal-light rounded-lg p-4">
          <h4 class="text-lg font-semibold text-principal-dark mb-4 flex items-center">
            <i class="fas fa-briefcase text-principal-primary mr-2"></i>
            Professional Information
          </h4>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-principal-silver">Role</dt>
              <dd class="text-sm text-principal-dark">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-principal-light text-principal-primary">
                  Teacher
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Subjects</dt>
              <dd class="text-sm text-principal-dark">${teacher.subjects || 'Not assigned'}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Joined Date</dt>
              <dd class="text-sm text-principal-dark">${formatDate(teacher.created_at)}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-principal-silver">Last Login</dt>
              <dd class="text-sm text-principal-dark">${formatDateTime(teacher.last_login)}</dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="bg-white border border-principal-light rounded-lg p-4">
        <h4 class="text-lg font-semibold text-principal-dark mb-4 flex items-center">
          <i class="fas fa-chart-line text-principal-primary mr-2"></i>
          Performance Metrics
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-principal-light rounded-lg">
            <div class="text-2xl font-bold text-principal-primary">${teacher.lecture_count || 0}</div>
            <div class="text-sm text-principal-dark">Total Lectures</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">${teacher.practical_count || 0}</div>
            <div class="text-sm text-green-800">Total Practicals</div>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">${teacher.student_count || 0}</div>
            <div class="text-sm text-blue-800">Students Taught</div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Initialize teacher buttons with vanilla JavaScript as backup
document.addEventListener('DOMContentLoaded', function() {
  // Check if enhanced modal is loaded before initializing fallback
  if (window.enhancedTeacherModalLoaded) {
    console.log('Enhanced teacher modal detected, skipping DOMContentLoaded initialization');
    return;
  }

  setTimeout(function() {
    initializeTeacherButtonsVanilla();
  }, 1000);
});
