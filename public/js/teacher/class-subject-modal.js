/**
 * Class-Subject-Teacher Modal
 * Displays class-wise number of lectures for each subject and the corresponding subject teacher
 */

// Global variables
let classSubjectData = [];
let filteredData = [];

// Initialize the modal
document.addEventListener('DOMContentLoaded', function() {
  // Add event listeners for the modal
  const openModalBtn = document.getElementById('open-class-subject-modal');
  const closeModalBtn = document.getElementById('close-class-subject-modal');
  const closeModalBtnFooter = document.getElementById('close-class-subject-btn');
  const classFilter = document.getElementById('modal-class-filter');
  const subjectFilter = document.getElementById('modal-subject-filter');

  if (openModalBtn) {
    openModalBtn.addEventListener('click', openClassSubjectModal);
  }

  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeClassSubjectModal);
  }

  if (closeModalBtnFooter) {
    closeModalBtnFooter.addEventListener('click', closeClassSubjectModal);
  }

  if (classFilter) {
    classFilter.addEventListener('change', filterClassSubjectData);
  }

  if (subjectFilter) {
    subjectFilter.addEventListener('change', filterClassSubjectData);
  }
});

// Open the modal and fetch data
function openClassSubjectModal() {
  console.log('Opening class-subject modal');
  
  // Show the modal
  const modal = document.getElementById('class-subject-modal');
  if (modal) {
    modal.classList.remove('hidden');
  }
  
  // Show loading indicator
  document.getElementById('class-subject-loading').classList.remove('hidden');
  document.getElementById('class-subject-content').classList.add('hidden');
  document.getElementById('class-subject-no-data').classList.add('hidden');
  
  // Fetch data from API
  fetchClassSubjectData();
}

// Close the modal
function closeClassSubjectModal() {
  console.log('Closing class-subject modal');
  
  const modal = document.getElementById('class-subject-modal');
  if (modal) {
    modal.classList.add('hidden');
  }
}

// Fetch data from API
async function fetchClassSubjectData() {
  try {
    console.log('Fetching class-subject data');
    
    const response = await fetch('/api/teacher/class-subject-data');
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Class-subject data received:', data);
    
    if (data.success) {
      // Store the data
      classSubjectData = data.data;
      
      // Populate filters
      populateFilters(classSubjectData);
      
      // Display the data
      displayClassSubjectData(classSubjectData);
    } else {
      throw new Error(data.message || 'Failed to fetch class-subject data');
    }
  } catch (error) {
    console.error('Error fetching class-subject data:', error);
    
    // Hide loading indicator
    document.getElementById('class-subject-loading').classList.add('hidden');
    
    // Show error message
    document.getElementById('class-subject-no-data').classList.remove('hidden');
    document.getElementById('class-subject-no-data').innerHTML = `
      <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p class="mt-2 text-red-600">Error loading data: ${error.message}</p>
      <button class="mt-2 px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary transition" onclick="fetchClassSubjectData()">
        Try Again
      </button>
    `;
  }
}

// Populate filter dropdowns
function populateFilters(data) {
  // Get unique classes and subjects
  const classes = [...new Set(data.map(item => item.class_name))];
  const subjects = [...new Set(data.map(item => item.subject_name))];
  
  // Sort alphabetically
  classes.sort();
  subjects.sort();
  
  // Populate class filter
  const classFilter = document.getElementById('modal-class-filter');
  if (classFilter) {
    // Clear existing options except the first one
    while (classFilter.options.length > 1) {
      classFilter.remove(1);
    }
    
    // Add new options
    classes.forEach(className => {
      const option = document.createElement('option');
      option.value = className;
      option.textContent = className;
      classFilter.appendChild(option);
    });
  }
  
  // Populate subject filter
  const subjectFilter = document.getElementById('modal-subject-filter');
  if (subjectFilter) {
    // Clear existing options except the first one
    while (subjectFilter.options.length > 1) {
      subjectFilter.remove(1);
    }
    
    // Add new options
    subjects.forEach(subject => {
      const option = document.createElement('option');
      option.value = subject;
      option.textContent = subject;
      subjectFilter.appendChild(option);
    });
  }
}

// Filter data based on selected filters
function filterClassSubjectData() {
  const classFilter = document.getElementById('modal-class-filter').value;
  const subjectFilter = document.getElementById('modal-subject-filter').value;
  
  console.log('Filtering data:', { classFilter, subjectFilter });
  
  // Apply filters
  filteredData = classSubjectData.filter(item => {
    const classMatch = classFilter === 'all' || item.class_name === classFilter;
    const subjectMatch = subjectFilter === 'all' || item.subject_name === subjectFilter;
    return classMatch && subjectMatch;
  });
  
  // Display filtered data
  displayClassSubjectData(filteredData);
}

// Display data in the modal
function displayClassSubjectData(data) {
  // Hide loading indicator
  document.getElementById('class-subject-loading').classList.add('hidden');
  
  // Check if we have data
  if (data.length === 0) {
    document.getElementById('class-subject-content').classList.add('hidden');
    document.getElementById('class-subject-no-data').classList.remove('hidden');
    return;
  }
  
  // Show content
  document.getElementById('class-subject-content').classList.remove('hidden');
  document.getElementById('class-subject-no-data').classList.add('hidden');
  
  // Populate table
  const tableBody = document.getElementById('class-subject-table-body');
  tableBody.innerHTML = '';
  
  data.forEach(item => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.class_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.subject_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.teacher_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.theory_lectures}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.practical_sessions}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.theory_lectures + item.practical_sessions}</td>
    `;
    tableBody.appendChild(row);
  });
  
  // Generate summary
  generateSummary(data);
}

// Generate summary statistics
function generateSummary(data) {
  const summaryContainer = document.getElementById('class-subject-summary');
  summaryContainer.innerHTML = '';
  
  // Calculate total lectures by class
  const classTotals = {};
  data.forEach(item => {
    const className = item.class_name;
    if (!classTotals[className]) {
      classTotals[className] = 0;
    }
    classTotals[className] += item.theory_lectures + item.practical_sessions;
  });
  
  // Calculate total lectures by subject
  const subjectTotals = {};
  data.forEach(item => {
    const subject = item.subject_name;
    if (!subjectTotals[subject]) {
      subjectTotals[subject] = 0;
    }
    subjectTotals[subject] += item.theory_lectures + item.practical_sessions;
  });
  
  // Calculate total lectures by teacher
  const teacherTotals = {};
  data.forEach(item => {
    const teacher = item.teacher_name;
    if (!teacherTotals[teacher]) {
      teacherTotals[teacher] = 0;
    }
    teacherTotals[teacher] += item.theory_lectures + item.practical_sessions;
  });
  
  // Create summary cards
  
  // Class summary
  const classCard = document.createElement('div');
  classCard.className = 'bg-white p-4 rounded-lg shadow';
  classCard.innerHTML = `
    <h4 class="text-sm font-medium text-gray-700 mb-2">Lectures by Class</h4>
    <ul class="space-y-1">
      ${Object.entries(classTotals)
        .sort((a, b) => b[1] - a[1])
        .map(([className, total]) => `
          <li class="flex justify-between">
            <span class="text-sm text-gray-600">${className}</span>
            <span class="text-sm font-medium">${total}</span>
          </li>
        `).join('')}
    </ul>
  `;
  summaryContainer.appendChild(classCard);
  
  // Subject summary
  const subjectCard = document.createElement('div');
  subjectCard.className = 'bg-white p-4 rounded-lg shadow';
  subjectCard.innerHTML = `
    <h4 class="text-sm font-medium text-gray-700 mb-2">Lectures by Subject</h4>
    <ul class="space-y-1">
      ${Object.entries(subjectTotals)
        .sort((a, b) => b[1] - a[1])
        .map(([subject, total]) => `
          <li class="flex justify-between">
            <span class="text-sm text-gray-600">${subject}</span>
            <span class="text-sm font-medium">${total}</span>
          </li>
        `).join('')}
    </ul>
  `;
  summaryContainer.appendChild(subjectCard);
  
  // Teacher summary
  const teacherCard = document.createElement('div');
  teacherCard.className = 'bg-white p-4 rounded-lg shadow';
  teacherCard.innerHTML = `
    <h4 class="text-sm font-medium text-gray-700 mb-2">Lectures by Teacher</h4>
    <ul class="space-y-1">
      ${Object.entries(teacherTotals)
        .sort((a, b) => b[1] - a[1])
        .map(([teacher, total]) => `
          <li class="flex justify-between">
            <span class="text-sm text-gray-600">${teacher}</span>
            <span class="text-sm font-medium">${total}</span>
          </li>
        `).join('')}
    </ul>
  `;
  summaryContainer.appendChild(teacherCard);
}
