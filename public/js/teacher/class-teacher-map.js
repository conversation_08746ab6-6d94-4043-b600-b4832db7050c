// Class-Teacher Mind Map Visualization
document.addEventListener('DOMContentLoaded', function() {
  console.log('Class-Teacher Mind Map script loaded');
  
  // Fetch data from API
  fetchMindMapData();
});

// Fetch data from API
async function fetchMindMapData() {
  try {
    console.log('Fetching mind map data...');
    const response = await fetch('/api/teacher/class-teacher-map');
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Mind map data received:', data);
    
    if (data.success) {
      // Hide loading indicator
      document.getElementById('loading-indicator').style.display = 'none';
      
      // Render the mind map
      renderMindMap(data.relationships);
    } else {
      throw new Error(data.message || 'Failed to fetch mind map data');
    }
  } catch (error) {
    console.error('Error fetching mind map data:', error);
    document.getElementById('loading-indicator').innerHTML = `
      <div class="text-center">
        <p class="text-red-500 mb-2">Failed to load visualization data</p>
        <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600" onclick="fetchMindMapData()">
          Try Again
        </button>
      </div>
    `;
  }
}

// Render mind map using D3.js
function renderMindMap(relationships) {
  // Get container dimensions
  const container = document.getElementById('mind-map-container');
  const width = container.clientWidth;
  const height = container.clientHeight;
  
  // Process data for D3 force layout
  const nodes = [];
  const links = [];
  
  // Create a map to track unique nodes
  const nodeMap = new Map();
  
  // Process relationships to create nodes and links
  relationships.forEach(rel => {
    // Create class node if it doesn't exist
    if (!nodeMap.has(`class-${rel.class_id}`)) {
      nodeMap.set(`class-${rel.class_id}`, {
        id: `class-${rel.class_id}`,
        name: rel.class_name,
        type: 'class',
        index: nodes.length
      });
      nodes.push(nodeMap.get(`class-${rel.class_id}`));
    }
    
    // Create subject node if it doesn't exist
    if (!nodeMap.has(`subject-${rel.subject_name}`)) {
      nodeMap.set(`subject-${rel.subject_name}`, {
        id: `subject-${rel.subject_name}`,
        name: rel.subject_name,
        type: 'subject',
        index: nodes.length
      });
      nodes.push(nodeMap.get(`subject-${rel.subject_name}`));
    }
    
    // Create teacher node if it doesn't exist
    if (!nodeMap.has(`teacher-${rel.teacher_id}`)) {
      nodeMap.set(`teacher-${rel.teacher_id}`, {
        id: `teacher-${rel.teacher_id}`,
        name: rel.teacher_name,
        type: 'teacher',
        index: nodes.length
      });
      nodes.push(nodeMap.get(`teacher-${rel.teacher_id}`));
    }
    
    // Create links between nodes
    links.push({
      source: nodeMap.get(`class-${rel.class_id}`).index,
      target: nodeMap.get(`subject-${rel.subject_name}`).index,
      value: 1
    });
    
    links.push({
      source: nodeMap.get(`subject-${rel.subject_name}`).index,
      target: nodeMap.get(`teacher-${rel.teacher_id}`).index,
      value: 1
    });
  });
  
  // Create SVG element
  const svg = d3.select('#mind-map-container')
    .append('svg')
    .attr('width', width)
    .attr('height', height);
  
  // Create a group for the visualization
  const g = svg.append('g');
  
  // Add zoom behavior
  svg.call(d3.zoom()
    .extent([[0, 0], [width, height]])
    .scaleExtent([0.1, 4])
    .on('zoom', (event) => {
      g.attr('transform', event.transform);
    }));
  
  // Create force simulation
  const simulation = d3.forceSimulation(nodes)
    .force('link', d3.forceLink(links).id(d => d.index).distance(100))
    .force('charge', d3.forceManyBody().strength(-300))
    .force('center', d3.forceCenter(width / 2, height / 2))
    .force('collision', d3.forceCollide().radius(50));
  
  // Create links
  const link = g.append('g')
    .attr('class', 'links')
    .selectAll('line')
    .data(links)
    .enter()
    .append('line')
    .attr('stroke', '#999')
    .attr('stroke-opacity', 0.6)
    .attr('stroke-width', d => Math.sqrt(d.value));
  
  // Create node groups
  const node = g.append('g')
    .attr('class', 'nodes')
    .selectAll('g')
    .data(nodes)
    .enter()
    .append('g')
    .call(d3.drag()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended));
  
  // Add circles to nodes with different colors based on type
  node.append('circle')
    .attr('r', 10)
    .attr('fill', d => {
      switch(d.type) {
        case 'class': return '#3b82f6'; // blue
        case 'subject': return '#10b981'; // green
        case 'teacher': return '#8b5cf6'; // purple
        default: return '#6b7280'; // gray
      }
    });
  
  // Add labels to nodes
  node.append('text')
    .attr('dx', 15)
    .attr('dy', '.35em')
    .text(d => d.name)
    .style('font-size', '12px')
    .style('fill', '#374151');
  
  // Add title for hover tooltip
  node.append('title')
    .text(d => {
      switch(d.type) {
        case 'class': return `Class: ${d.name}`;
        case 'subject': return `Subject: ${d.name}`;
        case 'teacher': return `Teacher: ${d.name}`;
        default: return d.name;
      }
    });
  
  // Update positions on each tick of the simulation
  simulation.on('tick', () => {
    link
      .attr('x1', d => d.source.x)
      .attr('y1', d => d.source.y)
      .attr('x2', d => d.target.x)
      .attr('y2', d => d.target.y);
    
    node
      .attr('transform', d => `translate(${d.x},${d.y})`);
  });
  
  // Drag functions
  function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
  }
  
  function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
  }
  
  function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
  }
}
