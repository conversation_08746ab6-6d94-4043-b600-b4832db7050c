/**
 * Common JavaScript functions for the teacher view
 */

// Function to show toast notifications
function showToast(type, title, message) {
  // Check if Toastify is available
  if (typeof Toastify === 'function') {
    // Use Toastify library
    let bgColor = '#4a5568'; // Default gray
    if (type === 'success') bgColor = '#10B981';
    if (type === 'error') bgColor = '#EF4444';
    if (type === 'warning') bgColor = '#F59E0B';
    if (type === 'info') bgColor = '#3B82F6';

    Toastify({
      text: title ? `${title}: ${message}` : message,
      duration: 5000,
      close: true,
      gravity: "top",
      position: "right",
      backgroundColor: bgColor,
      stopOnFocus: true
    }).showToast();

    return;
  }

  // Fallback to custom implementation if Toastify is not available
  // Make sure toast container exists
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
    document.body.appendChild(toastContainer);
  }

  const toast = document.createElement('div');
  toast.className = `toast ${type} animate-fade-in-down`;

  let bgColor = 'bg-gray-800';
  if (type === 'success') bgColor = 'bg-green-500';
  if (type === 'error') bgColor = 'bg-red-500';
  if (type === 'warning') bgColor = 'bg-yellow-500';
  if (type === 'info') bgColor = 'bg-blue-500';

  toast.innerHTML = `
    <div class="${bgColor} text-white px-4 py-3 rounded shadow-md flex items-center justify-between">
      <div>
        <p class="font-bold">${title}</p>
        <p class="text-sm">${message}</p>
      </div>
      <button class="ml-4 text-white" onclick="this.parentElement.parentElement.remove()">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  `;

  toastContainer.appendChild(toast);

  // Auto remove after 5 seconds
  setTimeout(() => {
    toast.remove();
  }, 5000);
}

// Function to initialize confirmation dialogs
function initConfirmDialog(title, message, confirmCallback) {
  const dialog = document.getElementById('confirmation-dialog');
  const titleElement = document.getElementById('confirmation-title');
  const messageElement = document.getElementById('confirmation-message');
  const confirmButton = document.getElementById('confirm-action');
  const cancelButton = document.getElementById('cancel-action');

  // Set dialog content
  titleElement.textContent = title;
  messageElement.textContent = message;

  // Show dialog
  dialog.classList.remove('hidden');

  // Remove old event listeners
  const newConfirmButton = confirmButton.cloneNode(true);
  const newCancelButton = cancelButton.cloneNode(true);
  confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);
  cancelButton.parentNode.replaceChild(newCancelButton, cancelButton);

  // Add new event listeners
  newConfirmButton.addEventListener('click', () => {
    dialog.classList.add('hidden');
    if (typeof confirmCallback === 'function') {
      confirmCallback();
    }
  });

  newCancelButton.addEventListener('click', () => {
    dialog.classList.add('hidden');
  });
}

// Function to make API calls
async function apiCall(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(endpoint, options);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'API call failed');
    }

    return result;
  } catch (error) {
    console.error('API call error:', error);
    showToast('error', 'Error', error.message || 'An error occurred while processing your request');
    throw error;
  }
}

// Make functions available globally
window.showToast = showToast;
window.initConfirmDialog = initConfirmDialog;
window.apiCall = apiCall;

// Log to console to verify the script is loaded
console.log('Teacher common.js loaded successfully');
