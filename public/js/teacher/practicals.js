// Practicals management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize toast container
    initializeToastContainer();
    
    // Initialize chosen dropdowns
    initializeChosenDropdowns();
    
    // Set default dates/times
    setDefaultDateTime();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Load data
    loadPracticals();
});

// Toast notification system
function initializeToastContainer() {
    // Create toast container if it doesn't exist
    if (!document.querySelector('.toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
}

function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container');
    const toastId = 'toast-' + Date.now();
    
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();
    
    // Remove toast after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// Initialize form dropdowns with Chosen
function initializeChosenDropdowns() {
    $('.chosen-enable').chosen({
        width: '100%',
        search_contains: true,
        no_results_text: "No results match",
        allow_single_deselect: true
    });
    
    // Adjust z-index for chosen dropdowns
    $('.chosen-container').css('z-index', '1050');
}

// Set default date and time values
function setDefaultDateTime() {
    const dateInput = document.getElementById('scheduleDate');
    const startTimeInput = document.getElementById('scheduleStartTime');
    const endTimeInput = document.getElementById('scheduleEndTime');
    
    // Set min date to today
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    dateInput.min = formattedDate;
    dateInput.value = formattedDate;
    
    // Set default start time to 30 minutes from now (rounded to nearest half hour)
    const currentMinutes = today.getMinutes();
    today.setMinutes(currentMinutes + (30 - (currentMinutes % 30)));
    const hours = String(today.getHours()).padStart(2, '0');
    const minutes = String(today.getMinutes()).padStart(2, '0');
    startTimeInput.value = `${hours}:${minutes}`;
    
    // Set default end time to 90 minutes after start time
    const endTime = new Date(today);
    endTime.setMinutes(endTime.getMinutes() + 90);
    const endHours = String(endTime.getHours()).padStart(2, '0');
    const endMinutes = String(endTime.getMinutes()).padStart(2, '0');
    endTimeInput.value = `${endHours}:${endMinutes}`;
}

// Initialize event listeners
function initializeEventListeners() {
    // Schedule form submission
    const scheduleForm = document.getElementById('scheduleForm');
    if (scheduleForm) {
        scheduleForm.addEventListener('submit', handleScheduleSubmit);
    }
    
    // Filter form submission
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.addEventListener('submit', handleFilterSubmit);
    }
    
    // Clear filters button
    const clearFiltersBtn = document.getElementById('clearFilters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearFilters);
    }
    
    // Date and time validation
    const scheduleDate = document.getElementById('scheduleDate');
    const scheduleStartTime = document.getElementById('scheduleStartTime');
    const scheduleEndTime = document.getElementById('scheduleEndTime');
    
    if (scheduleDate && scheduleStartTime && scheduleEndTime) {
        scheduleDate.addEventListener('change', validateDateTime);
        scheduleStartTime.addEventListener('change', function() {
            validateDateTime();
            updateEndTime();
        });
        scheduleEndTime.addEventListener('change', validateDateTime);
    }
}

// Validate the date and time inputs
function validateDateTime() {
    const dateInput = document.getElementById('scheduleDate');
    const startTimeInput = document.getElementById('scheduleStartTime');
    const endTimeInput = document.getElementById('scheduleEndTime');
    
    if (!dateInput || !startTimeInput || !endTimeInput) return true;
    
    const selectedDate = new Date(dateInput.value);
    const [startHours, startMinutes] = startTimeInput.value.split(':').map(Number);
    const selectedStartTime = new Date(selectedDate);
    selectedStartTime.setHours(startHours, startMinutes);
    
    const [endHours, endMinutes] = endTimeInput.value.split(':').map(Number);
    const selectedEndTime = new Date(selectedDate);
    selectedEndTime.setHours(endHours, endMinutes);
    
    const now = new Date();
    
    // Clear previous validation messages
    document.getElementById('dateTimeError').textContent = '';
    
    // Check if date and time are in the future
    if (selectedStartTime <= now) {
        document.getElementById('dateTimeError').textContent = 'The scheduled start time must be in the future.';
        return false;
    }
    
    // Check if end time is after start time
    if (selectedEndTime <= selectedStartTime) {
        document.getElementById('dateTimeError').textContent = 'End time must be after start time.';
        return false;
    }
    
    return true;
}

// Update end time to be 90 minutes after start time
function updateEndTime() {
    const startTimeInput = document.getElementById('scheduleStartTime');
    const endTimeInput = document.getElementById('scheduleEndTime');
    
    if (!startTimeInput || !endTimeInput) return;
    
    const [hours, minutes] = startTimeInput.value.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes);
    
    const endDate = new Date(startDate);
    endDate.setMinutes(endDate.getMinutes() + 90);
    
    const endHours = String(endDate.getHours()).padStart(2, '0');
    const endMinutes = String(endDate.getMinutes()).padStart(2, '0');
    endTimeInput.value = `${endHours}:${endMinutes}`;
}

// Handle schedule form submission
async function handleScheduleSubmit(e) {
    e.preventDefault();
    
    if (!validateDateTime()) {
        return;
    }
    
    // Get form data
    const form = e.target;
    const formData = new FormData(form);
    const data = {};
    
    // Convert FormData to JSON object
    for (const [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Get selected topics using Chosen
    const topicsSelect = $('#scheduleTopics');
    data.topics = topicsSelect.chosen().val() || [];
    
    // Validate required fields
    if (!data.class_id) {
        showToast('Please select a class', 'danger');
        return;
    }
    
    if (!data.subject_id) {
        showToast('Please select a subject', 'danger');
        return;
    }
    
    if (!data.topics || data.topics.length === 0) {
        showToast('Please select at least one topic', 'danger');
        return;
    }
    
    if (!data.lab_id) {
        showToast('Please select a lab', 'danger');
        return;
    }
    
    // Check if editing or creating
    const practicalId = form.dataset.practicalId;
    let url = '/teacher/practicals';
    let method = 'POST';
    
    if (practicalId) {
        url = `/teacher/practicals/${practicalId}`;
        method = 'PUT';
    }
    
    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast(result.message, 'success');
            
            // Reset form and reload practicals
            form.reset();
            setDefaultDateTime();
            initializeChosenDropdowns();
            
            // Reset edit mode
            form.dataset.practicalId = '';
            document.getElementById('scheduleModalLabel').textContent = 'Schedule a Practical';
            document.getElementById('scheduleSubmitBtn').textContent = 'Schedule';
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
            if (modal) {
                modal.hide();
            }
            
            // Reload practicals
            loadPracticals();
        } else {
            showToast(result.message || 'An error occurred', 'danger');
        }
    } catch (error) {
        console.error('Error submitting form:', error);
        showToast('Failed to submit the form. Please try again.', 'danger');
    }
}

// Load practicals data
async function loadPracticals() {
    try {
        // Show loading indicator
        const practicalsTable = document.getElementById('practicalsTable');
        const tableBody = practicalsTable.querySelector('tbody');
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Loading...</td></tr>';
        
        // Get the current filter values
        const filters = getFilterValues();
        
        // Construct query string
        const queryParams = new URLSearchParams(filters).toString();
        const url = queryParams ? `/teacher/practicals?${queryParams}` : '/teacher/practicals';
        
        const response = await fetch(url);
        const data = await response.json();
        
        // Clear loading indicator
        tableBody.innerHTML = '';
        
        if (data.practicals && data.practicals.length > 0) {
            // Display practicals
            data.practicals.forEach(practical => {
                const row = createPracticalRow(practical);
                tableBody.appendChild(row);
            });
        } else {
            // Show no data message
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No practicals found</td></tr>';
        }
    } catch (error) {
        console.error('Error loading practicals:', error);
        showToast('Failed to load practicals. Please try again.', 'danger');
    }
}

// Create a table row for a practical
function createPracticalRow(practical) {
    const row = document.createElement('tr');
    
    // Convert date to readable format
    const practicalDate = new Date(practical.date);
    const dateOptions = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
    const formattedDate = practicalDate.toLocaleDateString('en-US', dateOptions);
    
    // Format start and end times
    const startTime = practical.start_time.substring(0, 5);
    const endTime = practical.end_time.substring(0, 5);
    
    // Set row class based on status
    if (practical.status === 'Cancelled') {
        row.classList.add('table-danger');
    } else if (practical.status === 'Completed') {
        row.classList.add('table-success');
    } else {
        const now = new Date();
        const startDateTime = new Date(`${practical.date}T${practical.start_time}`);
        
        if (startDateTime < now) {
            row.classList.add('table-warning');
        }
    }
    
    // Format topics
    const topicsText = practical.topics.join(', ');
    
    row.innerHTML = `
        <td>
            <div class="fw-bold">${practical.class_name} ${practical.trade} ${practical.section}</div>
            <div class="small text-muted">${practical.subject_name}</div>
        </td>
        <td>
            <div>${formattedDate}</div>
            <div class="small text-muted">${startTime} - ${endTime}</div>
        </td>
        <td>${topicsText}</td>
        <td><span class="badge ${getStatusBadgeClass(practical.status)}">${practical.status}</span></td>
        <td>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Actions
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item view-practical" href="#" data-id="${practical.id}">View Details</a></li>
                    ${practical.status === 'Upcoming' ? `
                        <li><a class="dropdown-item edit-practical" href="#" data-id="${practical.id}">Edit</a></li>
                        <li><a class="dropdown-item cancel-practical" href="#" data-id="${practical.id}">Cancel</a></li>
                        <li><a class="dropdown-item complete-practical" href="#" data-id="${practical.id}">Mark as Completed</a></li>
                    ` : ''}
                    ${practical.status === 'Completed' ? `
                        <li><a class="dropdown-item view-records" href="#" data-id="${practical.id}">View Records</a></li>
                    ` : ''}
                </ul>
            </div>
        </td>
    `;
    
    // Add event listeners
    addRowEventListeners(row, practical.id);
    
    return row;
}

// Get the CSS class for status badges
function getStatusBadgeClass(status) {
    switch (status) {
        case 'Upcoming':
            return 'bg-primary';
        case 'Completed':
            return 'bg-success';
        case 'Cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// Add event listeners to the practical row actions
function addRowEventListeners(row, practicalId) {
    // View practical details
    const viewLink = row.querySelector('.view-practical');
    if (viewLink) {
        viewLink.addEventListener('click', function(e) {
            e.preventDefault();
            viewPracticalDetails(practicalId);
        });
    }
    
    // Edit practical
    const editLink = row.querySelector('.edit-practical');
    if (editLink) {
        editLink.addEventListener('click', function(e) {
            e.preventDefault();
            editPractical(practicalId);
        });
    }
    
    // Cancel practical
    const cancelLink = row.querySelector('.cancel-practical');
    if (cancelLink) {
        cancelLink.addEventListener('click', function(e) {
            e.preventDefault();
            cancelPractical(practicalId);
        });
    }
    
    // Mark practical as completed
    const completeLink = row.querySelector('.complete-practical');
    if (completeLink) {
        completeLink.addEventListener('click', function(e) {
            e.preventDefault();
            completePractical(practicalId);
        });
    }
    
    // View student records
    const recordsLink = row.querySelector('.view-records');
    if (recordsLink) {
        recordsLink.addEventListener('click', function(e) {
            e.preventDefault();
            viewStudentRecords(practicalId);
        });
    }
}

// View practical details
async function viewPracticalDetails(practicalId) {
    try {
        const response = await fetch(`/teacher/practicals/${practicalId}`);
        const practical = await response.json();
        
        if (!practical || practical.message) {
            showToast('Failed to load practical details.', 'danger');
            return;
        }
        
        // Format date
        const practicalDate = new Date(practical.date);
        const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        const formattedDate = practicalDate.toLocaleDateString('en-US', dateOptions);
        
        // Format times
        const startTime = practical.start_time.substring(0, 5);
        const endTime = practical.end_time.substring(0, 5);
        
        // Format topics
        const topicsHtml = practical.topics.map(topic => `<span class="badge bg-info me-1">${topic.name}</span>`).join('');
        
        // Create modal content
        const modalHtml = `
            <div class="modal fade" id="viewPracticalModal" tabindex="-1" aria-labelledby="viewPracticalModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="viewPracticalModalLabel">Practical Details</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Class:</h6>
                                    <p>${practical.class_name} ${practical.trade} ${practical.section}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Subject:</h6>
                                    <p>${practical.subject_name}</p>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Date:</h6>
                                    <p>${formattedDate}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Time:</h6>
                                    <p>${startTime} - ${endTime}</p>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Status:</h6>
                                    <p><span class="badge ${getStatusBadgeClass(practical.status)}">${practical.status}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Lab:</h6>
                                    <p>${practical.lab_id}</p>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="fw-bold">Topics:</h6>
                                <p>${topicsHtml}</p>
                            </div>
                            
                            ${practical.description ? `
                                <div class="mb-3">
                                    <h6 class="fw-bold">Description:</h6>
                                    <p>${practical.description}</p>
                                </div>
                            ` : ''}
                            
                            ${practical.prerequisites ? `
                                <div class="mb-3">
                                    <h6 class="fw-bold">Prerequisites:</h6>
                                    <p>${practical.prerequisites}</p>
                                </div>
                            ` : ''}
                            
                            ${practical.materials ? `
                                <div class="mb-3">
                                    <h6 class="fw-bold">Materials:</h6>
                                    <p>${practical.materials}</p>
                                </div>
                            ` : ''}
                            
                            ${practical.equipment ? `
                                <div class="mb-3">
                                    <h6 class="fw-bold">Equipment:</h6>
                                    <p>${practical.equipment}</p>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            ${practical.status === 'Upcoming' ? `
                                <button type="button" class="btn btn-primary edit-button" data-id="${practical.id}">Edit</button>
                                <button type="button" class="btn btn-danger cancel-button" data-id="${practical.id}">Cancel Practical</button>
                                <button type="button" class="btn btn-success complete-button" data-id="${practical.id}">Mark as Completed</button>
                            ` : ''}
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Initialize modal
        const modal = new bootstrap.Modal(document.getElementById('viewPracticalModal'));
        modal.show();
        
        // Add event listeners for action buttons
        const editButton = document.querySelector('#viewPracticalModal .edit-button');
        if (editButton) {
            editButton.addEventListener('click', function() {
                modal.hide();
                document.getElementById('viewPracticalModal').remove();
                editPractical(practicalId);
            });
        }
        
        const cancelButton = document.querySelector('#viewPracticalModal .cancel-button');
        if (cancelButton) {
            cancelButton.addEventListener('click', function() {
                modal.hide();
                document.getElementById('viewPracticalModal').remove();
                cancelPractical(practicalId);
            });
        }
        
        const completeButton = document.querySelector('#viewPracticalModal .complete-button');
        if (completeButton) {
            completeButton.addEventListener('click', function() {
                modal.hide();
                document.getElementById('viewPracticalModal').remove();
                completePractical(practicalId);
            });
        }
        
        // Remove modal from DOM when hidden
        document.getElementById('viewPracticalModal').addEventListener('hidden.bs.modal', function() {
            document.getElementById('viewPracticalModal').remove();
        });
    } catch (error) {
        console.error('Error viewing practical details:', error);
        showToast('Failed to load practical details.', 'danger');
    }
}

// Edit practical
async function editPractical(practicalId) {
    try {
        const response = await fetch(`/teacher/practicals/${practicalId}`);
        const practical = await response.json();
        
        if (!practical || practical.message) {
            showToast('Failed to load practical details for editing.', 'danger');
            return;
        }
        
        // Set form values
        const form = document.getElementById('scheduleForm');
        form.dataset.practicalId = practicalId;
        
        // Update modal title and button text
        document.getElementById('scheduleModalLabel').textContent = 'Edit Practical';
        document.getElementById('scheduleSubmitBtn').textContent = 'Update';
        
        // Set form fields
        document.getElementById('scheduleDate').value = practical.date;
        document.getElementById('scheduleStartTime').value = practical.start_time.substring(0, 5);
        document.getElementById('scheduleEndTime').value = practical.end_time.substring(0, 5);
        
        document.getElementById('scheduleClass').value = practical.class_id;
        document.getElementById('scheduleSubject').value = practical.subject_id;
        document.getElementById('scheduleLab').value = practical.lab_id;
        
        document.getElementById('scheduleDescription').value = practical.description || '';
        document.getElementById('schedulePrerequisites').value = practical.prerequisites || '';
        document.getElementById('scheduleMaterials').value = practical.materials || '';
        document.getElementById('scheduleEquipment').value = practical.equipment || '';
        
        // Set topics with Chosen
        const topicsSelect = $('#scheduleTopics');
        const topicIds = practical.topics.map(topic => topic.id.toString());
        topicsSelect.val(topicIds);
        topicsSelect.trigger('chosen:updated');
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading practical for editing:', error);
        showToast('Failed to load practical details for editing.', 'danger');
    }
}

// Cancel practical
function cancelPractical(practicalId) {
    if (confirm('Are you sure you want to cancel this practical?')) {
        fetch(`/teacher/practicals/${practicalId}/cancel`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast(result.message, 'success');
                loadPracticals();
            } else {
                showToast(result.message || 'An error occurred', 'danger');
            }
        })
        .catch(error => {
            console.error('Error cancelling practical:', error);
            showToast('Failed to cancel the practical. Please try again.', 'danger');
        });
    }
}

// Mark practical as completed
function completePractical(practicalId) {
    if (confirm('Are you sure you want to mark this practical as completed?')) {
        fetch(`/teacher/practicals/${practicalId}/complete`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast(result.message, 'success');
                loadPracticals();
            } else {
                showToast(result.message || 'An error occurred', 'danger');
            }
        })
        .catch(error => {
            console.error('Error completing practical:', error);
            showToast('Failed to mark the practical as completed. Please try again.', 'danger');
        });
    }
}

// View student records
async function viewStudentRecords(practicalId) {
    try {
        const response = await fetch(`/teacher/practicals/${practicalId}/records`);
        const data = await response.json();
        
        if (!data.success) {
            showToast(data.message || 'Failed to load student records.', 'danger');
            return;
        }
        
        const practical = data.practical;
        const records = data.records;
        
        // Format date
        const practicalDate = new Date(practical.date);
        const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        const formattedDate = practicalDate.toLocaleDateString('en-US', dateOptions);
        
        // Create records table rows
        let recordsHtml = '';
        
        if (records.length === 0) {
            recordsHtml = '<tr><td colspan="5" class="text-center">No student records found</td></tr>';
        } else {
            records.forEach(record => {
                recordsHtml += `
                    <tr>
                        <td>${record.roll_number}</td>
                        <td>${record.first_name} ${record.last_name}</td>
                        <td>
                            <span class="badge ${getAttendanceBadgeClass(record.attendance)}">${record.attendance}</span>
                        </td>
                        <td>${record.completion_percentage}%</td>
                        <td>${record.performance}</td>
                    </tr>
                `;
            });
        }
        
        // Create modal content
        const modalHtml = `
            <div class="modal fade" id="viewRecordsModal" tabindex="-1" aria-labelledby="viewRecordsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="viewRecordsModalLabel">Student Records</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <h6 class="fw-bold">Practical:</h6>
                                <p>${practical.subject_name} - ${practical.class_name} ${practical.trade} ${practical.section}</p>
                                <p>Date: ${formattedDate}</p>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Roll No.</th>
                                            <th>Name</th>
                                            <th>Attendance</th>
                                            <th>Completion</th>
                                            <th>Performance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${recordsHtml}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Initialize modal
        const modal = new bootstrap.Modal(document.getElementById('viewRecordsModal'));
        modal.show();
        
        // Remove modal from DOM when hidden
        document.getElementById('viewRecordsModal').addEventListener('hidden.bs.modal', function() {
            document.getElementById('viewRecordsModal').remove();
        });
    } catch (error) {
        console.error('Error viewing student records:', error);
        showToast('Failed to load student records.', 'danger');
    }
}

// Get badge class for attendance status
function getAttendanceBadgeClass(attendance) {
    switch (attendance) {
        case 'Present':
            return 'bg-success';
        case 'Absent':
            return 'bg-danger';
        case 'Late':
            return 'bg-warning';
        default:
            return 'bg-secondary';
    }
}

// Filter form submission
function handleFilterSubmit(e) {
    e.preventDefault();
    loadPracticals();
}

// Get filter values
function getFilterValues() {
    const filters = {};
    
    const classFilter = document.getElementById('filterClass');
    if (classFilter && classFilter.value) {
        filters.class_id = classFilter.value;
    }
    
    const subjectFilter = document.getElementById('filterSubject');
    if (subjectFilter && subjectFilter.value) {
        filters.subject_id = subjectFilter.value;
    }
    
    const statusFilter = document.getElementById('filterStatus');
    if (statusFilter && statusFilter.value) {
        filters.status = statusFilter.value;
    }
    
    const dateFromFilter = document.getElementById('filterDateFrom');
    if (dateFromFilter && dateFromFilter.value) {
        filters.date_from = dateFromFilter.value;
    }
    
    const dateToFilter = document.getElementById('filterDateTo');
    if (dateToFilter && dateToFilter.value) {
        filters.date_to = dateToFilter.value;
    }
    
    return filters;
}

// Clear filters
function clearFilters() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.reset();
        
        // Reset chosen dropdowns
        $('.chosen-enable').val('').trigger('chosen:updated');
        
        // Reload practicals
        loadPracticals();
    }
} 