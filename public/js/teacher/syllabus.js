/**
 * Syllabus Management JavaScript
 * Handles all functionality for the teacher syllabus page
 */

// Variables to store fetched data
let allTopics = [];
let allClasses = [];
let allSubjects = [];

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    console.log('Syllabus.js loaded');

    // Initialize event listeners
    initializeEventListeners();
});

// Initialize event listeners
function initializeEventListeners() {
    console.log('Initializing event listeners');

    // Section toggle buttons
    const sectionToggles = document.querySelectorAll('.section-toggle');
    console.log('Found', sectionToggles.length, 'section toggles');

    sectionToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section-id');
            console.log('Toggle clicked for section:', sectionId);
            toggleSection(sectionId);
        });
    });

    // Edit topic buttons
    const editButtons = document.querySelectorAll('.edit-topic-btn');
    console.log('Found', editButtons.length, 'edit buttons');

    editButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const topicId = this.getAttribute('data-topic-id');
            console.log('Edit button clicked for topic:', topicId);
            editTopic(topicId);
        });
    });

    // Filter dropdowns
    const classFilter = document.getElementById('class-filter');
    const subjectFilter = document.getElementById('subject-filter');
    const statusFilter = document.getElementById('status-filter');

    if (classFilter) {
        classFilter.addEventListener('change', filterSyllabus);
        console.log('Class filter listener added');
    }

    if (subjectFilter) {
        subjectFilter.addEventListener('change', filterSyllabus);
        console.log('Subject filter listener added');
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', filterSyllabus);
        console.log('Status filter listener added');
    }

    // Search input
    const searchInput = document.getElementById('search-topics');
    if (searchInput) {
        searchInput.addEventListener('input', searchTopics);
        console.log('Search input listener added');
    }

    // Topic edit modal close buttons
    const closeButtons = document.querySelectorAll('#close-topic-modal, #close-topic-btn');
    closeButtons.forEach(button => {
        if (button) {
            button.addEventListener('click', closeTopicModal);
            console.log('Close button listener added');
        }
    });

    // Save topic button
    const saveButton = document.getElementById('save-topic-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveTopic);
        console.log('Save button listener added');
    }

    // Manage topics button
    const manageTopicsBtn = document.getElementById('manage-topics-btn');
    if (manageTopicsBtn) {
        manageTopicsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Manage topics button clicked');
            console.log('Topic management will be available soon');
        });
    }
}

// Toggle section visibility
function toggleSection(sectionId) {
    console.log('Toggling section:', sectionId);

    const content = document.getElementById(`${sectionId}-content`);
    const icon = document.getElementById(`${sectionId}-icon`);

    if (!content) {
        console.error('Section content not found:', sectionId + '-content');
        return;
    }

    if (!icon) {
        console.error('Section icon not found:', sectionId + '-icon');
        return;
    }

    // Use classList to toggle classes instead of manually checking
    if (icon.classList.contains('rotate-180')) {
        icon.classList.remove('rotate-180');
        content.classList.remove('hidden');
        console.log('Section expanded');
    } else {
        icon.classList.add('rotate-180');
        content.classList.add('hidden');
        console.log('Section collapsed');
    }
}

// Edit topic function
function editTopic(topicId) {
    console.log('Editing topic:', topicId);

    // Get topic info from the DOM
    const topicElement = document.querySelector(`.edit-topic-btn[data-topic-id="${topicId}"]`).closest('.flex.items-center.justify-between');

    if (!topicElement) {
        console.error('Topic element not found for ID:', topicId);
        return;
    }

    // Get topic name from the DOM
    const topicName = topicElement.querySelector('.text-sm').textContent.trim();

    // Get topic status from the DOM
    const statusText = topicElement.querySelector('.text-xs').textContent.trim().toLowerCase();

    console.log('Topic data from DOM:', { name: topicName, status: statusText });

    // Populate edit modal
    document.getElementById('topic-name').value = topicName;
    document.getElementById('topic-status').value = statusText;

    // Set a default completion date if status is completed
    if (statusText === 'completed') {
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        document.getElementById('completion-date').value = formattedDate;
    } else {
        document.getElementById('completion-date').value = '';
    }

    // Set topic ID for save button
    document.getElementById('save-topic-btn').setAttribute('data-topic-id', topicId);

    // Show modal
    document.getElementById('topic-edit-modal').classList.remove('hidden');
}

// Close topic edit modal
function closeTopicModal() {
    console.log('Closing topic modal');
    document.getElementById('topic-edit-modal').classList.add('hidden');
}

// Save topic changes
function saveTopic() {
    console.log('Saving topic changes');

    // Get form values
    const topicId = document.getElementById('save-topic-btn').getAttribute('data-topic-id');
    const statusValue = document.getElementById('topic-status').value;
    const completionDate = document.getElementById('completion-date').value;
    const remarks = document.getElementById('topic-remarks').value;

    console.log('Form values:', { topicId, statusValue, completionDate, remarks });

    // Validate form
    if (statusValue === 'completed' && !completionDate) {
        alert('Please enter completion date for completed topics');
        return;
    }

    // Update the UI directly
    const topicElement = document.querySelector(`.edit-topic-btn[data-topic-id="${topicId}"]`).closest('.flex.items-center.justify-between');

    if (topicElement) {
        // Update status badge
        const statusElement = topicElement.querySelector('.text-xs');
        const statusDot = topicElement.querySelector('.w-2.h-2');

        if (statusElement && statusDot) {
            // Update status text
            statusElement.textContent = statusValue.charAt(0).toUpperCase() + statusValue.slice(1);

            // Update status colors
            topicElement.classList.remove('bg-green-50', 'bg-yellow-50', 'bg-gray-50', 'bg-red-50');
            statusElement.classList.remove('text-green-600', 'text-yellow-600', 'text-gray-600', 'text-red-600');
            statusDot.classList.remove('bg-green-500', 'bg-yellow-500', 'bg-gray-500', 'bg-red-500');

            if (statusValue === 'completed') {
                topicElement.classList.add('bg-green-50');
                statusElement.classList.add('text-green-600');
                statusDot.classList.add('bg-green-500');
            } else if (statusValue === 'in-progress' || statusValue === 'in progress') {
                topicElement.classList.add('bg-yellow-50');
                statusElement.classList.add('text-yellow-600');
                statusDot.classList.add('bg-yellow-500');
            } else if (statusValue === 'skipped') {
                topicElement.classList.add('bg-red-50');
                statusElement.classList.add('text-red-600');
                statusDot.classList.add('bg-red-500');
            } else {
                topicElement.classList.add('bg-gray-50');
                statusElement.classList.add('text-gray-600');
                statusDot.classList.add('bg-gray-500');
            }
        }
    }

    // Close the modal
    closeTopicModal();

    // Show success message
    alert('Topic status updated successfully');
}

// Filter syllabus based on selected filters
function filterSyllabus() {
    console.log('Filtering syllabus');

    const classFilter = document.getElementById('class-filter').value;
    const subjectFilter = document.getElementById('subject-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    console.log('Filter values:', { classFilter, subjectFilter, statusFilter });

    // Get all syllabus sections
    const sections = document.querySelectorAll('.syllabus-section');
    console.log('Found', sections.length, 'sections to filter');

    sections.forEach(section => {
        const sectionClass = section.getAttribute('data-class');
        const sectionSubject = section.getAttribute('data-subject');

        let showSection = true;

        // Apply class filter
        if (classFilter !== 'all' && sectionClass !== classFilter) {
            showSection = false;
        }

        // Apply subject filter
        if (subjectFilter !== 'all' && sectionSubject !== subjectFilter) {
            showSection = false;
        }

        // Apply section visibility
        section.style.display = showSection ? 'block' : 'none';

        // If status filter is active, filter topic items
        if (statusFilter !== 'all' && showSection) {
            const topics = section.querySelectorAll('.flex.items-center.justify-between');
            let hasVisibleTopics = false;

            topics.forEach(topic => {
                const statusText = topic.querySelector('.text-xs').textContent.toLowerCase();

                if (statusText.includes(statusFilter.toLowerCase())) {
                    topic.style.display = 'flex';
                    hasVisibleTopics = true;
                } else {
                    topic.style.display = 'none';
                }
            });

            // Hide section if no visible topics
            if (!hasVisibleTopics) {
                section.style.display = 'none';
            }
        } else if (showSection) {
            // Show all topics if section is visible
            const topics = section.querySelectorAll('.flex.items-center.justify-between');
            topics.forEach(topic => {
                topic.style.display = 'flex';
            });
        }
    });
}

// Search topics
function searchTopics() {
    console.log('Searching topics');

    const searchTerm = document.getElementById('search-topics').value.toLowerCase().trim();

    if (searchTerm === '') {
        // Reset to filter view if search is cleared
        filterSyllabus();
        return;
    }

    // Get all syllabus sections
    const sections = document.querySelectorAll('.syllabus-section');

    sections.forEach(section => {
        const topics = section.querySelectorAll('.flex.items-center.justify-between');
        let hasVisibleTopics = false;

        topics.forEach(topic => {
            const topicText = topic.querySelector('.text-sm').textContent.toLowerCase();

            if (topicText.includes(searchTerm)) {
                topic.style.display = 'flex';
                hasVisibleTopics = true;
            } else {
                topic.style.display = 'none';
            }
        });

        // Show or hide section based on whether it has visible topics
        section.style.display = hasVisibleTopics ? 'block' : 'none';
    });
}

// Make functions available globally
window.editTopic = editTopic;
window.toggleSection = toggleSection;
window.closeTopicModal = closeTopicModal;
window.saveTopic = saveTopic;
window.filterSyllabus = filterSyllabus;
window.searchTopics = searchTopics;
