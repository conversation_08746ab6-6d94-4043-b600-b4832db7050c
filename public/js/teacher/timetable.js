/**
 * Timetable Management JavaScript
 * Handles all functionality for the teacher timetable page
 */

// Ensure showToast function is available
if (typeof window.showToast !== 'function') {
  console.error('showToast function is not available. Defining a fallback version.');
  window.showToast = function(type, title, message) {
    console.log(`TOAST (${type}): ${title} - ${message}`);
    alert(`${title}: ${message}`);
  };
}

// Current week date range
let currentWeekStart = new Date();
currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay() + 1); // Monday

// Schedule settings
let currentSchedule = {
  effectiveDate: '2023-04-01',
  periodCount: 8,
  lectureDuration: 40,
  breakDuration: 20,
  startTime: '08:00',
  timeSlots: []
};

// School Infrastructure
const schoolInfrastructure = {
  classrooms: Array.from({length: 20}, (_, i) => ({ id: i + 1, name: `Classroom ${i + 1}`, capacity: 50 })),
  labs: [
    { id: 'P1', name: 'Physics Lab 1', capacity: 50, subject: 'Physics' },
    { id: 'P2', name: 'Physics Lab 2', capacity: 50, subject: 'Physics' },
    { id: 'C1', name: 'Chemistry Lab 1', capacity: 50, subject: 'Chemistry' },
    { id: 'C2', name: 'Chemistry Lab 2', capacity: 50, subject: 'Chemistry' },
    { id: 'CS1', name: 'Computer Lab 1', capacity: 50, subject: 'Computer Science' },
    { id: 'CS2', name: 'Computer Lab 2', capacity: 50, subject: 'Computer Science' },
    { id: 'B1', name: 'Biology Lab', capacity: 50, subject: 'Biology' },
    { id: 'R1', name: 'Robotics Lab', capacity: 50, subject: 'Robotics' }
  ],
  sections: {
    11: {
      'NM': Array.from({length: 6}, (_, i) => ({ id: `11-NM-${i+1}`, name: `11 Non-Medical ${i+1}`, strength: 50, stream: 'Non-Medical' })),
      'M': Array.from({length: 2}, (_, i) => ({ id: `11-M-${i+1}`, name: `11 Medical ${i+1}`, strength: 50, stream: 'Medical' })),
      'C': Array.from({length: 2}, (_, i) => ({ id: `11-C-${i+1}`, name: `11 Commerce ${i+1}`, strength: 50, stream: 'Commerce' }))
    },
    12: {
      'NM': Array.from({length: 6}, (_, i) => ({ id: `12-NM-${i+1}`, name: `12 Non-Medical ${i+1}`, strength: 50, stream: 'Non-Medical' })),
      'M': Array.from({length: 2}, (_, i) => ({ id: `12-M-${i+1}`, name: `12 Medical ${i+1}`, strength: 50, stream: 'Medical' })),
      'C': Array.from({length: 2}, (_, i) => ({ id: `12-C-${i+1}`, name: `12 Commerce ${i+1}`, strength: 50, stream: 'Commerce' }))
    }
  }
};

// Stream subject mapping
const streamSubjects = {
  'Non-Medical': ['Physics', 'Chemistry', 'Mathematics', 'English', 'Punjabi', 'Computer Science'],
  'Medical': ['Physics', 'Chemistry', 'Biology', 'English', 'Punjabi', 'Computer Science'],
  'Commerce': ['Business Studies', 'Accountancy', 'Economics', 'English', 'Punjabi', 'Computer Science']
};

// Teacher list
const teachers = [
  // Physics teachers
  { id: 'P1', name: 'Dr. Robert Chen', subjects: ['Physics'], qualification: 'PhD Physics' },
  { id: 'P2', name: 'Mr. Richard Feynman', subjects: ['Physics'], qualification: 'MSc Physics' },
  { id: 'P3', name: 'Dr. Marie Curie', subjects: ['Physics'], qualification: 'PhD Physics' },
  { id: 'P4', name: 'Mr. Stephen Hawking', subjects: ['Physics'], qualification: 'MSc Physics' },
  { id: 'P5', name: 'Ms. Lisa Randall', subjects: ['Physics'], qualification: 'MSc Physics' },
  { id: 'P6', name: 'Dr. Neil deGrasse Tyson', subjects: ['Physics'], qualification: 'PhD Astrophysics' },
  { id: 'P7', name: 'Ms. Chien-Shiung Wu', subjects: ['Physics'], qualification: 'MSc Physics' },
  { id: 'P8', name: 'Mr. Jayant Narlikar', subjects: ['Physics'], qualification: 'MSc Physics' },

  // Chemistry teachers
  { id: 'C1', name: 'Mrs. Sarah Johnson', subjects: ['Chemistry'], qualification: 'MSc Chemistry' },
  { id: 'C2', name: 'Dr. Linus Pauling', subjects: ['Chemistry'], qualification: 'PhD Chemistry' },
  { id: 'C3', name: 'Ms. Dorothy Hodgkin', subjects: ['Chemistry'], qualification: 'MSc Chemistry' },
  { id: 'C4', name: 'Dr. Rosalind Franklin', subjects: ['Chemistry'], qualification: 'PhD Chemistry' },
  { id: 'C5', name: 'Mr. Antoine Lavoisier', subjects: ['Chemistry'], qualification: 'MSc Chemistry' },
  { id: 'C6', name: 'Ms. Ada Yonath', subjects: ['Chemistry'], qualification: 'MSc Chemistry' },
  { id: 'C7', name: 'Dr. G.N. Lewis', subjects: ['Chemistry'], qualification: 'PhD Chemistry' },
  { id: 'C8', name: 'Mr. Dmitri Mendeleev', subjects: ['Chemistry'], qualification: 'MSc Chemistry' },

  // Mathematics teachers
  { id: 'M1', name: 'Mr. David Patel', subjects: ['Mathematics'], qualification: 'MSc Mathematics' },
  { id: 'M2', name: 'Dr. Srinivasa Ramanujan', subjects: ['Mathematics'], qualification: 'PhD Mathematics' },
  { id: 'M3', name: 'Ms. Emmy Noether', subjects: ['Mathematics'], qualification: 'MSc Mathematics' },
  { id: 'M4', name: 'Mr. Terence Tao', subjects: ['Mathematics'], qualification: 'MSc Mathematics' },

  // Biology teachers
  { id: 'B1', name: 'Ms. Emily Rodriguez', subjects: ['Biology'], qualification: 'MSc Biology' },
  { id: 'B2', name: 'Dr. Jane Goodall', subjects: ['Biology'], qualification: 'PhD Biology' },
  { id: 'B3', name: 'Mr. Charles Darwin', subjects: ['Biology'], qualification: 'MSc Biology' },
  { id: 'B4', name: 'Dr. Barbara McClintock', subjects: ['Biology'], qualification: 'PhD Biology' },

  // Computer Science teachers
  { id: 'CS1', name: 'Mr. John Smith', subjects: ['Computer Science'], qualification: 'MSc Computer Science' },
  { id: 'CS2', name: 'Ms. Ada Lovelace', subjects: ['Computer Science'], qualification: 'MSc Computer Science' },
  { id: 'CS3', name: 'Dr. Grace Hopper', subjects: ['Computer Science'], qualification: 'PhD Computer Science' },

  // English teachers
  { id: 'E1', name: 'Ms. Margaret Atwood', subjects: ['English'], qualification: 'MA English Literature' },
  { id: 'E2', name: 'Mr. William Shakespeare', subjects: ['English'], qualification: 'MA English' },
  { id: 'E3', name: 'Dr. James Joyce', subjects: ['English'], qualification: 'PhD English Literature' },
  { id: 'E4', name: 'Ms. Jane Austen', subjects: ['English'], qualification: 'MA English' },

  // Punjabi teachers
  { id: 'PJ1', name: 'Mr. Harpreet Singh', subjects: ['Punjabi'], qualification: 'MA Punjabi' },
  { id: 'PJ2', name: 'Ms. Gurpreet Kaur', subjects: ['Punjabi'], qualification: 'MA Punjabi Literature' },
  { id: 'PJ3', name: 'Dr. Surjit Patar', subjects: ['Punjabi'], qualification: 'PhD Punjabi Literature' },
  { id: 'PJ4', name: 'Ms. Amrita Pritam', subjects: ['Punjabi'], qualification: 'MA Punjabi' },

  // Commerce subjects teachers
  { id: 'BS1', name: 'Mr. Peter Drucker', subjects: ['Business Studies'], qualification: 'MBA' },
  { id: 'BS2', name: 'Ms. Indra Nooyi', subjects: ['Business Studies'], qualification: 'MBA' },
  { id: 'AC1', name: 'Mr. Warren Buffett', subjects: ['Accountancy'], qualification: 'CA' },
  { id: 'AC2', name: 'Ms. Sheryl Sandberg', subjects: ['Accountancy'], qualification: 'CPA' },
  { id: 'EC1', name: 'Dr. Amartya Sen', subjects: ['Economics'], qualification: 'PhD Economics' },
  { id: 'EC2', name: 'Ms. Esther Duflo', subjects: ['Economics'], qualification: 'PhD Economics' }
];

// Logged in teacher information (would normally come from server)
const loggedInTeacher = teachers.find(t => t.id === 'CS1') || {
  id: 'CS1',
  name: 'Mr. John Smith',
  subjects: ['Computer Science']
};

// Get full stream name
function getFullStreamName(streamCode) {
  const streamMap = {
    'NM': 'Non-Medical',
    'M': 'Medical',
    'C': 'Commerce'
  };
  return streamMap[streamCode] || streamCode;
}

// Populate teacher filter dropdown with chosen2
function populateTeacherFilter(teachers, currentTeacherId) {
  console.log('populateTeacherFilter called with', teachers.length, 'teachers and currentTeacherId:', currentTeacherId);

  // Check if we're on a page that has the teacher filter
  const teacherFilter = document.getElementById('teacher-filter');
  if (!teacherFilter) {
    console.log('teacher-filter element not found - this is normal on pages without teacher filter');
    return;
  }

  try {
    // Save the current value if it exists
    const currentValue = teacherFilter.value;
    console.log('Current teacher filter value before populating:', currentValue);

    // Clear existing options except the empty one
    while (teacherFilter.options.length > 1) {
      teacherFilter.remove(1);
    }

    // Add options for each teacher
    teachers.forEach(teacher => {
      const option = document.createElement('option');
      option.value = teacher.id;
      option.text = teacher.full_name;

      // Set selected if this is the current teacher or matches the current value
      if ((currentTeacherId && teacher.id == currentTeacherId) ||
          (currentValue && teacher.id == currentValue)) {
        option.selected = true;
        console.log('Setting selected option for teacher ID:', teacher.id);
      }

      teacherFilter.appendChild(option);
    });

    // Initialize or refresh chosen
    try {
      console.log('Updating chosen dropdown');

      // Check if jQuery is available
      if (typeof $ === 'undefined') {
        console.error('jQuery is not defined');
        return;
      }

      // Check if chosen is available
      if (typeof $.fn.chosen === 'undefined') {
        console.error('Chosen plugin is not available');

        // Try to initialize chosen directly
        if (typeof $.fn.chosen === 'undefined') {
          console.log('Attempting to initialize chosen directly');
          $(teacherFilter).chosen({
            width: '100%',
            search_contains: true,
            allow_single_deselect: true,
            no_results_text: 'No teachers found matching'
          });
        }
        return;
      }

      // Trigger chosen update
      $(teacherFilter).trigger('chosen:updated');
      console.log('Chosen update triggered');
    } catch (e) {
      console.error('Error updating chosen:', e);
    }
  } catch (error) {
    console.error('Error in populateTeacherFilter:', error);
  }
}

// Populate class filter with all classes and sections
function populateClassFilter() {
  const classFilter = document.getElementById('class-filter');
  if (!classFilter) {
    console.error('Class filter element not found');
    return;
  }

  // Clear existing options except 'all'
  while (classFilter.options.length > 1) {
    classFilter.remove(1);
  }

  // Create option groups for each grade
  const grade11Group = document.createElement('optgroup');
  grade11Group.label = 'Class 11';

  const grade12Group = document.createElement('optgroup');
  grade12Group.label = 'Class 12';

  // Add options for grade 11
  // First add grade-only options
  const option11 = document.createElement('option');
  option11.value = '11';
  option11.text = 'All Class 11';
  grade11Group.appendChild(option11);

  // Add grade-stream options
  ['NM', 'M', 'C'].forEach(stream => {
    const streamOption = document.createElement('option');
    streamOption.value = `11-${stream}`;
    streamOption.text = `11 ${getFullStreamName(stream)}`;
    grade11Group.appendChild(streamOption);

    // Add specific sections
    const sectionCount = stream === 'NM' ? 6 : 2; // NM has 6 sections, others have 2
    for (let i = 1; i <= sectionCount; i++) {
      const sectionOption = document.createElement('option');
      sectionOption.value = `11-${stream}-${i}`;

      // Convert section number to letter (1=A, 2=B, etc.)
      const sectionLetter = String.fromCharCode(64 + i); // 1 -> A, 2 -> B, etc.
      sectionOption.text = `11 ${getFullStreamName(stream)} ${sectionLetter}`;

      grade11Group.appendChild(sectionOption);
    }
  });

  // Add options for grade 12 (same structure as grade 11)
  const option12 = document.createElement('option');
  option12.value = '12';
  option12.text = 'All Class 12';
  grade12Group.appendChild(option12);

  ['NM', 'M', 'C'].forEach(stream => {
    const streamOption = document.createElement('option');
    streamOption.value = `12-${stream}`;
    streamOption.text = `12 ${getFullStreamName(stream)}`;
    grade12Group.appendChild(streamOption);

    const sectionCount = stream === 'NM' ? 6 : 2;
    for (let i = 1; i <= sectionCount; i++) {
      const sectionOption = document.createElement('option');
      sectionOption.value = `12-${stream}-${i}`;

      // Convert section number to letter (1=A, 2=B, etc.)
      const sectionLetter = String.fromCharCode(64 + i); // 1 -> A, 2 -> B, etc.
      sectionOption.text = `12 ${getFullStreamName(stream)} ${sectionLetter}`;

      grade12Group.appendChild(sectionOption);
    }
  });

  // Add option groups to select
  classFilter.appendChild(grade11Group);
  classFilter.appendChild(grade12Group);
}

// Update the filter display element
function updateFilterDisplay(text) {
  try {
    const filterDisplay = document.getElementById('filter-display');
    if (filterDisplay) {
      filterDisplay.textContent = text;
    } else {
      console.log('filter-display element not found - this is normal on pages without filter display');
    }
  } catch (error) {
    console.error('Error updating filter display:', error);
  }
}

// Update week display
function updateWeekDisplay(date) {
  console.log('updateWeekDisplay called with date:', date);

  // Set to Monday of the week
  const weekStart = new Date(date);
  weekStart.setDate(weekStart.getDate() - weekStart.getDay() + 1);
  currentWeekStart = weekStart;

  // Calculate end of week (Sunday)
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  // Format dates
  const startFormatted = formatDate(weekStart);
  const endFormatted = formatDate(weekEnd);

  // Update display
  const weekStartElement = document.getElementById('week-start-date');
  const weekEndElement = document.getElementById('week-end-date');
  const currentScheduleDateElement = document.getElementById('current-schedule-date');

  if (weekStartElement) {
    weekStartElement.textContent = startFormatted;
  } else {
    console.error('week-start-date element not found');
  }

  if (weekEndElement) {
    weekEndElement.textContent = endFormatted;
  } else {
    console.error('week-end-date element not found');
  }

  // Display current schedule date
  if (currentScheduleDateElement) {
    currentScheduleDateElement.textContent = formatDate(new Date(currentSchedule.effectiveDate));
  } else {
    console.error('current-schedule-date element not found');
  }

  // Fetch timetable data for this week
  console.log('Calling fetchTimetableData from updateWeekDisplay');
  fetchTimetableData(weekStart, weekEnd);
}

// Navigate weeks
function navigateWeek(direction) {
  const newDate = new Date(currentWeekStart);
  newDate.setDate(newDate.getDate() + (7 * direction));
  updateWeekDisplay(newDate);
}

// Format date as Month Day, Year
function formatDate(date) {
  const options = { month: 'long', day: 'numeric', year: 'numeric' };
  return date.toLocaleDateString('en-US', options);
}

// Store the current timetable data globally
let currentTimetableData = null;

// Fetch timetable data for the specified week
async function fetchTimetableData(weekStart, weekEnd) {
  console.log('fetchTimetableData called with:', { weekStart, weekEnd });
  try {
    // Format dates for API request
    const startFormatted = weekStart.toISOString().split('T')[0];
    const endFormatted = weekEnd.toISOString().split('T')[0];

    console.log('Formatted dates:', { startFormatted, endFormatted });

    // Show loading state
    const timetableBody = document.querySelector('#timetable-content');
    if (timetableBody) {
      console.log('Setting loading state in timetable body');
      timetableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">Loading timetable data...</td></tr>';
    } else {
      console.error('Could not find timetable-content element');
    }

    // Get selected teacher ID if we're in admin mode and have a teacher filter
    let selectedTeacherId = null;
    const teacherFilter = document.getElementById('teacher-filter');
    if (teacherFilter && teacherFilter.value) {
      selectedTeacherId = teacherFilter.value;
    }

    // Get selected class ID if we have a class filter
    let selectedClassId = null;
    const classFilter = document.getElementById('class-filter');
    if (classFilter && classFilter.value && classFilter.value !== 'all') {
      selectedClassId = classFilter.value;
    }

    // Build API URL with query parameters
    let apiUrl = `/api/teacher/timetable?start_date=${startFormatted}&end_date=${endFormatted}`;
    if (selectedTeacherId) {
      apiUrl += `&teacher_id=${selectedTeacherId}`;
    }
    if (selectedClassId) {
      apiUrl += `&class_id=${selectedClassId}`;
    }

    console.log('Fetching timetable data from API:', apiUrl);

    // Fetch data from API
    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    console.log('API response:', data);

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch timetable data');
    }

    // If we're in admin mode and this is the first load, populate the teacher filter
    if (data.teachers && data.teachers.length > 0) {
      populateTeacherFilter(data.teachers, data.currentTeacherId);
    }

    // If we have class data, populate the class filter
    if (data.classes && data.classes.length > 0 && classFilter && classFilter.options && !classFilter.options.length > 1) {
      populateClassFilterFromData(data.classes);
    }

    // Store full data globally for filtering
    currentTimetableData = { lectures: data.lectures || [] };

    // Render the timetable with the filtered data from the API
    renderTimetable(currentTimetableData);

    // Update filter display
    updateFilterDisplay(getFilterDisplayText(selectedClassId));

  } catch (error) {
    console.error('Error fetching timetable data:', error);

    // Show error message
    const timetableBody = document.querySelector('#timetable-content');
    if (timetableBody) {
      timetableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-red-600">Failed to load timetable. Please try again.</td></tr>';
    }

    showToast('error', 'Error', 'Failed to load timetable data. Please try again.');
  }
}

// Helper function to get filter display text
function getFilterDisplayText(filterValue) {
  if (!filterValue || filterValue === 'all') {
    return 'All Classes';
  }

  // Parse the filter value which will be in format "grade-stream-section" e.g. "11-NM-1"
  const parts = filterValue.split('-');

  if (parts.length === 1) {
    return `Class ${parts[0]}`;
  } else if (parts.length === 2) {
    return `Class ${parts[0]} ${getFullStreamName(parts[1])}`;
  } else if (parts.length === 3) {
    // Convert section number to letter (1=A, 2=B, etc.)
    const sectionLetter = String.fromCharCode(64 + parseInt(parts[2])); // 1 -> A, 2 -> B, etc.
    return `Class ${parts[0]} ${getFullStreamName(parts[1])} ${sectionLetter}`;
  }

  return 'All Classes';
}

// Populate class filter from API data
function populateClassFilterFromData(classes) {
  const classFilter = document.getElementById('class-filter');
  if (!classFilter) {
    console.error('Class filter element not found');
    return;
  }

  // Clear existing options except 'all'
  while (classFilter.options.length > 1) {
    classFilter.remove(1);
  }

  // Group classes by grade and stream
  const gradeMap = {};

  classes.forEach(cls => {
    const grade = cls.grade;
    const stream = cls.streamCode;

    if (!gradeMap[grade]) {
      gradeMap[grade] = {};
    }

    if (!gradeMap[grade][stream]) {
      gradeMap[grade][stream] = [];
    }

    gradeMap[grade][stream].push(cls);
  });

  // Create option groups for each grade
  Object.keys(gradeMap).sort().forEach(grade => {
    const gradeGroup = document.createElement('optgroup');
    gradeGroup.label = `Class ${grade}`;

    // Add grade-only option
    const gradeOption = document.createElement('option');
    gradeOption.value = grade;
    gradeOption.text = `All Class ${grade}`;
    gradeGroup.appendChild(gradeOption);

    // Add stream options for this grade
    Object.keys(gradeMap[grade]).sort().forEach(stream => {
      const streamOption = document.createElement('option');
      streamOption.value = `${grade}-${stream}`;
      streamOption.text = `${grade} ${getFullStreamName(stream)}`;
      gradeGroup.appendChild(streamOption);

      // Add section options for this stream
      const sections = gradeMap[grade][stream];
      const sectionMap = {};

      sections.forEach(section => {
        if (section.section) {
          const sectionParts = section.section.split('-');
          if (sectionParts.length === 2) {
            sectionMap[sectionParts[1]] = true;
          }
        }
      });

      Object.keys(sectionMap).sort((a, b) => parseInt(a) - parseInt(b)).forEach(sectionNum => {
        const sectionOption = document.createElement('option');
        sectionOption.value = `${grade}-${stream}-${sectionNum}`;

        // Convert section number to letter (1=A, 2=B, etc.)
        const sectionLetter = String.fromCharCode(64 + parseInt(sectionNum)); // 1 -> A, 2 -> B, etc.
        sectionOption.text = `${grade} ${getFullStreamName(stream)} ${sectionLetter}`;

        gradeGroup.appendChild(sectionOption);
      });
    });

    classFilter.appendChild(gradeGroup);
  });
}

// Filter timetable by class
function filterByClass(filterValue) {
  console.log('Filtering timetable by value:', filterValue);

  // Get the current week dates
  const weekStart = currentWeekStart;
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  // Fetch data with the filter applied
  // This will call the API with the class_id parameter
  fetchTimetableData(weekStart, weekEnd);
}

// Generate dummy timetable data
function generateDummyTimetableData(weekStart, weekEnd) {
  console.log('generateDummyTimetableData called with:', { weekStart, weekEnd });
  const lectures = [];

  // Create structured data for each class/section
  // For each grade (11, 12)
  ['11', '12'].forEach(grade => {
    // For each stream (NM, M, C)
    ['NM', 'M', 'C'].forEach(streamCode => {
      // Get sections for this stream
      const sections = schoolInfrastructure.sections[grade][streamCode];
      const sectionCount = sections.length;

      // For each section
      sections.forEach((section, sectionIndex) => {
        // Get stream full name and subjects
        const streamName = getFullStreamName(streamCode);
        const subjects = streamSubjects[streamName];

        // Convert section index to letter (0=A, 1=B, etc.)
        const sectionLetter = String.fromCharCode(65 + sectionIndex); // A, B, C, etc.

        // For each day (Monday-Saturday)
        for (let day = 0; day < 6; day++) {
          const currentDate = new Date(weekStart);
          currentDate.setDate(currentDate.getDate() + day);
          const dateString = currentDate.toISOString().split('T')[0];

          // For each time slot
          for (let timeSlot = 0; timeSlot < currentSchedule.periodCount; timeSlot++) {
            // Create unique lecture ID
            const lectureId = parseInt(`${grade}${streamCode.charCodeAt(0)}${sectionIndex+1}${day}${timeSlot}`, 10);

            // Select subject based on day and time - create a predictable pattern
            const subjectIndex = (timeSlot + day) % subjects.length;
            const subjectName = subjects[subjectIndex];

            // Find eligible teachers
            const eligibleTeachers = teachers.filter(t => t.subjects.includes(subjectName));
            const teacherIndex = (day + timeSlot + sectionIndex) % eligibleTeachers.length;
            const teacher = eligibleTeachers[teacherIndex];

            // Choose location
            let location;
            const labSubjects = ['Physics', 'Chemistry', 'Biology', 'Computer Science'];

            if (labSubjects.includes(subjectName)) {
              // Try to find a lab
              const labs = schoolInfrastructure.labs.filter(lab => lab.subject === subjectName);
              if (labs.length > 0) {
                const labIndex = (sectionIndex + day) % labs.length;
                location = labs[labIndex].name;
              } else {
                // Use classroom as fallback
                const classroomIndex = (sectionIndex + timeSlot + day) % schoolInfrastructure.classrooms.length;
                location = schoolInfrastructure.classrooms[classroomIndex].name;
              }
            } else {
              // Regular classroom
              const classroomIndex = (sectionIndex + timeSlot + day) % schoolInfrastructure.classrooms.length;
              location = schoolInfrastructure.classrooms[classroomIndex].name;
            }

            // Create time slots
            const timeSlots = generateTimeSlots();

            // Get a dummy topic for this subject
            const topics = getDummyTopics(subjectName);
            const topicIndex = (timeSlot + day + sectionIndex) % topics.length;
            const topic = topics[topicIndex];

            // Generate status
            let status = 'Pending';
            const randomStatus = (lectureId % 10);
            if (randomStatus < 2) status = 'Delivered';
            else if (randomStatus < 3) status = 'Rescheduled';

            // Create the lecture object
            lectures.push({
              id: lectureId,
              class_name: `Class ${grade}`,
              section: `${streamCode}-${sectionIndex+1}`,
              section_display: `${streamCode} ${sectionLetter}`,
              subject_name: subjectName,
              teacher_name: teacher.name,
              teacher_id: teacher.id,
              topic: topic,
              date: dateString,
              start_time: timeSlots[timeSlot].start,
              end_time: timeSlots[timeSlot].end,
              location: location,
              status: status,
              description: `This lecture will cover ${topic} for ${subjectName}.`,
              slot_index: timeSlot,
              stream: streamName,
              streamCode: streamCode,
              grade: grade,
              sectionLetter: sectionLetter
            });
          }
        }
      });
    });
  });

  console.log(`Generated ${lectures.length} dummy lectures`);
  return { lectures };
}

// Get dummy topics for each subject
function getDummyTopics(subjectName) {
  const subjectTopics = {
    'Physics': [
      'Kinematics', 'Newton\'s Laws', 'Work and Energy', 'Rotational Motion',
      'Gravitation', 'Properties of Matter', 'Thermodynamics', 'Waves'
    ],
    'Chemistry': [
      'Atomic Structure', 'Chemical Bonding', 'States of Matter', 'Thermochemistry',
      'Solutions', 'Equilibrium', 'Redox Reactions', 'Organic Chemistry Basics'
    ],
    'Mathematics': [
      'Functions', 'Limits', 'Derivatives', 'Integrals', 'Vectors',
      'Matrices', 'Probability', 'Statistics', 'Complex Numbers'
    ],
    'Biology': [
      'Cell Structure', 'Photosynthesis', 'Human Physiology', 'Genetics',
      'Evolution', 'Ecology', 'Molecular Biology', 'Biotechnology'
    ],
    'Computer Science': [
      'Data Types', 'Control Structures', 'Functions', 'Arrays',
      'Object-Oriented Programming', 'Data Structures', 'Algorithms', 'Web Development'
    ],
    'English': [
      'Reading Comprehension', 'Writing Skills', 'Grammar', 'Literature Analysis',
      'Poetry', 'Drama', 'Prose', 'Communication Skills'
    ],
    'Punjabi': [
      'Grammar', 'Literature', 'Poetry', 'Prose',
      'Writing Skills', 'Reading Comprehension', 'Cultural Studies', 'Communication'
    ],
    'Business Studies': [
      'Business Environment', 'Planning', 'Organizing', 'Staffing',
      'Directing', 'Controlling', 'Financial Management', 'Marketing'
    ],
    'Accountancy': [
      'Basic Accounting', 'Journal Entries', 'Ledger Accounts', 'Trial Balance',
      'Financial Statements', 'Depreciation', 'Company Accounts', 'Analysis of Financial Statements'
    ],
    'Economics': [
      'Microeconomics', 'Macroeconomics', 'National Income', 'Money and Banking',
      'Government Budget', 'Balance of Payments', 'International Trade', 'Development Economics'
    ],
  };

  return subjectTopics[subjectName] || ['General Topic'];
}

// View lecture details
async function viewLectureDetails(lectureId) {
  console.log('viewLectureDetails called with ID:', lectureId);
  try {
    // Show loading state
    const content = document.getElementById('lecture-details-content');
    if (!content) {
      console.error('lecture-details-content element not found');
      showToast('error', 'Error', 'Could not find lecture details container');
      return;
    }

    // Show modal first to provide immediate feedback
    const modal = document.getElementById('lecture-details-modal');
    if (!modal) {
      console.error('Modal element not found');
      showToast('error', 'Error', 'Could not find modal element');
      return;
    }
    modal.classList.remove('hidden');

    content.innerHTML = '<div class="text-center"><p>Loading lecture details...</p></div>';

    // Fetch lecture details from API to get the most up-to-date information
    console.log(`Fetching lecture details from API: /api/teacher/lectures/${lectureId}`);

    try {
      const response = await fetch(`/api/teacher/lectures/${lectureId}`);
      console.log('API response status:', response.status);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      console.log('API response data:', data);

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch lecture details');
      }

      const lecture = data.lecture;
      const instructionPlans = data.instructionPlans || [];
      const syllabusTopics = data.syllabusTopics || [];

      console.log('Lecture details loaded:', lecture);
      console.log('Instruction plans:', instructionPlans);
      console.log('Syllabus topics:', syllabusTopics);

      // Format date and time
      const lectureDate = new Date(lecture.date);
      const dateFormatted = lectureDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Format time
      const timeFormatted = `${lecture.start_time} - ${lecture.end_time}`;

      // Use section_display if available, otherwise fallback to section
      const sectionDisplay = lecture.section_display || lecture.section;

      // Prepare instruction plans HTML
      let instructionPlansHtml = '';
      if (instructionPlans.length > 0) {
        instructionPlansHtml = `
          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Linked Instruction Plans:</h3>
            <ul class="list-disc pl-5 text-sm text-gray-600">
              ${instructionPlans.map(plan => `
                <li>${plan.title} (${plan.subject_name})</li>
              `).join('')}
            </ul>
          </div>
        `;
      }

      // Prepare syllabus topics HTML
      let syllabusTopicsHtml = '';
      if (syllabusTopics.length > 0) {
        syllabusTopicsHtml = `
          <div class="mt-4">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Completed Syllabus Topics:</h3>
            <ul class="list-disc pl-5 text-sm text-gray-600">
              ${syllabusTopics.map(topic => `
                <li>${topic.topic} (${topic.subject_name})</li>
              `).join('')}
            </ul>
          </div>
        `;
      }

      // Populate modal content
      content.innerHTML = `
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">Class:</span>
              <span class="font-medium text-gray-900">${lecture.class_name} ${sectionDisplay}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Subject:</span>
              <span class="font-medium text-gray-900">${lecture.subject_name}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Teacher:</span>
              <span class="font-medium text-gray-900">${lecture.teacher_name}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Topic:</span>
              <span class="font-medium text-gray-900">${lecture.topic || 'N/A'}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Date:</span>
              <span class="font-medium text-gray-900">${dateFormatted}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Time:</span>
              <span class="font-medium text-gray-900">${timeFormatted}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Location:</span>
              <span class="font-medium text-gray-900">${lecture.location || 'N/A'}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Status:</span>
            <span class="font-medium ${
                lecture.status === 'delivered' ? 'text-green-600' :
                (lecture.status === 'rescheduled' ? 'text-yellow-600' : 'text-gray-600')
              }">${lecture.status}</span>
            </div>
            <div class="mt-3">
              <span class="text-gray-600 block mb-1">Description:</span>
              <p class="text-sm text-gray-800 bg-gray-50 p-3 rounded">${lecture.description || 'No description available'}</p>
            </div>
            ${instructionPlansHtml}
            ${syllabusTopicsHtml}
        </div>
      `;

      // Set lecture ID for action buttons
      const markDeliveredBtn = document.getElementById('mark-delivered-btn');
      const rescheduleBtn = document.getElementById('reschedule-btn');

      if (!markDeliveredBtn || !rescheduleBtn) {
        console.error('Action buttons not found:', {markDeliveredBtn, rescheduleBtn});
      } else {
        markDeliveredBtn.setAttribute('data-lecture-id', lectureId);
        rescheduleBtn.setAttribute('data-lecture-id', lectureId);

        // Show/hide buttons based on status
        if (lecture.status === 'Delivered') {
          markDeliveredBtn.style.display = 'none';
        } else {
          markDeliveredBtn.style.display = 'block';
        }

        if (lecture.status === 'Rescheduled') {
          rescheduleBtn.style.display = 'none';
        } else {
          rescheduleBtn.style.display = 'block';
        }
      }
    } catch (error) {
      console.error('Error fetching lecture details:', error);
      content.innerHTML = `
        <div class="text-center text-red-600">
          <p>Failed to load lecture details from API. Please try again.</p>
          <p class="text-sm mt-2">${error.message}</p>
        </div>
      `;
    }

  } catch (error) {
    console.error('Error in viewLectureDetails function:', error);

    // Show error message in modal
    const content = document.getElementById('lecture-details-content');
    if (content) {
      content.innerHTML = `
        <div class="text-center text-red-600">
          <p>Failed to load lecture details. Please try again.</p>
          <p class="text-sm mt-2">${error.message}</p>
        </div>
      `;
    }

    // Show toast with error
    showToast('error', 'Error', 'Failed to load lecture details: ' + error.message);

    // Show modal if it exists, even with error
    const modal = document.getElementById('lecture-details-modal');
    if (modal) {
      modal.classList.remove('hidden');
    }
  }
}

// Close lecture details modal
function closeLectureModal() {
  document.getElementById('lecture-details-modal').classList.add('hidden');
}

// Mark lecture as delivered
async function markAsDelivered(lectureId) {
  // Show a confirmation dialog
  if (confirm('Are you sure you want to mark this lecture as delivered?')) {
    // Show a loading toast
    showToast('info', 'Processing', 'Marking lecture as delivered...');

    try {
      // Get the lecture details to check if there are instruction plans or syllabus topics
      const response = await fetch(`/api/teacher/lectures/${lectureId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch lecture details');
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch lecture details');
      }

      const lecture = data.lecture;
      const availablePlans = data.availablePlans || [];
      const availableTopics = data.availableTopics || [];

      // Prepare the request data
      let requestData = {
        status: 'delivered'
      };

      console.log('Sending request to mark lecture as delivered:', requestData);

      // If there are available instruction plans, ask the user if they want to link one
      if (availablePlans.length > 0) {
        const linkPlan = confirm('Do you want to link this lecture to an instruction plan?');
        if (linkPlan) {
          // In a real implementation, we would show a dropdown to select a plan
          // For now, we'll just use the first available plan
          requestData.instruction_plan_id = availablePlans[0].id;
        }
      }

      // If there are available syllabus topics, ask the user if they want to mark one as completed
      if (availableTopics.length > 0) {
        const completeTopic = confirm('Do you want to mark a syllabus topic as completed with this lecture?');
        if (completeTopic) {
          // In a real implementation, we would show a dropdown to select a topic
          // For now, we'll just use the first available topic
          requestData.topic_id = availableTopics[0].id;
        }
      }

      // Send the API request to update the lecture status
      console.log(`Sending request to: /api/teacher/lectures/${lectureId}/status`);
      const updateResponse = await fetch(`/api/teacher/lectures/${lectureId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('Response status:', updateResponse.status);

      if (!updateResponse.ok) {
        const errorText = await updateResponse.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to mark lecture as delivered: ${updateResponse.status} ${errorText}`);
      }

      const result = await updateResponse.json();
      console.log('Response data:', result);

      if (!result.success) {
        throw new Error(result.message || 'Failed to mark lecture as delivered');
      }

      // Show success message
      showToast('success', 'Success', 'Lecture marked as delivered');

      // Close modal
      closeLectureModal();

      // Refresh the timetable data
      updateWeekDisplay(currentWeekStart);
    } catch (error) {
      console.error('Error marking lecture as delivered:', error);
      showToast('error', 'Error', 'Failed to mark lecture as delivered: ' + error.message);
    }
  }
}

// Reschedule lecture
async function reschedule(lectureId) {
  // Show confirmation toast
  showToast('info', 'Reschedule', 'Opening reschedule form...');

  try {
    // Close current modal first
    closeLectureModal();

    // In a real implementation, we would fetch lecture details for the reschedule form
    // For now, we'll just show a toast message
    setTimeout(() => {
      showToast('info', 'Reschedule', 'Reschedule functionality will be implemented soon');
    }, 1000);

  } catch (error) {
    console.error('Error preparing reschedule:', error);
    showToast('error', 'Error', 'Failed to prepare reschedule form. Please try again.');
  }
}

// Generate time slots based on current schedule settings
function generateTimeSlots() {
  const slots = [];
  let currentTime = new Date(`2000-01-01T${currentSchedule.startTime}`);

  for (let i = 0; i < currentSchedule.periodCount; i++) {
    // Format start time
    const startHours = currentTime.getHours().toString().padStart(2, '0');
    const startMinutes = currentTime.getMinutes().toString().padStart(2, '0');
    const startTime = `${startHours}:${startMinutes}`;

    // Add lecture duration
    currentTime.setMinutes(currentTime.getMinutes() + parseInt(currentSchedule.lectureDuration));

    // Format end time
    const endHours = currentTime.getHours().toString().padStart(2, '0');
    const endMinutes = currentTime.getMinutes().toString().padStart(2, '0');
    const endTime = `${endHours}:${endMinutes}`;

    // Add slot
    slots.push({
      start: startTime,
      end: endTime,
      display: `${startTime} - ${endTime}`
    });

    // Add break duration for next period (except after the last period)
    if (i < currentSchedule.periodCount - 1) {
      currentTime.setMinutes(currentTime.getMinutes() + parseInt(currentSchedule.breakDuration));
    }
  }

  return slots;
}

// Check if a lecture is currently ongoing
function isCurrentLecture(lecture) {
  const now = new Date();
  const today = now.toISOString().split('T')[0];

  // Check if lecture is today
  if (lecture.date !== today) {
    return false;
  }

  // Parse lecture times
  const lectureStart = new Date(`${today}T${lecture.start_time}`);
  const lectureEnd = new Date(`${today}T${lecture.end_time}`);

  // Check if current time is between lecture start and end
  return now >= lectureStart && now <= lectureEnd;
}

// Check if a lecture belongs to logged in teacher
function isTeacherLecture(lecture) {
  return lecture.teacher_name === loggedInTeacher.name ||
         loggedInTeacher.subjects.includes(lecture.subject_name);
}

// Add function to update the table header with dates
function updateTableHeaderDates(weekStart) {
  // Get all day header cells (skip the first one which is the time column)
  const headerCells = document.querySelectorAll('#timetable-content-header th:not(:first-child)');

  if (headerCells.length !== 6) {
    console.error('Could not find the expected 6 day header cells');
    return;
  }

  // Days of the week
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  // Update each header with day and date
  for (let i = 0; i < 6; i++) {
    const date = new Date(weekStart);
    date.setDate(date.getDate() + i);
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

    headerCells[i].innerHTML = `
      <div class="text-xs font-medium text-gray-500 uppercase">${days[i]}</div>
      <div class="text-xs text-gray-400">${formattedDate}</div>
    `;
  }
}

// Handle time slot generation preview
function handleGenerateTimeSlots() {
  try {
    // Check if we're on a page that has the time slot settings
    const periodCountElement = document.getElementById('period-count');
    const lectureDurationElement = document.getElementById('lecture-duration');
    const breakDurationElement = document.getElementById('break-duration');
    const startTimeElement = document.getElementById('start-time');

    // If any of these elements don't exist, we're not on the settings page
    if (!periodCountElement || !lectureDurationElement || !breakDurationElement || !startTimeElement) {
      console.log('Time slot settings elements not found - this is normal on pages without time slot settings');
      return;
    }

    // Get values from form
    const periodCount = parseInt(periodCountElement.value) || 8;
    const lectureDuration = parseInt(lectureDurationElement.value) || 40;
    const breakDuration = parseInt(breakDurationElement.value) || 20;
    const startTime = startTimeElement.value || '08:00';

    // Update current schedule
    currentSchedule.periodCount = periodCount;
    currentSchedule.lectureDuration = lectureDuration;
    currentSchedule.breakDuration = breakDuration;
    currentSchedule.startTime = startTime;

    // Generate time slots
    const timeSlots = generateTimeSlots();

    // Display time slots in preview
    const previewElement = document.getElementById('time-slots-preview');
    if (!previewElement) {
      console.log('time-slots-preview element not found');
      return;
    }

    let html = '<table class="min-w-full">';
    html += '<thead><tr><th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Period</th>';
    html += '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time</th></tr></thead>';
    html += '<tbody>';

    timeSlots.forEach((slot, index) => {
      html += `<tr>
        <td class="px-4 py-2 whitespace-nowrap text-sm">${index + 1}</td>
        <td class="px-4 py-2 whitespace-nowrap text-sm">${slot.display}</td>
      </tr>`;
    });

    html += '</tbody></table>';

    previewElement.innerHTML = html;
  } catch (error) {
    console.error('Error in handleGenerateTimeSlots:', error);
  }
}

// Save time slots settings
function saveTimeSlots() {
  // Get effective date
  const effectiveDate = document.getElementById('effective-date').value;

  // Validate
  if (!effectiveDate) {
    showToast('error', 'Error', 'Please select an effective date');
    return;
  }

  // Update current schedule
  currentSchedule.effectiveDate = effectiveDate;

  // In a real implementation, we would send this to the server
  // For demo purposes, just show a success message
  showToast('success', 'Success', `New schedule saved. Effective from ${formatDate(new Date(effectiveDate))}`);

  // Update week display to reflect new schedule
  updateWeekDisplay(currentWeekStart);

  // Toggle settings panel
  document.getElementById('settings-panel').classList.add('hidden');
  document.getElementById('toggle-settings').textContent = 'Show Settings';
}

// Reset time slots to default
function resetTimeSlots() {
  // Reset form values
  document.getElementById('period-count').value = '8';
  document.getElementById('lecture-duration').value = '40';
  document.getElementById('break-duration').value = '20';
  document.getElementById('start-time').value = '08:00';

  // Generate preview
  handleGenerateTimeSlots();

  showToast('info', 'Reset', 'Time slot settings have been reset to default values');
}

// Toggle settings panel
function toggleSettingsPanel() {
  const panel = document.getElementById('settings-panel');
  const button = document.getElementById('toggle-settings');

  if (panel.classList.contains('hidden')) {
    panel.classList.remove('hidden');
    button.textContent = 'Hide Settings';
  } else {
    panel.classList.add('hidden');
    button.textContent = 'Show Settings';
  }
}

// Initialize event listeners when DOM is loaded
$(document).ready(function() {
  console.log('Document ready event fired');

  // Populate class filter with all available classes
  console.log('Populating class filter dropdown');
  populateClassFilter();

  // Initialize date display
  console.log('Calling updateWeekDisplay with current date');
  updateWeekDisplay(new Date());

  // Set effective date default to tomorrow
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Check if the effective-date element exists before setting its value
  const effectiveDateElement = document.getElementById('effective-date');
  if (effectiveDateElement) {
    effectiveDateElement.valueAsDate = tomorrow;
  } else {
    console.log('effective-date element not found, skipping date initialization');
  }

  // Event listeners for week navigation
  console.log('Setting up event listeners');
  $('#prev-week').on('click', function() {
    console.log('Prev week button clicked');
    navigateWeek(-1);
  });

  $('#next-week').on('click', function() {
    console.log('Next week button clicked');
    navigateWeek(1);
  });

  $('#current-week').on('click', function() {
    console.log('Current week button clicked');
    updateWeekDisplay(new Date());
  });

  // Class filter
  $('#class-filter').on('change', function() {
    console.log('Class filter changed to:', this.value);
    filterByClass(this.value);
  });

  // Teacher filter event is now handled in teacher-timetable.ejs

  // Modal close buttons
  $('#close-lecture-modal, #close-modal-btn').on('click', closeLectureModal);

  // Action buttons in modal
  $('#mark-delivered-btn').on('click', function() {
    const lectureId = $(this).attr('data-lecture-id');
    console.log('Mark delivered button clicked for lecture:', lectureId);
    markAsDelivered(lectureId);
  });

  $('#reschedule-btn').on('click', function() {
    const lectureId = $(this).attr('data-lecture-id');
    console.log('Reschedule button clicked for lecture:', lectureId);
    reschedule(lectureId);
  });

  // Add click handler for lecture details buttons
  $(document).on('click', '.lecture-details-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();

    // Get the lecture ID from the data attribute
    const lectureId = $(this).data('lecture-id');
    console.log('Lecture details button clicked for lecture:', lectureId);
    if (lectureId) {
      viewLectureDetails(lectureId);
    }

    return false;
  });

  // Time slots settings - only set up if the elements exist
  if (document.getElementById('toggle-settings')) {
    $('#toggle-settings').on('click', toggleSettingsPanel);
  }

  if (document.getElementById('generate-slots')) {
    $('#generate-slots').on('click', handleGenerateTimeSlots);
  }

  if (document.getElementById('save-time-slots')) {
    $('#save-time-slots').on('click', saveTimeSlots);
  }

  if (document.getElementById('reset-time-slots')) {
    $('#reset-time-slots').on('click', resetTimeSlots);
  }

  // Generate initial time slots preview - only if we're on a page with time slot settings
  if (document.getElementById('period-count') &&
      document.getElementById('lecture-duration') &&
      document.getElementById('break-duration') &&
      document.getElementById('start-time')) {
    handleGenerateTimeSlots();
  }

  console.log('Timetable initialization completed');
});

// Log to console to verify the script is loaded
console.log('Timetable.js loaded successfully');

// Add safety check for jQuery
(function() {
  if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded. Timetable functionality will not work.');
    // Fallback initialization if jQuery is not available
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOMContentLoaded event fired without jQuery');
      // Call our updateWeekDisplay function directly to at least show some data
      try {
        updateWeekDisplay(new Date());
      } catch (error) {
        console.error('Error initializing timetable without jQuery:', error);
      }
    });
  } else {
    console.log('jQuery is available, version:', jQuery.fn.jquery);
  }
})();

// Make functions available globally for onclick handlers
window.viewLectureDetails = viewLectureDetails;
window.showToast = showToast;
window.filterByClass = filterByClass;
window.populateClassFilter = populateClassFilter;

// Render timetable with the provided data
function renderTimetable(data) {
  console.log('renderTimetable called with data:', data);
  const timetableBody = document.querySelector('#timetable-content');
  if (!timetableBody) {
    console.error('timetable-content element not found');
    return;
  }

  // Clear current content
  timetableBody.innerHTML = '';

  // Update header dates
  updateTableHeaderDates(currentWeekStart);

  // Check if we have any data
  if (!data || !data.lectures || data.lectures.length === 0) {
    console.warn('No lectures data available to render');
    timetableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">No lectures scheduled for this week</td></tr>';
    return;
  }

  console.log(`Rendering ${data.lectures.length} lectures`);

  // Get time slots from current schedule
  const timeSlots = generateTimeSlots();
  console.log('Generated time slots:', timeSlots);

  // Generate rows for each time slot
  timeSlots.forEach((timeSlot, index) => {
    console.log(`Creating row for time slot ${index}: ${timeSlot.display}`);
    const row = document.createElement('tr');

    // Time slot column
    const timeCell = document.createElement('td');
    timeCell.className = 'border border-gray-200 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50';
    timeCell.innerHTML = `<div>Lecture ${index + 1}</div><div>${timeSlot.display}</div>`;
    row.appendChild(timeCell);

    // Create cells for each day (Monday to Saturday)
    for (let day = 0; day < 6; day++) {
      const dayCell = document.createElement('td');
      dayCell.className = 'border border-gray-200 p-0';

      // Find lectures for this day and time slot
      const lecturesForSlot = data.lectures.filter(lecture => {
        const lectureDate = new Date(lecture.date);
        const lectureDay = lectureDate.getDay() - 1; // 0-6 (Sun-Sat) to 0-5 (Mon-Sat)

        // Check if the lecture has a slot_index, if not, try to determine it from the time
        if (lecture.slot_index !== undefined) {
          return (lectureDay === day) && lecture.slot_index === index;
        } else {
          // Try to match by time if slot_index is not available
          const lectureStartTime = lecture.start_time;
          const slotStartTime = timeSlot.start;
          return (lectureDay === day) && lectureStartTime === slotStartTime;
        }
      });

      console.log(`Day ${day}, slot ${index}: found ${lecturesForSlot.length} lectures`);

      if (lecturesForSlot.length > 0) {
        // We have lectures for this slot
        // Only display one lecture per slot (the first one)
        const lecture = lecturesForSlot[0];

        // If there are multiple lectures in this slot, add a badge to indicate this
        const hasMultipleLectures = lecturesForSlot.length > 1;

        // Determine background color based on subject
        let bgColorClass = '';
        switch(lecture.subject_name) {
          case 'Physics':
            bgColorClass = 'bg-blue-50';
            break;
          case 'Chemistry':
            bgColorClass = 'bg-green-50';
            break;
          case 'Mathematics':
            bgColorClass = 'bg-yellow-50';
            break;
          case 'Biology':
            bgColorClass = 'bg-purple-50';
            break;
          case 'Computer Science':
            bgColorClass = 'bg-red-50';
            break;
          case 'English':
            bgColorClass = 'bg-indigo-50';
            break;
          case 'Punjabi':
            bgColorClass = 'bg-orange-50';
            break;
          case 'Business Studies':
            bgColorClass = 'bg-teal-50';
            break;
          case 'Accountancy':
            bgColorClass = 'bg-cyan-50';
            break;
          case 'Economics':
            bgColorClass = 'bg-emerald-50';
            break;
          default:
            bgColorClass = 'bg-gray-50';
        }

        // Add stream-specific styling
        let streamBorderClass = '';
        switch(lecture.stream) {
          case 'Non-Medical':
            streamBorderClass = 'border-l-4 border-blue-400';
            break;
          case 'Medical':
            streamBorderClass = 'border-l-4 border-purple-400';
            break;
          case 'Commerce':
            streamBorderClass = 'border-l-4 border-teal-400';
            break;
          default:
            streamBorderClass = '';
        }

        // Determine status badge
        let statusBadge = '';
        switch(lecture.status) {
          case 'delivered':
            statusBadge = '<span class="px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full text-xs">Delivered</span>';
            break;
          case 'pending':
            statusBadge = '<span class="px-1.5 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">Pending</span>';
            break;
          case 'cancelled':
            statusBadge = '<span class="px-1.5 py-0.5 bg-red-100 text-red-800 rounded-full text-xs">Cancelled</span>';
            break;
          case 'rescheduled':
            statusBadge = '<span class="px-1.5 py-0.5 bg-yellow-100 text-yellow-800 rounded-full text-xs">Rescheduled</span>';
            break;
          default:
            statusBadge = '<span class="px-1.5 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">Unknown</span>';
        }

        // Check if this is current lecture - simplified highlight logic
        const isCurrent = isCurrentLecture(lecture);

        // Apply highlighting only for current lecture
        let specialClasses = '';
        if (isCurrent) {
          specialClasses = 'ring-2 ring-red-500 ring-offset-2';
          bgColorClass = 'bg-red-100';
        }

          // Use section_display if available, otherwise fallback to section
          const sectionDisplay = lecture.section_display || lecture.section;

          // Check if lecture has instruction plans or syllabus topics
          const hasInstructionPlan = lecture.has_instruction_plan > 0;
          const hasSyllabusTopic = lecture.has_syllabus_topic > 0;

          // Create badges for instruction plans and syllabus topics
          let instructionPlanBadge = hasInstructionPlan
            ? '<span class="px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs mr-1" title="Has instruction plan">IP</span>'
            : '';

          let syllabusTopicBadge = hasSyllabusTopic
            ? '<span class="px-1.5 py-0.5 bg-purple-100 text-purple-800 rounded-full text-xs mr-1" title="Has syllabus topic">ST</span>'
            : '';

          // Add badge for multiple lectures if needed
          let multipleLecturesBadge = lecturesForSlot.length > 1
            ? `<span class="px-1.5 py-0.5 bg-yellow-100 text-yellow-800 rounded-full text-xs mr-1" title="Multiple lectures in this slot">+${lecturesForSlot.length - 1}</span>`
            : '';

          const lectureElem = document.createElement('div');
          lectureElem.className = `lecture-slot ${bgColorClass} p-2 h-full ${specialClasses} ${streamBorderClass}`;
          lectureElem.innerHTML = `
            <div class="text-xs font-medium text-gray-500">${lecture.class_name} ${sectionDisplay} <span class="italic">(${lecture.stream})</span></div>
            <div class="text-sm font-semibold">${lecture.subject_name}</div>
            <div class="text-xs mt-1 line-clamp-1">${lecture.topic}</div>
            <div class="text-xs text-gray-600 mt-1">${lecture.teacher_name}</div>
            <div class="text-xs text-gray-600 mt-1">${lecture.location}</div>
            <div class="flex flex-wrap justify-between items-center mt-1">
              <div>
                ${statusBadge}
                ${instructionPlanBadge}
                ${syllabusTopicBadge}
                ${multipleLecturesBadge}
              </div>
              <button class="px-2 py-1 bg-white border border-gray-300 rounded text-xs lecture-details-btn hover:bg-gray-100" data-lecture-id="${lecture.id}">
                Details
              </button>
            </div>
          `;
          dayCell.appendChild(lectureElem);
      } else {
        // Empty slot
        dayCell.classList.add('empty-slot');
        dayCell.innerHTML = '<div class="p-2 text-center text-gray-400 text-sm h-24 flex items-center justify-center bg-gray-50">No lecture</div>';
      }

      row.appendChild(dayCell);
    }

    timetableBody.appendChild(row);
  });
}
