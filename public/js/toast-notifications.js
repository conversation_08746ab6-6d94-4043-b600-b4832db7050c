/**
 * Toast Notification System
 * Provides functions to show toast notifications for different types (success, error, info, warning)
 */

const ToastNotifications = {
  /**
   * Initialize the toast notification container
   */
  init: function() {
    // Create toast container if it doesn't exist
    if (!document.getElementById('toast-container')) {
      const toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';

      // Use different positioning for mobile vs desktop
      const isMobile = window.innerWidth < 768;
      if (isMobile) {
        // On mobile, position at bottom center
        toastContainer.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col space-y-2 w-full max-w-xs px-4';
      } else {
        // On desktop, position at top right
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
      }

      document.body.appendChild(toastContainer);

      // Update positioning on window resize
      window.addEventListener('resize', function() {
        const container = document.getElementById('toast-container');
        if (container) {
          const isMobile = window.innerWidth < 768;
          if (isMobile) {
            container.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col space-y-2 w-full max-w-xs px-4';
          } else {
            container.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
          }
        }
      });
    }
  },

  /**
   * Show a toast notification
   * @param {string} message - The message to display
   * @param {string} type - The type of notification (success, error, info, warning)
   * @param {number} duration - Duration in milliseconds to show the toast
   */
  show: function(message, type = 'info', duration = 5000) {
    this.init();

    // Create toast element
    const toast = document.createElement('div');

    // Check if mobile
    const isMobile = window.innerWidth < 768;

    // Different styling for mobile vs desktop
    if (isMobile) {
      toast.className = `flex items-center p-4 rounded-lg shadow-lg transition-all transform translate-y-0 opacity-0 w-full ${this._getBackgroundColor(type)}`;
    } else {
      toast.className = `flex items-center p-3 rounded shadow-lg transition-all transform translate-x-0 opacity-0 max-w-xs ${this._getBackgroundColor(type)}`;
    }

    // Add icon based on type
    toast.innerHTML = `
      <div class="flex-shrink-0 mr-3">
        ${this._getIcon(type)}
      </div>
      <div class="flex-grow text-white ${isMobile ? 'text-base' : 'text-sm'}">
        ${message}
      </div>
      <button class="ml-2 text-white focus:outline-none" onclick="this.parentElement.remove()">
        <svg class="${isMobile ? 'w-5 h-5' : 'w-4 h-4'}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    `;

    // Add to container
    const container = document.getElementById('toast-container');
    container.appendChild(toast);

    // Animate in
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        // For mobile, slide up from bottom
        if (isMobile) {
          toast.classList.add('opacity-100');
          toast.style.transform = 'translateY(0)';
        } else {
          // For desktop, fade in
          toast.classList.add('opacity-100');
        }
      });
    });

    // Auto remove after duration
    const timeoutId = setTimeout(() => {
      // For mobile, slide down
      if (isMobile) {
        toast.style.transform = 'translateY(10px)';
        toast.classList.add('opacity-0');
      } else {
        // For desktop, fade out
        toast.classList.add('opacity-0');
      }

      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    }, duration);

    // Allow clicking to dismiss early
    toast.addEventListener('click', () => {
      clearTimeout(timeoutId);
      toast.classList.add('opacity-0');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    });
  },

  /**
   * Show a success toast notification
   * @param {string} message - The message to display
   * @param {number} duration - Duration in milliseconds to show the toast
   */
  success: function(message, duration = 5000) {
    this.show(message, 'success', duration);
  },

  /**
   * Show an error toast notification
   * @param {string} message - The message to display
   * @param {number} duration - Duration in milliseconds to show the toast
   */
  error: function(message, duration = 5000) {
    this.show(message, 'error', duration);
  },

  /**
   * Show an info toast notification
   * @param {string} message - The message to display
   * @param {number} duration - Duration in milliseconds to show the toast
   */
  info: function(message, duration = 5000) {
    this.show(message, 'info', duration);
  },

  /**
   * Show a warning toast notification
   * @param {string} message - The message to display
   * @param {number} duration - Duration in milliseconds to show the toast
   */
  warning: function(message, duration = 5000) {
    this.show(message, 'warning', duration);
  },

  /**
   * Get the background color class based on the notification type
   * @param {string} type - The type of notification
   * @returns {string} - The background color class
   * @private
   */
  _getBackgroundColor: function(type) {
    switch (type) {
      case 'success':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
      case 'warning':
        return 'bg-yellow-600';
      case 'info':
      default:
        return 'bg-blue-600';
    }
  },

  /**
   * Get the icon based on the notification type
   * @param {string} type - The type of notification
   * @returns {string} - The SVG icon HTML
   * @private
   */
  _getIcon: function(type) {
    switch (type) {
      case 'success':
        return `<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>`;
      case 'error':
        return `<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>`;
      case 'warning':
        return `<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>`;
      case 'info':
      default:
        return `<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>`;
    }
  }
};

// Create a function to create a notification in the database and show a toast
async function createNotificationAndToast(message, type = 'info', link = null) {
  try {
    // Show toast notification
    ToastNotifications.show(message, type);

    // Create notification in database
    const response = await fetch('/api/notifications/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        type,
        link
      })
    });

    if (!response.ok) {
      console.error('Failed to create notification in database');
    }

    // Update notification count in navbar
    const notificationCountElement = document.querySelector('.notification-count');
    if (notificationCountElement) {
      const currentCount = parseInt(notificationCountElement.textContent || '0');
      notificationCountElement.textContent = currentCount + 1;
      notificationCountElement.classList.remove('hidden');
    }

    return response.ok;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
}

// Initialize toast notifications when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  ToastNotifications.init();
});
