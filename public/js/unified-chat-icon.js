/**
 * Unified Chat Icon
 * Provides a consistent chat icon across all pages
 */

class UnifiedChatIcon {
    constructor() {
        this.chatIcon = null;
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        try {
            this.createChatIcon();
            this.setupEventListeners();
            this.isInitialized = true;
            console.log('Unified chat icon initialized');
        } catch (error) {
            console.error('Error initializing chat icon:', error);
        }
    }

    createChatIcon() {
        // Check if chat icon already exists
        if (document.getElementById('unified-chat-icon')) {
            return;
        }

        // Create chat icon element
        this.chatIcon = document.createElement('div');
        this.chatIcon.id = 'unified-chat-icon';
        this.chatIcon.className = 'fixed bottom-6 right-6 z-50 cursor-pointer transform transition-all duration-300 hover:scale-110';
        
        this.chatIcon.innerHTML = `
            <div class="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                    </path>
                </svg>
            </div>
            <div class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden" id="chat-notification-badge">
                <span id="chat-notification-count">0</span>
            </div>
        `;

        // Add to page
        if (document.body) {
            document.body.appendChild(this.chatIcon);
        } else {
            // Wait for body to be available
            document.addEventListener('DOMContentLoaded', () => {
                if (document.body) {
                    document.body.appendChild(this.chatIcon);
                }
            });
        }
    }

    setupEventListeners() {
        if (!this.chatIcon) return;

        this.chatIcon.addEventListener('click', () => {
            this.openChat();
        });

        // Listen for chat notifications
        document.addEventListener('chatNotification', (event) => {
            this.updateNotificationBadge(event.detail.count);
        });
    }

    openChat() {
        try {
            // Check if there's a chat popup
            const chatPopup = document.getElementById('chatPopup');
            if (chatPopup) {
                this.toggleChatPopup(chatPopup);
            } else {
                // Redirect to chat page
                window.location.href = '/chat';
            }
        } catch (error) {
            console.error('Error opening chat:', error);
            // Fallback to chat page
            window.location.href = '/chat';
        }
    }

    toggleChatPopup(popup) {
        if (popup.classList.contains('hidden')) {
            popup.classList.remove('hidden');
            popup.classList.remove('scale-95', 'opacity-0');
            popup.classList.add('scale-100', 'opacity-100');
        } else {
            popup.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                popup.classList.add('hidden');
            }, 300);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.getElementById('chat-notification-badge');
        const countElement = document.getElementById('chat-notification-count');
        
        if (badge && countElement) {
            if (count > 0) {
                countElement.textContent = count > 99 ? '99+' : count;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }
    }

    show() {
        if (this.chatIcon) {
            this.chatIcon.style.display = 'block';
        }
    }

    hide() {
        if (this.chatIcon) {
            this.chatIcon.style.display = 'none';
        }
    }

    destroy() {
        if (this.chatIcon && this.chatIcon.parentNode) {
            this.chatIcon.parentNode.removeChild(this.chatIcon);
            this.chatIcon = null;
            this.isInitialized = false;
        }
    }
}

// Initialize when DOM is ready
function initUnifiedChatIcon() {
    if (!window.unifiedChatIcon) {
        window.unifiedChatIcon = new UnifiedChatIcon();
    }
}

// Multiple initialization strategies
document.addEventListener('DOMContentLoaded', initUnifiedChatIcon);

if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initUnifiedChatIcon();
}

// Fallback initialization
setTimeout(initUnifiedChatIcon, 500);

console.log('Unified chat icon script loaded');
