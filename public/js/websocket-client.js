/**
 * WebSocket client for real-time communication
 * Handles force logout and other real-time events
 */

// Track connection attempts
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 3;
let reconnectTimeout = null;
let isWebSocketSupported = true;

// Check if WebSocket is supported
if (typeof WebSocket === 'undefined') {
    console.warn('WebSocket not supported by this browser');
    isWebSocketSupported = false;
}

// Initialize WebSocket connection
function initWebSocket() {
    // Skip if WebSocket is not supported
    if (!isWebSocketSupported) {
        return;
    }

    // Don't attempt to connect if we've reached the maximum number of attempts
    if (connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
        console.log('Maximum WebSocket reconnection attempts reached. WebSocket features disabled.');
        return;
    }

    // Increment connection attempts
    connectionAttempts++;

    try {
        // Create WebSocket connection
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        // Only log first attempt to reduce noise
        if (connectionAttempts === 1) {
            console.log(`Attempting WebSocket connection to ${wsUrl}`);
        }

        const socket = new WebSocket(wsUrl);

        // Connection opened
        socket.addEventListener('open', (event) => {
            console.log('WebSocket connection established');
            // Reset connection attempts on successful connection
            connectionAttempts = 0;
        });

        // Listen for messages
        socket.addEventListener('message', (event) => {
            try {
                const data = JSON.parse(event.data);
                console.log('Message from server:', data);

                // Handle different message types
                switch (data.type) {
                    case 'connection':
                        console.log('WebSocket connection confirmed');
                        break;
                    case 'force-logout':
                        handleForceLogout(data.message);
                        break;
                    default:
                        console.log('Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('Error processing WebSocket message:', error);
            }
        });

        // Connection closed
        socket.addEventListener('close', (event) => {
            // Only log if this was an unexpected close
            if (connectionAttempts === 1) {
                console.log('WebSocket connection closed');
            }

            // Clear any existing reconnect timeout
            if (reconnectTimeout) {
                clearTimeout(reconnectTimeout);
            }

            // Try to reconnect after 5 seconds if not at max attempts
            if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
                if (connectionAttempts === 1) {
                    console.log(`Will attempt to reconnect in 5 seconds...`);
                }
                reconnectTimeout = setTimeout(initWebSocket, 5000);
            }
        });

        // Connection error
        socket.addEventListener('error', (event) => {
            // Only log error on first attempt to reduce noise
            if (connectionAttempts === 1) {
                console.warn('WebSocket connection failed - real-time features will be disabled');
            }
            // The close event will handle reconnection
        });

        // Store socket in window object for global access
        window.webSocket = socket;
    } catch (error) {
        console.error('Error initializing WebSocket:', error);
    }
}

// Handle force logout
function handleForceLogout(message) {
    console.log('Force logout received:', message);

    // Log message to console for debugging
    console.log('Force logout message:', message || 'You have been logged out by an administrator');

    // Redirect to login page
    window.location.href = '/login';
}

// Initialize WebSocket when page loads
document.addEventListener('DOMContentLoaded', function() {
    // WebSocket is currently disabled - no server endpoint configured
    // This prevents console errors and connection attempts
    console.log('WebSocket client loaded (currently disabled - no server configured)');

    // Set a flag to indicate WebSocket is not available
    window.webSocketDisabled = true;

    // To enable WebSocket, uncomment the line below and ensure server supports WebSocket:
    // initWebSocket();
});
