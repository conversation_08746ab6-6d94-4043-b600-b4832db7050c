/**
 * WhatsApp-style Chat Component
 * Provides a floating chat button with a vertical layout showing contacts on left and messages on right
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is logged in
    if (!document.body.classList.contains('logged-in')) return;

    // Create floating chat button
    const chatButton = document.createElement('div');
    chatButton.id = 'floatingChatButton';
    chatButton.className = 'fixed bottom-6 right-6 bg-purple-600 text-white rounded-full w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center shadow-lg cursor-pointer z-[99999] hover:bg-purple-700 transition-all';
    chatButton.style.cssText = 'position: fixed !important; bottom: 20px !important; right: 20px !important; z-index: 99999 !important; background-color: #7c3aed !important;';
    chatButton.innerHTML = `
        <svg class="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    `;
    document.body.appendChild(chatButton);

    // Create chat popup
    const chatPopup = document.createElement('div');
    chatPopup.id = 'chatPopup';
    chatPopup.className = 'fixed bottom-24 right-6 bg-white rounded-lg shadow-xl z-[99980] hidden transition-all transform scale-95 opacity-0 flex flex-col';
    chatPopup.style.cssText = 'position: fixed !important; bottom: 90px !important; right: 20px !important; z-index: 99980 !important;';
    chatPopup.innerHTML = `
        <div class="flex h-full w-full">
            <!-- Left sidebar with contacts -->
            <div class="w-1/3 md:w-2/5 border-r border-gray-200 flex flex-col">
                <!-- Search bar -->
                <div class="p-2 sm:p-3 border-b border-gray-200">
                    <div class="relative">
                        <input type="text" id="chatSearchInput" placeholder="Search" class="w-full pl-8 sm:pl-10 pr-2 sm:pr-4 py-1 sm:py-2 text-sm sm:text-base rounded-full bg-gray-100 focus:outline-none focus:ring-1 focus:ring-purple-500">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 absolute left-2 sm:left-3 top-1.5 sm:top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="flex border-b border-gray-200">
                    <button id="peopleTabBtn" class="flex-1 py-2 sm:py-3 px-2 sm:px-4 text-sm sm:text-base font-medium text-center border-b-2 border-purple-600 text-purple-600">
                        People
                    </button>
                    <button id="groupsTabBtn" class="flex-1 py-2 sm:py-3 px-2 sm:px-4 text-sm sm:text-base font-medium text-center border-b-2 border-transparent hover:text-purple-600">
                        Groups
                    </button>
                </div>

                <!-- People list -->
                <div id="peopleList" class="flex-1 overflow-y-auto">
                    <!-- People will be loaded here -->
                </div>

                <!-- Groups list -->
                <div id="groupsList" class="flex-1 overflow-y-auto hidden">
                    <!-- Groups will be loaded here -->
                </div>
            </div>

            <!-- Right side with messages -->
            <div class="w-2/3 md:w-3/5 flex flex-col">
                <!-- Chat header -->
                <div id="chatHeader" class="p-2 sm:p-3 border-b border-gray-200 flex items-center">
                    <div id="noChatSelected" class="text-center w-full text-gray-500 text-sm sm:text-base">
                        Select a chat to start messaging
                    </div>
                    <div id="chatRecipientInfo" class="flex items-center hidden w-full">
                        <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-purple-100 flex items-center justify-center mr-2 sm:mr-3">
                            <span id="chatRecipientInitial" class="text-purple-600 font-bold text-sm sm:text-base"></span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 id="chatRecipientName" class="font-medium text-sm sm:text-base truncate"></h3>
                            <p id="chatRecipientStatus" class="text-xs text-gray-500">Online</p>
                        </div>
                        <button id="closeChatBtn" class="ml-1 text-gray-500 hover:text-gray-700">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Messages area -->
                <div id="messagesContainer" class="flex-1 overflow-y-auto p-2 sm:p-4 bg-gray-50">
                    <!-- Messages will be loaded here -->
                </div>

                <!-- Message input -->
                <div id="messageInputContainer" class="p-2 sm:p-3 border-t border-gray-200 hidden">
                    <div class="flex items-center">
                        <input type="text" id="messageInput" placeholder="Message..." class="flex-1 px-3 py-2 text-sm sm:text-base sm:px-4 rounded-full bg-gray-100 focus:outline-none focus:ring-1 focus:ring-purple-500">
                        <button id="sendMessageBtn" class="ml-1 sm:ml-2 bg-purple-600 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center hover:bg-purple-700">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                        <button id="testDummyMessagesBtn" class="ml-1 sm:ml-2 bg-gray-500 text-white rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center hover:bg-gray-600" title="Test with dummy messages">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(chatPopup);

    // Variables to track current chat
    let currentChatId = null;
    let currentChatType = null; // 'user' or 'group'
    let currentUserId = document.body.getAttribute('data-user-id') || 1; // Get current user ID from body attribute

    // Event listeners
    chatButton.addEventListener('click', toggleChatPopup);
    document.getElementById('peopleTabBtn').addEventListener('click', showPeopleList);
    document.getElementById('groupsTabBtn').addEventListener('click', showGroupsList);
    document.getElementById('closeChatBtn').addEventListener('click', closeCurrentChat);
    document.getElementById('sendMessageBtn').addEventListener('click', sendMessage);
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') sendMessage();
    });
    document.getElementById('chatSearchInput').addEventListener('input', handleSearch);
    document.getElementById('testDummyMessagesBtn').addEventListener('click', addDummyMessages);

    // Load initial data
    loadPeopleList();
    loadGroupsList();
    updateUnreadCount();

    // Set interval to update unread count
    setInterval(updateUnreadCount, 30000);

    // Functions
    function toggleChatPopup() {
        const popup = document.getElementById('chatPopup');
        if (popup.classList.contains('hidden')) {
            // Show popup
            popup.classList.remove('hidden');
            setTimeout(() => {
                popup.classList.remove('scale-95', 'opacity-0');
                popup.classList.add('scale-100', 'opacity-100');
            }, 10);
        } else {
            // Hide popup
            popup.classList.remove('scale-100', 'opacity-100');
            popup.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                popup.classList.add('hidden');
            }, 300);
        }
    }

    function showPeopleList() {
        const peopleTabBtn = document.getElementById('peopleTabBtn');
        const groupsTabBtn = document.getElementById('groupsTabBtn');
        const peopleList = document.getElementById('peopleList');
        const groupsList = document.getElementById('groupsList');

        peopleTabBtn.classList.add('border-purple-600', 'text-purple-600');
        groupsTabBtn.classList.remove('border-purple-600', 'text-purple-600');
        peopleList.classList.remove('hidden');
        groupsList.classList.add('hidden');
    }

    function showGroupsList() {
        const peopleTabBtn = document.getElementById('peopleTabBtn');
        const groupsTabBtn = document.getElementById('groupsTabBtn');
        const peopleList = document.getElementById('peopleList');
        const groupsList = document.getElementById('groupsList');

        groupsTabBtn.classList.add('border-purple-600', 'text-purple-600');
        peopleTabBtn.classList.remove('border-purple-600', 'text-purple-600');
        groupsList.classList.remove('hidden');
        peopleList.classList.add('hidden');
    }

    async function loadPeopleList() {
        const peopleList = document.getElementById('peopleList');
        peopleList.innerHTML = '<div class="text-center py-4 text-gray-500">Loading...</div>';

        try {
            const response = await fetch('/api/chat/users');

            // Handle unauthorized errors
            if (response.status === 401) {
                peopleList.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view users</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                if (!data.users || data.users.length === 0) {
                    peopleList.innerHTML = '<div class="text-center py-4 text-gray-500">No users found</div>';
                    return;
                }

                peopleList.innerHTML = '';
                data.users.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100';
                    userItem.onclick = () => loadUserChat(user.id, user.username);

                    // Get first letter of username for avatar
                    const initial = user.username.charAt(0).toUpperCase();

                    userItem.innerHTML = `
                        <div class="relative">
                            ${user.profile_image
                                ? `<img src="${user.profile_image}" alt="${user.username}" class="w-12 h-12 rounded-full object-cover">`
                                : `<div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                                    <span class="text-purple-600 font-bold">${initial}</span>
                                   </div>`
                            }
                            ${user.unread_count > 0
                                ? `<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                    ${user.unread_count}
                                   </span>`
                                : ''
                            }
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">${user.username}</h3>
                                ${user.last_message_time
                                    ? `<span class="text-xs text-gray-500">${formatTime(user.last_message_time)}</span>`
                                    : ''
                                }
                            </div>
                            <p class="text-sm text-gray-500 truncate">${user.last_message || 'No messages yet'}</p>
                        </div>
                    `;
                    peopleList.appendChild(userItem);
                });
            } else {
                peopleList.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading users</div>';
            }
        } catch (error) {
            console.error('Error loading users:', error);
            peopleList.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading users</div>';
        }
    }

    async function loadGroupsList() {
        const groupsList = document.getElementById('groupsList');
        groupsList.innerHTML = '<div class="text-center py-4 text-gray-500">Loading...</div>';

        try {
            const response = await fetch('/api/chat/groups');

            // Handle unauthorized errors
            if (response.status === 401) {
                groupsList.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view groups</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                if (!data.groups || data.groups.length === 0) {
                    groupsList.innerHTML = '<div class="text-center py-4 text-gray-500">No groups found</div>';
                    return;
                }

                groupsList.innerHTML = '';
                data.groups.forEach(group => {
                    const groupItem = document.createElement('div');
                    groupItem.className = 'flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100';
                    groupItem.onclick = () => loadGroupChat(group.group_id, group.name);

                    // Get first letter of group name for avatar
                    const initial = group.name.charAt(0).toUpperCase();

                    groupItem.innerHTML = `
                        <div class="relative">
                            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                                <span class="text-purple-600 font-bold">${initial}</span>
                            </div>
                            ${group.unread_count > 0
                                ? `<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                    ${group.unread_count}
                                   </span>`
                                : ''
                            }
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="flex justify-between">
                                <h3 class="font-medium">${group.name}</h3>
                                ${group.last_message_time
                                    ? `<span class="text-xs text-gray-500">${formatTime(group.last_message_time)}</span>`
                                    : ''
                                }
                            </div>
                            <p class="text-sm text-gray-500 truncate">${group.last_message || 'No messages yet'}</p>
                        </div>
                    `;
                    groupsList.appendChild(groupItem);
                });
            } else {
                groupsList.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading groups</div>';
            }
        } catch (error) {
            console.error('Error loading groups:', error);
            groupsList.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading groups</div>';
        }
    }

    async function loadUserChat(userId, username) {
        currentChatId = userId;
        currentChatType = 'user';

        // Update UI
        document.getElementById('noChatSelected').classList.add('hidden');
        document.getElementById('chatRecipientInfo').classList.remove('hidden');
        document.getElementById('messageInputContainer').classList.remove('hidden');

        // Set recipient info
        document.getElementById('chatRecipientName').textContent = username;
        document.getElementById('chatRecipientInitial').textContent = username.charAt(0).toUpperCase();

        // Clear messages container
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Loading messages...</div>';

        try {
            const response = await fetch(`/api/chat/messages/user/${userId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view messages</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/user/${userId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));

                // Update unread count
                updateUnreadCount();
            } else {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
            }
        } catch (error) {
            console.error('Error loading user chat messages:', error);
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
        }
    }

    async function loadGroupChat(groupId, groupName) {
        currentChatId = groupId;
        currentChatType = 'group';

        // Update UI
        document.getElementById('noChatSelected').classList.add('hidden');
        document.getElementById('chatRecipientInfo').classList.remove('hidden');
        document.getElementById('messageInputContainer').classList.remove('hidden');

        // Set recipient info
        document.getElementById('chatRecipientName').textContent = groupName;
        document.getElementById('chatRecipientInitial').textContent = groupName.charAt(0).toUpperCase();

        // Clear messages container
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Loading messages...</div>';

        try {
            const response = await fetch(`/api/chat/messages/group/${groupId}`);

            // Handle unauthorized errors
            if (response.status === 401) {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Please log in to view messages</div>';
                return;
            }

            const data = await response.json();

            if (data.success) {
                displayMessages(data.messages);

                // Mark messages as read
                fetch(`/api/chat/messages/group/${groupId}/read`, { method: 'POST' })
                    .catch(err => console.error('Error marking messages as read:', err));

                // Update unread count
                updateUnreadCount();
            } else {
                messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
            }
        } catch (error) {
            console.error('Error loading group chat messages:', error);
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">Error loading messages</div>';
        }
    }

    function displayMessages(messages) {
        const messagesContainer = document.getElementById('messagesContainer');
        const currentUserId = parseInt(document.body.dataset.userId);

        if (!messages || messages.length === 0) {
            messagesContainer.innerHTML = '<div class="text-center py-4 text-gray-500">No messages yet</div>';
            return;
        }

        messagesContainer.innerHTML = '';

        // Group messages by date
        const messagesByDate = {};
        messages.forEach(message => {
            const date = new Date(message.created_at).toLocaleDateString();
            if (!messagesByDate[date]) {
                messagesByDate[date] = [];
            }
            messagesByDate[date].push(message);
        });

        // Display messages grouped by date
        Object.keys(messagesByDate).forEach(date => {
            // Add date separator
            const dateDiv = document.createElement('div');
            dateDiv.className = 'text-center my-3';
            dateDiv.innerHTML = `<span class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">${date}</span>`;
            messagesContainer.appendChild(dateDiv);

            // Add messages for this date
            messagesByDate[date].forEach(message => {
                const isCurrentUser = message.sender_id === currentUserId;
                const messageDiv = document.createElement('div');
                messageDiv.className = `flex mb-3 ${isCurrentUser ? 'justify-end' : 'justify-start'}`;

                messageDiv.innerHTML = `
                    <div class="${isCurrentUser ? 'bg-purple-600 text-white' : 'bg-white text-gray-800'} rounded-lg py-2 px-3 max-w-[70%] shadow-sm">
                        ${!isCurrentUser ? `<div class="text-xs text-gray-600 font-medium mb-1">${message.sender_name || 'User'}</div>` : ''}
                        <div>${message.message}</div>
                        <div class="text-xs ${isCurrentUser ? 'text-purple-200' : 'text-gray-500'} text-right mt-1">
                            ${new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                    </div>
                `;

                messagesContainer.appendChild(messageDiv);
            });
        });

        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Function to show error message in chat
    function showChatError(message) {
        const messagesContainer = document.getElementById('messagesContainer');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'flex justify-center my-2';
        errorDiv.innerHTML = `
            <div class="bg-red-100 text-red-800 rounded-lg py-1 px-3 text-sm shadow-sm">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    ${message}
                </div>
            </div>
        `;
        messagesContainer.appendChild(errorDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Remove error after 5 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    // Add some dummy messages for testing
    function addDummyMessages() {
        const messagesContainer = document.getElementById('messagesContainer');

        // Clear existing messages
        messagesContainer.innerHTML = '';

        // Add dummy messages
        const dummyMessages = [
            { sender_id: 999, message: 'Hello there!', created_at: new Date() },
            { sender_id: currentUserId, message: 'Hi! How are you?', created_at: new Date() },
            { sender_id: 999, message: 'I\'m doing great, thanks for asking!', created_at: new Date() },
            { sender_id: currentUserId, message: 'That\'s good to hear.', created_at: new Date() },
            { sender_id: 999, message: 'How about you?', created_at: new Date() }
        ];

        displayMessages(dummyMessages);

        // Show an error message
        showChatError('This is a test error message');
    }

    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        if (!message || !currentChatId || !currentChatType) {
            if (!message) {
                showChatError('Message cannot be empty');
            } else if (!currentChatId || !currentChatType) {
                showChatError('No chat selected');
            }
            return;
        }

        // Store message to restore if sending fails
        const originalMessage = message;
        messageInput.value = '';

        try {
            const endpoint = currentChatType === 'user'
                ? `/api/chat/messages/user/${currentChatId}/send`
                : `/api/chat/messages/group/${currentChatId}/send`;

            console.log('Sending message to endpoint:', endpoint);
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            // Handle unauthorized errors
            if (response.status === 401) {
                showChatError('Please log in to send messages');
                messageInput.value = originalMessage;
                return;
            }

            const data = await response.json();
            console.log('Send message response:', data);

            if (data.success) {
                // Reload messages
                if (currentChatType === 'user') {
                    loadUserChat(currentChatId, document.getElementById('chatRecipientName').textContent);
                } else {
                    loadGroupChat(currentChatId, document.getElementById('chatRecipientName').textContent);
                }
            } else {
                // Show error message
                showChatError(data.message || 'Failed to send message');
                // Restore message to input
                messageInput.value = originalMessage;
            }
        } catch (error) {
            console.error('Error sending message:', error);
            showChatError('Network error. Please try again.');
            // Restore message to input
            messageInput.value = originalMessage;
        }
    }

    function closeCurrentChat() {
        currentChatId = null;
        currentChatType = null;

        document.getElementById('noChatSelected').classList.remove('hidden');
        document.getElementById('chatRecipientInfo').classList.add('hidden');
        document.getElementById('messageInputContainer').classList.add('hidden');
        document.getElementById('messagesContainer').innerHTML = '';
    }

    function handleSearch() {
        const searchInput = document.getElementById('chatSearchInput');
        const searchTerm = searchInput.value.toLowerCase();

        // Search in people list
        const peopleList = document.getElementById('peopleList');
        const peopleItems = peopleList.querySelectorAll('div.flex.items-center');

        peopleItems.forEach(item => {
            const username = item.querySelector('h3').textContent.toLowerCase();
            if (username.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });

        // Search in groups list
        const groupsList = document.getElementById('groupsList');
        const groupItems = groupsList.querySelectorAll('div.flex.items-center');

        groupItems.forEach(item => {
            const groupName = item.querySelector('h3').textContent.toLowerCase();
            if (groupName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    async function updateUnreadCount() {
        try {
            const response = await fetch('/api/chat/unread-count');

            // Handle unauthorized errors silently
            if (response.status === 401) {
                return;
            }

            const data = await response.json();

            if (data.success) {
                const totalUnread = data.privateUnread + data.groupUnread;

                // Update chat button with unread count
                if (totalUnread > 0) {
                    chatButton.innerHTML = `
                        <div class="relative">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                ${totalUnread}
                            </span>
                        </div>
                    `;
                } else {
                    chatButton.innerHTML = `
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    `;
                }

                // Reload lists if chat popup is open
                if (!document.getElementById('chatPopup').classList.contains('hidden')) {
                    loadPeopleList();
                    loadGroupsList();
                }
            }
        } catch (error) {
            console.error('Error updating unread count:', error);
        }
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();

        // If today, show time
        if (date.toDateString() === now.toDateString()) {
            return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // If yesterday, show "Yesterday"
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        }

        // Otherwise show date
        return date.toLocaleDateString();
    }
});
