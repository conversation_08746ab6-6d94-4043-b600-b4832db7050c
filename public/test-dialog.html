<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Confirmation Dialog</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/js/confirmation-dialog.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold mb-4">Test Confirmation Dialog</h1>
        
        <button id="testDialog" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Test Dialog
        </button>
        
        <div class="mt-4 p-4 bg-gray-100 rounded">
            <h2 class="font-semibold mb-2">Console Output:</h2>
            <pre id="consoleOutput" class="text-sm bg-gray-800 text-white p-4 rounded overflow-auto max-h-60"></pre>
        </div>
    </div>

    <script>
        // Capture console.log output
        (function() {
            const oldLog = console.log;
            console.log = function(...args) {
                oldLog.apply(console, args);
                const output = document.getElementById('consoleOutput');
                output.textContent += args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
                ).join(' ') + '\n';
            };
        })();

        // Log initial state
        console.log('Page loaded');
        console.log('Confirmation dialog available:', !!window.confirmationDialog);

        // Test button click
        document.getElementById('testDialog').addEventListener('click', function() {
            console.log('Test button clicked');
            
            if (!window.confirmationDialog) {
                console.log('Creating new confirmation dialog');
                window.confirmationDialog = new ConfirmationDialog();
            }
            
            window.confirmationDialog.show({
                title: 'Test Confirmation',
                message: 'This is a test confirmation dialog. Is it working correctly?',
                confirmText: 'Yes, it works!',
                cancelText: 'No, it doesn\'t',
                type: 'info',
                onConfirm: () => {
                    console.log('Dialog confirmed');
                },
                onCancel: () => {
                    console.log('Dialog canceled');
                }
            });
        });
    </script>
</body>
</html>
