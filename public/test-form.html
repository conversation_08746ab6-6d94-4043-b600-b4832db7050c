<!DOCTYPE html>
<html>
<head>
    <title>Test Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { max-width: 500px; margin: 0 auto; }
        div { margin-bottom: 10px; }
        label { display: block; margin-bottom: 5px; }
        input, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; display: none; }
    </style>
</head>
<body>
    <h1>Test Form Submission</h1>
    
    <form id="testForm">
        <div>
            <label for="title">Title:</label>
            <input type="text" id="title" name="title" value="Test Procurement" required>
        </div>
        
        <div>
            <label for="description">Description:</label>
            <textarea id="description" name="description">Test description</textarea>
        </div>
        
        <div>
            <label for="request_date">Request Date:</label>
            <input type="date" id="request_date" name="request_date" value="2023-07-01">
        </div>
        
        <div>
            <label for="current_step">Current Step:</label>
            <input type="number" id="current_step" name="current_step" value="1" min="1" max="5">
        </div>
        
        <div>
            <button type="button" id="submitBtn">Submit Form</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('submitBtn').addEventListener('click', function() {
            const form = document.getElementById('testForm');
            const resultDiv = document.getElementById('result');
            
            // Create FormData object
            const formData = new FormData(form);
            
            // Display what we're sending
            console.log('Sending form data:');
            for (const pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }
            
            // Send the form data
            fetch('/it-admin/procurement/test-simple', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                
                let result;
                try {
                    result = JSON.parse(text);
                    resultDiv.innerHTML = '<h3>Success!</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                } catch (e) {
                    resultDiv.innerHTML = '<h3>Response (not JSON):</h3><pre>' + text + '</pre>';
                }
                
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = '<h3>Error:</h3><p>' + error.message + '</p>';
                resultDiv.style.display = 'block';
            });
        });
    </script>
</body>
</html>
