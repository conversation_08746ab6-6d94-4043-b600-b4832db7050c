<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .indicator { 
            position: fixed; 
            padding: 10px; 
            color: white; 
            font-weight: bold; 
            border-radius: 5px; 
            z-index: 9999; 
        }
        .red { background: red; top: 10px; left: 10px; }
        .green { background: green; top: 10px; left: 200px; }
        .blue { background: blue; top: 10px; left: 400px; }
    </style>
</head>
<body>
    <h1>JavaScript Test Page</h1>
    <p>This page tests if JavaScript is working at all.</p>
    
    <div id="results">
        <h2>Test Results:</h2>
        <div id="test-output">Waiting for JavaScript...</div>
    </div>

    <script>
        // Test 1: Basic JavaScript
        console.log('TEST 1: Basic JavaScript working');
        
        // Test 2: DOM manipulation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('TEST 2: DOM Content Loaded');
            
            // Add red indicator
            const redDiv = document.createElement('div');
            redDiv.className = 'indicator red';
            redDiv.textContent = '🔴 JS WORKS';
            document.body.appendChild(redDiv);
            
            // Update results
            document.getElementById('test-output').innerHTML = '✅ JavaScript is working!<br>✅ DOM manipulation works!';
        });
        
        // Test 3: Timeout
        setTimeout(function() {
            console.log('TEST 3: Timeout working');
            
            // Add green indicator
            const greenDiv = document.createElement('div');
            greenDiv.className = 'indicator green';
            greenDiv.textContent = '🟢 TIMEOUT WORKS';
            document.body.appendChild(greenDiv);
        }, 1000);
        
        // Test 4: Immediate execution
        console.log('TEST 4: Immediate execution');
        const blueDiv = document.createElement('div');
        blueDiv.className = 'indicator blue';
        blueDiv.textContent = '🔵 IMMEDIATE';
        blueDiv.style.cssText = 'position:fixed;top:10px;left:400px;background:blue;color:white;padding:10px;font-weight:bold;border-radius:5px;z-index:9999;';
        
        // Add blue indicator when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                document.body.appendChild(blueDiv);
            });
        } else {
            document.body.appendChild(blueDiv);
        }
    </script>
</body>
</html>
