<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Validation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .feedback {
            min-height: 20px;
            margin-top: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Test Validation</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" required minlength="3">
            <div id="username-feedback" class="feedback"></div>
        </div>
        
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
            <div id="email-feedback" class="feedback"></div>
        </div>
        
        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required minlength="8">
            <div id="password-strength" class="feedback"></div>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">Confirm Password</label>
            <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
            <div id="password-match-feedback" class="feedback"></div>
        </div>
        
        <button type="submit">Submit</button>
    </form>
    
    <script>
        // Execute immediately
        (function() {
            console.log('Self-executing validation function running');
            
            // Show validation messages immediately for testing
            setTimeout(function() {
                console.log('Showing test validation messages');
                
                const usernameFeedback = document.getElementById('username-feedback');
                if (usernameFeedback) {
                    usernameFeedback.textContent = 'Username validation is working';
                    usernameFeedback.style.color = 'green';
                } else {
                    console.error('username-feedback element not found');
                }
                
                const emailFeedback = document.getElementById('email-feedback');
                if (emailFeedback) {
                    emailFeedback.textContent = 'Email validation is working';
                    emailFeedback.style.color = 'green';
                } else {
                    console.error('email-feedback element not found');
                }
                
                const passwordStrength = document.getElementById('password-strength');
                if (passwordStrength) {
                    passwordStrength.textContent = 'Password strength validation is working';
                    passwordStrength.style.color = 'green';
                } else {
                    console.error('password-strength element not found');
                }
                
                const passwordMatchFeedback = document.getElementById('password-match-feedback');
                if (passwordMatchFeedback) {
                    passwordMatchFeedback.textContent = 'Password match validation is working';
                    passwordMatchFeedback.style.color = 'green';
                } else {
                    console.error('password-match-feedback element not found');
                }
            }, 1000);
            
            // Add event listeners
            document.getElementById('username').addEventListener('input', function() {
                const username = this.value.trim();
                const usernameFeedback = document.getElementById('username-feedback');
                
                if (username.length < 3) {
                    usernameFeedback.textContent = 'Username must be at least 3 characters';
                    usernameFeedback.style.color = 'red';
                } else {
                    usernameFeedback.textContent = 'Username is valid';
                    usernameFeedback.style.color = 'green';
                }
            });
            
            document.getElementById('email').addEventListener('input', function() {
                const email = this.value.trim();
                const emailFeedback = document.getElementById('email-feedback');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (!email || !emailRegex.test(email)) {
                    emailFeedback.textContent = 'Please enter a valid email address';
                    emailFeedback.style.color = 'red';
                } else {
                    emailFeedback.textContent = 'Email is valid';
                    emailFeedback.style.color = 'green';
                }
            });
            
            document.getElementById('password').addEventListener('input', function() {
                const password = this.value;
                const passwordStrength = document.getElementById('password-strength');
                
                if (password.length < 8) {
                    passwordStrength.textContent = 'Password must be at least 8 characters';
                    passwordStrength.style.color = 'red';
                } else if (password.length < 12) {
                    passwordStrength.textContent = 'Password strength: Medium';
                    passwordStrength.style.color = 'orange';
                } else {
                    passwordStrength.textContent = 'Password strength: Strong';
                    passwordStrength.style.color = 'green';
                }
                
                // Check password match if confirm password has a value
                const confirmPassword = document.getElementById('confirm_password').value;
                if (confirmPassword) {
                    checkPasswordMatch();
                }
            });
            
            document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);
            
            function checkPasswordMatch() {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                const passwordMatchFeedback = document.getElementById('password-match-feedback');
                
                if (!confirmPassword) {
                    passwordMatchFeedback.textContent = '';
                    return;
                }
                
                if (password === confirmPassword) {
                    passwordMatchFeedback.textContent = 'Passwords match';
                    passwordMatchFeedback.style.color = 'green';
                } else {
                    passwordMatchFeedback.textContent = 'Passwords do not match';
                    passwordMatchFeedback.style.color = 'red';
                }
            }
            
            // Prevent form submission for this test
            document.getElementById('testForm').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Form would be submitted here');
            });
        })();
    </script>
</body>
</html>
