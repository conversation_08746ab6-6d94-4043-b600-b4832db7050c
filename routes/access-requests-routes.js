const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Middleware to set layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.currentPage = 'access-requests';
    res.locals.navbar = 'admin';
    next();
});

// Access Requests index page (admin only)
router.get('/', async (req, res, next) => {
    console.log('Access Requests route hit');
    try {
        // Simplified version to debug the issue
        const page = 1;
        const perPage = 10;
        const offset = 0;
        const status = null;

        // Get status counts for quick filters - simplified query
        let statusStats = [{
            total: 0,
            pending: 0,
            approved: 0,
            rejected: 0
        }];

        try {
            const [stats] = await db.query('SELECT COUNT(*) as total FROM access_requests');
            statusStats[0].total = stats[0].total;
        } catch (dbError) {
            console.error('Error querying access_requests table:', dbError);
            // Continue with default values
        }

        // Empty requests array for now
        const requests = [];
        const totalRequests = 0;
        const totalPages = 1;

        res.render('admin/access-requests/index', {
            title: 'Test Access Requests',
            pageTitle: 'Test Access Requests',
            requests,
            status,
            stats: statusStats[0],
            pagination: {
                page,
                totalPages,
                perPage,
                totalItems: totalRequests
            },
            currentPage: 'access-requests',
            navbar: 'admin',
            formatDate,
            formatDateTime
        });
    } catch (error) {
        console.error('Error fetching access requests:', error);
        next(error);
    }
});

// Approve access request (admin only)
router.post('/approve', async (req, res) => {
    try {
        const { requestId, additionalAttempts } = req.body;
        const adminId = req.session.userId;

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Get the request details
            const [requests] = await db.query(
                'SELECT * FROM access_requests WHERE id = ? AND status = "pending"',
                [requestId]
            );

            if (requests.length === 0) {
                throw new Error('Request not found or already processed');
            }

            const request = requests[0];

            // Get the current max_attempts for the exam
            const [exams] = await db.query(
                'SELECT max_attempts FROM exams WHERE exam_id = ?',
                [request.exam_id]
            );

            if (exams.length === 0) {
                throw new Error('Exam not found');
            }

            // Get the user's completed attempts
            const [attempts] = await db.query(
                'SELECT COUNT(*) as completed_count FROM exam_attempts WHERE exam_id = ? AND user_id = ? AND status != "in_progress"',
                [request.exam_id, request.user_id]
            );

            const completedAttempts = attempts[0].completed_count;
            const currentMaxAttempts = exams[0].max_attempts || 1;

            // Calculate the new max attempts needed for this user
            // We need to ensure they can make at least 'additionalAttempts' more attempts
            const newMaxAttempts = Math.max(currentMaxAttempts, completedAttempts + parseInt(additionalAttempts));

            // Update the exam's max_attempts
            await db.query(
                'UPDATE exams SET max_attempts = ? WHERE exam_id = ?',
                [newMaxAttempts, request.exam_id]
            );

            // Update the request status
            await db.query(
                'UPDATE access_requests SET status = "approved", processed_at = NOW(), processed_by = ?, additional_attempts = ? WHERE id = ?',
                ['approved', adminId, additionalAttempts, requestId]
            );

            // Get user and exam details for notification
            const [users] = await db.query('SELECT username, email FROM users WHERE id = ?', [request.user_id]);
            const [examDetails] = await db.query('SELECT exam_name FROM exams WHERE exam_id = ?', [request.exam_id]);

            // Create a notification for the user
            await db.query(
                'INSERT INTO notifications (user_id, title, message, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
                [
                    request.user_id,
                    'Test Access Request Approved',
                    `Your request for additional attempts for the test "${examDetails[0].exam_name}" has been approved. You have been granted ${additionalAttempts} additional attempt(s).`
                ]
            );

            // Commit transaction
            await db.query('COMMIT');

            req.session.flashSuccess = 'Access request approved successfully';
            res.redirect('/admin/access-requests');
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error approving access request:', error);
        req.session.flashError = error.message || 'Error approving access request';
        res.redirect('/admin/access-requests');
    }
});

// Reject access request (admin only)
router.post('/reject', async (req, res) => {
    try {
        const { requestId, reason } = req.body;
        const adminId = req.session.userId;

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Get the request details
            const [requests] = await db.query(
                'SELECT * FROM access_requests WHERE id = ? AND status = "pending"',
                [requestId]
            );

            if (requests.length === 0) {
                throw new Error('Request not found or already processed');
            }

            const request = requests[0];

            // Update the request status
            await db.query(
                'UPDATE access_requests SET status = "rejected", processed_at = NOW(), processed_by = ? WHERE id = ?',
                [adminId, requestId]
            );

            // Get user and exam details for notification
            const [users] = await db.query('SELECT username, email FROM users WHERE id = ?', [request.user_id]);
            const [examDetails] = await db.query('SELECT exam_name FROM exams WHERE exam_id = ?', [request.exam_id]);

            // Create a notification for the user
            await db.query(
                'INSERT INTO notifications (user_id, title, message, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
                [
                    request.user_id,
                    'Test Access Request Rejected',
                    `Your request for additional attempts for the test "${examDetails[0].exam_name}" has been rejected. ${reason ? 'Reason: ' + reason : ''}`
                ]
            );

            // Commit transaction
            await db.query('COMMIT');

            req.session.flashSuccess = 'Access request rejected successfully';
            res.redirect('/admin/access-requests');
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error rejecting access request:', error);
        req.session.flashError = error.message || 'Error rejecting access request';
        res.redirect('/admin/access-requests');
    }
});

module.exports = router;
