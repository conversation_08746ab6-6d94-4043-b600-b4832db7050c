const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Access Requests index page
router.get('/', async (req, res, next) => {
    try {
        console.log('Access Requests route hit');
        const page = parseInt(req.query.page) || 1;
        const perPage = 10;
        const offset = (page - 1) * perPage;
        const status = req.query.status || null;

        // Get status counts for quick filters
        let statusStats = [{
            total: 0,
            pending: 0,
            approved: 0,
            rejected: 0
        }];

        try {
            const [stats] = await db.query(`
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
                FROM access_requests
            `);
            statusStats[0] = stats[0];
        } catch (dbError) {
            console.error('Error querying access_requests table:', dbError);
            // Continue with default values
        }

        // Build the query based on filters
        let query = `
            SELECT ar.*,
                   u.username, u.email,
                   e.exam_name,
                   admin.username as admin_username
            FROM access_requests ar
            JOIN users u ON ar.user_id = u.id
            JOIN exams e ON ar.exam_id = e.exam_id
            LEFT JOIN users admin ON ar.processed_by = admin.id
        `;

        const queryParams = [];

        // Add status filter if provided
        if (status) {
            query += ' WHERE ar.status = ?';
            queryParams.push(status);
        }

        // Get total count for pagination
        const countQuery = `
            SELECT COUNT(*) as total FROM access_requests ar
            ${status ? 'WHERE ar.status = ?' : ''}
        `;

        const [countResult] = await db.query(countQuery, status ? [status] : []);
        const totalRequests = countResult[0].total;
        const totalPages = Math.ceil(totalRequests / perPage);

        // Complete the query with sorting and pagination
        query += ' ORDER BY ar.requested_at DESC LIMIT ? OFFSET ?';
        queryParams.push(perPage, offset);

        // Execute the query
        const [requests] = await db.query(query, queryParams);

        // Check if there's an error in the session
        const lastError = req.session.lastError || null;

        // Clear the error from the session after retrieving it
        if (req.session.lastError) {
            delete req.session.lastError;
        }

        res.render('admin/access-requests', {
            title: 'Test Access Requests',
            pageTitle: 'Test Access Requests',
            requests,
            status,
            stats: statusStats[0],
            pagination: {
                page,
                totalPages,
                perPage,
                totalItems: totalRequests
            },
            currentPage: 'access-requests',
            formatDate,
            formatDateTime,
            lastError
        });
    } catch (error) {
        console.error('Error fetching access requests:', error);
        next(error);
    }
});

// Simplified and robust access request approval route
router.post('/approve', async (req, res) => {
    console.log('=== ACCESS REQUEST APPROVAL PROCESS STARTED ===');
    console.log('Request body:', req.body);

    // Use a connection with transaction to ensure atomicity
    let connection;

    try {
        // Get a connection from the pool
        connection = await db.getConnection();
        console.log('Database connection acquired');

        // Start transaction
        await connection.beginTransaction();
        console.log('Transaction started');

        // 1. Extract and validate basic parameters
        const requestId = parseInt(req.body.requestId);
        const additionalAttempts = Math.max(1, parseInt(req.body.additionalAttempts) || 1);
        const adminId = req.session.userId;

        console.log(`Processing request ID: ${requestId}, Additional attempts: ${additionalAttempts}, Admin ID: ${adminId}`);

        if (!requestId || isNaN(requestId)) {
            throw new Error('Invalid request ID');
        }

        // 2. Get the request details
        console.log('QUERY 1: Get access request details');
        const [requests] = await connection.query(
            'SELECT * FROM access_requests WHERE id = ?',
            [requestId]
        );

        if (requests.length === 0) {
            throw new Error('Access request not found');
        }

        const request = requests[0];
        console.log('Request details:', JSON.stringify(request));

        // 3. Check if request is already processed
        if (request.status !== 'pending') {
            throw new Error('This request has already been processed');
        }

        // 4. Update the request status first
        console.log('QUERY 2: Update access request status');
        const updateRequestResult = await connection.query(
            'UPDATE access_requests SET status = "approved", processed_at = NOW(), processed_by = ?, additional_attempts = ? WHERE id = ?',
            [adminId, additionalAttempts, requestId]
        );

        console.log('Request update result:', JSON.stringify(updateRequestResult));

        // Verify the update was successful
        if (updateRequestResult[0].affectedRows !== 1) {
            throw new Error(`Failed to update access request. Affected rows: ${updateRequestResult[0].affectedRows}`);
        }

        console.log(`Request ${requestId} marked as approved with ${additionalAttempts} additional attempts`);

        // 5. Check for existing assignment
        console.log('QUERY 3: Check for existing assignment');
        const [existingAssignments] = await connection.query(
            'SELECT assignment_id, max_attempts FROM test_assignments WHERE exam_id = ? AND user_id = ? AND is_active = 1',
            [request.exam_id, request.user_id]
        );

        console.log('Existing assignments:', JSON.stringify(existingAssignments));

        // 6. Update or create assignment
        if (existingAssignments.length > 0) {
            // Update existing assignment
            const assignmentId = existingAssignments[0].assignment_id;
            const currentMax = parseInt(existingAssignments[0].max_attempts) || 0;
            const newMaxAttempts = currentMax + additionalAttempts;

            console.log(`QUERY 4A: Update assignment - Current max: ${currentMax}, New max: ${newMaxAttempts}`);

            const updateResult = await connection.query(
                'UPDATE test_assignments SET max_attempts = ?, assigned_by = ?, assigned_at = NOW() WHERE assignment_id = ?',
                [newMaxAttempts, adminId, assignmentId]
            );

            console.log('Assignment update result:', JSON.stringify(updateResult));

            if (updateResult[0].affectedRows !== 1) {
                throw new Error(`Failed to update assignment. Affected rows: ${updateResult[0].affectedRows}`);
            }

            console.log(`Assignment ${assignmentId} updated with new max_attempts: ${newMaxAttempts}`);
        } else {
            // Create new assignment
            console.log(`QUERY 4B: Create new assignment with ${additionalAttempts} attempts`);

            const insertResult = await connection.query(
                'INSERT INTO test_assignments (exam_id, user_id, max_attempts, assigned_by, assigned_at, is_active) VALUES (?, ?, ?, ?, NOW(), 1)',
                [request.exam_id, request.user_id, additionalAttempts, adminId]
            );

            console.log('Assignment insert result:', JSON.stringify(insertResult));

            if (insertResult[0].affectedRows !== 1) {
                throw new Error(`Failed to create assignment. Affected rows: ${insertResult[0].affectedRows}`);
            }

            console.log(`New assignment created with ID ${insertResult[0].insertId} and ${additionalAttempts} attempts`);
        }

        // 7. Verify the assignment was updated correctly
        console.log('QUERY 5: Verify assignment after update');
        const [verifiedAssignments] = await connection.query(
            'SELECT * FROM test_assignments WHERE exam_id = ? AND user_id = ? AND is_active = 1',
            [request.exam_id, request.user_id]
        );

        console.log('Verified assignments:', JSON.stringify(verifiedAssignments));

        if (verifiedAssignments.length === 0) {
            throw new Error('No assignment found after update!');
        }

        // 8. Create notification
        try {
            console.log('QUERY 6: Get exam name');
            const [exams] = await connection.query(
                'SELECT exam_name FROM exams WHERE exam_id = ?',
                [request.exam_id]
            );

            const examName = exams.length > 0 ? exams[0].exam_name : 'the requested exam';

            console.log('QUERY 7: Create notification');
            await connection.query(
                'INSERT INTO notifications (user_id, title, message, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
                [
                    request.user_id,
                    'Test Access Request Approved',
                    `Your request for additional attempts for "${examName}" has been approved. You have been granted ${additionalAttempts} additional attempt(s).`
                ]
            );

            console.log('Notification created successfully');
        } catch (notificationError) {
            console.error('Error creating notification:', notificationError);
            // Continue with the process even if notification creation fails
        }

        // 9. Commit the transaction
        console.log('Committing transaction...');
        await connection.commit();
        console.log('Transaction committed successfully');

        console.log('=== ACCESS REQUEST APPROVAL PROCESS COMPLETED SUCCESSFULLY ===');

        // 10. Set success message and redirect
        req.session.flashSuccess = `Access request approved successfully. User granted ${additionalAttempts} additional attempt(s).`;
        return res.redirect('/admin/access-requests');

    } catch (error) {
        console.error('Error in access request approval:', error);
        console.error('Error stack:', error.stack);
        console.log('=== ACCESS REQUEST APPROVAL PROCESS FAILED ===');

        // Rollback the transaction if it was started
        if (connection) {
            try {
                await connection.rollback();
                console.log('Transaction rolled back due to error');
            } catch (rollbackError) {
                console.error('Error rolling back transaction:', rollbackError);
            }
        }

        // Create a detailed error object for debugging
        const errorDetails = {
            message: error.message,
            stack: error.stack,
            time: new Date().toISOString(),
            requestBody: req.body
        };

        // Store error details in session for display
        req.session.lastError = JSON.stringify(errorDetails, null, 2);

        // Set error message and redirect
        req.session.flashError = `Error approving access request: ${error.message}. See details for more information.`;
        return res.redirect('/admin/access-requests?showError=true');
    } finally {
        // Release the connection if it was acquired
        if (connection) {
            try {
                connection.release();
                console.log('Database connection released');
            } catch (releaseError) {
                console.error('Error releasing connection:', releaseError);
            }
        }
    }
});

// Reject access request
router.post('/reject', async (req, res) => {
    try {
        const { requestId, reason } = req.body;
        const adminId = req.session.userId;

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Get the request details
            const [requests] = await db.query(
                'SELECT * FROM access_requests WHERE id = ? AND status = "pending"',
                [requestId]
            );

            if (requests.length === 0) {
                throw new Error('Request not found or already processed');
            }

            const request = requests[0];

            // Update the request status
            await db.query(
                'UPDATE access_requests SET status = "rejected", processed_at = NOW(), processed_by = ? WHERE id = ?',
                [adminId, requestId]
            );

            // Get user and exam details for notification
            const [users] = await db.query('SELECT username, email FROM users WHERE id = ?', [request.user_id]);
            const [examDetails] = await db.query('SELECT exam_name FROM exams WHERE exam_id = ?', [request.exam_id]);

            // Create a notification for the user
            await db.query(
                'INSERT INTO notifications (user_id, title, message, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
                [
                    request.user_id,
                    'Test Access Request Rejected',
                    `Your request for additional attempts for the test "${examDetails[0].exam_name}" has been rejected. ${reason ? 'Reason: ' + reason : ''}`
                ]
            );

            // Commit transaction
            await db.query('COMMIT');

            req.session.flashSuccess = 'Access request rejected successfully';
            res.redirect('/admin/access-requests');
        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error rejecting access request:', error);
        req.session.flashError = error.message || 'Error rejecting access request';
        res.redirect('/admin/access-requests');
    }
});

module.exports = router;
