const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const formatDataSize = require('../utils/format-data-size');

// Apply authentication and admin middleware to all routes
router.use(checkAuthenticated);
router.use(checkAdmin);

// Error logs dashboard
router.get('/', async (req, res) => {
    try {
        console.log('=== ADMIN ERROR LOGS PAGE REQUESTED ===');

        // Get query parameters for filtering
        const logType = req.query.type || 'all';
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 50;
        const search = req.query.search || '';
        const startDate = req.query.startDate || '';
        const endDate = req.query.endDate || '';

        console.log('Fetching error logs with filters:', { logType, page, perPage, search, startDate, endDate });

        // Initialize variables
        let logs = [];
        let totalLogs = 0;
        let totalPages = 1;
        let logTypes = [];
        let dbErrors = [];
        let queryErrors = [];

        try {
            // Check if logs table exists
            console.log('Checking if logs table exists...');
            const [tableExists] = await db.query(`
                SELECT 1 FROM information_schema.tables
                WHERE table_schema = 'exam_prep_platform' AND table_name = 'logs'
            `);

            console.log('Logs table exists:', tableExists.length > 0);

            if (tableExists.length > 0) {
                // Get the schema of the logs table
                console.log('Getting logs table schema...');
                const [columns] = await db.query(`
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'exam_prep_platform'
                    AND TABLE_NAME = 'logs'
                    ORDER BY ORDINAL_POSITION
                `);

                const columnNames = columns.map(col => col.COLUMN_NAME);
                console.log('Logs table columns:', columnNames);

                // Check if required columns exist
                const hasLevelColumn = columnNames.includes('level');
                const hasErrorMessageColumn = columnNames.includes('error_message');
                const hasCategoryColumn = columnNames.includes('category');

                console.log('Has required columns:', { hasLevelColumn, hasErrorMessageColumn, hasCategoryColumn });

                // Base query with dynamic column checks
                let query = `
                    SELECT l.*, u.username as user_username
                    FROM logs l
                    LEFT JOIN users u ON l.user_id = u.id
                    WHERE 1=1
                `;

                // Add conditions based on available columns
                if (hasLevelColumn) {
                    query += ` AND (l.level = 'error'`;
                    if (hasErrorMessageColumn) {
                        query += ` OR l.error_message IS NOT NULL`;
                    }
                    query += `)`;
                } else if (hasErrorMessageColumn) {
                    query += ` AND l.error_message IS NOT NULL`;
                }

        const queryParams = [];

                // Add filters
                if (logType !== 'all' && hasCategoryColumn) {
                    query += ` AND l.category = ?`;
                    queryParams.push(logType);
                }

                if (search) {
                    query += ` AND (`;
                    const searchConditions = [];

                    if (columnNames.includes('details')) {
                        searchConditions.push(`l.details LIKE ?`);
                        queryParams.push(`%${search}%`);
                    }

                    if (hasErrorMessageColumn) {
                        searchConditions.push(`l.error_message LIKE ?`);
                        queryParams.push(`%${search}%`);
                    }

                    if (columnNames.includes('operation')) {
                        searchConditions.push(`l.operation LIKE ?`);
                        queryParams.push(`%${search}%`);
                    }

                    searchConditions.push(`u.username LIKE ?`);
                    queryParams.push(`%${search}%`);

                    query += searchConditions.join(' OR ');
                    query += `)`;
                }

                if (startDate) {
                    query += ` AND l.timestamp >= ?`;
                    queryParams.push(startDate);
                }

                if (endDate) {
                    query += ` AND l.timestamp <= ?`;
                    queryParams.push(endDate + ' 23:59:59');
                }

                // Add order and pagination
                query += ` ORDER BY l.timestamp DESC LIMIT ? OFFSET ?`;
                queryParams.push(perPage, (page - 1) * perPage);

                console.log('Final query:', query);
                console.log('Query params:', queryParams);

                // Execute query
                console.log('Executing logs query...');
                const [logsResult] = await db.query(query, queryParams);
                logs = logsResult;
                console.log(`Found ${logs.length} logs`);

                // Get total count for pagination with the same conditions
                console.log('Getting total count...');
                let countQuery = `
                    SELECT COUNT(*) as total
                    FROM logs l
                    LEFT JOIN users u ON l.user_id = u.id
                    WHERE 1=1
                `;

                // Add conditions based on available columns
                if (hasLevelColumn) {
                    countQuery += ` AND (l.level = 'error'`;
                    if (hasErrorMessageColumn) {
                        countQuery += ` OR l.error_message IS NOT NULL`;
                    }
                    countQuery += `)`;
                } else if (hasErrorMessageColumn) {
                    countQuery += ` AND l.error_message IS NOT NULL`;
                }

                const countParams = [];

                // Add the same filters to count query
                if (logType !== 'all' && hasCategoryColumn) {
                    countQuery += ` AND l.category = ?`;
                    countParams.push(logType);
                }

                if (search) {
                    countQuery += ` AND (`;
                    const searchConditions = [];

                    if (columnNames.includes('details')) {
                        searchConditions.push(`l.details LIKE ?`);
                        countParams.push(`%${search}%`);
                    }

                    if (hasErrorMessageColumn) {
                        searchConditions.push(`l.error_message LIKE ?`);
                        countParams.push(`%${search}%`);
                    }

                    if (columnNames.includes('operation')) {
                        searchConditions.push(`l.operation LIKE ?`);
                        countParams.push(`%${search}%`);
                    }

                    searchConditions.push(`u.username LIKE ?`);
                    countParams.push(`%${search}%`);

                    countQuery += searchConditions.join(' OR ');
                    countQuery += `)`;
                }

                if (startDate) {
                    countQuery += ` AND l.timestamp >= ?`;
                    countParams.push(startDate);
                }

                if (endDate) {
                    countQuery += ` AND l.timestamp <= ?`;
                    countParams.push(endDate + ' 23:59:59');
                }

                console.log('Count query:', countQuery);
                console.log('Count params:', countParams);

                const [countResult] = await db.query(countQuery, countParams);
                totalLogs = countResult[0].total;
                totalPages = Math.ceil(totalLogs / perPage);
                console.log('Total logs:', totalLogs, 'Total pages:', totalPages);

                // Get log types for filter dropdown if category column exists
                if (hasCategoryColumn) {
                    console.log('Getting log types...');
                    let logTypesQuery = `
                        SELECT DISTINCT category
                        FROM logs
                        WHERE 1=1
                    `;

                    if (hasLevelColumn) {
                        logTypesQuery += ` AND (level = 'error'`;
                        if (hasErrorMessageColumn) {
                            logTypesQuery += ` OR error_message IS NOT NULL`;
                        }
                        logTypesQuery += `)`;
                    } else if (hasErrorMessageColumn) {
                        logTypesQuery += ` AND error_message IS NOT NULL`;
                    }

                    logTypesQuery += ` ORDER BY category`;

                    const [logTypesResult] = await db.query(logTypesQuery);
                    logTypes = logTypesResult;
                    console.log(`Found ${logTypes.length} log types`);
                }
            } else {
                console.log('Logs table does not exist');
            }
        } catch (dbError) {
            console.error('Error querying logs table:', dbError);
            // Continue with empty logs array
        }

        // Get database error logs from jsvalues table
        const [jsvaluesExists] = await db.query(`
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'exam_prep_platform' AND table_name = 'jsvalues'
        `);

        if (jsvaluesExists.length > 0) {
            const [dbErrorsResult] = await db.query(`
                SELECT * FROM jsvalues
                WHERE context LIKE '%error%' OR context LIKE '%database%' OR variable_name LIKE '%error%'
                ORDER BY timestamp DESC
                LIMIT 100
            `);
            dbErrors = dbErrorsResult;
        } else {
            console.log('jsvalues table does not exist');
        }

        // Get query error logs
        const [queryErrorsExists] = await db.query(`
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'exam_prep_platform' AND table_name = 'query_error_logs'
        `);

        if (queryErrorsExists.length > 0) {
            const [queryErrorsResult] = await db.query(`
                SELECT * FROM query_error_logs
                ORDER BY timestamp DESC
                LIMIT 100
            `);
            queryErrors = queryErrorsResult;
        } else {
            console.log('query_error_logs table does not exist');
        }

        // Render the error logs page
        res.render('admin/error-logs', {
            title: 'Error Logs',
            pageTitle: 'Error Logs & Database Issues',
            currentPage: 'error-logs',
            logs,
            dbErrors,
            queryErrors,
            logTypes,
            filters: {
                logType,
                search,
                startDate,
                endDate
            },
            pagination: {
                page,
                totalPages: totalPages || 1,
                perPage,
                totalItems: totalLogs || 0
            },
            formatDate,
            formatDateTime,
            formatDataSize
        });
    } catch (error) {
        console.error('Error in error logs route:', error);
        console.error('Stack trace:', error.stack);
        req.session.flashError = `Failed to fetch error logs: ${error.message}`;
        return res.redirect('/admin/dashboard');
    }
});

// View specific log details
router.get('/view/:id', async (req, res) => {
    try {
        console.log('=== ADMIN ERROR LOG DETAILS REQUESTED ===');
        const logId = req.params.id;
        console.log('Log ID:', logId);

        // Get the schema of the logs table to determine the primary key column
        console.log('Getting logs table schema...');
        const [columns] = await db.query(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'exam_prep_platform'
            AND TABLE_NAME = 'logs'
            ORDER BY ORDINAL_POSITION
        `);

        const columnNames = columns.map(col => col.COLUMN_NAME);
        console.log('Logs table columns:', columnNames);

        // Determine the primary key column (log_id or id)
        const idColumn = columnNames.includes('log_id') ? 'log_id' : 'id';
        console.log('Using ID column:', idColumn);

        // Get log details
        console.log('Fetching log details...');
        const [logs] = await db.query(`
            SELECT l.*, u.username as user_username
            FROM logs l
            LEFT JOIN users u ON l.user_id = u.id
            WHERE l.${idColumn} = ?
        `, [logId]);

        console.log('Logs found:', logs.length);

        if (logs.length === 0) {
            req.session.flashError = 'Log not found';
            return res.redirect('/admin/error-logs');
        }

        const log = logs[0];
        console.log('Log data:', JSON.stringify(log));

        // Parse details if it's JSON
        try {
            if (log.details && typeof log.details === 'string' &&
                (log.details.startsWith('{') || log.details.startsWith('['))) {
                log.parsedDetails = JSON.parse(log.details);
                console.log('Successfully parsed details as JSON');
            }
        } catch (e) {
            console.error('Error parsing log details:', e);
            log.parsedDetails = null;
        }

        // Render the log details page
        res.render('admin/error-log-details', {
            title: 'Error Log Details',
            pageTitle: 'Error Log Details',
            currentPage: 'error-logs',
            log,
            formatDate,
            formatDateTime,
            formatDataSize
        });
    } catch (error) {
        console.error('Error fetching log details:', error);
        console.error('Stack trace:', error.stack);
        req.session.flashError = `Failed to fetch log details: ${error.message}`;
        return res.redirect('/admin/error-logs');
    }
});

// Database health check
router.get('/db-health', async (req, res) => {
    try {
        // Initialize variables with default values
        let dbStatus = [];
        let tableStatus = [];
        let processList = [];
        let engineStatus = [{ Status: 'Information not available' }];

        try {
            // Get database status
            const [dbStatusResult] = await db.query('SHOW STATUS');
            dbStatus = dbStatusResult;
        } catch (error) {
            console.error('Error getting database status:', error);
        }

        try {
            // Get table status
            const [tableStatusResult] = await db.query('SHOW TABLE STATUS');
            tableStatus = tableStatusResult;
        } catch (error) {
            console.error('Error getting table status:', error);
        }

        try {
            // Get process list
            const [processListResult] = await db.query('SHOW PROCESSLIST');
            processList = processListResult;
        } catch (error) {
            console.error('Error getting process list:', error);
        }

        try {
            // Get engine status
            const [engineStatusResult] = await db.query('SHOW ENGINE INNODB STATUS');
            engineStatus = engineStatusResult;
        } catch (error) {
            console.error('Error getting engine status:', error);
        }

        // Render the database health page
        res.render('admin/db-health', {
            title: 'Database Health',
            pageTitle: 'Database Health Check',
            currentPage: 'error-logs',
            dbStatus,
            tableStatus,
            processList,
            engineStatus: engineStatus[0],
            formatDate,
            formatDateTime,
            formatDataSize
        });
    } catch (error) {
        console.error('Error checking database health:', error);
        req.session.flashError = 'Failed to check database health';
        res.redirect('/admin/error-logs');
    }
});

module.exports = router;
