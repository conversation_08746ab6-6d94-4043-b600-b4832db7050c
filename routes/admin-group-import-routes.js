/**
 * Admin Group Import Routes
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const groupImportController = require('../controllers/group-import-controller');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (req, file, cb) => {
        // Accept only CSV and Excel files
        const filetypes = /csv|xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only CSV and Excel files are allowed'));
    }
});

// Group import page
router.get('/import', checkAdmin, (req, res) => {
    res.render('admin/groups/import', {
        title: 'Import Groups',
        pageTitle: 'Import Groups',
        layout: 'admin',
        currentPage: 'groups'
    });
});

// Download CSV template
router.get('/import/template/csv', checkAdmin, (req, res) => {
    const templatePath = path.join(__dirname, '../public/templates/group_import_template.csv');
    res.download(templatePath, 'group_import_template.csv');
});

// Process group import
router.post('/import', checkAdmin, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            req.session.flashError = 'No file uploaded';
            return res.redirect('/admin/groups/import');
        }
        
        // Determine file type
        const fileType = path.extname(req.file.originalname).toLowerCase() === '.csv' ? 'csv' : 'excel';
        
        // Import groups
        const results = await groupImportController.importGroups(
            req.file.buffer,
            fileType,
            req.session.userId
        );
        
        // Set flash messages
        if (results.imported > 0) {
            req.session.flashSuccess = `Successfully imported ${results.imported} groups`;
        }
        
        if (results.skipped > 0) {
            req.session.flashInfo = `Skipped ${results.skipped} groups`;
        }
        
        if (results.errors.length > 0) {
            req.session.importErrors = results.errors;
        }
        
        res.redirect('/admin/groups/import/results');
    } catch (error) {
        console.error('Error importing groups:', error);
        req.session.flashError = `Error importing groups: ${error.message}`;
        res.redirect('/admin/groups/import');
    }
});

// Display import results
router.get('/import/results', checkAdmin, (req, res) => {
    const errors = req.session.importErrors || [];
    delete req.session.importErrors;
    
    res.render('admin/groups/import-results', {
        title: 'Import Results',
        pageTitle: 'Group Import Results',
        errors,
        layout: 'admin',
        currentPage: 'groups'
    });
});

module.exports = router;
