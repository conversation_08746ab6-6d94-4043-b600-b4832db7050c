const express = require('express');
const router = express.Router();
const issueTrackerController = require('../controllers/issue-tracker-controller');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');

// Apply authentication and admin middleware to all routes
router.use(checkAuthenticated);
router.use(checkAdmin);

// Middleware to set layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.navbar = 'admin';
    res.locals.currentPage = 'admin-issues';
    next();
});

// Admin issue management dashboard
router.get('/dashboard', issueTrackerController.adminDashboard);

module.exports = router;
