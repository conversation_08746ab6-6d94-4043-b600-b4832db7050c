const express = require('express');
const router = express.Router();
const adminNotificationController = require('../controllers/admin-notification-controller');
const { checkAdmin } = require('../middleware/auth');

// Apply admin middleware to all routes
router.use(checkAdmin);

// Admin notification routes
router.get('/', adminNotificationController.index);
router.get('/create', adminNotificationController.createForm);
router.post('/create', adminNotificationController.create);
router.get('/:id/edit', adminNotificationController.editForm);
router.post('/:id/edit', adminNotificationController.update);
router.post('/:id/delete', adminNotificationController.delete);
router.post('/:id/toggle', adminNotificationController.toggleActive);

module.exports = router;
