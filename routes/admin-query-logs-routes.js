const express = require('express');
const router = express.Router();
const db = require('../config/database');
const fs = require('fs').promises;
const path = require('path');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const formatDataSize = require('../utils/format-data-size');

// Apply authentication and admin middleware to all routes
router.use(checkAuthenticated);
router.use(checkAdmin);

// Query logs dashboard
router.get('/', async (req, res) => {
    try {
        console.log('=== ADMIN QUERY LOGS PAGE REQUESTED ===');

        // Get query parameters for filtering
        const queryType = req.query.type || 'all';
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 50;
        const search = req.query.search || '';
        const startDate = req.query.startDate || '';
        const endDate = req.query.endDate || '';
        const minDuration = parseInt(req.query.minDuration) || 0;

        console.log('Fetching query logs with filters:', { queryType, page, perPage, search, startDate, endDate, minDuration });

        // Initialize variables
        let logs = [];
        let totalLogs = 0;
        let totalPages = 1;
        let queryTypes = [];
        let slowQueries = [];
        let recentQueries = [];

        // Check if query_logs table exists
        const [queryLogsTableExists] = await db.query(`
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = ? AND table_name = 'query_logs'
        `, [process.env.DB_NAME]);

        if (queryLogsTableExists.length === 0) {
            // Create query_logs table if it doesn't exist
            await db.query(`
                CREATE TABLE IF NOT EXISTS query_logs (
                    log_id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_id INT NULL,
                    query TEXT NOT NULL,
                    params TEXT NULL,
                    duration INT NOT NULL,
                    result TEXT NULL,
                    status VARCHAR(20) DEFAULT 'success',
                    error_message TEXT NULL,
                    table_name VARCHAR(100) NULL,
                    query_type VARCHAR(20) NULL,
                    affected_rows INT NULL,
                    ip_address VARCHAR(45) NULL,
                    route VARCHAR(255) NULL,
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_user_id (user_id),
                    INDEX idx_status (status),
                    INDEX idx_query_type (query_type),
                    INDEX idx_duration (duration)
                )
            `);
            console.log('Created query_logs table');
        }

        // Try to get logs from the database
        try {
            // Get query types for filter dropdown
            const [queryTypesResult] = await db.query(`
                SELECT DISTINCT query_type, COUNT(*) as count
                FROM query_logs
                GROUP BY query_type
                ORDER BY count DESC
            `);
            queryTypes = queryTypesResult;

            // Build query with filters
            let query = `
                SELECT ql.*, u.username as user_username
                FROM query_logs ql
                LEFT JOIN users u ON ql.user_id = u.id
                WHERE 1=1
            `;
            let queryParams = [];

            // Apply filters
            if (queryType !== 'all') {
                query += ` AND ql.query_type = ?`;
                queryParams.push(queryType);
            }

            if (search) {
                query += ` AND (ql.query LIKE ? OR ql.table_name LIKE ? OR ql.error_message LIKE ?)`;
                const searchParam = `%${search}%`;
                queryParams.push(searchParam, searchParam, searchParam);
            }

            if (startDate) {
                query += ` AND ql.timestamp >= ?`;
                queryParams.push(`${startDate} 00:00:00`);
            }

            if (endDate) {
                query += ` AND ql.timestamp <= ?`;
                queryParams.push(`${endDate} 23:59:59`);
            }

            if (minDuration > 0) {
                query += ` AND ql.duration >= ?`;
                queryParams.push(minDuration);
            }

            // Add order and pagination
            query += ` ORDER BY ql.timestamp DESC LIMIT ? OFFSET ?`;
            queryParams.push(perPage, (page - 1) * perPage);

            console.log('Final query:', query);
            console.log('Query params:', queryParams);

            // Execute query
            console.log('Executing logs query...');
            const [logsResult] = await db.query(query, queryParams);
            logs = logsResult;
            console.log(`Found ${logs.length} logs`);

            // Get total count for pagination with the same conditions
            console.log('Getting total count...');
            let countQuery = `
                SELECT COUNT(*) as total
                FROM query_logs ql
                LEFT JOIN users u ON ql.user_id = u.id
                WHERE 1=1
            `;

            // Apply the same filters to count query
            if (queryType !== 'all') {
                countQuery += ` AND ql.query_type = ?`;
            }

            if (search) {
                countQuery += ` AND (ql.query LIKE ? OR ql.table_name LIKE ? OR ql.error_message LIKE ?)`;
            }

            if (startDate) {
                countQuery += ` AND ql.timestamp >= ?`;
            }

            if (endDate) {
                countQuery += ` AND ql.timestamp <= ?`;
            }

            if (minDuration > 0) {
                countQuery += ` AND ql.duration >= ?`;
            }

            // Remove pagination params
            const countParams = queryParams.slice(0, -2);

            const [countResult] = await db.query(countQuery, countParams);
            totalLogs = countResult[0].total;
            totalPages = Math.ceil(totalLogs / perPage);
            console.log(`Total logs: ${totalLogs}, Total pages: ${totalPages}`);

            // Get slow queries (duration > 1000ms)
            const [slowQueriesResult] = await db.query(`
                SELECT ql.*, u.username as user_username
                FROM query_logs ql
                LEFT JOIN users u ON ql.user_id = u.id
                WHERE ql.duration > 1000
                ORDER BY ql.duration DESC
                LIMIT 10
            `);
            slowQueries = slowQueriesResult;

            // Get recent queries
            const [recentQueriesResult] = await db.query(`
                SELECT ql.*, u.username as user_username
                FROM query_logs ql
                LEFT JOIN users u ON ql.user_id = u.id
                ORDER BY ql.timestamp DESC
                LIMIT 10
            `);
            recentQueries = recentQueriesResult;
        } catch (dbError) {
            console.error('Error querying query_logs table:', dbError);
            // Continue with empty logs array
        }

        // Try to read query log files if database query failed or returned no results
        if (logs.length === 0) {
            try {
                // Read from query.log file
                const logFilePath = path.join(__dirname, '../logs/query.log');
                const fileExists = await fs.access(logFilePath).then(() => true).catch(() => false);
                
                if (fileExists) {
                    const fileContent = await fs.readFile(logFilePath, 'utf8');
                    const logEntries = fileContent.split('\n\n').filter(entry => entry.trim());
                    
                    // Parse log entries
                    const parsedLogs = logEntries.map((entry, index) => {
                        const lines = entry.split('\n');
                        const timestampMatch = lines[0].match(/\[(.*?)\]/);
                        const durationMatch = lines[0].match(/\((.*?)ms\)/);
                        const userMatch = lines[0].match(/User: (.*?)$/);
                        
                        const timestamp = timestampMatch ? timestampMatch[1] : '';
                        const duration = durationMatch ? parseInt(durationMatch[1]) : 0;
                        const userId = userMatch ? userMatch[1] : 'system';
                        
                        const queryLine = lines[1] || '';
                        const paramsLine = lines[2] || '';
                        
                        const query = queryLine.replace('Query: ', '');
                        const params = paramsLine.replace('Params: ', '');
                        
                        // Determine query type
                        const queryType = query.trim().split(' ')[0].toUpperCase();
                        
                        return {
                            log_id: index + 1,
                            timestamp: new Date(timestamp),
                            user_id: userId !== 'system' ? userId : null,
                            query,
                            params,
                            duration,
                            query_type: queryType,
                            status: 'success',
                            source: 'file'
                        };
                    });
                    
                    // Apply filters
                    let filteredLogs = parsedLogs;
                    
                    if (queryType !== 'all') {
                        filteredLogs = filteredLogs.filter(log => log.query_type === queryType);
                    }
                    
                    if (search) {
                        const searchLower = search.toLowerCase();
                        filteredLogs = filteredLogs.filter(log => 
                            log.query.toLowerCase().includes(searchLower) || 
                            log.params.toLowerCase().includes(searchLower)
                        );
                    }
                    
                    if (startDate) {
                        const startDateTime = new Date(`${startDate}T00:00:00`);
                        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDateTime);
                    }
                    
                    if (endDate) {
                        const endDateTime = new Date(`${endDate}T23:59:59`);
                        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDateTime);
                    }
                    
                    if (minDuration > 0) {
                        filteredLogs = filteredLogs.filter(log => log.duration >= minDuration);
                    }
                    
                    // Sort by timestamp (newest first)
                    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    
                    // Get total count
                    totalLogs = filteredLogs.length;
                    totalPages = Math.ceil(totalLogs / perPage);
                    
                    // Apply pagination
                    logs = filteredLogs.slice((page - 1) * perPage, page * perPage);
                    
                    // Get slow queries
                    slowQueries = parsedLogs
                        .filter(log => log.duration > 1000)
                        .sort((a, b) => b.duration - a.duration)
                        .slice(0, 10);
                    
                    // Get recent queries
                    recentQueries = parsedLogs
                        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                        .slice(0, 10);
                    
                    // Get query types
                    const queryTypesMap = {};
                    parsedLogs.forEach(log => {
                        if (!queryTypesMap[log.query_type]) {
                            queryTypesMap[log.query_type] = 0;
                        }
                        queryTypesMap[log.query_type]++;
                    });
                    
                    queryTypes = Object.entries(queryTypesMap).map(([query_type, count]) => ({
                        query_type,
                        count
                    })).sort((a, b) => b.count - a.count);
                }
            } catch (fileError) {
                console.error('Error reading query log file:', fileError);
            }
        }

        // Try to read slow query log file
        if (slowQueries.length === 0) {
            try {
                const slowLogFilePath = path.join(__dirname, '../logs/slow_query.log');
                const fileExists = await fs.access(slowLogFilePath).then(() => true).catch(() => false);
                
                if (fileExists) {
                    const fileContent = await fs.readFile(slowLogFilePath, 'utf8');
                    const logEntries = fileContent.split('\n\n').filter(entry => entry.trim());
                    
                    // Parse log entries
                    const parsedSlowLogs = logEntries.map((entry, index) => {
                        const lines = entry.split('\n');
                        const timestampMatch = lines[0].match(/\[(.*?)\]/);
                        const durationMatch = lines[0].match(/\((.*?)ms\)/);
                        
                        const timestamp = timestampMatch ? timestampMatch[1] : '';
                        const duration = durationMatch ? parseInt(durationMatch[1]) : 0;
                        
                        const queryLine = lines[1] || '';
                        const paramsLine = lines[2] || '';
                        
                        const query = queryLine.replace('Query: ', '');
                        const params = paramsLine.replace('Params: ', '');
                        
                        // Determine query type
                        const queryType = query.trim().split(' ')[0].toUpperCase();
                        
                        return {
                            log_id: `slow-${index + 1}`,
                            timestamp: new Date(timestamp),
                            query,
                            params,
                            duration,
                            query_type: queryType,
                            status: 'slow',
                            source: 'file'
                        };
                    });
                    
                    // Sort by duration (slowest first)
                    parsedSlowLogs.sort((a, b) => b.duration - a.duration);
                    
                    // Add to slow queries
                    slowQueries = [...slowQueries, ...parsedSlowLogs.slice(0, 10 - slowQueries.length)];
                }
            } catch (fileError) {
                console.error('Error reading slow query log file:', fileError);
            }
        }

        // Render the query logs page
        res.render('admin/query-logs', {
            title: 'Database Query Logs',
            pageTitle: 'Database Query Logs',
            currentPage: 'query-logs',
            logs,
            slowQueries,
            recentQueries,
            queryTypes,
            filters: {
                queryType,
                search,
                startDate,
                endDate,
                minDuration
            },
            pagination: {
                page,
                totalPages: totalPages || 1,
                perPage,
                totalItems: totalLogs || 0
            },
            formatDate,
            formatDateTime,
            formatDataSize
        });
    } catch (error) {
        console.error('Error in query logs route:', error);
        console.error('Stack trace:', error.stack);
        req.session.flashError = `Failed to fetch query logs: ${error.message}`;
        return res.redirect('/admin/dashboard');
    }
});

// View specific log details
router.get('/view/:id', async (req, res) => {
    try {
        console.log('=== ADMIN QUERY LOG DETAILS REQUESTED ===');
        const logId = req.params.id;
        console.log('Log ID:', logId);

        // Get the log details
        const [logs] = await db.query(`
            SELECT ql.*, u.username as user_username
            FROM query_logs ql
            LEFT JOIN users u ON ql.user_id = u.id
            WHERE ql.log_id = ?
        `, [logId]);

        if (logs.length === 0) {
            req.session.flashError = 'Query log not found';
            return res.redirect('/admin/query-logs');
        }

        const log = logs[0];

        // Parse params if it's a JSON string
        try {
            if (log.params && typeof log.params === 'string') {
                log.params = JSON.parse(log.params);
            }
        } catch (e) {
            console.error('Error parsing params JSON:', e);
        }

        // Parse result if it's a JSON string
        try {
            if (log.result && typeof log.result === 'string') {
                log.result = JSON.parse(log.result);
            }
        } catch (e) {
            console.error('Error parsing result JSON:', e);
        }

        res.render('admin/query-log-details', {
            title: 'Query Log Details',
            pageTitle: 'Query Log Details',
            currentPage: 'query-logs',
            log,
            formatDateTime
        });
    } catch (error) {
        console.error('Error in query log details route:', error);
        req.session.flashError = `Failed to fetch query log details: ${error.message}`;
        return res.redirect('/admin/query-logs');
    }
});

module.exports = router;
