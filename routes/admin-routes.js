const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const groupController = require('../controllers/group-controller');
const settingsController = require('../controllers/settings-controller');
const classController = require('../controllers/class-controller');
const adminInstructionPlanController = require('../controllers/admin-instruction-plan-controller');
const userRoleController = require('../controllers/user-role-controller');
const studentAssignmentController = require('../controllers/student-assignment-controller');
const adminSchemaController = require('../controllers/admin-schema-controller');
const facultyController = require('../controllers/faculty-controller');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Define isAdmin middleware for individual routes
const isAdmin = (req, res, next) => {
    if (req.session.userRole === 'admin') {
        return next();
    }
    res.status(403).render('error', {
        title: 'Access Denied',
        message: 'You do not have permission to access this page',
        layout: 'admin'
    });
};

// Middleware to set admin layout and current page
router.use(async (req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.currentPage = req.path.split('/')[1] || 'dashboard';
    res.locals.user = req.session.user; // Include the user object

    // Get pending access requests count
    try {
        const [pendingRequests] = await db.query(
            'SELECT COUNT(*) as count FROM access_requests WHERE status = "pending"'
        );
        res.locals.pendingAccessRequests = pendingRequests[0].count;
    } catch (error) {
        console.error('Error getting pending access requests count:', error);
        res.locals.pendingAccessRequests = 0;
    }

    // Get pending user approvals count
    try {
        const [pendingApprovals] = await db.query(
            'SELECT COUNT(*) as count FROM users WHERE is_approved = 0 AND role != "admin"'
        );
        res.locals.pendingApprovals = pendingApprovals[0].count;
    } catch (error) {
        console.error('Error getting pending user approvals count:', error);
        res.locals.pendingApprovals = 0;
    }

    // Helper function to format date and time
    res.locals.formatDateTime = function(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleString();
        } catch (error) {
            return 'Invalid date';
        }
    };

    next();
});

// Configure multer for file upload
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'public/uploads/profiles/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'profile-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only image files are allowed!'));
    }
});

// Active Users Page
router.get('/users/active', async (req, res, next) => {
    try {
        // Get all active sessions with user details
        const [activeUsers] = await db.query(`
            SELECT s.*, u.username, u.email, u.role, u.profile_image
            FROM active_sessions s
            JOIN users u ON s.user_id = u.id
            WHERE s.is_active = 1
            ORDER BY s.last_activity DESC
        `);

        // Get current user information
        const [currentUserInfo] = await db.query(
            'SELECT id, username, email, role FROM users WHERE id = ?',
            [req.session.userId]
        );

        const currentUser = currentUserInfo.length > 0 ? currentUserInfo[0] : null;

        res.render('admin/active-users', {
            title: 'Active Users',
            pageTitle: 'Active Users',
            currentPage: 'users',
            activeUsers,
            currentUser
        });
    } catch (error) {
        console.error('Error loading active users:', error);
        next(error);
    }
});

// Force logout a user
router.post('/users/logout/:id', async (req, res, next) => {
    try {
        const sessionId = req.params.id;

        // Get session details
        const [sessions] = await db.query(
            'SELECT * FROM active_sessions WHERE id = ?',
            [sessionId]
        );

        if (sessions.length === 0) {
            req.session.flashError = 'Session not found';
            return res.redirect('/admin/users/active');
        }

        const session = sessions[0];

        // Check if current user is trying to log out an admin (only allow if it's their own session)
        const [users] = await db.query(
            'SELECT role FROM users WHERE id = ?',
            [session.user_id]
        );

        if (users.length > 0 && users[0].role === 'admin' && session.user_id !== req.session.userId) {
            req.session.flashError = 'You cannot force logout another admin user';
            return res.redirect('/admin/users/active');
        }

        // Mark session as inactive
        await db.query(
            'UPDATE active_sessions SET is_active = 0 WHERE id = ?',
            [sessionId]
        );

        // Send force logout message via WebSocket
        const { forceLogout } = require('../websocket-server');
        forceLogout(session.user_id);

        req.session.flashSuccess = 'User has been logged out successfully';
        res.redirect('/admin/users/active');
    } catch (error) {
        console.error('Error forcing logout:', error);
        next(error);
    }
});

// Active Users Page
router.get('/users/active', async (req, res, next) => {
    try {
        // Get all active sessions with user details
        const [activeUsers] = await db.query(`
            SELECT as.*, u.username, u.email, u.role, u.profile_image
            FROM active_sessions as
            JOIN users u ON as.user_id = u.id
            WHERE as.is_active = 1
            ORDER BY as.last_activity DESC
        `);

        res.render('admin/active-users', {
            title: 'Active Users',
            pageTitle: 'Active Users',
            currentPage: 'users',
            activeUsers,
            currentUser: res.locals.user
        });
    } catch (error) {
        console.error('Error loading active users:', error);
        next(error);
    }
});

// Force logout a user
router.post('/users/logout/:sessionId', async (req, res, next) => {
    try {
        const { sessionId } = req.params;

        // Get session details
        const [sessions] = await db.query(
            'SELECT * FROM active_sessions WHERE session_id = ?',
            [sessionId]
        );

        if (sessions.length === 0) {
            req.session.flashError = 'Session not found';
            return res.redirect('/admin/users/active');
        }

        const session = sessions[0];

        // Check if current user is trying to log out an admin (only allow if it's their own session)
        const [users] = await db.query(
            'SELECT role FROM users WHERE id = ?',
            [session.user_id]
        );

        if (users.length > 0 && users[0].role === 'admin' && session.user_id !== req.session.userId) {
            req.session.flashError = 'You cannot force logout another admin user';
            return res.redirect('/admin/users/active');
        }

        // Mark session as inactive
        await db.query(
            'UPDATE active_sessions SET is_active = 0 WHERE session_id = ?',
            [sessionId]
        );

        req.session.flashSuccess = 'User has been logged out successfully';
        res.redirect('/admin/users/active');
    } catch (error) {
        console.error('Error forcing logout:', error);
        next(error);
    }
});

// Calendar view
router.get('/calendar', async (req, res, next) => {
    try {
        // Get holidays
        let holidays = [];
        try {
            // Check if holiday_calendar table exists
            const [tables] = await db.query(`
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = 'exam_prep_platform'
                AND TABLE_NAME = 'holiday_calendar'
            `);

            if (tables.length > 0) {
                [holidays] = await db.query(`
                    SELECT id, holiday_date, description, holiday_type, is_active
                    FROM holiday_calendar
                    ORDER BY holiday_date
                `);
            }
        } catch (err) {
            console.log('Holiday calendar table may not exist yet:', err.message);
        }

        // Get flash messages
        const flashSuccess = req.session.flashSuccess;
        const flashError = req.session.flashError;

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;

        res.render('admin/calendar', {
            title: 'Academic Calendar',
            pageTitle: 'Academic Calendar',
            currentPage: 'calendar',
            holidays: holidays || [],
            isAdmin: true,
            flashSuccess,
            flashError
        });
    } catch (error) {
        console.error('Error fetching calendar data:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to fetch calendar data',
            error: { status: 500, stack: error.stack },
            layout: 'admin'
        });
    }
});

// Import holidays from Excel
router.post('/calendar/import-holidays', async (req, res, next) => {
    // Set up multer for file upload
    const storage = multer.memoryStorage();
    const upload = multer({
        storage: storage,
        limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
        fileFilter: (req, file, cb) => {
            // Accept only Excel files
            const filetypes = /xlsx|xls/;
            const mimetype = filetypes.test(file.mimetype);
            const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

            if (mimetype && extname) {
                return cb(null, true);
            }
            cb(new Error('Only Excel files (.xlsx, .xls) are allowed'));
        }
    }).single('holidaysFile');

    upload(req, res, async (err) => {
        if (err) {
            req.session.flashError = err.message;
            return res.redirect('/admin/calendar');
        }

        if (!req.file) {
            req.session.flashError = 'No file uploaded';
            return res.redirect('/admin/calendar');
        }

        try {
            const ExcelJS = require('exceljs');
            const workbook = new ExcelJS.Workbook();

            // Load the workbook from the uploaded file buffer
            await workbook.xlsx.load(req.file.buffer);

            const worksheet = workbook.getWorksheet(1);
            if (!worksheet) {
                req.session.flashError = 'Worksheet not found in the uploaded file';
                return res.redirect('/admin/calendar');
            }

            // Parse the worksheet data
            const holidays = [];
            let headerRow = true;
            let dateColumnIndex = 0;
            let descriptionColumnIndex = 1;
            let typeColumnIndex = 2;
            let errors = [];
            let imported = 0;
            let skipped = 0;

            // Process each row
            worksheet.eachRow((row, rowNumber) => {
                // Skip header row
                if (headerRow) {
                    headerRow = false;
                    return;
                }

                try {
                    // Extract data from cells
                    const dateValue = row.getCell(dateColumnIndex + 1).value;
                    const description = row.getCell(descriptionColumnIndex + 1).value;
                    const holidayType = row.getCell(typeColumnIndex + 1).value;

                    // Skip rows with missing required data
                    if (!dateValue || !description) {
                        errors.push(`Row ${rowNumber}: Missing required data (date or description)`);
                        skipped++;
                        return;
                    }

                    // Parse date (handle both date objects and strings)
                    let holidayDate;
                    if (dateValue instanceof Date) {
                        // Use UTC to avoid timezone issues
                        const utcDate = new Date(Date.UTC(
                            dateValue.getFullYear(),
                            dateValue.getMonth(),
                            dateValue.getDate()
                        ));
                        holidayDate = utcDate.toISOString().split('T')[0];
                    } else if (typeof dateValue === 'string') {
                        // Try to parse the date string
                        // First check if it's in YYYY-MM-DD format
                        if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
                            holidayDate = dateValue; // Already in correct format
                        } else {
                            const parsedDate = new Date(dateValue);
                            if (isNaN(parsedDate.getTime())) {
                                errors.push(`Row ${rowNumber}: Invalid date format`);
                                skipped++;
                                return;
                            }
                            // Use UTC to avoid timezone issues
                            const utcDate = new Date(Date.UTC(
                                parsedDate.getFullYear(),
                                parsedDate.getMonth(),
                                parsedDate.getDate()
                            ));
                            holidayDate = utcDate.toISOString().split('T')[0];
                        }
                    } else {
                        errors.push(`Row ${rowNumber}: Invalid date format`);
                        skipped++;
                        return;
                    }

                    // Validate holiday type
                    let validatedType = 'Public Holiday'; // Default
                    if (holidayType) {
                        if (['Public Holiday', 'National Holiday', 'Festival'].includes(holidayType)) {
                            validatedType = holidayType;
                        } else if (holidayType.toLowerCase().includes('national')) {
                            validatedType = 'National Holiday';
                        } else if (holidayType.toLowerCase().includes('festival')) {
                            validatedType = 'Festival';
                        }
                    }

                    // Add to holidays array
                    holidays.push({
                        holiday_date: holidayDate,
                        description: description,
                        holiday_type: validatedType
                    });
                } catch (error) {
                    errors.push(`Row ${rowNumber}: ${error.message}`);
                    skipped++;
                }
            });

            // Insert holidays into database
            if (holidays.length > 0) {
                // Create connection with transaction support
                const connection = await db.getConnection();
                try {
                    await connection.beginTransaction();

                    for (const holiday of holidays) {
                        try {
                            // Check if holiday already exists
                            const [existing] = await connection.query(
                                'SELECT id FROM holiday_calendar WHERE holiday_date = ?',
                                [holiday.holiday_date]
                            );

                            if (existing.length > 0) {
                                // Update existing holiday
                                await connection.query(
                                    'UPDATE holiday_calendar SET description = ?, holiday_type = ? WHERE holiday_date = ?',
                                    [holiday.description, holiday.holiday_type, holiday.holiday_date]
                                );
                                errors.push(`Holiday on ${holiday.holiday_date} already exists and was updated`);
                            } else {
                                // Insert new holiday
                                await connection.query(
                                    'INSERT INTO holiday_calendar (holiday_date, description, holiday_type) VALUES (?, ?, ?)',
                                    [holiday.holiday_date, holiday.description, holiday.holiday_type]
                                );
                                imported++;
                            }
                        } catch (error) {
                            errors.push(`Error processing holiday on ${holiday.holiday_date}: ${error.message}`);
                            skipped++;
                        }
                    }

                    await connection.commit();
                } catch (error) {
                    await connection.rollback();
                    throw error;
                } finally {
                    connection.release();
                }
            }

            // Set flash messages
            if (imported > 0) {
                req.session.flashSuccess = `Successfully imported ${imported} holidays`;
            }

            if (errors.length > 0) {
                req.session.flashError = `Import completed with ${errors.length} issues: ${errors.slice(0, 3).join('; ')}${errors.length > 3 ? ' and more...' : ''}`;
            }

            res.redirect('/admin/calendar');
        } catch (error) {
            console.error('Error importing holidays:', error);
            req.session.flashError = `Error importing holidays: ${error.message}`;
            res.redirect('/admin/calendar');
        }
    });
});

// Database Schema Diagram
router.get('/schema', adminSchemaController.getSchemaPage);

// Admin Dashboard
router.get('/dashboard', async (req, res, next) => {
    try {
        // Get counts for dashboard stats
        const [userCount] = await db.query('SELECT COUNT(*) as count FROM users');
        // Count all tests including soft-deleted ones
        const [testCount] = await db.query('SELECT COUNT(*) as count FROM exams');
        // Also get count of active (non-deleted) tests
        const [activeTestCount] = await db.query('SELECT COUNT(*) as count FROM exams WHERE (is_deleted = 0 OR is_deleted IS NULL)');
        // Get count of deleted tests
        const [deletedTestCount] = await db.query('SELECT COUNT(*) as count FROM exams WHERE is_deleted = 1');
        const [questionCount] = await db.query('SELECT COUNT(*) as count FROM questions');

        // Check if exam_attempts table exists before querying it
        let attemptCount = [{ count: 0 }];
        try {
            [attemptCount] = await db.query('SELECT COUNT(*) as count FROM exam_attempts');
        } catch (error) {
            console.warn('exam_attempts table does not exist yet:', error.message);
            // Table doesn't exist, use default value of 0
        }

        // Get recent activity
        const [recentActivity] = await db.query(`
            SELECT 'user_registered' as type, username as subject, created_at as timestamp
            FROM users
            ORDER BY created_at DESC
            LIMIT 5
        `);

        // Get active users count
        const [activeUsersCount] = await db.query(
            'SELECT COUNT(*) as count FROM active_sessions WHERE is_active = 1'
        );

        // Get system status
        const systemStatus = {
            status: 'Operational',
            lastBackup: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
            activeUsers: activeUsersCount[0].count, // Real active users count
            serverLoad: (Math.random() * 30 + 10).toFixed(2) + '%' // Random percentage for demo
        };

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/dashboard', {
            title: 'Admin Dashboard',
            pageTitle: 'Admin Dashboard',
            currentPage: 'dashboard',
            stats: {
                users: userCount[0].count,
                tests: testCount[0].count,
                activeTests: activeTestCount[0].count,
                deletedTests: deletedTestCount[0].count,
                questions: questionCount[0].count,
                attempts: attemptCount[0].count
            },
            recentActivity,
            systemStatus,
            user: req.session.user,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading admin dashboard:', error);
        next(error);
    }
});

// Pending User Approvals
router.get('/users/pending-approvals', async (req, res, next) => {
    try {
        // Get all users pending approval
        const [pendingUsers] = await db.query(
            'SELECT * FROM users WHERE is_approved = 0 AND role != "admin" ORDER BY created_at DESC'
        );

        // Get flash messages from session
        const flashSuccess = req.session.flashSuccess;
        const flashError = req.session.flashError;

        // Clear flash messages from session
        delete req.session.flashSuccess;
        delete req.session.flashError;

        res.render('admin/users/pending-approvals', {
            title: 'Pending User Approvals',
            pageTitle: 'Pending User Approvals',
            currentPage: 'users',
            pendingUsers,
            flashSuccess,
            flashError
        });
    } catch (error) {
        console.error('Error loading pending user approvals:', error);
        next(error);
    }
});

// Approve user
router.post('/users/approve/:id', async (req, res, next) => {
    try {
        const userId = req.params.id;
        const adminId = req.session.userId;

        // Check if user exists and is pending approval
        const [users] = await db.query(
            'SELECT id, username, email FROM users WHERE id = ? AND is_approved = 0',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found or already approved';
            return res.redirect('/admin/users/pending-approvals');
        }

        const user = users[0];

        // Update user to approved status
        await db.query(
            'UPDATE users SET is_approved = 1 WHERE id = ?',
            [userId]
        );

        // Create notification for the user
        await db.query(
            'INSERT INTO notifications (user_id, type, title, message, is_read, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
            [userId, 'account_approved', 'Account Approved', 'Your account has been approved. You can now log in.', 0]
        );

        // Log the approval action
        await db.query(
            'INSERT INTO logs (timestamp, user_id, level, category, operation, details, status) VALUES (NOW(), ?, ?, ?, ?, ?, ?)',
            [adminId, 'info', 'user', 'User Approved', `Approved user account: ${user.username} (${user.email})`, 'success']
        );

        req.session.flashSuccess = `User ${user.username} has been approved successfully`;
        res.redirect('/admin/users/pending-approvals');
    } catch (error) {
        console.error('Error approving user:', error);
        req.session.flashError = 'An error occurred while approving the user';
        res.redirect('/admin/users/pending-approvals');
    }
});

// Reject user
router.post('/users/reject/:id', async (req, res, next) => {
    try {
        const userId = req.params.id;
        const adminId = req.session.userId;

        // Check if user exists and is pending approval
        const [users] = await db.query(
            'SELECT id, username, email FROM users WHERE id = ? AND is_approved = 0',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found or already processed';
            return res.redirect('/admin/users/pending-approvals');
        }

        const user = users[0];

        // Delete the user
        await db.query('DELETE FROM users WHERE id = ?', [userId]);

        // Log the rejection action
        await db.query(
            'INSERT INTO logs (timestamp, user_id, level, category, operation, details, status) VALUES (NOW(), ?, ?, ?, ?, ?, ?)',
            [adminId, 'info', 'user', 'User Rejected', `Rejected and deleted user account: ${user.username} (${user.email})`, 'success']
        );

        req.session.flashSuccess = `User ${user.username} has been rejected and removed`;
        res.redirect('/admin/users/pending-approvals');
    } catch (error) {
        console.error('Error rejecting user:', error);
        req.session.flashError = 'An error occurred while rejecting the user';
        res.redirect('/admin/users/pending-approvals');
    }
});

// Roles Management Page
router.get('/users/roles', async (req, res, next) => {
    try {
        // Get all roles with user counts
        const [roles] = await db.query(`
            SELECT r.role_id, r.role_name, r.description, r.is_system,
                   COUNT(u.id) as user_count
            FROM roles r
            LEFT JOIN users u ON r.role_name = u.role
            GROUP BY r.role_id
            ORDER BY r.role_name
        `);

        res.render('admin/users/roles', {
            title: 'User Roles',
            pageTitle: 'User Roles',
            currentPage: 'users',
            roles
        });
    } catch (error) {
        console.error('Error loading roles:', error);
        next(error);
    }
});

// User role management
router.get('/users', userRoleController.getUsers);
router.get('/users/:id/role', userRoleController.getAssignRole);
router.post('/users/:id/role', userRoleController.updateRole);

// Student management
router.get('/students', studentAssignmentController.getStudents);
router.get('/students/:id/assign-class', studentAssignmentController.getAssignClass);
router.post('/students/:id/assign-class', studentAssignmentController.updateClassAssignments);
router.get('/students/:id/assign-subjects', studentAssignmentController.getAssignSubjects);
router.post('/students/:id/assign-subjects', studentAssignmentController.updateSubjectAssignments);

// Users list (legacy route - will be replaced by the controller above)
router.get('/users-old', async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = 10; // Users per page
        const offset = (page - 1) * limit;

        // Get query parameters
        const query = {
            search: req.query.search || '',
            role: req.query.role || '',
            status: req.query.status || '',
            sort: req.query.sort || 'created_desc',
            fromDate: req.query.fromDate || '',
            toDate: req.query.toDate || ''
        };

        // Build the WHERE clause
        let whereClause = '1 = 1';
        let queryParams = [];

        if (query.search) {
            whereClause += ' AND (username LIKE ? OR email LIKE ?)';
            queryParams.push(`%${query.search}%`, `%${query.search}%`);
        }

        if (query.role) {
            whereClause += ' AND role = ?';
            queryParams.push(query.role);
        }

        if (query.fromDate) {
            whereClause += ' AND DATE(created_at) >= ?';
            queryParams.push(query.fromDate);
        }

        if (query.toDate) {
            whereClause += ' AND DATE(created_at) <= ?';
            queryParams.push(query.toDate);
        }

        // Build the ORDER BY clause
        let orderBy = '';
        switch (query.sort) {
            case 'created_asc':
                orderBy = 'created_at ASC';
                break;
            case 'username_asc':
                orderBy = 'username ASC';
                break;
            case 'username_desc':
                orderBy = 'username DESC';
                break;
            default: // created_desc
                orderBy = 'created_at DESC';
        }

        // Get total count with status filter
        let countQuery = `
            SELECT COUNT(*) as total FROM (
                SELECT u.id, u.created_at, u.is_blocked,
                       (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
                FROM users u
                WHERE ${whereClause}
                ${query.status === 'blocked' ? 'AND u.is_blocked = 1' : ''}
                ${query.status === 'online' ? 'HAVING is_online = 1' : ''}
                ${query.status === 'offline' ? 'HAVING is_online = 0' : ''}
                ${query.status === 'new' ? 'HAVING TIMESTAMPDIFF(HOUR, u.created_at, NOW()) <= 48' : ''}
            ) as filtered_users`;

        const [countResult] = await db.query(countQuery, queryParams);
        const totalUsers = countResult[0].total;
        const totalPages = Math.ceil(totalUsers / limit);

        // Get users with pagination and online status
        let mainQuery;
        if (query.status === 'online' || query.status === 'offline' || query.status === 'new') {
            // For queries that need HAVING clause, we need to use a subquery
            mainQuery = `
                SELECT u.*
                FROM (
                    SELECT u.*,
                           (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
                    FROM users u
                    WHERE ${whereClause}
                    ${query.status === 'online' ? 'HAVING is_online = 1' : ''}
                    ${query.status === 'offline' ? 'HAVING is_online = 0' : ''}
                    ${query.status === 'new' ? 'HAVING TIMESTAMPDIFF(HOUR, u.created_at, NOW()) <= 48' : ''}
                ) as u
                ORDER BY ${orderBy}
                LIMIT ? OFFSET ?`;
        } else if (query.status === 'blocked') {
            // For blocked users
            mainQuery = `
                SELECT u.*,
                       (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
                FROM users u
                WHERE ${whereClause} AND u.is_blocked = 1
                ORDER BY ${orderBy}
                LIMIT ? OFFSET ?`;
        } else {
            // For simple queries without HAVING, we can use a direct query
            mainQuery = `
                SELECT u.*,
                       (SELECT COUNT(*) > 0 FROM active_sessions a WHERE a.user_id = u.id AND a.is_active = 1) as is_online
                FROM users u
                WHERE ${whereClause}
                ORDER BY ${orderBy}
                LIMIT ? OFFSET ?`;
        }

        const [users] = await db.query(mainQuery, [...queryParams, limit, offset]);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/users/index', {
            title: 'Manage Users',
            pageTitle: 'Manage Users',
            currentPage: 'users',
            users,
            query, // Pass the query object
            pagination: {
                currentPage: page,
                totalPages,
                totalUsers,
                limit
            },
            formatDate, // Explicitly pass the date formatter function
            formatDateTime, // Explicitly pass the date-time formatter function
            currentUserId: req.session.userId // Explicitly pass the current user's ID
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        next(error);
    }
});

// Add user form
router.get('/users/add', (req, res) => {
    res.render('admin/users/add', {
        title: 'Add New User',
        pageTitle: 'Add New User',
        currentPage: 'add-user',
        error: null
    });
});

// Add user form - alternative path
router.get('/admin/users/add', (req, res) => {
    res.redirect('/admin/users/add');
});

// Add user process - alternative path
router.post('/admin/users/add', (req, res) => {
    // Forward the request to the main handler
    req.url = '/users/add';
    router.handle(req, res);
});

// Add user process
router.post('/users/add', async (req, res, next) => {
    // Handle file upload with express-fileupload
    let profileImagePath = null;

    // Log the request body and files for debugging
    console.log('Request body:', req.body);
    console.log('Request files:', req.files);

    try {
        // Process the form data
        const { username, email, password, role, date_of_birth, bio } = req.body;

        // Validate input
        if (!username || !email || !password || !role || !date_of_birth) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'All fields are required'
            });
        }

        // Validate email format
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Invalid email format'
            });
        }

        // Validate password complexity
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        if (!passwordRegex.test(password)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Password must be at least 8 characters with at least one uppercase letter, one number, and one special character'
            });
        }

        // Validate role (must be one of the allowed enum values)
        const allowedRoles = ['admin', 'teacher', 'student'];
        if (!allowedRoles.includes(role)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Invalid role selected'
            });
        }

        // Truncate bio if it exceeds 500 characters
        const truncatedBio = bio ? bio.substring(0, 500) : '';

        // Check if email already exists
        const [existingUsers] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            [email]
        );

        if (existingUsers.length > 0) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Email already registered'
            });
        }

        // Hash password
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash(password, 10);

        // Handle file upload with express-fileupload
        let profile_image = null;

        if (req.files && req.files.profile_image) {
            const profileImage = req.files.profile_image;

            // Generate a unique filename
            const timestamp = Date.now();
            const ext = path.extname(profileImage.name);
            const filename = `profile_${timestamp}${ext}`;

            // Move the file to the uploads directory
            const uploadPath = path.join(__dirname, '../public/uploads/profiles', filename);

            try {
                await profileImage.mv(uploadPath);
                profile_image = `/uploads/profiles/${filename}`;
            } catch (uploadError) {
                console.error('Error uploading profile image:', uploadError);
                // Continue without the profile image
            }
        }

        // Insert user with all fields
        const [result] = await db.query(
            `INSERT INTO users (
                username, name, email, password, role,
                date_of_birth, bio, last_login, profile_image,
                is_active, is_approved, is_deleted
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, 1, 1, 0)`,
            [
                username,
                username, // Using username as name
                email,
                hashedPassword,
                role,
                date_of_birth,
                truncatedBio || '', // Ensure bio is never null
                profile_image
            ]
        );

        // Add user to role-based group
        const userId = result.insertId;
        try {
            await groupController.addUserToRoleGroup(userId, role);
        } catch (groupError) {
            console.error('Error adding user to role group, but user was created:', groupError);
            // Continue with the user creation process even if group assignment fails
        }

        req.session.flashSuccess = 'User created successfully';
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Error creating user:', error);

        // Provide more specific error messages based on the error
        let errorMessage = 'An error occurred while creating the user.';

        if (error.code === 'ER_DUP_ENTRY') {
            if (error.message.includes('username')) {
                errorMessage = 'Username already exists. Please choose a different username.';
            } else if (error.message.includes('email')) {
                errorMessage = 'Email already exists. Please use a different email address.';
            }
        } else if (error.code === 'ER_NO_REFERENCED_ROW') {
            errorMessage = 'Invalid reference. Please check all fields and try again.';
        } else if (error.code === 'ER_BAD_NULL_ERROR') {
            // Extract the column name from the error message
            const match = error.message.match(/Column '(.+)' cannot be null/);
            const column = match ? match[1] : 'unknown';
            errorMessage = `The ${column} field cannot be empty. Please provide a value.`;
        }

        return res.render('admin/users/add', {
            title: 'Add New User',
            pageTitle: 'Add New User',
            currentPage: 'add-user',
            error: errorMessage
        });
    }
});

// View user details
router.get('/users/:id', async (req, res, next) => {
    try {
        const userId = req.params.id;

        // Get user data
        const [users] = await db.query(
            `SELECT u.*,
            (SELECT COUNT(*) FROM exam_attempts WHERE user_id = u.id) as test_count,
            (SELECT COUNT(*) FROM user_answers WHERE attempt_id IN (SELECT attempt_id FROM exam_attempts WHERE user_id = u.id) AND is_bookmarked = 1) as bookmark_count,
            (SELECT COUNT(*) FROM active_sessions WHERE user_id = u.id AND is_active = 1) as active_sessions
            FROM users u WHERE u.id = ?`,
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/admin/users');
        }

        // Get recent activity
        const [activity] = await db.query(
            `SELECT * FROM logs
            WHERE user_id = ?
            ORDER BY timestamp DESC
            LIMIT 10`,
            [userId]
        );

        // Get groups the user is part of
        const [userGroups] = await db.query(
            `SELECT g.*, gm.is_admin
            FROM groups g
            JOIN group_members gm ON g.group_id = gm.group_id
            WHERE gm.user_id = ?
            ORDER BY g.name ASC`,
            [userId]
        );

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/users/view', {
            title: 'User Details',
            pageTitle: 'User Details',
            currentPage: 'users',
            user: users[0],
            activity: activity,
            userGroups: userGroups,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading user details:', error);
        next(error);
    }
});

// Edit user form
router.get('/users/:id/edit', async (req, res, next) => {
    try {
        const userId = req.params.id;

        // Get user data
        const [users] = await db.query(
            'SELECT * FROM users WHERE id = ?',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/users');
        }

        res.render('admin/users/edit', {
            title: 'Edit User',
            pageTitle: 'Edit User',
            currentPage: 'users',
            user: users[0],
            error: null
        });
    } catch (error) {
        console.error('Error loading user:', error);
        next(error);
    }
});

// Update user
router.post('/users/:id/update', upload.single('profile_image'), async (req, res, next) => {
    try {
        const userId = req.params.id;
        const { username, email, role, date_of_birth, password, bio } = req.body;

        // Validate required fields
        if (!username || !email || !role || !date_of_birth) {
            const [users] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);

            return res.render('admin/users/edit', {
                title: 'Edit User',
                pageTitle: 'Edit User',
                currentPage: 'users',
                user: users[0],
                error: 'Required fields cannot be empty'
            });
        }

        // Validate email format
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            const [users] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
            return res.render('admin/users/edit', {
                title: 'Edit User',
                pageTitle: 'Edit User',
                currentPage: 'users',
                user: users[0],
                error: 'Invalid email format'
            });
        }

        // Validate role (must be one of the allowed enum values)
        const allowedRoles = ['admin', 'teacher', 'student'];
        if (!allowedRoles.includes(role)) {
            const [users] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
            return res.render('admin/users/edit', {
                title: 'Edit User',
                pageTitle: 'Edit User',
                currentPage: 'users',
                user: users[0],
                error: 'Invalid role selected'
            });
        }

        // Truncate bio if it exceeds 500 characters
        const truncatedBio = bio ? bio.substring(0, 500) : '';

        // Start building the update query
        let updateQuery = 'UPDATE users SET username = ?, email = ?, role = ?, date_of_birth = ?, bio = ?';
        let queryParams = [username, email, role, date_of_birth, truncatedBio];

        // Add profile image to update if new file was uploaded
        if (req.file) {
            updateQuery += ', profile_image = ?';
            queryParams.push(`/uploads/profiles/${req.file.filename}`);
        }

        // Add password to update if provided
        if (password) {
            // Validate password complexity
            const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
            if (!passwordRegex.test(password)) {
                const [users] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);
                return res.render('admin/users/edit', {
                    title: 'Edit User',
                    pageTitle: 'Edit User',
                    currentPage: 'users',
                    user: users[0],
                    error: 'Password must be at least 8 characters with at least one uppercase letter, one number, and one special character'
                });
            }

            const bcrypt = require('bcrypt');
            const hashedPassword = await bcrypt.hash(password, 10);
            updateQuery += ', password = ?';
            queryParams.push(hashedPassword);
        }

        // Add WHERE clause
        updateQuery += ' WHERE id = ?';
        queryParams.push(userId);

        // Execute update
        await db.query(updateQuery, queryParams);

        // Get the user's previous role if it was changed
        const [previousUser] = await db.query('SELECT role FROM users WHERE id = ?', [userId]);
        const previousRole = previousUser[0].role;

        // If role was changed, update role-based group membership
        if (previousRole !== role) {
            await groupController.addUserToRoleGroup(userId, role);
        }

        req.session.flashSuccess = 'User updated successfully';
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Error updating user:', error);
        next(error);
    }
});

// Groups management
router.get('/groups', async (req, res, next) => {
    try {
        console.log('Groups request with query params:', req.query);

        // Build query with filters
        let query = `
            SELECT g.*,
                   COUNT(DISTINCT gm.user_id) as member_count,
                   u.username as creator_name
            FROM groups g
            LEFT JOIN group_members gm ON g.group_id = gm.group_id
            LEFT JOIN users u ON g.created_by = u.id
        `;

        const filters = [];
        const params = [];

        // Search filter
        if (req.query.search) {
            filters.push('(g.name LIKE ? OR g.description LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        // Type filter
        if (req.query.type === 'custom') {
            filters.push('g.is_role_group = 0');
        } else if (req.query.type === 'role') {
            filters.push('g.is_role_group = 1');
        }

        // Date range filtering
        if (req.query.date_from) {
            filters.push('DATE(g.created_at) >= ?');
            params.push(req.query.date_from);
        }

        if (req.query.date_to) {
            filters.push('DATE(g.created_at) <= ?');
            params.push(req.query.date_to);
        }

        // Add WHERE clause if there are filters
        if (filters.length > 0) {
            query += ' WHERE ' + filters.join(' AND ');
        }

        // Add GROUP BY and ORDER BY
        query += ' GROUP BY g.group_id';

        // Member count filter (applied after GROUP BY)
        let havingClause = '';
        if (req.query.member_count) {
            if (req.query.member_count === '0') {
                havingClause = ' HAVING member_count = 0';
            } else if (req.query.member_count === '1-5') {
                havingClause = ' HAVING member_count BETWEEN 1 AND 5';
            } else if (req.query.member_count === '6-20') {
                havingClause = ' HAVING member_count BETWEEN 6 AND 20';
            } else if (req.query.member_count === '21+') {
                havingClause = ' HAVING member_count >= 21';
            }
        }

        query += havingClause;
        query += ' ORDER BY g.created_at DESC';

        console.log('Final query:', query);
        console.log('Query params:', params);

        // Execute the query
        const [groups] = await db.query(query, params);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/groups/index', {
            title: 'Manage Groups',
            pageTitle: 'Manage Groups',
            currentPage: 'users',
            groups,
            query: req.query,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading groups:', error);
        next(error);
    }
});

// Use group import routes
const adminGroupImportRoutes = require('./admin-group-import-routes');
router.use('/groups', adminGroupImportRoutes);

// Create group form
router.get('/groups/create', async (req, res, next) => {
    try {
        // Get all users for member selection
        const [users] = await db.query(`
            SELECT id, username, email, role
            FROM users
            ORDER BY username ASC
        `);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/groups/create', {
            title: 'Create Group',
            pageTitle: 'Create Group',
            currentPage: 'users',
            users,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading create group form:', error);
        next(error);
    }
});

// Group details
router.get('/groups/:id', async (req, res, next) => {
    try {
        const groupId = req.params.id;

        // Get group details
        const [groups] = await db.query(`
            SELECT g.*, u.username as creator_name
            FROM groups g
            LEFT JOIN users u ON g.created_by = u.id
            WHERE g.group_id = ?
        `, [groupId]);

        if (groups.length === 0) {
            req.session.flashError = 'Group not found';
            return res.redirect('/admin/groups');
        }

        // Get group members
        const [members] = await db.query(`
            SELECT u.id, u.username, u.email, u.role, u.profile_image, gm.is_admin
            FROM group_members gm
            JOIN users u ON gm.user_id = u.id
            WHERE gm.group_id = ?
            ORDER BY gm.is_admin DESC, u.username ASC
        `, [groupId]);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/groups/view', {
            layout: 'admin',
            title: 'Group Details',
            pageTitle: 'Group Details',
            currentPage: 'users',
            group: groups[0],
            members,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime, // Explicitly pass the date-time formatter function
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Groups', url: '/admin/groups', active: false },
                { text: 'Group Details', url: '#', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading group details:', error);
        next(error);
    }
});

// Search users for adding to a group
router.get('/groups/:id/search/users', async (req, res, next) => {
    try {
        const groupId = req.params.id;
        const query = req.query.q || '';

        if (!query.trim()) {
            return res.json([]);
        }

        // Search for users not in the group
        const [users] = await db.query(`
            SELECT u.id, u.username, u.email, u.profile_image
            FROM users u
            LEFT JOIN group_members gm ON u.id = gm.user_id AND gm.group_id = ?
            WHERE gm.user_id IS NULL
            AND (u.username LIKE ? OR u.email LIKE ?)
            LIMIT 10
        `, [groupId, `%${query}%`, `%${query}%`]);

        res.json(users);
    } catch (error) {
        console.error('Error searching users:', error);
        res.status(500).json({
            success: false,
            message: 'Error searching users'
        });
    }
});

// Add member to a group
router.post('/groups/:id/members', async (req, res, next) => {
    try {
        const groupId = req.params.id;
        const { user_id } = req.body;

        // Validate required fields
        if (!user_id) {
            return res.status(400).json({
                success: false,
                message: 'User ID is required'
            });
        }

        // Check if user is already a member
        const [existingMember] = await db.query(`
            SELECT * FROM group_members
            WHERE group_id = ? AND user_id = ?
        `, [groupId, user_id]);

        if (existingMember.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'User is already a member of this group'
            });
        }

        // Add member
        await db.query(`
            INSERT INTO group_members (group_id, user_id, is_admin, joined_at)
            VALUES (?, ?, 0, NOW())
        `, [groupId, user_id]);

        // Get user details for response
        const [users] = await db.query(`
            SELECT id, username, email, profile_image
            FROM users
            WHERE id = ?
        `, [user_id]);

        res.json({
            success: true,
            message: 'Member added successfully',
            member: {
                user_id: parseInt(user_id),
                is_admin: 0,
                ...users[0]
            }
        });
    } catch (error) {
        console.error('Error adding member:', error);
        res.status(500).json({
            success: false,
            message: 'Error adding member'
        });
    }
});

// Remove member from a group
router.delete('/groups/:id/members/:userId', async (req, res, next) => {
    try {
        const groupId = req.params.id;
        const userId = req.params.userId;

        // Don't allow removing yourself if you're the only admin
        if (parseInt(userId) === req.session.userId) {
            const [adminCount] = await db.query(`
                SELECT COUNT(*) as count FROM group_members
                WHERE group_id = ? AND is_admin = 1
            `, [groupId]);

            if (adminCount[0].count <= 1) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot remove yourself as the only admin'
                });
            }
        }

        // Remove member
        await db.query(`
            DELETE FROM group_members
            WHERE group_id = ? AND user_id = ?
        `, [groupId, userId]);

        res.json({
            success: true,
            message: 'Member removed successfully'
        });
    } catch (error) {
        console.error('Error removing member:', error);
        res.status(500).json({
            success: false,
            message: 'Error removing member'
        });
    }
});

// Toggle admin status of a group member
router.put('/groups/:id/members/:userId/admin', async (req, res, next) => {
    try {
        const groupId = req.params.id;
        const userId = req.params.userId;
        const { is_admin } = req.body;

        // Don't allow removing admin status from yourself if you're the only admin
        if (parseInt(userId) === req.session.userId && !is_admin) {
            const [adminCount] = await db.query(`
                SELECT COUNT(*) as count FROM group_members
                WHERE group_id = ? AND is_admin = 1
            `, [groupId]);

            if (adminCount[0].count <= 1) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot remove admin status from yourself as the only admin'
                });
            }
        }

        // Update admin status
        await db.query(`
            UPDATE group_members
            SET is_admin = ?
            WHERE group_id = ? AND user_id = ?
        `, [is_admin ? 1 : 0, groupId, userId]);

        res.json({
            success: true,
            message: 'Admin status updated successfully'
        });
    } catch (error) {
        console.error('Error updating admin status:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating admin status'
        });
    }
});

// Generate invite link for a group
router.post('/groups/:id/invite', async (req, res, next) => {
    try {
        const groupId = req.params.id;
        const { expiresIn, maxUses } = req.body;
        const { v4: uuidv4 } = require('uuid');

        // Generate invite ID
        const inviteId = uuidv4();

        // Calculate expiration date
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + (parseInt(expiresIn) || 24));

        // Check if group_invites table exists
        try {
            // Create invite
            await db.query(`
                INSERT INTO group_invites (invite_id, group_id, created_by, expires_at, max_uses, uses)
                VALUES (?, ?, ?, ?, ?, 0)
            `, [inviteId, groupId, req.session.userId, expiresAt, maxUses || null]);
        } catch (error) {
            // If table doesn't exist, create it
            if (error.code === 'ER_NO_SUCH_TABLE') {
                await db.query(`
                    CREATE TABLE IF NOT EXISTS group_invites (
                        invite_id VARCHAR(36) PRIMARY KEY,
                        group_id INT NOT NULL,
                        created_by INT NOT NULL,
                        expires_at DATETIME NOT NULL,
                        max_uses INT DEFAULT NULL,
                        uses INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES groups(group_id) ON DELETE CASCADE,
                        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
                    )
                `);

                // Try again
                await db.query(`
                    INSERT INTO group_invites (invite_id, group_id, created_by, expires_at, max_uses, uses)
                    VALUES (?, ?, ?, ?, ?, 0)
                `, [inviteId, groupId, req.session.userId, expiresAt, maxUses || null]);
            } else {
                throw error;
            }
        }

        res.json({
            success: true,
            inviteLink: `${req.protocol}://${req.get('host')}/groups/join/${inviteId}`,
            inviteId
        });
    } catch (error) {
        console.error('Error generating invite link:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating invite link'
        });
    }
});

// Create group
router.post('/groups/create', async (req, res, next) => {
    try {
        const { name, description, members, isRoleGroup, roleType } = req.body;

        // Validate required fields
        if (!name) {
            req.session.flashError = 'Group name is required';
            return res.redirect('/admin/groups/create');
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Create group
        const [result] = await db.query(
            'INSERT INTO groups (name, description, created_by, is_role_group, role_type, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
            [name, description || null, req.session.userId, isRoleGroup ? 1 : 0, roleType || null]
        );

        const groupId = result.insertId;

        // Add creator as admin
        await db.query(
            'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 1, NOW())',
            [groupId, req.session.userId]
        );

        // Add members if provided
        if (members && Array.isArray(members)) {
            for (const memberId of members) {
                if (memberId != req.session.userId) {
                    await db.query(
                        'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 0, NOW())',
                        [groupId, memberId]
                    );
                }
            }
        }

        // If it's a role group, add all users with that role
        if (isRoleGroup && roleType) {
            const [users] = await db.query(
                'SELECT id FROM users WHERE role = ? AND id != ?',
                [roleType, req.session.userId]
            );

            for (const user of users) {
                await db.query(
                    'INSERT INTO group_members (group_id, user_id, is_admin, joined_at) VALUES (?, ?, 0, NOW())',
                    [groupId, user.id]
                );
            }
        }

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Group created successfully';
        res.redirect('/admin/groups');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error creating group:', error);
        req.session.flashError = 'Error creating group';
        res.redirect('/admin/groups/create');
    }
});

// Delete group
router.post('/groups/:id/delete', async (req, res, next) => {
    try {
        const groupId = req.params.id;

        // Check if group exists
        const [groups] = await db.query(
            'SELECT group_id, is_role_group FROM groups WHERE group_id = ?',
            [groupId]
        );

        if (groups.length === 0) {
            req.session.flashError = 'Group not found';
            return res.redirect('/admin/groups');
        }

        // Don't allow deleting role groups
        if (groups[0].is_role_group) {
            req.session.flashError = 'Cannot delete role-based groups';
            return res.redirect('/admin/groups');
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Delete group members
        await db.query('DELETE FROM group_members WHERE group_id = ?', [groupId]);

        // Delete group messages
        await db.query('DELETE FROM group_messages WHERE group_id = ?', [groupId]);

        // Delete group
        await db.query('DELETE FROM groups WHERE group_id = ?', [groupId]);

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Group deleted successfully';
        res.redirect('/admin/groups');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error deleting group:', error);
        req.session.flashError = 'Error deleting group';
        res.redirect('/admin/groups');
    }
});

// Delete user
router.post('/users/:id/delete', async (req, res, next) => {
    try {
        const userId = req.params.id;

        // Check if user exists
        const [users] = await db.query(
            'SELECT id FROM users WHERE id = ?',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/users');
        }

        // Delete user
        await db.query('DELETE FROM users WHERE id = ?', [userId]);

        req.session.flashSuccess = 'User deleted successfully';
        res.redirect('/users');
    } catch (error) {
        console.error('Error deleting user:', error);
        next(error);
    }
});

// Block user
router.post('/users/:id/block', async (req, res, next) => {
    try {
        const userId = req.params.id;
        const adminId = req.session.userId;

        // Check if user exists
        const [users] = await db.query(
            'SELECT id, username, role, is_blocked FROM users WHERE id = ?',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/admin/users');
        }

        const user = users[0];

        // Prevent admin from blocking themselves
        if (userId == adminId) {
            req.session.flashError = 'You cannot block yourself';
            return res.redirect('/admin/users');
        }

        // Block the user
        await db.query('UPDATE users SET is_blocked = 1 WHERE id = ?', [userId]);

        // Log the action
        await db.query(
            'INSERT INTO logs (timestamp, user_id, level, category, operation, details, status, ip_address, request_method, request_uri, user_agent) VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [
                adminId,
                'info',
                'user',
                'User Blocked',
                `User ${user.username} has been blocked`,
                'success',
                req.ip,
                req.method,
                req.originalUrl,
                req.get('User-Agent')
            ]
        );

        // Force logout all sessions for this user
        await db.query('UPDATE active_sessions SET is_active = 0 WHERE user_id = ?', [userId]);

        req.session.flashSuccess = `User ${user.username} has been blocked successfully`;
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Error blocking user:', error);
        next(error);
    }
});

// Unblock user
router.post('/users/:id/unblock', async (req, res, next) => {
    try {
        const userId = req.params.id;
        const adminId = req.session.userId;

        // Check if user exists
        const [users] = await db.query(
            'SELECT id, username, role, is_blocked FROM users WHERE id = ?',
            [userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/admin/users');
        }

        const user = users[0];

        // Unblock the user
        await db.query('UPDATE users SET is_blocked = 0 WHERE id = ?', [userId]);

        // Log the action
        await db.query(
            'INSERT INTO logs (timestamp, user_id, level, category, operation, details, status, ip_address, request_method, request_uri, user_agent) VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [
                adminId,
                'info',
                'user',
                'User Unblocked',
                `User ${user.username} has been unblocked`,
                'success',
                req.ip,
                req.method,
                req.originalUrl,
                req.get('User-Agent')
            ]
        );

        req.session.flashSuccess = `User ${user.username} has been unblocked successfully`;
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Error unblocking user:', error);
        next(error);
    }
});

// Role management
const roleController = require('../controllers/role-controller');

// Main roles route
router.get('/users/roles', roleController.index);

// Alternative route to the same page for better UX
router.get('/roles', (req, res) => {
    res.redirect('/admin/users/roles');
});

router.get('/users/roles/create', roleController.createForm);
router.post('/users/roles/create', roleController.create);
router.get('/users/roles/:id/edit', roleController.editForm);
router.post('/users/roles/:id/edit', roleController.update);
router.post('/users/roles/:id/delete', roleController.delete);
router.get('/users/roles/:id/users', roleController.viewUsers);

// Tests list
router.get('/tests', async (req, res, next) => {
    try {
        console.log('Tests list request with query params:', req.query);
        console.log('Date filters:', { from: req.query.date_from, to: req.query.date_to });
        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Build filter conditions
        const filters = [];
        const params = [];

        // Always exclude soft-deleted tests
        filters.push('(e.is_deleted = 0 OR e.is_deleted IS NULL)');

        if (req.query.search) {
            filters.push('(e.exam_name LIKE ? OR e.description LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.status) {
            filters.push('e.status = ?');
            params.push(req.query.status);
        }

        // Filter by assignment status
        if (req.query.assignment_status) {
            if (req.query.assignment_status === 'assigned') {
                filters.push('EXISTS (SELECT 1 FROM test_assignments ta WHERE ta.exam_id = e.exam_id AND ta.is_active = 1)');
            } else if (req.query.assignment_status === 'unassigned') {
                filters.push('NOT EXISTS (SELECT 1 FROM test_assignments ta WHERE ta.exam_id = e.exam_id AND ta.is_active = 1)');
            }
        }

        if (req.query.category) {
            filters.push('e.category_id = ?');
            params.push(req.query.category);
        }

        // Handle date range filtering
        if (req.query.date_from) {
            console.log('Filtering by date_from:', req.query.date_from);
            filters.push('DATE(e.created_at) >= ?');
            params.push(req.query.date_from);
        }

        if (req.query.date_to) {
            console.log('Filtering by date_to:', req.query.date_to);
            filters.push('DATE(e.created_at) <= ?');
            params.push(req.query.date_to);
        }

        // Log the SQL filter conditions
        console.log('Filter conditions:', filters);
        console.log('Filter parameters:', params);

        // For backward compatibility
        if (req.query.date && !req.query.date_from && !req.query.date_to) {
            filters.push('DATE(e.created_at) = ?');
            params.push(req.query.date);
        }

        if (req.query.created_by) {
            filters.push('e.created_by = ?');
            params.push(req.query.created_by);
        }

        // Construct WHERE clause
        const whereClause = `WHERE ${filters.join(' AND ')}`;

        // Get total count for pagination
        const [[{ total }]] = await db.query(`
            SELECT COUNT(*) as total
            FROM exams e
            ${whereClause}
        `, params);

        // Get total count of all tests (including soft-deleted)
        const [[totalStats]] = await db.query(`
            SELECT COUNT(*) as total_count FROM exams
        `);

        // Get count of active tests (excluding soft-deleted)
        const [[activeStats]] = await db.query(`
            SELECT COUNT(*) as active_count FROM exams
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
        `);

        // Get count of deleted tests
        const [[deletedStats]] = await db.query(`
            SELECT COUNT(*) as deleted_count FROM exams
            WHERE is_deleted = 1
        `);

        // Get additional statistics
        const [[stats]] = await db.query(`
            SELECT
                COUNT(DISTINCT created_by) as creator_count,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_count,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_count,
                SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived_count,
                SUM(CASE WHEN status = 'scheduled' OR (publish_date IS NOT NULL AND publish_date > NOW() AND status = 'published') THEN 1 ELSE 0 END) as scheduled_count,
                (SELECT COUNT(DISTINCT section_id) FROM sections) as section_count
            FROM exams
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
        `);

        // Get filtered tests
        const [tests] = await db.query(`
            SELECT
                e.*,
                c.name as category_name,
                u.username as creator_name,
                (SELECT COUNT(*) FROM sections s WHERE s.exam_id = e.exam_id) as section_count,
                (SELECT COUNT(*) FROM exam_attempts ea WHERE ea.exam_id = e.exam_id) as attempt_count,
                e.status as display_status,
                (SELECT COUNT(*) FROM test_assignments ta WHERE ta.exam_id = e.exam_id AND ta.is_active = 1) as assignment_count,
                CASE
                    WHEN (SELECT COUNT(*) FROM test_assignments ta WHERE ta.exam_id = e.exam_id AND ta.is_active = 1) > 0 THEN 'assigned'
                    ELSE 'unassigned'
                END as assignment_status
            FROM exams e
            LEFT JOIN categories c ON e.category_id = c.category_id
            LEFT JOIN users u ON e.created_by = u.id
            ${whereClause}
            ORDER BY e.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, perPage, offset]);

        // Get sections for each test with question counts
        for (const test of tests) {
            const [sections] = await db.query(`
                SELECT
                    s.section_id,
                    s.section_name,
                    s.position,
                    (SELECT COUNT(*) FROM questions q WHERE q.section_id = s.section_id) as question_count
                FROM sections s
                WHERE s.exam_id = ?
                ORDER BY s.position
            `, [test.exam_id]);

            test.sections = sections;
        }

        // Get categories for filter dropdown
        const [categories] = await db.query('SELECT category_id, name FROM categories WHERE parent_id IS NULL');

        // Get creators for filter dropdown
        const [creators] = await db.query('SELECT DISTINCT u.id, u.username FROM users u JOIN exams e ON u.id = e.created_by ORDER BY u.username');

        // Get stats for count cards - include all tests regardless of deletion status
        const [[creatorStats]] = await db.query('SELECT COUNT(DISTINCT created_by) as creator_count FROM exams');
        const [[publishedStats]] = await db.query('SELECT COUNT(*) as published_count FROM exams WHERE status = "published" AND (is_deleted = 0 OR is_deleted IS NULL)');
        const [[draftStats]] = await db.query('SELECT COUNT(*) as draft_count FROM exams WHERE status = "draft" AND (is_deleted = 0 OR is_deleted IS NULL)');
        const [[scheduledStats]] = await db.query('SELECT COUNT(*) as scheduled_count FROM exams WHERE (status = "scheduled" OR (publish_date IS NOT NULL AND publish_date > NOW() AND status = "published")) AND (is_deleted = 0 OR is_deleted IS NULL)');
        const [[archivedStats]] = await db.query('SELECT COUNT(*) as archived_count FROM exams WHERE status = "archived" AND (is_deleted = 0 OR is_deleted IS NULL)');
        const [[sectionStats]] = await db.query('SELECT COUNT(DISTINCT section_id) as section_count FROM sections');

        // Create stats object
        const statsObj = {
            creator_count: creatorStats.creator_count,
            published_count: publishedStats.published_count,
            draft_count: draftStats.draft_count,
            scheduled_count: scheduledStats.scheduled_count,
            archived_count: archivedStats.archived_count,
            section_count: sectionStats.section_count
        };

        // Calculate pagination info
        const totalPages = Math.ceil(total / perPage);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/tests/index', {
            title: 'Test Management',
            pageTitle: 'All Tests',
            currentPage: 'tests',
            tests,
            categories,
            creators,
            pagination: {
                page,
                totalPages,
                perPage
            },
            query: req.query,
            totalTests: totalStats.total_count, // Total count of all tests (including deleted)
            activeTests: activeStats.active_count, // Count of active tests (excluding deleted)
            deletedTests: deletedStats.deleted_count, // Count of deleted tests
            filteredTests: total, // Count of filtered tests
            perPage,
            stats: statsObj,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading tests:', error);
        next(error);
    }
});

// Add test form
router.get('/tests/add', async (req, res) => {
    try {
        // Fetch categories from database
        const [categories] = await db.query('SELECT * FROM categories ORDER BY name');

        res.render('admin/tests/add', {
            title: 'Add New Test',
            pageTitle: 'Add New Test',
            categories: categories || []
        });
    } catch (error) {
        console.error('Error loading add test form:', error);
        res.render('admin/tests/add', {
            title: 'Add New Test',
            pageTitle: 'Add New Test',
            categories: [],
            error: 'Error loading categories'
        });
    }
});

// Download sample template for importing questions
router.get('/tests/download-sample-template', (req, res) => {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sample Questions');

    // Add headers
    worksheet.columns = [
        { header: 'Question Text', key: 'question_text', width: 40 },
        { header: 'Question Type', key: 'question_type', width: 15 },
        { header: 'Options', key: 'options', width: 30 },
        { header: 'Correct Answer', key: 'correct_answer', width: 20 },
        { header: 'Solution', key: 'solution', width: 30 },
        { header: 'Marks', key: 'marks', width: 10 },
        { header: 'Negative Marks', key: 'negative_marks', width: 15 },
        { header: 'Category', key: 'category', width: 15 }
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0F0FF' }
    };

    // Add sample data
    worksheet.addRow({
        question_text: 'What is 2+2?',
        question_type: 'mcq',
        options: '3|4|5|6',
        correct_answer: '4',
        solution: 'Basic addition',
        marks: 1,
        negative_marks: 0.25,
        category: 'Math'
    });

    worksheet.addRow({
        question_text: 'Is water wet?',
        question_type: 'true_false',
        options: '',
        correct_answer: 'true',
        solution: 'Water makes things wet',
        marks: 1,
        negative_marks: 0,
        category: 'Science'
    });

    worksheet.addRow({
        question_text: 'What is the capital of France?',
        question_type: 'short_answer',
        options: '',
        correct_answer: 'Paris',
        solution: 'Paris is the capital city of France',
        marks: 2,
        negative_marks: 0,
        category: 'Geography'
    });

    // Set content type and disposition
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=sample_questions_template.xlsx');

    // Write to response
    workbook.xlsx.write(res)
        .then(() => {
            res.end();
        })
        .catch(err => {
            console.error('Error generating Excel file:', err);
            res.status(500).send('Error generating template file');
        });
});

// Import questions from file
router.post('/tests/import-questions', (req, res) => {
    // Set up multer for file upload
    const storage = multer.memoryStorage();
    const upload = multer({
        storage: storage,
        limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
        fileFilter: (req, file, cb) => {
            // Accept only Excel and CSV files
            const filetypes = /xlsx|csv/;
            const mimetype = filetypes.test(file.mimetype);
            const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

            if (mimetype && extname) {
                return cb(null, true);
            }
            cb(new Error('Only .xlsx and .csv files are allowed'));
        }
    }).single('importFile');

    upload(req, res, async (err) => {
        if (err) {
            return res.status(400).json({ success: false, message: err.message });
        }

        if (!req.file) {
            return res.status(400).json({ success: false, message: 'No file uploaded' });
        }

        try {
            const ExcelJS = require('exceljs');
            const workbook = new ExcelJS.Workbook();

            // Load the workbook from the uploaded file buffer
            await workbook.xlsx.load(req.file.buffer);

            const worksheet = workbook.getWorksheet(1);
            if (!worksheet) {
                return res.status(400).json({ success: false, message: 'Worksheet not found in the uploaded file' });
            }

            // Parse the worksheet data
            const questions = [];
            const headers = {};

            // Extract headers
            worksheet.getRow(1).eachCell((cell, colNumber) => {
                headers[colNumber] = cell.value.toLowerCase().replace(/\s+/g, '_');
            });

            // Extract data rows
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber === 1) return; // Skip header row

                const question = {};
                row.eachCell((cell, colNumber) => {
                    const header = headers[colNumber];
                    if (header) {
                        question[header] = cell.value;
                    }
                });

                // Validate required fields
                if (question.question_text && question.question_type) {
                    questions.push(question);
                }
            });

            // Return the parsed questions
            res.json({ success: true, questions });
        } catch (error) {
            console.error('Error parsing file:', error);
            res.status(500).json({ success: false, message: 'Error parsing file: ' + error.message });
        }
    });
});

// Test Categories
router.get('/tests/categories', async (req, res, next) => {
    try {
        // Get all categories with test counts
        const [categories] = await db.query(`
            SELECT c.*, COUNT(e.exam_id) as test_count
            FROM categories c
            LEFT JOIN exams e ON c.category_id = e.category_id
            GROUP BY c.category_id
            ORDER BY c.name
        `);

        res.render('admin/tests/categories', {
            title: 'Test Categories',
            pageTitle: 'Test Categories',
            currentPage: 'tests',
            categories
        });
    } catch (error) {
        console.error('Error loading test categories:', error);
        next(error);
    }
});

// Add Test Category
router.post('/tests/categories/add', async (req, res, next) => {
    try {
        const { name, description } = req.body;

        if (!name) {
            req.session.flashError = 'Category name is required';
            return res.redirect('/admin/tests/categories');
        }

        await db.query(
            'INSERT INTO categories (name, description) VALUES (?, ?)',
            [name, description || null]
        );

        req.session.flashSuccess = 'Category added successfully';
        res.redirect('/admin/tests/categories');
    } catch (error) {
        console.error('Error adding category:', error);
        next(error);
    }
});

// Update Test Category
router.post('/tests/categories/:id/update', async (req, res, next) => {
    try {
        const categoryId = req.params.id;
        const { name, description } = req.body;

        if (!name) {
            req.session.flashError = 'Category name is required';
            return res.redirect('/admin/tests/categories');
        }

        await db.query(
            'UPDATE categories SET name = ?, description = ? WHERE category_id = ?',
            [name, description || null, categoryId]
        );

        req.session.flashSuccess = 'Category updated successfully';
        res.redirect('/admin/tests/categories');
    } catch (error) {
        console.error('Error updating category:', error);
        next(error);
    }
});

// Delete Test Category
router.post('/tests/categories/:id/delete', async (req, res, next) => {
    try {
        const categoryId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Update tests to remove category reference
        await db.query('UPDATE exams SET category_id = NULL WHERE category_id = ?', [categoryId]);

        // Delete category
        await db.query('DELETE FROM categories WHERE category_id = ?', [categoryId]);

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Category deleted successfully';
        res.redirect('/admin/tests/categories');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error deleting category:', error);
        next(error);
    }
});

// Deleted tests list
router.get('/tests/trash', async (req, res, next) => {
    try {
        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Build filter conditions
        const filters = [];
        const params = [];

        // Only include soft-deleted tests
        filters.push('e.is_deleted = 1');

        if (req.query.search) {
            filters.push('(e.exam_name LIKE ? OR e.description LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.created_by) {
            filters.push('e.created_by = ?');
            params.push(req.query.created_by);
        }

        // Construct WHERE clause
        const whereClause = `WHERE ${filters.join(' AND ')}`;

        // Get total count for pagination
        const [[{ total }]] = await db.query(`
            SELECT COUNT(*) as total
            FROM exams e
            ${whereClause}
        `, params);

        // Get total count of all deleted tests
        const [[totalStats]] = await db.query(`
            SELECT COUNT(*) as total_count FROM exams
            WHERE is_deleted = 1
        `);

        // Get filtered tests
        const [tests] = await db.query(`
            SELECT
                e.*,
                c.name as category_name,
                u.username as creator_name,
                (SELECT COUNT(*) FROM sections s WHERE s.exam_id = e.exam_id) as section_count,
                (SELECT COUNT(*) FROM exam_attempts ea WHERE ea.exam_id = e.exam_id) as attempt_count,
                e.status as display_status
            FROM exams e
            LEFT JOIN categories c ON e.category_id = c.category_id
            LEFT JOIN users u ON e.created_by = u.id
            ${whereClause}
            ORDER BY e.deleted_at DESC
            LIMIT ? OFFSET ?
        `, [...params, perPage, offset]);

        // Get creators for filter dropdown
        const [creators] = await db.query('SELECT DISTINCT u.id, u.username FROM users u JOIN exams e ON u.id = e.created_by WHERE e.is_deleted = 1 ORDER BY u.username');

        // Calculate pagination info
        const totalPages = Math.ceil(total / perPage);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/tests/trash', {
            title: 'Deleted Tests',
            pageTitle: 'Deleted Tests',
            currentPage: 'tests-trash',
            tests,
            creators,
            pagination: {
                page,
                totalPages,
                perPage
            },
            query: req.query,
            totalTests: totalStats.total_count,
            filteredTests: total,
            perPage,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading deleted tests:', error);
        next(error);
    }
});

// Logs page
router.get('/logs', async (req, res, next) => {
    try {
        // Get query parameters
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        console.log('\n==== LOGS PAGE REQUEST ====');
        console.log('Query parameters:', req.query);
        console.log('Pagination:', { page, perPage, offset });

        // Get query parameters for filtering
        const query = {
            search: req.query.search || '',
            level: req.query.level || '',
            category: req.query.category || '',
            fromDate: req.query.fromDate || '',
            toDate: req.query.toDate || '',
            userId: req.query.userId || '',
            sort: req.query.sort || 'timestamp_desc'
        };

        // Build filters array
        const filters = [];
        const params = [];

        if (query.search) {
            filters.push('(operation LIKE ? OR details LIKE ? OR error_message LIKE ?)');
            params.push(`%${query.search}%`, `%${query.search}%`, `%${query.search}%`);
        }

        if (query.level) {
            filters.push('level = ?');
            params.push(query.level);
        }

        if (query.category) {
            filters.push('category = ?');
            params.push(query.category);
        }

        if (query.userId) {
            filters.push('user_id = ?');
            params.push(query.userId);
        }

        if (query.fromDate) {
            filters.push('timestamp >= ?');
            params.push(`${query.fromDate} 00:00:00`);
        }

        if (query.toDate) {
            filters.push('timestamp <= ?');
            params.push(`${query.toDate} 23:59:59`);
        }

        // Build the WHERE clause
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';
        console.log('Filter conditions:', { filters, params, whereClause });

        // Build the ORDER BY clause
        let orderByClause = '';
        switch (query.sort) {
            case 'timestamp_asc':
                orderByClause = 'ORDER BY timestamp ASC';
                break;
            case 'level_asc':
                orderByClause = 'ORDER BY level ASC';
                break;
            case 'level_desc':
                orderByClause = 'ORDER BY level DESC';
                break;
            case 'category_asc':
                orderByClause = 'ORDER BY category ASC';
                break;
            case 'category_desc':
                orderByClause = 'ORDER BY category DESC';
                break;
            case 'timestamp_desc':
            default:
                orderByClause = 'ORDER BY timestamp DESC';
                break;
        }

        // Get total count of filtered logs
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM logs ${whereClause}`,
            params
        );

        const totalLogs = countResult[0].total;
        const totalPages = Math.ceil(totalLogs / perPage);
        console.log('Query results:', { totalLogs, totalPages });

        // Get logs with pagination
        const [logs] = await db.query(
            `SELECT logs.*, users.username
             FROM logs
             LEFT JOIN users ON logs.user_id = users.id
             ${whereClause}
             ${orderByClause}
             LIMIT ? OFFSET ?`,
            [...params, perPage, offset]
        );

        // Get total count of all logs (unfiltered)
        const [[totalStats]] = await db.query(`
            SELECT COUNT(*) as total_count FROM logs
        `);
        console.log('Total stats:', totalStats);

        // Get the total unfiltered count
        const totalCount = parseInt(totalStats.total_count) || 0;
        console.log('Total count:', totalCount);

        // Get unique levels
        const [levels] = await db.query(`
            SELECT DISTINCT level FROM logs ORDER BY level
        `);

        // Get unique categories
        const [categories] = await db.query(`
            SELECT DISTINCT category FROM logs ORDER BY category
        `);

        // Get level statistics
        const [levelStats] = await db.query(`
            SELECT level, COUNT(*) as count FROM logs GROUP BY level
        `);

        // Get category statistics
        const [categoryStats] = await db.query(`
            SELECT category, COUNT(*) as count FROM logs GROUP BY category
        `);

        // Get users who have logs
        const [users] = await db.query(`
            SELECT DISTINCT u.id, u.username
            FROM logs l
            JOIN users u ON l.user_id = u.id
            ORDER BY u.username
        `);

        // Create a stats object with explicit values
        const statsObject = {
            totalLogs: totalLogs,
            totalLogsUnfiltered: totalCount,
            levelStats: levelStats,
            categoryStats: categoryStats
        };

        console.log('Stats object:', statsObject);

        res.render('admin/logs/index', {
            title: 'System Logs',
            pageTitle: 'System Logs',
            logs,
            levels,
            categories,
            users,
            query: req.query,
            pagination: {
                currentPage: page,
                perPage,
                totalPages,
                totalItems: totalLogs,
                totalItemsUnfiltered: totalCount,
                baseUrl: '/admin/logs'
            },
            stats: statsObject
        });
    } catch (error) {
        console.error('Error loading logs:', error);
        next(error);
    }
});

// Logs page
router.get('/logs', checkAdmin, async (req, res, next) => {
    try {
        // Check if logs table exists
        try {
            await db.query('SELECT 1 FROM logs LIMIT 1');
        } catch (error) {
            if (error.code === 'ER_NO_SUCH_TABLE') {
                // Create logs table if it doesn't exist
                console.log('Logs table does not exist. Creating it now...');
                const createTableSQL = `
                    CREATE TABLE IF NOT EXISTS \`logs\` (
                      \`log_id\` int(11) NOT NULL AUTO_INCREMENT,
                      \`timestamp\` datetime NOT NULL DEFAULT current_timestamp(),
                      \`user_id\` int(11) DEFAULT NULL,
                      \`level\` enum('info','warning','error','debug') NOT NULL DEFAULT 'info',
                      \`category\` varchar(50) NOT NULL DEFAULT 'system',
                      \`operation\` varchar(100) NOT NULL,
                      \`details\` text DEFAULT NULL,
                      \`status\` varchar(20) DEFAULT NULL,
                      \`error_message\` text DEFAULT NULL,
                      \`ip_address\` varchar(45) DEFAULT NULL,
                      \`request_method\` varchar(10) DEFAULT NULL,
                      \`request_uri\` varchar(255) DEFAULT NULL,
                      \`user_agent\` varchar(255) DEFAULT NULL,
                      \`created_at\` timestamp NOT NULL DEFAULT current_timestamp(),
                      PRIMARY KEY (\`log_id\`),
                      KEY \`logs_user_id_foreign\` (\`user_id\`),
                      KEY \`logs_level_index\` (\`level\`),
                      KEY \`logs_category_index\` (\`category\`),
                      KEY \`logs_timestamp_index\` (\`timestamp\`),
                      CONSTRAINT \`logs_user_id_foreign\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\` (\`id\`) ON DELETE SET NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                `;
                await db.query(createTableSQL);

                // Insert sample log entries
                const sampleLogsSQL = `
                    INSERT INTO \`logs\` (\`timestamp\`, \`user_id\`, \`level\`, \`category\`, \`operation\`, \`details\`, \`status\`, \`error_message\`, \`ip_address\`, \`request_method\`, \`request_uri\`, \`user_agent\`)
                    VALUES
                        (NOW(), 1, 'info', 'auth', 'User Login', 'User logged in successfully', 'success', NULL, '127.0.0.1', 'POST', '/login', 'Mozilla/5.0'),
                        (NOW(), 1, 'info', 'user', 'Profile Update', 'User updated their profile information', 'success', NULL, '127.0.0.1', 'PUT', '/profile', 'Mozilla/5.0'),
                        (NOW(), 2, 'warning', 'auth', 'Failed Login Attempt', 'Multiple failed login attempts detected', 'warning', NULL, '***********', 'POST', '/login', 'Mozilla/5.0'),
                        (NOW(), NULL, 'error', 'system', 'Database Connection', 'Failed to connect to database', 'error', 'Connection refused', '127.0.0.1', 'GET', '/admin/dashboard', 'Mozilla/5.0'),
                        (NOW(), 3, 'info', 'test', 'Test Created', 'New test created: Math Test', 'success', NULL, '127.0.0.1', 'POST', '/admin/tests/add', 'Mozilla/5.0'),
                        (NOW(), 3, 'info', 'test', 'Test Updated', 'Test updated: Math Test', 'success', NULL, '127.0.0.1', 'PUT', '/admin/tests/5', 'Mozilla/5.0'),
                        (NOW(), 1, 'info', 'question', 'Question Created', 'New question created: What is 2+2?', 'success', NULL, '127.0.0.1', 'POST', '/admin/questions/add', 'Mozilla/5.0'),
                        (NOW(), 1, 'debug', 'system', 'Cache Cleared', 'Application cache cleared', 'success', NULL, '127.0.0.1', 'POST', '/admin/cache/clear', 'Mozilla/5.0'),
                        (NOW(), 2, 'error', 'test', 'Test Submission', 'Failed to submit test results', 'error', 'Database query error', '192.168.1.2', 'POST', '/tests/submit/3', 'Mozilla/5.0'),
                        (NOW(), NULL, 'warning', 'system', 'Disk Space', 'Low disk space warning', 'warning', NULL, '127.0.0.1', 'GET', '/admin/dashboard', 'Mozilla/5.0');
                `;
                await db.query(sampleLogsSQL);
                console.log('Sample logs inserted successfully.');
            } else {
                throw error;
            }
        }
        // Get query parameters
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        console.log('\n==== LOGS PAGE REQUEST ====');
        console.log('Query parameters:', req.query);
        console.log('Pagination:', { page, perPage, offset });

        // Get query parameters for filtering
        const query = {
            search: req.query.search || '',
            level: req.query.level || '',
            category: req.query.category || '',
            fromDate: req.query.fromDate || '',
            toDate: req.query.toDate || '',
            userId: req.query.userId || '',
            sort: req.query.sort || 'timestamp_desc'
        };

        // Build filters array
        const filters = [];
        const params = [];

        if (query.search) {
            filters.push('(operation LIKE ? OR details LIKE ? OR error_message LIKE ?)');
            params.push(`%${query.search}%`, `%${query.search}%`, `%${query.search}%`);
        }

        if (query.level) {
            filters.push('level = ?');
            params.push(query.level);
        }

        if (query.category) {
            filters.push('category = ?');
            params.push(query.category);
        }

        if (query.userId) {
            filters.push('user_id = ?');
            params.push(query.userId);
        }

        if (query.fromDate) {
            filters.push('timestamp >= ?');
            params.push(`${query.fromDate} 00:00:00`);
        }

        if (query.toDate) {
            filters.push('timestamp <= ?');
            params.push(`${query.toDate} 23:59:59`);
        }

        // Build the WHERE clause
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';
        console.log('Filter conditions:', { filters, params, whereClause });

        // Build the ORDER BY clause
        let orderByClause = '';
        switch (query.sort) {
            case 'timestamp_asc':
                orderByClause = 'ORDER BY timestamp ASC';
                break;
            case 'level_asc':
                orderByClause = 'ORDER BY level ASC';
                break;
            case 'level_desc':
                orderByClause = 'ORDER BY level DESC';
                break;
            case 'category_asc':
                orderByClause = 'ORDER BY category ASC';
                break;
            case 'category_desc':
                orderByClause = 'ORDER BY category DESC';
                break;
            case 'timestamp_desc':
            default:
                orderByClause = 'ORDER BY timestamp DESC';
                break;
        }

        // Get total count of filtered logs
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM logs ${whereClause}`,
            params
        );

        const totalLogs = countResult[0].total;
        const totalPages = Math.ceil(totalLogs / perPage);
        console.log('Query results:', { totalLogs, totalPages });

        // Get logs with pagination
        const [logs] = await db.query(
            `SELECT logs.*, users.username
             FROM logs
             LEFT JOIN users ON logs.user_id = users.id
             ${whereClause}
             ${orderByClause}
             LIMIT ? OFFSET ?`,
            [...params, perPage, offset]
        );

        // Get total count of all logs (unfiltered)
        const [[totalStats]] = await db.query(`
            SELECT COUNT(*) as total_count FROM logs
        `);
        console.log('Total stats:', totalStats);

        // Get the total unfiltered count
        const totalCount = parseInt(totalStats?.total_count) || 0;
        console.log('Total count:', totalCount);

        // Get unique levels
        const [levels] = await db.query(`
            SELECT DISTINCT level FROM logs ORDER BY level
        `);

        // Get unique categories
        const [categories] = await db.query(`
            SELECT DISTINCT category FROM logs ORDER BY category
        `);

        // Get level statistics
        const [levelStats] = await db.query(`
            SELECT level, COUNT(*) as count FROM logs GROUP BY level
        `);

        // Get category statistics
        const [categoryStats] = await db.query(`
            SELECT category, COUNT(*) as count FROM logs GROUP BY category
        `);

        // Get users who have logs
        const [users] = await db.query(`
            SELECT DISTINCT u.id, u.username
            FROM logs l
            JOIN users u ON l.user_id = u.id
            ORDER BY u.username
        `);

        // Create a stats object with explicit values
        const statsObject = {
            totalLogs: totalLogs,
            totalLogsUnfiltered: totalCount,
            levelStats: levelStats,
            categoryStats: categoryStats
        };

        console.log('Stats object:', statsObject);

        res.render('admin/logs/index', {
            title: 'System Logs',
            pageTitle: 'System Logs',
            logs,
            levels,
            categories,
            users,
            query: req.query,
            pagination: {
                currentPage: page,
                perPage,
                totalPages,
                totalItems: totalLogs,
                totalItemsUnfiltered: totalCount,
                baseUrl: '/admin/logs'
            },
            stats: statsObject
        });
    } catch (error) {
        console.error('Error loading logs:', error);
        next(error);
    }
});

// API endpoint to add logs
router.post('/logs/add', async (req, res) => {
    try {
        const { action, entity_type, entity_id, details } = req.body;
        const user_id = req.session.user?.id || 0;

        // Insert log into database
        await db.query(
            'INSERT INTO logs (user_id, action, entity_type, entity_id, details) VALUES (?, ?, ?, ?, ?)',
            [user_id, action, entity_type, entity_id, details]
        );

        res.json({ success: true });
    } catch (error) {
        console.error('Error adding log:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// API endpoint to fetch logs
router.get('/api/logs', checkAdmin, async (req, res) => {
    try {
        const [logs] = await db.query('SELECT * FROM logs ORDER BY timestamp DESC LIMIT 200');
        res.json({ success: true, logs });
    } catch (error) {
        console.error('Error fetching logs:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch logs', error: error.message });
    }
});

// Test route to add a new log entry
router.get('/test-log', checkAdmin, async (req, res) => {
    try {
        // Import the logger
        const { logEvent } = require('../utils/logger');

        // Log the event
        await logEvent(
            req,
            'info',
            'test',
            'Test Log Entry',
            'This is a test log entry created manually at ' + new Date().toISOString(),
            'success'
        );

        // Also log a warning and an error for testing different log levels
        await logEvent(
            req,
            'warning',
            'test',
            'Test Warning',
            'This is a test warning log entry',
            'warning'
        );

        await logEvent(
            req,
            'error',
            'test',
            'Test Error',
            'This is a test error log entry',
            'error',
            'This is a simulated error message'
        );

        res.redirect('/admin/logs');
    } catch (error) {
        console.error('Error creating test log:', error);
        res.status(500).json({ success: false, message: 'Failed to create test log', error: error.message });
    }
});

// API endpoint to fetch a single log by ID
router.get('/api/logs/:id', checkAdmin, async (req, res) => {
    try {
        const logId = req.params.id;
        const [logs] = await db.query(
            `SELECT logs.*, users.username
             FROM logs
             LEFT JOIN users ON logs.user_id = users.id
             WHERE log_id = ?`,
            [logId]
        );

        if (logs.length === 0) {
            return res.status(404).json({ success: false, message: 'Log not found' });
        }

        res.json({ success: true, log: logs[0] });
    } catch (error) {
        console.error('Error fetching log details:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch log details', error: error.message });
    }
});

// JavaScript Values Logger Routes
router.get('/jsvalues', checkAdmin, async (req, res, next) => {
    try {
        // Check if jsvalues table exists
        try {
            await db.query('SELECT 1 FROM jsvalues LIMIT 1');
        } catch (error) {
            if (error.code === 'ER_NO_SUCH_TABLE') {
                // Create jsvalues table if it doesn't exist
                console.log('JSValues table does not exist. Creating it now...');
                const createTableSQL = `
                    CREATE TABLE IF NOT EXISTS \`jsvalues\` (
                      \`id\` int(11) NOT NULL AUTO_INCREMENT,
                      \`timestamp\` datetime NOT NULL DEFAULT current_timestamp(),
                      \`route\` varchar(255) DEFAULT NULL,
                      \`context\` varchar(255) DEFAULT NULL,
                      \`variable_name\` varchar(255) NOT NULL,
                      \`variable_type\` varchar(50) DEFAULT NULL,
                      \`variable_value\` text DEFAULT NULL,
                      \`user_id\` int(11) DEFAULT NULL,
                      \`ip_address\` varchar(50) DEFAULT NULL,
                      \`user_agent\` text DEFAULT NULL,
                      PRIMARY KEY (\`id\`),
                      KEY \`route\` (\`route\`),
                      KEY \`variable_name\` (\`variable_name\`),
                      KEY \`user_id\` (\`user_id\`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                `;
                await db.query(createTableSQL);
            } else {
                throw error;
            }
        }

        // Pagination
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Get query parameters for filtering
        const query = {
            search: req.query.search || '',
            route: req.query.route || '',
            context: req.query.context || '',
            variableName: req.query.variableName || '',
            variableType: req.query.variableType || '',
            fromDate: req.query.fromDate || '',
            toDate: req.query.toDate || '',
            userId: req.query.userId || '',
            sort: req.query.sort || 'timestamp_desc'
        };

        // Build filters array
        const filters = [];
        const params = [];

        if (query.search) {
            filters.push('(route LIKE ? OR context LIKE ? OR variable_name LIKE ? OR variable_value LIKE ?)');
            const searchParam = `%${query.search}%`;
            params.push(searchParam, searchParam, searchParam, searchParam);
        }

        if (query.route) {
            filters.push('route LIKE ?');
            params.push(`%${query.route}%`);
        }

        if (query.context) {
            filters.push('context LIKE ?');
            params.push(`%${query.context}%`);
        }

        if (query.variableName) {
            filters.push('variable_name LIKE ?');
            params.push(`%${query.variableName}%`);
        }

        if (query.variableType) {
            filters.push('variable_type = ?');
            params.push(query.variableType);
        }

        if (query.fromDate) {
            filters.push('timestamp >= ?');
            params.push(`${query.fromDate} 00:00:00`);
        }

        if (query.toDate) {
            filters.push('timestamp <= ?');
            params.push(`${query.toDate} 23:59:59`);
        }

        if (query.userId) {
            filters.push('user_id = ?');
            params.push(query.userId);
        }

        // Build WHERE clause
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

        // Build ORDER BY clause
        let orderBy = 'timestamp DESC';
        if (query.sort) {
            const [field, direction] = query.sort.split('_');
            const validFields = ['timestamp', 'route', 'context', 'variable_name', 'variable_type'];
            const validDirections = ['asc', 'desc'];

            if (validFields.includes(field) && validDirections.includes(direction)) {
                orderBy = `${field} ${direction.toUpperCase()}`;
            }
        }

        // Get total count for pagination
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM jsvalues ${whereClause}`,
            params
        );
        const totalItems = countResult[0].total;

        // Get total count without filters
        const [countUnfilteredResult] = await db.query('SELECT COUNT(*) as total FROM jsvalues');
        const totalItemsUnfiltered = countUnfilteredResult[0].total;

        // Get jsvalues with pagination
        const [jsvalues] = await db.query(
            `SELECT * FROM jsvalues ${whereClause} ORDER BY ${orderBy} LIMIT ? OFFSET ?`,
            [...params, perPage, offset]
        );

        // Get unique routes for filter dropdown
        const [routes] = await db.query('SELECT DISTINCT route FROM jsvalues ORDER BY route');

        // Get unique contexts for filter dropdown
        const [contexts] = await db.query('SELECT DISTINCT context FROM jsvalues ORDER BY context');

        // Get unique variable names for filter dropdown
        const [variableNames] = await db.query('SELECT DISTINCT variable_name FROM jsvalues ORDER BY variable_name');

        // Get unique variable types for filter dropdown
        const [variableTypes] = await db.query('SELECT DISTINCT variable_type FROM jsvalues ORDER BY variable_type');

        // Get users for filter dropdown
        const [users] = await db.query('SELECT id, username FROM users ORDER BY username');

        // Calculate pagination info
        const totalPages = Math.ceil(totalItems / perPage);
        const pagination = {
            currentPage: page,
            perPage,
            totalPages,
            totalItems,
            totalItemsUnfiltered,
            baseUrl: '/admin/jsvalues'
        };

        // Calculate stats for count cards
        const [stats] = await db.query(`
            SELECT
                COUNT(*) as totalJsValues,
                COUNT(DISTINCT route) as routesCount,
                COUNT(DISTINCT context) as contextsCount,
                COUNT(DISTINCT variable_name) as variableNamesCount,
                COUNT(DISTINCT user_id) as usersCount
            FROM jsvalues
        `);

        // Get variable type stats for count cards
        const [typeStats] = await db.query(`
            SELECT
                variable_type,
                COUNT(*) as count
            FROM jsvalues
            GROUP BY variable_type
            ORDER BY count DESC
        `);

        // Render the page
        res.render('admin/jsvalues/index', {
            jsvalues,
            routes,
            contexts,
            variableNames,
            variableTypes,
            users,
            query,
            pagination,
            stats: {
                ...stats[0],
                typeStats
            }
        });
    } catch (error) {
        console.error('Error fetching JavaScript values:', error);
        next(error);
    }
});

// API route to get JavaScript value details
router.get('/api/jsvalues/:id', checkAdmin, async (req, res) => {
    try {
        const id = req.params.id;

        // Get jsvalue details
        const [jsvalues] = await db.query('SELECT * FROM jsvalues WHERE id = ?', [id]);

        if (jsvalues.length === 0) {
            return res.status(404).json({ success: false, message: 'JavaScript value not found' });
        }

        // Get username if user_id exists
        if (jsvalues[0].user_id) {
            const [users] = await db.query('SELECT username FROM users WHERE id = ?', [jsvalues[0].user_id]);
            if (users.length > 0) {
                jsvalues[0].username = users[0].username;
            }
        }

        res.json({ success: true, jsvalue: jsvalues[0] });
    } catch (error) {
        console.error('Error fetching JavaScript value details:', error);
        res.status(500).json({ success: false, message: 'Error fetching JavaScript value details' });
    }
});

// Helper function to get dashboard stats
async function getDashboardStats() {
    try {
        const [userCount] = await db.query('SELECT COUNT(*) as count FROM users');
        // Count all tests including soft-deleted ones
        const [testCount] = await db.query('SELECT COUNT(*) as count FROM exams');
        // Also get count of active (non-deleted) tests
        const [activeTestCount] = await db.query('SELECT COUNT(*) as count FROM exams WHERE (is_deleted = 0 OR is_deleted IS NULL)');
        // Get count of deleted tests
        const [deletedTestCount] = await db.query('SELECT COUNT(*) as count FROM exams WHERE is_deleted = 1');
        const [questionCount] = await db.query('SELECT COUNT(*) as count FROM questions');
        const [attemptCount] = await db.query('SELECT COUNT(*) as count FROM exam_attempts');

        return {
            users: userCount[0].count,
            tests: testCount[0].count,
            activeTests: activeTestCount[0].count,
            deletedTests: deletedTestCount[0].count,
            questions: questionCount[0].count,
            attempts: attemptCount[0].count
        };
    } catch (error) {
        console.error('Error getting dashboard stats:', error);
        return {
            users: 0,
            tests: 0,
            activeTests: 0,
            deletedTests: 0,
            questions: 0,
            attempts: 0
        };
    }
}

// Helper function to get recent activity
async function getRecentActivity(limit = 10) {
    try {
        // Get recent user registrations
        const [registrations] = await db.query(`
            SELECT 'user_registered' as type, username as subject, created_at as timestamp
            FROM users
            ORDER BY created_at DESC
            LIMIT ?
        `, [limit]);

        // Get recent test creations
        const [testCreations] = await db.query(`
            SELECT 'test_created' as type, exam_name as subject, created_at as timestamp
            FROM exams
            ORDER BY created_at DESC
            LIMIT ?
        `, [limit]);

        // Combine and sort by timestamp
        const activity = [...registrations, ...testCreations]
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, limit);

        return activity;
    } catch (error) {
        console.error('Error getting recent activity:', error);
        return [];
    }
}

// General Settings
router.get('/settings', async (req, res, next) => {
    try {
        // Get current settings from database
        const [settings] = await db.query(`
            SELECT *
            FROM system_settings
            WHERE id = 1
        `);

        res.render('admin/settings/index', {
            title: 'General Settings',
            pageTitle: 'General Settings',
            currentPage: 'settings',
            settings: settings[0] || {},
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Error loading settings:', error);
        next(error);
    }
});

// Logo Upload Page
router.get('/settings/logo', settingsController.logoUploadPage);

// Handle Logo Upload
router.post('/settings/logo', settingsController.uploadLogo);

// Delete Logo
router.post('/settings/logo/delete', settingsController.deleteLogo);

// Update General Settings
router.post('/settings', async (req, res, next) => {
    try {
        const {
            site_name,
            site_description,
            contact_email,
            support_phone,
            maintenance_mode,
            timezone,
            date_format
        } = req.body;

        await db.query(`
            INSERT INTO system_settings (
                site_name,
                site_description,
                contact_email,
                support_phone,
                maintenance_mode,
                timezone,
                date_format,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
                site_name = VALUES(site_name),
                site_description = VALUES(site_description),
                contact_email = VALUES(contact_email),
                support_phone = VALUES(support_phone),
                maintenance_mode = VALUES(maintenance_mode),
                timezone = VALUES(timezone),
                date_format = VALUES(date_format),
                updated_at = NOW()
        `, [
            site_name,
            site_description,
            contact_email,
            support_phone,
            maintenance_mode ? 1 : 0,
            timezone,
            date_format
        ]);

        req.flash('success', 'Settings updated successfully');
        res.redirect('/admin/settings');
    } catch (error) {
        console.error('Error updating settings:', error);
        req.flash('error', 'Failed to update settings');
        res.redirect('/admin/settings');
    }
});

// Renaming the test file to demo
// This will require updating the route to render the new demo file
router.get('/demo', (req, res) => {
    res.render('demo');
});

router.get('/questions', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Build the WHERE clause based on filters
        let whereClause = 'WHERE 1=1';
        const params = [];

        if (req.query.search) {
            whereClause += ' AND (q.question_text LIKE ? OR q.solution_text LIKE ?)';
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.type) {
            whereClause += ' AND q.question_type = ?';
            params.push(req.query.type);
        }

        if (req.query.category) {
            whereClause += ' AND qcm.category_id = ?';
            params.push(req.query.category);
        }

        if (req.query.exam) {
            whereClause += ' AND e.exam_id = ?';
            params.push(req.query.exam);
        }

        if (req.query.section) {
            whereClause += ' AND s.section_id = ?';
            params.push(req.query.section);
        }

        // Get total count with filters
        const [countResult] = await db.query(
            `SELECT COUNT(DISTINCT q.question_id) as total
             FROM questions q
             LEFT JOIN sections s ON q.section_id = s.section_id
             LEFT JOIN exams e ON s.exam_id = e.exam_id
             LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
             LEFT JOIN categories c ON qcm.category_id = c.category_id
             ${whereClause}`,
            params
        );
        const totalItems = countResult[0].total;
        const totalPages = Math.ceil(totalItems / perPage);

        // Get question type statistics
        const [questionTypeStats] = await db.query(`
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions
            GROUP BY question_type
        `);

        // Get total number of question types
        const [questionTypesCount] = await db.query(`
            SELECT COUNT(DISTINCT question_type) as count
            FROM questions
        `);

        // Get total number of categories
        const [categoriesCount] = await db.query(`
            SELECT COUNT(DISTINCT category_id) as count
            FROM categories
        `);

        // Get filtered questions for current page
        const [questions] = await db.query(
            `SELECT
                q.*,
                GROUP_CONCAT(DISTINCT c.name) as category_name,
                GROUP_CONCAT(DISTINCT c.category_id) as category_ids,
                e.exam_name,
                s.section_name
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            ${whereClause}
            GROUP BY q.question_id
            ORDER BY q.created_at DESC
            LIMIT ? OFFSET ?`,
            [...params, perPage, offset]
        );

        // Process the results to handle category arrays
        questions.forEach(question => {
            question.category_ids = question.category_ids ? question.category_ids.split(',').map(Number) : [];
            question.category_name = question.category_name ? question.category_name.split(',') : [];
        });

        // Get categories for filter
        const [categories] = await db.query(
            'SELECT category_id, name FROM categories ORDER BY name'
        );

        // Get exams for filter
        const [exams] = await db.query(
            'SELECT exam_id, exam_name FROM exams ORDER BY exam_name'
        );

        // Get sections for filter
        const [sections] = await db.query(
            'SELECT section_id, section_name FROM sections ORDER BY section_name'
        );

        // Fetch essays for the add question modal
        const [essays] = await db.query(`
            SELECT
                e.essay_id,
                e.title,
                COUNT(q.question_id) as question_count,
                DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
            FROM essays e
            LEFT JOIN questions q ON e.essay_id = q.essay_id
            GROUP BY e.essay_id
            ORDER BY e.title
        `);

        res.render('admin/questions/index', {
            title: 'Question Bank',
            pageTitle: 'Question Bank',
            questions,
            categories,
            exams,
            sections,
            essays: essays || [], // Ensure essays is always defined
            pagination: {
                currentPage: page,
                perPage: perPage,
                totalPages: totalPages,
                totalItems: totalItems
            },
            query: req.query || {},
            stats: {
                totalQuestions: totalItems,
                questionTypeStats,
                questionTypesCount: questionTypesCount[0].count,
                categoriesCount: categoriesCount[0].count
            }
        });
    } catch (error) {
        console.error('Error fetching questions:', error);
        res.status(500).send('Server error');
    }
});

// Include essays routes
const essaysRoutes = require('./essays-routes');
router.use('/', essaysRoutes);

// API endpoint to get categories
router.get('/api/categories', async (req, res) => {
    try {
        const [categories] = await db.query('SELECT category_id, name FROM categories ORDER BY name');
        res.json({ success: true, categories });
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// API endpoint to get linked questions count
router.get('/api/essays/linked-count', async (req, res) => {
    try {
        const [[result]] = await db.query(
            'SELECT COUNT(*) as count FROM questions WHERE essay_id IS NOT NULL'
        );
        res.json({ success: true, count: result.count });
    } catch (error) {
        console.error('Error fetching linked questions count:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch linked questions count' });
    }
});

// Section update route
router.post('/sections/:sectionId/update', async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const { sectionId } = req.params;
        const { section_name, instructions, passing_marks } = req.body;

        if (!section_name || typeof section_name !== 'string' || section_name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Section name must be a non-empty string'
            });
        }

        const trimmedSectionName = section_name.trim().replace(/\s+/g, ' ');

        // Get the exam_id for this section
        const [sections] = await db.query(
            'SELECT exam_id FROM sections WHERE section_id = ?',
            [sectionId]
        );

        if (sections.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Section not found'
            });
        }

        const examId = sections[0].exam_id;

        // Check if a section with this name already exists for this exam (excluding this section)
        const [existingSections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ? AND section_id != ?',
            [examId, sectionId]
        );

        const normalizedNewName = trimmedSectionName.toLowerCase();
        const hasDuplicate = existingSections.some(section => {
            const normalizedExistingName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');
            return normalizedExistingName === normalizedNewName;
        });

        if (hasDuplicate) {
            return res.status(400).json({
                success: false,
                message: 'A section with this name already exists. Please use a unique name.'
            });
        }

        // Validate passing marks if provided
        if (passing_marks && (isNaN(passing_marks) || passing_marks < 0)) {
            return res.status(400).json({
                success: false,
                message: 'Passing marks must be a non-negative number',
                toast: {
                    type: 'error',
                    title: 'Error',
                    message: 'Passing marks must be a non-negative number'
                }
            });
        }

        // Update section with instructions and passing marks
        await db.query(
            'UPDATE sections SET section_name = ?, instructions = ?, passing_marks = ? WHERE section_id = ?',
            [trimmedSectionName, instructions || null, passing_marks || null, sectionId]
        );

        // Log the update to the system logs
        const { logEvent } = require('../utils/logger');
        await logEvent(
            req,
            'info',
            'test',
            'Section Update',
            `Updated section ${sectionId} with name: ${trimmedSectionName}`,
            'success'
        );

        res.status(200).json({
            success: true,
            message: 'Section updated successfully',
            toast: {
                type: 'success',
                title: 'Success',
                message: 'Section updated successfully'
            }
        });
    } catch (error) {
        // Log error to system logs
        const { logEvent, logError } = require('../utils/logger');
        await logError(error, req);
        await logEvent(
            req,
            'error',
            'test',
            'Section Update',
            `Failed to update section ${req.params.sectionId}`,
            'error',
            error.message
        );

        res.status(500).json({
            success: false,
            message: error.message || 'Error updating section',
            toast: {
                type: 'error',
                title: 'Error',
                message: 'Failed to update section. Please try again.'
            }
        });
    }
});

// Section delete route
router.post('/sections/:sectionId/delete', async (req, res) => {
    try {
        const { sectionId } = req.params;

        // Get the exam_id for this section
        const [sections] = await db.query(
            'SELECT exam_id FROM sections WHERE section_id = ?',
            [sectionId]
        );

        if (sections.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Section not found'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Delete all options for questions in this section
            await db.query(
                'DELETE FROM options WHERE question_id IN (SELECT question_id FROM questions WHERE section_id = ?)',
                [sectionId]
            );

            // Delete all questions in this section
            await db.query('DELETE FROM questions WHERE section_id = ?', [sectionId]);

            // Delete the section
            await db.query('DELETE FROM sections WHERE section_id = ?', [sectionId]);

            // Reorder remaining sections
            const [remainingSections] = await db.query(
                'SELECT section_id FROM sections WHERE exam_id = ? ORDER BY position',
                [sections[0].exam_id]
            );

            for (let i = 0; i < remainingSections.length; i++) {
                await db.query(
                    'UPDATE sections SET position = ? WHERE section_id = ?',
                    [i + 1, remainingSections[i].section_id]
                );
            }

            // Commit transaction
            await db.query('COMMIT');

            res.status(200).json({
                success: true,
                message: 'Section deleted successfully'
            });
        } catch (error) {
            // Rollback transaction
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error deleting section:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting section'
        });
    }
});

// Access Requests Management
router.get('/access-requests', async (req, res) => {
    try {
        const status = req.query.status;
        const page = parseInt(req.query.page) || 1;
        const perPage = 10;
        const offset = (page - 1) * perPage;

        // Build the query based on status filter
        let statusFilter = '';
        if (status) {
            statusFilter = ` AND ar.status = '${status}' `;
        }

        // Get access requests with pagination
        const [requests] = await db.query(`
            SELECT ar.*, u.username, u.email, u.name, e.exam_name,
                   admin.username as admin_username,
                   ar.processed_at
            FROM access_requests ar
            JOIN users u ON ar.user_id = u.id
            JOIN exams e ON ar.exam_id = e.exam_id
            LEFT JOIN users admin ON ar.processed_by = admin.id
            WHERE 1=1 ${statusFilter}
            ORDER BY ar.requested_at DESC
            LIMIT ? OFFSET ?
        `, [perPage, offset]);

        // Get total count for pagination
        const [countResult] = await db.query(`
            SELECT COUNT(*) as total FROM access_requests ar WHERE 1=1 ${statusFilter}
        `);

        // Get stats for quick filters
        const [stats] = await db.query(`
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
            FROM access_requests
        `);

        const totalItems = countResult[0].total;
        const totalPages = Math.ceil(totalItems / perPage);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/access-requests/index', {
            title: 'Access Requests',
            requests: requests,
            status: status,
            stats: stats[0],
            pagination: {
                page: page,
                perPage: perPage,
                totalItems: totalItems,
                totalPages: totalPages
            },
            layout: 'admin',
            formatDate,
            formatDateTime
        });
    } catch (error) {
        console.error('Error fetching access requests:', error);
        req.session.flashError = 'An error occurred while fetching access requests';
        res.redirect('/admin/dashboard');
    }
});

// Approve access request
router.post('/access-requests/approve', async (req, res) => {
    try {
        const { requestId, additionalAttempts } = req.body;

        // Validate input
        if (!requestId || !additionalAttempts || isNaN(additionalAttempts) || additionalAttempts < 1) {
            req.session.flashError = 'Please provide a valid number of additional attempts';
            return res.redirect('/admin/access-requests');
        }

        // Get the request details
        const [requests] = await db.query(
            'SELECT * FROM access_requests WHERE id = ?',
            [requestId]
        );

        if (requests.length === 0) {
            req.session.flashError = 'Access request not found';
            return res.redirect('/admin/access-requests');
        }

        const request = requests[0];

        // Update the request status
        await db.query(
            'UPDATE access_requests SET status = "approved", processed_by = ?, processed_at = NOW(), additional_attempts = ? WHERE id = ?',
            [req.session.userId, additionalAttempts, requestId]
        );

        // Get current assignment if exists
        const [assignments] = await db.query(
            'SELECT * FROM test_assignments WHERE exam_id = ? AND user_id = ? ORDER BY assigned_at DESC LIMIT 1',
            [request.exam_id, request.user_id]
        );

        if (assignments.length > 0) {
            // Update existing assignment with new max_attempts
            const currentMaxAttempts = parseInt(assignments[0].max_attempts) || 1;
            const newMaxAttempts = currentMaxAttempts + parseInt(additionalAttempts);

            await db.query(
                'UPDATE test_assignments SET max_attempts = ?, assigned_by = ?, assigned_at = NOW() WHERE id = ?',
                [newMaxAttempts, req.session.userId, assignments[0].id]
            );
        } else {
            // Create new assignment
            await db.query(
                'INSERT INTO test_assignments (exam_id, user_id, max_attempts, assigned_by, assigned_at, is_active) VALUES (?, ?, ?, ?, NOW(), 1)',
                [request.exam_id, request.user_id, additionalAttempts, req.session.userId]
            );
        }

        req.session.flashSuccess = 'Access request approved successfully';
        res.redirect('/admin/access-requests');
    } catch (error) {
        console.error('Error approving access request:', error);
        req.session.flashError = 'An error occurred while approving the access request';
        res.redirect('/admin/access-requests');
    }
});

// Reject access request
router.post('/access-requests/reject', async (req, res) => {
    try {
        const { requestId, reason } = req.body;

        if (!requestId) {
            req.session.flashError = 'Invalid request ID';
            return res.redirect('/admin/access-requests');
        }

        // Update the request status
        await db.query(
            'UPDATE access_requests SET status = "rejected", processed_by = ?, processed_at = NOW(), message = ? WHERE id = ?',
            [req.session.userId, reason || null, requestId]
        );

        req.session.flashSuccess = 'Access request rejected successfully';
        res.redirect('/admin/access-requests');
    } catch (error) {
        console.error('Error rejecting access request:', error);
        req.session.flashError = 'An error occurred while rejecting the access request';
        res.redirect('/admin/access-requests');
    }
});

// Import routes have been moved to the top of the groups section

// Delete question route
router.post('/questions/:questionId/delete', async (req, res) => {
    try {
        const { questionId } = req.params;

        // Check if question exists
        const [questions] = await db.query('SELECT * FROM questions WHERE question_id = ?', [questionId]);

        if (questions.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found',
                toast: {
                    type: 'error',
                    title: 'Error',
                    message: 'Question not found'
                }
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Delete options if they exist
            await db.query('DELETE FROM options WHERE question_id = ?', [questionId]);

            // Delete the question
            await db.query('DELETE FROM questions WHERE question_id = ?', [questionId]);

            // Commit transaction
            await db.query('COMMIT');

            // Log the deletion
            const { logEvent } = require('../utils/logger');
            await logEvent(
                req,
                'info',
                'question',
                'Question Deleted',
                `Deleted question ID: ${questionId}`,
                'success'
            );

            res.status(200).json({
                success: true,
                message: 'Question deleted successfully',
                toast: {
                    type: 'success',
                    title: 'Success',
                    message: 'Question deleted successfully'
                }
            });
        } catch (error) {
            // Rollback transaction in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error deleting question:', error);

        // Log the error
        const { logError } = require('../utils/logger');
        await logError(error, req);

        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting question',
            toast: {
                type: 'error',
                title: 'Error',
                message: 'Failed to delete question. Please try again.'
            }
        });
    }
});

// Class controller is already imported at the top of the file

// Classes routes
router.get('/classes', isAdmin, classController.getClasses);
router.get('/classes/add', isAdmin, classController.getAddClass);
router.post('/classes/add', isAdmin, classController.postAddClass);
router.get('/classes/edit/:id', isAdmin, classController.getEditClass);
router.post('/classes/edit/:id', isAdmin, classController.postEditClass);
router.delete('/classes/delete/:id', isAdmin, classController.deleteClass);

// Trades routes
router.get('/trades', isAdmin, classController.getTrades);
router.get('/trades/add', isAdmin, classController.getAddTrade);
router.post('/trades/add', isAdmin, classController.postAddTrade);
router.get('/trades/edit/:id', isAdmin, classController.getEditTrade);
router.post('/trades/edit/:id', isAdmin, classController.postEditTrade);
router.delete('/trades/delete/:id', isAdmin, classController.deleteTrade);

// Sections routes
router.get('/sections', isAdmin, classController.getSections);
router.get('/sections/add', isAdmin, classController.getAddSection);
router.post('/sections/add', isAdmin, classController.postAddSection);
router.get('/sections/edit/:id', isAdmin, classController.getEditSection);
router.post('/sections/edit/:id', isAdmin, classController.postEditSection);
router.delete('/sections/delete/:id', isAdmin, classController.deleteSection);

// Instruction plans routes
router.get('/instruction-plans', isAdmin, adminInstructionPlanController.getInstructionPlans);
router.get('/instruction-plans/add', isAdmin, adminInstructionPlanController.getAddInstructionPlan);
router.post('/instruction-plans/add', isAdmin, adminInstructionPlanController.postAddInstructionPlan);
router.get('/instruction-plans/:id', isAdmin, adminInstructionPlanController.getInstructionPlanDetails);
router.get('/instruction-plans/edit/:id', isAdmin, adminInstructionPlanController.getEditInstructionPlan);
router.post('/instruction-plans/edit/:id', isAdmin, adminInstructionPlanController.postEditInstructionPlan);
router.delete('/instruction-plans/delete/:id', isAdmin, adminInstructionPlanController.deleteInstructionPlan);

// Faculty operations routes
router.get('/faculty', isAdmin, facultyController.getFaculty);
router.get('/faculty/:id', isAdmin, facultyController.getFacultyDetails);
router.get('/faculty/:id/subjects', isAdmin, facultyController.getFacultySubjects);
router.get('/faculty/:id/timetable', isAdmin, facultyController.getFacultyTimetable);
router.post('/faculty/:id/status', isAdmin, facultyController.updateFacultyStatus);

module.exports = router;
