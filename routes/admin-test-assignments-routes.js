const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const { formatDate, formatDateTime } = require('../utils/date-formatter');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Middleware to set layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.currentPage = 'test-assignments';
    res.locals.navbar = 'admin';
    next();
});

// Simple test route
router.get('/test', (req, res) => {
    res.send('Test assignments route is working!');
});

// Test Assignments index page
router.get('/', async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const perPage = 10;
        const offset = (page - 1) * perPage;

        // Get query parameters
        const query = {
            search: req.query.search || '',
            type: req.query.type || 'all', // 'user', 'group', or 'all'
            status: req.query.status || 'all', // 'active', 'expired', or 'all'
            sort: req.query.sort || 'newest'
        };

        // Build the WHERE clause
        let whereClause = '1 = 1';
        let queryParams = [];

        if (query.search) {
            whereClause += ` AND (
                e.exam_name LIKE ? OR
                u.username LIKE ? OR
                u.email LIKE ? OR
                g.name LIKE ?
            )`;
            const searchTerm = `%${query.search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        if (query.type === 'user') {
            whereClause += ' AND ta.user_id IS NOT NULL';
        } else if (query.type === 'group') {
            whereClause += ' AND ta.group_id IS NOT NULL';
        }

        if (query.status === 'active') {
            whereClause += ' AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW()) AND ta.is_active = 1';
        } else if (query.status === 'expired') {
            whereClause += ' AND (ta.end_datetime IS NOT NULL AND ta.end_datetime <= NOW() OR ta.is_active = 0)';
        }

        // Build the ORDER BY clause
        let orderBy = '';
        switch (query.sort) {
            case 'oldest':
                orderBy = 'ta.assigned_at ASC';
                break;
            case 'exam_name':
                orderBy = 'e.exam_name ASC';
                break;
            case 'end_date':
                orderBy = 'ta.end_datetime ASC';
                break;
            default: // newest
                orderBy = 'ta.assigned_at DESC';
        }

        // Get total count for pagination and quick filters
        const countQuery = `
            SELECT COUNT(*) as total
            FROM test_assignments ta
            LEFT JOIN users u ON ta.user_id = u.id
            LEFT JOIN groups g ON ta.group_id = g.group_id
            JOIN exams e ON ta.exam_id = e.exam_id
            WHERE ${whereClause}
        `;

        const [countResult] = await db.query(countQuery, queryParams);
        const totalAssignments = countResult[0].total;
        const totalPages = Math.ceil(totalAssignments / perPage);

        // Get counts for quick filters
        const [activeCountResult] = await db.query(`
            SELECT COUNT(*) as active_count
            FROM test_assignments ta
            JOIN exams e ON ta.exam_id = e.exam_id
            WHERE (ta.end_datetime IS NULL OR ta.end_datetime > NOW()) AND ta.is_active = 1
        `);

        const [inactiveCountResult] = await db.query(`
            SELECT COUNT(*) as inactive_count
            FROM test_assignments ta
            JOIN exams e ON ta.exam_id = e.exam_id
            WHERE (ta.end_datetime IS NOT NULL AND ta.end_datetime <= NOW() OR ta.is_active = 0)
        `);

        const activeCount = activeCountResult[0].active_count;
        const inactiveCount = inactiveCountResult[0].inactive_count;

        // Get assignments with pagination
        const assignmentsQuery = `
            SELECT
                ta.*,
                e.exam_name, e.description as exam_description, e.duration,
                u.username as user_username, u.email as user_email,
                g.name as group_name, g.description as group_description,
                admin.username as assigned_by_username,
                (
                    SELECT COUNT(*)
                    FROM exam_attempts ea
                    WHERE ea.exam_id = ta.exam_id AND
                    (
                        (ta.user_id IS NOT NULL AND ea.user_id = ta.user_id) OR
                        (ta.group_id IS NOT NULL AND ea.user_id IN (
                            SELECT user_id FROM group_members WHERE group_id = ta.group_id
                        ))
                    )
                ) as total_attempts
            FROM test_assignments ta
            JOIN exams e ON ta.exam_id = e.exam_id
            LEFT JOIN users u ON ta.user_id = u.id
            LEFT JOIN groups g ON ta.group_id = g.group_id
            LEFT JOIN users admin ON ta.assigned_by = admin.id
            WHERE ${whereClause}
            ORDER BY ${orderBy}
            LIMIT ? OFFSET ?
        `;

        const [assignments] = await db.query(assignmentsQuery, [...queryParams, perPage, offset]);

        // Get all published and scheduled exams for the assignment form (excluding archived tests)
        const [exams] = await db.query(`
            SELECT exam_id, exam_name, status, end_date
            FROM exams
            WHERE is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)
            AND (status = 'published' OR status = 'scheduled') AND status != 'archived'
            AND (end_date IS NULL OR end_date > NOW())
            ORDER BY exam_name ASC
        `);

        // Get all users for the assignment form
        const [users] = await db.query(`
            SELECT id, username, email
            FROM users
            WHERE is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)
            ORDER BY username ASC
        `);

        // Get all groups for the assignment form
        const [groups] = await db.query(`
            SELECT group_id, name
            FROM groups
            ORDER BY name ASC
        `);

        res.render('admin/test-assignments/index', {
            title: 'Test Assignments',
            pageTitle: 'Test Assignments',
            assignments,
            exams,
            users,
            groups,
            query,
            pagination: {
                currentPage: page,
                totalPages,
                totalAssignments,
                perPage
            },
            stats: {
                activeCount,
                inactiveCount,
                totalCount: activeCount + inactiveCount
            },
            formatDate,
            formatDateTime
        });
    } catch (error) {
        console.error('Error fetching test assignments:', error);
        next(error);
    }
});

// Create a new test assignment
router.post('/create', async (req, res) => {
    try {
        console.log('Request headers:', req.headers);
        console.log('Request body:', JSON.stringify(req.body, null, 2));

        // Check if we have the required fields - ENHANCED DEBUGGING
        console.log('Request Content-Type:', req.headers['content-type']);
        console.log('Checking exam_id:', req.body.exam_id, 'Type:', typeof req.body.exam_id);
        console.log('Full request body keys:', Object.keys(req.body));

        // Handle different content types and formats
        let examId;

        if (req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data')) {
            // For multipart form data
            examId = req.body.exam_id;
            console.log('Processing as multipart/form-data, exam_id:', examId);
        } else {
            // For JSON or urlencoded data
            examId = req.body.exam_id;
            console.log('Processing as JSON/urlencoded, exam_id:', examId);
        }

        // Accept any non-empty value for exam_id
        if (examId === undefined || examId === null || examId === '') {
            console.log('Missing or empty exam_id');
            return res.status(400).json({ success: false, message: 'Please select an exam' });
        }

        // Convert to string for consistency
        examId = String(examId);
        console.log('Final exam_id (as string):', examId);

        if (examId === 'undefined') {
            console.log('Invalid exam_id value: "undefined"');
            return res.status(400).json({ success: false, message: 'Please select a valid exam' });
        }

        // Validate that the exam exists - SIMPLIFIED APPROACH
        try {
            console.log('Validating exam with ID:', examId);

            // Direct lookup by ID - no fancy logic, just try to find the exam
            const examQuery = 'SELECT * FROM exams WHERE exam_id = ?';
            console.log('Executing query:', examQuery, 'with param:', examId);

            const [examResults] = await db.query(examQuery, [examId]);
            console.log('Query results count:', examResults.length);

            if (examResults.length === 0) {
                console.log('Exam not found with ID:', examId);
                return res.status(400).json({ success: false, message: 'Selected exam not found' });
            }

            // Use the found exam's ID for the assignment
            const validExamId = examResults[0].exam_id;
            const examName = examResults[0].exam_name;
            console.log('Exam found:', examName, 'with ID:', validExamId);

            // Make sure we're using the correct ID from the database
            req.body.exam_id = validExamId;
            console.log('Using exam_id for assignment:', validExamId);

        } catch (error) {
            console.error('Error validating exam:', error);
            return res.status(500).json({ success: false, message: 'Error validating exam' });
        }

        if (!req.body.assignment_type) {
            console.log('Missing assignment_type');
            return res.status(400).json({ success: false, message: 'Please select an assignment type' });
        }

        // Extract values with fallbacks
        const exam_id = req.body.exam_id;
        const assignment_type = req.body.assignment_type;

        // Handle array fields that might come in different formats
        let user_ids = [];
        if (req.body['user_ids[]']) {
            user_ids = Array.isArray(req.body['user_ids[]']) ? req.body['user_ids[]'] : [req.body['user_ids[]']];
        } else if (req.body.user_ids) {
            user_ids = Array.isArray(req.body.user_ids) ? req.body.user_ids : [req.body.user_ids];
        }

        let group_ids = [];
        if (req.body['group_ids[]']) {
            group_ids = Array.isArray(req.body['group_ids[]']) ? req.body['group_ids[]'] : [req.body['group_ids[]']];
        } else if (req.body.group_ids) {
            group_ids = Array.isArray(req.body.group_ids) ? req.body.group_ids : [req.body.group_ids];
        }

        const max_attempts = req.body.max_attempts || 1;
        const end_datetime = req.body.end_datetime || null;
        const is_resumable = req.body.is_resumable || 'off';

        console.log('Extracted values:', {
            exam_id,
            assignment_type,
            user_ids,
            group_ids,
            max_attempts,
            end_datetime,
            is_resumable
        });

        // Validate based on assignment type
        if (assignment_type === 'user' && (!user_ids || (Array.isArray(user_ids) && user_ids.length === 0))) {
            console.log('Missing user_ids');
            return res.status(400).json({ success: false, message: 'Please select at least one user' });
        }

        if (assignment_type === 'group' && (!group_ids || (Array.isArray(group_ids) && group_ids.length === 0))) {
            console.log('Missing group_ids');
            return res.status(400).json({ success: false, message: 'Please select at least one group' });
        }

        // Log the is_resumable value
        console.log('Is resumable value:', is_resumable);

        // We've already handled the array conversion above, so we can use user_ids and group_ids directly
        const userIdArray = user_ids;
        const groupIdArray = group_ids;

        // Validate required fields
        if (!exam_id || !assignment_type ||
            (assignment_type === 'user' && (!userIdArray || userIdArray.length === 0)) ||
            (assignment_type === 'group' && (!groupIdArray || groupIdArray.length === 0))) {
            req.session.flashError = 'Please fill in all required fields';
            return res.redirect('/admin/test-assignments');
        }

        // Check if the exam exists, is published or scheduled, not archived, and has a valid end date
        const [exams] = await db.query(
            `SELECT * FROM exams
             WHERE exam_id = ?
             AND is_active = 1
             AND (is_deleted = 0 OR is_deleted IS NULL)
             AND (status = 'published' OR status = 'scheduled')
             AND status != 'archived'
             AND (end_date IS NULL OR end_date > NOW())`,
            [exam_id]
        );

        if (exams.length === 0) {
            req.session.flashError = 'Selected exam does not exist, is not published or scheduled, is archived, or has expired';
            return res.redirect('/admin/test-assignments');
        }

        // Validate that end_datetime is in the future if provided
        if (end_datetime) {
            const endDate = new Date(end_datetime);
            const now = new Date();

            if (endDate <= now) {
                req.session.flashError = 'End date must be in the future';
                return res.redirect('/admin/test-assignments');
            }
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            let successCount = 0;
            let updateCount = 0;
            let errorCount = 0;

            if (assignment_type === 'user') {
                // Process each user
                for (const userId of userIdArray) {
                    // Check if assignment already exists
                    const [existingAssignments] = await db.query(
                        `SELECT * FROM test_assignments
                         WHERE exam_id = ? AND user_id = ? AND group_id IS NULL`,
                        [exam_id, userId]
                    );

                    if (existingAssignments.length > 0) {
                        // Get completed attempts count
                        const [attemptCounts] = await db.query(
                            'SELECT COUNT(*) as completed_count FROM exam_attempts WHERE exam_id = ? AND user_id = ? AND status != "in_progress"',
                            [exam_id, userId]
                        );

                        const completedAttempts = attemptCounts[0].completed_count;
                        const currentMaxAttempts = parseInt(existingAssignments[0].max_attempts) || 1;
                        const remainingAttempts = currentMaxAttempts - completedAttempts;

                        // If user still has attempts left, skip this user but continue with others
                        if (remainingAttempts > 0) {
                            errorCount++;
                            continue;
                        }

                        // Convert is_resumable to a boolean value (0 or 1)
                        const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

                        // Update existing assignment
                        await db.query(
                            `UPDATE test_assignments
                             SET max_attempts = ?, end_datetime = ?, is_active = 1, assigned_by = ?, is_resumable = ?
                             WHERE assignment_id = ?`,
                            [
                                max_attempts || 1,
                                end_datetime || null,
                                req.session.userId,
                                isResumable,
                                existingAssignments[0].assignment_id
                            ]
                        );
                        updateCount++;
                    } else {
                        // Convert is_resumable to a boolean value (0 or 1)
                        const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

                        // Create new assignment
                        await db.query(
                            `INSERT INTO test_assignments
                             (exam_id, user_id, group_id, assigned_by, max_attempts, end_datetime, is_resumable)
                             VALUES (?, ?, ?, ?, ?, ?, ?)`,
                            [
                                exam_id,
                                userId,
                                null,
                                req.session.userId,
                                max_attempts || 1,
                                end_datetime || null,
                                isResumable
                            ]
                        );
                        successCount++;
                    }
                }
            } else if (assignment_type === 'group') {
                // Process each group
                for (const groupId of groupIdArray) {
                    // Check if assignment already exists
                    const [existingAssignments] = await db.query(
                        `SELECT * FROM test_assignments
                         WHERE exam_id = ? AND group_id = ? AND user_id IS NULL`,
                        [exam_id, groupId]
                    );

                    if (existingAssignments.length > 0) {
                        // For group assignments, check if any group member has unused attempts
                        const [groupMembers] = await db.query(
                            'SELECT user_id FROM group_members WHERE group_id = ?',
                            [groupId]
                        );

                        let hasUnusedAttempts = false;
                        // Check each group member
                        for (const member of groupMembers) {
                            const [attemptCounts] = await db.query(
                                'SELECT COUNT(*) as completed_count FROM exam_attempts WHERE exam_id = ? AND user_id = ? AND status != "in_progress"',
                                [exam_id, member.user_id]
                            );

                            const completedAttempts = attemptCounts[0].completed_count;
                            const currentMaxAttempts = parseInt(existingAssignments[0].max_attempts) || 1;
                            const remainingAttempts = currentMaxAttempts - completedAttempts;

                            // If any member still has attempts left, skip this group
                            if (remainingAttempts > 0) {
                                hasUnusedAttempts = true;
                                break;
                            }
                        }

                        if (hasUnusedAttempts) {
                            errorCount++;
                            continue;
                        }

                        // Convert is_resumable to a boolean value (0 or 1)
                        const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

                        // Update existing assignment
                        await db.query(
                            `UPDATE test_assignments
                             SET max_attempts = ?, end_datetime = ?, is_active = 1, assigned_by = ?, is_resumable = ?
                             WHERE assignment_id = ?`,
                            [
                                max_attempts || 1,
                                end_datetime || null,
                                req.session.userId,
                                isResumable,
                                existingAssignments[0].assignment_id
                            ]
                        );
                        updateCount++;
                    } else {
                        // Convert is_resumable to a boolean value (0 or 1)
                        const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

                        // Create new assignment
                        await db.query(
                            `INSERT INTO test_assignments
                             (exam_id, user_id, group_id, assigned_by, max_attempts, end_datetime, is_resumable)
                             VALUES (?, ?, ?, ?, ?, ?, ?)`,
                            [
                                exam_id,
                                null,
                                groupId,
                                req.session.userId,
                                max_attempts || 1,
                                end_datetime || null,
                                isResumable
                            ]
                        );
                        successCount++;
                    }
                }
            }

            // Commit transaction
            await db.query('COMMIT');

            // Prepare success message
            let message;
            if (successCount > 0 && errorCount === 0) {
                message = `${successCount} new test assignment(s) created successfully${updateCount > 0 ? ` and ${updateCount} assignment(s) updated` : ''}`;
            } else if (updateCount > 0 && errorCount === 0) {
                message = `${updateCount} test assignment(s) updated successfully`;
            } else if (successCount > 0 || updateCount > 0) {
                message = `${successCount + updateCount} test assignment(s) processed successfully, but ${errorCount} could not be processed due to existing unused attempts`;
            } else {
                return res.json({
                    success: false,
                    message: 'No assignments could be processed. All selected users/groups have existing unused attempts'
                });
            }

            // Return JSON response
            res.json({
                success: true,
                message: message,
                successCount,
                updateCount,
                errorCount
            });
        } catch (error) {
            // Rollback transaction
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error creating test assignment:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating test assignment',
            error: error.message
        });
    }
});

// Update test assignment status
router.post('/:id/toggle-status', async (req, res) => {
    try {
        const { id } = req.params;

        // Get current status and check if the exam is valid for activation
        const [assignments] = await db.query(
            `SELECT ta.*, e.status as exam_status, e.end_date
             FROM test_assignments ta
             JOIN exams e ON ta.exam_id = e.exam_id
             WHERE ta.assignment_id = ?`,
            [id]
        );

        if (assignments.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Assignment not found'
            });
        }

        const assignment = assignments[0];
        const newStatus = assignment.is_active === 1 ? 0 : 1;

        // If trying to activate, check if the exam is published or scheduled, not archived, and not expired
        if (newStatus === 1) {
            if ((assignment.exam_status !== 'published' && assignment.exam_status !== 'scheduled') || assignment.exam_status === 'archived') {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot activate assignment: Test is not published or scheduled, or is archived'
                });
            }

            if (assignment.end_date && new Date(assignment.end_date) <= new Date()) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot activate assignment: Test has expired'
                });
            }
        }

        // Update status
        await db.query(
            'UPDATE test_assignments SET is_active = ? WHERE assignment_id = ?',
            [newStatus, id]
        );

        res.json({
            success: true,
            message: `Assignment ${newStatus === 1 ? 'activated' : 'deactivated'} successfully`,
            newStatus
        });
    } catch (error) {
        console.error('Error toggling assignment status:', error);
        res.status(500).json({
            success: false,
            message: 'Error toggling assignment status'
        });
    }
});

// Delete test assignment
router.post('/:id/delete', async (req, res) => {
    try {
        const { id } = req.params;

        // Delete assignment
        await db.query(
            'DELETE FROM test_assignments WHERE assignment_id = ?',
            [id]
        );

        req.session.flashSuccess = 'Test assignment deleted successfully';
        res.redirect('/admin/test-assignments');
    } catch (error) {
        console.error('Error deleting test assignment:', error);
        req.session.flashError = 'Error deleting test assignment';
        res.redirect('/admin/test-assignments');
    }
});

// Get assigned users and groups for a test
router.get('/exam/:examId/assigned', async (req, res) => {
    try {
        const { examId } = req.params;

        // Get test details - published or scheduled tests
        const [exams] = await db.query(
            'SELECT exam_id, exam_name, status FROM exams WHERE exam_id = ? AND (status = "published" OR status = "scheduled") AND (is_deleted = 0 OR is_deleted IS NULL)',
            [examId]
        );

        if (exams.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Test not found or not in published/scheduled status'
            });
        }

        // Get user assignments
        const [userAssignments] = await db.query(`
            SELECT ta.*, u.username, u.email, u.name,
                   admin.username as assigned_by_username
            FROM test_assignments ta
            JOIN users u ON ta.user_id = u.id
            LEFT JOIN users admin ON ta.assigned_by = admin.id
            WHERE ta.exam_id = ? AND ta.user_id IS NOT NULL
            ORDER BY u.username
        `, [examId]);

        // Get group assignments
        const [groupAssignments] = await db.query(`
            SELECT ta.*, g.name as group_name, g.description as group_description,
                   admin.username as assigned_by_username,
                   (SELECT COUNT(*) FROM group_members WHERE group_id = g.group_id) as member_count
            FROM test_assignments ta
            JOIN groups g ON ta.group_id = g.group_id
            LEFT JOIN users admin ON ta.assigned_by = admin.id
            WHERE ta.exam_id = ? AND ta.group_id IS NOT NULL
            ORDER BY g.name
        `, [examId]);

        res.json({
            success: true,
            exam: exams[0],
            userAssignments,
            groupAssignments
        });
    } catch (error) {
        console.error('Error fetching test assignments:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching test assignments'
        });
    }
});

module.exports = router;
