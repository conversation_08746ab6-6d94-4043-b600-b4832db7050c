const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const path = require('path');
const fs = require('fs');
const groupController = require('../controllers/group-controller');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Add user form
router.get('/add', (req, res) => {
    res.render('admin/users/add', {
        title: 'Add New User',
        pageTitle: 'Add New User',
        currentPage: 'add-user',
        error: null
    });
});

// Add user process
router.post('/add', async (req, res, next) => {
    // Log the request body and files for debugging
    console.log('Admin user routes - Request body:', req.body);
    console.log('Admin user routes - Request files:', req.files);

    // Check if the form was submitted with the correct form_type
    if (req.body.form_type !== 'user_add_form') {
        console.warn('Form type mismatch or missing in user add form');
    }

    try {
        // Process the form data
        const { username, email, password, role, date_of_birth, bio } = req.body;

        // Validate input
        if (!username || !email || !password || !role || !date_of_birth) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'All fields are required'
            });
        }

        // Validate email format
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Invalid email format'
            });
        }

        // Validate password complexity
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        if (!passwordRegex.test(password)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Password must be at least 8 characters with at least one uppercase letter, one number, and one special character'
            });
        }

        // Validate role (must be one of the allowed enum values)
        const allowedRoles = ['admin', 'teacher', 'student'];
        if (!allowedRoles.includes(role)) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Invalid role selected'
            });
        }

        // Truncate bio if it exceeds 500 characters
        const truncatedBio = bio ? bio.substring(0, 500) : '';

        // Check if email already exists
        const [existingUsers] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            [email]
        );

        if (existingUsers.length > 0) {
            return res.render('admin/users/add', {
                title: 'Add New User',
                pageTitle: 'Add New User',
                currentPage: 'add-user',
                error: 'Email already registered'
            });
        }

        // Hash password
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash(password, 10);

        // Handle file upload with express-fileupload
        let profile_image = null;

        if (req.files && req.files.profile_image) {
            const profileImage = req.files.profile_image;

            // Generate a unique filename
            const timestamp = Date.now();
            const ext = path.extname(profileImage.name);
            const filename = `profile_${timestamp}${ext}`;

            // Create uploads directory if it doesn't exist
            const uploadsDir = path.join(__dirname, '../public/uploads/profiles');
            if (!fs.existsSync(uploadsDir)) {
                fs.mkdirSync(uploadsDir, { recursive: true });
            }

            // Move the file to the uploads directory
            const uploadPath = path.join(uploadsDir, filename);

            try {
                await profileImage.mv(uploadPath);
                profile_image = `/uploads/profiles/${filename}`;
            } catch (uploadError) {
                console.error('Error uploading profile image:', uploadError);
                // Continue without the profile image
            }
        }

        // Insert user with all fields
        const [result] = await db.query(
            `INSERT INTO users (
                username, name, email, password, role,
                date_of_birth, bio, last_login, profile_image,
                is_active, is_approved, is_deleted
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, 1, 1, 0)`,
            [
                username,
                username, // Using username as name
                email,
                hashedPassword,
                role,
                date_of_birth,
                truncatedBio || '', // Ensure bio is never null
                profile_image
            ]
        );

        // Add user to role-based group
        const userId = result.insertId;
        try {
            await groupController.addUserToRoleGroup(userId, role);
        } catch (groupError) {
            console.error('Error adding user to role group, but user was created:', groupError);
            // Continue with the user creation process even if group assignment fails
        }

        req.session.flashSuccess = 'User created successfully';
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Error creating user:', error);

        // Provide more specific error messages based on the error
        let errorMessage = 'An error occurred while creating the user.';

        if (error.code === 'ER_DUP_ENTRY') {
            if (error.message.includes('username')) {
                errorMessage = 'Username already exists. Please choose a different username.';
            } else if (error.message.includes('email')) {
                errorMessage = 'Email already exists. Please use a different email address.';
            }
        } else if (error.code === 'ER_NO_REFERENCED_ROW') {
            errorMessage = 'Invalid reference. Please check all fields and try again.';
        } else if (error.code === 'ER_BAD_NULL_ERROR') {
            // Extract the column name from the error message
            const match = error.message.match(/Column '(.+)' cannot be null/);
            const column = match ? match[1] : 'unknown';
            errorMessage = `The ${column} field cannot be empty. Please provide a value.`;
        }

        return res.render('admin/users/add', {
            title: 'Add New User',
            pageTitle: 'Add New User',
            currentPage: 'add-user',
            error: errorMessage
        });
    }
});

module.exports = router;
