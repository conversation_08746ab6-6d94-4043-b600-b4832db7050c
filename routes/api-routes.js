/**
 * API Routes
 */

const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { logJavaScriptVariable, logUserAction } = require('../utils/debug-audit-logger');

// Import notification API routes
const notificationApiRoutes = require('./api/notification-api');

// API endpoint to receive client-side logs
router.post('/audit-log', async (req, res) => {
    try {
        const logData = req.body;

        // Validate the log data
        if (!logData || !logData.type) {
            return res.status(400).json({ success: false, message: 'Invalid log data' });
        }

        // Get user info from session
        const user = req.session ? {
            id: req.session.userId,
            username: req.session.username,
            role: req.session.userRole
        } : null;

        // Process the log data based on its type
        if (logData.type === 'variable') {
            // Log a JavaScript variable
            await logJavaScriptVariable(
                logData.name,
                logData.value,
                `Client-side: ${logData.context}, URL: ${logData.url}`,
                user,
                'info',
                logData.url || req.originalUrl
            );
        } else if (logData.type === 'action') {
            // Log a user action
            await logUserAction(
                req,
                logData.action,
                'client-side',
                logData.details,
                'info'
            );
        }

        // Return success
        res.status(200).json({ success: true });
    } catch (error) {
        console.error('Failed to process client-side log:', error);
        res.status(500).json({ success: false, message: 'Failed to process log' });
    }
});

// Register notification API routes
router.use('/notifications', notificationApiRoutes);

// Register chat API routes
const chatApiRoutes = require('./api/chat-api');
router.use('/chat', chatApiRoutes);

// Register admin notification API routes
const adminNotificationApiRoutes = require('./api/admin-notification-api');
router.use('/admin-notifications', adminNotificationApiRoutes);

// Register calendar API routes
const calendarApiRoutes = require('./api/calendar-api');
router.use('/calendar', calendarApiRoutes);

// Register user API routes
const userApiRoutes = require('./api/user-api');
router.use('/users', userApiRoutes);

// Register role API routes
const roleApiRoutes = require('./api/role-api');
router.use('/admin', roleApiRoutes);

// Register student API routes
const studentApiRoutes = require('./api/student-api');
router.use('/student', studentApiRoutes);

// API endpoint to get question counts
router.get('/counts', async (req, res) => {
    try {
        console.log('API counts endpoint called');

        // Get total questions count
        const [totalQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE is_deleted = 0');
        const totalQuestions = totalQuestionsResult[0].count;

        // Get essays count
        const [essaysResult] = await db.query('SELECT COUNT(*) as count FROM essays');
        const essaysCount = essaysResult[0].count;

        // Get linked questions count
        const [linkedQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE essay_id IS NOT NULL AND is_deleted = 0');
        const linkedQuestionsCount = linkedQuestionsResult[0].count;

        // Get question type counts
        const [questionTypeStats] = await db.query(`
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions
            WHERE is_deleted = 0
            GROUP BY question_type
        `);

        console.log('API counts:', { totalQuestions, essaysCount, linkedQuestionsCount, questionTypeStats });

        // Return the counts as JSON
        res.json({
            totalQuestions,
            totalQuestionsUnfiltered: totalQuestions, // Add this for the quick filter
            essaysCount,
            linkedQuestionsCount,
            questionTypeStats
        });
    } catch (error) {
        console.error('Error fetching counts:', error);
        res.status(500).json({ error: 'Failed to fetch counts' });
    }
});

module.exports = router;
