const express = require('express');
const router = express.Router();
const db = require('../../config/database');

/**
 * Get active admin notifications
 * GET /api/admin-notifications/active
 */
router.get('/active', async (req, res) => {
    try {
        // Get active notifications
        const [notifications] = await db.query(`
            SELECT * FROM admin_notifications
            WHERE is_active = 1
            AND (start_date IS NULL OR start_date <= NOW())
            AND (end_date IS NULL OR end_date >= NOW())
            ORDER BY created_at DESC
        `);

        res.json({
            success: true,
            notifications
        });
    } catch (error) {
        console.error('Error getting active notifications:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting active notifications'
        });
    }
});

/**
 * Create a new admin notification
 * POST /api/admin-notifications/create
 */
router.post('/create', async (req, res) => {
    try {
        // Check if user is admin
        if (req.session.userRole !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const { title, message, startDate, endDate } = req.body;
        const createdBy = req.session.userId;

        // Validate required fields
        if (!title || !message) {
            return res.status(400).json({
                success: false,
                message: 'Title and message are required'
            });
        }

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Create admin notification
            const [result] = await db.query(`
                INSERT INTO admin_notifications (title, message, created_by, start_date, end_date)
                VALUES (?, ?, ?, ?, ?)
            `, [title, message, createdBy, startDate || null, endDate || null]);

            const adminNotificationId = result.insertId;

            // Get all users to create individual notifications for them
            const [users] = await db.query(`
                SELECT id FROM users
            `);

            // Create individual notifications for each user
            for (const user of users) {
                await db.query(`
                    INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
                    VALUES (?, ?, ?, 'admin', ?, 0, NOW())
                `, [user.id, title, message, `/notifications`]);
            }

            // Commit the transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                notificationId: adminNotificationId
            });
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error creating admin notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error creating admin notification'
        });
    }
});

/**
 * Update an admin notification
 * PUT /api/admin-notifications/:id
 */
router.put('/:id', async (req, res) => {
    try {
        // Check if user is admin
        if (req.session.userRole !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const notificationId = parseInt(req.params.id);
        const { title, message, isActive, startDate, endDate } = req.body;

        // Validate required fields
        if (!title || !message) {
            return res.status(400).json({
                success: false,
                message: 'Title and message are required'
            });
        }

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Get the original notification to check if title or message changed
            const [originalNotification] = await db.query(`
                SELECT title, message, created_at FROM admin_notifications WHERE notification_id = ?
            `, [notificationId]);

            if (originalNotification.length === 0) {
                await db.query('ROLLBACK');
                return res.status(404).json({
                    success: false,
                    message: 'Notification not found'
                });
            }

            const original = originalNotification[0];
            const titleChanged = original.title !== title;
            const messageChanged = original.message !== message;

            // Update admin notification
            await db.query(`
                UPDATE admin_notifications
                SET title = ?, message = ?, is_active = ?, start_date = ?, end_date = ?
                WHERE notification_id = ?
            `, [title, message, isActive ? 1 : 0, startDate || null, endDate || null, notificationId]);

            // If title or message changed, update user notifications
            if (titleChanged || messageChanged) {
                // Update all user notifications with the same timestamp (approximate match)
                await db.query(`
                    UPDATE notifications
                    SET title = ?, message = ?
                    WHERE type = 'admin' AND DATE(created_at) = DATE(?)
                `, [title, message, original.created_at]);
            }

            // Commit the transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                message: 'Notification updated successfully'
            });
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error updating admin notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error updating admin notification'
        });
    }
});

/**
 * Delete an admin notification
 * DELETE /api/admin-notifications/:id
 */
router.delete('/:id', async (req, res) => {
    try {
        // Check if user is admin
        if (req.session.userRole !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const notificationId = parseInt(req.params.id);

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Get the notification to find related user notifications
            const [notification] = await db.query(`
                SELECT created_at FROM admin_notifications WHERE notification_id = ?
            `, [notificationId]);

            if (notification.length > 0) {
                const createdAt = notification[0].created_at;

                // Delete related user notifications
                await db.query(`
                    DELETE FROM notifications
                    WHERE type = 'admin' AND DATE(created_at) = DATE(?)
                `, [createdAt]);
            }

            // Delete admin notification
            await db.query(`
                DELETE FROM admin_notifications
                WHERE notification_id = ?
            `, [notificationId]);

            // Commit the transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                message: 'Notification deleted successfully'
            });
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error deleting admin notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting admin notification'
        });
    }
});

/**
 * Toggle notification active status
 * POST /api/admin-notifications/:id/toggle
 */
router.post('/:id/toggle', async (req, res) => {
    try {
        // Check if user is admin
        if (req.session.userRole !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const notificationId = parseInt(req.params.id);

        // Get current status
        const [notifications] = await db.query(`
            SELECT is_active FROM admin_notifications
            WHERE notification_id = ?
        `, [notificationId]);

        if (notifications.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        const isActive = notifications[0].is_active;

        // Toggle status
        await db.query(`
            UPDATE admin_notifications
            SET is_active = ?
            WHERE notification_id = ?
        `, [isActive ? 0 : 1, notificationId]);

        res.json({
            success: true,
            isActive: !isActive
        });
    } catch (error) {
        console.error('Error toggling notification status:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error toggling notification status'
        });
    }
});

module.exports = router;
