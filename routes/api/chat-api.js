const express = require('express');
const router = express.Router();
const db = require('../../config/database');

/**
 * Get all users for chat
 * GET /api/chat/users
 */
router.get('/users', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get all users except the current user
        const [users] = await db.query(`
            SELECT id, username, role, profile_image,
                   (SELECT COUNT(*) FROM active_sessions WHERE user_id = users.id AND is_active = 1) > 0 as is_online
            FROM users
            WHERE id != ? AND is_active = 1 AND is_blocked = 0
            ORDER BY username ASC
        `, [req.session.userId]);

        res.json({
            success: true,
            users
        });
    } catch (error) {
        console.error('Error getting users for chat:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting users for chat'
        });
    }
});

/**
 * Get all groups for chat
 * GET /api/chat/groups
 */
router.get('/groups', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get all groups the user is a member of
        const [groups] = await db.query(`
            SELECT g.*, gm.is_admin,
                   (SELECT COUNT(*) FROM group_members WHERE group_id = g.group_id) as member_count
            FROM groups g
            JOIN group_members gm ON g.group_id = gm.group_id
            WHERE gm.user_id = ?
            ORDER BY g.name ASC
        `, [req.session.userId]);

        res.json({
            success: true,
            groups
        });
    } catch (error) {
        console.error('Error getting groups for chat:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting groups for chat'
        });
    }
});

/**
 * Get unread message count
 * GET /api/chat/unread
 */
router.get('/unread', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get unread chat messages count
        let result;
        try {
            // Try to get both private and group messages
            [result] = await db.query(`
                SELECT
                    (SELECT COUNT(*) FROM chat_messages WHERE recipient_id = ? AND is_read = 0) +
                    (SELECT COUNT(*) FROM group_messages gm
                     LEFT JOIN group_message_reads gmr ON gm.message_id = gmr.message_id AND gmr.user_id = ?
                     JOIN group_members gme ON gm.group_id = gme.group_id AND gme.user_id = ?
                     WHERE gmr.read_at IS NULL AND gm.sender_id != ?) as count
            `, [req.session.userId, req.session.userId, req.session.userId, req.session.userId]);
        } catch (error) {
            try {
                // If there's an error (likely because group_messages table doesn't exist),
                // just get private messages
                [result] = await db.query(`
                    SELECT COUNT(*) as count FROM chat_messages WHERE recipient_id = ? AND is_read = 0
                `, [req.session.userId]);
            } catch (innerError) {
                // If there's still an error (likely because chat_messages table doesn't exist),
                // return 0
                result = [{ count: 0 }];
            }
        }

        res.json({
            success: true,
            count: result[0].count
        });
    } catch (error) {
        console.error('Error getting unread message count:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting unread message count'
        });
    }
});

/**
 * Get unread messages
 * GET /api/chat/messages/unread
 */
router.get('/messages/unread', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get unread private messages
        const [privateMessages] = await db.query(`
            SELECT
                cm.*,
                u.username as sender_name,
                u.profile_image as sender_image,
                NULL as group_name,
                NULL as group_id
            FROM chat_messages cm
            JOIN users u ON cm.sender_id = u.id
            WHERE cm.recipient_id = ?
            ORDER BY cm.created_at DESC
        `, [req.session.userId]);

        // Get unread group messages
        const [groupMessages] = await db.query(`
            SELECT
                cm.*,
                u.username as sender_name,
                u.profile_image as sender_image,
                g.name as group_name,
                g.group_id
            FROM chat_messages cm
            JOIN users u ON cm.sender_id = u.id
            JOIN groups g ON cm.group_id = g.group_id
            WHERE cm.group_id IN (
                SELECT group_id FROM group_members WHERE user_id = ?
            )
            ORDER BY cm.created_at DESC
        `, [req.session.userId]);

        // Combine messages
        const messages = [...privateMessages, ...groupMessages].sort((a, b) => {
            return new Date(b.created_at) - new Date(a.created_at);
        });

        res.json({
            success: true,
            messages
        });
    } catch (error) {
        console.error('Error getting unread messages:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting unread messages'
        });
    }
});

/**
 * Mark message as read
 * POST /api/chat/messages/:messageId/read
 */
router.post('/messages/:messageId/read', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const messageId = parseInt(req.params.messageId);

        // Mark message as read
        await db.query(`
            UPDATE message_status
            SET is_read = 1, read_at = NOW()
            WHERE message_id = ? AND user_id = ?
        `, [messageId, req.session.userId]);

        res.json({
            success: true
        });
    } catch (error) {
        console.error('Error marking message as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking message as read'
        });
    }
});

/**
 * Mark all messages as read
 * POST /api/chat/messages/read-all
 */
router.post('/messages/read-all', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Mark all messages as read
        await db.query(`
            UPDATE message_status
            SET is_read = 1, read_at = NOW()
            WHERE user_id = ? AND is_read = 0
        `, [req.session.userId]);

        res.json({
            success: true
        });
    } catch (error) {
        console.error('Error marking all messages as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking all messages as read'
        });
    }
});

/**
 * Get recent chats
 * GET /api/chat/recent
 */
router.get('/recent', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get recent private chats
        const [privateChats] = await db.query(`
            SELECT
                DISTINCT IF(cm.sender_id = ?, cm.recipient_id, cm.sender_id) as user_id,
                u.username,
                u.profile_image,
                (SELECT COUNT(*) FROM message_status ms
                 JOIN chat_messages cm2 ON ms.message_id = cm2.message_id
                 WHERE ms.user_id = ? AND ms.is_read = 0
                 AND ((cm2.sender_id = u.id AND cm2.recipient_id = ?)
                      OR (cm2.recipient_id = u.id AND cm2.sender_id = ?))
                ) as unread_count,
                (SELECT cm3.message FROM chat_messages cm3
                 WHERE (cm3.sender_id = ? AND cm3.recipient_id = u.id)
                    OR (cm3.recipient_id = ? AND cm3.sender_id = u.id)
                 ORDER BY cm3.created_at DESC LIMIT 1
                ) as last_message,
                (SELECT cm4.created_at FROM chat_messages cm4
                 WHERE (cm4.sender_id = ? AND cm4.recipient_id = u.id)
                    OR (cm4.recipient_id = ? AND cm4.sender_id = u.id)
                 ORDER BY cm4.created_at DESC LIMIT 1
                ) as last_message_time
            FROM chat_messages cm
            JOIN users u ON u.id = IF(cm.sender_id = ?, cm.recipient_id, cm.sender_id)
            WHERE (cm.sender_id = ? AND cm.recipient_id IS NOT NULL)
               OR (cm.recipient_id = ? AND cm.sender_id IS NOT NULL)
            ORDER BY last_message_time DESC
            LIMIT 5
        `, [
            req.session.userId, req.session.userId, req.session.userId, req.session.userId,
            req.session.userId, req.session.userId, req.session.userId, req.session.userId,
            req.session.userId, req.session.userId, req.session.userId
        ]);

        // Get recent group chats
        const [groupChats] = await db.query(`
            SELECT
                g.group_id,
                g.name,
                (SELECT COUNT(*) FROM message_status ms
                 JOIN chat_messages cm ON ms.message_id = cm.message_id
                 WHERE ms.user_id = ? AND ms.is_read = 0 AND cm.group_id = g.group_id
                ) as unread_count,
                (SELECT cm.message FROM chat_messages cm
                 WHERE cm.group_id = g.group_id
                 ORDER BY cm.created_at DESC LIMIT 1
                ) as last_message,
                (SELECT cm.created_at FROM chat_messages cm
                 WHERE cm.group_id = g.group_id
                 ORDER BY cm.created_at DESC LIMIT 1
                ) as last_message_time,
                (SELECT u.username FROM chat_messages cm
                 JOIN users u ON cm.sender_id = u.id
                 WHERE cm.group_id = g.group_id
                 ORDER BY cm.created_at DESC LIMIT 1
                ) as last_sender
            FROM groups g
            JOIN group_members gm ON g.group_id = gm.group_id
            WHERE gm.user_id = ?
            ORDER BY last_message_time DESC
            LIMIT 5
        `, [req.session.userId, req.session.userId]);

        res.json({
            success: true,
            privateChats,
            groupChats
        });
    } catch (error) {
        console.error('Error getting recent chats:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting recent chats'
        });
    }
});

/**
 * Get user chat messages
 * GET /api/chat/messages/user/:userId
 */
router.get('/messages/user/:userId', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const userId = parseInt(req.params.userId);
        const currentUserId = req.session.userId;

        // Get messages between users
        const [messages] = await db.query(`
            SELECT m.id, m.sender_id, u.username as sender_name, m.message, m.created_at
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE (
                (m.sender_id = ? AND m.recipient_id = ? AND m.recipient_type = 'user') OR
                (m.sender_id = ? AND m.recipient_id = ? AND m.recipient_type = 'user')
            )
            ORDER BY m.created_at ASC
        `, [currentUserId, userId, userId, currentUserId]);

        res.json({
            success: true,
            messages
        });
    } catch (error) {
        console.error('Error getting user chat messages:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting user chat messages'
        });
    }
});

/**
 * Get group chat messages
 * GET /api/chat/messages/group/:groupId
 */
router.get('/messages/group/:groupId', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const groupId = parseInt(req.params.groupId);
        const currentUserId = req.session.userId;

        // Check if user is a member of the group
        const [membership] = await db.query(`
            SELECT 1 FROM group_members
            WHERE group_id = ? AND user_id = ?
        `, [groupId, currentUserId]);

        if (membership.length === 0) {
            return res.status(403).json({
                success: false,
                message: 'You are not a member of this group'
            });
        }

        // Get group messages
        const [messages] = await db.query(`
            SELECT m.id, m.sender_id, u.username as sender_name, m.message, m.created_at
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE m.recipient_id = ? AND m.recipient_type = 'group'
            ORDER BY m.created_at ASC
        `, [groupId]);

        res.json({
            success: true,
            messages
        });
    } catch (error) {
        console.error('Error getting group chat messages:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting group chat messages'
        });
    }
});

/**
 * Mark user messages as read
 * POST /api/chat/messages/user/:userId/read
 */
router.post('/messages/user/:userId/read', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const userId = parseInt(req.params.userId);
        const currentUserId = req.session.userId;

        // For chat_messages table, we don't have a separate message_status table
        // Instead, we can mark messages as read in a different way if needed
        // This is a placeholder for future implementation

        res.json({
            success: true
        });
    } catch (error) {
        console.error('Error marking user messages as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking user messages as read'
        });
    }
});

/**
 * Mark group messages as read
 * POST /api/chat/messages/group/:groupId/read
 */
router.post('/messages/group/:groupId/read', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const groupId = parseInt(req.params.groupId);
        const currentUserId = req.session.userId;

        // Check if user is a member of the group
        const [membership] = await db.query(`
            SELECT 1 FROM group_members
            WHERE group_id = ? AND user_id = ?
        `, [groupId, currentUserId]);

        if (membership.length === 0) {
            return res.status(403).json({
                success: false,
                message: 'You are not a member of this group'
            });
        }

        // Mark messages as read
        await db.query(`
            UPDATE message_status ms
            JOIN messages m ON ms.message_id = m.id
            SET ms.is_read = 1, ms.read_at = NOW()
            WHERE ms.user_id = ? AND m.recipient_id = ? AND m.recipient_type = 'group' AND ms.is_read = 0
        `, [currentUserId, groupId]);

        res.json({
            success: true
        });
    } catch (error) {
        console.error('Error marking group messages as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking group messages as read'
        });
    }
});

/**
 * Send message to user
 * POST /api/chat/messages/user/:userId/send
 */
router.post('/messages/user/:userId/send', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const userId = parseInt(req.params.userId);
        const currentUserId = req.session.userId;
        const { message } = req.body;

        if (!message || message.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Message cannot be empty'
            });
        }

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Insert message
            const [result] = await db.query(`
                INSERT INTO chat_messages (sender_id, recipient_id, message, created_at)
                VALUES (?, ?, ?, NOW())
            `, [currentUserId, userId, message]);

            const messageId = result.insertId;

            // No need to create message status for recipient as we're using the chat_messages table

            // Commit the transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                messageId
            });
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error sending message to user:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error sending message to user'
        });
    }
});

/**
 * Send message to group
 * POST /api/chat/messages/group/:groupId/send
 */
router.post('/messages/group/:groupId/send', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const groupId = parseInt(req.params.groupId);
        const currentUserId = req.session.userId;
        const { message } = req.body;

        if (!message || message.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Message cannot be empty'
            });
        }

        // Check if user is a member of the group
        const [membership] = await db.query(`
            SELECT 1 FROM group_members
            WHERE group_id = ? AND user_id = ?
        `, [groupId, currentUserId]);

        if (membership.length === 0) {
            return res.status(403).json({
                success: false,
                message: 'You are not a member of this group'
            });
        }

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Insert message
            const [result] = await db.query(`
                INSERT INTO chat_messages (sender_id, group_id, message, created_at)
                VALUES (?, ?, ?, NOW())
            `, [currentUserId, groupId, message]);

            const messageId = result.insertId;

            // No need to create message status for group members as we're using the chat_messages table

            // Commit the transaction
            await db.query('COMMIT');

            res.json({
                success: true,
                messageId
            });
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error sending message to group:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error sending message to group'
        });
    }
});

module.exports = router;
