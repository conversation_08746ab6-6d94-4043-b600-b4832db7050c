const express = require('express');
const router = express.Router();
const db = require('../../config/database');

/**
 * Create a new notification
 * POST /api/notifications/create
 */
router.post('/create', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const { message, type = 'info', link = null, title = null } = req.body;

        // Validate required fields
        if (!message) {
            return res.status(400).json({
                success: false,
                message: 'Message is required'
            });
        }

        // Create notification in database
        await db.query(
            'INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at) VALUES (?, ?, ?, ?, ?, 0, NOW())',
            [req.session.userId, title || message.substring(0, 50), message, type, link]
        );

        res.json({
            success: true,
            message: 'Notification created successfully'
        });
    } catch (error) {
        console.error('Error creating notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error creating notification'
        });
    }
});

/**
 * Get all notifications for the current user
 * GET /api/notifications
 */
router.get('/', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        // Get notifications from database
        const [notifications] = await db.query(
            `SELECT n.*, DATE_FORMAT(n.created_at, '%d-%b-%Y %H:%i:%s') as formatted_date
             FROM notifications n
             WHERE n.user_id = ?
             ORDER BY n.created_at DESC`,
            [req.session.userId]
        );

        res.json({
            success: true,
            notifications
        });
    } catch (error) {
        console.error('Error fetching notifications:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error fetching notifications'
        });
    }
});

/**
 * Mark a notification as read
 * POST /api/notifications/:id/read
 */
router.post('/:id/read', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const notificationId = req.params.id;

        // Verify the notification belongs to the user
        const [notification] = await db.query(
            'SELECT * FROM notifications WHERE id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (notification.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        // Mark as read
        await db.query(
            'UPDATE notifications SET is_read = 1 WHERE id = ?',
            [notificationId]
        );

        res.json({
            success: true,
            message: 'Notification marked as read'
        });
    } catch (error) {
        console.error('Error marking notification as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking notification as read'
        });
    }
});

/**
 * Delete a notification
 * POST /api/notifications/:id/delete
 */
router.post('/:id/delete', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }

        const notificationId = req.params.id;

        // Verify the notification belongs to the user
        const [notification] = await db.query(
            'SELECT * FROM notifications WHERE id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (notification.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        // Delete notification
        await db.query(
            'DELETE FROM notifications WHERE id = ?',
            [notificationId]
        );

        res.json({
            success: true,
            message: 'Notification deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting notification'
        });
    }
});

/**
 * Get unread notification count
 * GET /api/notifications/unread
 */
router.get('/unread', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        // Get unread notification count
        const [result] = await db.query(
            'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0',
            [req.session.userId]
        );

        res.json({
            success: true,
            count: result[0].count
        });
    } catch (error) {
        console.error('Error getting unread notification count:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting unread notification count'
        });
    }
});

/**
 * Get broadcast notifications for the current user
 * GET /api/notifications/broadcast
 */
router.get('/broadcast', async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.session.userId) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        // Check if is_broadcast column exists
        const [columns] = await db.query(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'exam_prep_platform'
            AND TABLE_NAME = 'notifications'
            AND COLUMN_NAME = 'is_broadcast'
        `);

        let notifications;

        if (columns.length > 0) {
            // Get unread broadcast notifications if column exists
            [notifications] = await db.query(
                `SELECT * FROM notifications
                 WHERE user_id = ? AND is_read = 0 AND is_broadcast = 1
                 ORDER BY created_at DESC`,
                [req.session.userId]
            );
        } else {
            // If column doesn't exist, return empty array
            notifications = [];

            // Log warning
            console.warn('is_broadcast column does not exist in notifications table. Run fix-notifications-table.js to fix this issue.');
        }

        res.json({
            success: true,
            notifications
        });
    } catch (error) {
        console.error('Error getting broadcast notifications:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error getting broadcast notifications'
        });
    }
});

module.exports = router;
