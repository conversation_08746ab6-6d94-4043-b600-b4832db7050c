const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { checkAdmin } = require('../../middleware/auth');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Get all roles
router.get('/roles', async (req, res) => {
    try {
        // Check if roles table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'roles'
        `);

        let roles = [];

        if (tables.length > 0) {
            // Get all roles with user counts
            const [rolesResult] = await db.query(`
                SELECT r.role_id, r.role_name, r.description, r.is_system,
                       COUNT(u.id) as user_count
                FROM roles r
                LEFT JOIN users u ON r.role_name = u.role
                GROUP BY r.role_id
                ORDER BY r.role_name
            `);

            roles = rolesResult;
        } else {
            // If roles table doesn't exist, get roles from users table
            const [userRoles] = await db.query(`
                SELECT role as role_name, COUNT(*) as user_count
                FROM users
                GROUP BY role
                ORDER BY role
            `);

            // Format roles to match the expected structure
            roles = userRoles.map(role => ({
                role_id: 0,
                role_name: role.role_name,
                description: role.role_name === 'admin' ? 'Administrator with full system access' :
                            role.role_name === 'teacher' ? 'Teacher with access to create and manage tests' :
                            'Student with access to take tests',
                is_system: true,
                user_count: role.user_count
            }));
        }

        res.json({
            success: true,
            roles
        });
    } catch (error) {
        console.error('Error fetching roles:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch roles',
            error: error.message
        });
    }
});

// Get all permissions
router.get('/permissions', async (req, res) => {
    try {
        // Check if permissions table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'permissions'
        `);

        let permissions = {};

        if (tables.length > 0) {
            // Get all permissions
            const [permissionsResult] = await db.query(`
                SELECT permission_id, permission_name, description, category
                FROM permissions
                ORDER BY category, permission_name
            `);

            // Group permissions by category
            permissions = permissionsResult.reduce((acc, permission) => {
                if (!acc[permission.category]) {
                    acc[permission.category] = [];
                }
                acc[permission.category].push(permission);
                return acc;
            }, {});
        } else {
            // If permissions table doesn't exist, return empty object
            permissions = {
                'System': [
                    { permission_id: 1, permission_name: 'View Dashboard', description: 'Access to view the dashboard', category: 'System' },
                    { permission_id: 2, permission_name: 'Manage Users', description: 'Access to manage users', category: 'System' },
                    { permission_id: 3, permission_name: 'Manage Roles', description: 'Access to manage roles', category: 'System' }
                ],
                'Tests': [
                    { permission_id: 4, permission_name: 'Create Tests', description: 'Access to create tests', category: 'Tests' },
                    { permission_id: 5, permission_name: 'Edit Tests', description: 'Access to edit tests', category: 'Tests' },
                    { permission_id: 6, permission_name: 'Delete Tests', description: 'Access to delete tests', category: 'Tests' },
                    { permission_id: 7, permission_name: 'Assign Tests', description: 'Access to assign tests to users', category: 'Tests' }
                ],
                'Questions': [
                    { permission_id: 8, permission_name: 'Create Questions', description: 'Access to create questions', category: 'Questions' },
                    { permission_id: 9, permission_name: 'Edit Questions', description: 'Access to edit questions', category: 'Questions' },
                    { permission_id: 10, permission_name: 'Delete Questions', description: 'Access to delete questions', category: 'Questions' }
                ]
            };
        }

        res.json({
            success: true,
            permissions
        });
    } catch (error) {
        console.error('Error fetching permissions:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch permissions',
            error: error.message
        });
    }
});

// Get users with a specific role
router.get('/roles/:id/users', async (req, res) => {
    try {
        const roleId = req.params.id;

        // Get role details
        const [roles] = await db.query(`
            SELECT role_id, role_name, description
            FROM roles
            WHERE role_id = ?
        `, [roleId]);

        if (roles.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Role not found'
            });
        }

        const role = roles[0];

        // Get users with this role
        const [users] = await db.query(`
            SELECT id, username, email, profile_image, is_blocked, is_online, created_at
            FROM users
            WHERE role = ?
            ORDER BY username
        `, [role.role_name]);

        res.json({
            success: true,
            role_name: role.role_name,
            users
        });
    } catch (error) {
        console.error('Error fetching users with role:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users with role',
            error: error.message
        });
    }
});

// Get role permissions
router.get('/roles/:id/permissions', async (req, res) => {
    try {
        const roleId = req.params.id;

        // Check if role_permissions table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'role_permissions'
        `);

        let rolePermissions = [];
        let permissions = {};

        // Get all permissions
        const [permissionsResult] = await db.query(`
            SELECT permission_id, permission_name, description, category
            FROM permissions
            ORDER BY category, permission_name
        `);

        // Group permissions by category
        permissions = permissionsResult.reduce((acc, permission) => {
            if (!acc[permission.category]) {
                acc[permission.category] = [];
            }
            acc[permission.category].push(permission);
            return acc;
        }, {});

        if (tables.length > 0) {
            // Get role permissions
            const [rolePermissionsResult] = await db.query(`
                SELECT permission_id
                FROM role_permissions
                WHERE role_id = ?
            `, [roleId]);

            rolePermissions = rolePermissionsResult.map(rp => rp.permission_id);
        }

        res.json({
            success: true,
            permissions,
            rolePermissions
        });
    } catch (error) {
        console.error('Error fetching role permissions:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch role permissions',
            error: error.message
        });
    }
});

// Create role
router.post('/roles/create', async (req, res) => {
    try {
        const { role_name, description, permissions } = req.body;

        if (!role_name) {
            return res.status(400).json({
                success: false,
                message: 'Role name is required'
            });
        }

        // Check if roles table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'roles'
        `);

        if (tables.length === 0) {
            // Create roles table if it doesn't exist
            await db.query(`
                CREATE TABLE roles (
                    role_id INT AUTO_INCREMENT PRIMARY KEY,
                    role_name VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    is_system BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);

            // Create permissions table if it doesn't exist
            await db.query(`
                CREATE TABLE IF NOT EXISTS permissions (
                    permission_id INT AUTO_INCREMENT PRIMARY KEY,
                    permission_name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    category VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Create role_permissions table if it doesn't exist
            await db.query(`
                CREATE TABLE IF NOT EXISTS role_permissions (
                    role_permission_id INT AUTO_INCREMENT PRIMARY KEY,
                    role_id INT NOT NULL,
                    permission_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY role_permission_unique (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
                )
            `);

            // Insert default permissions
            await db.query(`
                INSERT INTO permissions (permission_name, description, category)
                VALUES
                ('View Dashboard', 'Access to view the dashboard', 'System'),
                ('Manage Users', 'Access to manage users', 'System'),
                ('Manage Roles', 'Access to manage roles', 'System'),
                ('Create Tests', 'Access to create tests', 'Tests'),
                ('Edit Tests', 'Access to edit tests', 'Tests'),
                ('Delete Tests', 'Access to delete tests', 'Tests'),
                ('Assign Tests', 'Access to assign tests to users', 'Tests'),
                ('Create Questions', 'Access to create questions', 'Questions'),
                ('Edit Questions', 'Access to edit questions', 'Questions'),
                ('Delete Questions', 'Access to delete questions', 'Questions')
            `);

            // Insert default roles
            await db.query(`
                INSERT INTO roles (role_name, description, is_system)
                VALUES
                ('admin', 'Administrator with full system access', TRUE),
                ('teacher', 'Teacher with access to create and manage tests', TRUE),
                ('student', 'Student with access to take tests', TRUE)
            `);
        }

        // Check if role already exists
        const [existingRoles] = await db.query(`
            SELECT role_id FROM roles WHERE role_name = ?
        `, [role_name]);

        if (existingRoles.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'Role with this name already exists'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Insert role
        const [result] = await db.query(`
            INSERT INTO roles (role_name, description, is_system)
            VALUES (?, ?, FALSE)
        `, [role_name, description || null]);

        const roleId = result.insertId;

        // Insert role permissions
        if (permissions && permissions.length > 0) {
            const values = permissions.map(permissionId => [roleId, permissionId]);

            await db.query(`
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ?
            `, [values]);
        }

        // Commit transaction
        await db.query('COMMIT');

        res.json({
            success: true,
            message: 'Role created successfully',
            roleId
        });
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');

        console.error('Error creating role:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create role',
            error: error.message
        });
    }
});

// Update role
router.post('/roles/:id/update', async (req, res) => {
    try {
        const roleId = req.params.id;
        const { role_name, description, permissions } = req.body;

        if (!role_name) {
            return res.status(400).json({
                success: false,
                message: 'Role name is required'
            });
        }

        // Check if role exists
        const [roles] = await db.query(`
            SELECT * FROM roles WHERE role_id = ?
        `, [roleId]);

        if (roles.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Role not found'
            });
        }

        const role = roles[0];

        // Check if role is system role
        if (role.is_system) {
            return res.status(400).json({
                success: false,
                message: 'System roles cannot be modified'
            });
        }

        // Check if role name already exists (excluding current role)
        const [existingRoles] = await db.query(`
            SELECT role_id FROM roles WHERE role_name = ? AND role_id != ?
        `, [role_name, roleId]);

        if (existingRoles.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'Role with this name already exists'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Update role
        await db.query(`
            UPDATE roles
            SET role_name = ?, description = ?
            WHERE role_id = ?
        `, [role_name, description || null, roleId]);

        // Delete existing role permissions
        await db.query(`
            DELETE FROM role_permissions
            WHERE role_id = ?
        `, [roleId]);

        // Insert new role permissions
        if (permissions && permissions.length > 0) {
            const values = permissions.map(permissionId => [roleId, permissionId]);

            await db.query(`
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ?
            `, [values]);
        }

        // Commit transaction
        await db.query('COMMIT');

        res.json({
            success: true,
            message: 'Role updated successfully'
        });
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');

        console.error('Error updating role:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update role',
            error: error.message
        });
    }
});

// Delete role
router.post('/roles/:id/delete', async (req, res) => {
    try {
        const roleId = req.params.id;

        // Check if role exists
        const [roles] = await db.query(`
            SELECT * FROM roles WHERE role_id = ?
        `, [roleId]);

        if (roles.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Role not found'
            });
        }

        const role = roles[0];

        // Check if role is system role
        if (role.is_system) {
            return res.status(400).json({
                success: false,
                message: 'System roles cannot be deleted'
            });
        }

        // Check if role has users
        const [users] = await db.query(`
            SELECT COUNT(*) as user_count
            FROM users
            WHERE role = ?
        `, [role.role_name]);

        if (users[0].user_count > 0) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete role with assigned users'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Delete role permissions
        await db.query(`
            DELETE FROM role_permissions
            WHERE role_id = ?
        `, [roleId]);

        // Delete role
        await db.query(`
            DELETE FROM roles
            WHERE role_id = ?
        `, [roleId]);

        // Commit transaction
        await db.query('COMMIT');

        res.json({
            success: true,
            message: 'Role deleted successfully'
        });
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');

        console.error('Error deleting role:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete role',
            error: error.message
        });
    }
});

module.exports = router;
