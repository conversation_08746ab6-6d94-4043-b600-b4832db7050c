/**
 * Student API Routes
 */

const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { checkAuthenticated, isStudent } = require('../../middleware/auth');

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(isStudent);

/**
 * Get student class information
 * GET /api/student/class-info
 */
router.get('/class-info', async (req, res) => {
  try {
    const studentId = req.session.userId;

    // Get student's classroom information with grade, section, and trade
    const [classInfo] = await db.query(`
      SELECT
        cr.id as classroom_id, c.id as class_id, c.name, c.grade, 
        cr.section, t.name as trade_name, cr.session as academic_year,
        cr.room_number,
        CONCAT(c.grade, ' ', t.name, ' ', cr.section) as full_class_name
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    `, [studentId]);
    
    // If no classroom found, try the old way with student_classes
    if (classInfo.length === 0) {
      const [oldClassInfo] = await db.query(`
        SELECT
          c.id, c.name, c.grade, c.trade, c.section, c.academic_year,
          CONCAT(c.grade, ' ', c.name, ' ', c.section) as full_class_name
        FROM classes c
        JOIN student_classes sc ON c.id = sc.class_id
        WHERE sc.student_id = ?
        LIMIT 1
      `, [studentId]);
      
      if (oldClassInfo.length > 0) {
        classInfo.push(oldClassInfo[0]);
      }
    }

    if (classInfo.length === 0) {
      // If no class is assigned, get the student's username to display
      const [userInfo] = await db.query(`
        SELECT username, email
        FROM users
        WHERE id = ?
      `, [studentId]);

      if (userInfo.length === 0) {
        return res.json({
          success: false,
          message: 'Student not found'
        });
      }

      // Return default values
      return res.json({
        success: true,
        classInfo: {
          id: null,
          name: 'Not Assigned',
          grade: 'N/A',
          trade: 'N/A',
          section: 'N/A',
          stream: 'N/A',
          academic_year: new Date().getFullYear().toString(),
          full_class_name: 'Class Not Assigned',
          username: userInfo[0].username,
          email: userInfo[0].email
        }
      });
    }

    res.json({
      success: true,
      classInfo: classInfo[0]
    });
  } catch (error) {
    console.error('Error fetching student class info:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student class information'
    });
  }
});

/**
 * Get student's classes
 * GET /api/student/classes
 */
router.get('/classes', async (req, res) => {
  try {
    const studentId = req.session.userId;

    // Get student's classes
    const [classes] = await db.query(`
      SELECT c.id, c.name, c.grade, c.trade, c.section, c.stream
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);

    res.json({
      success: true,
      classes
    });
  } catch (error) {
    console.error('Error fetching student classes:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student classes'
    });
  }
});


// Get student subjects by trade
router.get('/subjects-by-trade', async (req, res) => {
  try {
    const studentId = req.session.userId;
    
    // Get student's trade from classroom
    const [studentTrade] = await db.query(`
      SELECT
        t.id as trade_id, t.name as trade_name
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    `, [studentId]);
    
    if (studentTrade.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student trade not found'
      });
    }
    
    const tradeId = studentTrade[0].trade_id;
    
    // Get subjects for this trade
    const [subjects] = await db.query(`
      SELECT
        s.id, s.name, s.code, s.description,
        stc.is_compulsory, stc.is_elective,
        CASE WHEN ss.id IS NOT NULL THEN 1 ELSE 0 END as is_enrolled
      FROM subject_trade_combinations stc
      JOIN subjects s ON stc.subject_id = s.id
      LEFT JOIN student_subjects ss ON ss.subject_id = s.id AND ss.student_id = ?
      WHERE stc.trade_id = ?
      ORDER BY stc.is_compulsory DESC, s.name ASC
    `, [studentId, tradeId]);
    
    res.json({
      success: true,
      trade: studentTrade[0],
      subjects
    });
  } catch (error) {
    console.error('Error fetching student subjects by trade:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student subjects by trade'
    });
  }
});


// Get student subjects grouped by subject group
router.get('/subjects-by-group', async (req, res) => {
  try {
    const studentId = req.session.userId;
    
    // Get student's trade from classroom
    const [studentTrade] = await db.query(`
      SELECT
        t.id as trade_id, t.name as trade_name
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN trades t ON cr.trade_id = t.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    `, [studentId]);
    
    if (studentTrade.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student trade not found'
      });
    }
    
    const tradeId = studentTrade[0].trade_id;
    
    // Get all subjects for this trade grouped by subject_group
    const [subjects] = await db.query(`
      SELECT
        s.id, s.name, s.code, s.description, s.subject_group,
        stc.is_compulsory, stc.is_elective,
        CASE WHEN ss.id IS NOT NULL THEN 1 ELSE 0 END as is_enrolled
      FROM subject_trade_combinations stc
      JOIN subjects s ON stc.subject_id = s.id
      LEFT JOIN student_subjects ss ON ss.subject_id = s.id AND ss.student_id = ?
      WHERE stc.trade_id = ?
      ORDER BY s.subject_group, s.name ASC
    `, [studentId, tradeId]);
    
    // Group subjects by subject_group
    const subjectGroups = {};
    subjects.forEach(subject => {
      const groupId = subject.subject_group || 0;
      if (!subjectGroups[groupId]) {
        subjectGroups[groupId] = [];
      }
      subjectGroups[groupId].push(subject);
    });
    
    // Define group names
    const groupNames = {
      1: 'Language (any one)',
      2: 'Classical/Foreign Language (any one)',
      3: 'History',
      4: 'Economics/Business Studies/Accountancy',
      5: 'Mathematics',
      6: 'Political Science',
      7: 'Sociology',
      8: 'Public Administration',
      9: 'Philosophy',
      10: 'Religion',
      11: 'Geography',
      12: 'Computer Science',
      13: 'Welcome Life'
    };
    
    // Format response
    const formattedGroups = Object.keys(subjectGroups).map(groupId => {
      return {
        group_id: parseInt(groupId),
        group_name: groupNames[groupId] || `Group ${groupId}`,
        subjects: subjectGroups[groupId]
      };
    });
    
    // Sort by group_id
    formattedGroups.sort((a, b) => a.group_id - b.group_id);
    
    res.json({
      success: true,
      trade: studentTrade[0],
      subject_groups: formattedGroups
    });
  } catch (error) {
    console.error('Error fetching student subjects by group:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student subjects by group'
    });
  }
});

// Get student timetable
router.get('/timetable', async (req, res) => {
  try {
    const studentId = req.session.userId;
    
    // Get student's class
    const [studentClass] = await db.query(`
      SELECT
        c.id as class_id
      FROM student_classrooms sc
      JOIN classrooms cr ON sc.classroom_id = cr.id
      JOIN classes c ON cr.class_id = c.id
      WHERE sc.student_id = ? AND sc.status = 'active'
      LIMIT 1
    `, [studentId]);
    
    if (studentClass.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student class not found'
      });
    }
    
    const classId = studentClass[0].class_id;
    
    // Get timetable for this class
    const [timetable] = await db.query(`
      SELECT
        cwl.day_of_week, cwl.period, cwl.start_time, cwl.end_time,
        cwl.room, s.id as subject_id, s.name as subject_name,
        s.code as subject_code, u.id as teacher_id,
        u.full_name as teacher_name
      FROM class_weekly_lectures cwl
      JOIN subjects s ON cwl.subject_id = s.id
      JOIN users u ON cwl.teacher_id = u.id
      WHERE cwl.class_id = ?
      ORDER BY cwl.day_of_week, cwl.period
    `, [classId]);
    
    // Group by day of week
    const days = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    
    const timetableByDay = {};
    
    days.forEach((day, index) => {
      timetableByDay[day] = timetable.filter(lecture => lecture.day_of_week === index);
    });
    
    res.json({
      success: true,
      timetable: timetableByDay
    });
  } catch (error) {
    console.error('Error fetching student timetable:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching student timetable'
    });
  }
});

module.exports = router;
