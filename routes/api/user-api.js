const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { validateEmail } = require('../../utils/validators');

/**
 * Check if a username is available
 * GET /api/users/check-username?username=value
 */
router.get('/check-username', async (req, res) => {
    try {
        console.log('API - Check username called:', req.query);
        const { username } = req.query;

        if (!username || username.trim().length < 3) {
            console.log('API - Username too short:', username);
            return res.json({
                available: false,
                message: 'Username must be at least 3 characters long'
            });
        }

        // Check if username exists
        const [users] = await db.query(
            'SELECT id FROM users WHERE username = ?',
            [username.trim()]
        );

        if (users.length > 0) {
            return res.json({
                available: false,
                message: 'This username is already taken'
            });
        }

        return res.json({
            available: true,
            message: `Username "${username}" is available`
        });
    } catch (error) {
        console.error('Error checking username:', error);
        res.status(500).json({
            available: false,
            message: 'An error occurred while checking username availability'
        });
    }
});

/**
 * Check if an email is available
 * GET /api/users/check-email?email=value
 */
router.get('/check-email', async (req, res) => {
    try {
        console.log('API - Check email called:', req.query);
        const { email } = req.query;

        if (!email || !validateEmail(email)) {
            console.log('API - Invalid email format:', email);
            return res.json({
                available: false,
                message: 'Please enter a valid email address'
            });
        }

        // Check if email exists
        const [users] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            [email.trim()]
        );

        if (users.length > 0) {
            return res.json({
                available: false,
                message: 'This email is already registered'
            });
        }

        return res.json({
            available: true,
            message: 'Email is available'
        });
    } catch (error) {
        console.error('Error checking email:', error);
        res.status(500).json({
            available: false,
            message: 'An error occurred while checking email availability'
        });
    }
});

module.exports = router;
