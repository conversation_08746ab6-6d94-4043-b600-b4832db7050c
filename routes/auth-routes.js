const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const db = require('../config/database');
const i18n = require('../config/i18n');
const { cleanupSession } = require('../middleware/session-manager');
const groupController = require('../controllers/group-controller');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Define upload directory for profile images
const uploadDir = path.join(__dirname, '../public/uploads/profiles');

// Ensure upload directory exists
if (!fs.existsSync(uploadDir)) {
    try {
        fs.mkdirSync(uploadDir, { recursive: true });
        console.log('Created upload directory:', uploadDir);
    } catch (err) {
        console.error('Error creating upload directory:', err);
    }
}

// Configure multer with disk storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        let ext = path.extname(file.originalname);
        if (!ext) {
            // Default to .jpg if no extension
            ext = '.jpg';
        }
        cb(null, 'profile-' + uniqueSuffix + ext);
    }
});

// Multer configuration with disk storage
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: function (req, file, cb) {
        // Accept only image files
        if (file.mimetype.startsWith('image/')) {
            return cb(null, true);
        }
        cb(new Error('Only image files are allowed!'));
    }
});

// Helper function to check if request is API/curl
const isApiRequest = (req) => {
    return req.xhr ||
           (req.headers.accept && req.headers.accept.indexOf('json') > -1) ||
           req.headers['user-agent'].toLowerCase().includes('curl');
};

// Middleware to check if user is NOT authenticated (for login/register pages)
const checkNotAuthenticated = (req, res, next) => {
    console.log('Session check:', req.session);
    if (req.session.userId) {
        console.log('User already logged in, redirecting to home');
        if (req.isApiRequest) {
            return res.status(400).json({ error: 'Already authenticated' });
        }
        return res.redirect('/');
    }
    next();
};

// Login page
router.get('/login', checkNotAuthenticated, async (req, res) => {
    console.log('GET /login');
    if (req.isApiRequest) {
        return res.json({ message: 'Login page' });
    }

    // Set default language if not set
    const currentLanguage = req.getLocale ? req.getLocale() : 'en';
    const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

    // Check if session timed out
    const sessionTimedOut = req.query.timeout === 'true';
    let errorMessage = null;

    if (sessionTimedOut) {
        errorMessage = 'Your session has expired due to inactivity. Please log in again.';
    }

    // Get site settings for logo
    let siteSettings = {};
    try {
        const [settings] = await db.query('SELECT * FROM site_settings');
        if (settings && settings.length > 0) {
            settings.forEach(setting => {
                siteSettings[setting.setting_key] = setting.setting_value;
            });
        }
    } catch (error) {
        console.error('Error fetching site settings:', error);
    }

    res.render('auth/login', {
        title: 'Login',
        layout: 'layouts/auth',
        error: errorMessage,
        currentLanguage,
        availableLanguages,
        siteSettings
    });
});

// Login process
router.post('/login', async (req, res) => {
    console.log('Login attempt:', { email: req.body.email });

    try {
        const { email, password } = req.body;

        // Validate input
        if (!email || !password) {
            console.log('Missing credentials');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Please provide both email and password' });
            }
            return res.render('auth/login', {
                title: 'Login',
                error: 'Please provide both email and password'
            });
        }

        // Check if user exists by email or username
        const [users] = await db.query(
            'SELECT * FROM users WHERE email = ? OR username = ?',
            [email, email]
        );

        console.log('Checking for user with email or username:', email);

        console.log('User query result:', {
            found: users.length > 0,
            user: users.length > 0 ? {
                id: users[0].id,
                email: users[0].email,
                role: users[0].role
            } : null
        });

        if (users.length === 0) {
            console.log('User not found');
            if (req.isApiRequest) {
                return res.status(401).json({ error: 'Invalid email or password' });
            }
            return res.render('auth/login', {
                title: 'Login',
                error: 'Invalid email or password',
                layout: 'layouts/auth',
                currentLanguage: req.getLocale ? req.getLocale() : 'en',
                availableLanguages: i18n.getLocales ? i18n.getLocales() : ['en', 'pa']
            });
        }

        const user = users[0];

        // Check password
        const validPassword = await bcrypt.compare(password, user.password);
        console.log('Password check:', { valid: validPassword });

        if (!validPassword) {
            console.log('Invalid password');
            if (req.isApiRequest) {
                return res.status(401).json({ error: 'Invalid email or password' });
            }
            // Set default language if not set
            const currentLanguage = req.getLocale ? req.getLocale() : 'en';
            const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

            return res.render('auth/login', {
                title: 'Login',
                error: 'Invalid email or password',
                currentLanguage,
                availableLanguages,
                layout: 'layouts/auth' // Use wallpaper layout
            });
        }

        // Check if account is approved (skip for admin users)
        if (user.role !== 'admin' && user.is_approved === 0) {
            console.log('Account not approved');
            if (req.isApiRequest) {
                return res.status(403).json({ error: 'Your account is pending approval by an administrator. Please try again later.' });
            }
            // Set default language if not set
            const currentLanguage = req.getLocale ? req.getLocale() : 'en';
            const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

            return res.render('auth/login', {
                title: 'Login',
                error: 'Your account is pending approval by an administrator. Please try again later.',
                currentLanguage,
                availableLanguages,
                layout: 'layouts/auth' // Use wallpaper layout
            });
        }

        // For non-admin users, check if they already have an active session
        if (user.role !== 'admin') {
            const [activeSessions] = await db.query(
                'SELECT * FROM active_sessions WHERE user_id = ? AND is_active = 1',
                [user.id]
            );

            if (activeSessions.length > 0) {
                console.log('User already has an active session on another device');
                if (req.isApiRequest) {
                    return res.status(403).json({
                        error: 'You are already logged in on another device. Please log out from other devices first.',
                        canForceLogout: true,
                        userId: user.id
                    });
                }

                return res.render('auth/login', {
                    title: 'Login',
                    error: 'You are already logged in on another device. Please log out from other devices first.',
                    currentLanguage: req.getLocale ? req.getLocale() : 'en',
                    availableLanguages: i18n.getLocales ? i18n.getLocales() : ['en', 'pa'],
                    layout: 'layouts/auth',
                    canForceLogout: true,
                    userId: user.id
                });
            }
        }

        // Set session
        req.session.userId = user.id;
        req.session.userRole = user.role;
        req.session.username = user.username;

        console.log('Setting session:', {
            userId: user.id,
            userRole: user.role,
            username: user.username
        });

        // Save session
        await new Promise((resolve, reject) => {
            req.session.save((err) => {
                if (err) {
                    console.error('Session save error:', err);
                    reject(err);
                    return;
                }
                resolve();
            });
        });

        console.log('Session saved:', {
            userId: req.session.userId,
            role: req.session.userRole,
            username: req.session.username
        });

        if (req.isApiRequest) {
            return res.json({
                message: 'Login successful',
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role
                }
            });
        }

        // Set success message as toast
        req.session.toast = {
            message: 'Login successful',
            type: 'success'
        };

        // Redirect based on role
        if (user.role === 'admin') {
            console.log('Redirecting admin to dashboard');
            return res.redirect('/admin/dashboard');
        } else if (user.role === 'student') {
            console.log('Redirecting student to tests page');
            return res.redirect('/tests');
        } else {
            console.log('Redirecting user to home');
            return res.redirect('/');
        }

    } catch (error) {
        console.error('Login error:', error);
        if (req.isApiRequest) {
            return res.status(500).json({ error: 'An error occurred during login' });
        }
        res.render('auth/login', {
            title: 'Login',
            error: 'An error occurred during login',
            layout: 'layouts/auth', // Use wallpaper layout
            currentLanguage: req.getLocale ? req.getLocale() : 'en',
            availableLanguages: i18n.getLocales ? i18n.getLocales() : ['en', 'pa']
        });
    }
});

// Register page
router.get('/register', checkNotAuthenticated, async (req, res) => {
    console.log('Rendering register page');
    if (req.isApiRequest) {
        return res.json({ message: 'Register page' });
    }

    // Set default language if not set
    const currentLanguage = req.getLocale ? req.getLocale() : 'en';
    const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

    // Get site settings for logo
    let siteSettings = {};
    try {
        const [settings] = await db.query('SELECT * FROM site_settings');
        if (settings && settings.length > 0) {
            settings.forEach(setting => {
                siteSettings[setting.setting_key] = setting.setting_value;
            });
        }
    } catch (error) {
        console.error('Error fetching site settings:', error);
    }

    res.render('auth/register', {
        title: 'Register',
        error: null,
        currentLanguage,
        availableLanguages,
        layout: 'layouts/auth',
        siteSettings
    });
});

// Register process with Base64 image data
router.post('/register', async (req, res) => {
    console.log('Registration attempt:', {
        email: req.body.email,
        username: req.body.username,
        date_of_birth: req.body.date_of_birth
    });

    try {
        const { username, email, password, confirm_password, date_of_birth } = req.body;

        // Validate input
        if (!username || !email || !password || !confirm_password || !date_of_birth) {
            console.log('Missing required fields');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'All fields are required' });
            }
            // Set default language if not set
            const currentLanguage = req.getLocale ? req.getLocale() : 'en';
            const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

            return res.render('auth/register', {
                title: 'Register',
                error: 'All fields are required',
                currentLanguage,
                availableLanguages
            });
        }

        // Validate date of birth in DD-MMM-YYYY format
        console.log('Validating date of birth:', date_of_birth);

        // Check format
        const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4}$/;
        if (!dateRegex.test(date_of_birth)) {
            console.log('Invalid date format');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Date of birth must be in DD-MMM-YYYY format' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Date of birth must be in DD-MMM-YYYY format'
            });
        }

        // Parse the date
        const parts = date_of_birth.split('-');
        const day = parseInt(parts[0], 10);
        const monthStr = parts[1];
        const year = parseInt(parts[2], 10);

        // Convert month abbreviation to month number (0-11)
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const month = months.indexOf(monthStr);

        if (month === -1) {
            console.log('Invalid month abbreviation');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Invalid month abbreviation' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Invalid month abbreviation'
            });
        }

        const dobDate = new Date(year, month, day);
        const today = new Date();

        // Check if date is valid
        if (dobDate.getDate() !== day || dobDate.getMonth() !== month || dobDate.getFullYear() !== year) {
            console.log('Invalid date');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Invalid date' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Invalid date'
            });
        }

        // Check if date is in the future
        if (dobDate >= today) {
            console.log('Invalid date of birth - in the future');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Date of birth must be in the past' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Date of birth must be in the past'
            });
        }

        if (password !== confirm_password) {
            console.log('Passwords do not match');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Passwords do not match' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Passwords do not match'
            });
        }

        if (password.length < 8) {
            console.log('Password too short');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Password must be at least 8 characters long' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Password must be at least 8 characters long'
            });
        }

        // Check if email already exists
        const [existingUsers] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            [email]
        );

        if (existingUsers.length > 0) {
            console.log('Email already registered');
            if (req.isApiRequest) {
                return res.status(400).json({ error: 'Email already registered' });
            }
            return res.render('auth/register', {
                title: 'Register',
                error: 'Email already registered'
            });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);
        console.log('Password hashed successfully');

        // Format date_of_birth to YYYY-MM-DD format for MySQL
        // We already have the dobDate variable from validation
        const formattedDOB = `${dobDate.getFullYear()}-${String(dobDate.getMonth() + 1).padStart(2, '0')}-${String(dobDate.getDate()).padStart(2, '0')}`;
        console.log('Formatted DOB for database:', formattedDOB);

        // Extract additional fields
        const {
            bio,
            institution,
            grade,
            field_of_study,
            preferred_subjects,
            language_preference
        } = req.body;

        // Process Base64 image data if provided
        let profileImagePath = null;
        const base64Data = req.body.profile_image_base64;

        if (base64Data) {
            try {
                // Extract the MIME type and base64 data
                const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

                if (!matches || matches.length !== 3) {
                    throw new Error('Invalid base64 image data');
                }

                const type = matches[1];
                const data = matches[2];
                const buffer = Buffer.from(data, 'base64');

                // Validate image type
                if (!type.startsWith('image/')) {
                    throw new Error('Only image files are allowed');
                }

                // Validate file size (max 5MB)
                if (buffer.length > 5 * 1024 * 1024) {
                    throw new Error('Image file is too large. Maximum size is 5MB');
                }

                // Generate a unique filename
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                let ext = '.jpg';

                // Determine file extension from MIME type
                if (type === 'image/jpeg') ext = '.jpg';
                else if (type === 'image/png') ext = '.png';
                else if (type === 'image/gif') ext = '.gif';

                const filename = 'profile-' + uniqueSuffix + ext;
                const filepath = path.join(uploadDir, filename);

                // Ensure upload directory exists
                if (!fs.existsSync(uploadDir)) {
                    fs.mkdirSync(uploadDir, { recursive: true });
                }

                // Write the file to disk
                fs.writeFileSync(filepath, buffer);
                console.log('Profile image saved:', filename);

                // Set the profile image path for database
                profileImagePath = `/uploads/profiles/${filename}`;
            } catch (error) {
                console.error('Error processing profile image:', error);
                // Continue registration without the image
            }
        }

        // Insert user with all fields
        const [result] = await db.query(
            `INSERT INTO users (
                username, name, email, password, date_of_birth, role, bio,
                institution, grade, field_of_study, preferred_subjects,
                language_preference, profile_image, is_active, last_login
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
            [
                username,
                username,
                email,
                hashedPassword,
                formattedDOB,
                'student',
                bio || '',
                institution || null,
                grade || null,
                field_of_study || null,
                preferred_subjects || null,
                language_preference || 'English',
                profileImagePath,
                1
            ]
        );

        const userId = result.insertId;
        console.log('User created:', { userId });

        // Add user to role-based group
        await groupController.addUserToRoleGroup(userId, 'student');
        console.log('User added to role-based group');

        // Create notification for admins about new user registration
        try {
            // Get all admin users
            const [admins] = await db.query('SELECT id FROM users WHERE role = "admin"');

            // Create notification for each admin
            for (const admin of admins) {
                await db.query(
                    'INSERT INTO notifications (user_id, type, title, message, is_read, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
                    [admin.id, 'user_approval', 'New User Registration', `New user ${username} has registered and needs approval`, 0]
                );
            }
            console.log('Admin notifications created for new user approval');
        } catch (error) {
            console.error('Error creating admin notifications:', error);
        }

        if (req.isApiRequest) {
            return res.status(201).json({
                message: 'Registration successful. Your account is pending approval by an administrator.',
                user: {
                    id: userId,
                    username,
                    email,
                    date_of_birth,
                    role: 'user',
                    is_approved: 0
                }
            });
        }

        // Set toast message and redirect to login page
        req.session.toast = {
            message: 'Registration successful! Your account is pending approval by an administrator. You will be able to log in once your account is approved.',
            type: 'success'
        };
        return res.redirect('/login');

    } catch (error) {
        console.error('Registration error:', error);

        // Set appropriate error message
        let errorMessage = 'An error occurred during registration';
        if (error.message) {
            if (error.message.includes('Only image files are allowed')) {
                errorMessage = 'Only image files are allowed for profile picture.';
            } else if (error.message.includes('Image file is too large')) {
                errorMessage = 'Profile image is too large. Maximum size is 5MB.';
            } else if (error.message.includes('Invalid base64 image data')) {
                errorMessage = 'Invalid image format. Please try a different image.';
            } else {
                errorMessage = error.message;
            }
        }

        if (req.isApiRequest) {
            return res.status(500).json({ error: errorMessage });
        }

        // Set default language if not set
        const currentLanguage = req.getLocale ? req.getLocale() : 'en';
        const availableLanguages = i18n.getLocales ? i18n.getLocales() : ['en', 'pa'];

        res.render('auth/register', {
            title: 'Register',
            error: errorMessage,
            currentLanguage,
            availableLanguages,
            layout: 'layouts/auth'
        });
    }
});

// Logout
router.get('/logout', async (req, res) => {
    console.log('Logout requested for user:', req.session.userId);

    try {
        // Clean up the session in the active_sessions table
        if (req.session.userId) {
            await cleanupSession(req);
        }

        // Destroy the session
        req.session.destroy((err) => {
            if (err) {
                console.error('Logout error:', err);
                if (req.isApiRequest) {
                    return res.status(500).json({ error: 'Logout failed' });
                }
            }
            if (req.isApiRequest) {
                return res.json({ message: 'Logged out successfully' });
            }
            res.redirect('/login');
        });
    } catch (error) {
        console.error('Error during logout:', error);
        if (req.isApiRequest) {
            return res.status(500).json({ error: 'Logout failed' });
        }
        res.redirect('/login');
    }
});

// Force logout from login page
router.post('/force-logout/:userId', async (req, res) => {
    try {
        const userId = req.params.userId;
        console.log('Force logout requested for user:', userId);

        // Validate the user exists
        const [users] = await db.query('SELECT * FROM users WHERE id = ?', [userId]);

        if (users.length === 0) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }

        // Mark all active sessions for this user as inactive
        await db.query(
            'UPDATE active_sessions SET is_active = 0 WHERE user_id = ? AND is_active = 1',
            [userId]
        );

        // Send force logout message via WebSocket
        try {
            const { forceLogout } = require('../websocket-server');
            forceLogout(userId);
            console.log('Force logout WebSocket message sent for user:', userId);
        } catch (wsError) {
            console.error('Error sending WebSocket force logout message:', wsError);
        }

        if (req.isApiRequest) {
            return res.json({ success: true, message: 'All sessions have been logged out' });
        }

        // Redirect back to login page with toast success message
        req.session.toast = {
            message: 'All other sessions have been logged out. You can now log in.',
            type: 'success'
        };
        res.redirect('/login');
    } catch (error) {
        console.error('Error during force logout:', error);
        if (req.isApiRequest) {
            return res.status(500).json({ success: false, message: 'Force logout failed' });
        }
        res.render('auth/login', {
            title: 'Login',
            error: 'An error occurred while trying to force logout. Please try again.',
            layout: 'layouts/auth',
            currentLanguage: req.getLocale ? req.getLocale() : 'en',
            availableLanguages: i18n.getLocales ? i18n.getLocales() : ['en', 'pa']
        });
    }
});

// Forgot password page
router.get('/forgot-password', async (req, res) => {
    // Get site settings for logo
    let siteSettings = {};
    try {
        const [settings] = await db.query('SELECT * FROM site_settings');
        if (settings && settings.length > 0) {
            settings.forEach(setting => {
                siteSettings[setting.setting_key] = setting.setting_value;
            });
        }
    } catch (error) {
        console.error('Error fetching site settings:', error);
    }

    res.render('auth/forgot-password', {
        title: 'Forgot Password',
        error: null,
        success: null,
        layout: 'layouts/auth',
        siteSettings,
        currentLanguage: req.getLocale ? req.getLocale() : 'en',
        availableLanguages: i18n.getLocales ? i18n.getLocales() : ['en', 'pa']
    });
});

// Handle forgot password request
router.post('/forgot-password', async (req, res) => {
    try {
        const { username, date_of_birth } = req.body;

        // Validate input
        if (!username || !date_of_birth) {
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'Please provide both username and date of birth',
                success: null
            });
        }

        // Check if user exists with matching username and DOB
        const [users] = await db.query(
            'SELECT * FROM users WHERE username = ? AND date_of_birth = ?',
            [username, date_of_birth]
        );

        if (users.length === 0) {
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'No account found with these details',
                success: null
            });
        }

        const user = users[0];

        // Generate reset token
        const resetToken = require('crypto').randomBytes(32).toString('hex');
        const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour from now

        // Save reset token to database
        await db.query(
            'UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?',
            [resetToken, resetTokenExpires, user.id]
        );

        // In a real application, you would send an email with the reset link
        // For this demo, we'll just show the reset link on the page
        const resetLink = `/reset-password/${resetToken}`;

        res.render('auth/forgot-password', {
            title: 'Forgot Password',
            error: null,
            success: `Password reset link generated. In a real application, this would be sent to your email. For demo purposes, click here: <a href="${resetLink}" class="text-indigo-600 hover:text-indigo-500">Reset Password</a>`,
            successIsHtml: true
        });

    } catch (error) {
        console.error('Password recovery error:', error);
        res.render('auth/forgot-password', {
            title: 'Forgot Password',
            error: 'An error occurred during password recovery',
            success: null
        });
    }
});

// Reset password page
router.get('/reset-password/:token', async (req, res) => {
    try {
        const { token } = req.params;

        // Check if token is valid and not expired
        const [users] = await db.query(
            'SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()',
            [token]
        );

        if (users.length === 0) {
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'Invalid or expired reset token',
                success: null
            });
        }

        res.render('auth/reset-password', {
            title: 'Reset Password',
            token,
            error: null,
            layout: 'layouts/auth'
        });

    } catch (error) {
        console.error('Reset password error:', error);
        res.render('auth/forgot-password', {
            title: 'Forgot Password',
            error: 'An error occurred',
            success: null
        });
    }
});

// Handle password reset
router.post('/reset-password/:token', async (req, res) => {
    try {
        const { token } = req.params;
        const { password, confirm_password } = req.body;

        if (password !== confirm_password) {
            return res.render('auth/reset-password', {
                title: 'Reset Password',
                token,
                error: 'Passwords do not match'
            });
        }

        // Check if token is valid and not expired
        const [users] = await db.query(
            'SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()',
            [token]
        );

        if (users.length === 0) {
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'Invalid or expired reset token',
                success: null
            });
        }

        const user = users[0];

        // Hash new password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Update password and clear reset token
        await db.query(
            'UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
            [hashedPassword, user.id]
        );

        res.render('auth/login', {
            title: 'Login',
            error: null,
            success: 'Password has been reset successfully. Please login with your new password.'
        });

    } catch (error) {
        console.error('Reset password error:', error);
        res.render('auth/reset-password', {
            title: 'Reset Password',
            token: req.params.token,
            error: 'An error occurred while resetting password'
        });
    }
});

// Extend session route
router.post('/extend-session', (req, res) => {
    if (req.session.userId) {
        // Update last activity time
        req.session.lastActivity = Date.now();
        res.json({ success: true });
    } else {
        res.status(401).json({ success: false, message: 'Not authenticated' });
    }
});

module.exports = router;
