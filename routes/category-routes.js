const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');

// Search categories endpoint for Chosen
router.get('/admin/categories/search', checkAuthenticated, async (req, res) => {
    try {
        const searchTerm = req.query.q || '';
        const page = parseInt(req.query.page) || 1;
        const limit = 20;
        const offset = (page - 1) * limit;

        // Search for categories that match the search term
        const [categories] = await db.query(
            `SELECT category_id, name
             FROM categories
             WHERE name LIKE ?
             ORDER BY name ASC
             LIMIT ? OFFSET ?`,
            [`%${searchTerm}%`, limit, offset]
        );

        res.json(categories);
    } catch (error) {
        console.error('Error searching categories:', error);
        res.status(500).json({ error: 'Failed to search categories' });
    }
});

// Create new category endpoint - accessible to all authenticated users
router.post('/admin/categories', checkAuthenticated, async (req, res) => {
    try {
        const { name } = req.body;

        if (!name || name.trim() === '') {
            return res.status(400).json({ success: false, message: 'Category name is required' });
        }

        // Check if category already exists
        const [existingCategories] = await db.query(
            'SELECT category_id FROM categories WHERE name = ?',
            [name.trim()]
        );

        if (existingCategories.length > 0) {
            return res.json({
                success: true,
                message: 'Category already exists',
                category_id: existingCategories[0].category_id,
                name: name.trim()
            });
        }

        // Insert new category
        const [result] = await db.query(
            'INSERT INTO categories (name, created_by) VALUES (?, ?)',
            [name.trim(), req.session.userId]
        );

        res.json({
            success: true,
            message: 'Category created successfully',
            category_id: result.insertId,
            name: name.trim()
        });
    } catch (error) {
        console.error('Error creating category:', error);
        res.status(500).json({ success: false, message: 'Failed to create category' });
    }
});

module.exports = router;
