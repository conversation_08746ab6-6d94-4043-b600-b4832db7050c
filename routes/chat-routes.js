const express = require('express');
const router = express.Router();
const chatController = require('../controllers/chat-controller');
const { checkAuthenticated } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/uploads/chat'));
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'chat-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: function (req, file, cb) {
        // Accept images, audio, video, and documents
        const filetypes = /jpeg|jpg|png|gif|mp3|mp4|wav|pdf|doc|docx|xls|xlsx|ppt|pptx|txt/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only images, audio, video, and documents are allowed'));
    }
});

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Chat routes
router.get('/', chatController.index);
router.get('/user/:userId', chatController.privateChat);
router.get('/group/:groupId', chatController.groupChat);

// Group invite routes
router.post('/group/:groupId/invite', chatController.generateInviteLink);
router.get('/join/:inviteId', chatController.joinViaInvite);

// File upload route
router.post('/upload', upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No file uploaded'
            });
        }

        res.json({
            success: true,
            file: {
                url: `/uploads/chat/${req.file.filename}`,
                type: req.file.mimetype,
                name: req.file.originalname,
                size: req.file.size
            }
        });
    } catch (error) {
        console.error('Error uploading file:', error);
        res.status(500).json({
            success: false,
            message: 'Error uploading file'
        });
    }
});

module.exports = router;
