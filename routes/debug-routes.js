const express = require('express');
const router = express.Router();

// Route to check the current language
router.get('/check-language', (req, res) => {
    const currentLanguage = req.getLocale ? req.getLocale() : 'unknown';
    const availableLanguages = req.app.locals.i18n ? req.app.locals.i18n.getLocales() : ['unknown'];
    const translationExample = req.__('app.name');
    
    res.json({
        currentLanguage,
        availableLanguages,
        translationExample,
        cookies: req.cookies,
        headers: req.headers,
        session: req.session ? {
            userId: req.session.userId,
            userRole: req.session.userRole,
            username: req.session.username
        } : null
    });
});

module.exports = router;
