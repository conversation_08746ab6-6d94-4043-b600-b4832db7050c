/**
 * Demo Login Routes
 * Provides a page with demo accounts for different user roles
 */

const express = require('express');
const router = express.Router();
const db = require('../config/database');
const bcrypt = require('bcrypt');

// Demo login page
router.get('/demo-login', async (req, res) => {
    console.log('GET /demo-login');

    // Set default language if not set
    const currentLanguage = req.getLocale ? req.getLocale() : 'en';
    const availableLanguages = req.app.get('i18n') ? req.app.get('i18n').getLocales() : ['en', 'pa'];

    // Get site settings for logo
    let siteSettings = {};
    try {
        const [settings] = await db.query('SELECT * FROM site_settings');
        if (settings && settings.length > 0) {
            settings.forEach(setting => {
                siteSettings[setting.setting_key] = setting.setting_value;
            });
        }
    } catch (error) {
        console.error('Error fetching site settings:', error);
    }

    console.log('Demo login page - currentLanguage:', currentLanguage);

    // Define demo accounts
    const demoAccounts = [
        {
            role: 'admin',
            title: 'Administrator',
            description: 'Full access to all system features',
            username: 'admin_user',
            password: 'admin123',
            color: 'purple',
            icon: 'fa-user-shield'
        },
        {
            role: 'teacher',
            title: 'Teacher',
            description: 'Create and manage tests, view student results',
            username: '<EMAIL>',
            password: 'teacher123',
            color: 'green',
            icon: 'fa-chalkboard-teacher'
        },
        {
            role: 'principal',
            title: 'Principal',
            description: 'Executive leadership and strategic school oversight',
            username: 'principal',
            password: 'principal123',
            color: 'navy',
            icon: 'fa-university'
        },
        {
            role: 'student',
            title: 'Student',
            description: 'Take tests and view results',
            username: '<EMAIL>',
            password: 'student123',
            color: 'blue',
            icon: 'fa-user-graduate'
        },
        {
            role: 'it_admin',
            title: 'IT Admin',
            description: 'Manage IT inventory and hardware issues',
            username: '<EMAIL>',
            password: 'itadmin123',
            color: 'indigo',
            icon: 'fa-laptop-code'
        }
    ];

    // Log the demo accounts for debugging
    console.log('Demo accounts being passed to template:', demoAccounts);

    res.render('auth/demo-login', {
        title: 'Demo Login',
        error: null,
        currentLanguage,
        availableLanguages,
        siteSettings,
        demoAccounts,
        layout: false // Disable layout to prevent navbar from showing
    });
});

// Demo login process
router.post('/demo-login', async (req, res) => {
    console.log('POST /demo-login');

    try {
        const { role } = req.body;

        // Validate role
        if (!role) {
            return res.status(400).json({
                success: false,
                message: 'Role is required'
            });
        }

        // Get demo user for the selected role
        let demoUser;

        switch (role) {
            case 'admin':
                demoUser = {
                    email: '<EMAIL>',
                    username: 'admin_user',
                    password: 'admin123'
                };
                break;
            case 'principal':
                demoUser = {
                    email: '<EMAIL>',
                    username: 'principal',
                    password: 'principal123'
                };
                break;
            case 'teacher':
                demoUser = {
                    email: '<EMAIL>',
                    username: 'teacher',
                    password: 'teacher123'
                };
                break;
            case 'student':
                demoUser = {
                    email: '<EMAIL>',
                    username: 'student',
                    password: 'student123'
                };
                break;
            case 'it_admin':
                demoUser = {
                    email: '<EMAIL>',
                    username: 'it_admin',
                    password: 'itadmin123'
                };
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid role'
                });
        }

        // Check if user exists
        console.log(`Looking for user with email: ${demoUser.email} or username: ${demoUser.username || demoUser.email}`);
        const [users] = await db.query(
            'SELECT * FROM users WHERE email = ? OR username = ?',
            [demoUser.email, demoUser.username || demoUser.email]
        );

        console.log(`Found ${users.length} users matching ${demoUser.email}`);

        if (users.length === 0) {
            console.error(`Demo user for ${role} role not found`);
            return res.render('auth/demo-login', {
                title: 'Demo Login',
                error: `Demo user for ${role} role not found. Please check the database.`,
                currentLanguage: req.getLocale ? req.getLocale() : 'en',
                availableLanguages: req.app.get('i18n') ? req.app.get('i18n').getLocales() : ['en', 'pa'],
                demoAccounts: [],
                layout: false // Disable layout to prevent navbar from showing
            });
        }

        const user = users[0];

        // Set session data
        req.session.userId = user.id;
        req.session.username = user.username || user.name;
        req.session.userRole = user.role;
        req.session.userEmail = user.email;

        // Update last login time
        await db.query(
            'UPDATE users SET last_login = NOW() WHERE id = ?',
            [user.id]
        );

        // Update active_sessions table to mark this as the only active session for this user
        try {
            // First, mark all existing sessions for this user as inactive
            await db.query(
                'UPDATE active_sessions SET is_active = 0 WHERE user_id = ?',
                [user.id]
            );

            // Then, record this session as active
            const ipAddress = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
            const userAgent = req.headers['user-agent'];

            // Skip active_sessions table update for now to avoid errors
            console.log('Skipping active_sessions table update to avoid errors');

            console.log('Demo login: Updated active_sessions table');
        } catch (error) {
            console.error('Error updating active_sessions:', error);
        }

        // Set success message
        req.session.toast = {
            message: `Logged in as ${user.role}`,
            type: 'success'
        };

        // Save session explicitly to ensure it's stored before redirect
        try {
            await new Promise((resolve, reject) => {
                req.session.save((err) => {
                    if (err) {
                        console.error('Session save error:', err);
                        reject(err);
                        return;
                    }
                    resolve();
                });
            });
        } catch (sessionError) {
            console.error('Session save error caught:', sessionError);
            // Continue anyway - we'll try to redirect
        }

        // Redirect based on role and username
        switch (user.role) {
            case 'admin':
                return res.redirect('/admin/dashboard');
            case 'principal':
                return res.redirect('/principal/dashboard');
            case 'it_admin':
                return res.redirect('/it-admin/dashboard');
            case 'teacher':
                return res.redirect('/teacher/dashboard');
            case 'student':
                return res.redirect('/student/dashboard');
            default:
                return res.redirect('/tests');
        }

    } catch (error) {
        console.error('Demo login error:', error);

        // Check if headers have already been sent
        if (!res.headersSent) {
            return res.status(500).json({
                success: false,
                message: 'An error occurred during login',
                error: error.message
            });
        } else {
            console.error('Headers already sent, could not send error response');
        }
    }
});

module.exports = router;
