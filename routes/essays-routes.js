const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for PDF uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/essays');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'essay-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
        if (file.mimetype !== 'application/pdf') {
            return cb(new Error('Only PDF files are allowed'));
        }
        cb(null, true);
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Get all essays
router.get('/essays', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Get total count
        const [[{ total }]] = await db.query('SELECT COUNT(*) as total FROM essays');
        const totalPages = Math.ceil(total / perPage);

        // Get essays with pagination
        const [essays] = await db.query(
            `SELECT e.*, u.username as creator_name
             FROM essays e
             JOIN users u ON e.created_by = u.id
             ORDER BY e.created_at DESC
             LIMIT ? OFFSET ?`,
            [perPage, offset]
        );

        res.render('admin/essays/index', {
            title: 'Essay Management',
            pageTitle: 'Essay Management',
            currentPage: 'essays',
            essays,
            pagination: {
                page,
                perPage,
                totalPages,
                totalItems: total
            },
            query: req.query || {}
        });
    } catch (error) {
        console.error('Error fetching essays:', error);
        req.session.flashError = 'Failed to fetch essays';
        res.redirect('/admin/dashboard');
    }
});

// Add essay form
router.get('/essays/add', async (req, res) => {
    res.render('admin/essays/add', {
        title: 'Add New Essay',
        pageTitle: 'Add New Essay',
        currentPage: 'essays'
    });
});

// Create new essay
router.post('/essays', upload.single('pdf_file'), async (req, res) => {
    try {
        const { title, content } = req.body;
        const created_by = req.session.userId;
        let pdf_path = null;

        if (req.file) {
            pdf_path = `/uploads/essays/${req.file.filename}`;
        }

        const [result] = await db.query(
            'INSERT INTO essays (title, content, pdf_path, created_by) VALUES (?, ?, ?, ?)',
            [title, content, pdf_path, created_by]
        );

        req.session.flashSuccess = 'Essay created successfully';
        res.redirect('/admin/essays');
    } catch (error) {
        console.error('Error creating essay:', error);
        req.session.flashError = 'Failed to create essay';
        res.redirect('/admin/essays/add');
    }
});

// Edit essay form
router.get('/essays/:id/edit', async (req, res) => {
    try {
        const [essays] = await db.query(
            'SELECT * FROM essays WHERE essay_id = ?',
            [req.params.id]
        );

        if (essays.length === 0) {
            req.session.flashError = 'Essay not found';
            return res.redirect('/admin/essays');
        }

        res.render('admin/essays/edit', {
            title: 'Edit Essay',
            pageTitle: 'Edit Essay',
            currentPage: 'essays',
            essay: essays[0]
        });
    } catch (error) {
        console.error('Error fetching essay:', error);
        req.session.flashError = 'Failed to fetch essay';
        res.redirect('/admin/essays');
    }
});

// Update essay
router.post('/essays/:id', upload.single('pdf_file'), async (req, res) => {
    try {
        const { title, content } = req.body;
        const essayId = req.params.id;

        // Get current essay to check if there's an existing PDF
        const [essays] = await db.query(
            'SELECT pdf_path FROM essays WHERE essay_id = ?',
            [essayId]
        );

        if (essays.length === 0) {
            req.session.flashError = 'Essay not found';
            return res.redirect('/admin/essays');
        }

        let pdf_path = essays[0].pdf_path;

        // If a new PDF is uploaded, update the path and delete the old file if it exists
        if (req.file) {
            if (pdf_path) {
                const oldFilePath = path.join(__dirname, '../public', pdf_path);
                if (fs.existsSync(oldFilePath)) {
                    fs.unlinkSync(oldFilePath);
                }
            }
            pdf_path = `/uploads/essays/${req.file.filename}`;
        }

        await db.query(
            'UPDATE essays SET title = ?, content = ?, pdf_path = ? WHERE essay_id = ?',
            [title, content, pdf_path, essayId]
        );

        req.session.flashSuccess = 'Essay updated successfully';
        res.redirect('/admin/essays');
    } catch (error) {
        console.error('Error updating essay:', error);
        req.session.flashError = 'Failed to update essay';
        res.redirect(`/admin/essays/${req.params.id}/edit`);
    }
});

// View essay
router.get('/essays/:id/view', async (req, res) => {
    try {
        const [essays] = await db.query(
            `SELECT e.*, u.username as creator_name
             FROM essays e
             JOIN users u ON e.created_by = u.id
             WHERE e.essay_id = ?`,
            [req.params.id]
        );

        if (essays.length === 0) {
            req.session.flashError = 'Essay not found';
            return res.redirect('/admin/essays');
        }

        // Get questions linked to this essay
        const [questions] = await db.query(
            `SELECT question_id, question_text, question_type
             FROM questions
             WHERE essay_id = ?`,
            [req.params.id]
        );

        res.render('admin/essays/view', {
            title: essays[0].title,
            pageTitle: 'View Essay',
            currentPage: 'essays',
            essay: essays[0],
            questions
        });
    } catch (error) {
        console.error('Error fetching essay:', error);
        req.session.flashError = 'Failed to fetch essay';
        res.redirect('/admin/essays');
    }
});

// Delete essay
router.post('/essays/:id/delete', async (req, res) => {
    try {
        const essayId = req.params.id;

        // Get essay to check if there's a PDF to delete
        const [essays] = await db.query(
            'SELECT pdf_path FROM essays WHERE essay_id = ?',
            [essayId]
        );

        if (essays.length === 0) {
            req.session.flashError = 'Essay not found';
            return res.redirect('/admin/essays');
        }

        // First, update any questions that reference this essay
        await db.query(
            'UPDATE questions SET essay_id = NULL WHERE essay_id = ?',
            [essayId]
        );

        // Delete the PDF file if it exists
        if (essays[0].pdf_path) {
            const filePath = path.join(__dirname, '../public', essays[0].pdf_path);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }

        // Then delete the essay
        await db.query(
            'DELETE FROM essays WHERE essay_id = ?',
            [essayId]
        );

        req.session.flashSuccess = 'Essay deleted successfully';
        res.redirect('/admin/essays');
    } catch (error) {
        console.error('Error deleting essay:', error);
        req.session.flashError = 'Failed to delete essay';
        res.redirect('/admin/essays');
    }
});

// API endpoint to get all essays (for AJAX requests)
router.get('/api/essays', async (req, res) => {
    try {
        const [essays] = await db.query(
            'SELECT essay_id, title FROM essays ORDER BY title'
        );

        res.json({ success: true, essays });
    } catch (error) {
        console.error('Error fetching essays:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch essays' });
    }
});

// API endpoint for essay list (used by test creator)
router.get('/essays/api/list', async (req, res) => {
    try {
        const [essays] = await db.query(
            `SELECT e.essay_id, e.title, e.created_at,
             COUNT(q.question_id) as question_count
             FROM essays e
             LEFT JOIN questions q ON e.essay_id = q.essay_id
             GROUP BY e.essay_id
             ORDER BY e.title`
        );

        // Format dates for display
        const formattedEssays = essays.map(essay => ({
            ...essay,
            formatted_date: new Date(essay.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            })
        }));

        res.json({ success: true, essays: formattedEssays });
    } catch (error) {
        console.error('Error fetching essays for API:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch essays' });
    }
});

module.exports = router;
