const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const groupController = require('../controllers/group-controller');
const groupImportController = require('../controllers/group-import-controller');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (req, file, cb) => {
        // Accept only CSV and Excel files
        const filetypes = /csv|xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only CSV and Excel files are allowed'));
    }
});

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Group import routes
router.get('/import', checkAdmin, (req, res) => {
    res.render('groups/import', {
        title: 'Import Groups',
        pageTitle: 'Import Groups',
        layout: 'admin',
        currentPage: 'groups'
    });
});

// Download CSV template
router.get('/import/template/csv', (req, res) => {
    const templatePath = path.join(__dirname, '../public/templates/group_import_template.csv');
    res.download(templatePath, 'group_import_template.csv');
});

// Process group import
router.post('/import', checkAdmin, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            req.session.flashError = 'No file uploaded';
            return res.redirect('/groups/import');
        }

        // Determine file type
        const fileType = path.extname(req.file.originalname).toLowerCase() === '.csv' ? 'csv' : 'excel';

        // Import groups
        const results = await groupImportController.importGroups(
            req.file.buffer,
            fileType,
            req.session.userId
        );

        // Set flash messages
        if (results.imported > 0) {
            req.session.flashSuccess = `Successfully imported ${results.imported} groups`;
        }

        if (results.skipped > 0) {
            req.session.flashInfo = `Skipped ${results.skipped} groups`;
        }

        if (results.errors.length > 0) {
            req.session.importErrors = results.errors;
        }

        res.redirect('/groups/import/results');
    } catch (error) {
        console.error('Error importing groups:', error);
        req.session.flashError = `Error importing groups: ${error.message}`;
        res.redirect('/groups/import');
    }
});

// Display import results
router.get('/import/results', checkAdmin, (req, res) => {
    const errors = req.session.importErrors || [];
    delete req.session.importErrors;

    res.render('groups/import-results', {
        title: 'Import Results',
        pageTitle: 'Group Import Results',
        errors,
        layout: 'admin',
        currentPage: 'groups'
    });
});

// Group listing and management
router.get('/', groupController.index);
router.get('/create', groupController.createForm);
router.post('/create', groupController.create);
router.get('/:id', groupController.view);
router.get('/:id/edit', groupController.editForm);
router.post('/:id/edit', groupController.update);
router.post('/:id/delete', groupController.delete);

// Group membership
router.post('/:id/join', groupController.join);
router.post('/:id/leave', groupController.leave);
router.post('/:id/members', groupController.addMember);
router.delete('/:id/members/:userId', groupController.removeMember);
router.put('/:id/members/:userId/admin', groupController.toggleAdmin);

// Group invites
router.get('/join/:inviteId', groupController.joinViaInvite);

// Group exam assignments
router.post('/:id/exams', groupController.assignExam);
router.delete('/:id/exams/:examId', groupController.removeExam);

// Search APIs
router.get('/:id/search/users', groupController.searchUsers);
router.get('/:id/search/exams', groupController.searchExams);

module.exports = router;
