const express = require('express');
const router = express.Router();
const hardwareConditionController = require('../controllers/hardware-condition-controller');
const { checkAdmin } = require('../middleware/auth');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Get hardware parts by type
router.get('/parts/:hardwareType', hardwareConditionController.getPartsByType);

// Get condition data for an item
router.get('/item/:itemId', hardwareConditionController.getItemCondition);

// Save condition data for an item
router.post('/item/:itemId', hardwareConditionController.saveItemCondition);

// Render condition check form
router.get('/check/:itemId', hardwareConditionController.renderConditionCheckForm);

// Render condition check form for a transaction
router.get('/check/:itemId/transaction/:transactionId', hardwareConditionController.renderConditionCheckForm);

module.exports = router;
