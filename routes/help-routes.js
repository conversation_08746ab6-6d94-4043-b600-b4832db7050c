const express = require('express');
const router = express.Router();
const helpController = require('../controllers/help-controller');
const { checkAdmin } = require('../middleware/auth');

// Public help routes
router.get('/', helpController.index);
router.get('/search', helpController.search);
router.get('/category/:category', helpController.category);
router.get('/article/:slug', helpController.article);
router.post('/feedback', helpController.submitFeedback);

// Admin help management routes
router.get('/admin', checkAdmin, helpController.adminIndex);
router.get('/admin/create', checkAdmin, helpController.createForm);
router.post('/admin/create', checkAdmin, helpController.create);
router.get('/admin/:id/edit', checkAdmin, helpController.editForm);
router.post('/admin/:id/edit', checkAdmin, helpController.update);
router.post('/admin/:id/delete', checkAdmin, helpController.delete);

module.exports = router;
