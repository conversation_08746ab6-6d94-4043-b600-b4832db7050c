const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for image uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/questions');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'question-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif|svg/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only image files (jpg, jpeg, png, gif, svg) are allowed'));
    },
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
});

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Upload image
router.post('/upload', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ success: false, message: 'No image file provided' });
        }

        const { originalname, filename, mimetype, size } = req.file;
        const filePath = `/uploads/questions/${filename}`;
        const uploadedBy = req.session.userId;

        // Save image metadata to database
        const [result] = await db.query(
            'INSERT INTO question_images (file_path, file_name, file_type, file_size, uploaded_by) VALUES (?, ?, ?, ?, ?)',
            [filePath, originalname, mimetype, size, uploadedBy]
        );

        const imageId = result.insertId;

        // Return success response with image details
        res.json({
            success: true,
            image: {
                id: imageId,
                path: filePath,
                name: originalname
            }
        });
    } catch (error) {
        console.error('Error uploading image:', error);
        res.status(500).json({ success: false, message: 'Failed to upload image' });
    }
});

// Get all images
router.get('/list', async (req, res) => {
    try {
        const [images] = await db.query(
            'SELECT * FROM question_images ORDER BY created_at DESC'
        );

        res.json({ success: true, images });
    } catch (error) {
        console.error('Error fetching images:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch images' });
    }
});

// Delete image
router.delete('/:id', async (req, res) => {
    try {
        const imageId = req.params.id;

        // Get image details
        const [images] = await db.query(
            'SELECT * FROM question_images WHERE image_id = ?',
            [imageId]
        );

        if (images.length === 0) {
            return res.status(404).json({ success: false, message: 'Image not found' });
        }

        const image = images[0];

        // Delete file from filesystem
        const filePath = path.join(__dirname, '../public', image.file_path);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }

        // Delete from database
        await db.query(
            'DELETE FROM question_images WHERE image_id = ?',
            [imageId]
        );

        res.json({ success: true, message: 'Image deleted successfully' });
    } catch (error) {
        console.error('Error deleting image:', error);
        res.status(500).json({ success: false, message: 'Failed to delete image' });
    }
});

module.exports = router;
