const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { checkAdmin } = require('../middleware/auth');

// Create uploads directory if it doesn't exist
const inventoryImagesDir = path.join(__dirname, '../public/uploads/inventory');
if (!fs.existsSync(inventoryImagesDir)) {
    fs.mkdirSync(inventoryImagesDir, { recursive: true });
    console.log('Created upload directory:', inventoryImagesDir);
}

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, inventoryImagesDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        let ext = path.extname(file.originalname);

        // Default to .jpg if no extension
        if (!ext) {
            ext = '.jpg';
        }

        cb(null, 'inventory-' + uniqueSuffix + ext);
    }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
    console.log('Processing file:', file.originalname, 'Mimetype:', file.mimetype);

    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('Only image files are allowed! Received: ' + file.mimetype), false);
    }
};

// Configure multer upload with increased size limit
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        fieldSize: 10 * 1024 * 1024 // Also increase field size limit
    },
    fileFilter: fileFilter
}).single('image');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Route for setting an image as primary
router.post('/set-primary/:imageId', async (req, res) => {
    try {
        const imageId = req.params.imageId;
        const db = require('../config/database');

        // Get the item_id for this image
        const [images] = await db.query(
            'SELECT item_id FROM inventory_item_images WHERE image_id = ?',
            [imageId]
        );

        if (images.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Image not found'
            });
        }

        const itemId = images[0].item_id;

        // Start a transaction
        const connection = await db.getConnection();
        await connection.beginTransaction();

        try {
            // Set all images for this item to non-primary
            await connection.query(
                'UPDATE inventory_item_images SET is_primary = 0 WHERE item_id = ?',
                [itemId]
            );

            // Set the selected image as primary
            await connection.query(
                'UPDATE inventory_item_images SET is_primary = 1 WHERE image_id = ?',
                [imageId]
            );

            // Commit the transaction
            await connection.commit();

            res.json({
                success: true,
                message: 'Primary image set successfully'
            });
        } catch (error) {
            // Rollback the transaction if there's an error
            await connection.rollback();
            throw error;
        } finally {
            // Release the connection
            connection.release();
        }
    } catch (error) {
        console.error('Error setting primary image:', error);
        res.status(500).json({
            success: false,
            message: 'Error setting primary image',
            error: error.message
        });
    }
});

// Route for deleting an image
router.post('/delete/:imageId', async (req, res) => {
    try {
        const imageId = req.params.imageId;
        const db = require('../config/database');

        // Get the image details
        const [images] = await db.query(
            'SELECT * FROM inventory_item_images WHERE image_id = ?',
            [imageId]
        );

        if (images.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Image not found'
            });
        }

        const image = images[0];
        const itemId = image.item_id;
        const isPrimary = image.is_primary;

        // Start a transaction
        const connection = await db.getConnection();
        await connection.beginTransaction();

        try {
            // Delete the image
            await connection.query(
                'DELETE FROM inventory_item_images WHERE image_id = ?',
                [imageId]
            );

            // If this was the primary image, set another image as primary
            if (isPrimary) {
                const [remainingImages] = await connection.query(
                    'SELECT image_id FROM inventory_item_images WHERE item_id = ? LIMIT 1',
                    [itemId]
                );

                if (remainingImages.length > 0) {
                    await connection.query(
                        'UPDATE inventory_item_images SET is_primary = 1 WHERE image_id = ?',
                        [remainingImages[0].image_id]
                    );
                }
            }

            // Commit the transaction
            await connection.commit();

            res.json({
                success: true,
                message: 'Image deleted successfully'
            });
        } catch (error) {
            // Rollback the transaction if there's an error
            await connection.rollback();
            throw error;
        } finally {
            // Release the connection
            connection.release();
        }
    } catch (error) {
        console.error('Error deleting image:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting image',
            error: error.message
        });
    }
});

// Route for uploading inventory item images (traditional multipart form data approach)
router.post('/upload', (req, res, next) => {
    // Log the request headers for debugging
    console.log('Upload request headers:', req.headers);
    console.log('Content-Length:', req.headers['content-length']);
    console.log('Content-Type:', req.headers['content-type']);

    // Use multer directly instead of middleware pattern for better error control
    upload(req, res, function(err) {
        if (err) {
            console.error('Multer error:', err);

            let errorMessage = 'File upload failed';

            if (err instanceof multer.MulterError) {
                if (err.code === 'LIMIT_FILE_SIZE') {
                    errorMessage = 'Image is too large. Maximum size is 10MB';
                } else {
                    errorMessage = `Upload error: ${err.message} (code: ${err.code})`;
                }
            } else {
                // Handle specific "Unexpected end of form" error
                if (err.message && err.message.includes('Unexpected end of form')) {
                    console.error('Form parsing error details:', {
                        contentType: req.headers['content-type'],
                        contentLength: req.headers['content-length'],
                        error: err.toString()
                    });

                    errorMessage = 'File upload was interrupted or corrupted. Please try a smaller file or different image format.';
                } else {
                    errorMessage = `File upload failed: ${err.message}`;
                }
            }

            return res.status(400).json({
                success: false,
                message: errorMessage
            });
        }

        try {
            // Log the request body and file for debugging
            console.log('Request body:', req.body);
            console.log('Request file:', req.file);

            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No image file provided or file was not properly uploaded'
                });
            }

            // Return the URL to the uploaded image
            const imageUrl = '/uploads/inventory/' + req.file.filename;
            console.log('Image uploaded successfully:', imageUrl);

            res.json({
                success: true,
                imageUrl: imageUrl,
                message: 'Image uploaded successfully'
            });
        } catch (error) {
            console.error('Error uploading image:', error);
            res.status(500).json({
                success: false,
                message: 'Error uploading image',
                error: error.message
            });
        }
    });
});

// Route for uploading inventory item images using base64 encoding
router.post('/upload-base64', (req, res) => {
    try {
        console.log('Base64 upload request received');
        console.log('Request body size:', JSON.stringify(req.body).length);

        // Validate request body
        if (!req.body) {
            return res.status(400).json({
                success: false,
                message: 'No request body provided'
            });
        }

        if (!req.body.image) {
            return res.status(400).json({
                success: false,
                message: 'No image data provided'
            });
        }

        // Get image data from request
        const { image, filename, mimetype } = req.body;

        // Validate image data
        if (!image || typeof image !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Invalid image data format'
            });
        }

        // Check if the image data is a valid base64 string
        // Remove data:image prefix if present
        let base64Data = image;
        if (image.startsWith('data:image')) {
            const parts = image.split(',');
            if (parts.length !== 2) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid data URL format'
                });
            }
            base64Data = parts[1];
        }

        // Decode base64 string
        let imageBuffer;
        try {
            imageBuffer = Buffer.from(base64Data, 'base64');

            // Verify that the buffer is not empty
            if (imageBuffer.length === 0) {
                throw new Error('Empty buffer after decoding');
            }
        } catch (error) {
            console.error('Error decoding base64 data:', error);
            return res.status(400).json({
                success: false,
                message: 'Invalid base64 encoding: ' + error.message
            });
        }

        // Validate decoded data size
        if (imageBuffer.length > 10 * 1024 * 1024) { // 10MB limit
            return res.status(400).json({
                success: false,
                message: 'Image is too large. Maximum size is 10MB'
            });
        }

        // Generate a unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        let ext = '.jpg'; // Default extension

        // Try to get extension from filename or mimetype
        if (filename && filename.includes('.')) {
            ext = filename.substring(filename.lastIndexOf('.'));
        } else if (mimetype) {
            // Map mimetype to extension
            const mimeToExt = {
                'image/jpeg': '.jpg',
                'image/png': '.png',
                'image/gif': '.gif',
                'image/webp': '.webp',
                'image/svg+xml': '.svg'
            };
            ext = mimeToExt[mimetype] || '.jpg';
        }

        // Create final filename
        const finalFilename = 'inventory-' + uniqueSuffix + ext;

        // Create full path
        const filePath = path.join(inventoryImagesDir, finalFilename);

        // Write the file
        fs.writeFile(filePath, imageBuffer, (err) => {
            if (err) {
                console.error('Error writing file:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error saving image file'
                });
            }

            // Set file permissions
            try {
                fs.chmodSync(filePath, 0o666); // Read/write for everyone
            } catch (chmodError) {
                console.error('Error setting file permissions (non-critical):', chmodError);
                // Continue anyway, the file is already saved
            }

            // Return success response
            const imageUrl = '/uploads/inventory/' + finalFilename;
            console.log('Image uploaded successfully via base64:', imageUrl);

            res.json({
                success: true,
                imageUrl: imageUrl,
                message: 'Image uploaded successfully'
            });
        });
    } catch (error) {
        console.error('Error processing base64 image upload:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing image upload',
            error: error.message
        });
    }
});

module.exports = router;
