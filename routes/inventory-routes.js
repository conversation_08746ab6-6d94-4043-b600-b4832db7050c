const express = require('express');
const router = express.Router();
const inventoryController = require('../controllers/inventory-controller');
const { checkAdmin } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create uploads directory if it doesn't exist
const inventoryImagesDir = path.join(__dirname, '../public/uploads/inventory');
if (!fs.existsSync(inventoryImagesDir)) {
    fs.mkdirSync(inventoryImagesDir, { recursive: true });
    console.log('Created upload directory:', inventoryImagesDir);
}

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, inventoryImagesDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        let ext = path.extname(file.originalname);

        // Default to .jpg if no extension
        if (!ext) {
            ext = '.jpg';
        }

        cb(null, 'inventory-' + uniqueSuffix + ext);
    }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
    console.log('Processing file:', file.originalname, 'Mimetype:', file.mimetype);

    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('Only image files are allowed! Received: ' + file.mimetype), false);
    }
};

// Configure multer upload with increased size limit
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        fieldSize: 10 * 1024 * 1024, // Also increase field size limit
        parts: 200, // Increase parts limit for complex forms
        fields: 100 // Increase fields limit
    },
    fileFilter: fileFilter
});

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Middleware to set layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.navbar = 'admin';
    next();
});

// Dashboard
router.get('/', inventoryController.index);

// Items management
router.get('/items', inventoryController.items);
router.get('/items/add', inventoryController.addItemForm);
router.post('/items/add', upload.single('item_image'), inventoryController.addItem);
router.get('/items/:id', inventoryController.viewItem);
router.get('/items/:id/edit', inventoryController.editItemForm);
router.post('/items/:id/edit', upload.single('item_image'), inventoryController.updateItem);
router.post('/items/:id/delete', inventoryController.deleteItem);

// Categories management
router.get('/categories', inventoryController.categories);
router.post('/categories/add', inventoryController.addCategory);
router.post('/categories/:id/edit', inventoryController.updateCategory);
router.post('/categories/:id/delete', inventoryController.deleteCategory);

// Transactions management
router.get('/transactions', inventoryController.transactions);
router.get('/transactions/issue', inventoryController.issueItemForm);
router.post('/transactions/issue', inventoryController.issueItem);
router.get('/transactions/receive', inventoryController.receiveItemForm);
router.post('/transactions/receive', inventoryController.receiveItem);
router.get('/items/:id/issue-history', inventoryController.itemIssueHistory);
router.get('/transactions/:id/loan-voucher', inventoryController.generateLoanVoucher);

// Test routes
router.get('/test-voucher', (req, res) => {
    res.render('admin/inventory/test-voucher', {
        title: 'Test Loan Voucher',
        pageTitle: 'Test Loan Voucher Generation',
        layout: 'admin',
        currentPage: 'inventory'
    });
});

// Reports
router.get('/reports', inventoryController.report);

module.exports = router;
