const express = require('express');
const router = express.Router();
const issueTrackerController = require('../controllers/issue-tracker-controller');
const { checkAuthenticated } = require('../middleware/auth');
const { checkAdmin } = require('../middleware/auth');
const fileUpload = require('express-fileupload');

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Middleware to set layout based on user role
router.use((req, res, next) => {
    if (req.session.userRole === 'admin') {
        res.locals.layout = 'admin';
        res.locals.navbar = 'admin';
    } else if (req.session.userRole === 'it_admin') {
        res.locals.layout = 'layouts/it-admin';
    } else {
        res.locals.layout = 'default';
    }
    next();
});

// Issue tracker dashboard
router.get('/', issueTrackerController.index);

// List all issues
router.get('/list', issueTrackerController.listIssues);

// Report new issue form
router.get('/report', issueTrackerController.reportIssueForm);

// Configure file upload middleware for issue reports
const issueFileUploadMiddleware = fileUpload({
    useTempFiles: true,
    tempFileDir: '/tmp/',
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    abortOnLimit: true,
    debug: true,
    createParentPath: true,
    safeFileNames: true,
    preserveExtension: true
});

// Process new issue report
router.post('/report', issueFileUploadMiddleware, issueTrackerController.reportIssue);

// View issue details
router.get('/:id', issueTrackerController.viewIssue);

// Add comment to issue
router.post('/:id/comment', issueTrackerController.addComment);

// Update issue status
router.post('/:id/status', issueTrackerController.updateStatus);

// Assign issue to admin
router.post('/:id/assign', issueTrackerController.assignIssue);

// Upload additional attachments
router.post('/:id/attachments', issueFileUploadMiddleware, issueTrackerController.uploadAttachments);

// Upload additional attachments using base64
router.post('/:id/attachments-base64', issueTrackerController.uploadAttachmentsBase64);

// Update issue priority
router.post('/:id/priority', issueTrackerController.updatePriority);

module.exports = router;
