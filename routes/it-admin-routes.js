const express = require('express');
const router = express.Router();
const { checkAuthenticated, checkITAdmin } = require('../middleware/auth');
const itAdminController = require('../controllers/it-admin-controller');
const procurementController = require('../controllers/procurement-controller');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const PDFDocument = require('pdfkit');
const { prepareVoucherResponse, generateVoucher } = require('../middleware/voucher-middleware');

// Create uploads directory if it doesn't exist
const inventoryImagesDir = path.join(__dirname, '../public/uploads/inventory');
if (!fs.existsSync(inventoryImagesDir)) {
    fs.mkdirSync(inventoryImagesDir, { recursive: true });
    console.log('Created upload directory:', inventoryImagesDir);
}

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, inventoryImagesDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname) || '.jpg';
        cb(null, 'inventory-' + uniqueSuffix + ext);
    }
});

// File filter to only allow image files and PDFs
const fileFilter = (req, file, cb) => {
    console.log('Processing file:', file.originalname, 'Mimetype:', file.mimetype);

    if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
        cb(null, true);
    } else {
        cb(new Error('Only image files and PDFs are allowed! Received: ' + file.mimetype), false);
    }
};

// Configure multer upload with increased size limit
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        fieldSize: 10 * 1024 * 1024, // Also increase field size limit
        parts: 200 // Increase parts limit for complex forms
    },
    fileFilter: fileFilter
});

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkITAdmin);

// Test route to check if routes are working
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'IT Admin routes are working!' });
});

// Test route for attachments
router.get('/test-attachments/:id', (req, res) => {
  const issueId = req.params.id;
  res.json({
    success: true,
    message: `Test attachments route for issue ${issueId}`,
    attachments: [
      {
        attachment_id: 1,
        issue_id: issueId,
        file_path: '/uploads/issues/test-image.jpg',
        file_name: 'test-image.jpg',
        file_type: 'image/jpeg',
        file_size: 12345,
        uploaded_by: 1,
        uploaded_at: new Date()
      }
    ]
  });
});

// IT Admin dashboard
router.get('/dashboard', itAdminController.getDashboard);

// Calendar view
router.get('/calendar', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get holidays
    let holidays = [];
    try {
      // Check if holiday_calendar table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'holiday_calendar'
      `);

      if (tables.length > 0) {
        [holidays] = await db.query(`
          SELECT id, holiday_date, description, holiday_type, is_active
          FROM holiday_calendar
          WHERE is_active = 1
          ORDER BY holiday_date
        `);
      }
    } catch (err) {
      console.log('Holiday calendar table may not exist yet:', err.message);
    }

    res.render('it-admin/calendar', {
      title: 'Academic Calendar',
      layout: 'layouts/it-admin',
      currentPage: 'calendar',
      holidays: holidays || [],
      user: res.locals.user
    });
  } catch (error) {
    console.error('Error fetching calendar data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch calendar data',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// IT Inventory routes
router.get('/inventory', async (req, res) => {
  try {
    const db = require('../config/database');
    const { category, status, search } = req.query;

    // Build query with filters
    let query = `
      SELECT i.*, c.name as category_name,
             CASE
               WHEN i.status = 'assigned' THEN
                 (SELECT username FROM users WHERE id =
                   (SELECT issued_to FROM inventory_transactions
                    WHERE item_id = i.item_id AND received_date IS NULL
                    ORDER BY issued_date DESC LIMIT 1))
               ELSE NULL
             END as assigned_username
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE 1=1
    `;

    const queryParams = [];

    // Apply category filter
    if (category) {
      query += ` AND i.category_id = ?`;
      queryParams.push(category);
    }

    // Apply status filter
    if (status) {
      query += ` AND i.status = ?`;
      queryParams.push(status);
    }

    // Apply search filter
    if (search) {
      query += ` AND (i.name LIKE ? OR i.serial_number LIKE ? OR i.model LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Add order by
    query += ` ORDER BY i.updated_at DESC`;

    // Execute query
    const [inventory] = await db.query(query, queryParams);

    // Get inventory statistics by category
    const [inventoryTypeStats] = await db.query(`
      SELECT
        c.name as type,
        COUNT(*) as count,
        SUM(CASE WHEN i.status = 'available' THEN 1 ELSE 0 END) as available,
        SUM(CASE WHEN i.status = 'assigned' THEN 1 ELSE 0 END) as assigned,
        SUM(CASE WHEN i.status = 'maintenance' THEN 1 ELSE 0 END) as in_repair,
        SUM(CASE WHEN i.status = 'retired' THEN 1 ELSE 0 END) as retired
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      GROUP BY c.name
    `);

    // Get categories for filter
    const [categories] = await db.query(`
      SELECT * FROM inventory_categories
      ORDER BY name
    `);

    // Prepare inventory stats
    const inventoryStats = {
      desktop: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      laptop: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      tablet: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      projector: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      printer: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      network: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 },
      other: { count: 0, available: 0, assigned: 0, in_repair: 0, retired: 0 }
    };

    // Process inventory stats
    inventoryTypeStats.forEach(stat => {
      const type = stat.type ? stat.type.toLowerCase() : 'other';

      if (type.includes('desktop') || type.includes('pc')) {
        inventoryStats.desktop.count += parseInt(stat.count || 0);
        inventoryStats.desktop.available += parseInt(stat.available || 0);
        inventoryStats.desktop.assigned += parseInt(stat.assigned || 0);
        inventoryStats.desktop.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.desktop.retired += parseInt(stat.retired || 0);
      } else if (type.includes('laptop') || type.includes('notebook')) {
        inventoryStats.laptop.count += parseInt(stat.count || 0);
        inventoryStats.laptop.available += parseInt(stat.available || 0);
        inventoryStats.laptop.assigned += parseInt(stat.assigned || 0);
        inventoryStats.laptop.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.laptop.retired += parseInt(stat.retired || 0);
      } else if (type.includes('tablet') || type.includes('ipad')) {
        inventoryStats.tablet.count += parseInt(stat.count || 0);
        inventoryStats.tablet.available += parseInt(stat.available || 0);
        inventoryStats.tablet.assigned += parseInt(stat.assigned || 0);
        inventoryStats.tablet.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.tablet.retired += parseInt(stat.retired || 0);
      } else if (type.includes('projector') || type.includes('display')) {
        inventoryStats.projector.count += parseInt(stat.count || 0);
        inventoryStats.projector.available += parseInt(stat.available || 0);
        inventoryStats.projector.assigned += parseInt(stat.assigned || 0);
        inventoryStats.projector.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.projector.retired += parseInt(stat.retired || 0);
      } else if (type.includes('printer') || type.includes('scanner')) {
        inventoryStats.printer.count += parseInt(stat.count || 0);
        inventoryStats.printer.available += parseInt(stat.available || 0);
        inventoryStats.printer.assigned += parseInt(stat.assigned || 0);
        inventoryStats.printer.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.printer.retired += parseInt(stat.retired || 0);
      } else if (type.includes('network') || type.includes('router') || type.includes('switch')) {
        inventoryStats.network.count += parseInt(stat.count || 0);
        inventoryStats.network.available += parseInt(stat.available || 0);
        inventoryStats.network.assigned += parseInt(stat.assigned || 0);
        inventoryStats.network.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.network.retired += parseInt(stat.retired || 0);
      } else {
        inventoryStats.other.count += parseInt(stat.count || 0);
        inventoryStats.other.available += parseInt(stat.available || 0);
        inventoryStats.other.assigned += parseInt(stat.assigned || 0);
        inventoryStats.other.in_repair += parseInt(stat.in_repair || 0);
        inventoryStats.other.retired += parseInt(stat.retired || 0);
      }
    });

    res.render('it-admin/inventory/index', {
      title: 'IT Inventory',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      inventory,
      inventoryStats,
      categories,
      filters: {
        category: category || '',
        status: status || '',
        search: search || ''
      }
    });
  } catch (error) {
    console.error('Error loading inventory:', error);
    req.flash('error', 'Error loading inventory');
    res.redirect('/it-admin/dashboard');
  }
});

// System monitoring routes
router.get('/system-monitoring', itAdminController.getSystemMonitoring);

// Add new inventory item form
router.get('/inventory/add', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get categories
    const [categories] = await db.query(`
      SELECT * FROM inventory_categories
      ORDER BY name
    `);

    res.render('it-admin/inventory/add', {
      title: 'Add Inventory Item',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      categories
    });
  } catch (error) {
    console.error('Error loading add item form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading form',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});



// Edit inventory item form
router.get('/inventory/:id/edit', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Get item details
    const [items] = await db.query(`
      SELECT i.*, c.name as category_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE i.item_id = ?
    `, [itemId]);

    if (items.length === 0) {
      req.flash('error', 'Item not found');
      return res.redirect('/it-admin/inventory');
    }

    // Get categories for dropdown
    const [categories] = await db.query(`
      SELECT * FROM inventory_categories
      ORDER BY name
    `);

    // Parse network info if exists
    let networkInfo = null;
    if (items[0].network_info) {
      try {
        networkInfo = JSON.parse(items[0].network_info);
      } catch (e) {
        console.error('Error parsing network info:', e);
      }
    }

    // Add cache-busting timestamp to image URL if it exists
    const item = items[0];
    if (item.image) {
      // Check if the image path is valid
      const fs = require('fs');
      const path = require('path');
      const imagePath = path.join(__dirname, '../public', item.image);

      // Check if the file exists
      if (fs.existsSync(imagePath)) {
        const timestamp = new Date().getTime();
        item.image = `${item.image}?t=${timestamp}`;
      } else {
        console.log(`Image file not found: ${imagePath}`);
        // Set to null to trigger the fallback image
        item.image = null;
      }
    }

    res.render('it-admin/inventory/edit', {
      title: 'Edit Inventory Item',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      item: item,
      categories,
      networkInfo
    });
  } catch (error) {
    console.error('Error loading edit item form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading form',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Process edit inventory item
router.post('/inventory/:id/edit', async (req, res) => {
  try {
    const db = require('../config/database');
    const fs = require('fs');
    const path = require('path');
    const { v4: uuidv4 } = require('uuid');

    const itemId = req.params.id;
    const {
      name, description, category_id, serial_number, model,
      manufacturer, purchase_date, purchase_cost, warranty_expiry,
      status, location, notes, keep_current_image,
      // Network information fields
      hostname, ip_address, mac_address
    } = req.body;

    // Validate required fields
    if (!name) {
      req.flash('error', 'Item name is required');
      return res.redirect(`/it-admin/inventory/${itemId}/edit`);
    }

    // Prepare network info JSON if any network fields are provided
    let networkInfo = null;
    if (hostname || ip_address || mac_address) {
      networkInfo = JSON.stringify({
        hostname: hostname || null,
        ip_address: ip_address || null,
        mac_address: mac_address || null
      });
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Get current item data
      const [items] = await connection.query('SELECT * FROM inventory_items WHERE item_id = ?', [itemId]);
      if (items.length === 0) {
        throw new Error('Item not found');
      }

      const currentItem = items[0];

      // Process image uploads
      let primaryImage = null;
      const imageUrls = [];

      console.log('Files:', req.files);

      if (req.files && req.files.item_images) {
        const uploadDir = path.join(__dirname, '../public/uploads/inventory');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Handle multiple images
        const images = Array.isArray(req.files.item_images)
          ? req.files.item_images
          : [req.files.item_images];

        console.log(`Processing ${images.length} images`);

        // Limit to 5 images
        const maxImages = 5;
        const imagesToProcess = images.slice(0, maxImages);

        for (const image of imagesToProcess) {
          // Generate unique filename
          const uniqueFilename = `${uuidv4()}_${image.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
          const filePath = path.join(uploadDir, uniqueFilename);
          const fileUrl = `/uploads/inventory/${uniqueFilename}`;

          console.log(`Moving file to ${filePath}`);

          // Move file to uploads directory
          await image.mv(filePath);

          // Add to image URLs array
          imageUrls.push(fileUrl);

          // Set the first image as the primary image
          if (!primaryImage) {
            primaryImage = fileUrl;
          }
        }

        console.log(`Processed ${imageUrls.length} images, primary image: ${primaryImage}`);
      } else {
        console.log('No files uploaded');
      }

      // If no new images were uploaded and keep_current_image is checked, keep the current image
      if (!primaryImage && keep_current_image === '1') {
        primaryImage = currentItem.image;
        console.log(`Keeping current image: ${primaryImage}`);
      }

      // Update inventory item with primary image
      await connection.query(`
        UPDATE inventory_items
        SET name = ?, description = ?, category_id = ?, serial_number = ?, model = ?,
            manufacturer = ?, purchase_date = ?, purchase_cost = ?, warranty_expiry = ?,
            status = ?, location = ?, notes = ?, image = ?, network_info = ?,
            updated_at = NOW()
        WHERE item_id = ?
      `, [
        name,
        description || null,
        category_id || null,
        serial_number || null,
        model || null,
        manufacturer || null,
        purchase_date || null,
        purchase_cost || null,
        warranty_expiry || null,
        status || 'available',
        location || null,
        notes || null,
        primaryImage,
        networkInfo,
        itemId
      ]);

      // If new images were uploaded, store additional images in the item_images table
      if (imageUrls.length > 1) {
        // Delete existing additional images
        await connection.query('DELETE FROM item_images WHERE item_id = ?', [itemId]);

        // Skip the first image as it's already stored as the primary image
        for (let i = 1; i < imageUrls.length; i++) {
          await connection.query(`
            INSERT INTO item_images (
              item_id, image_url, uploaded_by, uploaded_at
            ) VALUES (?, ?, ?, NOW())
          `, [
            itemId,
            imageUrls[i],
            req.session.userId
          ]);
        }
      }

      await connection.commit();

      req.flash('success', 'Inventory item updated successfully');
      res.redirect('/it-admin/inventory');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating inventory item:', error);
    req.flash('error', 'Error updating inventory item');
    res.redirect(`/it-admin/inventory/${req.params.id}/edit`);
  }
});

// Inventory item details
router.get('/inventory/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Get item details
    const [items] = await db.query(`
      SELECT i.*, c.name as category_name, u.username as created_by_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      LEFT JOIN users u ON i.created_by = u.id
      WHERE i.item_id = ?
    `, [itemId]);

    if (items.length === 0) {
      return res.status(404).render('error', {
        title: 'Error',
        message: 'Item not found',
        error: { status: 404, stack: 'Item not found' },
        layout: 'layouts/it-admin'
      });
    }

    // Get transaction history
    const [transactions] = await db.query(`
      SELECT t.*,
             u1.username as issued_by_name,
             u2.username as issued_to_name,
             u3.username as received_by_name
      FROM inventory_transactions t
      LEFT JOIN users u1 ON t.issued_by = u1.id
      LEFT JOIN users u2 ON t.issued_to = u2.id
      LEFT JOIN users u3 ON t.received_by = u3.id
      WHERE t.item_id = ?
      ORDER BY t.issued_date DESC
    `, [itemId]);

    // Get hardware condition
    const [conditions] = await db.query(`
      SELECT hc.*, hp.part_name, hp.display_name, u.username as checked_by_name
      FROM hardware_condition hc
      JOIN hardware_parts hp ON hc.part_id = hp.part_id
      LEFT JOIN users u ON hc.checked_by = u.id
      WHERE hc.item_id = ?
      ORDER BY hp.display_name
    `, [itemId]);

    // Add cache-busting timestamp to image URL if it exists
    const item = items[0];
    if (item.image) {
      const timestamp = new Date().getTime();
      item.image = `${item.image}?t=${timestamp}`;
    }

    res.render('it-admin/inventory/view', {
      title: 'Inventory Item Details',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      item: item,
      transactions,
      conditions,
      formatDate: (date) => {
        return date ? new Date(date).toLocaleDateString() : 'N/A';
      },
      formatDateTime: (date) => {
        return date ? new Date(date).toLocaleString() : 'N/A';
      }
    });
  } catch (error) {
    console.error('Error fetching item details:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading item details',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Export inventory data - This route is now handled by the consolidated export route below

// Process add new inventory item (simplified version without file uploads)
router.post('/inventory/add-simple', async (req, res) => {
  try {
    const db = require('../config/database');
    const fs = require('fs');
    const path = require('path');
    const { v4: uuidv4 } = require('uuid');

    console.log('Processing inventory item add request');
    console.log('Request body:', req.body);
    console.log('Files:', req.files);
    console.log('Content-Type:', req.headers['content-type']);

    // Debug the request
    console.log('Request method:', req.method);
    console.log('Request path:', req.path);
    console.log('Request headers:', req.headers);

    // Check if express-fileupload middleware is working
    if (!req.files) {
      console.log('No files object found in request. Express-fileupload middleware may not be applied.');
    } else {
      console.log('Express-fileupload middleware is working. Files object found in request.');
    }

    // Check if req.body is empty
    if (!req.body || Object.keys(req.body).length === 0) {
      console.error('Request body is empty');
      req.flash('error', 'No form data received. Please check your form submission.');
      return res.redirect('/it-admin/inventory/add');
    }

    // Extract form data
    let name, description, category_id, serial_number, model,
        manufacturer, purchase_date, purchase_cost, warranty_expiry,
        status, location, notes, hostname, ip_address, mac_address,
        physical_damage, keyboard_condition, touchpad_condition,
        display_condition, battery_condition, charger_condition;

    try {
      // Try to extract form fields
      name = req.body.name;
      description = req.body.description;
      category_id = req.body.category_id;
      serial_number = req.body.serial_number;
      model = req.body.model;
      manufacturer = req.body.manufacturer;
      purchase_date = req.body.purchase_date;
      purchase_cost = req.body.purchase_cost;
      warranty_expiry = req.body.warranty_expiry;
      status = req.body.status;
      location = req.body.location;
      notes = req.body.notes;
      hostname = req.body.hostname;
      ip_address = req.body.ip_address;
      mac_address = req.body.mac_address;
      physical_damage = req.body.physical_damage;
      keyboard_condition = req.body.keyboard_condition;
      touchpad_condition = req.body.touchpad_condition;
      display_condition = req.body.display_condition;
      battery_condition = req.body.battery_condition;
      charger_condition = req.body.charger_condition;
    } catch (error) {
      console.error('Error extracting form fields:', error);
    }

    console.log('Extracted name:', name);

    // Validate required fields
    const requiredFields = [
      { field: name, name: 'Item Name' },
      { field: category_id, name: 'Category' },
      { field: serial_number, name: 'Serial Number' },
      { field: model, name: 'Model' },
      { field: manufacturer, name: 'Manufacturer' },
      { field: purchase_date, name: 'Purchase Date' },
      { field: warranty_expiry, name: 'Warranty Expiry' },
      { field: purchase_cost, name: 'Purchase Cost' }
    ];

    const missingFields = requiredFields
      .filter(field => !field.field || field.field.toString().trim() === '')
      .map(field => field.name);

    if (missingFields.length > 0) {
      console.error('Missing required fields:', missingFields);
      req.flash('error', `The following fields are required: ${missingFields.join(', ')}`);
      return res.redirect('/it-admin/inventory/add');
    }

    // Prepare network info JSON if any network fields are provided
    let networkInfo = null;
    if (hostname || ip_address || mac_address) {
      networkInfo = JSON.stringify({
        hostname: hostname || null,
        ip_address: ip_address || null,
        mac_address: mac_address || null
      });
    }

    // Prepare hardware condition JSON if any condition fields are provided
    let hardwareCondition = null;
    if (category_id && (physical_damage || keyboard_condition || touchpad_condition ||
        display_condition || battery_condition || charger_condition)) {
      hardwareCondition = JSON.stringify({
        physical_damage: physical_damage || 'None',
        keyboard: keyboard_condition || 'Working',
        touchpad: touchpad_condition || 'Working',
        display: display_condition || 'Perfect',
        battery: battery_condition || 'Good',
        charger: charger_condition || 'Included'
      });
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // No file uploads in this simplified version
      let primaryImage = null;
      const imageUrls = [];

      // Insert new inventory item with primary image
      const [result] = await connection.query(`
        INSERT INTO inventory_items (
          name, description, category_id, serial_number, model,
          manufacturer, purchase_date, purchase_cost, warranty_expiry,
          status, location, notes, image, network_info, created_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        name,
        description || null,
        category_id || null,
        serial_number || null,
        model || null,
        manufacturer || null,
        purchase_date || null,
        purchase_cost || null,
        warranty_expiry || null,
        status || 'available',
        location || null,
        notes || null,
        primaryImage,
        networkInfo,
        req.session.userId
      ]);

      const itemId = result.insertId;

      // Store additional images in the item_images table
      if (imageUrls.length > 1) {
        // Skip the first image as it's already stored as the primary image
        for (let i = 1; i < imageUrls.length; i++) {
          await connection.query(`
            INSERT INTO item_images (
              item_id, image_url, uploaded_by, uploaded_at
            ) VALUES (?, ?, ?, NOW())
          `, [
            itemId,
            imageUrls[i],
            req.session.userId
          ]);
        }
      }

      // If hardware condition data is available, insert it
      if (hardwareCondition && itemId) {
        // Get hardware parts for laptops
        const [parts] = await connection.query(`
          SELECT part_id FROM hardware_parts
          WHERE category_id = ? OR category_id IS NULL
        `, [category_id]);

        // Insert condition records for each part
        for (const part of parts) {
          await connection.query(`
            INSERT INTO hardware_condition (
              item_id, part_id, condition_value, checked_by, checked_at
            ) VALUES (?, ?, ?, ?, NOW())
          `, [
            itemId,
            part.part_id,
            'Working', // Default value
            req.session.userId
          ]);
        }
      }

      await connection.commit();

      // Set success flash message and redirect
      req.flash('success', 'Inventory item added successfully');
      res.redirect('/it-admin/inventory');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error adding inventory item:', error);
    console.error('Error stack:', error.stack);

    // Set error flash message and redirect
    req.flash('error', 'Error adding inventory item: ' + error.message);
    res.redirect('/it-admin/inventory/add');
  }
});

// Device transactions routes
router.get('/transactions', async (req, res) => {
  try {
    const db = require('../config/database');
    const { device_type, status, search } = req.query;

    // Build query with filters
    let query = `
      SELECT t.*, i.name as item_name, i.serial_number,
             u1.username as issued_by_name, u2.username as issued_to_name,
             c.name as category_name
      FROM inventory_transactions t
      JOIN inventory_items i ON t.item_id = i.item_id
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      LEFT JOIN users u1 ON t.issued_by = u1.id
      LEFT JOIN users u2 ON t.issued_to = u2.id
      WHERE 1=1
    `;

    const queryParams = [];

    // Apply device type filter
    if (device_type) {
      query += ` AND i.category_id = ?`;
      queryParams.push(device_type);
    }

    // Apply status filter
    if (status) {
      if (status === 'issued') {
        query += ` AND t.received_date IS NULL`;
      } else if (status === 'returned') {
        query += ` AND t.received_date IS NOT NULL`;
      }
    }

    // Apply search filter
    if (search) {
      query += ` AND (i.name LIKE ? OR i.serial_number LIKE ? OR u1.username LIKE ? OR u2.username LIKE ?)`;
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Add order by
    query += ` ORDER BY t.issued_date DESC LIMIT 100`;

    // Execute query
    const [transactions] = await db.query(query, queryParams);

    // Format dates
    transactions.forEach(transaction => {
      transaction.issued_date = transaction.issued_date ? new Date(transaction.issued_date).toLocaleDateString() : 'N/A';
      transaction.received_date = transaction.received_date ? new Date(transaction.received_date).toLocaleDateString() : null;
      transaction.expected_return_date = transaction.expected_return_date ? new Date(transaction.expected_return_date).toLocaleDateString() : null;
    });

    // Get categories for filter dropdown
    const [categories] = await db.query(`
      SELECT * FROM inventory_categories
      ORDER BY name
    `);

    res.render('it-admin/transactions', {
      title: 'Device Transactions',
      layout: 'layouts/it-admin',
      currentPage: 'transactions',
      transactions,
      categories,
      filters: {
        device_type: device_type || '',
        status: status || '',
        search: search || ''
      },
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error fetching device transactions:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading device transactions',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Legacy route for backward compatibility
router.get('/laptop-transactions', (req, res) => {
  res.redirect('/it-admin/transactions');
});

// Issue device form
router.get('/transactions/issue', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get all available devices
    const [items] = await db.query(`
      SELECT i.item_id, i.name, i.serial_number, i.model, c.name as category_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE i.status = 'available'
      ORDER BY c.name, i.name
    `);

    // Get users
    const [users] = await db.query(`
      SELECT id, username, name, role
      FROM users
      WHERE is_active = 1
      ORDER BY username
    `);

    // Get categories for filter dropdown
    const [categories] = await db.query(`
      SELECT category_id, name
      FROM inventory_categories
      ORDER BY name
    `);

    res.render('it-admin/device-issue', {
      title: 'Issue Device',
      layout: 'layouts/it-admin',
      currentPage: 'transactions',
      items,
      users,
      categories,
      query: req.query,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading issue device form:', error);
    req.flash('error', 'Error loading form');
    res.redirect('/it-admin/transactions');
  }
});

// Legacy route for backward compatibility
router.get('/laptop-transactions/issue', (req, res) => {
  res.redirect('/it-admin/transactions/issue');
});

// Process issue device
router.post('/transactions/issue', async (req, res) => {
  try {
    const db = require('../config/database');
    const { item_id, issued_to, expected_return_date, condition_on_issue, notes } = req.body;

    // Validate required fields
    if (!item_id || !issued_to) {
      req.flash('error', 'Device and user are required');
      return res.redirect('/it-admin/transactions/issue');
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Create transaction record
      await connection.query(`
        INSERT INTO inventory_transactions (
          item_id, transaction_type, issued_to, issued_by,
          issued_date, expected_return_date, condition_on_issue, notes
        ) VALUES (?, 'issue', ?, ?, NOW(), ?, ?, ?)
      `, [
        item_id, issued_to, req.session.userId,
        expected_return_date || null, condition_on_issue || null, notes || null
      ]);

      // Update item status
      await connection.query(`
        UPDATE inventory_items
        SET status = 'assigned'
        WHERE item_id = ?
      `, [item_id]);

      await connection.commit();

      req.flash('success', 'Device issued successfully');
      res.redirect('/it-admin/transactions');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error issuing device:', error);
    req.flash('error', 'Error issuing device');
    res.redirect('/it-admin/transactions/issue');
  }
});

// Legacy route for backward compatibility
router.post('/laptop-transactions/issue', async (req, res) => {
  // Forward the request to the new endpoint
  req.url = '/it-admin/transactions/issue';
  req.app.handle(req, res);
});

// Receive device form
router.get('/transactions/receive', async (req, res) => {
  try {
    const db = require('../config/database');
    const selectedTransactionId = req.query.transaction || null;

    // Get all assigned devices
    const [transactions] = await db.query(`
      SELECT t.transaction_id, t.item_id, i.name as item_name,
             i.serial_number, u.username as issued_to_name,
             t.issued_date, t.expected_return_date, c.name as category_name
      FROM inventory_transactions t
      JOIN inventory_items i ON t.item_id = i.item_id
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      JOIN users u ON t.issued_to = u.id
      WHERE t.transaction_type = 'issue'
      AND t.received_date IS NULL
      ORDER BY t.issued_date DESC
    `);

    // Format dates
    transactions.forEach(transaction => {
      transaction.issued_date = transaction.issued_date ? new Date(transaction.issued_date).toLocaleDateString() : 'N/A';
      transaction.expected_return_date = transaction.expected_return_date ? new Date(transaction.expected_return_date).toLocaleDateString() : 'Not specified';
    });

    res.render('it-admin/device-receive', {
      title: 'Receive Device',
      layout: 'layouts/it-admin',
      currentPage: 'transactions',
      transactions,
      selectedTransactionId,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading receive device form:', error);
    req.flash('error', 'Error loading form');
    res.redirect('/it-admin/transactions');
  }
});

// Legacy route for backward compatibility
router.get('/laptop-transactions/receive', (req, res) => {
  res.redirect('/it-admin/transactions/receive' + (req.query.transaction ? '?transaction=' + req.query.transaction : ''));
});

// Process receive device
router.post('/transactions/receive', async (req, res) => {
  try {
    const db = require('../config/database');
    const { transaction_id, condition_on_return, notes, create_issue_ticket, quick_condition, quick_condition_notes } = req.body;

    // Validate required fields
    if (!transaction_id) {
      req.flash('error', 'Transaction is required');
      return res.redirect('/it-admin/transactions/receive');
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Get item_id and device details from transaction
      const [transactionResult] = await connection.query(`
        SELECT t.item_id, i.name, i.serial_number, c.name as category_name
        FROM inventory_transactions t
        JOIN inventory_items i ON t.item_id = i.item_id
        LEFT JOIN inventory_categories c ON i.category_id = c.category_id
        WHERE t.transaction_id = ?
      `, [transaction_id]);

      if (transactionResult.length === 0) {
        throw new Error('Transaction not found');
      }

      const item_id = transactionResult[0].item_id;
      const deviceName = transactionResult[0].name;
      const serialNumber = transactionResult[0].serial_number;
      const categoryName = transactionResult[0].category_name || 'Device';

      // Update transaction record
      await connection.query(`
        UPDATE inventory_transactions
        SET received_date = NOW(), received_by = ?, condition_on_return = ?, return_notes = ?
        WHERE transaction_id = ?
      `, [
        req.session.userId, condition_on_return || null, notes || null, transaction_id
      ]);

      // Update item status
      await connection.query(`
        UPDATE inventory_items
        SET status = 'available'
        WHERE item_id = ?
      `, [item_id]);

      // Create issue ticket if requested
      let issueId = null;
      if (create_issue_ticket === '1') {
        const title = `Hardware issue with ${deviceName}`;

        // Build description from selected issues
        let issueDescription = `Issue reported during ${categoryName.toLowerCase()} return.\n\n${categoryName}: ${deviceName} (${serialNumber || 'No S/N'})\n\nCondition: ${condition_on_return}\n\n`;

        if (quick_condition && Array.isArray(quick_condition)) {
          issueDescription += 'Issues reported:\n';
          quick_condition.forEach(issue => {
            issueDescription += `- ${issue.replace(/_/g, ' ')}\n`;
          });
        }

        if (quick_condition_notes) {
          issueDescription += `\nAdditional details: ${quick_condition_notes}\n`;
        }

        if (notes) {
          issueDescription += `\nNotes: ${notes}`;
        }

        // Create issue
        const [issueResult] = await connection.query(`
          INSERT INTO it_issues (
            title, description, item_id, reported_by, issue_type, priority, status
          ) VALUES (?, ?, ?, ?, 'hardware', 'medium', 'open')
        `, [
          title, issueDescription, item_id, req.session.userId
        ]);

        issueId = issueResult.insertId;
      }

      await connection.commit();

      if (issueId) {
        req.flash('success', `${categoryName} received and issue ticket #${issueId} created.`);
        return res.redirect(`/it-admin/issues/${issueId}`);
      }

      req.flash('success', `${categoryName} received successfully`);
      res.redirect('/it-admin/transactions');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error receiving device:', error);
    req.flash('error', 'Error receiving device');
    res.redirect('/it-admin/transactions/receive');
  }
});

// Legacy route for backward compatibility
router.post('/laptop-transactions/receive', async (req, res) => {
  // Forward the request to the new endpoint
  req.url = '/it-admin/transactions/receive';
  req.app.handle(req, res);
});

// Issue tracker routes
router.get('/issues', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get filter parameters
    const status = req.query.status || '';
    const issueType = req.query.issue_type || '';
    const search = req.query.search || '';
    const priority = req.query.priority || '';
    const dateRange = req.query.date_range || '';

    // Build query with filters
    let query = `
      SELECT i.issue_id, i.title, i.description, i.item_id, i.issue_type, i.priority, i.status,
             i.resolution_notes, i.reported_by, i.created_at, i.updated_at,
             ii.name as item_name, u.username as reported_by_name
      FROM it_issues i
      LEFT JOIN inventory_items ii ON i.item_id = ii.item_id
      LEFT JOIN users u ON i.reported_by = u.id
      WHERE 1=1
    `;

    const queryParams = [];

    if (status) {
      query += ` AND i.status = ?`;
      queryParams.push(status);
    }

    if (issueType) {
      query += ` AND i.issue_type = ?`;
      queryParams.push(issueType);
    }

    if (priority) {
      query += ` AND i.priority = ?`;
      queryParams.push(priority);
    }

    if (dateRange) {
      const [startDate, endDate] = dateRange.split('to').map(date => date.trim());
      if (startDate && endDate) {
        query += ` AND i.created_at BETWEEN ? AND ?`;
        queryParams.push(`${startDate} 00:00:00`, `${endDate} 23:59:59`);
      } else if (startDate) {
        query += ` AND DATE(i.created_at) = ?`;
        queryParams.push(startDate);
      }
    }

    if (search) {
      query += ` AND (i.title LIKE ? OR i.description LIKE ? OR ii.name LIKE ? OR u.username LIKE ?)`;
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    query += ` ORDER BY i.created_at DESC`;

    // Get issues with filters
    const [issues] = await db.query(query, queryParams);

    // Get issue statistics
    const [issueStats] = await db.query(`
      SELECT
        COUNT(*) as totalIssues,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as openIssues,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inProgressIssues,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolvedIssues,
        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closedIssues
      FROM it_issues
    `);

    // Get issue type statistics
    const [issueTypeStats] = await db.query(`
      SELECT issue_type, COUNT(*) as count
      FROM it_issues
      GROUP BY issue_type
    `);

    // Get inventory items for the edit modal
    const [items] = await db.query(`
      SELECT item_id, name, serial_number
      FROM inventory_items
      ORDER BY name
    `);

    res.render('it-admin/issues/index', {
      title: 'IT Issues',
      layout: 'layouts/it-admin',
      currentPage: 'issues',
      issues,
      items,
      stats: issueStats[0],
      issueTypeStats: issueTypeStats.reduce((acc, curr) => {
        acc[curr.issue_type] = curr.count;
        return acc;
      }, {}),
      filters: {
        status,
        issueType,
        priority,
        dateRange,
        search
      },
      formatDate: (date) => {
        if (!date) return 'N/A';
        const d = new Date(date);
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const day = d.getDate().toString().padStart(2, '0');
        const month = months[d.getMonth()];
        const year = d.getFullYear();
        return `${day}-${month}-${year}`;
      },
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error fetching issues:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading issues',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Create issue form
router.get('/issues/create', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get inventory items for dropdown
    const [items] = await db.query(`
      SELECT item_id, name, serial_number
      FROM inventory_items
      ORDER BY name
    `);

    res.render('it-admin/issues/create', {
      title: 'Report IT Issue',
      layout: 'layouts/it-admin',
      currentPage: 'issues',
      items,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading create issue form:', error);
    req.flash('error', 'Error loading form');
    res.redirect('/it-admin/issues');
  }
});

// View issue details
router.get('/issues/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const issueId = req.params.id;

    // Get issue details
    const [issues] = await db.query(`
      SELECT i.*, ii.name as item_name, ii.serial_number, u.username as reported_by_name
      FROM it_issues i
      LEFT JOIN inventory_items ii ON i.item_id = ii.item_id
      LEFT JOIN users u ON i.reported_by = u.id
      WHERE i.issue_id = ?
    `, [issueId]);

    if (issues.length === 0) {
      req.flash('error', 'Issue not found');
      return res.redirect('/it-admin/issues');
    }

    res.render('it-admin/issues/view', {
      title: 'Issue Details',
      layout: 'layouts/it-admin',
      currentPage: 'issues',
      issue: issues[0],
      formatDate: (date) => {
        if (!date) return 'N/A';
        const d = new Date(date);
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const day = d.getDate().toString().padStart(2, '0');
        const month = months[d.getMonth()];
        const year = d.getFullYear();
        return `${day}-${month}-${year}`;
      },
      formatDateTime: (date) => {
        if (!date) return 'N/A';
        const d = new Date(date);
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const day = d.getDate().toString().padStart(2, '0');
        const month = months[d.getMonth()];
        const year = d.getFullYear();
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        return `${day}-${month}-${year} ${hours}:${minutes}`;
      },
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading issue details:', error);
    req.flash('error', 'Error loading issue details');
    res.redirect('/it-admin/issues');
  }
});

// Edit issue form
router.get('/issues/:id/edit', async (req, res) => {
  try {
    const db = require('../config/database');
    const issueId = req.params.id;

    // Get issue details
    const [issues] = await db.query(`
      SELECT i.*, ii.name as item_name
      FROM it_issues i
      LEFT JOIN inventory_items ii ON i.item_id = ii.item_id
      WHERE i.issue_id = ?
    `, [issueId]);

    if (issues.length === 0) {
      req.flash('error', 'Issue not found');
      return res.redirect('/it-admin/issues');
    }

    // Get inventory items for dropdown
    const [items] = await db.query(`
      SELECT item_id, name, serial_number
      FROM inventory_items
      ORDER BY name
    `);

    res.render('it-admin/issues/edit', {
      title: 'Edit Issue',
      layout: 'layouts/it-admin',
      currentPage: 'issues',
      issue: issues[0],
      items,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading edit issue form:', error);
    req.flash('error', 'Error loading edit form');
    res.redirect('/it-admin/issues');
  }
});

// Process edit issue
router.post('/issues/:id/edit', async (req, res) => {
  try {
    const db = require('../config/database');
    const path = require('path');
    const fs = require('fs');
    const { v4: uuidv4 } = require('uuid');
    const issueId = req.params.id;
    const isJsonRequest = req.headers['content-type'] && req.headers['content-type'].includes('application/json');
    const isMultipartRequest = req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data');

    console.log("Edit issue request received for ID:", issueId);
    console.log("Content-Type:", req.headers['content-type']);
    console.log("Request body:", req.body);
    console.log("Files:", req.files);

    // Parse body based on content type
    const { title, description, item_id, issue_type, priority, status, resolution_notes } = req.body;

    // Validate required fields
    if (!title || !description) {
      // Check if request is JSON
      if (isJsonRequest) {
        return res.status(400).json({ success: false, message: 'Title and description are required' });
      }
      req.flash('error', 'Title and description are required');
      return res.redirect(`/it-admin/issues/${issueId}/edit`);
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Update issue
      await connection.query(`
        UPDATE it_issues
        SET title = ?, description = ?, item_id = ?, issue_type = ?,
            priority = ?, status = ?, resolution_notes = ?, updated_at = NOW()
        WHERE issue_id = ?
      `, [
        title,
        description,
        item_id === '' ? null : item_id,
        issue_type || 'hardware',
        priority || 'medium',
        status || 'open',
        resolution_notes || null,
        issueId
      ]);

      // Check if issue was updated
      const [updatedIssue] = await connection.query('SELECT * FROM it_issues WHERE issue_id = ?', [issueId]);
      if (updatedIssue.length === 0) {
        await connection.rollback();
        if (isJsonRequest || isMultipartRequest) {
          return res.status(404).json({ success: false, message: 'Issue not found' });
        }
        req.flash('error', 'Issue not found');
        return res.redirect('/it-admin/issues');
      }

      // Handle file uploads if any
      if (req.files && req.files.photos) {
        console.log("Processing file uploads...");
        const photos = Array.isArray(req.files.photos) ? req.files.photos : [req.files.photos];
        const uploadDir = path.join(__dirname, '../public/uploads/issues');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Process each photo
        for (const photo of photos) {
          // Skip empty files
          if (!photo.size) {
            console.log("Skipping empty file");
            continue;
          }

          console.log("Processing file:", photo.name, "Size:", photo.size);

          try {
            // Generate unique filename
            const uniqueFilename = `${uuidv4()}_${photo.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
            const filePath = path.join(uploadDir, uniqueFilename);

            // Move file to uploads directory
            await photo.mv(filePath);
            console.log("File moved to:", filePath);

            // Save attachment record
            await connection.query(`
              INSERT INTO issue_attachments (
                issue_id, file_path, file_name, file_type, file_size, uploaded_by
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
              issueId,
              `/uploads/issues/${uniqueFilename}`,
              photo.name,
              photo.mimetype,
              photo.size,
              req.session.userId || 1 // Default to admin if no user ID
            ]);
            console.log("Attachment record saved to database");
          } catch (fileError) {
            console.error("Error processing file:", fileError);
            // Continue with other files even if one fails
          }
        }
      } else {
        console.log("No files to process");
      }

      // Commit transaction
      await connection.commit();
      console.log("Transaction committed successfully");

      // Return response based on request type
      if (isJsonRequest || isMultipartRequest) {
        return res.status(200).json({ success: true, message: 'Issue updated successfully' });
      }

      req.flash('success', 'Issue updated successfully');
      res.redirect(`/it-admin/issues/${issueId}`);
    } catch (error) {
      console.error("Error in transaction:", error);
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating issue:', error);

    // Check if request is JSON or multipart
    if (req.headers['content-type'] &&
        (req.headers['content-type'].includes('application/json') ||
         req.headers['content-type'].includes('multipart/form-data'))) {
      return res.status(500).json({ success: false, message: 'Error updating issue: ' + error.message });
    }

    req.flash('error', 'Error updating issue');
    res.redirect(`/it-admin/issues/${req.params.id}/edit`);
  }
});

// Get issue attachments
router.get('/issues/:id/attachments', async (req, res) => {
  console.log(`Getting attachments for issue ${req.params.id}`);
  try {
    const db = require('../config/database');
    const issueId = req.params.id;

    if (!issueId || isNaN(parseInt(issueId))) {
      console.error('Invalid issue ID:', issueId);
      return res.status(400).json({
        success: false,
        message: 'Invalid issue ID'
      });
    }

    // First check if the issue exists
    const [issues] = await db.query(`
      SELECT issue_id FROM it_issues WHERE issue_id = ?
    `, [issueId]);

    if (issues.length === 0) {
      console.error('Issue not found:', issueId);
      return res.status(404).json({
        success: false,
        message: 'Issue not found'
      });
    }

    // Get attachments for the issue
    const [attachments] = await db.query(`
      SELECT a.*, u.username as uploaded_by_name
      FROM issue_attachments a
      LEFT JOIN users u ON a.uploaded_by = u.id
      WHERE a.issue_id = ?
      ORDER BY a.uploaded_at DESC
    `, [issueId]);

    console.log(`Found ${attachments.length} attachments for issue ${issueId}`);

    return res.json({
      success: true,
      attachments
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching attachments: ' + error.message
    });
  }
});

// Process edit issue with base64 encoded files
router.post('/issues/:id/edit-base64', async (req, res) => {
  try {
    const db = require('../config/database');
    const path = require('path');
    const fs = require('fs');
    const { v4: uuidv4 } = require('uuid');
    const issueId = req.params.id;

    console.log("Edit issue request received for ID (base64 method):", issueId);

    // Extract data from request body
    const { title, description, item_id, issue_type, priority, status, resolution_notes, photos } = req.body;

    // Validate required fields
    if (!title || !description) {
      return res.status(400).json({ success: false, message: 'Title and description are required' });
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Update issue
      await connection.query(`
        UPDATE it_issues
        SET title = ?, description = ?, item_id = ?, issue_type = ?,
            priority = ?, status = ?, resolution_notes = ?, updated_at = NOW()
        WHERE issue_id = ?
      `, [
        title,
        description,
        item_id === '' ? null : item_id,
        issue_type || 'hardware',
        priority || 'medium',
        status || 'open',
        resolution_notes || null,
        issueId
      ]);

      // Check if issue was updated
      const [updatedIssue] = await connection.query('SELECT * FROM it_issues WHERE issue_id = ?', [issueId]);
      if (updatedIssue.length === 0) {
        await connection.rollback();
        return res.status(404).json({ success: false, message: 'Issue not found' });
      }

      // Handle base64 encoded files if any
      if (photos && photos.length > 0) {
        console.log(`Processing ${photos.length} base64 encoded files...`);
        const uploadDir = path.join(__dirname, '../public/uploads/issues');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Process each photo
        for (const photo of photos) {
          try {
            // Skip if no data
            if (!photo.data) {
              console.log("Skipping file with no data");
              continue;
            }

            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];
            if (!validTypes.includes(photo.type)) {
              console.log(`Invalid file type: ${photo.type}. Only JPG and PDF are allowed.`);
              continue;
            }

            // Extract base64 data
            const base64Data = photo.data.split(';base64,').pop();
            if (!base64Data) {
              console.log("Invalid base64 data format");
              continue;
            }

            // Generate unique filename with proper extension
            let fileExt;
            if (photo.type === 'application/pdf') {
              fileExt = 'pdf';
            } else {
              fileExt = 'jpg';
            }

            const uniqueFilename = `${uuidv4()}.${fileExt}`;
            const filePath = path.join(uploadDir, uniqueFilename);

            // Write file to disk
            fs.writeFileSync(filePath, base64Data, { encoding: 'base64' });
            console.log(`File saved to: ${filePath} (${photo.type})`);

            // Save attachment record
            await connection.query(`
              INSERT INTO issue_attachments (
                issue_id, file_path, file_name, file_type, file_size, uploaded_by
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
              issueId,
              `/uploads/issues/${uniqueFilename}`,
              photo.name,
              photo.type,
              photo.size || 0,
              req.session.userId || 1 // Default to admin if no user ID
            ]);
            console.log("Attachment record saved to database");
          } catch (fileError) {
            console.error("Error processing file:", fileError);
            // Continue with other files even if one fails
          }
        }
      } else {
        console.log("No base64 files to process");
      }

      // Commit transaction
      await connection.commit();
      console.log("Transaction committed successfully");

      return res.status(200).json({ success: true, message: 'Issue updated successfully' });
    } catch (error) {
      console.error("Error in transaction:", error);
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating issue (base64 method):', error);
    return res.status(500).json({ success: false, message: 'Error updating issue: ' + error.message });
  }
});

// Get issue attachments (alternative route)
router.get('/test-attachments-real/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const issueId = req.params.id;

    // Get attachments for the issue
    const [attachments] = await db.query(`
      SELECT a.*, u.username as uploaded_by_name
      FROM issue_attachments a
      LEFT JOIN users u ON a.uploaded_by = u.id
      WHERE a.issue_id = ?
      ORDER BY a.uploaded_at DESC
    `, [issueId]);

    return res.json({
      success: true,
      attachments
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching attachments: ' + error.message
    });
  }
});

// Delete attachment
router.post('/issues/attachments/:id/delete', async (req, res) => {
  try {
    const db = require('../config/database');
    const fs = require('fs');
    const path = require('path');
    const attachmentId = req.params.id;

    // Get attachment details
    const [attachments] = await db.query(`
      SELECT * FROM issue_attachments WHERE attachment_id = ?
    `, [attachmentId]);

    if (attachments.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    const attachment = attachments[0];

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Delete attachment record
      await connection.query(`
        DELETE FROM issue_attachments WHERE attachment_id = ?
      `, [attachmentId]);

      // Delete file from disk if it exists
      if (attachment.file_path) {
        const filePath = path.join(__dirname, '../public', attachment.file_path);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }

      await connection.commit();

      return res.json({
        success: true,
        message: 'Attachment deleted successfully'
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting attachment:', error);
    return res.status(500).json({
      success: false,
      message: 'Error deleting attachment: ' + error.message
    });
  }
});

// Delete issue
router.post('/issues/:id/delete', async (req, res) => {
  try {
    const db = require('../config/database');
    const fs = require('fs');
    const path = require('path');
    const issueId = req.params.id;
    const isJsonRequest = req.headers['content-type'] && req.headers['content-type'].includes('application/json');

    console.log("Delete issue request received for ID:", issueId);
    console.log("Content-Type:", req.headers['content-type']);

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Check if issue exists
      const [issues] = await connection.query('SELECT * FROM it_issues WHERE issue_id = ?', [issueId]);
      if (issues.length === 0) {
        await connection.rollback();
        return res.status(404).json({ success: false, message: 'Issue not found' });
      }

      // Get attachments for the issue
      const [attachments] = await connection.query('SELECT * FROM issue_attachments WHERE issue_id = ?', [issueId]);

      // Delete attachments from disk
      for (const attachment of attachments) {
        if (attachment.file_path) {
          const filePath = path.join(__dirname, '../public', attachment.file_path);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        }
      }

      // Delete the issue (this will cascade delete attachments due to foreign key constraint)
      await connection.query('DELETE FROM it_issues WHERE issue_id = ?', [issueId]);

      // Commit transaction
      await connection.commit();

      return res.status(200).json({ success: true, message: 'Issue deleted successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting issue:', error);
    return res.status(500).json({ success: false, message: 'Error deleting issue: ' + error.message });
  }
});

// Configure file upload middleware for issue creation
const fileUpload = require('express-fileupload');
const issueFileUploadMiddleware = fileUpload({
    useTempFiles: true,
    tempFileDir: '/tmp/',
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    abortOnLimit: true,
    debug: true,
    createParentPath: true,
    safeFileNames: true,
    preserveExtension: true
});

// Process create issue
router.post('/issues/create', issueFileUploadMiddleware, async (req, res) => {
  try {
    const db = require('../config/database');
    const path = require('path');
    const fs = require('fs');
    const { v4: uuidv4 } = require('uuid');
    const { title, description, item_id, issue_type, priority } = req.body;

    console.log("Create issue request received");
    console.log("Content-Type:", req.headers['content-type']);
    console.log("Request body:", req.body);
    console.log("Files:", req.files);

    // Validate required fields
    if (!title || !description) {
      req.flash('error', 'Title and description are required');
      return res.redirect('/it-admin/issues/create');
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Create issue
      const [result] = await connection.query(`
        INSERT INTO it_issues (
          title, description, item_id, reported_by, issue_type, priority, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'open', NOW())
      `, [
        title,
        description,
        item_id || null,
        req.session.userId,
        issue_type || 'hardware',
        priority || 'medium'
      ]);

      const issueId = result.insertId;

      // Handle file uploads if any
      if (req.files && req.files.photos) {
        console.log("Processing file uploads...");
        const photos = Array.isArray(req.files.photos) ? req.files.photos : [req.files.photos];
        const uploadDir = path.join(__dirname, '../public/uploads/issues');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Define validation constants
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
        const uploadedFiles = [];
        const errors = [];

        // Process each photo
        for (const photo of photos) {
          // Skip empty files
          if (!photo.size) {
            console.log("Skipping empty file");
            continue;
          }

          // Validate file type
          if (!validImageTypes.includes(photo.mimetype)) {
            errors.push(`File "${photo.name}" is not a valid image type. Allowed types: JPEG, PNG, GIF, WebP, BMP.`);
            continue;
          }

          // Validate file size
          if (photo.size > MAX_FILE_SIZE) {
            errors.push(`File "${photo.name}" exceeds the maximum file size of 5MB.`);
            continue;
          }

          console.log("Processing file:", photo.name, "Size:", photo.size);

          try {
            // Generate unique filename
            const uniqueFilename = `${uuidv4()}_${photo.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
            const filePath = path.join(uploadDir, uniqueFilename);

            // Move file to uploads directory
            await photo.mv(filePath);
            console.log("File moved to:", filePath);

            // Save attachment record
            await connection.query(`
              INSERT INTO issue_attachments (
                issue_id, file_path, file_name, file_type, file_size, uploaded_by
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
              issueId,
              `/uploads/issues/${uniqueFilename}`,
              photo.name,
              photo.mimetype,
              photo.size,
              req.session.userId || 1 // Default to admin if no user ID
            ]);
            console.log("Attachment record saved to database");

            uploadedFiles.push({
              name: photo.name,
              path: `/uploads/issues/${uniqueFilename}`
            });
          } catch (fileError) {
            console.error("Error processing file:", fileError);
            errors.push(`Error uploading "${photo.name}": ${fileError.message}`);
          }
        }

        // If there were errors, add them to the flash message
        if (errors.length > 0) {
          req.flash('warning', `Issue reported successfully, but some attachments failed: ${errors.join(', ')}`);
        }
      } else {
        console.log("No files to process");
      }

      // Commit transaction
      await connection.commit();
      console.log("Transaction committed successfully");

      req.flash('success', 'Issue reported successfully');
      res.redirect(`/it-admin/issues/${issueId}`);
    } catch (error) {
      console.error("Error in transaction:", error);
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error creating issue:', error);
    req.flash('error', 'Error creating issue: ' + error.message);
    res.redirect('/it-admin/issues/create');
  }
});

// Issue item form
router.get('/inventory/:id/issue', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Get item details
    const [items] = await db.query(`
      SELECT i.*, c.name as category_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE i.item_id = ? AND i.status = 'available'
    `, [itemId]);

    if (items.length === 0) {
      req.flash('error', 'Item not found or not available for issue');
      return res.redirect('/it-admin/inventory');
    }

    // Get users for dropdown
    const [users] = await db.query(`
      SELECT id, username, name, email
      FROM users
      WHERE is_active = 1
      ORDER BY username
    `);

    res.render('it-admin/inventory/issue', {
      title: 'Issue Item',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      item: items[0],
      users,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading issue item form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading form',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Process issue item
router.post('/inventory/:id/issue', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;
    const { issued_to, expected_return_date, condition_on_issue, notes } = req.body;

    // Validate required fields
    if (!issued_to) {
      req.flash('error', 'User is required');
      return res.redirect(`/it-admin/inventory/${itemId}/issue`);
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Create transaction record
      await connection.query(`
        INSERT INTO inventory_transactions (
          item_id, transaction_type, issued_to, issued_by,
          issued_date, expected_return_date, condition_on_issue, notes
        ) VALUES (?, 'issue', ?, ?, NOW(), ?, ?, ?)
      `, [
        itemId, issued_to, req.session.userId,
        expected_return_date || null, condition_on_issue || null, notes || null
      ]);

      // Update item status
      await connection.query(`
        UPDATE inventory_items
        SET status = 'assigned'
        WHERE item_id = ?
      `, [itemId]);

      await connection.commit();

      req.flash('success', 'Item issued successfully');
      res.redirect('/it-admin/inventory');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error issuing item:', error);
    req.flash('error', 'Error issuing item');
    res.redirect(`/it-admin/inventory/${req.params.id}/issue`);
  }
});

// Receive item form
router.get('/inventory/:id/receive', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Get item details
    const [items] = await db.query(`
      SELECT i.*, c.name as category_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE i.item_id = ? AND i.status = 'assigned'
    `, [itemId]);

    if (items.length === 0) {
      req.flash('error', 'Item not found or not currently assigned');
      return res.redirect('/it-admin/inventory');
    }

    // Get transaction details
    const [transactions] = await db.query(`
      SELECT t.*, u.username as issued_to_name
      FROM inventory_transactions t
      LEFT JOIN users u ON t.issued_to = u.id
      WHERE t.item_id = ? AND t.transaction_type = 'issue' AND t.received_date IS NULL
      ORDER BY t.issued_date DESC
      LIMIT 1
    `, [itemId]);

    res.render('it-admin/inventory/receive', {
      title: 'Receive Item',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      item: items[0],
      transaction: transactions.length > 0 ? transactions[0] : null,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading receive item form:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading form',
      error: { status: 500, stack: error.stack },
      layout: 'layouts/it-admin'
    });
  }
});

// Delete inventory item
router.delete('/inventory/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Check if item exists
    const [items] = await db.query('SELECT * FROM inventory_items WHERE item_id = ?', [itemId]);
    if (items.length === 0) {
      return res.status(404).json({ success: false, message: 'Item not found' });
    }

    // Check if item has any active transactions
    const [transactions] = await db.query(
      'SELECT * FROM inventory_transactions WHERE item_id = ? AND received_date IS NULL',
      [itemId]
    );
    if (transactions.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete item with active transactions. Please receive the item first.'
      });
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Delete related hardware conditions
      await connection.query('DELETE FROM hardware_condition WHERE item_id = ?', [itemId]);

      // Delete related transactions
      await connection.query('DELETE FROM inventory_transactions WHERE item_id = ?', [itemId]);

      // Delete the item
      await connection.query('DELETE FROM inventory_items WHERE item_id = ?', [itemId]);

      await connection.commit();

      return res.json({ success: true, message: 'Item deleted successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    return res.status(500).json({ success: false, message: 'Error deleting item' });
  }
});

// Process edit inventory item with base64 encoded images
router.post('/inventory/:id/edit-base64', async (req, res) => {
  try {
    const db = require('../config/database');
    const fs = require('fs');
    const path = require('path');
    const { v4: uuidv4 } = require('uuid');
    const itemId = req.params.id;

    console.log("Edit inventory item request received for ID (base64 method):", itemId);

    const {
      name, description, category_id, serial_number, model,
      manufacturer, purchase_date, purchase_cost, warranty_expiry,
      status, location, notes, keep_current_image,
      // Network information fields
      hostname, ip_address, mac_address,
      // Images
      images
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ success: false, message: 'Item name is required' });
    }

    // Prepare network info JSON if any network fields are provided
    let networkInfo = null;
    if (hostname || ip_address || mac_address) {
      networkInfo = JSON.stringify({
        hostname: hostname || null,
        ip_address: ip_address || null,
        mac_address: mac_address || null
      });
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Get current item data
      const [items] = await connection.query('SELECT * FROM inventory_items WHERE item_id = ?', [itemId]);
      if (items.length === 0) {
        await connection.rollback();
        return res.status(404).json({ success: false, message: 'Item not found' });
      }

      const currentItem = items[0];

      // Process base64 encoded images
      let primaryImage = null;
      const imageUrls = [];

      if (images && images.length > 0) {
        console.log(`Processing ${images.length} base64 encoded images...`);
        const uploadDir = path.join(__dirname, '../public/uploads/inventory');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Process each image
        for (const image of images) {
          try {
            // Skip if no data
            if (!image.data) {
              console.log("Skipping file with no data");
              continue;
            }

            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];
            if (!validTypes.includes(image.type)) {
              console.log(`Invalid file type: ${image.type}. Only JPG and PDF are allowed.`);
              continue;
            }

            // Extract base64 data
            const base64Data = image.data.split(';base64,').pop();
            if (!base64Data) {
              console.log("Invalid base64 data format");
              continue;
            }

            // Generate unique filename with proper extension
            let fileExt;
            if (image.type === 'application/pdf') {
              fileExt = 'pdf';
            } else {
              fileExt = 'jpg';
            }

            const uniqueFilename = `${uuidv4()}.${fileExt}`;
            const filePath = path.join(uploadDir, uniqueFilename);
            const fileUrl = `/uploads/inventory/${uniqueFilename}`;

            // Write file to disk
            fs.writeFileSync(filePath, base64Data, { encoding: 'base64' });
            console.log(`File saved to: ${filePath} (${image.type})`);

            // Add to image URLs array
            imageUrls.push(fileUrl);

            // Set the first image as the primary image
            if (!primaryImage) {
              primaryImage = fileUrl;
            }
          } catch (fileError) {
            console.error("Error processing file:", fileError);
            // Continue with other files even if one fails
          }
        }
      } else {
        console.log("No base64 files to process");
      }

      // If no new images were uploaded and keep_current_image is true, keep the current image
      if (!primaryImage && keep_current_image) {
        primaryImage = currentItem.image;
        console.log(`Keeping current image: ${primaryImage}`);
      }

      // Update inventory item with primary image
      await connection.query(`
        UPDATE inventory_items
        SET name = ?, description = ?, category_id = ?, serial_number = ?, model = ?,
            manufacturer = ?, purchase_date = ?, purchase_cost = ?, warranty_expiry = ?,
            status = ?, location = ?, notes = ?, image = ?, network_info = ?,
            updated_at = NOW()
        WHERE item_id = ?
      `, [
        name,
        description || null,
        category_id || null,
        serial_number || null,
        model || null,
        manufacturer || null,
        purchase_date || null,
        purchase_cost || null,
        warranty_expiry || null,
        status || 'available',
        location || null,
        notes || null,
        primaryImage,
        networkInfo,
        itemId
      ]);

      // If new images were uploaded, store additional images in the item_images table
      if (imageUrls.length > 1) {
        // Delete existing additional images
        await connection.query('DELETE FROM item_images WHERE item_id = ?', [itemId]);

        // Skip the first image as it's already stored as the primary image
        for (let i = 1; i < imageUrls.length; i++) {
          await connection.query(`
            INSERT INTO item_images (
              item_id, image_url, uploaded_by, uploaded_at
            ) VALUES (?, ?, ?, NOW())
          `, [
            itemId,
            imageUrls[i],
            req.session.userId
          ]);
        }
      }

      await connection.commit();
      console.log("Transaction committed successfully");

      return res.status(200).json({ success: true, message: 'Inventory item updated successfully' });
    } catch (error) {
      console.error("Error in transaction:", error);
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating inventory item (base64 method):', error);
    return res.status(500).json({ success: false, message: 'Error updating inventory item: ' + error.message });
  }
});

// Delete multiple inventory items
router.post('/inventory/delete-multiple', async (req, res) => {
  try {
    const db = require('../config/database');
    const { itemIds } = req.body;

    if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
      req.flash('error', 'No items selected for deletion');
      return res.redirect('/it-admin/inventory');
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Check for active transactions
      const [activeTransactions] = await connection.query(
        'SELECT item_id FROM inventory_transactions WHERE item_id IN (?) AND received_date IS NULL',
        [itemIds]
      );

      if (activeTransactions.length > 0) {
        const activeItemIds = activeTransactions.map(t => t.item_id);
        await connection.rollback();
        req.flash('error', `Cannot delete items with IDs ${activeItemIds.join(', ')} because they have active transactions`);
        return res.redirect('/it-admin/inventory');
      }

      // Delete related hardware conditions
      await connection.query('DELETE FROM hardware_condition WHERE item_id IN (?)', [itemIds]);

      // Delete related transactions
      await connection.query('DELETE FROM inventory_transactions WHERE item_id IN (?)', [itemIds]);

      // Delete the items
      const [result] = await connection.query('DELETE FROM inventory_items WHERE item_id IN (?)', [itemIds]);

      await connection.commit();

      req.flash('success', `Successfully deleted ${result.affectedRows} item(s)`);
      return res.redirect('/it-admin/inventory');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting multiple inventory items:', error);
    req.flash('error', 'Error deleting items');
    return res.redirect('/it-admin/inventory');
  }
});

// Process receive item
router.post('/inventory/:id/receive', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;
    const { transaction_id, condition_on_return, notes, create_issue_ticket } = req.body;

    // Validate required fields
    if (!transaction_id) {
      req.flash('error', 'Transaction is required');
      return res.redirect(`/it-admin/inventory/${itemId}/receive`);
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // Update transaction record
      await connection.query(`
        UPDATE inventory_transactions
        SET received_date = NOW(), received_by = ?, condition_on_return = ?, return_notes = ?
        WHERE transaction_id = ?
      `, [
        req.session.userId, condition_on_return || null, notes || null, transaction_id
      ]);

      // Update item status
      await connection.query(`
        UPDATE inventory_items
        SET status = 'available'
        WHERE item_id = ?
      `, [itemId]);

      // Create issue ticket if requested
      if (create_issue_ticket === 'yes' && condition_on_return) {
        // Get item details
        const [items] = await connection.query(`
          SELECT name, serial_number FROM inventory_items WHERE item_id = ?
        `, [itemId]);

        if (items.length > 0) {
          const item = items[0];
          const title = `Hardware issue with ${item.name}`;
          const description = `Issue reported during item return.\n\nItem: ${item.name} (${item.serial_number || 'No S/N'})\n\nCondition: ${condition_on_return}\n\nNotes: ${notes || 'None'}`;

          // Create issue
          await connection.query(`
            INSERT INTO it_issues (
              title, description, item_id, reported_by, issue_type, priority, status
            ) VALUES (?, ?, ?, ?, 'hardware', 'medium', 'open')
          `, [
            title, description, itemId, req.session.userId
          ]);
        }
      }

      await connection.commit();

      req.flash('success', 'Item received successfully');
      res.redirect('/it-admin/inventory');
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error receiving item:', error);
    req.flash('error', 'Error receiving item');
    res.redirect(`/it-admin/inventory/${itemId}/receive`);
  }
});

// Generate loan voucher
router.get('/inventory/transactions/:id/loan-voucher', prepareVoucherResponse, async (req, res) => {
  try {
    const db = require('../config/database');
    const transactionId = req.params.id;
    const openInNewTab = req.query.newTab === 'true';

    // Get transaction details with related data
    const [transactions] = await db.query(`
      SELECT t.*,
             i.name as item_name, i.serial_number, i.model, i.manufacturer,
             u1.username as issued_to_name, u1.name as issued_to_full_name, u1.email as issued_to_email,
             u2.username as issued_by_name
      FROM inventory_transactions t
      JOIN inventory_items i ON t.item_id = i.item_id
      JOIN users u1 ON t.issued_to = u1.id
      JOIN users u2 ON t.issued_by = u2.id
      WHERE t.transaction_id = ?
    `, [transactionId]);

    if (transactions.length === 0) {
      req.flash('error', 'Transaction not found');
      return res.redirect('/it-admin/transactions');
    }

    const transaction = transactions[0];

    // Format dates for display
    const formatDate = (date) => {
      if (!date) return null;
      const d = new Date(date);
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Prepare voucher options
    const voucherOptions = {
      transactionId: transaction.transaction_id,
      voucherType: 'loan',
      title: 'EQUIPMENT LOAN VOUCHER',
      itemName: transaction.item_name || 'N/A',
      serialNumber: transaction.serial_number || 'N/A',
      issuedTo: transaction.issued_to_full_name || transaction.issued_to_name || 'N/A',
      issuedBy: transaction.issued_by_name || 'N/A',
      issuedDate: formatDate(transaction.issued_date),
      expectedReturnDate: formatDate(transaction.expected_return_date)
    };

    // Generate the voucher using our middleware
    return await generateVoucher(voucherOptions, res, openInNewTab);

  } catch (error) {
    console.error('Error generating loan voucher:', error);
    req.flash('error', 'Error generating loan voucher');
    res.redirect('/it-admin/transactions');
  }
});

// Generate return voucher
router.get('/inventory/transactions/:id/return-voucher', prepareVoucherResponse, async (req, res) => {

  try {
    const db = require('../config/database');
    const transactionId = req.params.id;
    const openInNewTab = req.query.newTab === 'true';

    // Get transaction details with related data
    console.log('Fetching transaction details for ID:', transactionId);
    const [transactions] = await db.query(`
      SELECT t.*,
             i.name as item_name, i.serial_number, i.model, i.manufacturer,
             u1.username as issued_to_name, u1.name as issued_to_full_name, u1.email as issued_to_email,
             u2.username as issued_by_name,
             u3.username as received_by_name
      FROM inventory_transactions t
      JOIN inventory_items i ON t.item_id = i.item_id
      JOIN users u1 ON t.issued_to = u1.id
      JOIN users u2 ON t.issued_by = u2.id
      LEFT JOIN users u3 ON t.received_by = u3.id
      WHERE t.transaction_id = ?
    `, [transactionId]);

    console.log('Transaction data:', JSON.stringify(transactions, null, 2));

    // For testing purposes, if no transaction is found, create a sample one
    if (transactions.length === 0) {
      console.log('No transaction found, creating a sample one for testing');
      transactions.push({
        transaction_id: transactionId,
        item_id: 1,
        issued_to: 1,
        issued_by: 1,
        issued_date: new Date(),
        expected_return_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        received_date: new Date(),
        received_by: 1,
        condition_on_issue: 'Good',
        condition_on_return: 'Good',
        notes: 'Sample transaction for testing',
        item_name: 'Epson EB-X51',
        serial_number: 'EEB-X51-2022-003',
        model: 'EB-X51',
        manufacturer: 'Epson',
        issued_to_name: 'teacher',
        issued_to_full_name: 'Teacher User',
        issued_to_email: '<EMAIL>',
        issued_by_name: 'it_admin',
        received_by_name: 'it_admin'
      });
    }

    if (transactions.length === 0) {
      req.flash('error', 'Transaction not found');
      return res.redirect('/it-admin/transactions');
    }

    // Check if the item has been returned
    if (!transactions[0].received_date) {
      req.flash('error', 'This item has not been returned yet');
      return res.redirect('/it-admin/transactions');
    }

    const transaction = transactions[0];

    // Format dates for display
    const formatDate = (date) => {
      if (!date) return null;
      const d = new Date(date);
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Prepare voucher options
    const voucherOptions = {
      transactionId: transaction.transaction_id,
      voucherType: 'return',
      title: 'EQUIPMENT RETURN VOUCHER',
      itemName: transaction.item_name || 'N/A',
      serialNumber: transaction.serial_number || 'N/A',
      issuedTo: transaction.issued_to_full_name || transaction.issued_to_name || 'N/A',
      issuedBy: transaction.issued_by_name || 'N/A',
      issuedDate: formatDate(transaction.issued_date),
      expectedReturnDate: formatDate(transaction.expected_return_date),
      returnDate: formatDate(transaction.received_date),
      returnedBy: transaction.received_by_name || 'N/A',
      condition: transaction.condition_on_return || 'Not specified'
    };

    // Generate the voucher using our middleware
    return await generateVoucher(voucherOptions, res, openInNewTab);

  } catch (error) {
    console.error('Error generating return voucher:', error);
    req.flash('error', 'Error generating return voucher');
    res.redirect('/it-admin/transactions');
  }
});

// View item issue history
router.get('/inventory/items/:id/issue-history', async (req, res) => {
  try {
    const db = require('../config/database');
    const itemId = req.params.id;

    // Get item details
    const [items] = await db.query(`
      SELECT i.*, c.name as category_name
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE i.item_id = ?
    `, [itemId]);

    if (items.length === 0) {
      req.flash('error', 'Item not found');
      return res.redirect('/it-admin/inventory');
    }

    // Get transaction history
    const [transactions] = await db.query(`
      SELECT t.*,
             u1.username as issued_to_name, u1.name as issued_to_full_name,
             u2.username as issued_by_name,
             u3.username as received_by_name
      FROM inventory_transactions t
      LEFT JOIN users u1 ON t.issued_to = u1.id
      LEFT JOIN users u2 ON t.issued_by = u2.id
      LEFT JOIN users u3 ON t.received_by = u3.id
      WHERE t.item_id = ?
      ORDER BY t.issued_date DESC
    `, [itemId]);

    // Format dates
    transactions.forEach(transaction => {
      transaction.issued_date = transaction.issued_date ? new Date(transaction.issued_date).toLocaleDateString() : 'N/A';
      transaction.received_date = transaction.received_date ? new Date(transaction.received_date).toLocaleDateString() : null;
      transaction.expected_return_date = transaction.expected_return_date ? new Date(transaction.expected_return_date).toLocaleDateString() : 'Not specified';
    });

    res.render('it-admin/inventory/issue-history', {
      title: 'Item Issue History',
      layout: 'layouts/it-admin',
      currentPage: 'inventory',
      item: items[0],
      transactions,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });

  } catch (error) {
    console.error('Error loading item issue history:', error);
    req.flash('error', 'Error loading item issue history');
    res.redirect('/it-admin/inventory');
  }
});

// File upload validation function
const validateImportFile = (file) => {
  // Accept only CSV and Excel files
  const filetypes = /csv|xlsx|xls/;
  const mimetype = filetypes.test(file.mimetype);
  const extname = filetypes.test(path.extname(file.name).toLowerCase());

  if (!mimetype || !extname) {
    throw new Error('Only CSV and Excel files are allowed');
  }

  // Check file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    throw new Error('File size exceeds the 5MB limit');
  }

  return true;
};

// Helper function to parse file
const parseImportFile = async (file) => {
  const XLSX = require('xlsx');
  const fs = require('fs');
  const csv = require('csv-parser');
  let items = [];

  // Determine file type
  const fileType = path.extname(file.name).toLowerCase() === '.csv' ? 'csv' : 'excel';

  console.log(`Parsing file: ${file.name} (${fileType})`);

  // Parse file based on type
  if (fileType === 'csv') {
    // Use csv-parser for more reliable CSV parsing
    return new Promise((resolve, reject) => {
      const results = [];

      // Create a readable stream from the temp file
      const readStream = fs.createReadStream(file.tempFilePath);

      // Process the CSV file
      readStream
        .pipe(csv({
          mapHeaders: ({ header }) => {
            // Normalize header names (lowercase, trim, replace spaces with underscores)
            return header.toLowerCase().trim().replace(/\s+/g, '_');
          }
        }))
        .on('data', (data) => {
          // Normalize field names and values
          const normalizedData = {};

          Object.keys(data).forEach(key => {
            // Convert empty strings to null
            normalizedData[key] = data[key] && data[key].trim() !== '' ? data[key].trim() : null;
          });

          results.push(normalizedData);
        })
        .on('end', () => {
          console.log(`CSV parsing complete. Found ${results.length} items.`);
          console.log('Sample item:', results.length > 0 ? JSON.stringify(results[0]) : 'No items found');

          // Clean up the temp file
          try {
            fs.unlinkSync(file.tempFilePath);
          } catch (error) {
            console.warn('Error removing temp file:', error);
          }

          resolve(results);
        })
        .on('error', (error) => {
          console.error('Error parsing CSV:', error);
          reject(error);
        });
    });
  } else if (fileType === 'excel') {
    try {
      // Parse Excel data
      const workbook = XLSX.readFile(file.tempFilePath);

      // Get the Items sheet (second sheet)
      const sheetName = workbook.SheetNames.find(name => name === 'Items') || workbook.SheetNames[0];
      console.log(`Using sheet: ${sheetName}`);

      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON with header mapping
      const rawItems = XLSX.utils.sheet_to_json(worksheet);

      // Normalize field names
      items = rawItems.map(item => {
        const normalizedItem = {};

        Object.keys(item).forEach(key => {
          // Convert keys to lowercase and replace spaces with underscores
          const normalizedKey = key.toLowerCase().trim().replace(/\s+/g, '_');
          // Convert empty strings to null
          normalizedItem[normalizedKey] = item[key] && String(item[key]).trim() !== '' ? String(item[key]).trim() : null;
        });

        return normalizedItem;
      });

      console.log(`Excel parsing complete. Found ${items.length} items.`);
      console.log('Sample item:', items.length > 0 ? JSON.stringify(items[0]) : 'No items found');
    } catch (error) {
      console.error('Error parsing Excel file:', error);
      throw error;
    } finally {
      // Clean up the temp file
      try {
        fs.unlinkSync(file.tempFilePath);
      } catch (error) {
        console.warn('Error removing temp file:', error);
      }
    }
  }

  return items;
};

// Preview inventory import
router.post('/inventory/preview-import', async (req, res) => {
  try {
    // Check if file was uploaded
    if (!req.files || !req.files.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;

    // Validate the file
    try {
      validateImportFile(file);
    } catch (error) {
      return res.status(400).json({ error: error.message });
    }

    // Parse file
    const items = await parseImportFile(file);

    // Validate items
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: 'No valid items found in file' });
    }

    // Return preview data
    res.json({
      items: items,
      totalItems: items.length
    });
  } catch (error) {
    console.error('Error previewing inventory import:', error);
    res.status(500).json({ error: `Error previewing import: ${error.message}` });
  }
});

// Helper function to normalize status values
const normalizeStatus = (status) => {
  if (!status) return 'available'; // Default value

  // Convert to lowercase and trim
  const normalizedStatus = status.toLowerCase().trim();

  // Map various status values to allowed ENUM values
  const statusMap = {
    // Available variations
    'available': 'available',
    'avail': 'available',
    'in stock': 'available',
    'in-stock': 'available',
    'instock': 'available',
    'free': 'available',
    'unused': 'available',
    'ready': 'available',

    // Assigned variations
    'assigned': 'assigned',
    'in use': 'assigned',
    'in-use': 'assigned',
    'inuse': 'assigned',
    'allocated': 'assigned',
    'issued': 'assigned',

    // Maintenance variations
    'maintenance': 'maintenance',
    'in repair': 'maintenance',
    'in-repair': 'maintenance',
    'inrepair': 'maintenance',
    'repair': 'maintenance',
    'fixing': 'maintenance',
    'broken': 'maintenance',
    'damaged': 'maintenance',

    // Retired variations
    'retired': 'retired',
    'disposed': 'retired',
    'discarded': 'retired',
    'obsolete': 'retired',
    'end of life': 'retired',
    'end-of-life': 'retired',
    'eol': 'retired'
  };

  return statusMap[normalizedStatus] || 'available';
};

// Process inventory import
router.post('/inventory/import', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get items from request body
    const data = req.body;

    if (!data || !data.items || !Array.isArray(data.items)) {
      return res.status(400).json({ error: 'Invalid import data' });
    }

    const items = data.items;
    console.log(`Processing ${items.length} items for import`);

    // Log the first item for debugging
    if (items.length > 0) {
      console.log('First item:', JSON.stringify(items[0]));
    }

    // Start transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    // Import results
    const results = {
      imported: 0,
      skipped: 0,
      duplicates: 0,
      errors: [],
      duplicateItems: []
    };

    try {
      // Process each item
      for (const item of items) {
        try {
          // Get item name (case insensitive)
          const itemName = item.name || item.Name || item.NAME || item['Item Name'] || item['ITEM NAME'] || '';

          // Validate required fields
          if (!itemName) {
            results.errors.push(`Item without name skipped`);
            results.skipped++;
            continue;
          }

          // Get category (case insensitive)
          const category = item.category || item.Category || item.CATEGORY || '';

          // Validate additional required fields
          if (!category) {
            results.errors.push(`Item "${itemName}" without category skipped`);
            results.skipped++;
            continue;
          }

          // Get serial number (case insensitive)
          const serialNumber = item.serial_number || item['serial number'] || item.serialnumber ||
                              item.SerialNumber || item['Serial Number'] || item['SERIAL NUMBER'] || '';

          if (!serialNumber) {
            results.errors.push(`Item "${itemName}" without serial number skipped`);
            results.skipped++;
            continue;
          }

          // Check if serial number already exists in the database
          const [existingItems] = await connection.query(
            'SELECT item_id, name, model, manufacturer FROM inventory_items WHERE serial_number = ?',
            [serialNumber]
          );

          if (existingItems.length > 0) {
            // Item with this serial number already exists, skip it
            const existingItem = existingItems[0];
            const duplicateInfo = {
              name: itemName,
              serial_number: serialNumber,
              existing_item: {
                id: existingItem.item_id,
                name: existingItem.name,
                model: existingItem.model,
                manufacturer: existingItem.manufacturer
              }
            };

            results.duplicateItems.push(duplicateInfo);
            results.duplicates++;
            console.log(`Duplicate item skipped: "${itemName}" with serial number "${serialNumber}" already exists`);
            continue;
          }

          // Get model (case insensitive)
          const model = item.model || item.Model || item.MODEL || '';

          if (!model) {
            results.errors.push(`Item "${itemName}" without model skipped`);
            results.skipped++;
            continue;
          }

          // Get manufacturer (case insensitive)
          const manufacturer = item.manufacturer || item.Manufacturer || item.MANUFACTURER || '';

          if (!manufacturer) {
            results.errors.push(`Item "${itemName}" without manufacturer skipped`);
            results.skipped++;
            continue;
          }

          // Get category ID
          let categoryId = null;
          if (category) {
            const [categories] = await connection.query(
              'SELECT category_id FROM inventory_categories WHERE name = ?',
              [category]
            );

            if (categories.length > 0) {
              categoryId = categories[0].category_id;
            } else {
              // Create new category
              const [result] = await connection.query(
                'INSERT INTO inventory_categories (name) VALUES (?)',
                [category]
              );
              categoryId = result.insertId;
            }
          }

          // Get other fields (case insensitive)
          const description = item.description || item.Description || item.DESCRIPTION || null;
          const purchaseDate = item.purchase_date || item['purchase date'] || item.purchasedate ||
                              item.PurchaseDate || item['Purchase Date'] || item['PURCHASE DATE'] || null;
          const purchaseCost = item.purchase_cost || item['purchase cost'] || item.purchasecost ||
                              item.PurchaseCost || item['Purchase Cost'] || item['PURCHASE COST'] || null;
          const warrantyExpiry = item.warranty_expiry || item['warranty expiry'] || item.warrantyexpiry ||
                                item.WarrantyExpiry || item['Warranty Expiry'] || item['WARRANTY EXPIRY'] || null;

          // Get status and normalize it to ensure it matches the allowed ENUM values
          const rawStatus = item.status || item.Status || item.STATUS || 'available';
          const status = normalizeStatus(rawStatus);
          const location = item.location || item.Location || item.LOCATION || null;
          const notes = item.notes || item.Notes || item.NOTES || null;

          // Get network info fields (case insensitive)
          const hostname = item.hostname || item.Hostname || item.HOSTNAME || null;
          const ipAddress = item.ip_address || item['ip address'] || item.ipaddress ||
                           item.IPAddress || item['IP Address'] || item['IP ADDRESS'] || null;
          const macAddress = item.mac_address || item['mac address'] || item.macaddress ||
                            item.MACAddress || item['MAC Address'] || item['MAC ADDRESS'] || null;

          // Prepare network info
          const networkInfo = JSON.stringify({
            hostname: hostname,
            ip_address: ipAddress,
            mac_address: macAddress
          });

          // Insert item
          const [result] = await connection.query(`
            INSERT INTO inventory_items (
              name, description, category_id, serial_number, model,
              manufacturer, purchase_date, purchase_cost, warranty_expiry,
              status, location, notes, network_info, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            itemName,
            description,
            categoryId,
            serialNumber,
            model,
            manufacturer,
            purchaseDate,
            purchaseCost,
            warrantyExpiry,
            status,
            location,
            notes,
            networkInfo,
            req.session.userId
          ]);

          results.imported++;
          console.log(`Imported item: ${itemName}`);
        } catch (error) {
          const itemName = item.name || item.Name || item.NAME || item['Item Name'] || item['ITEM NAME'] || 'Unknown';
          results.errors.push(`Error importing item "${itemName}": ${error.message}`);
          results.skipped++;
          console.error(`Error importing item "${itemName}":`, error);
        }
      }

      // Commit transaction
      await connection.commit();
      console.log(`Import complete: ${results.imported} imported, ${results.duplicates} duplicates, ${results.skipped} skipped`);

      // Return results
      res.json({
        success: true,
        imported: results.imported,
        duplicates: results.duplicates,
        skipped: results.skipped,
        errors: results.errors,
        duplicateItems: results.duplicateItems
      });
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      console.error('Transaction error:', error);
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error importing inventory items:', error);
    res.status(500).json({ error: `Error importing inventory items: ${error.message}` });
  }
});

// Download inventory import template
router.get('/inventory/import/template', async (req, res) => {
  try {
    const XLSX = require('xlsx');

    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Create the instructions sheet
    const instructionsData = [
      {
        field: "Instructions",
        value: "This template is for importing inventory items into the system. Fill out the Items sheet with your inventory information."
      },
      {
        field: "name",
        value: "Required. The name of the item."
      },
      {
        field: "category",
        value: "Required. The category of the item. If it doesn't exist, it will be created."
      },
      {
        field: "serial_number",
        value: "Required. The serial number of the item."
      },
      {
        field: "model",
        value: "Required. The model of the item."
      },
      {
        field: "manufacturer",
        value: "Required. The manufacturer of the item."
      },
      {
        field: "description",
        value: "Optional. A description of the item."
      },
      {
        field: "status",
        value: "Optional. The status of the item. Valid values are: 'available', 'assigned', 'maintenance', 'retired'. Default is 'available'."
      },
      {
        field: "model",
        value: "Optional. The model of the item."
      },
      {
        field: "manufacturer",
        value: "Optional. The manufacturer of the item."
      },
      {
        field: "purchase_date",
        value: "Optional. The purchase date of the item (YYYY-MM-DD)."
      },
      {
        field: "purchase_cost",
        value: "Optional. The purchase cost of the item."
      },
      {
        field: "warranty_expiry",
        value: "Optional. The warranty expiry date of the item (YYYY-MM-DD)."
      },
      {
        field: "status",
        value: "Optional. The status of the item (available, assigned, maintenance, retired). Defaults to 'available'."
      },
      {
        field: "location",
        value: "Optional. The location of the item."
      },
      {
        field: "notes",
        value: "Optional. Additional notes about the item."
      },
      {
        field: "hostname",
        value: "Optional. The hostname of the device (for network devices)."
      },
      {
        field: "ip_address",
        value: "Optional. The IP address of the device (for network devices)."
      },
      {
        field: "mac_address",
        value: "Optional. The MAC address of the device (for network devices)."
      }
    ];

    const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
    XLSX.utils.book_append_sheet(wb, instructionsSheet, "Instructions");

    // Create the Items sheet
    const itemsData = [
      {
        name: "Dell Latitude 5420",
        category: "Laptop",
        description: "Business laptop for staff use",
        serial_number: "SN12345678",
        model: "Latitude 5420",
        manufacturer: "Dell",
        purchase_date: "2023-01-15",
        purchase_cost: "65000",
        warranty_expiry: "2026-01-15",
        status: "available",
        location: "IT Department",
        notes: "Assigned to new staff members",
        hostname: "LAPTOP-DEL01",
        ip_address: "*************",
        mac_address: "00:1A:2B:3C:4D:5E"
      },
      {
        name: "HP ProDesk 400 G7",
        category: "Desktop",
        description: "Desktop computer for lab use",
        serial_number: "HP87654321",
        model: "ProDesk 400 G7",
        manufacturer: "HP",
        purchase_date: "2022-11-20",
        purchase_cost: "45000",
        warranty_expiry: "2025-11-20",
        status: "available",
        location: "Computer Lab 2",
        notes: "For student use",
        hostname: "DESKTOP-HP01",
        ip_address: "*************",
        mac_address: "00:2C:3D:4E:5F:6G"
      },
      {
        name: "Epson EB-X51",
        category: "Projector",
        description: "Classroom projector",
        serial_number: "EP55667788",
        model: "EB-X51",
        manufacturer: "Epson",
        purchase_date: "2023-02-10",
        purchase_cost: "35000",
        warranty_expiry: "2025-02-10",
        status: "available",
        location: "Classroom 101",
        notes: "Ceiling mounted"
      }
    ];

    const itemsSheet = XLSX.utils.json_to_sheet(itemsData);
    XLSX.utils.book_append_sheet(wb, itemsSheet, "Items");

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=inventory_import_template.xlsx');

    // Write to response
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'buffer' });
    res.send(excelBuffer);
  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).send('Error generating template');
  }
});

// Export inventory data - Consolidated route that handles both HTML view and file export
// Handle both GET and POST requests for inventory export
router.all('/inventory/export', async (req, res) => {
  try {
    const db = require('../config/database');
    // Get format from either query params (GET) or request body (POST)
    const format = req.method === 'GET' ? req.query.format : req.body.format;
    // Get selected item IDs if provided in POST request
    const selectedItemIds = req.method === 'POST' && req.body.itemIds ? req.body.itemIds : [];

    console.log(`Export inventory data requested with format: ${format || 'HTML'}`);
    if (selectedItemIds.length > 0) {
      console.log(`Exporting ${selectedItemIds.length} selected items`);
    } else {
      console.log('Exporting all items');
    }

    // Build the SQL query based on whether specific items are selected
    let query = `
      SELECT i.*, c.name as category_name,
             CASE
               WHEN i.status = 'assigned' THEN
                 (SELECT username FROM users WHERE id =
                   (SELECT issued_to FROM inventory_transactions
                    WHERE item_id = i.item_id AND received_date IS NULL
                    ORDER BY issued_date DESC LIMIT 1))
               ELSE NULL
             END as assigned_to
      FROM inventory_items i
      LEFT JOIN inventory_categories c ON i.category_id = c.category_id
    `;

    // Add WHERE clause if specific items are selected
    if (selectedItemIds.length > 0) {
      query += ` WHERE i.item_id IN (${selectedItemIds.map(() => '?').join(',')})`;
    }

    query += ` ORDER BY i.name`;

    // Execute the query with or without parameters
    const [items] = selectedItemIds.length > 0
      ? await db.query(query, selectedItemIds)
      : await db.query(query);

    console.log(`Retrieved ${items.length} inventory items for export`);

    // If no format is specified, render the HTML view
    if (!format) {
      console.log('Rendering HTML export view');
      // Format dates for HTML view
      items.forEach(item => {
        item.purchase_date = item.purchase_date ? new Date(item.purchase_date).toLocaleDateString() : '';
        item.warranty_expiry = item.warranty_expiry ? new Date(item.warranty_expiry).toLocaleDateString() : '';
        item.created_at = item.created_at ? new Date(item.created_at).toLocaleDateString() : '';
        item.updated_at = item.updated_at ? new Date(item.updated_at).toLocaleDateString() : '';
      });

      return res.render('it-admin/inventory/export', {
        title: 'Export Inventory Data',
        layout: 'layouts/it-admin',
        currentPage: 'inventory',
        items,
        hasItems: items.length > 0
      });
    }

    // Format dates and prepare data for export
    const formattedItems = items.map(item => {
      return {
        ...item,
        purchase_date: item.purchase_date ? new Date(item.purchase_date).toLocaleDateString() : 'N/A',
        warranty_expiry: item.warranty_expiry ? new Date(item.warranty_expiry).toLocaleDateString() : 'N/A',
        created_at: item.created_at ? new Date(item.created_at).toLocaleDateString() : 'N/A',
        updated_at: item.updated_at ? new Date(item.updated_at).toLocaleDateString() : 'N/A',
        category_name: item.category_name || 'Uncategorized',
        assigned_to: item.assigned_to || 'N/A'
      };
    });

    // For file export formats
    const fs = require('fs');
    const path = require('path');



    if (format === 'pdf') {
      try {
        console.log('Generating PDF export');
        // Import PDFKit
        const PDFDocument = require('pdfkit');
        const fs = require('fs');
        const path = require('path');

        // Get orientation from query params or request body
        const orientation = req.method === 'GET' ? req.query.orientation : req.body.orientation;
        const isLandscape = orientation === 'landscape';

        // Generate a unique filename with timestamp
        const timestamp = Date.now();
        const filename = `inventory_${timestamp}.pdf`;

        // Create a temporary file path for the PDF
        const tempFilePath = path.join(__dirname, '..', 'public', 'temp', filename);

        // Ensure temp directory exists
        const tempDir = path.join(__dirname, '..', 'public', 'temp');
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }

        // Store the filename for later use
        const pdfFilename = filename;

        // Create a PDF document with appropriate orientation
        const doc = new PDFDocument({
          // Use consistent margins for better layout - wider margins in landscape mode
          margins: {
            top: 50,
            bottom: 60, // Increased bottom margin for page numbers
            left: 50,
            right: isLandscape ? 70 : 50 // Increased right margin in landscape mode
          },
          layout: isLandscape ? 'landscape' : 'portrait',
          size: 'A4',
          bufferPages: true, // Enable page buffering for better page management
          autoFirstPage: true, // Automatically create the first page
          info: {
            Title: 'IT Inventory Report',
            Author: 'Senior Secondary Residential School for Meritorious Students, Ludhiana',
            Subject: 'Inventory Report',
            Keywords: 'inventory, report, IT, equipment',
            Creator: 'IT Inventory System'
          }
        });

        // Create a write stream to the temporary file
        const writeStream = fs.createWriteStream(tempFilePath);
        doc.pipe(writeStream);

        // Set response headers for PDF to open in browser with PDF.js
        res.setHeader('Content-Type', 'text/html');

        // Add title
        doc.fontSize(20).text('IT Inventory Report', { align: 'center' });
        doc.moveDown();
        doc.fontSize(12).text(`Generated on: ${new Date().toLocaleString()}`, { align: 'center' });

        // Add export information
        if (selectedItemIds.length > 0) {
          doc.fontSize(10).text(`Showing ${items.length} selected items`, { align: 'center' });
        } else {
          doc.fontSize(10).text(`Showing all ${items.length} items`, { align: 'center' });
        }
        doc.moveDown(2);

      // Define table columns based on orientation
      const tableTop = 150;
      let columns;

      if (isLandscape) {
        // Calculate available width for landscape mode (A4 landscape width minus margins)
        // A4 landscape width is 842 points, minus left margin (50) and right margin (70)
        const availableWidth = 842 - 50 - 70;

        // Adjusted columns for landscape mode to fit within available width
        columns = [
          { header: 'Name', width: Math.floor(availableWidth * 0.25) }, // 25% of available width
          { header: 'Category', width: Math.floor(availableWidth * 0.15) }, // 15% of available width
          { header: 'Serial Number', width: Math.floor(availableWidth * 0.25) }, // 25% of available width
          { header: 'Status', width: Math.floor(availableWidth * 0.15) }, // 15% of available width
          { header: 'Assigned To', width: Math.floor(availableWidth * 0.20) }  // 20% of available width
        ];
      } else {
        // Original columns for portrait mode
        // A4 portrait width is 595 points, minus left margin (50) and right margin (50)
        const availableWidth = 595 - 50 - 50;

        columns = [
          { header: 'Name', width: Math.floor(availableWidth * 0.25) }, // 25% of available width
          { header: 'Category', width: Math.floor(availableWidth * 0.20) }, // 20% of available width
          { header: 'Serial Number', width: Math.floor(availableWidth * 0.25) }, // 25% of available width
          { header: 'Status', width: Math.floor(availableWidth * 0.15) }, // 15% of available width
          { header: 'Assigned To', width: Math.floor(availableWidth * 0.15) }  // 15% of available width
        ];
      }

      // Draw table header
      let currentY = tableTop;
      doc.fontSize(10).font('Helvetica-Bold');

      // Calculate total width of all columns
      const totalWidth = columns.reduce((sum, column) => sum + column.width, 0);

      // Draw header background
      doc.fillColor('#f3f4f6').rect(50, currentY, totalWidth, 20).fill();
      doc.fillColor('#000000');

      // Draw header border
      doc.lineWidth(1).rect(50, currentY, totalWidth, 20).stroke();

      // Draw header text
      let currentX = 50;
      columns.forEach(column => {
        // Add header text with proper options to prevent overflow
        doc.text(column.header, currentX + 5, currentY + 5, {
          width: column.width - 10,
          align: 'left',
          ellipsis: true
        });

        // Draw vertical line for column separation (except for the first column)
        if (currentX > 50) {
          doc.moveTo(currentX, currentY).lineTo(currentX, currentY + 20).stroke();
        }
        currentX += column.width;
      });

      currentY += 20;
      doc.font('Helvetica');

      // Draw table rows
      if (formattedItems.length === 0) {
        // If no items, add a message
        currentY += 20;
        doc.fontSize(14).font('Helvetica-Bold').text('No inventory items found.', 50, currentY, { align: 'center', width: totalWidth });
        currentY += 20;
        doc.fontSize(12).font('Helvetica').text('Please add inventory items before generating a report.', 50, currentY, { align: 'center', width: totalWidth });
      } else {
        // Track the current page number for page break detection
        let currentPage = 1;

        // Track the actual content height to avoid empty pages
        let contentHeight = 0;

        formattedItems.forEach((item, i) => {
          // Define row height (reduced since we don't have images)
          const rowHeight = 20; // Further reduced height for better fit

          // Calculate page height based on orientation, accounting for margins and headers/footers
          // Subtract more space for footer in landscape mode
          const pageHeight = isLandscape ? 520 : 700;
          const headerHeight = 20; // Height of the table header

          // Check if we need a new page before drawing this row
          // Only add a new page if we've already drawn some content on the current page
          if (currentY + rowHeight > pageHeight && contentHeight > 0) {
            // Add a new page with the same orientation and proper margins
            doc.addPage({
              layout: isLandscape ? 'landscape' : 'portrait',
              margins: {
                top: 50,
                bottom: 60,
                left: 50,
                right: isLandscape ? 70 : 50
              }
            });

            // Reset tracking variables
            currentPage++;
            currentY = 50;
            contentHeight = 0;

            // Draw header on new page
            doc.fontSize(10).font('Helvetica-Bold');
            doc.fillColor('#f3f4f6').rect(50, currentY, totalWidth, headerHeight).fill();
            doc.fillColor('#000000');

            // Draw header border
            doc.lineWidth(1).rect(50, currentY, totalWidth, headerHeight).stroke();

            currentX = 50;
            columns.forEach(column => {
              // Add header text with proper options to prevent overflow
              doc.text(column.header, currentX + 5, currentY + 5, {
                width: column.width - 10,
                align: 'left',
                ellipsis: true
              });

              // Draw vertical line for column separation (except for the first column)
              if (currentX > 50) {
                doc.moveTo(currentX, currentY).lineTo(currentX, currentY + headerHeight).stroke();
              }
              currentX += column.width;
            });

            currentY += headerHeight;
            doc.font('Helvetica');
          }

          // Track content height
          contentHeight += rowHeight;

          // Alternate row background
          if (i % 2 === 0) {
            doc.fillColor('#f9fafb').rect(50, currentY, totalWidth, rowHeight).fill();
            doc.fillColor('#000000');
          }

          // Draw row border
          doc.lineWidth(1).rect(50, currentY, totalWidth, rowHeight).stroke();

          currentX = 50;

          // Handle each column with borders
          columns.forEach((column, j) => {
            // Draw vertical line for column separation (except for the first column)
            if (currentX > 50) {
              doc.moveTo(currentX, currentY).lineTo(currentX, currentY + rowHeight).stroke();
            }

            // Add text based on column index
            let value = '';
            switch (j) {
              case 0: value = item.name || 'N/A'; break;
              case 1: value = item.category_name || 'N/A'; break;
              case 2: value = item.serial_number || 'N/A'; break;
              case 3: value = item.status || 'N/A'; break;
              case 4: value = item.assigned_to || 'N/A'; break;
            }

            // Truncate text if it's too long (prevent overflow)
            const maxChars = Math.floor(column.width / 5); // Approximate chars per width
            if (value.length > maxChars) {
              value = value.substring(0, maxChars - 3) + '...';
            }

            // Add text with proper options to prevent overflow
            doc.text(value, currentX + 5, currentY + 10, {
              width: column.width - 10,
              align: 'left',
              ellipsis: true
            });
            currentX += column.width;
          });

          // Move to next row (use rowHeight)
          currentY += rowHeight;

          // Add a small gap between rows for better readability
          // But not so much that it creates empty pages
          currentY += 2;
        });
      }

      // Add page numbers to all pages
      const totalPages = doc.bufferedPageRange().count;
      for (let i = 0; i < totalPages; i++) {
        doc.switchToPage(i);

        // Get page dimensions based on orientation
        const pageWidth = isLandscape ? 842 : 595; // A4 dimensions in points
        const pageHeight = isLandscape ? 595 : 842; // A4 dimensions in points

        // Calculate footer positions based on orientation and margins
        // Position footers closer to the bottom edge in landscape mode
        const footerY1 = pageHeight - (isLandscape ? 30 : 35); // Page number position
        const footerY2 = pageHeight - (isLandscape ? 15 : 20); // Timestamp position

        // Calculate available width accounting for margins
        const availableWidth = pageWidth - (isLandscape ? 120 : 100); // Account for margins
        const leftPosition = isLandscape ? 60 : 50; // Left margin

        // Add page number at the bottom
        doc.fontSize(8).fillColor('#666666');
        doc.text(
          `Page ${i + 1} of ${totalPages}`,
          leftPosition,
          footerY1,
          {
            align: 'center',
            width: availableWidth
          }
        );

        // Add footer with timestamp
        doc.fontSize(8).fillColor('#666666');
        doc.text(
          `Generated on: ${new Date().toLocaleString()}`,
          leftPosition,
          footerY2,
          {
            align: 'center',
            width: availableWidth
          }
        );
      }

      // Finalize the PDF
      doc.end();

      // Wait for the PDF to be fully written
      writeStream.on('finish', () => {
        // Get the relative path to the PDF file
        const relativePath = `/temp/${pdfFilename}`;

        // Create an HTML page with PDF.js viewer
        const pdfViewerHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>IT Inventory Report</title>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
          <style>
            body, html { margin: 0; padding: 0; height: 100%; background-color: #f5f5f5; }
            #pdfContainer { width: 100%; height: 100vh; }
            #loadingMessage {
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-family: Arial, sans-serif;
              font-size: 16px;
              color: #333;
              text-align: center;
              background-color: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 4px 8px rgba(0,0,0,0.1);
              max-width: 80%;
            }
            .spinner {
              display: inline-block;
              margin-bottom: 10px;
              font-size: 24px;
              animation: spin 1s linear infinite;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            #errorMessage {
              color: #e53e3e;
              margin-top: 10px;
              display: none;
            }
            #retryButton {
              margin-top: 15px;
              padding: 8px 16px;
              background-color: #4299e1;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              display: none;
            }
            #retryButton:hover {
              background-color: #3182ce;
            }
            #toolbar {
              position: fixed;
              top: 0;
              left: 0;
              right: 0;
              background-color: #2c5282;
              color: white;
              padding: 10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              z-index: 100;
            }
            #toolbar h1 {
              margin: 0;
              font-size: 18px;
            }
            #toolbar-buttons {
              display: flex;
              gap: 10px;
            }
            .toolbar-button {
              background-color: rgba(255,255,255,0.2);
              border: none;
              color: white;
              padding: 5px 10px;
              border-radius: 4px;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 5px;
            }
            .toolbar-button:hover {
              background-color: rgba(255,255,255,0.3);
            }
            #pdfViewer {
              margin-top: 50px;
              display: flex;
              justify-content: center;
              padding: 20px;
            }
            #pdfEmbed {
              width: 100%;
              height: 100vh;
              border: none;
            }
          </style>
        </head>
        <body>
          <div id="toolbar">
            <h1>IT Inventory Report</h1>
            <div id="toolbar-buttons">
              <button class="toolbar-button" id="downloadButton">
                <i class="fas fa-download"></i> Download
              </button>
              <button class="toolbar-button" id="printButton">
                <i class="fas fa-print"></i> Print
              </button>
            </div>
          </div>

          <div id="pdfViewer" style="margin-top: 50px;">
            <iframe id="pdfEmbed" src="${relativePath}" type="application/pdf"></iframe>
          </div>

          <div id="loadingMessage" style="display: none;">
            <div class="spinner"><i class="fas fa-spinner"></i></div>
            <div>Loading PDF...</div>
            <div id="errorMessage"></div>
            <button id="retryButton">Retry</button>
          </div>

          <script>
            // Setup download button
            document.getElementById('downloadButton').addEventListener('click', function() {
              const a = document.createElement('a');
              a.href = '${relativePath}';
              a.download = 'inventory_report.pdf';
              a.click();
            });

            // Setup print button
            document.getElementById('printButton').addEventListener('click', function() {
              window.open('${relativePath}', '_blank');
            });

            // Check if PDF loaded successfully
            document.getElementById('pdfEmbed').addEventListener('load', function() {
              console.log('PDF loaded successfully');
            });

            document.getElementById('pdfEmbed').addEventListener('error', function() {
              console.error('Error loading PDF');
              document.getElementById('pdfViewer').style.display = 'none';
              document.getElementById('loadingMessage').style.display = 'block';
              document.getElementById('errorMessage').style.display = 'block';
              document.getElementById('errorMessage').textContent = 'Error loading PDF. Please try again.';
              document.getElementById('retryButton').style.display = 'inline-block';
            });

            // Retry button
            document.getElementById('retryButton').addEventListener('click', function() {
              window.location.reload();
            });
          </script>
        </body>
        </html>
        `;

        // Send the HTML with PDF.js viewer
        res.send(pdfViewerHtml);
      });

      // Handle errors
      writeStream.on('error', (err) => {
        console.error('Error writing PDF file:', err);
        req.flash('error', 'Error generating PDF export');
        return res.redirect('/it-admin/inventory');
      });

      } catch (pdfError) {
        console.error('Error generating PDF:', pdfError);
        req.flash('error', 'Error generating PDF export');
        return res.redirect('/it-admin/inventory');
      }

    } else if (format === 'xlsx') {
      try {
        console.log('Generating Excel export');
        // Import ExcelJS
        const ExcelJS = require('exceljs');

        // Create a new Excel workbook
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Inventory');

      // Define columns
      worksheet.columns = [
        { header: 'ID', key: 'item_id', width: 10 },
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Category', key: 'category_name', width: 20 },
        { header: 'Serial Number', key: 'serial_number', width: 20 },
        { header: 'Model', key: 'model', width: 20 },
        { header: 'Manufacturer', key: 'manufacturer', width: 20 },
        { header: 'Purchase Date', key: 'purchase_date', width: 15 },
        { header: 'Warranty Expiry', key: 'warranty_expiry', width: 15 },
        { header: 'Status', key: 'status', width: 15 },
        { header: 'Location', key: 'location', width: 20 },
        { header: 'Assigned To', key: 'assigned_to', width: 20 },
        { header: 'Last Updated', key: 'updated_at', width: 15 }
      ];

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add export information row
      const infoRow = worksheet.addRow({});
      if (selectedItemIds.length > 0) {
        infoRow.getCell(1).value = `Showing ${formattedItems.length} selected items`;
      } else {
        infoRow.getCell(1).value = `Showing all ${formattedItems.length} items`;
      }
      infoRow.getCell(1).font = { italic: true, color: { argb: 'FF666666' } };
      worksheet.mergeCells(`A2:L2`);

      // Add a blank row for spacing
      worksheet.addRow({});

      // Add rows
      if (formattedItems.length === 0) {
        // If no items, add a message row
        const messageRow = worksheet.addRow({});
        messageRow.getCell(1).value = 'No inventory items found';
        messageRow.getCell(1).font = { bold: true, size: 14, color: { argb: 'FF333333' } };
        worksheet.mergeCells(`A4:L4`);

        // Add a second row with additional information
        const noItemsRow = worksheet.addRow({});
        noItemsRow.getCell(1).value = 'Please add inventory items before generating a report.';
        noItemsRow.getCell(1).font = { italic: true, color: { argb: 'FF666666' } };
        worksheet.mergeCells(`A5:L5`);
      } else {
        formattedItems.forEach(item => {
          worksheet.addRow({
            item_id: item.item_id,
            name: item.name,
            category_name: item.category_name,
            serial_number: item.serial_number || 'N/A',
            model: item.model || 'N/A',
            manufacturer: item.manufacturer || 'N/A',
            purchase_date: item.purchase_date,
            warranty_expiry: item.warranty_expiry,
            status: item.status,
            location: item.location || 'N/A',
            assigned_to: item.assigned_to,
            updated_at: item.updated_at
          });
        });
      }

      // Set response headers for Excel download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=inventory_${Date.now()}.xlsx`);

      // Write to response
      await workbook.xlsx.write(res);
      res.end();

      } catch (excelError) {
        console.error('Error generating Excel:', excelError);
        req.flash('error', 'Error generating Excel export');
        return res.redirect('/it-admin/inventory');
      }

    } else if (format === 'csv') {
      try {
        console.log('Generating CSV export');

        // Set response headers for CSV download
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=inventory_${Date.now()}.csv`);

        // Create CSV header
        let csvContent = 'ID,Name,Category,Serial Number,Model,Manufacturer,Purchase Date,Warranty Expiry,Status,Location,Assigned To,Notes\n';

        // Add export information
        if (selectedItemIds.length > 0) {
          csvContent += `"Export Info","Showing ${formattedItems.length} selected items","","","","","","","","","",""\n`;
        } else {
          csvContent += `"Export Info","Showing all ${formattedItems.length} items","","","","","","","","","",""\n`;
        }
        csvContent += `"Generated On","${new Date().toLocaleString()}","","","","","","","","","",""\n\n`;

        // Add data rows
        if (formattedItems.length === 0) {
          csvContent += '"No inventory items found","Please add inventory items before generating a report.","","","","","","","","","",""\n';
        } else {
          formattedItems.forEach(item => {
            // Escape fields that might contain commas
            const escapeCsv = (field) => {
              if (field === null || field === undefined) return '""';
              return `"${String(field).replace(/"/g, '""')}"`;
            };

            csvContent += [
              item.item_id,
              escapeCsv(item.name),
              escapeCsv(item.category_name),
              escapeCsv(item.serial_number),
              escapeCsv(item.model),
              escapeCsv(item.manufacturer),
              escapeCsv(item.purchase_date),
              escapeCsv(item.warranty_expiry),
              escapeCsv(item.status),
              escapeCsv(item.location),
              escapeCsv(item.assigned_to),
              escapeCsv(item.notes)
            ].join(',') + '\n';
          });
        }

        // Send CSV content
        res.send(csvContent);
      } catch (csvError) {
        console.error('Error generating CSV:', csvError);
        req.flash('error', 'Error generating CSV export');
        return res.redirect('/it-admin/inventory');
      }
    } else {
      // Invalid format
      req.flash('error', 'Invalid export format. Please use pdf, xlsx, or csv.');
      return res.redirect('/it-admin/inventory');
    }
  } catch (error) {
    console.error('Error preparing inventory export:', error);
    req.flash('error', 'Error preparing inventory export');
    res.redirect('/it-admin/inventory');
  }
});

// Vendors management
router.get('/vendors', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get all vendors with repair count
    const [vendors] = await db.query(`
      SELECT v.*,
             (SELECT COUNT(*) FROM repair_history WHERE vendor_id = v.vendor_id) as repair_count
      FROM repair_vendors v
      ORDER BY v.name
    `);

    res.render('it-admin/vendors/index', {
      title: 'Repair Vendors',
      layout: 'layouts/it-admin',
      currentPage: 'vendors',
      vendors,
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading repair vendors:', error);
    req.flash('error', 'Error loading repair vendors');
    res.redirect('/it-admin/dashboard');
  }
});

// Add vendor form
router.get('/vendors/add', async (req, res) => {
  try {
    res.render('it-admin/vendors/add', {
      title: 'Add Repair Vendor',
      layout: 'layouts/it-admin',
      currentPage: 'vendors',
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading add vendor form:', error);
    req.flash('error', 'Error loading form');
    res.redirect('/it-admin/vendors');
  }
});

// Process add vendor
router.post('/vendors/add', async (req, res) => {
  try {
    const db = require('../config/database');
    const { name, contact_person, phone, email, address, specialization, notes } = req.body;

    // Validate required fields
    if (!name) {
      req.flash('error', 'Vendor name is required');
      return res.redirect('/it-admin/vendors/add');
    }

    // Insert new vendor
    await db.query(`
      INSERT INTO repair_vendors (
        name, contact_person, phone, email, address, specialization, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      name, contact_person || null, phone || null, email || null,
      address || null, specialization || null, notes || null
    ]);

    req.flash('success', 'Repair vendor added successfully');
    res.redirect('/it-admin/vendors');
  } catch (error) {
    console.error('Error adding repair vendor:', error);
    req.flash('error', 'Error adding repair vendor');
    res.redirect('/it-admin/vendors/add');
  }
});

// View vendor details
router.get('/vendors/:id', async (req, res) => {
  try {
    const db = require('../config/database');
    const vendorId = req.params.id;

    // Get vendor details
    const [vendors] = await db.query(`
      SELECT * FROM repair_vendors WHERE vendor_id = ?
    `, [vendorId]);

    if (vendors.length === 0) {
      req.flash('error', 'Vendor not found');
      return res.redirect('/it-admin/vendors');
    }

    const vendor = vendors[0];

    // Get repair history
    const [repairs] = await db.query(`
      SELECT r.*, i.name as item_name, i.serial_number,
             u1.username as sent_by_name, u2.username as received_by_name
      FROM repair_history r
      JOIN inventory_items i ON r.item_id = i.item_id
      LEFT JOIN users u1 ON r.sent_by = u1.id
      LEFT JOIN users u2 ON r.received_by = u2.id
      WHERE r.vendor_id = ?
      ORDER BY r.sent_date DESC
    `, [vendorId]);

    res.render('it-admin/vendors/view', {
      title: 'Vendor Details',
      layout: 'layouts/it-admin',
      currentPage: 'vendors',
      vendor,
      repairs,
      formatDate: (date) => {
        return date ? new Date(date).toLocaleDateString() : 'N/A';
      },
      user: req.session.user || { username: req.session.username },
      notificationCount: 0
    });
  } catch (error) {
    console.error('Error loading vendor details:', error);
    req.flash('error', 'Error loading vendor details');
    res.redirect('/it-admin/vendors');
  }
});

// =============================================
// Procurement Routes
// =============================================

// Procurement dashboard
router.get('/procurement', procurementController.index);

// New procurement form
router.get('/procurement/new', procurementController.newProcurement);

// List all procurements
router.get('/procurement/all', procurementController.listAll);

// List draft procurements
router.get('/procurement/drafts', procurementController.listDrafts);

// Generate reports
router.get('/procurement/reports', procurementController.generateReports);

// Import multer configuration
const { createStepSpecificUpload } = require('../config/multer-config');

// Simple test endpoint
router.post('/procurement/test-simple', (req, res) => {
  try {
    console.log('Test simple endpoint called');
    console.log('Body:', req.body);
    console.log('Files:', req.files);

    // Always return success
    res.json({
      success: true,
      message: 'Test successful',
      received: {
        body: req.body,
        files: req.files ? req.files.length : 0
      }
    });
  } catch (error) {
    console.error('Error in test endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Error in test endpoint: ' + error.message
    });
  }
});

// Save procurement draft endpoint with error handling for multipart form issues
router.post('/procurement/save-draft', (req, res) => {
  console.log('Received save-draft request');

  // Check if this is a JSON-only request (for steps without file uploads)
  const contentType = req.headers['content-type'] || '';
  const isMultipart = contentType.includes('multipart/form-data');

  console.log(`Request content type: ${contentType}, isMultipart: ${isMultipart}`);

  if (isMultipart) {
    // Handle multipart form data (with or without files)
    console.log('Processing multipart form data');

    // First, get the current step from the form data
    let currentStep = 1;

    // Create a temporary middleware to extract the current_step
    const extractCurrentStep = (req, callback) => {
      // For multipart forms, we need to check the fields as they come in
      if (req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data')) {
        let body = '';
        req.on('data', chunk => {
          // Only collect a small amount of data to find the current_step field
          if (body.length < 5000) {
            body += chunk.toString();
          }
        });

        req.on('end', () => {
          // Try to extract current_step from the form data
          const match = body.match(/name="current_step"\r\n\r\n(\d+)/);
          if (match && match[1]) {
            currentStep = parseInt(match[1]);
            console.log(`Extracted current_step from form data: ${currentStep}`);
          }
          callback();
        });
      } else {
        // For non-multipart forms, we can access the body directly
        if (req.body && req.body.current_step) {
          currentStep = parseInt(req.body.current_step);
          console.log(`Extracted current_step from request body: ${currentStep}`);
        }
        callback();
      }
    };

    // Extract the current step first
    extractCurrentStep(req, () => {
      console.log(`Processing save-draft for step ${currentStep}`);

      // Check if this step has file uploads
      const hasFileUploads = [1, 3, 5, 6].includes(currentStep);
      console.log(`Step ${currentStep} ${hasFileUploads ? 'has' : 'does not have'} file uploads`);

      // Use the step-specific upload middleware
      const stepSpecificUpload = createStepSpecificUpload(currentStep);
      const multerMiddleware = stepSpecificUpload.any();

      multerMiddleware(req, res, (err) => {
        if (err) {
          console.error(`Multer error in save-draft (step ${currentStep}):`, err);
          // Return a more user-friendly error message
          if (err.message && err.message.includes('Unexpected end of form')) {
            return res.status(400).json({
              success: false,
              error: 'File upload error',
              message: 'There was a problem with your file upload. Please try with smaller files or fewer files at once.'
            });
          }
          // Handle other multer errors
          return res.status(400).json({
            success: false,
            error: err.message,
            message: 'Error processing form data. Please try again.'
          });
        }

        // Log the processed files
        console.log(`Successfully processed ${req.files ? req.files.length : 0} files for step ${currentStep}`);

        // Check if we have JSON data for steps without file uploads
        if (!hasFileUploads && req.body && req.body.json_data) {
          try {
            // Parse the JSON data and add it to req.body
            const jsonData = JSON.parse(req.body.json_data);
            console.log('Parsed JSON data:', Object.keys(jsonData));

            // Merge the JSON data with req.body
            for (const key in jsonData) {
              if (key !== 'json_data') {
                req.body[key] = jsonData[key];
              }
            }

            // Remove the original json_data to avoid confusion
            delete req.body.json_data;
          } catch (error) {
            console.error('Error parsing JSON data:', error);
            return res.status(400).json({
              success: false,
              error: 'Invalid JSON data',
              message: 'The form data could not be processed. Please try again.'
            });
          }
        }

        // If no error, proceed to the controller
        procurementController.saveDraft(req, res);
      });
    });
  } else {
    // Handle JSON data (for steps without file uploads)
    console.log('Processing JSON data');

    // If it's not multipart, we can access the body directly
    if (!req.body) {
      return res.status(400).json({
        success: false,
        error: 'No data received',
        message: 'No form data was received. Please try again.'
      });
    }

    // Proceed to the controller
    procurementController.saveDraft(req, res);
  }
});

// Generate PDF report
router.get('/procurement/:id/pdf', procurementController.generatePDF);

// Generate quotation PDF
router.post('/procurement/generate-quotation', procurementController.generateQuotation);

// Generate comparative statement PDF
router.post('/procurement/generate-comparative-pdf', procurementController.generateComparativePdf);

// Delete procurement
router.post('/procurement/:id/delete', procurementController.deleteProcurement);

// Submit procurement
router.post('/procurement/:id/submit', procurementController.submitProcurement);

// Get procurement details
router.get('/procurement/:id', procurementController.getProcurement);

module.exports = router;
