const express = require('express');
const router = express.Router();
const languageController = require('../controllers/language-controller');

// Test route to verify the language routes are working
router.get('/test', (req, res) => {
    res.send('Language routes are working!');
});

// Route to switch language - support both GET and POST
router.get('/switch/:lang', languageController.switchLanguage);
router.post('/switch/:lang', languageController.switchLanguage);

module.exports = router;
