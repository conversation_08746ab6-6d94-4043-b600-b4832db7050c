const express = require('express');
const router = express.Router();
const db = require('../config/database');

// Middleware to set layout and current page
router.use((req, res, next) => {
    // Determine if admin or user layout should be used
    res.locals.layout = req.session.userRole === 'admin' ? 'admin' : 'user';
    res.locals.currentPage = 'notifications';
    next();
});

// Get all notifications for the current user
router.get('/', async (req, res, next) => {
    try {
        console.log('Fetching notifications for user:', req.session.userId);
        // Check if notifications table exists
        let notifications = [];
        try {
            const [results] = await db.query(
                `SELECT n.*, DATE_FORMAT(n.created_at, '%d-%b-%Y %H:%i:%s') as formatted_date
                 FROM notifications n
                 WHERE n.user_id = ?
                 ORDER BY n.created_at DESC`,
                [req.session.userId]
            );
            notifications = results;
            console.log('Notifications found:', notifications.length, notifications);

            // Mark all as read
            await db.query(
                'UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0',
                [req.session.userId]
            );
        } catch (error) {
            console.warn('Error fetching notifications:', error.message);
            // Table might not exist yet, use empty array
        }

        res.render('notifications/index', {
            title: 'Notifications',
            pageTitle: 'Your Notifications',
            notifications
        });
    } catch (error) {
        console.error('Error loading notifications:', error);
        next(error);
    }
});

// Mark notification as read
router.post('/mark-read/:id', async (req, res) => {
    try {
        const notificationId = req.params.id;

        await db.query(
            'UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (req.xhr) {
            return res.json({ success: true });
        }

        res.redirect('/notifications');
    } catch (error) {
        console.error('Error marking notification as read:', error);

        if (req.xhr) {
            return res.status(500).json({ error: 'Failed to mark notification as read' });
        }

        req.session.flashError = 'Failed to mark notification as read';
        res.redirect('/notifications');
    }
});

// Delete notification
router.post('/delete/:id', async (req, res) => {
    try {
        const notificationId = req.params.id;

        await db.query(
            'DELETE FROM notifications WHERE id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (req.xhr) {
            return res.json({ success: true });
        }

        req.session.flashSuccess = 'Notification deleted';
        res.redirect('/notifications');
    } catch (error) {
        console.error('Error deleting notification:', error);

        if (req.xhr) {
            return res.status(500).json({ error: 'Failed to delete notification' });
        }

        req.session.flashError = 'Failed to delete notification';
        res.redirect('/notifications');
    }
});

// Mark all notifications as read
router.post('/mark-all-read', async (req, res) => {
    try {
        await db.query(
            'UPDATE notifications SET is_read = 1 WHERE user_id = ?',
            [req.session.userId]
        );

        if (req.xhr) {
            return res.json({ success: true });
        }

        req.session.flashSuccess = 'All notifications marked as read';
        res.redirect('/notifications');
    } catch (error) {
        console.error('Error marking all notifications as read:', error);

        if (req.xhr) {
            return res.status(500).json({ error: 'Failed to mark all notifications as read' });
        }

        req.session.flashError = 'Failed to mark all notifications as read';
        res.redirect('/notifications');
    }
});

// Helper function to create a notification
async function createNotification(userId, message, type = 'info') {
    try {
        await db.query(
            'INSERT INTO notifications (user_id, message, type, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
            [userId, message, type]
        );
        return true;
    } catch (error) {
        console.error('Error creating notification:', error);
        return false;
    }
}

// Export router and helper function
// Mark a notification as read
router.post('/:id/read', async (req, res) => {
    try {
        const notificationId = req.params.id;

        // Verify the notification belongs to the user
        const [notification] = await db.query(
            'SELECT * FROM notifications WHERE notification_id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (notification.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        // Mark as read
        await db.query(
            'UPDATE notifications SET is_read = 1 WHERE notification_id = ?',
            [notificationId]
        );

        res.json({
            success: true,
            message: 'Notification marked as read'
        });
    } catch (error) {
        console.error('Error marking notification as read:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error marking notification as read'
        });
    }
});

// Delete a notification
router.post('/:id/delete', async (req, res) => {
    try {
        const notificationId = req.params.id;

        // Verify the notification belongs to the user
        const [notification] = await db.query(
            'SELECT * FROM notifications WHERE notification_id = ? AND user_id = ?',
            [notificationId, req.session.userId]
        );

        if (notification.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

        // Delete the notification
        await db.query(
            'DELETE FROM notifications WHERE notification_id = ?',
            [notificationId]
        );

        res.json({
            success: true,
            message: 'Notification deleted'
        });
    } catch (error) {
        console.error('Error deleting notification:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting notification'
        });
    }
});

// Helper function to create a notification (for use in other routes)
function createNotification(userId, message, type = 'info', link = null, title = null) {
    return new Promise(async (resolve, reject) => {
        try {
            await db.query(
                'INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at) VALUES (?, ?, ?, ?, ?, 0, NOW())',
                [userId, title || message.substring(0, 50), message, type, link]
            );
            resolve(true);
        } catch (error) {
            console.error('Error creating notification:', error);
            resolve(false);
        }
    });
}

// Add sample test notifications (for development only)
router.get('/add-samples', async (req, res) => {
    try {
        if (!req.session.userId) {
            return res.redirect('/login');
        }

        console.log('Adding sample notifications for user:', req.session.userId);

        // Start a transaction
        await db.query('START TRANSACTION');

        try {
            // Add sample notifications
            const sampleNotifications = [
                {
                    title: 'Welcome to the platform',
                    message: 'Thank you for joining our exam preparation platform. We hope you find it useful for your studies.',
                    type: 'system',
                    link: '/dashboard'
                },
                {
                    title: 'New test available',
                    message: 'A new practice test has been added to your account. Check it out to improve your skills!',
                    type: 'test',
                    link: '/tests'
                },
                {
                    title: 'Group invitation',
                    message: 'You have been invited to join the "Advanced Study Group". Join now to collaborate with other students.',
                    type: 'group',
                    link: '/groups'
                },
                {
                    title: 'Important announcement',
                    message: 'The platform will be undergoing maintenance this weekend. Please plan your study schedule accordingly.',
                    type: 'admin',
                    link: '/notifications'
                },
                {
                    title: 'Test results available',
                    message: 'Your results for the recent practice test are now available. Check your performance and areas for improvement.',
                    type: 'test',
                    link: '/tests/results'
                }
            ];

            // Insert sample notifications
            for (const notification of sampleNotifications) {
                console.log('Inserting notification:', notification);
                const [result] = await db.query(
                    `INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
                     VALUES (?, ?, ?, ?, ?, 0, NOW())`,
                    [req.session.userId, notification.title, notification.message, notification.type, notification.link]
                );
                console.log('Insert result:', result);
            }

            // Commit the transaction
            await db.query('COMMIT');
            console.log('Transaction committed successfully');

            req.flash('success', '5 sample notifications have been added to your account');
            res.redirect('/notifications');
        } catch (error) {
            // Rollback in case of error
            await db.query('ROLLBACK');
            console.error('Error in transaction, rolled back:', error);
            throw error;
        }
    } catch (error) {
        console.error('Error adding sample notifications:', error);
        req.flash('error', 'Failed to add sample notifications: ' + error.message);
        res.redirect('/notifications');
    }
});

// Export the router and createNotification function
module.exports = router;
module.exports.createNotification = createNotification;