const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated } = require('../middleware/auth');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const bodyParser = require('body-parser');
const fileUpload = require('express-fileupload');

// Formidable dependency removed

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Ensure upload directory exists
const uploadDir = path.join(__dirname, '../public/uploads/profiles');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Create a new upload directory with full permissions
const newUploadDir = path.join(__dirname, '../public/uploads/profile_new');
if (!fs.existsSync(newUploadDir)) {
    fs.mkdirSync(newUploadDir, { recursive: true });
    // Set full permissions
    fs.chmodSync(newUploadDir, 0o777);
}

// Helper function to generate a unique filename
const generateUniqueFilename = (originalName) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    let ext = path.extname(originalName);
    if (!ext) {
        // Default to .jpg if no extension
        ext = '.jpg';
    }
    return 'profile-' + uniqueSuffix + ext;
};

// Helper function to validate file type
const isImageFile = (mimetype) => {
    return mimetype && mimetype.startsWith('image/');
};

// Ensure upload directory exists
if (!fs.existsSync(newUploadDir)) {
    try {
        fs.mkdirSync(newUploadDir, { recursive: true });
        console.log('Created upload directory:', newUploadDir);
    } catch (err) {
        console.error('Error creating upload directory:', err);
    }
}

// Configure express-fileupload for profile image uploads
const profileFileUploadMiddleware = fileUpload({
    useTempFiles: true,
    tempFileDir: '/tmp/',
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    abortOnLimit: true,
    debug: true,
    createParentPath: true,
    safeFileNames: true,
    preserveExtension: true
});

// Custom middleware to handle file uploads with express-fileupload
const handleFileUpload = (req, res, next) => {
    console.log('Starting file upload process with express-fileupload...');

    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
        console.log('No files were uploaded');
        req.session.flashError = 'No file was uploaded';
        return res.redirect('/profile/edit');
    }

    // Get the uploaded file
    const profileImage = req.files.profile_image;

    // Validate file type
    if (!profileImage.mimetype.startsWith('image/')) {
        console.error('Invalid file type:', profileImage.mimetype);
        req.session.flashError = 'Only image files are allowed!';
        return res.redirect('/profile/edit');
    }

    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    let ext = path.extname(profileImage.name);
    if (!ext) {
        // Default to .jpg if no extension
        ext = '.jpg';
    }
    const filename = 'profile-' + uniqueSuffix + ext;

    // Ensure upload directory exists
    if (!fs.existsSync(newUploadDir)) {
        try {
            fs.mkdirSync(newUploadDir, { recursive: true });
            console.log('Created upload directory on-demand:', newUploadDir);
        } catch (err) {
            console.error('Error creating upload directory on-demand:', err);
            req.session.flashError = 'Could not create upload directory';
            return res.redirect('/profile/edit');
        }
    }

    // Set the upload path
    const uploadPath = path.join(newUploadDir, filename);

    // Move the file to the upload directory
    profileImage.mv(uploadPath, function(err) {
        if (err) {
            console.error('Error moving uploaded file:', err);
            req.session.flashError = `File upload failed: ${err.message || 'Unknown error'}`;
            return res.redirect('/profile/edit');
        }

        // Add file info to the request object
        req.file = {
            filename: filename,
            originalname: profileImage.name,
            mimetype: profileImage.mimetype,
            size: profileImage.size,
            path: uploadPath
        };

        console.log('File uploaded successfully:', {
            filename: filename,
            originalname: profileImage.name,
            mimetype: profileImage.mimetype,
            size: profileImage.size,
            path: uploadPath
        });

        // Everything went fine
        next();
    });
};

// Middleware to set layout and current page
router.use((req, res, next) => {
    // Determine if admin or user layout should be used
    res.locals.layout = req.session.userRole === 'admin' ? 'admin' : 'user';
    res.locals.currentPage = 'profile';
    next();
});

// Profile page
router.get('/', async (req, res, next) => {
    try {
        // Get user data
        const [users] = await db.query(
            'SELECT * FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/logout');
        }

        const user = users[0];

        // Debug profile image path
        console.log('User profile image path:', user.profile_image);

        // Get user's test attempts
        const [attempts] = await db.query(`
            SELECT a.*, e.exam_name, e.passing_marks,
                   TIMESTAMPDIFF(MINUTE, a.start_time, a.end_time) as duration_minutes
            FROM exam_attempts a
            JOIN exams e ON a.exam_id = e.exam_id
            WHERE a.user_id = ?
            ORDER BY a.attempt_date DESC
            LIMIT 10
        `, [req.session.userId]);

        res.render('profile/index', {
            title: 'My Profile',
            pageTitle: 'My Profile',
            user,
            attempts,
            currentPage: 'profile',
            layout: 'user', // Explicitly set the layout
            formatDate, // Pass the date formatter function
            formatDateTime // Pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading profile:', error);
        next(error);
    }
});

// Edit profile page
router.get('/edit', async (req, res, next) => {
    try {
        // Get user data
        const [users] = await db.query(
            'SELECT * FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/profile');
        }

        res.render('profile/edit', {
            title: 'Edit Profile',
            pageTitle: 'Edit Profile',
            user: users[0],
            currentPage: 'profile',
            layout: 'user', // Explicitly set the layout
            formatDate, // Pass the date formatter function
            formatDateTime // Pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading edit profile:', error);
        next(error);
    }
});

// Profile update route with all fields - no file handling
router.post('/update', bodyParser.urlencoded({ extended: true }), async (req, res) => {
    console.log('PROFILE INFO UPDATE REQUEST RECEIVED');
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Request body:', req.body);

    try {
        // Process the form data directly
        await handleProfileUpdate(req, res);
    } catch (error) {
        console.error('Error in profile update:', error);
        req.session.flashError = 'An error occurred while updating your profile. Please try again.';
        return res.redirect('/profile/edit');
    }
});

// Helper function to handle the profile update logic
async function handleProfileUpdate(req, res) {
    console.log('Processing profile update with body:', req.body);

    try {
        // Extract all fields
        const {
            name,
            email,
            bio,
            institution,
            grade,
            field_of_study,
            preferred_subjects,
            target_exams,
            study_goal,
            language_preference,
            time_zone,
            accessibility_needs,
            image_changed,
            image_action
        } = req.body;

        // Get current user data for image handling
        const [users] = await db.query(
            'SELECT profile_image FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/profile/edit');
        }

        // Build the query with all fields
        let query = `
            UPDATE users SET
                name = ?,
                email = ?,
                bio = ?,
                institution = ?,
                grade = ?,
                field_of_study = ?,
                preferred_subjects = ?,
                target_exams = ?,
                study_goal = ?,
                language_preference = ?,
                time_zone = ?,
                accessibility_needs = ?
        `;

        let params = [
            name || '',
            email,
            bio || '',
            institution || null,
            grade || null,
            field_of_study || null,
            preferred_subjects || null,
            target_exams || null,
            study_goal || null,
            language_preference || null,
            time_zone || null,
            accessibility_needs || null
        ];

        // Handle image changes if needed
        if (image_changed === 'true') {
            console.log('Image changed, action:', image_action);

            if (image_action === 'clear') {
                // Set profile image to null to use initials avatar
                query += ', profile_image = ?';
                params.push(null);

                // Try to delete the old image file
                try {
                    const currentImage = users[0].profile_image;
                    if (currentImage &&
                        !currentImage.includes('default') &&
                        !currentImage.includes('default-avatar')) {

                        // Check for different possible paths
                        const possiblePaths = [
                            // Standard path
                            path.join(__dirname, '../public', currentImage),
                            // Try without 'public' if the path already includes it
                            path.join(__dirname, '..', currentImage),
                            // Try with just the filename for uploads directory
                            path.join(__dirname, '../public/uploads/profiles', path.basename(currentImage)),
                            // Try profile_new directory
                            path.join(__dirname, '../public/uploads/profile_new', path.basename(currentImage))
                        ];

                        let deleted = false;

                        // Try each possible path
                        for (const imagePath of possiblePaths) {
                            console.log('Checking for image at:', imagePath);

                            if (fs.existsSync(imagePath)) {
                                console.log('Found image at:', imagePath);
                                console.log('Deleting profile image:', imagePath);
                                fs.unlinkSync(imagePath);
                                console.log('Profile image file deleted successfully');
                                deleted = true;
                                break;
                            }
                        }

                        if (!deleted) {
                            console.log('Image file not found in any of the checked locations');
                        }
                    }
                } catch (fileError) {
                    // Log the error but don't fail the request
                    console.error('Error deleting profile image file (non-critical):', fileError);
                }
            } else if (image_action === 'upload' && req.file) {
                // Handle file upload with Multer
                const file = req.file;

                // File is already validated by Multer, just get the path
                const filename = file.filename;
                const filepath = file.path;
                const profileImagePath = `/uploads/profile_new/${filename}`;

                console.log('File saved to:', filepath);

                // Add to query
                query += ', profile_image = ?';
                params.push(profileImagePath);

                // Delete old image if exists
                try {
                    const currentImage = users[0].profile_image;
                    if (currentImage &&
                        !currentImage.includes('default') &&
                        !currentImage.includes('default-avatar')) {

                        const oldImagePath = path.join(__dirname, '../public', currentImage);
                        if (fs.existsSync(oldImagePath)) {
                            fs.unlinkSync(oldImagePath);
                            console.log('Old profile image deleted successfully');
                        }
                    }
                } catch (deleteError) {
                    console.error('Error deleting old profile image (non-critical):', deleteError);
                }
            }
        }

        // Complete the query
        query += ' WHERE id = ?';
        params.push(req.session.userId);

        console.log('Executing query:', query);
        console.log('With params:', params);

        // Execute the update
        await db.query(query, params);

        console.log('Profile information updated successfully');
        req.session.flashSuccess = 'Profile information updated successfully';
        return res.redirect('/profile');
    } catch (error) {
        console.error('Error updating profile information:', error);
        req.session.flashError = 'Failed to update profile information. Please try again.';
        return res.redirect('/profile/edit');
    }
}

// Profile image upload route using express-fileupload
router.post('/upload-image', profileFileUploadMiddleware, handleFileUpload, async (req, res) => {
    console.log('PROFILE IMAGE UPLOAD REQUEST RECEIVED');
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Request file:', req.file ? 'File received' : 'No file');
    console.log('Request body:', req.body);

    try {
        // Check if file exists in the request
        if (!req.file) {
            console.log('No file was uploaded or no profile_image field');
            req.session.flashError = 'No image file selected';
            return res.redirect('/profile/edit');
        }

        const file = req.file;
        console.log('File info:', {
            originalname: file.originalname,
            filename: file.filename,
            mimetype: file.mimetype,
            size: file.size,
            path: file.path
        });

        // Validate file type
        if (!isImageFile(file.mimetype)) {
            console.log('Invalid file type:', file.mimetype);
            req.session.flashError = 'Only image files are allowed!';
            return res.redirect('/profile/edit');
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            console.log('File too large:', file.size, 'bytes');
            req.session.flashError = 'Image file is too large. Maximum size is 5MB.';
            return res.redirect('/profile/edit');
        }

        // File is already saved by multer, just get the path
        const filename = file.filename;
        const filepath = file.path;

        // Use the direct image serving route instead of the static path
        const profileImagePath = `/profile/image/${filename}`;

        console.log('File saved to:', filepath);
        console.log('Profile image path for database:', profileImagePath);

        // Set permissions on the uploaded file and verify file exists
        try {
            // Check if file exists
            if (fs.existsSync(filepath)) {
                console.log('Verified file exists at:', filepath);
                fs.chmodSync(filepath, 0o666); // Read/write for everyone
                console.log('Set permissions on uploaded file');
            } else {
                console.error('File does not exist at path:', filepath);
                throw new Error('File not found on disk after upload');
            }
        } catch (chmodError) {
            console.error('Error with file permissions or verification:', chmodError);
            // Continue anyway, the file might still be accessible
        }

        // Get current user data for old image path
        const [users] = await db.query(
            'SELECT profile_image FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/profile/edit');
        }

        // Update user profile with new image in database first
        try {
            // Ensure the path is correct for the web server
            const webPath = profileImagePath.startsWith('/') ? profileImagePath : `/${profileImagePath}`;

            await db.query(
                'UPDATE users SET profile_image = ? WHERE id = ?',
                [webPath, req.session.userId]
            );
            console.log('Database updated with new profile image path:', webPath);
        } catch (dbError) {
            console.error('Database error:', dbError);
            // Try to clean up the uploaded file since we couldn't update the database
            try {
                if (fs.existsSync(filepath)) {
                    fs.unlinkSync(filepath);
                    console.log('Cleaned up uploaded file after database error');
                }
            } catch (cleanupError) {
                console.error('Error cleaning up file after database error:', cleanupError);
            }
            req.session.flashError = 'Failed to update profile image in database';
            return res.redirect('/profile/edit');
        }

        // Now try to delete the old profile image if it exists
        try {
            const currentImage = users[0].profile_image;
            if (currentImage &&
                !currentImage.includes('default') &&
                !currentImage.includes('default-avatar')) {

                const oldImagePath = path.join(__dirname, '../public', currentImage);
                console.log('Checking for old image to delete at:', oldImagePath);

                if (fs.existsSync(oldImagePath)) {
                    console.log('Deleting old profile image:', oldImagePath);
                    fs.unlinkSync(oldImagePath);
                    console.log('Old profile image deleted successfully');
                } else {
                    console.log('Old image file not found on disk');
                }
            } else {
                console.log('No old custom profile image to delete');
            }
        } catch (deleteError) {
            // Log error but continue since this is non-critical
            console.error('Error deleting old profile image (non-critical):', deleteError);
            console.log('Database was still updated successfully');
        }

        console.log('Profile image updated successfully');
        req.session.flashSuccess = 'Profile image updated successfully';
        return res.redirect('/profile');
    } catch (error) {
        console.error('Error updating profile image:', error);
        req.session.flashError = 'Failed to update profile image. Please try again.';
        return res.redirect('/profile/edit');
    }
    // End of the route handler
});

// Clear profile image route - supports both GET and POST
router.all('/clear-image', async (req, res) => {
    console.log('Clear image request method:', req.method);
    console.log('CLEAR PROFILE IMAGE REQUEST RECEIVED');

    try {
        // Get current profile image
        const [users] = await db.query(
            'SELECT profile_image FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/profile/edit');
        }

        // Log the current image path for debugging
        const currentImage = users[0].profile_image;
        console.log('Current profile image path:', currentImage);

        // Always update the database first to ensure the profile image is cleared
        // even if file deletion fails
        const defaultImage = '/images/default-avatar.png';
        await db.query(
            'UPDATE users SET profile_image = ? WHERE id = ?',
            [defaultImage, req.session.userId]
        );
        console.log('Database updated with default image path');

        // Now try to delete the file if it exists
        try {
            // Attempt to delete if there's any image path
            if (currentImage) {

                // Check for different possible paths
                const possiblePaths = [
                    // Standard path
                    path.join(__dirname, '../public', currentImage),
                    // Try without 'public' if the path already includes it
                    path.join(__dirname, '..', currentImage),
                    // Try with just the filename for uploads directory
                    path.join(__dirname, '../public/uploads/profiles', path.basename(currentImage)),
                    // Try profile_new directory
                    path.join(__dirname, '../public/uploads/profile_new', path.basename(currentImage))
                ];

                let deleted = false;

                // Try each possible path
                for (const imagePath of possiblePaths) {
                    console.log('Checking for image at:', imagePath);

                    if (fs.existsSync(imagePath)) {
                        console.log('Found image at:', imagePath);
                        console.log('Deleting profile image:', imagePath);
                        fs.unlinkSync(imagePath);
                        console.log('Profile image file deleted successfully');
                        deleted = true;
                        break;
                    }
                }

                if (!deleted) {
                    console.log('Image file not found in any of the checked locations');
                    // List contents of uploads directories for debugging
                    try {
                        const profilesDir = path.join(__dirname, '../public/uploads/profiles');
                        const profileNewDir = path.join(__dirname, '../public/uploads/profile_new');

                        if (fs.existsSync(profilesDir)) {
                            console.log('Contents of profiles directory:', fs.readdirSync(profilesDir));
                        }

                        if (fs.existsSync(profileNewDir)) {
                            console.log('Contents of profile_new directory:', fs.readdirSync(profileNewDir));
                        }
                    } catch (listError) {
                        console.error('Error listing directory contents:', listError);
                    }
                }
            } else {
                console.log('No custom profile image to delete or using default image');
            }
        } catch (fileError) {
            // Log the error but don't fail the request since the database was updated
            console.error('Error deleting profile image file (non-critical):', fileError);
            console.log('Database was still updated successfully');
        }

        console.log('Profile image cleared successfully');
        req.session.flashSuccess = 'Profile image cleared successfully';
        return res.redirect('/profile');
    } catch (error) {
        console.error('Error clearing profile image:', error);
        req.session.flashError = 'Failed to clear profile image. Please try again.';
        return res.redirect('/profile/edit');
    }
});

// Change password page
router.get('/change-password', (req, res) => {
    res.render('profile/change-password', {
        title: 'Change Password',
        pageTitle: 'Change Password',
        currentPage: 'profile',
        layout: 'user', // Explicitly set the layout
        formatDate, // Pass the date formatter function
        formatDateTime // Pass the date-time formatter function
    });
});

// Update password
router.post('/change-password', async (req, res, next) => {
    try {
        const { current_password, new_password, confirm_password } = req.body;

        // Validate input
        if (!current_password || !new_password || !confirm_password) {
            req.session.flashError = 'All fields are required';
            return res.redirect('/profile/change-password');
        }

        if (new_password !== confirm_password) {
            req.session.flashError = 'New passwords do not match';
            return res.redirect('/profile/change-password');
        }

        // Get user data
        const [users] = await db.query(
            'SELECT password FROM users WHERE id = ?',
            [req.session.userId]
        );

        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/profile');
        }

        const user = users[0];

        // Verify current password
        const bcrypt = require('bcrypt');
        const isMatch = await bcrypt.compare(current_password, user.password);

        if (!isMatch) {
            req.session.flashError = 'Current password is incorrect';
            return res.redirect('/profile/change-password');
        }

        // Hash new password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(new_password, salt);

        // Update password
        await db.query(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, req.session.userId]
        );

        req.session.flashSuccess = 'Password changed successfully';
        res.redirect('/profile');
    } catch (error) {
        console.error('Error changing password:', error);
        next(error);
    }
});

// Route to serve profile images directly
router.get('/image/:filename', (req, res) => {
    const filename = req.params.filename;
    const imagePath = path.join(__dirname, '../public/uploads/profile_new', filename);

    console.log('Serving profile image from:', imagePath);

    // Check if file exists
    if (fs.existsSync(imagePath)) {
        res.sendFile(imagePath);
    } else {
        console.error('Profile image not found:', imagePath);
        res.status(404).send('Image not found');
    }
});

// Delete account
router.post('/delete-account', async (req, res, next) => {
    try {
        const { confirm_delete } = req.body;

        if (confirm_delete !== 'DELETE') {
            req.session.flashError = 'Please type DELETE to confirm account deletion';
            return res.redirect('/profile');
        }

        // Get user data
        const [users] = await db.query(
            'SELECT profile_image FROM users WHERE id = ?',
            [req.session.userId]
        );

        // Delete profile image if exists
        if (users.length > 0 && users[0].profile_image) {
            const imagePath = path.join(__dirname, '../public', users[0].profile_image);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        // Delete user
        await db.query('DELETE FROM users WHERE id = ?', [req.session.userId]);

        // Destroy session
        req.session.destroy();

        res.redirect('/login?deleted=true');
    } catch (error) {
        console.error('Error deleting account:', error);
        next(error);
    }
});

module.exports = router;