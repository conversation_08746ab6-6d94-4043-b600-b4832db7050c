const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated } = require('../middleware/auth');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/profiles');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname) || '.jpg';
        cb(null, 'profile-' + uniqueSuffix + ext);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            return cb(null, true);
        }
        cb(new Error('Only image files are allowed!'));
    }
});

// Simple profile update route
router.post('/basic', upload.single('profile_image'), async (req, res) => {
    console.log('Basic profile update route called');
    
    try {
        // Extract form fields
        const {
            name,
            email,
            bio,
            institution,
            grade,
            field_of_study,
            preferred_subjects,
            target_exams,
            study_goal,
            language_preference,
            time_zone,
            accessibility_needs
        } = req.body;
        
        console.log('Received form data:', { name, email, bio });
        
        // Start building the query
        let query = 'UPDATE users SET email = ?, bio = ?';
        let params = [email, bio || null];
        
        // Add name if provided
        if (name) {
            query += ', name = ?';
            params.push(name);
        }
        
        // Add education details
        if (institution !== undefined) {
            query += ', institution = ?';
            params.push(institution || null);
        }
        
        if (grade !== undefined) {
            query += ', grade = ?';
            params.push(grade || null);
        }
        
        if (field_of_study !== undefined) {
            query += ', field_of_study = ?';
            params.push(field_of_study || null);
        }
        
        // Add exam preferences
        if (preferred_subjects !== undefined) {
            query += ', preferred_subjects = ?';
            params.push(preferred_subjects || null);
        }
        
        if (target_exams !== undefined) {
            query += ', target_exams = ?';
            params.push(target_exams || null);
        }
        
        if (study_goal !== undefined) {
            query += ', study_goal = ?';
            params.push(study_goal || null);
        }
        
        // Add additional information
        if (language_preference !== undefined) {
            query += ', language_preference = ?';
            params.push(language_preference || null);
        }
        
        if (time_zone !== undefined) {
            query += ', time_zone = ?';
            params.push(time_zone || null);
        }
        
        if (accessibility_needs !== undefined) {
            query += ', accessibility_needs = ?';
            params.push(accessibility_needs || null);
        }
        
        // Handle profile image if uploaded
        if (req.file) {
            console.log('Profile image uploaded:', req.file);
            
            const profileImagePath = `/uploads/profiles/${path.basename(req.file.path)}`;
            query += ', profile_image = ?';
            params.push(profileImagePath);
            
            // Delete old profile image if exists
            try {
                const [users] = await db.query(
                    'SELECT profile_image FROM users WHERE id = ?',
                    [req.session.userId]
                );
                
                if (users.length > 0 && users[0].profile_image && users[0].profile_image !== profileImagePath) {
                    const oldImagePath = path.join(__dirname, '../public', users[0].profile_image);
                    console.log('Checking for old image at:', oldImagePath);
                    
                    if (fs.existsSync(oldImagePath)) {
                        console.log('Deleting old profile image:', oldImagePath);
                        fs.unlinkSync(oldImagePath);
                    } else {
                        console.log('Old image file not found on disk');
                    }
                }
            } catch (deleteError) {
                // Log error but continue with update
                console.error('Error deleting old profile image:', deleteError);
            }
        } else {
            console.log('No profile image uploaded');
        }
        
        // Complete the query
        query += ' WHERE id = ?';
        params.push(req.session.userId);
        
        console.log('Executing query:', query);
        console.log('With params:', params);
        
        // Execute the update
        await db.query(query, params);
        
        console.log('Profile updated successfully');
        req.session.flashSuccess = 'Profile updated successfully';
        return res.redirect('/profile');
    } catch (error) {
        console.error('Error updating profile:', error);
        
        // Set a user-friendly error message
        req.session.flashError = 'Failed to update profile. Please try again.';
        
        // Log detailed error for debugging
        const errorDetails = {
            message: error.message,
            stack: error.stack,
            code: error.code,
            sqlState: error.sqlState,
            sqlMessage: error.sqlMessage
        };
        console.error('Profile update error details:', errorDetails);
        
        // Redirect back to edit page instead of showing error page
        return res.redirect('/profile/edit');
    }
});

module.exports = router;
