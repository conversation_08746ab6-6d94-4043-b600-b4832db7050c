const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAdmin } = require('../middleware/auth');
const multer = require('multer');
const csv = require('csv-parse');
const xlsx = require('xlsx');
const upload = multer({ storage: multer.memoryStorage() });
// Simple logging function to replace the enhanced logger
const logJavaScriptVariable = (varName, varValue, options) => {
    const context = options.context || 'unknown';
    const user = options.user || null;

    // Skip logging objects and arrays to avoid console clutter
    if (typeof varValue === 'object' && varValue !== null) {
        console.log(`[QUESTION-ROUTES] ${context} - ${varName} - [Object/Array]`);
    } else {
        console.log(`[QUESTION-ROUTES] ${context} - ${varName} - ${varValue}`);
    }
};
const routeVariableLogger = require('../middleware/route-variable-logger');

// Apply admin check middleware to all routes except API endpoints
router.use((req, res, next) => {
    if (req.path.startsWith('/api/')) {
        return next();
    }
    checkAdmin(req, res, next);
});

// API endpoint to get counts directly
router.get('/api/counts', async (req, res) => {
    try {
        console.log('API counts endpoint called');

        // Get total questions count
        const [totalQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE is_deleted = 0');
        const totalQuestions = totalQuestionsResult[0].count;

        // Get essays count
        const [essaysResult] = await db.query('SELECT COUNT(*) as count FROM essays');
        const essaysCount = essaysResult[0].count;

        // Get linked questions count
        const [linkedQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE essay_id IS NOT NULL AND is_deleted = 0');
        const linkedQuestionsCount = linkedQuestionsResult[0].count;

        // Get question type counts
        const [questionTypeStats] = await db.query(`
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions
            WHERE is_deleted = 0
            GROUP BY question_type
        `);

        console.log('API counts:', { totalQuestions, essaysCount, linkedQuestionsCount, questionTypeStats });

        // Return the counts as JSON
        res.json({
            totalQuestions,
            totalQuestionsUnfiltered: totalQuestions, // Add this for the quick filter
            essaysCount,
            linkedQuestionsCount,
            questionTypeStats
        });
    } catch (error) {
        console.error('Error fetching counts:', error);
        res.status(500).json({ error: 'Failed to fetch counts' });
    }
});

// New API endpoint with the correct path that matches the client request
router.get('/api/counts/questions', async (req, res) => {
    try {
        console.log('API counts endpoint called');

        // Get total questions count
        const [totalQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE is_deleted = 0');
        const totalQuestions = totalQuestionsResult[0].count;

        // Get essays count
        const [essaysResult] = await db.query('SELECT COUNT(*) as count FROM essays');
        const essaysCount = essaysResult[0].count;

        // Get linked questions count
        const [linkedQuestionsResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE essay_id IS NOT NULL AND is_deleted = 0');
        const linkedQuestionsCount = linkedQuestionsResult[0].count;

        // Get question type counts
        const [questionTypeStats] = await db.query(`
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions
            WHERE is_deleted = 0
            GROUP BY question_type
        `);

        console.log('API counts:', { totalQuestions, essaysCount, linkedQuestionsCount, questionTypeStats });

        // Return the counts as JSON
        res.json({
            totalQuestions,
            totalQuestionsUnfiltered: totalQuestions, // Add this for the quick filter
            essaysCount,
            linkedQuestionsCount,
            questionTypeStats
        });
    } catch (error) {
        console.error('Error fetching counts:', error);
        res.status(500).json({ error: 'Failed to fetch counts' });
    }
});

// Middleware to set admin layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.currentPage = req.path.split('/')[1] || 'questions';
    next();
});

// Middleware to pass flash messages to all templates
router.use((req, res, next) => {
    if (req.flash) {
        res.locals.messages = req.flash();
    }
    next();
});

// Question Bank - Main page
router.get('/', routeVariableLogger('questions-index'), async (req, res, next) => {
    try {
        console.log('\n==== QUESTIONS PAGE REQUEST ====');
        console.log('Query parameters:', req.query);

        // Get all questions with pagination
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        console.log('Pagination:', { page, perPage, offset });

        // Build filter conditions
        const filters = [];
        const params = [];

        if (req.query.search) {
            filters.push('(q.question_text LIKE ? OR q.solution_text LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.type) {
            filters.push('q.question_type = ?');
            params.push(req.query.type);
        }

        if (req.query.category) {
            filters.push('qcm.category_id = ?');
            params.push(req.query.category);
        }

        if (req.query.exam) {
            filters.push('e.exam_id = ?');
            params.push(req.query.exam);
        }

        if (req.query.section) {
            filters.push('s.section_id = ?');
            params.push(req.query.section);
        }

        // Build the WHERE clause
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

        console.log('Filter conditions:', { filters, params, whereClause });

        // Get total count for pagination
        const [countResult] = await db.query(`
            SELECT COUNT(DISTINCT q.question_id) as total
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            ${whereClause}
        `, params);

        const totalQuestions = countResult[0].total;
        const totalPages = Math.ceil(totalQuestions / perPage);

        console.log('Query results:', { totalQuestions, totalPages });

        // Get question type statistics with filter conditions
        let questionTypeQuery = `
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            ${whereClause}
            GROUP BY question_type
        `;

        const [questionTypeStats] = await db.query(questionTypeQuery, params);

        // Get total number of question types
        const [questionTypesCount] = await db.query(`
            SELECT COUNT(DISTINCT question_type) as count
            FROM questions
        `);

        // Get total count of all questions (unfiltered) - Using a direct query
        let totalQuestionsUnfiltered = 0;
        try {
            // Use a simple, reliable query
            const [totalQuestionsResult] = await db.query('SELECT COUNT(*) as total FROM questions');
            totalQuestionsUnfiltered = totalQuestionsResult[0].total;
            console.log('Total questions count:', totalQuestionsUnfiltered);
        } catch (countError) {
            console.error('Error getting total questions count:', countError);
            // Try with the alternative database name
            try {
                    const [totalQuestionsResult] = await db.query('SELECT COUNT(*) as total FROM exam_prep_platform.questions');
                totalQuestionsUnfiltered = totalQuestionsResult[0].total;
                console.log('Total questions count (alternative DB):', totalQuestionsUnfiltered);
            } catch (altError) {
                console.error('Error getting total questions count from alternative DB:', altError);
                // Set a default value if all queries fail
                totalQuestionsUnfiltered = totalQuestions || 0;
                console.log('Using default value for total questions count:', totalQuestionsUnfiltered);
            }
        }

        console.log('Total questions unfiltered:', totalQuestionsUnfiltered);
        console.log('Comparison of counts:', {
            filtered: totalQuestions,
            unfiltered: totalQuestionsUnfiltered,
            difference: totalQuestionsUnfiltered - totalQuestions
        });

        // Get unfiltered question type statistics
        // This query should always run regardless of filters
        const [unfilteredQuestionTypeStats] = await db.query(`
            SELECT
                question_type,
                COUNT(*) as count
            FROM questions
            GROUP BY question_type
        `);

        // Get total number of categories
        const [categoriesCount] = await db.query(`
            SELECT COUNT(DISTINCT category_id) as count
            FROM categories
        `);

        // Get questions with all related information
        const [questions] = await db.query(`
            SELECT
                q.*,
                e.exam_id,
                e.exam_name,
                s.section_id,
                s.section_name,
                GROUP_CONCAT(DISTINCT c.name) as category_names,
                GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            ${whereClause} AND q.is_deleted = 0 AND q.is_archived = 0
            GROUP BY q.question_id
            ORDER BY q.question_id DESC
            LIMIT ? OFFSET ?
        `, [...params, perPage, offset]);

        // Process the results to convert comma-separated strings to arrays
        questions.forEach(q => {
            q.category_ids = q.category_ids ? q.category_ids.split(',').map(Number) : [];
            q.category_names = q.category_names ? q.category_names.split(',') : [];
            q.category_name = q.category_names.join(', ');
        });

        // Get categories for filter
        const [categories] = await db.query('SELECT category_id, name FROM categories ORDER BY name');

        // Get exams for filter
        const [exams] = await db.query('SELECT exam_id, exam_name FROM exams ORDER BY exam_name');

        // Get sections for filter
        const [allSections] = await db.query('SELECT section_id, section_name FROM sections ORDER BY section_name');

        // Process sections to ensure uniqueness (case-insensitive)
        const uniqueSections = [];
        const seenNames = new Set();

        for (const section of allSections) {
            // Skip sections with empty names
            if (!section.section_name || section.section_name.trim() === '') {
                continue;
            }

            // Normalize the section name: lowercase, trim whitespace, and normalize multiple spaces
            const normalizedName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');

            if (!seenNames.has(normalizedName)) {
                seenNames.add(normalizedName);
                uniqueSections.push(section);
            }
        }

        const sections = uniqueSections;

        // Log the stats object for debugging
        console.log('Final stats object:', {
            totalQuestions,
            totalQuestionsUnfiltered,
            questionTypeStats: questionTypeStats?.length,
            unfilteredQuestionTypeStats: unfilteredQuestionTypeStats?.length,
            questionTypesCount: questionTypesCount[0].count,
            categoriesCount: categoriesCount[0].count
        });

        // Log the actual stats object being passed to the template
        console.log('Stats object passed to template:', {
            totalQuestions: totalQuestions,
            totalQuestionsUnfiltered: totalQuestionsUnfiltered,
            questionTypeStats: questionTypeStats?.map(t => ({ type: t.question_type, count: t.count })),
            unfilteredQuestionTypeStats: unfilteredQuestionTypeStats?.map(t => ({ type: t.question_type, count: t.count }))
        });

        // Get total count of all questions (regardless of filters)
        const [[totalStats]] = await db.query(`
            SELECT COUNT(*) as total_count FROM questions
        `);
        console.log('Total stats:', totalStats);

        // Get the total unfiltered count
        const totalCount = parseInt(totalStats?.total_count) || 0;
        console.log('Total count:', totalCount);

        // Get total number of essays
        let essaysCount = [{ count: 0 }];
        try {
            // Use a simple query without specifying the database name
            [essaysCount] = await db.query('SELECT COUNT(*) as count FROM essays');
            console.log('Essays count result:', essaysCount);
        } catch (error) {
            console.error('Error fetching essays count:', error);
            essaysCount = [{ count: 0 }]; // Ensure we have a default value
        }

        // Get total number of questions linked to essays
        let linkedQuestionsCount = [{ count: 0 }];
        try {
            // Use a simple query without specifying the database name
            [linkedQuestionsCount] = await db.query('SELECT COUNT(*) as count FROM questions WHERE essay_id IS NOT NULL AND is_deleted = 0');
            console.log('Linked questions count result:', linkedQuestionsCount);
        } catch (error) {
            console.error('Error fetching linked questions count:', error);
            linkedQuestionsCount = [{ count: 0 }]; // Ensure we have a default value
        }

        // Get total count of all questions (unfiltered) directly
        let realTotalQuestions = 0;
        try {
            const [realTotalResult] = await db.query('SELECT COUNT(*) as total FROM questions WHERE is_deleted = 0');
            realTotalQuestions = realTotalResult[0].total;
            console.log('Real total questions count:', realTotalQuestions);
        } catch (error) {
            console.error('Error getting real total questions count:', error);
        }

        // Create a stats object with real values
        const statsObject = {
            totalQuestions: totalQuestions || 0,
            totalCount: totalCount || 0,
            questionTypeStats: questionTypeStats || [],
            unfilteredQuestionTypeStats: unfilteredQuestionTypeStats || [],
            questionTypesCount: questionTypesCount?.[0]?.count || 0,
            categoriesCount: categoriesCount?.[0]?.count || 0,
            totalQuestionsUnfiltered: realTotalQuestions || totalQuestionsUnfiltered || 0,
            essaysCount: essaysCount?.[0]?.count || 0,
            linkedQuestionsCount: linkedQuestionsCount?.[0]?.count || 0
        };

        console.log('Essays count:', essaysCount[0].count);
        console.log('Linked questions count:', linkedQuestionsCount[0].count);

        console.log('Stats object before render:', statsObject);
        console.log('Stats object properties:', Object.keys(statsObject));
        console.log('totalQuestionsUnfiltered value:', statsObject.totalQuestionsUnfiltered);

        // Log variables for audit
        try {
            logJavaScriptVariable('statsObject', statsObject, {
                context: 'questions-index-page',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
            logJavaScriptVariable('filters', { filters, params }, {
                context: 'questions-index-page',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
            logJavaScriptVariable('pagination', { page, perPage, offset, totalPages, totalQuestions }, {
                context: 'questions-index-page',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
        } catch (logError) {
            console.error('Error logging variables:', logError);
        }

        // Fetch essays for the add question modal
        const [essays] = await db.query(`
            SELECT
                e.essay_id,
                e.title,
                COUNT(q.question_id) as question_count,
                DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
            FROM essays e
            LEFT JOIN questions q ON e.essay_id = q.essay_id
            GROUP BY e.essay_id
            ORDER BY e.title
        `);

        console.log('Essays for index page:', essays);

        res.render('admin/questions/index', {
            title: 'Question Bank',
            pageTitle: 'Question Bank',
            questions,
            categories,
            exams,
            sections,
            essays: essays || [], // Ensure essays is always defined
            query: req.query,
            pagination: {
                currentPage: page,
                perPage,
                totalPages,
                totalItems: totalQuestions,
                totalItemsUnfiltered: totalQuestionsUnfiltered, // Use our reliable count
                baseUrl: '/admin/questions'
            },
            stats: statsObject
        });
    } catch (error) {
        console.error('Error loading questions:', error);
        next(error);
    }
});

// Add Question Form
router.get('/add', routeVariableLogger('question-add-page'), async (req, res) => {
    let exams = [];
    let categories = [];
    let sections = []; // Initialize sections as empty array
    let essays = []; // Initialize essays as empty array
    let selectedEssayId = req.query.essay_id || null;

    try {
        // Fetch data
        const [examsResult, categoriesResult, sectionsResult, essaysResult] = await Promise.all([
            db.query('SELECT exam_id, exam_name FROM exams'),
            db.query('SELECT category_id, name FROM categories'),
            db.query('SELECT section_id, section_name FROM sections ORDER BY section_name'),
            db.query(`
                SELECT e.essay_id, e.title,
                       COUNT(q.question_id) as question_count,
                       DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
                FROM essays e
                LEFT JOIN questions q ON e.essay_id = q.essay_id
                GROUP BY e.essay_id
                ORDER BY e.title
            `)
        ]);

        // Log the essays result for debugging
        console.log('Essays result:', essaysResult);

        // Assign results to variables
        exams = examsResult[0];
        categories = categoriesResult[0];
        essays = essaysResult[0];

        // Process sections to ensure uniqueness (case-insensitive)
        const uniqueSections = [];
        const seenNames = new Set();

        for (const section of sectionsResult[0]) {
            // Skip sections with empty names
            if (!section.section_name || section.section_name.trim() === '') {
                continue;
            }

            // Normalize the section name: lowercase, trim whitespace, and normalize multiple spaces
            const normalizedName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');

            if (!seenNames.has(normalizedName)) {
                seenNames.add(normalizedName);
                uniqueSections.push(section);
            }
        }

        sections = uniqueSections;

    } catch (error) {
        console.error('Error fetching data for add question:', error);
        // Keep using the initialized empty arrays
    }

    // Debug essays variable
    console.log('Essays before rendering add form:', essays);

    res.render('admin/questions/add', {
        title: 'Add Question',
        pageTitle: 'Add Question',
        exams,
        categories,
        sections, // sections will always be defined, even if empty
        essays: essays || [], // Ensure essays is always defined
        selectedEssayId,
        formData: {},
        error: null
    });
});

// Get sections for an exam (AJAX)
router.get('/get-sections/:examId', routeVariableLogger('question-get-sections'), async (req, res) => {
    try {
        const examId = req.params.examId;

        const [sections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ? ORDER BY position',
            [examId]
        );

        res.json(sections);
    } catch (error) {
        console.error('Error fetching sections:', error);
        res.status(500).json({ error: 'Failed to fetch sections' });
    }
});

// Process Question Add
router.post('/add', routeVariableLogger('question-add'), async (req, res) => {
    // Check if it's an AJAX request
    const isAjax = req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1);
    const {
        exam_id, section_id, question_type, question_text,
        option_text, correct_option, true_false_answer,
        solution_text, marks, negative_marks, category_ids,
        essay_id, image_id, solution_image_id,
        min_word_count, max_word_count
    } = req.body;

    // Initialize section_id to a default value if it's empty
    let validSectionId = section_id || null; // Set to null if empty

    // If section_id is not provided, you can either:
    // 1. Set a default section_id based on exam_id
    // 2. Create a new section or handle it as per your business logic

    if (!validSectionId) {
        // Example: Find a default section based on exam_id
        if (exam_id) {
            const [sections] = await db.query('SELECT section_id FROM sections WHERE exam_id = ? LIMIT 1', [exam_id]);
            if (sections.length > 0) {
                validSectionId = sections[0].section_id; // Use the first found section_id
            } else {
                // Optionally create a new section if none exists
                const [result] = await db.query('INSERT INTO sections (exam_id, section_name) VALUES (?, ?)', [exam_id, 'Default Section']);
                validSectionId = result.insertId; // Use the newly created section_id
            }
        }
    }

    // Validate inputs and handle errors
    if (!question_type || !question_text) {
        return res.render('admin/questions/add', {
            title: 'Add Question',
            pageTitle: 'Add Question',
            exams: await db.query('SELECT exam_id, exam_name FROM exams'),
            categories: await db.query('SELECT category_id, name FROM categories'),
            error: 'Please fill in all required fields.', // Pass error message (if any)
            formData: req.body // Pass back the submitted data
        });
    }

    // Start a transaction
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
        // Prepare metadata for essay questions
        let metadata = null;
        if (question_type === 'essay') {
            metadata = JSON.stringify({
                min_word_count: parseInt(min_word_count) || 0,
                max_word_count: parseInt(max_word_count) || 500
            });
        }

        // Insert the question
        const [result] = await connection.query(
            `INSERT INTO questions (
                section_id,
                question_type,
                question_text,
                solution_text,
                marks,
                negative_marks,
                correct_answer,
                essay_id,
                image_id,
                solution_image_id,
                metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                validSectionId,
                question_type,
                question_text,
                solution_text || null,
                marks || 1.00,
                negative_marks || 0.00,
                true_false_answer || null,
                essay_id || null,
                image_id || null,
                solution_image_id || null,
                metadata
            ]
        );

        const questionId = result.insertId;

        // Handle options for multiple choice questions
        if (question_type === 'multiple_choice') {
            const optionTexts = option_text || [];

            // Get correct options as an array
            let correctOptions = [];
            if (req.body['correct_option[]']) {
                // Make sure correct_option is an array
                correctOptions = Array.isArray(req.body['correct_option[]'])
                    ? req.body['correct_option[]'].map(opt => parseInt(opt))
                    : [parseInt(req.body['correct_option[]'])];
            } else if (req.body.correct_option !== undefined) {
                // For backward compatibility with single selection
                const correctOptionIndex = parseInt(correct_option);
                if (!isNaN(correctOptionIndex)) {
                    correctOptions = [correctOptionIndex];
                }
            }

            console.log('Option texts:', optionTexts);
            console.log('Correct options:', correctOptions);

            if (optionTexts.length > 0) {
                // Process each option individually to handle option images
                for (let i = 0; i < optionTexts.length; i++) {
                    const text = optionTexts[i];
                    if (text && text.trim()) {
                        const optionImageId = req.body[`option_image_id_${i}`] || null;
                        const isCorrect = correctOptions.includes(i) ? 1 : 0;
                        console.log(`Option ${i}: text=${text.trim()}, isCorrect=${isCorrect}, imageId=${optionImageId}`);

                        await connection.query(
                            'INSERT INTO options (question_id, option_text, is_correct, position, image_id) VALUES (?, ?, ?, ?, ?)',
                            [questionId, text.trim(), isCorrect, i + 1, optionImageId]
                        );
                    }
                }
            }
        }

        // Add category associations
        if (category_ids && category_ids.length > 0) {
            const values = category_ids.map(catId => [questionId, catId]);
            await connection.query(
                'INSERT INTO question_category_mappings (question_id, category_id) VALUES ?',
                [values]
            );
        }

        await connection.commit();
        console.log('Question added successfully');

        // Handle response based on request type
        if (isAjax) {
            return res.json({ success: true, message: 'Question added successfully', questionId: questionId });
        } else {
            //req.flash('success', 'Question added successfully');
            return res.redirect('/admin/questions');
        }
    } catch (error) {
        await connection.rollback();
        console.error('Error adding question:', error);

        // Handle error response based on request type
        if (isAjax) {
            return res.status(400).json({ success: false, message: error.message });
        } else {
            // Fetch data for re-rendering the form
            const [exams] = await db.query('SELECT exam_id, exam_name FROM exams');
            const [categories] = await db.query('SELECT category_id, name FROM categories');
            const [essays] = await db.query('SELECT essay_id, title FROM essays ORDER BY title');

            return res.render('admin/questions/add', {
                title: 'Add Question',
                pageTitle: 'Add Question',
                exams,
                categories,
                sections: [], // No sections available in error case
                essays,
                selectedEssayId: req.body.essay_id || null,
                error: error.message,
                formData: req.body // Pass back the submitted data
            });
        }
    } finally {
        connection.release();
    }
});

// Get Question Data for Modal
router.get('/:id/data', routeVariableLogger('question-data'), async (req, res) => {
    try {
        const questionId = req.params.id;

        // Get question with all related data including categories and options
        const [questions] = await db.query(
            `SELECT
                q.*,
                e.exam_id,
                e.exam_name,
                s.section_id,
                s.section_name,
                es.essay_id,
                es.title as essay_title,
                qi1.file_path as image_path,
                qi2.file_path as solution_image_path,
                GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids,
                GROUP_CONCAT(DISTINCT c.name) as category_names
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN essays es ON q.essay_id = es.essay_id
            LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
            LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.question_id = ? AND q.is_deleted = 0
            GROUP BY q.question_id`,
            [questionId]
        );

        if (questions.length === 0) {
            return res.status(404).json({ success: false, message: 'Question not found' });
        }

        const question = questions[0];

        // Get options for the question
        const [options] = await db.query(
            'SELECT * FROM options WHERE question_id = ? ORDER BY position',
            [questionId]
        );

        // Add options to the question object
        question.options = options;

        // Parse metadata if it exists
        if (question.metadata && typeof question.metadata === 'string') {
            try {
                question.metadata = JSON.parse(question.metadata);
            } catch (error) {
                console.error('Error parsing question metadata:', error);
                question.metadata = {};
            }
        }

        res.json({ success: true, question });
    } catch (error) {
        console.error('Error fetching question data:', error);
        console.error('Error details:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Error fetching question data: ' + error.message });
    }
});

// Edit Question Form
router.get('/:id/edit', routeVariableLogger('question-edit'), async (req, res, next) => {
    try {
        // Get question with all related data including categories and options
        const [questions] = await db.query(
            `SELECT
                q.*,
                e.exam_id,
                e.exam_name,
                s.section_id,
                s.section_name,
                es.essay_id,
                es.title as essay_title,
                qi1.file_path as image_path,
                qi2.file_path as solution_image_path,
                GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids,
                GROUP_CONCAT(DISTINCT c.name) as category_names
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN essays es ON q.essay_id = es.essay_id
            LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
            LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.question_id = ?
            GROUP BY q.question_id`,
            [req.params.id]
        );

        if (!questions.length) {
            return res.status(404).send('Question not found');
        }

        const question = questions[0];

        // Convert comma-separated category_ids to array
        question.category_ids = question.category_ids ? question.category_ids.split(',').map(Number) : [];
        question.category_names = question.category_names ? question.category_names.split(',') : [];

        // Get options if it's a multiple choice question
        if (question.question_type === 'multiple_choice') {
            const [options] = await db.query(
                `SELECT o.id, o.option_text, o.is_correct, o.position, o.image_id, qi.file_path as image_path
                 FROM options o
                 LEFT JOIN question_images qi ON o.image_id = qi.image_id
                 WHERE o.question_id = ?
                 ORDER BY o.position`,
                [question.question_id]
            );
            question.options = options;

            // Find the correct option index
            const correctOption = options.findIndex(opt => opt.is_correct === 1);
            question.correct_option = correctOption >= 0 ? correctOption : null;
        }

        // For true/false questions, get the correct answer from correct_answer field
        if (question.question_type === 'true_false') {
            question.correct_answer = question.correct_answer || 'false';
        }

        // Get all necessary data for dropdowns
        const [categories] = await db.query('SELECT category_id, name FROM categories ORDER BY name');
        const [exams] = await db.query('SELECT exam_id, exam_name FROM exams ORDER BY exam_name');
        const [sections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ? ORDER BY section_name',
            [question.exam_id]
        );
        const [essays] = await db.query(`
            SELECT e.essay_id, e.title,
                   COUNT(q.question_id) as question_count,
                   DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
            FROM essays e
            LEFT JOIN questions q ON e.essay_id = q.essay_id
            GROUP BY e.essay_id
            ORDER BY e.title
        `);

        // Debug essays variable
        console.log('Essays before rendering edit form:', essays);

        res.render('admin/questions/edit', {
            title: 'Edit Question',
            pageTitle: 'Edit Question',
            question,
            categories,
            exams,
            sections,
            essays: essays || [], // Ensure essays is always defined
            error: null,
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Questions', url: '/admin/questions', active: false },
                { text: 'Edit Question', url: '#', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading edit question form:', error);
        next(error);
    }
});

// Process Question Edit
router.post('/:id/edit', express.urlencoded({ extended: true }), routeVariableLogger('question-update'), async (req, res) => {
    console.log('Updating question with ID:', req.params.id);
    console.log('Request body:', req.body);

    // Add flash message for debugging
    res.locals.messages = req.flash();

    // Validate required fields
    if (!req.body.question_type) {
        req.flash('error', 'Question type is required');
        return res.redirect(`/admin/questions/${req.params.id}/edit`);
    }

    if (!req.body.question_text) {
        req.flash('error', 'Question text is required');
        return res.redirect(`/admin/questions/${req.params.id}/edit`);
    }

    const connection = await db.getConnection();
    try {
        await connection.beginTransaction();
        console.log('Transaction started');

        const questionId = req.params.id;
        const {
            exam_id,
            section_id,
            question_type,
            question_text,
            solution_text,
            marks,
            category_ids,
            true_false_answer,
            essay_id,
            min_word_count,
            max_word_count
        } = req.body;

        console.log('Parsed data:', {
            questionId,
            exam_id,
            section_id,
            question_type,
            marks,
            category_ids,
            true_false_answer,
            essay_id
        });

        // Validate question type
        const validQuestionTypes = ['multiple_choice', 'true_false', 'essay', 'fill_up'];
        if (!validQuestionTypes.includes(question_type)) {
            throw new Error(`Invalid question type: ${question_type}`);
        }

        // Initialize correct_answer variable
        let correct_answer = null;

        // For true/false questions, set the correct answer
        if (question_type === 'true_false') {
            correct_answer = true_false_answer === 'true' ? 'true' : 'false';
        }

        // Handle section_id logic
        let finalSectionId = section_id;
        // If explicitly set to "null" or if exam_id is null/empty, remove section association
        if (section_id === 'null' || exam_id === 'null' || !exam_id) {
            finalSectionId = null;
        } else if (!finalSectionId || finalSectionId === '') {
            // If section not provided but exam is, keep existing section if it belongs to the selected exam
            const [currentSectionResult] = await connection.query(
                `SELECT q.section_id, s.exam_id
                 FROM questions q
                 LEFT JOIN sections s ON q.section_id = s.section_id
                 WHERE q.question_id = ?`,
                [questionId]
            );

            if (currentSectionResult.length > 0) {
                // If current section's exam matches selected exam, keep it
                if (currentSectionResult[0].exam_id == exam_id) {
                    finalSectionId = currentSectionResult[0].section_id;
                } else {
                    // Otherwise, clear the section_id since it belongs to a different exam
                    finalSectionId = null;
                }
            }
        }

        // Prepare metadata for essay questions
        let metadata = null;
        if (question_type === 'essay') {
            metadata = JSON.stringify({
                min_word_count: parseInt(min_word_count) || 0,
                max_word_count: parseInt(max_word_count) || 500
            });
        }

        // Update the question
        const questionUpdateData = {
            finalSectionId,
            question_type,
            question_text: question_text ? question_text.substring(0, 50) + '...' : null,
            solution_text: solution_text ? solution_text.substring(0, 50) + '...' : null,
            marks: marks || 1.00,
            correct_answer,
            questionId,
            image_id: req.body.image_id || null,
            solution_image_id: req.body.solution_image_id || null,
            metadata
        };

        console.log('Updating question with values:', questionUpdateData);
        console.log('Image IDs:', {
            question_image_id: req.body.image_id,
            solution_image_id: req.body.solution_image_id
        });

        // Log variables for audit
        try {
            logJavaScriptVariable('questionUpdateData', questionUpdateData, {
                context: 'question-edit',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
        } catch (logError) {
            console.error('Error logging question update data:', logError);
        }

        const updateQuery = `UPDATE questions SET
            section_id = ?,
            question_type = ?,
            question_text = ?,
            solution_text = ?,
            marks = ?,
            negative_marks = ?,
            correct_answer = ?,
            essay_id = ?,
            image_id = ?,
            solution_image_id = ?,
            metadata = ?,
            updated_at = NOW()
            WHERE question_id = ?`;

        const updateParams = [
            finalSectionId,
            question_type,
            question_text,
            solution_text || null,
            marks || 1.00,
            req.body.negative_marks || 0.00,
            correct_answer,
            essay_id || null,
            req.body.image_id || null,
            req.body.solution_image_id || null,
            metadata,
            questionId
        ];

        try {
            await connection.query(updateQuery, updateParams);
            console.log('Question updated successfully');
        } catch (updateError) {
            console.error('Error updating question:', updateError);
            throw updateError;
        }

        // Handle options for multiple choice questions
        if (question_type === 'multiple_choice') {
            // Delete existing options
            await connection.query('DELETE FROM options WHERE question_id = ?', [questionId]);

            // Insert new options if they exist
            const optionTexts = req.body.option_text || [];

            // Get correct options as an array
            let correctOptions = [];
            if (req.body['correct_option[]']) {
                // Make sure correct_option is an array
                correctOptions = Array.isArray(req.body['correct_option[]'])
                    ? req.body['correct_option[]'].map(opt => parseInt(opt))
                    : [parseInt(req.body['correct_option[]'])];
            }

            console.log('Option texts:', optionTexts);
            console.log('Correct options:', correctOptions);

            if (optionTexts.length > 0) {
                console.log('Processing options with images');

                // Process each option individually to handle option images
                for (let i = 0; i < optionTexts.length; i++) {
                    const text = optionTexts[i];
                    if (text && text.trim()) {
                        const optionImageId = req.body[`option_image_id_${i}`] || null;
                        const isCorrect = correctOptions.includes(i) ? 1 : 0;
                        console.log(`Option ${i}: text=${text.trim()}, isCorrect=${isCorrect}, imageId=${optionImageId}`);

                        await connection.query(
                            'INSERT INTO options (question_id, option_text, is_correct, position, image_id) VALUES (?, ?, ?, ?, ?)',
                            [questionId, text.trim(), isCorrect, i + 1, optionImageId]
                        );
                    }
                }
            }
        } else {
            // Delete any existing options for non-multiple choice questions
            await connection.query('DELETE FROM options WHERE question_id = ?', [questionId]);
        }

        // Update categories
        console.log('Category IDs from request:', category_ids);

        // Remove existing category associations
        await connection.query('DELETE FROM question_category_mappings WHERE question_id = ?', [questionId]);

        // Add new category associations if any categories were selected
        if (category_ids) {
            // Ensure category_ids is an array
            const categoryIdsArray = Array.isArray(category_ids) ? category_ids : [category_ids];
            console.log('Category IDs array:', categoryIdsArray);

            if (categoryIdsArray.length > 0) {
                // Filter out any invalid values and convert to numbers
                const validCategoryIds = categoryIdsArray
                    .filter(id => id && !isNaN(parseInt(id)))
                    .map(id => parseInt(id));

                console.log('Valid category IDs:', validCategoryIds);

                if (validCategoryIds.length > 0) {
                    const values = validCategoryIds.map(catId => [questionId, catId]);
                    await connection.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES ?',
                        [values]
                    );
                }
            }
        }

        // Commit the transaction
        await connection.commit();
        console.log('Transaction committed successfully');

        // Set success message
        req.flash('success', 'Question updated successfully');

        // Redirect with success message
        return res.redirect('/admin/questions');

    } catch (error) {
        // Rollback the transaction
        await connection.rollback();
        console.error('Error updating question - transaction rolled back:', error);

        // Set error message
        req.flash('error', `Failed to update question: ${error.message}`);

        try {
            // Fetch data for re-rendering the form
            const [exams] = await db.query('SELECT exam_id, exam_name FROM exams');
            const [categories] = await db.query('SELECT category_id, name FROM categories');

            // Fetch essays for the dropdown
            const [essays] = await db.query(`
                SELECT e.essay_id, e.title,
                       COUNT(q.question_id) as question_count,
                       DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
                FROM essays e
                LEFT JOIN questions q ON e.essay_id = q.essay_id
                GROUP BY e.essay_id
                ORDER BY e.title
            `);

            // Get question with all related data
            const [questions] = await db.query(
                `SELECT
                    q.*,
                    e.exam_id,
                    e.exam_name,
                    s.section_id,
                    s.section_name,
                    GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
                FROM questions q
                LEFT JOIN sections s ON q.section_id = s.section_id
                LEFT JOIN exams e ON s.exam_id = e.exam_id
                LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
                WHERE q.question_id = ?
                GROUP BY q.question_id`,
                [req.params.id]
            );

            if (!questions || questions.length === 0) {
                console.error('Question not found after error');
                req.flash('error', 'Question not found');
                return res.redirect('/admin/questions');
            }

            const question = questions[0];
            question.category_ids = question.category_ids ? question.category_ids.split(',').map(Number) : [];

            // Get sections for the exam
            const [sections] = await db.query(
                'SELECT section_id, section_name FROM sections WHERE exam_id = ? ORDER BY section_name',
                [question.exam_id]
            );

            // Get options if it's a multiple choice question
            if (question.question_type === 'multiple_choice') {
                const [options] = await db.query(
                    'SELECT id, option_text, is_correct, position FROM options WHERE question_id = ? ORDER BY position',
                    [req.params.id]
                );
                question.options = options;

                // Find the correct option index
                const correctOption = options.findIndex(opt => opt.is_correct === 1);
                question.correct_option = correctOption >= 0 ? correctOption : null;
            }

            // Add flash messages to locals
            res.locals.messages = req.flash();

            // Render the form with error message
            return res.render('admin/questions/edit', {
                title: 'Edit Question',
                pageTitle: 'Edit Question',
                question,
                exams,
                categories,
                sections,
                essays: essays || [], // Include essays for the dropdown
                error: error.message,
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Questions', url: '/admin/questions', active: false },
                    { text: 'Edit Question', url: '#', active: true }
                ]
            });
        } catch (renderError) {
            console.error('Error preparing form data after update failure:', renderError);
            req.flash('error', 'An unexpected error occurred. Please try again.');
            return res.redirect('/admin/questions');
        }
    } finally {
        connection.release();
    }
});

// Delete Question (Soft Delete)
router.post('/:id/delete', async (req, res, next) => {
    try {
        const questionId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Soft delete the question
        await db.query(
            'UPDATE questions SET is_deleted = 1, deleted_at = NOW() WHERE question_id = ?',
            [questionId]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question deleted successfully';
        res.redirect('/admin/questions');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error deleting question:', error);
        next(error);
    }
});

// Archive Question
router.post('/:id/archive', async (req, res, next) => {
    try {
        const questionId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Archive the question
        await db.query(
            'UPDATE questions SET is_archived = 1, archived_at = NOW() WHERE question_id = ?',
            [questionId]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question archived successfully';
        res.redirect('/admin/questions');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error archiving question:', error);
        next(error);
    }
});

// Bulk Delete Questions
router.post('/bulk-delete', async (req, res, next) => {
    try {
        const questionIds = req.body['question_ids[]'];

        // Ensure questionIds is an array
        const ids = Array.isArray(questionIds) ? questionIds : [questionIds];

        if (!ids.length) {
            req.session.flashError = 'No questions selected';
            return res.redirect('/admin/questions');
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Soft delete the questions
        await db.query(
            'UPDATE questions SET is_deleted = 1, deleted_at = NOW() WHERE question_id IN (?)',
            [ids]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = `${ids.length} question(s) deleted successfully`;
        res.redirect('/admin/questions');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error bulk deleting questions:', error);
        next(error);
    }
});

// Bulk Archive Questions
router.post('/bulk-archive', async (req, res, next) => {
    try {
        const questionIds = req.body['question_ids[]'];

        // Ensure questionIds is an array
        const ids = Array.isArray(questionIds) ? questionIds : [questionIds];

        if (!ids.length) {
            req.session.flashError = 'No questions selected';
            return res.redirect('/admin/questions');
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Archive the questions
        await db.query(
            'UPDATE questions SET is_archived = 1, archived_at = NOW() WHERE question_id IN (?)',
            [ids]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = `${ids.length} question(s) archived successfully`;
        res.redirect('/admin/questions');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error bulk archiving questions:', error);
        next(error);
    }
});

// Trash Page (Deleted Questions)
router.get('/trash', async (req, res, next) => {
    try {
        // Get all deleted questions with pagination
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Get deleted questions count
        const [countResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE is_deleted = 1');
        const totalQuestions = countResult[0].count;
        const totalPages = Math.ceil(totalQuestions / perPage);

        // Get deleted questions with all related information
        const [questions] = await db.query(`
            SELECT
                q.*,
                e.exam_id,
                e.exam_name,
                s.section_id,
                s.section_name,
                GROUP_CONCAT(DISTINCT c.name) as category_names,
                GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.is_deleted = 1
            GROUP BY q.question_id
            ORDER BY q.deleted_at DESC
            LIMIT ? OFFSET ?
        `, [perPage, offset]);

        // Process the results to convert comma-separated strings to arrays
        questions.forEach(q => {
            q.category_ids = q.category_ids ? q.category_ids.split(',').map(Number) : [];
        });

        res.render('admin/questions/trash', {
            layout: 'admin',
            title: 'Deleted Questions',
            pageTitle: 'Deleted Questions',
            currentPage: 'questions',
            questions,
            query: req.query,
            pagination: {
                currentPage: page,
                perPage,
                totalPages,
                totalItems: totalQuestions,
                baseUrl: '/admin/questions/trash'
            },
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Question Bank', url: '/admin/questions', active: false },
                { text: 'Deleted Questions', url: '#', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading deleted questions:', error);
        next(error);
    }
});

// Restore Question
router.post('/restore/:id', async (req, res, next) => {
    try {
        const questionId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Restore the question
        await db.query(
            'UPDATE questions SET is_deleted = 0, deleted_at = NULL WHERE question_id = ?',
            [questionId]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question restored successfully';
        res.redirect('/admin/questions/trash');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error restoring question:', error);
        next(error);
    }
});

// Permanently Delete Question
router.post('/permanent-delete/:id', async (req, res, next) => {
    try {
        const questionId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Delete options first (if any)
        await db.query('DELETE FROM options WHERE question_id = ?', [questionId]);

        // Delete category mappings
        await db.query('DELETE FROM question_category_mappings WHERE question_id = ?', [questionId]);

        // Delete question
        await db.query('DELETE FROM questions WHERE question_id = ?', [questionId]);

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question permanently deleted';
        res.redirect('/admin/questions/trash');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error permanently deleting question:', error);
        next(error);
    }
});

// Archived Questions Page
router.get('/archived', async (req, res, next) => {
    try {
        // Get all archived questions with pagination
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Get archived questions count
        const [countResult] = await db.query('SELECT COUNT(*) as count FROM questions WHERE is_archived = 1 AND is_deleted = 0');
        const totalQuestions = countResult[0].count;
        const totalPages = Math.ceil(totalQuestions / perPage);

        // Get archived questions with all related information
        const [questions] = await db.query(`
            SELECT
                q.*,
                e.exam_id,
                e.exam_name,
                s.section_id,
                s.section_name,
                GROUP_CONCAT(DISTINCT c.name) as category_names,
                GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN exams e ON s.exam_id = e.exam_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.is_archived = 1 AND q.is_deleted = 0
            GROUP BY q.question_id
            ORDER BY q.archived_at DESC
            LIMIT ? OFFSET ?
        `, [perPage, offset]);

        // Process the results to convert comma-separated strings to arrays
        questions.forEach(q => {
            q.category_ids = q.category_ids ? q.category_ids.split(',').map(Number) : [];
        });

        res.render('admin/questions/archived', {
            layout: 'admin',
            title: 'Archived Questions',
            pageTitle: 'Archived Questions',
            currentPage: 'questions',
            questions,
            query: req.query,
            pagination: {
                currentPage: page,
                perPage,
                totalPages,
                totalItems: totalQuestions,
                baseUrl: '/admin/questions/archived'
            },
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Question Bank', url: '/admin/questions', active: false },
                { text: 'Archived Questions', url: '#', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading archived questions:', error);
        next(error);
    }
});

// Unarchive Question
router.post('/unarchive/:id', async (req, res, next) => {
    try {
        const questionId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Unarchive the question
        await db.query(
            'UPDATE questions SET is_archived = 0, archived_at = NULL WHERE question_id = ?',
            [questionId]
        );

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question unarchived successfully';
        res.redirect('/admin/questions/archived');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error unarchiving question:', error);
        next(error);
    }
});

// Import Questions Page
router.get('/import', routeVariableLogger('question-import-page'), async (_, res) => {
    try {
        // Get all exams for the dropdown
        const exams = await getExams();

        res.render('admin/questions/import', {
            title: 'Import Questions',
            pageTitle: 'Import Questions',
            error: null,
            success: null,
            exams: exams,
            newExamId: null, // Initialize newExamId as null to prevent undefined errors
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Question Bank', url: '/admin/questions', active: false },
                { text: 'Import Questions', url: '/admin/questions/import', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading import page:', error);
        res.status(500).render('error', {
            message: 'Error loading import page',
            newExamId: null // Ensure newExamId is defined even in error case
        });
    }
});

// Preview Excel Import
router.post('/preview-import', upload.single('file'), routeVariableLogger('question-preview-import'), async (req, res) => {
    const isAjax = req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1);

    try {
        const { import_format, import_method, new_exam_name } = req.body;

        // Only handle Excel files for preview
        if (import_format !== 'excel' || !req.file) {
            throw new Error('Only Excel files can be previewed');
        }

        console.log('Preview: Processing Excel file:', req.file.originalname);
        console.log('Preview: Buffer type:', typeof req.file.buffer);
        console.log('Preview: Buffer length:', req.file.buffer ? req.file.buffer.length || (req.file.buffer.data ? req.file.buffer.data.length : 0) : 0);
        console.log('Preview: File mimetype:', req.file.mimetype);
        console.log('Preview: File size:', req.file.size);

        // Dump the first few bytes of the buffer for debugging
        if (req.file.buffer) {
            const bufferSample = req.file.buffer.slice(0, 50);
            console.log('Preview: Buffer sample (first 50 bytes):', bufferSample.toString('hex'));
        }

        // Parse the Excel file using our enhanced parseExcel function
        console.log('Calling parseExcel with multiple sheets option');
        const excelData = parseExcel(req.file.buffer, true);

        // Log the parsed data
        console.log('Excel data parsed successfully');
        console.log('Number of sections:', excelData.sections.length);
        console.log('Instructions length:', excelData.instructions.length);
        console.log('Section names:', excelData.sections.map(s => s.name).join(', '));

        // Validate that we have at least one section
        if (excelData.sections.length === 0) {
            console.error('Excel validation failed in preview: No sections found');
            throw new Error('Excel file must have at least two sheets: the first sheet for instructions and at least one more sheet for questions. All sheets after the first one will be processed as question sections.');
        }

        if (!excelData.sections || !Array.isArray(excelData.sections)) {
            throw new Error('No valid sections found in Excel file. Make sure each sheet (except the first) contains questions in the correct format.');
        }

        if (excelData.sections.length === 0) {
            throw new Error(`No sections with questions found in Excel file. Make sure the first sheet contains instructions and other sheets contain questions in the correct format. All sheets after the first one will be processed as question sections.`);
        }

        // Prepare preview data
        const previewData = {
            fileName: req.file.originalname,
            fileSize: (req.file.size / 1024).toFixed(2) + ' KB',
            examName: new_exam_name || req.file.originalname.replace(/\.[^/.]+$/, ""),
            instructions: excelData.instructions,
            totalSections: excelData.sections.length,
            totalQuestions: excelData.sections.reduce((total, section) => total + section.questions.length, 0),
            sections: excelData.sections.map(section => ({
                name: section.name,
                introduction: section.introduction ? section.introduction.substring(0, 100) + '...' : '',
                questionCount: section.questions.length
            }))
        };

        // Store the file buffer in session for later use
        console.log('Storing Excel buffer in session');
        req.session.excelBuffer = req.file.buffer;
        console.log('Session buffer type after storage:', typeof req.session.excelBuffer);
        console.log('Session buffer length after storage:', req.session.excelBuffer ? req.session.excelBuffer.length || (req.session.excelBuffer.data ? req.session.excelBuffer.data.length : 0) : 0);

        req.session.importData = {
            import_format,
            import_method,
            new_exam_name: previewData.examName,
            originalFileName: req.file.originalname
        };
        console.log('Import data stored in session:', req.session.importData);

        if (isAjax) {
            return res.json({
                success: true,
                previewData
            });
        } else {
            return res.render('admin/questions/import_preview', {
                title: 'Preview Import',
                pageTitle: 'Preview Import',
                previewData,
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Question Bank', url: '/admin/questions', active: false },
                    { text: 'Import Questions', url: '/admin/questions/import', active: false },
                    { text: 'Preview Import', url: '#', active: true }
                ]
            });
        }
    } catch (error) {
        console.error('Error previewing import:', error);

        if (isAjax) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        } else {
            req.session.flashError = error.message;
            return res.redirect('/admin/questions/import');
        }
    }
});

// Import Questions Process
router.post('/import', upload.single('file'), routeVariableLogger('question-import'), async (req, res) => {
    // Check if this is a confirmation from the preview page
    if (req.body.confirm_import === 'true' && req.session.excelBuffer) {
        console.log('Using stored Excel buffer from session');
        console.log('Buffer type:', typeof req.session.excelBuffer);
        console.log('Buffer length:', req.session.excelBuffer ? req.session.excelBuffer.length || (req.session.excelBuffer.data ? req.session.excelBuffer.data.length : 0) : 0);

        // Use the stored file buffer and import data from the session
        if (!req.file) {
            console.log('Creating req.file from session data');
            req.file = {
                buffer: req.session.excelBuffer,
                originalname: req.session.importData.originalFileName
            };
            console.log('Created req.file:', req.file.originalname);
        }

        // Use the stored import data
        if (req.session.importData) {
            req.body.import_format = req.session.importData.import_format;
            req.body.import_method = req.session.importData.import_method;
            req.body.new_exam_name = req.session.importData.new_exam_name;
        }

        // Clear the session data after using it
        delete req.session.excelBuffer;
        delete req.session.importData;
    }
    // Check if it's an AJAX request
    const isAjax = req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1);
    try {
        const { import_format, import_method, exam_id, section_id, new_exam_name } = req.body;
        let questionsData = [];
        let importedCount = 0;
        let createdSections = 0;
        let successMessage = '';
        // Initialize newExamId at the top level of the function to ensure it's in scope for the entire function
        let newExamId = null;

        // Log import request data for audit
        const importRequestData = {
            import_format,
            import_method,
            exam_id,
            section_id,
            new_exam_name,
            file: req.file ? req.file.originalname : 'No file uploaded'
        };

        console.log('Import request:', importRequestData);

        // Log variables for audit
        try {
            logJavaScriptVariable('importRequestData', importRequestData, {
                context: 'question-import',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
        } catch (logError) {
            console.error('Error logging import request data:', logError);
        }

        // Validate input based on import method
        if (import_method === 'existing' && (!exam_id || !section_id)) {
            return res.render('admin/questions/import', {
                title: 'Import Questions',
                pageTitle: 'Import Questions',
                error: 'Exam and section are required when importing to existing exam',
                success: null,
                exams: await getExams(),
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Question Bank', url: '/admin/questions', active: false },
                    { text: 'Import Questions', url: '/admin/questions/import', active: true }
                ]
            });
        }

        if (import_method === 'new' && !new_exam_name) {
            return res.render('admin/questions/import', {
                title: 'Import Questions',
                pageTitle: 'Import Questions',
                error: 'Exam name is required when creating a new exam',
                success: null,
                exams: await getExams(),
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Question Bank', url: '/admin/questions', active: false },
                    { text: 'Import Questions', url: '/admin/questions/import', active: true }
                ]
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        // Parse data based on import format
        if (import_format === 'csv' && req.file) {
            const csvData = req.file.buffer.toString();
            questionsData = await parseCSV(csvData);

            if (!Array.isArray(questionsData)) {
                throw new Error('Invalid data format - expected an array of questions');
            }

            // Import questions to existing section
            importedCount = await importQuestionsToSection(questionsData, section_id);
            successMessage = `Successfully imported ${importedCount} questions to existing section`;

        } else if (import_format === 'excel' && req.file) {
            console.log('Processing Excel file:', req.file.originalname);
            console.log('Buffer type:', typeof req.file.buffer);
            console.log('Buffer length:', req.file.buffer ? req.file.buffer.length || (req.file.buffer.data ? req.file.buffer.data.length : 0) : 0);

            if (import_method === 'existing') {
                // Parse Excel with multiple sheets to get instructions and questions
                const excelData = parseExcel(req.file.buffer, true);

                // Get instructions from the first sheet
                const instructions = excelData.instructions || '';
                console.log('Instructions from Excel for existing exam:', instructions);

                // Get the exam ID from the section
                const [examResult] = await db.query(
                    'SELECT exam_id FROM sections WHERE section_id = ?',
                    [section_id]
                );

                if (examResult.length === 0) {
                    throw new Error('Section not found');
                }

                const examId = examResult[0].exam_id;

                // If instructions exist, append them to the exam instructions column
                if (instructions && instructions.trim() !== '') {
                    // Get current instructions
                    const [currentExamResult] = await db.query(
                        'SELECT instructions FROM exams WHERE exam_id = ?',
                        [examId]
                    );

                    let currentInstructions = '';
                    if (currentExamResult.length > 0 && currentExamResult[0].instructions) {
                        currentInstructions = currentExamResult[0].instructions;
                    }

                    // Append new instructions
                    const updatedInstructions = currentInstructions
                        ? `${currentInstructions}\n\n${instructions}`
                        : instructions;

                    // Update exam instructions
                    await db.query(
                        'UPDATE exams SET instructions = ? WHERE exam_id = ?',
                        [updatedInstructions, examId]
                    );

                    console.log('Updated exam instructions from Excel');
                }

                // Get all sections for this exam to match by name
                const [existingSections] = await db.query(
                    'SELECT section_id, section_name FROM sections WHERE exam_id = ?',
                    [examId]
                );

                // Create a map of section names to section IDs for easy lookup
                const sectionMap = {};
                existingSections.forEach(section => {
                    sectionMap[section.section_name.toLowerCase()] = section.section_id;
                });

                // Track statistics
                let sectionsMatched = 0;
                let sectionsCreated = 0;
                let totalQuestionsImported = 0;

                // Process each section from the Excel file
                if (excelData.sections && excelData.sections.length > 0) {
                    for (const excelSection of excelData.sections) {
                        const sectionName = excelSection.name;
                        const questions = excelSection.questions;

                        if (!Array.isArray(questions) || questions.length === 0) {
                            console.log(`Skipping section ${sectionName} - no valid questions found`);
                            continue;
                        }

                        let targetSectionId;

                        // Check if this section name matches an existing section
                        if (sectionMap[sectionName.toLowerCase()]) {
                            // Use existing section
                            targetSectionId = sectionMap[sectionName.toLowerCase()];
                            sectionsMatched++;
                            console.log(`Matched section: ${sectionName} (ID: ${targetSectionId})`);
                        } else {
                            // Create a new section in the existing exam
                            const [newSectionResult] = await db.query(
                                'INSERT INTO sections (exam_id, section_name) VALUES (?, ?)',
                                [examId, sectionName]
                            );
                            targetSectionId = newSectionResult.insertId;
                            sectionsCreated++;
                            console.log(`Created new section: ${sectionName} (ID: ${targetSectionId})`);
                        }

                        // Import questions to this section
                        const sectionImportCount = await importQuestionsToSection(questions, targetSectionId);
                        totalQuestionsImported += sectionImportCount;
                        console.log(`Imported ${sectionImportCount} questions to section ${sectionName}`);
                    }

                    // Build success message
                    successMessage = `Successfully imported ${totalQuestionsImported} questions to exam`;
                    if (sectionsMatched > 0) {
                        successMessage += `, updated ${sectionsMatched} existing section(s)`;
                    }
                    if (sectionsCreated > 0) {
                        successMessage += `, created ${sectionsCreated} new section(s)`;
                    }
                    if (instructions && instructions.trim() !== '') {
                        successMessage += ' and updated exam instructions';
                    }

                    importedCount = totalQuestionsImported;
                } else {
                    // Fallback to traditional parsing if no sections found
                    questionsData = parseExcel(req.file.buffer, false);

                    if (!Array.isArray(questionsData)) {
                        throw new Error('Invalid data format - expected an array of questions');
                    }

                    // Import questions to the selected section
                    importedCount = await importQuestionsToSection(questionsData, section_id);
                    successMessage = `Successfully imported ${importedCount} questions to existing section`;

                    if (instructions && instructions.trim() !== '') {
                        successMessage += ' and updated exam instructions';
                    }
                }

            } else if (import_method === 'new') {
                // Create new exam and sections from multiple sheets
                console.log('Calling parseExcel with multiple sheets option for new exam');
                const excelData = parseExcel(req.file.buffer, true);

                // Log the parsed data
                console.log('Excel data parsed successfully for new exam');
                console.log('Number of sections:', excelData.sections.length);
                console.log('Instructions length:', excelData.instructions.length);
                console.log('Section names:', excelData.sections.map(s => s.name).join(', '));

                if (!excelData.sections || !Array.isArray(excelData.sections)) {
                    throw new Error('No valid sections found in Excel file. Make sure each sheet (except the first) contains questions in the correct format.');
                }

                if (excelData.sections.length === 0) {
                    throw new Error(`No sections with questions found in Excel file. Make sure the first sheet contains instructions and other sheets contain questions in the correct format. All sheets after the first one will be processed as question sections.`);
                }

                // Get instructions from the first sheet
                const instructions = excelData.instructions || `Imported on ${new Date().toISOString()}`;
                console.log('Instructions from Excel:', instructions);

                // Create new exam
                console.log('Creating new exam with name:', new_exam_name);
                console.log('Instructions length:', instructions ? instructions.length : 0);
                console.log('User ID:', req.session.userId || 1);

                try {
                    const [examResult] = await db.query(
                        'INSERT INTO exams (exam_name, instructions, created_by) VALUES (?, ?, ?)',
                        [new_exam_name, instructions, req.session.userId || 1]
                    );

                    // Set newExamId for use in the response
                    newExamId = examResult.insertId;
                    console.log('Created new exam with ID:', newExamId);
                    console.log('Exam result:', JSON.stringify(examResult));

                    // Verify the exam was created by querying it back
                    const [verifyExam] = await db.query('SELECT * FROM exams WHERE exam_id = ?', [newExamId]);
                    console.log('Verified exam exists:', verifyExam.length > 0 ? 'Yes' : 'No');
                    if (verifyExam.length > 0) {
                        console.log('Exam details:', JSON.stringify(verifyExam[0]));
                    }
                } catch (dbError) {
                    console.error('Error creating exam in database:', dbError);
                    throw new Error(`Database error creating exam: ${dbError.message}`);
                }

                // Create sections and import questions
                for (const section of excelData.sections) {
                    let newSectionId;
                    try {
                        // Create section with instructions if available
                        const [sectionResult] = await db.query(
                            'INSERT INTO sections (exam_id, section_name, instructions) VALUES (?, ?, ?)',
                            [newExamId, section.name, section.introduction || null]
                        );

                        newSectionId = sectionResult.insertId;
                        createdSections++;
                        console.log(`Created section "${section.name}" with ID ${newSectionId}`);
                        if (section.introduction) {
                            console.log(`Added introduction to section "${section.name}" (${section.introduction.length} chars)`);
                        }

                        // Import questions to this section
                        const sectionImportCount = await importQuestionsToSection(section.questions, newSectionId);
                        importedCount += sectionImportCount;
                        console.log(`Imported ${sectionImportCount} questions to section "${section.name}"`);
                    } catch (sectionError) {
                        console.error('Error creating section:', sectionError);
                        throw new Error(`Error creating section ${section.name}: ${sectionError.message}`);
                    }
                }

                successMessage = `Successfully created exam "${new_exam_name}" with ${createdSections} sections and imported ${importedCount} questions`;
            }
        } else {
            throw new Error('Invalid import format or missing file');
        }

        // Log import results for audit
        const importResults = {
            totalQuestionsImported: importedCount,
            createdSections: createdSections,
            successMessage: successMessage,
            importFormat: import_format,
            importMethod: import_method,
            examId: import_method === 'existing' ? exam_id : (newExamId || null),
            newExamId: newExamId || null, // Always include newExamId explicitly
            fileName: req.file ? req.file.originalname : 'No file uploaded'
        };

        // Log the newExamId for debugging
        console.log('newExamId in importResults:', newExamId);

        console.log('Import results:', importResults);

        // Log variables for audit
        try {
            logJavaScriptVariable('importResults', importResults, {
                context: 'question-import-results',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
        } catch (logError) {
            console.error('Error logging import results:', logError);
        }

        // Commit transaction
        await db.query('COMMIT');

        // Handle response based on request type
        if (isAjax) {
            return res.json({
                success: true,
                message: successMessage,
                count: importedCount,
                newExamId: import_method === 'new' ? newExamId : null
            });
        } else {
            return res.render('admin/questions/import', {
                title: 'Import Questions',
                pageTitle: 'Import Questions',
                error: null,
                success: successMessage,
                exams: await getExams(),
                newExamId: import_method === 'new' ? newExamId : null,
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Question Bank', url: '/admin/questions', active: false },
                    { text: 'Import Questions', url: '/admin/questions/import', active: true }
                ]
            });
        }
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error importing questions:', error);

        // Handle error response based on request type
        if (isAjax) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        } else {
            return res.render('admin/questions/import', {
                title: 'Import Questions',
                pageTitle: 'Import Questions',
                error: error.message,
                success: null,
                exams: await getExams(),
                newExamId: null, // Ensure newExamId is defined even in error case
                breadcrumbs: [
                    { text: 'Dashboard', url: '/admin/dashboard', active: false },
                    { text: 'Question Bank', url: '/admin/questions', active: false },
                    { text: 'Import Questions', url: '/admin/questions/import', active: true }
                ]
            });
        }
    }
});

// Helper function to import questions to a section
async function importQuestionsToSection(questions, sectionId) {
    let importedCount = 0;

    for (const q of questions) {
        // Log each question for debugging
        console.log('Processing question:', JSON.stringify(q));

        // Normalize field names - handle different column naming conventions
        const questionText = q.question_text || q.questionText || q.question || q.text || '';

        // Get the raw question type
        let rawQuestionType = q.question_type || q.questionType || q.type || 'multiple_choice';

        // Normalize question type to match database ENUM values
        let questionType;
        if (rawQuestionType.toLowerCase() === 'mcq' || rawQuestionType.toLowerCase() === 'multiple choice' ||
            rawQuestionType.toLowerCase() === 'multiple-choice' || rawQuestionType.toLowerCase() === 'multiple_choice') {
            questionType = 'multiple_choice';
        } else if (rawQuestionType.toLowerCase() === 'tf' || rawQuestionType.toLowerCase() === 'true/false' ||
                   rawQuestionType.toLowerCase() === 'true_false' || rawQuestionType.toLowerCase() === 'true false') {
            questionType = 'true_false';
        } else if (rawQuestionType.toLowerCase() === 'essay' || rawQuestionType.toLowerCase() === 'long answer') {
            questionType = 'essay';
        } else if (rawQuestionType.toLowerCase() === 'fill_up' || rawQuestionType.toLowerCase() === 'fill-up' ||
                   rawQuestionType.toLowerCase() === 'fillup' || rawQuestionType.toLowerCase() === 'fill in the blank' ||
                   rawQuestionType.toLowerCase() === 'fill-in-the-blank') {
            questionType = 'fill_up';
        } else {
            // Default to multiple_choice if unknown type
            questionType = 'multiple_choice';
            console.log(`Unknown question type '${rawQuestionType}' normalized to 'multiple_choice'`);
        }

        const solutionText = q.solution_text || q.solutionText || q.solution || q.explanation || null;
        const correctAnswer = q.correct_answer || q.correctAnswer || q.answer || null;
        const marks = q.marks || q.mark || 1;

        // Check if the question has the required fields
        if (questionText) {
            // Handle options for multiple_choice
            let options = [];
            if (questionType === 'multiple_choice') {
                // Try different possible option field names
                const optionsField = q.options || q.option || q.choices || q.choice || '';
                if (Array.isArray(optionsField)) {
                    options = optionsField;
                } else if (typeof optionsField === 'string') {
                    options = optionsField.split(',').map(opt => opt.trim());
                } else {
                    // Try to extract options from individual option fields (option1, option2, etc.)
                    for (let i = 1; i <= 10; i++) {
                        const optionKey = `option${i}`;
                        if (q[optionKey] && q[optionKey].trim() !== '') {
                            options.push(q[optionKey]);
                        }
                    }
                }
            }

            // Log the options found
            console.log(`Found ${options.length} options for question`);

            // Log the question data before insertion
            console.log('Inserting question with data:', {
                section_id: sectionId,
                question_type: questionType,
                question_text: questionText.substring(0, 50) + (questionText.length > 50 ? '...' : ''),
                solution_text: solutionText ? solutionText.substring(0, 50) + (solutionText.length > 50 ? '...' : '') : null,
                correct_answer: correctAnswer,
                marks: marks
            });

            // Insert question
            const [questionResult] = await db.query(
                'INSERT INTO questions (section_id, question_type, question_text, solution_text, correct_answer, marks) VALUES (?, ?, ?, ?, ?, ?)',
                [sectionId, questionType, questionText, solutionText, correctAnswer, marks]
            );

            const questionId = questionResult.insertId;

            // Insert options for multiple_choice
            if (questionType === 'multiple_choice' && options.length > 0) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].trim() !== '') {
                        // Determine if this option is correct
                        let isCorrect = 0;

                        // Try different ways to determine the correct answer
                        if (correctAnswer !== null) {
                            // If correctAnswer is a number, check if it matches the index
                            if (!isNaN(parseInt(correctAnswer))) {
                                isCorrect = i === parseInt(correctAnswer) ? 1 : 0;
                            }
                            // If correctAnswer is a string, check if it matches the option text
                            else if (typeof correctAnswer === 'string') {
                                isCorrect = options[i].trim().toLowerCase() === correctAnswer.trim().toLowerCase() ? 1 : 0;
                            }
                        }

                        // Also check if there's a direct is_correct field for this option
                        const optionCorrectKey = `option${i+1}_correct` in q ? `option${i+1}_correct` :
                                               `correct_${i+1}` in q ? `correct_${i+1}` : null;

                        if (optionCorrectKey && q[optionCorrectKey]) {
                            isCorrect = 1;
                        }

                        await db.query(
                            'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                            [questionId, options[i], isCorrect, i]
                        );
                    }
                }
            }

            importedCount++;
        }
    }

    return importedCount;
}

// Helper function to get all exams
async function getExams() {
    const [exams] = await db.query(`
        SELECT exam_id, exam_name
        FROM exams
        WHERE status = 'published' OR status IS NULL
        ORDER BY exam_name
    `);
    return exams;
}

// Helper function to parse CSV data
function parseCSV(csvData) {
    return new Promise((resolve, reject) => {
        csv.parse(csvData, {
            columns: true,
            skip_empty_lines: true,
            trim: true
        }, (err, records) => {
            if (err) reject(err);
            else resolve(records);
        });
    });
}

// Helper function to parse Excel data
function parseExcel(buffer, parseMultipleSheets = false) {
    console.log('parseExcel called with buffer type:', typeof buffer);
    console.log('Buffer length:', buffer ? buffer.length || (buffer.data ? buffer.data.length : 0) : 0);

    try {
        // Check if buffer is valid
        if (!buffer) {
            console.error('Invalid buffer provided to parseExcel: buffer is null or undefined');
            throw new Error('Invalid Excel file: The file appears to be empty or corrupted.');
        }

        // Convert buffer.data to buffer if needed
        if (typeof buffer === 'object' && buffer.data && buffer.data.length > 0) {
            console.log('Converting buffer.data to buffer');
            buffer = Buffer.from(buffer.data);
        }

        // Dump the first few bytes of the buffer for debugging
        const bufferSample = buffer.slice(0, 50);
        console.log('Buffer sample (first 50 bytes):', bufferSample.toString('hex'));

        // Try different read options
        let workbook;
        try {
            console.log('Trying to read Excel file with type: buffer');
            workbook = xlsx.read(buffer, { type: 'buffer' });
        } catch (e) {
            console.error('Error reading Excel file with type buffer:', e.message);
            try {
                console.log('Trying to read Excel file with type: array');
                workbook = xlsx.read(new Uint8Array(buffer), { type: 'array' });
            } catch (e2) {
                console.error('Error reading Excel file with type array:', e2.message);
                throw new Error('Could not read Excel file: ' + e2.message);
            }
        }

        console.log(`Excel file has ${workbook.SheetNames.length} sheets:`, workbook.SheetNames);

        // Check if the workbook has the expected structure
        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
            console.error('Invalid workbook structure: No sheets found');
            throw new Error('Invalid Excel file: No sheets found in the workbook.');
        }

        if (!parseMultipleSheets) {
            // Traditional single sheet parsing
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            return xlsx.utils.sheet_to_json(worksheet);
        } else {
            // Parse multiple sheets - first sheet contains introduction data, other sheets contain questions
            const result = {
                instructions: '',
                sectionIntros: {},  // Store section introductions by section name
                sections: []
            };

            // Process the first sheet (introduction sheet)
            if (workbook.SheetNames.length > 0) {
                const firstSheetName = workbook.SheetNames[0];
                const introSheet = workbook.Sheets[firstSheetName];

                try {
                    // Parse the first sheet as JSON with headers
                    const introData = xlsx.utils.sheet_to_json(introSheet);
                    console.log('First sheet data:', JSON.stringify(introData.slice(0, 2)));

                    // Look for the test introduction in the first row
                    if (introData.length > 0) {
                        const firstRow = introData[0];

                        // Check if the first row contains test introduction
                        if (firstRow.title && firstRow.title.toLowerCase().includes('test introduction')) {
                            // Use the introduction text from the first row
                            result.instructions = firstRow.introduction || '';
                            console.log('Found test introduction:', result.instructions.substring(0, 50) + '...');
                        }

                        // Process subsequent rows for section introductions
                        for (let i = 1; i < introData.length; i++) {
                            const row = introData[i];
                            if (row.title && row.introduction) {
                                // Store section introduction by section name
                                result.sectionIntros[row.title] = row.introduction;
                                console.log(`Found section intro for "${row.title}": ${row.introduction.substring(0, 30)}...`);
                            }
                        }
                    }

                    // If no structured data found, try to extract from cells directly
                    if (!result.instructions) {
                        // Fallback to the old method of extracting from A1
                        const instructionsCell = introSheet['A1'];
                        if (instructionsCell && instructionsCell.v) {
                            result.instructions = instructionsCell.v.toString();
                            console.log('Fallback: Found instructions in cell A1:', result.instructions.substring(0, 50) + '...');
                        }
                    }
                } catch (e) {
                    console.error('Error processing introduction sheet:', e);
                }
            }

            // Parse all sheets (except the first one) for questions
            // Start from the second sheet (index 1) as the first sheet is for introductions
            // If there are 3 or more sheets, process all of them
            for (let i = 1; i < workbook.SheetNames.length; i++) {
                const sheetName = workbook.SheetNames[i];
                const worksheet = workbook.Sheets[sheetName];

                try {
                    // Try to parse the sheet as questions
                    const questions = xlsx.utils.sheet_to_json(worksheet);
                    console.log(`Sheet "${sheetName}" parsed with ${questions.length} rows`);

                    // Check if the sheet has valid question data
                    // More lenient validation - consider any non-empty sheet as potentially valid
                    const hasValidQuestions = questions.length > 0;

                    // Log the first row to help debug
                    if (questions.length > 0) {
                        console.log(`First row of sheet "${sheetName}":`, JSON.stringify(questions[0]));
                    }

                    if (hasValidQuestions) {
                        console.log(`Processing sheet "${sheetName}" with ${questions.length} questions`);

                        // Get section introduction if available
                        const sectionIntro = result.sectionIntros[sheetName] || '';

                        result.sections.push({
                            name: sheetName,
                            introduction: sectionIntro, // Keep this as 'introduction' for backward compatibility
                            instructions: sectionIntro, // Add this for the new column name
                            questions: questions
                        });
                    } else {
                        console.log(`Sheet "${sheetName}" has no valid questions, skipping`);
                    }
                } catch (e) {
                    console.error(`Error processing sheet "${sheetName}":`, e);
                }
            }

            // Log summary of parsed data
            console.log(`Parsed Excel file: ${result.sections.length} sections with questions`);
            console.log(`Test instructions length: ${result.instructions.length} characters`);
            console.log(`Section introductions: ${Object.keys(result.sectionIntros).join(', ')}`);

            return result;
        }
    } catch (error) {
        console.error('Error in parseExcel:', error);
        throw error;
    }
}

// Category Mappings Page
router.get('/category-mappings', async (req, res, next) => {
    try {
        // Get all questions with their text
        const [questions] = await db.query(`
            SELECT question_id, question_text
            FROM questions
            WHERE is_deleted = 0 AND is_archived = 0
            ORDER BY question_id DESC
        `);

        // Get all categories
        const [categories] = await db.query(`
            SELECT * FROM categories ORDER BY name
        `);

        // Get all mappings with question text and category name
        const [mappings] = await db.query(`
            SELECT qcm.mapping_id, qcm.question_id, qcm.category_id,
                   q.question_text, c.name as category_name
            FROM question_category_mappings qcm
            JOIN questions q ON qcm.question_id = q.question_id
            JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.is_deleted = 0 AND q.is_archived = 0
            ORDER BY qcm.question_id DESC, c.name
        `);

        res.render('admin/questions/category_mappings', {
            title: 'Question Category Mappings',
            questions,
            categories,
            mappings
        });
    } catch (error) {
        console.error('Error loading category mappings:', error);
        next(error);
    }
});

// Add Category Mapping
router.post('/category-mappings/add', async (req, res, next) => {
    try {
        const { question_id, category_id } = req.body;

        if (!question_id || !category_id) {
            req.session.flashError = 'Question and category are required';
            return res.redirect('/admin/questions/category-mappings');
        }

        // Check if mapping already exists
        const [existingMappings] = await db.query(
            'SELECT * FROM question_category_mappings WHERE question_id = ? AND category_id = ?',
            [question_id, category_id]
        );

        if (existingMappings.length > 0) {
            req.session.flashError = 'This mapping already exists';
            return res.redirect('/admin/questions/category-mappings');
        }

        // Add the mapping
        await db.query(
            'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
            [question_id, category_id]
        );

        req.session.flashSuccess = 'Category mapping added successfully';
        res.redirect('/admin/questions/category-mappings');
    } catch (error) {
        console.error('Error adding category mapping:', error);
        next(error);
    }
});

// Delete Category Mapping
router.post('/category-mappings/:id/delete', async (req, res, next) => {
    try {
        const mappingId = req.params.id;

        // Delete the mapping
        await db.query('DELETE FROM question_category_mappings WHERE mapping_id = ?', [mappingId]);

        req.session.flashSuccess = 'Category mapping deleted successfully';
        res.redirect('/admin/questions/category-mappings');
    } catch (error) {
        console.error('Error deleting category mapping:', error);
        next(error);
    }
});

// Categories Page
router.get('/categories', async (_, res, next) => {
    try {
        // Get all categories with question counts
        const [categories] = await db.query(`
            SELECT c.*, COUNT(qcm.question_id) as question_count
            FROM categories c
            LEFT JOIN question_category_mappings qcm ON c.category_id = qcm.category_id
            LEFT JOIN questions q ON q.question_id = qcm.question_id
            GROUP BY c.category_id
            ORDER BY c.name
        `);

        res.render('admin/questions/categories', {
            title: 'Question Categories',
            pageTitle: 'Question Categories',
            categories,
            error: null,
            success: null,
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Question Bank', url: '/admin/questions', active: false },
                { text: 'Categories', url: '/admin/questions/categories', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading categories:', error);
        next(error);
    }
});
// Add Category
router.post('/categories/add', async (req, res, next) => {
    try {
        const { name, description } = req.body;

        if (!name) {
            req.session.flashError = 'Category name is required';
            return res.redirect('/admin/questions/categories');
        }

        await db.query(
            'INSERT INTO categories (name, description) VALUES (?, ?)',
            [name, description || null]
        );

        req.session.flashSuccess = 'Category added successfully';
        res.redirect('/admin/questions/categories');
    } catch (error) {
        console.error('Error adding category:', error);
        next(error);
    }
});

// Update Category
router.post('/categories/:id/update', async (req, res, next) => {
    try {
        const categoryId = req.params.id;
        const { name, description } = req.body;

        if (!name) {
            req.session.flashError = 'Category name is required';
            return res.redirect('/admin/questions/categories');
        }

        await db.query(
            'UPDATE categories SET name = ?, description = ? WHERE category_id = ?',
            [name, description || null, categoryId]
        );

        req.session.flashSuccess = 'Category updated successfully';
        res.redirect('/admin/questions/categories');
    } catch (error) {
        console.error('Error updating category:', error);
        next(error);
    }
});

// Delete Category
router.post('/categories/:id/delete', async (req, res, next) => {
    try {
        const categoryId = req.params.id;

        // Start transaction
        await db.query('START TRANSACTION');

        // Update questions to remove category reference
        await db.query('UPDATE questions SET category_id = NULL WHERE category_id = ?', [categoryId]);

        // Delete category
        await db.query('DELETE FROM categories WHERE category_id = ?', [categoryId]);

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Category deleted successfully';
        res.redirect('/admin/questions/categories');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error deleting category:', error);
        next(error);
    }
});

// Save Draft
router.post('/admin/save-draft', routeVariableLogger('question-save-draft'), async (req, res) => {
    try {
        const { section_name, question_type, question_text, solution_text, correct_answer, options, passing_marks } = req.body;

        // Log draft data for audit
        const draftData = {
            section_name,
            question_type,
            question_text: question_text ? question_text.substring(0, 50) + '...' : null,
            solution_text: solution_text ? solution_text.substring(0, 50) + '...' : null,
            correct_answer,
            options: options ? options.length : 0,
            passing_marks
        };

        console.log('Saving draft question:', draftData);

        // Log variables for audit
        try {
            logJavaScriptVariable('draftQuestionData', draftData, {
                context: 'save-draft',
                user: req.session ? { id: req.session.userId, username: req.session.username } : null
            });
        } catch (logError) {
            console.error('Error logging draft question data:', logError);
        }

        // Validate input
        if (!section_name || !question_type || !question_text || passing_marks === undefined) {
            return res.status(400).json({ success: false, message: 'Section, question type, question text, and passing marks are required' });
        }

        // Insert draft question into the database
        const [result] = await db.query(
            'INSERT INTO questions (section_name, question_type, question_text, solution_text, correct_answer, passing_marks, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [section_name, question_type, question_text, solution_text || null, correct_answer || null, passing_marks, 'draft']
        );

        // If it's a multiple choice question, insert options
        if (question_type === 'mcq' && options && Array.isArray(options)) {
            const questionId = result.insertId;
            for (let i = 0; i < options.length; i++) {
                if (options[i].trim() !== '') {
                    const isCorrect = i === parseInt(correct_answer) ? 1 : 0;
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, options[i], isCorrect, i]
                    );
                }
            }
        }

        res.json({ success: true, message: 'Draft saved successfully' });
    } catch (error) {
        console.error('Error saving draft:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Delete question route
router.post('/:id/delete', checkAdmin, async (req, res) => {
    const connection = await db.getConnection();
    try {
        await connection.beginTransaction();

        const questionId = req.params.id;

        // Get question details before deleting (for logging)
        const [questionDetails] = await connection.query(
            'SELECT question_type, question_text, section_id FROM questions WHERE question_id = ?',
            [questionId]
        );

        if (questionDetails.length === 0) {
            await connection.rollback();
            return res.status(404).json({
                success: false,
                message: 'Question not found'
            });
        }

        // Delete options first (foreign key constraint)
        await connection.query('DELETE FROM options WHERE question_id = ?', [questionId]);

        // Delete the question
        await connection.query('DELETE FROM questions WHERE question_id = ?', [questionId]);

        // Commit the transaction
        await connection.commit();

        // Log the deletion
        console.log(`Question deleted: ID ${questionId}, Type: ${questionDetails[0].question_type}`);

        res.json({
            success: true,
            message: 'Question deleted successfully'
        });
    } catch (error) {
        await connection.rollback();
        console.error('Error deleting question:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'An error occurred while deleting the question'
        });
    } finally {
        connection.release();
    }
});

module.exports = router;
