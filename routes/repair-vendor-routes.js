/**
 * Repair Vendor Routes
 */
const express = require('express');
const router = express.Router();
const repairVendorController = require('../controllers/repair-vendor-controller');
const repairHistoryController = require('../controllers/repair-history-controller');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkAdmin);

// Middleware to set layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.navbar = 'admin';
    next();
});

// Repair Vendor Routes
router.get('/vendors', repairVendorController.index);
router.get('/vendors/add', repairVendorController.addVendorForm);
router.post('/vendors/add', repairVendorController.addVendor);
router.get('/vendors/:id/edit', repairVendorController.editVendorForm);
router.post('/vendors/:id/edit', repairVendorController.updateVendor);
router.post('/vendors/:id/delete', repairVendorController.deleteVendor);
router.get('/vendors/:id', repairVendorController.viewVendor);

// Repair History Routes
router.get('/history', repairHistoryController.index);
router.get('/history/send', repairHistoryController.sendItemForm);
router.post('/history/send', repairHistoryController.sendItem);
router.get('/history/receive', repairHistoryController.receiveItemForm);
router.post('/history/receive', repairHistoryController.receiveItem);
router.post('/history/:id/status', repairHistoryController.updateStatus);
router.get('/history/:id', repairHistoryController.viewRepair);

module.exports = router;
