const express = require('express');
const router = express.Router();
const db = require('../config/database');
const reportController = require('../controllers/reportController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Reports Dashboard - Root route (/admin/reports/)
router.get('/', async (req, res, next) => {
    // Get filter parameters
    const { exam_id, user_id, start_date, end_date, status, result_type } = req.query;

    // Pagination parameters
    const page = parseInt(req.query.page) || 1;
    const perPage = parseInt(req.query.perPage) || 10;
    const offset = (page - 1) * perPage;
    try {
        // Get summary statistics
        const [userStats] = await db.query(`
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
                COUNT(CASE WHEN role = 'user' THEN 1 END) as user_count,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
            FROM users
            WHERE is_deleted = 0
        `);

        const [testStats] = await db.query(`
            SELECT
                COUNT(*) as total_tests,
                COUNT(CASE WHEN status = 'published' THEN 1 END) as published_tests,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
            FROM exams
            WHERE is_active = 1
        `);

        const [attemptStats] = await db.query(`
            SELECT
                COUNT(*) as total_attempts,
                AVG(total_score) as average_score,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                COUNT(CASE WHEN status = 'completed' AND total_score >= 60 THEN 1 END) as pass_count,
                COUNT(CASE WHEN status = 'completed' AND total_score < 60 THEN 1 END) as fail_count
            FROM exam_attempts
        `);

        // Get result type counts for quick filters
        const [resultStats] = await db.query(`
            SELECT
                COUNT(CASE WHEN ea.status = 'completed' AND ea.total_score >= 60 THEN 1 END) as pass_count,
                COUNT(CASE WHEN ea.status = 'completed' AND ea.total_score < 60 THEN 1 END) as fail_count,
                COUNT(CASE WHEN ea.status != 'completed' THEN 1 END) as tbd_count
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
        `);

        // Get status counts for quick filters
        const [statusStats] = await db.query(`
            SELECT
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_count,
                COUNT(CASE WHEN status = 'abandoned' THEN 1 END) as abandoned_count
            FROM exam_attempts
        `);

        // Build the query with filters
        let attemptsQuery = `
            SELECT
                ea.attempt_id,
                u.username,
                u.id as user_id,
                e.exam_name,
                e.exam_id,
                e.passing_marks,
                ea.total_score,
                CASE
                    WHEN ea.status != 'completed' THEN 'tbd'
                    WHEN ea.total_score >= e.passing_marks THEN 'pass'
                    ELSE 'fail'
                END as pass_status,
                ea.status,
                ea.attempt_date,
                ea.start_time,
                ea.end_time,
                (SELECT COUNT(*) FROM sections WHERE exam_id = e.exam_id) as section_count,
                (SELECT COUNT(*) FROM user_answers WHERE attempt_id = ea.attempt_id) as attempted_questions,
                (SELECT COUNT(*) FROM questions q1 JOIN sections s2 ON q1.section_id = s2.section_id WHERE s2.exam_id = e.exam_id) as total_questions
            FROM exam_attempts ea
            JOIN users u ON ea.user_id = u.id
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE 1=1
        `;

        const queryParams = [];

        // Add filters if provided
        if (exam_id) {
            attemptsQuery += ` AND e.exam_id = ?`;
            queryParams.push(exam_id);
        }

        if (user_id) {
            attemptsQuery += ` AND u.id = ?`;
            queryParams.push(user_id);
        }

        if (start_date) {
            attemptsQuery += ` AND ea.attempt_date >= ?`;
            queryParams.push(start_date);
        }

        if (end_date) {
            attemptsQuery += ` AND ea.attempt_date <= ?`;
            queryParams.push(end_date);
        }

        if (status) {
            attemptsQuery += ` AND ea.status = ?`;
            queryParams.push(status);
        }

        if (result_type) {
            // Always add status=completed when filtering by result_type
            if (!status) {
                attemptsQuery += ` AND ea.status = ?`;
                queryParams.push('completed');
            }

            if (result_type === 'pass') {
                // Use the pass_status calculation directly in the query
                attemptsQuery += ` AND (ea.total_score >= e.passing_marks)`;
            } else if (result_type === 'fail') {
                // Use the pass_status calculation directly in the query
                attemptsQuery += ` AND (ea.total_score < e.passing_marks)`;
            }
        }

        // Create a simpler count query to get total records
        let countQuery = `
            SELECT COUNT(DISTINCT ea.attempt_id) as total
            FROM exam_attempts ea
            JOIN users u ON ea.user_id = u.id
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE 1=1
        `;

        // Create a copy of the query parameters for the count query
        const countQueryParams = [...queryParams];

        // Add the same filters to the count query
        if (exam_id) {
            countQuery += ` AND e.exam_id = ?`;
        }

        if (user_id) {
            countQuery += ` AND u.id = ?`;
        }

        if (start_date) {
            countQuery += ` AND ea.attempt_date >= ?`;
        }

        if (end_date) {
            countQuery += ` AND ea.attempt_date <= ?`;
        }

        if (status) {
            countQuery += ` AND ea.status = ?`;
        }

        if (result_type) {
            // Always add status=completed when filtering by result_type
            if (!status) {
                countQuery += ` AND ea.status = ?`;
                countQueryParams.push('completed');
            }

            if (result_type === 'pass') {
                // Use the pass_status calculation directly in the query
                countQuery += ` AND (ea.total_score >= e.passing_marks)`;
            } else if (result_type === 'fail') {
                // Use the pass_status calculation directly in the query
                countQuery += ` AND (ea.total_score < e.passing_marks)`;
            }
        }

        // Execute the count query
        const [countResult] = await db.query(countQuery, countQueryParams);
        const totalAttempts = countResult[0].total;
        const totalPages = Math.ceil(totalAttempts / perPage);

        // Complete the query with pagination
        attemptsQuery += `
            GROUP BY ea.attempt_id
            ORDER BY ea.attempt_date DESC
            LIMIT ? OFFSET ?
        `;

        // Execute the query with filters and pagination
        const [recentAttempts] = await db.query(attemptsQuery, [...queryParams, perPage, offset]);

        // Get section-wise attempt information for each attempt
        for (const attempt of recentAttempts) {
            const [sectionStats] = await db.query(`
                SELECT
                    s.section_id,
                    s.section_name,
                    (
                        SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        JOIN questions q2 ON ua.question_id = q2.question_id
                        WHERE ua.attempt_id = ? AND q2.section_id = s.section_id
                    ) as attempted_questions,
                    (
                        SELECT COUNT(DISTINCT q3.question_id)
                        FROM questions q3
                        WHERE q3.section_id = s.section_id
                    ) as total_questions,
                    ROUND((
                        IF(
                            (
                                SELECT COUNT(DISTINCT q3.question_id)
                                FROM questions q3
                                WHERE q3.section_id = s.section_id
                            ) > 0,
                            (
                                SELECT COUNT(DISTINCT ua.question_id)
                                FROM user_answers ua
                                JOIN questions q2 ON ua.question_id = q2.question_id
                                WHERE ua.attempt_id = ? AND q2.section_id = s.section_id
                            ) /
                            (
                                SELECT COUNT(DISTINCT q3.question_id)
                                FROM questions q3
                                WHERE q3.section_id = s.section_id
                            ),
                            0
                        )
                    ) * 100, 2) as attempt_percentage,
                    (
                        SELECT COALESCE(SUM(ua.marks_obtained), 0)
                        FROM user_answers ua
                        JOIN questions q2 ON ua.question_id = q2.question_id
                        WHERE ua.attempt_id = ? AND q2.section_id = s.section_id
                    ) as marks_obtained,
                    (
                        SELECT COALESCE(SUM(q3.marks), 0)
                        FROM questions q3
                        WHERE q3.section_id = s.section_id
                    ) as total_marks,
                    ROUND((
                        IF(
                            (
                                SELECT COALESCE(SUM(q3.marks), 0)
                                FROM questions q3
                                WHERE q3.section_id = s.section_id
                            ) > 0,
                            (
                                SELECT COALESCE(SUM(ua.marks_obtained), 0)
                                FROM user_answers ua
                                JOIN questions q2 ON ua.question_id = q2.question_id
                                WHERE ua.attempt_id = ? AND q2.section_id = s.section_id
                            ) /
                            (
                                SELECT COALESCE(SUM(q3.marks), 0)
                                FROM questions q3
                                WHERE q3.section_id = s.section_id
                            ),
                            0
                        )
                    ) * 100, 2) as marks_percentage
                FROM sections s
                JOIN exams e ON s.exam_id = e.exam_id
                WHERE s.exam_id = ? AND s.section_id IS NOT NULL
                GROUP BY s.section_id, s.section_name
            `, [attempt.attempt_id, attempt.attempt_id, attempt.attempt_id, attempt.attempt_id, attempt.exam_id]);

            attempt.sectionStats = sectionStats;

            // Calculate correct totals from section data
            let totalAttemptedQuestions = 0;
            let totalQuestions = 0;
            let totalMarksObtained = 0;
            let totalPossibleMarks = 0;

            sectionStats.forEach(section => {
                totalAttemptedQuestions += parseInt(section.attempted_questions || 0);
                totalQuestions += parseInt(section.total_questions || 0);
                totalMarksObtained += parseFloat(section.marks_obtained || 0);
                totalPossibleMarks += parseFloat(section.total_marks || 0);
            });

            // Update the attempt with the correct totals
            attempt.attempted_questions = totalAttemptedQuestions;
            attempt.total_questions = totalQuestions;
            attempt.total_marks_obtained = totalMarksObtained;
            attempt.total_possible_marks = totalPossibleMarks;

            // Calculate the correct percentage
            if (totalPossibleMarks > 0) {
                attempt.total_score = Math.round((totalMarksObtained / totalPossibleMarks) * 100);
                attempt.pass_status = attempt.total_score >= attempt.passing_marks ? 'pass' : 'fail';
            }
        }

        // Get all exams for filter dropdown
        const [exams] = await db.query(`
            SELECT exam_id, exam_name FROM exams ORDER BY exam_name
        `);

        // Get all users for filter dropdown
        const [users] = await db.query(`
            SELECT id, username, name FROM users ORDER BY username
        `);

        res.render('admin/reports/index', {
            title: 'Reports Dashboard',
            pageTitle: 'Reports Dashboard',
            userStats: userStats[0],
            testStats: testStats[0],
            attemptStats: attemptStats[0],
            resultStats: resultStats[0],
            statusStats: statusStats[0],
            recentAttempts,
            exams,
            users,
            filters: {
                exam_id: exam_id || '',
                user_id: user_id || '',
                start_date: start_date || '',
                end_date: end_date || '',
                status: status || '',
                result_type: result_type || ''
            },
            pagination: {
                currentPage: page,
                perPage: perPage,
                totalItems: totalAttempts,
                totalPages: totalPages,
                filteredCount: totalAttempts
            },
            query: req.query,
            layout: 'admin',
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Reports', url: '/admin/reports', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading reports dashboard:', error);
        next(error);
    }
});

// API endpoint for dashboard data
router.get('/api/dashboard', async (req, res) => {
    try {
        const [userStats] = await db.query(`
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
                COUNT(CASE WHEN role = 'student' THEN 1 END) as student_count
            FROM users
            WHERE is_deleted = 0
        `);

        const [testStats] = await db.query(`
            SELECT
                COUNT(*) as total_tests,
                COUNT(CASE WHEN status = 'published' THEN 1 END) as published_tests
            FROM exams
            WHERE is_active = 1
        `);

        const [attemptStats] = await db.query(`
            SELECT
                COUNT(*) as total_attempts,
                AVG(total_score) as average_score
            FROM exam_attempts
            WHERE status = 'completed'
        `);

        res.json({
            userStats: userStats[0],
            testStats: testStats[0],
            attemptStats: attemptStats[0]
        });
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Export reports page
router.get('/export', async (req, res) => {
    try {
        // Get users for filter
        const [users] = await db.query(
            `SELECT id, username, email FROM users WHERE is_active = 1 ORDER BY username`
        );

        // Get exams for filter
        const [exams] = await db.query(
            `SELECT exam_id, exam_name FROM exams WHERE is_active = 1 ORDER BY exam_name`
        );

        // Get categories for filter
        const [categories] = await db.query(
            `SELECT category_id, name FROM categories ORDER BY name`
        );

        // Get recent reports
        const [recentReports] = await db.query(
            `SELECT id, name, type, format, file_url, generated_at
             FROM reports
             WHERE user_id = ? AND is_deleted = 0
             ORDER BY generated_at DESC
             LIMIT 10`,
            [req.session.userId]
        );

        res.render('admin/reports/export', {
            title: 'Export Reports',
            users,
            exams,
            categories,
            recentReports,
            currentPage: 'export-reports'
        });
    } catch (error) {
        console.error('Error loading export page:', error);
        req.flash('error', 'Failed to load export page');
        res.redirect('/admin/reports');
    }
});

// Generate report
router.post('/generate', async (req, res) => {
    try {
        const {
            report_type,
            export_format,
            start_date,
            end_date,
            user_id,
            exam_id,
            category_id,
            delivery_options,
            email_address,
            show_report
        } = req.body;

        console.log('Generate report request:', req.body);

        // Validate required fields
        if (!report_type || !export_format) {
            req.flash('error', 'Report type and export format are required');
            return res.redirect('/admin/reports/export');
        }

        // Create reports directory if it doesn't exist
        const fs = require('fs').promises;
        const path = require('path');
        const reportsDir = path.join(__dirname, '..', 'public', 'uploads', 'reports');
        await fs.mkdir(reportsDir, { recursive: true });

        // Generate a simple report based on type and format
        const timestamp = Date.now();
        const reportName = `${report_type}_${timestamp}`;
        let filePath;
        let fileUrl;

        // Prepare filters for query
        const filters = {
            start_date: start_date || null,
            end_date: end_date || null,
            user_id: user_id || null,
            exam_id: exam_id || null,
            category_id: category_id || null
        };

        // Get report data based on type
        let reportData;
        let reportTitle;

        switch (report_type) {
            case 'user_performance':
                reportTitle = 'User Performance Report';
                // Get user performance data
                let userCondition = '';
                let userParams = [];

                if (user_id) {
                    userCondition = 'AND ea.user_id = ?';
                    userParams.push(user_id);
                }

                const [userPerformance] = await db.query(`
                    SELECT
                        u.username,
                        u.email,
                        e.exam_name,
                        ea.total_score,
                        ea.status,
                        ea.attempt_date,
                        CASE
                            WHEN ea.status != 'completed' THEN 'In Progress'
                            WHEN ea.total_score >= e.passing_marks THEN 'Pass'
                            ELSE 'Fail'
                        END as result
                    FROM exam_attempts ea
                    JOIN users u ON ea.user_id = u.id
                    JOIN exams e ON ea.exam_id = e.exam_id
                    WHERE 1=1 ${userCondition}
                    ORDER BY ea.attempt_date DESC
                    LIMIT 100
                `, userParams);

                reportData = userPerformance;
                break;

            case 'test_analytics':
                reportTitle = 'Test Analytics Report';
                // Get test analytics data
                let examCondition = '';
                let examParams = [];

                if (exam_id) {
                    examCondition = 'AND e.exam_id = ?';
                    examParams.push(exam_id);
                }

                const [testAnalytics] = await db.query(`
                    SELECT
                        e.exam_name,
                        e.status,
                        COUNT(DISTINCT ea.attempt_id) as total_attempts,
                        COUNT(DISTINCT ea.user_id) as unique_users,
                        AVG(ea.total_score) as average_score,
                        COUNT(DISTINCT CASE WHEN ea.status = 'completed' AND ea.total_score >= e.passing_marks THEN ea.attempt_id END) as passed_attempts,
                        COUNT(DISTINCT CASE WHEN ea.status = 'completed' THEN ea.attempt_id END) as completed_attempts
                    FROM exams e
                    LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id
                    WHERE e.is_active = 1 ${examCondition}
                    GROUP BY e.exam_id
                    ORDER BY total_attempts DESC
                `, examParams);

                reportData = testAnalytics;
                break;

            default:
                reportTitle = 'General Report';
                // Get general data
                const [generalData] = await db.query(`
                    SELECT
                        (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
                        (SELECT COUNT(*) FROM exams WHERE is_active = 1) as total_exams,
                        (SELECT COUNT(*) FROM exam_attempts) as total_attempts,
                        (SELECT COUNT(*) FROM questions WHERE is_deleted = 0) as total_questions
                `);

                reportData = generalData;
        }

        // Generate the report based on format
        switch (export_format) {
            case 'json':
                filePath = path.join(reportsDir, `${reportName}.json`);
                fileUrl = `/uploads/reports/${reportName}.json`;
                await fs.writeFile(filePath, JSON.stringify({
                    title: reportTitle,
                    generated_at: new Date().toISOString(),
                    filters,
                    data: reportData
                }, null, 2));
                break;

            case 'csv':
                filePath = path.join(reportsDir, `${reportName}.csv`);
                fileUrl = `/uploads/reports/${reportName}.csv`;

                // Create CSV content
                let csvContent = '';

                if (reportData && reportData.length > 0) {
                    // Add headers
                    const headers = Object.keys(reportData[0]);
                    csvContent += headers.join(',') + '\n';

                    // Add data rows
                    reportData.forEach(row => {
                        const values = headers.map(header => {
                            const value = row[header];
                            // Handle values with commas by wrapping in quotes
                            return value === null ? '' :
                                   typeof value === 'string' && value.includes(',') ?
                                   `"${value}"` : value;
                        });
                        csvContent += values.join(',') + '\n';
                    });
                } else {
                    csvContent = 'No data available';
                }

                await fs.writeFile(filePath, csvContent);
                break;

            case 'pdf':
            case 'excel':
            default:
                // For simplicity, we'll just create a text file for now
                filePath = path.join(reportsDir, `${reportName}.txt`);
                fileUrl = `/uploads/reports/${reportName}.txt`;

                let textContent = `${reportTitle}\n`;
                textContent += `Generated at: ${new Date().toLocaleString()}\n\n`;
                textContent += `Filters: ${JSON.stringify(filters, null, 2)}\n\n`;
                textContent += `Data: ${JSON.stringify(reportData, null, 2)}`;

                await fs.writeFile(filePath, textContent);
                break;
        }

        // Save report metadata to database
        const [result] = await db.query(
            `INSERT INTO reports (name, type, format, file_path, file_url, user_id, filters, generated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
            [reportTitle, report_type, export_format, filePath, fileUrl, req.session.userId, JSON.stringify(filters)]
        );

        const reportId = result.insertId;

        // Handle delivery options
        const deliveryOptions = Array.isArray(delivery_options) ? delivery_options : [delivery_options];

        // Check if this is a show report request
        if (show_report === 'true') {
            return res.redirect(`/admin/reports/view/${reportId}`);
        }

        // Download (default)
        if (deliveryOptions.includes('download') || deliveryOptions.length === 0) {
            return res.redirect(`/admin/reports/download/${reportId}`);
        }

        // Redirect back to reports page
        req.flash('success', 'Report generated successfully');
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error generating report:', error);
        req.flash('error', `Failed to generate report: ${error.message}`);
        res.redirect('/admin/reports/export');
    }
});

// Download report
router.get('/download/:id', async (req, res) => {
    try {
        const reportId = req.params.id;
        const fs = require('fs').promises;
        const path = require('path');

        // Get report details from database
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            req.flash('error', 'Report not found');
            return res.redirect('/admin/reports/export');
        }

        const report = reports[0];

        // Check if file exists
        try {
            await fs.access(report.file_path);
        } catch (error) {
            console.error('File not found:', report.file_path);
            req.flash('error', 'Report file not found');
            return res.redirect('/admin/reports/export');
        }

        // Set appropriate content type based on format
        const contentTypes = {
            'pdf': 'application/pdf',
            'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv',
            'json': 'application/json',
            'txt': 'text/plain'
        };

        // Get file extension
        const fileExt = path.extname(report.file_path).substring(1);
        const contentType = contentTypes[report.format] || contentTypes[fileExt] || 'application/octet-stream';

        // Set headers for download
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename="${path.basename(report.file_path)}"`);

        // Stream the file
        const fileStream = require('fs').createReadStream(report.file_path);
        fileStream.pipe(res);

        // Log the download
        await db.query(
            `INSERT INTO logs (user_id, operation, category, details, status, ip_address, request_method, request_uri)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                req.session.userId,
                'download_report',
                'reports',
                JSON.stringify({ report_id: reportId, report_name: report.name }),
                'success',
                req.ip,
                req.method,
                req.originalUrl
            ]
        );
    } catch (error) {
        console.error('Error downloading report:', error);
        req.flash('error', 'Failed to download report: ' + error.message);
        res.redirect('/admin/reports/export');
    }
});

// Email report
router.post('/email/:id', async (req, res) => {
    try {
        const reportId = req.params.id;
        const { email } = req.body;

        // Validate email
        if (!email) {
            req.flash('error', 'Email address is required');
            return res.redirect('/admin/reports/export');
        }

        // For now, just redirect back with a message
        req.flash('success', `Email would be sent to ${email} (feature under development)`);
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error emailing report:', error);
        req.flash('error', 'Failed to email report');
        res.redirect('/admin/reports/export');
    }
});

// Email form
router.get('/email/:id', async (req, res) => {
    res.render('admin/reports/email', {
        title: 'Email Report',
        reportId: req.params.id,
        currentPage: 'export-reports'
    });
});

// Share report
router.get('/share/:id', async (req, res) => {
    try {
        const reportId = req.params.id;

        // For now, just redirect back with a message
        req.flash('info', 'Share feature is under development');
        res.redirect('/admin/reports/export');
    } catch (error) {
        console.error('Error sharing report:', error);
        req.flash('error', 'Failed to share report');
        res.redirect('/admin/reports/export');
    }
});

// View report
router.get('/view/:id', async (req, res) => {
    try {
        const reportId = req.params.id;
        const fs = require('fs').promises;
        const path = require('path');

        // Get report details from database
        const [reports] = await db.query(
            'SELECT * FROM reports WHERE id = ? AND user_id = ? AND is_deleted = 0',
            [reportId, req.session.userId]
        );

        if (reports.length === 0) {
            req.flash('error', 'Report not found');
            return res.redirect('/admin/reports/export');
        }

        const report = reports[0];

        // Check if file exists
        try {
            await fs.access(report.file_path);
        } catch (error) {
            console.error('File not found:', report.file_path);
            req.flash('error', 'Report file not found');
            return res.redirect('/admin/reports/export');
        }

        // Read file content
        const fileContent = await fs.readFile(report.file_path, 'utf8');

        // Handle different formats
        let displayContent;
        let contentType = 'text';

        switch (report.format) {
            case 'json':
                try {
                    // Pretty print JSON
                    const jsonData = JSON.parse(fileContent);
                    displayContent = JSON.stringify(jsonData, null, 4);
                    contentType = 'json';
                } catch (e) {
                    displayContent = fileContent;
                }
                break;

            case 'csv':
                displayContent = fileContent;
                contentType = 'csv';
                break;

            default:
                displayContent = fileContent;
        }

        // Render the view report page
        res.render('admin/reports/view', {
            title: 'View Report',
            report,
            content: displayContent,
            contentType,
            currentPage: 'export-reports'
        });

    } catch (error) {
        console.error('Error viewing report:', error);
        req.flash('error', 'Failed to view report: ' + error.message);
        res.redirect('/admin/reports/export');
    }
});

// Delete report
router.delete('/delete/:id', async (req, res) => {
    try {
        const reportId = req.params.id;

        // For now, just return success
        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting report:', error);
        res.status(500).json({ success: false, message: 'Failed to delete report' });
    }
});

module.exports = router;
