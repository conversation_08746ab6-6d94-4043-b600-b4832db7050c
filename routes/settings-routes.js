const express = require('express');
const router = express.Router();
const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const { checkAdmin } = require('../middleware/auth');

// Apply admin check middleware to all routes
router.use(checkAdmin);

// Middleware to set admin layout and current page
router.use((req, res, next) => {
    res.locals.layout = 'admin';
    res.locals.currentPage = req.path.split('/')[1] || 'settings';
    next();
});

// General Settings
router.get('/', async (req, res, next) => {
    try {
        // Get all settings
        const [settings] = await db.query('SELECT * FROM settings');
        
        // Convert to key-value object
        const settingsObj = {};
        settings.forEach(setting => {
            settingsObj[setting.setting_key] = setting.setting_value;
        });
        
        res.render('admin/settings/index', {
            title: 'General Settings',
            pageTitle: 'General Settings',
            settings: settingsObj,
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Settings', url: '/admin/settings', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading settings:', error);
        next(error);
    }
});

// Update Settings
router.post('/update', async (req, res, next) => {
    try {
        const { 
            site_name, 
            site_description, 
            contact_email,
            items_per_page,
            enable_registration,
            maintenance_mode
        } = req.body;
        
        // Start transaction
        await db.query('START TRANSACTION');
        
        // Update settings
        await updateSetting('site_name', site_name);
        await updateSetting('site_description', site_description);
        await updateSetting('contact_email', contact_email);
        await updateSetting('items_per_page', items_per_page);
        await updateSetting('enable_registration', enable_registration ? '1' : '0');
        await updateSetting('maintenance_mode', maintenance_mode ? '1' : '0');
        
        // Commit transaction
        await db.query('COMMIT');
        
        req.flash('success', 'Settings updated successfully');
        res.redirect('/admin/settings');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error updating settings:', error);
        req.flash('error', 'Failed to update settings');
        res.redirect('/admin/settings');
    }
});

// Helper function to update a setting
async function updateSetting(key, value) {
    // Check if setting exists
    const [existingSetting] = await db.query(
        'SELECT * FROM settings WHERE setting_key = ?',
        [key]
    );
    
    if (existingSetting.length > 0) {
        // Update existing setting
        await db.query(
            'UPDATE settings SET setting_value = ? WHERE setting_key = ?',
            [value, key]
        );
    } else {
        // Insert new setting
        await db.query(
            'INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)',
            [key, value]
        );
    }
}

// Email Templates
router.get('/email', async (req, res, next) => {
    try {
        // Get all email templates
        const [templates] = await db.query('SELECT * FROM email_templates ORDER BY template_name');
        
        res.render('admin/settings/email', {
            title: 'Email Templates',
            pageTitle: 'Email Templates',
            templates,
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Settings', url: '/admin/settings', active: false },
                { text: 'Email Templates', url: '/admin/settings/email', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading email templates:', error);
        next(error);
    }
});

// Edit Email Template
router.get('/email/:id', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        
        // Get template
        const [templates] = await db.query(
            'SELECT * FROM email_templates WHERE id = ?',
            [templateId]
        );
        
        if (templates.length === 0) {
            req.session.flashError = 'Template not found';
            return res.redirect('/admin/settings/email');
        }
        
        res.render('admin/settings/email-edit', {
            title: 'Edit Email Template',
            pageTitle: 'Edit Email Template',
            template: templates[0],
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Settings', url: '/admin/settings', active: false },
                { text: 'Email Templates', url: '/admin/settings/email', active: false },
                { text: 'Edit Template', url: `/admin/settings/email/${templateId}`, active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading email template:', error);
        next(error);
    }
});

// Update Email Template
router.post('/email/:id', async (req, res, next) => {
    try {
        const templateId = req.params.id;
        const { subject, body } = req.body;
        
        if (!subject || !body) {
            req.session.flashError = 'Subject and body are required';
            return res.redirect(`/admin/settings/email/${templateId}`);
        }
        
        await db.query(
            'UPDATE email_templates SET subject = ?, body = ? WHERE id = ?',
            [subject, body, templateId]
        );
        
        req.session.flashSuccess = 'Email template updated successfully';
        res.redirect('/admin/settings/email');
    } catch (error) {
        console.error('Error updating email template:', error);
        next(error);
    }
});

// Security Settings
router.get('/security', async (req, res, next) => {
    try {
        // Get security settings
        const [settings] = await db.query('SELECT * FROM settings WHERE setting_key LIKE "security_%"');
        
        // Convert to key-value object
        const securitySettings = {};
        settings.forEach(setting => {
            securitySettings[setting.setting_key.replace('security_', '')] = setting.setting_value;
        });
        
        res.render('admin/settings/security', {
            title: 'Security Settings',
            pageTitle: 'Security Settings',
            settings: securitySettings,
            breadcrumbs: [
                { text: 'Dashboard', url: '/admin/dashboard', active: false },
                { text: 'Settings', url: '/admin/settings', active: false },
                { text: 'Security', url: '/admin/settings/security', active: true }
            ]
        });
    } catch (error) {
        console.error('Error loading security settings:', error);
        next(error);
    }
});

// Update Security Settings
router.post('/security', async (req, res, next) => {
    try {
        const { 
            password_min_length,
            password_require_uppercase,
            password_require_number,
            password_require_special,
            login_max_attempts,
            login_lockout_duration,
            session_timeout
        } = req.body;
        
        // Start transaction
        await db.query('START TRANSACTION');
        
        // Update settings
        await updateSetting('security_password_min_length', password_min_length);
        await updateSetting('security_password_require_uppercase', password_require_uppercase ? '1' : '0');
        await updateSetting('security_password_require_number', password_require_number ? '1' : '0');
        await updateSetting('security_password_require_special', password_require_special ? '1' : '0');
        await updateSetting('security_login_max_attempts', login_max_attempts);
        await updateSetting('security_login_lockout_duration', login_lockout_duration);
        await updateSetting('security_session_timeout', session_timeout);
        
        // Commit transaction
        await db.query('COMMIT');
        
        req.session.flashSuccess = 'Security settings updated successfully';
        res.redirect('/admin/settings/security');
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error updating security settings:', error);
        next(error);
    }
});

// Backup & Restore
router.get('/backup', (req, res) => {
    // Get list of existing backups
    const backupDir = path.join(__dirname, '../backups');
    let backups = [];
    
    if (fs.existsSync(backupDir)) {
        backups = fs.readdirSync(backupDir)
            .filter(file => file.endsWith('.sql'))
            .map(file => {
                const stats = fs.statSync(path.join(backupDir, file));
                return {
                    name: file,
                    size: (stats.size / (1024 * 1024)).toFixed(2) + ' MB',
                    date: stats.mtime
                };
            })
            .sort((a, b) => b.date - a.date);
    }
    
    res.render('admin/settings/backup', {
        title: 'Backup & Restore',
        pageTitle: 'Backup & Restore',
        backups,
        breadcrumbs: [
            { text: 'Dashboard', url: '/admin/dashboard', active: false },
            { text: 'Settings', url: '/admin/settings', active: false },
            { text: 'Backup & Restore', url: '/admin/settings/backup', active: true }
        ]
    });
});

// Create Backup
router.post('/backup/create', (req, res, next) => {
    try {
        const { exec } = require('child_process');
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupDir = path.join(__dirname, '../backups');
        
        // Create backup directory if it doesn't exist
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }
        
        const backupFile = path.join(backupDir, `backup-${timestamp}.sql`);
        
        // Get database config
        const dbConfig = require('../config/database-config');
        
        // Create backup command
        const command = `mysqldump -h ${dbConfig.host} -u ${dbConfig.user} ${dbConfig.password ? `-p${dbConfig.password}` : ''} ${dbConfig.database} > "${backupFile}"`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error('Error creating backup:', error);
                req.session.flashError = 'Failed to create backup: ' + error.message;
                return res.redirect('/admin/settings/backup');
            }
            
            req.session.flashSuccess = 'Backup created successfully';
            res.redirect('/admin/settings/backup');
        });
    } catch (error) {
        console.error('Error creating backup:', error);
        next(error);
    }
});

// Restore Backup
router.post('/backup/restore/:file', (req, res, next) => {
    try {
        const backupFile = req.params.file;
        const { exec } = require('child_process');
        const backupPath = path.join(__dirname, '../backups', backupFile);
        
        // Check if file exists
        if (!fs.existsSync(backupPath)) {
            req.session.flashError = 'Backup file not found';
            return res.redirect('/admin/settings/backup');
        }
        
        // Get database config
        const dbConfig = require('../config/database-config');
        
        // Restore backup command
        const command = `mysql -h ${dbConfig.host} -u ${dbConfig.user} ${dbConfig.password ? `-p${dbConfig.password}` : ''} ${dbConfig.database} < "${backupPath}"`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error('Error restoring backup:', error);
                req.session.flashError = 'Failed to restore backup: ' + error.message;
                return res.redirect('/admin/settings/backup');
            }
            
            req.session.flashSuccess = 'Backup restored successfully';
            res.redirect('/admin/settings/backup');
        });
    } catch (error) {
        console.error('Error restoring backup:', error);
        next(error);
    }
});

// Delete Backup
router.post('/backup/delete/:file', (req, res, next) => {
    try {
        const backupFile = req.params.file;
        const backupPath = path.join(__dirname, '../backups', backupFile);
        
        // Check if file exists
        if (!fs.existsSync(backupPath)) {
            req.session.flashError = 'Backup file not found';
            return res.redirect('/admin/settings/backup');
        }
        
        // Delete file
        fs.unlinkSync(backupPath);
        
        req.session.flashSuccess = 'Backup deleted successfully';
        res.redirect('/admin/settings/backup');
    } catch (error) {
        console.error('Error deleting backup:', error);
        next(error);
    }
});

module.exports = router; 