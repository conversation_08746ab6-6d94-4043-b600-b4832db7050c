const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const formidable = require('formidable');

// Ensure upload directory exists
const uploadDir = path.join(__dirname, '../public/uploads/simple');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Simple upload test page
router.get('/', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Simple File Upload Test</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
                .form-group { margin-bottom: 15px; }
                label { display: block; margin-bottom: 5px; }
                input[type="file"] { display: block; margin-bottom: 10px; }
                button { background: #4CAF50; color: white; border: none; padding: 10px 15px; cursor: pointer; }
                .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>Simple File Upload Test</h1>
            <p>This page uses formidable instead of multer for file uploads.</p>
            
            <form action="/simple-upload/upload" method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="simple_file">Select a file to upload:</label>
                    <input type="file" id="simple_file" name="simple_file">
                </div>
                <button type="submit">Upload File</button>
            </form>
            
            <div class="result">
                <h2>Result</h2>
                <p>${req.query.message || 'No upload attempted yet'}</p>
                ${req.query.file ? `<p>Uploaded file: ${req.query.file}</p><img src="${req.query.file}" style="max-width: 100%; max-height: 300px;">` : ''}
            </div>
        </body>
        </html>
    `);
});

// Handle file upload with formidable
router.post('/upload', (req, res) => {
    // Create a new formidable form
    const form = new formidable.IncomingForm({
        uploadDir: uploadDir,
        keepExtensions: true,
        maxFileSize: 10 * 1024 * 1024 // 10MB
    });
    
    // Parse the form
    form.parse(req, (err, fields, files) => {
        let message = '';
        let file = '';
        
        if (err) {
            console.error('Formidable error:', err);
            message = `Upload error: ${err.message}`;
        } else if (!files.simple_file) {
            message = 'No file selected or file field name does not match';
        } else {
            const uploadedFile = files.simple_file;
            const fileName = path.basename(uploadedFile.filepath);
            
            message = 'File uploaded successfully!';
            file = `/uploads/simple/${fileName}`;
            
            console.log('File details:', {
                originalFilename: uploadedFile.originalFilename,
                mimetype: uploadedFile.mimetype,
                size: uploadedFile.size,
                filepath: uploadedFile.filepath
            });
        }
        
        // Redirect back to the test page with result
        res.redirect(`/simple-upload?message=${encodeURIComponent(message)}${file ? `&file=${encodeURIComponent(file)}` : ''}`);
    });
});

module.exports = router;
