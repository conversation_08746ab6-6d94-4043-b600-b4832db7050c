const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');
const studentImportController = require('../controllers/student-import-controller');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: (req, file, cb) => {
        console.log('File filter check:', {
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size
        });

        // Accept only CSV and Excel files
        const filetypes = /csv|xlsx|xls/;
        const mimetype = filetypes.test(file.mimetype) ||
                        file.mimetype === 'application/vnd.ms-excel' ||
                        file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                        file.mimetype === 'text/csv';
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            console.log('File accepted by filter');
            return cb(null, true);
        }
        console.log('File rejected by filter');
        cb(new Error('Only CSV and Excel files are allowed'));
    }
});

// Multer error handling middleware
const handleMulterError = (err, req, res, next) => {
    if (err instanceof multer.MulterError) {
        console.error('Multer error:', err);

        let errorMessage;
        if (err.code === 'LIMIT_FILE_SIZE') {
            errorMessage = 'File too large. Maximum size is 10MB.';
        } else {
            errorMessage = `File upload error: ${err.message}`;
        }

        // Check if this is an AJAX request
        const isAjax = req.headers['x-requested-with'] === 'XMLHttpRequest' ||
                       req.headers.accept && req.headers.accept.includes('application/json');

        if (isAjax) {
            return res.status(400).json({
                success: false,
                message: errorMessage
            });
        } else {
            req.session.flashError = errorMessage;
            return res.redirect('/admin/students/import');
        }
    } else if (err) {
        console.error('File upload error:', err);
        const errorMessage = `File upload error: ${err.message}`;

        // Check if this is an AJAX request
        const isAjax = req.headers['x-requested-with'] === 'XMLHttpRequest' ||
                       req.headers.accept && req.headers.accept.includes('application/json');

        if (isAjax) {
            return res.status(400).json({
                success: false,
                message: errorMessage
            });
        } else {
            req.session.flashError = errorMessage;
            return res.redirect('/admin/students/import');
        }
    }
    next();
};

// Student import page
router.get('/import', checkAuthenticated, checkAdmin, (req, res) => {
    res.render('admin/students/import', {
        title: 'Import Students',
        pageTitle: 'Import Students',
        layout: 'admin',
        currentPage: 'students',
        flashSuccess: req.session.flashSuccess,
        flashError: req.session.flashError,
        flashInfo: req.session.flashInfo
    });

    // Clear flash messages after rendering
    delete req.session.flashSuccess;
    delete req.session.flashError;
    delete req.session.flashInfo;
});

// Download sample CSV template
router.get('/import/template', checkAuthenticated, checkAdmin, (req, res) => {
    try {
        const csvTemplate = studentImportController.getSampleCSVTemplate();

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="student_import_template.csv"');
        res.send(csvTemplate);
    } catch (error) {
        console.error('Error generating template:', error);
        req.session.flashError = 'Error generating template file';
        res.redirect('/admin/students/import');
    }
});

// Download sample CSV with test data
router.get('/import/sample', checkAuthenticated, checkAdmin, (req, res) => {
    try {
        const sampleCsv = `sno,student_id,udise_code,name,father_name,mother_name,dob,gender,class,section,session,stream,trade,caste_category_name,bpl,disability,religion_name,medium_name,height,weight,admission_no,admission_date,state_name,district_name,cur_address,village_ward,gram_panchayat,pin_code,roll_no,contact_no,ifsc_code,bank_name,column1,account_holder_code,account_holder,account_holder_name
1,3955049,***********,ANUJ SINGH,CHARANJIT SINGH,RANJIT KAUR,01-Oct-2009,Male,11th,A,2024-25,Science,Non-Medical,SC,No,No,SIKH,English,147,35,4781,28-Oct-2024,Punjab,LUDHIANA,VPO HANS KALAN,HANS KALAN,HANS KALAN,142026,60,**********,SBIN0051220,State Bank of India,'***********,5,Joint With Mother,anuj singh
2,9830253,***********,AVNISH PATEL,ARVIND SINGH,SAVITA PATEL,09-Nov-2008,Male,11th,A,2024-25,Science,Non-Medical,General,No,No,HINDU,English,165,50,4817,30-Oct-2024,Punjab,LUDHIANA,"H. No. 441/1, St. No. 2",Moga Colony,Mundian khurd,141123,62,**********,ORBC0100240,Oriental Bank of Commerce,'**************,1,Self,AVNISH PATEL`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="student_sample_data.csv"');
        res.send(sampleCsv);
    } catch (error) {
        console.error('Error generating sample data:', error);
        req.session.flashError = 'Error generating sample data file';
        res.redirect('/admin/students/import');
    }
});

// Process student import
router.post('/import', checkAuthenticated, checkAdmin, upload.single('file'), handleMulterError, async (req, res) => {
    console.log('=== STUDENT IMPORT REQUEST START ===');
    console.log('Request body:', req.body);
    console.log('File info:', req.file ? {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
    } : 'No file');
    console.log('User info:', res.locals.user ? {
        id: res.locals.user.id,
        username: res.locals.user.username,
        role: res.locals.user.role
    } : 'No user');
    console.log('Session info:', {
        userId: req.session.userId,
        userRole: req.session.userRole
    });
    console.log('res.locals keys:', Object.keys(res.locals || {}));

    // Check if this is an AJAX request
    const isAjax = req.headers['x-requested-with'] === 'XMLHttpRequest' ||
                   req.headers.accept && req.headers.accept.includes('application/json');
    console.log('Is AJAX request:', isAjax);

    try {
        if (!req.file) {
            console.error('No file uploaded in request');
            const errorMessage = 'No file uploaded. Please select a file to import.';

            if (isAjax) {
                return res.status(400).json({
                    success: false,
                    message: errorMessage
                });
            } else {
                req.session.flashError = errorMessage;
                return res.redirect('/admin/students/import');
            }
        }

        // Determine file type
        const fileExtension = path.extname(req.file.originalname).toLowerCase();
        let fileType;

        if (fileExtension === '.csv') {
            fileType = 'csv';
        } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
            fileType = 'excel';
        } else {
            console.error('Invalid file type:', fileExtension);
            const errorMessage = 'Invalid file type. Please upload CSV or Excel files only.';

            if (isAjax) {
                return res.status(400).json({
                    success: false,
                    message: errorMessage
                });
            } else {
                req.session.flashError = errorMessage;
                return res.redirect('/admin/students/import');
            }
        }

        console.log(`Processing ${fileType} file: ${req.file.originalname} (${req.file.size} bytes)`);

        // Get session from form data or use default
        const session = req.body.session || '2024-25'; // Default to current session if not provided
        console.log(`Using session: ${session} (from form: ${req.body.session || 'not provided'})`);

        console.log(`Starting import for session: ${session}`);

        // Import students
        const userId = res.locals.user ? res.locals.user.id : req.session.userId;
        if (!userId) {
            const errorMessage = 'User ID not found. Please log in again.';

            if (isAjax) {
                return res.status(401).json({
                    success: false,
                    message: errorMessage
                });
            } else {
                req.session.flashError = errorMessage;
                return res.redirect('/admin/students/import');
            }
        }

        const results = await studentImportController.importStudents(
            req.file.buffer,
            fileType,
            userId,
            session
        );

        // Set flash messages based on results
        const messages = [];

        if (results.imported > 0) {
            messages.push(`Successfully imported ${results.imported} new students`);
        }

        if (results.updated > 0) {
            messages.push(`Updated ${results.updated} existing students`);
        }

        if (results.usersCreated > 0) {
            messages.push(`Created ${results.usersCreated} user accounts`);
        }

        if (results.skipped > 0) {
            messages.push(`Skipped ${results.skipped} students due to errors`);
        }

        if (messages.length > 0) {
            req.session.flashSuccess = messages.join('. ');
        }

        if (results.errors.length > 0) {
            req.session.importErrors = results.errors;
            req.session.flashInfo = `${results.errors.length} errors occurred during import. Check details below.`;
        }

        // Store complete results for the results page
        req.session.importResults = results;

        console.log('=== IMPORT COMPLETED SUCCESSFULLY ===');
        console.log('Results:', {
            imported: results.imported,
            updated: results.updated,
            skipped: results.skipped,
            usersCreated: results.usersCreated,
            errorCount: results.errors.length
        });

        if (isAjax) {
            return res.json({
                success: true,
                message: 'Import completed successfully',
                results: {
                    imported: results.imported,
                    updated: results.updated,
                    skipped: results.skipped,
                    usersCreated: results.usersCreated,
                    errorCount: results.errors.length
                },
                redirectUrl: '/admin/students/import/results'
            });
        } else {
            res.redirect('/admin/students/import/results');
        }

    } catch (error) {
        console.error('=== IMPORT ERROR ===');
        console.error('Error importing students:', error);
        console.error('Error stack:', error.stack);
        console.error('Request details:', {
            file: req.file ? req.file.originalname : 'No file',
            session: req.body.session,
            userId: res.locals.user ? res.locals.user.id : req.session.userId || 'No user'
        });

        const errorMessage = `Error importing students: ${error.message}`;

        if (isAjax) {
            return res.status(500).json({
                success: false,
                message: errorMessage,
                error: error.message
            });
        } else {
            req.session.flashError = errorMessage;
            res.redirect('/admin/students/import');
        }
    }
});

// Display import results
router.get('/import/results', checkAuthenticated, checkAdmin, (req, res) => {
    const errors = req.session.importErrors || [];
    const results = req.session.importResults || {};

    // Clean up session data
    delete req.session.importErrors;
    delete req.session.importResults;

    res.render('admin/students/import-results', {
        title: 'Import Results',
        pageTitle: 'Student Import Results',
        errors,
        results,
        layout: 'admin',
        currentPage: 'students',
        flashSuccess: req.session.flashSuccess,
        flashError: req.session.flashError,
        flashInfo: req.session.flashInfo
    });
});

// Student management page
router.get('/manage', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const classFilter = req.query.class || '';
        const sectionFilter = req.query.section || '';
        const sessionFilter = req.query.session || '';
        const searchQuery = req.query.search || '';

        // Build WHERE clause
        let whereConditions = ['s.is_active = TRUE'];
        let queryParams = [];

        if (classFilter) {
            whereConditions.push('s.class = ?');
            queryParams.push(classFilter);
        }

        if (sectionFilter) {
            whereConditions.push('s.section = ?');
            queryParams.push(sectionFilter);
        }

        if (sessionFilter) {
            whereConditions.push('s.session = ?');
            queryParams.push(sessionFilter);
        }

        if (searchQuery) {
            whereConditions.push('(s.name LIKE ? OR s.student_id LIKE ? OR s.roll_no LIKE ?)');
            const searchPattern = `%${searchQuery}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students s WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;

        // Get students with pagination
        const [students] = await db.query(
            `SELECT s.*, r.full_name as classroom_name
             FROM students s
             LEFT JOIN rooms r ON s.room_number = r.room_number
             WHERE ${whereClause}
             ORDER BY s.class, s.section, s.roll_no, s.name
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        // Get filter options
        const [classes] = await db.query('SELECT DISTINCT class FROM students WHERE is_active = TRUE ORDER BY class');
        const [sections] = await db.query('SELECT DISTINCT section FROM students WHERE is_active = TRUE AND section IS NOT NULL ORDER BY section');
        const [sessions] = await db.query('SELECT DISTINCT session FROM students WHERE is_active = TRUE ORDER BY session DESC');

        const totalPages = Math.ceil(totalStudents / limit);

        res.render('admin/students/manage', {
            title: 'Manage Students',
            pageTitle: 'Student Management',
            layout: 'admin',
            currentPage: 'students',
            students,
            classes: classes.map(c => c.class),
            sections: sections.map(s => s.section),
            sessions: sessions.map(s => s.session),
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters: {
                class: classFilter,
                section: sectionFilter,
                session: sessionFilter,
                search: searchQuery
            },
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

    } catch (error) {
        console.error('Error loading student management page:', error);
        req.session.flashError = 'Error loading students';
        res.redirect('/admin/dashboard');
    }
});

// Comprehensive student data management page
router.get('/data', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('class = ?');
            queryParams.push(filters.class);
        }

        if (filters.section) {
            whereConditions.push('section = ?');
            queryParams.push(filters.section);
        }

        if (filters.session) {
            whereConditions.push('session = ?');
            queryParams.push(filters.session);
        }

        if (filters.gender) {
            whereConditions.push('gender = ?');
            queryParams.push(filters.gender);
        }

        if (filters.stream) {
            whereConditions.push('stream = ?');
            queryParams.push(filters.stream);
        }

        if (filters.bpl) {
            whereConditions.push('bpl = ?');
            queryParams.push(filters.bpl);
        }

        if (filters.disability) {
            whereConditions.push('disability = ?');
            queryParams.push(filters.disability);
        }

        if (filters.search) {
            whereConditions.push('(name LIKE ? OR student_id LIKE ? OR father_name LIKE ? OR roll_no LIKE ? OR admission_no LIKE ?)');
            const searchPattern = `%${filters.search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;
        const totalPages = Math.ceil(totalStudents / limit);

        // Get students with pagination
        const [students] = await db.query(
            `SELECT * FROM students
             WHERE ${whereClause}
             ORDER BY class, section, roll_no, name
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        // Check if export is requested
        if (req.query.export === 'excel') {
            const XLSX = require('xlsx');

            // Prepare data for export
            const exportData = students.map(student => ({
                'S.No': student.sno || '',
                'Student ID': student.student_id || '',
                'UdiseCode': student.udise_code || '',
                'Name': student.name || '',
                'Father Name': student.father_name || '',
                'Mother Name': student.mother_name || '',
                'DOB': student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                'Gender': student.gender || '',
                'Class': student.class || '',
                'Section': student.section || '',
                'Stream': student.stream || '',
                'Trade': student.trade || '',
                'Caste Category': student.caste_category_name || '',
                'BPL': student.bpl || '',
                'Disability': student.disability || '',
                'Religion': student.religion_name || '',
                'Medium': student.medium_name || '',
                'Height': student.height || '',
                'Weight': student.weight || '',
                'Admission No': student.admission_no || '',
                'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                'State': student.state_name || '',
                'District': student.district_name || '',
                'Address': student.cur_address || '',
                'Village/Ward': student.village_ward || '',
                'Gram Panchayat': student.gram_panchayat || '',
                'Pin Code': student.pin_code || '',
                'Roll No': student.roll_no || '',
                'Contact No': student.contact_no || '',
                'IFSC Code': student.ifsc_code || '',
                'Bank Name': student.bank_name || '',
                'Column1': student.column1 || '',
                'Account Holder Code': student.account_holder_code || '',
                'Account Holder': student.account_holder || '',
                'Account Holder Name': student.account_holder_name || ''
            }));

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Students');

            // Generate buffer
            const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

            // Set headers for download
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="students_export_${new Date().toISOString().split('T')[0]}.xlsx"`);

            return res.send(buffer);
        }

        // Get filter options
        const [classes] = await db.query('SELECT DISTINCT class FROM students WHERE class IS NOT NULL AND class != "" ORDER BY class');
        const [sections] = await db.query('SELECT DISTINCT section FROM students WHERE section IS NOT NULL AND section != "" ORDER BY section');
        const [sessions] = await db.query('SELECT DISTINCT session FROM students WHERE session IS NOT NULL AND session != "" ORDER BY session');
        const [streams] = await db.query('SELECT DISTINCT stream FROM students WHERE stream IS NOT NULL AND stream != "" ORDER BY stream');

        res.render('admin/students/data', {
            title: 'Student Data Management',
            pageTitle: 'Student Data Management',
            layout: 'admin',
            currentPage: 'students',
            students,
            classes: classes.map(c => c.class),
            sections: sections.map(s => s.section),
            sessions: sessions.map(s => s.session),
            streams: streams.map(s => s.stream),
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;
        delete req.session.flashInfo;

    } catch (error) {
        console.error('Error loading student data page:', error);
        req.session.flashError = 'Error loading student data';
        res.redirect('/admin/dashboard');
    }
});

// Simple auth test route
router.get('/auth-test', checkAuthenticated, checkAdmin, (req, res) => {
    res.json({
        message: 'Authentication working',
        user: res.locals.user,
        session: {
            userId: req.session.userId,
            userRole: req.session.userRole
        }
    });
});

// Debug route to test import functionality
router.get('/debug', checkAuthenticated, checkAdmin, (req, res) => {
    res.json({
        message: 'Student import debug endpoint',
        user: res.locals.user ? {
            id: res.locals.user.id,
            username: res.locals.user.username,
            role: res.locals.user.role
        } : null,
        sessionUser: {
            userId: req.session.userId,
            userRole: req.session.userRole
        },
        session: {
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        }
    });
});

// Test page route
router.get('/import-test', checkAuthenticated, checkAdmin, (req, res) => {
    res.render('admin/students/import-test', {
        title: 'Student Import Test',
        layout: false // Use no layout for this test page
    });
});

// Test import route with sample data
router.post('/test-import', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        console.log('=== TEST IMPORT START ===');

        // Create test CSV data
        const testCsvData = `sno,student_id,udise_code,name,father_name,mother_name,dob,gender,class,section,session,stream,trade,caste_category_name,bpl,disability,religion_name,medium_name,height,weight,admission_no,admission_date,state_name,district_name,cur_address,village_ward,gram_panchayat,pin_code,roll_no,contact_no,ifsc_code,bank_name,column1,account_holder_code,account_holder,account_holder_name
1,TEST001,UD99999,Test Student,Test Father,Test Mother,2008-01-01,Male,10,A,2024-25,Science,Computer Science,General,No,No,Hindu,English,165.5,55.2,ADM999,2023-04-01,Punjab,Ludhiana,Test Address,Test Ward,Test Panchayat,141001,R999,**********,SBIN0009999,Test Bank,Test Info,ACC999,Test Student,Test Student`;

        const testBuffer = Buffer.from(testCsvData);

        // Import students
        const userId = res.locals.user ? res.locals.user.id : req.session.userId || 1;
        const results = await studentImportController.importStudents(
            testBuffer,
            'csv',
            userId,
            '2024-25'
        );

        console.log('Test import results:', results);

        res.json({
            success: true,
            message: 'Test import completed',
            results: results
        });

    } catch (error) {
        console.error('Test import error:', error);
        res.status(500).json({
            success: false,
            message: 'Test import failed',
            error: error.message
        });
    }
});

// Get single student data
router.get('/:id', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ?',
            [studentId]
        );

        if (students.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        res.json({
            success: true,
            student: students[0]
        });

    } catch (error) {
        console.error('Error fetching student:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data'
        });
    }
});

// Update single student
router.put('/:id', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;
        const updateData = req.body;

        // Remove empty values
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === null) {
                delete updateData[key];
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No data to update'
            });
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);
        updateValues.push(studentId);

        await db.query(
            `UPDATE students SET ${updateFields}, updated_at = NOW() WHERE id = ? AND is_active = 1`,
            updateValues
        );

        res.json({
            success: true,
            message: 'Student updated successfully'
        });

    } catch (error) {
        console.error('Error updating student:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating student'
        });
    }
});

// Bulk delete students (soft delete)
router.post('/bulk-delete', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No students selected for deletion';
            return res.redirect('/admin/students/data');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student IDs';
            return res.redirect('/admin/students/data');
        }

        const placeholders = idsArray.map(() => '?').join(',');
        await db.query(
            `UPDATE students SET is_active = 0, updated_at = NOW() WHERE id IN (${placeholders})`,
            idsArray
        );

        req.session.flashSuccess = `Successfully deleted ${idsArray.length} students`;
        res.redirect('/admin/students/data');

    } catch (error) {
        console.error('Error bulk deleting students:', error);
        req.session.flashError = 'Error deleting students';
        res.redirect('/admin/students/data');
    }
});

// Bulk edit students
router.post('/bulk-edit', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds, ...updateData } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No students selected for editing';
            return res.redirect('/admin/students/data');
        }

        // Remove empty values
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === null) {
                delete updateData[key];
            }
        });

        if (Object.keys(updateData).length === 0) {
            req.session.flashError = 'No fields to update';
            return res.redirect('/admin/students/data');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student IDs';
            return res.redirect('/admin/students/data');
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);
        const placeholders = idsArray.map(() => '?').join(',');

        await db.query(
            `UPDATE students SET ${updateFields}, updated_at = NOW() WHERE id IN (${placeholders}) AND is_active = 1`,
            [...updateValues, ...idsArray]
        );

        req.session.flashSuccess = `Successfully updated ${idsArray.length} students`;
        res.redirect('/admin/students/data');

    } catch (error) {
        console.error('Error bulk editing students:', error);
        req.session.flashError = 'Error updating students';
        res.redirect('/admin/students/data');
    }
});

// Single student delete
router.post('/delete', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No student selected for deletion';
            return res.redirect('/admin/students/data');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student ID';
            return res.redirect('/admin/students/data');
        }

        const placeholders = idsArray.map(() => '?').join(',');
        await db.query(
            `UPDATE students SET is_active = 0, updated_at = NOW() WHERE id IN (${placeholders})`,
            idsArray
        );

        req.session.flashSuccess = `Successfully deleted ${idsArray.length} student(s)`;
        res.redirect('/admin/students/data');

    } catch (error) {
        console.error('Error deleting student:', error);
        req.session.flashError = 'Error deleting student';
        res.redirect('/admin/students/data');
    }
});

// Get trash data for modal (must come before /:id route)
router.get('/trash-data', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');

        // Get soft deleted students
        const [students] = await db.query(
            `SELECT
                id, student_id, name, class, section, gender, contact_no, updated_at as deleted_at
             FROM students
             WHERE is_active = 0
             ORDER BY updated_at DESC
             LIMIT 50`
        );

        res.json({
            success: true,
            students: students
        });

    } catch (error) {
        console.error('Error fetching trash data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching trash data'
        });
    }
});

// Trash view - show soft deleted students
router.get('/trash', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const filters = {
            search: req.query.search || ''
        };

        // Build WHERE clause for soft deleted students
        let whereConditions = ['is_active = 0'];
        let queryParams = [];

        if (filters.search) {
            whereConditions.push('(name LIKE ? OR student_id LIKE ? OR father_name LIKE ?)');
            const searchPattern = `%${filters.search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;
        const totalPages = Math.ceil(totalStudents / limit);

        // Get students with pagination
        const [students] = await db.query(
            `SELECT * FROM students
             WHERE ${whereClause}
             ORDER BY updated_at DESC
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        res.render('admin/students/trash', {
            title: 'Student Trash',
            pageTitle: 'Deleted Students',
            layout: 'admin',
            currentPage: 'students',
            students,
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;
        delete req.session.flashInfo;

    } catch (error) {
        console.error('Error loading trash page:', error);
        req.session.flashError = 'Error loading deleted students';
        res.redirect('/admin/students/data');
    }
});

// Restore students from trash
router.post('/restore', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No students selected for restoration';
            return res.redirect('/admin/students/trash');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student IDs';
            return res.redirect('/admin/students/trash');
        }

        const placeholders = idsArray.map(() => '?').join(',');
        await db.query(
            `UPDATE students SET is_active = 1, updated_at = NOW() WHERE id IN (${placeholders})`,
            idsArray
        );

        req.session.flashSuccess = `Successfully restored ${idsArray.length} student(s)`;
        res.redirect('/admin/students/trash');

    } catch (error) {
        console.error('Error restoring students:', error);
        req.session.flashError = 'Error restoring students';
        res.redirect('/admin/students/trash');
    }
});

// Bulk restore students from trash
router.post('/bulk-restore', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No students selected for restoration';
            return res.redirect('/admin/students/trash');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student IDs';
            return res.redirect('/admin/students/trash');
        }

        const placeholders = idsArray.map(() => '?').join(',');
        await db.query(
            `UPDATE students SET is_active = 1, updated_at = NOW() WHERE id IN (${placeholders})`,
            idsArray
        );

        req.session.flashSuccess = `Successfully restored ${idsArray.length} students`;
        res.redirect('/admin/students/trash');

    } catch (error) {
        console.error('Error bulk restoring students:', error);
        req.session.flashError = 'Error restoring students';
        res.redirect('/admin/students/trash');
    }
});

// Permanently delete students
router.post('/permanent-delete', checkAuthenticated, checkAdmin, async (req, res) => {
    try {
        const db = require('../config/database');
        const { studentIds } = req.body;

        if (!studentIds || studentIds.trim() === '') {
            req.session.flashError = 'No students selected for permanent deletion';
            return res.redirect('/admin/students/trash');
        }

        const idsArray = studentIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

        if (idsArray.length === 0) {
            req.session.flashError = 'Invalid student IDs';
            return res.redirect('/admin/students/trash');
        }

        const placeholders = idsArray.map(() => '?').join(',');
        await db.query(
            `DELETE FROM students WHERE id IN (${placeholders}) AND is_active = 0`,
            idsArray
        );

        req.session.flashSuccess = `Successfully permanently deleted ${idsArray.length} student(s)`;
        res.redirect('/admin/students/trash');

    } catch (error) {
        console.error('Error permanently deleting students:', error);
        req.session.flashError = 'Error permanently deleting students';
        res.redirect('/admin/students/trash');
    }
});

module.exports = router;
