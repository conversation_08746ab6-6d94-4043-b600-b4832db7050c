const express = require('express');
const router = express.Router();
const { checkAuthenticated } = require('../middleware/auth');
const studentInstructionPlanController = require('../controllers/student-instruction-plan-controller');
const studentAssignmentsController = require('../controllers/student-assignments-controller');

// Middleware to check if user is a student
const checkStudent = (req, res, next) => {
  if (req.session.userRole === 'student') {
    return next();
  }
  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access this resource.',
    error: { status: 403 },
    layout: 'auth'
  });
};

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkStudent);

// Import student controller
const studentController = require('../controllers/student-controller');

// Student dashboard
router.get('/dashboard', studentController.getDashboard);

// Instruction plans routes
router.get('/instruction-plans', studentInstructionPlanController.getInstructionPlans);
router.get('/instruction-plans/:id', studentInstructionPlanController.getInstructionPlanDetails);
router.post('/instruction-plans/:id/complete', studentInstructionPlanController.markPlanCompleted);

// Activity history
router.get('/activity', studentController.getActivity);

// Practicals routes
const practicalsRoutes = require('./student/practicals');
router.use('/practicals', practicalsRoutes);

// Calendar route
router.get('/calendar', async (req, res) => {
  try {
    const studentId = req.session.userId || req.session.user.id;
    const db = require('../config/database');
    const { formatDate, formatDateTime } = require('../utils/date-formatter');

    // Get student's classes for practicals
    const [studentClasses] = await db.query(`
      SELECT c.id
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);

    // Handle case where studentClasses might be empty
    const classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];

    // Get all practicals for the student's classes
    let practicals = [];
    if (classIds.length > 0) {
      try {
        // Check if practicals table exists
        const [tables] = await db.query(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'exam_prep_platform'
          AND TABLE_NAME = 'practicals'
        `);

        if (tables.length > 0) {
          const query = `
            SELECT p.*,
                  (SELECT COUNT(*) FROM practical_records
                    WHERE practical_id = p.id AND student_id = ?) as has_submission,
                  (SELECT status FROM practical_records
                    WHERE practical_id = p.id AND student_id = ?) as submission_status,
                  s.name as subject_name
            FROM practicals p
            LEFT JOIN subjects s ON p.subject_id = s.id
            WHERE p.class_id IN (?)
            ORDER BY p.date ASC, p.start_time ASC
          `;

          [practicals] = await db.query(query, [studentId, studentId, classIds]);
        }
      } catch (err) {
        console.log('Practicals table may not exist yet:', err.message);
        // Continue without practicals data
      }
    }

    // Get test assignments for this student
    let testAssignments = [];
    try {
      // Check if test_assignments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'test_assignments'
      `);

      if (tables.length > 0) {
        [testAssignments] = await db.query(`
          SELECT ta.*, e.exam_name, e.duration,
                (SELECT COUNT(*) FROM exam_attempts
                  WHERE exam_id = e.exam_id AND user_id = ?) as attempts_used
          FROM test_assignments ta
          JOIN exams e ON ta.exam_id = e.exam_id
          WHERE (ta.user_id = ? OR
                ta.group_id IN (SELECT group_id FROM group_members WHERE user_id = ?))
          AND ta.is_active = 1
          AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
        `, [studentId, studentId, studentId]);
      }
    } catch (err) {
      console.log('Test assignments table may not exist yet:', err.message);
      // Continue without test assignments data
    }

    // Get assignments for this student
    let assignments = [];
    try {
      // Check if assignments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'assignments'
      `);

      if (tables.length > 0 && classIds.length > 0) {
        // If assignments table exists, fetch assignments
        [assignments] = await db.query(`
          SELECT a.*, s.name as subject_name
          FROM assignments a
          LEFT JOIN subjects s ON a.subject_id = s.id
          WHERE (a.student_id = ? OR a.class_id IN (?))
          AND a.due_date >= NOW()
          ORDER BY a.due_date ASC
        `, [studentId, classIds]);
      }
    } catch (err) {
      console.log('Assignments table may not exist yet:', err.message);
      // Continue without assignments data
    }

    // Get academic plan assessments (another type of assignment)
    let assessments = [];
    try {
      // Check if academic_plan_assessments table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'academic_plan_assessments'
      `);

      if (tables.length > 0 && classIds.length > 0) {
        // If table exists, fetch assessments
        [assessments] = await db.query(`
          SELECT apa.*, ip.title as plan_title, s.name as subject_name
          FROM academic_plan_assessments apa
          JOIN instruction_plans ip ON apa.plan_id = ip.id
          LEFT JOIN subjects s ON ip.subject_id = s.id
          WHERE ip.class_id IN (?)
          AND apa.assessment_date >= CURDATE()
          ORDER BY apa.assessment_date ASC
        `, [classIds]);
      }
    } catch (err) {
      console.log('Academic plan assessments table may not exist yet:', err.message);
      // Continue without assessments data
    }

    // Get learning plans
    let learningPlans = [];
    try {
      // Check if instruction_plans table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'instruction_plans'
      `);

      if (tables.length > 0) {
        // Get student's subjects
        const [studentSubjects] = await db.query(`
          SELECT ss.subject_id
          FROM student_subjects ss
          WHERE ss.student_id = ?
        `, [studentId]);

        // Handle case where studentSubjects might be empty
        const subjectIds = studentSubjects && studentSubjects.length > 0 ? studentSubjects.map(s => s.subject_id) : [];

        if (subjectIds.length > 0 || classIds.length > 0) {
          // Build the query based on available data
          let query = `
            SELECT p.*, s.name as subject_name,
                  u.full_name as teacher_name
            FROM instruction_plans p
            LEFT JOIN subjects s ON p.subject_id = s.id
            LEFT JOIN users u ON p.teacher_id = u.id
            WHERE p.status = 'published'
            AND (
          `;

          const queryParams = [];

          const conditions = [];

          if (subjectIds.length > 0) {
            conditions.push(`p.subject_id IN (?)`);
            queryParams.push(subjectIds);
          }

          if (classIds.length > 0) {
            conditions.push(`p.class_id IN (?)`);
            queryParams.push(classIds);
          }

          query += conditions.join(' OR ');
          query += `) ORDER BY p.created_at DESC`;

          [learningPlans] = await db.query(query, queryParams);
        }
      }
    } catch (err) {
      console.log('Instruction plans table may not exist yet:', err.message);
      // Continue without learning plans data
    }

    // Get holidays
    let holidays = [];
    try {
      // Check if holiday_calendar table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'holiday_calendar'
      `);

      if (tables.length > 0) {
        [holidays] = await db.query(`
          SELECT id, holiday_date, description, holiday_type, is_active
          FROM holiday_calendar
          WHERE is_active = 1
          ORDER BY holiday_date
        `);
      }
    } catch (err) {
      console.log('Holiday calendar table may not exist yet:', err.message);
      // Continue without holidays data
    }

    res.render('student/calendar', {
      title: 'Academic Calendar',
      practicals: practicals || [],
      testAssignments: testAssignments || [],
      assignments: assignments || [],
      assessments: assessments || [],
      learningPlans: learningPlans || [],
      holidays: holidays || [],
      user: res.locals.user,
      layout: 'student',
      currentPage: 'calendar',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching calendar data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch calendar data',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0
    });
  }
});

// Exams routes
router.get('/exams', async (req, res) => {
  try {
    const studentId = req.session.userId || req.session.user.id;

    // Get upcoming exams (not yet completed or with remaining attempts)
    const [upcomingExams] = await db.query(`
      SELECT e.exam_id, e.exam_name, e.category_id as subject, e.duration,
             ta.end_datetime as end_date, ta.max_attempts,
             COUNT(ea.attempt_id) as attempts_used,
             ta.max_attempts - COUNT(ea.attempt_id) as remaining_attempts
      FROM test_assignments ta
      JOIN exams e ON ta.exam_id = e.exam_id
      LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id AND ea.user_id = ?
      WHERE ta.user_id = ?
      AND (ta.end_datetime IS NULL OR ta.end_datetime >= CURDATE())
      GROUP BY e.exam_id, e.exam_name, e.category_id, e.duration, ta.end_datetime, ta.max_attempts
      HAVING COUNT(ea.attempt_id) < ta.max_attempts
      ORDER BY ta.end_datetime ASC
    `, [studentId, studentId]);

    // Get completed exams
    const [completedExams] = await db.query(`
      SELECT e.exam_id, e.exam_name, e.category_id as subject,
             MAX(ea.total_score) as highest_score,
             COUNT(ea.attempt_id) as attempts_used,
             MAX(ea.end_time) as completion_date
      FROM exam_attempts ea
      JOIN exams e ON ea.exam_id = e.exam_id
      WHERE ea.user_id = ? AND ea.status = 'completed'
      GROUP BY e.exam_id, e.exam_name, e.category_id
      ORDER BY MAX(ea.end_time) DESC
    `, [studentId]);

    // Get all assigned exams
    const [allExams] = await db.query(`
      SELECT e.exam_id, e.exam_name, e.category_id as subject, e.duration,
             ta.end_datetime as end_date, ta.max_attempts,
             COUNT(ea.attempt_id) as attempts_used,
             MAX(ea.status = 'completed') as is_completed
      FROM test_assignments ta
      JOIN exams e ON ta.exam_id = e.exam_id
      LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id AND ea.user_id = ?
      WHERE ta.user_id = ?
      GROUP BY e.exam_id, e.exam_name, e.category_id, e.duration, ta.end_datetime, ta.max_attempts
      ORDER BY ta.assigned_at DESC
    `, [studentId, studentId]);

    res.render('student/exams/index', {
      title: 'My Exams',
      layout: 'student',
      currentPage: 'exams',
      user: res.locals.user,
      notificationCount: 0,
      upcomingExams,
      completedExams,
      allExams
    });
  } catch (error) {
    console.error('Error fetching student exams:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Error loading exams',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      user: res.locals.user,
      notificationCount: 0
    });
  }
});

// Assignments routes
router.get('/assignments', studentAssignmentsController.getAssignments);
router.get('/assignments/:id', studentAssignmentsController.getAssignmentDetails);
router.post('/assignments/:id/submit', studentAssignmentsController.submitAssignment);

// Progress routes
router.get('/progress', (req, res) => {
  res.render('student/progress', {
    title: 'My Progress',
    layout: 'student',
    currentPage: 'progress',
    user: res.locals.user,
    notificationCount: 0
  });
});

// Progress API endpoint
router.get('/api/progress', (req, res) => {
  // This is a placeholder API that returns empty data
  // In a real application, this would fetch data from the database
  res.json({
    subjectPerformance: [
      { subject: 'Mathematics', score: 0 },
      { subject: 'Science', score: 0 },
      { subject: 'English', score: 0 },
      { subject: 'Social Studies', score: 0 },
      { subject: 'Computer Science', score: 0 }
    ],
    progressOverTime: [
      { month: 'Jan', score: 0 },
      { month: 'Feb', score: 0 },
      { month: 'Mar', score: 0 },
      { month: 'Apr', score: 0 },
      { month: 'May', score: 0 },
      { month: 'Jun', score: 0 }
    ],
    recentExams: []
  });
});

// Results routes
router.get('/results', (req, res) => {
  res.render('student/results', {
    title: 'My Results',
    layout: 'student',
    currentPage: 'results',
    user: res.locals.user,
    notificationCount: 0
  });
});

// Subjects routes
router.get('/subjects', (req, res) => {
  res.render('student/subjects', {
    title: 'My Subjects',
    layout: 'student',
    currentPage: 'subjects',
    user: res.locals.user,
    notificationCount: 0
  });
});

// Groups routes
router.get('/groups', (req, res) => {
  res.render('student/groups', {
    title: 'My Groups',
    layout: 'student',
    currentPage: 'groups',
    user: res.locals.user,
    notificationCount: 0
  });
});

// Profile routes
router.get('/profile', (req, res) => {
  res.render('student/profile', {
    title: 'My Profile',
    layout: 'student',
    currentPage: 'profile',
    user: res.locals.user,
    notificationCount: 0
  });
});

module.exports = router;
