const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const auth = require('../../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { formatDate, formatDateTime } = require('../../utils/date-formatter');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dir = 'public/uploads/practical_submissions';
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'practical-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: function (req, file, cb) {
    const filetypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only .jpeg, .jpg, .png, .pdf, .doc, .docx files are allowed'));
    }
  }
}).single('attachment');

// Get all practicals for the student
router.get('/', auth.isStudent, async (req, res) => {
  try {
    // Get student's classes
    const studentId = req.session.userId || req.session.user.id;
    const [studentClasses] = await db.query(`
      SELECT c.id
      FROM classes c
      JOIN student_classes sc ON c.id = sc.class_id
      WHERE sc.student_id = ?
    `, [studentId]);

    // Handle case where studentClasses might be empty
    const classIds = studentClasses && studentClasses.length > 0 ? studentClasses.map(c => c.id) : [];

    if (classIds.length === 0) {
      return res.render('student/practicals/index', {
        title: 'Practicals',
        practicals: [],
        upcomingPracticals: [],
        completedPracticals: [],
        user: res.locals.user,
        layout: 'student',
        currentPage: 'practicals',
        subPage: 'list',
        notificationCount: 0
      });
    }

    // Get all practicals for the student's classes
    const query = `
      SELECT p.*,
             c.name AS class_name,
             c.trade,
             c.section,
             s.name AS subject_name,
             l.name AS venue,
             (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
              FROM practical_topics pt
              JOIN topics t ON pt.topic_id = t.id
              WHERE pt.practical_id = p.id) AS practical_topic,
             (SELECT COUNT(*) FROM practical_records
              WHERE practical_id = p.id AND student_id = ?) as has_submission,
             (SELECT status FROM practical_records
              WHERE practical_id = p.id AND student_id = ?) as submission_status
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN labs l ON p.lab_id = l.id
      WHERE p.class_id IN (?)
      ORDER BY p.date DESC, p.start_time DESC
    `;

    // If classIds is empty, use a dummy value that won't match any records
    const [practicals] = await db.query(
      classIds.length > 0
        ? query
        : `SELECT * FROM practicals WHERE 1=0`, // Empty result set if no classes
      classIds.length > 0 ? [studentId, studentId, classIds] : []
    );

    // Separate upcoming and completed practicals
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const upcomingPracticals = practicals.filter(p => {
      const practicalDate = new Date(p.date);
      practicalDate.setHours(0, 0, 0, 0);
      return practicalDate >= today &&
             p.status !== 'cancelled' &&
             p.status !== 'Cancelled' &&
             p.status !== 'completed' &&
             p.status !== 'Completed';
    });

    const completedPracticals = practicals.filter(p => {
      const practicalDate = new Date(p.date);
      practicalDate.setHours(0, 0, 0, 0);
      return practicalDate < today ||
             p.status === 'completed' ||
             p.status === 'Completed';
    });

    res.render('student/practicals/index', {
      title: 'Practicals',
      practicals,
      upcomingPracticals,
      completedPracticals,
      user: res.locals.user,
      layout: 'student',
      currentPage: 'practicals',
      subPage: 'list',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching practicals:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch practicals',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  }
});

// Calendar route removed - now using global calendar at /student/calendar
router.get('/calendar', auth.isStudent, (req, res) => {
  // Redirect to the global calendar
  res.redirect('/student/calendar');
});

// Get all submissions for the student
router.get('/submissions/all', auth.isStudent, async (req, res) => {
  try {
    // Get all submissions for this student
    const studentId = req.session.userId || req.session.user.id;
    const [submissions] = await db.query(`
      SELECT pr.*,
             p.date as practical_date,
             p.start_time,
             p.end_time,
             p.description,
             c.name as class_name,
             c.trade,
             c.section,
             s.name as subject_name,
             l.name as venue,
             (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
              FROM practical_topics pt
              JOIN topics t ON pt.topic_id = t.id
              WHERE pt.practical_id = p.id) AS practical_topic
      FROM practical_records pr
      LEFT JOIN practicals p ON pr.practical_id = p.id
      LEFT JOIN classes c ON p.class_id = c.id
      LEFT JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN labs l ON p.lab_id = l.id
      WHERE pr.student_id = ?
      ORDER BY pr.submission_date DESC
    `, [studentId]);

    res.render('student/practicals/submissions', {
      title: 'My Practical Submissions',
      submissions,
      user: res.locals.user,
      layout: 'student',
      currentPage: 'practicals',
      subPage: 'submissions',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching submissions:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch submissions',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  }
});

// Get a specific submission
router.get('/submissions/:id', auth.isStudent, async (req, res) => {
  try {
    const studentId = req.session.userId || req.session.user.id;

    // First, get the submission record
    const [submissions] = await db.query(`
      SELECT pr.*
      FROM practical_records pr
      WHERE pr.id = ? AND pr.student_id = ?
    `, [req.params.id, studentId]);

    if (submissions.length === 0) {
      req.flash('error', 'Submission not found');
      return res.redirect('/student/practicals/submissions/all');
    }

    const submission = submissions[0];

    // Now get the practical details
    const [practicals] = await db.query(`
      SELECT p.*,
             u.name as teacher_name,
             c.name as class_name,
             c.trade,
             c.section,
             s.name as subject_name,
             l.name as venue,
             (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
              FROM practical_topics pt
              JOIN topics t ON pt.topic_id = t.id
              WHERE pt.practical_id = p.id) AS practical_topic
      FROM practicals p
      JOIN users u ON p.teacher_id = u.id
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN labs l ON p.lab_id = l.id
      WHERE p.id = ?
    `, [submission.practical_id]);

    if (practicals.length === 0) {
      req.flash('error', 'Practical not found');
      return res.redirect('/student/practicals/submissions/all');
    }

    const practical = practicals[0];

    res.render('student/practicals/view', {
      title: practical.description || 'Practical Details',
      practical,
      submission,
      hasSubmission: true,
      user: res.locals.user,
      layout: 'student',
      currentPage: 'practicals',
      subPage: 'view',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching submission details:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch submission details',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  }
});

// Get a specific practical
router.get('/:id', auth.isStudent, async (req, res) => {
  try {
    // Get practical details
    const [practicals] = await db.query(`
      SELECT p.*,
             u.name as teacher_name,
             c.name as class_name,
             c.trade,
             c.section,
             s.name as subject_name,
             l.name as venue,
             (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
              FROM practical_topics pt
              JOIN topics t ON pt.topic_id = t.id
              WHERE pt.practical_id = p.id) AS practical_topic
      FROM practicals p
      JOIN users u ON p.teacher_id = u.id
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      LEFT JOIN labs l ON p.lab_id = l.id
      WHERE p.id = ?
    `, [req.params.id]);

    if (practicals.length === 0) {
      req.flash('error', 'Practical not found');
      return res.redirect('/student/practicals');
    }

    const practical = practicals[0];

    // Check if student has submitted for this practical
    const studentId = req.session.userId || req.session.user.id;
    const [submissions] = await db.query(`
      SELECT * FROM practical_records
      WHERE practical_id = ? AND student_id = ?
    `, [req.params.id, studentId]);

    const hasSubmission = submissions.length > 0;
    const submission = hasSubmission ? submissions[0] : null;

    res.render('student/practicals/view', {
      title: practical.description || 'Practical Details',
      practical,
      submission,
      hasSubmission,
      user: res.locals.user,
      layout: 'student',
      currentPage: 'practicals',
      subPage: 'view',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  } catch (error) {
    console.error('Error fetching practical details:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch practical details',
      error: { status: 500, stack: error.stack },
      layout: 'student',
      notificationCount: 0,
      formatDate,
      formatDateTime
    });
  }
});

// Submit practical work
router.post('/:id/submit', auth.isStudent, (req, res) => {
  upload(req, res, async function (err) {
    if (err) {
      req.flash('error', err.message);
      return res.redirect(`/student/practicals/${req.params.id}`);
    }

    const connection = await db.getConnection();
    try {
      await connection.beginTransaction();

      const { content } = req.body;
      const attachment = req.file ? `/uploads/practical_submissions/${req.file.filename}` : null;

      // Get practical details to copy class and subject info
      const [practicals] = await connection.query(`
        SELECT p.*,
               c.name as class_name,
               c.trade,
               c.section,
               s.name as subject_name,
               l.name as venue,
               (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
                FROM practical_topics pt
                JOIN topics t ON pt.topic_id = t.id
                WHERE pt.practical_id = p.id) AS practical_topic
        FROM practicals p
        JOIN classes c ON p.class_id = c.id
        JOIN subjects s ON p.subject_id = s.id
        LEFT JOIN labs l ON p.lab_id = l.id
        WHERE p.id = ?
      `, [req.params.id]);

      if (practicals.length === 0) {
        await connection.rollback();
        req.flash('error', 'Practical not found');
        return res.redirect('/student/practicals');
      }

      const practical = practicals[0];

      // Check if student already has a submission
      const studentId = req.session.userId || req.session.user.id;
      const [existingSubmissions] = await connection.query(`
        SELECT id FROM practical_records
        WHERE practical_id = ? AND student_id = ?
      `, [req.params.id, studentId]);

      if (existingSubmissions.length > 0) {
        // Update existing submission
        await connection.query(`
          UPDATE practical_records
          SET content = ?,
              attachment = COALESCE(?, attachment),
              submission_date = NOW(),
              status = 'submitted',
              updated_at = NOW()
          WHERE id = ?
        `, [content, attachment, existingSubmissions[0].id]);
      } else {
        // Create new submission
        await connection.query(`
          INSERT INTO practical_records
          (practical_id, student_id, content, attachment, submission_date, status)
          VALUES (?, ?, ?, ?, NOW(), 'submitted')
        `, [
          req.params.id,
          studentId,
          content,
          attachment
        ]);
      }

      await connection.commit();
      req.flash('success', 'Practical work submitted successfully');
      res.redirect(`/student/practicals/${req.params.id}`);
    } catch (error) {
      await connection.rollback();
      console.error('Error submitting practical work:', error);
      req.flash('error', 'Failed to submit practical work');
      res.redirect(`/student/practicals/${req.params.id}`);
    } finally {
      connection.release();
    }
  });
});

module.exports = router;
