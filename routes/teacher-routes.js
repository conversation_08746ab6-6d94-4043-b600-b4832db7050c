const express = require('express');
const router = express.Router();
const { checkAuthenticated, isTeacher, isPrincipal } = require('../middleware/auth');
const { isComputerTeacher, restrictComputerTeacher } = require('../middleware/computer-teacher-check');
const teacherController = require('../controllers/teacher-controller');
const teacherInstructionPlanController = require('../controllers/teacher-instruction-plan-controller');
const teacherViewController = require('../controllers/teacher-view-controller');

// Middleware to check if user is a teacher, principal, or admin
const checkTeacher = (req, res, next) => {
  if (req.session.userRole === 'teacher' || req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }
  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access this resource.',
    error: { status: 403 },
    layout: 'layouts/auth'
  });
};

// Middleware to check if user has global data access (admin only)
const checkGlobalAccess = async (req, res, next) => {
  if (req.session.userRole === 'admin') {
    return next();
  }

  // Check if user is a computer teacher
  if (req.session.userRole === 'teacher') {
    if (req.isComputerTeacher === undefined) {
      await isComputerTeacher(req, res, () => {});
    }

    // Computer teachers cannot access global data
    if (req.isComputerTeacher) {
      return res.status(403).render('error', {
        title: 'Access Denied',
        message: 'Computer teachers cannot access global data. You can only view your own data.',
        error: { status: 403 },
        layout: 'teacher'
      });
    }
  }

  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access global data. This feature requires admin privileges.',
    error: { status: 403 },
    layout: 'teacher'
  });
};

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkTeacher);
router.use(isComputerTeacher);

// Teacher dashboard
router.get('/dashboard', restrictComputerTeacher, teacherController.getDashboard);

// Teacher profile (will be replaced by Classes)
router.get('/profile', restrictComputerTeacher, teacherController.getProfile);

// Enhanced teacher profile with timeline
router.get('/profile-enhanced', (req, res) => {
  res.render('teacher/profile-enhanced', {
    title: 'Enhanced Teacher Profile',
    layout: 'teacher',
    pageTitle: 'Enhanced Profile',
    currentPage: 'profile-enhanced'
  });
});

// Classes page
router.get('/classes', restrictComputerTeacher, teacherController.getClasses);

// Timetable page
router.get('/timetable', restrictComputerTeacher, teacherController.getTimetable);

// Teacher timetable with teacher filter (requires global access)
router.get('/teacher-timetable', checkGlobalAccess, teacherController.getTeacherTimetable);

// Teacher's personal timetable
router.get('/my-timetable', restrictComputerTeacher, teacherController.getMyTimetable);

// Class-Teacher-Subject mind map (requires global access)
router.get('/class-teacher-map', checkGlobalAccess, teacherController.getClassTeacherMap);

// Class-Subject Summary page (requires global access)
router.get('/class-subject-summary', checkGlobalAccess, teacherController.getClassSubjectSummary);

// Syllabus management
router.get('/syllabus', restrictComputerTeacher, teacherController.getSyllabus);
router.get('/syllabus/topics', restrictComputerTeacher, teacherController.getSyllabusTopics);
router.get('/syllabus/progress', restrictComputerTeacher, teacherController.getSyllabusProgress);

// Practicals management
router.get('/practicals', restrictComputerTeacher, teacherController.getPracticals);
router.get('/practicals/schedule', restrictComputerTeacher, teacherController.getSchedulePractical);
router.get('/practicals/:id', restrictComputerTeacher, teacherController.getPracticalDetails);
router.get('/practicals/:id/edit', restrictComputerTeacher, teacherController.getEditPractical);
router.get('/practicals/:id/records', restrictComputerTeacher, teacherController.getPracticalStudentRecords);

// Practical records management
router.get('/practical-records', restrictComputerTeacher, teacherController.getPracticalRecords);

// Reports
router.get('/reports', restrictComputerTeacher, teacherController.getReports);
router.get('/reports/lectures', restrictComputerTeacher, teacherController.getLectureReports);

// Lecture management
router.get('/lectures', restrictComputerTeacher, teacherController.getLectures);
router.get('/lectures/mark', restrictComputerTeacher, teacherController.getMarkLecture);

// Syllabus management
router.get('/syllabus/update', restrictComputerTeacher, teacherController.getUpdateSyllabus);

// Student practical records
router.get('/practicals/records', restrictComputerTeacher, teacherController.getPracticalRecords);

// Reports
router.get('/reports/lectures', restrictComputerTeacher, teacherController.getLectureReports);
router.get('/reports/export', restrictComputerTeacher, teacherController.getExportReports);

// These API routes are now handled in routes/api/teacher-api.js
// Keeping these routes for backward compatibility with existing code
router.post('/api/lectures', (req, res) => {
  res.redirect(307, '/api/teacher/lectures'); // 307 preserves the HTTP method
});
router.get('/api/lectures/:id', (req, res) => {
  res.redirect(`/api/teacher/lectures/${req.params.id}`);
});
router.put('/api/lectures/:id/status', (req, res) => {
  res.redirect(307, `/api/teacher/lectures/${req.params.id}/status`);
});
router.put('/api/practical-records/:id/grade', teacherController.gradePracticalRecord);

// Add these routes when implementing practical scheduling functionality
router.post('/api/practicals', (req, res) => {
  res.redirect(307, '/api/teacher/practicals');
});
router.get('/api/practicals/:id', (req, res) => {
  res.redirect(`/api/teacher/practicals/${req.params.id}`);
});
router.put('/api/practicals/:id/status', (req, res) => {
  res.redirect(307, `/api/teacher/practicals/${req.params.id}/status`);
});

// Instruction plans routes for teachers
router.get('/instruction-plans', teacherInstructionPlanController.getInstructionPlans);
router.get('/instruction-plans/add', teacherInstructionPlanController.getAddInstructionPlan);
router.post('/instruction-plans/add', teacherInstructionPlanController.postAddInstructionPlan);
router.get('/instruction-plans/:id', teacherInstructionPlanController.getInstructionPlanDetails);
router.get('/instruction-plans/edit/:id', teacherInstructionPlanController.getEditInstructionPlan);
router.post('/instruction-plans/edit/:id', teacherInstructionPlanController.postEditInstructionPlan);
router.delete('/instruction-plans/delete/:id', teacherInstructionPlanController.deleteInstructionPlan);

// Teacher view routes for enhanced functionality
router.get('/subject-eligibility', checkGlobalAccess, teacherViewController.getTeacherSubjectEligibility);
router.get('/class-assignments', checkGlobalAccess, teacherViewController.getClassAssignments);
router.get('/lecture-schedules', checkGlobalAccess, teacherViewController.getLectureSchedules);
router.get('/daily-instruction-plans', restrictComputerTeacher, teacherViewController.getInstructionPlans);
router.get('/holiday-calendar', restrictComputerTeacher, teacherViewController.getHolidayCalendar);
router.get('/calendar', restrictComputerTeacher, async (req, res) => {
  try {
    const db = require('../config/database');

    // Get holidays
    let holidays = [];
    try {
      // Check if holiday_calendar table exists
      const [tables] = await db.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'exam_prep_platform'
        AND TABLE_NAME = 'holiday_calendar'
      `);

      if (tables.length > 0) {
        [holidays] = await db.query(`
          SELECT id, holiday_date, description, holiday_type, is_active
          FROM holiday_calendar
          WHERE is_active = 1
          ORDER BY holiday_date
        `);
      }
    } catch (err) {
      console.log('Holiday calendar table may not exist yet:', err.message);
    }

    res.render('teacher/calendar', {
      title: 'Academic Calendar',
      layout: 'teacher',
      currentPage: 'calendar',
      holidays: holidays || [],
      user: res.locals.user
    });
  } catch (error) {
    console.error('Error fetching calendar data:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to fetch calendar data',
      error: { status: 500, stack: error.stack },
      layout: 'teacher'
    });
  }
});

module.exports = router;