const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const auth = require('../../middleware/auth');
const { isComputerTeacher, restrictComputerTeacher } = require('../../middleware/computer-teacher-check');

// Apply middleware to all routes
router.use(auth.isTeacher);
router.use(isComputerTeacher);

// Get all practicals
router.get('/', restrictComputerTeacher, async (req, res) => {
  try {
    const teacherId = req.session.user.id;

    const query = `
      SELECT p.*, c.name AS class_name, c.trade, c.section, s.name AS subject_name
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      WHERE p.teacher_id = ?
      ORDER BY p.date DESC, p.start_time DESC
    `;

    const [practicals] = await db.query(query, [teacherId]);

    // Get topics for each practical
    for (const practical of practicals) {
      const topicQuery = `
        SELECT t.name
        FROM topics t
        JOIN practical_topics pt ON t.id = pt.topic_id
        WHERE pt.practical_id = ?
      `;
      const [topics] = await db.query(topicQuery, [practical.id]);
      practical.topics = topics.map(t => t.name);
    }

    // Fetch only classes assigned to this teacher
    const classQuery = `
      SELECT DISTINCT c.id, c.name, c.trade, c.section
      FROM classes c
      JOIN teacher_classes tc ON c.id = tc.class_id
      WHERE tc.teacher_id = ? AND c.is_active = 1
    `;

    // Fallback query if teacher_classes table doesn't exist or no classes are assigned
    const fallbackClassQuery = `
      SELECT DISTINCT c.id, c.name, c.trade, c.section
      FROM classes c
      JOIN practicals p ON c.id = p.class_id
      WHERE p.teacher_id = ? AND c.is_active = 1
    `;

    // Try the primary query first
    let [classSections] = await db.query(classQuery, [teacherId]);

    // If no results, try the fallback query
    if (classSections.length === 0) {
      [classSections] = await db.query(fallbackClassQuery, [teacherId]);

      // If still no results, get a limited set of classes
      if (classSections.length === 0) {
        [classSections] = await db.query(
          `SELECT id, name, trade, section FROM classes WHERE is_active = 1 LIMIT 5`
        );
      }
    }

    // Fetch only subjects taught by this teacher
    const subjectQuery = `
      SELECT DISTINCT s.id, s.name
      FROM subjects s
      JOIN teacher_subjects ts ON s.id = ts.subject_id
      WHERE ts.teacher_id = ? AND s.is_active = 1
    `;

    // Fallback query if teacher_subjects table doesn't exist or no subjects are assigned
    const fallbackSubjectQuery = `
      SELECT DISTINCT s.id, s.name
      FROM subjects s
      JOIN practicals p ON s.id = p.subject_id
      WHERE p.teacher_id = ? AND s.is_active = 1
    `;

    // Try the primary query first
    let [subjects] = await db.query(subjectQuery, [teacherId]);

    // If no results, try the fallback query
    if (subjects.length === 0) {
      [subjects] = await db.query(fallbackSubjectQuery, [teacherId]);

      // If still no results, get a limited set of subjects
      if (subjects.length === 0) {
        [subjects] = await db.query(
          `SELECT id, name FROM subjects WHERE is_active = 1 LIMIT 5`
        );
      }
    }

    // Fetch topics for the dropdown - filter by subjects taught by this teacher
    const topicQuery = `
      SELECT DISTINCT t.id, t.name
      FROM topics t
      JOIN practical_topics pt ON t.id = pt.topic_id
      JOIN practicals p ON pt.practical_id = p.id
      WHERE p.teacher_id = ? AND t.is_active = 1
    `;

    // Try the filtered query first
    let [topics] = await db.query(topicQuery, [teacherId]);

    // If no results, get all topics
    if (topics.length === 0) {
      [topics] = await db.query(
        `SELECT id, name FROM topics WHERE is_active = 1`
      );
    }

    // Fetch labs used by this teacher
    const labQuery = `
      SELECT DISTINCT l.id, l.name
      FROM labs l
      JOIN practicals p ON l.id = p.lab_id
      WHERE p.teacher_id = ? AND l.is_active = 1
    `;

    // Try the filtered query first
    let [labs] = await db.query(labQuery, [teacherId]);

    // If no results, get all labs
    if (labs.length === 0) {
      [labs] = await db.query(
        `SELECT id, name FROM labs WHERE is_active = 1`
      );
    }

    res.render('teacher/practicals', {
      practicals,
      classSections,
      subjects,
      topics,
      labs,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching practicals:', error);
    res.status(500).send('Server error');
  }
});

// Get practical details by ID
router.get('/:id', restrictComputerTeacher, async (req, res) => {
  try {
    const query = `
      SELECT p.*, c.name AS class_name, c.trade, c.section, s.name AS subject_name
      FROM practicals p
      JOIN classes c ON p.class_id = c.id
      JOIN subjects s ON p.subject_id = s.id
      WHERE p.id = ? AND p.teacher_id = ?
    `;

    const [practicals] = await db.query(query, [req.params.id, req.session.user.id]);

    if (practicals.length === 0) {
      return res.status(404).json({ message: 'Practical not found' });
    }

    const practical = practicals[0];

    // Get topics for the practical
    const topicQuery = `
      SELECT t.id, t.name
      FROM topics t
      JOIN practical_topics pt ON t.id = pt.topic_id
      WHERE pt.practical_id = ?
    `;
    const [topics] = await db.query(topicQuery, [practical.id]);
    practical.topics = topics;

    res.json(practical);
  } catch (error) {
    console.error('Error fetching practical details:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new practical
router.post('/', restrictComputerTeacher, async (req, res) => {
  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    const {
      date,
      start_time,
      end_time,
      class_id,
      subject_id,
      lab_id,
      topics,
      description,
      prerequisites,
      materials,
      equipment
    } = req.body;

    // Insert into practicals table
    const [result] = await connection.query(
      `INSERT INTO practicals (
        date, start_time, end_time, class_id, subject_id, lab_id,
        description, prerequisites, materials, equipment, status, teacher_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Upcoming', ?)`,
      [
        date, start_time, end_time, class_id, subject_id, lab_id,
        description, prerequisites, materials, equipment, req.session.user.id
      ]
    );

    const practicalId = result.insertId;

    // Insert topics
    if (topics && topics.length > 0) {
      // Check if topics exist, create new ones if needed
      for (const topic of topics) {
        let topicId;

        // Check if topic exists
        const [existingTopic] = await connection.query(
          'SELECT id FROM topics WHERE name = ?',
          [topic]
        );

        if (existingTopic.length > 0) {
          topicId = existingTopic[0].id;
        } else {
          // Create new topic
          const [newTopic] = await connection.query(
            'INSERT INTO topics (name, is_active) VALUES (?, 1)',
            [topic]
          );
          topicId = newTopic.insertId;
        }

        // Insert into junction table
        await connection.query(
          'INSERT INTO practical_topics (practical_id, topic_id) VALUES (?, ?)',
          [practicalId, topicId]
        );
      }
    }

    await connection.commit();
    res.status(201).json({
      success: true,
      message: 'Practical scheduled successfully',
      id: practicalId
    });
  } catch (error) {
    await connection.rollback();
    console.error('Error creating practical:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  } finally {
    connection.release();
  }
});

// Update a practical
router.put('/:id', restrictComputerTeacher, async (req, res) => {
  const connection = await db.getConnection();
  try {
    await connection.beginTransaction();

    const {
      date,
      start_time,
      end_time,
      class_id,
      subject_id,
      lab_id,
      topics,
      description,
      prerequisites,
      materials,
      equipment
    } = req.body;

    // Check if practical exists and belongs to this teacher
    const [existingPractical] = await connection.query(
      'SELECT id FROM practicals WHERE id = ? AND teacher_id = ?',
      [req.params.id, req.session.user.id]
    );

    if (existingPractical.length === 0) {
      return res.status(404).json({ success: false, message: 'Practical not found' });
    }

    // Update practical
    await connection.query(
      `UPDATE practicals SET
        date = ?, start_time = ?, end_time = ?, class_id = ?, subject_id = ?, lab_id = ?,
        description = ?, prerequisites = ?, materials = ?, equipment = ?
      WHERE id = ?`,
      [
        date, start_time, end_time, class_id, subject_id, lab_id,
        description, prerequisites, materials, equipment, req.params.id
      ]
    );

    // Delete existing topics relationships
    await connection.query(
      'DELETE FROM practical_topics WHERE practical_id = ?',
      [req.params.id]
    );

    // Insert updated topics
    if (topics && topics.length > 0) {
      for (const topic of topics) {
        let topicId;

        // Check if topic exists
        const [existingTopic] = await connection.query(
          'SELECT id FROM topics WHERE name = ?',
          [topic]
        );

        if (existingTopic.length > 0) {
          topicId = existingTopic[0].id;
        } else {
          // Create new topic
          const [newTopic] = await connection.query(
            'INSERT INTO topics (name, is_active) VALUES (?, 1)',
            [topic]
          );
          topicId = newTopic.insertId;
        }

        // Insert into junction table
        await connection.query(
          'INSERT INTO practical_topics (practical_id, topic_id) VALUES (?, ?)',
          [req.params.id, topicId]
        );
      }
    }

    await connection.commit();
    res.json({ success: true, message: 'Practical updated successfully' });
  } catch (error) {
    await connection.rollback();
    console.error('Error updating practical:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  } finally {
    connection.release();
  }
});

// Cancel a practical
router.patch('/:id/cancel', restrictComputerTeacher, async (req, res) => {
  try {
    // Check if practical exists and belongs to this teacher
    const [existingPractical] = await db.query(
      'SELECT id FROM practicals WHERE id = ? AND teacher_id = ?',
      [req.params.id, req.session.user.id]
    );

    if (existingPractical.length === 0) {
      return res.status(404).json({ success: false, message: 'Practical not found' });
    }

    // Update status to Cancelled
    await db.query(
      'UPDATE practicals SET status = ? WHERE id = ?',
      ['Cancelled', req.params.id]
    );

    res.json({ success: true, message: 'Practical cancelled successfully' });
  } catch (error) {
    console.error('Error cancelling practical:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Mark a practical as completed
router.patch('/:id/complete', restrictComputerTeacher, async (req, res) => {
  try {
    // Check if practical exists and belongs to this teacher
    const [existingPractical] = await db.query(
      'SELECT id FROM practicals WHERE id = ? AND teacher_id = ?',
      [req.params.id, req.session.user.id]
    );

    if (existingPractical.length === 0) {
      return res.status(404).json({ success: false, message: 'Practical not found' });
    }

    // Update status to Completed
    await db.query(
      'UPDATE practicals SET status = ? WHERE id = ?',
      ['Completed', req.params.id]
    );

    res.json({ success: true, message: 'Practical marked as completed' });
  } catch (error) {
    console.error('Error completing practical:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get student records for a practical
router.get('/:id/records', restrictComputerTeacher, async (req, res) => {
  try {
    // Check if practical exists and belongs to this teacher
    const [practicals] = await db.query(
      `SELECT p.*, c.name AS class_name, c.trade, c.section, s.name AS subject_name
       FROM practicals p
       JOIN classes c ON p.class_id = c.id
       JOIN subjects s ON p.subject_id = s.id
       WHERE p.id = ? AND p.teacher_id = ?`,
      [req.params.id, req.session.user.id]
    );

    if (practicals.length === 0) {
      return res.status(404).json({ success: false, message: 'Practical not found' });
    }

    const practical = practicals[0];

    // Get student records
    const [records] = await db.query(
      `SELECT pr.*, s.roll_number, s.first_name, s.last_name
       FROM practical_records pr
       JOIN students s ON pr.student_id = s.id
       WHERE pr.practical_id = ?
       ORDER BY s.roll_number`,
      [req.params.id]
    );

    res.json({
      success: true,
      practical: practical,
      records: records
    });
  } catch (error) {
    console.error('Error fetching practical records:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router;