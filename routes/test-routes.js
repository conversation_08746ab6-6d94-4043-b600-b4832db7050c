const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');
const logError = require('../utils/logger');
const { formatDate, formatDateTime } = require('../utils/date-formatter');
const fs = require('fs').promises;
const path = require('path');
const { generatePDF } = require('../utils/pdf-generator');
const { enterTestMode, exitTestMode } = require('../middleware/test-mode-middleware');

// Apply authentication middleware to all routes
router.use(checkAuthenticated);

// Middleware to set layout and current page
router.use((req, res, next) => {
    // Set layout based on user role and path
    if (req.path.startsWith('/admin')) {
        res.locals.layout = 'admin';
    } else if (req.session.userRole === 'student') {
        res.locals.layout = 'student';
    } else {
        res.locals.layout = 'user';
    }

    res.locals.currentPage = 'tests';
    res.locals.navbar = 'tests';
    next();
});

// Create temp directory if it doesn't exist
fs.mkdir(path.join(__dirname, '../temp'), { recursive: true })
    .catch(err => console.error('Error creating temp directory:', err));

// Create uploads/pdf directory if it doesn't exist
fs.mkdir(path.join(__dirname, '../public/uploads/pdf'), { recursive: true })
    .catch(err => console.error('Error creating PDF directory:', err));

// Middleware to add user to res.locals
router.use((req, res, next) => {
    // Get user from session
    if (req.session.userId) {
        res.locals.user = {
            id: req.session.userId,
            name: req.session.username || 'User',
            role: req.session.userRole || 'user',
            notifications: []
        };
    } else {
        // Fallback for testing
        res.locals.user = {
            name: 'Guest User',
            role: 'guest',
            notifications: [],
            profile_image: null
        };
    }
    next();
});

// Use this function in error handlers
router.use(async (err, req, res, next) => {
    await logError(err, req);
    res.status(500).json({ success: false, message: 'Internal server error', error: err.message });
});

// Redirect from /admin to the admin tests page
router.get('/admin', checkAdmin, (req, res) => {
    res.redirect('/admin/tests');
});

// This route is deprecated - use /admin/tests instead
// Keeping it for backward compatibility
router.get('/admin/tests-old', checkAdmin, async (req, res) => {
    try {
        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const perPage = parseInt(req.query.perPage) || 10;
        const offset = (page - 1) * perPage;

        // Build filter conditions
        const filters = [];
        const params = [];

        if (req.query.search) {
            filters.push('(e.exam_name LIKE ? OR e.description LIKE ?)');
            params.push(`%${req.query.search}%`, `%${req.query.search}%`);
        }

        if (req.query.status) {
            filters.push('e.status = ?');
            params.push(req.query.status);
        }

        if (req.query.category) {
            filters.push('e.category_id = ?');
            params.push(req.query.category);
        }

        if (req.query.date) {
            filters.push('DATE(e.created_at) = ?');
            params.push(req.query.date);
        }

        // Construct WHERE clause
        const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

        // Get total count for pagination
        const [[{ total }]] = await db.query(`
            SELECT COUNT(*) as total
            FROM exams e
            ${whereClause}
        `, params);

        // Get filtered tests
        const [tests] = await db.query(`
            SELECT
                e.*,
                c.name as category_name,
                u.username as creator_name,
                (SELECT COUNT(*) FROM sections s WHERE s.exam_id = e.exam_id) as section_count,
                (SELECT COUNT(*) FROM exam_attempts ea WHERE ea.exam_id = e.exam_id) as attempt_count
            FROM exams e
            LEFT JOIN categories c ON e.category_id = c.category_id
            LEFT JOIN users u ON e.created_by = u.id
            ${whereClause}
            ORDER BY e.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, perPage, offset]);

        // Get categories for filter dropdown
        const [categories] = await db.query('SELECT category_id, name FROM categories WHERE parent_id IS NULL');

        // Get stats for count cards
        const [[creatorStats]] = await db.query('SELECT COUNT(DISTINCT created_by) as creator_count FROM exams');
        const [[publishedStats]] = await db.query('SELECT COUNT(*) as published_count FROM exams WHERE status = "published"');
        const [[draftStats]] = await db.query('SELECT COUNT(*) as draft_count FROM exams WHERE status = "draft"');
        const [[sectionStats]] = await db.query('SELECT COUNT(*) as section_count FROM sections');

        // Calculate pagination info
        const totalPages = Math.ceil(total / perPage);

        // Import date formatter functions
        const { formatDate, formatDateTime } = require('../utils/date-formatter');

        res.render('admin/tests/index', {
            tests,
            categories,
            pagination: {
                page,
                totalPages,
                perPage
            },
            query: req.query,
            totalTests: total,
            stats: {
                creator_count: creatorStats.creator_count,
                published_count: publishedStats.published_count,
                draft_count: draftStats.draft_count,
                section_count: sectionStats.section_count
            },
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });

    } catch (error) {
        console.error('Error in /admin/tests:', error);
        res.status(500).send('Internal Server Error');
    }
});

// Add test form (admin only)


// Archive test (admin only)
router.post('/admin/:examId/archive', checkAdmin, async (req, res) => {
    try {
        const { examId } = req.params;

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // 1. Check if exam exists
            const [exam] = await db.query(
                'SELECT * FROM exams WHERE exam_id = ?',
                [examId]
            );

            if (!exam.length) {
                throw new Error('Test not found');
            }

            // 2. Update exam status to archived
            await db.query(
                `UPDATE exams
                 SET status = 'archived',
                     updated_at = NOW()
                 WHERE exam_id = ?`,
                [examId]
            );

            // 3. Commit transaction
            await db.query('COMMIT');

            // 4. Redirect back to tests page with success message
            req.session.flashSuccess = 'Test archived successfully';
            return res.redirect('/tests/admin');

        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error archiving test:', error);
        req.session.flashError = error.message || 'Error archiving test';
        return res.redirect('/tests/admin');
    }
});

// Publish test (admin only)
router.post('/admin/:examId/publish', checkAdmin, async (req, res) => {
    try {
        console.log('Publish request received:');
        console.log('Params:', req.params);
        console.log('Body:', req.body);

        const { examId } = req.params;
        let publishDatetime = req.body.publish_date;

        // If publishDatetime is not provided, use current time
        if (!publishDatetime) {
            const now = new Date();
            publishDatetime = now.toISOString().slice(0, 16);
            console.log('Using current time for publishDatetime:', publishDatetime);
        }

        // Convert the ISO datetime string to a proper MySQL datetime format
        // The input format is YYYY-MM-DDTHH:MM
        // The output format should be YYYY-MM-DD HH:MM:SS
        const formattedDatetime = publishDatetime.replace('T', ' ') + ':00';
        console.log('Formatted datetime for MySQL:', formattedDatetime);

        console.log('Extracted values:');
        console.log('examId:', examId);
        console.log('publishDatetime:', publishDatetime);

        // Input validation
        if (!examId) {
            console.log('Validation failed: Missing examId');
            req.session.flashError = 'Exam ID is required';
            return res.redirect('/admin/tests');
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // 1. Check if exam exists and validate its state
            const [exam] = await db.query(
                `SELECT e.*,
                        (SELECT COUNT(*) FROM sections s WHERE s.exam_id = e.exam_id) as section_count,
                        (SELECT COUNT(*) FROM questions q
                         INNER JOIN sections s ON q.section_id = s.section_id
                         WHERE s.exam_id = e.exam_id) as question_count
                 FROM exams e
                 WHERE e.exam_id = ?`,
                [examId]
            );

            if (!exam.length) {
                throw new Error('Test not found');
            }

            const examData = exam[0];

            // 2. Determine if the test should be published immediately or scheduled
            // Get the publish option and date from the form
            const publishOption = req.body.publish_option;
            const publishDateFromForm = req.body.publish_date;
            console.log('Publish option:', publishOption);
            console.log('Publish date from form:', publishDateFromForm);

            // Ignore any passing_marks or other fields that might be sent
            // We only care about the publish_option and publish_date for publishing a test

            // If publishing now, use the current time from the form
            if (publishOption === 'now') {
                // The form should have set the publish_date field to the current time
                // But we'll parse it here to make sure it's in the correct format for MySQL
                if (publishDateFromForm) {
                    // Convert the datetime-local value (YYYY-MM-DDTHH:MM) to a Date object
                    const [datePart, timePart] = publishDateFromForm.split('T');
                    const [year, month, day] = datePart.split('-');
                    const [hour, minute] = timePart.split(':');

                    // Format for MySQL
                    const mysqlDatetime = `${year}-${month}-${day} ${hour}:${minute}:00`;

                    console.log('Publishing now with date from form');
                    console.log('MySQL datetime format:', mysqlDatetime);

                    // Update exam status and publish details
                    await db.query(
                        `UPDATE exams
                         SET status = 'published',
                             publish_date = ?,
                             updated_at = NOW()
                         WHERE exam_id = ?`,
                        [mysqlDatetime, examId]
                    );
                } else {
                    // Fallback to server's current time if no date was provided
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const hour = String(now.getHours()).padStart(2, '0');
                    const minute = String(now.getMinutes()).padStart(2, '0');

                    const mysqlDatetime = `${year}-${month}-${day} ${hour}:${minute}:00`;

                    console.log('Publishing now with server time');
                    console.log('MySQL datetime format:', mysqlDatetime);

                    // Update exam status and publish details
                    await db.query(
                        `UPDATE exams
                         SET status = 'published',
                             publish_date = ?,
                             updated_at = NOW()
                         WHERE exam_id = ?`,
                        [mysqlDatetime, examId]
                    );
                }
            }
            // If publishing later and a date was selected, use that date
            else if (publishOption === 'later' && publishDateFromForm) {
                // Convert the datetime-local value (YYYY-MM-DDTHH:MM) to a Date object
                const [datePart, timePart] = publishDateFromForm.split('T');
                const [year, month, day] = datePart.split('-');
                const [hour, minute] = timePart.split(':');

                // Create date objects with explicit parts to avoid timezone issues
                const publishDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
                const now = new Date();

                // Format for MySQL
                const mysqlDatetime = `${year}-${month}-${day} ${hour}:${minute}:00`;

                // Log detailed date information for debugging
                console.log('Publishing later');
                console.log('Date parts:', { year, month, day, hour, minute });
                console.log('Publish date (parsed):', publishDate);
                console.log('MySQL datetime format:', mysqlDatetime);
                console.log('Current date:', now);

                // Compare the dates
                const isScheduled = publishDate > now;
                console.log('Is future date?', isScheduled);

                // Set status based on comparison
                const status = isScheduled ? 'scheduled' : 'published';

                // Update exam status and publish details
                await db.query(
                    `UPDATE exams
                     SET status = ?,
                         publish_date = ?,
                         updated_at = NOW()
                     WHERE exam_id = ?`,
                    [status, mysqlDatetime, examId]
                );
            }
            // Fallback to current time if something went wrong
            else {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hour = String(now.getHours()).padStart(2, '0');
                const minute = String(now.getMinutes()).padStart(2, '0');

                const mysqlDatetime = `${year}-${month}-${day} ${hour}:${minute}:00`;

                console.log('Fallback to current time');
                console.log('MySQL datetime format:', mysqlDatetime);

                // Update exam status and publish details
                await db.query(
                    `UPDATE exams
                     SET status = 'published',
                         publish_date = ?,
                         updated_at = NOW()
                     WHERE exam_id = ?`,
                    [mysqlDatetime, examId]
                );
            }

            // Success message
            console.log('Test published successfully');

            // 3. Commit transaction
            await db.query('COMMIT');

            // 4. Return success response or redirect based on request type
            if (req.xhr || req.headers.accept.includes('application/json')) {
                return res.json({
                    success: true,
                    message: 'Test published successfully',
                    examId: examId
                });
            } else {
                req.session.flashSuccess = 'Test published successfully';
                return res.redirect('/tests/admin');
            }

        } catch (error) {
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error publishing test:', error);

        // Return error response or redirect based on request type
        if (req.xhr || req.headers.accept.includes('application/json')) {
            return res.status(500).json({
                success: false,
                message: error.message || 'Error publishing test'
            });
        } else {
            req.session.flashError = error.message || 'Error publishing test';
            return res.redirect('/tests/admin');
        }
    }
});


router.post('/admin/add', checkAdmin, async (req, res) => {
    try {
        // Extract only the exam_name from the request body
        const { exam_name } = req.body;

        // Validate that exam_name is provided
        if (!exam_name) {
            return res.status(400).json({
                success: false,
                message: 'Exam name is required'
            });
        }
        console.log('Received exam_name:', exam_name);

        // Extract is_resumable from the request body if present
        const is_resumable = req.body.is_resumable === 'on' || req.body.is_resumable === true ? 1 : 0;

        // Insert the new test with minimal fields
        const [result] = await db.query(
            'INSERT INTO exams (exam_name, created_by, status, is_resumable) VALUES (?, ?, "draft", ?)',
            [exam_name, req.session.userId, is_resumable] // User ID from session, assuming authentication
        );

        const examId = result.insertId;

        // Respond with the new exam ID
        res.status(201).json({
            success: true,
            examId,
            message: 'Test draft created successfully'
        });
    } catch (error) {
        console.error('Error creating test draft:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});



// Add section to test (admin only) - old route
router.post('/admin/tests/:id/sections/add', checkAdmin, async (req, res, next) => {
    try {
        console.log('Request body:', req.body);
        const examId = req.params.id;
        const { section_name } = req.body;

        console.log('Adding section:', section_name);
        if (!section_name || typeof section_name !== 'string' || section_name.trim() === '') {
            throw new Error('Section name must be a non-empty string');
        }

        const trimmedSectionName = section_name.trim().replace(/\s+/g, ' ');

        // Check if a section with this name already exists for this exam
        const [existingSections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ?',
            [examId]
        );

        const normalizedNewName = trimmedSectionName.toLowerCase();
        const hasDuplicate = existingSections.some(section => {
            const normalizedExistingName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');
            return normalizedExistingName === normalizedNewName;
        });

        if (hasDuplicate) {
            req.session.flashError = 'A section with this name already exists. Please use a unique name.';
            return res.redirect(`/admin/tests/${examId}/edit`);
        }

        // Get current highest position
        const [positions] = await db.query(
            'SELECT MAX(position) as max_pos FROM sections WHERE exam_id = ?',
            [examId]
        );

        const newPosition = (positions[0].max_pos || 0) + 1;

        // Insert new section
        await db.query(
            'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
            [examId, trimmedSectionName, newPosition]
        );

        req.session.flashSuccess = 'Section added successfully';
        res.redirect(`/admin/tests/${examId}/edit`);
    } catch (error) {
        console.error('Error adding section:', error);
        req.session.flashError = `Error adding section: ${error.message}`;
        res.redirect(`/admin/tests/${examId}/edit`);
    }
});

// Add question to section (admin only)
router.post('/admin/tests/:examId/sections/:sectionId/questions/add', checkAdmin, async (req, res, next) => {
    try {
        console.log('Request body:', req.body);
        const { examId, sectionId } = req.params;
        const { question_type, question_text, solution_text, correct_answer, options } = req.body;

        // Start transaction
        await db.query('START TRANSACTION');

        // Insert question
        const [questionResult] = await db.query(
            'INSERT INTO questions (section_id, question_type, question_text, solution_text, correct_answer) VALUES (?, ?, ?, ?, ?)',
            [sectionId, question_type, question_text, solution_text || null, correct_answer || null]
        );

        const questionId = questionResult.insertId;

        // Insert options for MCQ questions
        if (question_type === 'mcq' && Array.isArray(options)) {
            for (let i = 0; i < options.length; i++) {
                if (options[i].trim() !== '') {
                    const isCorrect = i === parseInt(correct_answer) ? 1 : 0;
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                        [questionId, options[i], isCorrect, i]
                    );
                }
            }
        }

        // Commit transaction
        await db.query('COMMIT');

        req.session.flashSuccess = 'Question added successfully';
        res.redirect(`/admin/tests/${examId}/edit`);
    } catch (error) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error('Error adding question:', error);
        res.status(500).json({ success: false, message: 'Error adding question', error: error.message });
    }
});



/*Route to handle edit test form display
router.post('/edit', async (req, res, next) => {
    try {
        console.log('Request body:', req.body);
        const examId = req.body.id;

        if (!examId) {
            const error = new Error('Exam ID is required');
            error.status = 400;
            throw error;
        }

        // Check if exam exists
        const [examRows] = await db.query(
            'SELECT exam_id FROM exams WHERE exam_id = ?',
            [examId]
        );

        if (examRows.length === 0) {
            const error = new Error('Exam not found');
            error.status = 404;
            throw error;
        }

        // Render the add-test template with the exam ID
        res.render('tests/add-test', {
            title: 'Edit Test',
            testId: examId,
            error: null
        });
    } catch (error) {
        console.error('Error handling edit test form:', error);
        res.status(error.status || 500).json({ success: false, message: 'Error handling edit test form', error: error.message });
    }
});*/

// API route to get exam data for editing
router.get('/api/:id', async (req, res) => {
    try {
        console.log('Request params:', req.params);
        const examId = req.params.id;

        // Fetch the exam
        const [examRows] = await db.query(
            'SELECT * FROM exams WHERE exam_id = ?',
            [examId]
        );

        if (examRows.length === 0) {
            return res.status(404).json({ error: 'Exam not found' });
        }

        const exam = examRows[0];

        // Fetch sections
        const [sections] = await db.query(
            'SELECT * FROM sections WHERE exam_id = ? ORDER BY position',
            [examId]
        );

        // For each section, fetch its questions
        const sectionsWithQuestions = await Promise.all(sections.map(async (section) => {
            const [questions] = await db.query(
                'SELECT * FROM questions WHERE section_id = ? ORDER BY position',
                [section.section_id]
            );

            // For MCQ questions, fetch options
            const questionsWithOptions = await Promise.all(questions.map(async (question) => {
                if (question.question_type === 'mcq') {
                    const [options] = await db.query(
                        'SELECT * FROM options WHERE question_id = ? ORDER BY position',
                        [question.question_id]
                    );

                    // Convert options to array format expected by frontend
                    const optionsArray = options.map(option => option.option_text);

                    return {
                        ...question,
                        options: optionsArray
                    };
                }
                return question;
            }));

            return {
                ...section,
                questions: questionsWithOptions
            };
        }));

        // Format the response
        const response = {
            id: exam.exam_id,
            exam_name: exam.exam_name,
            duration: exam.duration,
            instructions: exam.instructions,
            publish_date: exam.publish_date,
            sections: sectionsWithQuestions
        };

        res.json(response);
    } catch (error) {
        console.error('Error fetching exam data:', error);
        res.status(500).json({ error: 'Failed to load exam data', message: error.message });
    }
});

// Route to update an existing test
router.put('/update/:id', async (req, res) => {
    try {
        const examId = req.params.id;
        const { exam_name, duration, instructions, publish_option, publish_date, sections } = req.body;

        // Begin transaction
        await db.query('START TRANSACTION');

        // Update the exam
        await db.query(
            'UPDATE exams SET exam_name = ?, duration = ?, instructions = ?, publish_date = ? WHERE exam_id = ?',
            [exam_name, duration, instructions, publish_option === 'later' ? publish_date : null, examId]
        );

        // Get existing sections
        const [existingSections] = await db.query('SELECT section_id FROM sections WHERE exam_id = ?', [examId]);
        const existingSectionIds = existingSections.map(s => s.section_id);

        // Update sections
        for (let [index, section] of sections.entries()) {
            let sectionId;

            if (index < existingSectionIds.length) {
                // Update existing section
                sectionId = existingSectionIds[index];
                await db.query(
                    'UPDATE sections SET section_name = ?, position = ? WHERE section_id = ?',
                    [section.name, index, sectionId]
                );
            } else {
                // Create new section
                const [result] = await db.query(
                    'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
                    [examId, section.name, index]
                );
                sectionId = result.insertId;
            }

            // Handle questions for this section
            await handleQuestions(sectionId, section.questions);
        }

        // Delete excess sections
        if (existingSectionIds.length > sections.length) {
            const sectionsToDelete = existingSectionIds.slice(sections.length);
            if (sectionsToDelete.length > 0) {
                const placeholders = sectionsToDelete.map(() => '?').join(',');
                await db.query(`DELETE FROM sections WHERE section_id IN (${placeholders})`, sectionsToDelete);
            }
        }

        // Commit transaction
        await db.query('COMMIT');

        res.status(200).json({ message: 'Test updated successfully' });
    } catch (error) {
        // Rollback in case of error
        await db.query('ROLLBACK');
        console.error('Error updating test:', error);
        res.status(500).json({ error: 'Failed to update test: ' + error.message });
    }
});

// Helper function to handle questions
async function handleQuestions(sectionId, questions) {
    // Get existing questions
    const [existingQuestions] = await db.query('SELECT question_id FROM questions WHERE section_id = ?', [sectionId]);
    const existingQuestionIds = existingQuestions.map(q => q.question_id);

    // Update/create questions
    for (let [index, question] of questions.entries()) {
        let questionId;

        if (index < existingQuestionIds.length) {
            // Update existing question
            questionId = existingQuestionIds[index];
            await db.query(
                'UPDATE questions SET question_type = ?, question_text = ?, solution_text = ?, position = ? WHERE question_id = ?',
                [question.type, question.question_text, question.solution_text, index, questionId]
            );
        } else {
            // Create new question
            const [result] = await db.query(
                'INSERT INTO questions (section_id, question_type, question_text, solution_text, position) VALUES (?, ?, ?, ?, ?)',
                [sectionId, question.type, question.question_text, question.solution_text, index]
            );
            questionId = result.insertId;
        }

        // Handle options/answers
        if (question.type === 'mcq') {
            // Delete existing options
            await db.query('DELETE FROM options WHERE question_id = ?', [questionId]);

            // Insert new options
            for (let [optionIndex, optionText] of question.options.entries()) {
                await db.query(
                    'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                    [questionId, optionText, optionIndex === question.correct_option ? 1 : 0, optionIndex]
                );
            }
        } else {
            // Update correct answer for true/false or fill in
            await db.query(
                'UPDATE questions SET correct_answer = ? WHERE question_id = ?',
                [question.correct_option, questionId]
            );
        }
    }

    // Delete excess questions
    if (existingQuestionIds.length > questions.length) {
        const questionsToDelete = existingQuestionIds.slice(questions.length);
        if (questionsToDelete.length > 0) {
            const placeholders = questionsToDelete.map(() => '?').join(',');
            await db.query(
                `DELETE FROM questions WHERE question_id IN (${placeholders})`,
                questionsToDelete
            );
        }
    }
}

// Preview test (admin only)
router.get('/admin/:examId/preview', checkAdmin, async (req, res, next) => {
    try {
        const { examId } = req.params;

        // Get test details
        const [tests] = await db.query(`
            SELECT e.*, u.username as creator_name
            FROM exams e
            LEFT JOIN users u ON e.created_by = u.id
            WHERE e.exam_id = ?
        `, [examId]);

        if (tests.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/admin/tests');
        }

        const test = tests[0];

        // Get sections and questions
        const [sections] = await db.query(`
            SELECT s.*
            FROM sections s
            WHERE s.exam_id = ?
            ORDER BY s.position
        `, [examId]);

        // Get questions for each section
        const sectionsWithQuestions = await Promise.all(sections.map(async (section) => {
            const [questions] = await db.query(`
                SELECT q.*,
                       e.essay_id, e.title as essay_title, e.content as essay_content,
                       qi1.file_path as image_path, qi2.file_path as solution_image_path,
                       GROUP_CONCAT(DISTINCT c.name) as category_names,
                       GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
                FROM questions q
                LEFT JOIN essays e ON q.essay_id = e.essay_id
                LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
                LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
                LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
                LEFT JOIN categories c ON qcm.category_id = c.category_id
                WHERE q.section_id = ?
                GROUP BY q.question_id
                ORDER BY q.position
            `, [section.section_id]);

            // For multiple choice questions, get options
            const questionsWithOptions = await Promise.all(questions.map(async (question) => {
                if (question.question_type === 'multiple_choice') {
                    const [options] = await db.query(`
                        SELECT * FROM options
                        WHERE question_id = ?
                        ORDER BY position
                    `, [question.question_id]);
                    return { ...question, options };
                }
                return question;
            }));

            return { ...section, questions: questionsWithOptions };
        }));

        // Render the preview template
        res.render('admin/tests/preview', {
            title: `Preview: ${test.exam_name}`,
            test,
            sections: sectionsWithQuestions
        });
    } catch (error) {
        console.error('Error loading test preview:', error);
        req.session.flashError = 'Error loading test preview';
        res.redirect('/admin/tests');
    }
});

// Edit test form (admin only)
router.get('/admin/:examId/edit', checkAdmin, async (req, res, next) => {
    try {
        const { examId } = req.params;

        // Get test details
        const [tests] = await db.query(`
            SELECT e.*, u.username as creator_name
            FROM exams e
            LEFT JOIN users u ON e.created_by = u.id
            WHERE e.exam_id = ? AND e.created_by = ?
        `, [examId, req.session.userId]);

        if (tests.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/tests/admin');
        }

        // Get sections and questions with counts by type and total marks
        const [sections] = await db.query(`
            SELECT s.*,
                   COUNT(q.question_id) as question_count,
                   SUM(CASE WHEN q.question_type = 'multiple_choice' THEN 1 ELSE 0 END) as mcq_count,
                   SUM(CASE WHEN q.question_type = 'true_false' THEN 1 ELSE 0 END) as true_false_count,
                   SUM(CASE WHEN q.question_type = 'fill_up' THEN 1 ELSE 0 END) as fill_up_count,
                   SUM(CASE WHEN q.question_type = 'essay' THEN 1 ELSE 0 END) as essay_count,
                   SUM(COALESCE(q.marks, 1)) as total_marks
            FROM sections s
            LEFT JOIN questions q ON s.section_id = q.section_id
            WHERE s.exam_id = ?
            GROUP BY s.section_id
            ORDER BY s.position
        `, [examId]);

        // Get questions for each section
        const sectionsWithQuestions = await Promise.all(sections.map(async (section) => {
            const [questions] = await db.query(`
                SELECT q.*,
                       e.essay_id, e.title as essay_title, e.content as essay_content,
                       qi1.file_path as image_path, qi2.file_path as solution_image_path,
                       GROUP_CONCAT(DISTINCT c.name) as category_names,
                       GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
                FROM questions q
                LEFT JOIN essays e ON q.essay_id = e.essay_id
                LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
                LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
                LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
                LEFT JOIN categories c ON qcm.category_id = c.category_id
                WHERE q.section_id = ?
                GROUP BY q.question_id
                ORDER BY q.position
            `, [section.section_id]);

            // For multiple choice questions, get options
            const questionsWithOptions = await Promise.all(questions.map(async (question) => {
                if (question.question_type === 'multiple_choice') {
                    const [options] = await db.query(`
                        SELECT * FROM options
                        WHERE question_id = ?
                        ORDER BY position
                    `, [question.question_id]);
                    return { ...question, options };
                }
                return question;
            }));

            return { ...section, questions: questionsWithOptions };
        }));

        // Calculate total questions count across all sections
        let totalQuestionsCount = 0;
        sectionsWithQuestions.forEach(section => {
            totalQuestionsCount += section.questions.length;
        });

        res.render('admin/tests/edit', {
            title: 'Edit Test',
            pageTitle: 'Edit Test',
            test: tests[0],
            sections: sectionsWithQuestions,
            totalQuestionsCount: totalQuestionsCount,
            currentPage: 'tests',
            navbar: 'tests'
        });
    } catch (error) {
        console.error('Error loading test for edit:', error);
        next(error);
    }
});

// Update test (admin only)
router.post('/admin/:examId/edit', checkAdmin, async (req, res, next) => {
    try {
        console.log('Request body:', req.body);
        const { examId } = req.params;
        const { exam_name, description, duration, passing_marks, max_attempts } = req.body;

        // Validate inputs
        if (!exam_name || typeof exam_name !== 'string' || exam_name.trim() === '') {
            req.session.flashError = 'Test name is required';
            return res.redirect(`/tests/admin/${examId}/edit`);
        }

        // Check if the test has at least one section
        const [sections] = await db.query(
            'SELECT COUNT(*) as section_count FROM sections WHERE exam_id = ?',
            [examId]
        );

        if (sections[0].section_count === 0) {
            req.session.flashError = 'Test must have at least one section';
            return res.redirect(`/tests/admin/${examId}/edit`);
        }

        // Check if each section has at least one question
        const [sectionIds] = await db.query(
            'SELECT section_id FROM sections WHERE exam_id = ?',
            [examId]
        );

        for (const section of sectionIds) {
            const [questions] = await db.query(
                'SELECT COUNT(*) as question_count FROM questions WHERE section_id = ?',
                [section.section_id]
            );

            if (questions[0].question_count === 0) {
                req.session.flashError = 'Each section must have at least one question';
                return res.redirect(`/tests/admin/${examId}/edit`);
            }
        }

        // Update test
        await db.query(`
            UPDATE exams
            SET exam_name = ?,
                description = ?,
                duration = ?,
                passing_marks = ?,
                max_attempts = ?,
                updated_at = NOW()
            WHERE exam_id = ? AND created_by = ?
        `, [exam_name, description, duration, passing_marks, max_attempts || 1, examId, req.session.userId]);

        // Log the update to the system logs
        const { logEvent } = require('../utils/logger');
        await logEvent(
            req,
            'info',
            'test',
            'Test Update',
            `Updated test ${examId} with name: ${exam_name}`,
            'success'
        );

        // Set flash and toast messages
        req.session.flashSuccess = 'Test updated successfully';
        req.session.toast = {
            type: 'success',
            title: 'Success',
            message: 'Test updated successfully'
        };

        // Check if this is an AJAX request
        if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
            // Respond with JSON for AJAX requests
            return res.json({
                success: true,
                message: 'Test updated successfully'
            });
        }

        // For regular form submissions, redirect back to the same page
        res.redirect(`/tests/admin/${examId}/edit`);
    } catch (error) {
        console.error('Error updating test:', error);

        // Log the error to the system logs
        const { logError } = require('../utils/logger');
        await logError(error, req);

        req.session.flashError = 'Error updating test';

        // Set toast notification in session for error
        req.session.toast = {
            type: 'error',
            title: 'Error',
            message: 'Failed to update test. Please try again.'
        };

        // Check if this is an AJAX request
        if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
            // Respond with JSON for AJAX requests
            return res.status(500).json({
                success: false,
                message: 'Failed to update test. Please try again.'
            });
        }

        res.redirect(`/tests/admin/${req.params.examId}/edit`);
    }
});

// Add section to test (admin only) - new route
router.post('/admin/sections/add', checkAdmin, async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const { exam_id, section_name } = req.body;

        if (!exam_id) {
            return res.status(400).json({
                success: false,
                message: 'Exam ID is required'
            });
        }

        if (!section_name || typeof section_name !== 'string' || section_name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Section name must be a non-empty string'
            });
        }

        const trimmedSectionName = section_name.trim().replace(/\s+/g, ' ');

        // Check if a section with this name already exists for this exam
        const [existingSections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ?',
            [exam_id]
        );

        const normalizedNewName = trimmedSectionName.toLowerCase();
        const hasDuplicate = existingSections.some(section => {
            const normalizedExistingName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');
            return normalizedExistingName === normalizedNewName;
        });

        if (hasDuplicate) {
            return res.status(400).json({
                success: false,
                message: 'A section with this name already exists. Please use a unique name.'
            });
        }

        // Get current highest position
        const [positions] = await db.query(
            'SELECT MAX(position) as max_pos FROM sections WHERE exam_id = ?',
            [exam_id]
        );

        const newPosition = (positions[0].max_pos || 0) + 1;

        // Insert new section with instructions and passing marks
        const { instructions, passing_marks } = req.body;

        // Validate passing marks if provided
        if (passing_marks && (isNaN(passing_marks) || passing_marks < 0)) {
            return res.status(400).json({
                success: false,
                message: 'Passing marks must be a non-negative number'
            });
        }

        const [result] = await db.query(
            'INSERT INTO sections (exam_id, section_name, instructions, passing_marks, position) VALUES (?, ?, ?, ?, ?)',
            [exam_id, trimmedSectionName, instructions || null, passing_marks || null, newPosition]
        );

        res.status(201).json({
            success: true,
            section_id: result.insertId,
            message: 'Section added successfully'
        });
    } catch (error) {
        console.error('Error adding section:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error adding section'
        });
    }
});

// Update section (admin only)
router.post('/admin/sections/:sectionId/update', checkAdmin, async (req, res) => {
    // This route is kept for backward compatibility
    return res.redirect(307, `/tests/admin/sections/${req.params.sectionId}/update`);
});

router.post('/tests/admin/sections/:sectionId/update', checkAdmin, async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const { sectionId } = req.params;
        const { section_name, instructions, passing_marks } = req.body;

        // Validate passing marks if provided
        if (passing_marks && (isNaN(passing_marks) || passing_marks < 0)) {
            return res.status(400).json({
                success: false,
                message: 'Passing marks must be a non-negative number'
            });
        }

        if (!section_name || typeof section_name !== 'string' || section_name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Section name must be a non-empty string'
            });
        }

        const trimmedSectionName = section_name.trim().replace(/\s+/g, ' ');

        // Get the exam_id for this section
        const [sections] = await db.query(
            'SELECT exam_id FROM sections WHERE section_id = ?',
            [sectionId]
        );

        if (sections.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Section not found'
            });
        }

        const examId = sections[0].exam_id;

        // Check if a section with this name already exists for this exam (excluding this section)
        const [existingSections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ? AND section_id != ?',
            [examId, sectionId]
        );

        const normalizedNewName = trimmedSectionName.toLowerCase();
        const hasDuplicate = existingSections.some(section => {
            const normalizedExistingName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');
            return normalizedExistingName === normalizedNewName;
        });

        if (hasDuplicate) {
            return res.status(400).json({
                success: false,
                message: 'A section with this name already exists. Please use a unique name.'
            });
        }

        // Get total marks for this section to validate passing marks
        if (passing_marks) {
            const [totalMarksResult] = await db.query(
                'SELECT SUM(COALESCE(marks, 1)) as total_marks FROM questions WHERE section_id = ?',
                [sectionId]
            );

            const totalMarks = totalMarksResult[0]?.total_marks || 0;

            if (parseFloat(passing_marks) > parseFloat(totalMarks)) {
                return res.status(400).json({
                    success: false,
                    message: `Passing marks (${passing_marks}) cannot be greater than total marks (${totalMarks})`
                });
            }
        }

        // Update section with instructions and passing marks
        await db.query(
            'UPDATE sections SET section_name = ?, instructions = ?, passing_marks = ? WHERE section_id = ?',
            [trimmedSectionName, instructions || null, passing_marks || null, sectionId]
        );

        res.status(200).json({
            success: true,
            message: 'Section updated successfully'
        });
    } catch (error) {
        console.error('Error updating section:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error updating section'
        });
    }
});

// Delete section (admin only)
router.post('/admin/sections/:sectionId/delete', checkAdmin, async (req, res) => {
    try {
        const { sectionId } = req.params;

        // Get the exam_id for this section
        const [sections] = await db.query(
            'SELECT exam_id FROM sections WHERE section_id = ?',
            [sectionId]
        );

        if (sections.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Section not found'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Delete all options for questions in this section
            await db.query(
                'DELETE FROM options WHERE question_id IN (SELECT question_id FROM questions WHERE section_id = ?)',
                [sectionId]
            );

            // Delete all questions in this section
            await db.query('DELETE FROM questions WHERE section_id = ?', [sectionId]);

            // Delete the section
            await db.query('DELETE FROM sections WHERE section_id = ?', [sectionId]);

            // Reorder remaining sections
            const [remainingSections] = await db.query(
                'SELECT section_id FROM sections WHERE exam_id = ? ORDER BY position',
                [sections[0].exam_id]
            );

            for (let i = 0; i < remainingSections.length; i++) {
                await db.query(
                    'UPDATE sections SET position = ? WHERE section_id = ?',
                    [i + 1, remainingSections[i].section_id]
                );
            }

            // Commit transaction
            await db.query('COMMIT');

            res.status(200).json({
                success: true,
                message: 'Section deleted successfully'
            });
        } catch (error) {
            // Rollback transaction
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error deleting section:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error deleting section'
        });
    }
});

// Add question to a section (admin only)
router.post('/admin/:examId/sections/:sectionId/questions/add', checkAdmin, async (req, res) => {
    try {
        const { examId, sectionId } = req.params;
        const { question_type, question_text, solution_text, correct_answer, options, linked_essay_id, question_image_url, solution_image_url } = req.body;

        // Validate inputs
        if (!question_text || typeof question_text !== 'string' || question_text.trim() === '') {
            return res.status(400).json({
                success: false,
                message: 'Question text must be a non-empty string'
            });
        }

        // Get current highest position
        const [positions] = await db.query(
            'SELECT MAX(position) as max_pos FROM questions WHERE section_id = ?',
            [sectionId]
        );

        const newPosition = (positions[0].max_pos || 0) + 1;

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Map the question type to the database enum values
            let dbQuestionType;
            if (question_type === 'mcq') {
                dbQuestionType = 'multiple_choice';
            } else if (question_type === 'true_false') {
                dbQuestionType = 'true_false';
            } else if (question_type === 'fill_up') {
                dbQuestionType = 'multiple_choice'; // Store fill-up as multiple_choice with one option
            } else if (question_type === 'essay') {
                dbQuestionType = 'essay';
            } else {
                dbQuestionType = 'multiple_choice'; // Default
            }

            // Insert question
            let result;
            // Prepare metadata object
            const metadata = {};
            if (linked_essay_id) {
                metadata.linked_essay_id = linked_essay_id;
            }
            if (question_image_url) {
                metadata.question_image_url = question_image_url;
            }
            if (solution_image_url) {
                metadata.solution_image_url = solution_image_url;
            }

            // Insert question with metadata
            [result] = await db.query(
                'INSERT INTO questions (section_id, question_type, question_text, solution_text, position, metadata, image_path) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [sectionId, dbQuestionType, question_text, solution_text || null, newPosition,
                 Object.keys(metadata).length > 0 ? JSON.stringify(metadata) : null,
                 question_image_url || null]
            );

            const questionId = result.insertId;

            // Handle options based on question type
            if (question_type === 'mcq') {
                // Insert options for MCQ
                for (let i = 0; i < options.length; i++) {
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                        [questionId, options[i], i.toString() === correct_answer ? 1 : 0]
                    );
                }
            } else if (question_type === 'true_false') {
                // Insert options for True/False
                await db.query(
                    'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                    [questionId, 'True', correct_answer === 'true' ? 1 : 0]
                );
                await db.query(
                    'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                    [questionId, 'False', correct_answer === 'false' ? 1 : 0]
                );
            } else if (question_type === 'fill_up') {
                // Insert correct answer for fill-up
                await db.query(
                    'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                    [questionId, correct_answer, 1]
                );
            } else if (question_type === 'essay') {
                // For essay questions, store the metadata in options
                if (options && options.length > 0) {
                    try {
                        const essayOptions = JSON.parse(options[0]);
                        await db.query(
                            'INSERT INTO options (question_id, option_text, is_correct, metadata) VALUES (?, ?, ?, ?)',
                            [questionId, 'Essay', 1, JSON.stringify(essayOptions)]
                        );
                    } catch (error) {
                        console.error('Error parsing essay options:', error);
                        await db.query(
                            'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                            [questionId, 'Essay', 1]
                        );
                    }
                } else {
                    await db.query(
                        'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                        [questionId, 'Essay', 1]
                    );
                }
            }

            // Commit transaction
            await db.query('COMMIT');

            res.status(201).json({
                success: true,
                question_id: questionId,
                message: 'Question added successfully'
            });
        } catch (error) {
            // Rollback transaction
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error adding question:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error adding question'
        });
    }
});

// Get questions for a section (admin only)
router.get('/admin/sections/:sectionId/questions', checkAdmin, async (req, res) => {
    try {
        const { sectionId } = req.params;

        // Get section details
        const [sections] = await db.query(`
            SELECT s.*, e.exam_name, e.exam_id
            FROM sections s
            JOIN exams e ON s.exam_id = e.exam_id
            WHERE s.section_id = ?
        `, [sectionId]);

        if (sections.length === 0) {
            req.session.flashError = 'Section not found';
            return res.redirect('/tests/admin');
        }

        const section = sections[0];

        // Get questions for this section
        const [questions] = await db.query(`
            SELECT q.*,
                   (SELECT COUNT(*) FROM options WHERE question_id = q.question_id) as option_count,
                   GROUP_CONCAT(DISTINCT c.name) as category_names,
                   GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE q.section_id = ?
            GROUP BY q.question_id
            ORDER BY q.position
        `, [sectionId]);

        // Get categories for the add question modal
        const [categories] = await db.query(`
            SELECT category_id, name, color
            FROM categories
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            ORDER BY name
        `);

        // Get essays for the add question modal
        const [essays] = await db.query(`
            SELECT e.essay_id, e.title, e.created_at,
                   (SELECT COUNT(*) FROM questions WHERE essay_id = e.essay_id) as question_count,
                   DATE_FORMAT(e.created_at, '%d-%b-%Y') as formatted_date
            FROM essays e
            WHERE (e.is_deleted = 0 OR e.is_deleted IS NULL)
            ORDER BY e.created_at DESC
        `);

        // Render the questions page
        res.render('admin/tests/section-questions', {
            title: `Questions for ${section.section_name}`,
            section,
            questions,
            categories,
            essays,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            layout: 'admin'
        });

        // Clear flash messages
        req.session.flashSuccess = null;
        req.session.flashError = null;
    } catch (error) {
        console.error('Error loading section questions:', error);
        req.session.flashError = 'Error loading section questions';
        res.redirect('/tests/admin');
    }
});

// Delete test (admin only)
router.post('/admin/:examId/delete', checkAdmin, async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const { examId } = req.params;

        // Check if test exists and belongs to user
        const [tests] = await db.query(
            'SELECT exam_id FROM exams WHERE exam_id = ? AND created_by = ?',
            [examId, req.session.userId]
        );

        if (tests.length === 0) {
            req.session.flashError = 'Test not found or unauthorized';
            return res.redirect('/tests/admin');
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Delete all related records in correct order
            await db.query('DELETE FROM options WHERE question_id IN (SELECT question_id FROM questions WHERE section_id IN (SELECT section_id FROM sections WHERE exam_id = ?))', [examId]);
            await db.query('DELETE FROM questions WHERE section_id IN (SELECT section_id FROM sections WHERE exam_id = ?)', [examId]);
            await db.query('DELETE FROM sections WHERE exam_id = ?', [examId]);
            await db.query('DELETE FROM exam_attempts WHERE exam_id = ?', [examId]);
            await db.query('DELETE FROM exams WHERE exam_id = ? AND created_by = ?', [examId, req.session.userId]);

            // Commit transaction
            await db.query('COMMIT');

            req.session.flashSuccess = 'Test deleted successfully';
            res.redirect('/admin/tests');
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            console.error('Error deleting test:', error);
            req.session.flashError = 'Error deleting test';
            res.redirect('/tests/admin');
        }
    } catch (error) {
        console.error('Error deleting test:', error);
        req.session.flashError = 'Error deleting test';
        res.redirect('/tests/admin');
    }
});
// Route for saving drafts
router.post('/save-draft', checkAdmin, async (req, res) => {
    try {
        const { exam_id, exam_name, instructions, duration, passing_score, description, sections, is_resumable } = req.body;

        // Log the request body for debugging
        console.log('Request body for /admin/save-draft:', req.body);

        // Validate required fields
        if (!exam_name) {
            return res.status(400).json({ success: false, message: 'Exam name is required' });
        }

        let examId = exam_id;

        // If no exam_id is provided, create a new exam
        if (!examId) {
            console.log('No exam_id provided, creating new exam');
            try {
                // Begin transaction
                await db.query('START TRANSACTION');

                // Check for duplicate exam name
                const [nameCheck] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ?', [exam_name]);
                if (nameCheck.length > 0) {
                    await db.query('ROLLBACK');
                    return res.status(400).json({
                        success: false,
                        message: `An exam with the name "${exam_name}" already exists. Please choose a different name.`
                    });
                }

                // Convert is_resumable to a boolean value (0 or 1)
                const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

                // Insert the new test with minimal fields
                const [result] = await db.query(
                    'INSERT INTO exams (exam_name, created_by, status, description, is_resumable) VALUES (?, ?, "draft", ?, ?)',
                    [exam_name, req.session.userId, description || null, isResumable] // User ID from session
                );

                examId = result.insertId;
                console.log('Created new exam with ID:', examId);

                // Commit transaction
                await db.query('COMMIT');
            } catch (error) {
                await db.query('ROLLBACK');
                throw error;
            }
        }

        // Check if the exam exists (only if exam_id was provided)
        if (exam_id) {
            const [examCheck] = await db.query('SELECT exam_id FROM exams WHERE exam_id = ?', [exam_id]);
            if (examCheck.length === 0) {
                return res.status(404).json({ success: false, message: 'Exam not found' });
            }
        }

        // Filter valid sections: must have a non-empty section_name
        const validSections = sections ? sections.filter(section =>
            (section.name || section.section_title) &&
            (section.name || section.section_title).trim() !== ''
        ) : [];

        // Start a transaction for data integrity
        await db.query('START TRANSACTION');

        try {
            // Check if the updated name conflicts with another exam
            if (exam_id) {
                const [nameCheck] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ? AND exam_id != ?', [exam_name, examId]);
                if (nameCheck.length > 0) {
                    await db.query('ROLLBACK');
                    return res.status(400).json({
                        success: false,
                        message: `An exam with the name "${exam_name}" already exists. Please choose a different name.`
                    });
                }
            }

            // Convert is_resumable to a boolean value (0 or 1)
            const isResumable = is_resumable === 'on' || is_resumable === true || is_resumable === '1' || is_resumable === 1 ? 1 : 0;

            // Update exam details
            await db.query(
                'UPDATE exams SET exam_name = ?, instructions = ?, duration = ?, passing_marks = ?, is_resumable = ? WHERE exam_id = ?',
                [exam_name, instructions || null, duration || null, passing_score || null, isResumable, examId]
            );

            // Handle valid sections if provided
            if (validSections.length > 0) {
                console.log('Number of valid sections to process:', validSections.length);

                // Fetch existing sections
                const [existingSections] = await db.query('SELECT section_id FROM sections WHERE exam_id = ?', [examId]);
                const existingSectionIds = existingSections.map((s) => s.section_id);
                console.log('Existing section IDs:', existingSectionIds);

                // Process each valid section
                for (const [index, section] of validSections.entries()) {
                    let sectionId;

                    if (index < existingSectionIds.length) {
                        // Update existing section
                        sectionId = existingSectionIds[index];
                        await db.query(
                            'UPDATE sections SET section_name = ?, position = ?, instructions = ?, passing_marks = ? WHERE section_id = ?',
                            [section.name || section.section_title, index, section.instructions || null, section.passing_marks || null, sectionId]
                        );
                    } else {
                        // Insert new section
                        const [result] = await db.query(
                            'INSERT INTO sections (exam_id, section_name, position, instructions, passing_marks) VALUES (?, ?, ?, ?, ?)',
                            [examId, section.name || section.section_title, index, section.instructions || null, section.passing_marks || null]
                        );
                        sectionId = result.insertId;
                    }

                    // Process questions if provided in the section
                    if (section.questions && Array.isArray(section.questions)) {
                        // Get existing questions for this section
                        const [existingQuestions] = await db.query('SELECT question_id FROM questions WHERE section_id = ?', [sectionId]);
                        const existingQuestionIds = existingQuestions.map(q => q.question_id);
                        console.log(`Section ${sectionId} has ${existingQuestionIds.length} existing questions`);

                        // Process each question
                        for (const [qIndex, question] of section.questions.entries()) {
                            const { question_id, question_text, question_type, options, correct_answer, marks, solution } = question;

                            // Skip if question text is empty
                            if (!question_text || question_text.trim() === '') {
                                console.log('Skipping question with empty text');
                                continue;
                            }

                            let questionId = question_id;

                            // If question_id exists and is in our existing questions, update it
                            if (questionId && existingQuestionIds.includes(parseInt(questionId))) {
                                console.log(`Updating existing question ${questionId}`);
                                await db.query(
                                    `UPDATE questions
                                     SET question_text = ?,
                                         question_type = ?,
                                         correct_answer = ?,
                                         marks = ?,
                                         solution_text = ?
                                     WHERE question_id = ? AND section_id = ?`,
                                    [question_text, question_type || 'mcq', correct_answer || '', marks || 1, solution || '', questionId, sectionId]
                                );

                                // Remove from existing IDs list so we know it's been processed
                                const idIndex = existingQuestionIds.indexOf(parseInt(questionId));
                                if (idIndex > -1) {
                                    existingQuestionIds.splice(idIndex, 1);
                                }
                            } else if (qIndex < existingQuestionIds.length) {
                                // Update an existing question by position
                                questionId = existingQuestionIds[qIndex];
                                console.log(`Updating question at position ${qIndex}, ID: ${questionId}`);
                                await db.query(
                                    `UPDATE questions
                                     SET question_text = ?,
                                         question_type = ?,
                                         correct_answer = ?,
                                         marks = ?,
                                         solution_text = ?
                                     WHERE question_id = ?`,
                                    [question_text, (question_type === 'mcq' ? 'multiple_choice' : question_type) || 'multiple_choice', correct_answer || '', marks || 1, solution || '', questionId]
                                );

                                // Remove from existing IDs list
                                existingQuestionIds.splice(qIndex, 1);
                            } else {
                                // Insert new question
                                console.log(`Inserting new question: ${question_text.substring(0, 30)}...`);
                                const [qResult] = await db.query(
                                    `INSERT INTO questions
                                     (section_id, question_text, question_type, correct_answer, marks, solution_text)
                                     VALUES (?, ?, ?, ?, ?, ?)`,
                                    [sectionId, question_text, (question_type === 'mcq' ? 'multiple_choice' : question_type) || 'multiple_choice', correct_answer || '', marks || 1, solution || '']
                                );
                                questionId = qResult.insertId;
                                console.log('Question inserted with ID:', questionId);
                            }

                            // Process options if this is an MCQ or T/F question
                            if ((question_type === 'mcq' || question_type === 'multiple_choice' || question_type === 'true_false') && options && Array.isArray(options)) {
                                // Get existing options for this question
                                const [existingOptions] = await db.query('SELECT id FROM options WHERE question_id = ?', [questionId]);
                                const existingOptionIds = existingOptions.map(o => o.id);

                                // Process each option
                                for (const [oIndex, option] of options.entries()) {
                                    const { option_id, option_text, is_correct } = option;

                                    // Skip if option text is empty
                                    if (!option_text || option_text.trim() === '') {
                                        continue;
                                    }

                                    if (option_id && existingOptionIds.includes(parseInt(option_id))) {
                                        // Update existing option
                                        await db.query(
                                            'UPDATE options SET option_text = ?, is_correct = ? WHERE id = ?',
                                            [option_text, is_correct ? 1 : 0, option_id]
                                        );

                                        // Remove from existing IDs list
                                        const idIndex = existingOptionIds.indexOf(parseInt(option_id));
                                        if (idIndex > -1) {
                                            existingOptionIds.splice(idIndex, 1);
                                        }
                                    } else if (oIndex < existingOptionIds.length) {
                                        // Update option by position
                                        const optionId = existingOptionIds[oIndex];
                                        await db.query(
                                            'UPDATE options SET option_text = ?, is_correct = ? WHERE id = ?',
                                            [option_text, is_correct ? 1 : 0, optionId]
                                        );

                                        // Remove from existing IDs list
                                        existingOptionIds.splice(oIndex, 1);
                                    } else {
                                        // Insert new option
                                        await db.query(
                                            'INSERT INTO options (question_id, option_text, is_correct) VALUES (?, ?, ?)',
                                            [questionId, option_text, is_correct ? 1 : 0]
                                        );
                                    }
                                }

                                // Delete any remaining options that weren't updated
                                if (existingOptionIds.length > 0) {
                                    await db.query(
                                        'DELETE FROM options WHERE id IN (?)',
                                        [existingOptionIds]
                                    );
                                }
                            }
                        }

                        // Delete any remaining questions that weren't updated
                        if (existingQuestionIds.length > 0) {
                            console.log(`Deleting ${existingQuestionIds.length} unused questions`);
                            for (const qId of existingQuestionIds) {
                                // Delete options first
                                await db.query('DELETE FROM options WHERE question_id = ?', [qId]);
                                // Then delete the question
                                await db.query('DELETE FROM questions WHERE question_id = ?', [qId]);
                            }
                        }
                    }
                }
            }

            // Commit the transaction
            await db.query('COMMIT');
            res.status(200).json({ success: true, exam_id: examId, message: 'Draft saved successfully' });
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error saving draft:', error);
        res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
    }
});

// Fetch logs
router.get('/admin/logs', checkAdmin, async (req, res) => {
    try {
        const [logs] = await db.query('SELECT * FROM logs ORDER BY timestamp DESC');
        res.json({ success: true, logs });
    } catch (error) {
        await logError(error, req);
        res.status(500).json({ success: false, message: 'Failed to fetch logs', error: error.message });
    }
});
router.get('/search/group-member', async (req, res) => {
    const query = req.query.q;
    try {
        const [results] = await db.query(`
            SELECT id, CONCAT(first_name, ' ', last_name) AS name
            FROM users
            WHERE first_name LIKE ? OR last_name LIKE ?
        `, [`%${query}%`, `%${query}%`]);
        res.json(results);
    } catch (error) {
        console.error('Error searching groups/members:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});
// Add test name validation endpoint
router.post('/admin/tests/check-name', checkAdmin, async (req, res) => {
    try {
        const { exam_name } = req.body;
        const [result] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ?', [exam_name]);
        res.json({ available: result.length === 0 });
    } catch (error) {
        console.error('Error checking test name:', error);
        res.status(500).json({ error: 'Failed to check test name' });
    }
});
router.post('/admin/tests/simulate', checkAdmin, (req, res) => {
    // Extract form data from the request body
    const formData = req.body;

    // Get username from session for display in the simulation
    const username = req.session.username || 'Guest';

    // Render the simulation template with form data and username
    res.render('simulation-template', { formData, username });
});
// Move test name validation endpoint
router.post('/admin/check-test-name', checkAdmin, async (req, res) => {
    try {
        const { exam_name } = req.body;
        const [result] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ?', [exam_name]);
        res.json({ available: result.length === 0 });
    } catch (error) {
        console.error('Error checking test name:', error);
        res.status(500).json({ error: 'Failed to check test name' });
    }
});

router.post('/tests/admin/log-change', checkAdmin, async (req, res) => {
    try {
        const { timestamp, message, page } = req.body;
        const logEntry = `[${timestamp}] ${page}: ${message}\n`;

        // Create logs directory if it doesn't exist
        const logsDir = path.join(__dirname, '../logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir);
        }

        // Log to changelog file
        await fs.promises.appendFile(
            path.join(logsDir, 'changelog.log'),
            logEntry
        );

        res.json({ success: true });
    } catch (error) {
        console.error('Error logging change:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to log change'
        });
    }
});

// Log changes endpoint
router.post('/admin/log-change', checkAdmin, async (req, res) => {
    try {
        const { timestamp, message, page } = req.body;

        // Validate required fields
        if (!message) {
            return res.status(400).json({
                success: false,
                message: 'Message is required'
            });
        }

        // Create logs directory if it doesn't exist
        const logsDir = path.join(__dirname, '..', 'logs');
        await fs.mkdir(logsDir, { recursive: true });

        // Create log entry
        const logEntry = {
            timestamp: timestamp || new Date().toISOString(),
            message,
            page: page || 'unknown',
            userId: req.session.userId,
            username: req.session.username
        };

        // Write to log file
        const logFile = path.join(logsDir, 'test-changes.log');
        await fs.appendFile(
            logFile,
            JSON.stringify(logEntry) + '\n',
            'utf8'
        );

        res.json({
            success: true,
            message: 'Change logged successfully'
        });
    } catch (error) {
        console.error('Error logging change:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to log change',
            error: error.message
        });
    }
});

// Tests index route
router.get('/', checkAuthenticated, async (req, res) => {
    try {
        const userId = req.session.userId;

        // Get all tests assigned to this user directly
        const [userAssignments] = await db.query(`
            SELECT ta.*, e.exam_name, e.description, e.duration, e.status,
                   (SELECT COUNT(*) FROM questions q
                    JOIN sections s ON q.section_id = s.section_id
                    WHERE s.exam_id = e.exam_id) as question_count,
                   (SELECT attempt_id FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as in_progress_attempt_id,
                   (SELECT start_time FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as start_time,
                   (SELECT max_attempts FROM test_assignments
                    WHERE exam_id = e.exam_id AND user_id = ?
                    ORDER BY assigned_at DESC LIMIT 1) as max_attempts,
                   (SELECT is_resumable FROM test_assignments
                    WHERE exam_id = e.exam_id AND user_id = ?
                    ORDER BY assigned_at DESC LIMIT 1) as is_resumable,
                   (SELECT COUNT(*) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as completed_attempts,
                   (SELECT MAX(assigned_at) FROM test_assignments
                    WHERE exam_id = e.exam_id AND user_id = ?) as latest_assignment_date,
                   (SELECT MAX(attempt_date) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as latest_attempt_date,
                   ta.assigned_by,
                   (SELECT username FROM users WHERE id = ta.assigned_by) as assigned_by_username
            FROM test_assignments ta
            JOIN exams e ON ta.exam_id = e.exam_id
            WHERE ta.user_id = ? AND ta.is_active = 1
            AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
            AND e.status = 'published'
            AND (e.publish_date IS NULL OR e.publish_date <= NOW())
            AND (e.end_date IS NULL OR e.end_date >= NOW())
            AND e.is_active = 1
            AND (e.is_deleted = 0 OR e.is_deleted IS NULL)
        `, [userId, userId, userId, userId, userId, userId, userId, userId]);

        // Get all tests assigned to groups this user belongs to
        const [groupAssignments] = await db.query(`
            SELECT ta.*, e.exam_name, e.description, e.duration, e.status, g.name as group_name,
                   (SELECT COUNT(*) FROM questions q
                    JOIN sections s ON q.section_id = s.section_id
                    WHERE s.exam_id = e.exam_id) as question_count,
                   (SELECT attempt_id FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as in_progress_attempt_id,
                   (SELECT start_time FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as start_time,
                   (SELECT max_attempts FROM test_assignments
                    WHERE exam_id = e.exam_id AND group_id = g.group_id
                    ORDER BY assigned_at DESC LIMIT 1) as max_attempts,
                   (SELECT is_resumable FROM test_assignments
                    WHERE exam_id = e.exam_id AND group_id = g.group_id
                    ORDER BY assigned_at DESC LIMIT 1) as is_resumable,
                   (SELECT COUNT(*) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as completed_attempts,
                   (SELECT MAX(assigned_at) FROM test_assignments
                    WHERE exam_id = e.exam_id AND group_id = g.group_id) as latest_assignment_date,
                   (SELECT MAX(attempt_date) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as latest_attempt_date,
                   ta.assigned_by,
                   (SELECT username FROM users WHERE id = ta.assigned_by) as assigned_by_username
            FROM test_assignments ta
            JOIN exams e ON ta.exam_id = e.exam_id
            JOIN groups g ON ta.group_id = g.group_id
            JOIN group_members gm ON g.group_id = gm.group_id
            WHERE gm.user_id = ? AND ta.is_active = 1
            AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
            AND e.status = 'published'
            AND (e.publish_date IS NULL OR e.publish_date <= NOW())
            AND (e.end_date IS NULL OR e.end_date >= NOW())
            AND e.is_active = 1
            AND (e.is_deleted = 0 OR e.is_deleted IS NULL)
        `, [userId, userId, userId, userId, userId, userId]);

        // We no longer show public tests that aren't assigned to the user
        // All tests must be assigned to either the user directly or to a group they belong to
        const publicTests = [];

        // Get all tests that are assigned to the user or their groups but not already included
        // This is to ensure assigned tests show up in the available tests list
        const [assignedTests] = await db.query(`
            SELECT DISTINCT e.*,
                   (SELECT COUNT(*) FROM questions q
                    JOIN sections s ON q.section_id = s.section_id
                    WHERE s.exam_id = e.exam_id) as question_count,
                   (SELECT attempt_id FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as in_progress_attempt_id,
                   (SELECT start_time FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status = 'in_progress'
                    LIMIT 1) as start_time,
                   COALESCE(
                       (SELECT max_attempts FROM test_assignments
                        WHERE exam_id = e.exam_id AND user_id = ?
                        ORDER BY assigned_at DESC LIMIT 1),
                       (SELECT max_attempts FROM test_assignments
                        WHERE exam_id = e.exam_id AND group_id IN (
                            SELECT group_id FROM group_members WHERE user_id = ?
                        ) ORDER BY assigned_at DESC LIMIT 1),
                       e.max_attempts,
                       1
                   ) as max_attempts,
                   COALESCE(
                       (SELECT is_resumable FROM test_assignments
                        WHERE exam_id = e.exam_id AND user_id = ?
                        ORDER BY assigned_at DESC LIMIT 1),
                       (SELECT is_resumable FROM test_assignments
                        WHERE exam_id = e.exam_id AND group_id IN (
                            SELECT group_id FROM group_members WHERE user_id = ?
                        ) ORDER BY assigned_at DESC LIMIT 1),
                       0
                   ) as is_resumable,
                   (SELECT COUNT(*) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as completed_attempts,
                   (SELECT MAX(assigned_at) FROM test_assignments
                    WHERE exam_id = e.exam_id AND (user_id = ? OR group_id IN (
                        SELECT group_id FROM group_members WHERE user_id = ?
                    ))) as latest_assignment_date,
                   (SELECT MAX(attempt_date) FROM exam_attempts
                    WHERE exam_id = e.exam_id AND user_id = ? AND exam_attempts.status != 'in_progress') as latest_attempt_date,
                   1 as is_assigned,
                   (SELECT assigned_by FROM test_assignments
                    WHERE exam_id = e.exam_id AND (user_id = ? OR group_id IN (
                        SELECT group_id FROM group_members WHERE user_id = ?
                    ))
                    ORDER BY assigned_at DESC LIMIT 1) as assigned_by,
                   (SELECT username FROM users WHERE id = (
                        SELECT assigned_by FROM test_assignments
                        WHERE exam_id = e.exam_id AND (user_id = ? OR group_id IN (
                            SELECT group_id FROM group_members WHERE user_id = ?
                        ))
                        ORDER BY assigned_at DESC LIMIT 1
                   )) as assigned_by_username
            FROM exams e
            JOIN test_assignments ta ON e.exam_id = ta.exam_id
            WHERE e.status = 'published'
            AND (e.publish_date IS NULL OR e.publish_date <= NOW())
            AND (e.end_date IS NULL OR e.end_date >= NOW())
            AND e.is_active = 1
            AND (e.is_deleted = 0 OR e.is_deleted IS NULL)
            AND ta.is_active = 1
            AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
            AND (ta.user_id = ? OR ta.group_id IN (
                SELECT group_id FROM group_members WHERE user_id = ?
            ))
            AND e.exam_id NOT IN (
                ${userAssignments.map(a => a.exam_id).join(',') || '0'},
                ${groupAssignments.map(a => a.exam_id).join(',') || '0'}
            )
            ORDER BY e.created_at DESC
        `, [userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId, userId]);

        // Combine all tests, marking assigned ones
        const userAssignmentsWithSource = userAssignments.map(test => ({
            ...test,
            is_assigned: 1,
            assignment_type: 'user'
        }));

        const groupAssignmentsWithSource = groupAssignments.map(test => ({
            ...test,
            is_assigned: 1,
            assignment_type: 'group'
        }));

        // Combine all tests
        const tests = [
            ...userAssignmentsWithSource,
            ...groupAssignmentsWithSource,
            ...assignedTests,
            ...publicTests
        ];

        // Debug log for tests
        tests.forEach(test => {
            // Ensure max_attempts is a number and not null
            test.max_attempts = parseInt(test.max_attempts) || 1;
            test.completed_attempts = parseInt(test.completed_attempts) || 0;

            console.log(`Test ${test.exam_id} - ${test.exam_name}:`);
            console.log(`  Max attempts: ${test.max_attempts}`);
            console.log(`  Completed attempts: ${test.completed_attempts}`);
            console.log(`  Remaining attempts: ${test.max_attempts - test.completed_attempts}`);
            console.log(`  Latest assignment date: ${test.latest_assignment_date}`);
            console.log(`  Latest attempt date: ${test.latest_attempt_date}`);

            // Check if test has been reassigned after the last attempt
            const hasBeenReassigned = test.latest_assignment_date && test.latest_attempt_date &&
                                    new Date(test.latest_assignment_date) > new Date(test.latest_attempt_date);
            console.log(`  Has been reassigned: ${hasBeenReassigned}`);
        });

        // Get user's recent test attempts (only completed ones, not in-progress)
        const [attempts] = await db.query(`
            SELECT ea.*, e.exam_name, e.passing_marks,
                   TIMESTAMPDIFF(MINUTE, ea.start_time, ea.end_time) as duration_minutes,
                   (SELECT duration_formatted FROM attempt_statistics WHERE attempt_id = ea.attempt_id) as duration_formatted,
                   COALESCE(ea.continuation_count, 0) as continuation_count
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ?
            AND ea.status != 'in_progress'
            ORDER BY ea.attempt_date DESC
            LIMIT 5
        `, [req.session.userId]);

        res.render('tests/index', {
            title: 'Available Tests',
            tests,
            attempts,
            currentPage: 'tests',
            navbar: 'user',
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error fetching tests:', error);
        res.status(500).send('Error fetching tests');
    }
});

// Take test route
router.get('/take/:examId', checkAuthenticated, async (req, res, next) => {
    try {
        const { examId } = req.params;
        const userId = req.session.userId;

        // Check if exam exists and is not soft-deleted
        const [exams] = await db.query(
            'SELECT *, COALESCE(is_resumable, 0) as is_resumable FROM exams WHERE exam_id = ? AND (is_deleted = 0 OR is_deleted IS NULL)',
            [examId]
        );

        if (exams.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/tests');
        }

        // Check for test assignments for this user
        const [userAssignments] = await db.query(
            'SELECT * FROM test_assignments WHERE exam_id = ? AND user_id = ? AND is_active = 1 AND (end_datetime IS NULL OR end_datetime > NOW()) ORDER BY assigned_at DESC',
            [examId, userId]
        );

        // Check for group assignments for this user
        const [groupAssignments] = await db.query(
            `SELECT ta.*
             FROM test_assignments ta
             JOIN group_members gm ON ta.group_id = gm.group_id
             WHERE ta.exam_id = ? AND gm.user_id = ? AND ta.is_active = 1
             AND (ta.end_datetime IS NULL OR ta.end_datetime > NOW())
             ORDER BY ta.assigned_at DESC`,
            [examId, userId]
        );

        // Determine max attempts from assignments or default to exam setting
        let maxAttempts = exams[0].max_attempts || 1;
        console.log('Default max attempts from exam:', maxAttempts);

        // If there's a user-specific assignment, it takes precedence
        if (userAssignments.length > 0) {
            console.log('User assignments found:', userAssignments.length);
            console.log('User assignment max attempts:', userAssignments[0].max_attempts);
            // Ensure max_attempts is a number and not null
            maxAttempts = parseInt(userAssignments[0].max_attempts) || 1;
        }
        // Otherwise, if there's a group assignment, use that
        else if (groupAssignments.length > 0) {
            console.log('Group assignments found:', groupAssignments.length);
            // If user is in multiple groups with assignments, use the highest max_attempts
            // Ensure each max_attempts is a number and not null
            maxAttempts = Math.max(...groupAssignments.map(a => parseInt(a.max_attempts) || 1));
            console.log('Group assignment max attempts:', maxAttempts);
        }

        // Check if user has reached the maximum number of attempts
        // Only count completed attempts (not in-progress ones)
        const [attemptCounts] = await db.query(
            'SELECT COUNT(*) as completed_count, MAX(attempt_date) as latest_attempt_date FROM exam_attempts WHERE exam_id = ? AND user_id = ? AND exam_attempts.status != "in_progress"',
            [examId, userId]
        );

        // Get the latest assignment date
        const [assignmentDates] = await db.query(
            `SELECT MAX(assigned_at) as latest_assignment_date FROM test_assignments
             WHERE exam_id = ? AND (user_id = ? OR group_id IN (
                 SELECT group_id FROM group_members WHERE user_id = ?
             ))`,
            [examId, userId, userId]
        );

        const completedAttempts = attemptCounts[0].completed_count;
        const latestAttemptDate = attemptCounts[0].latest_attempt_date;
        const latestAssignmentDate = assignmentDates[0].latest_assignment_date;

        console.log('Completed attempts:', completedAttempts);
        console.log('Max attempts:', maxAttempts);
        console.log('Remaining attempts:', maxAttempts - completedAttempts);
        console.log('Latest attempt date:', latestAttemptDate);
        console.log('Latest assignment date:', latestAssignmentDate);

        // Check if user has any remaining attempts
        const remainingAttempts = maxAttempts - completedAttempts;
        console.log(`User has ${remainingAttempts} remaining attempts`);

        if (remainingAttempts <= 0) {
            // Check if there's an access request option
            const [accessRequests] = await db.query(
                'SELECT * FROM access_requests WHERE exam_id = ? AND user_id = ? ORDER BY requested_at DESC',
                [examId, userId]
            );

            // Check if there's a pending request
            const pendingRequests = accessRequests.filter(req => req.status === 'pending');

            // Check if there's a recently approved request that might not be reflected in the attempts count yet
            const recentlyApprovedRequests = accessRequests.filter(req => {
                return req.status === 'approved' &&
                       req.processed_at &&
                       new Date(req.processed_at) > new Date(latestAttemptDate || '2000-01-01');
            });

            console.log('Pending requests:', pendingRequests.length);
            console.log('Recently approved requests:', recentlyApprovedRequests.length);

            // If there's a recently approved request, allow access
            if (recentlyApprovedRequests.length > 0) {
                console.log('User has recently approved access request - allowing access');
                // Continue with the test - don't redirect
            }
            // If there's a pending request, show pending message
            else if (pendingRequests.length > 0) {
                req.session.flashError = `You have reached the maximum number of attempts (${maxAttempts}) for this test. Your request for additional attempts is pending approval.`;
                return res.redirect('/tests');
            }
            // Otherwise, show request access message
            else {
                req.session.flashError = `You have reached the maximum number of attempts (${maxAttempts}) for this test. <a href="/tests/request-access/${examId}" class="text-blue-600 hover:underline">Request additional attempts</a>`;
                return res.redirect('/tests');
            }
        }

        const exam = exams[0];

        // Check if test is published and available
        const now = new Date();
        const publishDate = exam.publish_date ? new Date(exam.publish_date) : null;
        const endDate = exam.end_date ? new Date(exam.end_date) : null;

        // Determine test status
        let testStatus = 'ongoing';
        if (publishDate && now < publishDate) {
            testStatus = 'upcoming';
            req.session.flashError = 'This test is not yet available';
            return res.redirect('/tests');
        } else if (endDate && now > endDate) {
            testStatus = 'expired';
            req.session.flashError = 'This test has expired';
            return res.redirect('/tests');
        }

        // Check if user already has an ongoing attempt
        const [attempts] = await db.query(
            'SELECT * FROM exam_attempts WHERE exam_id = ? AND user_id = ? AND status = "in_progress"',
            [examId, userId]
        );

        let attemptId;

        if (attempts.length > 0) {
            // Use existing attempt
            attemptId = attempts[0].attempt_id;
        }
        // We don't create a new attempt here anymore - we'll create it when the user clicks Start Test

        // Get sections
        const [sections] = await db.query(
            'SELECT * FROM sections WHERE exam_id = ? ORDER BY position',
            [examId]
        );

        // Get all questions for the exam, including essay content if linked
        const [questions] = await db.query(`
            SELECT q.*, s.section_name as section_name, s.position as section_position,
                   e.essay_id, e.title as essay_title, e.content as essay_content, e.pdf_path as essay_pdf_path,
                   qi1.file_path as image_path,
                   qi2.file_path as solution_image_path,
                   GROUP_CONCAT(DISTINCT c.name) as category_names,
                   GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN essays e ON q.essay_id = e.essay_id
            LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
            LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE s.exam_id = ?
            GROUP BY q.question_id, s.section_name, s.position, e.essay_id, e.title, e.content, e.pdf_path
            ORDER BY s.position, q.position
        `, [examId]);

        // Get user answers for this attempt
        const [userAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ?',
            [attemptId]
        );

        // Convert to a map for easy lookup
        const answersMap = {};
        const bookmarksMap = {};

        userAnswers.forEach(answer => {
            answersMap[answer.question_id] = answer.selected_option_id || answer.answer_text || answer.essay_answer || '';
            bookmarksMap[answer.question_id] = answer.is_bookmarked === 1;
        });

        // Format questions for rendering
        const formattedQuestions = [];

        // Process each question and fetch options if needed
        for (const question of questions) {
            let options = [];

            // For MCQ questions, fetch options
            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                const [optionResults] = await db.query(
                    'SELECT * FROM options WHERE question_id = ? ORDER BY position',
                    [question.question_id]
                );
                options = optionResults;
            }

            // Process category names
            let categoryNames = [];
            if (question.category_names) {
                categoryNames = question.category_names.split(',').map(name => name.trim()).filter(Boolean);
            }

            formattedQuestions.push({
                ...question,
                options,
                user_answer: answersMap[question.question_id] || '',
                is_bookmarked: bookmarksMap[question.question_id] || false,
                category_names: categoryNames
            });
        }

        // Group questions by section
        const sectionMap = {};
        formattedQuestions.forEach(question => {
            if (!sectionMap[question.section_id]) {
                sectionMap[question.section_id] = {
                    section_id: question.section_id,
                    name: question.section_name,
                    position: question.section_position,
                    questions: []
                };
            }
            sectionMap[question.section_id].questions.push(question);
        });

        const formattedSections = Object.values(sectionMap);

        res.render('tests/take_new', {
            title: exam.exam_name,
            exam,
            sections: formattedSections,
            attemptId,
            testStatus,
            attempts, // Pass the attempt information to the template
            currentPage: 'tests',
            navbar: 'tests'
        });
    } catch (error) {
        console.error('Error loading test for taking:', error);
        next(error);
    }
});

// Continue test with existing attempt ID
router.get('/take/:examId/:attemptId', checkAuthenticated, async (req, res, next) => {
    try {
        const { examId, attemptId } = req.params;
        const userId = req.session.userId;

        // Check if exam exists and is not soft-deleted
        const [exams] = await db.query(
            'SELECT *, COALESCE(is_resumable, 0) as is_resumable FROM exams WHERE exam_id = ? AND (is_deleted = 0 OR is_deleted IS NULL)',
            [examId]
        );

        if (exams.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/tests');
        }

        const exam = exams[0];

        // Validate that the attempt belongs to the user and is in progress or paused
        const [attempts] = await db.query(
            'SELECT * FROM exam_attempts WHERE attempt_id = ? AND exam_id = ? AND user_id = ? AND (exam_attempts.status = "in_progress" OR exam_attempts.status = "paused")',
            [attemptId, examId, userId]
        );

        // Check if the attempt exists
        const [attemptData] = await db.query(
            'SELECT * FROM exam_attempts WHERE attempt_id = ?',
            [attemptId]
        );

        // Check if the assignment allows resuming
        const [assignmentData] = await db.query(
            `SELECT COALESCE(ta.is_resumable, 0) as is_resumable
             FROM test_assignments ta
             WHERE ta.exam_id = ? AND (ta.user_id = ? OR ta.group_id IN (
                 SELECT group_id FROM group_members WHERE user_id = ?
             ))
             ORDER BY ta.assigned_at DESC LIMIT 1`,
            [exam.exam_id, userId, userId]
        );

        const assignmentIsResumable = assignmentData.length > 0 ? assignmentData[0].is_resumable === 1 : false;

        // If the assignment doesn't allow resuming, auto-submit the test
        if (!assignmentIsResumable) {
            // Update the attempt status to completed
            await db.query(
                'UPDATE exam_attempts SET status = "completed", end_time = NOW() WHERE attempt_id = ?',
                [attemptId]
            );

            const message = 'This test assignment does not allow resuming. The test has been automatically submitted.';

            req.session.flashSuccess = message;
            return res.redirect('/tests');
        }

        if (attempts.length === 0) {
            req.session.flashError = 'Test attempt not found or already completed';
            return res.redirect('/tests');
        }

        // Get sections and questions
        const [sectionsResult] = await db.query(`
            SELECT s.*, e.instructions
            FROM sections s
            JOIN exams e ON s.exam_id = e.exam_id
            WHERE s.exam_id = ?
            ORDER BY s.position
        `, [examId]);

        // Get all questions for the exam
        const [questions] = await db.query(`
            SELECT q.*, s.section_name as section_name, s.position as section_position,
                   e.essay_id, e.title as essay_title, e.content as essay_content, e.pdf_path as essay_pdf_path,
                   qi1.file_path as image_path,
                   qi2.file_path as solution_image_path
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN essays e ON q.essay_id = e.essay_id
            LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
            LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
            WHERE s.exam_id = ?
            ORDER BY s.position, q.position
        `, [examId]);

        // Get user's answers for this attempt
        const [userAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ?',
            [attemptId]
        );

        // Create a map of question_id to user_answer
        const answersMap = {};
        const bookmarksMap = {};

        userAnswers.forEach(answer => {
            answersMap[answer.question_id] = answer.selected_option_id || answer.answer_text || answer.essay_answer || '';
            bookmarksMap[answer.question_id] = answer.is_bookmarked === 1;
        });

        // Format questions for rendering
        const formattedQuestions = [];

        // Process each question and fetch options if needed
        for (const question of questions) {
            let options = [];

            // For MCQ questions, fetch options
            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                const [optionResults] = await db.query(
                    `SELECT o.*, qi.file_path as image_path
                     FROM options o
                     LEFT JOIN question_images qi ON o.image_id = qi.image_id
                     WHERE o.question_id = ?
                     ORDER BY o.position`,
                    [question.question_id]
                );
                options = optionResults;
            }

            // Process category names
            let categoryNames = [];
            if (question.category_names) {
                categoryNames = question.category_names.split(',').map(name => name.trim()).filter(Boolean);
            }

            formattedQuestions.push({
                ...question,
                options,
                user_answer: answersMap[question.question_id] || '',
                is_bookmarked: bookmarksMap[question.question_id] || false,
                category_names: categoryNames
            });
        }

        // Group questions by section
        const sectionMap = {};
        formattedQuestions.forEach(question => {
            if (!sectionMap[question.section_id]) {
                sectionMap[question.section_id] = {
                    section_id: question.section_id,
                    name: question.section_name,
                    position: question.section_position,
                    questions: []
                };
            }
            sectionMap[question.section_id].questions.push(question);
        });

        const formattedSections = Object.values(sectionMap);

        // Determine test status
        const testStatus = {
            timeRemaining: null,
            isTimeLimited: exam.time_limit > 0,
            timeLimit: exam.time_limit
        };

        res.render('tests/take_new', {
            title: exam.exam_name,
            exam,
            sections: formattedSections,
            attemptId,
            testStatus,
            attempts, // Pass the attempt information to the template
            currentPage: 'tests',
            navbar: 'tests'
        });
    } catch (error) {
        console.error('Error loading test for continuation:', error);
        next(error);
    }
});

// Save answer endpoint
router.post('/answer', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId, questionId, answer, isBookmarked } = req.body;

        // Validate that the attempt belongs to the user
        const [attempts] = await db.query(
            'SELECT * FROM exam_attempts WHERE attempt_id = ? AND user_id = ?',
            [attemptId, req.session.userId]
        );

        if (attempts.length === 0) {
            return res.status(403).json({ success: false, message: 'Unauthorized' });
        }

        // Get question details to calculate marks
        const [questions] = await db.query(
            'SELECT * FROM questions WHERE question_id = ?',
            [questionId]
        );

        if (questions.length === 0) {
            return res.status(400).json({ success: false, message: 'Invalid question ID' });
        }

        const question = questions[0];
        let marks_obtained = 0;
        let selectedOptionId = null;
        let essayAnswer = null;
        let isCorrect = false;

        if (answer && answer.match(/^\d+$/)) {
            // It's a numeric answer, likely an option ID
            // Check if the option exists for this question
            const [options] = await db.query(
                'SELECT * FROM options WHERE question_id = ? AND id = ?',
                [questionId, answer]
            );

            if (options.length === 0) {
                // Option doesn't exist, try looking by position
                const [positionOptions] = await db.query(
                    'SELECT * FROM options WHERE question_id = ? AND position = ?',
                    [questionId, parseInt(answer)]
                );

                if (positionOptions.length > 0) {
                    // Found by position
                    selectedOptionId = positionOptions[0].id;
                    isCorrect = positionOptions[0].is_correct === 1;
                } else {
                    // Still not found, fall back to treating as text
                    essayAnswer = answer;
                }
            } else {
                // Option exists, use it
                selectedOptionId = options[0].id;
                isCorrect = options[0].is_correct === 1;
            }
        } else {
            // It's not a number, treat as essay/text answer
            essayAnswer = answer;
        }

        // Calculate marks based on correctness
        if (isCorrect) {
            marks_obtained = parseFloat(question.marks) || 1.00; // Default to 1.00 if marks is not set
        }

        // If not correct by direct comparison, check if the option is marked as correct in the database
        if (!isCorrect && selectedOptionId) {
            const [correctOption] = await db.query(
                'SELECT * FROM options WHERE id = ? AND is_correct = 1',
                [selectedOptionId]
            );

            if (correctOption.length > 0) {
                isCorrect = true;
                marks_obtained = parseFloat(question.marks) || 1.00; // Default to 1.00 if marks is not set
                console.log(`Option ${selectedOptionId} is marked as correct in the database`);
            }
        }

        // Check if answer already exists
        const [existingAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ? AND question_id = ?',
            [attemptId, questionId]
        );

        if (existingAnswers.length > 0) {
            // Update existing answer
            await db.query(
                'UPDATE user_answers SET selected_option_id = ?, answer_text = ?, essay_answer = ?, is_bookmarked = ?, marks_obtained = ?, updated_at = NOW() WHERE attempt_id = ? AND question_id = ?',
                [selectedOptionId, essayAnswer, essayAnswer, isBookmarked ? 1 : 0, marks_obtained, attemptId, questionId]
            );
        } else {
            // Insert new answer
            await db.query(
                'INSERT INTO user_answers (attempt_id, question_id, selected_option_id, answer_text, essay_answer, is_bookmarked, marks_obtained, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())',
                [attemptId, questionId, selectedOptionId, essayAnswer, essayAnswer, isBookmarked ? 1 : 0, marks_obtained]
            );
        }

        res.json({ success: true });
    } catch (error) {
        console.error('Error saving answer:', error);
        res.status(500).json({ success: false, message: 'Error saving answer' });
    }
});

// Submit test endpoint
router.post('/submit/:attemptId', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId } = req.params;
        console.log(`Processing test submission for attemptId: ${attemptId}, user: ${req.session.userId}`);

        // Validate that the attempt belongs to the user and has been started
        const [attempts] = await db.query(
            `SELECT ea.*, e.passing_marks, e.exam_name, e.exam_id
             FROM exam_attempts ea
             JOIN exams e ON ea.exam_id = e.exam_id
             WHERE ea.attempt_id = ? AND ea.user_id = ? AND (ea.status = 'in_progress' OR ea.status = 'paused')`,
            [attemptId, req.session.userId]
        );

        if (attempts.length === 0) {
            console.error(`No active attempt found for attemptId: ${attemptId}, user: ${req.session.userId}`);
            req.session.flashError = 'Unauthorized attempt or test already completed';
            return res.redirect('/tests');
        }

        // Check if the test has actually been started
        if (attempts[0].status === 'not_started') {
            console.log(`Test not started yet, redirecting to dashboard. Status: ${attempts[0].status}`);
            return res.redirect('/dashboard');
        }

        const attempt = attempts[0];
        console.log(`Found attempt: ${JSON.stringify(attempt)}`);

        // Calculate score
        const [questions] = await db.query(`
            SELECT q.*, s.section_id
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            WHERE s.exam_id = ?
        `, [attempt.exam_id]);

        const [userAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ?',
            [attemptId]
        );

        console.log(`Found ${questions.length} questions and ${userAnswers.length} user answers`);

        // Convert to a map for easy lookup
        const answersMap = {};
        userAnswers.forEach(answer => {
            answersMap[answer.question_id] = answer.selected_option_id || answer.answer_text || answer.essay_answer || '';
        });

        let totalQuestions = questions.length;
        let correctAnswers = 0;

        // Track section-wise performance
        const sectionPerformance = {};

        // Get all option IDs from user answers
        const optionIds = userAnswers
            .filter(answer => answer.selected_option_id)
            .map(answer => answer.selected_option_id);

        // Get all options in a single query
        let optionsMap = {};
        if (optionIds.length > 0) {
            const [allOptions] = await db.query(
                'SELECT id, option_text FROM options WHERE id IN (?)',
                [optionIds]
            );

            // Create a map of option ID to option text
            allOptions.forEach(option => {
                optionsMap[option.id] = option.option_text;
            });
        }

        // Now process each question
        for (const question of questions) {
            const userAnswer = answersMap[question.question_id];
            let isCorrect = false;
            let marks_obtained = 0;

            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                if (userAnswer && !isNaN(parseInt(userAnswer))) {
                    // Get the text of the selected option
                    const optionText = optionsMap[userAnswer];

                    // Compare option text with correct answer
                    if (optionText && question.correct_answer) {
                        isCorrect = optionText.toLowerCase() === question.correct_answer.toLowerCase();
                        console.log(`Question ${question.question_id}: Selected option text: "${optionText}", Correct answer: "${question.correct_answer}", isCorrect: ${isCorrect}`);

                        // If correct, award marks; if incorrect, deduct negative marks
                        if (isCorrect) {
                            marks_obtained = parseFloat(question.marks) || 1.00; // Default to 1.00 if marks is not set
                        } else if (userAnswer) { // Only deduct marks if the user attempted the question
                            marks_obtained = -(parseFloat(question.negative_marks) || 0.00); // Default to 0.00 if negative_marks is not set
                        }
                    }
                }

                // Alternative method: Check if the selected option has is_correct = 1
                if (!isCorrect && userAnswer && !isNaN(parseInt(userAnswer))) {
                    const [correctOption] = await db.query(
                        'SELECT * FROM options WHERE question_id = ? AND id = ? AND is_correct = 1',
                        [question.question_id, userAnswer]
                    );

                    if (correctOption.length > 0) {
                        isCorrect = true;
                        marks_obtained = parseFloat(question.marks) || 1.00; // Default to 1.00 if marks is not set
                        console.log(`Question ${question.question_id}: Correct option found by is_correct flag, isCorrect: ${isCorrect}`);
                    } else if (userAnswer) { // Only deduct marks if the user attempted the question
                        marks_obtained = -(parseFloat(question.negative_marks) || 0.00); // Default to 0.00 if negative_marks is not set
                        console.log(`Question ${question.question_id}: Incorrect answer, deducting ${marks_obtained} marks`);
                    }
                }
            } else {
                // For non-MCQ questions, direct comparison
                if (userAnswer && question.correct_answer) {
                    isCorrect = userAnswer.toString().toLowerCase() === question.correct_answer.toString().toLowerCase();

                    // If correct, award marks; if incorrect, deduct negative marks
                    if (isCorrect) {
                        marks_obtained = parseFloat(question.marks) || 1.00; // Default to 1.00 if marks is not set
                    } else if (userAnswer) { // Only deduct marks if the user attempted the question
                        marks_obtained = -(parseFloat(question.negative_marks) || 0.00); // Default to 0.00 if negative_marks is not set
                        console.log(`Question ${question.question_id}: Incorrect answer, deducting ${marks_obtained} marks`);
                    }
                }
            }

            if (isCorrect) {
                correctAnswers++;
            }

            // Update marks_obtained in user_answers table
            if (userAnswer) {
                console.log(`Updating marks for question ${question.question_id}: marks_obtained = ${marks_obtained}`);
                await db.query(
                    'UPDATE user_answers SET marks_obtained = ? WHERE attempt_id = ? AND question_id = ?',
                    [marks_obtained, attemptId, question.question_id]
                );
            }

            // Track section performance
            if (!sectionPerformance[question.section_id]) {
                sectionPerformance[question.section_id] = {
                    totalQuestions: 0,
                    correctAnswers: 0,
                    totalMarks: 0,
                    marksObtained: 0
                };
            }

            sectionPerformance[question.section_id].totalQuestions++;
            sectionPerformance[question.section_id].totalMarks += parseFloat(question.marks) || 1.00;
            if (isCorrect) {
                sectionPerformance[question.section_id].correctAnswers++;
                sectionPerformance[question.section_id].marksObtained += marks_obtained;
            }
        }

        // Calculate total marks possible and total marks obtained
        let totalMarksPossible = 0;
        let totalMarksObtained = 0;

        // Recalculate section performance with negative marks
        for (const sectionId in sectionPerformance) {
            sectionPerformance[sectionId].marksObtained = 0; // Reset marks obtained
        }

        // Calculate total marks for each question
        for (const question of questions) {
            const userAnswer = answersMap[question.question_id];
            const marks = parseFloat(question.marks) || 1.00;
            totalMarksPossible += marks;

            // If user answered this question
            if (userAnswer) {
                const marksForQuestion = parseFloat(userAnswers.find(a => a.question_id == question.question_id)?.marks_obtained || 0);
                totalMarksObtained += marksForQuestion;

                // Update section performance
                if (sectionPerformance[question.section_id]) {
                    sectionPerformance[question.section_id].marksObtained += marksForQuestion;
                }
            }
        }

        // Calculate score percentage based on marks
        const scorePercentage = totalMarksPossible > 0 ? (totalMarksObtained / totalMarksPossible) * 100 : 0;
        // Ensure score is not negative
        const finalScorePercentage = Math.max(0, scorePercentage);
        const passed = finalScorePercentage >= attempt.passing_marks;

        console.log(`Total Marks Possible: ${totalMarksPossible}, Total Marks Obtained: ${totalMarksObtained}`);
        console.log(`Score: ${finalScorePercentage.toFixed(2)}%, Passed: ${passed}`);

        // Calculate test duration
        const now = new Date();
        const startTime = new Date(attempt.start_time);
        const durationMs = now - startTime;

        // Format duration as HH:MM:SS
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
        const durationFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        const durationSeconds = Math.floor(durationMs / 1000);

        console.log(`Duration: ${durationFormatted} (${durationSeconds} seconds)`);

        // Count the number of attempts for this test
        const [attemptCounts] = await db.query(
            'SELECT COUNT(*) as attempt_count FROM exam_attempts WHERE exam_id = ? AND user_id = ?',
            [attempt.exam_id, req.session.userId]
        );

        const attemptNumber = attemptCounts[0].attempt_count;
        console.log(`Attempt number: ${attemptNumber}`);

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            console.log('Starting database transaction...');

            // Clear all bookmarks
            await db.query(
                'UPDATE user_answers SET is_bookmarked = 0 WHERE attempt_id = ?',
                [attemptId]
            );
            console.log('Bookmarks cleared successfully');

            // Update attempt status
            await db.query(
                'UPDATE exam_attempts SET status = ?, total_score = ?, end_time = NOW(), total_marks_possible = ?, total_marks_obtained = ? WHERE attempt_id = ?',
                ['completed', finalScorePercentage, totalMarksPossible, totalMarksObtained, attemptId]
            );
            console.log('Attempt status updated successfully');

            // Insert attempt statistics
            try {
                await db.query(
                    `INSERT INTO attempt_statistics
                     (user_id, exam_id, attempt_id, attempt_number, duration_seconds, duration_formatted, total_questions, correct_answers)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [req.session.userId, attempt.exam_id, attemptId, attemptNumber, durationSeconds, durationFormatted, totalQuestions, correctAnswers]
                );
                console.log('Attempt statistics inserted successfully');
            } catch (statError) {
                console.error('Error inserting attempt statistics:', statError);
                // Continue with the transaction even if this fails
            }

            // Insert section-wise performance
            try {
                for (const sectionId in sectionPerformance) {
                    const sectionData = sectionPerformance[sectionId];
                    // Calculate section score based on marks obtained vs total marks
                    const sectionTotalMarks = sectionData.totalMarks;
                    const sectionMarksObtained = sectionData.marksObtained;
                    const sectionScore = sectionTotalMarks > 0 ? (sectionMarksObtained / sectionTotalMarks) * 100 : 0;
                    // Ensure section score is not negative
                    const finalSectionScore = Math.max(0, sectionScore);

                    // Categorize performance
                    let performanceCategory;
                    if (finalSectionScore < 30) {
                        performanceCategory = 'weak';
                    } else if (finalSectionScore < 60) {
                        performanceCategory = 'medium';
                    } else {
                        performanceCategory = 'strong';
                    }

                    await db.query(
                        `INSERT INTO user_performance
                         (user_id, exam_id, attempt_id, section_id, score_percentage, performance_category)
                         VALUES (?, ?, ?, ?, ?, ?)`,
                        [req.session.userId, attempt.exam_id, attemptId, sectionId, finalSectionScore, performanceCategory]
                    );
                    console.log(`Section performance for section ${sectionId} inserted successfully`);
                }
            } catch (perfError) {
                console.error('Error inserting section performance:', perfError);
                // Continue with the transaction even if this fails
            }

            // Commit transaction
            await db.query('COMMIT');
            console.log('Transaction committed successfully');

            // Exit test mode to re-enable chat
            exitTestMode(req.session.userId);

            req.session.flashSuccess = 'Test submitted successfully!';
            res.redirect(`/tests/thank-you/${attemptId}`);
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            console.error('Transaction error, rollback performed:', error);
            throw error;
        }
    } catch (error) {
        console.error('Error submitting test:', error);
        req.session.flashError = `Error submitting test: ${error.message}`;
        res.redirect('/tests');
    }
});

// Thank you page after test submission
router.get('/thank-you/:attemptId', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId } = req.params;

        // Get basic test info
        const [attempts] = await db.query(
            `SELECT ea.*, e.exam_name
             FROM exam_attempts ea
             JOIN exams e ON ea.exam_id = e.exam_id
             WHERE ea.attempt_id = ? AND ea.user_id = ?`,
            [attemptId, req.session.userId]
        );

        if (attempts.length === 0) {
            req.session.flashError = 'Test attempt not found';
            return res.redirect('/tests');
        }

        const attempt = attempts[0];

        // Explicitly set the layout to 'user'
        res.render('tests/thank-you', {
            title: 'Test Submitted',
            attemptId,
            examName: attempt.exam_name,
            layout: 'user' // Explicitly set the layout
        });
    } catch (error) {
        console.error('Error loading thank you page:', error);
        req.session.flashError = 'Error loading thank you page';
        res.redirect('/tests');
    }
});

// Test results page
router.get('/results/:attemptId', checkAuthenticated, async (req, res, next) => {
    try {
        const { attemptId } = req.params;

        // Get attempt details
        const [attempts] = await db.query(`
            SELECT ea.*, e.exam_name, e.passing_marks,
                   COALESCE(ea.total_score, 0.00) as total_score
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.attempt_id = ? AND ea.user_id = ?
            AND (e.is_deleted = 0 OR e.is_deleted IS NULL)
        `, [attemptId, req.session.userId]);

        if (attempts.length === 0) {
            req.session.flashError = 'Result not found';
            return res.redirect('/tests');
        }

        const attempt = attempts[0];

        // Debug attempt data
        console.log('Attempt data for results page:', {
            attempt_id: attempt.attempt_id,
            start_time: attempt.start_time,
            end_time: attempt.end_time,
            total_score: attempt.total_score,
            status: attempt.status
        });

        // Get questions and user answers, including essay content if linked
        const [questions] = await db.query(`
            SELECT q.*, s.section_name as section_name,
                   e.essay_id, e.title as essay_title, e.content as essay_content, e.pdf_path as essay_pdf_path,
                   GROUP_CONCAT(DISTINCT c.name) as category_names,
                   GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            JOIN exams ex ON s.exam_id = ex.exam_id
            LEFT JOIN essays e ON q.essay_id = e.essay_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE ex.exam_id = ?
            GROUP BY q.question_id, s.section_name, e.essay_id, e.title, e.content, e.pdf_path
            ORDER BY s.position, q.position
        `, [attempt.exam_id]);

        const [userAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ?',
            [attemptId]
        );

        // Convert to a map for easy lookup
        const answersMap = {};
        const bookmarksMap = {};
        userAnswers.forEach(answer => {
            answersMap[answer.question_id] = answer.selected_option_id || answer.answer_text || answer.essay_answer || '';
            // Check if is_bookmarked field exists before accessing it
            if (answer.hasOwnProperty('is_bookmarked')) {
                bookmarksMap[answer.question_id] = answer.is_bookmarked;
            } else {
                bookmarksMap[answer.question_id] = false;
            }
        });

        // Format questions for rendering
        const formattedQuestions = [];

        // Process each question and fetch options if needed
        for (const question of questions) {
            let options = [];
            let userAnswer = answersMap[question.question_id] || '';
            let userAnswerText = userAnswer; // Default to the raw answer

            // For MCQ questions, fetch options
            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                const [optionResults] = await db.query(
                    'SELECT * FROM options WHERE question_id = ? ORDER BY position',
                    [question.question_id]
                );
                options = optionResults;

                // If user answer is an option ID, look up the option text
                if (userAnswer && !isNaN(userAnswer)) {
                    const selectedOption = options.find(opt => opt.id == userAnswer);
                    if (selectedOption) {
                        userAnswerText = selectedOption.option_text;
                    }
                }
            }

            // Determine if the answer is correct
            let isCorrect = false;
            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                // For multiple choice, check multiple ways to determine correctness

                // Method 1: Check if the selected option has is_correct = 1
                const correctOption = options.find(opt => opt.is_correct === 1);
                if (correctOption && userAnswer == correctOption.id) {
                    isCorrect = true;
                }
                // Method 2: Check if the option text matches the correct_answer field
                else if (userAnswer) {
                    const selectedOption = options.find(opt => opt.id == userAnswer);
                    if (selectedOption && question.correct_answer &&
                        selectedOption.option_text.toLowerCase() === question.correct_answer.toLowerCase()) {
                        isCorrect = true;
                    }
                }
            } else if (question.question_type === 'true_false') {
                // For true/false, direct comparison
                if (userAnswer && userAnswer.toLowerCase() === question.correct_answer.toLowerCase()) {
                    isCorrect = true;
                }
            } else if (question.question_type === 'essay') {
                // Essay questions need manual grading, so we can't determine correctness automatically
                isCorrect = null; // Use null to indicate it can't be automatically determined
            }

            formattedQuestions.push({
                ...question,
                options,
                user_answer: userAnswerText, // Use the option text instead of ID
                user_answer_id: userAnswer,  // Keep the original ID for reference
                is_bookmarked: bookmarksMap[question.question_id] || false,
                is_correct: isCorrect
            });
        }

        // Group questions by section
        const sectionMap = {};
        formattedQuestions.forEach(question => {
            if (!sectionMap[question.section_id]) {
                sectionMap[question.section_id] = {
                    section_id: question.section_id,
                    name: question.section_name,
                    questions: []
                };
            }
            sectionMap[question.section_id].questions.push(question);
        });

        const formattedSections = Object.values(sectionMap);

        res.render('tests/results-tabbed', {
            title: 'Test Results',
            attempt,
            sections: formattedSections,
            currentPage: 'tests',
            formatDate,
            formatDateTime,
            navbar: 'tests',
            layout: 'user' // Explicitly set the layout
        });
    } catch (error) {
        console.error('Error loading test results:', error);
        next(error);
    }
});

// Test results list page
router.get('/results', checkAuthenticated, async (req, res, next) => {
    try {
        // Get all user's completed attempts (not in-progress)
        const [attempts] = await db.query(`
            SELECT ea.*, e.exam_name, e.passing_marks,
                   COALESCE(ea.continuation_count, 0) as continuation_count
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ?
            AND ea.status != 'in_progress'
            AND (e.is_deleted = 0 OR e.is_deleted IS NULL)
            ORDER BY ea.attempt_date DESC
        `, [req.session.userId]);

        res.render('tests/results-list', {
            title: 'Test Results',
            attempts: attempts,
            currentPage: 'results',
            navbar: 'user',
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading test results list:', error);
        next(error);
    }
});

// User performance analysis page
router.get('/performance', checkAuthenticated, async (req, res, next) => {
    try {
        let overallPerformance = [];
        let attemptStats = [];
        let sectionPerformance = [];

        try {
            // Get summary of user's performance across all exams
            [overallPerformance] = await db.query(`
                SELECT
                    e.exam_name,
                    COUNT(DISTINCT ea.attempt_id) as attempt_count,
                    AVG(ea.total_score) as average_score,
                    MAX(ea.total_score) as highest_score,
                    MIN(ea.total_score) as lowest_score,
                    SUM(CASE WHEN ea.total_score >= e.passing_marks THEN 1 ELSE 0 END) as pass_count,
                    SUM(CASE WHEN ea.total_score < e.passing_marks THEN 1 ELSE 0 END) as fail_count
                FROM exam_attempts ea
                JOIN exams e ON ea.exam_id = e.exam_id
                WHERE ea.user_id = ? AND ea.status = 'completed'
                GROUP BY e.exam_id
                ORDER BY attempt_count DESC
            `, [req.session.userId]);
        } catch (error) {
            console.error('Error loading overall performance:', error);
            // Continue with empty array
        }

        try {
            // Get detailed attempt statistics
            [attemptStats] = await db.query(`
                SELECT
                    ea.attempt_id,
                    ea.exam_id,
                    e.exam_name,
                    ea.total_score,
                    e.passing_marks,
                    ea.status,
                    ea.attempt_date,
                    ea.start_time,
                    ea.end_time,
                    ea.duration_seconds,
                    TIMESTAMPDIFF(MINUTE, ea.start_time, ea.end_time) as duration_minutes,
                    ast.duration_formatted,
                    ast.total_questions,
                    ast.correct_answers,
                    (ast.correct_answers / ast.total_questions) * 100 as score_percentage,
                    ast.created_at
                FROM exam_attempts ea
                JOIN exams e ON ea.exam_id = e.exam_id
                LEFT JOIN attempt_statistics ast ON ea.attempt_id = ast.attempt_id
                WHERE ea.user_id = ?
                ORDER BY ea.attempt_date DESC
            `, [req.session.userId]);
        } catch (error) {
            console.error('Error loading attempt statistics:', error);
            // Continue with empty array
        }

        try {
            // Get section-wise performance categorized as weak, medium, or strong
            [sectionPerformance] = await db.query(`
                SELECT
                    up.id,
                    e.exam_name,
                    s.section_name,
                    up.score_percentage,
                    up.performance_category,
                    up.created_at
                FROM user_performance up
                JOIN exams e ON up.exam_id = e.exam_id
                JOIN sections s ON up.section_id = s.section_id
                WHERE up.user_id = ?
                ORDER BY up.created_at DESC
            `, [req.session.userId]);
        } catch (error) {
            console.error('Error loading section performance:', error);
            // Continue with empty array
        }

        // Group section performance by category
        const performanceByCategory = {
            weak: sectionPerformance.filter(p => p.performance_category === 'weak'),
            medium: sectionPerformance.filter(p => p.performance_category === 'medium'),
            strong: sectionPerformance.filter(p => p.performance_category === 'strong')
        };

        // Calculate total time spent
        let totalTimeSpent = 0;
        if (attemptStats && attemptStats.length > 0) {
            attemptStats.forEach(attempt => {
                if (attempt.duration_seconds) {
                    totalTimeSpent += parseInt(attempt.duration_seconds);
                }
            });
        }

        // Format total time spent
        const timeSpentHours = Math.floor(totalTimeSpent / 3600);
        const timeSpentMinutes = Math.floor((totalTimeSpent % 3600) / 60);
        const timeSpentFormatted = `${timeSpentHours} hours ${timeSpentMinutes} minutes`;

        // Format data for charts
        const chartData = {
            exams: overallPerformance.map(p => p.exam_name || ''),
            scores: overallPerformance.map(p => parseFloat(p.average_score || 0).toFixed(2)),
            attempts: overallPerformance.map(p => p.attempt_count || 0),
            passRate: overallPerformance.map(p => {
                const total = parseInt(p.pass_count || 0) + parseInt(p.fail_count || 0);
                return total > 0 ? (parseInt(p.pass_count || 0) / total * 100).toFixed(2) : 0;
            })
        };

        res.render('tests/performance', {
            title: 'Performance Analysis',
            overallPerformance,
            performanceByCategory,
            attemptStats,
            chartData: JSON.stringify(chartData),
            currentPage: 'performance',
            navbar: 'user',
            totalTimeSpent,
            timeSpentHours,
            timeSpentMinutes,
            timeSpentFormatted,
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    } catch (error) {
        console.error('Error loading performance analysis:', error);
        res.render('tests/performance', {
            title: 'Performance Analysis',
            overallPerformance: [],
            performanceByCategory: { weak: [], medium: [], strong: [] },
            attemptStats: [],
            chartData: JSON.stringify({ exams: [], scores: [], attempts: [], passRate: [] }),
            currentPage: 'performance',
            navbar: 'user',
            error: 'An error occurred while loading your performance data. Please try again later.',
            formatDate, // Explicitly pass the date formatter function
            formatDateTime // Explicitly pass the date-time formatter function
        });
    }
});

// Reorder sections endpoint
router.post('/admin/sections/reorder', checkAdmin, async (req, res) => {
    try {
        const { sections } = req.body;

        if (!sections || !Array.isArray(sections) || sections.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid sections data'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Update each section's position
            for (const section of sections) {
                await db.query(
                    'UPDATE sections SET position = ? WHERE section_id = ?',
                    [section.position, section.section_id]
                );
            }

            // Commit transaction
            await db.query('COMMIT');

            return res.json({
                success: true,
                message: 'Section order updated successfully'
            });
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error reordering sections:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Error reordering sections'
        });
    }
});

// Reorder questions endpoint
router.post('/admin/questions/reorder', checkAdmin, async (req, res) => {
    try {
        const { section_id, questions } = req.body;

        if (!section_id || !questions || !Array.isArray(questions) || questions.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid questions data'
            });
        }

        // Start transaction
        await db.query('START TRANSACTION');

        try {
            // Update each question's position
            for (const question of questions) {
                await db.query(
                    'UPDATE questions SET position = ? WHERE question_id = ?',
                    [question.position, question.question_id]
                );
            }

            // Commit transaction
            await db.query('COMMIT');

            return res.json({
                success: true,
                message: 'Question order updated successfully'
            });
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error reordering questions:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Error reordering questions'
        });
    }
});

// Verify option exists endpoint
router.get('/verify-option', checkAuthenticated, async (req, res) => {
    try {
        const { questionId, optionId } = req.query;

        // Check if the option exists for this question
        const [options] = await db.query(
            'SELECT * FROM options WHERE question_id = ? AND id = ?',
            [questionId, optionId]
        );

        res.json({ exists: options.length > 0 });
    } catch (error) {
        console.error('Error verifying option:', error);
        res.status(500).json({ exists: false, error: 'Error verifying option' });
    }
});

// Request additional attempts for a test
router.post('/request-access', checkAuthenticated, async (req, res) => {
    try {
        console.log('Request body:', req.body);
        const { examId, message } = req.body;
        const userId = req.session.userId;

        if (!examId) {
            req.session.flashError = 'Exam ID is required';
            return res.redirect('/tests');
        }

        // Get user information
        const [users] = await db.query('SELECT username, email FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            req.session.flashError = 'User not found';
            return res.redirect('/tests');
        }
        const user = users[0];

        // Get test information
        const [tests] = await db.query('SELECT exam_name FROM exams WHERE exam_id = ?', [examId]);
        if (tests.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/tests');
        }
        const test = tests[0];

        // Check if there's already a pending request for this user and test
        const [existingRequests] = await db.query(
            'SELECT id FROM access_requests WHERE user_id = ? AND exam_id = ? AND status = "pending"',
            [userId, examId]
        );

        if (existingRequests.length > 0) {
            req.session.flashSuccess = 'You already have a pending request for this test. An administrator will review it soon.';
            return res.redirect('/tests');
        }

        // Insert the access request
        await db.query(
            'INSERT INTO access_requests (user_id, exam_id, message, status, requested_at) VALUES (?, ?, ?, "pending", NOW())',
            [userId, examId, message || null]
        );

        // Create a notification for all admin users
        const [admins] = await db.query('SELECT id FROM users WHERE role = "admin"');

        // Insert notifications for each admin
        for (const admin of admins) {
            await db.query(
                'INSERT INTO notifications (user_id, title, message, is_read, created_at) VALUES (?, ?, ?, 0, NOW())',
                [
                    admin.id,
                    'Test Access Request',
                    `User ${user.username} (${user.email}) has requested additional attempts for test: ${test.exam_name}. Message: ${message || 'No message provided'}`
                ]
            );
        }

        req.session.flashSuccess = 'Your request has been sent to the administrator.';
        return res.redirect('/tests');
    } catch (error) {
        console.error('Error requesting access:', error);
        req.session.flashError = 'An error occurred while sending your request. Please try again.';
        return res.redirect('/tests');
    }
});

// Pause test
router.post('/pause/:attemptId', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId } = req.params;
        const userId = req.session.userId;
        const { remainingTime } = req.body;

        console.log(`Pausing test for attemptId: ${attemptId}, user: ${userId}, remainingTime: ${remainingTime}`);

        // Validate that the attempt belongs to the user and is in progress or paused
        const [attempts] = await db.query(
            'SELECT * FROM exam_attempts WHERE attempt_id = ? AND user_id = ? AND (exam_attempts.status = "in_progress" OR exam_attempts.status = "paused")',
            [attemptId, userId]
        );

        if (attempts.length === 0) {
            console.error(`No valid attempt found for attemptId: ${attemptId}, user: ${userId}`);
            return res.status(404).json({ success: false, message: 'Test attempt not found or already completed' });
        }

        // Update the attempt status to paused and store the remaining time
        await db.query(
            'UPDATE exam_attempts SET status = "paused", remaining_time = ? WHERE attempt_id = ?',
            [remainingTime, attemptId]
        );

        console.log(`Test paused successfully for attemptId: ${attemptId}, remaining time: ${remainingTime}ms`);

        // Exit test mode
        exitTestMode(userId);

        return res.json({ success: true, message: 'Test paused successfully' });
    } catch (error) {
        console.error('Error pausing test:', error);
        return res.status(500).json({ success: false, message: 'Error pausing test' });
    }
});

// Resume test
router.post('/resume/:attemptId', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId } = req.params;
        const userId = req.session.userId;
        const { remainingTime } = req.body;

        console.log(`Resuming test for attemptId: ${attemptId}, user: ${userId}, remainingTime: ${remainingTime}`);

        // Validate that the attempt belongs to the user and is paused
        const [attempts] = await db.query(
            'SELECT * FROM exam_attempts WHERE attempt_id = ? AND user_id = ? AND exam_attempts.status = "paused"',
            [attemptId, userId]
        );

        if (attempts.length === 0) {
            console.error(`No paused attempt found for attemptId: ${attemptId}, user: ${userId}`);
            return res.status(404).json({ success: false, message: 'Test attempt not found or not paused' });
        }

        // Update the attempt status to in_progress and store the remaining time
        await db.query(
            'UPDATE exam_attempts SET status = "in_progress", remaining_time = ? WHERE attempt_id = ?',
            [remainingTime, attemptId]
        );

        console.log(`Test resumed successfully for attemptId: ${attemptId}, remaining time: ${remainingTime}ms`);

        return res.json({ success: true, message: 'Test resumed successfully' });
    } catch (error) {
        console.error('Error resuming test:', error);
        return res.status(500).json({ success: false, message: 'Error resuming test' });
    }
});

// Record test start time
router.post('/record-start', checkAuthenticated, async (req, res) => {
    try {
        const { examId, attemptId } = req.body;
        const userId = req.session.userId;
        let finalAttemptId = attemptId;

        // If no attemptId is provided, we need to create a new attempt
        if (!attemptId) {
            if (!examId) {
                return res.status(400).json({ success: false, message: 'Exam ID is required when no attempt ID is provided' });
            }

            // A test cannot be reassigned if the user has not used all attempts from the previous assignment
            // So we don't need to check for reassignment here

            // Create a new attempt
            console.log(`Creating new attempt for exam ${examId} and user ${userId}`);
            const [result] = await db.query(
                'INSERT INTO exam_attempts (exam_id, user_id, status, attempt_date, start_time) VALUES (?, ?, "in_progress", NOW(), NOW())',
                [examId, userId]
            );
            finalAttemptId = result.insertId;
            console.log(`New attempt created with ID: ${finalAttemptId}`);
        } else {
            // Validate that the attempt belongs to the user
            const [attempts] = await db.query(
                'SELECT * FROM exam_attempts WHERE attempt_id = ? AND user_id = ?',
                [attemptId, userId]
            );

            if (attempts.length === 0) {
                return res.status(403).json({ success: false, message: 'Unauthorized' });
            }

            // Check if the attempt exists
            const [attemptData] = await db.query(
                'SELECT * FROM exam_attempts WHERE attempt_id = ?',
                [attemptId]
            );

            if (attemptData.length === 0) {
                return res.status(404).json({ success: false, message: 'Attempt not found' });
            }

            // Get exam ID from the attempt
            const [attemptExamData] = await db.query(
                'SELECT exam_id, user_id FROM exam_attempts WHERE attempt_id = ?',
                [attemptId]
            );

            if (attemptExamData.length === 0) {
                return res.status(404).json({ success: false, message: 'Attempt not found' });
            }

            const examId = attemptExamData[0].exam_id;
            const attemptUserId = attemptExamData[0].user_id;

            // Check if the assignment allows resuming
            const [assignmentData] = await db.query(
                `SELECT COALESCE(ta.is_resumable, 0) as is_resumable
                 FROM test_assignments ta
                 WHERE ta.exam_id = ? AND (ta.user_id = ? OR ta.group_id IN (
                     SELECT group_id FROM group_members WHERE user_id = ?
                 ))
                 ORDER BY ta.assigned_at DESC LIMIT 1`,
                [examId, attemptUserId, attemptUserId]
            );

            const isResumable = assignmentData.length > 0 ? assignmentData[0].is_resumable === 1 : false;

            // If the test is not resumable, auto-submit the test immediately
            if (!isResumable) {
                // Update the attempt status to completed
                await db.query(
                    'UPDATE exam_attempts SET status = "completed", end_time = NOW() WHERE attempt_id = ?',
                    [attemptId]
                );
                console.log(`Test not resumable: Test auto-submitted for attempt ID ${attemptId}`);

                // Return success but indicate the test was auto-submitted
                const message = 'This test does not allow resuming. The test has been automatically submitted.';

                return res.json({
                    success: true,
                    attemptId: attemptId,
                    autoSubmitted: true,
                    message: message
                });
            } else {
                // For resumable tests, don't update the start time to preserve the original timer
                // Just update the status to in_progress
                await db.query(
                    'UPDATE exam_attempts SET status = "in_progress" WHERE attempt_id = ?',
                    [attemptId]
                );
                console.log(`Test resumed: Attempt ID ${attemptId} - Status updated to in_progress`);
            }
        }

        // Enter test mode to disable chat
        enterTestMode(userId);

        res.json({ success: true, attemptId: finalAttemptId });
    } catch (error) {
        console.error('Error recording start time:', error);
        res.status(500).json({ success: false, message: 'Error recording start time' });
    }
});

// Generate PDF for offline use
router.get('/generate-pdf/:examId', checkAuthenticated, async (req, res, next) => {
    try {
        const { examId } = req.params;
        const userId = req.session.userId;

        // Get exam details
        const [exams] = await db.query(
            'SELECT * FROM exams WHERE exam_id = ? AND (is_deleted = 0 OR is_deleted IS NULL)',
            [examId]
        );

        if (exams.length === 0) {
            req.session.flashError = 'Test not found';
            return res.redirect('/tests');
        }

        const exam = exams[0];

        // Get sections and questions
        const [questions] = await db.query(`
            SELECT q.*, s.section_name as section_name, s.position as section_position,
                   e.essay_id, e.title as essay_title, e.content as essay_content, e.pdf_path as essay_pdf_path,
                   qi1.file_path as image_path, qi1.image_id,
                   qi2.file_path as solution_image_path, qi2.image_id as solution_image_id,
                   GROUP_CONCAT(DISTINCT c.name) as category_names,
                   GROUP_CONCAT(DISTINCT qcm.category_id) as category_ids
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            LEFT JOIN essays e ON q.essay_id = e.essay_id
            LEFT JOIN question_images qi1 ON q.image_id = qi1.image_id
            LEFT JOIN question_images qi2 ON q.solution_image_id = qi2.image_id
            LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
            LEFT JOIN categories c ON qcm.category_id = c.category_id
            WHERE s.exam_id = ?
            GROUP BY q.question_id, s.section_name, s.position, e.essay_id, e.title, e.content, e.pdf_path
            ORDER BY s.position, q.position
        `, [examId]);

        // Log the questions to check image paths
        console.log('Questions with images:', questions.filter(q => q.image_path).map(q => ({ id: q.question_id, image_path: q.image_path })));

        // Process each question and fetch options if needed
        const formattedQuestions = [];
        let totalMarks = 0;

        for (const question of questions) {
            let options = [];

            // For MCQ questions, fetch options
            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                const [optionResults] = await db.query(
                    'SELECT * FROM options WHERE question_id = ? ORDER BY position',
                    [question.question_id]
                );
                options = optionResults;
            }

            formattedQuestions.push({
                ...question,
                options,
                negative_marks: question.negative_marks || 0
            });

            // Calculate total marks
            totalMarks += parseFloat(question.marks || 1);
        }

        // Group questions by section
        const sectionMap = {};
        formattedQuestions.forEach(question => {
            const sectionName = question.section_name;
            const sectionPosition = question.section_position;

            if (!sectionMap[sectionPosition]) {
                sectionMap[sectionPosition] = {
                    name: sectionName,
                    position: sectionPosition,
                    questions: []
                };
            }

            sectionMap[sectionPosition].questions.push(question);
        });

        // Convert section map to array and sort by position
        const sections = Object.values(sectionMap).sort((a, b) => a.position - b.position);

        // Generate a unique filename
        const timestamp = Date.now();
        const filename = `${exam.exam_name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.pdf`;
        const outputPath = path.join(__dirname, '../public/uploads/pdf', filename);
        const templatePath = path.join(__dirname, '../views/tests/pdf_template.ejs');

        // Generate PDF
        await generatePDF({ exam, sections, totalMarks }, templatePath, outputPath);

        // Return the PDF URL
        const pdfUrl = `/uploads/pdf/${filename}`;
        res.json({ success: true, pdfUrl });
    } catch (error) {
        console.error('Error generating PDF:', error);
        res.status(500).json({ success: false, message: 'Error generating PDF' });
    }
});

// Bulk archive tests (admin only)
router.post('/admin/bulk-archive', checkAdmin, async (req, res) => {
    try {
        const { examIds } = req.body;
        let ids = [];

        // Parse the JSON string to get the array of IDs
        try {
            ids = JSON.parse(examIds);
        } catch (error) {
            console.error('Error parsing examIds:', error);
            req.session.flashError = 'Invalid exam IDs format';
            return res.redirect('/admin/tests');
        }

        if (!Array.isArray(ids) || ids.length === 0) {
            req.session.flashError = 'No tests selected for archiving';
            return res.redirect('/admin/tests');
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Update all selected exams to archived status
            const placeholders = ids.map(() => '?').join(',');
            const [result] = await db.query(
                `UPDATE exams
                 SET status = 'archived',
                     updated_at = NOW()
                 WHERE exam_id IN (${placeholders})`,
                ids
            );

            // Commit transaction
            await db.query('COMMIT');

            // Set success message
            req.session.flashSuccess = `${result.affectedRows} test(s) archived successfully`;
            return res.redirect('/admin/tests');

        } catch (error) {
            // Rollback transaction on error
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error bulk archiving tests:', error);
        req.session.flashError = error.message || 'Error archiving tests';
        return res.redirect('/admin/tests');
    }
});

// Bulk delete tests (admin only) - Soft delete implementation
router.post('/admin/bulk-delete', checkAdmin, async (req, res) => {
    try {
        const { examIds } = req.body;
        let ids = [];

        // Parse the JSON string to get the array of IDs
        try {
            ids = JSON.parse(examIds);
        } catch (error) {
            console.error('Error parsing examIds:', error);
            req.session.flashError = 'Invalid exam IDs format';
            return res.redirect('/admin/tests');
        }

        if (!Array.isArray(ids) || ids.length === 0) {
            req.session.flashError = 'No tests selected for deletion';
            return res.redirect('/admin/tests');
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Create placeholders for the IN clause
            const placeholders = ids.map(() => '?').join(',');

            // Soft delete the exams by setting is_deleted = 1 and deleted_at = NOW()
            const [result] = await db.query(
                `UPDATE exams
                 SET is_deleted = 1,
                     deleted_at = NOW(),
                     updated_at = NOW()
                 WHERE exam_id IN (${placeholders})`,
                ids
            );

            // Commit transaction
            await db.query('COMMIT');

            // Set success message
            req.session.flashSuccess = `${result.affectedRows} test(s) moved to trash successfully`;
            return res.redirect('/admin/tests');

        } catch (error) {
            // Rollback transaction on error
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error soft deleting tests:', error);
        req.session.flashError = error.message || 'Error moving tests to trash';
        return res.redirect('/admin/tests');
    }
});

// Restore test from trash (admin only)
router.post('/admin/restore', checkAdmin, async (req, res) => {
    try {
        const { examId } = req.body;

        if (!examId) {
            req.session.flashError = 'Test ID is required';
            return res.redirect('/admin/tests/trash');
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Restore the exam by setting is_deleted = 0 and clearing deleted_at
            const [result] = await db.query(
                `UPDATE exams
                 SET is_deleted = 0,
                     deleted_at = NULL,
                     updated_at = NOW()
                 WHERE exam_id = ?`,
                [examId]
            );

            // Commit transaction
            await db.query('COMMIT');

            // Set success message
            req.session.flashSuccess = 'Test restored successfully';
            return res.redirect('/admin/tests');

        } catch (error) {
            // Rollback transaction on error
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error restoring test:', error);
        req.session.flashError = error.message || 'Error restoring test';
        return res.redirect('/admin/tests/trash');
    }
});

// Permanently delete test (admin only)
router.post('/admin/permanent-delete', checkAdmin, async (req, res) => {
    try {
        const { examId } = req.body;

        if (!examId) {
            req.session.flashError = 'Test ID is required';
            return res.redirect('/admin/tests/trash');
        }

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            // Create placeholders for the IN clause
            const placeholders = '?';

            // Delete all related records in correct order
            await db.query(
                `DELETE FROM options
                 WHERE question_id IN (
                     SELECT question_id FROM questions
                     WHERE section_id IN (
                         SELECT section_id FROM sections
                         WHERE exam_id = ?
                     )
                 )`,
                [examId]
            );

            await db.query(
                `DELETE FROM questions
                 WHERE section_id IN (
                     SELECT section_id FROM sections
                     WHERE exam_id = ?
                 )`,
                [examId]
            );

            await db.query(
                `DELETE FROM sections
                 WHERE exam_id = ?`,
                [examId]
            );

            // Get all attempt IDs for the exam we're deleting
            const [attempts] = await db.query(
                `SELECT attempt_id FROM exam_attempts
                 WHERE exam_id = ?`,
                [examId]
            );

            if (attempts.length > 0) {
                // Create placeholders for attempt IDs
                const attemptIds = attempts.map(a => a.attempt_id);
                const attemptPlaceholders = attemptIds.map(() => '?').join(',');

                // Delete from user_answers table
                await db.query(
                    `DELETE FROM user_answers
                     WHERE attempt_id IN (${attemptPlaceholders})`,
                    attemptIds
                );

                // Delete from attempt_statistics table
                await db.query(
                    `DELETE FROM attempt_statistics
                     WHERE attempt_id IN (${attemptPlaceholders})`,
                    attemptIds
                );

                // Delete from user_performance table related to these attempts
                await db.query(
                    `DELETE FROM user_performance
                     WHERE attempt_id IN (${attemptPlaceholders})`,
                    attemptIds
                );

                // Also delete user_performance records related to this exam
                await db.query(
                    `DELETE FROM user_performance
                     WHERE exam_id = ?`,
                    [examId]
                );

                // Now delete from exam_attempts
                await db.query(
                    `DELETE FROM exam_attempts
                     WHERE exam_id = ?`,
                    [examId]
                );
            }

            // Finally delete the exam
            const [result] = await db.query(
                `DELETE FROM exams
                 WHERE exam_id = ?`,
                [examId]
            );

            // Commit transaction
            await db.query('COMMIT');

            // Set success message
            req.session.flashSuccess = 'Test permanently deleted successfully';
            return res.redirect('/admin/tests/trash');

        } catch (error) {
            // Rollback transaction on error
            await db.query('ROLLBACK');
            throw error;
        }

    } catch (error) {
        console.error('Error permanently deleting test:', error);
        req.session.flashError = error.message || 'Error permanently deleting test';
        return res.redirect('/admin/tests/trash');
    }
});

// Add a catch-all error handler for database errors
router.use(async (err, req, res, next) => {
    console.error('Caught an error in test-routes:', err);

    // Process specific database errors
    if (err.code === 'ER_NO_SUCH_TABLE') {
        req.session.flashError = `Database table does not exist: ${err.sqlMessage}`;
        return res.redirect('/tests');
    }

    if (err.code === 'ER_BAD_FIELD_ERROR') {
        req.session.flashError = `Database column does not exist: ${err.sqlMessage}`;
        return res.redirect('/tests');
    }

    // For any other error
    req.session.flashError = `An error occurred: ${err.message}`;
    res.redirect('/tests');
});

// Request additional access to a test
router.get('/request-access/:examId', checkAuthenticated, async (req, res) => {
    try {
        const userId = req.session.userId;
        const examId = req.params.examId;

        // Get test details
        const [exams] = await db.query(
            'SELECT * FROM exams WHERE exam_id = ? AND status = "published" AND (is_deleted = 0 OR is_deleted IS NULL)',
            [examId]
        );

        if (exams.length === 0) {
            req.session.flashError = 'Test not found or not available';
            return res.redirect('/tests');
        }

        // Check if user already has a pending request
        const [existingRequests] = await db.query(
            'SELECT * FROM access_requests WHERE exam_id = ? AND user_id = ? AND status = "pending"',
            [examId, userId]
        );

        if (existingRequests.length > 0) {
            req.session.flashError = 'You already have a pending request for this test';
            return res.redirect('/tests');
        }

        // Render the request access form
        res.render('tests/request-access', {
            title: `Request Access - ${exams[0].exam_name}`,
            exam: exams[0],
            layout: 'default'
        });
    } catch (error) {
        console.error('Error requesting access:', error);
        req.session.flashError = 'An error occurred while processing your request';
        res.redirect('/tests');
    }
});

// Submit access request
router.post('/request-access/:examId', checkAuthenticated, async (req, res) => {
    try {
        const userId = req.session.userId;
        const examId = req.params.examId;
        const { reason } = req.body;

        // Validate input
        if (!reason || reason.trim() === '') {
            req.session.flashError = 'Please provide a reason for your request';
            return res.redirect(`/tests/request-access/${examId}`);
        }

        // Check if user already has a pending request
        const [existingRequests] = await db.query(
            'SELECT * FROM access_requests WHERE exam_id = ? AND user_id = ? AND status = "pending"',
            [examId, userId]
        );

        if (existingRequests.length > 0) {
            req.session.flashError = 'You already have a pending request for this test';
            return res.redirect('/tests');
        }

        // Insert the access request
        await db.query(
            'INSERT INTO access_requests (exam_id, user_id, reason, requested_at, status) VALUES (?, ?, ?, NOW(), "pending")',
            [examId, userId, reason]
        );

        req.session.flashSuccess = 'Your access request has been submitted and is pending approval';
        res.redirect('/tests');
    } catch (error) {
        console.error('Error submitting access request:', error);
        req.session.flashError = 'An error occurred while processing your request';
        res.redirect('/tests');
    }
});

// Test Results Page
router.get('/results', checkAuthenticated, async (req, res) => {
    try {
        const userId = req.session.userId;

        // Get all completed test attempts for the user
        const [attempts] = await db.query(`
            SELECT
                ea.*, e.exam_name, e.duration, e.total_questions, e.passing_marks,
                DATE_FORMAT(ea.start_time, '%d-%b-%Y %H:%i:%s') as formatted_start_time,
                DATE_FORMAT(ea.end_time, '%d-%b-%Y %H:%i:%s') as formatted_end_time
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ? AND ea.status = 'completed'
            ORDER BY ea.end_time DESC
        `, [userId]);

        res.render('tests/results', {
            title: 'Test Results',
            attempts: attempts,
            formatDate: formatDate,
            formatDateTime: formatDateTime
        });
    } catch (error) {
        console.error('Error fetching test results:', error);
        req.session.flashError = 'An error occurred while fetching your test results';
        res.redirect('/tests');
    }
});

// Test Performance Page
router.get('/performance', checkAuthenticated, async (req, res) => {
    try {
        const userId = req.session.userId;

        // Get overall performance statistics
        const [overallStats] = await db.query(`
            SELECT
                COUNT(*) as total_tests,
                SUM(CASE WHEN score >= passing_marks THEN 1 ELSE 0 END) as tests_passed,
                AVG(score) as average_score,
                AVG(TIMESTAMPDIFF(MINUTE, start_time, end_time)) as average_time_taken
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ? AND ea.status = 'completed'
        `, [userId]);

        // Get performance by subject/category
        const [categoryStats] = await db.query(`
            SELECT
                e.category,
                COUNT(*) as attempts,
                AVG(ea.score) as average_score,
                SUM(CASE WHEN ea.score >= e.passing_marks THEN 1 ELSE 0 END) as passed
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ? AND ea.status = 'completed'
            GROUP BY e.category
            ORDER BY attempts DESC
        `, [userId]);

        // Get recent test attempts
        const [recentAttempts] = await db.query(`
            SELECT
                ea.*, e.exam_name, e.passing_marks,
                DATE_FORMAT(ea.end_time, '%d-%b-%Y %H:%i:%s') as formatted_end_time
            FROM exam_attempts ea
            JOIN exams e ON ea.exam_id = e.exam_id
            WHERE ea.user_id = ? AND ea.status = 'completed'
            ORDER BY ea.end_time DESC
            LIMIT 5
        `, [userId]);

        // Get improvement over time
        const [improvementData] = await db.query(`
            SELECT
                DATE_FORMAT(ea.end_time, '%Y-%m') as month,
                AVG(ea.score) as average_score,
                COUNT(*) as attempts
            FROM exam_attempts ea
            WHERE ea.user_id = ? AND ea.status = 'completed'
            GROUP BY month
            ORDER BY month ASC
        `, [userId]);

        res.render('tests/performance-new', {
            title: 'Test Performance',
            overallStats: overallStats[0],
            categoryStats: categoryStats,
            recentAttempts: recentAttempts,
            improvementData: improvementData,
            formatDate: formatDate,
            formatDateTime: formatDateTime
        });
    } catch (error) {
        console.error('Error fetching performance data:', error);
        req.session.flashError = 'An error occurred while fetching your performance data';
        res.redirect('/tests');
    }
});

module.exports = router;
