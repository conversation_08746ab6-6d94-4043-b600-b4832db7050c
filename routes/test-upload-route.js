const express = require('express');
const router = express.Router();
const fileUpload = require('express-fileupload');

// Configure express-fileupload for this route only
router.use(fileUpload({
    createParentPath: true,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    debug: true
}));

// Test upload route
router.get('/', (req, res) => {
    res.render('test-upload', {
        title: 'Test File Upload',
        pageTitle: 'Test File Upload'
    });
});

// Handle file upload
router.post('/upload', (req, res) => {
    console.log('TEST UPLOAD REQUEST RECEIVED');
    console.log('Request headers:', req.headers);
    console.log('Content-Type:', req.headers['content-type']);
    
    // Log files object
    if (req.files) {
        console.log('Files received:', Object.keys(req.files));
        if (req.files.test_file) {
            console.log('Test file details:', {
                name: req.files.test_file.name,
                size: req.files.test_file.size,
                mimetype: req.files.test_file.mimetype
            });
        } else {
            console.log('No test_file field in files object');
        }
    } else {
        console.log('No files object in request');
    }
    
    // Log body
    console.log('Request body:', req.body);
    
    if (!req.files || !req.files.test_file) {
        return res.status(400).send('No file was uploaded');
    }
    
    const file = req.files.test_file;
    
    // Send success response
    res.send(`File uploaded successfully: ${file.name}`);
});

module.exports = router;
