const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directory exists
const uploadDir = path.join(__dirname, '../public/uploads/test');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Basic storage configuration
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname) || '.jpg';
        cb(null, 'test-' + uniqueSuffix + ext);
    }
});

// Create a simple multer instance
const upload = multer({ 
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});

// Test upload page
router.get('/', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test File Upload</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
                .form-group { margin-bottom: 15px; }
                label { display: block; margin-bottom: 5px; }
                input[type="file"] { display: block; margin-bottom: 10px; }
                button { background: #4CAF50; color: white; border: none; padding: 10px 15px; cursor: pointer; }
                .result { margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>Test File Upload</h1>
            <form action="/test-upload/upload" method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="test_file">Select a file to upload:</label>
                    <input type="file" id="test_file" name="test_file">
                </div>
                <button type="submit">Upload File</button>
            </form>
            <div class="result">
                <h2>Result</h2>
                <p>${req.query.message || 'No upload attempted yet'}</p>
                ${req.query.file ? `<p>Uploaded file: ${req.query.file}</p><img src="${req.query.file}" style="max-width: 100%; max-height: 300px;">` : ''}
            </div>
        </body>
        </html>
    `);
});

// Handle file upload
router.post('/upload', (req, res) => {
    // Use single file upload without middleware to handle errors manually
    const uploadSingle = upload.single('test_file');
    
    uploadSingle(req, res, function(err) {
        let message = '';
        let file = '';
        
        if (err) {
            console.error('Upload error:', err);
            if (err instanceof multer.MulterError) {
                message = `Multer error: ${err.code} - ${err.message}`;
            } else {
                message = `Upload error: ${err.message}`;
            }
        } else if (!req.file) {
            message = 'No file selected or file field name does not match';
        } else {
            message = 'File uploaded successfully!';
            file = `/uploads/test/${req.file.filename}`;
            console.log('File details:', {
                originalname: req.file.originalname,
                mimetype: req.file.mimetype,
                size: req.file.size,
                filename: req.file.filename,
                path: req.file.path
            });
        }
        
        // Redirect back to the test page with result
        res.redirect(`/test-upload?message=${encodeURIComponent(message)}${file ? `&file=${encodeURIComponent(file)}` : ''}`);
    });
});

module.exports = router;
