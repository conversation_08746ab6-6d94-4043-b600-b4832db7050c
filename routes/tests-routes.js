router.get('/take/:id', async (req, res, next) => {
    try {
        const attemptId = req.params.id;
        
        // Get attempt details
        const [attempts] = await db.query(
            'SELECT * FROM test_attempts WHERE id = ? AND user_id = ?',
            [attemptId, req.user.id]
        );
        
        if (attempts.length === 0) {
            req.session.flashError = 'Test attempt not found';
            return res.redirect('/tests');
        }
        
        const attempt = attempts[0];
        
        // Get exam details
        const [exams] = await db.query(
            'SELECT * FROM exams WHERE id = ?',
            [attempt.exam_id]
        );
        
        if (exams.length === 0) {
            req.session.flashError = 'Exam not found';
            return res.redirect('/tests');
        }
        
        const exam = exams[0];
        
        // Get sections and questions
        const [sections] = await db.query(
            'SELECT * FROM sections WHERE exam_id = ? ORDER BY order_index',
            [exam.id]
        );
        
        // Get questions for each section
        for (let section of sections) {
            const [questions] = await db.query(
                `SELECT q.*, 
                    CASE WHEN ta.answer IS NOT NULL THEN ta.answer ELSE '' END as user_answer,
                    CASE WHEN ta.is_bookmarked = 1 THEN 1 ELSE 0 END as is_bookmarked
                FROM questions q
                LEFT JOIN test_answers ta ON q.id = ta.question_id AND ta.attempt_id = ?
                WHERE q.section_id = ?
                ORDER BY q.order_index`,
                [attemptId, section.id]
            );
            
            // Get options for each question
            for (let question of questions) {
                const [options] = await db.query(
                    'SELECT * FROM options WHERE question_id = ? ORDER BY position',
                    [question.id]
                );
                question.options = options;
            }
            
            section.questions = questions;
        }
        
        // Render the new test interface
        res.render('tests/take_new', {
            title: exam.exam_name,
            exam: exam,
            sections: sections,
            attemptId: attemptId,
            user: req.user
        });
    } catch (error) {
        console.error('Error loading test:', error);
        next(error);
    }
}); 