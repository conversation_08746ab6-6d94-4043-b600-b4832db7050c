const express = require('express');
const router = express.Router();
const Test = require('../models/Test');
const { validateTestData } = require('../validators/testValidator');

// Save draft endpoint
router.post('/admin/save-draft', async (req, res) => {
    try {
        const { isDraft, ...testData } = req.body;

        // Validate test data
        const { error } = validateTestData(testData);
        if (error) {
            return res.status(400).json({ 
                success: false, 
                message: error.details[0].message 
            });
        }

        // Save or update draft
        if (req.body.testId) {
            // Update existing draft
            await Test.findByIdAndUpdate(req.body.testId, {
                ...testData,
                isDraft: true,
                updatedAt: new Date()
            });
        } else {
            // Create new draft
            const test = new Test({
                ...testData,
                isDraft: true
            });
            await test.save();
        }

        res.json({ success: true });
    } catch (err) {
        console.error('Error saving draft:', err);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error' 
        });
    }
});
// Add this route for searching groups and members
router.get('/search/group-member', async (req, res) => {
    const query = req.query.q;
    try {
        const [results] = await db.query(`
            SELECT id, CONCAT(first_name, ' ', last_name) AS name 
            FROM users 
            WHERE first_name LIKE ? OR last_name LIKE ?
        `, [`%${query}%`, `%${query}%`]);
        res.json(results);
    } catch (error) {
        console.error('Error searching groups/members:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});
module.exports = router;
