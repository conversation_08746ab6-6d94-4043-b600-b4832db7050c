const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { checkAuthenticated, checkAdmin } = require('../middleware/auth');
const sharp = require('sharp');

// Create uploads directory if it doesn't exist
const questionImagesDir = path.join(__dirname, '../public/uploads/questions');
fs.mkdirSync(questionImagesDir, { recursive: true });

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, questionImagesDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, 'question-' + uniqueSuffix + ext);
    }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('Only image files are allowed!'), false);
    }
};

// Configure multer upload
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: fileFilter
});

// Route for uploading question images
router.post('/question-image', checkAuthenticated, upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ success: false, message: 'No image file provided' });
        }

        // Optimize the image without resizing to preserve original dimensions
        const optimizedImagePath = path.join(questionImagesDir, 'optimized-' + req.file.filename);

        await sharp(req.file.path)
            .withMetadata() // Preserve metadata including orientation
            .jpeg({ quality: 85 }) // Higher quality to preserve details
            .toFile(optimizedImagePath);

        // Delete the original file
        fs.unlinkSync(req.file.path);

        // Rename the optimized file to the original filename
        fs.renameSync(optimizedImagePath, req.file.path);

        // Return the URL to the uploaded image
        const imageUrl = '/uploads/questions/' + req.file.filename;

        res.json({
            success: true,
            imageUrl: imageUrl,
            message: 'Image uploaded successfully'
        });
    } catch (error) {
        console.error('Error uploading image:', error);
        res.status(500).json({
            success: false,
            message: 'Error uploading image',
            error: error.message
        });
    }
});

module.exports = router;
