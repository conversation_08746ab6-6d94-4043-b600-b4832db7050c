/**
 * <PERSON><PERSON><PERSON> to run the fix foreign keys migration
 */

const { spawn } = require('child_process');

console.log('Running fix foreign keys migration...');

const migration = spawn('node', ['migrations/fix-foreign-keys.js']);

migration.stdout.on('data', (data) => {
  console.log(`${data}`);
});

migration.stderr.on('data', (data) => {
  console.error(`${data}`);
});

migration.on('close', (code) => {
  console.log(`Migration process exited with code ${code}`);
  
  if (code === 0) {
    console.log('Migration completed successfully!');
    console.log('Foreign key constraints have been fixed.');
  } else {
    console.error('Migration failed. Please check the error messages above.');
  }
});
