const db = require('./config/database');
const fs = require('fs');
const path = require('path');

async function runMigration() {
    try {
        console.log('Running migration to add scheduled status...');
        
        // Read the migration file
        const migrationPath = path.join(__dirname, 'database/migrations/add_scheduled_status.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Execute the migration
        await db.query(migrationSQL);
        
        console.log('Migration completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error running migration:', error);
        process.exit(1);
    }
}

runMigration();
