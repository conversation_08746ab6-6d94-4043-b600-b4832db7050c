const migration = require('./migrations/create_roles_permissions_tables');

async function runMigration() {
    try {
        console.log('Running roles and permissions migration...');
        await migration.up();
        console.log('Migration completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
}

runMigration();
