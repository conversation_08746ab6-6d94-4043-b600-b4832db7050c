/**
 * <PERSON><PERSON>t to run all subject-related migrations in sequence
 */

const { spawn } = require('child_process');
const path = require('path');

const migrations = [
  'migrations/update-arts-subjects.js',
  'migrations/update-student-subjects-and-plans.js',
  'migrations/create-student-timetable.js',
  'migrations/update-student-api-for-subject-groups.js'
];

async function runMigration(migrationPath) {
  return new Promise((resolve, reject) => {
    console.log(`\n=== Running migration: ${migrationPath} ===\n`);
    
    const fullPath = path.join(__dirname, migrationPath);
    const process = spawn('node', [fullPath], { stdio: 'inherit' });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`\n=== Migration ${migrationPath} completed successfully ===\n`);
        resolve();
      } else {
        console.error(`\n=== Migration ${migrationPath} failed with code ${code} ===\n`);
        reject(new Error(`Migration ${migrationPath} failed with code ${code}`));
      }
    });
    
    process.on('error', (err) => {
      console.error(`\n=== Error executing migration ${migrationPath}: ${err.message} ===\n`);
      reject(err);
    });
  });
}

async function runAllMigrations() {
  try {
    for (const migration of migrations) {
      await runMigration(migration);
    }
    console.log('\n=== All migrations completed successfully ===\n');
    process.exit(0);
  } catch (error) {
    console.error('\n=== Migration process failed ===\n');
    console.error(error);
    process.exit(1);
  }
}

runAllMigrations();
