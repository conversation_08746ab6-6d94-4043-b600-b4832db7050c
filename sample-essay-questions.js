const db = require('./config/database');

async function createSampleEssayQuestions(forceCreate = true) {
    try {
        console.log('Creating sample essay questions and K12 test...');

        // Get admin user ID
        const [adminUsers] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (adminUsers.length === 0) {
            throw new Error('No admin user found. Please create an admin user first.');
        }

        const adminId = adminUsers[0].id;

        // Get the essays we created earlier
        const [essays] = await db.query('SELECT essay_id, title FROM essays');
        if (essays.length < 2) {
            throw new Error('Sample essays not found. Please run the sample-essays.js script first.');
        }

        // Create a K12 test category if it doesn't exist
        let k12CategoryId;
        const [existingCategories] = await db.query('SELECT category_id FROM categories WHERE name = "K12"');

        if (existingCategories.length > 0) {
            k12CategoryId = existingCategories[0].category_id;
        } else {
            const [result] = await db.query('INSERT INTO categories (name, description) VALUES (?, ?)',
                ['K12', 'K-12 Education Tests']);
            k12CategoryId = result.insertId;
        }

        // Check if the exam already exists
        const examName = 'K12 Reading Comprehension Test';
        const [existingExams] = await db.query('SELECT exam_id FROM exams WHERE exam_name = ?', [examName]);

        let examId;
        if (existingExams.length > 0) {
            console.log(`Exam '${examName}' already exists, using existing exam.`);
            examId = existingExams[0].exam_id;
        } else {
            // Create a new exam for K12 with essay sections
            const [examResult] = await db.query(
                'INSERT INTO exams (exam_name, description, category_id, status, created_by) VALUES (?, ?, ?, ?, ?)',
                [
                    examName,
                    'A comprehensive test to assess reading comprehension skills with essay-based questions',
                    k12CategoryId,
                    'published',
                    adminId
                ]
            );

            examId = examResult.insertId;
        }

        // Check if sections already exist for this exam
        const [existingSections] = await db.query(
            'SELECT section_id, section_name FROM sections WHERE exam_id = ?',
            [examId]
        );

        // Create sections for each essay
        const sections = [];
        for (let i = 0; i < essays.length; i++) {
            const sectionName = `Section ${i+1}: ${essays[i].title}`;

            // Check if this section already exists
            const existingSection = existingSections.find(s => s.section_name === sectionName);

            let sectionId;
            if (existingSection) {
                console.log(`Section '${sectionName}' already exists, using existing section.`);
                sectionId = existingSection.section_id;
            } else {
                // Create new section
                const [sectionResult] = await db.query(
                    'INSERT INTO sections (exam_id, section_name, position) VALUES (?, ?, ?)',
                    [
                        examId,
                        sectionName,
                        i+1
                    ]
                );
                sectionId = sectionResult.insertId;
            }

            sections.push({
                section_id: sectionId,
                essay_id: essays[i].essay_id,
                title: essays[i].title
            });
        }

        // Sample questions for the Climate Change essay
        const climateChangeQuestions = [
            {
                question_type: 'multiple_choice',
                question_text: 'According to the essay, which ecosystem is warming at approximately twice the global average rate?',
                options: [
                    { option_text: 'Tropical rainforests', is_correct: false },
                    { option_text: 'Arctic ecosystems', is_correct: true },
                    { option_text: 'Marine ecosystems', is_correct: false },
                    { option_text: 'Freshwater ecosystems', is_correct: false }
                ],
                correct_answer: '1', // Index of the correct option
                solution_text: 'The essay states that "The Arctic is warming at approximately twice the global average rate."',
                marks: 1
            },
            {
                question_type: 'true_false',
                question_text: 'Ocean acidification is primarily caused by increased absorption of atmospheric CO2.',
                correct_answer: 'true',
                solution_text: 'The essay mentions that "Ocean warming and acidification, caused by increased absorption of atmospheric CO2, are threatening coral reefs."',
                marks: 1
            },
            {
                question_type: 'essay', // Using 'essay' type for fill-up questions since the database doesn't support 'fill_up' type
                question_text: 'Climate change is altering the timing of seasonal events, such as ________ and migration.',
                correct_answer: 'flowering',
                solution_text: 'The essay states that "In terrestrial ecosystems, climate change is altering the timing of seasonal events, such as flowering and migration."',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which of the following is NOT mentioned as an impact of climate change on freshwater ecosystems?',
                options: [
                    { option_text: 'Altered precipitation patterns', is_correct: false },
                    { option_text: 'Increased water temperatures', is_correct: false },
                    { option_text: 'Changes in river flow timing', is_correct: false },
                    { option_text: 'Increased water clarity', is_correct: true }
                ],
                correct_answer: '3', // Index of the correct option
                solution_text: 'The essay mentions altered precipitation patterns, increased water temperatures, and changes in river flows, but does not mention increased water clarity.',
                marks: 1
            },
            {
                question_type: 'true_false',
                question_text: 'According to the essay, conservation efforts should focus solely on reducing greenhouse gas emissions.',
                correct_answer: 'false',
                solution_text: 'The essay states that addressing climate change requires "both mitigation strategies to reduce greenhouse gas emissions and adaptation measures to help ecosystems and human communities cope with unavoidable changes."',
                marks: 1
            }
        ];

        // Sample questions for the AI essay
        const aiQuestions = [
            {
                question_type: 'multiple_choice',
                question_text: 'What technological advancement was crucial for the success of deep learning in the 2010s?',
                options: [
                    { option_text: 'The invention of the internet', is_correct: false },
                    { option_text: 'Significant increases in computing power', is_correct: true },
                    { option_text: 'The development of quantum computers', is_correct: false },
                    { option_text: 'The creation of social media platforms', is_correct: false }
                ],
                correct_answer: '1', // Index of the correct option
                solution_text: 'The essay mentions that deep learning was enabled by "vast amounts of digital data, significant increases in computing power, and algorithmic innovations."',
                marks: 1
            },
            {
                question_type: 'essay', // Using 'essay' type for fill-up questions since the database doesn't support 'fill_up' type
                question_text: 'The period of reduced funding and interest in AI research in the 1970s became known as the "AI ________."',
                correct_answer: 'winter',
                solution_text: "The essay states that the limitations of early AI approaches 'led to what became known as the AI winter, a period of reduced funding and interest in AI research.'",
                marks: 1
            },
            {
                question_type: 'true_false',
                question_text: 'According to the essay, modern AI systems have achieved general intelligence comparable to humans.',
                correct_answer: 'false',
                solution_text: 'The essay states that "Current systems excel at narrow tasks but lack the general intelligence and common sense reasoning that humans possess."',
                marks: 1
            },
            {
                question_type: 'multiple_choice',
                question_text: 'Which of the following was characteristic of early AI in the 1950s and 1960s?',
                options: [
                    { option_text: 'Deep learning models', is_correct: false },
                    { option_text: 'Neural networks with many layers', is_correct: false },
                    { option_text: 'Rule-based systems and symbolic reasoning', is_correct: true },
                    { option_text: 'Reinforcement learning algorithms', is_correct: false }
                ],
                correct_answer: '2', // Index of the correct option
                solution_text: 'The essay states that "The early days of AI in the 1950s and 1960s were characterized by rule-based systems and symbolic reasoning."',
                marks: 1
            },
            {
                question_type: 'essay', // Using 'essay' type for fill-up questions since the database doesn't support 'fill_up' type
                question_text: 'The 1980s saw the rise of ________ systems, which attempted to capture the knowledge and decision-making processes of human experts in specific domains.',
                correct_answer: 'expert',
                solution_text: 'The essay mentions that "The 1980s saw the rise of expert systems, which attempted to capture the knowledge and decision-making processes of human experts in specific domains."',
                marks: 1
            }
        ];

        // Insert questions for each section
        const allQuestions = [
            { section: sections[0], questions: climateChangeQuestions },
            { section: sections[1], questions: aiQuestions }
        ];

        for (const section of allQuestions) {
            console.log(`Adding questions for section: ${section.section.title}`);

            // Check if questions already exist for this section
            const [existingQuestions] = await db.query(
                'SELECT question_id, question_text FROM questions WHERE section_id = ? AND essay_id = ?',
                [section.section.section_id, section.section.essay_id]
            );

            if (!forceCreate && existingQuestions.length > 0) {
                console.log(`Found ${existingQuestions.length} existing questions for this section, skipping question creation.`);
                continue;
            } else if (existingQuestions.length > 0) {
                console.log(`Found ${existingQuestions.length} existing questions for this section, but force creating new ones.`);
            }

            for (const q of section.questions) {
                // Insert the question
                const [questionResult] = await db.query(
                    `INSERT INTO questions (
                        section_id,
                        question_type,
                        question_text,
                        correct_answer,
                        solution_text,
                        marks,
                        essay_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [
                        section.section.section_id,
                        q.question_type,
                        q.question_text,
                        q.correct_answer,
                        q.solution_text,
                        q.marks,
                        section.section.essay_id
                    ]
                );

                const questionId = questionResult.insertId;

                // If it's a multiple choice question, insert the options
                if (q.question_type === 'multiple_choice' && q.options) {
                    // Insert options into the options table
                    for (let i = 0; i < q.options.length; i++) {
                        const option = q.options[i];
                        await db.query(
                            'INSERT INTO options (question_id, option_text, is_correct, position) VALUES (?, ?, ?, ?)',
                            [questionId, option.option_text, option.is_correct ? 1 : 0, i + 1]
                        );
                    }
                }
            }
        }

        console.log('Sample essay questions and K12 test created successfully!');
        console.log(`Created exam ID: ${examId}`);
        console.log(`Created sections: ${sections.map(s => s.section_id).join(', ')}`);
        process.exit(0);
    } catch (error) {
        console.error('Error creating sample essay questions:', error);
        process.exit(1);
    }
}

// Run the function
createSampleEssayQuestions();
