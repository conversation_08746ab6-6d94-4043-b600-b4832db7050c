const db = require('./config/database');

async function insertSampleEssays() {
    try {
        console.log('Inserting sample essays...');
        
        // Get admin user ID
        const [adminUsers] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (adminUsers.length === 0) {
            throw new Error('No admin user found. Please create an admin user first.');
        }
        
        const adminId = adminUsers[0].id;
        
        // Sample essays
        const essays = [
            {
                title: "The Impact of Climate Change on Global Ecosystems",
                content: `Climate change is one of the most pressing challenges facing our planet today. Rising global temperatures, changing precipitation patterns, and increasing frequency of extreme weather events are having profound effects on ecosystems worldwide.

In terrestrial ecosystems, climate change is altering the timing of seasonal events, such as flowering and migration. Many plant species are flowering earlier in spring, while some migratory birds are arriving at breeding grounds sooner. These phenological changes can lead to mismatches between interdependent species, such as plants and their pollinators, or predators and their prey.

Marine ecosystems are particularly vulnerable to climate change impacts. Ocean warming and acidification, caused by increased absorption of atmospheric CO2, are threatening coral reefs—one of the most diverse ecosystems on Earth. Coral bleaching events have become more frequent and severe, leading to widespread reef degradation.

Arctic ecosystems are experiencing some of the most rapid and dramatic changes. The Arctic is warming at approximately twice the global average rate, resulting in extensive sea ice loss. This affects not only iconic species like polar bears but also alters entire food webs and impacts indigenous communities that depend on these ecosystems.

Freshwater ecosystems face challenges from altered precipitation patterns and increased water temperatures. Changes in the timing and volume of river flows affect aquatic species' life cycles, while warmer waters can reduce oxygen levels and favor invasive species.

Addressing climate change requires both mitigation strategies to reduce greenhouse gas emissions and adaptation measures to help ecosystems and human communities cope with unavoidable changes. Conservation efforts increasingly need to consider climate resilience, such as protecting climate refugia and establishing ecological corridors to facilitate species movement.

The interconnected nature of climate change impacts highlights the importance of ecosystem-based approaches to adaptation and the need for integrated management of natural resources. By understanding and addressing these complex interactions, we can work toward more effective conservation and sustainable development in a changing world.`,
                created_by: adminId
            },
            {
                title: "The Evolution of Artificial Intelligence: From Rule-Based Systems to Deep Learning",
                content: `Artificial Intelligence (AI) has undergone a remarkable evolution since its inception in the mid-20th century. This journey from simple rule-based systems to sophisticated deep learning models represents one of the most significant technological transformations in human history.

The early days of AI in the 1950s and 1960s were characterized by rule-based systems and symbolic reasoning. These systems relied on explicit programming of logical rules and knowledge representation. While they showed promise in controlled environments, they struggled with the complexity and ambiguity of real-world problems. The limitations of these early approaches led to what became known as the "AI winter," a period of reduced funding and interest in AI research.

The 1980s saw the rise of expert systems, which attempted to capture the knowledge and decision-making processes of human experts in specific domains. These systems found applications in fields like medicine and finance but still faced challenges in adapting to new situations or learning from experience.

A paradigm shift occurred with the emergence of machine learning, where algorithms could learn patterns from data rather than following explicitly programmed rules. Early machine learning approaches included decision trees, support vector machines, and neural networks with limited layers. These methods demonstrated the potential for computers to improve their performance through experience.

The true breakthrough came in the 2010s with the advent of deep learning, powered by neural networks with many layers (hence "deep"). This approach was enabled by three key factors: vast amounts of digital data, significant increases in computing power, and algorithmic innovations. Deep learning has achieved remarkable success in areas previously considered challenging for AI, such as image and speech recognition, natural language processing, and game playing.

Today, AI systems based on deep learning are ubiquitous in our daily lives, from virtual assistants and recommendation systems to autonomous vehicles and medical diagnostics. The field continues to advance rapidly, with developments in areas such as reinforcement learning, generative models, and multimodal learning.

Despite these advances, modern AI still faces significant challenges. Current systems excel at narrow tasks but lack the general intelligence and common sense reasoning that humans possess. They require large amounts of data and energy, can perpetuate or amplify biases present in their training data, and often function as "black boxes" with limited explainability.

The future of AI will likely involve addressing these limitations while continuing to expand AI's capabilities and applications. This includes developing more efficient and environmentally sustainable models, ensuring ethical and responsible AI deployment, and potentially moving toward more general forms of artificial intelligence.

The evolution of AI from simple rule-based systems to sophisticated deep learning models illustrates the field's remarkable progress and hints at its vast potential for future innovation and impact across virtually all domains of human activity.`,
                created_by: adminId
            }
        ];
        
        // Insert essays
        for (const essay of essays) {
            await db.query(
                'INSERT INTO essays (title, content, created_by) VALUES (?, ?, ?)',
                [essay.title, essay.content, essay.created_by]
            );
        }
        
        console.log('Sample essays inserted successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error inserting sample essays:', error);
        process.exit(1);
    }
}

// Run the function
insertSampleEssays();
