/**
 * <PERSON><PERSON><PERSON> to add admin users as collaborators for all instruction plans
 * This ensures that admins can view and edit all instruction plans
 */

const db = require('../config/database');

async function addAdminCollaborators() {
  try {
    console.log('\n=== Database Configuration ===');
    console.log(`Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`User: ${process.env.DB_USER || 'root'}`);
    console.log(`Database: ${process.env.DB_NAME || 'exam_prep_platform'}`);
    console.log(`Port: ${process.env.DB_PORT || '3306'}`);
    console.log('===========================\n');

    console.log('Adding admin users as collaborators for all instruction plans...\n');
    
    // Get all admin users
    const [admins] = await db.query(`
      SELECT id FROM users
      WHERE role = 'admin' AND is_active = 1
    `);
    
    if (admins.length === 0) {
      console.log('⚠️ No admin users found');
      return;
    }
    
    console.log(`Found ${admins.length} admin users`);
    
    // Get all instruction plans
    const [plans] = await db.query(`
      SELECT id FROM instruction_plans
    `);
    
    if (plans.length === 0) {
      console.log('⚠️ No instruction plans found');
      return;
    }
    
    console.log(`Found ${plans.length} instruction plans`);
    
    // Begin transaction
    await db.query('START TRANSACTION');
    
    try {
      let addedCount = 0;
      
      // For each admin and plan, add a collaborator entry if it doesn't exist
      for (const admin of admins) {
        for (const plan of plans) {
          // Check if collaborator entry already exists
          const [existing] = await db.query(`
            SELECT id FROM plan_collaborators
            WHERE plan_id = ? AND user_id = ?
          `, [plan.id, admin.id]);
          
          if (existing.length === 0) {
            // Add admin as a collaborator with 'owner' role
            await db.query(`
              INSERT INTO plan_collaborators (plan_id, user_id, role)
              VALUES (?, ?, 'owner')
            `, [plan.id, admin.id]);
            
            addedCount++;
          }
        }
      }
      
      // Commit transaction
      await db.query('COMMIT');
      
      console.log(`✅ Added ${addedCount} collaborator entries for admin users`);
      console.log('✅ All admin users are now collaborators for all instruction plans');
      
    } catch (error) {
      // Rollback transaction on error
      await db.query('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('❌ Error adding admin collaborators:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
addAdminCollaborators();
