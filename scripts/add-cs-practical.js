/**
 * <PERSON><PERSON><PERSON> to add a practical to the practicals table
 * This will make the practical visible to students
 */

const db = require('../config/database');

async function addPractical() {
  try {
    console.log('Starting to add practical to practicals table...');

    // Begin transaction
    await db.query('START TRANSACTION');

    // 1. Find the CS student
    const [students] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csstudent']
    );

    if (students.length === 0) {
      console.log('CS student not found!');
      return;
    }

    const studentId = students[0].id;
    console.log(`Found CS student with ID: ${studentId}`);

    // 2. Find the CS teacher
    const [teachers] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csteacher']
    );

    if (teachers.length === 0) {
      console.log('CS teacher not found!');
      return;
    }

    const teacherId = teachers[0].id;
    console.log(`Found CS teacher with ID: ${teacherId}`);

    // 3. Find the Computer Science subject
    const [subjects] = await db.query(
      'SELECT id FROM subjects WHERE name = ?',
      ['Computer Science']
    );

    if (subjects.length === 0) {
      console.log('Computer Science subject not found!');
      return;
    }

    const subjectId = subjects[0].id;
    console.log(`Found Computer Science subject with ID: ${subjectId}`);

    // 4. Find the student's class
    const [classes] = await db.query(
      `SELECT c.id
       FROM classes c
       JOIN student_classes sc ON c.id = sc.class_id
       WHERE sc.student_id = ?`,
      [studentId]
    );

    if (classes.length === 0) {
      console.log('No classes found for student!');
      return;
    }

    const classId = classes[0].id;
    console.log(`Found class with ID: ${classId}`);

    // 5. Check if practicals table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
      AND TABLE_NAME = 'practicals'
    `);

    if (tables.length === 0) {
      console.log('Practicals table does not exist, creating it...');

      // Create practicals table
      await db.query(`
        CREATE TABLE IF NOT EXISTS practicals (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_id INT NOT NULL,
          subject_id INT NOT NULL,
          lab_id INT,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          description TEXT,
          prerequisites TEXT,
          materials TEXT,
          equipment TEXT,
          status ENUM('Upcoming', 'Completed', 'Cancelled') DEFAULT 'Upcoming',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
        )
      `);

      console.log('Created practicals table');
    }

    // 6. Check if labs table exists
    const [labTables] = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
      AND TABLE_NAME = 'labs'
    `);

    let labId = null;
    if (labTables.length === 0) {
      console.log('Labs table does not exist, creating it...');

      // Create labs table
      await db.query(`
        CREATE TABLE IF NOT EXISTS labs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          location VARCHAR(100),
          capacity INT,
          description TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log('Created labs table');

      // Add a lab
      const [labResult] = await db.query(`
        INSERT INTO labs (name, location, capacity, description)
        VALUES (?, ?, ?, ?)
      `, ['Computer Lab 1', 'Main Building, 2nd Floor', 30, 'Computer lab with 30 workstations']);

      labId = labResult.insertId;
      console.log(`Added lab with ID: ${labId}`);
    } else {
      // Check if lab exists
      const [labs] = await db.query(`
        SELECT id FROM labs WHERE name = ?
      `, ['Computer Lab 1']);

      if (labs.length === 0) {
        // Add a lab
        const [labResult] = await db.query(`
          INSERT INTO labs (name, location, capacity, description)
          VALUES (?, ?, ?, ?)
        `, ['Computer Lab 1', 'Main Building, 2nd Floor', 30, 'Computer lab with 30 workstations']);

        labId = labResult.insertId;
        console.log(`Added lab with ID: ${labId}`);
      } else {
        labId = labs[0].id;
        console.log(`Found lab with ID: ${labId}`);
      }
    }

    // 7. Check if topics table exists
    const [topicTables] = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
      AND TABLE_NAME = 'topics'
    `);

    if (topicTables.length === 0) {
      console.log('Topics table does not exist, creating it...');

      // Create topics table
      await db.query(`
        CREATE TABLE IF NOT EXISTS topics (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          subject_id INT,
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL
        )
      `);

      console.log('Created topics table');
    }

    // 8. Check the structure of the topics table
    const [topicColumns] = await db.query('SHOW COLUMNS FROM topics');
    console.log('Topics table columns:');
    topicColumns.forEach(col => console.log(`- ${col.Field} (${col.Type})`));

    // Add a topic
    const [topicResult] = await db.query(`
      INSERT INTO topics (name, is_active)
      VALUES (?, ?)
      ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
    `, ['Data Structures', 1]);

    const topicId = topicResult.insertId;
    console.log(`Added/found topic with ID: ${topicId}`);

    // 9. Add a practical
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    const [practicalResult] = await db.query(`
      INSERT INTO practicals (
        teacher_id, class_id, subject_id, lab_id, date, start_time, end_time,
        description, prerequisites, materials, equipment, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      teacherId,
      classId,
      subjectId,
      labId,
      tomorrowStr,
      '14:00:00',
      '16:00:00',
      'Implementation of basic data structures in Python',
      'Basic Python programming knowledge',
      'Python documentation, lecture notes',
      'Laptop with Python installed',
      'Upcoming'
    ]);

    const practicalId = practicalResult.insertId;
    console.log(`Added practical with ID: ${practicalId}`);

    // 10. Check if practical_topics table exists
    const [practicalTopicTables] = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'exam_prep_platform'
      AND TABLE_NAME = 'practical_topics'
    `);

    if (practicalTopicTables.length === 0) {
      console.log('Practical topics table does not exist, creating it...');

      // Create practical_topics table
      await db.query(`
        CREATE TABLE IF NOT EXISTS practical_topics (
          id INT AUTO_INCREMENT PRIMARY KEY,
          practical_id INT NOT NULL,
          topic_id INT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (practical_id) REFERENCES practicals(id) ON DELETE CASCADE,
          FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE,
          UNIQUE KEY (practical_id, topic_id)
        )
      `);

      console.log('Created practical_topics table');
    }

    // 11. Link practical to topic
    await db.query(`
      INSERT INTO practical_topics (practical_id, topic_id)
      VALUES (?, ?)
    `, [practicalId, topicId]);

    console.log(`Linked practical ${practicalId} to topic ${topicId}`);

    // Commit transaction
    await db.query('COMMIT');
    console.log('Successfully added practical to practicals table!');

  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');
    console.error('Error adding practical:', error);
    throw error;
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
addPractical();
