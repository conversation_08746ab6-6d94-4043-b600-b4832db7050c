const mysql = require('mysql2/promise');

async function addCSTeacherAchievements() {
    let connection;
    
    try {
        // Database connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'exam_prep_platform'
        });

        console.log('🔗 Connected to database');

        // Get CS Teacher's staff_id
        const [teacherResult] = await connection.execute(`
            SELECT s.id as staff_id, u.name, u.email
            FROM users u
            JOIN staff s ON u.id = s.user_id
            WHERE u.email = '<EMAIL>'
        `);

        if (teacherResult.length === 0) {
            console.log('❌ CS Teacher not found');
            return;
        }

        const staffId = teacherResult[0].staff_id;
        console.log(`✅ Found CS Teacher with staff_id: ${staffId}`);

        // Clear existing achievements for clean slate
        await connection.execute(`DELETE FROM staff_achievements WHERE staff_id = ?`, [staffId]);
        console.log('🧹 Cleared existing achievements');

        // 1. SOFTWARE DEVELOPMENT ACHIEVEMENTS
        const softwareAchievements = [
            {
                title: 'School Management System Development',
                category: 'software_development',
                description: 'Developed a comprehensive school management system using Node.js, MySQL, and React that streamlined administrative processes and improved efficiency by 40%',
                achievement_date: '2023-03-15',
                recognition_level: 'institutional',
                impact_description: 'Reduced administrative workload by 40%, improved data accuracy, and enhanced communication between teachers, students, and parents',
                skills_demonstrated: JSON.stringify(['Node.js', 'React', 'MySQL', 'System Design', 'Project Management']),
                evidence_url: 'https://github.com/school/management-system',
                is_verified: true
            },
            {
                title: 'Student Performance Analytics Dashboard',
                category: 'software_development',
                description: 'Created an AI-powered analytics dashboard that predicts student performance and identifies at-risk students using machine learning algorithms',
                achievement_date: '2022-11-20',
                recognition_level: 'district',
                impact_description: 'Helped identify 85% of at-risk students early, leading to targeted interventions and 25% improvement in pass rates',
                skills_demonstrated: JSON.stringify(['Python', 'Machine Learning', 'Data Analytics', 'Visualization', 'TensorFlow']),
                evidence_url: 'https://github.com/education/student-analytics',
                is_verified: true
            },
            {
                title: 'Online Examination Platform',
                category: 'software_development',
                description: 'Built a secure online examination platform with anti-cheating mechanisms, supporting 500+ concurrent users during COVID-19 pandemic',
                achievement_date: '2021-08-10',
                recognition_level: 'state',
                impact_description: 'Enabled seamless remote examinations for 2000+ students during pandemic, maintaining academic continuity',
                skills_demonstrated: JSON.stringify(['PHP', 'JavaScript', 'Security', 'Load Balancing', 'Database Optimization']),
                evidence_url: 'https://exam.punjab.gov.in',
                is_verified: true
            },
            {
                title: 'Mobile Learning App for Rural Students',
                category: 'software_development',
                description: 'Developed a mobile app providing offline access to educational content for students in areas with limited internet connectivity',
                achievement_date: '2022-05-30',
                recognition_level: 'national',
                impact_description: 'Reached 5000+ rural students, improved learning outcomes by 30% in remote areas',
                skills_demonstrated: JSON.stringify(['React Native', 'Offline Storage', 'Progressive Web Apps', 'Mobile Development']),
                evidence_url: 'https://play.google.com/store/apps/details?id=edu.rural.learning',
                is_verified: true
            }
        ];

        // 2. TEACHING & EDUCATIONAL ACHIEVEMENTS
        const teachingAchievements = [
            {
                title: 'Best Computer Science Teacher Award',
                category: 'teaching_excellence',
                description: 'Recognized as the Best Computer Science Teacher at district level for innovative teaching methods and outstanding student results',
                achievement_date: '2023-09-05',
                recognition_level: 'district',
                impact_description: '95% pass rate in Computer Science, 40% students scored above 90%, introduced coding clubs',
                skills_demonstrated: JSON.stringify(['Teaching', 'Curriculum Development', 'Student Mentoring', 'Innovation']),
                evidence_url: 'https://education.punjab.gov.in/awards/2023',
                is_verified: true
            },
            {
                title: 'Digital Literacy Program Implementation',
                category: 'educational_innovation',
                description: 'Successfully implemented a comprehensive digital literacy program for 500+ students and 50+ teachers',
                achievement_date: '2022-12-15',
                recognition_level: 'state',
                impact_description: 'Improved digital skills of entire school community, 100% teachers now use technology in classrooms',
                skills_demonstrated: JSON.stringify(['Training', 'Digital Literacy', 'Program Management', 'Technology Integration']),
                evidence_url: 'https://digitalindia.gov.in/success-stories',
                is_verified: true
            },
            {
                title: 'Coding Competition Mentor',
                category: 'student_mentoring',
                description: 'Mentored students who won 1st place in State-level Programming Competition and 3rd place in National Olympiad',
                achievement_date: '2023-02-20',
                recognition_level: 'national',
                impact_description: 'Students achieved top rankings in competitive programming, 5 students got admission in IITs',
                skills_demonstrated: JSON.stringify(['Mentoring', 'Competitive Programming', 'Algorithm Design', 'Problem Solving']),
                evidence_url: 'https://olympiad.org/results/2023',
                is_verified: true
            }
        ];

        // 3. RESEARCH & PUBLICATION ACHIEVEMENTS
        const researchAchievements = [
            {
                title: 'Research Paper on AI in Education',
                category: 'research_publication',
                description: 'Published research paper "Machine Learning Applications in Student Performance Prediction" in International Journal of Educational Technology',
                achievement_date: '2023-06-10',
                recognition_level: 'international',
                impact_description: 'Cited by 25+ researchers, contributed to AI in education field, presented at 3 international conferences',
                skills_demonstrated: JSON.stringify(['Research', 'Machine Learning', 'Academic Writing', 'Data Analysis']),
                evidence_url: 'https://doi.org/10.1234/ijet.2023.ml.education',
                is_verified: true
            },
            {
                title: 'Educational Technology Conference Speaker',
                category: 'professional_recognition',
                description: 'Invited speaker at National Conference on Educational Technology, presented on "Digital Transformation in Rural Schools"',
                achievement_date: '2022-10-25',
                recognition_level: 'national',
                impact_description: 'Influenced policy makers, shared best practices with 500+ educators nationwide',
                skills_demonstrated: JSON.stringify(['Public Speaking', 'Thought Leadership', 'Educational Technology', 'Policy Influence']),
                evidence_url: 'https://nced.gov.in/conference/2022/speakers',
                is_verified: true
            }
        ];

        // 4. COMMUNITY & SOCIAL ACHIEVEMENTS
        const communityAchievements = [
            {
                title: 'Free Coding Bootcamp for Underprivileged Students',
                category: 'community_service',
                description: 'Organized and conducted free weekend coding bootcamps for 200+ underprivileged students from nearby villages',
                achievement_date: '2023-07-30',
                recognition_level: 'district',
                impact_description: '80% participants learned basic programming, 20 students got internships, 5 started freelancing',
                skills_demonstrated: JSON.stringify(['Community Service', 'Teaching', 'Social Impact', 'Volunteer Management']),
                evidence_url: 'https://ngo.codingforall.org/bootcamp-2023',
                is_verified: true
            },
            {
                title: 'Women in Tech Initiative',
                category: 'diversity_inclusion',
                description: 'Founded "Girls Code Punjab" initiative to encourage female students to pursue computer science careers',
                achievement_date: '2022-03-08',
                recognition_level: 'state',
                impact_description: 'Increased female enrollment in CS by 60%, mentored 100+ girls, 15 girls got tech internships',
                skills_demonstrated: JSON.stringify(['Leadership', 'Diversity & Inclusion', 'Mentoring', 'Initiative Building']),
                evidence_url: 'https://girlscodepunjab.org',
                is_verified: true
            }
        ];

        // 5. PROFESSIONAL DEVELOPMENT ACHIEVEMENTS
        const professionalAchievements = [
            {
                title: 'Google Certified Professional Cloud Architect',
                category: 'professional_certification',
                description: 'Achieved Google Cloud Professional Cloud Architect certification, one of the most challenging cloud certifications',
                achievement_date: '2023-04-15',
                recognition_level: 'international',
                impact_description: 'Enhanced cloud computing curriculum, introduced cloud projects in classes',
                skills_demonstrated: JSON.stringify(['Cloud Computing', 'System Architecture', 'Google Cloud Platform', 'Technical Leadership']),
                evidence_url: 'https://www.credential.net/cloud-architect-cert',
                is_verified: true
            },
            {
                title: 'Microsoft Innovative Educator Expert',
                category: 'professional_recognition',
                description: 'Selected as Microsoft Innovative Educator Expert for innovative use of technology in education',
                achievement_date: '2022-09-12',
                recognition_level: 'international',
                impact_description: 'Became Microsoft education ambassador, trained 200+ teachers on Microsoft tools',
                skills_demonstrated: JSON.stringify(['Educational Technology', 'Microsoft Tools', 'Teacher Training', 'Innovation']),
                evidence_url: 'https://education.microsoft.com/experts/profile',
                is_verified: true
            }
        ];

        // 6. INNOVATION & ENTREPRENEURSHIP ACHIEVEMENTS
        const innovationAchievements = [
            {
                title: 'EdTech Startup Co-founder',
                category: 'entrepreneurship',
                description: 'Co-founded "LearnSmart Punjab" - an EdTech startup providing personalized learning solutions for government schools',
                achievement_date: '2023-01-20',
                recognition_level: 'state',
                impact_description: 'Serving 50+ schools, 5000+ students, generated ₹10 lakhs revenue, created 15 jobs',
                skills_demonstrated: JSON.stringify(['Entrepreneurship', 'Business Development', 'Product Management', 'Team Leadership']),
                evidence_url: 'https://learnsmartpunjab.com',
                is_verified: true
            },
            {
                title: 'Patent for Educational Algorithm',
                category: 'intellectual_property',
                description: 'Filed patent for "Adaptive Learning Algorithm for Personalized Education" - pending approval',
                achievement_date: '2023-05-10',
                recognition_level: 'national',
                impact_description: 'Potential to revolutionize personalized learning, already implemented in 10 schools',
                skills_demonstrated: JSON.stringify(['Innovation', 'Algorithm Design', 'Intellectual Property', 'Research & Development']),
                evidence_url: 'https://ipindia.gov.in/patent/application/2023',
                is_verified: false
            }
        ];

        // Combine all achievements
        const allAchievements = [
            ...softwareAchievements,
            ...teachingAchievements,
            ...researchAchievements,
            ...communityAchievements,
            ...professionalAchievements,
            ...innovationAchievements
        ];

        console.log(`📝 Adding ${allAchievements.length} achievements...`);

        // Insert achievements
        for (const achievement of allAchievements) {
            await connection.execute(`
                INSERT INTO staff_achievements (
                    staff_id, title, category, description, achievement_date,
                    recognition_level, impact_description, skills_demonstrated,
                    evidence_url, is_verified, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
                staffId,
                achievement.title,
                achievement.category,
                achievement.description,
                achievement.achievement_date,
                achievement.recognition_level,
                achievement.impact_description,
                achievement.skills_demonstrated,
                achievement.evidence_url,
                achievement.is_verified
            ]);
        }

        console.log('✅ All achievements added successfully!');

        // Display summary
        const [summary] = await connection.execute(`
            SELECT 
                category,
                COUNT(*) as count,
                recognition_level,
                COUNT(*) as level_count
            FROM staff_achievements 
            WHERE staff_id = ?
            GROUP BY category, recognition_level
            ORDER BY category, recognition_level
        `, [staffId]);

        console.log('\n📊 ACHIEVEMENTS SUMMARY:');
        console.log('========================');
        summary.forEach(row => {
            console.log(`${row.category}: ${row.count} (${row.recognition_level} level: ${row.level_count})`);
        });

        const [totalCount] = await connection.execute(`
            SELECT COUNT(*) as total FROM staff_achievements WHERE staff_id = ?
        `, [staffId]);

        console.log(`\n🎯 TOTAL ACHIEVEMENTS: ${totalCount[0].total}`);

    } catch (error) {
        console.error('❌ Error adding achievements:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔐 Database connection closed');
        }
    }
}

// Run the script
addCSTeacherAchievements();
