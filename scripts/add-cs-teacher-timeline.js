const mysql = require('mysql2/promise');

async function addCSTeacherTimeline() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Adding CS Teacher timeline data...');

        // Get the user ID for CS teacher
        const [userResult] = await connection.execute(
            'SELECT id FROM users WHERE email = ?',
            ['c<PERSON><PERSON>@example.com']
        );

        if (userResult.length === 0) {
            console.log('CS Teacher not found');
            return;
        }

        const userId = userResult[0].id;
        console.log('CS Teacher User ID:', userId);

        // Create teacher_education_timeline table if it doesn't exist
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS teacher_education_timeline (
                id INT AUTO_INCREMENT PRIMARY KEY,
                teacher_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                institution VARCHAR(255) NOT NULL,
                year YEAR NOT NULL,
                board VARCHAR(100),
                stream VARCHAR(100),
                specialization VARCHAR(255),
                percentage DECIMAL(5,2),
                thesis VARCHAR(500),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);

        // Create teacher_experience_timeline table if it doesn't exist
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS teacher_experience_timeline (
                id INT AUTO_INCREMENT PRIMARY KEY,
                teacher_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                institution VARCHAR(255) NOT NULL,
                duration VARCHAR(100) NOT NULL,
                description TEXT,
                is_current BOOLEAN DEFAULT FALSE,
                start_date DATE,
                end_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
            )
        `);

        // Clear existing timeline data for this teacher
        await connection.execute('DELETE FROM teacher_education_timeline WHERE teacher_id = ?', [userId]);
        await connection.execute('DELETE FROM teacher_experience_timeline WHERE teacher_id = ?', [userId]);

        // Insert education timeline data
        await connection.execute(`
            INSERT INTO teacher_education_timeline (teacher_id, title, institution, year, specialization)
            VALUES (?, ?, ?, ?, ?)
        `, [userId, 'B.Tech', 'Computer Science and Engineering', 2011, 'Computer Science and Engineering']);

        // Insert combined experience timeline data
        await connection.execute(`
            INSERT INTO teacher_experience_timeline (teacher_id, title, institution, duration, description, is_current, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            userId,
            'Software Engineer',
            'Software Company',
            'June 2011 - Oct 2013',
            'Developed applications and systems, gained expertise in programming and software development',
            false,
            '2011-06-01',
            '2013-10-31'
        ]);

        await connection.execute(`
            INSERT INTO teacher_experience_timeline (teacher_id, title, institution, duration, description, is_current, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            userId,
            'Physics Tutor',
            'Tutoring Center',
            'Nov 2013 - Nov 2015',
            'Taught physics to students, developed teaching skills and educational methodologies',
            false,
            '2013-11-01',
            '2015-11-30'
        ]);

        await connection.execute(`
            INSERT INTO teacher_experience_timeline (teacher_id, title, institution, duration, description, is_current, start_date, end_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            userId,
            'Computer Science Teacher',
            'Current School',
            'Nov 2015 - Present',
            'Teaching computer science and programming, mentoring students in competitive programming',
            true,
            '2015-11-20',
            null
        ]);

        console.log('CS Teacher timeline data added successfully!');

    } catch (error) {
        console.error('Error adding CS teacher timeline data:', error);
    } finally {
        await connection.end();
    }
}

addCSTeacherTimeline();
