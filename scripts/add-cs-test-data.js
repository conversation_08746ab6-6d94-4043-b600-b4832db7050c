/**
 * <PERSON><PERSON><PERSON> to add test data for Computer Science subjects, practicals, and assignments
 * This will help test the functionality of the student and teacher views
 */

const db = require('../config/database');

async function addTestData() {
  try {
    console.log('Starting to add Computer Science test data...');

    // Begin transaction
    await db.query('START TRANSACTION');

    // 1. Make sure Computer Science subject exists
    console.log('Checking if Computer Science subject exists...');
    const [existingSubjects] = await db.query(
      'SELECT id FROM subjects WHERE name = ?',
      ['Computer Science']
    );

    let csSubjectId;
    if (existingSubjects.length > 0) {
      csSubjectId = existingSubjects[0].id;
      console.log(`Computer Science subject already exists with ID: ${csSubjectId}`);
    } else {
      const [result] = await db.query(
        'INSERT INTO subjects (name, code, description) VALUES (?, ?, ?)',
        ['Computer Science', 'CS', 'Computer Science for classes 11-12']
      );
      csSubjectId = result.insertId;
      console.log(`Created Computer Science subject with ID: ${csSubjectId}`);
    }

    // 2. Make sure we have a CS teacher
    console.log('Checking if CS teacher exists...');
    const [existingTeachers] = await db.query(
      'SELECT id FROM users WHERE role = ? AND username = ?',
      ['teacher', 'csteacher']
    );

    let csTeacherId;
    if (existingTeachers.length > 0) {
      csTeacherId = existingTeachers[0].id;
      console.log(`CS teacher already exists with ID: ${csTeacherId}`);
    } else {
      // Create a CS teacher
      const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
      const [result] = await db.query(
        'INSERT INTO users (username, email, password, name, full_name, role, is_active, bio, last_login) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        ['csteacher', '<EMAIL>', '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQXwk/Yw1QDwHiGrQAZRlkFkAVEtfC', 'CS Teacher', 'CS Teacher', 'teacher', 1, 'Computer Science teacher with expertise in programming and data structures.', now]
      );
      csTeacherId = result.insertId;
      console.log(`Created CS teacher with ID: ${csTeacherId}`);
    }

    // 3. Make sure we have a CS student
    console.log('Checking if CS student exists...');
    const [existingStudents] = await db.query(
      'SELECT id FROM users WHERE role = ? AND username = ?',
      ['student', 'csstudent']
    );

    let csStudentId;
    if (existingStudents.length > 0) {
      csStudentId = existingStudents[0].id;
      console.log(`CS student already exists with ID: ${csStudentId}`);
    } else {
      // Create a CS student
      const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
      const [result] = await db.query(
        'INSERT INTO users (username, email, password, name, full_name, role, is_active, bio, last_login) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        ['csstudent', '<EMAIL>', '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQXwk/Yw1QDwHiGrQAZRlkFkAVEtfC', 'CS Student', 'CS Student', 'student', 1, 'Computer Science student interested in programming and web development.', now]
      );
      csStudentId = result.insertId;
      console.log(`Created CS student with ID: ${csStudentId}`);
    }

    // 4. Make sure we have a class for CS
    console.log('Checking if CS class exists...');
    const [existingClasses] = await db.query(
      'SELECT id FROM classes WHERE name = ?',
      ['12']
    );

    let csClassId;
    if (existingClasses.length > 0) {
      csClassId = existingClasses[0].id;
      console.log(`Class 12 already exists with ID: ${csClassId}`);
    } else {
      // Create a class
      const [result] = await db.query(
        'INSERT INTO classes (name, description, is_active) VALUES (?, ?, ?)',
        ['12', 'Class 12', 1]
      );
      csClassId = result.insertId;
      console.log(`Created Class 12 with ID: ${csClassId}`);
    }

    // 5. Make sure we have a class section
    console.log('Checking if class section exists...');
    const [existingClassSections] = await db.query(
      'SELECT id FROM class_sections WHERE class_id = ? AND section = ?',
      [csClassId, 'A']
    );

    let classSectionId;
    if (existingClassSections.length > 0) {
      classSectionId = existingClassSections[0].id;
      console.log(`Class section already exists with ID: ${classSectionId}`);
    } else {
      // Create a class section
      const [result] = await db.query(
        'INSERT INTO class_sections (class_id, section, is_active) VALUES (?, ?, ?)',
        [csClassId, 'A', 1]
      );
      classSectionId = result.insertId;
      console.log(`Created class section with ID: ${classSectionId}`);
    }

    // 6. Assign student to class
    console.log('Assigning student to class...');
    const [existingStudentClasses] = await db.query(
      'SELECT id FROM student_classes WHERE student_id = ? AND class_id = ?',
      [csStudentId, csClassId]
    );

    if (existingStudentClasses.length === 0) {
      await db.query(
        'INSERT INTO student_classes (student_id, class_id) VALUES (?, ?)',
        [csStudentId, csClassId]
      );
      console.log(`Assigned student ${csStudentId} to class ${csClassId}`);
    } else {
      console.log(`Student ${csStudentId} already assigned to class ${csClassId}`);
    }

    // 7. Assign student to subject
    console.log('Assigning student to subject...');
    const [existingStudentSubjects] = await db.query(
      'SELECT id FROM student_subjects WHERE student_id = ? AND subject_id = ?',
      [csStudentId, csSubjectId]
    );

    if (existingStudentSubjects.length === 0) {
      await db.query(
        'INSERT INTO student_subjects (student_id, subject_id) VALUES (?, ?)',
        [csStudentId, csSubjectId]
      );
      console.log(`Assigned student ${csStudentId} to subject ${csSubjectId}`);
    } else {
      console.log(`Student ${csStudentId} already assigned to subject ${csSubjectId}`);
    }

    // 8. Add CS lectures
    console.log('Adding CS lectures...');
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayStr = today.toISOString().split('T')[0];
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // Add a pending lecture for today
    await db.query(
      `INSERT INTO teacher_lectures
       (teacher_id, date, start_time, end_time, class_name, subject_name, topic, status, notes, slot_index)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [csTeacherId, todayStr, '10:00:00', '11:00:00', 'Class 12-A', 'Computer Science', 'Introduction to Data Structures', 'pending', 'Cover basic concepts of data structures', 1]
    );
    console.log('Added pending lecture for today');

    // Add a delivered lecture for yesterday
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    await db.query(
      `INSERT INTO teacher_lectures
       (teacher_id, date, start_time, end_time, class_name, subject_name, topic, status, notes, slot_index)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [csTeacherId, yesterdayStr, '10:00:00', '11:00:00', 'Class 12-A', 'Computer Science', 'Introduction to Algorithms', 'delivered', 'Covered basic algorithm concepts', 1]
    );
    console.log('Added delivered lecture for yesterday');

    // 9. Add CS practicals
    console.log('Adding CS practicals...');

    // Add a pending practical for tomorrow
    const [practicalResult] = await db.query(
      `INSERT INTO teacher_practicals
       (teacher_id, date, start_time, end_time, class_name, class_section_id, subject_name, practical_topic, venue, status, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [csTeacherId, tomorrowStr, '14:00:00', '16:00:00', 'Class 12-A', classSectionId, 'Computer Science', 'Implementing Linked Lists', 'Computer Lab 1', 'pending', 'Students should bring their laptops']
    );
    const practicalId = practicalResult.insertId;
    console.log(`Added pending practical with ID: ${practicalId}`);

    // 10. Add CS assignments
    console.log('Adding CS assignments...');

    // Add an assignment due next week
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);
    const nextWeekStr = nextWeek.toISOString().split('T')[0];

    const [assignmentResult] = await db.query(
      `INSERT INTO assignments
       (title, description, subject_id, class_id, teacher_id, due_date, total_marks)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['Implement a Stack and Queue', 'Create a program that implements both stack and queue data structures with proper documentation', csSubjectId, csClassId, csTeacherId, `${nextWeekStr} 23:59:59`, 20]
    );
    const assignmentId = assignmentResult.insertId;
    console.log(`Added assignment with ID: ${assignmentId}`);

    // 11. Add student practical record
    console.log('Adding student practical record...');

    // Add a practical record for the student
    await db.query(
      `INSERT INTO student_practical_records
       (student_id, practical_id, teacher_id, submission_date, class_name, subject_name, practical_topic, status, feedback)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [csStudentId, practicalId, csTeacherId, today.toISOString().split('T')[0], 'Class 12-A', 'Computer Science', 'Implementing Linked Lists', 'submitted', 'I have implemented the linked list as instructed. Please find my code attached.']
    );
    console.log('Added student practical record');

    // Commit transaction
    await db.query('COMMIT');
    console.log('Successfully added all Computer Science test data!');

  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');
    console.error('Error adding test data:', error);
    throw error;
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
addTestData();
