/**
 * <PERSON><PERSON><PERSON> to add a default admin user
 */

const db = require('../config/database');
const bcrypt = require('bcrypt');

async function addDefaultAdmin() {
  try {
    console.log('Adding default admin user...');

    // Check if admin_user already exists
    const [existingUsers] = await db.query(`
      SELECT * FROM users WHERE username = 'admin_user'
    `);

    if (existingUsers.length > 0) {
      console.log('Admin user already exists, updating password...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      // Update the password
      await db.query(`
        UPDATE users SET password = ? WHERE username = 'admin_user'
      `, [hashedPassword]);
      
      console.log('Admin password updated successfully');
    } else {
      console.log('Creating new admin user...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      // Insert the admin user
      await db.query(`
        INSERT INTO users (username, password, role)
        VALUES ('admin_user', ?, 'admin')
      `, [hashedPassword]);
      
      console.log('Admin user created successfully');
    }

    // Get the admin user
    const [adminUsers] = await db.query(`
      SELECT id, username, role FROM users WHERE username = 'admin_user'
    `);
    
    console.log('Admin user:', adminUsers[0]);
    
    process.exit(0);
  } catch (error) {
    console.error('Error adding default admin:', error);
    process.exit(1);
  }
}

// Run the function
addDefaultAdmin();
