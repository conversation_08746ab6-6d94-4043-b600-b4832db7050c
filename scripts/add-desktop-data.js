/**
 * <PERSON><PERSON><PERSON> to import desktop computer data into the inventory system
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Desktop data from the provided list
const desktopData = [
    { serial_no: 'UXVKSSI114F3541924', mac_address: 'C0-7C-D1-3E-10-53', ip_address: '*************', hostname: 'CL1-PC-001' },
    { serial_no: 'UXVKSSI114F3541902', mac_address: 'C0-7C-D1-3E-15-27', ip_address: '*************', hostname: 'CL1-PC-002' },
    { serial_no: 'UXVKSSI114F3542001', mac_address: 'C0-7C-D1-3E-14-89', ip_address: '*************', hostname: 'CL1-PC-003' },
    { serial_no: 'UXVKSSI114F3541829', mac_address: 'C0-7C-D1-3E-14-ED', ip_address: '*************', hostname: 'CL1-PC-004' },
    { serial_no: 'UXVKSSI114F3541806', mac_address: 'C0-7C-D1-3E-OE-5F', ip_address: '*************', hostname: 'CL1-PC-005' },
    { serial_no: 'UXVKSSI114F3541874', mac_address: 'C0-7C-D1-3E-15-0F', ip_address: '*************', hostname: 'CL1-PC-006' },
    { serial_no: 'UXVKSSI114F3541804', mac_address: 'C0-7C-D1-3E-0E-66', ip_address: '*************', hostname: 'CL1-PC-007' },
    { serial_no: 'UXVKSSI114F3541833', mac_address: 'C0-7C-D1-3E-16-88', ip_address: '*************', hostname: 'CL1-PC-008' },
    { serial_no: 'UXVKSSI114F3541906', mac_address: 'C0-7C-D1-3E-14-84', ip_address: '*************', hostname: 'CL1-PC-009' },
    { serial_no: 'UXVKSSI114F3541974', mac_address: 'C0-7C-D1-3E-15-7A', ip_address: '*************', hostname: 'CL1-PC-010' },
    { serial_no: 'UXVKSSI114F3541912', mac_address: 'C0-7C-D1-3E-15-35', ip_address: '*************', hostname: 'CL1-PC-011' },
    { serial_no: 'UXVKSSI114F3542032', mac_address: 'C0-7C-D1-3E-14-82', ip_address: '*************', hostname: 'CL1-PC-012' },
    { serial_no: 'UXVKSSI114F3541823', mac_address: 'C0-7C-D1-3E-16-53', ip_address: '*************', hostname: 'CL1-PC-013' },
    { serial_no: 'UXVKSSI114F3541967', mac_address: 'C0-7C-D1-3E-15-B7', ip_address: '*************', hostname: 'CL1-PC-014' },
    { serial_no: 'UXVKSSI114F3542008', mac_address: 'C0-7C-D1-3E-10-52', ip_address: '*************', hostname: 'CL1-PC-015' },
    { serial_no: 'UXVKSSI114F3541843', mac_address: 'C0-7C-D1-3E-18-72', ip_address: '*************', hostname: 'CL2-PC-001' },
    { serial_no: 'UXVKSSI114F3541817', mac_address: 'C0-7C-D1-3E-0E-84', ip_address: '*************', hostname: 'CL2-PC-002' },
    { serial_no: 'UXVKSSI114F3541821', mac_address: 'C0-7C-D1-3E-16-61', ip_address: '*************', hostname: 'CL2-PC-003' },
    { serial_no: 'UXVKSSI114F3541930', mac_address: 'C0-7C-D1-3E-18-77', ip_address: '*************', hostname: 'CL2-PC-004' },
    { serial_no: 'UXVKSSI114F3541929', mac_address: 'C0-7C-D1-3E-10-57', ip_address: '*************', hostname: 'CL2-PC-005' },
    { serial_no: 'UXVKSSI114F3542010', mac_address: 'C0-7C-D1-3E-0E-41', ip_address: '*************', hostname: 'CL2-PC-006' },
    { serial_no: 'UXVKSSI114F3541896', mac_address: 'C0-7C-D1-3E-17-01', ip_address: '*************', hostname: 'CL2-PC-007' },
    { serial_no: 'UXVKSSI114F3541899', mac_address: 'C0-7C-D1-3E-17-06', ip_address: '*************', hostname: 'CL2-PC-008' },
    { serial_no: 'UXVKSSI114F3541868', mac_address: 'C0-7C-D1-3E-4A-1A', ip_address: '*************', hostname: 'CL2-PC-009' },
    { serial_no: 'UXVKSSI114F3541858', mac_address: 'C0-7C-D1-3E-18-3C', ip_address: '*************', hostname: 'CL2-PC-010' },
    { serial_no: 'UXVKSSI114F3541877', mac_address: 'C0-7C-D1-3E-16-5B', ip_address: '*************', hostname: 'CL2-PC-011' },
    { serial_no: 'UXVKSSI114F3541867', mac_address: 'C0-7C-D1-3E-18-BF', ip_address: '*************', hostname: 'CL2-PC-012' },
    { serial_no: 'UXVKSSI114F3541809', mac_address: 'C0-7C-D1-3E-0E-72', ip_address: '*************', hostname: 'CL2-PC-013' },
    { serial_no: 'UXVKSSI114F3541951', mac_address: 'C0-7C-D1-3E-0E-3E', ip_address: '*************', hostname: 'CL2-PC-014' },
    { serial_no: 'UXVKSSI114F3541849', mac_address: 'C0-7C-D1-3E-17-57', ip_address: '*************', hostname: 'CL2-PC-015' },
    { serial_no: 'UXVKSSI114F3541858', mac_address: 'C0-7C-D1-3E-10-68', ip_address: '*************', hostname: 'CL2-PC-016' },
    { serial_no: 'UXVKSSI114F3541859', mac_address: 'C0-7C-D1-3E-10-69', ip_address: '*************', hostname: 'CL2-PC-017' },
    { serial_no: 'UXVKSSI114F3541860', mac_address: 'C0-7C-D1-3E-61-EA', ip_address: '*************', hostname: 'CL2-PC-018' },
    { serial_no: 'UXVKSSI114F3541861', mac_address: 'C0-7C-D1-3E-10-71', ip_address: '*************', hostname: 'CL2-PC-019' },
    { serial_no: 'UXVKSSI114F3541862', mac_address: 'C0-7C-D1-3E-10-72', ip_address: '*************', hostname: 'CL2-PC-020' },
    { serial_no: 'UXVKSSI114F3541987', mac_address: 'C0-7C-D1-3E-15-78', ip_address: '*************', hostname: 'CO-PC-001' },
    { serial_no: 'UXVKSSI114F3541909', mac_address: 'C0-7C-D1-3E-15-2B', ip_address: '*************', hostname: 'CO-PC-002' },
    { serial_no: 'UXVKSSI114F3541828', mac_address: 'C0-7C-D1-3E-16-58', ip_address: '*************', hostname: 'CO-PC-003' },
    { serial_no: 'UXVJSSIF65F1753100', mac_address: '00-71-C2-31-4A-3B', ip_address: '*************', hostname: 'COACH-PC-001' },
    { serial_no: 'UXVJSSIF65F1753047', mac_address: '00-71-C2-31-4C-61', ip_address: '*************', hostname: 'COACH-PC-002' },
    { serial_no: 'UXVJSSIF65F1753064', mac_address: '00-71-C2-31-4A-58', ip_address: '*************', hostname: 'COACH-PC-003' },
    { serial_no: 'UXVJSSIF65F1749849', mac_address: '00-71-C2-30-2B-33', ip_address: '*************', hostname: 'COACH-PC-004' },
    { serial_no: 'UXVJSSIF65F1749822', mac_address: '00-71-C2-30-2B-24', ip_address: '*************', hostname: 'COACH-PC-005' },
    { serial_no: 'UXVKSSI114F3541934', mac_address: 'C0-7C-D1-3E-18-54', ip_address: '*************', hostname: 'BI-PC-001' },
    { serial_no: 'UXVKSSI114F3541969', mac_address: 'C0-7C-D1-3E-15-B4', ip_address: '*************', hostname: 'CH-PC-001' },
    { serial_no: 'UXVKSSI114F3541972', mac_address: 'C0-7C-D1-3E-15-95', ip_address: '*************', hostname: 'PH-PC-001' },
    { serial_no: 'UXVKSSI114F3542036', mac_address: 'C0-7C-D1-3E-15-6C', ip_address: '*************', hostname: 'VP-PC-001' },
    { serial_no: 'UXVKSSI114F3541852', mac_address: 'C0-7C-D1-3E-18-B5', ip_address: '*************', hostname: 'PR-PC-001' },
    { serial_no: 'UXVKSSI114F3541953', mac_address: 'C0-7C-D1-3E-0E-4D', ip_address: '*************', hostname: 'LI-PC-001' },
    { serial_no: 'UXVKSSI114F3541945', mac_address: 'C0-7C-D1-3E-15-6C', ip_address: '*************', hostname: 'WA-PC-001' },
    { serial_no: 'UXVKSSI114F3541946', mac_address: 'C0-7C-D1-3E-15-6C', ip_address: '*************', hostname: 'WA-PC-002' }
];

// Function to add desktop data to the inventory
async function addDesktopData() {
    let connection;
    
    try {
        // Create database connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'exam_prep_platform'
        });
        
        console.log('Connected to database');
        
        // Check if desktop category exists, create if not
        const [categories] = await connection.query(
            'SELECT category_id FROM inventory_categories WHERE name LIKE ?',
            ['%Desktop%']
        );
        
        let categoryId;
        
        if (categories.length === 0) {
            // Create desktop category
            const [result] = await connection.query(
                'INSERT INTO inventory_categories (name, description) VALUES (?, ?)',
                ['Desktop Computer', 'Desktop computers and workstations']
            );
            
            categoryId = result.insertId;
            console.log('Created Desktop Computer category with ID:', categoryId);
        } else {
            categoryId = categories[0].category_id;
            console.log('Using existing Desktop Computer category with ID:', categoryId);
        }
        
        // Check if we need to add network_info column to inventory_items table
        try {
            const [columns] = await connection.query(
                'SHOW COLUMNS FROM inventory_items LIKE ?',
                ['network_info']
            );
            
            if (columns.length === 0) {
                // Add network_info column
                await connection.query(
                    'ALTER TABLE inventory_items ADD COLUMN network_info JSON NULL AFTER notes'
                );
                console.log('Added network_info column to inventory_items table');
            }
        } catch (error) {
            console.error('Error checking/adding network_info column:', error);
            // Continue with import even if column check fails
        }
        
        // Import desktop data
        let addedCount = 0;
        let skippedCount = 0;
        
        for (const desktop of desktopData) {
            // Check if desktop already exists
            const [existingItems] = await connection.query(
                'SELECT item_id FROM inventory_items WHERE serial_number = ?',
                [desktop.serial_no]
            );
            
            if (existingItems.length > 0) {
                console.log(`Desktop with serial ${desktop.serial_no} already exists, updating network info`);
                
                // Update network info for existing item
                await connection.query(
                    'UPDATE inventory_items SET network_info = ? WHERE item_id = ?',
                    [
                        JSON.stringify({
                            mac_address: desktop.mac_address,
                            ip_address: desktop.ip_address,
                            hostname: desktop.hostname
                        }),
                        existingItems[0].item_id
                    ]
                );
                
                skippedCount++;
                continue;
            }
            
            // Extract location from hostname (e.g., CL1, CL2, CO, etc.)
            const hostnamePrefix = desktop.hostname.split('-')[0];
            let location = 'IT Department';
            
            if (hostnamePrefix === 'CL1') {
                location = 'Computer Lab 1';
            } else if (hostnamePrefix === 'CL2') {
                location = 'Computer Lab 2';
            } else if (hostnamePrefix === 'CO') {
                location = 'Computer Office';
            } else if (hostnamePrefix === 'COACH') {
                location = 'Coaching Department';
            } else if (hostnamePrefix === 'BI') {
                location = 'Biology Lab';
            } else if (hostnamePrefix === 'CH') {
                location = 'Chemistry Lab';
            } else if (hostnamePrefix === 'PH') {
                location = 'Physics Lab';
            } else if (hostnamePrefix === 'VP') {
                location = 'Vice Principal Office';
            } else if (hostnamePrefix === 'PR') {
                location = 'Principal Office';
            } else if (hostnamePrefix === 'LI') {
                location = 'Library';
            } else if (hostnamePrefix === 'WA') {
                location = 'Warden Office';
            }
            
            // Insert new desktop
            const [result] = await connection.query(`
                INSERT INTO inventory_items (
                    name, description, category_id, serial_number, model,
                    manufacturer, status, location, notes, created_by, network_info
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                desktop.hostname,
                `Acer Veriton Desktop Computer - ${desktop.hostname}`,
                categoryId,
                desktop.serial_no,
                'Acer Veriton',
                'Acer',
                'available',
                location,
                `Serial Number: ${desktop.serial_no}\nHostname: ${desktop.hostname}`,
                1, // Admin user ID
                JSON.stringify({
                    mac_address: desktop.mac_address,
                    ip_address: desktop.ip_address,
                    hostname: desktop.hostname
                })
            ]);
            
            console.log(`Added desktop ${desktop.hostname} with ID:`, result.insertId);
            addedCount++;
        }
        
        console.log(`Import completed: ${addedCount} desktops added, ${skippedCount} skipped (already exist)`);
        
    } catch (error) {
        console.error('Error importing desktop data:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('Database connection closed');
        }
    }
}

// Run the script
addDesktopData();
