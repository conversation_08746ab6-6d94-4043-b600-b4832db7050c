const mysql = require('mysql2/promise');

async function addGenderColumn() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔧 ADDING GENDER COLUMN TO STAFF TABLE\n');

        // Check if gender column already exists
        const [columns] = await connection.execute('DESCRIBE staff');
        const hasGender = columns.some(col => col.Field === 'gender');

        if (hasGender) {
            console.log('✅ Gender column already exists in staff table');
        } else {
            console.log('📋 Adding gender column to staff table...');
            
            // Add gender column
            await connection.execute(`
                ALTER TABLE staff 
                ADD COLUMN gender ENUM('male', 'female', 'other', 'prefer_not_to_say') 
                AFTER pincode
            `);
            
            console.log('✅ Gender column added successfully');
        }

        // Update CS teacher with gender information
        console.log('\n👨‍🏫 Updating CS teacher gender information...');
        
        const [updateResult] = await connection.execute(`
            UPDATE staff 
            SET gender = 'male' 
            WHERE id = (
                SELECT s.id FROM staff s 
                JOIN users u ON s.user_id = u.id 
                WHERE u.email = '<EMAIL>'
            )
        `);

        if (updateResult.affectedRows > 0) {
            console.log('✅ CS teacher gender updated to male');
        } else {
            console.log('⚠️ No rows updated - CS teacher may not exist');
        }

        // Verify the update
        console.log('\n🔍 Verifying gender column and data:');
        const [staffData] = await connection.execute(`
            SELECT s.id, s.employee_id, s.designation, s.gender, u.name, u.email
            FROM staff s
            JOIN users u ON s.user_id = u.id
            WHERE u.role = 'teacher'
            ORDER BY s.id
        `);

        console.log('Staff records with gender:');
        staffData.forEach(staff => {
            console.log(`- ${staff.name} (${staff.designation}): ${staff.gender || 'Not set'}`);
        });

        console.log('\n✅ Gender column setup completed successfully!');

    } catch (error) {
        console.error('Error adding gender column:', error);
    } finally {
        await connection.end();
    }
}

addGenderColumn();
