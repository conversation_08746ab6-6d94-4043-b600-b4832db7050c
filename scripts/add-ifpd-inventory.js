/**
 * <PERSON><PERSON><PERSON> to add IFPD panels and accessories to the inventory
 */
const db = require('../config/database');

async function addCategories() {
    console.log('Adding new inventory categories...');
    
    const categories = [
        {
            name: 'Interactive Panels',
            description: 'Interactive Flat Panel Displays (IFPD) and touchscreens'
        },
        {
            name: 'Audio/Video Equipment',
            description: 'Cameras, microphones, speakers, and other A/V equipment'
        },
        {
            name: 'Power Equipment',
            description: 'UPS, power supplies, and other power-related equipment'
        }
    ];
    
    for (const category of categories) {
        try {
            // Check if category already exists
            const [existingCategories] = await db.query(
                'SELECT * FROM inventory_categories WHERE name = ?',
                [category.name]
            );
            
            if (existingCategories.length === 0) {
                // Add new category
                await db.query(
                    'INSERT INTO inventory_categories (name, description) VALUES (?, ?)',
                    [category.name, category.description]
                );
                console.log(`Added category: ${category.name}`);
            } else {
                console.log(`Category already exists: ${category.name}`);
            }
        } catch (error) {
            console.error(`Error adding category ${category.name}:`, error);
        }
    }
}

async function getCategoryId(categoryName) {
    const [categories] = await db.query(
        'SELECT category_id FROM inventory_categories WHERE name = ?',
        [categoryName]
    );
    
    if (categories.length > 0) {
        return categories[0].category_id;
    }
    
    return null;
}

async function addInventoryItems() {
    console.log('Adding IFPD panels and accessories to inventory...');
    
    // Get category IDs
    const interactivePanelsCategoryId = await getCategoryId('Interactive Panels');
    const avEquipmentCategoryId = await getCategoryId('Audio/Video Equipment');
    const powerEquipmentCategoryId = await getCategoryId('Power Equipment');
    const computersCategoryId = await getCategoryId('Computers');
    
    // Define inventory items
    const items = [
        {
            name: 'ACER PANEL 75"',
            description: 'Interactive Flat Panel Display 75 inches',
            category_id: interactivePanelsCategoryId,
            serial_number: 'ZLA01SC01850200449H4700',
            model: 'ACER 75"',
            manufacturer: 'ACER',
            status: 'available',
            location: 'CLASSROOM 3',
            notes: 'IFPD Panel'
        },
        {
            name: 'ACER PANEL 75"',
            description: 'Interactive Flat Panel Display 75 inches',
            category_id: interactivePanelsCategoryId,
            serial_number: 'ZLA01SC01850200448H4700',
            model: 'ACER 75"',
            manufacturer: 'ACER',
            status: 'available',
            location: 'CLASSROOM 5',
            notes: 'IFPD Panel'
        },
        {
            name: 'OPS i5 11th Generation 8GB/256GB',
            description: 'Open Pluggable Specification PC Module for IFPD',
            category_id: computersCategoryId,
            serial_number: '89991237485',
            model: 'OPS i5 11th Gen',
            manufacturer: 'Intel',
            status: 'available',
            location: 'CLASSROOM 3',
            notes: 'Installed in ACER Panel'
        },
        {
            name: 'OPS i5 11th Generation 8GB/256GB',
            description: 'Open Pluggable Specification PC Module for IFPD',
            category_id: computersCategoryId,
            serial_number: '89991237486',
            model: 'OPS i5 11th Gen',
            manufacturer: 'Intel',
            status: 'available',
            location: 'CLASSROOM 5',
            notes: 'Installed in ACER Panel'
        },
        {
            name: 'LOGITECH BRIO CAMERA 4K',
            description: '4K Ultra HD webcam',
            category_id: avEquipmentCategoryId,
            serial_number: '24442BL4YSU9',
            model: 'BRIO',
            manufacturer: 'LOGITECH',
            status: 'available',
            location: 'CLASSROOM 3',
            notes: 'Used with IFPD'
        },
        {
            name: 'LOGITECH BRIO CAMERA 4K',
            description: '4K Ultra HD webcam',
            category_id: avEquipmentCategoryId,
            serial_number: '2335LZ53QDE9',
            model: 'BRIO',
            manufacturer: 'LOGITECH',
            status: 'available',
            location: 'CLASSROOM 5',
            notes: 'Used with IFPD'
        },
        {
            name: 'DIGITEK DWM 101',
            description: 'Wireless microphone system',
            category_id: avEquipmentCategoryId,
            serial_number: '8906038531207',
            model: 'DWM 101',
            manufacturer: 'DIGITEK',
            status: 'available',
            location: 'CLASSROOM 3',
            notes: 'Used with IFPD'
        },
        {
            name: 'DIGITEK DWM 101',
            description: 'Wireless microphone system',
            category_id: avEquipmentCategoryId,
            serial_number: '8906038531207',
            model: 'DWM 101',
            manufacturer: 'DIGITEK',
            status: 'available',
            location: 'CLASSROOM 5',
            notes: 'Used with IFPD'
        },
        {
            name: 'MICROTEK LEGEND UPS',
            description: 'Uninterruptible Power Supply 1600 VA',
            category_id: powerEquipmentCategoryId,
            serial_number: '24I0U1704Q05020174',
            model: 'LEGEND 1600 VA',
            manufacturer: 'MICROTEK',
            status: 'available',
            location: 'CLASSROOM 3',
            notes: 'Used with IFPD'
        },
        {
            name: 'MICROTEK LEGEND UPS',
            description: 'Uninterruptible Power Supply 1600 VA',
            category_id: powerEquipmentCategoryId,
            serial_number: '24I0U1704Q05020180',
            model: 'LEGEND 1600 VA',
            manufacturer: 'MICROTEK',
            status: 'available',
            location: 'CLASSROOM 5',
            notes: 'Used with IFPD'
        }
    ];
    
    // Insert items
    for (const item of items) {
        try {
            // Check if item with this serial number already exists
            const [existingItems] = await db.query(
                'SELECT * FROM inventory_items WHERE serial_number = ?',
                [item.serial_number]
            );
            
            if (existingItems.length === 0) {
                // Add new item
                await db.query(`
                    INSERT INTO inventory_items (
                        name, description, category_id, serial_number, model,
                        manufacturer, status, location, notes, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                `, [
                    item.name, item.description, item.category_id, item.serial_number, item.model,
                    item.manufacturer, item.status, item.location, item.notes
                ]);
                console.log(`Added item: ${item.name} (${item.serial_number})`);
            } else {
                console.log(`Item already exists: ${item.name} (${item.serial_number})`);
            }
        } catch (error) {
            console.error(`Error adding item ${item.name}:`, error);
        }
    }
}

async function main() {
    try {
        await addCategories();
        await addInventoryItems();
        console.log('IFPD inventory import completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error importing IFPD inventory:', error);
        process.exit(1);
    }
}

main();
