/**
 * <PERSON><PERSON>t to add is_approved column to users table
 */

const db = require('../config/database');
const fs = require('fs').promises;
const path = require('path');

async function runMigration() {
    try {
        console.log('Adding is_approved column to users table...');
        
        // Read the migration file
        const migrationFile = path.join(__dirname, '../database/migrations/add_is_approved_column.sql');
        const migrationSql = await fs.readFile(migrationFile, 'utf8');
        
        // Split the SQL statements
        const statements = migrationSql.split(';').filter(stmt => stmt.trim() !== '');
        
        // Execute each statement
        for (const statement of statements) {
            await db.query(statement);
            console.log('Executed SQL:', statement.trim());
        }
        
        console.log('Migration completed successfully');
        
    } catch (error) {
        console.error('Error running migration:', error);
    }
}

// Run the migration
runMigration()
    .then(() => {
        console.log('Script completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('<PERSON><PERSON><PERSON> failed:', err);
        process.exit(1);
    });
