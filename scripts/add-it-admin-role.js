/**
 * <PERSON><PERSON><PERSON> to add the IT admin role to the database
 * This script:
 * 1. Alters the users table to add 'it_admin' to the role enum
 * 2. Adds the 'it_admin' role to the roles table if it doesn't exist
 * 3. Assigns IT-related permissions to the IT admin role
 */

const db = require('../config/database');

async function addITAdminRole() {
  try {
    console.log('\n=== Adding IT Admin Role ===');
    
    // Start a transaction
    await db.query('START TRANSACTION');
    
    // 1. Check if the users table has the it_admin role in the enum
    const [columns] = await db.query(`
      SHOW COLUMNS FROM users WHERE Field = 'role'
    `);
    
    if (columns.length > 0) {
      const typeInfo = columns[0].Type;
      console.log(`Current role enum: ${typeInfo}`);
      
      // Check if it_admin is already in the enum
      if (!typeInfo.includes('it_admin')) {
        console.log('Adding it_admin to role enum...');
        
        // Extract the enum values
        const enumValues = typeInfo.match(/enum\('([^']*)'(?:,'([^']*)')*\)/);
        if (enumValues) {
          // Add it_admin to the enum values
          const newEnumValues = typeInfo.replace(')', ",'it_admin')");
          
          // Alter the table
          await db.query(`
            ALTER TABLE users MODIFY COLUMN role ${newEnumValues} DEFAULT 'student'
          `);
          
          console.log('✅ Added it_admin to role enum in users table');
        } else {
          console.error('❌ Could not parse enum values from column type');
          await db.query('ROLLBACK');
          return;
        }
      } else {
        console.log('✅ it_admin already exists in role enum');
      }
    } else {
      console.error('❌ Could not find role column in users table');
      await db.query('ROLLBACK');
      return;
    }
    
    // 2. Check if the roles table exists
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'roles'
    `);
    
    if (tables.length > 0) {
      // Check if it_admin role already exists in roles table
      const [existingRole] = await db.query(`
        SELECT role_id FROM roles WHERE role_name = 'it_admin'
      `);
      
      let itAdminRoleId;
      
      if (existingRole.length === 0) {
        console.log('Adding it_admin role to roles table...');
        
        // Insert the it_admin role
        const [result] = await db.query(`
          INSERT INTO roles (role_name, description, is_system)
          VALUES ('it_admin', 'IT Administrator with access to IT inventory and system monitoring', TRUE)
        `);
        
        itAdminRoleId = result.insertId;
        console.log(`✅ Added it_admin role to roles table with ID ${itAdminRoleId}`);
      } else {
        itAdminRoleId = existingRole[0].role_id;
        console.log(`✅ it_admin role already exists with ID ${itAdminRoleId}`);
      }
      
      // 3. Assign IT-related permissions to the IT admin role
      if (itAdminRoleId) {
        // Check if permissions table exists
        const [permissionTables] = await db.query(`
          SHOW TABLES LIKE 'permissions'
        `);
        
        if (permissionTables.length > 0) {
          // Get all IT-related permissions
          const [itPermissions] = await db.query(`
            SELECT permission_id FROM permissions
            WHERE category IN ('inventory', 'system', 'hardware', 'it')
            OR permission_name LIKE '%inventory%'
            OR permission_name LIKE '%hardware%'
            OR permission_name LIKE '%system%'
            OR permission_name LIKE '%monitor%'
          `);
          
          if (itPermissions.length > 0) {
            console.log(`Found ${itPermissions.length} IT-related permissions`);
            
            // Check existing permissions for this role
            const [existingPermissions] = await db.query(`
              SELECT permission_id FROM role_permissions
              WHERE role_id = ?
            `, [itAdminRoleId]);
            
            const existingPermissionIds = existingPermissions.map(p => p.permission_id);
            
            // Filter out permissions that are already assigned
            const newPermissions = itPermissions.filter(p => !existingPermissionIds.includes(p.permission_id));
            
            if (newPermissions.length > 0) {
              console.log(`Assigning ${newPermissions.length} new permissions to it_admin role...`);
              
              // Create values for bulk insert
              const values = newPermissions.map(p => [itAdminRoleId, p.permission_id]);
              
              // Insert the permissions
              await db.query(`
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ?
              `, [values]);
              
              console.log('✅ Assigned IT-related permissions to it_admin role');
            } else {
              console.log('✅ All IT-related permissions already assigned to it_admin role');
            }
          } else {
            console.log('No IT-related permissions found, assigning all permissions...');
            
            // Get all permissions
            const [allPermissions] = await db.query(`
              SELECT permission_id FROM permissions
            `);
            
            // Check existing permissions for this role
            const [existingPermissions] = await db.query(`
              SELECT permission_id FROM role_permissions
              WHERE role_id = ?
            `, [itAdminRoleId]);
            
            const existingPermissionIds = existingPermissions.map(p => p.permission_id);
            
            // Filter out permissions that are already assigned
            const newPermissions = allPermissions.filter(p => !existingPermissionIds.includes(p.permission_id));
            
            if (newPermissions.length > 0) {
              console.log(`Assigning ${newPermissions.length} new permissions to it_admin role...`);
              
              // Create values for bulk insert
              const values = newPermissions.map(p => [itAdminRoleId, p.permission_id]);
              
              // Insert the permissions
              await db.query(`
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ?
              `, [values]);
              
              console.log('✅ Assigned all permissions to it_admin role');
            } else {
              console.log('✅ All permissions already assigned to it_admin role');
            }
          }
        } else {
          console.log('Permissions table does not exist, skipping permission assignment');
        }
      }
    } else {
      console.log('Roles table does not exist, skipping role creation');
    }
    
    // 4. Create a default IT admin user if none exists
    const [itAdmins] = await db.query(`
      SELECT id FROM users WHERE role = 'it_admin'
    `);
    
    if (itAdmins.length === 0) {
      console.log('Creating default IT admin user...');
      
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('itadmin123', 10);
      
      await db.query(`
        INSERT INTO users (
          username, name, email, password, role, 
          date_of_birth, bio, last_login, created_at
        ) VALUES (
          'it_admin', 'IT Administrator', '<EMAIL>', 
          ?, 'it_admin', '1990-01-01', 'IT Administrator account', 
          NOW(), NOW()
        )
      `, [hashedPassword]);
      
      console.log('✅ Created default IT admin user (username: it_admin, password: itadmin123)');
    } else {
      console.log(`✅ ${itAdmins.length} IT admin users already exist`);
    }
    
    // Commit the transaction
    await db.query('COMMIT');
    
    console.log('\n✅ IT Admin role setup completed successfully');
    console.log('=================================');
    
  } catch (error) {
    console.error('❌ Error adding IT admin role:', error);
    
    // Rollback the transaction
    try {
      await db.query('ROLLBACK');
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }
  } finally {
    // Close the database connection
    try {
      await db.end();
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
    
    process.exit(0);
  }
}

// Run the function
addITAdminRole();
