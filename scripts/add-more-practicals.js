/**
 * <PERSON><PERSON><PERSON> to add more practicals
 */

const db = require('../config/database');

async function addMorePracticals() {
  try {
    console.log('Adding more practicals...');

    // Get admin and teacher IDs
    const [users] = await db.query(`
      SELECT id, username, role FROM users WHERE role IN ('admin', 'teacher')
    `);

    const adminId = users.find(user => user.role === 'admin')?.id;
    const teacherId = users.find(user => user.role === 'teacher')?.id;

    if (!adminId) {
      throw new Error('No admin user found in the database');
    }

    console.log(`Using admin ID: ${adminId}, teacher ID: ${teacherId || 'none'}`);

    // Define subjects and venues
    const subjects = ['Physics', 'Chemistry', 'Biology', 'Computer Science'];
    const venues = ['Physics Lab', 'Chemistry Lab', 'Biology Lab', 'Computer Lab'];

    // Get class sections if they exist
    let classSectionId = 1;
    try {
      const [sections] = await db.query(`
        SELECT id FROM class_sections LIMIT 1
      `);

      if (sections.length > 0) {
        classSectionId = sections[0].id;
      }
    } catch (error) {
      console.log('Error fetching class sections:', error.message);
    }

    // Generate practicals
    const practicalCount = 20;

    for (let i = 0; i < practicalCount; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const venue = venues[subjects.indexOf(subject)];
      const title = `${subject} Practical ${i + 21}`; // Start from 21 to avoid duplicates
      const description = `Description for ${title}`;

      // Generate date and time
      const date = new Date();
      date.setDate(date.getDate() + Math.floor(Math.random() * 30) - 15); // Random date between -15 and +15 days
      const dateStr = date.toISOString().split('T')[0];

      const startHour = Math.floor(Math.random() * 6) + 9; // 9 AM to 2 PM
      const startTime = `${startHour.toString().padStart(2, '0')}:00:00`;
      const endTime = `${(startHour + 2).toString().padStart(2, '0')}:00:00`;

      // Determine status based on date
      let status;
      if (date < new Date()) {
        status = Math.random() < 0.8 ? 'completed' : 'cancelled';
      } else {
        status = 'pending';
      }

      // Insert practical for admin
      await db.query(`
        INSERT INTO teacher_practicals
        (teacher_id, class_section_id, class_name, subject_name, practical_topic, date, start_time, end_time, venue, status, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        adminId,
        classSectionId,
        'Class 11-A', // class_name
        subject,      // subject_name
        title,        // practical_topic
        dateStr,      // date
        startTime,    // start_time
        endTime,      // end_time
        venue,        // venue
        status,       // status
        description   // notes
      ]);

      console.log(`Added practical for admin: ${title} on ${dateStr} (${status})`);

      // Insert practical for teacher if exists
      if (teacherId) {
        await db.query(`
          INSERT INTO teacher_practicals
          (teacher_id, class_section_id, class_name, subject_name, practical_topic, date, start_time, end_time, venue, status, notes)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          teacherId,
          classSectionId,
          'Class 11-B', // class_name
          subject,      // subject_name
          title,        // practical_topic
          dateStr,      // date
          startTime,    // start_time
          endTime,      // end_time
          venue,        // venue
          status,       // status
          description   // notes
        ]);

        console.log(`Added practical for teacher: ${title} on ${dateStr} (${status})`);
      }
    }

    console.log('Added more practicals successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error adding more practicals:', error);
    process.exit(1);
  }
}

// Run the function
addMorePracticals();
