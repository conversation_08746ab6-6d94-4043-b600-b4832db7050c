const mysql = require('mysql2/promise');

async function addProfileImageColumn() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking if profile_image column exists in users table...');

        // Check if column exists
        const [columns] = await connection.query('SHOW COLUMNS FROM users LIKE ?', ['profile_image']);
        
        if (columns.length === 0) {
            console.log('profile_image column does not exist. Adding it now...');
            
            // Add the column
            await connection.query(`
                ALTER TABLE users 
                ADD COLUMN profile_image VARCHAR(255) NULL AFTER email
            `);
            
            console.log('profile_image column added successfully!');
        } else {
            console.log('profile_image column already exists.');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

addProfileImageColumn(); 