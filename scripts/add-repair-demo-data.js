/**
 * <PERSON><PERSON><PERSON> to add demo data for repair vendors and repair history
 */
const db = require('../config/database');

async function addRepairDemoData() {
    try {
        console.log('Adding repair vendor demo data...');
        
        // Start transaction
        const connection = await db.getConnection();
        await connection.beginTransaction();
        
        try {
            // Check if vendors already exist
            const [vendorCount] = await connection.query('SELECT COUNT(*) as count FROM repair_vendors');
            if (vendorCount[0].count > 0) {
                console.log('Repair vendors already exist. Skipping vendor creation.');
            } else {
                // Add repair vendors
                const vendors = [
                    {
                        name: 'TechFix Solutions',
                        contact_person: '<PERSON><PERSON>',
                        phone: '98765-43210',
                        email: '<EMAIL>',
                        address: '123 IT Park, Sector 17, Chandigarh',
                        specialization: 'Laptops, Desktops, Servers',
                        notes: 'Preferred vendor for computer repairs. Offers 3 months warranty on repairs.'
                    },
                    {
                        name: '<PERSON><PERSON><PERSON> <PERSON>',
                        contact_person: '<PERSON><PERSON>',
                        phone: '87654-32109',
                        email: '<EMAIL>',
                        address: '456 Electronics Market, Ludhiana',
                        specialization: 'Monitors, Projectors, Interactive Panels',
                        notes: 'Specializes in display technology repairs. Fast turnaround time.'
                    },
                    {
                        name: 'Network Solutions',
                        contact_person: 'Amit Sharma',
                        phone: '76543-21098',
                        email: '<EMAIL>',
                        address: '789 Tech Hub, Mohali',
                        specialization: 'Networking Equipment, Routers, Switches',
                        notes: 'Expert in network equipment repairs and configuration.'
                    },
                    {
                        name: 'AudioVision Repairs',
                        contact_person: 'Neha Verma',
                        phone: '65432-10987',
                        email: '<EMAIL>',
                        address: '101 Sound Street, Jalandhar',
                        specialization: 'Speakers, Microphones, Audio Equipment',
                        notes: 'Specialized in audio equipment repairs.'
                    }
                ];
                
                for (const vendor of vendors) {
                    await connection.query(`
                        INSERT INTO repair_vendors (
                            name, contact_person, phone, email, address, specialization, notes, is_active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                    `, [
                        vendor.name,
                        vendor.contact_person,
                        vendor.phone,
                        vendor.email,
                        vendor.address,
                        vendor.specialization,
                        vendor.notes
                    ]);
                }
                
                console.log('Added repair vendors successfully');
            }
            
            // Get available inventory items
            const [items] = await connection.query(`
                SELECT i.item_id, i.name, i.serial_number, i.status, c.name as category_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.category_id
                WHERE i.status = 'available'
                LIMIT 10
            `);
            
            if (items.length === 0) {
                console.log('No available inventory items found. Skipping repair history creation.');
            } else {
                // Get vendor IDs
                const [vendors] = await connection.query('SELECT vendor_id, name, specialization FROM repair_vendors');
                
                if (vendors.length === 0) {
                    console.log('No repair vendors found. Skipping repair history creation.');
                } else {
                    // Get admin user ID
                    const [admins] = await connection.query("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
                    
                    if (admins.length === 0) {
                        console.log('No admin user found. Skipping repair history creation.');
                    } else {
                        const adminId = admins[0].id;
                        
                        // Check if repair history already exists
                        const [repairCount] = await connection.query('SELECT COUNT(*) as count FROM repair_history');
                        
                        if (repairCount[0].count > 0) {
                            console.log('Repair history already exists. Skipping repair history creation.');
                        } else {
                            // Create repair history records
                            const repairIssues = [
                                'Display not working properly. Screen shows flickering images.',
                                'Keyboard has multiple keys not responding. Need replacement.',
                                'Battery not charging. Device only works when plugged in.',
                                'System overheating and shutting down randomly.',
                                'Touchpad not responding to gestures.',
                                'USB ports not recognizing devices.',
                                'Audio not working. Internal speakers produce no sound.',
                                'Wi-Fi connectivity issues. Frequently disconnects from network.',
                                'Power button stuck. Difficult to turn on/off.',
                                'HDMI port damaged. Unable to connect to external displays.'
                            ];
                            
                            // Create repair records with different statuses
                            const statuses = ['sent', 'in_progress', 'completed', 'cancelled'];
                            const today = new Date();
                            
                            // Create 5 repair records
                            for (let i = 0; i < Math.min(5, items.length); i++) {
                                const item = items[i];
                                const vendorIndex = i % vendors.length;
                                const vendor = vendors[vendorIndex];
                                const issueIndex = i % repairIssues.length;
                                const statusIndex = i % statuses.length;
                                const status = statuses[statusIndex];
                                
                                // Calculate dates
                                const sentDate = new Date(today);
                                sentDate.setDate(today.getDate() - (30 - i * 5)); // Spread over the last 30 days
                                
                                const expectedReturnDate = new Date(sentDate);
                                expectedReturnDate.setDate(sentDate.getDate() + 14); // Expected return in 14 days
                                
                                let returnedDate = null;
                                if (status === 'completed') {
                                    returnedDate = new Date(sentDate);
                                    returnedDate.setDate(sentDate.getDate() + 10); // Returned in 10 days
                                }
                                
                                // Format dates for MySQL
                                const formattedSentDate = sentDate.toISOString().split('T')[0];
                                const formattedExpectedDate = expectedReturnDate.toISOString().split('T')[0];
                                const formattedReturnedDate = returnedDate ? returnedDate.toISOString().split('T')[0] : null;
                                
                                // Insert repair history record
                                const [result] = await connection.query(`
                                    INSERT INTO repair_history (
                                        item_id, vendor_id, sent_date, expected_return_date, returned_date,
                                        issue_description, sent_by, received_by, repair_cost, repair_details,
                                        status, notes
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                `, [
                                    item.item_id,
                                    vendor.vendor_id,
                                    formattedSentDate,
                                    formattedExpectedDate,
                                    formattedReturnedDate,
                                    repairIssues[issueIndex],
                                    adminId,
                                    status === 'completed' ? adminId : null,
                                    status === 'completed' ? Math.floor(Math.random() * 5000) + 500 : null, // Random cost between 500-5500
                                    status === 'completed' ? `Repaired and tested. ${repairIssues[issueIndex].replace('not', 'now')}` : null,
                                    status,
                                    `Demo repair record for ${item.name} (${item.serial_number || 'No S/N'})`
                                ]);
                                
                                // Update item status based on repair status
                                let newStatus = 'available';
                                if (status === 'sent' || status === 'in_progress') {
                                    newStatus = 'maintenance';
                                }
                                
                                await connection.query(`
                                    UPDATE inventory_items SET status = ? WHERE item_id = ?
                                `, [newStatus, item.item_id]);
                                
                                // If completed, create a transaction record
                                if (status === 'completed') {
                                    await connection.query(`
                                        INSERT INTO inventory_transactions (
                                            item_id, transaction_type, issued_to, issued_by,
                                            issued_date, received_date, received_by,
                                            condition_on_return, notes, condition_check_required,
                                            repair_vendor_id, from_repair
                                        ) VALUES (?, 'receive', NULL, ?,
                                            ?, ?, ?,
                                            'Repaired', ?, ?,
                                            ?, 1)
                                    `, [
                                        item.item_id,
                                        adminId,
                                        formattedReturnedDate,
                                        formattedReturnedDate,
                                        adminId,
                                        `Received from repair vendor: ${vendor.name}. ${repairIssues[issueIndex].replace('not', 'now')}`,
                                        item.category_name && (item.category_name.toLowerCase().includes('laptop') || 
                                                              item.category_name.toLowerCase().includes('desktop')) ? 1 : 0,
                                        vendor.vendor_id
                                    ]);
                                }
                                
                                console.log(`Created repair record for ${item.name} with status ${status}`);
                            }
                            
                            console.log('Added repair history records successfully');
                        }
                    }
                }
            }
            
            await connection.commit();
            console.log('Repair demo data added successfully');
            process.exit(0);
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    } catch (error) {
        console.error('Error adding repair demo data:', error);
        process.exit(1);
    }
}

addRepairDemoData();
