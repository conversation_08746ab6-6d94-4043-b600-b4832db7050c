/**
 * <PERSON><PERSON><PERSON> to add student practical records for existing practicals
 */

const db = require('../config/database');

async function addStudentPracticalRecords() {
  try {
    console.log('Adding student practical records for existing practicals...');

    // Get admin and student IDs
    const [users] = await db.query(`
      SELECT id, username, role FROM users
    `);

    const adminId = users.find(user => user.role === 'admin')?.id;
    const studentIds = users.filter(user => user.role === 'student').map(user => user.id);

    if (!adminId) {
      throw new Error('No admin user found in the database');
    }

    if (studentIds.length === 0) {
      // Create some student users if none exist
      console.log('No student users found, creating some...');

      for (let i = 1; i <= 5; i++) {
        const username = `student${i}`;
        const password = '$2b$10$3euPcmQFCiblsZeEu5s7p.9wVsw1S0RRQkZbg4KRBUud5pa1AiXOK'; // 'password' hashed

        await db.query(`
          INSERT INTO users (username, password, role)
          VALUES (?, ?, 'student')
        `, [username, password]);

        console.log(`Created student user: ${username}`);
      }

      // Get the newly created student IDs
      const [newStudents] = await db.query(`
        SELECT id FROM users WHERE role = 'student'
      `);

      if (newStudents.length === 0) {
        throw new Error('Failed to create student users');
      }

      studentIds.push(...newStudents.map(student => student.id));
    }

    console.log(`Using admin ID: ${adminId}, student IDs: ${studentIds.join(', ')}`);

    // Get all practicals
    const [practicals] = await db.query(`
      SELECT * FROM teacher_practicals
    `);

    if (practicals.length === 0) {
      throw new Error('No practicals found in the database');
    }

    console.log(`Found ${practicals.length} practicals`);

    // Add records for each student and practical
    let recordsAdded = 0;

    for (const studentId of studentIds) {
      // Each student submits 5-10 practicals
      const recordCount = Math.floor(Math.random() * 6) + 5;
      const practicalIndices = [];

      // Generate unique random indices
      while (practicalIndices.length < recordCount && practicalIndices.length < practicals.length) {
        const index = Math.floor(Math.random() * practicals.length);
        if (!practicalIndices.includes(index)) {
          practicalIndices.push(index);
        }
      }

      for (const index of practicalIndices) {
        const practical = practicals[index];
        const practical_topic = practical.practical_topic || practical.title || 'Practical';
        const subject_name = practical.subject_name || 'General';
        const class_name = practical.class_name || 'Class 11-A';

        // Generate submission date
        const submissionDate = new Date(practical.date);
        submissionDate.setDate(submissionDate.getDate() + Math.floor(Math.random() * 5)); // 0-5 days after practical
        const submissionDateStr = submissionDate.toISOString().split('T')[0];

        // Determine status and grade
        let status, grade, feedback;
        if (submissionDate < new Date()) {
          if (Math.random() < 0.7) {
            status = 'graded';
            grade = ['A', 'B', 'C', 'D', 'F'][Math.floor(Math.random() * 5)];
            feedback = `Feedback for ${practical_topic}: ${grade === 'F' ? 'Needs improvement' : 'Good work'}`;
          } else {
            status = 'submitted';
            grade = null;
            feedback = null;
          }
        } else {
          status = 'pending';
          grade = null;
          feedback = null;
        }

        // Insert record
        await db.query(`
          INSERT INTO student_practical_records
          (student_id, teacher_id, practical_id, class_name, subject_name, practical_topic, submission_date, status, grade, feedback)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          studentId,
          practical.teacher_id,
          practical.id,
          class_name,
          subject_name,
          practical_topic,
          submissionDateStr,
          status,
          grade,
          feedback
        ]);

        recordsAdded++;
        console.log(`Added practical record for student ${studentId}: ${practical_topic} (${status})`);
      }
    }

    console.log(`Added ${recordsAdded} student practical records successfully`);
    process.exit(0);
  } catch (error) {
    console.error('Error adding student practical records:', error);
    process.exit(1);
  }
}

// Run the function
addStudentPracticalRecords();
