/**
 * <PERSON><PERSON><PERSON> to add test performance data
 * Run with: node scripts/add-test-performance-data.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function addTestPerformanceData() {
  let connection;
  
  try {
    console.log('Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'meritorious'
    });
    
    // First check if tables exist
    console.log('Checking if performance tables exist...');
    try {
      await connection.execute(`SELECT 1 FROM user_performance LIMIT 1`);
      await connection.execute(`SELECT 1 FROM attempt_statistics LIMIT 1`);
    } catch (error) {
      console.log('Performance tables don\'t exist. Please run create-performance-tables.js first.');
      return;
    }
    
    // Get the user ID for which to add data (we'll use user_id 3 as an example)
    const userId = 3;
    
    // Check if user exists
    const [users] = await connection.execute(`SELECT * FROM users WHERE id = ?`, [userId]);
    if (!users.length) {
      console.log(`User with ID ${userId} not found.`);
      return;
    }
    
    // Get available exams
    const [exams] = await connection.execute(`SELECT * FROM exams LIMIT 5`);
    if (!exams.length) {
      console.log('No exams found. Please create some exams first.');
      return;
    }
    
    // Get available attempts for the user
    const [attempts] = await connection.execute(`
      SELECT * FROM exam_attempts 
      WHERE user_id = ? 
      ORDER BY attempt_id
      LIMIT 10
    `, [userId]);
    
    // If no attempts, we'll create fake attempt IDs for testing
    let fakeAttemptId = 1000;
    
    // Clear existing data for the user (optional)
    console.log(`Clearing existing performance data for user ${userId}...`);
    await connection.execute(`DELETE FROM user_performance WHERE user_id = ?`, [userId]);
    await connection.execute(`DELETE FROM attempt_statistics WHERE user_id = ?`, [userId]);
    
    // Get sections for each exam
    for (const exam of exams) {
      const [sections] = await connection.execute(
        `SELECT * FROM sections WHERE exam_id = ?`, 
        [exam.exam_id]
      );
      
      if (sections.length === 0) {
        console.log(`No sections found for exam ${exam.exam_name}, skipping...`);
        continue;
      }
      
      // Find an attempt for this exam or create a fake one
      let attemptId;
      const matchingAttempt = attempts.find(a => a.exam_id === exam.exam_id);
      if (matchingAttempt) {
        attemptId = matchingAttempt.attempt_id;
      } else {
        attemptId = fakeAttemptId++;
      }
      
      // Instead of null for section_id, use the first section
      const firstSectionId = sections[0].section_id;
      
      // Insert overall performance for this exam
      console.log(`Adding overall performance data for exam ${exam.exam_name}...`);
      await connection.execute(`
        INSERT INTO user_performance 
        (user_id, exam_id, attempt_id, section_id, score_percentage, performance_category)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        userId, 
        exam.exam_id,
        attemptId,
        firstSectionId, // Use first section instead of NULL
        Math.floor(Math.random() * 100),  // Random score
        ['weak', 'medium', 'strong'][Math.floor(Math.random() * 3)] // Random category
      ]);
      
      // For each section, add section-level performance
      for (const section of sections) {
        console.log(`Adding section performance data for section ${section.section_id}...`);
        
        const scorePercentage = Math.floor(Math.random() * 100);
        let category;
        
        // Determine category based on score
        if (scorePercentage < 40) category = 'weak';
        else if (scorePercentage < 75) category = 'medium';
        else category = 'strong';
        
        await connection.execute(`
          INSERT INTO user_performance 
          (user_id, exam_id, attempt_id, section_id, score_percentage, performance_category)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          userId, 
          exam.exam_id,
          attemptId,
          section.section_id,
          scorePercentage,  // Score percentage
          category          // Category based on score
        ]);
      }
      
      // Add attempt statistics (3 attempts per exam)
      for (let i = 1; i <= 3; i++) {
        console.log(`Adding attempt statistics ${i} for exam ${exam.exam_name}...`);
        
        // Calculate random statistics
        const totalQuestions = Math.floor(Math.random() * 40) + 10;
        const correctAnswers = Math.floor(Math.random() * totalQuestions);
        const durationSeconds = Math.floor(Math.random() * 3600) + 600;
        
        // Format duration as HH:MM:SS
        const hours = Math.floor(durationSeconds / 3600);
        const minutes = Math.floor((durationSeconds % 3600) / 60);
        const seconds = durationSeconds % 60;
        const durationFormatted = 
          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        await connection.execute(`
          INSERT INTO attempt_statistics
          (user_id, exam_id, attempt_id, attempt_number, duration_seconds, duration_formatted, 
           total_questions, correct_answers)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          userId,
          exam.exam_id,
          attemptId,
          i,
          durationSeconds,
          durationFormatted,
          totalQuestions,
          correctAnswers
        ]);
      }
    }
    
    console.log('Test performance data added successfully!');
    
  } catch (error) {
    console.error('Error adding test performance data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed.');
    }
  }
}

addTestPerformanceData(); 