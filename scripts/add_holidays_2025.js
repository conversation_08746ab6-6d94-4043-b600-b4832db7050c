const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function addHolidays2025() {
  console.log('Starting to add 2025 holidays to the database...');

  // Read the SQL file
  const sqlFilePath = path.join(__dirname, '../sql/add_holidays_2025.sql');
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

  // Split the SQL content into individual statements
  const statements = sqlContent
    .split(';')
    .filter(statement => statement.trim() !== '')
    .map(statement => statement.trim() + ';');

  // Create a database connection
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'exam_prep_platform',
    multipleStatements: true
  });

  try {
    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}`);
      await connection.query(statement);
    }

    console.log('All 2025 holidays have been added successfully!');

    // Verify the holidays were added
    const [rows] = await connection.query('SELECT COUNT(*) as count FROM holiday_calendar WHERE holiday_date LIKE "2025-%"');
    console.log(`Total 2025 holidays in the database: ${rows[0].count}`);

  } catch (error) {
    console.error('Error adding holidays:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

// Run the function
addHolidays2025().catch(console.error);
