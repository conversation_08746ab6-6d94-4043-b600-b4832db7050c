/**
 * <PERSON><PERSON><PERSON> to add sample IT issue records
 */

require('dotenv').config();
const db = require('../config/database');

async function addITIssues() {
    try {
        console.log('Adding sample IT issue records...');
        
        // Get admin user ID
        const [admins] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (admins.length === 0) {
            throw new Error('Admin user not found');
        }
        const adminId = admins[0].id;
        
        // Get a regular user ID
        const [users] = await db.query('SELECT id FROM users WHERE role = "user" LIMIT 1');
        const userId = users.length > 0 ? users[0].id : adminId;
        
        // Sample IT issue data
        const issueRecords = [
            // Dell Latitude 5420 (item_id: 1)
            {
                title: 'Laptop screen flickering',
                description: 'The screen flickers when on battery power. Works fine when plugged in.',
                item_id: 1,
                reported_by: userId,
                assigned_to: adminId,
                status: 'in_progress',
                priority: 'medium',
                issue_type: 'hardware',
                resolution_notes: 'Investigating possible display cable issue.'
            },
            {
                title: 'Windows not updating',
                description: 'Windows Update shows error code 0x80070057 when trying to install updates.',
                item_id: 1,
                reported_by: userId,
                assigned_to: adminId,
                status: 'resolved',
                priority: 'low',
                issue_type: 'software',
                resolution_notes: 'Ran Windows Update troubleshooter and cleared update cache. Updates now installing correctly.'
            },
            
            // HP ProDesk 400 G7 (item_id: 2)
            {
                title: 'Computer freezing randomly',
                description: 'Desktop freezes randomly during use, requiring hard restart.',
                item_id: 2,
                reported_by: userId,
                assigned_to: adminId,
                status: 'open',
                priority: 'high',
                issue_type: 'hardware'
            },
            
            // Logitech MX Master 3 (item_id: 3)
            {
                title: 'Mouse scroll wheel not working',
                description: 'The scroll wheel on the mouse doesn\'t respond. Other buttons work fine.',
                item_id: 3,
                reported_by: userId,
                status: 'open',
                priority: 'low',
                issue_type: 'hardware'
            },
            
            // Cisco Catalyst 2960 (item_id: 4)
            {
                title: 'Switch port 5 not connecting',
                description: 'Port 5 on the switch doesn\'t recognize connected devices. No link light.',
                item_id: 4,
                reported_by: adminId,
                assigned_to: adminId,
                status: 'resolved',
                priority: 'medium',
                issue_type: 'hardware',
                resolution_notes: 'Port was disabled in configuration. Re-enabled port and tested with multiple devices.'
            },
            
            // iPad Air 4th Gen (item_id: 5)
            {
                title: 'iPad not charging',
                description: 'iPad shows charging symbol but battery percentage doesn\'t increase.',
                item_id: 5,
                reported_by: userId,
                assigned_to: adminId,
                status: 'in_progress',
                priority: 'medium',
                issue_type: 'hardware',
                resolution_notes: 'Tested with multiple chargers. Issue persists. Scheduled for repair.'
            },
            
            // ThinkPad X1 Carbon (item_id: 8)
            {
                title: 'Laptop touchpad not responding',
                description: 'Touchpad stops responding after about 10 minutes of use. External mouse works fine.',
                item_id: 8,
                reported_by: userId,
                status: 'open',
                priority: 'medium',
                issue_type: 'hardware'
            }
        ];
        
        // Insert issue records
        for (const record of issueRecords) {
            // Insert the issue
            const [result] = await db.query(`
                INSERT INTO it_issues (
                    title, description, item_id, reported_by, assigned_to,
                    status, priority, issue_type, resolution_notes,
                    resolved_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                record.title,
                record.description,
                record.item_id,
                record.reported_by,
                record.assigned_to || null,
                record.status,
                record.priority,
                record.issue_type,
                record.resolution_notes || null,
                record.status === 'resolved' ? new Date() : null
            ]);
            
            const issueId = result.insertId;
            console.log(`Added IT issue record for item ID ${record.item_id}, issue ID: ${issueId}`);
            
            // Add history record for issue creation
            await db.query(`
                INSERT INTO issue_history (
                    issue_id, changed_by, field_changed, old_value, new_value
                ) VALUES (?, ?, ?, ?, ?)
            `, [
                issueId,
                record.reported_by,
                'status',
                null,
                'open'
            ]);
            
            // Add history record for status change if not open
            if (record.status !== 'open') {
                await db.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    record.assigned_to || record.reported_by,
                    'status',
                    'open',
                    record.status
                ]);
            }
            
            // Add history record for assignment if assigned
            if (record.assigned_to) {
                await db.query(`
                    INSERT INTO issue_history (
                        issue_id, changed_by, field_changed, old_value, new_value
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    issueId,
                    adminId,
                    'assigned_to',
                    null,
                    record.assigned_to.toString()
                ]);
            }
            
            // Add a comment if resolved
            if (record.status === 'resolved' && record.resolution_notes) {
                await db.query(`
                    INSERT INTO issue_comments (
                        issue_id, comment, commented_by
                    ) VALUES (?, ?, ?)
                `, [
                    issueId,
                    `Resolution: ${record.resolution_notes}`,
                    record.assigned_to || adminId
                ]);
            }
        }
        
        console.log('Sample IT issue records added successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error adding IT issue records:', error);
        process.exit(1);
    }
}

// Run the function
addITIssues();
