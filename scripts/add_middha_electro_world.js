/**
 * <PERSON><PERSON><PERSON> to add Middha Electro World to repair_vendors table
 */

const db = require('../config/database');
const fs = require('fs');
const path = require('path');

async function addMiddhaElectroWorld() {
    try {
        console.log('Adding Middha Electro World to repair_vendors table...');
        
        // Check if vendor already exists
        const [existingVendors] = await db.query(
            'SELECT * FROM repair_vendors WHERE name = ? OR email = ?',
            ['Middha Electro World', '<EMAIL>']
        );
        
        if (existingVendors.length > 0) {
            console.log('Middha Electro World already exists in the database.');
            return;
        }
        
        // Insert the vendor
        await db.query(`
            INSERT INTO repair_vendors (
                name,
                contact_person,
                phone,
                email,
                address,
                specialization,
                notes,
                is_active
            ) VALUES (
                'Middha Electro World',
                '',
                '+91 98553 83888',
                '<EMAIL>',
                '15/7, Model Gram, Ludhiana, Punjab 141002, India',
                'Computer accessories, peripherals, CCTV systems, DVRs, home theater equipment, electronic gadgets',
                'Ludhiana-based wholesaler and distributor specializing in computer accessories, peripherals, and electronic gadgets. They offer a wide range of products including CCTV systems, DVRs, home theater equipment, and various electronics accessories. Also associated with Middha Electronics, which offers a full range of Portronics products. GSTIN: 03FFZPS3403E1ZA',
                1
            )
        `);
        
        console.log('Middha Electro World added successfully!');
    } catch (error) {
        console.error('Error adding Middha Electro World:', error);
    } finally {
        // Close the database connection
        db.end();
    }
}

// Run the function
addMiddhaElectroWorld();
