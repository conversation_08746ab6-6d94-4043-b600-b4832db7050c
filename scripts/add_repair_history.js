/**
 * <PERSON><PERSON><PERSON> to add sample repair history records
 */

require('dotenv').config();
const db = require('../config/database');

async function addRepairHistory() {
    try {
        console.log('Adding sample repair history records...');
        
        // Get admin user ID
        const [admins] = await db.query('SELECT id FROM users WHERE role = "admin" LIMIT 1');
        if (admins.length === 0) {
            throw new Error('Admin user not found');
        }
        const adminId = admins[0].id;
        
        // Sample repair history data
        const repairRecords = [
            // Dell Latitude 5420 (item_id: 1)
            {
                item_id: 1,
                vendor_id: 1, // TechFix Solutions
                sent_date: '2024-02-15',
                expected_return_date: '2024-02-25',
                returned_date: '2024-02-23',
                sent_by: adminId,
                received_by: adminId,
                issue_description: 'Keyboard not responding to certain keys. "A", "S", and "D" keys not working.',
                repair_cost: 120.50,
                repair_details: 'Replaced keyboard assembly. Tested all keys and confirmed working.',
                status: 'completed',
                notes: 'Repair completed ahead of schedule.'
            },
            {
                item_id: 1,
                vendor_id: 1, // TechFix Solutions
                sent_date: '2023-10-05',
                expected_return_date: '2023-10-15',
                returned_date: '2023-10-18',
                sent_by: adminId,
                received_by: adminId,
                issue_description: 'Battery not holding charge. Laptop shuts down after 15 minutes even when showing 50% battery.',
                repair_cost: 95.00,
                repair_details: 'Replaced battery. Tested for 4 hours on battery power, working correctly.',
                status: 'completed',
                notes: 'Slight delay in repair due to parts availability.'
            },
            
            // HP ProDesk 400 G7 (item_id: 2)
            {
                item_id: 2,
                vendor_id: 1, // TechFix Solutions
                sent_date: '2024-03-10',
                expected_return_date: '2024-03-20',
                returned_date: '2024-03-19',
                sent_by: adminId,
                received_by: adminId,
                issue_description: 'Computer not booting. Shows power light but no display.',
                repair_cost: 150.00,
                repair_details: 'Replaced faulty RAM module and reseated graphics card. System now boots normally.',
                status: 'completed',
                notes: 'Also cleaned dust from internal components.'
            },
            
            // iPad Air 4th Gen (item_id: 5)
            {
                item_id: 5,
                vendor_id: 1, // TechFix Solutions
                sent_date: '2024-04-01',
                expected_return_date: '2024-04-15',
                sent_date: '2024-04-01',
                sent_by: adminId,
                issue_description: 'Cracked screen and touch not responding in bottom right corner.',
                status: 'in_progress',
                notes: 'Waiting for replacement screen to arrive.'
            },
            
            // ThinkPad X1 Carbon (item_id: 8)
            {
                item_id: 8,
                vendor_id: 1, // TechFix Solutions
                sent_date: '2023-12-10',
                expected_return_date: '2023-12-20',
                returned_date: '2023-12-18',
                sent_by: adminId,
                received_by: adminId,
                issue_description: 'Overheating and fan making loud noise.',
                repair_cost: 85.00,
                repair_details: 'Cleaned heat sink and fan assembly. Replaced thermal paste on CPU.',
                status: 'completed',
                notes: 'Recommended regular cleaning every 6 months.'
            }
        ];
        
        // Insert repair records
        for (const record of repairRecords) {
            await db.query(`
                INSERT INTO repair_history (
                    item_id, vendor_id, sent_date, expected_return_date, returned_date,
                    sent_by, received_by, issue_description, repair_cost, repair_details,
                    status, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                record.item_id,
                record.vendor_id,
                record.sent_date,
                record.expected_return_date || null,
                record.returned_date || null,
                record.sent_by,
                record.received_by || null,
                record.issue_description,
                record.repair_cost || null,
                record.repair_details || null,
                record.status,
                record.notes || null
            ]);
            
            console.log(`Added repair record for item ID ${record.item_id}`);
            
            // Update item status if repair is in progress
            if (record.status === 'in_progress') {
                await db.query(`
                    UPDATE inventory_items
                    SET status = 'maintenance'
                    WHERE item_id = ?
                `, [record.item_id]);
                
                console.log(`Updated item ${record.item_id} status to maintenance`);
            }
        }
        
        console.log('Sample repair history records added successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error adding repair history records:', error);
        process.exit(1);
    }
}

// Run the function
addRepairHistory();
