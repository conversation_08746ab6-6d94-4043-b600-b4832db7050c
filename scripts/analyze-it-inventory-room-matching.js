/**
 * <PERSON><PERSON><PERSON> to analyze and fix IT inventory location matching with room numbers
 * 
 * This script:
 * 1. Analyzes current IT inventory location data
 * 2. Checks matching with rooms table
 * 3. Identifies unmatched equipment
 * 4. Provides suggestions for fixing location data
 * 5. Optionally fixes the location data to match room numbers
 */

const db = require('../config/database');
const SQLQueries = require('../config/sql-queries');

async function analyzeITInventoryRoomMatching() {
    console.log('🔍 Analyzing IT Inventory Location Matching with Rooms...\n');

    try {
        // 1. Get all rooms
        console.log('1. Fetching all rooms...');
        const [rooms] = await db.query(`
            SELECT id, room_number, capacity, building, floor
            FROM rooms
            ORDER BY room_number
        `);
        console.log(`   ✅ Found ${rooms.length} rooms in database`);
        
        // Display rooms
        console.log('\n   📋 Available Rooms:');
        rooms.forEach(room => {
            console.log(`      • ${room.room_number} (ID: ${room.id}, Floor: ${room.floor}, Capacity: ${room.capacity})`);
        });

        // 2. Get all IT inventory items
        console.log('\n2. Fetching all IT inventory items...');
        const [itItems] = await db.query(`
            SELECT id, name, type, serial_number, location, status, manufacturer, model
            FROM it_inventory
            ORDER BY location, name
        `);
        console.log(`   ✅ Found ${itItems.length} IT inventory items`);

        // 3. Analyze location patterns
        console.log('\n3. Analyzing location patterns...');
        const locationPatterns = new Map();
        const unmatchedLocations = new Set();
        
        itItems.forEach(item => {
            if (!item.location || item.location.trim() === '') {
                unmatchedLocations.add('(Empty/NULL)');
                return;
            }
            
            const location = item.location.trim();
            if (!locationPatterns.has(location)) {
                locationPatterns.set(location, []);
            }
            locationPatterns.get(location).push(item);
        });

        console.log(`   📊 Found ${locationPatterns.size} unique location patterns:`);
        for (const [location, items] of locationPatterns) {
            console.log(`      • "${location}" - ${items.length} items`);
        }

        // 4. Check matching with rooms using various patterns
        console.log('\n4. Checking location matching with rooms...');
        
        const matchedItems = new Map(); // roomId -> items[]
        const unmatchedItems = [];
        
        for (const [location, items] of locationPatterns) {
            let matched = false;
            
            // Try different matching patterns
            for (const room of rooms) {
                const roomNumber = room.room_number;
                const roomNumOnly = roomNumber.match(/(\d+)/)?.[1];
                
                // Pattern 1: Exact match
                if (location === roomNumber) {
                    if (!matchedItems.has(room.id)) matchedItems.set(room.id, []);
                    matchedItems.get(room.id).push(...items);
                    matched = true;
                    break;
                }
                
                // Pattern 2: "Room X" format
                if (roomNumOnly && location.toLowerCase().includes(`room ${roomNumOnly}`)) {
                    if (!matchedItems.has(room.id)) matchedItems.set(room.id, []);
                    matchedItems.get(room.id).push(...items);
                    matched = true;
                    break;
                }
                
                // Pattern 3: Just number
                if (roomNumOnly && location === roomNumOnly) {
                    if (!matchedItems.has(room.id)) matchedItems.set(room.id, []);
                    matchedItems.get(room.id).push(...items);
                    matched = true;
                    break;
                }
                
                // Pattern 4: Contains room number
                if (roomNumOnly && location.toLowerCase().includes(roomNumOnly)) {
                    if (!matchedItems.has(room.id)) matchedItems.set(room.id, []);
                    matchedItems.get(room.id).push(...items);
                    matched = true;
                    break;
                }
            }
            
            if (!matched) {
                unmatchedItems.push(...items);
            }
        }

        // 5. Display matching results
        console.log('\n5. Matching Results:');
        console.log(`   ✅ Matched: ${itItems.length - unmatchedItems.length} items`);
        console.log(`   ❌ Unmatched: ${unmatchedItems.length} items`);
        
        // Show matched items by room
        console.log('\n   📋 Matched Items by Room:');
        for (const [roomId, items] of matchedItems) {
            const room = rooms.find(r => r.id === roomId);
            console.log(`\n      🏠 ${room.room_number} (${items.length} items):`);
            
            const itemsByType = items.reduce((acc, item) => {
                if (!acc[item.type]) acc[item.type] = [];
                acc[item.type].push(item);
                return acc;
            }, {});
            
            for (const [type, typeItems] of Object.entries(itemsByType)) {
                console.log(`         📱 ${type}: ${typeItems.length} items`);
                typeItems.forEach(item => {
                    console.log(`            • ${item.name} (${item.serial_number || 'No SN'}) - ${item.status}`);
                });
            }
        }

        // Show unmatched items
        if (unmatchedItems.length > 0) {
            console.log('\n   ❌ Unmatched Items:');
            const unmatchedByLocation = unmatchedItems.reduce((acc, item) => {
                const loc = item.location || '(Empty)';
                if (!acc[loc]) acc[loc] = [];
                acc[loc].push(item);
                return acc;
            }, {});
            
            for (const [location, items] of Object.entries(unmatchedByLocation)) {
                console.log(`\n      📍 Location: "${location}" (${items.length} items):`);
                items.forEach(item => {
                    console.log(`         • ${item.name} (${item.type}) - ${item.serial_number || 'No SN'}`);
                });
            }
        }

        // 6. Provide suggestions for fixing
        console.log('\n6. Suggestions for fixing unmatched locations:');
        if (unmatchedItems.length > 0) {
            const suggestions = [];
            
            unmatchedItems.forEach(item => {
                const location = item.location || '';
                
                // Try to suggest room matches
                for (const room of rooms) {
                    const roomNumOnly = room.room_number.match(/(\d+)/)?.[1];
                    
                    if (roomNumOnly && location.toLowerCase().includes(roomNumOnly)) {
                        suggestions.push({
                            item: item,
                            currentLocation: location,
                            suggestedRoom: room.room_number,
                            reason: `Contains room number ${roomNumOnly}`
                        });
                        break;
                    }
                }
                
                // If no match found, suggest general locations
                if (!suggestions.find(s => s.item.id === item.id)) {
                    if (location.toLowerCase().includes('lab')) {
                        suggestions.push({
                            item: item,
                            currentLocation: location,
                            suggestedRoom: 'Room 1', // Computer lab
                            reason: 'Lab equipment - suggested computer lab'
                        });
                    } else if (location.toLowerCase().includes('office')) {
                        suggestions.push({
                            item: item,
                            currentLocation: location,
                            suggestedRoom: 'Room 20', // Admin office
                            reason: 'Office equipment - suggested admin area'
                        });
                    } else if (location.toLowerCase().includes('storage')) {
                        suggestions.push({
                            item: item,
                            currentLocation: location,
                            suggestedRoom: 'Room 19', // Storage
                            reason: 'Storage equipment - suggested storage room'
                        });
                    }
                }
            });
            
            if (suggestions.length > 0) {
                console.log('\n   💡 Suggested fixes:');
                suggestions.forEach(suggestion => {
                    console.log(`      • ${suggestion.item.name}: "${suggestion.currentLocation}" → "${suggestion.suggestedRoom}"`);
                    console.log(`        Reason: ${suggestion.reason}`);
                });
            }
        } else {
            console.log('   ✅ All items are properly matched!');
        }

        // 7. Summary statistics
        console.log('\n7. Summary Statistics:');
        console.log(`   📊 Total Rooms: ${rooms.length}`);
        console.log(`   📊 Total IT Items: ${itItems.length}`);
        console.log(`   📊 Matched Items: ${itItems.length - unmatchedItems.length} (${((itItems.length - unmatchedItems.length) / itItems.length * 100).toFixed(1)}%)`);
        console.log(`   📊 Unmatched Items: ${unmatchedItems.length} (${(unmatchedItems.length / itItems.length * 100).toFixed(1)}%)`);
        console.log(`   📊 Rooms with Equipment: ${matchedItems.size}`);
        console.log(`   📊 Empty Rooms: ${rooms.length - matchedItems.size}`);

        return {
            rooms,
            itItems,
            matchedItems,
            unmatchedItems,
            totalMatched: itItems.length - unmatchedItems.length,
            matchPercentage: ((itItems.length - unmatchedItems.length) / itItems.length * 100).toFixed(1)
        };

    } catch (error) {
        console.error('❌ Error analyzing IT inventory:', error);
        throw error;
    }
}

// Run the analysis if this script is executed directly
if (require.main === module) {
    analyzeITInventoryRoomMatching()
        .then((results) => {
            console.log('\n✅ Analysis completed successfully!');
            console.log(`📈 Match Rate: ${results.matchPercentage}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Analysis failed:', error);
            process.exit(1);
        });
}

module.exports = { analyzeITInventoryRoomMatching };
