const mysql = require('mysql2/promise');

async function main() {
    // Create database connection
    const conn = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Starting category assignment process...');

        // Get all categories
        const [categories] = await conn.query('SELECT * FROM categories');
        console.log(`Found ${categories.length} categories`);

        // Get questions without categories
        const [questions] = await conn.query(`
            SELECT q.question_id, q.question_text, s.section_name 
            FROM questions q
            LEFT JOIN sections s ON q.section_id = s.section_id
            WHERE q.question_id NOT IN (
                SELECT DISTINCT question_id FROM question_category_mappings
            )
        `);
        console.log(`Found ${questions.length} questions without categories`);

        // Assign categories to questions
        let assignedCount = 0;
        for (const question of questions) {
            try {
                // Assign 2-3 random categories to each question
                const numCategories = Math.floor(Math.random() * 2) + 2; // 2-3 categories
                const shuffled = [...categories].sort(() => 0.5 - Math.random());
                const selectedCategories = shuffled.slice(0, numCategories);

                // Insert category mappings
                for (const category of selectedCategories) {
                    await conn.query(
                        'INSERT INTO question_category_mappings (question_id, category_id) VALUES (?, ?)',
                        [question.question_id, category.category_id]
                    );
                }
                
                assignedCount++;
                console.log(`Assigned ${numCategories} categories to question ${question.question_id}`);
            } catch (err) {
                console.error(`Error assigning categories to question ${question.question_id}:`, err.message);
            }
        }

        console.log(`Successfully assigned categories to ${assignedCount} questions`);
    } catch (err) {
        console.error('Error:', err);
    } finally {
        await conn.end();
        console.log('Database connection closed');
    }
}

main();
