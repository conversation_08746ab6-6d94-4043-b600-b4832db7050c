/**
 * <PERSON><PERSON><PERSON> to check admin users in the database
 */

const db = require('../config/database');

async function checkAdminUsers() {
  try {
    console.log('Checking admin users in the database...');
    
    // Get all admin users
    const [adminUsers] = await db.query(`
      SELECT id, username, role FROM users WHERE role = 'admin'
    `);
    
    console.log('Admin users found:', adminUsers.length);
    console.log('Admin users:', adminUsers);
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking admin users:', error);
    process.exit(1);
  }
}

// Run the check
checkAdminUsers();
