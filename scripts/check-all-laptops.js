/**
 * <PERSON><PERSON><PERSON> to check all laptops in the IT inventory system
 * This will show current locations and help identify laptops to move
 */

const db = require('../config/database');

async function checkAllLaptops() {
    try {
        console.log('🔍 Checking all laptops in the IT inventory system...\n');

        // 1. Get all laptops from it_inventory
        console.log('1. Checking it_inventory table for laptops...');
        
        const [itLaptops] = await db.query(`
            SELECT id, name, type, serial_number, model, manufacturer, location, status, room_id
            FROM it_inventory 
            WHERE type = 'laptop'
            ORDER BY location, name
        `);

        if (itLaptops.length > 0) {
            console.log(`   📱 Found ${itLaptops.length} laptop(s) in it_inventory:`);
            itLaptops.forEach(laptop => {
                console.log(`      - ${laptop.name} (${laptop.serial_number || 'No SN'}) - Location: ${laptop.location || 'No location'} - Status: ${laptop.status}`);
            });
        } else {
            console.log('   ℹ️  No laptops found in it_inventory table');
        }

        // 2. Check inventory_items table for laptops
        console.log('\n2. Checking inventory_items table for laptops...');
        
        const [inventoryLaptops] = await db.query(`
            SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer, i.location, i.status, i.room_id,
                   c.name as category_name
            FROM inventory_items i
            LEFT JOIN inventory_categories c ON i.category_id = c.category_id
            WHERE (c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
            ORDER BY i.location, i.name
        `);

        if (inventoryLaptops.length > 0) {
            console.log(`   💻 Found ${inventoryLaptops.length} laptop(s) in inventory_items:`);
            inventoryLaptops.forEach(laptop => {
                console.log(`      - ${laptop.name} (${laptop.serial_number || 'No SN'}) - Location: ${laptop.location || 'No location'} - Status: ${laptop.status}`);
            });
        } else {
            console.log('   ℹ️  No laptops found in inventory_items table');
        }

        // 3. Check for laptops in Room 16 specifically
        console.log('\n3. Checking specifically for equipment in Room 16...');
        
        const [room16Equipment] = await db.query(`
            SELECT 'it_inventory' as source, id, name, type, serial_number, location, status
            FROM it_inventory 
            WHERE location LIKE '%16%' OR location LIKE '%Room 16%'
            UNION ALL
            SELECT 'inventory_items' as source, item_id as id, name, 'item' as type, serial_number, location, status
            FROM inventory_items 
            WHERE location LIKE '%16%' OR location LIKE '%Room 16%'
            ORDER BY source, name
        `);

        if (room16Equipment.length > 0) {
            console.log(`   🏫 Found ${room16Equipment.length} item(s) in Room 16:`);
            room16Equipment.forEach(item => {
                console.log(`      - [${item.source}] ${item.name} (${item.type}) - Location: ${item.location} - Status: ${item.status}`);
            });
        } else {
            console.log('   ℹ️  No equipment found in Room 16');
        }

        // 4. Check Computer Lab 2 current equipment
        console.log('\n4. Checking current equipment in Computer Lab 2...');
        
        const [lab2Equipment] = await db.query(`
            SELECT 'it_inventory' as source, id, name, type, serial_number, location, status
            FROM it_inventory 
            WHERE location = 'Computer Lab 2'
            UNION ALL
            SELECT 'inventory_items' as source, item_id as id, name, 'item' as type, serial_number, location, status
            FROM inventory_items 
            WHERE location = 'Computer Lab 2'
            ORDER BY source, type, name
        `);

        if (lab2Equipment.length > 0) {
            console.log(`   🖥️  Found ${lab2Equipment.length} item(s) in Computer Lab 2:`);
            lab2Equipment.forEach(item => {
                console.log(`      - [${item.source}] ${item.name} (${item.type}) - Status: ${item.status}`);
            });
        } else {
            console.log('   ℹ️  No equipment found in Computer Lab 2');
        }

        // 5. Show summary by location
        console.log('\n5. Equipment summary by location...');
        
        const [locationSummary] = await db.query(`
            SELECT 
                location,
                COUNT(*) as total_items,
                COUNT(CASE WHEN type = 'laptop' THEN 1 END) as laptops,
                COUNT(CASE WHEN type = 'desktop' THEN 1 END) as desktops
            FROM (
                SELECT location, type FROM it_inventory WHERE location IS NOT NULL AND location != ''
                UNION ALL
                SELECT location, 'item' as type FROM inventory_items WHERE location IS NOT NULL AND location != ''
            ) combined
            GROUP BY location
            HAVING total_items > 0
            ORDER BY location
        `);

        console.log(`   📊 Equipment by location:`);
        locationSummary.forEach(loc => {
            console.log(`      - ${loc.location}: ${loc.total_items} items (${loc.laptops} laptops, ${loc.desktops} desktops)`);
        });

        console.log('\n✅ Laptop inventory check completed!');

    } catch (error) {
        console.error('❌ Error during laptop check:', error);
        throw error;
    }
}

// Run the script
if (require.main === module) {
    checkAllLaptops()
        .then(() => {
            console.log('\n🎉 Script completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Script failed:', error);
            process.exit(1);
        });
}

module.exports = { checkAllLaptops };
