/**
 * <PERSON><PERSON>t to check what data is available for the CS student
 * This will help identify what's missing in our test data
 */

const db = require('../config/database');

async function checkStudentData() {
  try {
    console.log('Starting to check CS student data...');

    // Find the CS student
    const [students] = await db.query(
      'SELECT id, username, email FROM users WHERE username = ?',
      ['csstudent']
    );

    if (students.length === 0) {
      console.log('CS student not found!');
      return;
    }

    const studentId = students[0].id;
    console.log(`Found CS student with ID: ${studentId}`);

    // Check class assignments
    const [classes] = await db.query(
      `SELECT c.id, c.name
       FROM classes c
       JOIN student_classes sc ON c.id = sc.class_id
       WHERE sc.student_id = ?`,
      [studentId]
    );

    console.log('\nClass Assignments:');
    if (classes.length === 0) {
      console.log('No classes assigned to student');
    } else {
      classes.forEach(c => console.log(`- Class ${c.name} (ID: ${c.id})`));
    }

    // Check subject assignments
    const [subjects] = await db.query(
      `SELECT s.id, s.name
       FROM subjects s
       JOIN student_subjects ss ON s.id = ss.subject_id
       WHERE ss.student_id = ?`,
      [studentId]
    );

    console.log('\nSubject Assignments:');
    if (subjects.length === 0) {
      console.log('No subjects assigned to student');
    } else {
      subjects.forEach(s => console.log(`- ${s.name} (ID: ${s.id})`));
    }

    // Check upcoming practicals
    const [practicals] = await db.query(
      `SELECT p.id, p.date, p.start_time, p.end_time, c.name as class_name, s.name as subject_name,
              (SELECT GROUP_CONCAT(t.name SEPARATOR ', ')
               FROM practical_topics pt
               JOIN topics t ON pt.topic_id = t.id
               WHERE pt.practical_id = p.id) AS practical_topic,
              l.name as venue, p.status
       FROM practicals p
       JOIN classes c ON p.class_id = c.id
       JOIN subjects s ON p.subject_id = s.id
       LEFT JOIN labs l ON p.lab_id = l.id
       WHERE p.class_id IN (
         SELECT class_id FROM student_classes WHERE student_id = ?
       ) AND p.date >= CURDATE()
       ORDER BY p.date ASC, p.start_time ASC`,
      [studentId]
    );

    console.log('\nUpcoming Practicals:');
    if (practicals.length === 0) {
      console.log('No upcoming practicals found');
    } else {
      practicals.forEach(p => console.log(`- ${p.date} ${p.start_time}-${p.end_time}: ${p.subject_name} - ${p.practical_topic} (${p.status})`));
    }

    // Check practical records
    const [records] = await db.query(
      `SELECT spr.id, spr.practical_id, spr.submission_date, spr.status, spr.grade, spr.feedback,
              tp.practical_topic, tp.subject_name
       FROM student_practical_records spr
       LEFT JOIN teacher_practicals tp ON spr.practical_id = tp.id
       WHERE spr.student_id = ?
       ORDER BY spr.submission_date DESC`,
      [studentId]
    );

    console.log('\nPractical Records:');
    if (records.length === 0) {
      console.log('No practical records found');
    } else {
      records.forEach(r => console.log(`- ${r.subject_name}: ${r.practical_topic} (Status: ${r.status}, Grade: ${r.grade || 'Not graded'})`));
    }

    // Check assignments
    const [assignments] = await db.query(
      `SELECT a.id, a.title, a.description, a.due_date, a.total_marks, s.name as subject_name
       FROM assignments a
       LEFT JOIN subjects s ON a.subject_id = s.id
       WHERE (a.student_id = ? OR a.class_id IN (
         SELECT class_id FROM student_classes WHERE student_id = ?
       ))
       ORDER BY a.due_date ASC`,
      [studentId, studentId]
    );

    console.log('\nAssignments:');
    if (assignments.length === 0) {
      console.log('No assignments found');
    } else {
      assignments.forEach(a => console.log(`- ${a.subject_name}: ${a.title} (Due: ${a.due_date}, Marks: ${a.total_marks})`));
    }

    // Check assignment submissions
    const [submissions] = await db.query(
      `SELECT s.id, s.assignment_id, s.submission_date, s.status, s.marks_obtained, a.title, a.total_marks
       FROM assignment_submissions s
       JOIN assignments a ON s.assignment_id = a.id
       WHERE s.student_id = ?
       ORDER BY s.submission_date DESC`,
      [studentId]
    );

    console.log('\nAssignment Submissions:');
    if (submissions.length === 0) {
      console.log('No assignment submissions found');
    } else {
      submissions.forEach(s => console.log(`- ${s.title} (Status: ${s.status}, Marks: ${s.marks_obtained || 'Not graded'}/${s.total_marks})`));
    }

    // Check tests assigned to student
    const [tests] = await db.query(
      `SELECT e.exam_id, e.exam_name, e.description, e.duration, e.status,
              ea.start_datetime, ea.end_datetime, ea.max_attempts
       FROM exams e
       JOIN exam_assignments ea ON e.exam_id = ea.exam_id
       WHERE (ea.student_id = ? OR ea.group_id IN (
         SELECT group_id FROM group_members WHERE user_id = ?
       ))
       ORDER BY ea.end_datetime ASC`,
      [studentId, studentId]
    );

    console.log('\nTests:');
    if (tests.length === 0) {
      console.log('No tests assigned to student');
    } else {
      tests.forEach(t => console.log(`- ${t.exam_name} (Duration: ${t.duration} min, Due: ${t.end_datetime}, Status: ${t.status})`));
    }

    console.log('\nCheck completed!');

  } catch (error) {
    console.error('Error checking student data:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
checkStudentData();
