const db = require('../config/database');

async function checkDatabase() {
    try {
        // Check if database exists
        const [databases] = await db.query('SHOW DATABASES LIKE ?', ['exam_prep_platform']);
        if (databases.length === 0) {
            console.error('Database "exam_prep_platform" does not exist!');
            process.exit(1);
        }
        console.log('✓ Database "exam_prep_platform" exists');

        // Check users table structure
        const [userTableInfo] = await db.query('DESCRIBE users');
        console.log('\nUsers Table Structure:');
        console.table(userTableInfo);

        // Check if users table has any records
        const [userCount] = await db.query('SELECT COUNT(*) as count FROM users');
        console.log('\nTotal Users:', userCount[0].count);

        // Show all users (without passwords)
        const [users] = await db.query('SELECT id, name, email, role, created_at FROM users');
        if (users.length > 0) {
            console.log('\nExisting Users:');
            console.table(users);
        } else {
            console.log('\nNo users found in the database');
        }

        // Check sessions table
        const [sessionTableInfo] = await db.query('DESCRIBE sessions');
        console.log('\nSessions Table Structure:');
        console.table(sessionTableInfo);

        // Show active sessions
        const [sessions] = await db.query('SELECT * FROM sessions');
        console.log('\nActive Sessions:', sessions.length);
        if (sessions.length > 0) {
            console.table(sessions);
        }

        process.exit(0);
    } catch (error) {
        console.error('Error checking database:', error);
        process.exit(1);
    }
}

// Run the check
checkDatabase(); 