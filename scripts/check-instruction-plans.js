/**
 * <PERSON><PERSON><PERSON> to check for existing instruction plans in the database
 */

const db = require('../config/database');

async function checkInstructionPlans() {
  try {
    console.log('Checking for existing instruction plans...');
    
    // Check if instruction_plans table exists
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'instruction_plans'
    `);
    
    if (tables.length === 0) {
      console.log('instruction_plans table does not exist.');
      return;
    }
    
    // Get all instruction plans
    const [plans] = await db.query(`
      SELECT ip.id, ip.title, ip.description, ip.status, 
             u.username as teacher_username, u.email as teacher_email,
             s.name as subject_name, c.name as class_name
      FROM instruction_plans ip
      LEFT JOIN users u ON ip.teacher_id = u.id
      LEFT JOIN subjects s ON ip.subject_id = s.id
      LEFT JOIN classes c ON ip.class_id = c.id
    `);
    
    if (plans.length === 0) {
      console.log('No instruction plans found.');
    } else {
      console.log(`Found ${plans.length} instruction plans:`);
      plans.forEach(plan => {
        console.log(`ID: ${plan.id}, Title: ${plan.title}, Teacher: ${plan.teacher_username} (${plan.teacher_email}), Subject: ${plan.subject_name}, Class: ${plan.class_name}, Status: ${plan.status}`);
      });
    }
    
    // Check for CS teacher's instruction plans
    const [csTeacherPlans] = await db.query(`
      SELECT ip.id, ip.title, ip.description, ip.status, 
             u.username as teacher_username, u.email as teacher_email,
             s.name as subject_name, c.name as class_name
      FROM instruction_plans ip
      LEFT JOIN users u ON ip.teacher_id = u.id
      LEFT JOIN subjects s ON ip.subject_id = s.id
      LEFT JOIN classes c ON ip.class_id = c.id
      WHERE u.email = '<EMAIL>'
    `);
    
    if (csTeacherPlans.length === 0) {
      console.log('\nNo instruction plans found for CS teacher (<EMAIL>).');
    } else {
      console.log(`\nFound ${csTeacherPlans.length} instruction plans for CS teacher:`);
      csTeacherPlans.forEach(plan => {
        console.log(`ID: ${plan.id}, Title: ${plan.title}, Subject: ${plan.subject_name}, Class: ${plan.class_name}, Status: ${plan.status}`);
      });
    }
    
    // Check for daily instruction plans
    const [dailyPlans] = await db.query(`
      SHOW TABLES LIKE 'daily_instruction_plan'
    `);
    
    if (dailyPlans.length === 0) {
      console.log('\ndaily_instruction_plan table does not exist.');
    } else {
      const [plans] = await db.query(`
        SELECT dip.id, dip.topic, dip.date, ls.day_of_week, ls.start_time, ls.end_time,
               u.username as teacher_username, u.email as teacher_email,
               s.name as subject_name, c.name as class_name
        FROM daily_instruction_plan dip
        JOIN lecture_schedule ls ON dip.schedule_id = ls.id
        JOIN subject_class_assignment sca ON ls.assignment_id = sca.id
        JOIN users u ON ls.teacher_id = u.id
        JOIN subjects s ON sca.subject_id = s.id
        JOIN classes c ON sca.class_id = c.id
      `);
      
      if (plans.length === 0) {
        console.log('No daily instruction plans found.');
      } else {
        console.log(`\nFound ${plans.length} daily instruction plans:`);
        plans.forEach(plan => {
          console.log(`ID: ${plan.id}, Topic: ${plan.topic}, Date: ${plan.date}, Teacher: ${plan.teacher_username} (${plan.teacher_email}), Subject: ${plan.subject_name}, Class: ${plan.class_name}`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error checking instruction plans:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
checkInstructionPlans();
