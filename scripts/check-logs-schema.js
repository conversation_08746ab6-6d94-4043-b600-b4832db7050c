const db = require('../config/database');

async function checkLogsSchema() {
    try {
        console.log('Checking logs table schema...');
        
        // Get the schema of the logs table
        const [columns] = await db.query(`
            SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'exam_prep_platform'
            AND TABLE_NAME = 'logs'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('Logs table schema:');
        columns.forEach(column => {
            console.log(`${column.COLUMN_NAME} - ${column.COLUMN_TYPE} - ${column.IS_NULLABLE} - ${column.COLUMN_KEY} - ${column.COLUMN_DEFAULT}`);
        });
        
        process.exit(0);
    } catch (error) {
        console.error('Error checking logs schema:', error);
        process.exit(1);
    }
}

// Run the check
checkLogsSchema();
