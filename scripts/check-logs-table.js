const db = require('../config/database');

async function checkLogsTable() {
    try {
        console.log('Checking if logs table exists...');
        
        // Check if logs table exists
        const [tables] = await db.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'logs'
        `);
        
        if (tables.length === 0) {
            console.log('❌ logs table does not exist. Creating it...');
            
            // Create the logs table
            await db.query(`
                CREATE TABLE logs (
                    id INT NOT NULL AUTO_INCREMENT,
                    user_id INT NULL,
                    action VARCHAR(255) NOT NULL,
                    entity_type VARCHAR(50) NULL,
                    entity_id INT NULL,
                    details TEXT NULL,
                    ip_address VARCHAR(50) NULL,
                    user_agent TEXT NULL,
                    route VARCHAR(255) NULL,
                    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    <PERSON>EY idx_user_id (user_id),
                    KEY idx_action (action),
                    KEY idx_timestamp (timestamp),
                    CONSTRAINT fk_logs_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('✅ logs table created successfully');
        } else {
            console.log('✅ logs table already exists');
        }
        
        // Check if jsvalues table exists
        const [jsvaluesTables] = await db.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'jsvalues'
        `);
        
        if (jsvaluesTables.length === 0) {
            console.log('❌ jsvalues table does not exist. Creating it...');
            
            // Create the jsvalues table
            await db.query(`
                CREATE TABLE jsvalues (
                    id INT NOT NULL AUTO_INCREMENT,
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    route VARCHAR(255) DEFAULT NULL,
                    context VARCHAR(255) DEFAULT NULL,
                    variable_name VARCHAR(255) NOT NULL,
                    variable_type VARCHAR(50) DEFAULT NULL,
                    variable_value TEXT DEFAULT NULL,
                    user_id INT DEFAULT NULL,
                    ip_address VARCHAR(50) DEFAULT NULL,
                    user_agent TEXT DEFAULT NULL,
                    PRIMARY KEY (id),
                    KEY idx_timestamp (timestamp),
                    KEY idx_context (context),
                    KEY idx_variable_name (variable_name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('✅ jsvalues table created successfully');
        } else {
            console.log('✅ jsvalues table already exists');
        }
        
        // Check if query_error_logs table exists
        const [queryErrorTables] = await db.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'query_error_logs'
        `);
        
        if (queryErrorTables.length === 0) {
            console.log('❌ query_error_logs table does not exist. Creating it...');
            
            // Create the query_error_logs table
            await db.query(`
                CREATE TABLE query_error_logs (
                    id INT NOT NULL AUTO_INCREMENT,
                    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    error_code VARCHAR(50),
                    error_message TEXT,
                    query TEXT,
                    parameters TEXT,
                    route VARCHAR(255),
                    user_id INT,
                    ip_address VARCHAR(50),
                    user_agent TEXT,
                    stack_trace TEXT,
                    PRIMARY KEY (id),
                    KEY idx_timestamp (timestamp),
                    KEY idx_error_code (error_code),
                    KEY idx_user_id (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('✅ query_error_logs table created successfully');
        } else {
            console.log('✅ query_error_logs table already exists');
        }
        
        console.log('All required tables exist or have been created.');
        process.exit(0);
    } catch (error) {
        console.error('Error checking/creating tables:', error);
        process.exit(1);
    }
}

// Run the check
checkLogsTable();
