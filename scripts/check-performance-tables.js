/**
 * <PERSON><PERSON><PERSON> to check the structure of performance tables
 * Run with: node scripts/check-performance-tables.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkPerformanceTables() {
  let connection;
  
  try {
    console.log('Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'meritorious'
    });
    
    // Check user_performance table structure
    console.log('Checking user_performance table structure:');
    const [userPerformanceColumns] = await connection.execute(`
      SHOW COLUMNS FROM user_performance
    `);
    
    console.log('\nuser_performance table columns:');
    userPerformanceColumns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type}${column.Null === 'YES' ? ', NULL' : ''}${column.Default !== null ? `, DEFAULT ${column.Default}` : ''})`);
    });
    
    // Check attempt_statistics table structure
    console.log('\nChecking attempt_statistics table structure:');
    const [attemptStatsColumns] = await connection.execute(`
      SHOW COLUMNS FROM attempt_statistics
    `);
    
    console.log('\nattempt_statistics table columns:');
    attemptStatsColumns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type}${column.Null === 'YES' ? ', NULL' : ''}${column.Default !== null ? `, DEFAULT ${column.Default}` : ''})`);
    });
    
  } catch (error) {
    console.error('Error checking performance tables:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nDatabase connection closed.');
    }
  }
}

checkPerformanceTables(); 