/**
 * <PERSON><PERSON>t to check teacher_practicals table structure
 */

const db = require('../config/database');

async function checkPracticalsTable() {
  try {
    console.log('Checking teacher_practicals table structure...');
    
    // Get table structure
    const [columns] = await db.query(`
      SHOW COLUMNS FROM teacher_practicals
    `);
    
    console.log('Columns in teacher_practicals table:');
    for (const column of columns) {
      console.log(`- ${column.Field} (${column.Type})`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking table structure:', error);
    process.exit(1);
  }
}

// Run the check
checkPracticalsTable();
