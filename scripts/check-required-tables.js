/**
 * <PERSON><PERSON><PERSON> to check if all required tables exist in the database
 * This helps identify missing tables that might cause errors
 */

const db = require('../config/database');

// List of tables that should exist in the database
const requiredTables = [
  'users',
  'sessions',
  'exams',
  'sections',
  'questions',
  'options',
  'exam_attempts',
  'user_answers',
  'notifications',
  'classes',
  'subjects',
  'student_classes',
  'student_subjects',
  'instruction_plans',
  'instruction_plan_resources',
  'plan_collaborators',
  'teacher_lectures',
  'teacher_practicals',
  'student_practical_records',
  'it_inventory',
  'it_issues',
  'active_sessions',
  'logs',
  'activity_log'
];

async function checkRequiredTables() {
  try {
    console.log('Checking for required tables...');
    
    // Get all tables in the database
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [process.env.DB_NAME]);
    
    const existingTables = tables.map(t => t.TABLE_NAME);
    
    console.log('\nTable Status:');
    console.log('=============');
    
    let missingTables = [];
    
    // Check each required table
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        console.log(`✅ ${table}`);
      } else {
        console.log(`❌ ${table} (MISSING)`);
        missingTables.push(table);
      }
    }
    
    console.log('\nSummary:');
    console.log('========');
    console.log(`Total required tables: ${requiredTables.length}`);
    console.log(`Existing tables: ${requiredTables.length - missingTables.length}`);
    console.log(`Missing tables: ${missingTables.length}`);
    
    if (missingTables.length > 0) {
      console.log('\nMissing Tables:');
      missingTables.forEach(table => console.log(`- ${table}`));
      
      console.log('\nRecommendation:');
      console.log('Create the missing tables using the appropriate scripts:');
      missingTables.forEach(table => {
        console.log(`- node scripts/create-${table.replace(/_/g, '-')}-table.js`);
      });
    } else {
      console.log('\n✅ All required tables exist!');
    }
    
  } catch (error) {
    console.error('❌ Error checking required tables:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
checkRequiredTables();
