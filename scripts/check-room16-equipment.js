/**
 * <PERSON><PERSON><PERSON> to check what equipment is currently showing in Room 16
 * This will help identify why laptops are still appearing in the principal view
 */

const db = require('../config/database');

async function checkRoom16Equipment() {
    try {
        console.log('🔍 Checking Room 16 Equipment Status...\n');
        console.log('=' .repeat(60));

        // 1. Find Room 16 in the rooms table
        console.log('\n1. Checking Room 16 in rooms table...');
        
        const [room16Info] = await db.query(`
            SELECT id, room_number, building, floor, capacity
            FROM rooms 
            WHERE room_number LIKE '%16%' OR room_number = 'Room 16'
            ORDER BY room_number
        `);

        if (room16Info.length > 0) {
            console.log(`   🏫 Found ${room16Info.length} room(s) matching Room 16:`);
            room16Info.forEach(room => {
                console.log(`      - ID: ${room.id}, Name: "${room.room_number}", Building: ${room.building}, Floor: ${room.floor}`);
            });
        } else {
            console.log('   ⚠️  No rooms found matching Room 16');
        }

        // 2. Check inventory_items table for Room 16 equipment (this is what the API uses)
        console.log('\n2. Checking inventory_items table for Room 16 equipment...');
        
        for (const room of room16Info) {
            console.log(`\n   📦 Equipment in room ID ${room.id} (${room.room_number}):`);
            
            const [inventoryItems] = await db.query(`
                SELECT
                    i.item_id,
                    i.name as item_name,
                    CASE
                        WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
                        WHEN i.name LIKE '%UPS%' THEN 'other'
                        WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
                        WHEN i.name LIKE '%Laptop%' THEN 'laptop'
                        WHEN i.name LIKE '%PANEL%' THEN 'other'
                        WHEN i.name LIKE '%CAMERA%' THEN 'other'
                        WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
                        WHEN i.name LIKE '%Router%' THEN 'network'
                        ELSE 'other'
                    END as item_type,
                    i.serial_number,
                    i.status,
                    i.manufacturer,
                    i.model,
                    i.location,
                    i.room_id
                FROM inventory_items i
                WHERE i.room_id = ?
                ORDER BY item_type, i.name
            `, [room.id]);

            if (inventoryItems.length > 0) {
                console.log(`      Found ${inventoryItems.length} item(s):`);
                inventoryItems.forEach(item => {
                    console.log(`         - ${item.item_name} (${item.item_type}) - Serial: ${item.serial_number || 'N/A'} - Status: ${item.status}`);
                    if (item.item_type === 'laptop') {
                        console.log(`           🚨 LAPTOP FOUND: This should have been moved!`);
                    }
                });
            } else {
                console.log(`      No equipment found in inventory_items for this room`);
            }
        }

        // 3. Check it_inventory table for Room 16 equipment
        console.log('\n3. Checking it_inventory table for Room 16 equipment...');
        
        const [itInventoryItems] = await db.query(`
            SELECT id, name, type, serial_number, model, manufacturer, location, status, room_id
            FROM it_inventory 
            WHERE location LIKE '%16%' OR location = 'Room 16' OR room_id IN (${room16Info.map(r => r.id).join(',') || '0'})
            ORDER BY type, name
        `);

        if (itInventoryItems.length > 0) {
            console.log(`   🔧 Found ${itInventoryItems.length} item(s) in it_inventory:`);
            itInventoryItems.forEach(item => {
                console.log(`      - ${item.name} (${item.type}) - Location: "${item.location}" - Room ID: ${item.room_id} - Status: ${item.status}`);
                if (item.type === 'laptop') {
                    console.log(`        🚨 LAPTOP FOUND in it_inventory: This might be causing the issue!`);
                }
            });
        } else {
            console.log('   ✅ No equipment found in it_inventory for Room 16');
        }

        // 4. Check for any equipment with location containing "16"
        console.log('\n4. Checking for any equipment with location containing "16"...');
        
        const [allRoom16Equipment] = await db.query(`
            SELECT 'inventory_items' as source, item_id as id, name, location, status, room_id
            FROM inventory_items 
            WHERE location LIKE '%16%'
            UNION ALL
            SELECT 'it_inventory' as source, id, name, location, status, room_id
            FROM it_inventory 
            WHERE location LIKE '%16%'
            ORDER BY source, name
        `);

        if (allRoom16Equipment.length > 0) {
            console.log(`   📋 Found ${allRoom16Equipment.length} item(s) with location containing "16":`);
            allRoom16Equipment.forEach(item => {
                console.log(`      - [${item.source}] ${item.name} - Location: "${item.location}" - Room ID: ${item.room_id} - Status: ${item.status}`);
            });
        } else {
            console.log('   ✅ No equipment found with location containing "16"');
        }

        console.log('\n' + '='.repeat(60));
        console.log('✅ Room 16 equipment check completed!');

    } catch (error) {
        console.error('❌ Error checking Room 16 equipment:', error);
        throw error;
    }
}

// Run the script
if (require.main === module) {
    checkRoom16Equipment()
        .then(() => {
            console.log('\n🎉 Check completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Check failed:', error);
            process.exit(1);
        });
}

module.exports = { checkRoom16Equipment };
