/**
 * <PERSON><PERSON><PERSON> to check if all database tables are included in the schema visualization
 * This helps identify tables that might be missing from the visualization
 */

const db = require('../config/database');

// Categories from admin-schema-controller.js
const tableCategories = {
    'User Management': [
        'users', 'roles', 'groups', 'group_members', 'active_sessions', 'role_permissions',
        'permissions', 'users_old', 'user_notification_settings', 'user_performance'
    ],
    'Academic Structure': [
        'classes', 'subjects', 'trades', 'classrooms', 'student_classes', 'student_subjects', 'class_sections',
        'class_schedule', 'class_weekly_lectures', 'student_classrooms', 'subject_category',
        'subject_class_assignment', 'subject_syllabus', 'subject_trade_combinations',
        'teacher_subject_class_view', 'teacher_subject_eligibility', 'rooms'
    ],
    'Teacher Management': [
        'teacher_classes', 'teacher_subjects', 'class_incharge', 'teacher_specialization',
        'teacher_lectures', 'teacher_practicals', 'teacher_syllabus', 'teacher_weekly_lectures',
        'lecture_instruction_plan', 'lecture_schedule', 'lecture_syllabus_topic', 'lab_incharge'
    ],
    'Instruction Plans': [
        'instruction_plans', 'instruction_plan_resources', 'plan_collaborators',
        'daily_instruction_plan', 'instruction_plan_completions', 'instruction_plan_content',
        'instruction_plan_views', 'student_instruction_plans', 'student_plan_completions',
        'syllabus_progress', 'syllabus_subtopics', 'syllabus_topics'
    ],
    'Exams & Questions': [
        'exams', 'sections', 'questions', 'options', 'exam_attempts', 'user_answers',
        'categories', 'question_category_mappings', 'question_categories_view', 'question_images',
        'exam_assignments', 'group_exam_assignments', 'student_test_attempts', 'test_assignments',
        'essays'
    ],
    'IT Management': [
        'it_inventory', 'it_issues', 'inventory_items', 'inventory_categories', 'inventory_transactions',
        'procurement_items', 'vendor_quotation_items', 'hardware_condition', 'hardware_parts',
        'procurement_committee_members', 'procurement_documents', 'procurement_requests', 'procurement_vendors',
        'repair_history', 'repair_vendors'
    ],
    'System': [
        'logs', 'notifications', 'system_settings', 'holiday_calendar', 'departments',
        'access_requests', 'activity_log', 'admin_notifications', 'calendar_events',
        'email_templates', 'help_articles', 'help_article_categories', 'help_categories',
        'help_feedback', 'issue_attachments', 'issue_comments', 'issue_history',
        'messages', 'message_status', 'query_error_logs', 'query_logs', 'reports',
        'sessions', 'site_settings', 'attempt_statistics', 'chat_messages', 'group_invites', 'jsvalues'
    ],
    'Assignments & Practicals': [
        'assignments', 'assignment_submissions', 'student_assignments',
        'practicals', 'practical_records', 'practical_topics', 'student_practical_records',
        'labs', 'topics'
    ]
};

// Flatten the categories into a single array of table names
const categorizedTables = Object.values(tableCategories).flat();

async function checkSchemaTables() {
    try {
        console.log('Checking if all database tables are included in the schema visualization...');

        // Get all tables in the database
        const [tables] = await db.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'exam_prep_platform'
            ORDER BY table_name
        `);

        const allTables = tables.map(table => table.table_name);

        console.log(`\nTotal tables in database: ${allTables.length}`);

        // Find tables that are not categorized
        const uncategorizedTables = allTables.filter(table => !categorizedTables.includes(table));

        if (uncategorizedTables.length > 0) {
            console.log('\nTables not included in schema visualization:');
            uncategorizedTables.forEach(table => {
                console.log(`- ${table}`);
            });

            console.log('\nSuggested category assignments:');
            uncategorizedTables.forEach(table => {
                let suggestedCategory = 'System';

                // Try to guess the category based on table name
                if (table.includes('user') || table.includes('role') || table.includes('permission') || table.includes('auth')) {
                    suggestedCategory = 'User Management';
                } else if (table.includes('class') || table.includes('subject') || table.includes('trade') || table.includes('section')) {
                    suggestedCategory = 'Academic Structure';
                } else if (table.includes('teacher') || table.includes('lecture')) {
                    suggestedCategory = 'Teacher Management';
                } else if (table.includes('plan') || table.includes('syllabus') || table.includes('curriculum')) {
                    suggestedCategory = 'Instruction Plans';
                } else if (table.includes('exam') || table.includes('test') || table.includes('question') || table.includes('answer')) {
                    suggestedCategory = 'Exams & Questions';
                } else if (table.includes('inventory') || table.includes('item') || table.includes('equipment')) {
                    suggestedCategory = 'IT Management';
                }

                console.log(`- ${table} -> ${suggestedCategory}`);
            });
        } else {
            console.log('\nAll tables are included in the schema visualization.');
        }

        // Check for tables in categories that don't exist in the database
        const nonExistentTables = categorizedTables.filter(table => !allTables.includes(table));

        if (nonExistentTables.length > 0) {
            console.log('\nTables in categories that don\'t exist in the database:');
            nonExistentTables.forEach(table => {
                // Find which category this table is in
                const category = Object.keys(tableCategories).find(cat =>
                    tableCategories[cat].includes(table)
                );
                console.log(`- ${table} (in ${category})`);
            });
        } else {
            console.log('\nAll categorized tables exist in the database.');
        }

        process.exit(0);
    } catch (error) {
        console.error('Error checking schema tables:', error);
        process.exit(1);
    }
}

checkSchemaTables();
