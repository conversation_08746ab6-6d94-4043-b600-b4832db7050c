const mysql = require('mysql2/promise');

async function checkStaffTableStructure() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('📋 CHECKING STAFF TABLE STRUCTURE\n');

        // Get staff table structure
        const [columns] = await connection.execute('DESCRIBE staff');
        
        console.log('STAFF TABLE COLUMNS:');
        console.log('=' .repeat(50));
        columns.forEach(column => {
            console.log(`${column.Field} - ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'}`);
        });

        // Also check users table structure
        console.log('\n👤 USERS TABLE COLUMNS:');
        console.log('=' .repeat(50));
        const [userColumns] = await connection.execute('DESCRIBE users');
        userColumns.forEach(column => {
            console.log(`${column.Field} - ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'}`);
        });

    } catch (error) {
        console.error('Error checking table structure:', error);
    } finally {
        await connection.end();
    }
}

checkStaffTableStructure();
