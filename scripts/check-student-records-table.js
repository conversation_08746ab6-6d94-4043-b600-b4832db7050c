/**
 * <PERSON><PERSON>t to check student_practical_records table structure
 */

const db = require('../config/database');

async function checkStudentRecordsTable() {
  try {
    console.log('Checking student_practical_records table structure...');
    
    // Get table structure
    const [columns] = await db.query(`
      SHOW COLUMNS FROM student_practical_records
    `);
    
    console.log('Columns in student_practical_records table:');
    for (const column of columns) {
      console.log(`- ${column.Field} (${column.Type})`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking table structure:', error);
    process.exit(1);
  }
}

// Run the check
checkStudentRecordsTable();
