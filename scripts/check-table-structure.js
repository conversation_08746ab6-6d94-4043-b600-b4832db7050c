const db = require('../config/database');

async function checkTableStructure() {
    try {
        console.log('Checking database structure...');
        
        // Check if users table exists
        const [tables] = await db.query('SHOW TABLES LIKE "users"');
        if (tables.length === 0) {
            console.error('ERROR: Users table does not exist!');
            process.exit(1);
        }
        
        // Get users table structure
        const [columns] = await db.query('DESCRIBE users');
        console.log('\nUsers Table Structure:');
        console.table(columns);
        
        // Check for required columns
        const requiredColumns = ['id', 'username', 'email', 'password', 'date_of_birth', 'role'];
        const missingColumns = [];
        
        for (const required of requiredColumns) {
            const found = columns.some(col => col.Field === required);
            if (!found) {
                missingColumns.push(required);
            }
        }
        
        if (missingColumns.length > 0) {
            console.error(`\nERROR: Missing required columns: ${missingColumns.join(', ')}`);
            
            // Create missing columns
            console.log('\nAttempting to add missing columns...');
            
            for (const column of missingColumns) {
                try {
                    if (column === 'password') {
                        await db.query('ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL');
                        console.log('✓ Added password column');
                    } else if (column === 'date_of_birth') {
                        await db.query('ALTER TABLE users ADD COLUMN date_of_birth DATE NOT NULL DEFAULT "2000-01-01"');
                        console.log('✓ Added date_of_birth column');
                    } else if (column === 'role') {
                        await db.query('ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT "user"');
                        console.log('✓ Added role column');
                    } else if (column === 'username') {
                        await db.query('ALTER TABLE users ADD COLUMN username VARCHAR(255) NOT NULL DEFAULT "User"');
                        console.log('✓ Added username column');
                    } else if (column === 'email') {
                        await db.query('ALTER TABLE users ADD COLUMN email VARCHAR(255) NOT NULL UNIQUE');
                        console.log('✓ Added email column');
                    } else if (column === 'id') {
                        await db.query('ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY');
                        console.log('✓ Added id column');
                    }
                } catch (error) {
                    console.error(`Error adding ${column} column:`, error.message);
                }
            }
            
            // Check structure again
            const [updatedColumns] = await db.query('DESCRIBE users');
            console.log('\nUpdated Users Table Structure:');
            console.table(updatedColumns);
        } else {
            console.log('\n✓ All required columns exist');
        }
        
        // Check for any existing users
        const [userCount] = await db.query('SELECT COUNT(*) as count FROM users');
        console.log(`\nTotal users in database: ${userCount[0].count}`);
        
        if (userCount[0].count > 0) {
            const [users] = await db.query('SELECT id, username, email, date_of_birth, role FROM users LIMIT 5');
            console.log('\nSample users:');
            console.table(users);
        }
        
        console.log('\nDatabase check complete.');
    } catch (error) {
        console.error('Error checking database structure:', error);
    } finally {
        process.exit(0);
    }
}

checkTableStructure(); 