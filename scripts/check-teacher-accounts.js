/**
 * <PERSON><PERSON><PERSON> to check for existing teacher accounts in the database
 */

const db = require('../config/database');

async function checkTeacherAccounts() {
  try {
    console.log('Checking for existing teacher accounts...');
    
    const [teachers] = await db.query(`
      SELECT id, username, email, role, full_name 
      FROM users 
      WHERE role = 'teacher'
    `);
    
    if (teachers.length === 0) {
      console.log('No teacher accounts found.');
    } else {
      console.log(`Found ${teachers.length} teacher accounts:`);
      teachers.forEach(teacher => {
        console.log(`ID: ${teacher.id}, Username: ${teacher.username}, Email: ${teacher.email}, Name: ${teacher.full_name}`);
      });
    }
    
    // Check for demo teacher account
    const [demoTeacher] = await db.query(`
      SELECT id, username, email, role, full_name 
      FROM users 
      WHERE email = '<EMAIL>'
    `);
    
    if (demoTeacher.length > 0) {
      console.log('\nFound demo account:');
      console.log(`ID: ${demoTeacher[0].id}, Username: ${demoTeacher[0].username}, Email: ${demoTeacher[0].email}, Role: ${demoTeacher[0].role}, Name: ${demoTeacher[0].full_name}`);
    } else {
      console.log('\nDemo account (<EMAIL>) not found.');
    }
    
  } catch (error) {
    console.error('Error checking teacher accounts:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
checkTeacherAccounts();
