/**
 * <PERSON><PERSON>t to check teacher_lectures table structure
 */

const db = require('../config/database');

async function checkTeacherLectures() {
  try {
    console.log('Checking teacher_lectures table structure...');
    
    // Get table structure
    const [columns] = await db.query(`
      SHOW COLUMNS FROM teacher_lectures
    `);
    
    console.log('Columns in teacher_lectures table:');
    columns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking table structure:', error);
    process.exit(1);
  }
}

// Run the check
checkTeacherLectures();
