const mysql = require('mysql2/promise');

async function checkTimelineData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking timeline data for CS Teacher...');

        // Get CS teacher ID
        const [userResult] = await connection.execute(
            'SELECT id, email FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (userResult.length === 0) {
            console.log('CS Teacher not found');
            return;
        }

        const userId = userResult[0].id;
        console.log('CS Teacher User ID:', userId, 'Email:', userResult[0].email);

        // Check education timeline data
        console.log('\n=== EDUCATION TIMELINE DATA ===');
        const [educationData] = await connection.execute(
            'SELECT * FROM teacher_education_timeline WHERE teacher_id = ?',
            [userId]
        );
        console.log('Education timeline entries:', educationData.length);
        educationData.forEach((item, index) => {
            console.log(`${index + 1}. ${item.title} - ${item.institution} (${item.year})`);
        });

        // Check experience timeline data
        console.log('\n=== EXPERIENCE TIMELINE DATA ===');
        const [experienceData] = await connection.execute(
            'SELECT * FROM teacher_experience_timeline WHERE teacher_id = ?',
            [userId]
        );
        console.log('Experience timeline entries:', experienceData.length);
        experienceData.forEach((item, index) => {
            console.log(`${index + 1}. ${item.title} - ${item.institution}`);
            console.log(`   Duration: ${item.duration}`);
            console.log(`   Description: ${item.description}`);
            console.log(`   Current: ${item.is_current}`);
            console.log(`   Start Date: ${item.start_date}`);
            console.log(`   End Date: ${item.end_date}`);
            console.log('---');
        });

        // Check staff table data
        console.log('\n=== STAFF TABLE DATA ===');
        const [staffData] = await connection.execute(
            'SELECT employee_id, designation, joining_date, previous_organizations FROM staff WHERE user_id = ?',
            [userId]
        );
        if (staffData.length > 0) {
            console.log('Staff data found:');
            console.log('Employee ID:', staffData[0].employee_id);
            console.log('Designation:', staffData[0].designation);
            console.log('Joining Date:', staffData[0].joining_date);
            console.log('Previous Organizations:', staffData[0].previous_organizations);
        } else {
            console.log('No staff data found');
        }

    } catch (error) {
        console.error('Error checking timeline data:', error);
    } finally {
        await connection.end();
    }
}

checkTimelineData();
