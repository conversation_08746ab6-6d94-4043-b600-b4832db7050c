const mysql = require('mysql2/promise');

async function cleanStaffPreviousOrgs() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Cleaning previous_organizations field from staff table...');

        // Update the CS teacher's staff record to remove previous_organizations
        await connection.execute(`
            UPDATE staff 
            SET previous_organizations = NULL 
            WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')
        `);

        console.log('Removed previous_organizations data from CS teacher staff record');

        // Verify the change
        const [staffData] = await connection.execute(`
            SELECT employee_id, designation, joining_date, previous_organizations 
            FROM staff 
            WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>')
        `);

        if (staffData.length > 0) {
            console.log('Updated staff data:');
            console.log('Employee ID:', staffData[0].employee_id);
            console.log('Designation:', staffData[0].designation);
            console.log('Joining Date:', staffData[0].joining_date);
            console.log('Previous Organizations:', staffData[0].previous_organizations);
        }

    } catch (error) {
        console.error('Error cleaning staff data:', error);
    } finally {
        await connection.end();
    }
}

cleanStaffPreviousOrgs();
