const mysql = require('mysql2/promise');

async function getCombinedTeacherData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🎯 COMBINED TEACHER DATA FOR ONE ID (103 - CS Teacher)\n');

        const teacherId = 103;

        // Execute the comprehensive combined query
        const combinedQuery = `
            SELECT 
                -- User Table Data
                u.id as user_id,
                u.username,
                u.name,
                u.full_name,
                u.email,
                u.role,
                u.profile_image,
                u.subjects as user_subjects,
                u.bio,
                u.date_of_birth,
                u.created_at,
                u.last_login,
                u.is_active,
                u.institution,
                u.grade,
                u.field_of_study,
                u.preferred_subjects,
                u.target_exams,
                
                -- Staff Table Data
                s.id as staff_id,
                s.employee_id,
                s.designation,
                s.department,
                s.current_school,
                s.joining_date,
                s.employment_type,
                s.phone,
                s.alternate_phone,
                s.emergency_contact,
                s.address,
                s.city,
                s.state,
                s.pincode,
                s.gender,
                s.current_salary,
                s.probation_period_months,
                s.confirmation_date,
                s.last_promotion_date,
                s.performance_rating,
                s.is_on_leave,
                s.office_location,
                s.subjects_taught,
                s.classes_handled,
                s.total_experience_years,
                s.teaching_experience_years,
                s.administrative_experience_years,
                s.awards_received,
                s.publications,
                s.research_papers,
                s.conferences_attended,
                s.training_programs,
                s.notes as staff_notes,
                s.is_active as staff_active,
                
                -- Calculated Fields
                CASE 
                    WHEN u.date_of_birth IS NOT NULL THEN 
                        FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25)
                    ELSE NULL 
                END as calculated_age,
                
                CASE 
                    WHEN u.last_login IS NOT NULL THEN 
                        DATE_FORMAT(u.last_login, '%M %d, %Y at %h:%i %p')
                    ELSE 'Never logged in' 
                END as formatted_last_login,
                
                CASE 
                    WHEN u.created_at IS NOT NULL THEN 
                        DATE_FORMAT(u.created_at, '%M %d, %Y')
                    ELSE 'Unknown' 
                END as formatted_account_created,
                
                CASE 
                    WHEN u.is_active = 1 THEN 'Active'
                    ELSE 'Inactive' 
                END as account_status,
                
                COALESCE(u.full_name, u.name, u.username) as display_name,
                
                -- Summary Statistics
                (SELECT COUNT(*) FROM staff_educational_qualifications WHERE staff_id = s.id) as total_qualifications,
                (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id) as total_experience,
                (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id AND is_current = 0) as previous_experience_count,
                (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id AND is_current = 1) as current_position_count,
                (SELECT COUNT(*) FROM staff_certifications WHERE staff_id = s.id) as total_certifications,
                (SELECT COUNT(*) FROM staff_skills WHERE staff_id = s.id) as total_skills,
                (SELECT COUNT(DISTINCT skill_category) FROM staff_skills WHERE staff_id = s.id) as skill_categories_count
                
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `;

        console.log('📋 EXECUTING COMBINED QUERY...\n');
        const [teacherResult] = await connection.execute(combinedQuery, [teacherId]);
        
        if (teacherResult.length === 0) {
            console.log('❌ No teacher found with ID:', teacherId);
            return;
        }

        const teacher = teacherResult[0];

        // Get all related data
        const [educationData] = await connection.execute(`
            SELECT * FROM staff_educational_qualifications 
            WHERE staff_id = ? ORDER BY completion_year ASC
        `, [teacher.staff_id]);

        const [experienceData] = await connection.execute(`
            SELECT * FROM staff_professional_experience 
            WHERE staff_id = ? ORDER BY start_date ASC
        `, [teacher.staff_id]);

        const [certificationsData] = await connection.execute(`
            SELECT * FROM staff_certifications 
            WHERE staff_id = ? ORDER BY issue_date DESC
        `, [teacher.staff_id]);

        const [skillsData] = await connection.execute(`
            SELECT * FROM staff_skills 
            WHERE staff_id = ? ORDER BY skill_category, proficiency_level DESC
        `, [teacher.staff_id]);

        // Display comprehensive results
        console.log('✅ COMPLETE TEACHER PROFILE DATA');
        console.log('=' .repeat(100));
        
        console.log('\n👤 PERSONAL & ACCOUNT INFORMATION:');
        console.log('-' .repeat(50));
        console.log(`User ID: ${teacher.user_id}`);
        console.log(`Staff ID: ${teacher.staff_id}`);
        console.log(`Display Name: ${teacher.display_name}`);
        console.log(`Username: ${teacher.username}`);
        console.log(`Email: ${teacher.email}`);
        console.log(`Role: ${teacher.role}`);
        console.log(`Gender: ${teacher.gender}`);
        console.log(`Date of Birth: ${teacher.date_of_birth}`);
        console.log(`Age: ${teacher.calculated_age} years`);
        console.log(`Bio: ${teacher.bio || 'Not provided'}`);
        console.log(`Last Login: ${teacher.formatted_last_login}`);
        console.log(`Account Created: ${teacher.formatted_account_created}`);
        console.log(`Account Status: ${teacher.account_status}`);

        console.log('\n🏢 PROFESSIONAL INFORMATION:');
        console.log('-' .repeat(50));
        console.log(`Employee ID: ${teacher.employee_id}`);
        console.log(`Designation: ${teacher.designation}`);
        console.log(`Department: ${teacher.department}`);
        console.log(`Current School: ${teacher.current_school || 'Not specified'}`);
        console.log(`Joining Date: ${teacher.joining_date}`);
        console.log(`Employment Type: ${teacher.employment_type}`);
        console.log(`Phone: ${teacher.phone}`);
        console.log(`Address: ${teacher.address || 'Not provided'}, ${teacher.city || ''}, ${teacher.state || ''}`);
        console.log(`Subjects Taught: ${teacher.subjects_taught}`);
        console.log(`Classes Handled: ${teacher.classes_handled || 'Not specified'}`);
        console.log(`Total Experience: ${teacher.total_experience_years} years`);
        console.log(`Teaching Experience: ${teacher.teaching_experience_years} years`);
        console.log(`Performance Rating: ${teacher.performance_rating}`);
        console.log(`Current Salary: ${teacher.current_salary || 'Not disclosed'}`);
        console.log(`Awards: ${teacher.awards_received || 'None listed'}`);
        console.log(`Staff Notes: ${teacher.staff_notes || 'No notes'}`);

        console.log('\n📊 SUMMARY STATISTICS:');
        console.log('-' .repeat(50));
        console.log(`Total Educational Qualifications: ${teacher.total_qualifications}`);
        console.log(`Total Professional Experience: ${teacher.total_experience}`);
        console.log(`Previous Positions: ${teacher.previous_experience_count}`);
        console.log(`Current Position: ${teacher.current_position_count}`);
        console.log(`Total Certifications: ${teacher.total_certifications}`);
        console.log(`Total Skills: ${teacher.total_skills}`);
        console.log(`Skill Categories: ${teacher.skill_categories_count}`);

        console.log('\n📚 EDUCATIONAL QUALIFICATIONS DETAILS:');
        console.log('-' .repeat(50));
        educationData.forEach((edu, index) => {
            console.log(`${index + 1}. ${edu.qualification_name} (${edu.completion_year})`);
            console.log(`   Institution: ${edu.institution_name}`);
            console.log(`   Board: ${edu.university_board}`);
            console.log(`   Specialization: ${edu.specialization}`);
            console.log(`   Performance: ${edu.percentage}% | Grade: ${edu.grade} | CGPA: ${edu.cgpa || 'N/A'}`);
            console.log(`   Marks: ${edu.total_marks_obtained}/${edu.total_marks_maximum}`);
            if (edu.subjects) {
                const subjects = JSON.parse(edu.subjects);
                console.log(`   Subjects (${Object.keys(subjects).length}): ${Object.keys(subjects).join(', ')}`);
            }
            console.log(`   Achievements: ${edu.achievements || 'None listed'}`);
            console.log('');
        });

        console.log('\n💼 PROFESSIONAL EXPERIENCE DETAILS:');
        console.log('-' .repeat(50));
        experienceData.forEach((exp, index) => {
            const duration = exp.end_date 
                ? `${new Date(exp.start_date).toLocaleDateString()} - ${new Date(exp.end_date).toLocaleDateString()}`
                : `${new Date(exp.start_date).toLocaleDateString()} - Present`;
            
            console.log(`${index + 1}. ${exp.job_title} ${exp.is_current ? '(CURRENT)' : '(PREVIOUS)'}`);
            console.log(`   Organization: ${exp.organization_name} (${exp.organization_type})`);
            console.log(`   Duration: ${duration} (${exp.total_duration_months} months)`);
            console.log(`   Description: ${exp.job_description}`);
            console.log(`   Performance: ${exp.performance_rating} | Salary: ${exp.salary_range}`);
            
            if (exp.key_responsibilities) {
                const responsibilities = JSON.parse(exp.key_responsibilities);
                console.log(`   Responsibilities (${responsibilities.length}): ${responsibilities.slice(0, 2).join('; ')}...`);
            }
            
            if (exp.achievements) {
                const achievements = JSON.parse(exp.achievements);
                console.log(`   Achievements (${achievements.length}): ${achievements.slice(0, 2).join('; ')}...`);
            }
            
            if (exp.skills_used) {
                const skills = JSON.parse(exp.skills_used);
                console.log(`   Skills Used: ${skills.slice(0, 5).join(', ')}${skills.length > 5 ? '...' : ''}`);
            }
            console.log('');
        });

        console.log('\n📜 CERTIFICATIONS DETAILS:');
        console.log('-' .repeat(50));
        certificationsData.forEach((cert, index) => {
            console.log(`${index + 1}. ${cert.certification_name}`);
            console.log(`   Type: ${cert.certification_type} | Issuer: ${cert.issuing_organization}`);
            console.log(`   Issue Date: ${cert.issue_date} | Expiry: ${cert.is_lifetime ? 'Lifetime' : cert.expiry_date}`);
            console.log(`   Status: ${cert.verification_status} | ID: ${cert.certificate_id}`);
            console.log(`   Description: ${cert.description || 'Not provided'}`);
            if (cert.skills_covered) {
                const skills = JSON.parse(cert.skills_covered);
                console.log(`   Skills Covered: ${skills.join(', ')}`);
            }
            console.log('');
        });

        console.log('\n🛠️ SKILLS BREAKDOWN BY CATEGORY:');
        console.log('-' .repeat(50));
        const skillsByCategory = {};
        skillsData.forEach(skill => {
            if (!skillsByCategory[skill.skill_category]) {
                skillsByCategory[skill.skill_category] = [];
            }
            skillsByCategory[skill.skill_category].push(skill);
        });

        Object.entries(skillsByCategory).forEach(([category, skills]) => {
            console.log(`${category.toUpperCase()} (${skills.length} skills):`);
            skills.forEach(skill => {
                console.log(`  • ${skill.skill_name} (${skill.proficiency_level}) - ${skill.years_of_experience} years ${skill.is_certified ? '✓ Certified' : ''}`);
            });
            console.log('');
        });

        console.log('\n🎉 COMPLETE ENHANCED TEACHER PROFILE RETRIEVED!');
        console.log('=' .repeat(100));
        console.log('This represents the FULL data structure that the enhanced teacher modal displays.');
        console.log('All three issues have been resolved:');
        console.log('✅ 1. Gender information is present and displayed');
        console.log('✅ 2. Educational timeline is comprehensive with subject details');
        console.log('✅ 3. Previous experience is properly categorized and shown');

    } catch (error) {
        console.error('Error retrieving combined teacher data:', error);
    } finally {
        await connection.end();
    }
}

getCombinedTeacherData();
