/**
 * Complete Room and Equipment Linking Fix
 * 
 * This script:
 * 1. Creates missing rooms referenced in equipment tables
 * 2. Fixes IT inventory location matching
 * 3. Fixes electrical inventory room number matching
 * 4. Ensures all equipment is properly linked to existing rooms
 * 5. Provides comprehensive reporting
 */

const db = require('../config/database');

async function completeRoomEquipmentFix() {
    console.log('🔧 Complete Room and Equipment Linking Fix...\n');

    try {
        // 1. Analyze missing rooms
        console.log('1. Analyzing missing rooms...');
        
        // Get all room references from IT inventory
        const [itRoomRefs] = await db.query(`
            SELECT DISTINCT location
            FROM it_inventory
            WHERE location IS NOT NULL AND location != ''
            AND location REGEXP '^Room [0-9]+$'
        `);

        // Get all room references from electrical inventory
        const [electricalRoomRefs] = await db.query(`
            SELECT DISTINCT room_number, location
            FROM electrical_inventory
            WHERE (room_number IS NOT NULL AND room_number != '') 
               OR (location IS NOT NULL AND location != '')
        `);

        // Get existing rooms
        const [existingRooms] = await db.query(`
            SELECT room_number FROM rooms
        `);

        const existingRoomNumbers = new Set(existingRooms.map(r => r.room_number));
        const missingRooms = new Set();

        // Check IT inventory references
        itRoomRefs.forEach(ref => {
            if (!existingRoomNumbers.has(ref.location)) {
                missingRooms.add(ref.location);
            }
        });

        // Check electrical inventory references
        electricalRoomRefs.forEach(ref => {
            // Check room_number field
            if (ref.room_number) {
                const roomName = `Room ${ref.room_number}`;
                if (!existingRoomNumbers.has(roomName)) {
                    missingRooms.add(roomName);
                }
            }
            
            // Check location field for classroom patterns
            if (ref.location && ref.location.match(/Classroom (\d+)/i)) {
                const match = ref.location.match(/Classroom (\d+)/i);
                const roomName = `Room ${match[1]}`;
                if (!existingRoomNumbers.has(roomName)) {
                    missingRooms.add(roomName);
                }
            }
        });

        console.log(`   📊 Found ${missingRooms.size} missing rooms:`);
        missingRooms.forEach(room => {
            console.log(`      • ${room}`);
        });

        // 2. Create missing rooms
        console.log('\n2. Creating missing rooms...');
        
        let createdRooms = 0;
        for (const roomName of missingRooms) {
            const match = roomName.match(/Room (\d+)/);
            if (match) {
                const roomNumber = parseInt(match[1]);
                const floor = Math.ceil(roomNumber / 5);
                
                try {
                    await db.query(`
                        INSERT INTO rooms (room_number, capacity, building, floor, created_at, updated_at)
                        VALUES (?, 50, 'Main Building', ?, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE updated_at = NOW()
                    `, [roomName, floor]);
                    
                    console.log(`   ✅ Created room: ${roomName} (Floor ${floor})`);
                    createdRooms++;
                } catch (error) {
                    console.log(`   ❌ Failed to create room ${roomName}: ${error.message}`);
                }
            }
        }

        // 3. Fix IT inventory locations
        console.log('\n3. Fixing IT inventory locations...');
        
        const [itItems] = await db.query(`
            SELECT id, name, type, location
            FROM it_inventory
            WHERE location IS NOT NULL AND location != ''
        `);

        let itFixed = 0;
        for (const item of itItems) {
            let newLocation = item.location;
            let needsUpdate = false;

            // Check if location needs standardization
            if (!item.location.startsWith('Room ')) {
                // Try to extract room number and standardize
                const patterns = [
                    { regex: /Room\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                    { regex: /Classroom\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                    { regex: /Lab\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                ];

                for (const pattern of patterns) {
                    const match = item.location.match(pattern.regex);
                    if (match) {
                        newLocation = pattern.format(match);
                        needsUpdate = true;
                        break;
                    }
                }

                // Special mappings for non-room locations
                if (!needsUpdate) {
                    const specialMappings = {
                        'admin office': 'Room 20',
                        'principal office': 'Room 19',
                        'server room': 'Room 16',
                        'storage': 'Room 17',
                        'it storage': 'Room 17',
                        'it repair': 'Room 17',
                        'science dept': 'Room 3',
                        'it lab': 'Room 1',
                        'computer lab': 'Room 1'
                    };

                    const lowerLocation = item.location.toLowerCase();
                    for (const [key, value] of Object.entries(specialMappings)) {
                        if (lowerLocation.includes(key)) {
                            newLocation = value;
                            needsUpdate = true;
                            break;
                        }
                    }
                }
            }

            if (needsUpdate) {
                try {
                    await db.query(`
                        UPDATE it_inventory 
                        SET location = ?, updated_at = NOW()
                        WHERE id = ?
                    `, [newLocation, item.id]);
                    
                    console.log(`   ✅ ${item.name}: "${item.location}" → "${newLocation}"`);
                    itFixed++;
                } catch (error) {
                    console.log(`   ❌ Failed to update ${item.name}: ${error.message}`);
                }
            }
        }

        // 4. Fix electrical inventory room assignments
        console.log('\n4. Fixing electrical inventory room assignments...');
        
        const [electricalItems] = await db.query(`
            SELECT id, item_name, room_number, location
            FROM electrical_inventory
            WHERE (room_number IS NOT NULL AND room_number != '') 
               OR (location IS NOT NULL AND location != '')
        `);

        let electricalFixed = 0;
        for (const item of electricalItems) {
            let newRoomNumber = item.room_number;
            let needsUpdate = false;

            // Check location field for room number extraction
            if (item.location && item.location.match(/Classroom (\d+)/i)) {
                const match = item.location.match(/Classroom (\d+)/i);
                newRoomNumber = match[1];
                needsUpdate = true;
            } else if (item.location && item.location.match(/Room (\d+)/i)) {
                const match = item.location.match(/Room (\d+)/i);
                newRoomNumber = match[1];
                needsUpdate = true;
            }

            // Special location mappings
            if (item.location && !needsUpdate) {
                const specialMappings = {
                    'electrical room': '0',
                    'generator room': '0',
                    'main corridor': '0',
                    'staircase': '0'
                };

                const lowerLocation = item.location.toLowerCase();
                for (const [key, value] of Object.entries(specialMappings)) {
                    if (lowerLocation.includes(key)) {
                        newRoomNumber = value;
                        needsUpdate = true;
                        break;
                    }
                }
            }

            if (needsUpdate && newRoomNumber !== item.room_number) {
                try {
                    await db.query(`
                        UPDATE electrical_inventory 
                        SET room_number = ?, updated_at = NOW()
                        WHERE id = ?
                    `, [newRoomNumber, item.id]);
                    
                    console.log(`   ✅ ${item.item_name}: Room ${item.room_number} → Room ${newRoomNumber}`);
                    electricalFixed++;
                } catch (error) {
                    console.log(`   ❌ Failed to update ${item.item_name}: ${error.message}`);
                }
            }
        }

        // 5. Verify final results
        console.log('\n5. Verifying final results...');
        
        // Check IT equipment matching
        const [itMatched] = await db.query(`
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END) as matched
            FROM it_inventory i
            LEFT JOIN rooms r ON i.location = r.room_number
            WHERE i.location IS NOT NULL AND i.location != ''
        `);

        // Check electrical equipment matching
        const [electricalMatched] = await db.query(`
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END) as matched
            FROM electrical_inventory e
            LEFT JOIN rooms r ON CONCAT('Room ', e.room_number) = r.room_number
            WHERE e.room_number IS NOT NULL AND e.room_number != ''
        `);

        const itMatchRate = (itMatched[0].matched / itMatched[0].total * 100).toFixed(1);
        const electricalMatchRate = (electricalMatched[0].matched / electricalMatched[0].total * 100).toFixed(1);

        // 6. Final summary
        console.log('\n6. Final Summary:');
        console.log(`   🏗️  Rooms created: ${createdRooms}`);
        console.log(`   🔧 IT equipment fixed: ${itFixed}`);
        console.log(`   ⚡ Electrical equipment fixed: ${electricalFixed}`);
        console.log(`   📊 IT equipment match rate: ${itMatchRate}%`);
        console.log(`   📊 Electrical equipment match rate: ${electricalMatchRate}%`);

        // Get room distribution
        const [roomDistribution] = await db.query(`
            SELECT 
                r.room_number,
                COUNT(DISTINCT i.id) as it_count,
                COUNT(DISTINCT e.id) as electrical_count
            FROM rooms r
            LEFT JOIN it_inventory i ON i.location = r.room_number
            LEFT JOIN electrical_inventory e ON CONCAT('Room ', e.room_number) = r.room_number
            GROUP BY r.id, r.room_number
            HAVING it_count > 0 OR electrical_count > 0
            ORDER BY r.room_number
        `);

        console.log('\n   📊 Equipment distribution by room:');
        roomDistribution.forEach(room => {
            console.log(`      • ${room.room_number}: ${room.it_count} IT + ${room.electrical_count} electrical`);
        });

        return {
            roomsCreated: createdRooms,
            itFixed,
            electricalFixed,
            itMatchRate: parseFloat(itMatchRate),
            electricalMatchRate: parseFloat(electricalMatchRate)
        };

    } catch (error) {
        console.error('❌ Error in complete room equipment fix:', error);
        throw error;
    }
}

// Run the fix if this script is executed directly
if (require.main === module) {
    completeRoomEquipmentFix()
        .then((results) => {
            console.log('\n✅ Complete room equipment fix completed successfully!');
            console.log(`📈 IT Match Rate: ${results.itMatchRate}%`);
            console.log(`📈 Electrical Match Rate: ${results.electricalMatchRate}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Complete fix failed:', error);
            process.exit(1);
        });
}

module.exports = { completeRoomEquipmentFix };
