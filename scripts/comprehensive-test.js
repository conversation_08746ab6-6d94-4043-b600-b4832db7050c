const mysql = require('mysql2/promise');

async function comprehensiveTest() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🧪 COMPREHENSIVE TEST FOR ALL THREE ISSUES\n');

        // Get CS teacher data
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.email = '<EMAIL>' AND u.role = 'teacher'
        `);

        if (teacherData.length === 0) {
            console.log('❌ CS Teacher not found');
            return;
        }

        const teacher = teacherData[0];
        console.log('✅ CS Teacher found successfully\n');

        // TEST 1: Gender Column
        console.log('🔍 TEST 1: GENDER COLUMN');
        console.log('=' .repeat(50));
        console.log(`Gender from staff table: ${teacher.gender || 'Not set'}`);
        if (teacher.gender) {
            console.log('✅ Gender column exists and has data');
        } else {
            console.log('❌ Gender column missing or no data');
        }

        // TEST 2: Educational Timeline
        console.log('\n🔍 TEST 2: EDUCATIONAL TIMELINE');
        console.log('=' .repeat(50));
        
        if (teacher.staff_id) {
            const [educationData] = await connection.execute(`
                SELECT qualification_level, qualification_name, specialization, institution_name,
                       university_board, total_marks_obtained, total_marks_maximum, percentage,
                       grade, cgpa, completion_year, subjects, achievements, thesis_title
                FROM staff_educational_qualifications
                WHERE staff_id = ?
                ORDER BY completion_year ASC
            `, [teacher.staff_id]);

            console.log(`Found ${educationData.length} educational qualifications:`);
            if (educationData.length > 0) {
                educationData.forEach((item, index) => {
                    console.log(`${index + 1}. ${item.qualification_name} (${item.completion_year}) - ${item.percentage}%`);
                });
                console.log('✅ Educational timeline has data');
            } else {
                console.log('❌ Educational timeline is empty');
            }
        } else {
            console.log('❌ No staff_id found');
        }

        // TEST 3: Experience Timeline (Previous Experience)
        console.log('\n🔍 TEST 3: EXPERIENCE TIMELINE (PREVIOUS EXPERIENCE)');
        console.log('=' .repeat(50));
        
        if (teacher.staff_id) {
            const [experienceData] = await connection.execute(`
                SELECT job_title, organization_name, organization_type, start_date, end_date,
                       is_current, total_duration_months, job_description, key_responsibilities,
                       achievements, skills_used, salary_range, performance_rating
                FROM staff_professional_experience
                WHERE staff_id = ?
                ORDER BY start_date ASC
            `, [teacher.staff_id]);

            console.log(`Found ${experienceData.length} professional experiences:`);
            
            let currentCount = 0;
            let previousCount = 0;
            
            experienceData.forEach((item, index) => {
                const status = item.is_current ? 'CURRENT' : 'PREVIOUS';
                const duration = item.end_date 
                    ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                    : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;
                
                console.log(`${index + 1}. ${item.job_title} (${status}) - ${duration}`);
                
                if (item.is_current) {
                    currentCount++;
                } else {
                    previousCount++;
                }
            });
            
            console.log(`\nSummary: ${currentCount} current, ${previousCount} previous positions`);
            
            if (previousCount > 0) {
                console.log('✅ Previous experience data exists');
            } else {
                console.log('❌ No previous experience found');
            }
        } else {
            console.log('❌ No staff_id found');
        }

        // TEST 4: API Data Formatting
        console.log('\n🔍 TEST 4: API DATA FORMATTING');
        console.log('=' .repeat(50));
        
        // Format user data like the API does
        const displayName = teacher.full_name || teacher.name || teacher.username;
        const primaryEmail = teacher.email;
        const dateOfBirth = teacher.date_of_birth;
        
        // Calculate age if date of birth is available
        let age = null;
        if (teacher.date_of_birth) {
            const today = new Date();
            const birthDate = new Date(teacher.date_of_birth);
            age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
        }
        
        const lastLoginFormatted = teacher.last_login ? new Date(teacher.last_login).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Never logged in';
        
        const accountCreated = teacher.created_at ? new Date(teacher.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }) : 'Unknown';
        
        const accountStatus = teacher.is_active ? 'Active' : 'Inactive';

        console.log('Formatted API data:');
        console.log(`- Display Name: ${displayName}`);
        console.log(`- Primary Email: ${primaryEmail}`);
        console.log(`- Gender: ${teacher.gender || 'Not specified'}`);
        console.log(`- Date of Birth: ${dateOfBirth || 'Not provided'}`);
        console.log(`- Age: ${age || 'Not calculated'}`);
        console.log(`- Bio: ${teacher.bio || 'Not provided'}`);
        console.log(`- Last Login: ${lastLoginFormatted}`);
        console.log(`- Account Status: ${accountStatus}`);
        console.log(`- Staff Notes: ${teacher.staff_notes || 'No notes'}`);

        console.log('\n✅ COMPREHENSIVE TEST COMPLETED!');
        console.log('\n📊 FINAL SUMMARY:');
        console.log(`1. Gender Column: ${teacher.gender ? '✅ Working' : '❌ Missing'}`);
        console.log(`2. Educational Timeline: ${teacher.staff_id ? '✅ Available' : '❌ Missing'}`);
        console.log(`3. Experience Timeline: ${teacher.staff_id ? '✅ Available' : '❌ Missing'}`);
        console.log(`4. User Data Integration: ✅ Working`);

    } catch (error) {
        console.error('Error in comprehensive test:', error);
    } finally {
        await connection.end();
    }
}

comprehensiveTest();
