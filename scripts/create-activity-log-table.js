/**
 * <PERSON><PERSON><PERSON> to create the activity_log table
 * This table is used to track user activities in the system
 */

const db = require('../config/database');

async function createActivityLogTable() {
  try {
    console.log('Checking if activity_log table exists...');
    
    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activity_log'
    `, [process.env.DB_NAME]);
    
    if (tables.length > 0) {
      console.log('✅ activity_log table already exists');
      return;
    }
    
    console.log('Creating activity_log table...');
    
    // Create the table
    await db.query(`
      CREATE TABLE activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent VARCHA<PERSON>(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ activity_log table created successfully');
    
    // Add some sample data if users table has data
    const [users] = await db.query(`
      SELECT id FROM users LIMIT 3
    `);
    
    if (users.length > 0) {
      console.log('Adding sample data to activity_log table...');
      
      // Sample activities
      const sampleActivities = [
        { action: 'login', details: 'User logged in', ip: '*************', agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)' },
        { action: 'logout', details: 'User logged out', ip: '*************', agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)' },
        { action: 'profile_update', details: 'User updated profile information', ip: '*************', agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)' },
        { action: 'password_change', details: 'User changed password', ip: '*************', agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)' },
        { action: 'test_started', details: 'User started a test', ip: '*************', agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)' },
        { action: 'test_completed', details: 'User completed a test with score 85%', ip: '*************', agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)' },
        { action: 'role_updated', details: 'Admin updated user role', ip: '*************', agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)' }
      ];
      
      // Add sample activities for each user
      for (const user of users) {
        // Add 3-5 random activities for each user
        const numActivities = Math.floor(Math.random() * 3) + 3;
        
        for (let i = 0; i < numActivities; i++) {
          // Pick a random activity
          const activityIndex = Math.floor(Math.random() * sampleActivities.length);
          const activity = sampleActivities[activityIndex];
          
          // Add timestamp offset for realistic activity log
          const daysAgo = Math.floor(Math.random() * 30);
          const hoursAgo = Math.floor(Math.random() * 24);
          const timestamp = new Date();
          timestamp.setDate(timestamp.getDate() - daysAgo);
          timestamp.setHours(timestamp.getHours() - hoursAgo);
          
          await db.query(`
            INSERT INTO activity_log 
            (user_id, action, details, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [
            user.id,
            activity.action,
            activity.details,
            activity.ip,
            activity.agent,
            timestamp
          ]);
        }
      }
      
      console.log('✅ Sample data added to activity_log table');
    } else {
      console.log('⚠️ No users found to add sample activities');
    }
    
  } catch (error) {
    console.error('❌ Error creating activity_log table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createActivityLogTable();
