const bcrypt = require('bcrypt');
const db = require('../config/database');

async function createAdminUser() {
    try {
        // Check if admin already exists
        const [existingUsers] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (existingUsers.length > 0) {
            console.log('Admin user already exists');
            process.exit(0);
        }

        // Hash password
        const hashedPassword = await bcrypt.hash('pass123#', 10);

        // Insert admin user
        const [result] = await db.query(
            'INSERT INTO users (username, name, email, password, role, date_of_birth, created_at, last_login, bio) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)',
            ['admin', 'Admin User', '<EMAIL>', hashedPassword, 'admin', '1990-01-01', 'Administrator account']
        );

        console.log('Admin user created successfully');
        console.log('Email: <EMAIL>');
        console.log('Password: pass123#');
        process.exit(0);
    } catch (error) {
        console.error('Error creating admin user:', error);
        process.exit(1);
    }
}

createAdminUser();