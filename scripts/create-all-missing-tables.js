/**
 * <PERSON><PERSON><PERSON> to create all missing tables in one go
 * This is useful for setting up a new database or fixing missing tables
 */

const db = require('../config/database');
const fs = require('fs');
const path = require('path');

// List of all required tables
const requiredTables = [
  'users',
  'sessions',
  'exams',
  'sections',
  'questions',
  'options',
  'exam_attempts',
  'user_answers',
  'notifications',
  'classes',
  'subjects',
  'student_classes',
  'student_subjects',
  'instruction_plans',
  'instruction_plan_resources',
  'plan_collaborators',
  'teacher_lectures',
  'teacher_practicals',
  'student_practical_records',
  'it_inventory',
  'it_issues',
  'active_sessions',
  'logs',
  'activity_log'
];

// Map of table names to their creation scripts
const tableScripts = {
  'student_classes': require('./create-student-classes-table'),
  'student_subjects': require('./create-student-subjects-table'),
  'instruction_plan_resources': require('./create-instruction-plan-resources-table'),
  'plan_collaborators': require('./create-plan-collaborators-table'),
  'it_inventory': require('./create-it-inventory-table'),
  'activity_log': require('./create-activity-log-table')
};

async function createAllMissingTables() {
  try {
    console.log('Checking for missing tables...');
    
    // Get all tables in the database
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [process.env.DB_NAME]);
    
    const existingTables = tables.map(t => t.TABLE_NAME);
    const missingTables = requiredTables.filter(t => !existingTables.includes(t));
    
    console.log(`Found ${missingTables.length} missing tables.`);
    
    if (missingTables.length === 0) {
      console.log('✅ All required tables already exist!');
      return;
    }
    
    console.log('Missing tables:');
    missingTables.forEach(table => console.log(`- ${table}`));
    
    // Create each missing table
    for (const table of missingTables) {
      if (tableScripts[table]) {
        console.log(`\nCreating ${table} table...`);
        await tableScripts[table]();
        console.log(`✅ ${table} table created successfully`);
      } else {
        console.log(`⚠️ No creation script found for ${table} table`);
      }
    }
    
    console.log('\n✅ All missing tables have been created!');
    
  } catch (error) {
    console.error('❌ Error creating missing tables:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
createAllMissingTables();
