/**
 * <PERSON><PERSON><PERSON> to create demo users for different roles
 */

const bcrypt = require('bcrypt');
const db = require('../config/database');

async function createDemoUsers() {
    try {
        console.log('Creating demo users...');

        // Demo users configuration
        const demoUsers = [
            {
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123',
                role: 'admin',
                fullName: 'Admin User',
                dateOfBirth: '1990-01-01',
                bio: 'Demo administrator account'
            },
            {
                username: 'teacher',
                email: '<EMAIL>',
                password: 'teacher123',
                role: 'teacher',
                fullName: 'Teacher User',
                dateOfBirth: '1985-05-15',
                bio: 'Demo teacher account'
            },
            {
                username: 'student',
                email: '<EMAIL>',
                password: 'student123',
                role: 'student',
                fullName: 'Student User',
                dateOfBirth: '2000-03-20',
                bio: 'Demo student account'
            },
            {
                username: 'itadmin',
                email: '<EMAIL>',
                password: 'itadmin123',
                role: 'it_admin',
                fullName: 'IT Admin User',
                dateOfBirth: '1988-07-10',
                bio: 'Demo IT administrator account'
            }
        ];

        // Create each demo user if they don't exist
        for (const user of demoUsers) {
            // Check if user already exists
            const [existingUsers] = await db.query(
                'SELECT id FROM users WHERE email = ? OR username = ?',
                [user.email, user.username]
            );

            if (existingUsers.length > 0) {
                console.log(`Demo user ${user.username} (${user.role}) already exists`);
                continue;
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(user.password, 10);

            // Insert user
            await db.query(
                `INSERT INTO users (
                    username,
                    name,
                    email,
                    password,
                    role,
                    full_name,
                    date_of_birth,
                    bio,
                    is_active,
                    is_approved,
                    created_at,
                    last_login
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1, NOW(), NOW())`,
                [
                    user.username,
                    user.fullName, // Use fullName for the name field
                    user.email,
                    hashedPassword,
                    user.role,
                    user.fullName,
                    user.dateOfBirth,
                    user.bio
                ]
            );

            console.log(`Created demo user: ${user.username} (${user.role})`);
        }

        console.log('Demo users creation completed!');

    } catch (error) {
        console.error('Error creating demo users:', error);
    } finally {
        process.exit(0);
    }
}

// Run the function
createDemoUsers();
