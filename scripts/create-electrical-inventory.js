/**
 * Create Electrical Inventory Table and Populate Data
 */

const mysql = require('mysql2/promise');

async function createElectricalInventory() {
  try {
    console.log('🔌 Creating Electrical Inventory System...\n');

    // Database connection
    const db = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'exam_prep_platform'
    });

    console.log('✅ Connected to database');

    // Create electrical_inventory table
    console.log('📋 Creating electrical_inventory table...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS electrical_inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        item_name VARCHAR(255) NOT NULL,
        item_type ENUM('tubelight', 'fan', 'outlet', 'switch', 'wire', 'other') NOT NULL,
        serial_number VARCHAR(100) UNIQUE NOT NULL,
        location VARCHAR(100) NOT NULL,
        room_number INT,
        installation_date DATE,
        warranty_end_date DATE,
        status ENUM('working', 'faulty', 'maintenance', 'replaced') NOT NULL DEFAULT 'working',
        wattage INT,
        voltage VARCHAR(20) DEFAULT '220V',
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        notes TEXT,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Electrical inventory table created');

    // Clear existing data
    await db.query('DELETE FROM electrical_inventory');
    console.log('🧹 Cleared existing electrical inventory data');

    // Insert electrical items for all 20 classrooms
    console.log('💡 Inserting electrical inventory data...');

    const electricalItems = [];

    for (let room = 1; room <= 20; room++) {
      // 5 Tube lights per room
      for (let i = 1; i <= 5; i++) {
        electricalItems.push([
          `Tube Light ${i}`,
          'tubelight',
          `TL-${room}-${String(i).padStart(3, '0')}`,
          `Classroom ${room}`,
          room,
          '2023-01-15',
          '2025-01-15',
          'working',
          40,
          '220V',
          'Philips',
          'TL-D 36W',
          `Tube light ${i} in classroom ${room}`,
          '2024-01-15',
          '2024-07-15'
        ]);
      }

      // 4 Ceiling fans per room
      for (let i = 1; i <= 4; i++) {
        electricalItems.push([
          `Ceiling Fan ${i}`,
          'fan',
          `CF-${room}-${String(i).padStart(3, '0')}`,
          `Classroom ${room}`,
          room,
          '2023-01-15',
          '2026-01-15',
          'working',
          75,
          '220V',
          'Bajaj',
          'Maxima 1200mm',
          `Ceiling fan ${i} in classroom ${room}`,
          '2024-01-15',
          '2024-07-15'
        ]);
      }

      // 8 Power outlets per room
      for (let i = 1; i <= 8; i++) {
        electricalItems.push([
          `Power Outlet ${i}`,
          'outlet',
          `PO-${room}-${String(i).padStart(3, '0')}`,
          `Classroom ${room}`,
          room,
          '2023-01-15',
          '2028-01-15',
          'working',
          null,
          '220V',
          'Legrand',
          'Myrius 6A Socket',
          `Power outlet ${i} in classroom ${room}`,
          '2024-01-15',
          '2024-12-15'
        ]);
      }

      // 2 Light switches per room
      for (let i = 1; i <= 2; i++) {
        electricalItems.push([
          `Light Switch ${i}`,
          'switch',
          `LS-${room}-${String(i).padStart(3, '0')}`,
          `Classroom ${room}`,
          room,
          '2023-01-15',
          '2028-01-15',
          'working',
          null,
          '220V',
          'Legrand',
          'Myrius 16A Switch',
          `Light switch ${i} in classroom ${room}`,
          '2024-01-15',
          '2024-12-15'
        ]);
      }

      // 2 Fan switches per room
      for (let i = 1; i <= 2; i++) {
        electricalItems.push([
          `Fan Switch ${i}`,
          'switch',
          `FS-${room}-${String(i).padStart(3, '0')}`,
          `Classroom ${room}`,
          room,
          '2023-01-15',
          '2028-01-15',
          'working',
          null,
          '220V',
          'Legrand',
          'Myrius Fan Regulator',
          `Fan switch ${i} in classroom ${room}`,
          '2024-01-15',
          '2024-12-15'
        ]);
      }
    }

    // Insert all electrical items
    const insertQuery = `
      INSERT INTO electrical_inventory (
        item_name, item_type, serial_number, location, room_number,
        installation_date, warranty_end_date, status, wattage, voltage,
        manufacturer, model, notes, last_maintenance_date, next_maintenance_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    for (const item of electricalItems) {
      await db.query(insertQuery, item);
    }

    console.log(`✅ Inserted ${electricalItems.length} electrical items`);

    // Add some common area electrical items
    console.log('🏢 Adding common area electrical items...');

    const commonAreaItems = [
      ['Main Distribution Board', 'other', 'MDB-001', 'Electrical Room', 0, '2023-01-15', '2030-01-15', 'working', null, '440V', 'Schneider', 'Acti 9', 'Main electrical distribution board', '2024-01-15', '2024-06-15'],
      ['Emergency Exit Light 1', 'other', 'EEL-001', 'Main Corridor', 0, '2023-01-15', '2025-01-15', 'working', 8, '220V', 'Crompton', 'Emergency LED', 'Emergency exit light in main corridor', '2024-01-15', '2024-06-15'],
      ['Emergency Exit Light 2', 'other', 'EEL-002', 'Staircase 1', 0, '2023-01-15', '2025-01-15', 'working', 8, '220V', 'Crompton', 'Emergency LED', 'Emergency exit light in staircase 1', '2024-01-15', '2024-06-15'],
      ['Emergency Exit Light 3', 'other', 'EEL-003', 'Staircase 2', 0, '2023-01-15', '2025-01-15', 'working', 8, '220V', 'Crompton', 'Emergency LED', 'Emergency exit light in staircase 2', '2024-01-15', '2024-06-15'],
      ['Generator', 'other', 'GEN-001', 'Generator Room', 0, '2023-01-15', '2028-01-15', 'working', 50000, '440V', 'Cummins', 'C50 D5', 'Backup generator for school', '2024-01-15', '2024-04-15']
    ];

    for (const item of commonAreaItems) {
      await db.query(insertQuery, item);
    }

    console.log(`✅ Added ${commonAreaItems.length} common area electrical items`);

    // Display summary
    const [summary] = await db.query(`
      SELECT
        item_type,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'working' THEN 1 ELSE 0 END) as working_count
      FROM electrical_inventory
      GROUP BY item_type
      ORDER BY item_type
    `);

    console.log('\n📊 ELECTRICAL INVENTORY SUMMARY:');
    console.log('=====================================');
    summary.forEach(row => {
      console.log(`${row.item_type.toUpperCase()}: ${row.count} total (${row.working_count} working)`);
    });

    const [total] = await db.query('SELECT COUNT(*) as total FROM electrical_inventory');
    console.log(`\n🎯 TOTAL ELECTRICAL ITEMS: ${total[0].total}`);

    await db.end();
    console.log('\n✅ Electrical inventory system created successfully!');

  } catch (error) {
    console.error('❌ Error creating electrical inventory:', error.message);
  }
}

// Run the script
createElectricalInventory();
