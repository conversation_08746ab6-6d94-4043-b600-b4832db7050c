/**
 * <PERSON><PERSON><PERSON> to create the instruction_plan_resources table
 * This table is used to store resources associated with instruction plans
 */

const db = require('../config/database');

async function createInstructionPlanResourcesTable() {
  try {
    console.log('Checking if instruction_plan_resources table exists...');
    
    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'instruction_plan_resources'
    `, [process.env.DB_NAME]);
    
    if (tables.length > 0) {
      console.log('✅ instruction_plan_resources table already exists');
      return;
    }
    
    console.log('Creating instruction_plan_resources table...');
    
    // Create the table
    await db.query(`
      CREATE TABLE instruction_plan_resources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL,
        resource_name VARCHAR(255) NOT NULL,
        resource_type ENUM('file', 'link', 'text') NOT NULL DEFAULT 'file',
        resource_path VARCHAR(255) DEFAULT NULL,
        resource_content TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ instruction_plan_resources table created successfully');
    
    // Add some sample data if instruction_plans table has data
    const [plans] = await db.query(`
      SELECT id FROM instruction_plans LIMIT 3
    `);
    
    if (plans.length > 0) {
      console.log('Adding sample data to instruction_plan_resources table...');
      
      // Sample resource data
      const sampleResources = [
        { name: 'Lecture Notes', type: 'file', path: '/uploads/resources/sample-notes.pdf' },
        { name: 'Reference Material', type: 'link', path: 'https://example.com/reference' },
        { name: 'Additional Reading', type: 'text', content: 'Students should read chapters 5-7 of the textbook before the next class.' },
        { name: 'Practice Problems', type: 'file', path: '/uploads/resources/practice-problems.pdf' },
        { name: 'Video Tutorial', type: 'link', path: 'https://example.com/tutorial' }
      ];
      
      // Add sample resources to each plan
      for (const plan of plans) {
        // Add 2-4 random resources to each plan
        const numResources = Math.floor(Math.random() * 3) + 2;
        const usedResourceIndices = [];
        
        for (let i = 0; i < numResources; i++) {
          // Pick a random resource that hasn't been used yet
          let resourceIndex;
          do {
            resourceIndex = Math.floor(Math.random() * sampleResources.length);
          } while (usedResourceIndices.includes(resourceIndex));
          
          usedResourceIndices.push(resourceIndex);
          const resource = sampleResources[resourceIndex];
          
          if (resource.type === 'text') {
            await db.query(`
              INSERT INTO instruction_plan_resources 
              (plan_id, resource_name, resource_type, resource_content)
              VALUES (?, ?, ?, ?)
            `, [plan.id, resource.name, resource.type, resource.content]);
          } else {
            await db.query(`
              INSERT INTO instruction_plan_resources 
              (plan_id, resource_name, resource_type, resource_path)
              VALUES (?, ?, ?, ?)
            `, [plan.id, resource.name, resource.type, resource.path]);
          }
        }
      }
      
      console.log('✅ Sample data added to instruction_plan_resources table');
    } else {
      console.log('⚠️ No instruction plans found to add sample resources');
    }
    
  } catch (error) {
    console.error('❌ Error creating instruction_plan_resources table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createInstructionPlanResourcesTable();
