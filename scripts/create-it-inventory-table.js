/**
 * <PERSON><PERSON><PERSON> to create the it_inventory table
 * This table is used to track IT hardware inventory
 */

const db = require('../config/database');

async function createITInventoryTable() {
  try {
    console.log('Checking if it_inventory table exists...');
    
    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'it_inventory'
    `, [process.env.DB_NAME]);
    
    if (tables.length > 0) {
      console.log('✅ it_inventory table already exists');
      return;
    }
    
    console.log('Creating it_inventory table...');
    
    // Create the table
    await db.query(`
      CREATE TABLE it_inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type ENUM('laptop', 'desktop', 'tablet', 'projector', 'printer', 'network', 'other') NOT NULL,
        serial_number VARCHAR(100),
        model VARCHAR(100),
        manufacturer VARCHAR(100),
        purchase_date DATE,
        warranty_end_date DATE,
        status ENUM('available', 'assigned', 'in_repair', 'retired') NOT NULL DEFAULT 'available',
        condition_status ENUM('excellent', 'good', 'fair', 'poor') NOT NULL DEFAULT 'good',
        assigned_to INT DEFAULT NULL,
        location VARCHAR(100),
        notes TEXT,
        mac_address VARCHAR(20),
        ip_address VARCHAR(20),
        hostname VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ it_inventory table created successfully');
    
    // Add some sample data
    console.log('Adding sample data to it_inventory table...');
    
    // Sample laptop data
    await db.query(`
      INSERT INTO it_inventory 
      (name, type, serial_number, model, manufacturer, purchase_date, warranty_end_date, status, condition_status, location, notes)
      VALUES
      ('Laptop 101', 'laptop', 'SN12345678', 'ThinkPad X1', 'Lenovo', '2022-01-15', '2025-01-15', 'available', 'excellent', 'IT Lab', 'New laptop for staff'),
      ('Laptop 102', 'laptop', 'SN23456789', 'MacBook Pro', 'Apple', '2021-06-10', '2024-06-10', 'assigned', 'good', 'Science Dept', 'Assigned to science teacher'),
      ('Laptop 103', 'laptop', 'SN34567890', 'XPS 13', 'Dell', '2022-03-20', '2025-03-20', 'in_repair', 'fair', 'IT Repair', 'Screen flickering issue')
    `);
    
    // Sample desktop data
    await db.query(`
      INSERT INTO it_inventory 
      (name, type, serial_number, model, manufacturer, purchase_date, warranty_end_date, status, condition_status, location, notes, mac_address, ip_address, hostname)
      VALUES
      ('Desktop 201', 'desktop', 'SN45678901', 'Veriton X', 'Acer', '2022-02-15', '2025-02-15', 'available', 'excellent', 'Computer Lab 1', 'Lab computer', '00:1A:2B:3C:4D:5E', '*************', 'LAB1-PC01'),
      ('Desktop 202', 'desktop', 'SN56789012', 'Veriton X', 'Acer', '2022-02-15', '2025-02-15', 'available', 'good', 'Computer Lab 1', 'Lab computer', '00:1A:2B:3C:4D:5F', '*************', 'LAB1-PC02'),
      ('Desktop 203', 'desktop', 'SN67890123', 'Veriton X', 'Acer', '2022-02-15', '2025-02-15', 'in_repair', 'poor', 'IT Repair', 'Not booting', '00:1A:2B:3C:4D:60', '*************', 'LAB1-PC03')
    `);
    
    // Sample other equipment
    await db.query(`
      INSERT INTO it_inventory 
      (name, type, serial_number, model, manufacturer, purchase_date, warranty_end_date, status, condition_status, location, notes)
      VALUES
      ('Projector 301', 'projector', 'SN78901234', 'EB-X05', 'Epson', '2022-04-10', '2024-04-10', 'available', 'good', 'Storage', 'Portable projector'),
      ('Printer 401', 'printer', 'SN89012345', 'LaserJet Pro', 'HP', '2021-12-05', '2023-12-05', 'available', 'good', 'Admin Office', 'Main office printer'),
      ('Router 501', 'network', 'SN90123456', 'RT-AX88U', 'Asus', '2022-01-20', '2025-01-20', 'available', 'excellent', 'Server Room', 'Main router')
    `);
    
    console.log('✅ Sample data added to it_inventory table');
    
  } catch (error) {
    console.error('❌ Error creating it_inventory table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createITInventoryTable();
