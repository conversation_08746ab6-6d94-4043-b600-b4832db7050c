const mysql = require('mysql2/promise');

async function createNotificationsTable() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking for notifications table...');

        // Check if notifications table exists
        const [tables] = await connection.query(`
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'notifications'
        `);

        if (tables.length === 0) {
            console.log('Creating notifications table...');
            
            // Create the notifications table
            await connection.query(`
                CREATE TABLE notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    message TEXT NOT NULL,
                    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                    \`read\` BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX (user_id),
                    INDEX (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
            
            console.log('Notifications table created successfully!');
            
            // Add some sample notifications for testing
            console.log('Adding sample notifications...');
            
            // Get all user IDs
            const [users] = await connection.query('SELECT id FROM users');
            
            if (users.length > 0) {
                // Sample notification messages
                const sampleNotifications = [
                    { message: 'Welcome to Meritorious EP! Start by taking a test.', type: 'info' },
                    { message: 'Your profile has been created successfully.', type: 'success' },
                    { message: 'New test available: Mathematics Fundamentals', type: 'info' },
                    { message: 'Your last test score: 85%. Great job!', type: 'success' },
                    { message: 'Remember to complete your profile information.', type: 'warning' }
                ];
                
                // Add notifications for each user
                for (const user of users) {
                    for (const notification of sampleNotifications) {
                        await connection.query(
                            'INSERT INTO notifications (user_id, message, type, created_at) VALUES (?, ?, ?, DATE_SUB(NOW(), INTERVAL RAND()*30 DAY))',
                            [user.id, notification.message, notification.type]
                        );
                    }
                }
                
                console.log('Sample notifications added successfully!');
            }
        } else {
            console.log('Notifications table already exists.');
        }

        console.log('Database setup completed!');

    } catch (error) {
        console.error('Error setting up database:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

createNotificationsTable(); 