/**
 * <PERSON><PERSON><PERSON> to create performance analysis tables
 * Run with: node scripts/create-performance-tables.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function createPerformanceTables() {
  let connection;
  
  try {
    console.log('Connecting to database...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'meritorious'
    });
    
    console.log('Creating user_performance table if it doesn\'t exist...');
    
    // Create user_performance table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        exam_id INT NOT NULL,
        attempt_id INT NOT NULL,
        section_id INT NOT NULL,
        score_percentage DECIMAL(5,2) DEFAULT 0,
        performance_category ENUM('weak', 'medium', 'strong') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
        FOREIGN KEY (section_id) REFERENCES sections(section_id) ON DELETE CASCADE,
        FOREIGN KEY (attempt_id) REFERENCES exam_attempts(attempt_id) ON DELETE CASCADE
      )
    `);
    
    console.log('Creating attempt_statistics table if it doesn\'t exist...');
    
    // Create attempt_statistics table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS attempt_statistics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        exam_id INT NOT NULL,
        attempt_id INT NOT NULL,
        attempt_number INT DEFAULT 1,
        duration_seconds INT DEFAULT 0,
        duration_formatted VARCHAR(50) DEFAULT '00:00:00',
        total_questions INT DEFAULT 0,
        correct_answers INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
        FOREIGN KEY (attempt_id) REFERENCES exam_attempts(attempt_id) ON DELETE CASCADE
      )
    `);
    
    console.log('Performance tables created successfully!');
    
  } catch (error) {
    console.error('Error creating performance tables:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed.');
    }
  }
}

createPerformanceTables(); 