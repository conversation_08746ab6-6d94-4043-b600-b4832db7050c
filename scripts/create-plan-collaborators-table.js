/**
 * <PERSON><PERSON><PERSON> to create the plan_collaborators table
 * This table is used to track collaborators for instruction plans
 */

const db = require('../config/database');

async function createPlanCollaboratorsTable() {
  try {
    console.log('Checking if plan_collaborators table exists...');

    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME
      FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'plan_collaborators'
    `, [process.env.DB_NAME]);

    if (tables.length > 0) {
      console.log('✅ plan_collaborators table already exists');
      return;
    }

    console.log('Creating plan_collaborators table...');

    // Create the table
    await db.query(`
      CREATE TABLE plan_collaborators (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_id INT NOT NULL,
        user_id INT NOT NULL,
        role ENUM('owner', 'editor', 'viewer') NOT NULL DEFAULT 'viewer',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_plan_user (plan_id, user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ plan_collaborators table created successfully');

    // Add some sample data if instruction_plans table has data
    const [plans] = await db.query(`
      SELECT id FROM instruction_plans LIMIT 5
    `);

    if (plans.length > 0) {
      console.log('Adding sample data to plan_collaborators table...');

      // Get some teacher IDs
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' LIMIT 3
      `);

      if (teachers.length > 0) {
        // Add sample collaborators
        for (const plan of plans) {
          // Make the first teacher the owner
          await db.query(`
            INSERT INTO plan_collaborators (plan_id, user_id, role)
            VALUES (?, ?, 'owner')
          `, [plan.id, teachers[0].id]);

          // Add other teachers as editors or viewers if available
          if (teachers.length > 1) {
            await db.query(`
              INSERT INTO plan_collaborators (plan_id, user_id, role)
              VALUES (?, ?, 'editor')
            `, [plan.id, teachers[1].id]);
          }

          if (teachers.length > 2) {
            await db.query(`
              INSERT INTO plan_collaborators (plan_id, user_id, role)
              VALUES (?, ?, 'viewer')
            `, [plan.id, teachers[2].id]);
          }
        }

        console.log('✅ Sample data added to plan_collaborators table');
      } else {
        console.log('⚠️ No teachers found to add as sample collaborators');
      }
    } else {
      console.log('⚠️ No instruction plans found to add sample collaborators');
    }

  } catch (error) {
    console.error('❌ Error creating plan_collaborators table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createPlanCollaboratorsTable();
