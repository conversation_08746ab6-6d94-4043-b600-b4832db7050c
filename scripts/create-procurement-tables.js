const db = require('../config/database');
const fs = require('fs');
const path = require('path');

async function createProcurementTables() {
    try {
        console.log('Creating procurement tables...');

        // Create procurement_requests table
        console.log('Creating procurement_requests table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS procurement_requests (
                request_id INT NOT NULL AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                description TEXT NULL,
                request_date DATE NOT NULL,
                requested_by INT NOT NULL,
                department VARCHAR(100) NULL,
                budget_code VARCHAR(50) NULL,
                total_amount DECIMAL(12,2) NULL,
                status ENUM('draft', 'in_progress', 'approved', 'rejected', 'completed') DEFAULT 'draft',
                current_step INT DEFAULT 1,
                hq_letter_ref VARCHAR(100) NULL,
                hq_letter_date DATE NULL,
                meeting_date DATE NULL,
                bill_number VARCHAR(100) NULL,
                bill_date DATE NULL,
                payment_mode VARCHAR(50) NULL,
                payment_date DATE NULL,
                payment_status ENUM('pending', 'partial', 'complete') DEFAULT 'pending',
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (request_id),
                FOREIGN KEY (requested_by) REFERENCES users (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        // Create procurement_items table
        console.log('Creating procurement_items table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS procurement_items (
                item_id INT NOT NULL AUTO_INCREMENT,
                request_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                quantity INT NOT NULL DEFAULT 1,
                estimated_unit_price DECIMAL(10,2) NULL,
                selected_vendor_id INT NULL,
                final_unit_price DECIMAL(10,2) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (item_id),
                FOREIGN KEY (request_id) REFERENCES procurement_requests (request_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        // Create procurement_vendors table
        console.log('Creating procurement_vendors table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS procurement_vendors (
                vendor_id INT NOT NULL AUTO_INCREMENT,
                request_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                contact_person VARCHAR(100) NULL,
                phone VARCHAR(20) NULL,
                email VARCHAR(100) NULL,
                address TEXT NULL,
                gst_number VARCHAR(20) NULL,
                quotation_date DATE NULL,
                quotation_reference VARCHAR(100) NULL,
                total_amount DECIMAL(12,2) NULL,
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (vendor_id),
                FOREIGN KEY (request_id) REFERENCES procurement_requests (request_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        // Create vendor_quotation_items table
        console.log('Creating vendor_quotation_items table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS vendor_quotation_items (
                id INT NOT NULL AUTO_INCREMENT,
                vendor_id INT NOT NULL,
                item_id INT NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                gst_percentage DECIMAL(5,2) DEFAULT 0,
                gst_amount DECIMAL(10,2) DEFAULT 0,
                total_price DECIMAL(10,2) NOT NULL,
                is_lowest BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                FOREIGN KEY (vendor_id) REFERENCES procurement_vendors (vendor_id) ON DELETE CASCADE,
                FOREIGN KEY (item_id) REFERENCES procurement_items (item_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        // Create procurement_committee_members table
        console.log('Creating procurement_committee_members table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS procurement_committee_members (
                id INT NOT NULL AUTO_INCREMENT,
                request_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                designation VARCHAR(100) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                FOREIGN KEY (request_id) REFERENCES procurement_requests (request_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        // Create procurement_documents table
        console.log('Creating procurement_documents table...');
        await db.query(`
            CREATE TABLE IF NOT EXISTS procurement_documents (
                document_id INT NOT NULL AUTO_INCREMENT,
                request_id INT NOT NULL,
                document_type ENUM('request_letter', 'quotation', 'comparative_statement', 'committee_proceedings', 'bill', 'payment_proof') NOT NULL,
                vendor_id INT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                file_size INT NOT NULL,
                uploaded_by INT NOT NULL,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (document_id),
                FOREIGN KEY (request_id) REFERENCES procurement_requests (request_id) ON DELETE CASCADE,
                FOREIGN KEY (vendor_id) REFERENCES procurement_vendors (vendor_id) ON DELETE SET NULL,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Procurement tables created successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error creating procurement tables:', error);
        process.exit(1);
    }
}

createProcurementTables();
