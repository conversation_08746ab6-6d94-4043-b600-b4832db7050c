/**
 * <PERSON><PERSON>t to create repair vendor tables
 */
const db = require('../config/database');
const fs = require('fs');
const path = require('path');

async function createRepairVendorTables() {
    try {
        console.log('Creating repair vendor tables...');
        
        // Read SQL file
        const sqlFilePath = path.join(__dirname, '../database/migrations/create_repair_vendor_tables.sql');
        const sql = fs.readFileSync(sqlFilePath, 'utf8');
        
        // Split SQL statements
        const statements = sql.split(';').filter(statement => statement.trim() !== '');
        
        // Execute each statement
        for (const statement of statements) {
            await db.query(statement);
            console.log('Executed SQL statement successfully');
        }
        
        console.log('Repair vendor tables created successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error creating repair vendor tables:', error);
        process.exit(1);
    }
}

createRepairVendorTables();
