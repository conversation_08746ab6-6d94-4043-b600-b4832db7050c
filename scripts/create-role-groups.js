/**
 * <PERSON><PERSON>t to create role-based groups for existing users
 */
const db = require('../config/database');
const groupController = require('../controllers/group-controller');

async function createRoleGroups() {
    try {
        console.log('Starting role group creation...');
        
        // Get all distinct roles from users
        const [roles] = await db.query('SELECT DISTINCT role FROM users WHERE role IS NOT NULL');
        
        if (roles.length === 0) {
            console.log('No roles found in the database.');
            return;
        }
        
        console.log(`Found ${roles.length} distinct roles: ${roles.map(r => r.role).join(', ')}`);
        
        // Create a group for each role
        for (const roleObj of roles) {
            const role = roleObj.role;
            console.log(`Creating/checking group for role: ${role}`);
            
            try {
                const groupId = await groupController.getOrCreateRoleGroup(role);
                console.log(`Group for role ${role} exists or was created with ID: ${groupId}`);
                
                // Get all users with this role
                const [users] = await db.query('SELECT id, username FROM users WHERE role = ?', [role]);
                console.log(`Found ${users.length} users with role ${role}`);
                
                // Add each user to the role group
                for (const user of users) {
                    try {
                        await groupController.addUserToRoleGroup(user.id, role);
                        console.log(`Added user ${user.username} (ID: ${user.id}) to ${role} group`);
                    } catch (error) {
                        console.error(`Error adding user ${user.id} to ${role} group:`, error);
                    }
                }
            } catch (error) {
                console.error(`Error processing role ${role}:`, error);
            }
        }
        
        console.log('Role group creation completed.');
    } catch (error) {
        console.error('Error creating role groups:', error);
    } finally {
        process.exit(0);
    }
}

// Run the script
createRoleGroups();
