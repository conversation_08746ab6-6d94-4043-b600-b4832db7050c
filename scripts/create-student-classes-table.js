/**
 * <PERSON><PERSON><PERSON> to create the student_classes table
 * This table is used to track which classes students are assigned to
 */

const db = require('../config/database');

async function createStudentClassesTable() {
  try {
    console.log('Checking if student_classes table exists...');
    
    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'student_classes'
    `, [process.env.DB_NAME]);
    
    if (tables.length > 0) {
      console.log('✅ student_classes table already exists');
      return;
    }
    
    console.log('Creating student_classes table...');
    
    // Create the table
    await db.query(`
      CREATE TABLE student_classes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        class_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON>OREIG<PERSON> KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        UNIQUE KEY unique_student_class (student_id, class_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ student_classes table created successfully');
    
    // Add some sample data if users and classes tables have data
    const [students] = await db.query(`
      SELECT id FROM users WHERE role = 'student' LIMIT 5
    `);
    
    const [classes] = await db.query(`
      SELECT id FROM classes LIMIT 3
    `);
    
    if (students.length > 0 && classes.length > 0) {
      console.log('Adding sample data to student_classes table...');
      
      // Add sample assignments
      for (const student of students) {
        // Assign each student to 1-2 random classes
        const numClasses = Math.floor(Math.random() * 2) + 1;
        const assignedClasses = [];
        
        for (let i = 0; i < numClasses; i++) {
          // Pick a random class that hasn't been assigned yet
          let classIndex;
          do {
            classIndex = Math.floor(Math.random() * classes.length);
          } while (assignedClasses.includes(classIndex));
          
          assignedClasses.push(classIndex);
          
          await db.query(`
            INSERT INTO student_classes (student_id, class_id)
            VALUES (?, ?)
          `, [student.id, classes[classIndex].id]);
        }
      }
      
      console.log('✅ Sample data added to student_classes table');
    } else {
      console.log('⚠️ No students or classes found to add sample data');
    }
    
  } catch (error) {
    console.error('❌ Error creating student_classes table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createStudentClassesTable();
