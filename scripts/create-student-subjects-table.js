/**
 * <PERSON><PERSON><PERSON> to create the student_subjects table
 * This table is used to track which subjects students are assigned to
 */

const db = require('../config/database');

async function createStudentSubjectsTable() {
  try {
    console.log('Checking if student_subjects table exists...');
    
    // Check if table exists
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'student_subjects'
    `, [process.env.DB_NAME]);
    
    if (tables.length > 0) {
      console.log('✅ student_subjects table already exists');
      return;
    }
    
    console.log('Creating student_subjects table...');
    
    // Create the table
    await db.query(`
      CREATE TABLE student_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        subject_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON><PERSON>EI<PERSON><PERSON> KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_student_subject (student_id, subject_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ student_subjects table created successfully');
    
    // Add some sample data if users and subjects tables have data
    const [students] = await db.query(`
      SELECT id FROM users WHERE role = 'student' LIMIT 5
    `);
    
    const [subjects] = await db.query(`
      SELECT id FROM subjects LIMIT 5
    `);
    
    if (students.length > 0 && subjects.length > 0) {
      console.log('Adding sample data to student_subjects table...');
      
      // Add sample assignments
      for (const student of students) {
        // Assign each student to 3-5 random subjects
        const numSubjects = Math.floor(Math.random() * 3) + 3;
        const assignedSubjects = [];
        
        for (let i = 0; i < numSubjects; i++) {
          // Pick a random subject that hasn't been assigned yet
          let subjectIndex;
          do {
            subjectIndex = Math.floor(Math.random() * subjects.length);
          } while (assignedSubjects.includes(subjectIndex));
          
          assignedSubjects.push(subjectIndex);
          
          await db.query(`
            INSERT INTO student_subjects (student_id, subject_id)
            VALUES (?, ?)
          `, [student.id, subjects[subjectIndex].id]);
        }
      }
      
      console.log('✅ Sample data added to student_subjects table');
    } else {
      console.log('⚠️ No students or subjects found to add sample data');
    }
    
  } catch (error) {
    console.error('❌ Error creating student_subjects table:', error);
    throw error;
  } finally {
    process.exit(0);
  }
}

// Run the function
createStudentSubjectsTable();
