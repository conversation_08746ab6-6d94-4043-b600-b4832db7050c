const fs = require('fs');
const path = require('path');
const db = require('../config/database');

async function createTestAssignmentsTable() {
    try {
        console.log('Creating test_assignments table...');
        
        // Read the SQL file
        const sqlPath = path.join(__dirname, '../database/migrations/create_test_assignments_table.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        // Execute the SQL
        await db.query(sql);
        
        console.log('✅ test_assignments table created successfully');
    } catch (error) {
        console.error('❌ Error creating test_assignments table:', error);
    } finally {
        process.exit();
    }
}

createTestAssignmentsTable();
