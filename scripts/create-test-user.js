const bcrypt = require('bcrypt');
const db = require('../config/database');

async function createTestUser() {
    try {
        // Check if test user already exists
        const [existingUsers] = await db.query(
            'SELECT id FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (existingUsers.length > 0) {
            console.log('Test user already exists');
            process.exit(0);
        }

        // Hash password
        const hashedPassword = await bcrypt.hash('Test@123', 10);

        // Insert test user
        const [result] = await db.query(
            'INSERT INTO users (username, name, email, password, role, date_of_birth, created_at, last_login, bio) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)',
            ['testuser', 'Test User', '<EMAIL>', hashedPassword, 'student', '2000-01-01', 'Test user account']
        );

        console.log('Test user created successfully');
        console.log('Username: testuser');
        console.log('Email: <EMAIL>');
        console.log('Password: Test@123');
        console.log('Role: student');
        process.exit(0);
    } catch (error) {
        console.error('Error creating test user:', error);
        process.exit(1);
    }
}

createTestUser();
