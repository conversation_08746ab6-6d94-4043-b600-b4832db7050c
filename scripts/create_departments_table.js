/**
 * <PERSON><PERSON><PERSON> to create departments table and populate it
 */

const db = require('../config/database');

async function createDepartmentsTable() {
    try {
        console.log('Creating departments table...');
        
        // Create the table if it doesn't exist
        await db.query(`
            CREATE TABLE IF NOT EXISTS departments (
                department_id INT NOT NULL AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT NULL,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (department_id),
                UNIQUE KEY unique_department_name (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
        
        console.log('Departments table created successfully!');
        
        // Check if departments already exist
        const [existingDepartments] = await db.query('SELECT COUNT(*) as count FROM departments');
        
        if (existingDepartments[0].count > 0) {
            console.log(`${existingDepartments[0].count} departments already exist in the table.`);
        } else {
            console.log('Populating departments table...');
            
            // Insert departments
            const departments = [
                { name: 'PHYSICS', description: 'Physics Department' },
                { name: 'CHEMISTRY', description: 'Chemistry Department' },
                { name: 'BIOLOGY', description: 'Biology Department' },
                { name: 'MATHEMATICS', description: 'Mathematics Department' },
                { name: 'ENGLISH', description: 'English Department' },
                { name: 'PUNJABI', description: 'Punjabi Department' },
                { name: 'COMPUTER SCIENCE', description: 'Computer Science Department' }
            ];
            
            for (const dept of departments) {
                await db.query(
                    'INSERT INTO departments (name, description, is_active) VALUES (?, ?, 1)',
                    [dept.name, dept.description]
                );
            }
            
            console.log('Departments added successfully!');
        }
        
        // Display all departments
        const [allDepartments] = await db.query('SELECT * FROM departments');
        console.log('\nCurrent departments in the database:');
        console.log('-----------------------------------');
        allDepartments.forEach(dept => {
            console.log(`ID: ${dept.department_id}, Name: ${dept.name}`);
        });
        console.log('-----------------------------------');
        
    } catch (error) {
        console.error('Error creating departments table:', error);
    }
}

// Run the function
createDepartmentsTable();
