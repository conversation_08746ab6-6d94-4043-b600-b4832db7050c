/**
 * <PERSON><PERSON>t to fix chat tables
 */

const db = require('../config/database');
const fs = require('fs').promises;
const path = require('path');

async function runMigration() {
    try {
        console.log('Fixing chat tables...');
        
        // Read the migration file
        const migrationFile = path.join(__dirname, '../database/migrations/fix_chat_tables.sql');
        const migrationSql = await fs.readFile(migrationFile, 'utf8');
        
        // Split the SQL statements
        const statements = migrationSql.split(';').filter(stmt => stmt.trim() !== '');
        
        // Execute each statement
        for (const statement of statements) {
            await db.query(statement);
            console.log('Executed SQL:', statement.trim().substring(0, 50) + '...');
        }
        
        console.log('Migration completed successfully');
        
    } catch (error) {
        console.error('Error running migration:', error);
    }
}

// Run the migration
runMigration()
    .then(() => {
        console.log('<PERSON>ript completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('<PERSON><PERSON><PERSON> failed:', err);
        process.exit(1);
    });
