/**
 * <PERSON><PERSON><PERSON> to fix computer teacher assignments according to the new rules:
 * 1. Computer teachers can only teach computer-related subjects
 * 2. Computer teachers cannot be class incharge, only lab incharge
 * 3. All inchargeships are session-wise
 */

const db = require('../config/database');

async function fixComputerTeacherAssignments() {
  try {
    console.log('Starting to fix computer teacher assignments...');

    // Find computer science teachers
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_subjects ts ON u.id = ts.teacher_id
      JOIN subjects s ON ts.subject_id = s.id
      WHERE u.role = 'teacher'
      AND (
        s.name LIKE '%computer%'
        OR s.name LIKE '%Computer%'
        OR s.code LIKE '%CS%'
        OR s.code LIKE '%comp%'
      )
    `);

    if (computerTeachers.length === 0) {
      console.log('No computer teachers found');
      return;
    }

    console.log(`Found ${computerTeachers.length} computer teachers, fixing their assignments...`);

    // Get all computer science subjects
    const [computerSubjects] = await db.query(`
      SELECT id, name, code
      FROM subjects
      WHERE name LIKE '%computer%'
      OR name LIKE '%Computer%'
      OR code LIKE '%CS%'
      OR code LIKE '%comp%'
    `);

    if (computerSubjects.length === 0) {
      console.log('No computer subjects found');
      return;
    }

    console.log(`Found ${computerSubjects.length} computer subjects`);

    // For each computer teacher
    for (const teacher of computerTeachers) {
      console.log(`\nFixing assignments for teacher ${teacher.full_name} (ID: ${teacher.id})...`);

      // 1. Remove non-computer subjects from teacher_subjects
      const [nonComputerSubjects] = await db.query(`
        SELECT ts.id, s.name, s.code
        FROM teacher_subjects ts
        JOIN subjects s ON ts.subject_id = s.id
        WHERE ts.teacher_id = ?
        AND s.id NOT IN (${computerSubjects.map(s => s.id).join(',')})
      `, [teacher.id]);

      if (nonComputerSubjects.length > 0) {
        console.log(`Found ${nonComputerSubjects.length} non-computer subjects assigned to this teacher, removing them...`);

        for (const subject of nonComputerSubjects) {
          await db.query(`
            DELETE FROM teacher_subjects
            WHERE id = ?
          `, [subject.id]);

          console.log(`Removed subject ${subject.name} (ID: ${subject.id}) from teacher ${teacher.full_name}`);
        }
      } else {
        console.log('No non-computer subjects found for this teacher');
      }

      // 2. Check if teacher is a class incharge and remove if so
      const [classInchargeExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'class_incharge'
      `);

      if (classInchargeExists[0].table_exists > 0) {
        const [classInchargeAssignments] = await db.query(`
          SELECT ci.id, c.name, c.grade, c.trade, c.section
          FROM class_incharge ci
          JOIN classes c ON ci.class_id = c.id
          WHERE ci.teacher_id = ?
        `, [teacher.id]);

        if (classInchargeAssignments.length > 0) {
          console.log(`Found ${classInchargeAssignments.length} class incharge assignments for this teacher, removing them...`);

          // Get non-computer teachers to reassign as class incharge
          const [nonComputerTeachers] = await db.query(`
            SELECT u.id, u.full_name
            FROM users u
            WHERE u.role = 'teacher'
            AND u.is_active = 1
            AND u.id NOT IN (${computerTeachers.map(t => t.id).join(',')})
            LIMIT ${classInchargeAssignments.length}
          `);

          if (nonComputerTeachers.length > 0) {
            for (let i = 0; i < classInchargeAssignments.length; i++) {
              const assignment = classInchargeAssignments[i];
              const newTeacher = nonComputerTeachers[i % nonComputerTeachers.length];

              // Update class incharge assignment
              await db.query(`
                UPDATE class_incharge
                SET teacher_id = ?
                WHERE id = ?
              `, [newTeacher.id, assignment.id]);

              console.log(`Reassigned class ${assignment.name} (${assignment.grade} ${assignment.trade} ${assignment.section}) from ${teacher.full_name} to ${newTeacher.full_name}`);
            }
          } else {
            // If no non-computer teachers found, just remove the assignments
            for (const assignment of classInchargeAssignments) {
              await db.query(`
                DELETE FROM class_incharge
                WHERE id = ?
              `, [assignment.id]);

              console.log(`Removed class incharge assignment for class ${assignment.name} (${assignment.grade} ${assignment.trade} ${assignment.section})`);
            }
          }
        } else {
          console.log('No class incharge assignments found for this teacher');
        }
      }

      // 3. Ensure teacher is assigned as lab incharge for computer labs
      const [labInchargeExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'lab_incharge'
      `);

      if (labInchargeExists[0].table_exists > 0) {
        // Get computer labs
        const [computerLabs] = await db.query(`
          SELECT id, name, capacity
          FROM labs
          WHERE name LIKE '%computer%'
          OR name LIKE '%CS%'
          OR name LIKE '%comp%'
        `);

        if (computerLabs.length > 0) {
          console.log(`Found ${computerLabs.length} computer labs, checking if teacher is assigned as lab incharge...`);

          // Check if teacher is already a lab incharge
          const [labInchargeAssignments] = await db.query(`
            SELECT li.id, l.name
            FROM lab_incharge li
            JOIN labs l ON li.lab_id = l.id
            WHERE li.teacher_id = ?
            AND li.session = '2024-2025'
          `, [teacher.id]);

          if (labInchargeAssignments.length === 0) {
            // Find a computer lab without an incharge
            const [availableLabs] = await db.query(`
              SELECT l.id, l.name
              FROM labs l
              LEFT JOIN lab_incharge li ON l.id = li.lab_id AND li.session = '2024-2025'
              WHERE (l.name LIKE '%computer%' OR l.name LIKE '%CS%' OR l.name LIKE '%comp%')
              AND li.id IS NULL
              LIMIT 1
            `);

            if (availableLabs.length > 0) {
              // Assign teacher as lab incharge
              await db.query(`
                INSERT INTO lab_incharge (lab_id, teacher_id, session)
                VALUES (?, ?, '2024-2025')
              `, [availableLabs[0].id, teacher.id]);

              console.log(`Assigned teacher ${teacher.full_name} as incharge for lab ${availableLabs[0].name} for session 2024-2025`);
            } else {
              console.log('No available computer labs found for assignment');
            }
          } else {
            console.log(`Teacher is already assigned as incharge for ${labInchargeAssignments.length} labs`);
          }
        } else {
          console.log('No computer labs found');
        }
      }
    }

    console.log('\nComputer teacher assignments fixed successfully');
    return true;
  } catch (error) {
    console.error('Error fixing computer teacher assignments:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  fixComputerTeacherAssignments()
    .then(() => {
      console.log('Computer teacher assignments fix completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Computer teacher assignments fix failed:', err);
      process.exit(1);
    });
}

module.exports = fixComputerTeacherAssignments;
