/**
 * <PERSON><PERSON><PERSON> to fix the CS test data issues
 * This will address the problems identified in the check-cs-student-data.js script
 */

const db = require('../config/database');

async function fixTestData() {
  try {
    console.log('Starting to fix CS test data...');

    // Begin transaction
    await db.query('START TRANSACTION');

    // 1. Find the CS student
    const [students] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csstudent']
    );

    if (students.length === 0) {
      console.log('CS student not found!');
      return;
    }

    const studentId = students[0].id;
    console.log(`Found CS student with ID: ${studentId}`);

    // 2. Find the CS teacher
    const [teachers] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csteacher']
    );

    if (teachers.length === 0) {
      console.log('CS teacher not found!');
      return;
    }

    const teacherId = teachers[0].id;
    console.log(`Found CS teacher with ID: ${teacherId}`);

    // 3. Fix class assignments - remove Class 10 assignment
    console.log('Fixing class assignments...');
    await db.query(
      'DELETE FROM student_classes WHERE student_id = ? AND class_id = ?',
      [studentId, 2] // Class 10 ID
    );
    console.log('Removed Class 10 assignment');

    // 4. Fix duplicate assignments
    console.log('Fixing duplicate assignments...');

    // Get all assignments
    const [assignments] = await db.query(
      `SELECT id, title, subject_id, class_id, student_id
       FROM assignments
       WHERE (student_id = ? OR class_id IN (
         SELECT class_id FROM student_classes WHERE student_id = ?
       ))`,
      [studentId, studentId]
    );

    // Group assignments by title and subject
    const assignmentGroups = {};
    assignments.forEach(a => {
      const key = `${a.title}-${a.subject_id}`;
      if (!assignmentGroups[key]) {
        assignmentGroups[key] = [];
      }
      assignmentGroups[key].push(a);
    });

    // Delete duplicates
    for (const key in assignmentGroups) {
      const group = assignmentGroups[key];
      if (group.length > 1) {
        // Keep the first one, delete the rest
        for (let i = 1; i < group.length; i++) {
          await db.query(
            'DELETE FROM assignments WHERE id = ?',
            [group[i].id]
          );
          console.log(`Deleted duplicate assignment: ${group[i].id}`);
        }
      }
    }

    // 5. Add upcoming practical for the student
    console.log('Adding upcoming practical...');

    // Get class section ID
    const [classSections] = await db.query(
      `SELECT cs.id
       FROM class_sections cs
       JOIN student_classes sc ON cs.class_id = sc.class_id
       WHERE sc.student_id = ?`,
      [studentId]
    );

    if (classSections.length === 0) {
      console.log('No class sections found for student');
      return;
    }

    const classSectionId = classSections[0].id;

    // Add a practical for tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // Check the structure of the teacher_practicals table
    const [columns] = await db.query('SHOW COLUMNS FROM teacher_practicals');
    console.log('Teacher practicals table columns:');
    columns.forEach(col => console.log(`- ${col.Field} (${col.Type})`));

    const [practicalResult] = await db.query(
      `INSERT INTO teacher_practicals
       (teacher_id, date, class_name, class_section_id, start_time, end_time, subject_name, practical_topic, venue, status, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [teacherId, tomorrowStr, 'Class 12-A', classSectionId, '14:00:00', '16:00:00', 'Computer Science', 'Data Structures Implementation', 'Computer Lab 1', 'pending', 'Students should bring their laptops']
    );

    const practicalId = practicalResult.insertId;
    console.log(`Added upcoming practical with ID: ${practicalId}`);

    // 6. Create exam_assignments table if it doesn't exist
    console.log('Creating exam_assignments table if it doesn\'t exist...');

    await db.query(`
      CREATE TABLE IF NOT EXISTS exam_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        exam_id INT NOT NULL,
        student_id INT,
        group_id INT,
        start_datetime DATETIME NOT NULL,
        end_datetime DATETIME NOT NULL,
        max_attempts INT NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.log('Created exam_assignments table');

    // 7. Create a test and assign it to the student
    console.log('Creating a test for the student...');

    // First, check if we have a Computer Science test
    const [existingExams] = await db.query(
      `SELECT exam_id FROM exams WHERE exam_name LIKE ?`,
      ['%Computer Science%']
    );

    let examId;
    if (existingExams.length > 0) {
      examId = existingExams[0].exam_id;
      console.log(`Found existing Computer Science exam with ID: ${examId}`);
    } else {
      // Create a new exam
      const [examResult] = await db.query(`
        INSERT INTO exams (
          exam_name, description, duration, instructions, is_active,
          created_by, passing_marks, status, difficulty
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'Computer Science Test - Data Structures',
        'Test on basic data structures concepts',
        60,
        'Answer all questions. Each question carries equal marks.',
        1,
        teacherId,
        60.00,
        'published',
        'intermediate'
      ]);

      examId = examResult.insertId;
      console.log(`Created new Computer Science exam with ID: ${examId}`);

      // Create a section for the exam
      const [sectionResult] = await db.query(`
        INSERT INTO sections (
          exam_id, section_name, description, position
        ) VALUES (?, ?, ?, ?)
      `, [
        examId,
        'Data Structures',
        'Questions on data structures',
        1
      ]);

      const sectionId = sectionResult.insertId;
      console.log(`Created section with ID: ${sectionId}`);

      // Add some questions to the section
      const questions = [
        {
          text: 'Which data structure follows the Last In First Out (LIFO) principle?',
          type: 'multiple_choice',
          marks: 5,
          options: ['Queue', 'Stack', 'Linked List', 'Array'],
          correct: 'Stack'
        },
        {
          text: 'Which data structure follows the First In First Out (FIFO) principle?',
          type: 'multiple_choice',
          marks: 5,
          options: ['Queue', 'Stack', 'Tree', 'Graph'],
          correct: 'Queue'
        },
        {
          text: 'What is the time complexity of searching an element in a binary search tree in the worst case?',
          type: 'multiple_choice',
          marks: 5,
          options: ['O(1)', 'O(log n)', 'O(n)', 'O(n²)'],
          correct: 'O(n)'
        }
      ];

      for (let i = 0; i < questions.length; i++) {
        const q = questions[i];

        // Add question
        const [questionResult] = await db.query(`
          INSERT INTO questions (
            section_id, question_text, question_type, marks, correct_answer, position, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          sectionId,
          q.text,
          q.type,
          q.marks,
          q.correct,
          i + 1,
          1
        ]);

        const questionId = questionResult.insertId;
        console.log(`Added question with ID: ${questionId}`);

        // Add options
        for (let j = 0; j < q.options.length; j++) {
          await db.query(`
            INSERT INTO options (
              question_id, option_text, is_correct, position
            ) VALUES (?, ?, ?, ?)
          `, [
            questionId,
            q.options[j],
            q.options[j] === q.correct ? 1 : 0,
            j + 1
          ]);
        }

        console.log(`Added options for question ${questionId}`);
      }
    }

    // Assign the exam to the student
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const nextWeekStr = nextWeek.toISOString().slice(0, 19).replace('T', ' ');

    const twoWeeksLater = new Date();
    twoWeeksLater.setDate(twoWeeksLater.getDate() + 14);
    const twoWeeksLaterStr = twoWeeksLater.toISOString().slice(0, 19).replace('T', ' ');

    await db.query(`
      INSERT INTO exam_assignments (
        exam_id, student_id, start_datetime, end_datetime, max_attempts
      ) VALUES (?, ?, ?, ?, ?)
    `, [
      examId,
      studentId,
      nextWeekStr,
      twoWeeksLaterStr,
      2
    ]);

    console.log(`Assigned exam ${examId} to student ${studentId}`);

    // Commit transaction
    await db.query('COMMIT');
    console.log('Successfully fixed all CS test data!');

  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');
    console.error('Error fixing test data:', error);
    throw error;
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
fixTestData();
