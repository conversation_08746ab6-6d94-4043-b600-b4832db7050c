const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function fixDatabase() {
    let connection;
    try {
        console.log('Connecting to database...');
        
        // Create connection without database
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: ''
        });
        
        // Check if database exists
        const [databases] = await connection.query('SHOW DATABASES LIKE "exam_prep_platform"');
        if (databases.length === 0) {
            console.log('Creating database...');
            await connection.query('CREATE DATABASE exam_prep_platform');
        }
        
        // Use the database
        await connection.query('USE exam_prep_platform');
        
        // Check if users table exists
        const [tables] = await connection.query('SHOW TABLES LIKE "users"');
        
        if (tables.length === 0) {
            console.log('Creating users table...');
            
            // Create users table
            await connection.query(`
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(255) NOT NULL,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    date_of_birth DATE NOT NULL,
                    role VARCHAR(50) DEFAULT 'user',
                    profile_image VARCHAR(255),
                    reset_token VARCHAR(255),
                    reset_token_expires DATETIME,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX email_index (email),
                    INDEX username_dob_index (username, date_of_birth)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            
            console.log('✓ Users table created');
        } else {
            console.log('Users table exists, checking structure...');
            
            // Get current columns
            const [columns] = await connection.query('DESCRIBE users');
            const columnNames = columns.map(col => col.Field);
            
            // Check for required columns
            if (!columnNames.includes('password')) {
                console.log('Adding password column...');
                await connection.query('ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL');
            }
            
            if (!columnNames.includes('date_of_birth')) {
                console.log('Adding date_of_birth column...');
                await connection.query('ALTER TABLE users ADD COLUMN date_of_birth DATE NOT NULL DEFAULT "2000-01-01"');
            }
            
            if (!columnNames.includes('role')) {
                console.log('Adding role column...');
                await connection.query('ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT "user"');
            }
            
            if (!columnNames.includes('reset_token')) {
                console.log('Adding reset_token column...');
                await connection.query('ALTER TABLE users ADD COLUMN reset_token VARCHAR(255)');
            }
            
            if (!columnNames.includes('reset_token_expires')) {
                console.log('Adding reset_token_expires column...');
                await connection.query('ALTER TABLE users ADD COLUMN reset_token_expires DATETIME');
            }
            
            console.log('✓ Users table structure updated');
        }
        
        // Check if sessions table exists
        const [sessionTables] = await connection.query('SHOW TABLES LIKE "sessions"');
        
        if (sessionTables.length === 0) {
            console.log('Creating sessions table...');
            
            // Create sessions table
            await connection.query(`
                CREATE TABLE sessions (
                    session_id VARCHAR(128) NOT NULL PRIMARY KEY,
                    expires BIGINT,
                    data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            
            console.log('✓ Sessions table created');
        }
        
        // Check if we have test users
        const [userCount] = await connection.query('SELECT COUNT(*) as count FROM users');
        
        if (userCount[0].count === 0) {
            console.log('Adding test users...');
            
            // Create admin user
            const adminPassword = await bcrypt.hash('admin123', 10);
            await connection.query(
                'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
                ['Admin User', '<EMAIL>', adminPassword, '1990-01-01', 'admin']
            );
            
            // Create test user
            const userPassword = await bcrypt.hash('user123', 10);
            await connection.query(
                'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
                ['Test User', '<EMAIL>', userPassword, '1995-05-15', 'user']
            );
            
            // Create CSI user
            const csiPassword = await bcrypt.hash('password123', 10);
            await connection.query(
                'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
                ['CSI User', '<EMAIL>', csiPassword, '2000-12-25', 'user']
            );
            
            console.log('✓ Test users created');
        }
        
        console.log('\nDatabase fix complete!');
        console.log('\nTest Accounts:');
        console.log('Admin - Email: <EMAIL>, Password: admin123, DOB: 1990-01-01');
        console.log('User  - Email: <EMAIL>, Password: user123, DOB: 1995-05-15');
        console.log('CSI   - Email: <EMAIL>, Password: password123, DOB: 2000-12-25');
        
    } catch (error) {
        console.error('Error fixing database:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
        process.exit(0);
    }
}

fixDatabase(); 