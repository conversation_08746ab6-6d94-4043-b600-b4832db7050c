/**
 * <PERSON><PERSON><PERSON> to identify and fix duplicate section names in the database
 * This will identify sections with identical names (case-insensitive, ignoring whitespace)
 * and update them to ensure uniqueness
 */
const mysql = require('mysql2/promise');

async function fixDuplicateSections() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking for duplicate section names...');

        // Get all sections
        const [sections] = await connection.query('SELECT section_id, section_name, exam_id FROM sections ORDER BY section_name');
        
        // Group sections by normalized name
        const sectionsByName = new Map();
        
        // First pass: Group sections by normalized name
        sections.forEach(section => {
            if (!section.section_name) {
                return; // Skip null/undefined names
            }
            
            const normalizedName = section.section_name.toLowerCase().trim().replace(/\s+/g, ' ');
            
            if (normalizedName === '') {
                return; // Skip empty names
            }
            
            if (!sectionsByName.has(normalizedName)) {
                sectionsByName.set(normalizedName, []);
            }
            
            sectionsByName.get(normalizedName).push(section);
        });
        
        // Second pass: Fix duplicates
        let duplicatesFixed = 0;
        let emptyNamesFixed = 0;
        
        // Fix empty section names
        const [emptyNameRows] = await connection.query(`
            SELECT section_id, exam_id FROM sections 
            WHERE section_name IS NULL OR section_name = '' OR TRIM(section_name) = ''
        `);
        
        if (emptyNameRows.length > 0) {
            console.log(`Found ${emptyNameRows.length} sections with empty names.`);
            
            for (const section of emptyNameRows) {
                let defaultName = 'Unnamed Section';
                
                // If the section has an exam, include exam ID in the name
                if (section.exam_id) {
                    defaultName += ` (Exam ${section.exam_id})`;
                }
                
                // Make sure the name is unique
                let counter = 1;
                let finalName = defaultName;
                
                while (sectionsByName.has(finalName.toLowerCase())) {
                    finalName = `${defaultName} ${counter}`;
                    counter++;
                }
                
                await connection.query(
                    'UPDATE sections SET section_name = ? WHERE section_id = ?',
                    [finalName, section.section_id]
                );
                
                console.log(`Fixed empty section name: section_id=${section.section_id}, new name="${finalName}"`);
                emptyNamesFixed++;
            }
        }
        
        // Fix duplicate names
        for (const [normalizedName, sectionsGroup] of sectionsByName.entries()) {
            if (sectionsGroup.length > 1) {
                console.log(`Found ${sectionsGroup.length} duplicate sections with name "${normalizedName}"`);
                
                // Keep the first one as is, rename the rest
                for (let i = 1; i < sectionsGroup.length; i++) {
                    const section = sectionsGroup[i];
                    const originalName = section.section_name;
                    let newName;
                    
                    // If the section has an exam, use exam_id for suffix
                    if (section.exam_id) {
                        newName = `${originalName} (Exam ${section.exam_id})`;
                    } else {
                        newName = `${originalName} (${i})`;
                    }
                    
                    await connection.query(
                        'UPDATE sections SET section_name = ? WHERE section_id = ?',
                        [newName, section.section_id]
                    );
                    
                    console.log(`Renamed section: section_id=${section.section_id}, "${originalName}" -> "${newName}"`);
                    duplicatesFixed++;
                }
            }
        }
        
        console.log(`Fixed ${emptyNamesFixed} empty section names.`);
        console.log(`Fixed ${duplicatesFixed} duplicate section names.`);
        console.log('Section name deduplication complete.');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

fixDuplicateSections(); 