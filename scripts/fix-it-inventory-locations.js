/**
 * <PERSON><PERSON><PERSON> to fix IT inventory location data to properly match room numbers
 * 
 * This script:
 * 1. Analyzes current location mismatches
 * 2. Applies intelligent fixes to location data
 * 3. Updates IT inventory with standardized room locations
 * 4. Creates missing rooms if needed
 * 5. Provides detailed reporting of changes made
 */

const db = require('../config/database');

async function fixITInventoryLocations() {
    console.log('🔧 Fixing IT Inventory Location Data...\n');

    try {
        // 1. Get all rooms
        console.log('1. Fetching existing rooms...');
        const [rooms] = await db.query(`
            SELECT id, room_number, capacity, building, floor
            FROM rooms
            ORDER BY room_number
        `);
        console.log(`   ✅ Found ${rooms.length} rooms`);

        // Create room lookup maps
        const roomByNumber = new Map();
        const roomByNumericId = new Map();
        
        rooms.forEach(room => {
            roomByNumber.set(room.room_number, room);
            const numericId = room.room_number.match(/(\d+)/)?.[1];
            if (numericId) {
                roomByNumericId.set(numericId, room);
            }
        });

        // 2. Get all IT inventory items
        console.log('\n2. Fetching IT inventory items...');
        const [itItems] = await db.query(`
            SELECT id, name, type, serial_number, location, status, manufacturer, model
            FROM it_inventory
            WHERE location IS NOT NULL AND location != ''
            ORDER BY location, name
        `);
        console.log(`   ✅ Found ${itItems.length} items with location data`);

        // 3. Analyze and prepare fixes
        console.log('\n3. Analyzing location data and preparing fixes...');
        
        const fixes = [];
        const createRooms = [];
        let alreadyCorrect = 0;
        
        for (const item of itItems) {
            const currentLocation = item.location.trim();
            let suggestedLocation = null;
            let reason = '';
            
            // Check if already correctly formatted
            if (roomByNumber.has(currentLocation)) {
                alreadyCorrect++;
                continue;
            }
            
            // Pattern matching for fixes
            const patterns = [
                // Extract room number patterns
                { regex: /Room\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                { regex: /Classroom\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                { regex: /Class\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
                { regex: /R(\d+)/i, format: (match) => `Room ${match[1]}` },
                { regex: /^(\d+)$/, format: (match) => `Room ${match[1]}` },
                { regex: /Lab\s*(\d+)/i, format: (match) => `Room ${match[1]}` },
            ];
            
            // Try pattern matching
            for (const pattern of patterns) {
                const match = currentLocation.match(pattern.regex);
                if (match) {
                    const proposedRoom = pattern.format(match);
                    if (roomByNumber.has(proposedRoom)) {
                        suggestedLocation = proposedRoom;
                        reason = `Pattern match: ${pattern.regex.source}`;
                        break;
                    } else {
                        // Room doesn't exist, might need to create it
                        const roomNum = match[1];
                        if (roomNum && parseInt(roomNum) >= 1 && parseInt(roomNum) <= 25) {
                            createRooms.push({
                                room_number: proposedRoom,
                                capacity: 40,
                                building: 'Main Building',
                                floor: Math.ceil(parseInt(roomNum) / 5)
                            });
                            suggestedLocation = proposedRoom;
                            reason = `Pattern match with room creation: ${pattern.regex.source}`;
                            break;
                        }
                    }
                }
            }
            
            // Special location mappings
            if (!suggestedLocation) {
                const specialMappings = {
                    'computer lab': 'Room 1',
                    'computer lab 1': 'Room 1',
                    'computer lab 2': 'Room 2',
                    'it lab': 'Room 1',
                    'science lab': 'Room 3',
                    'physics lab': 'Room 4',
                    'chemistry lab': 'Room 5',
                    'biology lab': 'Room 6',
                    'library': 'Room 7',
                    'admin office': 'Room 20',
                    'principal office': 'Room 19',
                    'staff room': 'Room 18',
                    'storage': 'Room 17',
                    'it storage': 'Room 17',
                    'server room': 'Room 16',
                    'maintenance': 'Room 15'
                };
                
                const lowerLocation = currentLocation.toLowerCase();
                for (const [key, value] of Object.entries(specialMappings)) {
                    if (lowerLocation.includes(key)) {
                        suggestedLocation = value;
                        reason = `Special mapping: ${key} → ${value}`;
                        break;
                    }
                }
            }
            
            // If still no match, assign to general storage
            if (!suggestedLocation) {
                suggestedLocation = 'Room 17'; // Storage room
                reason = 'Default assignment to storage room';
            }
            
            fixes.push({
                item: item,
                currentLocation: currentLocation,
                suggestedLocation: suggestedLocation,
                reason: reason
            });
        }

        console.log(`   📊 Analysis Results:`);
        console.log(`      ✅ Already correct: ${alreadyCorrect} items`);
        console.log(`      🔧 Need fixing: ${fixes.length} items`);
        console.log(`      🏗️  Rooms to create: ${createRooms.length}`);

        // 4. Create missing rooms
        if (createRooms.length > 0) {
            console.log('\n4. Creating missing rooms...');
            
            // Remove duplicates
            const uniqueRooms = createRooms.filter((room, index, self) => 
                index === self.findIndex(r => r.room_number === room.room_number)
            );
            
            for (const room of uniqueRooms) {
                try {
                    await db.query(`
                        INSERT INTO rooms (room_number, capacity, building, floor, created_at, updated_at)
                        VALUES (?, ?, ?, ?, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE updated_at = NOW()
                    `, [room.room_number, room.capacity, room.building, room.floor]);
                    
                    console.log(`   ✅ Created/Updated room: ${room.room_number}`);
                } catch (error) {
                    console.log(`   ❌ Failed to create room ${room.room_number}: ${error.message}`);
                }
            }
        }

        // 5. Apply fixes
        console.log('\n5. Applying location fixes...');
        
        let successCount = 0;
        let errorCount = 0;
        
        for (const fix of fixes) {
            try {
                await db.query(`
                    UPDATE it_inventory 
                    SET location = ?, updated_at = NOW()
                    WHERE id = ?
                `, [fix.suggestedLocation, fix.item.id]);
                
                console.log(`   ✅ ${fix.item.name}: "${fix.currentLocation}" → "${fix.suggestedLocation}"`);
                console.log(`      Reason: ${fix.reason}`);
                successCount++;
                
            } catch (error) {
                console.log(`   ❌ Failed to update ${fix.item.name}: ${error.message}`);
                errorCount++;
            }
        }

        // 6. Verify results
        console.log('\n6. Verifying results...');
        
        const [updatedItems] = await db.query(`
            SELECT 
                i.location,
                COUNT(*) as item_count,
                GROUP_CONCAT(DISTINCT i.type) as equipment_types
            FROM it_inventory i
            WHERE i.location IS NOT NULL AND i.location != ''
            GROUP BY i.location
            ORDER BY i.location
        `);
        
        console.log(`   📊 Updated location distribution:`);
        updatedItems.forEach(item => {
            console.log(`      • ${item.location}: ${item.item_count} items (${item.equipment_types})`);
        });

        // 7. Final summary
        console.log('\n7. Summary:');
        console.log(`   📊 Total items processed: ${itItems.length}`);
        console.log(`   ✅ Already correct: ${alreadyCorrect}`);
        console.log(`   🔧 Successfully fixed: ${successCount}`);
        console.log(`   ❌ Failed to fix: ${errorCount}`);
        console.log(`   🏗️  Rooms created: ${createRooms.length}`);
        console.log(`   📈 Success rate: ${((alreadyCorrect + successCount) / itItems.length * 100).toFixed(1)}%`);

        return {
            totalItems: itItems.length,
            alreadyCorrect,
            successfulFixes: successCount,
            failedFixes: errorCount,
            roomsCreated: createRooms.length,
            successRate: ((alreadyCorrect + successCount) / itItems.length * 100).toFixed(1)
        };

    } catch (error) {
        console.error('❌ Error fixing IT inventory locations:', error);
        throw error;
    }
}

// Run the fix if this script is executed directly
if (require.main === module) {
    fixITInventoryLocations()
        .then((results) => {
            console.log('\n✅ Location fixing completed successfully!');
            console.log(`📈 Final Success Rate: ${results.successRate}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Location fixing failed:', error);
            process.exit(1);
        });
}

module.exports = { fixITInventoryLocations };
