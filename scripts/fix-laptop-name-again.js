/**
 * <PERSON><PERSON><PERSON> to fix the name of a specific laptop again
 * 
 * This script updates the name of the laptop with ID 21 to "Laptop CR-11"
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function main() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'exam_prep_platform'
    });
    
    console.log('Connected to database');
    
    // Get current laptop details
    const [items] = await connection.query(
      'SELECT * FROM inventory_items WHERE item_id = 21'
    );
    
    if (items.length === 0) {
      console.log('No laptop found with ID 21');
      return;
    }
    
    const laptop = items[0];
    console.log('\nCurrent laptop details:');
    console.log('----------------------');
    console.log(`ID: ${laptop.item_id}`);
    console.log(`Name: ${laptop.name}`);
    console.log(`Model: ${laptop.model || 'N/A'}`);
    console.log(`Manufacturer: ${laptop.manufacturer || 'N/A'}`);
    console.log(`Serial Number: ${laptop.serial_number || 'N/A'}`);
    
    // Update laptop name
    await connection.query(
      'UPDATE inventory_items SET name = ? WHERE item_id = 21',
      ['Laptop CR-11']
    );
    
    console.log('\nLaptop name updated successfully!');
    
    // Get updated laptop details
    const [updatedItems] = await connection.query(
      'SELECT * FROM inventory_items WHERE item_id = 21'
    );
    
    const updatedLaptop = updatedItems[0];
    console.log('\nUpdated laptop details:');
    console.log('----------------------');
    console.log(`ID: ${updatedLaptop.item_id}`);
    console.log(`Name: ${updatedLaptop.name}`);
    console.log(`Model: ${updatedLaptop.model || 'N/A'}`);
    console.log(`Manufacturer: ${updatedLaptop.manufacturer || 'N/A'}`);
    console.log(`Serial Number: ${updatedLaptop.serial_number || 'N/A'}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

main();
