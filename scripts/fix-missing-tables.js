const mysql = require('mysql2/promise');

async function fixMissingTables() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking for missing tables...');

        // Check if exam_attempts table exists
        const [tables] = await connection.query(`
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'exam_attempts'
        `);

        if (tables.length === 0) {
            console.log('Creating exam_attempts table...');
            
            // Create the exam_attempts table
            await connection.query(`
                CREATE TABLE exam_attempts (
                    attempt_id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    exam_id INT NOT NULL,
                    score DECIMAL(5,2) DEFAULT 0,
                    status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
                    start_time DATETIME NOT NULL,
                    end_time DATETIME,
                    attempt_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (exam_id) REFERENCES exams(exam_id) ON DELETE CASCADE,
                    INDEX (user_id),
                    INDEX (exam_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
            
            console.log('exam_attempts table created successfully!');
        } else {
            console.log('exam_attempts table already exists.');
        }

        // Check if user_answers table exists
        const [answerTables] = await connection.query(`
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = 'exam_prep_platform' 
            AND TABLE_NAME = 'user_answers'
        `);

        if (answerTables.length === 0) {
            console.log('Creating user_answers table...');
            
            // Create the user_answers table
            await connection.query(`
                CREATE TABLE user_answers (
                    answer_id INT AUTO_INCREMENT PRIMARY KEY,
                    attempt_id INT NOT NULL,
                    question_id INT NOT NULL,
                    user_answer TEXT,
                    is_correct BOOLEAN DEFAULT FALSE,
                    score DECIMAL(5,2) DEFAULT 0,
                    answer_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (attempt_id) REFERENCES exam_attempts(attempt_id) ON DELETE CASCADE,
                    FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE,
                    INDEX (attempt_id),
                    INDEX (question_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
            
            console.log('user_answers table created successfully!');
        } else {
            console.log('user_answers table already exists.');
        }

        console.log('Database schema update completed!');

    } catch (error) {
        console.error('Error updating database schema:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

fixMissingTables(); 