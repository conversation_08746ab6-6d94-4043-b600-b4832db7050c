/**
 * <PERSON><PERSON><PERSON> to fix the practical records issue
 * This will add a record to the student_practical_records table that references the practical we created
 */

const db = require('../config/database');

async function fixPracticalRecords() {
  try {
    console.log('Starting to fix practical records...');
    
    // Begin transaction
    await db.query('START TRANSACTION');
    
    // 1. Find the CS student
    const [students] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csstudent']
    );
    
    if (students.length === 0) {
      console.log('CS student not found!');
      return;
    }
    
    const studentId = students[0].id;
    console.log(`Found CS student with ID: ${studentId}`);
    
    // 2. Find the CS teacher
    const [teachers] = await db.query(
      'SELECT id FROM users WHERE username = ?',
      ['csteacher']
    );
    
    if (teachers.length === 0) {
      console.log('CS teacher not found!');
      return;
    }
    
    const teacherId = teachers[0].id;
    console.log(`Found CS teacher with ID: ${teacherId}`);
    
    // 3. Find the practical we created
    const [practicals] = await db.query(
      'SELECT id FROM practicals WHERE teacher_id = ? ORDER BY id DESC LIMIT 1',
      [teacherId]
    );
    
    if (practicals.length === 0) {
      console.log('Practical not found!');
      return;
    }
    
    const practicalId = practicals[0].id;
    console.log(`Found practical with ID: ${practicalId}`);
    
    // 4. Check if a record already exists
    const [existingRecords] = await db.query(
      'SELECT id FROM practical_records WHERE practical_id = ? AND student_id = ?',
      [practicalId, studentId]
    );
    
    if (existingRecords.length > 0) {
      console.log(`Record already exists with ID: ${existingRecords[0].id}`);
      await db.query('COMMIT');
      return;
    }
    
    // 5. Add a record to the practical_records table
    const [recordResult] = await db.query(
      `INSERT INTO practical_records 
       (practical_id, student_id, content, submission_date, status)
       VALUES (?, ?, ?, NOW(), ?)`,
      [practicalId, studentId, 'I have implemented the data structures as instructed.', 'submitted']
    );
    
    console.log(`Added practical record with ID: ${recordResult.insertId}`);
    
    // Commit transaction
    await db.query('COMMIT');
    console.log('Successfully fixed practical records!');
    
  } catch (error) {
    // Rollback transaction on error
    await db.query('ROLLBACK');
    console.error('Error fixing practical records:', error);
    throw error;
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
fixPracticalRecords();
