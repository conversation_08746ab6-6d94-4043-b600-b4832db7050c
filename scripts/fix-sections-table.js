const mysql = require('mysql2/promise');

async function fixSectionsTable() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Checking sections table structure...');

        // Check if 'name' column exists
        const [nameColumns] = await connection.query('SHOW COLUMNS FROM sections LIKE ?', ['name']);
        
        // Check if 'section_name' column exists
        const [sectionNameColumns] = await connection.query('SHOW COLUMNS FROM sections LIKE ?', ['section_name']);
        
        if (nameColumns.length > 0 && sectionNameColumns.length === 0) {
            console.log("Found 'name' column but no 'section_name' column. Renaming column...");
            
            // Rename the column
            await connection.query(`
                ALTER TABLE sections 
                CHANGE COLUMN name section_name VARCHAR(255) NOT NULL
            `);
            
            console.log("Column renamed from 'name' to 'section_name' successfully!");
        } else if (sectionNameColumns.length > 0) {
            console.log("'section_name' column already exists.");
        } else if (nameColumns.length === 0 && sectionNameColumns.length === 0) {
            console.log("Neither 'name' nor 'section_name' column exists. Adding 'section_name' column...");
            
            // Add the column
            await connection.query(`
                ALTER TABLE sections 
                ADD COLUMN section_name VARCHAR(255) NOT NULL AFTER section_id
            `);
            
            console.log("'section_name' column added successfully!");
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

fixSectionsTable(); 