const mysql = require('mysql2/promise');

async function fixUserAnswersTable() {
    let connection;
    try {
        console.log('Connecting to database...');

        // Create connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'exam_prep_platform'
        });

        console.log('Checking user_answers table structure...');

        // Get current columns
        const [columns] = await connection.query('DESCRIBE user_answers');
        const columnNames = columns.map(col => col.Field);

        // Check for required columns
        if (!columnNames.includes('is_bookmarked')) {
            console.log('Adding is_bookmarked column...');
            await connection.query('ALTER TABLE user_answers ADD COLUMN is_bookmarked TINYINT(1) NOT NULL DEFAULT 0 AFTER essay_answer');
            console.log('✓ is_bookmarked column added');
        } else {
            console.log('✓ is_bookmarked column exists');
        }

        if (!columnNames.includes('answer_text')) {
            console.log('Adding answer_text column...');
            await connection.query('ALTER TABLE user_answers ADD COLUMN answer_text TEXT NULL AFTER selected_option_id');
            console.log('✓ answer_text column added');
        } else {
            console.log('✓ answer_text column exists');
        }

        console.log('\nUser_answers table fix complete!');

    } catch (error) {
        console.error('Error fixing user_answers table:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
        process.exit(0);
    }
}

fixUserAnswersTable();