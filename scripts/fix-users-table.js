const db = require('../config/database');

async function fixUsersTable() {
    try {
        console.log('Starting to fix users table structure...');
        
        // Check if 'name' column exists
        const [nameColumns] = await db.query('SHOW COLUMNS FROM users LIKE ?', ['name']);
        const [usernameColumns] = await db.query('SHOW COLUMNS FROM users LIKE ?', ['username']);
        
        if (nameColumns.length === 0 && usernameColumns.length > 0) {
            // If 'name' doesn't exist but 'username' does, add 'name' column
            await db.query('ALTER TABLE users ADD COLUMN name VARCHAR(255) AFTER id');
            // Copy username values to name column
            await db.query('UPDATE users SET name = username WHERE name IS NULL');
            console.log('Added name column and copied username values to it');
        } else if (nameColumns.length === 0) {
            // If neither exists, add name column
            await db.query('ALTER TABLE users ADD COLUMN name VARCHAR(255) AFTER id');
            console.log('Added name column');
        } else {
            console.log('Name column already exists');
        }
        
        // Check and add bio column if it doesn't exist
        const [bioColumns] = await db.query('SHOW COLUMNS FROM users LIKE ?', ['bio']);
        if (bioColumns.length === 0) {
            await db.query('ALTER TABLE users ADD COLUMN bio TEXT AFTER profile_image');
            console.log('Added bio column');
        } else {
            console.log('Bio column already exists');
        }
        
        // Check and add profile_image column if it doesn't exist
        const [profileImageColumns] = await db.query('SHOW COLUMNS FROM users LIKE ?', ['profile_image']);
        if (profileImageColumns.length === 0) {
            await db.query('ALTER TABLE users ADD COLUMN profile_image VARCHAR(255) AFTER role');
            console.log('Added profile_image column');
        } else {
            console.log('Profile image column already exists');
        }
        
        // Verify the table structure
        const [columns] = await db.query('SHOW COLUMNS FROM users');
        console.log('Current users table columns:', columns.map(col => col.Field).join(', '));
        
        console.log('Users table structure has been updated successfully');
    } catch (error) {
        console.error('Error fixing users table:', error);
        process.exit(1);
    }
}

fixUsersTable(); 