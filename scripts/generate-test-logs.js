const db = require('../config/database');

async function generateTestLogs() {
    try {
        console.log('Generating test error logs...');
        
        // Check if logs table exists
        const [logsExists] = await db.query(`
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'exam_prep_platform' AND table_name = 'logs'
        `);
        
        if (logsExists.length === 0) {
            console.log('Creating logs table...');
            await db.query(`
                CREATE TABLE logs (
                    id INT NOT NULL AUTO_INCREMENT,
                    user_id INT NULL,
                    action VARCHAR(255) NOT NULL,
                    entity_type VARCHAR(50) NULL,
                    entity_id INT NULL,
                    details TEXT NULL,
                    ip_address VARCHAR(50) NULL,
                    user_agent TEXT NULL,
                    route VARCHAR(255) NULL,
                    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY idx_user_id (user_id),
                    KEY idx_action (action),
                    KEY idx_timestamp (timestamp)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('Logs table created');
        }
        
        // Generate system error logs
        const errorTypes = [
            'database_connection_error',
            'query_execution_error',
            'authentication_failed',
            'permission_denied',
            'file_upload_error',
            'api_request_failed',
            'session_expired',
            'validation_error'
        ];
        
        const routes = [
            '/admin/dashboard',
            '/admin/tests',
            '/admin/users',
            '/admin/access-requests',
            '/tests',
            '/profile',
            '/login'
        ];
        
        const ipAddresses = [
            '***********',
            '********',
            '**********',
            '127.0.0.1'
        ];
        
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
        ];
        
        // Generate 20 random error logs
        for (let i = 0; i < 20; i++) {
            const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)];
            const route = routes[Math.floor(Math.random() * routes.length)];
            const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
            const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
            const userId = Math.floor(Math.random() * 5) + 1; // Assuming user IDs 1-5 exist
            
            // Generate random timestamp within the last 30 days
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));
            const timestamp = date.toISOString().slice(0, 19).replace('T', ' ');
            
            // Generate error details
            let details;
            switch (errorType) {
                case 'database_connection_error':
                    details = JSON.stringify({
                        message: 'Failed to connect to database',
                        code: 'ECONNREFUSED',
                        host: 'localhost',
                        port: 3306
                    });
                    break;
                case 'query_execution_error':
                    details = JSON.stringify({
                        message: 'Error executing query',
                        query: 'SELECT * FROM non_existent_table',
                        error: 'Table \'exam_prep_platform.non_existent_table\' doesn\'t exist'
                    });
                    break;
                case 'authentication_failed':
                    details = JSON.stringify({
                        message: 'Authentication failed',
                        username: 'user' + Math.floor(Math.random() * 100),
                        reason: 'Invalid credentials'
                    });
                    break;
                case 'permission_denied':
                    details = JSON.stringify({
                        message: 'Permission denied',
                        resource: route,
                        requiredRole: 'admin'
                    });
                    break;
                case 'file_upload_error':
                    details = JSON.stringify({
                        message: 'File upload failed',
                        filename: 'document' + Math.floor(Math.random() * 100) + '.pdf',
                        reason: 'File size exceeds limit'
                    });
                    break;
                case 'api_request_failed':
                    details = JSON.stringify({
                        message: 'API request failed',
                        endpoint: '/api/data',
                        statusCode: 500,
                        response: 'Internal server error'
                    });
                    break;
                case 'session_expired':
                    details = JSON.stringify({
                        message: 'Session expired',
                        sessionId: 'sess_' + Math.random().toString(36).substring(2, 15),
                        lastActivity: new Date(date.getTime() - 3600000).toISOString()
                    });
                    break;
                case 'validation_error':
                    details = JSON.stringify({
                        message: 'Validation error',
                        field: ['email', 'password', 'username'][Math.floor(Math.random() * 3)],
                        value: 'invalid_value',
                        constraint: 'required'
                    });
                    break;
                default:
                    details = JSON.stringify({
                        message: 'Unknown error',
                        timestamp: new Date().toISOString()
                    });
            }
            
            // Insert log
            await db.query(`
                INSERT INTO logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent, route, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                errorType,
                'system',
                null,
                details,
                ipAddress,
                userAgent,
                route,
                timestamp
            ]);
            
            console.log(`Generated ${errorType} log`);
        }
        
        // Check if jsvalues table exists
        const [jsvaluesExists] = await db.query(`
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'exam_prep_platform' AND table_name = 'jsvalues'
        `);
        
        if (jsvaluesExists.length === 0) {
            console.log('Creating jsvalues table...');
            await db.query(`
                CREATE TABLE jsvalues (
                    id INT NOT NULL AUTO_INCREMENT,
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    route VARCHAR(255) DEFAULT NULL,
                    context VARCHAR(255) DEFAULT NULL,
                    variable_name VARCHAR(255) NOT NULL,
                    variable_type VARCHAR(50) DEFAULT NULL,
                    variable_value TEXT DEFAULT NULL,
                    user_id INT DEFAULT NULL,
                    ip_address VARCHAR(50) DEFAULT NULL,
                    user_agent TEXT DEFAULT NULL,
                    PRIMARY KEY (id),
                    KEY idx_timestamp (timestamp),
                    KEY idx_context (context),
                    KEY idx_variable_name (variable_name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('jsvalues table created');
        }
        
        // Generate database error logs
        const dbErrorContexts = [
            'database_error',
            'query_error',
            'connection_error',
            'transaction_error'
        ];
        
        const variableNames = [
            'error_message',
            'sql_error',
            'connection_failed',
            'transaction_failed'
        ];
        
        // Generate 10 random database error logs
        for (let i = 0; i < 10; i++) {
            const context = dbErrorContexts[Math.floor(Math.random() * dbErrorContexts.length)];
            const variableName = variableNames[Math.floor(Math.random() * variableNames.length)];
            const route = routes[Math.floor(Math.random() * routes.length)];
            const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
            const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
            const userId = Math.floor(Math.random() * 5) + 1; // Assuming user IDs 1-5 exist
            
            // Generate random timestamp within the last 30 days
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));
            const timestamp = date.toISOString().slice(0, 19).replace('T', ' ');
            
            // Generate variable value
            let variableValue;
            switch (context) {
                case 'database_error':
                    variableValue = JSON.stringify({
                        message: 'Database error occurred',
                        code: 'ER_DB_ERROR',
                        sqlState: '42000'
                    });
                    break;
                case 'query_error':
                    variableValue = JSON.stringify({
                        message: 'Error executing query',
                        query: 'SELECT * FROM users WHERE username = ?',
                        parameters: ['user123'],
                        error: 'Syntax error'
                    });
                    break;
                case 'connection_error':
                    variableValue = JSON.stringify({
                        message: 'Failed to connect to database',
                        host: 'localhost',
                        port: 3306,
                        error: 'Connection refused'
                    });
                    break;
                case 'transaction_error':
                    variableValue = JSON.stringify({
                        message: 'Transaction failed',
                        operation: 'COMMIT',
                        error: 'Deadlock detected'
                    });
                    break;
                default:
                    variableValue = JSON.stringify({
                        message: 'Unknown database error',
                        timestamp: new Date().toISOString()
                    });
            }
            
            // Insert jsvalue
            await db.query(`
                INSERT INTO jsvalues (timestamp, route, context, variable_name, variable_type, variable_value, user_id, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                timestamp,
                route,
                context,
                variableName,
                'object',
                variableValue,
                userId,
                ipAddress,
                userAgent
            ]);
            
            console.log(`Generated ${context} jsvalue`);
        }
        
        // Check if query_error_logs table exists
        const [queryErrorsExists] = await db.query(`
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'exam_prep_platform' AND table_name = 'query_error_logs'
        `);
        
        if (queryErrorsExists.length === 0) {
            console.log('Creating query_error_logs table...');
            await db.query(`
                CREATE TABLE query_error_logs (
                    id INT NOT NULL AUTO_INCREMENT,
                    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    error_code VARCHAR(50),
                    error_message TEXT,
                    query TEXT,
                    parameters TEXT,
                    route VARCHAR(255),
                    user_id INT,
                    ip_address VARCHAR(50),
                    user_agent TEXT,
                    stack_trace TEXT,
                    PRIMARY KEY (id),
                    KEY idx_timestamp (timestamp),
                    KEY idx_error_code (error_code),
                    KEY idx_user_id (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            console.log('query_error_logs table created');
        }
        
        // Generate query error logs
        const errorCodes = [
            'ER_NO_SUCH_TABLE',
            'ER_BAD_FIELD_ERROR',
            'ER_PARSE_ERROR',
            'ER_NO_REFERENCED_ROW',
            'ER_DUP_ENTRY'
        ];
        
        const queries = [
            'SELECT * FROM non_existent_table',
            'SELECT non_existent_column FROM users',
            'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
            'UPDATE users SET role_id = ? WHERE id = ?',
            'DELETE FROM users WHERE id = ?'
        ];
        
        // Generate 10 random query error logs
        for (let i = 0; i < 10; i++) {
            const errorCode = errorCodes[Math.floor(Math.random() * errorCodes.length)];
            const query = queries[Math.floor(Math.random() * queries.length)];
            const route = routes[Math.floor(Math.random() * routes.length)];
            const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
            const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
            const userId = Math.floor(Math.random() * 5) + 1; // Assuming user IDs 1-5 exist
            
            // Generate random timestamp within the last 30 days
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));
            const timestamp = date.toISOString().slice(0, 19).replace('T', ' ');
            
            // Generate error message and parameters
            let errorMessage, parameters, stackTrace;
            switch (errorCode) {
                case 'ER_NO_SUCH_TABLE':
                    errorMessage = 'Table \'exam_prep_platform.non_existent_table\' doesn\'t exist';
                    parameters = '[]';
                    break;
                case 'ER_BAD_FIELD_ERROR':
                    errorMessage = 'Unknown column \'non_existent_column\' in \'field list\'';
                    parameters = '[]';
                    break;
                case 'ER_PARSE_ERROR':
                    errorMessage = 'You have an error in your SQL syntax';
                    parameters = '["user123", "<EMAIL>", "password123"]';
                    break;
                case 'ER_NO_REFERENCED_ROW':
                    errorMessage = 'Cannot add or update a child row: a foreign key constraint fails';
                    parameters = '[999, 1]';
                    break;
                case 'ER_DUP_ENTRY':
                    errorMessage = 'Duplicate entry \'user123\' for key \'users.username\'';
                    parameters = '["user123", "<EMAIL>", "password123"]';
                    break;
                default:
                    errorMessage = 'Unknown database error';
                    parameters = '[]';
            }
            
            // Generate stack trace
            stackTrace = `Error: ${errorMessage}
    at Query.Sequence._packetToError (/app/node_modules/mysql2/lib/sequence.js:47:14)
    at Query.ErrorPacket (/app/node_modules/mysql2/lib/query.js:53:18)
    at Connection.execute (/app/node_modules/mysql2/lib/connection.js:198:14)
    at Connection.query (/app/node_modules/mysql2/lib/connection.js:94:16)
    at /app/config/database.js:45:20
    at processTicksAndRejections (internal/process/task_queues.js:95:5)`;
            
            // Insert query error log
            await db.query(`
                INSERT INTO query_error_logs (timestamp, error_code, error_message, query, parameters, route, user_id, ip_address, user_agent, stack_trace)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                timestamp,
                errorCode,
                errorMessage,
                query,
                parameters,
                route,
                userId,
                ipAddress,
                userAgent,
                stackTrace
            ]);
            
            console.log(`Generated ${errorCode} query error log`);
        }
        
        console.log('Test logs generation completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error generating test logs:', error);
        process.exit(1);
    }
}

// Run the generator
generateTestLogs();
