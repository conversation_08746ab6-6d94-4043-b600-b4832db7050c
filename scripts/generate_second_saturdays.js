const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * Finds the second Saturday of a given month and year
 * @param {number} year - The year
 * @param {number} month - The month (0-11)
 * @returns {Date} - The date of the second Saturday
 */
function findSecondSaturday(year, month) {
  // Start with the 1st of the month
  const date = new Date(year, month, 1);
  
  // Find the first Saturday (day 6)
  while (date.getDay() !== 6) {
    date.setDate(date.getDate() + 1);
  }
  
  // Add 7 days to get to the second Saturday
  date.setDate(date.getDate() + 7);
  
  return date;
}

/**
 * Formats a date as YYYY-MM-DD
 * @param {Date} date - The date to format
 * @returns {string} - The formatted date string
 */
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Generates and adds second Saturdays for a range of years
 * @param {number} startYear - The starting year
 * @param {number} endYear - The ending year (inclusive)
 */
async function generateAndAddSecondSaturdays(startYear, endYear) {
  console.log(`Generating second Saturdays for years ${startYear} to ${endYear}...`);
  
  // Create a database connection
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'exam_prep_platform'
  });
  
  try {
    // Make sure the holiday_calendar table exists
    await connection.query(`
      CREATE TABLE IF NOT EXISTS holiday_calendar (
        id INT AUTO_INCREMENT PRIMARY KEY,
        holiday_date DATE NOT NULL,
        description VARCHAR(255) NOT NULL,
        holiday_type VARCHAR(50) DEFAULT 'Public Holiday',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        UNIQUE KEY unique_holiday_date (holiday_date)
      )
    `);
    
    // Generate second Saturdays for each month in the range
    const secondSaturdays = [];
    for (let year = startYear; year <= endYear; year++) {
      for (let month = 0; month < 12; month++) {
        const secondSaturday = findSecondSaturday(year, month);
        secondSaturdays.push({
          date: formatDate(secondSaturday),
          description: 'Second Saturday',
          type: 'Public Holiday'
        });
      }
    }
    
    // Insert the second Saturdays into the database
    console.log(`Adding ${secondSaturdays.length} second Saturdays to the database...`);
    
    for (const holiday of secondSaturdays) {
      await connection.query(`
        INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
        VALUES (?, ?, ?)
      `, [holiday.date, holiday.description, holiday.type]);
    }
    
    // Verify the holidays were added
    const [rows] = await connection.query(`
      SELECT COUNT(*) as count FROM holiday_calendar 
      WHERE description = 'Second Saturday' 
      AND holiday_date BETWEEN ? AND ?
    `, [`${startYear}-01-01`, `${endYear}-12-31`]);
    
    console.log(`Total second Saturday holidays for ${startYear}-${endYear} in the database: ${rows[0].count}`);
    
  } catch (error) {
    console.error('Error generating and adding second Saturdays:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

// Default to current year and next year if no arguments provided
const startYear = parseInt(process.argv[2]) || new Date().getFullYear();
const endYear = parseInt(process.argv[3]) || startYear + 1;

// Run the function
generateAndAddSecondSaturdays(startYear, endYear).catch(console.error);
