const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * Finds all Sundays in a given year
 * @param {number} year - The year
 * @returns {Date[]} - Array of dates for all Sundays in the year
 */
function findAllSundaysInYear(year) {
  const sundays = [];
  
  // Start with January 1st of the given year
  const date = new Date(year, 0, 1);
  
  // Find the first Sunday
  while (date.getDay() !== 0) {
    date.setDate(date.getDate() + 1);
  }
  
  // Add all Sundays for the year
  while (date.getFullYear() === year) {
    sundays.push(new Date(date));
    date.setDate(date.getDate() + 7);
  }
  
  return sundays;
}

/**
 * Formats a date as YYYY-MM-DD
 * @param {Date} date - The date to format
 * @returns {string} - The formatted date string
 */
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Generates and adds all Sundays for a range of years
 * @param {number} startYear - The starting year
 * @param {number} endYear - The ending year (inclusive)
 */
async function generateAndAddSundays(startYear, endYear) {
  console.log(`Generating Sundays for years ${startYear} to ${endYear}...`);
  
  // Create a database connection
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'exam_prep_platform'
  });
  
  try {
    // Make sure the holiday_calendar table exists
    await connection.query(`
      CREATE TABLE IF NOT EXISTS holiday_calendar (
        id INT AUTO_INCREMENT PRIMARY KEY,
        holiday_date DATE NOT NULL,
        description VARCHAR(255) NOT NULL,
        holiday_type VARCHAR(50) DEFAULT 'Public Holiday',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        UNIQUE KEY unique_holiday_date (holiday_date)
      )
    `);
    
    // Generate Sundays for each year in the range
    const allSundays = [];
    for (let year = startYear; year <= endYear; year++) {
      const sundays = findAllSundaysInYear(year);
      sundays.forEach(sunday => {
        allSundays.push({
          date: formatDate(sunday),
          description: 'Sunday',
          type: 'National Holiday'
        });
      });
    }
    
    // Insert the Sundays into the database
    console.log(`Adding ${allSundays.length} Sundays to the database...`);
    
    for (const holiday of allSundays) {
      await connection.query(`
        INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
        VALUES (?, ?, ?)
      `, [holiday.date, holiday.description, holiday.type]);
    }
    
    // Verify the holidays were added
    const [rows] = await connection.query(`
      SELECT COUNT(*) as count FROM holiday_calendar 
      WHERE description = 'Sunday' 
      AND holiday_date BETWEEN ? AND ?
    `, [`${startYear}-01-01`, `${endYear}-12-31`]);
    
    console.log(`Total Sunday holidays for ${startYear}-${endYear} in the database: ${rows[0].count}`);
    
  } catch (error) {
    console.error('Error generating and adding Sundays:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

// Default to current year and next year if no arguments provided
const startYear = parseInt(process.argv[2]) || new Date().getFullYear();
const endYear = parseInt(process.argv[3]) || startYear + 1;

// Run the function
generateAndAddSundays(startYear, endYear).catch(console.error);
