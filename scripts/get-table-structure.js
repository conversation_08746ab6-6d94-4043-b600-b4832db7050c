/**
 * <PERSON><PERSON><PERSON> to get the structure of all tables in the database
 * This helps understand the database schema and relationships
 */

const db = require('../config/database');

// List of tables that should exist in the database
const requiredTables = [
  'users',
  'sessions',
  'exams',
  'sections',
  'questions',
  'options',
  'exam_attempts',
  'user_answers',
  'notifications',
  'classes',
  'subjects',
  'student_classes',
  'student_subjects',
  'instruction_plans',
  'instruction_plan_resources',
  'plan_collaborators',
  'teacher_lectures',
  'teacher_practicals',
  'student_practical_records',
  'it_inventory',
  'it_issues',
  'active_sessions',
  'logs',
  'activity_log'
];

async function getTableStructure() {
  try {
    console.log('\n=== Database Configuration ===');
    console.log(`Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`User: ${process.env.DB_USER || 'root'}`);
    console.log(`Database: ${process.env.DB_NAME || 'exam_prep_platform'}`);
    console.log(`Port: ${process.env.DB_PORT || '3306'}`);
    console.log('===========================\n');

    console.log('Getting table structure for all tables...\n');
    
    // Get all tables in the database
    const [tables] = await db.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [process.env.DB_NAME]);
    
    const existingTables = tables.map(t => t.TABLE_NAME);
    
    // Create a map to store table structures
    const tableStructures = {};
    
    // Get structure for each table
    for (const table of existingTables) {
      const [columns] = await db.query(`
        SHOW COLUMNS FROM ${table}
      `);
      
      tableStructures[table] = columns;
    }
    
    // Print table structures
    console.log('Table Structures:');
    console.log('================\n');
    
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        console.log(`Table: ${table}`);
        console.log('-'.repeat(table.length + 7));
        
        const columns = tableStructures[table];
        console.log('| Field | Type | Null | Key | Default | Extra |');
        console.log('|-------|------|------|-----|---------|-------|');
        
        columns.forEach(column => {
          console.log(`| ${column.Field} | ${column.Type} | ${column.Null} | ${column.Key} | ${column.Default || 'NULL'} | ${column.Extra} |`);
        });
        
        console.log('\n');
      }
    }
    
    // Find common fields across tables
    console.log('Common Fields Analysis:');
    console.log('=====================\n');
    
    const fieldCounts = {};
    
    // Count occurrences of each field
    for (const table in tableStructures) {
      tableStructures[table].forEach(column => {
        if (!fieldCounts[column.Field]) {
          fieldCounts[column.Field] = {
            count: 0,
            tables: []
          };
        }
        
        fieldCounts[column.Field].count++;
        fieldCounts[column.Field].tables.push(table);
      });
    }
    
    // Sort fields by occurrence count (descending)
    const sortedFields = Object.keys(fieldCounts).sort((a, b) => {
      return fieldCounts[b].count - fieldCounts[a].count;
    });
    
    // Print common fields (appearing in more than one table)
    console.log('Fields appearing in multiple tables:');
    console.log('| Field | Count | Tables |');
    console.log('|-------|-------|--------|');
    
    sortedFields.forEach(field => {
      if (fieldCounts[field].count > 1) {
        console.log(`| ${field} | ${fieldCounts[field].count} | ${fieldCounts[field].tables.join(', ')} |`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting table structure:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
getTableStructure();
