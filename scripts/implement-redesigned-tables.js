const mysql = require('mysql2/promise');
const fs = require('fs');

async function implementRedesignedTables() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Implementing redesigned staff tables...');

        // Read and execute the SQL file
        const sqlContent = fs.readFileSync('database/redesigned-staff-tables.sql', 'utf8');
        
        // Split SQL statements and execute them one by one
        const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i].trim();
            if (statement.length > 0) {
                try {
                    console.log(`Executing statement ${i + 1}/${statements.length}...`);
                    await connection.execute(statement);
                } catch (error) {
                    // Some ALTER TABLE statements might fail if columns don't exist, that's okay
                    if (!error.message.includes("check that column/key exists")) {
                        console.log(`Warning on statement ${i + 1}: ${error.message}`);
                    }
                }
            }
        }

        console.log('Redesigned tables implemented successfully!');

        // Verify tables were created
        const [tables] = await connection.execute(`
            SHOW TABLES LIKE 'staff_%'
        `);
        
        console.log('\nCreated tables:');
        tables.forEach(table => {
            console.log(`- ${Object.values(table)[0]}`);
        });

    } catch (error) {
        console.error('Error implementing redesigned tables:', error);
    } finally {
        await connection.end();
    }
}

implementRedesignedTables();
