const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function initializeDatabase() {
    let connection;
    try {
        // Create connection without database
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: ''
        });

        // Create database if not exists
        await connection.query('CREATE DATABASE IF NOT EXISTS exam_prep_platform');
        console.log('✓ Database created or already exists');

        // Use the database
        await connection.query('USE exam_prep_platform');

        // Drop existing tables to ensure clean setup
        await connection.query('DROP TABLE IF EXISTS sessions');
        await connection.query('DROP TABLE IF EXISTS users');
        console.log('✓ Old tables dropped');

        // Create users table with correct column names and date_of_birth
        await connection.query(`
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                date_of_birth DATE NOT NULL,
                role VARCHAR(50) DEFAULT 'user',
                profile_image VARCHAR(255),
                reset_token VARCHAR(255),
                reset_token_expires DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX email_index (email),
                INDEX username_dob_index (username, date_of_birth)
            )
        `);
        console.log('✓ Users table created');

        // Create sessions table
        await connection.query(`
            CREATE TABLE sessions (
                session_id VARCHAR(128) NOT NULL PRIMARY KEY,
                expires BIGINT,
                data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('✓ Sessions table created');

        // Create test admin user
        const adminPassword = await bcrypt.hash('admin123', 10);
        await connection.query(
            'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
            ['Admin User', '<EMAIL>', adminPassword, '1990-01-01', 'admin']
        );
        console.log('✓ Admin user created');

        // Create test regular user
        const userPassword = await bcrypt.hash('user123', 10);
        await connection.query(
            'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
            ['Test User', '<EMAIL>', userPassword, '1995-05-15', 'user']
        );
        console.log('✓ Test user created');

        // Create test user for CSI
        const csiPassword = await bcrypt.hash('password123', 10);
        await connection.query(
            'INSERT INTO users (username, email, password, date_of_birth, role) VALUES (?, ?, ?, ?, ?)',
            ['CSI User', '<EMAIL>', csiPassword, '2000-12-25', 'user']
        );
        console.log('✓ CSI user created');

        console.log('\nDatabase initialized successfully!');
        console.log('\nTest Accounts:');
        console.log('Admin - Email: <EMAIL>, Password: admin123, DOB: 1990-01-01');
        console.log('User  - Email: <EMAIL>, Password: user123, DOB: 1995-05-15');
        console.log('CSI   - Email: <EMAIL>, Password: password123, DOB: 2000-12-25');

        // Show all users
        const [users] = await connection.query('SELECT id, username, email, date_of_birth, role FROM users');
        console.log('\nCreated Users:');
        console.table(users);

    } catch (error) {
        console.error('Error initializing database:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Run the initialization
initializeDatabase(); 