/**
 * <PERSON><PERSON><PERSON> to initialize student practical records
 */

const db = require('../config/database');

async function initPracticalRecords() {
  try {
    console.log('Starting student practical records initialization...');

    // Check if student_practical_records table exists
    const [recordsTables] = await db.query(`
      SHOW TABLES LIKE 'student_practical_records'
    `);

    if (recordsTables.length === 0) {
      console.log('Creating student_practical_records table...');
      
      // Create student_practical_records table
      await db.query(`
        CREATE TABLE IF NOT EXISTS student_practical_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id INT NOT NULL,
          teacher_id INT NOT NULL,
          practical_id INT,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          submission_date DATE,
          status ENUM('submitted', 'graded', 'pending') DEFAULT 'pending',
          grade VARCHAR(10),
          feedback TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('student_practical_records table created successfully');
    } else {
      console.log('student_practical_records table already exists');
    }

    // Check if there's any data in the student_practical_records table
    const [recordsCount] = await db.query(`
      SELECT COUNT(*) as count FROM student_practical_records
    `);

    // Get admin, teacher and student IDs
    const [users] = await db.query(`
      SELECT id, username, role FROM users
    `);
    
    const adminId = users.find(user => user.role === 'admin')?.id;
    const teacherId = users.find(user => user.role === 'teacher')?.id;
    const studentIds = users.filter(user => user.role === 'student').map(user => user.id);
    
    if (!adminId) {
      throw new Error('No admin user found in the database');
    }
    
    if (studentIds.length === 0) {
      throw new Error('No student users found in the database');
    }
    
    console.log(`Using admin ID: ${adminId}, teacher ID: ${teacherId || 'none'}, student IDs: ${studentIds.join(', ')}`);

    // Check if teacher_practicals table exists
    const [practicalsTables] = await db.query(`
      SHOW TABLES LIKE 'teacher_practicals'
    `);

    if (practicalsTables.length === 0) {
      console.log('Creating teacher_practicals table...');
      
      // Create teacher_practicals table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_practicals (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_section_id INT,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          venue VARCHAR(100),
          status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('teacher_practicals table created successfully');
    } else {
      console.log('teacher_practicals table already exists');
    }

    // Check if there's any data in the teacher_practicals table
    const [practicalsCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_practicals
    `);

    if (practicalsCount[0].count === 0) {
      console.log('Adding sample data to teacher_practicals table...');
      
      // Define subjects and venues
      const subjects = ['Physics', 'Chemistry', 'Biology', 'Computer Science'];
      const venues = ['Physics Lab', 'Chemistry Lab', 'Biology Lab', 'Computer Lab'];
      
      // Get class sections if they exist
      let classSectionId = 1;
      try {
        const [sections] = await db.query(`
          SELECT id FROM class_sections LIMIT 1
        `);
        
        if (sections.length > 0) {
          classSectionId = sections[0].id;
        }
      } catch (error) {
        console.log('Error fetching class sections:', error.message);
      }
      
      // Generate practicals
      const practicalCount = 20;
      const practicalIds = [];
      
      for (let i = 0; i < practicalCount; i++) {
        const subject = subjects[Math.floor(Math.random() * subjects.length)];
        const venue = venues[subjects.indexOf(subject)];
        const title = `${subject} Practical ${i + 1}`;
        const description = `Description for ${title}`;
        
        // Generate date and time
        const date = new Date();
        date.setDate(date.getDate() + Math.floor(Math.random() * 30) - 15); // Random date between -15 and +15 days
        const dateStr = date.toISOString().split('T')[0];
        
        const startHour = Math.floor(Math.random() * 6) + 9; // 9 AM to 2 PM
        const startTime = `${startHour.toString().padStart(2, '0')}:00:00`;
        const endTime = `${(startHour + 2).toString().padStart(2, '0')}:00:00`;
        
        // Determine status based on date
        let status;
        if (date < new Date()) {
          status = Math.random() < 0.8 ? 'completed' : 'cancelled';
        } else {
          status = 'pending';
        }
        
        // Insert practical for admin
        const [result] = await db.query(`
          INSERT INTO teacher_practicals 
          (teacher_id, class_section_id, title, description, date, start_time, end_time, venue, status) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          adminId,
          classSectionId,
          title,
          description,
          dateStr,
          startTime,
          endTime,
          venue,
          status
        ]);
        
        practicalIds.push(result.insertId);
        console.log(`Added practical for admin: ${title} on ${dateStr} (${status})`);
        
        // Insert practical for teacher if exists
        if (teacherId) {
          await db.query(`
            INSERT INTO teacher_practicals 
            (teacher_id, class_section_id, title, description, date, start_time, end_time, venue, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            teacherId,
            classSectionId,
            title,
            description,
            dateStr,
            startTime,
            endTime,
            venue,
            status
          ]);
          
          console.log(`Added practical for teacher: ${title} on ${dateStr} (${status})`);
        }
      }
      
      console.log('Sample data added to teacher_practicals table');
      
      // Now add student practical records
      if (recordsCount[0].count === 0) {
        console.log('Adding sample data to student_practical_records table...');
        
        // Generate records for each student and practical
        for (const studentId of studentIds) {
          // Each student submits 5-10 practicals
          const recordCount = Math.floor(Math.random() * 6) + 5;
          
          for (let i = 0; i < recordCount; i++) {
            // Pick a random practical
            const practicalId = practicalIds[Math.floor(Math.random() * practicalIds.length)];
            
            // Get practical details
            const [practicals] = await db.query(`
              SELECT * FROM teacher_practicals WHERE id = ?
            `, [practicalId]);
            
            if (practicals.length === 0) {
              console.log(`Practical ${practicalId} not found, skipping record`);
              continue;
            }
            
            const practical = practicals[0];
            const title = `Submission for ${practical.title}`;
            const description = `Student submission for ${practical.title}`;
            
            // Generate submission date
            const submissionDate = new Date(practical.date);
            submissionDate.setDate(submissionDate.getDate() + Math.floor(Math.random() * 5)); // 0-5 days after practical
            const submissionDateStr = submissionDate.toISOString().split('T')[0];
            
            // Determine status and grade
            let status, grade, feedback;
            if (submissionDate < new Date()) {
              if (Math.random() < 0.7) {
                status = 'graded';
                grade = ['A', 'B', 'C', 'D', 'F'][Math.floor(Math.random() * 5)];
                feedback = `Feedback for ${title}: ${grade === 'F' ? 'Needs improvement' : 'Good work'}`;
              } else {
                status = 'submitted';
                grade = null;
                feedback = null;
              }
            } else {
              status = 'pending';
              grade = null;
              feedback = null;
            }
            
            // Insert record for admin
            await db.query(`
              INSERT INTO student_practical_records 
              (student_id, teacher_id, practical_id, title, description, submission_date, status, grade, feedback) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              studentId,
              adminId,
              practicalId,
              title,
              description,
              submissionDateStr,
              status,
              grade,
              feedback
            ]);
            
            console.log(`Added practical record for student ${studentId} to admin: ${title} (${status})`);
            
            // Insert record for teacher if exists
            if (teacherId) {
              await db.query(`
                INSERT INTO student_practical_records 
                (student_id, teacher_id, practical_id, title, description, submission_date, status, grade, feedback) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                studentId,
                teacherId,
                practicalId,
                title,
                description,
                submissionDateStr,
                status,
                grade,
                feedback
              ]);
              
              console.log(`Added practical record for student ${studentId} to teacher: ${title} (${status})`);
            }
          }
        }
        
        console.log('Sample data added to student_practical_records table');
      } else {
        console.log(`student_practical_records table already has ${recordsCount[0].count} records`);
      }
    } else {
      console.log(`teacher_practicals table already has ${practicalsCount[0].count} records`);
      
      // If practicals exist but no records, add records
      if (recordsCount[0].count === 0) {
        console.log('Adding sample data to student_practical_records table...');
        
        // Get practicals
        const [practicals] = await db.query(`
          SELECT * FROM teacher_practicals
        `);
        
        // Generate records for each student and practical
        for (const studentId of studentIds) {
          // Each student submits 5-10 practicals
          const recordCount = Math.floor(Math.random() * 6) + 5;
          
          for (let i = 0; i < recordCount; i++) {
            // Pick a random practical
            const practical = practicals[Math.floor(Math.random() * practicals.length)];
            
            const title = `Submission for ${practical.title}`;
            const description = `Student submission for ${practical.title}`;
            
            // Generate submission date
            const submissionDate = new Date(practical.date);
            submissionDate.setDate(submissionDate.getDate() + Math.floor(Math.random() * 5)); // 0-5 days after practical
            const submissionDateStr = submissionDate.toISOString().split('T')[0];
            
            // Determine status and grade
            let status, grade, feedback;
            if (submissionDate < new Date()) {
              if (Math.random() < 0.7) {
                status = 'graded';
                grade = ['A', 'B', 'C', 'D', 'F'][Math.floor(Math.random() * 5)];
                feedback = `Feedback for ${title}: ${grade === 'F' ? 'Needs improvement' : 'Good work'}`;
              } else {
                status = 'submitted';
                grade = null;
                feedback = null;
              }
            } else {
              status = 'pending';
              grade = null;
              feedback = null;
            }
            
            // Insert record
            await db.query(`
              INSERT INTO student_practical_records 
              (student_id, teacher_id, practical_id, title, description, submission_date, status, grade, feedback) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              studentId,
              practical.teacher_id,
              practical.id,
              title,
              description,
              submissionDateStr,
              status,
              grade,
              feedback
            ]);
            
            console.log(`Added practical record for student ${studentId}: ${title} (${status})`);
          }
        }
        
        console.log('Sample data added to student_practical_records table');
      } else {
        console.log(`student_practical_records table already has ${recordsCount[0].count} records`);
      }
    }

    console.log('Student practical records initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing student practical records:', error);
    process.exit(1);
  }
}

// Run the initialization
initPracticalRecords();
