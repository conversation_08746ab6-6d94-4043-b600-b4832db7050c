/**
 * <PERSON><PERSON>t to initialize teacher lectures data
 */

const db = require('../config/database');

async function initTeacherLectures() {
  try {
    console.log('Starting teacher lectures initialization...');

    // Check if teacher_lectures table exists
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (tables.length === 0) {
      console.log('Creating teacher_lectures table...');

      // Create teacher_lectures table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_lectures (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          room VARCHAR(50),
          status ENUM('pending', 'delivered', 'cancelled', 'rescheduled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('teacher_lectures table created successfully');
    } else {
      console.log('teacher_lectures table already exists');
    }

    // Check if there's any data in the teacher_lectures table
    const [lectureCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_lectures
    `);

    if (lectureCount[0].count < 20) {
      console.log('Adding sample data to teacher_lectures table...');

      // Get a teacher ID (or admin ID if no teacher exists)
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' OR role = 'admin' LIMIT 1
      `);

      if (teachers.length === 0) {
        throw new Error('No teacher or admin user found in the database');
      }

      const teacherId = teachers[0].id;

      // Define subjects and class names
      const subjects = ['Physics', 'Chemistry', 'Biology', 'Mathematics', 'Computer Science'];
      const classNames = ['Class 11-A', 'Class 11-B', 'Class 12-A', 'Class 12-B'];
      const rooms = ['Room 101', 'Room 102', 'Lab 1', 'Lab 2', 'Computer Lab'];
      const statuses = ['pending', 'delivered', 'cancelled'];

      // Generate lectures for the past 6 months
      const today = new Date();
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(today.getMonth() - 6);

      // Create an array of dates from six months ago to today
      const dates = [];
      const currentDate = new Date(sixMonthsAgo);
      while (currentDate <= today) {
        // Skip weekends (0 = Sunday, 6 = Saturday)
        if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
          dates.push(new Date(currentDate));
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Generate lectures for each subject and class
      for (const subject of subjects) {
        for (const className of classNames) {
          // Generate 10-15 lectures per subject and class
          const lectureCount = Math.floor(Math.random() * 6) + 10;

          for (let i = 0; i < lectureCount; i++) {
            // Pick a random date from the dates array
            const dateIndex = Math.floor(Math.random() * dates.length);
            const date = dates[dateIndex];

            // Generate a random topic
            const topic = `${subject} Topic ${i + 1}`;

            // Generate random start and end times
            const startHour = Math.floor(Math.random() * 6) + 8; // 8 AM to 1 PM
            const startTime = `${startHour.toString().padStart(2, '0')}:00:00`;
            const endTime = `${(startHour + 1).toString().padStart(2, '0')}:00:00`;

            // Pick a random room
            const room = rooms[Math.floor(Math.random() * rooms.length)];

            // Determine status based on date
            let status;
            if (date < today) {
              // Past lectures are either delivered or cancelled
              status = Math.random() < 0.8 ? 'delivered' : 'cancelled';
            } else {
              // Future lectures are pending
              status = 'pending';
            }

            // Generate notes
            const notes = `Notes for ${subject} lecture on ${topic}`;

            // Insert lecture
            await db.query(`
              INSERT INTO teacher_lectures
              (teacher_id, class_name, subject_name, topic, date, start_time, end_time, status, notes)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              teacherId,
              className,
              subject,
              topic,
              date.toISOString().split('T')[0],
              startTime,
              endTime,
              status,
              notes
            ]);

            console.log(`Added lecture: ${subject} - ${topic} on ${date.toISOString().split('T')[0]}`);
          }
        }
      }

      console.log('Sample data added to teacher_lectures table');
    } else {
      console.log(`teacher_lectures table already has ${lectureCount[0].count} records`);
    }

    // Check if syllabus_progress table exists
    const [syllabusProgressTables] = await db.query(`
      SHOW TABLES LIKE 'syllabus_progress'
    `);

    if (syllabusProgressTables.length === 0) {
      console.log('Creating syllabus_progress table...');

      // Create syllabus_progress table
      await db.query(`
        CREATE TABLE IF NOT EXISTS syllabus_progress (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          completed_topics INT DEFAULT 0,
          total_topics INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('syllabus_progress table created successfully');
    } else {
      console.log('syllabus_progress table already exists');
    }

    // Check if there's any data in the syllabus_progress table
    const [progressCount] = await db.query(`
      SELECT COUNT(*) as count FROM syllabus_progress
    `);

    if (progressCount[0].count === 0) {
      console.log('Adding sample data to syllabus_progress table...');

      // Get a teacher ID (or admin ID if no teacher exists)
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' OR role = 'admin' LIMIT 1
      `);

      if (teachers.length === 0) {
        throw new Error('No teacher or admin user found in the database');
      }

      const teacherId = teachers[0].id;

      // Add progress data for each subject
      for (const subject of subjects) {
        const totalTopics = Math.floor(Math.random() * 10) + 20; // 20-30 topics
        const completedTopics = Math.floor(Math.random() * totalTopics); // 0 to totalTopics

        await db.query(`
          INSERT INTO syllabus_progress
          (teacher_id, subject_name, completed_topics, total_topics)
          VALUES (?, ?, ?, ?)
        `, [
          teacherId,
          subject,
          completedTopics,
          totalTopics
        ]);

        console.log(`Added syllabus progress for ${subject}: ${completedTopics}/${totalTopics}`);
      }

      console.log('Sample data added to syllabus_progress table');
    } else {
      console.log(`syllabus_progress table already has ${progressCount[0].count} records`);
    }

    // Check if teacher_syllabus table exists
    const [syllabusTopicsTables] = await db.query(`
      SHOW TABLES LIKE 'teacher_syllabus'
    `);

    if (syllabusTopicsTables.length === 0) {
      console.log('Creating teacher_syllabus table...');

      // Create teacher_syllabus table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_syllabus (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_section_id INT,
          class_name VARCHAR(100) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          topic_name VARCHAR(255) NOT NULL,
          description TEXT,
          status ENUM('pending', 'completed') DEFAULT 'pending',
          completion_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log('teacher_syllabus table created successfully');
    } else {
      console.log('teacher_syllabus table already exists');
    }

    // Check if there's any data in the teacher_syllabus table
    const [topicsCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_syllabus
    `);

    if (topicsCount[0].count === 0) {
      console.log('Adding sample data to teacher_syllabus table...');

      // Get a teacher ID (or admin ID if no teacher exists)
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' OR role = 'admin' LIMIT 1
      `);

      if (teachers.length === 0) {
        throw new Error('No teacher or admin user found in the database');
      }

      const teacherId = teachers[0].id;

      // Get class sections
      let classSections;
      try {
        const [sections] = await db.query(`
          SELECT cs.id, c.name as class_name, cs.section, t.name as trade_name
          FROM class_sections cs
          JOIN classes c ON cs.class_id = c.id
          LEFT JOIN trades t ON cs.trade_id = t.id
          LIMIT 4
        `);

        if (sections.length > 0) {
          classSections = sections;
        } else {
          throw new Error('No class sections found');
        }
      } catch (error) {
        console.log('Error fetching class sections:', error.message);

        // Use class names as fallback
        classSections = classNames.map((name, index) => ({
          id: index + 1,
          class_name: name,
          section: 'A',
          trade_name: 'General'
        }));
      }

      // Add topics for each subject and class
      for (const subject of subjects) {
        // Get syllabus progress for this subject
        const [progress] = await db.query(`
          SELECT * FROM syllabus_progress
          WHERE teacher_id = ? AND subject_name = ?
        `, [teacherId, subject]);

        if (progress.length === 0) {
          console.log(`No syllabus progress found for ${subject}, skipping topics`);
          continue;
        }

        const totalTopics = progress[0].total_topics;
        const completedTopics = progress[0].completed_topics;

        // Generate topics
        for (let i = 0; i < totalTopics; i++) {
          const classSection = classSections[Math.floor(Math.random() * classSections.length)];
          const className = `Class ${classSection.class_name}-${classSection.section}`;
          const topicName = `${subject} Topic ${i + 1}`;
          const description = `Description for ${topicName}`;

          // Determine status based on completed topics
          const status = i < completedTopics ? 'completed' : 'pending';

          // Generate completion date for completed topics
          let completionDate = null;
          if (status === 'completed') {
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 90)); // Random date in the past 90 days
            completionDate = date.toISOString().split('T')[0];
          }

          await db.query(`
            INSERT INTO teacher_syllabus
            (teacher_id, class_section_id, class_name, subject_name, topic_name, description, status, completion_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            teacherId,
            classSection.id,
            className,
            subject,
            topicName,
            description,
            status,
            completionDate
          ]);

          console.log(`Added syllabus topic: ${subject} - ${topicName} (${status})`);
        }
      }

      console.log('Sample data added to teacher_syllabus table');
    } else {
      console.log(`teacher_syllabus table already has ${topicsCount[0].count} records`);
    }

    console.log('Teacher lectures initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing teacher lectures:', error);
    process.exit(1);
  }
}

// Run the initialization
initTeacherLectures();
