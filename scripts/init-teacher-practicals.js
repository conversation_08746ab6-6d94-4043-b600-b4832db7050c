/**
 * <PERSON><PERSON>t to initialize teacher practicals data
 */

const db = require('../config/database');

async function initTeacherPracticals() {
  try {
    console.log('Starting teacher practicals initialization...');

    // Check if teacher_practicals table exists
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'teacher_practicals'
    `);

    if (tables.length === 0) {
      console.log('Creating teacher_practicals table...');
      
      // Create teacher_practicals table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_practicals (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_section_id INT NOT NULL,
          class_name VARCHAR(100) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          practical_topic VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          venue VARCHAR(100) NOT NULL,
          status ENUM('pending', 'conducted', 'cancelled', 'rescheduled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('teacher_practicals table created successfully');
    } else {
      console.log('teacher_practicals table already exists');
    }

    // Check if student_practical_records table exists
    const [recordTables] = await db.query(`
      SHOW TABLES LIKE 'student_practical_records'
    `);

    if (recordTables.length === 0) {
      console.log('Creating student_practical_records table...');
      
      // Create student_practical_records table
      await db.query(`
        CREATE TABLE IF NOT EXISTS student_practical_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          practical_id INT NOT NULL,
          student_id INT NOT NULL,
          teacher_id INT NOT NULL,
          submission_date DATETIME,
          content TEXT,
          attachment VARCHAR(255),
          grade VARCHAR(10),
          remarks TEXT,
          status ENUM('pending', 'submitted', 'graded') DEFAULT 'pending',
          graded_at DATETIME,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (practical_id) REFERENCES teacher_practicals(id) ON DELETE CASCADE,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('student_practical_records table created successfully');
    } else {
      console.log('student_practical_records table already exists');
    }

    // Check if there's any data in the teacher_practicals table
    const [practicalCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_practicals
    `);

    if (practicalCount[0].count === 0) {
      console.log('Adding sample data to teacher_practicals table...');
      
      // Get a teacher ID (or admin ID if no teacher exists)
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' OR role = 'admin' LIMIT 1
      `);
      
      if (teachers.length === 0) {
        throw new Error('No teacher or admin user found in the database');
      }
      
      const teacherId = teachers[0].id;
      
      // Get class sections
      const [classSections] = await db.query(`
        SELECT cs.id, c.name as class_name, cs.section, t.name as trade_name
        FROM class_sections cs
        JOIN classes c ON cs.class_id = c.id
        LEFT JOIN trades t ON cs.trade_id = t.id
        LIMIT 3
      `);
      
      if (classSections.length === 0) {
        // Create classes, trades, and class sections if they don't exist
        console.log('No class sections found. Creating sample class sections...');
        
        // Create classes
        await db.query(`
          INSERT INTO classes (name, description)
          VALUES 
          ('11', 'Class 11'),
          ('12', 'Class 12')
        `);
        
        // Create trades
        await db.query(`
          INSERT INTO trades (name, description)
          VALUES 
          ('Science', 'Science Stream'),
          ('Commerce', 'Commerce Stream'),
          ('General', 'General Stream')
        `);
        
        // Get class and trade IDs
        const [classes] = await db.query(`SELECT id, name FROM classes`);
        const [trades] = await db.query(`SELECT id, name FROM trades`);
        
        // Create class sections
        for (const cls of classes) {
          for (const trade of trades) {
            await db.query(`
              INSERT INTO class_sections (class_id, trade_id, section)
              VALUES (?, ?, 'A')
            `, [cls.id, trade.id]);
          }
        }
        
        // Get the newly created class sections
        const [newClassSections] = await db.query(`
          SELECT cs.id, c.name as class_name, cs.section, t.name as trade_name
          FROM class_sections cs
          JOIN classes c ON cs.class_id = c.id
          LEFT JOIN trades t ON cs.trade_id = t.id
          LIMIT 3
        `);
        
        if (newClassSections.length === 0) {
          throw new Error('Failed to create class sections');
        }
        
        classSections.push(...newClassSections);
      }
      
      // Add sample practical data
      for (let i = 0; i < classSections.length; i++) {
        const section = classSections[i];
        const className = section.trade_name === 'General' 
          ? `Class ${section.class_name}-${section.section}` 
          : `Class ${section.class_name}-${section.section} (${section.trade_name})`;
        
        // Add practicals with different statuses
        const statuses = ['pending', 'conducted', 'cancelled'];
        const subjects = ['Physics', 'Chemistry', 'Biology', 'Computer Science'];
        const venues = ['Lab 1', 'Lab 2', 'Computer Lab', 'Science Lab'];
        
        for (let j = 0; j < 3; j++) {
          const subject = subjects[Math.floor(Math.random() * subjects.length)];
          const venue = venues[Math.floor(Math.random() * venues.length)];
          const status = statuses[j % statuses.length];
          const date = new Date();
          date.setDate(date.getDate() + j);
          
          const [result] = await db.query(`
            INSERT INTO teacher_practicals 
            (teacher_id, class_section_id, class_name, subject_name, practical_topic, date, start_time, end_time, venue, status, notes) 
            VALUES 
            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            teacherId, 
            section.id, 
            className, 
            subject, 
            `${subject} Practical ${j+1}`, 
            date.toISOString().split('T')[0], 
            '09:00:00', 
            '10:30:00', 
            venue, 
            status, 
            `Notes for ${subject} practical ${j+1}`
          ]);
          
          console.log(`Added practical ID ${result.insertId} for ${className}`);
          
          // Add student records for conducted practicals
          if (status === 'conducted') {
            // Get some students
            const [students] = await db.query(`
              SELECT id FROM users WHERE role = 'student' LIMIT 5
            `);
            
            if (students.length === 0) {
              // Create sample students if none exist
              await db.query(`
                INSERT INTO users (username, password, name, email, role)
                VALUES 
                ('student1', '$2b$10$XpC5nKJ5.NTDXt0afGL1tuNy2IpXPyLTZpHMGV/S6oFuLo5/3nWZO', 'Student One', '<EMAIL>', 'student'),
                ('student2', '$2b$10$XpC5nKJ5.NTDXt0afGL1tuNy2IpXPyLTZpHMGV/S6oFuLo5/3nWZO', 'Student Two', '<EMAIL>', 'student'),
                ('student3', '$2b$10$XpC5nKJ5.NTDXt0afGL1tuNy2IpXPyLTZpHMGV/S6oFuLo5/3nWZO', 'Student Three', '<EMAIL>', 'student')
              `);
              
              const [newStudents] = await db.query(`
                SELECT id FROM users WHERE role = 'student' LIMIT 3
              `);
              
              students.push(...newStudents);
            }
            
            // Add records for each student
            for (const student of students) {
              await db.query(`
                INSERT INTO student_practical_records 
                (practical_id, student_id, teacher_id, submission_date, content, status) 
                VALUES 
                (?, ?, ?, ?, ?, ?)
              `, [
                result.insertId, 
                student.id, 
                teacherId, 
                new Date().toISOString().slice(0, 19).replace('T', ' '), 
                `Submission for ${subject} practical by student ${student.id}`, 
                'submitted'
              ]);
            }
          }
        }
      }
      
      console.log('Sample data added to teacher_practicals table');
    } else {
      console.log(`teacher_practicals table already has ${practicalCount[0].count} records`);
    }

    console.log('Teacher practicals initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing teacher practicals:', error);
    process.exit(1);
  }
}

// Run the initialization
initTeacherPracticals();
