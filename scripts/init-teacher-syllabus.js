/**
 * <PERSON><PERSON>t to initialize teacher syllabus data
 */

const db = require('../config/database');

async function initTeacherSyllabus() {
  try {
    console.log('Starting teacher syllabus initialization...');

    // Check if syllabus_progress table exists
    const [syllabusProgressTables] = await db.query(`
      SHOW TABLES LIKE 'syllabus_progress'
    `);

    if (syllabusProgressTables.length === 0) {
      console.log('Creating syllabus_progress table...');
      
      // Create syllabus_progress table
      await db.query(`
        CREATE TABLE IF NOT EXISTS syllabus_progress (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          completed_topics INT DEFAULT 0,
          total_topics INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('syllabus_progress table created successfully');
    } else {
      console.log('syllabus_progress table already exists');
    }

    // Check if there's any data in the syllabus_progress table
    const [progressCount] = await db.query(`
      SELECT COUNT(*) as count FROM syllabus_progress
    `);

    // Get admin and teacher IDs
    const [users] = await db.query(`
      SELECT id, username, role FROM users WHERE role IN ('admin', 'teacher')
    `);
    
    const adminId = users.find(user => user.role === 'admin')?.id;
    const teacherId = users.find(user => user.role === 'teacher')?.id;
    
    if (!adminId) {
      throw new Error('No admin user found in the database');
    }
    
    console.log(`Using admin ID: ${adminId}, teacher ID: ${teacherId || 'none'}`);

    // Define subjects
    const subjects = ['Physics', 'Chemistry', 'Biology', 'Mathematics', 'Computer Science', 'English', 'Hindi', 'Social Science'];

    if (progressCount[0].count === 0) {
      console.log('Adding sample data to syllabus_progress table...');
      
      // Add progress data for each subject for admin
      for (const subject of subjects) {
        const totalTopics = Math.floor(Math.random() * 10) + 20; // 20-30 topics
        const completedTopics = Math.floor(Math.random() * totalTopics); // 0 to totalTopics
        
        await db.query(`
          INSERT INTO syllabus_progress 
          (teacher_id, subject_name, completed_topics, total_topics) 
          VALUES (?, ?, ?, ?)
        `, [
          adminId,
          subject,
          completedTopics,
          totalTopics
        ]);
        
        console.log(`Added syllabus progress for admin - ${subject}: ${completedTopics}/${totalTopics}`);
      }
      
      // Add progress data for teacher if exists
      if (teacherId) {
        for (const subject of subjects) {
          const totalTopics = Math.floor(Math.random() * 10) + 20; // 20-30 topics
          const completedTopics = Math.floor(Math.random() * totalTopics); // 0 to totalTopics
          
          await db.query(`
            INSERT INTO syllabus_progress 
            (teacher_id, subject_name, completed_topics, total_topics) 
            VALUES (?, ?, ?, ?)
          `, [
            teacherId,
            subject,
            completedTopics,
            totalTopics
          ]);
          
          console.log(`Added syllabus progress for teacher - ${subject}: ${completedTopics}/${totalTopics}`);
        }
      }
      
      console.log('Sample data added to syllabus_progress table');
    } else {
      console.log(`syllabus_progress table already has ${progressCount[0].count} records`);
    }

    // Check if teacher_syllabus table exists
    const [syllabusTopicsTables] = await db.query(`
      SHOW TABLES LIKE 'teacher_syllabus'
    `);

    if (syllabusTopicsTables.length === 0) {
      console.log('Creating teacher_syllabus table...');
      
      // Create teacher_syllabus table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_syllabus (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_section_id INT,
          class_name VARCHAR(100) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          topic_name VARCHAR(255) NOT NULL,
          description TEXT,
          status ENUM('pending', 'completed') DEFAULT 'pending',
          completion_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('teacher_syllabus table created successfully');
    } else {
      console.log('teacher_syllabus table already exists');
    }

    // Check if there's any data in the teacher_syllabus table
    const [topicsCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_syllabus
    `);

    if (topicsCount[0].count === 0) {
      console.log('Adding sample data to teacher_syllabus table...');
      
      // Define class names
      const classNames = ['Class 11-A', 'Class 11-B', 'Class 12-A', 'Class 12-B'];
      
      // Get class sections if they exist
      let classSections;
      try {
        const [sections] = await db.query(`
          SELECT cs.id, c.name as class_name, cs.section, t.name as trade_name
          FROM class_sections cs
          JOIN classes c ON cs.class_id = c.id
          LEFT JOIN trades t ON cs.trade_id = t.id
          LIMIT 4
        `);
        
        if (sections.length > 0) {
          classSections = sections;
        } else {
          throw new Error('No class sections found');
        }
      } catch (error) {
        console.log('Error fetching class sections:', error.message);
        
        // Use class names as fallback
        classSections = classNames.map((name, index) => ({
          id: index + 1,
          class_name: name,
          section: 'A',
          trade_name: 'General'
        }));
      }
      
      // Add topics for each subject and class for admin
      for (const subject of subjects) {
        // Get syllabus progress for this subject
        const [progress] = await db.query(`
          SELECT * FROM syllabus_progress
          WHERE teacher_id = ? AND subject_name = ?
        `, [adminId, subject]);
        
        if (progress.length === 0) {
          console.log(`No syllabus progress found for ${subject}, skipping topics`);
          continue;
        }
        
        const totalTopics = progress[0].total_topics;
        const completedTopics = progress[0].completed_topics;
        
        // Generate topics
        for (let i = 0; i < totalTopics; i++) {
          const classSection = classSections[Math.floor(Math.random() * classSections.length)];
          const className = classSection.class_name + '-' + classSection.section;
          const topicName = `${subject} Topic ${i + 1}`;
          const description = `Description for ${topicName}`;
          
          // Determine status based on completed topics
          const status = i < completedTopics ? 'completed' : 'pending';
          
          // Generate completion date for completed topics
          let completionDate = null;
          if (status === 'completed') {
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 90)); // Random date in the past 90 days
            completionDate = date.toISOString().split('T')[0];
          }
          
          await db.query(`
            INSERT INTO teacher_syllabus 
            (teacher_id, class_section_id, class_name, subject_name, topic_name, description, status, completion_date) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            adminId,
            classSection.id,
            className,
            subject,
            topicName,
            description,
            status,
            completionDate
          ]);
          
          console.log(`Added syllabus topic for admin: ${subject} - ${topicName} (${status})`);
        }
      }
      
      // Add topics for teacher if exists
      if (teacherId) {
        for (const subject of subjects) {
          // Get syllabus progress for this subject
          const [progress] = await db.query(`
            SELECT * FROM syllabus_progress
            WHERE teacher_id = ? AND subject_name = ?
          `, [teacherId, subject]);
          
          if (progress.length === 0) {
            console.log(`No syllabus progress found for teacher's ${subject}, skipping topics`);
            continue;
          }
          
          const totalTopics = progress[0].total_topics;
          const completedTopics = progress[0].completed_topics;
          
          // Generate topics
          for (let i = 0; i < totalTopics; i++) {
            const classSection = classSections[Math.floor(Math.random() * classSections.length)];
            const className = classSection.class_name + '-' + classSection.section;
            const topicName = `${subject} Topic ${i + 1}`;
            const description = `Description for ${topicName}`;
            
            // Determine status based on completed topics
            const status = i < completedTopics ? 'completed' : 'pending';
            
            // Generate completion date for completed topics
            let completionDate = null;
            if (status === 'completed') {
              const date = new Date();
              date.setDate(date.getDate() - Math.floor(Math.random() * 90)); // Random date in the past 90 days
              completionDate = date.toISOString().split('T')[0];
            }
            
            await db.query(`
              INSERT INTO teacher_syllabus 
              (teacher_id, class_section_id, class_name, subject_name, topic_name, description, status, completion_date) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              teacherId,
              classSection.id,
              className,
              subject,
              topicName,
              description,
              status,
              completionDate
            ]);
            
            console.log(`Added syllabus topic for teacher: ${subject} - ${topicName} (${status})`);
          }
        }
      }
      
      console.log('Sample data added to teacher_syllabus table');
    } else {
      console.log(`teacher_syllabus table already has ${topicsCount[0].count} records`);
    }

    console.log('Teacher syllabus initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing teacher syllabus:', error);
    process.exit(1);
  }
}

// Run the initialization
initTeacherSyllabus();
