/**
 * <PERSON><PERSON>t to initialize teacher-related tables and sample data
 */

const db = require('../config/database');

async function initTeacherTables() {
  try {
    console.log('Starting teacher tables initialization...');

    // Check if teacher_lectures table exists
    const [tables] = await db.query(`
      SHOW TABLES LIKE 'teacher_lectures'
    `);

    if (tables.length === 0) {
      console.log('Creating teacher_lectures table...');
      
      // Create teacher_lectures table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_lectures (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_name VARCHAR(50) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          topic VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          room VARCHAR(50),
          status ENUM('pending', 'delivered', 'cancelled', 'rescheduled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('teacher_lectures table created successfully');
    } else {
      console.log('teacher_lectures table already exists');
    }

    // Check if there's any data in the teacher_lectures table
    const [lectureCount] = await db.query(`
      SELECT COUNT(*) as count FROM teacher_lectures
    `);

    if (lectureCount[0].count === 0) {
      console.log('Adding sample data to teacher_lectures table...');
      
      // Get a teacher ID (or admin ID if no teacher exists)
      const [teachers] = await db.query(`
        SELECT id FROM users WHERE role = 'teacher' OR role = 'admin' LIMIT 1
      `);
      
      if (teachers.length === 0) {
        throw new Error('No teacher or admin user found in the database');
      }
      
      const teacherId = teachers[0].id;
      
      // Add sample lecture data
      await db.query(`
        INSERT INTO teacher_lectures 
        (teacher_id, class_name, subject_name, topic, date, start_time, end_time, room, status, notes) 
        VALUES 
        (?, 'Class 11', 'Computer Science', 'Introduction to Programming', CURDATE(), '08:00:00', '08:45:00', 'Lab 1', 'delivered', 'Covered basics of programming concepts'),
        (?, 'Class 12', 'Computer Science', 'Data Structures', CURDATE(), '08:00:00', '08:45:00', 'Room 103', 'pending', 'Will cover arrays, linked lists, and stacks'),
        (?, 'Class 11', 'Computer Science', 'Control Structures', CURDATE(), '08:00:00', '08:45:00', 'Lab 2', 'pending', 'Will cover if-else, loops, and switch statements'),
        (?, 'Class 12', 'Computer Science', 'Object-Oriented Programming', CURDATE(), '08:00:00', '08:45:00', 'Lab 1', 'rescheduled', 'Rescheduled due to lab maintenance'),
        (?, 'Class 11', 'Computer Science', 'Functions and Methods', CURDATE(), '08:45:00', '09:30:00', 'Room 101', 'pending', 'Will cover function declaration, parameters, and return values'),
        (?, 'Class 12', 'Computer Science', 'Advanced Data Structures', CURDATE(), '08:45:00', '09:30:00', 'Lab 2', 'delivered', 'Covered trees, graphs, and hash tables'),
        (?, 'Class 11', 'Computer Science', 'File Handling', CURDATE(), '08:45:00', '09:30:00', 'Room 102', 'pending', 'Will cover file operations, reading, and writing')
      `, [teacherId, teacherId, teacherId, teacherId, teacherId, teacherId, teacherId]);
      
      console.log('Sample data added to teacher_lectures table');
    } else {
      console.log(`teacher_lectures table already has ${lectureCount[0].count} records`);
    }

    // Check if teacher_practicals table exists
    const [practicalTables] = await db.query(`
      SHOW TABLES LIKE 'teacher_practicals'
    `);

    if (practicalTables.length === 0) {
      console.log('Creating teacher_practicals table...');
      
      // Create teacher_practicals table
      await db.query(`
        CREATE TABLE IF NOT EXISTS teacher_practicals (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          class_name VARCHAR(50) NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          practical_topic VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          lab VARCHAR(50),
          status ENUM('pending', 'conducted', 'cancelled', 'rescheduled') DEFAULT 'pending',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('teacher_practicals table created successfully');
    } else {
      console.log('teacher_practicals table already exists');
    }

    // Check if syllabus_progress table exists
    const [syllabusProgressTables] = await db.query(`
      SHOW TABLES LIKE 'syllabus_progress'
    `);

    if (syllabusProgressTables.length === 0) {
      console.log('Creating syllabus_progress table...');
      
      // Create syllabus_progress table
      await db.query(`
        CREATE TABLE IF NOT EXISTS syllabus_progress (
          id INT AUTO_INCREMENT PRIMARY KEY,
          teacher_id INT NOT NULL,
          subject_name VARCHAR(100) NOT NULL,
          completed_topics INT DEFAULT 0,
          total_topics INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log('syllabus_progress table created successfully');
    } else {
      console.log('syllabus_progress table already exists');
    }

    console.log('Teacher tables initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing teacher tables:', error);
    process.exit(1);
  }
}

// Run the initialization
initTeacherTables();
