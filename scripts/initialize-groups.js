/**
 * <PERSON><PERSON><PERSON> to initialize default role-based groups
 * Run this script after setting up the database tables
 */
const db = require('../config/database');
const groupController = require('../controllers/group-controller');

async function initializeGroups() {
    try {
        console.log('Starting group initialization...');

        // Create default role-based groups
        const result = await groupController.createDefaultGroups();

        if (result) {
            console.log('Default role-based groups created successfully!');
        } else {
            console.error('Failed to create default role-based groups');
        }

        // No need to close the connection as we're using a pool
        console.log('Script completed');

    } catch (error) {
        console.error('Error initializing groups:', error);
        process.exit(1);
    }
}

// Run the initialization
initializeGroups();
