/**
 * <PERSON><PERSON><PERSON> to initialize help content with some default articles
 * Run this script after setting up the database tables
 */
const db = require('../config/database');

const helpArticles = [
    {
        title: 'Getting Started with Meritorious EP',
        slug: 'getting-started',
        content: `
            <h2>Welcome to Meritorious Exam Preparation Platform!</h2>
            <p>This guide will help you get started with the platform and understand its key features.</p>

            <h3>What is Meritorious EP?</h3>
            <p>Meritorious EP is an exam preparation platform designed to help students prepare for various exams through practice tests, study materials, and performance tracking.</p>

            <h3>Key Features</h3>
            <ul>
                <li><strong>Practice Tests:</strong> Take practice tests in various subjects and topics</li>
                <li><strong>Performance Tracking:</strong> Track your progress and identify areas for improvement</li>
                <li><strong>Study Groups:</strong> Join or create study groups to collaborate with others</li>
                <li><strong>Personalized Learning:</strong> Get recommendations based on your performance</li>
            </ul>

            <h3>Next Steps</h3>
            <p>To get started, we recommend:</p>
            <ol>
                <li>Complete your profile</li>
                <li>Browse available tests</li>
                <li>Join a study group</li>
                <li>Take your first practice test</li>
            </ol>
        `,
        category: 'Getting Started',
        is_published: true
    },
    {
        title: 'How to Join a Group',
        slug: 'how-to-join-a-group',
        content: `
            <h2>Joining a Group</h2>
            <p>Groups in Meritorious EP allow you to collaborate with other students, share resources, and participate in group study sessions.</p>

            <h3>Finding Groups</h3>
            <p>To find and join a group:</p>
            <ol>
                <li>Click on the "Groups" link in the main navigation</li>
                <li>Browse the list of available groups</li>
                <li>Click on a group to view its details</li>
                <li>Click the "Join Group" button</li>
            </ol>

            <h3>Role-Based Groups</h3>
            <p>The system automatically adds you to groups based on your role (student, teacher, etc.). These system groups help organize users and provide relevant content.</p>

            <h3>Group Activities</h3>
            <p>Once you've joined a group, you can:</p>
            <ul>
                <li>View assigned exams</li>
                <li>Interact with other group members</li>
                <li>Access group-specific resources</li>
                <li>Participate in discussions</li>
            </ul>

            <h3>Creating Your Own Group</h3>
            <p>If you can't find a suitable group, you can create your own:</p>
            <ol>
                <li>Go to the Groups page</li>
                <li>Click "Create New Group"</li>
                <li>Fill in the group details</li>
                <li>Invite members to join</li>
            </ol>
        `,
        category: 'Groups',
        is_published: true
    },
    {
        title: 'Taking Your First Test',
        slug: 'taking-your-first-test',
        content: `
            <h2>Taking Your First Test</h2>
            <p>Practice tests are a core feature of Meritorious EP. This guide will walk you through taking your first test.</p>

            <h3>Finding Tests</h3>
            <p>To find available tests:</p>
            <ol>
                <li>Click on "Tests" in the main navigation</li>
                <li>Browse the list of available tests</li>
                <li>Use filters to narrow down by subject, difficulty, or category</li>
                <li>Click on a test to view details</li>
            </ol>

            <h3>Starting a Test</h3>
            <p>To start a test:</p>
            <ol>
                <li>Click the "Take Test" button on the test details page</li>
                <li>Read the instructions carefully</li>
                <li>Note the time limit (if applicable)</li>
                <li>Click "Start Test" when ready</li>
            </ol>

            <h3>During the Test</h3>
            <p>While taking a test:</p>
            <ul>
                <li>Answer questions by selecting options or typing responses</li>
                <li>Use the navigation to move between questions</li>
                <li>Mark questions for review if you're unsure</li>
                <li>Keep an eye on the timer (if applicable)</li>
            </ul>

            <h3>Submitting Your Test</h3>
            <p>When you're finished:</p>
            <ol>
                <li>Review your answers</li>
                <li>Check for unanswered questions</li>
                <li>Click "Submit Test" when ready</li>
                <li>Confirm your submission</li>
            </ol>

            <h3>Viewing Results</h3>
            <p>After submission:</p>
            <ul>
                <li>View your score and performance</li>
                <li>See correct answers and explanations</li>
                <li>Identify areas for improvement</li>
                <li>Track your progress over time</li>
            </ul>
        `,
        category: 'Exams',
        is_published: true
    },
    {
        title: 'Account Management',
        slug: 'account-management',
        content: `
            <h2>Managing Your Account</h2>
            <p>This guide covers how to manage your account settings, profile, and preferences.</p>

            <h3>Updating Your Profile</h3>
            <p>To update your profile information:</p>
            <ol>
                <li>Click on your username in the top-right corner</li>
                <li>Select "Profile" from the dropdown menu</li>
                <li>Click "Edit Profile"</li>
                <li>Update your information</li>
                <li>Click "Save Changes"</li>
            </ol>

            <h3>Changing Your Password</h3>
            <p>To change your password:</p>
            <ol>
                <li>Go to your profile page</li>
                <li>Click "Change Password"</li>
                <li>Enter your current password</li>
                <li>Enter and confirm your new password</li>
                <li>Click "Update Password"</li>
            </ol>

            <h3>Notification Settings</h3>
            <p>To manage your notification preferences:</p>
            <ol>
                <li>Go to your profile page</li>
                <li>Click "Notification Settings"</li>
                <li>Toggle notifications on/off</li>
                <li>Select delivery methods (email, in-app)</li>
                <li>Save your preferences</li>
            </ol>

            <h3>Language Preferences</h3>
            <p>To change the interface language:</p>
            <ol>
                <li>Click on the language selector in the footer</li>
                <li>Choose your preferred language</li>
                <li>The interface will update immediately</li>
            </ol>
        `,
        category: 'Account Management',
        is_published: true
    },
    {
        title: 'For Administrators: Managing Users',
        slug: 'admin-managing-users',
        content: `
            <h2>Administrator Guide: Managing Users</h2>
            <p>This guide is for system administrators and covers user management tasks.</p>

            <h3>Viewing Users</h3>
            <p>To view and manage users:</p>
            <ol>
                <li>Log in with your administrator account</li>
                <li>Go to the Admin Dashboard</li>
                <li>Click "Users" in the admin navigation</li>
                <li>Browse the list of users</li>
            </ol>

            <h3>Creating New Users</h3>
            <p>To create a new user:</p>
            <ol>
                <li>Go to the Users page</li>
                <li>Click "Add User"</li>
                <li>Fill in the user details</li>
                <li>Assign a role (student, teacher, admin)</li>
                <li>Click "Create User"</li>
            </ol>

            <h3>Editing User Details</h3>
            <p>To edit a user's information:</p>
            <ol>
                <li>Find the user in the users list</li>
                <li>Click "Edit" for that user</li>
                <li>Update the user's information</li>
                <li>Click "Save Changes"</li>
            </ol>

            <h3>Managing User Roles</h3>
            <p>To change a user's role:</p>
            <ol>
                <li>Edit the user</li>
                <li>Change the role dropdown</li>
                <li>Save the changes</li>
                <li>The user will automatically be added to the appropriate role-based group</li>
            </ol>

            <h3>Deactivating Users</h3>
            <p>To deactivate a user:</p>
            <ol>
                <li>Find the user in the users list</li>
                <li>Click "Deactivate" for that user</li>
                <li>Confirm the deactivation</li>
            </ol>
            <p>Deactivated users cannot log in but their data is preserved.</p>
        `,
        category: 'For Administrators',
        is_published: true
    }
];

async function initializeHelpContent() {
    try {
        console.log('Starting help content initialization...');

        // Get admin user for created_by field
        const [adminUsers] = await db.query(`
            SELECT id FROM users
            WHERE role = 'admin'
            ORDER BY id ASC
            LIMIT 1
        `);

        const adminId = adminUsers.length > 0 ? adminUsers[0].id : null;

        // Get categories
        const [categories] = await db.query(`
            SELECT * FROM help_categories
        `);

        const categoryMap = {};
        categories.forEach(category => {
            categoryMap[category.name] = category.category_id;
        });

        // Insert articles
        for (const article of helpArticles) {
            console.log(`Creating article: ${article.title}`);

            // Insert article
            const [result] = await db.query(`
                INSERT INTO help_articles (
                    title, slug, content, is_published, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            `, [
                article.title,
                article.slug,
                article.content,
                article.is_published ? 1 : 0,
                adminId
            ]);

            const articleId = result.insertId;

            // Add category mapping if category exists
            if (article.category && categoryMap[article.category]) {
                await db.query(`
                    INSERT INTO help_article_categories (article_id, category_id)
                    VALUES (?, ?)
                `, [articleId, categoryMap[article.category]]);
            }
        }

        console.log('Help content initialized successfully!');

        // No need to close the connection as we're using a pool
        console.log('Script completed');

    } catch (error) {
        console.error('Error initializing help content:', error);
        process.exit(1);
    }
}

// Run the initialization
initializeHelpContent();
