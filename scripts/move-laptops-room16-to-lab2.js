/**
 * <PERSON><PERSON><PERSON> to move laptops from Room 16 to Computer Lab 2
 * This script will:
 * 1. Find all laptops currently in Room 16
 * 2. Update their location to Computer Lab 2
 * 3. Update room_id if Computer Lab 2 exists in rooms table
 */

const db = require('../config/database');

async function moveLaptopsToComputerLab2() {
    try {
        console.log('🔄 Starting laptop relocation from Room 16 to Computer Lab 2...\n');

        // 1. Check current laptops in Room 16
        console.log('1. Checking current laptops in Room 16...');
        
        const [laptopsInRoom16] = await db.query(`
            SELECT id, name, type, serial_number, model, manufacturer, location, status, room_id
            FROM it_inventory 
            WHERE (location LIKE '%Room 16%' OR location = 'Room 16') 
            AND type = 'laptop'
            ORDER BY name
        `);

        if (laptopsInRoom16.length === 0) {
            console.log('   ℹ️  No laptops found in Room 16');
            return;
        }

        console.log(`   📱 Found ${laptopsInRoom16.length} laptop(s) in Room 16:`);
        laptopsInRoom16.forEach(laptop => {
            console.log(`      - ${laptop.name} (${laptop.serial_number || 'No SN'}) - Status: ${laptop.status}`);
        });

        // 2. Check if Computer Lab 2 exists in rooms table
        console.log('\n2. Checking Computer Lab 2 room information...');
        
        const [computerLab2] = await db.query(`
            SELECT id, room_number, building, floor, capacity
            FROM rooms 
            WHERE room_number = 'Computer Lab 2'
        `);

        let computerLab2RoomId = null;
        if (computerLab2.length > 0) {
            computerLab2RoomId = computerLab2[0].id;
            console.log(`   🏫 Computer Lab 2 found: ID ${computerLab2RoomId}, Building: ${computerLab2[0].building}, Floor: ${computerLab2[0].floor}`);
        } else {
            console.log('   ⚠️  Computer Lab 2 not found in rooms table - will update location only');
        }

        // 3. Check current equipment in Computer Lab 2
        console.log('\n3. Checking current equipment in Computer Lab 2...');
        
        const [currentEquipment] = await db.query(`
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN type = 'laptop' THEN 1 END) as laptop_count,
                   COUNT(CASE WHEN type = 'desktop' THEN 1 END) as desktop_count
            FROM it_inventory 
            WHERE location = 'Computer Lab 2'
        `);

        console.log(`   📊 Current equipment in Computer Lab 2:`);
        console.log(`      - Total items: ${currentEquipment[0].total_count}`);
        console.log(`      - Laptops: ${currentEquipment[0].laptop_count}`);
        console.log(`      - Desktops: ${currentEquipment[0].desktop_count}`);

        // 4. Confirm the move
        console.log('\n4. Preparing to move laptops...');
        console.log(`   📦 Moving ${laptopsInRoom16.length} laptop(s) from Room 16 to Computer Lab 2`);

        // 5. Perform the update
        console.log('\n5. Updating laptop locations...');
        
        let successCount = 0;
        let errorCount = 0;

        for (const laptop of laptopsInRoom16) {
            try {
                // Update location and room_id if available
                if (computerLab2RoomId) {
                    await db.query(`
                        UPDATE it_inventory 
                        SET location = 'Computer Lab 2', 
                            room_id = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    `, [computerLab2RoomId, laptop.id]);
                } else {
                    await db.query(`
                        UPDATE it_inventory 
                        SET location = 'Computer Lab 2',
                            updated_at = NOW()
                        WHERE id = ?
                    `, [laptop.id]);
                }

                console.log(`   ✅ ${laptop.name} (${laptop.serial_number || 'No SN'}): Room 16 → Computer Lab 2`);
                successCount++;

            } catch (error) {
                console.log(`   ❌ Failed to move ${laptop.name}: ${error.message}`);
                errorCount++;
            }
        }

        // 6. Verify the move
        console.log('\n6. Verifying the move...');
        
        const [verifyRoom16] = await db.query(`
            SELECT COUNT(*) as remaining_laptops
            FROM it_inventory 
            WHERE (location LIKE '%Room 16%' OR location = 'Room 16') 
            AND type = 'laptop'
        `);

        const [verifyComputerLab2] = await db.query(`
            SELECT COUNT(*) as total_laptops
            FROM it_inventory 
            WHERE location = 'Computer Lab 2' 
            AND type = 'laptop'
        `);

        console.log(`   📊 Verification Results:`);
        console.log(`      - Laptops remaining in Room 16: ${verifyRoom16[0].remaining_laptops}`);
        console.log(`      - Total laptops now in Computer Lab 2: ${verifyComputerLab2[0].total_laptops}`);
        console.log(`      - Successfully moved: ${successCount}`);
        console.log(`      - Errors: ${errorCount}`);

        // 7. Show updated Computer Lab 2 equipment summary
        console.log('\n7. Updated Computer Lab 2 equipment summary...');
        
        const [updatedEquipment] = await db.query(`
            SELECT 
                type,
                COUNT(*) as count,
                GROUP_CONCAT(DISTINCT status) as statuses
            FROM it_inventory 
            WHERE location = 'Computer Lab 2'
            GROUP BY type
            ORDER BY type
        `);

        console.log(`   📋 Equipment in Computer Lab 2 after move:`);
        updatedEquipment.forEach(equipment => {
            console.log(`      - ${equipment.type}: ${equipment.count} (Status: ${equipment.statuses})`);
        });

        console.log('\n✅ Laptop relocation completed successfully!');

    } catch (error) {
        console.error('❌ Error during laptop relocation:', error);
        throw error;
    }
}

// Run the script
if (require.main === module) {
    moveLaptopsToComputerLab2()
        .then(() => {
            console.log('\n🎉 Script completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Script failed:', error);
            process.exit(1);
        });
}

module.exports = { moveLaptopsToComputerLab2 };
