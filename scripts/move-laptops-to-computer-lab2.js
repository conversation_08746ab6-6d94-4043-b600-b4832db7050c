/**
 * <PERSON><PERSON><PERSON> to move laptops to Computer Lab 2
 * This script will move laptops from IT Department or other locations to Computer Lab 2
 */

const db = require('../config/database');

async function moveLaptopsToComputerLab2() {
    try {
        console.log('🔄 Starting laptop relocation to Computer Lab 2...\n');

        // 1. Check available laptops that can be moved
        console.log('1. Checking available laptops for relocation...');

        const [availableLaptops] = await db.query(`
            SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer, i.location, i.status, i.room_id
            FROM inventory_items i
            LEFT JOIN inventory_categories c ON i.category_id = c.category_id
            WHERE (c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
            AND i.status = 'available'
            AND i.location != 'Computer Lab 2'
            ORDER BY i.location, i.name
        `);

        if (availableLaptops.length === 0) {
            console.log('   ℹ️  No available laptops found for relocation');
            return;
        }

        console.log(`   📱 Found ${availableLaptops.length} available laptop(s) that can be moved:`);
        availableLaptops.forEach((laptop, index) => {
            console.log(`      ${index + 1}. ${laptop.name} (${laptop.serial_number || 'No SN'}) - Current Location: ${laptop.location || 'No location'}`);
        });

        // 2. Check if Computer Lab 2 exists in rooms table
        console.log('\n2. Checking Computer Lab 2 room information...');

        const [computerLab2] = await db.query(`
            SELECT id, room_number, building, floor, capacity
            FROM rooms
            WHERE room_number = 'Computer Lab 2'
        `);

        let computerLab2RoomId = null;
        if (computerLab2.length > 0) {
            computerLab2RoomId = computerLab2[0].id;
            console.log(`   🏫 Computer Lab 2 found: ID ${computerLab2RoomId}, Building: ${computerLab2[0].building}, Floor: ${computerLab2[0].floor}, Capacity: ${computerLab2[0].capacity}`);
        } else {
            console.log('   ⚠️  Computer Lab 2 not found in rooms table - will update location only');
        }

        // 3. Check current equipment in Computer Lab 2
        console.log('\n3. Checking current equipment in Computer Lab 2...');

        const [currentEquipment] = await db.query(`
            SELECT
                COUNT(*) as total_count,
                COUNT(CASE WHEN c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%' THEN 1 END) as laptop_count,
                COUNT(CASE WHEN c.name LIKE '%Desktop%' OR i.name LIKE '%PC%' THEN 1 END) as desktop_count
            FROM inventory_items i
            LEFT JOIN inventory_categories c ON i.category_id = c.category_id
            WHERE i.location = 'Computer Lab 2'
        `);

        console.log(`   📊 Current equipment in Computer Lab 2:`);
        console.log(`      - Total items: ${currentEquipment[0].total_count}`);
        console.log(`      - Laptops: ${currentEquipment[0].laptop_count}`);
        console.log(`      - Desktops: ${currentEquipment[0].desktop_count}`);

        // 4. Ask user how many laptops to move (for now, let's move 5 laptops)
        const laptopsToMove = Math.min(5, availableLaptops.length);
        const selectedLaptops = availableLaptops.slice(0, laptopsToMove);

        console.log(`\n4. Preparing to move ${laptopsToMove} laptop(s) to Computer Lab 2:`);
        selectedLaptops.forEach((laptop, index) => {
            console.log(`   ${index + 1}. ${laptop.name} (${laptop.serial_number || 'No SN'}) from ${laptop.location}`);
        });

        // 5. Perform the update
        console.log('\n5. Updating laptop locations...');

        let successCount = 0;
        let errorCount = 0;

        for (const laptop of selectedLaptops) {
            try {
                // Update location and room_id if available
                if (computerLab2RoomId) {
                    await db.query(`
                        UPDATE inventory_items
                        SET location = 'Computer Lab 2',
                            room_id = ?,
                            updated_at = NOW()
                        WHERE item_id = ?
                    `, [computerLab2RoomId, laptop.item_id]);
                } else {
                    await db.query(`
                        UPDATE inventory_items
                        SET location = 'Computer Lab 2',
                            updated_at = NOW()
                        WHERE item_id = ?
                    `, [laptop.item_id]);
                }

                console.log(`   ✅ ${laptop.name} (${laptop.serial_number || 'No SN'}): ${laptop.location} → Computer Lab 2`);
                successCount++;

            } catch (error) {
                console.log(`   ❌ Failed to move ${laptop.name}: ${error.message}`);
                errorCount++;
            }
        }

        // 6. Verify the move
        console.log('\n6. Verifying the move...');

        const [verifyComputerLab2] = await db.query(`
            SELECT COUNT(*) as total_laptops
            FROM inventory_items i
            LEFT JOIN inventory_categories c ON i.category_id = c.category_id
            WHERE i.location = 'Computer Lab 2'
            AND (c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%')
        `);

        console.log(`   📊 Verification Results:`);
        console.log(`      - Total laptops now in Computer Lab 2: ${verifyComputerLab2[0].total_laptops}`);
        console.log(`      - Successfully moved: ${successCount}`);
        console.log(`      - Errors: ${errorCount}`);

        console.log('\n✅ Laptop relocation completed successfully!');

    } catch (error) {
        console.error('❌ Error during laptop relocation:', error);
        throw error;
    }
}

// Run the script
if (require.main === module) {
    moveLaptopsToComputerLab2()
        .then(() => {
            console.log('\n🎉 Script completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Script failed:', error);
            process.exit(1);
        });
}

module.exports = { moveLaptopsToComputerLab2 };
