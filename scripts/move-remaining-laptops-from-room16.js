/**
 * <PERSON><PERSON><PERSON> to move the remaining laptops from Room 16 to Computer Lab 2
 * These are the laptops that are still showing in the principal infrastructure view
 */

const db = require('../config/database');

async function moveRemainingLaptopsFromRoom16() {
    try {
        console.log('🔄 Moving remaining laptops from Room 16 to Computer Lab 2...\n');

        // 1. Find Room 16 and Computer Lab 2 IDs
        console.log('1. Finding room IDs...');
        
        const [room16] = await db.query(`
            SELECT id, room_number FROM rooms WHERE room_number = 'Room 16'
        `);
        
        const [computerLab2] = await db.query(`
            SELECT id, room_number FROM rooms WHERE room_number = 'Computer Lab 2'
        `);

        if (room16.length === 0) {
            console.log('   ❌ Room 16 not found');
            return;
        }

        if (computerLab2.length === 0) {
            console.log('   ❌ Computer Lab 2 not found');
            return;
        }

        const room16Id = room16[0].id;
        const computerLab2Id = computerLab2[0].id;

        console.log(`   ✅ Room 16 ID: ${room16Id}`);
        console.log(`   ✅ Computer Lab 2 ID: ${computerLab2Id}`);

        // 2. Find all laptops currently in Room 16
        console.log('\n2. Finding laptops in Room 16...');
        
        const [laptopsInRoom16] = await db.query(`
            SELECT
                i.item_id,
                i.name,
                i.serial_number,
                i.status,
                i.location,
                i.room_id
            FROM inventory_items i
            WHERE i.room_id = ?
            AND (i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
            ORDER BY i.name
        `, [room16Id]);

        if (laptopsInRoom16.length === 0) {
            console.log('   ✅ No laptops found in Room 16 - already moved!');
            return;
        }

        console.log(`   📱 Found ${laptopsInRoom16.length} laptop(s) in Room 16:`);
        laptopsInRoom16.forEach((laptop, index) => {
            console.log(`      ${index + 1}. ${laptop.name} (${laptop.serial_number}) - Status: ${laptop.status}`);
        });

        // 3. Check current laptops in Computer Lab 2
        console.log('\n3. Checking current laptops in Computer Lab 2...');
        
        const [currentLaptopsInLab2] = await db.query(`
            SELECT COUNT(*) as laptop_count
            FROM inventory_items i
            WHERE i.room_id = ?
            AND (i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
        `, [computerLab2Id]);

        console.log(`   📊 Current laptops in Computer Lab 2: ${currentLaptopsInLab2[0].laptop_count}`);

        // 4. Move the laptops
        console.log('\n4. Moving laptops from Room 16 to Computer Lab 2...');
        
        let successCount = 0;
        let errorCount = 0;

        for (const laptop of laptopsInRoom16) {
            try {
                await db.query(`
                    UPDATE inventory_items 
                    SET location = 'Computer Lab 2', 
                        room_id = ?,
                        updated_at = NOW()
                    WHERE item_id = ?
                `, [computerLab2Id, laptop.item_id]);

                console.log(`   ✅ ${laptop.name} (${laptop.serial_number}): Room 16 → Computer Lab 2`);
                successCount++;

            } catch (error) {
                console.log(`   ❌ Failed to move ${laptop.name}: ${error.message}`);
                errorCount++;
            }
        }

        // 5. Verify the move
        console.log('\n5. Verifying the move...');
        
        const [verifyRoom16] = await db.query(`
            SELECT COUNT(*) as remaining_laptops
            FROM inventory_items i
            WHERE i.room_id = ?
            AND (i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
        `, [room16Id]);

        const [verifyComputerLab2] = await db.query(`
            SELECT COUNT(*) as total_laptops
            FROM inventory_items i
            WHERE i.room_id = ?
            AND (i.name LIKE '%Laptop%' OR i.name LIKE '%laptop%')
        `, [computerLab2Id]);

        console.log(`   📊 Verification Results:`);
        console.log(`      - Laptops remaining in Room 16: ${verifyRoom16[0].remaining_laptops}`);
        console.log(`      - Total laptops now in Computer Lab 2: ${verifyComputerLab2[0].total_laptops}`);
        console.log(`      - Successfully moved: ${successCount}`);
        console.log(`      - Errors: ${errorCount}`);

        // 6. Check what's left in Room 16
        console.log('\n6. Checking remaining equipment in Room 16...');
        
        const [remainingEquipment] = await db.query(`
            SELECT
                i.name,
                CASE
                    WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
                    WHEN i.name LIKE '%UPS%' THEN 'ups'
                    WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
                    WHEN i.name LIKE '%Laptop%' THEN 'laptop'
                    WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
                    ELSE 'other'
                END as item_type,
                i.status
            FROM inventory_items i
            WHERE i.room_id = ?
            ORDER BY item_type, i.name
        `, [room16Id]);

        if (remainingEquipment.length > 0) {
            console.log(`   📋 Remaining equipment in Room 16 (${remainingEquipment.length} items):`);
            const equipmentByType = {};
            remainingEquipment.forEach(item => {
                if (!equipmentByType[item.item_type]) {
                    equipmentByType[item.item_type] = 0;
                }
                equipmentByType[item.item_type]++;
            });
            
            Object.entries(equipmentByType).forEach(([type, count]) => {
                console.log(`      - ${type}: ${count} item(s)`);
            });
        } else {
            console.log(`   ✅ No equipment remaining in Room 16`);
        }

        console.log('\n✅ Laptop move operation completed successfully!');
        
        if (verifyRoom16[0].remaining_laptops === 0) {
            console.log('🎉 All laptops have been successfully moved from Room 16!');
            console.log('   The principal infrastructure view should no longer show laptops in Room 16.');
        } else {
            console.log(`⚠️  ${verifyRoom16[0].remaining_laptops} laptop(s) still remain in Room 16.`);
        }

    } catch (error) {
        console.error('❌ Error during laptop move operation:', error);
        throw error;
    }
}

// Run the script
if (require.main === module) {
    moveRemainingLaptopsFromRoom16()
        .then(() => {
            console.log('\n🎉 Script completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Script failed:', error);
            process.exit(1);
        });
}

module.exports = { moveRemainingLaptopsFromRoom16 };
