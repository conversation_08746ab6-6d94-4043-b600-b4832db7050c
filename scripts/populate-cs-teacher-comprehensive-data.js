const mysql = require('mysql2/promise');

async function populateCSTeacherData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Populating comprehensive CS Teacher data...');

        // Get CS teacher user and staff IDs
        const [userResult] = await connection.execute(
            'SELECT u.id as user_id, s.id as staff_id FROM users u JOIN staff s ON u.id = s.user_id WHERE u.email = ?',
            ['<EMAIL>']
        );

        if (userResult.length === 0) {
            console.log('CS Teacher not found');
            return;
        }

        const { user_id: userId, staff_id: staffId } = userResult[0];
        console.log('CS Teacher - User ID:', userId, 'Staff ID:', staffId);

        // Clear existing data
        await connection.execute('DELETE FROM staff_educational_qualifications WHERE staff_id = ?', [staffId]);
        await connection.execute('DELETE FROM staff_professional_experience WHERE staff_id = ?', [staffId]);
        await connection.execute('DELETE FROM staff_certifications WHERE staff_id = ?', [staffId]);
        await connection.execute('DELETE FROM staff_skills WHERE staff_id = ?', [staffId]);

        // =====================================================
        // 1. EDUCATIONAL QUALIFICATIONS
        // =====================================================
        console.log('\n1. Adding Educational Qualifications...');

        // Class 10th
        await connection.execute(`
            INSERT INTO staff_educational_qualifications (
                staff_id, qualification_level, qualification_name, specialization,
                institution_name, university_board, subjects, total_marks_obtained,
                total_marks_maximum, percentage, grade, start_year, completion_year,
                achievements, verification_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, '10th', 'Secondary School Certificate', 'Science',
            'Government High School, Ludhiana', 'Punjab School Education Board',
            JSON.stringify({
                "Mathematics": {"marks": 92, "total": 100},
                "Science": {"marks": 88, "total": 100},
                "English": {"marks": 85, "total": 100},
                "Hindi": {"marks": 90, "total": 100},
                "Social Science": {"marks": 87, "total": 100}
            }),
            442, 500, 88.40, 'A+', 2006, 2007,
            'School topper in Mathematics, Science Olympiad participant',
            'verified'
        ]);

        // Class 12th
        await connection.execute(`
            INSERT INTO staff_educational_qualifications (
                staff_id, qualification_level, qualification_name, specialization,
                institution_name, university_board, subjects, total_marks_obtained,
                total_marks_maximum, percentage, grade, start_year, completion_year,
                achievements, verification_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, '12th', 'Higher Secondary Certificate', 'Science (Non-Medical)',
            'DAV College, Ludhiana', 'Punjab School Education Board',
            JSON.stringify({
                "Mathematics": {"marks": 95, "total": 100},
                "Physics": {"marks": 91, "total": 100},
                "Chemistry": {"marks": 89, "total": 100},
                "Computer Science": {"marks": 96, "total": 100},
                "English": {"marks": 88, "total": 100}
            }),
            459, 500, 91.80, 'A+', 2007, 2009,
            'District rank 15, Computer Science subject topper',
            'verified'
        ]);

        // B.Tech
        await connection.execute(`
            INSERT INTO staff_educational_qualifications (
                staff_id, qualification_level, qualification_name, specialization,
                institution_name, university_board, subjects, total_marks_obtained,
                total_marks_maximum, percentage, grade, cgpa, start_year, completion_year,
                thesis_title, achievements, verification_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'graduation', 'Bachelor of Technology', 'Computer Science and Engineering',
            'Punjab Technical University', 'Punjab Technical University',
            JSON.stringify({
                "Data Structures": {"marks": 89, "total": 100},
                "Algorithms": {"marks": 92, "total": 100},
                "Database Management": {"marks": 88, "total": 100},
                "Software Engineering": {"marks": 90, "total": 100},
                "Computer Networks": {"marks": 87, "total": 100},
                "Operating Systems": {"marks": 91, "total": 100},
                "Programming Languages": {"marks": 94, "total": 100},
                "Web Technologies": {"marks": 86, "total": 100}
            }),
            2847, 3200, 88.97, 'A', 8.9, 2007, 2011,
            'Student Management System using Java and MySQL',
            'Dean\'s List for 3 semesters, Best Project Award 2011, Technical Society President',
            'verified'
        ]);

        // =====================================================
        // 2. PROFESSIONAL EXPERIENCE
        // =====================================================
        console.log('\n2. Adding Professional Experience...');

        // Software Engineer (2011-2013)
        await connection.execute(`
            INSERT INTO staff_professional_experience (
                staff_id, job_title, department, employment_type, job_category,
                organization_name, organization_type, organization_location,
                start_date, end_date, is_current, total_duration_months,
                job_description, key_responsibilities, achievements, skills_used,
                salary_range, performance_rating, reason_for_leaving,
                supervisor_name, supervisor_contact, reference_available
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'Software Engineer', 'Development', 'full_time', 'industry',
            'TechSolutions Pvt Ltd', 'company', 'Chandigarh, Punjab',
            '2011-06-15', '2013-10-31', false, 28,
            'Developed web applications and desktop software solutions for clients in education and healthcare sectors.',
            JSON.stringify([
                "Developed Java-based web applications using Spring framework",
                "Designed and implemented MySQL database schemas",
                "Created responsive front-end interfaces using HTML, CSS, JavaScript",
                "Participated in code reviews and testing processes",
                "Collaborated with cross-functional teams for project delivery"
            ]),
            JSON.stringify([
                "Successfully delivered 8 client projects on time",
                "Reduced application load time by 40% through optimization",
                "Mentored 2 junior developers",
                "Implemented automated testing reducing bugs by 60%"
            ]),
            JSON.stringify([
                "Java", "Spring Framework", "MySQL", "HTML", "CSS", "JavaScript",
                "Git", "Eclipse IDE", "Agile Development", "Team Collaboration"
            ]),
            '25000-35000', 'good', 'Career transition to education sector',
            'Rajesh Kumar', '<EMAIL>', true
        ]);

        // Physics Tutor (2013-2015)
        await connection.execute(`
            INSERT INTO staff_professional_experience (
                staff_id, job_title, department, employment_type, job_category,
                organization_name, organization_type, organization_location,
                start_date, end_date, is_current, total_duration_months,
                job_description, key_responsibilities, achievements, skills_used,
                salary_range, performance_rating, reason_for_leaving,
                supervisor_name, supervisor_contact, reference_available
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'Physics Tutor', 'Academic', 'part_time', 'teaching',
            'Bright Future Coaching Center', 'other', 'Ludhiana, Punjab',
            '2013-11-01', '2015-11-19', false, 24,
            'Taught Physics to Class 11 and 12 students preparing for competitive exams like JEE and NEET.',
            JSON.stringify([
                "Conducted physics classes for 11th and 12th grade students",
                "Prepared lesson plans and study materials",
                "Conducted doubt-clearing sessions and mock tests",
                "Tracked student progress and provided feedback",
                "Developed innovative teaching methods for complex concepts"
            ]),
            JSON.stringify([
                "Improved average student scores by 35%",
                "100% pass rate for students in board exams",
                "15 students qualified for engineering entrance exams",
                "Developed digital physics simulations for better understanding"
            ]),
            JSON.stringify([
                "Physics Teaching", "Lesson Planning", "Student Assessment",
                "Educational Technology", "Presentation Skills", "Mentoring"
            ]),
            '15000-20000', 'excellent', 'Opportunity for full-time teaching position',
            'Dr. Suresh Sharma', '<EMAIL>', true
        ]);

        // Current Position - Computer Science Teacher (2015-Present)
        await connection.execute(`
            INSERT INTO staff_professional_experience (
                staff_id, job_title, department, employment_type, job_category,
                organization_name, organization_type, organization_location,
                start_date, end_date, is_current, total_duration_months,
                job_description, key_responsibilities, achievements, skills_used,
                salary_range, performance_rating, supervisor_name, reference_available
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'Computer Science Teacher', 'Academic', 'full_time', 'teaching',
            'Government Senior Secondary School', 'school', 'Ludhiana, Punjab',
            '2015-11-20', null, true, 98,
            'Teaching Computer Science to students from Class 9 to 12, managing computer lab, and mentoring students in programming competitions.',
            JSON.stringify([
                "Teach Computer Science subjects to classes 9-12",
                "Manage and maintain computer laboratory",
                "Develop curriculum and lesson plans for CS subjects",
                "Conduct programming workshops and coding competitions",
                "Mentor students for technical competitions and projects",
                "Coordinate with other teachers for interdisciplinary projects"
            ]),
            JSON.stringify([
                "Improved CS subject pass rate from 75% to 95%",
                "Led school team to district-level coding competition victory",
                "Implemented digital learning tools increasing engagement by 50%",
                "Trained 5 teachers in basic computer skills",
                "Established computer lab with modern equipment",
                "Developed school website and management system"
            ]),
            JSON.stringify([
                "Computer Science Teaching", "Programming (Java, Python, C++)",
                "Database Management", "Web Development", "Lab Management",
                "Student Mentoring", "Curriculum Development", "Educational Technology"
            ]),
            '45000-55000', 'excellent', 'Principal Harpreet Singh', true
        ]);

        // =====================================================
        // 3. CERTIFICATIONS
        // =====================================================
        console.log('\n3. Adding Certifications...');

        // Oracle Java Certification
        await connection.execute(`
            INSERT INTO staff_certifications (
                staff_id, certification_name, certification_type, issuing_organization,
                issue_date, expiry_date, is_lifetime, certificate_id,
                verification_status, description, skills_covered
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'Oracle Certified Professional Java SE 8 Programmer',
            'technical', 'Oracle Corporation', '2016-03-15', '2026-03-15', false,
            'OCP-JAVA-SE8-2016-001234', 'verified',
            'Professional certification in Java programming and software development',
            JSON.stringify(["Java Programming", "Object-Oriented Programming", "Exception Handling", "Collections Framework"])
        ]);

        // Teaching Certification
        await connection.execute(`
            INSERT INTO staff_certifications (
                staff_id, certification_name, certification_type, issuing_organization,
                issue_date, is_lifetime, certificate_id, verification_status,
                description, skills_covered
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            staffId, 'B.Ed (Bachelor of Education)', 'teaching',
            'Punjab University', '2017-06-30', true, 'BED-PU-2017-5678',
            'verified', 'Professional teaching qualification for secondary education',
            JSON.stringify(["Educational Psychology", "Curriculum Development", "Assessment Methods", "Classroom Management"])
        ]);

        // =====================================================
        // 4. SKILLS
        // =====================================================
        console.log('\n4. Adding Skills...');

        const skills = [
            // Technical Skills
            {name: 'Java Programming', category: 'technical', proficiency: 'expert', years: 12, certified: true},
            {name: 'Python Programming', category: 'technical', proficiency: 'advanced', years: 8, certified: false},
            {name: 'C++ Programming', category: 'technical', proficiency: 'advanced', years: 10, certified: false},
            {name: 'MySQL Database', category: 'technical', proficiency: 'advanced', years: 12, certified: false},
            {name: 'Web Development', category: 'technical', proficiency: 'intermediate', years: 6, certified: false},
            {name: 'Data Structures', category: 'technical', proficiency: 'expert', years: 12, certified: true},

            // Teaching Skills
            {name: 'Curriculum Development', category: 'teaching', proficiency: 'advanced', years: 8, certified: true},
            {name: 'Student Assessment', category: 'teaching', proficiency: 'expert', years: 8, certified: true},
            {name: 'Classroom Management', category: 'teaching', proficiency: 'expert', years: 8, certified: true},
            {name: 'Educational Technology', category: 'teaching', proficiency: 'advanced', years: 6, certified: false},

            // Language Skills
            {name: 'English', category: 'language', proficiency: 'expert', years: 20, certified: false},
            {name: 'Hindi', category: 'language', proficiency: 'expert', years: 20, certified: false},
            {name: 'Punjabi', category: 'language', proficiency: 'expert', years: 20, certified: false},

            // Soft Skills
            {name: 'Team Leadership', category: 'soft_skill', proficiency: 'advanced', years: 8, certified: false},
            {name: 'Communication', category: 'soft_skill', proficiency: 'expert', years: 12, certified: false},
            {name: 'Problem Solving', category: 'soft_skill', proficiency: 'expert', years: 12, certified: false}
        ];

        for (const skill of skills) {
            await connection.execute(`
                INSERT INTO staff_skills (
                    staff_id, skill_name, skill_category, proficiency_level,
                    years_of_experience, last_used_date, is_certified
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
                staffId, skill.name, skill.category, skill.proficiency,
                skill.years, '2024-01-15', skill.certified
            ]);
        }

        console.log('✅ CS Teacher comprehensive data populated successfully!');

        // Display summary
        console.log('\n📊 DATA SUMMARY:');
        const [qualCount] = await connection.execute('SELECT COUNT(*) as count FROM staff_educational_qualifications WHERE staff_id = ?', [staffId]);
        const [expCount] = await connection.execute('SELECT COUNT(*) as count FROM staff_professional_experience WHERE staff_id = ?', [staffId]);
        const [certCount] = await connection.execute('SELECT COUNT(*) as count FROM staff_certifications WHERE staff_id = ?', [staffId]);
        const [skillCount] = await connection.execute('SELECT COUNT(*) as count FROM staff_skills WHERE staff_id = ?', [staffId]);

        console.log(`- Educational Qualifications: ${qualCount[0].count}`);
        console.log(`- Professional Experience: ${expCount[0].count}`);
        console.log(`- Certifications: ${certCount[0].count}`);
        console.log(`- Skills: ${skillCount[0].count}`);

    } catch (error) {
        console.error('Error populating CS teacher data:', error);
    } finally {
        await connection.end();
    }
}

populateCSTeacherData();
