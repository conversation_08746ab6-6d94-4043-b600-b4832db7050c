/**
 * <PERSON><PERSON>t to populate staff table with sample data for existing teachers
 * This ensures the enhanced teacher profile timeline functionality works
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'exam_prep_platform'
};

async function populateTeacherStaffData() {
  let connection;
  
  try {
    console.log('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Check if staff table exists
    const [tables] = await connection.query(`
      SHOW TABLES LIKE 'staff'
    `);
    
    if (tables.length === 0) {
      console.log('Staff table does not exist. Creating it...');
      
      // Create staff table
      await connection.query(`
        CREATE TABLE IF NOT EXISTS \`staff\` (
          \`id\` INT AUTO_INCREMENT PRIMARY KEY,
          \`user_id\` INT NOT NULL,
          \`employee_id\` VARCHAR(50) UNIQUE,
          \`designation\` VARCHAR(100),
          \`department\` VARCHAR(100),
          \`joining_date\` DATE,
          \`employment_type\` ENUM('permanent', 'temporary', 'contract', 'part_time') DEFAULT 'permanent',
          \`phone\` VARCHAR(15),
          \`alternate_phone\` VARCHAR(15),
          \`emergency_contact\` VARCHAR(15),
          \`address\` TEXT,
          \`city\` VARCHAR(100),
          \`state\` VARCHAR(100),
          \`pincode\` VARCHAR(10),
          \`class_10_board\` VARCHAR(100),
          \`class_10_year\` YEAR,
          \`class_10_percentage\` DECIMAL(5,2),
          \`class_10_school\` VARCHAR(255),
          \`class_12_board\` VARCHAR(100),
          \`class_12_year\` YEAR,
          \`class_12_percentage\` DECIMAL(5,2),
          \`class_12_school\` VARCHAR(255),
          \`class_12_stream\` VARCHAR(100),
          \`graduation_degree\` VARCHAR(255),
          \`graduation_university\` VARCHAR(255),
          \`graduation_year\` YEAR,
          \`graduation_percentage\` DECIMAL(5,2),
          \`graduation_specialization\` VARCHAR(255),
          \`post_graduation_degree\` VARCHAR(255),
          \`post_graduation_university\` VARCHAR(255),
          \`post_graduation_year\` YEAR,
          \`post_graduation_percentage\` DECIMAL(5,2),
          \`post_graduation_specialization\` VARCHAR(255),
          \`phd_subject\` VARCHAR(255),
          \`phd_university\` VARCHAR(255),
          \`phd_year\` YEAR,
          \`phd_thesis_title\` TEXT,
          \`other_qualifications\` TEXT,
          \`professional_certifications\` TEXT,
          \`total_experience_years\` INT,
          \`teaching_experience_years\` INT,
          \`administrative_experience_years\` INT,
          \`previous_organizations\` TEXT,
          \`current_salary\` DECIMAL(10,2),
          \`subjects_taught\` TEXT,
          \`classes_handled\` TEXT,
          \`awards_received\` TEXT,
          \`publications\` TEXT,
          \`research_papers\` TEXT,
          \`conferences_attended\` TEXT,
          \`training_programs\` TEXT,
          \`special_skills\` TEXT,
          \`languages_known\` TEXT,
          \`office_location\` VARCHAR(255),
          \`reporting_manager_id\` INT,
          \`probation_period_months\` INT,
          \`confirmation_date\` DATE,
          \`last_promotion_date\` DATE,
          \`performance_rating\` DECIMAL(3,2),
          \`resume_file\` VARCHAR(255),
          \`photo_file\` VARCHAR(255),
          \`id_proof_file\` VARCHAR(255),
          \`address_proof_file\` VARCHAR(255),
          \`qualification_certificates\` TEXT,
          \`is_active\` BOOLEAN DEFAULT TRUE,
          \`is_on_leave\` BOOLEAN DEFAULT FALSE,
          \`leave_start_date\` DATE,
          \`leave_end_date\` DATE,
          \`notes\` TEXT,
          \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE,
          INDEX \`idx_user_id\` (\`user_id\`),
          INDEX \`idx_employee_id\` (\`employee_id\`),
          INDEX \`idx_department\` (\`department\`),
          INDEX \`idx_designation\` (\`designation\`),
          INDEX \`idx_joining_date\` (\`joining_date\`)
        )
      `);
      
      console.log('Staff table created successfully.');
    }
    
    // Get all teachers who don't have staff records
    const [teachers] = await connection.query(`
      SELECT u.id, u.name, u.email, u.subjects
      FROM users u 
      LEFT JOIN staff s ON u.id = s.user_id
      WHERE u.role = 'teacher' AND u.is_active = 1 AND s.user_id IS NULL
    `);
    
    console.log(`Found ${teachers.length} teachers without staff records.`);
    
    if (teachers.length === 0) {
      console.log('All teachers already have staff records.');
      return;
    }
    
    // Insert staff data for each teacher
    for (const teacher of teachers) {
      console.log(`Creating staff record for ${teacher.name} (${teacher.email})...`);
      
      const employeeId = `EMP${String(teacher.id).padStart(4, '0')}`;
      const joiningDate = '2020-07-01'; // Default joining date
      
      await connection.query(`
        INSERT INTO staff (
          user_id, employee_id, designation, department, joining_date, employment_type,
          phone, address, city, state, pincode,
          class_10_board, class_10_year, class_10_percentage, class_10_school,
          class_12_board, class_12_year, class_12_percentage, class_12_school, class_12_stream,
          graduation_degree, graduation_university, graduation_year, graduation_percentage, graduation_specialization,
          post_graduation_degree, post_graduation_university, post_graduation_year, post_graduation_percentage, post_graduation_specialization,
          total_experience_years, teaching_experience_years,
          special_skills, languages_known, awards_received, training_programs
        ) VALUES (
          ?, ?, 'Computer Science Teacher', 'Academic Department', ?, 'permanent',
          '+91-98765-43210', 'Teacher Quarters, School Campus', 'Chandigarh', 'Punjab', '160001',
          'Punjab School Education Board', 2007, 79.8, 'Government High School',
          'Punjab School Education Board', 2009, 82.1, 'Government Senior Secondary School', 'Science (PCM)',
          'B.Sc. Computer Science', 'Punjab University', 2012, 78.4, 'Computer Science',
          'M.Sc. Computer Science', 'Guru Nanak Dev University', 2014, 85.6, 'Software Engineering',
          10, 8,
          'Programming, Web Development, Database Management, Machine Learning', 
          'English, Hindi, Punjabi',
          'Best Teacher Award 2023, Excellence in Computer Science Teaching 2022',
          'Digital Teaching Methods Workshop, Advanced Programming Techniques, Educational Technology Integration'
        )
      `, [teacher.id, employeeId, joiningDate]);
      
      console.log(`✓ Staff record created for ${teacher.name}`);
    }
    
    console.log('All teacher staff records have been created successfully!');
    
  } catch (error) {
    console.error('Error populating teacher staff data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
if (require.main === module) {
  populateTeacherStaffData()
    .then(() => {
      console.log('Script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { populateTeacherStaffData };
