const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
    console.log('Starting migration to add metadata column to questions table...');

    // Create database connection
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'exam_prep_platform',
        multipleStatements: true // Allow multiple SQL statements
    });

    try {
        // Read the migration SQL file
        const migrationPath = path.join(__dirname, '../database/migrations/add_metadata_column.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

        // Execute the migration
        console.log('Executing SQL migration...');
        await connection.query(migrationSQL);

        console.log('Migration completed successfully!');
    } catch (error) {
        console.error('Error running migration:', error);
    } finally {
        // Close the database connection
        await connection.end();
    }
}

// Run the migration
runMigration();
