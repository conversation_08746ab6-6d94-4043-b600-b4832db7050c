/**
 * <PERSON><PERSON>t to run a specific migration
 * 
 * Usage: node scripts/run-migration.js <migration-name> [up|down]
 * Example: node scripts/run-migration.js create_query_logs_table up
 */

require('dotenv').config();
const path = require('path');
const fs = require('fs');

// Get migration name and direction from command line arguments
const migrationName = process.argv[2];
const direction = process.argv[3] || 'up';

if (!migrationName) {
    console.error('Migration name is required');
    console.log('Usage: node scripts/run-migration.js <migration-name> [up|down]');
    process.exit(1);
}

// Validate direction
if (direction !== 'up' && direction !== 'down') {
    console.error('Invalid direction. Must be "up" or "down"');
    process.exit(1);
}

// Path to migrations directory
const migrationsDir = path.join(__dirname, '../migrations');

// Check if migration file exists
const migrationFile = path.join(migrationsDir, `${migrationName}.js`);
if (!fs.existsSync(migrationFile)) {
    console.error(`Migration file not found: ${migrationFile}`);
    process.exit(1);
}

// Run migration
async function runMigration() {
    try {
        console.log(`Running migration: ${migrationName} (${direction})`);
        
        // Import migration
        const migration = require(migrationFile);
        
        // Run migration in specified direction
        await migration[direction]();
        
        console.log(`Migration ${migrationName} (${direction}) completed successfully`);
        process.exit(0);
    } catch (error) {
        console.error(`Migration ${migrationName} (${direction}) failed:`, error);
        process.exit(1);
    }
}

// Run migration
runMigration();
