const fs = require('fs');
const path = require('path');
const db = require('../config/database');

async function runMigration() {
    try {
        console.log('Running migration to add profile fields...');
        
        // Read the SQL file
        const sqlPath = path.join(__dirname, '../database/migrations/add_profile_fields.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        // Execute the SQL
        await db.query(sql);
        
        console.log('✅ Migration completed successfully!');
        console.log('Added the following columns to users table:');
        console.log('- institution');
        console.log('- grade');
        console.log('- field_of_study');
        console.log('- preferred_subjects');
        console.log('- target_exams');
        console.log('- study_goal');
        console.log('- language_preference');
        console.log('- time_zone');
        console.log('- accessibility_needs');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

runMigration();
