/**
 * <PERSON><PERSON><PERSON> to run the teacher view database setup
 */

const { exec } = require('child_process');
const path = require('path');

// Path to the SQL file
const sqlFilePath = path.join(__dirname, '../database/teacher_view_tables.sql');

// MySQL command
const mysqlCommand = `/Applications/XAMPP/xamppfiles/bin/mysql -u root exam_prep_platform < ${sqlFilePath}`;

console.log('Running teacher view database setup...');
console.log(`Command: ${mysqlCommand}`);

// Execute the command
exec(mysqlCommand, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error: ${error.message}`);
    return;
  }
  
  if (stderr) {
    console.error(`stderr: ${stderr}`);
    return;
  }
  
  console.log('Teacher view database setup completed successfully!');
  console.log(`stdout: ${stdout}`);
});
