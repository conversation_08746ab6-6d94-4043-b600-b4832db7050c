const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function seedDemoExamData() {
    try {
        // Create database connection
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'exam_prep_platform',
            multipleStatements: true // Important for running multiple SQL statements
        });

        console.log('Connected to the database successfully.');

        // Read the SQL file
        const sqlPath = path.join(__dirname, '../database/seed/demo-exam-data.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');

        // Execute the SQL script
        console.log('Running demo exam seed data...');
        await connection.query(sql);
        console.log('Demo exam data created successfully!');

        // Close the connection
        await connection.end();
        console.log('Database connection closed.');
    } catch (error) {
        console.error('Error seeding demo exam data:', error);
        process.exit(1);
    }
}

// Run the function
seedDemoExamData(); 