const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

async function seedQuestions() {
    try {
        // Create database connection
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'exam_prep_platform'
        });

        // Read the SQL file
        const sqlFile = path.join(__dirname, '../database/sample_questions.sql');
        const sqlContent = await fs.readFile(sqlFile, 'utf8');

        // Split the SQL content into individual statements
        const statements = sqlContent
            .split(';')
            .map(statement => statement.trim())
            .filter(statement => statement.length > 0);

        // Execute each statement
        for (const statement of statements) {
            await connection.query(statement);
            console.log('Executed SQL statement successfully');
        }

        console.log('All questions seeded successfully');
        await connection.end();
    } catch (error) {
        console.error('Error seeding questions:', error);
        process.exit(1);
    }
}

seedQuestions();