/**
 * <PERSON><PERSON><PERSON> to send email notifications for upcoming practicals
 * This script should be run daily via a cron job
 */

const db = require('../config/database');
const { sendEmail } = require('../utils/email-sender');
const ejs = require('ejs');
const fs = require('fs').promises;
const path = require('path');

async function sendPracticalNotifications() {
  try {
    console.log('Starting to send practical notifications...');
    
    // Get practicals scheduled for tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    console.log(`Looking for practicals scheduled for ${tomorrowStr}`);
    
    // Get all practicals for tomorrow
    const [practicals] = await db.query(`
      SELECT tp.*, u.full_name as teacher_name, u.email as teacher_email
      FROM teacher_practicals tp
      JOIN users u ON tp.teacher_id = u.id
      WHERE tp.date = ? AND tp.status != 'cancelled'
    `, [tomorrowStr]);
    
    console.log(`Found ${practicals.length} practicals scheduled for tomorrow`);
    
    if (practicals.length === 0) {
      console.log('No practicals to notify about. Exiting.');
      return;
    }
    
    // For each practical, get students in the class
    for (const practical of practicals) {
      console.log(`Processing practical: ${practical.practical_topic}`);
      
      // Get students in this class
      const [students] = await db.query(`
        SELECT u.id, u.full_name, u.email
        FROM users u
        JOIN student_classes sc ON u.id = sc.student_id
        WHERE sc.class_section_id = ? AND u.role = 'student'
      `, [practical.class_section_id]);
      
      console.log(`Found ${students.length} students in class for practical ${practical.id}`);
      
      // Load email template
      let templateContent;
      try {
        const templatePath = path.join(__dirname, '../views/emails/practical_reminder.ejs');
        templateContent = await fs.readFile(templatePath, 'utf8');
      } catch (error) {
        console.log('Email template not found, using default template');
        templateContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
            <h2 style="color: #3b82f6;">Upcoming Lab Practical Reminder</h2>
            <p>Hello <%= studentName %>,</p>
            <p>This is a reminder that you have a lab practical scheduled for tomorrow:</p>
            <div style="background-color: #f9fafb; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <h3 style="margin-top: 0; color: #1f2937;"><%= practical.practical_topic %></h3>
              <p><strong>Date:</strong> <%= formattedDate %></p>
              <p><strong>Time:</strong> <%= practical.start_time.substring(0, 5) %> - <%= practical.end_time.substring(0, 5) %></p>
              <p><strong>Venue:</strong> <%= practical.venue %></p>
              <p><strong>Subject:</strong> <%= practical.subject_name %></p>
              <p><strong>Teacher:</strong> <%= practical.teacher_name %></p>
            </div>
            <p>Please make sure to arrive on time and bring any necessary materials.</p>
            <p>You can view more details and submit your work by logging into your account.</p>
            <p>Best regards,<br>The Meritorious EP Team</p>
          </div>
        `;
      }
      
      // Format date
      const practicalDate = new Date(practical.date);
      const formattedDate = practicalDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
      
      // Send email to each student
      for (const student of students) {
        try {
          // Skip if no email
          if (!student.email) {
            console.log(`Student ${student.id} has no email. Skipping.`);
            continue;
          }
          
          // Render the template
          const html = ejs.render(templateContent, { 
            studentName: student.full_name || 'Student',
            practical,
            formattedDate
          });
          
          // Send email
          await sendEmail({
            to: student.email,
            subject: `Reminder: Lab Practical Tomorrow - ${practical.practical_topic}`,
            text: `Reminder: You have a lab practical (${practical.practical_topic}) scheduled for tomorrow at ${practical.start_time.substring(0, 5)} in ${practical.venue}.`,
            html
          });
          
          console.log(`Sent notification email to ${student.email} for practical ${practical.id}`);
          
          // Create in-app notification
          await db.query(`
            INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
            VALUES (?, ?, ?, 'practical', ?, 0, NOW())
          `, [
            student.id,
            `Practical Tomorrow: ${practical.practical_topic}`,
            `You have a lab practical (${practical.practical_topic}) scheduled for tomorrow at ${practical.start_time.substring(0, 5)} in ${practical.venue}.`,
            `/student/practicals/${practical.id}`
          ]);
          
          console.log(`Created in-app notification for student ${student.id}`);
          
          // Small delay to prevent overwhelming the email server
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (error) {
          console.error(`Error sending notification to student ${student.id}:`, error);
        }
      }
      
      // Also notify the teacher
      try {
        if (practical.teacher_email) {
          // Render the template for teacher
          const teacherTemplate = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
              <h2 style="color: #3b82f6;">Your Scheduled Lab Practical Tomorrow</h2>
              <p>Hello <%= teacherName %>,</p>
              <p>This is a reminder that you have a lab practical scheduled to conduct tomorrow:</p>
              <div style="background-color: #f9fafb; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h3 style="margin-top: 0; color: #1f2937;"><%= practical.practical_topic %></h3>
                <p><strong>Date:</strong> <%= formattedDate %></p>
                <p><strong>Time:</strong> <%= practical.start_time.substring(0, 5) %> - <%= practical.end_time.substring(0, 5) %></p>
                <p><strong>Venue:</strong> <%= practical.venue %></p>
                <p><strong>Subject:</strong> <%= practical.subject_name %></p>
                <p><strong>Class:</strong> <%= practical.class_name %></p>
              </div>
              <p>Please make sure to arrive on time and prepare any necessary materials.</p>
              <p>You can view more details by logging into your account.</p>
              <p>Best regards,<br>The Meritorious EP Team</p>
            </div>
          `;
          
          const teacherHtml = ejs.render(teacherTemplate, { 
            teacherName: practical.teacher_name || 'Teacher',
            practical,
            formattedDate
          });
          
          // Send email to teacher
          await sendEmail({
            to: practical.teacher_email,
            subject: `Reminder: You're Conducting a Lab Practical Tomorrow - ${practical.practical_topic}`,
            text: `Reminder: You're scheduled to conduct a lab practical (${practical.practical_topic}) tomorrow at ${practical.start_time.substring(0, 5)} in ${practical.venue}.`,
            html: teacherHtml
          });
          
          console.log(`Sent notification email to teacher ${practical.teacher_id} for practical ${practical.id}`);
          
          // Create in-app notification for teacher
          await db.query(`
            INSERT INTO notifications (user_id, title, message, type, link, is_read, created_at)
            VALUES (?, ?, ?, 'practical', ?, 0, NOW())
          `, [
            practical.teacher_id,
            `You're Conducting a Practical Tomorrow: ${practical.practical_topic}`,
            `You're scheduled to conduct a lab practical (${practical.practical_topic}) tomorrow at ${practical.start_time.substring(0, 5)} in ${practical.venue}.`,
            `/teacher/practicals/${practical.id}`
          ]);
          
          console.log(`Created in-app notification for teacher ${practical.teacher_id}`);
        }
      } catch (error) {
        console.error(`Error sending notification to teacher ${practical.teacher_id}:`, error);
      }
    }
    
    console.log('Finished sending practical notifications');
  } catch (error) {
    console.error('Error sending practical notifications:', error);
  } finally {
    // Close database connection
    db.end();
  }
}

// Run the function
sendPracticalNotifications();
