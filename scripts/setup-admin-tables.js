const mysql = require('mysql2/promise');

async function setupAdminTables() {
    // Create database connection
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Setting up admin tables...');

        // Create settings table if it doesn't exist
        await connection.query(`
            CREATE TABLE IF NOT EXISTS settings (
                setting_id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (setting_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
        console.log('Settings table created or already exists');

        // Create email_templates table if it doesn't exist
        await connection.query(`
            CREATE TABLE IF NOT EXISTS email_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                subject VARCHAR(255) NOT NULL,
                body TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
        console.log('Email templates table created or already exists');

        // Create question_categories table if it doesn't exist
        await connection.query(`
            CREATE TABLE IF NOT EXISTS question_categories (
                category_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX (name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
        console.log('Question categories table created or already exists');

        // Create question_category_mappings table if it doesn't exist
        await connection.query(`
            CREATE TABLE IF NOT EXISTS question_category_mappings (
                mapping_id INT AUTO_INCREMENT PRIMARY KEY,
                question_id INT NOT NULL,
                category_id INT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (question_id) REFERENCES questions(question_id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES question_categories(category_id) ON DELETE CASCADE,
                UNIQUE KEY (question_id, category_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
        console.log('Question category mappings table created or already exists');

        // Create user_sessions table if it doesn't exist
        await connection.query(`
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id VARCHAR(128) PRIMARY KEY,
                user_id INT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                last_activity DATETIME,
                data TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX (user_id),
                INDEX (last_activity)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        `);
        console.log('User sessions table created or already exists');

        // Insert default settings if they don't exist
        const defaultSettings = [
            { key: 'site_name', value: 'Meritorious Exam Preparation Platform' },
            { key: 'site_description', value: 'A comprehensive platform for exam preparation and assessment' },
            { key: 'contact_email', value: '<EMAIL>' },
            { key: 'items_per_page', value: '20' },
            { key: 'enable_registration', value: '1' },
            { key: 'maintenance_mode', value: '0' },
            { key: 'security_password_min_length', value: '8' },
            { key: 'security_password_require_uppercase', value: '1' },
            { key: 'security_password_require_number', value: '1' },
            { key: 'security_password_require_special', value: '0' },
            { key: 'security_login_max_attempts', value: '5' },
            { key: 'security_login_lockout_duration', value: '30' },
            { key: 'security_session_timeout', value: '1440' }
        ];

        for (const setting of defaultSettings) {
            await connection.query(
                'INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)',
                [setting.key, setting.value]
            );
        }
        console.log('Default settings inserted');

        // Insert default email templates if they don't exist
        const defaultTemplates = [
            {
                name: 'welcome_email',
                subject: 'Welcome to Meritorious EP',
                body: `<p>Dear {username},</p>
                <p>Welcome to Meritorious Exam Preparation Platform! Your account has been created successfully.</p>
                <p>You can now log in and start preparing for your exams.</p>
                <p>Best regards,<br>The Meritorious EP Team</p>`
            },
            {
                name: 'password_reset',
                subject: 'Password Reset Request',
                body: `<p>Dear {username},</p>
                <p>We received a request to reset your password. Click the link below to reset your password:</p>
                <p><a href="{reset_link}">{reset_link}</a></p>
                <p>If you did not request a password reset, please ignore this email.</p>
                <p>Best regards,<br>The Meritorious EP Team</p>`
            },
            {
                name: 'test_result',
                subject: 'Your Test Results',
                body: `<p>Dear {username},</p>
                <p>You have completed the test: {test_name}</p>
                <p>Your score: {score}%</p>
                <p>Status: {status}</p>
                <p>You can view your detailed results by logging into your account.</p>
                <p>Best regards,<br>The Meritorious EP Team</p>`
            }
        ];

        for (const template of defaultTemplates) {
            await connection.query(
                'INSERT IGNORE INTO email_templates (name, subject, body) VALUES (?, ?, ?)',
                [template.name, template.subject, template.body]
            );
        }
        console.log('Default email templates inserted');

        // Insert default question categories if they don't exist
        const defaultCategories = [
            { name: 'Mathematics', description: 'Questions related to mathematics' },
            { name: 'Science', description: 'Questions related to science' },
            { name: 'English', description: 'Questions related to English language and literature' },
            { name: 'History', description: 'Questions related to history' },
            { name: 'Geography', description: 'Questions related to geography' }
        ];

        for (const category of defaultCategories) {
            await connection.query(
                'INSERT IGNORE INTO question_categories (name, description) VALUES (?, ?)',
                [category.name, category.description]
            );
        }
        console.log('Default question categories inserted');

        console.log('Admin tables setup completed successfully!');
    } catch (error) {
        console.error('Error setting up admin tables:', error);
    } finally {
        await connection.end();
        process.exit(0);
    }
}

setupAdminTables(); 