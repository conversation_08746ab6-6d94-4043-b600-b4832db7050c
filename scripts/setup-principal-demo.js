/**
 * Setup Principal Demo User
 * Creates the principal user for demo login
 */

const bcrypt = require('bcrypt');
const db = require('../config/database');

async function setupPrincipalDemo() {
  try {
    console.log('Setting up Principal demo user...\n');

    // 1. Check if users table supports principal role
    console.log('1. Checking users table structure...');
    const [columns] = await db.query(`
      SHOW COLUMNS FROM users WHERE Field = 'role'
    `);

    if (columns.length > 0) {
      console.log('✅ Users table role column found');
      console.log('   Type:', columns[0].Type);

      if (!columns[0].Type.includes('principal')) {
        console.log('⚠️  Adding principal role to enum...');
        await db.query(`
          ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'teacher', 'student', 'principal') DEFAULT 'student'
        `);
        console.log('✅ Principal role added to enum');
      } else {
        console.log('✅ Principal role already exists in enum');
      }
    }

    // 2. Create/update principal user
    console.log('\n2. Creating principal user...');

    // Hash the password 'principal123'
    const hashedPassword = await bcrypt.hash('principal123', 10);
    console.log('✅ Password hashed');

    // Check if principal user already exists
    const [existingUsers] = await db.query(`
      SELECT id FROM users WHERE username = 'principal' OR email = '<EMAIL>'
    `);

    if (existingUsers.length > 0) {
      // Update existing user
      console.log('📝 Updating existing principal user...');
      await db.query(`
        UPDATE users
        SET password = ?, role = 'principal', is_active = 1, updated_at = NOW()
        WHERE username = 'principal' OR email = '<EMAIL>'
      `, [hashedPassword]);
      console.log('✅ Principal user updated');
    } else {
      // Create new user with all required fields
      console.log('🆕 Creating new principal user...');
      const [result] = await db.query(`
        INSERT INTO users (
          username,
          name,
          email,
          password,
          role,
          full_name,
          bio,
          last_login,
          is_active,
          created_at,
          updated_at
        ) VALUES (
          'principal',
          'Dr. Sarah Johnson',
          '<EMAIL>',
          ?,
          'principal',
          'Dr. Sarah Johnson',
          'School Principal with executive leadership and strategic oversight responsibilities.',
          NOW(),
          1,
          NOW(),
          NOW()
        )
      `, [hashedPassword]);
      console.log('✅ Principal user created with ID:', result.insertId);
    }

    // 3. Verify the principal user
    console.log('\n3. Verifying principal user...');
    const [principalUsers] = await db.query(`
      SELECT id, username, name, email, role, is_active, created_at
      FROM users
      WHERE role = 'principal'
    `);

    if (principalUsers.length > 0) {
      console.log('✅ Principal users found:');
      principalUsers.forEach(user => {
        console.log(`   - ID: ${user.id}`);
        console.log(`   - Username: ${user.username}`);
        console.log(`   - Name: ${user.name}`);
        console.log(`   - Email: ${user.email}`);
        console.log(`   - Role: ${user.role}`);
        console.log(`   - Active: ${user.is_active}`);
        console.log(`   - Created: ${user.created_at}`);
        console.log('');
      });
    } else {
      console.log('❌ No principal users found');
    }

    // 4. Test password verification
    console.log('4. Testing password verification...');
    const testPassword = 'principal123';
    const isValid = await bcrypt.compare(testPassword, hashedPassword);

    if (isValid) {
      console.log('✅ Password verification successful');
    } else {
      console.log('❌ Password verification failed');
    }

    console.log('\n=== Principal Demo Setup Complete ===');
    console.log('\nDemo Login Credentials:');
    console.log('Username: principal');
    console.log('Password: principal123');
    console.log('Email: <EMAIL>');
    console.log('\nAccess URLs:');
    console.log('- Demo Login: /demo-login');
    console.log('- Direct Login: /login');
    console.log('- Principal Dashboard: /principal/dashboard');

    process.exit(0);
  } catch (error) {
    console.error('Error setting up principal demo:', error);
    process.exit(1);
  }
}

// Run the setup
setupPrincipalDemo();
