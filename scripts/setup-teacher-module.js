const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupTeacherModule() {
  console.log('Setting up teacher module tables and demo data...');

  // Create a database connection
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'exam_prep_platform',
    multipleStatements: true // Allow multiple statements
  });

  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'sql', 'teacher_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');

    // Execute each statement
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      
      try {
        console.log(`Executing statement ${i + 1}/${statements.length}`);
        await connection.query(statement);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}:`, error.message);
        console.log('Statement:', statement);
        
        // If this is a "table already exists" error, we can continue
        if (error.code === 'ER_TABLE_EXISTS_ERROR') {
          console.log('Table already exists, continuing...');
        } else {
          throw error;
        }
      }
    }

    console.log('Teacher module setup completed successfully!');
    
    // Check if there's a teacher user to use for the demo data
    const [rows] = await connection.query(
      "SELECT id FROM users WHERE role = 'teacher' LIMIT 1"
    );
    
    if (rows.length === 0) {
      console.log('No teacher user found in the database.');
      console.log('Create a teacher user before using the teacher module.');
      console.log('You can update an existing user to have the "teacher" role.');
    } else {
      console.log(`Found teacher user with ID ${rows[0].id}`);
      console.log('Teacher module is ready to use.');
    }

  } catch (error) {
    console.error('Error setting up teacher module:', error);
  } finally {
    await connection.end();
  }
}

setupTeacherModule().catch(console.error); 