/**
 * <PERSON><PERSON><PERSON> to set up the teacher view database tables
 * This script runs the SQL in database/teacher_view_tables.sql
 */

const fs = require('fs');
const path = require('path');
const db = require('../config/database');

async function setupTeacherView() {
  try {
    console.log('Setting up teacher view database tables...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../database/teacher_view_tables.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL file into individual statements
    // This is a simple approach and might not work for all SQL files
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim();
      if (stmt) {
        try {
          // Handle DELIMITER statements specially
          if (stmt.toUpperCase().startsWith('DELIMITER')) {
            console.log('Skipping DELIMITER statement...');
            continue;
          }
          
          // Handle CREATE PROCEDURE statements
          if (stmt.toUpperCase().includes('CREATE PROCEDURE')) {
            console.log('Executing procedure creation...');
            await db.query(stmt + ';');
          } else {
            await db.query(stmt);
          }
          
          console.log(`Executed statement ${i + 1}/${statements.length}`);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error.message);
          console.log('Statement:', stmt);
        }
      }
    }
    
    console.log('Teacher view database setup completed successfully!');
  } catch (error) {
    console.error('Error setting up teacher view database:', error);
  } finally {
    // Close the database connection
    await db.end();
  }
}

// Run the setup function
setupTeacherView();
