/**
 * <PERSON><PERSON><PERSON> to show the structure of a table
 */

const db = require('../config/database');

async function showTableStructure(tableName) {
    try {
        console.log(`Showing structure of table: ${tableName}`);
        
        const [columns] = await db.query(`
            DESCRIBE ${tableName}
        `);
        
        console.log('Columns:');
        columns.forEach(column => {
            console.log(`- ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Key} ${column.Default ? `DEFAULT ${column.Default}` : ''} ${column.Extra}`);
        });
        
    } catch (error) {
        console.error('Error showing table structure:', error);
    }
}

// Run the script
const tableName = process.argv[2];
if (!tableName) {
    console.error('Please provide a table name as an argument');
    process.exit(1);
}

showTableStructure(tableName)
    .then(() => {
        console.log('Script completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('<PERSON><PERSON><PERSON> failed:', err);
        process.exit(1);
    });
