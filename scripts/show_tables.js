/**
 * <PERSON><PERSON><PERSON> to show all tables in the database
 */

const db = require('../config/database');

async function showTables() {
    try {
        console.log('Showing all tables in the database');
        
        const [tables] = await db.query(`
            SHOW TABLES FROM exam_prep_platform
        `);
        
        console.log('Tables:');
        tables.forEach(table => {
            console.log(`- ${Object.values(table)[0]}`);
        });
        
    } catch (error) {
        console.error('Error showing tables:', error);
    }
}

// Run the script
showTables()
    .then(() => {
        console.log('Script completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('<PERSON><PERSON><PERSON> failed:', err);
        process.exit(1);
    });
