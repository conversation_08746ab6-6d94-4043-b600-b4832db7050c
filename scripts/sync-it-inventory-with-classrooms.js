/**
 * <PERSON><PERSON><PERSON> to sync IT equipment from inventory with classroom assignments
 * 
 * This script:
 * 1. Fetches IT equipment from both it_inventory and inventory_items tables
 * 2. Matches location fields with room numbers
 * 3. Updates classroom assignments based on location data
 * 4. Creates missing room entries if needed
 * 5. Provides detailed reporting of sync results
 */

const db = require('../config/database');

async function syncITInventoryWithClassrooms() {
    console.log('🔄 Starting IT Inventory to Classroom Sync...\n');

    try {
        // 1. Check existing table structures
        console.log('1. Checking database structure...');
        
        // Check if tables exist
        const [itInventoryExists] = await db.query(`
            SELECT COUNT(*) as table_exists
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'it_inventory'
        `);

        const [inventoryItemsExists] = await db.query(`
            SELECT COUNT(*) as table_exists
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'inventory_items'
        `);

        const [roomsExists] = await db.query(`
            SELECT COUNT(*) as table_exists
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'rooms'
        `);

        console.log(`   - it_inventory table: ${itInventoryExists[0].table_exists ? '✅ Exists' : '❌ Missing'}`);
        console.log(`   - inventory_items table: ${inventoryItemsExists[0].table_exists ? '✅ Exists' : '❌ Missing'}`);
        console.log(`   - rooms table: ${roomsExists[0].table_exists ? '✅ Exists' : '❌ Missing'}`);

        // 2. Get all IT equipment from both tables
        console.log('\n2. Fetching IT equipment data...');
        
        let allEquipment = [];

        // Fetch from it_inventory table if it exists
        if (itInventoryExists[0].table_exists) {
            const [itInventory] = await db.query(`
                SELECT 
                    id,
                    name,
                    type,
                    serial_number,
                    model,
                    manufacturer,
                    status,
                    location,
                    notes,
                    'it_inventory' as source_table
                FROM it_inventory
                WHERE location IS NOT NULL AND location != ''
                ORDER BY location, name
            `);
            
            allEquipment = allEquipment.concat(itInventory);
            console.log(`   - Found ${itInventory.length} items in it_inventory table`);
        }

        // Fetch from inventory_items table if it exists
        if (inventoryItemsExists[0].table_exists) {
            const [inventoryItems] = await db.query(`
                SELECT 
                    item_id as id,
                    name,
                    'computer' as type,
                    serial_number,
                    model,
                    manufacturer,
                    status,
                    location,
                    notes,
                    'inventory_items' as source_table
                FROM inventory_items
                WHERE location IS NOT NULL AND location != ''
                ORDER BY location, name
            `);
            
            allEquipment = allEquipment.concat(inventoryItems);
            console.log(`   - Found ${inventoryItems.length} items in inventory_items table`);
        }

        console.log(`   - Total equipment items: ${allEquipment.length}`);

        // 3. Extract room numbers from location fields
        console.log('\n3. Analyzing location data...');
        
        const roomMatches = new Map();
        const unmatchedLocations = new Set();
        
        allEquipment.forEach(item => {
            const location = item.location.trim();
            
            // Try to extract room number using various patterns
            const roomPatterns = [
                /Room\s*(\d+)/i,           // "Room 5", "Room 12"
                /Classroom\s*(\d+)/i,      // "Classroom 5"
                /Class\s*(\d+)/i,          // "Class 5"
                /R(\d+)/i,                 // "R5", "R12"
                /^(\d+)$/,                 // Just "5", "12"
                /Room\s*(\d+[A-Z]?)/i,     // "Room 5A", "Room 12B"
            ];
            
            let roomNumber = null;
            
            for (const pattern of roomPatterns) {
                const match = location.match(pattern);
                if (match) {
                    roomNumber = match[1];
                    break;
                }
            }
            
            if (roomNumber) {
                if (!roomMatches.has(roomNumber)) {
                    roomMatches.set(roomNumber, []);
                }
                roomMatches.get(roomNumber).push(item);
            } else {
                unmatchedLocations.add(location);
            }
        });

        console.log(`   - Matched ${roomMatches.size} different room numbers`);
        console.log(`   - Unmatched locations: ${unmatchedLocations.size}`);
        
        if (unmatchedLocations.size > 0) {
            console.log('   - Unmatched locations:');
            unmatchedLocations.forEach(location => {
                console.log(`     • "${location}"`);
            });
        }

        // 4. Get existing rooms from database
        console.log('\n4. Checking existing rooms...');
        
        const [existingRooms] = await db.query(`
            SELECT id, room_number
            FROM rooms
            ORDER BY room_number
        `);
        
        const roomNumberToId = new Map();
        existingRooms.forEach(room => {
            // Extract number from room_number (e.g., "Room 5" -> "5")
            const match = room.room_number.match(/(\d+)/);
            if (match) {
                roomNumberToId.set(match[1], room.id);
            }
        });
        
        console.log(`   - Found ${existingRooms.length} existing rooms in database`);

        // 5. Create missing rooms if needed
        console.log('\n5. Creating missing rooms...');
        
        let createdRooms = 0;
        
        for (const [roomNumber, equipment] of roomMatches) {
            if (!roomNumberToId.has(roomNumber)) {
                console.log(`   - Creating Room ${roomNumber}...`);
                
                const [result] = await db.query(`
                    INSERT INTO rooms (room_number, capacity, building, floor, created_at, updated_at)
                    VALUES (?, 40, 'Main Building', ?, NOW(), NOW())
                `, [`Room ${roomNumber}`, Math.ceil(parseInt(roomNumber) / 5)]);
                
                roomNumberToId.set(roomNumber, result.insertId);
                createdRooms++;
            }
        }
        
        console.log(`   - Created ${createdRooms} new rooms`);

        // 6. Update equipment with room assignments
        console.log('\n6. Updating equipment room assignments...');
        
        let updatedCount = 0;
        const updateResults = [];
        
        for (const [roomNumber, equipment] of roomMatches) {
            const roomId = roomNumberToId.get(roomNumber);
            
            console.log(`\n   Room ${roomNumber} (ID: ${roomId}):`);
            
            for (const item of equipment) {
                try {
                    if (item.source_table === 'it_inventory') {
                        // Update it_inventory table
                        await db.query(`
                            UPDATE it_inventory 
                            SET location = ?
                            WHERE id = ?
                        `, [`Room ${roomNumber}`, item.id]);
                        
                    } else if (item.source_table === 'inventory_items') {
                        // Update inventory_items table
                        await db.query(`
                            UPDATE inventory_items 
                            SET location = ?
                            WHERE item_id = ?
                        `, [`Room ${roomNumber}`, item.id]);
                    }
                    
                    updatedCount++;
                    console.log(`     ✅ ${item.name} (${item.serial_number || 'No SN'})`);
                    
                    updateResults.push({
                        roomNumber,
                        roomId,
                        itemName: item.name,
                        serialNumber: item.serial_number,
                        type: item.type,
                        status: item.status,
                        sourceTable: item.source_table
                    });
                    
                } catch (error) {
                    console.log(`     ❌ Failed to update ${item.name}: ${error.message}`);
                }
            }
        }

        // 7. Generate summary report
        console.log('\n=== SYNC SUMMARY REPORT ===');
        console.log(`📊 Total equipment processed: ${allEquipment.length}`);
        console.log(`🏫 Rooms with equipment: ${roomMatches.size}`);
        console.log(`🆕 New rooms created: ${createdRooms}`);
        console.log(`✅ Equipment items updated: ${updatedCount}`);
        console.log(`❌ Unmatched locations: ${unmatchedLocations.size}`);

        // 8. Room-by-room breakdown
        console.log('\n=== ROOM BREAKDOWN ===');
        for (const [roomNumber, equipment] of roomMatches) {
            console.log(`\n🏫 Room ${roomNumber}:`);
            console.log(`   Equipment count: ${equipment.length}`);
            
            const typeBreakdown = {};
            equipment.forEach(item => {
                typeBreakdown[item.type] = (typeBreakdown[item.type] || 0) + 1;
            });
            
            Object.entries(typeBreakdown).forEach(([type, count]) => {
                console.log(`   - ${type}: ${count}`);
            });
        }

        // 9. Test the updated data
        console.log('\n=== TESTING UPDATED DATA ===');
        
        // Test a few rooms to verify the sync worked
        const testRooms = Array.from(roomMatches.keys()).slice(0, 3);
        
        for (const roomNumber of testRooms) {
            console.log(`\n🧪 Testing Room ${roomNumber}:`);
            
            // Query the principal API endpoint format
            const [testResults] = await db.query(`
                SELECT
                    name as item_name,
                    type as item_type,
                    serial_number,
                    status,
                    manufacturer,
                    model,
                    notes
                FROM it_inventory
                WHERE location = ? OR location LIKE ?
                ORDER BY type, name
            `, [`Room ${roomNumber}`, `%Room ${roomNumber}%`]);
            
            console.log(`   API query returned: ${testResults.length} items`);
            testResults.forEach(item => {
                console.log(`   - ${item.item_name} (${item.item_type})`);
            });
        }

        console.log('\n✅ IT Inventory sync completed successfully!');
        
        return {
            totalProcessed: allEquipment.length,
            roomsWithEquipment: roomMatches.size,
            newRoomsCreated: createdRooms,
            equipmentUpdated: updatedCount,
            unmatchedLocations: unmatchedLocations.size,
            updateResults
        };

    } catch (error) {
        console.error('❌ Error during IT inventory sync:', error);
        throw error;
    }
}

// Run the sync if this script is executed directly
if (require.main === module) {
    syncITInventoryWithClassrooms()
        .then(results => {
            console.log('\n🎉 Sync completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('💥 Sync failed:', error);
            process.exit(1);
        });
}

module.exports = { syncITInventoryWithClassrooms };
