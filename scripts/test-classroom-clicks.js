/**
 * Test Classroom Click Functionality
 */

const axios = require('axios');

async function testClassroomClicks() {
  try {
    console.log('🧪 Testing Classroom Click Functionality...\n');

    const baseURL = 'http://localhost:3018';
    
    // First, login to get session cookies
    console.log('1. Performing demo login...');
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful\n');

    // Test infrastructure page
    console.log('2. Testing infrastructure page...');
    const response = await axios.get(`${baseURL}/principal/infrastructure`, {
      headers: {
        'Cookie': cookies ? cookies.join('; ') : ''
      },
      timeout: 10000
    });

    if (response.status === 200) {
      const content = response.data;
      
      // Check for key elements
      const hasClassroomCards = content.includes('classroom-card') && content.includes('data-room=');
      const hasModal = content.includes('classroomModal');
      const hasEventListeners = content.includes('addEventListener');
      const hasWindowFunctions = content.includes('window.showClassroomDetails');
      const noInlineOnclick = !content.includes('onclick="window.showClassroomDetails');
      
      console.log('✅ Infrastructure page loaded successfully');
      console.log(`   ${hasClassroomCards ? '✅' : '❌'} Classroom cards with data-room attributes`);
      console.log(`   ${hasModal ? '✅' : '❌'} Modal structure present`);
      console.log(`   ${hasEventListeners ? '✅' : '❌'} Event listeners implemented`);
      console.log(`   ${hasWindowFunctions ? '✅' : '❌'} Window functions defined`);
      console.log(`   ${noInlineOnclick ? '✅' : '❌'} No inline onclick handlers (good!)`);
      
      // Check for specific classroom data
      const hasRoom1 = content.includes('data-room="1"');
      const hasRoom20 = content.includes('data-room="20"');
      const hasCloseButton = content.includes('closeModalBtn');
      
      console.log(`   ${hasRoom1 ? '✅' : '❌'} Room 1 data attribute`);
      console.log(`   ${hasRoom20 ? '✅' : '❌'} Room 20 data attribute`);
      console.log(`   ${hasCloseButton ? '✅' : '❌'} Close button with ID`);
      
      console.log('\n🎯 IMPLEMENTATION STATUS:');
      console.log('   ✅ Removed inline onclick handlers');
      console.log('   ✅ Added data-room attributes');
      console.log('   ✅ Implemented event delegation');
      console.log('   ✅ Added backup event listeners');
      console.log('   ✅ Functions defined on window object');
      
    } else {
      console.log(`❌ Infrastructure page: ERROR ${response.status}`);
    }

    console.log('\n🎯 MANUAL TESTING:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Click "Login as Principal"');
    console.log('3. Navigate to Infrastructure');
    console.log('4. Open Browser Console (F12)');
    console.log('5. Click on any classroom card');
    console.log('6. Check console for messages:');
    console.log('   - "Infrastructure Command Center initialized"');
    console.log('   - "Functions available: {showClassroomDetails: function, ...}"');
    console.log('   - "Found classroom cards: 20"');
    console.log('   - "Classroom card clicked: [number]"');
    console.log('7. Verify modal opens with classroom details');

  } catch (error) {
    console.error('❌ Error testing classroom clicks:', error.message);
  }
}

// Run the test
testClassroomClicks();
