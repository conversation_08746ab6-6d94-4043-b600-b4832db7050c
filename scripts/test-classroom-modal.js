/**
 * Test Classroom Modal Functionality
 */

const puppeteer = require('puppeteer');

async function testClassroomModal() {
  let browser;
  try {
    console.log('🧪 Testing Classroom Modal Functionality...\n');

    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log('Browser Console:', msg.text());
    });
    
    // Enable error logging
    page.on('pageerror', error => {
      console.error('Browser Error:', error.message);
    });

    console.log('1. Navigating to demo login...');
    await page.goto('http://localhost:3018/demo-login');
    
    console.log('2. Logging in as principal...');
    await page.click('button[onclick*="principal"]');
    await page.waitForNavigation();
    
    console.log('3. Navigating to infrastructure...');
    await page.goto('http://localhost:3018/principal/infrastructure');
    await page.waitForSelector('.classroom-card');
    
    console.log('4. Testing classroom card click...');
    
    // Wait for the page to fully load
    await page.waitForTimeout(2000);
    
    // Check if functions are defined
    const functionsExist = await page.evaluate(() => {
      return {
        showClassroomDetails: typeof window.showClassroomDetails === 'function',
        closeClassroomModal: typeof window.closeClassroomModal === 'function'
      };
    });
    
    console.log('Functions exist:', functionsExist);
    
    // Try to click the first classroom
    try {
      await page.click('.classroom-card[data-room="1"]');
      console.log('✅ Clicked classroom 1 successfully');
      
      // Wait for modal to appear
      await page.waitForSelector('#classroomModal:not(.hidden)', { timeout: 5000 });
      console.log('✅ Modal appeared successfully');
      
      // Check modal content
      const modalContent = await page.evaluate(() => {
        const modal = document.getElementById('classroomModal');
        const title = document.getElementById('modalTitle');
        const content = document.getElementById('modalContent');
        
        return {
          isVisible: !modal.classList.contains('hidden'),
          title: title ? title.textContent : 'No title',
          hasContent: content && content.innerHTML.length > 100
        };
      });
      
      console.log('Modal content:', modalContent);
      
      if (modalContent.isVisible) {
        console.log('✅ Modal is visible');
        console.log(`✅ Modal title: ${modalContent.title}`);
        console.log(`✅ Modal has content: ${modalContent.hasContent}`);
        
        // Try to close modal
        await page.click('#classroomModal button');
        await page.waitForTimeout(1000);
        
        const modalClosed = await page.evaluate(() => {
          const modal = document.getElementById('classroomModal');
          return modal.classList.contains('hidden');
        });
        
        if (modalClosed) {
          console.log('✅ Modal closed successfully');
        } else {
          console.log('❌ Modal did not close');
        }
      }
      
    } catch (error) {
      console.error('❌ Error clicking classroom or modal:', error.message);
    }
    
    console.log('\n🎯 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  testClassroomModal();
} catch (error) {
  console.log('⚠️  Puppeteer not available. Manual testing required.');
  console.log('\n🎯 MANUAL TESTING STEPS:');
  console.log('1. Open: http://localhost:3018/demo-login');
  console.log('2. Click "Login as Principal"');
  console.log('3. Navigate to Infrastructure');
  console.log('4. Click on Classroom 1');
  console.log('5. Verify modal opens with classroom details');
  console.log('6. Click X to close modal');
}
