/**
 * Test Infrastructure JavaScript Loading
 */

const axios = require('axios');

async function testInfrastructureJS() {
  try {
    console.log('🧪 Testing Infrastructure JavaScript Loading...\n');

    const baseURL = 'http://localhost:3018';

    // Create a session by doing demo login first
    console.log('1. Performing demo login...');
    
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful, got session cookies');

    // Now access the infrastructure page with the session
    console.log('\n2. Accessing infrastructure page...');
    
    const infraResponse = await axios.get(`${baseURL}/principal/infrastructure`, {
      headers: {
        'Cookie': cookies ? cookies.join('; ') : ''
      }
    });

    if (infraResponse.status === 200) {
      console.log('✅ Infrastructure page accessible');
      
      const content = infraResponse.data;
      
      // Check if the external JS file is included
      if (content.includes('src="/js/infrastructure.js"')) {
        console.log('✅ External infrastructure.js script tag found');
      } else {
        console.log('❌ External infrastructure.js script tag NOT found');
      }
      
      // Check if emergency test code is removed
      if (content.includes('EMERGENCY JAVASCRIPT TEST')) {
        console.log('❌ Emergency test code still present (should be removed)');
      } else {
        console.log('✅ Emergency test code removed');
      }
      
      // Check if inline script is removed
      if (content.includes('🚨 EMERGENCY: Script block starting')) {
        console.log('❌ Emergency inline script still present (should be removed)');
      } else {
        console.log('✅ Emergency inline script removed');
      }
      
      // Check if classroom cards are present
      if (content.includes('classroom-card')) {
        console.log('✅ Classroom cards found');
      } else {
        console.log('❌ Classroom cards NOT found');
      }
      
      // Check if modal structure is present
      if (content.includes('classroomModal')) {
        console.log('✅ Classroom modal structure found');
      } else {
        console.log('❌ Classroom modal structure NOT found');
      }
      
      // Check if data-room attributes are present
      if (content.includes('data-room=')) {
        console.log('✅ Data-room attributes found');
      } else {
        console.log('❌ Data-room attributes NOT found');
      }
      
    } else {
      console.log('❌ Infrastructure page not accessible, status:', infraResponse.status);
    }

    // Test if the external JS file is accessible
    console.log('\n3. Testing external JS file accessibility...');
    
    try {
      const jsResponse = await axios.get(`${baseURL}/js/infrastructure.js`);
      if (jsResponse.status === 200) {
        console.log('✅ infrastructure.js file is accessible');
        
        const jsContent = jsResponse.data;
        if (jsContent.includes('Infrastructure Page JavaScript')) {
          console.log('✅ infrastructure.js contains expected content');
        } else {
          console.log('❌ infrastructure.js does NOT contain expected content');
        }
        
        if (jsContent.includes('showClassroomDetails')) {
          console.log('✅ showClassroomDetails function found in JS file');
        } else {
          console.log('❌ showClassroomDetails function NOT found in JS file');
        }
        
      } else {
        console.log('❌ infrastructure.js file not accessible, status:', jsResponse.status);
      }
    } catch (jsError) {
      console.log('❌ Error accessing infrastructure.js:', jsError.message);
    }

    console.log('\n=== Infrastructure JavaScript Test Complete ===');
    console.log('\n🎯 Manual Testing:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Click the navy blue "Principal" card');
    console.log('3. Navigate to Infrastructure Command');
    console.log('4. Click on any classroom card');
    console.log('5. Should see a modal with classroom details');
    console.log('6. Check browser console for infrastructure.js loading messages');

  } catch (error) {
    if (error.response && error.response.status === 302) {
      console.log('✅ Got redirect (expected for demo login)');
      console.log('   Redirect to:', error.response.headers.location);
    } else {
      console.error('Error testing infrastructure JS:', error.message);
    }
  }
}

// Run the test
testInfrastructureJS();
