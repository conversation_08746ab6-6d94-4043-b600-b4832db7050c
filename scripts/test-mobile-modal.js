const axios = require('axios');

async function testMobileModal() {
    console.log('🧪 Testing Mobile-Friendly Infrastructure Modal...\n');

    const baseURL = 'http://localhost:3018';
    let cookies = [];

    try {
        // 1. Perform demo login
        console.log('1. Performing demo login...');
        const loginResponse = await axios.post(`${baseURL}/demo-login`, {
            role: 'principal'
        }, {
            maxRedirects: 0,
            validateStatus: function (status) {
                return status >= 200 && status < 400;
            }
        });

        if (loginResponse.headers['set-cookie']) {
            cookies = loginResponse.headers['set-cookie'];
            console.log('✅ Demo login successful, got session cookies');
        }

        // 2. Test infrastructure page accessibility
        console.log('\n2. Testing infrastructure page...');
        const pageResponse = await axios.get(`${baseURL}/principal/infrastructure`, {
            headers: {
                'Cookie': cookies ? cookies.join('; ') : ''
            }
        });

        console.log('✅ Infrastructure page accessible');

        // Check for mobile-friendly modal structure
        const pageContent = pageResponse.data;
        
        // Check for responsive modal classes
        if (pageContent.includes('w-full h-full sm:h-auto')) {
            console.log('✅ Mobile-friendly modal structure found');
        } else {
            console.log('❌ Mobile-friendly modal structure not found');
        }

        // Check for responsive padding
        if (pageContent.includes('p-2 sm:p-4')) {
            console.log('✅ Responsive padding classes found');
        } else {
            console.log('❌ Responsive padding classes not found');
        }

        // Check for responsive text sizing
        if (pageContent.includes('text-lg sm:text-2xl')) {
            console.log('✅ Responsive text sizing found');
        } else {
            console.log('❌ Responsive text sizing not found');
        }

        // 3. Test API endpoint with mobile user agent
        console.log('\n3. Testing API with mobile user agent...');
        const apiResponse = await axios.get(`${baseURL}/principal/api/classroom/7`, {
            headers: {
                'Cookie': cookies ? cookies.join('; ') : '',
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
            }
        });

        if (apiResponse.data.success) {
            console.log('✅ API works with mobile user agent');
            console.log(`   - Students: ${apiResponse.data.data.students.total}`);
            console.log(`   - IT Equipment: ${apiResponse.data.data.equipment.it.length}`);
            console.log(`   - Electrical Equipment: ${apiResponse.data.data.equipment.electrical.length}`);
        } else {
            console.log('❌ API failed with mobile user agent');
        }

        // 4. Check for responsive CSS
        console.log('\n4. Checking responsive CSS...');
        if (pageContent.includes('@media (max-width: 640px)')) {
            console.log('✅ Mobile breakpoint CSS found');
        } else {
            console.log('❌ Mobile breakpoint CSS not found');
        }

        if (pageContent.includes('clamp(')) {
            console.log('✅ Fluid typography (clamp) found');
        } else {
            console.log('❌ Fluid typography (clamp) not found');
        }

        // 5. Check for viewport meta tag
        if (pageContent.includes('name="viewport"')) {
            console.log('✅ Viewport meta tag found');
        } else {
            console.log('❌ Viewport meta tag not found');
        }

        console.log('\n=== Mobile Modal Test Complete ===\n');

        console.log('🎯 Mobile Testing Instructions:');
        console.log('1. Open: http://localhost:3018/demo-login');
        console.log('2. Click the navy blue "Principal" card');
        console.log('3. Navigate to Infrastructure Command');
        console.log('4. Test on different screen sizes:');
        console.log('   - Desktop: Modal should be 90-95% width');
        console.log('   - Tablet: Modal should be 95% width');
        console.log('   - Mobile: Modal should be full screen');
        console.log('5. Click on any classroom card');
        console.log('6. Verify:');
        console.log('   - No horizontal scrolling needed');
        console.log('   - Text is readable on small screens');
        console.log('   - Equipment details are properly grouped');
        console.log('   - Status indicators are visible');
        console.log('   - Modal closes properly');

        console.log('\n📱 Browser Developer Tools Test:');
        console.log('1. Open browser dev tools (F12)');
        console.log('2. Toggle device toolbar (Ctrl+Shift+M)');
        console.log('3. Test different device sizes:');
        console.log('   - iPhone SE (375px)');
        console.log('   - iPad (768px)');
        console.log('   - Desktop (1200px+)');
        console.log('4. Verify modal adapts to each screen size');

    } catch (error) {
        console.error('❌ Error during mobile modal test:', error.message);
        if (error.response) {
            console.error('   Status:', error.response.status);
            console.error('   Headers:', error.response.headers);
        }
    }
}

// Run the test
testMobileModal();
