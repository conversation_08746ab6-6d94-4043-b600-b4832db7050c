/**
 * Test Modal Styling and Functionality
 */

const puppeteer = require('puppeteer');

async function testModalStyling() {
  let browser;
  try {
    console.log('🎨 Testing Modal Styling and Centering...\n');

    browser = await puppeteer.launch({ 
      headless: false, 
      defaultViewport: null,
      args: ['--start-maximized']
    });
    
    const page = await browser.newPage();
    
    console.log('1. Navigating to demo login...');
    await page.goto('http://localhost:3018/demo-login');
    
    console.log('2. Logging in as principal...');
    await page.click('button[onclick*="principal"]');
    await page.waitForNavigation();
    
    console.log('3. Navigating to infrastructure...');
    await page.goto('http://localhost:3018/principal/infrastructure');
    await page.waitForSelector('.classroom-card');
    
    console.log('4. Testing modal styling...');
    
    // Wait for the page to fully load
    await page.waitForTimeout(2000);
    
    // Check modal structure
    const modalExists = await page.$('#classroomModal');
    console.log('✅ Modal element exists:', !!modalExists);
    
    // Check if classroom cards exist
    const classroomCards = await page.$$('.classroom-card');
    console.log('✅ Found classroom cards:', classroomCards.length);
    
    if (classroomCards.length > 0) {
      console.log('5. Testing modal opening...');
      
      // Click on the first classroom card
      await classroomCards[0].click();
      
      // Wait for modal to appear
      await page.waitForTimeout(1000);
      
      // Check if modal is visible
      const modalVisible = await page.evaluate(() => {
        const modal = document.getElementById('classroomModal');
        return modal && !modal.classList.contains('hidden');
      });
      
      console.log('✅ Modal opens successfully:', modalVisible);
      
      if (modalVisible) {
        // Take a screenshot of the modal
        await page.screenshot({ 
          path: 'modal-styling-test.png',
          fullPage: true
        });
        console.log('📸 Screenshot saved as modal-styling-test.png');
        
        // Test modal centering and responsiveness
        const modalInfo = await page.evaluate(() => {
          const modal = document.getElementById('classroomModal');
          const modalContent = modal.querySelector('.modal-content');
          const rect = modalContent.getBoundingClientRect();
          
          return {
            centered: rect.left > 0 && rect.right < window.innerWidth,
            verticalCentered: rect.top > 0 && rect.bottom < window.innerHeight,
            width: rect.width,
            height: rect.height,
            hasBackdrop: modal.classList.contains('bg-black'),
            hasBlur: modal.classList.contains('backdrop-blur-sm'),
            hasAnimation: modalContent.classList.contains('modal-content')
          };
        });
        
        console.log('📐 Modal positioning and styling:');
        console.log('   ✅ Horizontally centered:', modalInfo.centered);
        console.log('   ✅ Vertically centered:', modalInfo.verticalCentered);
        console.log('   ✅ Has backdrop:', modalInfo.hasBackdrop);
        console.log('   ✅ Has blur effect:', modalInfo.hasBlur);
        console.log('   ✅ Has animation class:', modalInfo.hasAnimation);
        console.log('   📏 Dimensions:', modalInfo.width + 'x' + modalInfo.height);
        
        // Test close button
        console.log('6. Testing close functionality...');
        await page.click('#closeModalBtn');
        await page.waitForTimeout(500);
        
        const modalClosed = await page.evaluate(() => {
          const modal = document.getElementById('classroomModal');
          return modal.classList.contains('hidden');
        });
        
        console.log('✅ Modal closes successfully:', modalClosed);
      }
    }
    
    console.log('\n🎨 Modal Styling Test Complete!');
    console.log('\n✨ Improvements Made:');
    console.log('   🎯 Perfect centering with flexbox');
    console.log('   🌈 Beautiful gradient headers');
    console.log('   📱 Fully responsive design');
    console.log('   ✨ Smooth animations and transitions');
    console.log('   🎨 Enhanced visual hierarchy');
    console.log('   📜 Custom scrollbars');
    console.log('   🔄 Loading states with animations');
    console.log('   💫 Backdrop blur effects');
    console.log('   🎪 Interactive hover effects');
    
    // Keep browser open for manual inspection
    console.log('\n👀 Browser will stay open for manual inspection...');
    console.log('   Click on classroom cards to test the modal!');
    
    // Wait for user to manually close
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.error('Error testing modal styling:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testModalStyling();
