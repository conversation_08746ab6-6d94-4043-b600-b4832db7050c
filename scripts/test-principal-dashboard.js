/**
 * Test Principal Dashboard Access
 */

const axios = require('axios');

async function testPrincipalDashboard() {
  try {
    console.log('🧪 Testing Principal Dashboard Access...\n');

    const baseURL = 'http://localhost:3018';

    // Create a session by doing demo login first
    console.log('1. Performing demo login...');
    
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful, got session cookies');

    // Now access the principal dashboard with the session
    console.log('\n2. Accessing principal dashboard...');
    
    const dashboardResponse = await axios.get(`${baseURL}/principal/dashboard`, {
      headers: {
        'Cookie': cookies ? cookies.join('; ') : ''
      }
    });

    if (dashboardResponse.status === 200) {
      console.log('✅ Principal dashboard accessible');
      
      // Check if the response contains principal-specific content
      const content = dashboardResponse.data;
      
      if (content.includes('Principal Command Center')) {
        console.log('✅ Principal Command Center title found');
      } else {
        console.log('❌ Principal Command Center title NOT found');
      }
      
      if (content.includes('principal-primary')) {
        console.log('✅ Principal theme colors found');
      } else {
        console.log('❌ Principal theme colors NOT found');
      }
      
      if (content.includes('Executive leadership')) {
        console.log('✅ Executive leadership text found');
      } else {
        console.log('❌ Executive leadership text NOT found');
      }
      
      if (content.includes('Strategic Operations')) {
        console.log('✅ Strategic Operations content found');
      } else {
        console.log('❌ Strategic Operations content NOT found');
      }
      
      if (content.includes('layouts/principal')) {
        console.log('✅ Principal layout being used');
      } else {
        console.log('❌ Principal layout NOT being used');
      }
      
      // Check for teacher-specific content (should NOT be present)
      if (content.includes('teacher-primary') || content.includes('Teacher Dashboard')) {
        console.log('❌ Teacher content found (this is bad!)');
      } else {
        console.log('✅ No teacher content found');
      }
      
    } else {
      console.log('❌ Principal dashboard not accessible, status:', dashboardResponse.status);
    }

    console.log('\n=== Principal Dashboard Test Complete ===');
    console.log('\n🎯 Manual Testing:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Click the navy blue "Principal" card');
    console.log('3. Should see navy & gold theme with "Principal Command Center"');
    console.log('4. Check for executive terminology and leadership badges');

  } catch (error) {
    if (error.response && error.response.status === 302) {
      console.log('✅ Got redirect (expected for demo login)');
      console.log('   Redirect to:', error.response.headers.location);
    } else {
      console.error('Error testing principal dashboard:', error.message);
    }
  }
}

// Run the test
testPrincipalDashboard();
