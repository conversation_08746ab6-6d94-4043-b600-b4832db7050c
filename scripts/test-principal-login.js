/**
 * Test Principal Login
 * Tests the principal login functionality
 */

const axios = require('axios');

async function testPrincipalLogin() {
  try {
    console.log('🧪 Testing Principal Login...\n');

    const baseURL = 'http://localhost:3018';

    // 1. Test demo login page
    console.log('1. Testing demo login page...');
    try {
      const demoResponse = await axios.get(`${baseURL}/demo-login`);
      if (demoResponse.status === 200) {
        console.log('✅ Demo login page accessible');
        
        // Check if principal option is in the response
        if (demoResponse.data.includes('Principal')) {
          console.log('✅ Principal option found in demo login');
        } else {
          console.log('❌ Principal option NOT found in demo login');
        }
      }
    } catch (error) {
      console.log('❌ Demo login page not accessible:', error.message);
    }

    // 2. Test demo login POST for principal
    console.log('\n2. Testing principal demo login...');
    try {
      const loginResponse = await axios.post(`${baseURL}/demo-login`, {
        role: 'principal'
      }, {
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400; // Accept redirects
        }
      });
      
      if (loginResponse.status === 302) {
        console.log('✅ Principal demo login successful (redirect)');
        console.log('   Redirect location:', loginResponse.headers.location);
        
        if (loginResponse.headers.location === '/principal/dashboard') {
          console.log('✅ Correct redirect to principal dashboard');
        } else {
          console.log('❌ Incorrect redirect location');
        }
      }
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Principal demo login successful (redirect)');
        console.log('   Redirect location:', error.response.headers.location);
      } else {
        console.log('❌ Principal demo login failed:', error.message);
      }
    }

    // 3. Test regular login
    console.log('\n3. Testing regular principal login...');
    try {
      const regularLoginResponse = await axios.post(`${baseURL}/login`, {
        username: 'principal',
        password: 'principal123'
      }, {
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });
      
      if (regularLoginResponse.status === 302) {
        console.log('✅ Regular principal login successful (redirect)');
        console.log('   Redirect location:', regularLoginResponse.headers.location);
      }
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Regular principal login successful (redirect)');
        console.log('   Redirect location:', error.response.headers.location);
      } else {
        console.log('❌ Regular principal login failed:', error.message);
      }
    }

    console.log('\n=== Principal Login Test Complete ===');
    console.log('\n🎯 Manual Testing Instructions:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Look for the navy blue "Principal" card');
    console.log('3. Click "Login as Principal"');
    console.log('4. Should redirect to: http://localhost:3018/principal/dashboard');
    console.log('5. Verify the executive navy & gold theme');
    console.log('\n📋 Expected Features:');
    console.log('- Navy blue & gold color scheme');
    console.log('- "Principal Command Center" header');
    console.log('- Executive leadership terminology');
    console.log('- Strategic operations monitoring');
    console.log('- Leadership badges and professional styling');

  } catch (error) {
    console.error('Error testing principal login:', error.message);
  }
}

// Run the test
testPrincipalLogin();
