/**
 * Test All Principal Routes
 */

const axios = require('axios');

async function testPrincipalRoutes() {
  try {
    console.log('🧪 Testing All Principal Routes...\n');

    const baseURL = 'http://localhost:3018';
    
    // First, login to get session cookies
    console.log('1. Performing demo login...');
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful, got session cookies\n');

    // Define all principal routes to test
    const routes = [
      { path: '/principal/dashboard', name: 'Dashboard' },
      { path: '/principal/academic-progress', name: 'Academic Progress' },
      { path: '/principal/teacher-management', name: 'Teacher Management' },
      { path: '/principal/teacher-timetables', name: 'Teacher Timetables' },
      { path: '/principal/student-analytics', name: 'Student Analytics' },
      { path: '/principal/infrastructure', name: 'Infrastructure' },
      { path: '/principal/reports', name: 'Reports' },
      { path: '/principal/reports/academic', name: 'Academic Reports' },
      { path: '/principal/reports/attendance', name: 'Attendance Reports' },
      { path: '/principal/reports/performance', name: 'Performance Reports' }
    ];

    console.log('2. Testing all principal routes...\n');

    const results = [];

    for (const route of routes) {
      try {
        const response = await axios.get(`${baseURL}${route.path}`, {
          headers: {
            'Cookie': cookies ? cookies.join('; ') : ''
          },
          timeout: 10000
        });

        if (response.status === 200) {
          const content = response.data;
          
          // Check for principal-specific content
          const hasCommandCenter = content.includes('Command Center') || content.includes('Principal');
          const hasPrincipalTheme = content.includes('principal-primary') || content.includes('executive-card');
          const hasNavyTheme = content.includes('layouts/principal');
          
          results.push({
            route: route.path,
            name: route.name,
            status: 'SUCCESS',
            hasCommandCenter,
            hasPrincipalTheme,
            hasNavyTheme
          });
          
          console.log(`✅ ${route.name}: SUCCESS`);
          if (hasCommandCenter) console.log(`   ✅ Command Center branding found`);
          if (hasPrincipalTheme) console.log(`   ✅ Principal theme found`);
          if (hasNavyTheme) console.log(`   ✅ Principal layout found`);
          
        } else {
          results.push({
            route: route.path,
            name: route.name,
            status: `ERROR ${response.status}`,
            hasCommandCenter: false,
            hasPrincipalTheme: false,
            hasNavyTheme: false
          });
          console.log(`❌ ${route.name}: ERROR ${response.status}`);
        }
      } catch (error) {
        results.push({
          route: route.path,
          name: route.name,
          status: `ERROR: ${error.message}`,
          hasCommandCenter: false,
          hasPrincipalTheme: false,
          hasNavyTheme: false
        });
        console.log(`❌ ${route.name}: ERROR - ${error.message}`);
      }
      console.log(''); // Empty line for readability
    }

    // Summary
    console.log('\n=== PRINCIPAL ROUTES TEST SUMMARY ===\n');
    
    const successful = results.filter(r => r.status === 'SUCCESS');
    const failed = results.filter(r => r.status !== 'SUCCESS');
    
    console.log(`✅ Successful Routes: ${successful.length}/${routes.length}`);
    console.log(`❌ Failed Routes: ${failed.length}/${routes.length}\n`);
    
    if (successful.length > 0) {
      console.log('✅ WORKING ROUTES:');
      successful.forEach(result => {
        console.log(`   - ${result.name} (${result.route})`);
      });
      console.log('');
    }
    
    if (failed.length > 0) {
      console.log('❌ FAILED ROUTES:');
      failed.forEach(result => {
        console.log(`   - ${result.name} (${result.route}) - ${result.status}`);
      });
      console.log('');
    }

    // Theme Analysis
    const withTheme = successful.filter(r => r.hasPrincipalTheme);
    const withLayout = successful.filter(r => r.hasNavyTheme);
    
    console.log('🎨 THEME ANALYSIS:');
    console.log(`   - Routes with Principal Theme: ${withTheme.length}/${successful.length}`);
    console.log(`   - Routes with Principal Layout: ${withLayout.length}/${successful.length}`);
    
    console.log('\n🎯 MANUAL TESTING:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Click "Login as Principal"');
    console.log('3. Test sidebar navigation links');
    console.log('4. Verify navy & gold theme throughout');

  } catch (error) {
    console.error('Error testing principal routes:', error.message);
  }
}

// Run the test
testPrincipalRoutes();
