/**
 * Test script to verify Principal setup
 */

const db = require('../config/database');

async function testPrincipalSetup() {
  try {
    console.log('Testing Principal setup...\n');

    // 1. Check if principal role exists in users table
    console.log('1. Checking users table structure...');
    const [columns] = await db.query(`
      SHOW COLUMNS FROM users WHERE Field = 'role'
    `);
    
    if (columns.length > 0) {
      console.log('✅ Users table role column found');
      console.log('   Type:', columns[0].Type);
      
      if (columns[0].Type.includes('principal')) {
        console.log('✅ Principal role is included in enum');
      } else {
        console.log('❌ Principal role NOT found in enum');
        console.log('   Run: database/migrations/add_principal_role.sql');
      }
    } else {
      console.log('❌ Role column not found in users table');
    }

    // 2. Check if principal user exists
    console.log('\n2. Checking for principal users...');
    const [principalUsers] = await db.query(`
      SELECT id, username, name, email, role, is_active 
      FROM users 
      WHERE role = 'principal'
    `);
    
    if (principalUsers.length > 0) {
      console.log('✅ Principal users found:');
      principalUsers.forEach(user => {
        console.log(`   - ${user.name} (${user.username}) - ${user.email} - Active: ${user.is_active}`);
      });
    } else {
      console.log('❌ No principal users found');
      console.log('   Run: database/migrations/add_principal_role.sql to create sample principal user');
    }

    // 3. Check required tables for principal functionality
    console.log('\n3. Checking required tables...');
    const requiredTables = [
      'users',
      'classes', 
      'subjects',
      'teacher_lectures'
    ];

    for (const table of requiredTables) {
      const [tableExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = ?
      `, [table]);
      
      if (tableExists[0].table_exists > 0) {
        console.log(`✅ Table ${table} exists`);
      } else {
        console.log(`❌ Table ${table} does not exist`);
      }
    }

    // 4. Check sample data
    console.log('\n4. Checking sample data...');
    
    const [teacherCount] = await db.query(`
      SELECT COUNT(*) as count FROM users WHERE role = 'teacher' AND is_active = 1
    `);
    console.log(`   Teachers: ${teacherCount[0].count}`);
    
    const [studentCount] = await db.query(`
      SELECT COUNT(*) as count FROM users WHERE role = 'student' AND is_active = 1
    `);
    console.log(`   Students: ${studentCount[0].count}`);
    
    const [classCount] = await db.query(`
      SELECT COUNT(*) as count FROM classes
    `);
    console.log(`   Classes: ${classCount[0].count}`);
    
    const [subjectCount] = await db.query(`
      SELECT COUNT(*) as count FROM subjects
    `);
    console.log(`   Subjects: ${subjectCount[0].count}`);

    // 5. Test principal dashboard query
    console.log('\n5. Testing principal dashboard queries...');
    try {
      const [dashboardStats] = await db.query(`
        SELECT 
          (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
          (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
          (SELECT COUNT(*) FROM classes) as total_classes,
          (SELECT COUNT(*) FROM subjects) as total_subjects
      `);
      
      console.log('✅ Dashboard stats query successful:');
      console.log(`   Teachers: ${dashboardStats[0].total_teachers}`);
      console.log(`   Students: ${dashboardStats[0].total_students}`);
      console.log(`   Classes: ${dashboardStats[0].total_classes}`);
      console.log(`   Subjects: ${dashboardStats[0].total_subjects}`);
    } catch (error) {
      console.log('❌ Dashboard stats query failed:', error.message);
    }

    console.log('\n=== Principal Setup Test Complete ===');
    console.log('\nTo access the Principal view:');
    console.log('1. Run the database migration: database/migrations/add_principal_role.sql');
    console.log('2. Login with username: principal, password: principal123');
    console.log('3. Navigate to: /principal/dashboard');
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing principal setup:', error);
    process.exit(1);
  }
}

// Run the test
testPrincipalSetup();
