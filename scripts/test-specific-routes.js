/**
 * Test Specific Principal Routes
 */

const axios = require('axios');

async function testSpecificRoutes() {
  try {
    console.log('🧪 Testing Specific Principal Routes...\n');

    const baseURL = 'http://localhost:3018';
    
    // First, login to get session cookies
    console.log('1. Performing demo login...');
    const loginResponse = await axios.post(`${baseURL}/demo-login`, {
      role: 'principal'
    }, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400;
      }
    });

    const cookies = loginResponse.headers['set-cookie'];
    console.log('✅ Demo login successful\n');

    // Test the specific routes that were failing
    const routesToTest = [
      { path: '/principal/academic-progress', name: 'Academic Intelligence' },
      { path: '/principal/teacher-management', name: 'Faculty Operations' }
    ];

    console.log('2. Testing specific routes...\n');

    for (const route of routesToTest) {
      try {
        console.log(`Testing ${route.name}...`);
        const response = await axios.get(`${baseURL}${route.path}`, {
          headers: {
            'Cookie': cookies ? cookies.join('; ') : ''
          },
          timeout: 10000
        });

        if (response.status === 200) {
          const content = response.data;
          
          // Check for principal-specific content
          const hasCommandCenter = content.includes('Command Center') || content.includes('Intelligence');
          const hasPrincipalTheme = content.includes('principal-primary') || content.includes('executive-card');
          
          console.log(`✅ ${route.name}: SUCCESS`);
          if (hasCommandCenter) console.log(`   ✅ Command Center branding found`);
          if (hasPrincipalTheme) console.log(`   ✅ Principal theme found`);
          
        } else {
          console.log(`❌ ${route.name}: ERROR ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${route.name}: ERROR - ${error.message}`);
        if (error.response && error.response.status === 500) {
          console.log(`   💡 This is likely a .toFixed() error that we just fixed`);
        }
      }
      console.log(''); // Empty line for readability
    }

    console.log('🎯 MANUAL TESTING:');
    console.log('1. Open: http://localhost:3018/demo-login');
    console.log('2. Click "Login as Principal"');
    console.log('3. Test Academic Intelligence and Faculty Operations');

  } catch (error) {
    console.error('Error testing routes:', error.message);
  }
}

// Run the test
testSpecificRoutes();
