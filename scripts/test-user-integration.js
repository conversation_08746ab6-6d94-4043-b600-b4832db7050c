const mysql = require('mysql2/promise');

async function testUserIntegration() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 TESTING USER TABLE INTEGRATION WITH ENHANCED API\n');

        // Test the exact query that the API uses
        const teacherId = 103; // CS teacher user ID
        
        console.log('📋 Testing Enhanced API Query:');
        console.log('=' .repeat(60));
        
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        if (teacherData.length === 0) {
            console.log('❌ No teacher data found');
            return;
        }

        const teacher = teacherData[0];
        
        console.log('✅ TEACHER DATA RETRIEVED SUCCESSFULLY\n');
        
        // Display User Table Data
        console.log('👤 USER TABLE DATA:');
        console.log('=' .repeat(40));
        console.log(`User ID: ${teacher.user_id}`);
        console.log(`Username: ${teacher.username}`);
        console.log(`Name: ${teacher.name}`);
        console.log(`Full Name: ${teacher.full_name}`);
        console.log(`Email: ${teacher.email}`);
        console.log(`Role: ${teacher.role}`);
        console.log(`Bio: ${teacher.bio || 'Not provided'}`);
        console.log(`Date of Birth: ${teacher.date_of_birth || 'Not provided'}`);
        console.log(`Account Created: ${teacher.created_at}`);
        console.log(`Last Login: ${teacher.last_login || 'Never logged in'}`);
        console.log(`Account Active: ${teacher.is_active ? 'Yes' : 'No'}`);
        console.log(`Profile Image: ${teacher.profile_image || 'Not set'}`);
        console.log(`Subjects (User): ${teacher.subjects || 'Not specified'}`);

        // Display Staff Table Data
        console.log('\n👨‍🏫 STAFF TABLE DATA:');
        console.log('=' .repeat(40));
        console.log(`Staff ID: ${teacher.staff_id || 'Not found'}`);
        console.log(`Employee ID: ${teacher.employee_id || 'Not set'}`);
        console.log(`Designation: ${teacher.designation || 'Not set'}`);
        console.log(`Department: ${teacher.department || 'Not set'}`);
        console.log(`Joining Date: ${teacher.joining_date || 'Not set'}`);
        console.log(`Employment Type: ${teacher.employment_type || 'Not set'}`);
        console.log(`Phone: ${teacher.phone || 'Not provided'}`);
        console.log(`Alternate Phone: ${teacher.alternate_phone || 'Not provided'}`);
        console.log(`Emergency Contact: ${teacher.emergency_contact || 'Not provided'}`);
        console.log(`Address: ${teacher.address || 'Not provided'}`);
        console.log(`City: ${teacher.city || 'Not provided'}`);
        console.log(`State: ${teacher.state || 'Not provided'}`);
        console.log(`Current School: ${teacher.current_school || 'Not specified'}`);
        console.log(`Current Salary: ${teacher.current_salary || 'Not specified'}`);
        console.log(`Subjects Taught (Staff): ${teacher.subjects_taught || 'Not specified'}`);
        console.log(`Classes Handled: ${teacher.classes_handled || 'Not specified'}`);
        console.log(`Total Experience: ${teacher.total_experience_years || 0} years`);
        console.log(`Teaching Experience: ${teacher.teaching_experience_years || 0} years`);
        console.log(`Awards Received: ${teacher.awards_received || 'None listed'}`);
        console.log(`Publications: ${teacher.publications || 'None listed'}`);
        console.log(`Staff Notes: ${teacher.staff_notes || 'No notes'}`);
        console.log(`Staff Active: ${teacher.staff_active ? 'Yes' : 'No'}`);

        // Test calculated fields that API would create
        console.log('\n🔧 CALCULATED FIELDS (API Processing):');
        console.log('=' .repeat(50));
        
        const displayName = teacher.full_name || teacher.name || teacher.username;
        console.log(`Display Name: ${displayName}`);
        
        const primaryEmail = teacher.email;
        console.log(`Primary Email: ${primaryEmail}`);
        
        const dateOfBirth = teacher.date_of_birth;
        console.log(`Date of Birth: ${dateOfBirth || 'Not provided'}`);
        
        // Calculate age if date of birth is available
        if (teacher.date_of_birth) {
            const today = new Date();
            const birthDate = new Date(teacher.date_of_birth);
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            console.log(`Calculated Age: ${age} years`);
        }
        
        const lastLoginFormatted = teacher.last_login ? new Date(teacher.last_login).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Never logged in';
        console.log(`Last Login Formatted: ${lastLoginFormatted}`);
        
        const accountCreated = teacher.created_at ? new Date(teacher.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }) : 'Unknown';
        console.log(`Account Created Formatted: ${accountCreated}`);
        
        const accountStatus = teacher.is_active ? 'Active' : 'Inactive';
        console.log(`Account Status: ${accountStatus}`);

        // Test if staff_id exists for related table queries
        console.log('\n🔗 RELATED TABLE AVAILABILITY:');
        console.log('=' .repeat(40));
        console.log(`Staff ID Available: ${teacher.staff_id ? 'Yes (' + teacher.staff_id + ')' : 'No'}`);
        
        if (teacher.staff_id) {
            // Test educational qualifications
            const [educationCount] = await connection.execute(
                'SELECT COUNT(*) as count FROM staff_educational_qualifications WHERE staff_id = ?',
                [teacher.staff_id]
            );
            console.log(`Educational Qualifications: ${educationCount[0].count} records`);
            
            // Test professional experience
            const [experienceCount] = await connection.execute(
                'SELECT COUNT(*) as count FROM staff_professional_experience WHERE staff_id = ?',
                [teacher.staff_id]
            );
            console.log(`Professional Experience: ${experienceCount[0].count} records`);
            
            // Test certifications
            const [certCount] = await connection.execute(
                'SELECT COUNT(*) as count FROM staff_certifications WHERE staff_id = ?',
                [teacher.staff_id]
            );
            console.log(`Certifications: ${certCount[0].count} records`);
            
            // Test skills
            const [skillsCount] = await connection.execute(
                'SELECT COUNT(*) as count FROM staff_skills WHERE staff_id = ?',
                [teacher.staff_id]
            );
            console.log(`Skills: ${skillsCount[0].count} records`);
        }

        console.log('\n✅ USER TABLE INTEGRATION TEST COMPLETED SUCCESSFULLY!');
        console.log('\n📊 INTEGRATION SUMMARY:');
        console.log(`- User data: ✅ Available`);
        console.log(`- Staff data: ${teacher.staff_id ? '✅ Available' : '❌ Missing'}`);
        console.log(`- Bio information: ${teacher.bio ? '✅ Available' : '❌ Not provided'}`);
        console.log(`- Date of birth: ${teacher.date_of_birth ? '✅ Available' : '❌ Not provided'}`);
        console.log(`- Account status: ✅ Available (${accountStatus})`);
        console.log(`- Last login: ${teacher.last_login ? '✅ Available' : '⚠️ Never logged in'}`);

    } catch (error) {
        console.error('Error testing user integration:', error);
    } finally {
        await connection.end();
    }
}

testUserIntegration();
