/**
 * <PERSON><PERSON><PERSON> to update admin password
 */

const db = require('../config/database');
const bcrypt = require('bcrypt');

async function updateAdminPassword() {
  try {
    console.log('Updating admin password...');
    
    // Hash the password
    const password = 'admin123';
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Update the admin user password
    await db.query(`
      UPDATE users SET password = ? WHERE username = 'admin_user'
    `, [hashedPassword]);
    
    console.log('Admin password updated successfully');
    console.log('Username: admin_user');
    console.log('Password: admin123');
    
    // Also update the other admin user password
    await db.query(`
      UPDATE users SET password = ? WHERE username = 'itadmin485'
    `, [hashedPassword]);
    
    console.log('itadmin485 password updated successfully');
    console.log('Username: itadmin485');
    console.log('Password: admin123');
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating admin password:', error);
    process.exit(1);
  }
}

// Run the update
updateAdminPassword();
