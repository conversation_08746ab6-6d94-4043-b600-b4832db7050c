/**
 * <PERSON><PERSON><PERSON> to update all laptops to have the same model and manufacturer
 * 
 * This script updates all laptops in the inventory system to have:
 * - Model: "ACER TRAVELMATE 246M"
 * - Manufacturer: "ACER"
 * 
 * It preserves the original names of the laptops.
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt user for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'exam_prep_platform'
    });
    
    console.log('Connected to database');
    
    // Find all laptops
    const [laptops] = await connection.query(`
      SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer
      FROM inventory_items i
      JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%'
      ORDER BY i.name
    `);
    
    if (laptops.length === 0) {
      console.log('No laptops found in the inventory system.');
      return;
    }
    
    console.log(`\nFound ${laptops.length} laptops in the inventory system.`);
    console.log('\nCurrent laptop details:');
    console.log('----------------------');
    
    laptops.forEach((laptop, index) => {
      console.log(`${index + 1}. ID: ${laptop.item_id}, Name: ${laptop.name}, Model: ${laptop.model || 'N/A'}, Manufacturer: ${laptop.manufacturer || 'N/A'}, Serial: ${laptop.serial_number || 'N/A'}`);
    });
    
    // Confirm update
    const confirm = await prompt('\nUpdate ALL laptops to have model "ACER TRAVELMATE 246M" and manufacturer "ACER"? (y/n): ');
    
    if (confirm.toLowerCase() !== 'y') {
      console.log('Operation cancelled.');
      return;
    }
    
    // Update all laptops
    const [result] = await connection.query(
      'UPDATE inventory_items SET model = ?, manufacturer = ? WHERE item_id IN (?)',
      ['ACER TRAVELMATE 246M', 'ACER', laptops.map(laptop => laptop.item_id)]
    );
    
    console.log(`\nUpdated ${result.affectedRows} laptops successfully!`);
    
    // Get updated laptops
    const [updatedLaptops] = await connection.query(`
      SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer
      FROM inventory_items i
      JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%'
      ORDER BY i.name
    `);
    
    console.log('\nUpdated laptop details:');
    console.log('----------------------');
    
    updatedLaptops.forEach((laptop, index) => {
      console.log(`${index + 1}. ID: ${laptop.item_id}, Name: ${laptop.name}, Model: ${laptop.model || 'N/A'}, Manufacturer: ${laptop.manufacturer || 'N/A'}, Serial: ${laptop.serial_number || 'N/A'}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    rl.close();
  }
}

main();
