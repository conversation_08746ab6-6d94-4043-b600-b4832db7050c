const mysql = require('mysql2/promise');

async function updateCSTeacherData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Updating CS Teacher data...');

        // First, update the user table (keep original email)
        await connection.execute(`
            UPDATE users
            SET
                date_of_birth = '1988-11-30',
                name = 'CS Teacher',
                full_name = 'CS Teacher'
            WHERE email = '<EMAIL>'
        `);

        // Get the user ID
        const [userResult] = await connection.execute(
            'SELECT id FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (userResult.length === 0) {
            console.log('User not found, creating new user...');
            // Create user if not exists
            await connection.execute(`
                INSERT INTO users (username, email, password, name, full_name, role, is_active, bio, date_of_birth, last_login)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
                'cste<PERSON>',
                '<EMAIL>',
                '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQXwk/Yw1QDwHiGrQAZRlkFkAVEtfC',
                'CS Teacher',
                'CS Teacher',
                'teacher',
                1,
                'Computer Science teacher with expertise in programming and data structures.',
                '1988-11-30'
            ]);

            const [newUserResult] = await connection.execute(
                'SELECT id FROM users WHERE email = ?',
                ['<EMAIL>']
            );
            var userId = newUserResult[0].id;
        } else {
            var userId = userResult[0].id;
        }

        console.log('User ID:', userId);

        // Check if staff record exists
        const [staffCheck] = await connection.execute(
            'SELECT id FROM staff WHERE user_id = ?',
            [userId]
        );

        if (staffCheck.length === 0) {
            // Insert new staff record
            await connection.execute(`
                INSERT INTO staff (
                    user_id, employee_id, designation, department, joining_date,
                    phone, employment_type,
                    graduation_degree, graduation_university, graduation_year, graduation_specialization,
                    total_experience_years, teaching_experience_years,
                    previous_organizations, subjects_taught,
                    special_skills, languages_known
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                '607564904',
                'Computer Science Teacher',
                'Academic',
                '2015-11-20',
                '+91 7888610740',
                'permanent',
                'B.Tech',
                'Computer Science and Engineering',
                2011,
                'Computer Science and Engineering',
                4,
                2,
                'Software Engineer (June 2011 - Oct 2013), Physics Tutor (Nov 2013 - Nov 2015)',
                'Computer Science, Programming, Data Structures',
                'Programming, Software Development, Database Management',
                'English, Hindi, Punjabi'
            ]);
        } else {
            // Update existing staff record
            await connection.execute(`
                UPDATE staff SET
                    employee_id = '607564904',
                    designation = 'Computer Science Teacher',
                    department = 'Academic',
                    joining_date = '2015-11-20',
                    phone = '+91 7888610740',
                    employment_type = 'permanent',
                    graduation_degree = 'B.Tech',
                    graduation_university = 'Computer Science and Engineering',
                    graduation_year = 2011,
                    graduation_specialization = 'Computer Science and Engineering',
                    total_experience_years = 4,
                    teaching_experience_years = 2,
                    previous_organizations = 'Software Engineer (June 2011 - Oct 2013), Physics Tutor (Nov 2013 - Nov 2015)',
                    subjects_taught = 'Computer Science, Programming, Data Structures',
                    special_skills = 'Programming, Software Development, Database Management',
                    languages_known = 'English, Hindi, Punjabi'
                WHERE user_id = ?
            `, [userId]);
        }

        console.log('CS Teacher data updated successfully!');

    } catch (error) {
        console.error('Error updating CS teacher data:', error);
    } finally {
        await connection.end();
    }
}

updateCSTeacherData();
