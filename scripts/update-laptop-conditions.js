/**
 * <PERSON><PERSON><PERSON> to update laptop condition data for existing laptops
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Laptop data from the image
const laptopData = [
    { laptop_no: 'CR-01', serial_no: 'UNVA8S1001F3879436' },
    { laptop_no: 'CR-02', serial_no: 'UNVA8S1001F3879401' },
    { laptop_no: 'CR-03', serial_no: 'UNVA8S1001F3879487' },
    { laptop_no: 'CR-04', serial_no: 'UNVA8S1001F3879480' },
    { laptop_no: 'CR-05', serial_no: 'UNVA8S1001F3879475' },
    { laptop_no: 'CR-06', serial_no: 'UNVA8S1001F3879452' },
    { laptop_no: 'CR-07', serial_no: 'UNVA8S1001F3879393' },
    { laptop_no: 'CR-08', serial_no: 'UNVA8S1001F3879474' },
    { laptop_no: 'CR-09', serial_no: 'UNVA8S1001F3879391' },
    { laptop_no: 'CR-10', serial_no: 'UNVA8S1001F3879399' },
    { laptop_no: 'CR-11', serial_no: 'UNVA8S1001F3879441' },
    { laptop_no: 'CR-12', serial_no: 'UNVA8S1001F3879370' },
    { laptop_no: 'CR-13', serial_no: 'UNVA8S1001F3879486' },
    { laptop_no: 'CR-14', serial_no: 'UNVA8S1001F3879397' },
    { laptop_no: 'CR-15', serial_no: 'UNVA8S1001F3879427' },
    { laptop_no: 'CR-16', serial_no: 'UNVA8S1001F3879455' },
    { laptop_no: 'CR-17', serial_no: 'UNVA8S1001F3879459' },
    { laptop_no: 'CR-18', serial_no: 'UNVA8S1001F3879409' },
    { laptop_no: 'SL-01', serial_no: 'UNVA8S1001F3875894' },
    { laptop_no: 'SL-02', serial_no: 'UNVA8S1001F3875893' },
    { laptop_no: 'SL-03', serial_no: 'UNVA8S1001F3879450' }
];

async function updateLaptopConditions() {
    let connection;
    
    try {
        // Create database connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'exam_prep_platform'
        });
        
        console.log('Connected to database');
        
        // Update each laptop's condition fields
        for (const laptop of laptopData) {
            // Find the laptop by serial number
            const [items] = await connection.query(
                'SELECT * FROM inventory_items WHERE serial_number = ?',
                [laptop.serial_no]
            );
            
            if (items.length === 0) {
                console.log(`Laptop ${laptop.laptop_no} with serial ${laptop.serial_no} not found, skipping`);
                continue;
            }
            
            const item = items[0];
            
            // Update laptop condition fields
            await connection.query(`
                UPDATE inventory_items SET
                    physical_damage = ?,
                    keyboard_condition = ?,
                    touchpad_condition = ?,
                    hdmi_port_condition = ?,
                    ethernet_wifi_condition = ?,
                    vga_port_condition = ?,
                    usb_port_condition = ?,
                    speaker_port_condition = ?,
                    speakers_condition = ?,
                    display_condition = ?,
                    cd_drive_condition = ?,
                    webcam_condition = ?,
                    charger_port_condition = ?,
                    os_drivers_condition = ?,
                    laptop_bag_condition = ?
                WHERE item_id = ?
            `, [
                'None', // physical_damage
                'Working', // keyboard_condition
                'Working', // touchpad_condition
                'Working', // hdmi_port_condition
                'Both Working', // ethernet_wifi_condition
                'Working', // vga_port_condition
                'All Working', // usb_port_condition
                'Working', // speaker_port_condition
                'Working', // speakers_condition
                'Perfect', // display_condition
                'Working', // cd_drive_condition
                'Working', // webcam_condition
                'Both Working', // charger_port_condition
                'Fully Functional', // os_drivers_condition
                'Good Condition', // laptop_bag_condition
                item.item_id
            ]);
            
            console.log(`Updated laptop ${laptop.laptop_no} with ID:`, item.item_id);
        }
        
        console.log('Laptop condition update completed successfully');
        
    } catch (error) {
        console.error('Error updating laptop conditions:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('Database connection closed');
        }
    }
}

// Run the script
updateLaptopConditions();
