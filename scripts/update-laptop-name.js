/**
 * Script to update a laptop name to match its model
 * 
 * This script updates the name of a laptop in the inventory system to match its model.
 * It can be run with:
 * node scripts/update-laptop-name.js [laptop_id]
 * 
 * If no laptop_id is provided, it will list all laptops and prompt for selection.
 */

require('dotenv').config();
const mysql = require('mysql2/promise');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt user for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'exam_prep_platform'
    });
    
    console.log('Connected to database');
    
    // Get laptop ID from command line arguments or prompt user
    let laptopId = process.argv[2];
    
    if (!laptopId) {
      // Find all laptops
      const [laptops] = await connection.query(`
        SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer
        FROM inventory_items i
        JOIN inventory_categories c ON i.category_id = c.category_id
        WHERE c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%' OR i.name LIKE '%ACER%'
        ORDER BY i.name
      `);
      
      if (laptops.length === 0) {
        console.log('No laptops found in the inventory system.');
        return;
      }
      
      console.log('\nAvailable laptops:');
      console.log('------------------');
      laptops.forEach((laptop, index) => {
        console.log(`${index + 1}. ID: ${laptop.item_id}, Name: ${laptop.name}, Model: ${laptop.model || 'N/A'}, Manufacturer: ${laptop.manufacturer || 'N/A'}, Serial: ${laptop.serial_number || 'N/A'}`);
      });
      
      const selection = await prompt('\nEnter the number of the laptop to update (or "q" to quit): ');
      
      if (selection.toLowerCase() === 'q') {
        console.log('Operation cancelled.');
        return;
      }
      
      const selectedIndex = parseInt(selection) - 1;
      if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= laptops.length) {
        console.log('Invalid selection.');
        return;
      }
      
      laptopId = laptops[selectedIndex].item_id;
    }
    
    // Get current laptop details
    const [items] = await connection.query(
      'SELECT * FROM inventory_items WHERE item_id = ?',
      [laptopId]
    );
    
    if (items.length === 0) {
      console.log(`No laptop found with ID ${laptopId}`);
      return;
    }
    
    const laptop = items[0];
    console.log('\nCurrent laptop details:');
    console.log('----------------------');
    console.log(`ID: ${laptop.item_id}`);
    console.log(`Name: ${laptop.name}`);
    console.log(`Model: ${laptop.model || 'N/A'}`);
    console.log(`Manufacturer: ${laptop.manufacturer || 'N/A'}`);
    console.log(`Serial Number: ${laptop.serial_number || 'N/A'}`);
    
    if (!laptop.model) {
      console.log('\nThis laptop does not have a model specified. Cannot update name to match model.');
      return;
    }
    
    // Confirm update
    const confirm = await prompt(`\nUpdate this laptop name to "${laptop.model}"? (y/n): `);
    
    if (confirm.toLowerCase() !== 'y') {
      console.log('Operation cancelled.');
      return;
    }
    
    // Update laptop name
    await connection.query(
      'UPDATE inventory_items SET name = ? WHERE item_id = ?',
      [laptop.model, laptopId]
    );
    
    console.log('\nLaptop name updated successfully!');
    
    // Get updated laptop details
    const [updatedItems] = await connection.query(
      'SELECT * FROM inventory_items WHERE item_id = ?',
      [laptopId]
    );
    
    const updatedLaptop = updatedItems[0];
    console.log('\nUpdated laptop details:');
    console.log('----------------------');
    console.log(`ID: ${updatedLaptop.item_id}`);
    console.log(`Name: ${updatedLaptop.name}`);
    console.log(`Model: ${updatedLaptop.model || 'N/A'}`);
    console.log(`Manufacturer: ${updatedLaptop.manufacturer || 'N/A'}`);
    console.log(`Serial Number: ${updatedLaptop.serial_number || 'N/A'}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
    rl.close();
  }
}

main();
