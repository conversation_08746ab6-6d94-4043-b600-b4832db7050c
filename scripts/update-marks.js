require('dotenv').config();
const mysql = require('mysql2/promise');

async function main() {
    let connection;

    try {
        console.log('Connecting to database...');

        // Create database connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'exam_prep_platform'
        });

        console.log('Connected to database successfully');

        // Get all attempts
        const [attempts] = await connection.execute('SELECT attempt_id, exam_id FROM exam_attempts');
        console.log(`Found ${attempts.length} attempts to process`);

        for (const attempt of attempts) {
            console.log(`Processing attempt ${attempt.attempt_id} for exam ${attempt.exam_id}...`);

            // Get all questions for this exam
            const [questions] = await connection.execute(`
                SELECT q.*, s.section_id
                FROM questions q
                JOIN sections s ON q.section_id = s.section_id
                WHERE s.exam_id = ?
            `, [attempt.exam_id]);

            console.log(`Found ${questions.length} questions for exam ${attempt.exam_id}`);

            // Get all user answers for this attempt
            const [userAnswers] = await connection.execute(
                'SELECT * FROM user_answers WHERE attempt_id = ?',
                [attempt.attempt_id]
            );

            console.log(`Found ${userAnswers.length} user answers for attempt ${attempt.attempt_id}`);

            // Convert to a map for easy lookup
            const answersMap = {};
            userAnswers.forEach(answer => {
                answersMap[answer.question_id] = answer.selected_option_id || answer.essay_answer || '';
            });

            // Get all option IDs from user answers
            const optionIds = userAnswers
                .filter(answer => answer.selected_option_id)
                .map(answer => answer.selected_option_id);

            // Get all options in a single query
            let optionsMap = {};
            if (optionIds.length > 0) {
                try {
                    // Get options one by one to avoid IN clause issues
                    for (const optionId of optionIds) {
                        const [option] = await connection.execute(
                            'SELECT id, question_id, option_text, is_correct FROM options WHERE id = ?',
                            [optionId]
                        );

                        if (option.length > 0) {
                            optionsMap[optionId] = option[0];
                        }
                    }
                } catch (error) {
                    console.error(`Error fetching options: ${error.message}`);
                }
            }

            // Process each question
            for (const question of questions) {
                const userAnswer = answersMap[question.question_id];
                if (!userAnswer) continue; // Skip if no answer

                let isCorrect = false;
                let marks_obtained = 0;

                if (question.question_type === 'multiple_choice' || question.question_type === 'mcq') {
                    // Check if the selected option is correct
                    if (userAnswer && !isNaN(parseInt(userAnswer))) {
                        const optionId = parseInt(userAnswer);
                        const option = optionsMap[optionId];

                        if (option) {
                            // Check if the option is marked as correct
                            if (option.is_correct === 1) {
                                isCorrect = true;
                                marks_obtained = parseFloat(question.marks) || 1.00;
                                console.log(`Question ${question.question_id}: Option ${optionId} is correct`);
                            }
                            // Also check if the option text matches the correct answer
                            else if (question.correct_answer && option.option_text.toLowerCase() === question.correct_answer.toLowerCase()) {
                                isCorrect = true;
                                marks_obtained = parseFloat(question.marks) || 1.00;
                                console.log(`Question ${question.question_id}: Option text matches correct answer`);
                            }
                        }
                    }
                } else if (question.question_type === 'true_false') {
                    // For true/false, direct comparison
                    if (userAnswer && question.correct_answer) {
                        isCorrect = userAnswer.toString().toLowerCase() === question.correct_answer.toString().toLowerCase();
                        if (isCorrect) {
                            marks_obtained = parseFloat(question.marks) || 1.00;
                            console.log(`Question ${question.question_id}: True/False answer is correct`);
                        }
                    }
                }

                // Update marks_obtained in user_answers table
                if (userAnswer) {
                    await connection.execute(
                        'UPDATE user_answers SET marks_obtained = ? WHERE attempt_id = ? AND question_id = ?',
                        [marks_obtained, attempt.attempt_id, question.question_id]
                    );
                    console.log(`Updated marks for question ${question.question_id}: marks_obtained = ${marks_obtained}`);
                }
            }

            console.log(`Completed processing attempt ${attempt.attempt_id}`);
        }

        console.log('All attempts processed successfully');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('Database connection closed');
        }
    }
}

main().catch(console.error);
