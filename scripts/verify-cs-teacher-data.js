const mysql = require('mysql2/promise');

async function verifyCSTeacherData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 VERIFYING CS TEACHER COMPREHENSIVE DATA\n');

        // Get staff ID
        const [userResult] = await connection.execute(
            'SELECT u.id as user_id, s.id as staff_id, u.name, u.email FROM users u JOIN staff s ON u.id = s.user_id WHERE u.email = ?',
            ['<EMAIL>']
        );

        const { staff_id: staffId, name, email } = userResult[0];
        console.log(`👨‍🏫 Teacher: ${name} (${email})`);
        console.log(`📋 Staff ID: ${staffId}\n`);

        // =====================================================
        // 1. EDUCATIONAL QUALIFICATIONS
        // =====================================================
        console.log('🎓 EDUCATIONAL QUALIFICATIONS:');
        console.log('=' .repeat(50));
        
        const [qualifications] = await connection.execute(`
            SELECT qualification_level, qualification_name, specialization, institution_name,
                   university_board, total_marks_obtained, total_marks_maximum, percentage,
                   grade, cgpa, completion_year, subjects, achievements
            FROM staff_educational_qualifications 
            WHERE staff_id = ? 
            ORDER BY completion_year
        `, [staffId]);

        qualifications.forEach((qual, index) => {
            console.log(`${index + 1}. ${qual.qualification_name} (${qual.completion_year})`);
            console.log(`   🏫 Institution: ${qual.institution_name}`);
            console.log(`   📚 Specialization: ${qual.specialization}`);
            console.log(`   📊 Performance: ${qual.percentage}% (${qual.total_marks_obtained}/${qual.total_marks_maximum})`);
            if (qual.grade) console.log(`   🏆 Grade: ${qual.grade}`);
            if (qual.cgpa) console.log(`   📈 CGPA: ${qual.cgpa}`);
            
            if (qual.subjects) {
                console.log(`   📖 Subjects:`);
                const subjects = JSON.parse(qual.subjects);
                Object.entries(subjects).forEach(([subject, marks]) => {
                    console.log(`      • ${subject}: ${marks.marks}/${marks.total}`);
                });
            }
            
            if (qual.achievements) {
                console.log(`   🏅 Achievements: ${qual.achievements}`);
            }
            console.log('');
        });

        // =====================================================
        // 2. PROFESSIONAL EXPERIENCE
        // =====================================================
        console.log('💼 PROFESSIONAL EXPERIENCE:');
        console.log('=' .repeat(50));
        
        const [experience] = await connection.execute(`
            SELECT job_title, organization_name, organization_type, start_date, end_date,
                   is_current, total_duration_months, job_description, key_responsibilities,
                   achievements, skills_used, salary_range, performance_rating
            FROM staff_professional_experience 
            WHERE staff_id = ? 
            ORDER BY start_date
        `, [staffId]);

        experience.forEach((exp, index) => {
            const duration = exp.end_date 
                ? `${exp.start_date.toISOString().split('T')[0]} to ${exp.end_date.toISOString().split('T')[0]}`
                : `${exp.start_date.toISOString().split('T')[0]} to Present`;
            
            console.log(`${index + 1}. ${exp.job_title} ${exp.is_current ? '(CURRENT)' : ''}`);
            console.log(`   🏢 Organization: ${exp.organization_name} (${exp.organization_type})`);
            console.log(`   📅 Duration: ${duration} (${exp.total_duration_months} months)`);
            console.log(`   💰 Salary Range: ${exp.salary_range}`);
            console.log(`   ⭐ Performance: ${exp.performance_rating}`);
            console.log(`   📝 Description: ${exp.job_description}`);
            
            if (exp.key_responsibilities) {
                console.log(`   🎯 Key Responsibilities:`);
                JSON.parse(exp.key_responsibilities).forEach(resp => {
                    console.log(`      • ${resp}`);
                });
            }
            
            if (exp.achievements) {
                console.log(`   🏆 Achievements:`);
                JSON.parse(exp.achievements).forEach(achievement => {
                    console.log(`      • ${achievement}`);
                });
            }
            
            if (exp.skills_used) {
                console.log(`   🛠️ Skills Used: ${JSON.parse(exp.skills_used).join(', ')}`);
            }
            console.log('');
        });

        // =====================================================
        // 3. CERTIFICATIONS
        // =====================================================
        console.log('📜 CERTIFICATIONS:');
        console.log('=' .repeat(50));
        
        const [certifications] = await connection.execute(`
            SELECT certification_name, certification_type, issuing_organization,
                   issue_date, expiry_date, is_lifetime, certificate_id,
                   verification_status, description, skills_covered
            FROM staff_certifications 
            WHERE staff_id = ? 
            ORDER BY issue_date DESC
        `, [staffId]);

        certifications.forEach((cert, index) => {
            console.log(`${index + 1}. ${cert.certification_name}`);
            console.log(`   🏛️ Issuer: ${cert.issuing_organization}`);
            console.log(`   📅 Issued: ${cert.issue_date.toISOString().split('T')[0]}`);
            if (cert.is_lifetime) {
                console.log(`   ♾️ Validity: Lifetime`);
            } else if (cert.expiry_date) {
                console.log(`   📅 Expires: ${cert.expiry_date.toISOString().split('T')[0]}`);
            }
            console.log(`   🆔 Certificate ID: ${cert.certificate_id}`);
            console.log(`   ✅ Status: ${cert.verification_status}`);
            console.log(`   📝 Description: ${cert.description}`);
            if (cert.skills_covered) {
                console.log(`   🎯 Skills Covered: ${JSON.parse(cert.skills_covered).join(', ')}`);
            }
            console.log('');
        });

        // =====================================================
        // 4. SKILLS SUMMARY
        // =====================================================
        console.log('🛠️ SKILLS SUMMARY:');
        console.log('=' .repeat(50));
        
        const [skills] = await connection.execute(`
            SELECT skill_category, COUNT(*) as count,
                   GROUP_CONCAT(CONCAT(skill_name, ' (', proficiency_level, ')') SEPARATOR ', ') as skills_list
            FROM staff_skills 
            WHERE staff_id = ? 
            GROUP BY skill_category
            ORDER BY skill_category
        `, [staffId]);

        skills.forEach(category => {
            console.log(`${category.skill_category.toUpperCase()} (${category.count} skills):`);
            console.log(`   ${category.skills_list}`);
            console.log('');
        });

    } catch (error) {
        console.error('Error verifying CS teacher data:', error);
    } finally {
        await connection.end();
    }
}

verifyCSTeacherData();
