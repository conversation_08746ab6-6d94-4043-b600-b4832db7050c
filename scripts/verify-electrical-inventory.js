/**
 * Verify Electrical Inventory Database
 */

const mysql = require('mysql2/promise');

async function verifyElectricalInventory() {
  try {
    console.log('🔍 Verifying Electrical Inventory Database...\n');

    // Database connection
    const db = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'exam_prep_platform'
    });

    console.log('✅ Connected to database');

    // Check if table exists
    const [tables] = await db.query("SHOW TABLES LIKE 'electrical_inventory'");
    if (tables.length === 0) {
      console.log('❌ electrical_inventory table does not exist');
      return;
    }
    console.log('✅ electrical_inventory table exists');

    // Get table structure
    const [structure] = await db.query('DESCRIBE electrical_inventory');
    console.log('\n📋 TABLE STRUCTURE:');
    structure.forEach(col => {
      console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(NOT NULL)' : ''}`);
    });

    // Get summary by room
    const [roomSummary] = await db.query(`
      SELECT 
        room_number,
        COUNT(*) as total_items,
        SUM(CASE WHEN item_type = 'tubelight' THEN 1 ELSE 0 END) as tubelights,
        SUM(CASE WHEN item_type = 'fan' THEN 1 ELSE 0 END) as fans,
        SUM(CASE WHEN item_type = 'outlet' THEN 1 ELSE 0 END) as outlets,
        SUM(CASE WHEN item_type = 'switch' THEN 1 ELSE 0 END) as switches
      FROM electrical_inventory 
      WHERE room_number > 0
      GROUP BY room_number 
      ORDER BY room_number
      LIMIT 5
    `);

    console.log('\n📊 SAMPLE ROOM INVENTORY (First 5 rooms):');
    console.log('Room | Total | Lights | Fans | Outlets | Switches');
    console.log('-----|-------|--------|------|---------|----------');
    roomSummary.forEach(room => {
      console.log(`  ${String(room.room_number).padStart(2)} |   ${String(room.total_items).padStart(2)}  |   ${String(room.tubelights).padStart(2)}   |  ${String(room.fans).padStart(2)}  |    ${String(room.outlets).padStart(2)}   |    ${String(room.switches).padStart(2)}`);
    });

    // Get sample items from classroom 1
    const [classroom1Items] = await db.query(`
      SELECT item_name, item_type, serial_number, status, manufacturer, model
      FROM electrical_inventory 
      WHERE room_number = 1 
      ORDER BY item_type, item_name
      LIMIT 10
    `);

    console.log('\n🏫 SAMPLE ITEMS FROM CLASSROOM 1:');
    classroom1Items.forEach(item => {
      console.log(`   ${item.serial_number}: ${item.item_name} (${item.manufacturer} ${item.model}) - ${item.status.toUpperCase()}`);
    });

    // Check for rooms 3 and 5 (should have different equipment)
    const [specialRooms] = await db.query(`
      SELECT room_number, item_name, serial_number
      FROM electrical_inventory 
      WHERE room_number IN (3, 5) AND item_type = 'other'
      ORDER BY room_number
    `);

    if (specialRooms.length > 0) {
      console.log('\n🎯 SPECIAL EQUIPMENT (Rooms 3 & 5):');
      specialRooms.forEach(item => {
        console.log(`   Room ${item.room_number}: ${item.item_name} (${item.serial_number})`);
      });
    }

    // Overall statistics
    const [stats] = await db.query(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(DISTINCT room_number) as rooms_covered,
        SUM(CASE WHEN status = 'working' THEN 1 ELSE 0 END) as working_items,
        SUM(CASE WHEN status = 'faulty' THEN 1 ELSE 0 END) as faulty_items
      FROM electrical_inventory
    `);

    console.log('\n📈 OVERALL STATISTICS:');
    console.log(`   Total Items: ${stats[0].total_items}`);
    console.log(`   Rooms Covered: ${stats[0].rooms_covered}`);
    console.log(`   Working Items: ${stats[0].working_items}`);
    console.log(`   Faulty Items: ${stats[0].faulty_items}`);
    console.log(`   Working Rate: ${((stats[0].working_items / stats[0].total_items) * 100).toFixed(1)}%`);

    await db.end();
    console.log('\n✅ Electrical inventory verification complete!');

  } catch (error) {
    console.error('❌ Error verifying electrical inventory:', error.message);
  }
}

// Run the verification
verifyElectricalInventory();
