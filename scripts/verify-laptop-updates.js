/**
 * <PERSON><PERSON><PERSON> to verify all laptops have been updated correctly
 * 
 * This script checks that all laptops have:
 * - Model: "ACER TRAVELMATE 246M"
 * - Manufacturer: "ACER"
 * - Original names preserved
 */

require('dotenv').config();
const mysql = require('mysql2/promise');

async function main() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'exam_prep_platform'
    });
    
    console.log('Connected to database');
    
    // Find all laptops
    const [laptops] = await connection.query(`
      SELECT i.item_id, i.name, i.serial_number, i.model, i.manufacturer
      FROM inventory_items i
      JOIN inventory_categories c ON i.category_id = c.category_id
      WHERE c.name LIKE '%Laptop%' OR i.name LIKE '%Laptop%'
      ORDER BY i.name
    `);
    
    if (laptops.length === 0) {
      console.log('No laptops found in the inventory system.');
      return;
    }
    
    console.log(`\nFound ${laptops.length} laptops in the inventory system.`);
    console.log('\nVerifying laptop details:');
    console.log('----------------------');
    
    let allCorrect = true;
    
    laptops.forEach((laptop, index) => {
      const modelCorrect = laptop.model === 'ACER TRAVELMATE 246M';
      const manufacturerCorrect = laptop.manufacturer === 'ACER';
      
      console.log(`${index + 1}. ID: ${laptop.item_id}, Name: ${laptop.name}`);
      console.log(`   Model: ${laptop.model} ${modelCorrect ? '✓' : '✗'}`);
      console.log(`   Manufacturer: ${laptop.manufacturer} ${manufacturerCorrect ? '✓' : '✗'}`);
      console.log(`   Serial: ${laptop.serial_number}`);
      
      if (!modelCorrect || !manufacturerCorrect) {
        allCorrect = false;
      }
    });
    
    console.log('\nVerification result:');
    if (allCorrect) {
      console.log('✅ All laptops have been updated correctly!');
    } else {
      console.log('❌ Some laptops have not been updated correctly.');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

main();
