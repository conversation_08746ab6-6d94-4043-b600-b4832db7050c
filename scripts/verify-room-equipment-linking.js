/**
 * <PERSON><PERSON><PERSON> to verify room and equipment linking after fixes
 * 
 * This script:
 * 1. Verifies IT inventory location matching with rooms
 * 2. Checks electrical inventory room assignments
 * 3. Validates equipment distribution across rooms
 * 4. Generates comprehensive reports
 * 5. Identifies any remaining issues
 */

const db = require('../config/database');

async function verifyRoomEquipmentLinking() {
    console.log('🔍 Verifying Room and Equipment Linking...\n');

    try {
        // 1. Get all rooms with equipment counts
        console.log('1. Analyzing room and equipment distribution...');
        
        const [roomEquipmentData] = await db.query(`
            SELECT 
                r.id,
                r.room_number,
                r.capacity,
                r.building,
                r.floor,
                COUNT(DISTINCT i.id) as it_equipment_count,
                COUNT(DISTINCT e.id) as electrical_equipment_count,
                GROUP_CONCAT(DISTINCT i.type ORDER BY i.type) as it_equipment_types,
                GROUP_CONCAT(DISTINCT e.item_type ORDER BY e.item_type) as electrical_equipment_types
            FROM rooms r
            LEFT JOIN it_inventory i ON (
                i.location = r.room_number
            )
            LEFT JOIN electrical_inventory e ON (
                e.room_number = REGEXP_SUBSTR(r.room_number, '[0-9]+') OR
                e.location = r.room_number
            )
            GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
            ORDER BY r.room_number
        `);

        console.log(`   ✅ Found ${roomEquipmentData.length} rooms`);

        // 2. Detailed room analysis
        console.log('\n2. Detailed Room Equipment Analysis:');
        
        let totalITEquipment = 0;
        let totalElectricalEquipment = 0;
        let roomsWithIT = 0;
        let roomsWithElectrical = 0;
        let emptyRooms = 0;

        roomEquipmentData.forEach(room => {
            const itCount = parseInt(room.it_equipment_count) || 0;
            const electricalCount = parseInt(room.electrical_equipment_count) || 0;
            
            totalITEquipment += itCount;
            totalElectricalEquipment += electricalCount;
            
            if (itCount > 0) roomsWithIT++;
            if (electricalCount > 0) roomsWithElectrical++;
            if (itCount === 0 && electricalCount === 0) emptyRooms++;

            console.log(`\n   🏠 ${room.room_number} (Floor ${room.floor}, Capacity: ${room.capacity}):`);
            console.log(`      📱 IT Equipment: ${itCount} items`);
            if (room.it_equipment_types) {
                console.log(`         Types: ${room.it_equipment_types}`);
            }
            console.log(`      ⚡ Electrical Equipment: ${electricalCount} items`);
            if (room.electrical_equipment_types) {
                console.log(`         Types: ${room.electrical_equipment_types}`);
            }
            
            if (itCount === 0 && electricalCount === 0) {
                console.log(`      ⚠️  Empty room - no equipment assigned`);
            }
        });

        // 3. Check for unmatched IT equipment
        console.log('\n3. Checking for unmatched IT equipment...');
        
        const [unmatchedIT] = await db.query(`
            SELECT 
                i.id,
                i.name,
                i.type,
                i.location,
                i.status,
                i.serial_number
            FROM it_inventory i
            LEFT JOIN rooms r ON i.location = r.room_number
            WHERE r.id IS NULL
            AND i.location IS NOT NULL 
            AND i.location != ''
            ORDER BY i.location, i.name
        `);

        if (unmatchedIT.length > 0) {
            console.log(`   ❌ Found ${unmatchedIT.length} unmatched IT equipment items:`);
            unmatchedIT.forEach(item => {
                console.log(`      • ${item.name} (${item.type}) - Location: "${item.location}"`);
            });
        } else {
            console.log(`   ✅ All IT equipment is properly matched to rooms!`);
        }

        // 4. Check for unmatched electrical equipment
        console.log('\n4. Checking for unmatched electrical equipment...');
        
        const [unmatchedElectrical] = await db.query(`
            SELECT 
                e.id,
                e.item_name,
                e.item_type,
                e.location,
                e.room_number,
                e.status,
                e.serial_number
            FROM electrical_inventory e
            LEFT JOIN rooms r ON (
                e.room_number = REGEXP_SUBSTR(r.room_number, '[0-9]+') OR
                e.location = r.room_number
            )
            WHERE r.id IS NULL
            AND (e.location IS NOT NULL OR e.room_number IS NOT NULL)
            ORDER BY e.location, e.room_number, e.item_name
        `);

        if (unmatchedElectrical.length > 0) {
            console.log(`   ❌ Found ${unmatchedElectrical.length} unmatched electrical equipment items:`);
            unmatchedElectrical.forEach(item => {
                console.log(`      • ${item.item_name} (${item.item_type}) - Room: ${item.room_number}, Location: "${item.location}"`);
            });
        } else {
            console.log(`   ✅ All electrical equipment is properly matched to rooms!`);
        }

        // 5. Equipment type distribution analysis
        console.log('\n5. Equipment Type Distribution Analysis:');
        
        const [itTypeDistribution] = await db.query(`
            SELECT 
                i.type,
                COUNT(*) as total_count,
                COUNT(DISTINCT i.location) as locations_count,
                GROUP_CONCAT(DISTINCT r.room_number ORDER BY r.room_number) as rooms
            FROM it_inventory i
            LEFT JOIN rooms r ON i.location = r.room_number
            WHERE i.location IS NOT NULL AND i.location != ''
            GROUP BY i.type
            ORDER BY total_count DESC
        `);

        console.log('\n   📱 IT Equipment Distribution:');
        itTypeDistribution.forEach(type => {
            console.log(`      • ${type.type}: ${type.total_count} items in ${type.locations_count} locations`);
            if (type.rooms) {
                console.log(`        Rooms: ${type.rooms}`);
            }
        });

        const [electricalTypeDistribution] = await db.query(`
            SELECT 
                e.item_type,
                COUNT(*) as total_count,
                COUNT(DISTINCT COALESCE(r.room_number, e.location)) as locations_count,
                GROUP_CONCAT(DISTINCT r.room_number ORDER BY r.room_number) as rooms
            FROM electrical_inventory e
            LEFT JOIN rooms r ON (
                e.room_number = REGEXP_SUBSTR(r.room_number, '[0-9]+') OR
                e.location = r.room_number
            )
            GROUP BY e.item_type
            ORDER BY total_count DESC
        `);

        console.log('\n   ⚡ Electrical Equipment Distribution:');
        electricalTypeDistribution.forEach(type => {
            console.log(`      • ${type.item_type}: ${type.total_count} items in ${type.locations_count} locations`);
            if (type.rooms) {
                console.log(`        Rooms: ${type.rooms}`);
            }
        });

        // 6. Room utilization analysis
        console.log('\n6. Room Utilization Analysis:');
        
        const utilizationStats = {
            highUtilization: roomEquipmentData.filter(r => (r.it_equipment_count + r.electrical_equipment_count) >= 10),
            mediumUtilization: roomEquipmentData.filter(r => (r.it_equipment_count + r.electrical_equipment_count) >= 5 && (r.it_equipment_count + r.electrical_equipment_count) < 10),
            lowUtilization: roomEquipmentData.filter(r => (r.it_equipment_count + r.electrical_equipment_count) >= 1 && (r.it_equipment_count + r.electrical_equipment_count) < 5),
            emptyRooms: roomEquipmentData.filter(r => (r.it_equipment_count + r.electrical_equipment_count) === 0)
        };

        console.log(`   📊 Utilization Categories:`);
        console.log(`      🔥 High (10+ items): ${utilizationStats.highUtilization.length} rooms`);
        console.log(`      🔶 Medium (5-9 items): ${utilizationStats.mediumUtilization.length} rooms`);
        console.log(`      🔸 Low (1-4 items): ${utilizationStats.lowUtilization.length} rooms`);
        console.log(`      ⚪ Empty (0 items): ${utilizationStats.emptyRooms.length} rooms`);

        // 7. Final summary and recommendations
        console.log('\n7. Final Summary and Recommendations:');
        
        const totalRooms = roomEquipmentData.length;
        const matchRate = ((totalITEquipment - unmatchedIT.length) / totalITEquipment * 100).toFixed(1);
        
        console.log(`\n   📊 Overall Statistics:`);
        console.log(`      🏠 Total Rooms: ${totalRooms}`);
        console.log(`      📱 Total IT Equipment: ${totalITEquipment}`);
        console.log(`      ⚡ Total Electrical Equipment: ${totalElectricalEquipment}`);
        console.log(`      🔗 IT Equipment Match Rate: ${matchRate}%`);
        console.log(`      🏠 Rooms with IT Equipment: ${roomsWithIT}/${totalRooms} (${(roomsWithIT/totalRooms*100).toFixed(1)}%)`);
        console.log(`      🏠 Rooms with Electrical Equipment: ${roomsWithElectrical}/${totalRooms} (${(roomsWithElectrical/totalRooms*100).toFixed(1)}%)`);
        console.log(`      🏠 Empty Rooms: ${emptyRooms}/${totalRooms} (${(emptyRooms/totalRooms*100).toFixed(1)}%)`);

        // Recommendations
        console.log(`\n   💡 Recommendations:`);
        
        if (unmatchedIT.length > 0) {
            console.log(`      ⚠️  Fix ${unmatchedIT.length} unmatched IT equipment items`);
        }
        
        if (unmatchedElectrical.length > 0) {
            console.log(`      ⚠️  Fix ${unmatchedElectrical.length} unmatched electrical equipment items`);
        }
        
        if (emptyRooms > totalRooms * 0.3) {
            console.log(`      📝 Consider equipment allocation for ${emptyRooms} empty rooms`);
        }
        
        if (parseFloat(matchRate) < 95) {
            console.log(`      🔧 Improve location data quality (current match rate: ${matchRate}%)`);
        } else {
            console.log(`      ✅ Excellent equipment-room linking (${matchRate}% match rate)`);
        }

        return {
            totalRooms,
            totalITEquipment,
            totalElectricalEquipment,
            unmatchedIT: unmatchedIT.length,
            unmatchedElectrical: unmatchedElectrical.length,
            matchRate: parseFloat(matchRate),
            roomsWithEquipment: roomsWithIT + roomsWithElectrical,
            emptyRooms
        };

    } catch (error) {
        console.error('❌ Error verifying room equipment linking:', error);
        throw error;
    }
}

// Run the verification if this script is executed directly
if (require.main === module) {
    verifyRoomEquipmentLinking()
        .then((results) => {
            console.log('\n✅ Verification completed successfully!');
            console.log(`📈 Equipment Match Rate: ${results.matchRate}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Verification failed:', error);
            process.exit(1);
        });
}

module.exports = { verifyRoomEquipmentLinking };
