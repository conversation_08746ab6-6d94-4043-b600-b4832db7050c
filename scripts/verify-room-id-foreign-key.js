/**
 * <PERSON><PERSON><PERSON> to verify the room_id foreign key relationship in IT inventory
 *
 * This script:
 * 1. Tests the foreign key constraint
 * 2. Verifies data integrity
 * 3. Compares old location-based vs new room_id-based queries
 * 4. Provides performance analysis
 * 5. Validates the relationship works correctly
 */

const db = require('../config/database');
const SQLQueries = require('../config/sql-queries');

async function verifyRoomIdForeignKey() {
    console.log('🔍 Verifying room_id Foreign Key Relationship...\n');

    try {
        // 1. Check foreign key constraint exists
        console.log('1. Checking foreign key constraint...');

        const [constraints] = await db.query(`
            SELECT
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
                AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
            WHERE kcu.TABLE_SCHEMA = ?
            AND kcu.TABLE_NAME = 'it_inventory'
            AND kcu.COLUMN_NAME = 'room_id'
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `, [process.env.DB_NAME || 'exam_prep_platform']);

        if (constraints.length > 0) {
            console.log('   ✅ Foreign key constraint found:');
            constraints.forEach(constraint => {
                console.log(`      • ${constraint.CONSTRAINT_NAME}: it_inventory.${constraint.COLUMN_NAME} → ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
                console.log(`        Delete Rule: ${constraint.DELETE_RULE}, Update Rule: ${constraint.UPDATE_RULE}`);
            });
        } else {
            console.log('   ❌ No foreign key constraint found!');
            return;
        }

        // 2. Test data integrity
        console.log('\n2. Testing data integrity...');

        const [integrityCheck] = await db.query(`
            SELECT
                COUNT(*) as total_items,
                COUNT(room_id) as items_with_room_id,
                COUNT(CASE WHEN room_id IS NOT NULL AND r.id IS NULL THEN 1 END) as broken_links
            FROM it_inventory i
            LEFT JOIN rooms r ON i.room_id = r.id
        `);

        console.log(`   📊 Data Integrity Results:`);
        console.log(`      • Total IT items: ${integrityCheck[0].total_items}`);
        console.log(`      • Items with room_id: ${integrityCheck[0].items_with_room_id}`);
        console.log(`      • Broken links: ${integrityCheck[0].broken_links}`);

        if (integrityCheck[0].broken_links > 0) {
            console.log('   ⚠️  Warning: Found broken foreign key references!');
        } else {
            console.log('   ✅ All foreign key references are valid');
        }

        // 3. Compare old vs new query methods
        console.log('\n3. Comparing query methods...');

        // Old method (location-based)
        const startTimeOld = Date.now();
        const [oldResults] = await db.query(`
            SELECT
                r.room_number,
                COUNT(i.id) as equipment_count
            FROM rooms r
            LEFT JOIN it_inventory i ON i.location = r.room_number
            GROUP BY r.id, r.room_number
            ORDER BY r.room_number
        `);
        const oldQueryTime = Date.now() - startTimeOld;

        // New method (foreign key-based)
        const startTimeNew = Date.now();
        const [newResults] = await db.query(`
            SELECT
                r.room_number,
                COUNT(i.id) as equipment_count
            FROM rooms r
            LEFT JOIN it_inventory i ON r.id = i.room_id
            GROUP BY r.id, r.room_number
            ORDER BY r.room_number
        `);
        const newQueryTime = Date.now() - startTimeNew;

        console.log(`   ⏱️  Query Performance:`);
        console.log(`      • Old method (location-based): ${oldQueryTime}ms`);
        console.log(`      • New method (foreign key): ${newQueryTime}ms`);
        console.log(`      • Performance improvement: ${((oldQueryTime - newQueryTime) / oldQueryTime * 100).toFixed(1)}%`);

        // 4. Test new SQL queries
        console.log('\n4. Testing new SQL queries...');

        // Test checkITInventoryRoomLink
        const [linkCheck] = await db.query(SQLQueries.infrastructure.checkITInventoryRoomLink);
        console.log(`   📋 Room Link Status:`);
        const linkStats = linkCheck.reduce((acc, item) => {
            acc[item.link_status] = (acc[item.link_status] || 0) + 1;
            return acc;
        }, {});

        Object.entries(linkStats).forEach(([status, count]) => {
            console.log(`      • ${status}: ${count} items`);
        });

        // Test getEquipmentSummaryByRoom
        const [equipmentSummary] = await db.query(SQLQueries.infrastructure.getEquipmentSummaryByRoom);
        console.log(`\n   📊 Equipment Summary by Room (top 5):`);
        equipmentSummary
            .filter(room => room.total_it_equipment > 0)
            .slice(0, 5)
            .forEach(room => {
                console.log(`      • ${room.room_number}: ${room.total_it_equipment} items (${room.projectors} projectors, ${room.laptops} laptops, ${room.desktops} desktops)`);
            });

        // 5. Test foreign key constraint enforcement
        console.log('\n5. Testing foreign key constraint enforcement...');

        try {
            // Try to insert invalid room_id
            await db.query(`
                INSERT INTO it_inventory (name, type, room_id)
                VALUES ('Test Item', 'other', 99999)
            `);
            console.log('   ❌ Foreign key constraint not working - invalid insert succeeded!');
        } catch (error) {
            if (error.code === 'ER_NO_REFERENCED_ROW_2') {
                console.log('   ✅ Foreign key constraint working - invalid insert rejected');
            } else {
                console.log(`   ⚠️  Unexpected error: ${error.message}`);
            }
        }

        // 6. Test cascade operations
        console.log('\n6. Testing cascade operations...');

        // Create a test room
        const [testRoom] = await db.query(`
            INSERT INTO rooms (room_number, capacity, building, floor)
            VALUES ('Test Room 999', 50, 'Test Building', 1)
        `);
        const testRoomId = testRoom.insertId;

        // Create a test IT item linked to the test room
        const [testItem] = await db.query(`
            INSERT INTO it_inventory (name, type, room_id)
            VALUES ('Test Equipment', 'other', ?)
        `, [testRoomId]);
        const testItemId = testItem.insertId;

        // Verify the link
        const [linkVerify] = await db.query(`
            SELECT i.name, r.room_number
            FROM it_inventory i
            JOIN rooms r ON i.room_id = r.id
            WHERE i.id = ?
        `, [testItemId]);

        if (linkVerify.length > 0) {
            console.log(`   ✅ Test link created: ${linkVerify[0].name} → ${linkVerify[0].room_number}`);
        }

        // Test ON DELETE SET NULL
        await db.query(`DELETE FROM rooms WHERE id = ?`, [testRoomId]);

        const [afterDelete] = await db.query(`
            SELECT room_id FROM it_inventory WHERE id = ?
        `, [testItemId]);

        if (afterDelete[0].room_id === null) {
            console.log('   ✅ ON DELETE SET NULL working correctly');
        } else {
            console.log('   ❌ ON DELETE SET NULL not working');
        }

        // Clean up test data
        await db.query(`DELETE FROM it_inventory WHERE id = ?`, [testItemId]);

        // 7. Performance analysis
        console.log('\n7. Performance analysis...');

        const [indexCheck] = await db.query(`
            SHOW INDEX FROM it_inventory WHERE Column_name = 'room_id'
        `);

        if (indexCheck.length > 0) {
            console.log('   ✅ Index on room_id column exists');
        } else {
            console.log('   ⚠️  No index found on room_id column');
        }

        // 8. Final summary
        console.log('\n8. Final Summary:');

        const totalLinked = integrityCheck[0].items_with_room_id;
        const totalItems = integrityCheck[0].total_items;
        const linkPercentage = (totalLinked / totalItems * 100).toFixed(1);

        console.log(`   📊 Overall Statistics:`);
        console.log(`      • Total IT Equipment: ${totalItems}`);
        console.log(`      • Items with room_id: ${totalLinked} (${linkPercentage}%)`);
        console.log(`      • Foreign Key Constraint: ✅ Active`);
        console.log(`      • Data Integrity: ${integrityCheck[0].broken_links === 0 ? '✅ Perfect' : '⚠️ Issues Found'}`);
        console.log(`      • Query Performance: ${newQueryTime < oldQueryTime ? '✅ Improved' : '⚠️ Slower'}`);

        console.log('\n   🎯 Benefits of Foreign Key Relationship:');
        console.log('      • ✅ Data integrity enforced at database level');
        console.log('      • ✅ Faster queries using indexed joins');
        console.log('      • ✅ Automatic cascade operations');
        console.log('      • ✅ Referential integrity guaranteed');
        console.log('      • ✅ Better query optimization by database engine');

        return {
            success: true,
            totalItems,
            linkedItems: totalLinked,
            linkPercentage: parseFloat(linkPercentage),
            brokenLinks: integrityCheck[0].broken_links,
            performanceImprovement: ((oldQueryTime - newQueryTime) / oldQueryTime * 100).toFixed(1)
        };

    } catch (error) {
        console.error('❌ Error verifying room_id foreign key:', error);
        throw error;
    }
}

// Run verification if this script is executed directly
if (require.main === module) {
    verifyRoomIdForeignKey()
        .then((results) => {
            console.log('\n✅ Foreign key verification completed successfully!');
            console.log(`🔗 Link Rate: ${results.linkPercentage}%`);
            console.log(`⚡ Performance Improvement: ${results.performanceImprovement}%`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Verification failed:', error);
            process.exit(1);
        });
}

module.exports = { verifyRoomIdForeignKey };
