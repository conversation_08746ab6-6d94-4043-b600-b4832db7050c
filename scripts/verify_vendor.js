/**
 * <PERSON><PERSON><PERSON> to verify Middha Electro World was added to repair_vendors table
 */

const db = require('../config/database');

async function verifyVendor() {
    try {
        console.log('Checking if Middha Electro World exists in repair_vendors table...');
        
        // Query the database
        const [vendors] = await db.query(
            'SELECT * FROM repair_vendors WHERE name = ?',
            ['Middha Electro World']
        );
        
        if (vendors.length > 0) {
            const vendor = vendors[0];
            console.log('✅ Vendor found successfully!');
            console.log('----------------------------');
            console.log(`ID: ${vendor.vendor_id}`);
            console.log(`Name: ${vendor.name}`);
            console.log(`Phone: ${vendor.phone}`);
            console.log(`Email: ${vendor.email}`);
            console.log(`Address: ${vendor.address}`);
            console.log(`Specialization: ${vendor.specialization}`);
            console.log(`Notes: ${vendor.notes}`);
            console.log(`Active: ${vendor.is_active ? 'Yes' : 'No'}`);
            console.log('----------------------------');
        } else {
            console.log('❌ No vendor found with the name Middha Electro World');
        }
    } catch (error) {
        console.error('Error verifying vendor:', error);
    }
}

// Run the function
verifyVendor();
