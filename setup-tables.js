const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'exam_prep_platform',
  multipleStatements: true // Important for running multiple SQL statements
};

async function executeSQL(filePath) {
  console.log(`Executing SQL file: ${filePath}`);
  
  try {
    // Read the SQL file
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Create a connection
    const connection = await mysql.createConnection(dbConfig);
    
    // Execute the SQL statements
    const [results] = await connection.query(sqlContent);
    
    console.log(`Successfully executed SQL file: ${filePath}`);
    
    // Close the connection
    await connection.end();
    
    return true;
  } catch (error) {
    console.error(`Error executing SQL file ${filePath}:`, error);
    return false;
  }
}

async function main() {
  console.log('Setting up database tables...');
  
  // List of SQL files to execute
  const sqlFiles = [
    path.join(__dirname, 'sql', 'lecture_instruction_plan_link.sql'),
    path.join(__dirname, 'sql', 'syllabus_progress.sql')
  ];
  
  // Execute each SQL file
  for (const file of sqlFiles) {
    const success = await executeSQL(file);
    if (!success) {
      console.error(`Failed to execute ${file}. Stopping execution.`);
      process.exit(1);
    }
  }
  
  console.log('All SQL files executed successfully!');
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
