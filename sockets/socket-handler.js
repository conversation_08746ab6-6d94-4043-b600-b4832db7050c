/**
 * Socket.io handler for real-time communication
 */
const db = require('../config/database');

// Map to store active user connections
const activeUsers = new Map();

// Map to store users in test mode
const usersInTest = new Map();

module.exports = (io) => {
    // Middleware for authentication
    io.use(async (socket, next) => {
        try {
            const sessionId = socket.handshake.auth.sessionId;
            
            if (!sessionId) {
                return next(new Error('Authentication error'));
            }
            
            // Get user from session
            const [sessions] = await db.query(
                'SELECT * FROM sessions WHERE session_id = ?',
                [sessionId]
            );
            
            if (sessions.length === 0) {
                return next(new Error('Invalid session'));
            }
            
            const sessionData = JSON.parse(sessions[0].data);
            
            if (!sessionData.userId) {
                return next(new Error('Not authenticated'));
            }
            
            // Get user details
            const [users] = await db.query(
                'SELECT id, username, role, profile_image FROM users WHERE id = ?',
                [sessionData.userId]
            );
            
            if (users.length === 0) {
                return next(new Error('User not found'));
            }
            
            // Attach user data to socket
            socket.user = users[0];
            next();
        } catch (error) {
            console.error('Socket authentication error:', error);
            next(new Error('Authentication error'));
        }
    });

    // Handle connections
    io.on('connection', (socket) => {
        console.log(`User connected: ${socket.user.username} (${socket.user.id})`);
        
        // Add user to active users
        activeUsers.set(socket.user.id, {
            socket: socket.id,
            user: socket.user
        });
        
        // Emit active users list
        io.emit('users:active', Array.from(activeUsers.values()).map(u => ({
            id: u.user.id,
            username: u.user.username,
            profile_image: u.user.profile_image,
            role: u.user.role
        })));
        
        // Join user to their private room
        socket.join(`user:${socket.user.id}`);
        
        // Join user to rooms for groups they belong to
        joinUserGroups(socket);
        
        // Handle private messages
        socket.on('message:private', async (data) => {
            try {
                // Check if user is in test mode
                if (usersInTest.has(socket.user.id)) {
                    socket.emit('error', {
                        message: 'You cannot send messages while taking a test'
                    });
                    return;
                }
                
                // Check if recipient is in test mode
                if (usersInTest.has(data.recipientId)) {
                    socket.emit('error', {
                        message: 'Recipient is currently taking a test and cannot receive messages'
                    });
                    return;
                }
                
                // Validate data
                if (!data.message || !data.recipientId) {
                    socket.emit('error', {
                        message: 'Invalid message data'
                    });
                    return;
                }
                
                // Save message to database
                const [result] = await db.query(
                    'INSERT INTO chat_messages (sender_id, recipient_id, message, attachment_url, attachment_type) VALUES (?, ?, ?, ?, ?)',
                    [socket.user.id, data.recipientId, data.message, data.attachmentUrl || null, data.attachmentType || null]
                );
                
                const messageId = result.insertId;
                
                // Create message status record
                await db.query(
                    'INSERT INTO message_status (message_id, user_id, is_read) VALUES (?, ?, ?)',
                    [messageId, data.recipientId, false]
                );
                
                // Get the message with sender details
                const [messages] = await db.query(
                    `SELECT m.*, 
                            u.username as sender_name, 
                            u.profile_image as sender_image
                     FROM chat_messages m
                     JOIN users u ON m.sender_id = u.id
                     WHERE m.message_id = ?`,
                    [messageId]
                );
                
                if (messages.length === 0) {
                    socket.emit('error', {
                        message: 'Failed to send message'
                    });
                    return;
                }
                
                const message = messages[0];
                
                // Send message to recipient
                io.to(`user:${data.recipientId}`).emit('message:new', {
                    id: message.message_id,
                    senderId: message.sender_id,
                    senderName: message.sender_name,
                    senderImage: message.sender_image,
                    message: message.message,
                    attachmentUrl: message.attachment_url,
                    attachmentType: message.attachment_type,
                    createdAt: message.created_at,
                    isPrivate: true
                });
                
                // Send confirmation to sender
                socket.emit('message:sent', {
                    id: message.message_id,
                    recipientId: data.recipientId,
                    message: message.message,
                    attachmentUrl: message.attachment_url,
                    attachmentType: message.attachment_type,
                    createdAt: message.created_at
                });
            } catch (error) {
                console.error('Error sending private message:', error);
                socket.emit('error', {
                    message: 'Failed to send message'
                });
            }
        });
        
        // Handle group messages
        socket.on('message:group', async (data) => {
            try {
                // Check if user is in test mode
                if (usersInTest.has(socket.user.id)) {
                    socket.emit('error', {
                        message: 'You cannot send messages while taking a test'
                    });
                    return;
                }
                
                // Validate data
                if (!data.message || !data.groupId) {
                    socket.emit('error', {
                        message: 'Invalid message data'
                    });
                    return;
                }
                
                // Check if user is a member of the group
                const [members] = await db.query(
                    'SELECT * FROM group_members WHERE group_id = ? AND user_id = ?',
                    [data.groupId, socket.user.id]
                );
                
                if (members.length === 0) {
                    socket.emit('error', {
                        message: 'You are not a member of this group'
                    });
                    return;
                }
                
                // Check if chat is allowed in this group
                const [groups] = await db.query(
                    'SELECT allow_chat FROM groups WHERE group_id = ?',
                    [data.groupId]
                );
                
                if (groups.length === 0) {
                    socket.emit('error', {
                        message: 'Group not found'
                    });
                    return;
                }
                
                if (!groups[0].allow_chat) {
                    socket.emit('error', {
                        message: 'Chat is disabled in this group'
                    });
                    return;
                }
                
                // Save message to database
                const [result] = await db.query(
                    'INSERT INTO chat_messages (sender_id, group_id, message, attachment_url, attachment_type) VALUES (?, ?, ?, ?, ?)',
                    [socket.user.id, data.groupId, data.message, data.attachmentUrl || null, data.attachmentType || null]
                );
                
                const messageId = result.insertId;
                
                // Get all group members
                const [groupMembers] = await db.query(
                    'SELECT user_id FROM group_members WHERE group_id = ? AND user_id != ?',
                    [data.groupId, socket.user.id]
                );
                
                // Create message status records for all members
                for (const member of groupMembers) {
                    // Skip users in test mode
                    if (usersInTest.has(member.user_id)) {
                        continue;
                    }
                    
                    await db.query(
                        'INSERT INTO message_status (message_id, user_id, is_read) VALUES (?, ?, ?)',
                        [messageId, member.user_id, false]
                    );
                }
                
                // Get the message with sender details
                const [messages] = await db.query(
                    `SELECT m.*, 
                            u.username as sender_name, 
                            u.profile_image as sender_image,
                            g.name as group_name
                     FROM chat_messages m
                     JOIN users u ON m.sender_id = u.id
                     JOIN groups g ON m.group_id = g.group_id
                     WHERE m.message_id = ?`,
                    [messageId]
                );
                
                if (messages.length === 0) {
                    socket.emit('error', {
                        message: 'Failed to send message'
                    });
                    return;
                }
                
                const message = messages[0];
                
                // Send message to group
                io.to(`group:${data.groupId}`).emit('message:new', {
                    id: message.message_id,
                    senderId: message.sender_id,
                    senderName: message.sender_name,
                    senderImage: message.sender_image,
                    groupId: message.group_id,
                    groupName: message.group_name,
                    message: message.message,
                    attachmentUrl: message.attachment_url,
                    attachmentType: message.attachment_type,
                    createdAt: message.created_at,
                    isPrivate: false
                });
                
                // Send confirmation to sender
                socket.emit('message:sent', {
                    id: message.message_id,
                    groupId: data.groupId,
                    message: message.message,
                    attachmentUrl: message.attachment_url,
                    attachmentType: message.attachment_type,
                    createdAt: message.created_at
                });
            } catch (error) {
                console.error('Error sending group message:', error);
                socket.emit('error', {
                    message: 'Failed to send message'
                });
            }
        });
        
        // Handle message read status
        socket.on('message:read', async (data) => {
            try {
                // Validate data
                if (!data.messageId) {
                    return;
                }
                
                // Update message status
                await db.query(
                    'UPDATE message_status SET is_read = 1, read_at = NOW() WHERE message_id = ? AND user_id = ?',
                    [data.messageId, socket.user.id]
                );
                
                // Get message details
                const [messages] = await db.query(
                    'SELECT * FROM chat_messages WHERE message_id = ?',
                    [data.messageId]
                );
                
                if (messages.length === 0) {
                    return;
                }
                
                const message = messages[0];
                
                // Notify sender that message was read
                if (message.sender_id) {
                    io.to(`user:${message.sender_id}`).emit('message:read', {
                        messageId: data.messageId,
                        readBy: socket.user.id,
                        readAt: new Date()
                    });
                }
            } catch (error) {
                console.error('Error marking message as read:', error);
            }
        });
        
        // Handle test mode
        socket.on('test:start', () => {
            usersInTest.set(socket.user.id, true);
            socket.emit('test:mode', { active: true });
        });
        
        socket.on('test:end', () => {
            usersInTest.delete(socket.user.id);
            socket.emit('test:mode', { active: false });
        });
        
        // Handle disconnection
        socket.on('disconnect', () => {
            console.log(`User disconnected: ${socket.user.username} (${socket.user.id})`);
            
            // Remove user from active users
            activeUsers.delete(socket.user.id);
            
            // Emit updated active users list
            io.emit('users:active', Array.from(activeUsers.values()).map(u => ({
                id: u.user.id,
                username: u.user.username,
                profile_image: u.user.profile_image,
                role: u.user.role
            })));
        });
    });
    
    // Helper function to join user to their group rooms
    async function joinUserGroups(socket) {
        try {
            // Get user's groups
            const [groups] = await db.query(
                'SELECT g.* FROM groups g JOIN group_members gm ON g.group_id = gm.group_id WHERE gm.user_id = ?',
                [socket.user.id]
            );
            
            // Join each group room
            for (const group of groups) {
                socket.join(`group:${group.group_id}`);
            }
        } catch (error) {
            console.error('Error joining user groups:', error);
        }
    }
};
