-- <PERSON><PERSON> Script to add sample <NAME_EMAIL>

-- First, check if the user already exists
SET @email = '<EMAIL>';
SET @username = 'csstudent';
SET @password = '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS'; -- This is the hashed version of 'password'

-- Update the user if it exists, otherwise create it
INSERT INTO users (username, name, email, password, role, full_name, date_of_birth, bio, is_active, last_login, created_at)
VALUES (@username, 'CS Student', @email, @password, 'student', 'Computer Science Student', '2005-01-15', 'I am a computer science student interested in programming and web development.', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    username = @username,
    name = 'CS Student',
    password = @password,
    role = 'student',
    full_name = 'Computer Science Student',
    date_of_birth = '2005-01-15',
    bio = 'I am a computer science student interested in programming and web development.',
    is_active = 1,
    last_login = NOW();

-- Get the user ID
SET @student_id = (SELECT id FROM users WHERE BINARY email = BINARY @email);

-- Add user to a Computer Science class
-- First, check if we have a Computer Science class
SET @cs_class_id = (SELECT id FROM classes WHERE trade = 'Science' AND name = '12' LIMIT 1);

-- If no CS class exists, create one
INSERT IGNORE INTO classes (name, trade, section, grade, description)
VALUES ('12', 'Science', 'A', '12', 'Computer Science Class');

-- Get the class ID again if it was just created
SET @cs_class_id = IFNULL(@cs_class_id, (SELECT LAST_INSERT_ID()));

-- Add student to the class if not already added
-- Check if the table has joined_date or created_at column
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = 'exam_prep_platform'
    AND table_name = 'student_classes'
    AND column_name = 'joined_date'
);

-- Use the appropriate column based on what exists in the database
SET @sql = IF(@column_exists > 0,
    'INSERT IGNORE INTO student_classes (student_id, class_id, joined_date) VALUES (?, ?, NOW())',
    'INSERT IGNORE INTO student_classes (student_id, class_id, created_at) VALUES (?, ?, NOW())'
);

PREPARE stmt FROM @sql;
SET @a = @student_id;
SET @b = @cs_class_id;
EXECUTE stmt USING @a, @b;
DEALLOCATE PREPARE stmt;

-- Add Computer Science subject if it doesn't exist
INSERT IGNORE INTO subjects (name, code, description)
VALUES ('Computer Science', 'CS', 'Computer Science for senior secondary');

-- Get the subject ID
SET @cs_subject_id = (SELECT id FROM subjects WHERE BINARY code = BINARY 'CS');

-- Create a sample test for Computer Science
INSERT INTO exams (exam_name, description, duration, instructions, passing_marks, status, difficulty, category_id, created_by)
SELECT
    'Computer Science Fundamentals',
    'Test your knowledge of basic computer science concepts including programming, data structures, and algorithms',
    60,
    'Read each question carefully and select the best answer. You have 60 minutes to complete the exam.',
    60.00,
    'published',
    'intermediate',
    @cs_subject_id,
    1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM exams WHERE BINARY exam_name = BINARY 'Computer Science Fundamentals');

-- Get the exam ID
SET @cs_exam_id = (SELECT exam_id FROM exams WHERE BINARY exam_name = BINARY 'Computer Science Fundamentals');

-- Create a section for the exam if it doesn't exist
INSERT INTO sections (exam_id, section_name, description, duration, position)
SELECT
    @cs_exam_id,
    'Programming Basics',
    'Basic concepts in programming including variables, loops, and functions',
    30,
    1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM sections WHERE exam_id = @cs_exam_id AND BINARY section_name = BINARY 'Programming Basics');

-- Get the section ID
SET @cs_section_id = (SELECT section_id FROM sections WHERE exam_id = @cs_exam_id AND BINARY section_name = BINARY 'Programming Basics');

-- Add questions to the section
-- Question 1
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
SELECT
    @cs_section_id,
    'Which of the following is not a programming language?',
    'HTML',
    'multiple_choice',
    1.00,
    'HTML is a markup language, not a programming language. It is used to structure content on the web.',
    1
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM questions WHERE section_id = @cs_section_id AND position = 1);

-- Get the question ID
SET @q1_id = (SELECT question_id FROM questions WHERE section_id = @cs_section_id AND position = 1);

-- Add options for this question
INSERT IGNORE INTO options (question_id, option_text, position) VALUES
(@q1_id, 'Python', 1),
(@q1_id, 'Java', 2),
(@q1_id, 'HTML', 3),
(@q1_id, 'C++', 4);

-- Question 2
INSERT INTO questions (section_id, question_text, correct_answer, question_type, marks, solution_text, position)
SELECT
    @cs_section_id,
    'What does the acronym "API" stand for?',
    'Application Programming Interface',
    'multiple_choice',
    1.00,
    'API stands for Application Programming Interface. It allows different software applications to communicate with each other.',
    2
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM questions WHERE section_id = @cs_section_id AND position = 2);

-- Get the question ID
SET @q2_id = (SELECT question_id FROM questions WHERE section_id = @cs_section_id AND position = 2);

-- Add options for this question
INSERT IGNORE INTO options (question_id, option_text, position) VALUES
(@q2_id, 'Application Programming Interface', 1),
(@q2_id, 'Advanced Programming Implementation', 2),
(@q2_id, 'Automated Program Integration', 3),
(@q2_id, 'Application Process Instruction', 4);

-- Assign the test to the student
INSERT IGNORE INTO test_assignments (exam_id, user_id, assigned_at, end_datetime, max_attempts)
VALUES (@cs_exam_id, @student_id, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 3);

-- Create a sample practical for the student
INSERT INTO practicals (date, start_time, end_time, class_id, subject_id, lab_id, description, prerequisites, materials, equipment, status, teacher_id)
SELECT
    DATE_ADD(CURDATE(), INTERVAL 7 DAY),
    '09:00:00',
    '11:00:00',
    @cs_class_id,
    @cs_subject_id,
    (SELECT id FROM labs WHERE name = 'Computer Lab 1' LIMIT 1),
    'Introduction to Web Development with HTML, CSS, and JavaScript',
    'Basic computer knowledge',
    'Web development guide, sample code',
    'Computers with text editors and browsers',
    'Upcoming',
    1
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM practicals
    WHERE class_id = @cs_class_id
    AND subject_id = @cs_subject_id
    AND description = 'Introduction to Web Development with HTML, CSS, and JavaScript'
);

-- Get the practical ID
SET @practical_id = (
    SELECT id FROM practicals
    WHERE class_id = @cs_class_id
    AND subject_id = @cs_subject_id
    AND description = 'Introduction to Web Development with HTML, CSS, and JavaScript'
);

-- Add a completed practical record for the student
INSERT INTO practical_records (practical_id, student_id, content, submission_date, status, grade, feedback)
SELECT
    @practical_id,
    @student_id,
    'I created a simple website with HTML, CSS, and JavaScript. The website includes a navigation menu, a contact form, and responsive design.',
    DATE_SUB(CURDATE(), INTERVAL 5 DAY),
    'graded',
    'A',
    'Excellent work! Your website is well-structured and the JavaScript functionality works perfectly.'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM practical_records
    WHERE practical_id = @practical_id
    AND student_id = @student_id
);

-- Add the student to a group
INSERT IGNORE INTO groups (name, description, created_by)
VALUES ('Computer Science Study Group', 'A group for students studying computer science', 1);

-- Get the group ID
SET @group_id = (SELECT group_id FROM groups WHERE BINARY name = BINARY 'Computer Science Study Group');

-- Add the student to the group
INSERT IGNORE INTO group_members (group_id, user_id, joined_at)
VALUES (@group_id, @student_id, NOW());

-- Add some activity history for the student
INSERT IGNORE INTO activity_log (user_id, action, details, created_at)
VALUES
(@student_id, 'login', 'Logged in to the system', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(@student_id, 'test_started', 'Started test: Computer Science Fundamentals', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(@student_id, 'test_completed', 'Completed test: Computer Science Fundamentals with score 85%', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(@student_id, 'practical_submitted', 'Submitted practical: Introduction to Web Development', DATE_SUB(NOW(), INTERVAL 5 DAY));

-- Add a notification for the student
INSERT IGNORE INTO notifications (user_id, title, message, is_read, created_at)
VALUES
(@student_id, 'New Test Available', 'A new Computer Science test has been assigned to you.', 0, NOW()),
(@student_id, 'Practical Graded', 'Your Web Development practical has been graded. You received an A!', 0, NOW());

-- Commit the changes
COMMIT;

-- Display a success message
SELECT CONCAT('Sample data for ', @email, ' has been added successfully.') AS Result;
