-- Simple SQL Script <NAME_EMAIL> user

-- First, check if the user already exists
SET @email = '<EMAIL>';
SET @username = 'csstudent';
SET @password = '$2b$10$EiI/FrX3PLY2CQg6c8Z0EuTTxMcKqHzRVGw1vkwVXBcB4HGQjJWTS'; -- This is the hashed version of 'password'

-- Update the user if it exists, otherwise create it
INSERT INTO users (username, name, email, password, role, full_name, date_of_birth, bio, is_active, last_login, created_at)
VALUES (@username, 'CS Student', @email, @password, 'student', 'Computer Science Student', '2005-01-15', 'I am a computer science student interested in programming and web development.', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    username = @username,
    name = 'CS Student',
    password = @password,
    role = 'student',
    full_name = 'Computer Science Student',
    date_of_birth = '2005-01-15',
    bio = 'I am a computer science student interested in programming and web development.',
    is_active = 1,
    last_login = NOW();

-- Display a success message
SELECT CONCAT('User ', @email, ' has been added successfully.') AS Result;
