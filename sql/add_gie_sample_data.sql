-- SQL script to add sample data for the GIE account
-- Run this script with:
-- cd /Applications/XAMPP/xamppfiles/bin && ./mysql -u root exam_prep_platform < /Applications/XAMPP/xamppfiles/htdocs/meritorious_epx1/sql/add_gie_sample_data.sql

-- Add GIE account to student_subjects if it doesn't exist
INSERT IGNORE INTO student_subjects (student_id, subject_id, created_at)
VALUES 
(55, 1, NOW()),  -- Physics
(55, 2, NOW()),  -- Chemistry
(55, 3, NOW()),  -- Mathematics
(55, 5, NOW());  -- Computer Science

-- Add GIE account to student_classes if it doesn't exist
INSERT IGNORE INTO student_classes (student_id, class_id, created_at)
VALUES (55, 2, NOW());

-- Add sample instruction plans for GIE account
INSERT INTO instruction_plans (title, description, subject_id, teacher_id, status, created_at)
VALUES 
('Introduction to Programming Concepts', 'This learning plan covers the basics of programming including variables, data types, and control structures.', 5, 19, 'published', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('Advanced Programming Techniques', 'This learning plan covers advanced programming concepts including functions, arrays, and object-oriented programming.', 5, 19, 'published', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('Physics Mechanics', 'This learning plan covers the basics of mechanics including motion, forces, and energy.', 1, 22, 'published', CURDATE()),
('Chemical Bonding', 'This learning plan covers different types of chemical bonds and their properties.', 2, 30, 'published', DATE_ADD(CURDATE(), INTERVAL 1 DAY)),
('Algebra and Functions', 'This learning plan covers algebraic expressions, equations, and functions.', 3, 42, 'published', DATE_ADD(CURDATE(), INTERVAL 2 DAY));

-- Add sample assignments for GIE account
INSERT INTO assignments (title, description, subject_id, class_id, student_id, teacher_id, due_date, total_marks)
VALUES 
('Programming Basics Assignment', 'Create a simple program that calculates the area and perimeter of different shapes.', 5, 2, 55, 19, DATE_ADD(CURDATE(), INTERVAL 3 DAY), 20),
('Physics Problem Set', 'Solve the given problems related to motion, forces, and energy.', 1, 2, 55, 22, DATE_ADD(CURDATE(), INTERVAL 5 DAY), 25),
('Chemistry Lab Report', 'Write a lab report on the chemical bonding experiment conducted in class.', 2, 2, 55, 30, DATE_ADD(CURDATE(), INTERVAL 7 DAY), 30),
('Mathematics Problem Set', 'Solve the given problems related to algebraic expressions and functions.', 3, 2, 55, 42, DATE_ADD(CURDATE(), INTERVAL 10 DAY), 25),
('Programming Project', 'Develop a simple calculator application using object-oriented programming principles.', 5, 2, 55, 19, DATE_ADD(CURDATE(), INTERVAL 14 DAY), 50);

-- Add sample practicals for GIE account
INSERT INTO practicals (description, date, start_time, end_time, class_id, subject_id, teacher_id, prerequisites, materials, equipment, status)
VALUES 
('Introduction to Python Programming - Basic programming concepts and syntax in Python.', DATE_ADD(CURDATE(), INTERVAL 2 DAY), '09:00:00', '11:00:00', 2, 5, 19, 'Basic computer knowledge', 'Python installation guide, sample code', 'Computers with Python installed', 'Upcoming'),
('Measurement of g using Simple Pendulum - Determine the acceleration due to gravity using a simple pendulum.', DATE_ADD(CURDATE(), INTERVAL 4 DAY), '10:00:00', '12:00:00', 2, 1, 22, 'Basic knowledge of mechanics', 'Lab manual, pendulum setup', 'Pendulum, stopwatch, meter scale', 'Upcoming'),
('Acid-Base Titration - Determine the concentration of an acid using titration.', DATE_ADD(CURDATE(), INTERVAL 6 DAY), '09:30:00', '11:30:00', 2, 2, 30, 'Basic knowledge of acids and bases', 'Lab manual, safety instructions', 'Burette, pipette, conical flask, indicators', 'Upcoming'),
('Object-Oriented Programming in Python - Learn about classes, objects, inheritance, and polymorphism.', DATE_ADD(CURDATE(), INTERVAL 9 DAY), '09:00:00', '11:00:00', 2, 5, 19, 'Basic Python programming', 'OOP concepts guide, sample code', 'Computers with Python installed', 'Upcoming'),
('Verification of Ohm\'s Law - Verify Ohm\'s law using a simple circuit.', DATE_ADD(CURDATE(), INTERVAL 11 DAY), '10:00:00', '12:00:00', 2, 1, 22, 'Basic knowledge of electricity', 'Lab manual, circuit diagrams', 'Resistors, ammeter, voltmeter, power supply', 'Upcoming');

-- Create a view for calendar events with explicit collation
CREATE OR REPLACE VIEW calendar_events AS
SELECT 
    'instruction_plan' COLLATE utf8mb4_unicode_ci AS event_type,
    id AS event_id,
    title AS event_title,
    created_at AS event_date,
    subject_id,
    NULL AS class_id,
    NULL AS teacher_id,
    NULL AS due_date,
    NULL AS start_time,
    NULL AS end_time,
    NULL AS status
FROM instruction_plans
WHERE status = 'published'

UNION ALL

SELECT 
    'assignment' COLLATE utf8mb4_unicode_ci AS event_type,
    id AS event_id,
    title AS event_title,
    created_at AS event_date,
    subject_id,
    class_id,
    teacher_id,
    due_date,
    NULL AS start_time,
    NULL AS end_time,
    NULL AS status
FROM assignments

UNION ALL

SELECT 
    'practical' COLLATE utf8mb4_unicode_ci AS event_type,
    id AS event_id,
    CONVERT(description USING utf8mb4) COLLATE utf8mb4_unicode_ci AS event_title,
    date AS event_date,
    subject_id,
    class_id,
    teacher_id,
    NULL AS due_date,
    start_time,
    end_time,
    status COLLATE utf8mb4_unicode_ci
FROM practicals;
