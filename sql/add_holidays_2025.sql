-- Add holidays for 2025 to the holiday_calendar table

-- First, make sure the holiday_calendar table exists
CREATE TABLE IF NOT EXISTS `holiday_calendar` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `holiday_date` DATE NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `holiday_type` VARCHAR(50) DEFAULT 'Public Holiday',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  UNIQUE KEY `unique_holiday_date` (`holiday_date`)
);

-- Insert holidays for 2025
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2025-01-06', '<PERSON>', 'Festival'),
('2025-01-26', 'Republic Day', 'National Holiday'),
('2025-02-03', '<PERSON><PERSON><PERSON><PERSON>', 'Festival'),
('2025-02-12', '<PERSON>', 'Festival'),
('2025-02-26', '<PERSON><PERSON>', 'Festival'),
('2025-03-14', 'Ho<PERSON>', 'Festival'),
('2025-03-31', 'Idul Fitr', 'Festival'),
('2025-04-06', 'Ram Navami', 'Festival'),
('2025-04-10', 'Mahavir Jayanti', 'Festival'),
('2025-04-13', 'Vaisakh', 'Festival'),
('2025-04-14', 'Dr Ambedkar Jayanti', 'National Holiday'),
('2025-04-18', 'Good Friday', 'Festival'),
('2025-05-30', 'Sri Guru Arjun Dev Ji\'s Martyrdom Day', 'Festival'),
('2025-06-07', 'Bakrid / Eid al Adha', 'Festival'),
('2025-06-11', 'Sant Guru Kabir Jayanti', 'Festival'),
('2025-08-15', 'Independence Day', 'National Holiday'),
('2025-08-16', 'Janmashtami', 'Festival'),
('2025-10-02', 'Vijaya Dashami', 'Festival'),
('2025-10-02', 'Mahatma Gandhi Jayanti', 'National Holiday'),
('2025-10-21', 'Diwali', 'Festival'),
('2025-11-05', 'Guru Nanak Jayanti', 'Festival'),
('2025-11-25', 'Sri Guru Tej Bahadur Ji\'s Martyrdom Day', 'Festival'),
('2025-12-25', 'Christmas Day', 'National Holiday'),
('2025-12-27', 'Guru Gobind Singh Jayanti', 'Festival');
