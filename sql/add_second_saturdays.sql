-- Add second Saturdays of each month as holidays to the holiday_calendar table

-- First, make sure the holiday_calendar table exists
CREATE TABLE IF NOT EXISTS `holiday_calendar` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `holiday_date` DATE NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `holiday_type` VARCHAR(50) DEFAULT 'Public Holiday',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  UNIQUE KEY `unique_holiday_date` (`holiday_date`)
);

-- Insert second Saturdays for 2024
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2024-01-13', 'Second Saturday', 'Public Holiday'),
('2024-02-10', 'Second Saturday', 'Public Holiday'),
('2024-03-09', 'Second Saturday', 'Public Holiday'),
('2024-04-13', 'Second Saturday', 'Public Holiday'),
('2024-05-11', 'Second Saturday', 'Public Holiday'),
('2024-06-08', 'Second Saturday', 'Public Holiday'),
('2024-07-13', 'Second Saturday', 'Public Holiday'),
('2024-08-10', 'Second Saturday', 'Public Holiday'),
('2024-09-14', 'Second Saturday', 'Public Holiday'),
('2024-10-12', 'Second Saturday', 'Public Holiday'),
('2024-11-09', 'Second Saturday', 'Public Holiday'),
('2024-12-14', 'Second Saturday', 'Public Holiday');

-- Insert second Saturdays for 2025
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2025-01-11', 'Second Saturday', 'Public Holiday'),
('2025-02-08', 'Second Saturday', 'Public Holiday'),
('2025-03-08', 'Second Saturday', 'Public Holiday'),
('2025-04-12', 'Second Saturday', 'Public Holiday'),
('2025-05-10', 'Second Saturday', 'Public Holiday'),
('2025-06-14', 'Second Saturday', 'Public Holiday'),
('2025-07-12', 'Second Saturday', 'Public Holiday'),
('2025-08-09', 'Second Saturday', 'Public Holiday'),
('2025-09-13', 'Second Saturday', 'Public Holiday'),
('2025-10-11', 'Second Saturday', 'Public Holiday'),
('2025-11-08', 'Second Saturday', 'Public Holiday'),
('2025-12-13', 'Second Saturday', 'Public Holiday');
