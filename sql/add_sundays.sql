-- Add all Sundays as holidays to the holiday_calendar table

-- First, make sure the holiday_calendar table exists
CREATE TABLE IF NOT EXISTS `holiday_calendar` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `holiday_date` DATE NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `holiday_type` VARCHAR(50) DEFAULT 'Public Holiday',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  UNIQUE KEY `unique_holiday_date` (`holiday_date`)
);

-- Insert Sundays for 2024 (January to December)
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2024-01-07', 'Sunday', 'National Holiday'),
('2024-01-14', 'Sunday', 'National Holiday'),
('2024-01-21', 'Sunday', 'National Holiday'),
('2024-01-28', 'Sunday', 'National Holiday'),
('2024-02-04', 'Sunday', 'National Holiday'),
('2024-02-11', 'Sunday', 'National Holiday'),
('2024-02-18', 'Sunday', 'National Holiday'),
('2024-02-25', 'Sunday', 'National Holiday'),
('2024-03-03', 'Sunday', 'National Holiday'),
('2024-03-10', 'Sunday', 'National Holiday'),
('2024-03-17', 'Sunday', 'National Holiday'),
('2024-03-24', 'Sunday', 'National Holiday'),
('2024-03-31', 'Sunday', 'National Holiday'),
('2024-04-07', 'Sunday', 'National Holiday'),
('2024-04-14', 'Sunday', 'National Holiday'),
('2024-04-21', 'Sunday', 'National Holiday'),
('2024-04-28', 'Sunday', 'National Holiday'),
('2024-05-05', 'Sunday', 'National Holiday'),
('2024-05-12', 'Sunday', 'National Holiday'),
('2024-05-19', 'Sunday', 'National Holiday'),
('2024-05-26', 'Sunday', 'National Holiday'),
('2024-06-02', 'Sunday', 'National Holiday'),
('2024-06-09', 'Sunday', 'National Holiday'),
('2024-06-16', 'Sunday', 'National Holiday'),
('2024-06-23', 'Sunday', 'National Holiday'),
('2024-06-30', 'Sunday', 'National Holiday'),
('2024-07-07', 'Sunday', 'National Holiday'),
('2024-07-14', 'Sunday', 'National Holiday'),
('2024-07-21', 'Sunday', 'National Holiday'),
('2024-07-28', 'Sunday', 'National Holiday'),
('2024-08-04', 'Sunday', 'National Holiday'),
('2024-08-11', 'Sunday', 'National Holiday'),
('2024-08-18', 'Sunday', 'National Holiday'),
('2024-08-25', 'Sunday', 'National Holiday'),
('2024-09-01', 'Sunday', 'National Holiday'),
('2024-09-08', 'Sunday', 'National Holiday'),
('2024-09-15', 'Sunday', 'National Holiday'),
('2024-09-22', 'Sunday', 'National Holiday'),
('2024-09-29', 'Sunday', 'National Holiday'),
('2024-10-06', 'Sunday', 'National Holiday'),
('2024-10-13', 'Sunday', 'National Holiday'),
('2024-10-20', 'Sunday', 'National Holiday'),
('2024-10-27', 'Sunday', 'National Holiday'),
('2024-11-03', 'Sunday', 'National Holiday'),
('2024-11-10', 'Sunday', 'National Holiday'),
('2024-11-17', 'Sunday', 'National Holiday'),
('2024-11-24', 'Sunday', 'National Holiday'),
('2024-12-01', 'Sunday', 'National Holiday'),
('2024-12-08', 'Sunday', 'National Holiday'),
('2024-12-15', 'Sunday', 'National Holiday'),
('2024-12-22', 'Sunday', 'National Holiday'),
('2024-12-29', 'Sunday', 'National Holiday');

-- Insert Sundays for 2025 (January to December)
INSERT IGNORE INTO holiday_calendar (holiday_date, description, holiday_type)
VALUES 
('2025-01-05', 'Sunday', 'National Holiday'),
('2025-01-12', 'Sunday', 'National Holiday'),
('2025-01-19', 'Sunday', 'National Holiday'),
('2025-01-26', 'Sunday', 'National Holiday'),
('2025-02-02', 'Sunday', 'National Holiday'),
('2025-02-09', 'Sunday', 'National Holiday'),
('2025-02-16', 'Sunday', 'National Holiday'),
('2025-02-23', 'Sunday', 'National Holiday'),
('2025-03-02', 'Sunday', 'National Holiday'),
('2025-03-09', 'Sunday', 'National Holiday'),
('2025-03-16', 'Sunday', 'National Holiday'),
('2025-03-23', 'Sunday', 'National Holiday'),
('2025-03-30', 'Sunday', 'National Holiday'),
('2025-04-06', 'Sunday', 'National Holiday'),
('2025-04-13', 'Sunday', 'National Holiday'),
('2025-04-20', 'Sunday', 'National Holiday'),
('2025-04-27', 'Sunday', 'National Holiday'),
('2025-05-04', 'Sunday', 'National Holiday'),
('2025-05-11', 'Sunday', 'National Holiday'),
('2025-05-18', 'Sunday', 'National Holiday'),
('2025-05-25', 'Sunday', 'National Holiday'),
('2025-06-01', 'Sunday', 'National Holiday'),
('2025-06-08', 'Sunday', 'National Holiday'),
('2025-06-15', 'Sunday', 'National Holiday'),
('2025-06-22', 'Sunday', 'National Holiday'),
('2025-06-29', 'Sunday', 'National Holiday'),
('2025-07-06', 'Sunday', 'National Holiday'),
('2025-07-13', 'Sunday', 'National Holiday'),
('2025-07-20', 'Sunday', 'National Holiday'),
('2025-07-27', 'Sunday', 'National Holiday'),
('2025-08-03', 'Sunday', 'National Holiday'),
('2025-08-10', 'Sunday', 'National Holiday'),
('2025-08-17', 'Sunday', 'National Holiday'),
('2025-08-24', 'Sunday', 'National Holiday'),
('2025-08-31', 'Sunday', 'National Holiday'),
('2025-09-07', 'Sunday', 'National Holiday'),
('2025-09-14', 'Sunday', 'National Holiday'),
('2025-09-21', 'Sunday', 'National Holiday'),
('2025-09-28', 'Sunday', 'National Holiday'),
('2025-10-05', 'Sunday', 'National Holiday'),
('2025-10-12', 'Sunday', 'National Holiday'),
('2025-10-19', 'Sunday', 'National Holiday'),
('2025-10-26', 'Sunday', 'National Holiday'),
('2025-11-02', 'Sunday', 'National Holiday'),
('2025-11-09', 'Sunday', 'National Holiday'),
('2025-11-16', 'Sunday', 'National Holiday'),
('2025-11-23', 'Sunday', 'National Holiday'),
('2025-11-30', 'Sunday', 'National Holiday'),
('2025-12-07', 'Sunday', 'National Holiday'),
('2025-12-14', 'Sunday', 'National Holiday'),
('2025-12-21', 'Sunday', 'National Holiday'),
('2025-12-28', 'Sunday', 'National Holiday');
