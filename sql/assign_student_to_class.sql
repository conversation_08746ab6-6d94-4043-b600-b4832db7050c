-- Create student_classes table if it doesn't exist
CREATE TABLE IF NOT EXISTS `student_classes` (
  `student_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`student_id`,`class_id`),
  KEY `class_id` (`class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Check if there are any students in the users table
SELECT COUNT(*) AS student_count FROM users WHERE role = 'student';

-- Let's just use an existing student instead of creating a new one
-- Get the ID of an existing student
SET @student_id = (SELECT id FROM users WHERE role = 'student' LIMIT 1);

-- Check if there are any classes
SELECT COUNT(*) AS class_count FROM classes;

-- If there are no classes, create a sample class
INSERT INTO classes (name, trade, section, grade, description)
SELECT 'Class 12', 'Science', 'A', '12', 'Sample class for practicals'
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM classes LIMIT 1);

-- Get the ID of a class (either existing or newly created)
SET @class_id = (SELECT id FROM classes LIMIT 1);

-- Assign the student to the class if not already assigned
INSERT IGNORE INTO student_classes (student_id, class_id)
VALUES (@student_id, @class_id);

-- Verify the assignment
SELECT u.id AS student_id, u.username, c.id AS class_id, c.name AS class_name
FROM users u
JOIN student_classes sc ON u.id = sc.student_id
JOIN classes c ON sc.class_id = c.id
WHERE u.id = @student_id;
