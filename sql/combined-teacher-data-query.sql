-- =====================================================
-- COMBINED TEACHER DATA QUERY FOR ONE ID
-- Retrieves ALL teacher data across ALL tables for a single teacher
-- =====================================================

-- Main query combining all teacher-related data
WITH teacher_base AS (
    SELECT
        -- User Table Fields
        u.id as user_id,
        u.username,
        u.name,
        u.full_name,
        u.email,
        u.role,
        u.profile_image,
        u.subjects as user_subjects,
        u.bio,
        u.date_of_birth,
        u.created_at,
        u.last_login,
        u.is_active,
        u.institution,
        u.grade,
        u.field_of_study,
        u.preferred_subjects,
        u.target_exams,
        
        -- Staff Table Fields
        s.id as staff_id,
        s.employee_id,
        s.designation,
        s.department,
        s.current_school,
        s.joining_date,
        s.employment_type,
        s.phone,
        s.alternate_phone,
        s.emergency_contact,
        s.address,
        s.city,
        s.state,
        s.pincode,
        s.gender,
        s.current_salary,
        s.probation_period_months,
        s.confirmation_date,
        s.last_promotion_date,
        s.performance_rating,
        s.is_on_leave,
        s.office_location,
        s.subjects_taught,
        s.classes_handled,
        s.total_experience_years,
        s.teaching_experience_years,
        s.administrative_experience_years,
        s.awards_received,
        s.publications,
        s.research_papers,
        s.conferences_attended,
        s.training_programs,
        s.notes as staff_notes,
        s.is_active as staff_active
    FROM users u
    LEFT JOIN staff s ON u.id = s.user_id
    WHERE u.id = 103 AND u.role = 'teacher'
),
education_data AS (
    SELECT 
        staff_id,
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'qualification_level', qualification_level,
                'qualification_name', qualification_name,
                'specialization', specialization,
                'institution_name', institution_name,
                'university_board', university_board,
                'total_marks_obtained', total_marks_obtained,
                'total_marks_maximum', total_marks_maximum,
                'percentage', percentage,
                'grade', grade,
                'cgpa', cgpa,
                'completion_year', completion_year,
                'subjects', subjects,
                'achievements', achievements,
                'thesis_title', thesis_title
            )
        ) as education_timeline
    FROM staff_educational_qualifications
    WHERE staff_id = (SELECT staff_id FROM teacher_base)
    GROUP BY staff_id
),
experience_data AS (
    SELECT 
        staff_id,
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'job_title', job_title,
                'organization_name', organization_name,
                'organization_type', organization_type,
                'start_date', start_date,
                'end_date', end_date,
                'is_current', is_current,
                'total_duration_months', total_duration_months,
                'job_description', job_description,
                'key_responsibilities', key_responsibilities,
                'achievements', achievements,
                'skills_used', skills_used,
                'salary_range', salary_range,
                'performance_rating', performance_rating
            )
        ) as experience_timeline
    FROM staff_professional_experience
    WHERE staff_id = (SELECT staff_id FROM teacher_base)
    GROUP BY staff_id
),
certifications_data AS (
    SELECT 
        staff_id,
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'certification_name', certification_name,
                'certification_type', certification_type,
                'issuing_organization', issuing_organization,
                'issue_date', issue_date,
                'expiry_date', expiry_date,
                'is_lifetime', is_lifetime,
                'certificate_id', certificate_id,
                'verification_status', verification_status,
                'description', description,
                'skills_covered', skills_covered
            )
        ) as certifications
    FROM staff_certifications
    WHERE staff_id = (SELECT staff_id FROM teacher_base)
    GROUP BY staff_id
),
skills_data AS (
    SELECT 
        staff_id,
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'skill_category', skill_category,
                'skill_name', skill_name,
                'proficiency_level', proficiency_level,
                'years_of_experience', years_of_experience,
                'is_certified', is_certified,
                'last_used_date', last_used_date
            )
        ) as skills
    FROM staff_skills
    WHERE staff_id = (SELECT staff_id FROM teacher_base)
    GROUP BY staff_id
)
SELECT 
    tb.*,
    COALESCE(ed.education_timeline, JSON_ARRAY()) as education_timeline,
    COALESCE(ex.experience_timeline, JSON_ARRAY()) as experience_timeline,
    COALESCE(cd.certifications, JSON_ARRAY()) as certifications,
    COALESCE(sd.skills, JSON_ARRAY()) as skills,
    
    -- Calculated fields
    CASE 
        WHEN tb.date_of_birth IS NOT NULL THEN 
            FLOOR(DATEDIFF(CURDATE(), tb.date_of_birth) / 365.25)
        ELSE NULL 
    END as calculated_age,
    
    CASE 
        WHEN tb.last_login IS NOT NULL THEN 
            DATE_FORMAT(tb.last_login, '%M %d, %Y at %h:%i %p')
        ELSE 'Never logged in' 
    END as formatted_last_login,
    
    CASE 
        WHEN tb.created_at IS NOT NULL THEN 
            DATE_FORMAT(tb.created_at, '%M %d, %Y')
        ELSE 'Unknown' 
    END as formatted_account_created,
    
    CASE 
        WHEN tb.is_active = 1 THEN 'Active'
        ELSE 'Inactive' 
    END as account_status,
    
    COALESCE(tb.full_name, tb.name, tb.username) as display_name,
    
    -- Summary statistics
    (SELECT COUNT(*) FROM staff_educational_qualifications WHERE staff_id = tb.staff_id) as total_qualifications,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = tb.staff_id) as total_experience,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = tb.staff_id AND is_current = 0) as previous_experience_count,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = tb.staff_id AND is_current = 1) as current_position_count,
    (SELECT COUNT(*) FROM staff_certifications WHERE staff_id = tb.staff_id) as total_certifications,
    (SELECT COUNT(*) FROM staff_skills WHERE staff_id = tb.staff_id) as total_skills,
    (SELECT COUNT(DISTINCT skill_category) FROM staff_skills WHERE staff_id = tb.staff_id) as skill_categories_count

FROM teacher_base tb
LEFT JOIN education_data ed ON tb.staff_id = ed.staff_id
LEFT JOIN experience_data ex ON tb.staff_id = ex.staff_id
LEFT JOIN certifications_data cd ON tb.staff_id = cd.staff_id
LEFT JOIN skills_data sd ON tb.staff_id = sd.staff_id;
