-- =====================================================
-- COMPLETE TEACHER DATA QUERIES - GETS ALL DATA INCLUDING PREVIOUS EXPERIENCE
-- These are the CORRECT queries that retrieve ALL detailed data
-- =====================================================

-- QUERY 1: MAIN TEACHER INFORMATION (User + Staff Tables)
-- This gets the basic teacher profile information
SELECT 
    -- User Table Data
    u.id as user_id,
    u.username,
    u.name,
    u.full_name,
    u.email,
    u.role,
    u.profile_image,
    u.subjects as user_subjects,
    u.bio,
    u.date_of_birth,
    u.created_at,
    u.last_login,
    u.is_active,
    
    -- Staff Table Data
    s.id as staff_id,
    s.employee_id,
    s.designation,
    s.department,
    s.current_school,
    s.joining_date,
    s.employment_type,
    s.phone,
    s.alternate_phone,
    s.emergency_contact,
    s.address,
    s.city,
    s.state,
    s.pincode,
    s.gender,                    -- ✅ Gender column
    s.current_salary,
    s.subjects_taught,
    s.classes_handled,
    s.total_experience_years,
    s.teaching_experience_years,
    s.performance_rating,
    s.awards_received,
    s.notes as staff_notes,
    
    -- Calculated Fields
    FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
    COALESCE(u.full_name, u.name, u.username) as display_name,
    CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
    
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.id = 103 AND u.role = 'teacher';

-- =====================================================

-- QUERY 2: ALL EDUCATIONAL QUALIFICATIONS (✅ Gets all education records)
-- This retrieves ALL educational qualifications with complete details
SELECT 
    eq.id,
    eq.qualification_level,
    eq.qualification_name,
    eq.specialization,
    eq.institution_name,
    eq.university_board,
    eq.total_marks_obtained,
    eq.total_marks_maximum,
    eq.percentage,
    eq.grade,
    eq.cgpa,
    eq.completion_year,
    eq.subjects,                 -- JSON field with subject-wise marks
    eq.achievements,
    eq.thesis_title,
    eq.created_at
FROM staff_educational_qualifications eq
WHERE eq.staff_id = 48          -- CS Teacher's staff_id
ORDER BY eq.completion_year ASC;

-- =====================================================

-- QUERY 3: ALL PROFESSIONAL EXPERIENCE (✅ Gets BOTH current AND previous)
-- This retrieves ALL professional experience including previous positions
SELECT 
    pe.id,
    pe.job_title,
    pe.organization_name,
    pe.organization_type,
    pe.start_date,
    pe.end_date,
    pe.is_current,               -- 0 = PREVIOUS, 1 = CURRENT
    pe.total_duration_months,
    pe.job_description,
    pe.key_responsibilities,     -- JSON field with responsibilities list
    pe.achievements,             -- JSON field with achievements list
    pe.skills_used,              -- JSON field with skills list
    pe.salary_range,
    pe.performance_rating,
    pe.created_at,
    CASE WHEN pe.is_current = 1 THEN 'CURRENT' ELSE 'PREVIOUS' END as position_status
FROM staff_professional_experience pe
WHERE pe.staff_id = 48          -- CS Teacher's staff_id
ORDER BY pe.start_date ASC;

-- =====================================================

-- QUERY 4: ALL CERTIFICATIONS
-- This retrieves all professional certifications
SELECT 
    c.id,
    c.certification_name,
    c.certification_type,
    c.issuing_organization,
    c.issue_date,
    c.expiry_date,
    c.is_lifetime,
    c.certificate_id,
    c.verification_status,
    c.description,
    c.skills_covered             -- JSON field with skills covered
FROM staff_certifications c
WHERE c.staff_id = 48           -- CS Teacher's staff_id
ORDER BY c.issue_date DESC;

-- =====================================================

-- QUERY 5: ALL SKILLS BY CATEGORY
-- This retrieves all skills organized by category
SELECT 
    sk.id,
    sk.skill_category,
    sk.skill_name,
    sk.proficiency_level,
    sk.years_of_experience,
    sk.is_certified,
    sk.last_used_date
FROM staff_skills sk
WHERE sk.staff_id = 48          -- CS Teacher's staff_id
ORDER BY sk.skill_category, sk.proficiency_level DESC;

-- =====================================================
-- SAMPLE RESULTS FOR CS TEACHER (User ID: 103, Staff ID: 48)
-- =====================================================

/*
QUERY 1 RESULT - MAIN TEACHER INFO:
- User ID: 103, Staff ID: 48
- Name: CS Teacher, Email: <EMAIL>
- Gender: male ✅, Age: 36 years
- Bio: Computer Science teacher with expertise in programming
- Account Status: Active, Last Login: May 12, 2025

QUERY 2 RESULT - EDUCATIONAL QUALIFICATIONS (3 records): ✅
1. Secondary School Certificate (2007) - 88.40%
   - Government High School, Ludhiana
   - 5 subjects with individual marks
2. Higher Secondary Certificate (2009) - 91.80%
   - DAV College, Ludhiana, Science (Non-Medical)
3. Bachelor of Technology (2011) - 88.97%, CGPA: 8.9
   - Punjab Technical University, CSE

QUERY 3 RESULT - PROFESSIONAL EXPERIENCE (3 records): ✅
1. Software Engineer (PREVIOUS) ✅
   - Accenture Pvt Ltd (2011-2013)
   - 5 responsibilities, 4 achievements, 10 skills
2. Physics Tutor (PREVIOUS) ✅
   - Career Makers (2013-2015)
   - 5 responsibilities, 4 achievements, 6 skills
3. Computer Science Teacher (CURRENT)
   - Government School (2015-Present)
   - 6 responsibilities, 6 achievements, 8 skills

QUERY 4 RESULT - CERTIFICATIONS (2 records):
1. B.Ed (Bachelor of Education) - Teaching, Lifetime
2. Oracle Java SE 8 Programmer - Technical, Expires 2026

QUERY 5 RESULT - SKILLS (16 records across 4 categories):
- TECHNICAL (6): Java (Expert), Python (Advanced), C++, MySQL, etc.
- TEACHING (4): Assessment (Expert), Classroom Management (Expert), etc.
- LANGUAGE (3): English, Hindi, Punjabi (All Expert)
- SOFT_SKILL (3): Communication (Expert), Problem Solving (Expert), etc.
*/

-- =====================================================
-- VERIFICATION OF ISSUES RESOLVED
-- =====================================================

/*
✅ ISSUE 1: Gender Column
- Query 1 includes s.gender field
- Result shows: Gender: male

✅ ISSUE 2: Educational Timeline Empty
- Query 2 retrieves ALL educational qualifications
- Result shows: 3 comprehensive education records

✅ ISSUE 3: Previous Experience Not Showing
- Query 3 retrieves ALL professional experience
- Result shows: 2 PREVIOUS positions + 1 CURRENT position
- Previous positions are properly identified with is_current = 0

ALL THREE ISSUES HAVE BEEN RESOLVED!
*/

-- =====================================================
-- USAGE INSTRUCTIONS
-- =====================================================

/*
1. Execute Query 1 to get main teacher information
2. Execute Query 2 to get all educational qualifications
3. Execute Query 3 to get all professional experience (current + previous)
4. Execute Query 4 to get all certifications
5. Execute Query 5 to get all skills by category

Replace the hardcoded IDs (103 for user_id, 48 for staff_id) with 
the actual IDs for the teacher you want to query.

These queries return the complete enhanced teacher profile data
that is displayed in the modal.
*/
