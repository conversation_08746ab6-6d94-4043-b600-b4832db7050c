-- =====================================================
-- CORRECTED COMBINED QUERY - GETS ALL DATA INCLUDING PREVIOUS EXPERIENCE AND EDUCATION
-- This query actually retrieves the detailed data, not just summary statistics
-- =====================================================

-- OPTION 1: UNION ALL APPROACH - Gets all data in one result set
-- This combines teacher info with all related records

SELECT 
    'TEACHER_INFO' as record_type,
    -- User Table Data
    u.id as user_id,
    u.username,
    u.name,
    u.full_name,
    u.email,
    u.role,
    u.bio,
    u.date_of_birth,
    u.last_login,
    u.is_active,
    
    -- Staff Table Data
    s.id as staff_id,
    s.employee_id,
    s.designation,
    s.department,
    s.gender,
    s.phone,
    s.joining_date,
    s.employment_type,
    s.subjects_taught,
    s.classes_handled,
    s.total_experience_years,
    s.teaching_experience_years,
    s.performance_rating,
    s.current_salary,
    s.awards_received,
    s.notes as staff_notes,
    
    -- Calculated fields
    FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
    COALESCE(u.full_name, u.name, u.username) as display_name,
    
    -- Placeholder fields for other record types
    NULL as qualification_name,
    NULL as institution_name,
    NULL as completion_year,
    NULL as percentage,
    NULL as subjects_json,
    NULL as job_title,
    NULL as organization_name,
    NULL as start_date,
    NULL as end_date,
    NULL as is_current,
    NULL as job_description,
    NULL as responsibilities_json,
    NULL as achievements_json,
    NULL as skills_used_json,
    NULL as certification_name,
    NULL as issuing_organization,
    NULL as issue_date,
    NULL as skill_category,
    NULL as skill_name,
    NULL as proficiency_level

FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.id = 103 AND u.role = 'teacher'

UNION ALL

-- Educational Qualifications
SELECT 
    'EDUCATION' as record_type,
    u.id as user_id,
    u.username,
    u.name,
    u.full_name,
    u.email,
    u.role,
    u.bio,
    u.date_of_birth,
    u.last_login,
    u.is_active,
    s.id as staff_id,
    s.employee_id,
    s.designation,
    s.department,
    s.gender,
    s.phone,
    s.joining_date,
    s.employment_type,
    s.subjects_taught,
    s.classes_handled,
    s.total_experience_years,
    s.teaching_experience_years,
    s.performance_rating,
    s.current_salary,
    s.awards_received,
    s.notes as staff_notes,
    FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
    COALESCE(u.full_name, u.name, u.username) as display_name,
    
    -- Education specific fields
    eq.qualification_name,
    eq.institution_name,
    eq.completion_year,
    eq.percentage,
    eq.subjects as subjects_json,
    
    -- Placeholder fields
    NULL as job_title,
    NULL as organization_name,
    NULL as start_date,
    NULL as end_date,
    NULL as is_current,
    NULL as job_description,
    NULL as responsibilities_json,
    NULL as achievements_json,
    NULL as skills_used_json,
    NULL as certification_name,
    NULL as issuing_organization,
    NULL as issue_date,
    NULL as skill_category,
    NULL as skill_name,
    NULL as proficiency_level

FROM users u
LEFT JOIN staff s ON u.id = s.user_id
LEFT JOIN staff_educational_qualifications eq ON s.id = eq.staff_id
WHERE u.id = 103 AND u.role = 'teacher' AND eq.id IS NOT NULL

UNION ALL

-- Professional Experience (INCLUDING PREVIOUS)
SELECT 
    CASE WHEN pe.is_current = 1 THEN 'CURRENT_EXPERIENCE' ELSE 'PREVIOUS_EXPERIENCE' END as record_type,
    u.id as user_id,
    u.username,
    u.name,
    u.full_name,
    u.email,
    u.role,
    u.bio,
    u.date_of_birth,
    u.last_login,
    u.is_active,
    s.id as staff_id,
    s.employee_id,
    s.designation,
    s.department,
    s.gender,
    s.phone,
    s.joining_date,
    s.employment_type,
    s.subjects_taught,
    s.classes_handled,
    s.total_experience_years,
    s.teaching_experience_years,
    s.performance_rating,
    s.current_salary,
    s.awards_received,
    s.notes as staff_notes,
    FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
    COALESCE(u.full_name, u.name, u.username) as display_name,
    
    -- Placeholder education fields
    NULL as qualification_name,
    NULL as institution_name,
    NULL as completion_year,
    NULL as percentage,
    NULL as subjects_json,
    
    -- Experience specific fields
    pe.job_title,
    pe.organization_name,
    pe.start_date,
    pe.end_date,
    pe.is_current,
    pe.job_description,
    pe.key_responsibilities as responsibilities_json,
    pe.achievements as achievements_json,
    pe.skills_used as skills_used_json,
    
    -- Placeholder fields
    NULL as certification_name,
    NULL as issuing_organization,
    NULL as issue_date,
    NULL as skill_category,
    NULL as skill_name,
    NULL as proficiency_level

FROM users u
LEFT JOIN staff s ON u.id = s.user_id
LEFT JOIN staff_professional_experience pe ON s.id = pe.staff_id
WHERE u.id = 103 AND u.role = 'teacher' AND pe.id IS NOT NULL

ORDER BY record_type, completion_year, start_date;
