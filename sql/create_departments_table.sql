-- Create departments table
CREATE TABLE IF NOT EXISTS `departments` (
  `department_id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `is_active` TINYINT(1) NOT NULL DEFAULT 1,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`department_id`),
  UNIQUE KEY `unique_department_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert departments
INSERT INTO `departments` (`name`, `description`, `is_active`) VALUES
('PHYSICS', 'Physics Department', 1),
('CHEMISTRY', 'Chemistry Department', 1),
('BIOLOGY', 'Biology Department', 1),
('MATHEMATICS', 'Mathematics Department', 1),
('ENGLISH', 'English Department', 1),
('PUNJABI', 'Punjabi Department', 1),
('COMPUTER SCIENCE', 'Computer Science Department', 1);
