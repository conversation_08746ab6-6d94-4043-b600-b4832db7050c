-- Create inventory_item_images table to store multiple images per item
CREATE TABLE IF NOT EXISTS `inventory_item_images` (
  `image_id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`image_id`),
  KEY `item_id` (`item_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `inventory_item_images_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`item_id`) ON DELETE CASCADE,
  CONSTRAINT `inventory_item_images_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add index for faster retrieval of primary images
CREATE INDEX idx_inventory_item_images_primary ON inventory_item_images(item_id, is_primary);

-- Migrate existing images from inventory_items to inventory_item_images
INSERT INTO inventory_item_images (item_id, image_url, is_primary, created_by)
SELECT item_id, image, 1, created_by FROM inventory_items WHERE image IS NOT NULL AND image != '';
