-- =====================================================
-- ENHANCED TEACHER PROFILE QUERIES
-- Complete set of queries to retrieve comprehensive teacher data
-- =====================================================

-- 1. MAIN TEACHER DATA QUERY (Users + Staff Tables)
-- This query joins user and staff tables to get complete teacher information
SELECT
    -- User Table Fields
    u.id as user_id, 
    u.username, 
    u.name, 
    u.full_name, 
    u.email, 
    u.role, 
    u.profile_image,
    u.subjects as user_subjects, 
    u.bio, 
    u.date_of_birth, 
    u.created_at, 
    u.last_login, 
    u.is_active,
    u.institution, 
    u.grade, 
    u.field_of_study, 
    u.preferred_subjects, 
    u.target_exams,
    
    -- Staff Table Fields
    s.id as staff_id, 
    s.employee_id, 
    s.designation, 
    s.department, 
    s.current_school,
    s.joining_date, 
    s.employment_type, 
    s.phone, 
    s.alternate_phone, 
    s.emergency_contact,
    s.address, 
    s.city, 
    s.state, 
    s.pincode, 
    s.gender, 
    s.current_salary, 
    s.probation_period_months,
    s.confirmation_date, 
    s.last_promotion_date, 
    s.performance_rating, 
    s.is_on_leave,
    s.office_location, 
    s.subjects_taught, 
    s.classes_handled, 
    s.total_experience_years,
    s.teaching_experience_years, 
    s.administrative_experience_years, 
    s.awards_received,
    s.publications, 
    s.research_papers, 
    s.conferences_attended, 
    s.training_programs,
    s.notes as staff_notes, 
    s.is_active as staff_active
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.id = ? AND u.role = 'teacher';

-- 2. EDUCATIONAL QUALIFICATIONS QUERY
-- Retrieves detailed educational background with subject-wise marks
SELECT 
    qualification_level, 
    qualification_name, 
    specialization, 
    institution_name,
    university_board, 
    total_marks_obtained, 
    total_marks_maximum, 
    percentage,
    grade, 
    cgpa, 
    completion_year, 
    subjects,  -- JSON field containing subject-wise marks
    achievements, 
    thesis_title
FROM staff_educational_qualifications
WHERE staff_id = ?
ORDER BY completion_year ASC;

-- 3. PROFESSIONAL EXPERIENCE QUERY
-- Retrieves complete work history with responsibilities and achievements
SELECT 
    job_title, 
    organization_name, 
    organization_type, 
    start_date, 
    end_date,
    is_current, 
    total_duration_months, 
    job_description, 
    key_responsibilities,  -- JSON field containing list of responsibilities
    achievements,          -- JSON field containing list of achievements
    skills_used,          -- JSON field containing list of skills
    salary_range, 
    performance_rating
FROM staff_professional_experience
WHERE staff_id = ?
ORDER BY start_date ASC;

-- 4. CERTIFICATIONS QUERY
-- Retrieves professional certifications and qualifications
SELECT 
    certification_name, 
    certification_type, 
    issuing_organization,
    issue_date, 
    expiry_date, 
    is_lifetime, 
    certificate_id,
    verification_status, 
    description, 
    skills_covered  -- JSON field containing skills covered by certification
FROM staff_certifications
WHERE staff_id = ?
ORDER BY issue_date DESC;

-- 5. SKILLS QUERY
-- Retrieves categorized skills with proficiency levels
SELECT 
    skill_category, 
    skill_name, 
    proficiency_level, 
    years_of_experience,
    is_certified, 
    last_used_date
FROM staff_skills
WHERE staff_id = ?
ORDER BY skill_category, proficiency_level DESC;

-- =====================================================
-- SAMPLE DATA STRUCTURE FOR REFERENCE
-- =====================================================

-- Educational Qualifications Sample:
-- {
--   "qualification_level": "graduation",
--   "qualification_name": "Bachelor of Technology",
--   "specialization": "Computer Science and Engineering",
--   "institution_name": "Punjab Technical University",
--   "university_board": "Punjab Technical University",
--   "percentage": 88.97,
--   "grade": "A",
--   "cgpa": 8.9,
--   "completion_year": 2011,
--   "subjects": {
--     "Data Structures": {"marks": 89, "total": 100},
--     "Algorithms": {"marks": 92, "total": 100},
--     "Database Management": {"marks": 88, "total": 100}
--   },
--   "achievements": "Dean's List for 3 semesters, Best Project Award 2011"
-- }

-- Professional Experience Sample:
-- {
--   "job_title": "Software Engineer",
--   "organization_name": "Accenture Pvt Ltd",
--   "is_current": false,
--   "key_responsibilities": [
--     "Developed Java-based web applications using Spring framework",
--     "Designed and implemented MySQL database schemas",
--     "Created responsive front-end interfaces using HTML, CSS, JavaScript"
--   ],
--   "achievements": [
--     "Successfully delivered 8 client projects on time",
--     "Reduced application load time by 40% through optimization"
--   ],
--   "skills_used": ["Java", "Spring Framework", "MySQL", "HTML", "CSS"]
-- }

-- Skills Sample:
-- {
--   "skill_category": "TECHNICAL",
--   "skill_name": "Java Programming",
--   "proficiency_level": "expert",
--   "years_of_experience": 12,
--   "is_certified": true
-- }

-- =====================================================
-- USAGE NOTES
-- =====================================================

-- 1. Replace ? with actual teacher user_id for main query
-- 2. Replace ? with actual staff_id for related table queries
-- 3. JSON fields (subjects, key_responsibilities, achievements, skills_used, skills_covered) 
--    need to be parsed in application code
-- 4. Date fields are returned as MySQL DATE/DATETIME objects
-- 5. Boolean fields (is_current, is_certified, is_lifetime) are returned as 0/1
-- 6. All queries use LEFT JOIN or WHERE conditions to handle missing data gracefully

-- =====================================================
-- EXAMPLE USAGE IN APPLICATION
-- =====================================================

-- Step 1: Get teacher basic data
-- const [teacherData] = await db.query(mainQuery, [teacherId]);

-- Step 2: Get educational timeline
-- const [educationData] = await db.query(educationQuery, [staffId]);

-- Step 3: Get experience timeline  
-- const [experienceData] = await db.query(experienceQuery, [staffId]);

-- Step 4: Get certifications
-- const [certificationsData] = await db.query(certificationsQuery, [staffId]);

-- Step 5: Get skills
-- const [skillsData] = await db.query(skillsQuery, [staffId]);

-- Step 6: Process and combine all data for frontend display
