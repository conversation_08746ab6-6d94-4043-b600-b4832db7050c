-- =====================================================
-- EXPERIENCE CALCULATION QUERIES BY MONTHS
-- =====================================================

-- 1. BASIC EXPERIENCE CALCULATION FOR A SPECIFIC TEACHER
-- Calculates total experience in months for staff_id = 48
SELECT 
    staff_id,
    job_title,
    organization_name,
    start_date,
    end_date,
    is_current,
    total_duration_months,
    -- Calculate months if total_duration_months is NULL
    CASE 
        WHEN total_duration_months IS NOT NULL THEN total_duration_months
        WHEN end_date IS NOT NULL THEN 
            TIMESTAMPDIFF(MONTH, start_date, end_date)
        ELSE 
            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
    END as calculated_months,
    -- Convert to years with decimal
    ROUND(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months / 12
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date) / 12
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE()) / 12
        END, 1
    ) as experience_years
FROM staff_professional_experience 
WHERE staff_id = 48
ORDER BY start_date ASC;

-- =====================================================
-- 2. TOTAL EXPERIENCE SUMMARY FOR A TEACHER
-- =====================================================
SELECT 
    staff_id,
    COUNT(*) as total_positions,
    -- Total months across all positions
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    -- Total years (rounded to 1 decimal)
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48;

-- =====================================================
-- 3. EXPERIENCE BY CATEGORY (Teaching vs Administrative)
-- =====================================================
SELECT 
    staff_id,
    job_category,
    COUNT(*) as positions_count,
    -- Total months by category
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    -- Total years by category
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48
GROUP BY staff_id, job_category
ORDER BY job_category;

-- =====================================================
-- 4. DETAILED EXPERIENCE BREAKDOWN WITH SMART CATEGORIZATION
-- =====================================================
SELECT 
    staff_id,
    job_title,
    organization_name,
    organization_type,
    job_category,
    start_date,
    end_date,
    is_current,
    -- Calculate months
    CASE 
        WHEN total_duration_months IS NOT NULL THEN total_duration_months
        WHEN end_date IS NOT NULL THEN 
            TIMESTAMPDIFF(MONTH, start_date, end_date)
        ELSE 
            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
    END as duration_months,
    -- Smart categorization based on job title and organization
    CASE 
        WHEN job_category = 'teaching' OR 
             LOWER(job_title) LIKE '%teacher%' OR 
             LOWER(job_title) LIKE '%professor%' OR 
             LOWER(job_title) LIKE '%instructor%' OR 
             LOWER(job_title) LIKE '%lecturer%' OR
             organization_type IN ('school', 'college', 'university') 
        THEN 'Teaching'
        WHEN job_category = 'administrative' OR 
             LOWER(job_title) LIKE '%admin%' OR 
             LOWER(job_title) LIKE '%principal%' OR 
             LOWER(job_title) LIKE '%head%' OR 
             LOWER(job_title) LIKE '%manager%' OR 
             LOWER(job_title) LIKE '%director%' OR 
             LOWER(job_title) LIKE '%coordinator%'
        THEN 'Administrative'
        ELSE 'Other'
    END as experience_type,
    -- Duration display
    CASE 
        WHEN end_date IS NULL THEN CONCAT(DATE_FORMAT(start_date, '%b %Y'), ' - Present')
        ELSE CONCAT(DATE_FORMAT(start_date, '%b %Y'), ' - ', DATE_FORMAT(end_date, '%b %Y'))
    END as duration_display
FROM staff_professional_experience 
WHERE staff_id = 48
ORDER BY start_date ASC;

-- =====================================================
-- 5. EXPERIENCE SUMMARY BY TYPE (Teaching/Administrative/Other)
-- =====================================================
SELECT 
    staff_id,
    CASE 
        WHEN job_category = 'teaching' OR 
             LOWER(job_title) LIKE '%teacher%' OR 
             LOWER(job_title) LIKE '%professor%' OR 
             LOWER(job_title) LIKE '%instructor%' OR 
             LOWER(job_title) LIKE '%lecturer%' OR
             organization_type IN ('school', 'college', 'university') 
        THEN 'Teaching'
        WHEN job_category = 'administrative' OR 
             LOWER(job_title) LIKE '%admin%' OR 
             LOWER(job_title) LIKE '%principal%' OR 
             LOWER(job_title) LIKE '%head%' OR 
             LOWER(job_title) LIKE '%manager%' OR 
             LOWER(job_title) LIKE '%director%' OR 
             LOWER(job_title) LIKE '%coordinator%'
        THEN 'Administrative'
        ELSE 'Other'
    END as experience_type,
    COUNT(*) as positions_count,
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48
GROUP BY staff_id, experience_type
ORDER BY experience_type;

-- =====================================================
-- 6. CURRENT VS PREVIOUS EXPERIENCE
-- =====================================================
SELECT 
    staff_id,
    CASE 
        WHEN is_current = 1 THEN 'Current Position'
        ELSE 'Previous Experience'
    END as position_status,
    COUNT(*) as positions_count,
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48
GROUP BY staff_id, is_current
ORDER BY is_current DESC;

-- =====================================================
-- 7. COMPLETE EXPERIENCE SUMMARY FOR API RESPONSE
-- =====================================================
SELECT 
    staff_id,
    -- Total experience
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years,
    
    -- Teaching experience
    SUM(
        CASE 
            WHEN (job_category = 'teaching' OR 
                  LOWER(job_title) LIKE '%teacher%' OR 
                  LOWER(job_title) LIKE '%professor%' OR 
                  LOWER(job_title) LIKE '%instructor%' OR 
                  LOWER(job_title) LIKE '%lecturer%' OR
                  organization_type IN ('school', 'college', 'university'))
            THEN 
                CASE 
                    WHEN total_duration_months IS NOT NULL THEN total_duration_months
                    WHEN end_date IS NOT NULL THEN 
                        TIMESTAMPDIFF(MONTH, start_date, end_date)
                    ELSE 
                        TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                END
            ELSE 0
        END
    ) as teaching_months,
    ROUND(
        SUM(
            CASE 
                WHEN (job_category = 'teaching' OR 
                      LOWER(job_title) LIKE '%teacher%' OR 
                      LOWER(job_title) LIKE '%professor%' OR 
                      LOWER(job_title) LIKE '%instructor%' OR 
                      LOWER(job_title) LIKE '%lecturer%' OR
                      organization_type IN ('school', 'college', 'university'))
                THEN 
                    CASE 
                        WHEN total_duration_months IS NOT NULL THEN total_duration_months
                        WHEN end_date IS NOT NULL THEN 
                            TIMESTAMPDIFF(MONTH, start_date, end_date)
                        ELSE 
                            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                    END
                ELSE 0
            END
        ) / 12, 1
    ) as teaching_years,
    
    -- Administrative experience
    SUM(
        CASE 
            WHEN (job_category = 'administrative' OR 
                  LOWER(job_title) LIKE '%admin%' OR 
                  LOWER(job_title) LIKE '%principal%' OR 
                  LOWER(job_title) LIKE '%head%' OR 
                  LOWER(job_title) LIKE '%manager%' OR 
                  LOWER(job_title) LIKE '%director%' OR 
                  LOWER(job_title) LIKE '%coordinator%')
            THEN 
                CASE 
                    WHEN total_duration_months IS NOT NULL THEN total_duration_months
                    WHEN end_date IS NOT NULL THEN 
                        TIMESTAMPDIFF(MONTH, start_date, end_date)
                    ELSE 
                        TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                END
            ELSE 0
        END
    ) as administrative_months,
    ROUND(
        SUM(
            CASE 
                WHEN (job_category = 'administrative' OR 
                      LOWER(job_title) LIKE '%admin%' OR 
                      LOWER(job_title) LIKE '%principal%' OR 
                      LOWER(job_title) LIKE '%head%' OR 
                      LOWER(job_title) LIKE '%manager%' OR 
                      LOWER(job_title) LIKE '%director%' OR 
                      LOWER(job_title) LIKE '%coordinator%')
                THEN 
                    CASE 
                        WHEN total_duration_months IS NOT NULL THEN total_duration_months
                        WHEN end_date IS NOT NULL THEN 
                            TIMESTAMPDIFF(MONTH, start_date, end_date)
                        ELSE 
                            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                    END
                ELSE 0
            END
        ) / 12, 1
    ) as administrative_years
FROM staff_professional_experience
WHERE staff_id = 48;

-- =====================================================
-- 8. SIMPLE QUERY FOR API INTEGRATION
-- =====================================================
-- This is the optimized query for use in the API
SELECT
    staff_id,
    -- Individual position details with calculated months
    job_title,
    organization_name,
    start_date,
    end_date,
    is_current,
    CASE
        WHEN total_duration_months IS NOT NULL THEN total_duration_months
        WHEN end_date IS NOT NULL THEN
            TIMESTAMPDIFF(MONTH, start_date, end_date)
        ELSE
            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
    END as duration_months,
    ROUND(
        CASE
            WHEN total_duration_months IS NOT NULL THEN total_duration_months / 12
            WHEN end_date IS NOT NULL THEN
                TIMESTAMPDIFF(MONTH, start_date, end_date) / 12
            ELSE
                TIMESTAMPDIFF(MONTH, start_date, CURDATE()) / 12
        END, 1
    ) as duration_years,
    -- Experience categorization
    CASE
        WHEN job_category = 'teaching' OR
             LOWER(job_title) LIKE '%teacher%' OR
             LOWER(job_title) LIKE '%professor%' OR
             organization_type IN ('school', 'college', 'university')
        THEN 'teaching'
        WHEN job_category = 'administrative' OR
             LOWER(job_title) LIKE '%admin%' OR
             LOWER(job_title) LIKE '%principal%' OR
             LOWER(job_title) LIKE '%manager%'
        THEN 'administrative'
        ELSE 'other'
    END as experience_category
FROM staff_professional_experience
WHERE staff_id = ?
ORDER BY start_date ASC;

-- =====================================================
-- 9. UPDATE TOTAL_DURATION_MONTHS FOR EXISTING RECORDS
-- =====================================================
-- Use this to populate the total_duration_months field if it's NULL
UPDATE staff_professional_experience
SET total_duration_months = CASE
    WHEN end_date IS NOT NULL THEN
        TIMESTAMPDIFF(MONTH, start_date, end_date)
    ELSE
        TIMESTAMPDIFF(MONTH, start_date, CURDATE())
END
WHERE total_duration_months IS NULL;

-- =====================================================
-- 10. EXPERIENCE CALCULATION FOR ALL TEACHERS
-- =====================================================
SELECT
    u.id as user_id,
    u.name as teacher_name,
    u.email,
    s.id as staff_id,
    COUNT(spe.id) as total_positions,

    -- Total experience calculation
    COALESCE(SUM(
        CASE
            WHEN spe.total_duration_months IS NOT NULL THEN spe.total_duration_months
            WHEN spe.end_date IS NOT NULL THEN
                TIMESTAMPDIFF(MONTH, spe.start_date, spe.end_date)
            ELSE
                TIMESTAMPDIFF(MONTH, spe.start_date, CURDATE())
        END
    ), 0) as total_months,

    ROUND(COALESCE(SUM(
        CASE
            WHEN spe.total_duration_months IS NOT NULL THEN spe.total_duration_months
            WHEN spe.end_date IS NOT NULL THEN
                TIMESTAMPDIFF(MONTH, spe.start_date, spe.end_date)
            ELSE
                TIMESTAMPDIFF(MONTH, spe.start_date, CURDATE())
        END
    ), 0) / 12, 1) as total_years,

    -- Teaching experience
    ROUND(COALESCE(SUM(
        CASE
            WHEN (spe.job_category = 'teaching' OR
                  LOWER(spe.job_title) LIKE '%teacher%' OR
                  LOWER(spe.job_title) LIKE '%professor%' OR
                  spe.organization_type IN ('school', 'college', 'university'))
            THEN
                CASE
                    WHEN spe.total_duration_months IS NOT NULL THEN spe.total_duration_months
                    WHEN spe.end_date IS NOT NULL THEN
                        TIMESTAMPDIFF(MONTH, spe.start_date, spe.end_date)
                    ELSE
                        TIMESTAMPDIFF(MONTH, spe.start_date, CURDATE())
                END
            ELSE 0
        END
    ), 0) / 12, 1) as teaching_years,

    -- Administrative experience
    ROUND(COALESCE(SUM(
        CASE
            WHEN (spe.job_category = 'administrative' OR
                  LOWER(spe.job_title) LIKE '%admin%' OR
                  LOWER(spe.job_title) LIKE '%principal%' OR
                  LOWER(spe.job_title) LIKE '%manager%')
            THEN
                CASE
                    WHEN spe.total_duration_months IS NOT NULL THEN spe.total_duration_months
                    WHEN spe.end_date IS NOT NULL THEN
                        TIMESTAMPDIFF(MONTH, spe.start_date, spe.end_date)
                    ELSE
                        TIMESTAMPDIFF(MONTH, spe.start_date, CURDATE())
                END
            ELSE 0
        END
    ), 0) / 12, 1) as administrative_years

FROM users u
LEFT JOIN staff s ON u.id = s.user_id
LEFT JOIN staff_professional_experience spe ON s.id = spe.staff_id
WHERE u.role = 'teacher'
GROUP BY u.id, u.name, u.email, s.id
ORDER BY total_years DESC;
