-- =====================================================
-- FINAL COMBINED ENHANCED TEACHER QUERY FOR ID 103
-- Complete teacher profile data across all tables
-- =====================================================

-- MAIN COMBINED QUERY - Gets all teacher data in one comprehensive result
SELECT 
    -- User Table Data (Personal & Account Information)
    u.id as user_id,
    u.username,
    u.name,
    u.full_name,
    u.email,
    u.role,
    u.profile_image,
    u.subjects as user_subjects,
    u.bio,
    u.date_of_birth,
    u.created_at,
    u.last_login,
    u.is_active,
    u.institution,
    u.grade,
    u.field_of_study,
    u.preferred_subjects,
    u.target_exams,
    
    -- Staff Table Data (Professional Information)
    s.id as staff_id,
    s.employee_id,
    s.designation,
    s.department,
    s.current_school,
    s.joining_date,
    s.employment_type,
    s.phone,
    s.alternate_phone,
    s.emergency_contact,
    s.address,
    s.city,
    s.state,
    s.pincode,
    s.gender,                    -- ✅ ISSUE 1: Gender column added
    s.current_salary,
    s.probation_period_months,
    s.confirmation_date,
    s.last_promotion_date,
    s.performance_rating,
    s.is_on_leave,
    s.office_location,
    s.subjects_taught,
    s.classes_handled,
    s.total_experience_years,
    s.teaching_experience_years,
    s.administrative_experience_years,
    s.awards_received,
    s.publications,
    s.research_papers,
    s.conferences_attended,
    s.training_programs,
    s.notes as staff_notes,
    s.is_active as staff_active,
    
    -- Calculated Fields for Enhanced Display
    CASE 
        WHEN u.date_of_birth IS NOT NULL THEN 
            FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25)
        ELSE NULL 
    END as calculated_age,
    
    CASE 
        WHEN u.last_login IS NOT NULL THEN 
            DATE_FORMAT(u.last_login, '%M %d, %Y at %h:%i %p')
        ELSE 'Never logged in' 
    END as formatted_last_login,
    
    CASE 
        WHEN u.created_at IS NOT NULL THEN 
            DATE_FORMAT(u.created_at, '%M %d, %Y')
        ELSE 'Unknown' 
    END as formatted_account_created,
    
    CASE 
        WHEN u.is_active = 1 THEN 'Active'
        ELSE 'Inactive' 
    END as account_status,
    
    COALESCE(u.full_name, u.name, u.username) as display_name,
    
    -- Summary Statistics
    (SELECT COUNT(*) FROM staff_educational_qualifications WHERE staff_id = s.id) as total_qualifications,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id) as total_experience,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id AND is_current = 0) as previous_experience_count,
    (SELECT COUNT(*) FROM staff_professional_experience WHERE staff_id = s.id AND is_current = 1) as current_position_count,
    (SELECT COUNT(*) FROM staff_certifications WHERE staff_id = s.id) as total_certifications,
    (SELECT COUNT(*) FROM staff_skills WHERE staff_id = s.id) as total_skills,
    (SELECT COUNT(DISTINCT skill_category) FROM staff_skills WHERE staff_id = s.id) as skill_categories_count
    
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
WHERE u.id = 103 AND u.role = 'teacher';

-- =====================================================
-- RELATED DATA QUERIES (Execute separately)
-- =====================================================

-- Educational Qualifications (✅ ISSUE 2: Educational timeline populated)
SELECT 
    qualification_level,
    qualification_name,
    specialization,
    institution_name,
    university_board,
    total_marks_obtained,
    total_marks_maximum,
    percentage,
    grade,
    cgpa,
    completion_year,
    subjects,        -- JSON: {"Mathematics": {"marks": 92, "total": 100}, ...}
    achievements,
    thesis_title
FROM staff_educational_qualifications 
WHERE staff_id = 48  -- CS Teacher's staff_id
ORDER BY completion_year ASC;

-- Professional Experience (✅ ISSUE 3: Previous experience categorized)
SELECT 
    job_title,
    organization_name,
    organization_type,
    start_date,
    end_date,
    is_current,      -- 0 = PREVIOUS, 1 = CURRENT
    total_duration_months,
    job_description,
    key_responsibilities,  -- JSON: ["Responsibility 1", "Responsibility 2", ...]
    achievements,         -- JSON: ["Achievement 1", "Achievement 2", ...]
    skills_used,         -- JSON: ["Skill 1", "Skill 2", ...]
    salary_range,
    performance_rating
FROM staff_professional_experience 
WHERE staff_id = 48
ORDER BY start_date ASC;

-- Certifications
SELECT 
    certification_name,
    certification_type,
    issuing_organization,
    issue_date,
    expiry_date,
    is_lifetime,
    certificate_id,
    verification_status,
    description,
    skills_covered    -- JSON: ["Skill 1", "Skill 2", ...]
FROM staff_certifications 
WHERE staff_id = 48
ORDER BY issue_date DESC;

-- Skills by Category
SELECT 
    skill_category,
    skill_name,
    proficiency_level,
    years_of_experience,
    is_certified,
    last_used_date
FROM staff_skills 
WHERE staff_id = 48
ORDER BY skill_category, proficiency_level DESC;

-- =====================================================
-- SAMPLE RESULTS FOR CS TEACHER (ID: 103)
-- =====================================================

/*
MAIN TEACHER DATA:
- User ID: 103
- Staff ID: 48
- Display Name: CS Teacher
- Email: <EMAIL>
- Gender: male ✅
- Age: 36 years
- Bio: Computer Science teacher with expertise in programming and data structures
- Last Login: May 12, 2025 at 07:10 AM
- Account Status: Active

EDUCATIONAL QUALIFICATIONS (3 records): ✅
1. Secondary School Certificate (2007) - 88.40%
   - 5 subjects with individual marks
   - Achievements: School topper in Mathematics
2. Higher Secondary Certificate (2009) - 91.80%
   - Science (Non-Medical) stream
   - Achievements: District rank 15
3. Bachelor of Technology (2011) - 88.97%
   - Computer Science and Engineering
   - 8 technical subjects, Best Project Award

PROFESSIONAL EXPERIENCE (3 records): ✅
1. Software Engineer (2011-2013) - PREVIOUS ✅
   - Accenture Pvt Ltd
   - 5 responsibilities, 4 achievements
2. Physics Tutor (2013-2015) - PREVIOUS ✅
   - Career Makers
   - Excellent performance rating
3. Computer Science Teacher (2015-Present) - CURRENT
   - Government School
   - 6 responsibilities, 6 achievements

CERTIFICATIONS (2 records):
- Oracle Java SE 8 Programmer (Technical)
- B.Ed Bachelor of Education (Teaching)

SKILLS (16 records across 4 categories):
- Technical: Java, Python, C++, MySQL, Web Development
- Teaching: Assessment, Classroom Management, Curriculum
- Language: English, Hindi, Punjabi (all expert)
- Soft Skills: Communication, Problem Solving, Leadership

SUMMARY STATISTICS:
- Total Qualifications: 3
- Previous Experience: 2 positions ✅
- Current Position: 1 position
- Total Certifications: 2
- Total Skills: 16 across 4 categories
*/

-- =====================================================
-- USAGE NOTES
-- =====================================================

/*
1. Replace user ID (103) with desired teacher's user ID
2. Replace staff ID (48) in related queries with corresponding staff_id
3. JSON fields need to be parsed in application code
4. All three original issues have been resolved:
   ✅ Gender column exists and populated
   ✅ Educational timeline comprehensive with subject details
   ✅ Previous experience properly categorized and displayed
5. This query structure is used by the enhanced teacher profile API
6. Frontend modal displays all this data in organized sections
*/
