-- Syllabus table to store curriculum requirements
CREATE TABLE IF NOT EXISTS syllabus (
  id INT AUTO_INCREMENT PRIMARY KEY,
  subject_id INT NOT NULL,
  grade VARCHAR(10) NOT NULL,
  stream VARCHAR(50) NOT NULL,
  unit_name VARCHAR(255) NOT NULL,
  topic_name VARCHAR(255) NOT NULL,
  description TEXT,
  estimated_periods INT DEFAULT 1,
  sequence_order INT NOT NULL,
  is_practical BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (subject_id) REFERENCES subjects(id)
);

-- Instruction plan table to link syllabus with actual teaching plan
CREATE TABLE IF NOT EXISTS instruction_plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  subject_id INT NOT NULL,
  class_id INT,
  trade_id INT,
  section_id INT,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (class_id) REFERENCES classes(id),
  FOREIGN KEY (trade_id) REFERENCES trades(id),
  FOREIGN KEY (section_id) REFERENCES sections(id)
);

-- Instruction plan resources table
CREATE TABLE IF NOT EXISTS instruction_plan_resources (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  resource_name VARCHAR(255) NOT NULL,
  resource_type ENUM('file', 'link', 'text') NOT NULL,
  resource_path VARCHAR(255),
  resource_content TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
);

-- Instruction plan details table to store topic-specific plans
CREATE TABLE IF NOT EXISTS instruction_plan_details (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  syllabus_id INT NOT NULL,
  planned_start_date DATE,
  planned_end_date DATE,
  actual_start_date DATE,
  actual_end_date DATE,
  status ENUM('pending', 'in_progress', 'completed', 'delayed') DEFAULT 'pending',
  completion_percentage INT DEFAULT 0,
  teaching_methodology TEXT,
  resources_required TEXT,
  assessment_strategy TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
  FOREIGN KEY (syllabus_id) REFERENCES syllabus(id)
);

-- Lecture-instruction plan mapping
CREATE TABLE IF NOT EXISTS lecture_instruction_map (
  id INT AUTO_INCREMENT PRIMARY KEY,
  lecture_id INT NOT NULL,
  instruction_plan_detail_id INT NOT NULL,
  actual_content_covered TEXT,
  completion_status ENUM('not_started', 'partial', 'completed') DEFAULT 'not_started',
  teacher_notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (lecture_id) REFERENCES teacher_lectures(id) ON DELETE CASCADE,
  FOREIGN KEY (instruction_plan_detail_id) REFERENCES instruction_plan_details(id) ON DELETE CASCADE
);

-- Academic plan assessments
CREATE TABLE IF NOT EXISTS academic_plan_assessments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  assessment_type VARCHAR(100) NOT NULL,
  assessment_date DATE NOT NULL,
  topics_covered TEXT,
  status ENUM('planned', 'in_progress', 'completed', 'cancelled') DEFAULT 'planned',
  max_marks INT,
  passing_marks INT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
);

-- Remedial sessions
CREATE TABLE IF NOT EXISTS remedial_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  plan_id INT NOT NULL,
  session_date DATE NOT NULL,
  topic VARCHAR(255) NOT NULL,
  status ENUM('planned', 'completed', 'cancelled') DEFAULT 'planned',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE
);

-- Remedial session students
CREATE TABLE IF NOT EXISTS remedial_session_students (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id INT NOT NULL,
  student_id INT NOT NULL,
  attendance BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES remedial_sessions(id) ON DELETE CASCADE,
  FOREIGN KEY (student_id) REFERENCES users(id)
);
