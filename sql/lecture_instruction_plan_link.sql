-- Create instruction plans table first
CREATE TABLE IF NOT EXISTS instruction_plans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  subject_id INT,
  teacher_id INT,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20),
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_subject_name (name)
);

-- Add foreign key to instruction_plans after subjects table is created
ALTER TABLE instruction_plans
ADD CONSTRAINT fk_instruction_plan_subject
FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL;

-- Create table to link lectures with instruction plans
CREATE TABLE IF NOT EXISTS lecture_instruction_plan (
  id INT AUTO_INCREMENT PRIMARY KEY,
  lecture_id INT NOT NULL,
  instruction_plan_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (lecture_id) REFERENCES teacher_lectures(id) ON DELETE CASCADE,
  FOREIGN KEY (instruction_plan_id) REFERENCES instruction_plans(id) ON DELETE CASCADE,
  UNIQUE KEY unique_lecture_plan (lecture_id, instruction_plan_id)
);

-- Create table to track syllabus topics completed through lectures
CREATE TABLE IF NOT EXISTS lecture_syllabus_topic (
  id INT AUTO_INCREMENT PRIMARY KEY,
  lecture_id INT NOT NULL,
  topic_id INT NOT NULL,
  completion_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (lecture_id) REFERENCES teacher_lectures(id) ON DELETE CASCADE,
  FOREIGN KEY (topic_id) REFERENCES teacher_syllabus(id) ON DELETE CASCADE,
  UNIQUE KEY unique_lecture_topic (lecture_id, topic_id)
);
