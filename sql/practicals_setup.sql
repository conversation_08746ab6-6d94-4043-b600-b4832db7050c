-- Create practicals table if it doesn't exist
CREATE TABLE IF NOT EXISTS `practicals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `class_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `lab_id` int(11) DEFAULT NULL,
  `description` text,
  `prerequisites` text,
  `materials` text,
  `equipment` text,
  `status` enum('Upcoming','Completed','Cancelled') NOT NULL DEFAULT 'Upcoming',
  `teacher_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  KEY `subject_id` (`subject_id`),
  KEY `teacher_id` (`teacher_id`),
  KEY `lab_id` (`lab_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create practical_records table if it doesn't exist
CREATE TABLE IF NOT EXISTS `practical_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `practical_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `content` text,
  `attachment` varchar(255) DEFAULT NULL,
  `submission_date` datetime DEFAULT NULL,
  `status` enum('submitted','graded') NOT NULL DEFAULT 'submitted',
  `grade` varchar(10) DEFAULT NULL,
  `feedback` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `practical_student` (`practical_id`,`student_id`),
  KEY `practical_id` (`practical_id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create topics table if it doesn't exist
CREATE TABLE IF NOT EXISTS `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create practical_topics junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS `practical_topics` (
  `practical_id` int(11) NOT NULL,
  `topic_id` int(11) NOT NULL,
  PRIMARY KEY (`practical_id`,`topic_id`),
  KEY `topic_id` (`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create labs table if it doesn't exist
CREATE TABLE IF NOT EXISTS `labs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `capacity` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data into labs table
INSERT INTO `labs` (`name`, `capacity`, `is_active`) VALUES
('Computer Lab 1', 30, 1),
('Physics Lab', 25, 1),
('Chemistry Lab', 25, 1),
('Biology Lab', 20, 1),
('Electronics Lab', 15, 1);

-- Insert sample data into topics table
INSERT INTO `topics` (`name`, `is_active`) VALUES
('Electricity and Magnetism', 1),
('Chemical Reactions', 1),
('Cell Biology', 1),
('Programming Fundamentals', 1),
('Data Structures', 1),
('Digital Electronics', 1),
('Mechanics', 1),
('Organic Chemistry', 1);

-- Insert sample data into practicals table
-- Note: Make sure class_id, subject_id, and teacher_id exist in your database
INSERT INTO `practicals` (`date`, `start_time`, `end_time`, `class_id`, `subject_id`, `lab_id`, `description`, `prerequisites`, `materials`, `equipment`, `status`, `teacher_id`) VALUES
(DATE_ADD(CURDATE(), INTERVAL 7 DAY), '09:00:00', '11:00:00', 1, 1, 1, 'Introduction to Programming with Python', 'Basic computer knowledge', 'Python installation guide, sample code', 'Computers with Python installed', 'Upcoming', 1),
(DATE_ADD(CURDATE(), INTERVAL 14 DAY), '13:00:00', '15:00:00', 1, 2, 2, 'Measuring Acceleration Due to Gravity', 'Understanding of Newton\'s laws', 'Lab manual, graph paper', 'Pendulum, stopwatch, meter rule', 'Upcoming', 2),
(DATE_ADD(CURDATE(), INTERVAL -7 DAY), '10:00:00', '12:00:00', 2, 3, 3, 'Acid-Base Titration', 'Understanding of pH scale', 'Lab manual, safety guidelines', 'Burette, pipette, pH indicators, acids and bases', 'Completed', 3),
(DATE_ADD(CURDATE(), INTERVAL 10 DAY), '14:00:00', '16:00:00', 2, 4, 4, 'Cell Structure Observation', 'Basic knowledge of cell theory', 'Lab manual, drawing sheets', 'Microscopes, slides, staining materials', 'Upcoming', 4),
(DATE_ADD(CURDATE(), INTERVAL -14 DAY), '09:30:00', '11:30:00', 3, 5, 5, 'Logic Gate Implementation', 'Understanding of Boolean algebra', 'Circuit diagrams, truth tables', 'Breadboards, logic ICs, LEDs, resistors', 'Completed', 5);

-- Insert sample data into practical_topics junction table
INSERT INTO `practical_topics` (`practical_id`, `topic_id`) VALUES
(1, 4), -- Programming Fundamentals for Python practical
(1, 5), -- Data Structures for Python practical
(2, 7), -- Mechanics for Physics practical
(3, 8), -- Organic Chemistry for Chemistry practical
(4, 3), -- Cell Biology for Biology practical
(5, 6); -- Digital Electronics for Electronics practical

-- Insert sample data into practical_records table
-- Note: Make sure student_id exists in your database
INSERT INTO `practical_records` (`practical_id`, `student_id`, `content`, `submission_date`, `status`, `grade`, `feedback`) VALUES
(3, 1, 'I performed the acid-base titration experiment and observed the color change at pH 7. The calculated molarity of the unknown acid was 0.1M.', DATE_ADD(CURDATE(), INTERVAL -5 DAY), 'graded', 'A', 'Excellent work! Your calculations are accurate and your observations are detailed.'),
(3, 2, 'The titration experiment showed that the endpoint was reached after adding 25ml of the base. The unknown acid concentration was calculated to be 0.098M.', DATE_ADD(CURDATE(), INTERVAL -6 DAY), 'graded', 'B+', 'Good work, but your calculation has a small error. Please review the formula.'),
(5, 1, 'I built the logic circuits as instructed and verified all truth tables. The AND, OR, and NOT gates worked as expected.', DATE_ADD(CURDATE(), INTERVAL -12 DAY), 'graded', 'A-', 'Very good implementation. Your circuit was well-organized and functioned correctly.'),
(5, 3, 'I constructed the logic gates and tested each one. The NAND gate implementation had some issues initially but I fixed it by replacing the IC.', DATE_ADD(CURDATE(), INTERVAL -13 DAY), 'submitted', NULL, NULL);
