-- Insert sample subjects if they don't exist
INSERT IGNORE INTO subjects (name, code, description)
VALUES
('Physics', 'PHY', 'Physics for senior secondary'),
('Chemistry', 'CHE', 'Chemistry for senior secondary'),
('Mathematics', 'MAT', 'Mathematics for senior secondary'),
('Biology', 'BIO', 'Biology for senior secondary'),
('Computer Science', 'CS', 'Computer Science for senior secondary');

-- Insert sample instruction plans
INSERT INTO instruction_plans (title, description, subject_id, teacher_id, status)
VALUES
('Physics Mechanics Plan', 'Instruction plan for mechanics', 1, 11, 'published'),
('Physics Electromagnetism Plan', 'Instruction plan for electromagnetism', 1, 11, 'published'),
('Chemistry Organic Plan', 'Instruction plan for organic chemistry', 2, 12, 'published'),
('Mathematics Calculus Plan', 'Instruction plan for calculus', 3, 13, 'published'),
('Biology Cell Biology Plan', 'Instruction plan for cell biology', 4, 14, 'published');

-- Insert sample lecture-instruction plan links with specific lecture IDs
INSERT INTO lecture_instruction_plan (lecture_id, instruction_plan_id)
VALUES
(1, 6),  -- Physics lecture 1 with Physics Mechanics Plan
(6, 7),  -- Physics lecture 6 with Physics Electromagnetism Plan
(12, 6), -- Physics lecture 12 with Physics Mechanics Plan
(3, 8),  -- Chemistry lecture 3 with Chemistry Organic Plan
(10, 8), -- Chemistry lecture 10 with Chemistry Organic Plan
(15, 8); -- Chemistry lecture 15 with Chemistry Organic Plan

-- Update specific teacher_syllabus records to have 'completed' status
UPDATE teacher_syllabus
SET status = 'completed', completion_date = CURDATE()
WHERE id IN (1, 2, 3, 4, 5);

-- Insert sample lecture-syllabus topic links with specific IDs
INSERT INTO lecture_syllabus_topic (lecture_id, topic_id, completion_date)
VALUES
(1, 1, CURDATE()),  -- Physics lecture 1 with topic 1
(6, 2, CURDATE()),  -- Physics lecture 6 with topic 2
(12, 3, CURDATE()), -- Physics lecture 12 with topic 3
(3, 4, CURDATE()),  -- Chemistry lecture 3 with topic 4
(10, 5, CURDATE()); -- Chemistry lecture 10 with topic 5

-- Insert sample syllabus progress records
INSERT INTO syllabus_progress (teacher_id, subject_name, total_topics, completed_topics)
VALUES
(11, 'Physics', 10, 3),
(12, 'Chemistry', 12, 5),
(13, 'Mathematics', 15, 7),
(14, 'Biology', 8, 2);
