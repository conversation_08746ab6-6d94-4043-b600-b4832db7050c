-- Create table to track syllabus progress
CREATE TABLE IF NOT EXISTS syllabus_progress (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  total_topics INT NOT NULL DEFAULT 0,
  completed_topics INT NOT NULL DEFAULT 0,
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_teacher_subject (teacher_id, subject_name)
);

-- Add status and completion_date columns to teacher_syllabus table if they don't exist
ALTER TABLE teacher_syllabus 
ADD COLUMN IF NOT EXISTS status ENUM('pending', 'completed') NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS completion_date DATE NULL;

-- Add section letter column to teacher_lectures table if it doesn't exist
ALTER TABLE teacher_lectures
ADD COLUMN IF NOT EXISTS sectionLetter VARCHAR(10) NULL AFTER streamCode;
