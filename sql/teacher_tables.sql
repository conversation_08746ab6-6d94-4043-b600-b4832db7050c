-- Teacher module database tables

-- Table for teacher lectures
CREATE TABLE IF NOT EXISTS teacher_lectures (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  class_name VARCHAR(50) NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  topic VARCHAR(200) NOT NULL,
  status ENUM('pending', 'delivered', 'cancelled') DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for syllabus topics
CREATE TABLE IF NOT EXISTS teacher_syllabus (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  topic VARCHAR(200) NOT NULL,
  status ENUM('pending', 'completed') DEFAULT 'pending',
  completion_date DATE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for practical/lab sessions
CREATE TABLE IF NOT EXISTS teacher_practicals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  class_name VARCHAR(50) NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  practical_topic VARCHAR(200) NOT NULL,
  venue VARCHAR(100) NOT NULL,
  status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for student practical records
CREATE TABLE IF NOT EXISTS student_practical_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  teacher_id INT NOT NULL,
  practical_id INT,
  class_name VARCHAR(50) NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  practical_topic VARCHAR(200) NOT NULL,
  submission_date DATE,
  status ENUM('pending', 'submitted', 'graded') DEFAULT 'pending',
  grade VARCHAR(10),
  feedback TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (practical_id) REFERENCES teacher_practicals(id) ON DELETE SET NULL
);

-- Table for subject syllabus completion tracking
CREATE TABLE IF NOT EXISTS syllabus_progress (
  id INT AUTO_INCREMENT PRIMARY KEY,
  teacher_id INT NOT NULL,
  subject_name VARCHAR(100) NOT NULL,
  total_topics INT NOT NULL DEFAULT 0,
  completed_topics INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY `unique_teacher_subject` (teacher_id, subject_name)
);

-- Insert demo data for sample teacher (assuming user ID 1 is a teacher)
-- Replace with actual teacher user ID in your system

-- Demo lectures
INSERT INTO teacher_lectures (teacher_id, date, start_time, end_time, class_name, subject_name, topic, status, notes) VALUES
(1, CURDATE(), '09:00:00', '10:00:00', 'Class 10-A', 'Mathematics', 'Quadratic Equations', 'pending', 'Cover basic concepts and examples'),
(1, CURDATE(), '11:00:00', '12:00:00', 'Class 9-B', 'Science', 'Periodic Table', 'delivered', 'Discussed elements and their properties'),
(1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '10:00:00', 'Class 10-A', 'Mathematics', 'Solving Quadratic Equations', 'pending', 'Practice problems and applications');

-- Demo syllabus subjects with progress tracking
INSERT INTO syllabus_progress (teacher_id, subject_name, total_topics, completed_topics) VALUES
(1, 'Mathematics', 20, 12),
(1, 'Science', 18, 15),
(1, 'Computer Science', 15, 8);

-- Demo syllabus topics
INSERT INTO teacher_syllabus (teacher_id, subject_name, topic, status, completion_date) VALUES
(1, 'Mathematics', 'Number Systems', 'completed', DATE_SUB(CURDATE(), INTERVAL 30 DAY)),
(1, 'Mathematics', 'Algebra Basics', 'completed', DATE_SUB(CURDATE(), INTERVAL 25 DAY)),
(1, 'Mathematics', 'Linear Equations', 'completed', DATE_SUB(CURDATE(), INTERVAL 20 DAY)),
(1, 'Mathematics', 'Quadratic Equations', 'pending', NULL),
(1, 'Science', 'States of Matter', 'completed', DATE_SUB(CURDATE(), INTERVAL 15 DAY)),
(1, 'Science', 'Atomic Structure', 'completed', DATE_SUB(CURDATE(), INTERVAL 10 DAY)),
(1, 'Science', 'Periodic Table', 'completed', DATE_SUB(CURDATE(), INTERVAL 5 DAY)),
(1, 'Computer Science', 'HTML Basics', 'completed', DATE_SUB(CURDATE(), INTERVAL 12 DAY)),
(1, 'Computer Science', 'CSS Styling', 'completed', DATE_SUB(CURDATE(), INTERVAL 8 DAY)),
(1, 'Computer Science', 'JavaScript Fundamentals', 'pending', NULL);

-- Demo practical sessions
INSERT INTO teacher_practicals (teacher_id, date, start_time, end_time, class_name, subject_name, practical_topic, venue, status) VALUES
(1, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '10:00:00', '12:00:00', 'Class 10-A', 'Computer Science', 'HTML & CSS Basics', 'Computer Lab 1', 'pending'),
(1, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '13:00:00', '15:00:00', 'Class 9-B', 'Science', 'Chemical Reactions', 'Science Lab 2', 'pending');

-- Demo student practical records (assuming student IDs 2, 3, 4)
INSERT INTO student_practical_records (student_id, teacher_id, practical_id, class_name, subject_name, practical_topic, submission_date, status, grade, feedback) VALUES
(2, 1, 1, 'Class 10-A', 'Computer Science', 'HTML & CSS Basics', DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'graded', 'A', 'Excellent work on structuring the HTML. CSS could use some improvement.'),
(3, 1, 1, 'Class 10-A', 'Computer Science', 'HTML & CSS Basics', DATE_SUB(CURDATE(), INTERVAL 6 DAY), 'graded', 'B+', 'Good effort, but needs more attention to detail on responsive design.'),
(4, 1, 1, 'Class 10-A', 'Computer Science', 'HTML & CSS Basics', NULL, 'pending', NULL, NULL); 