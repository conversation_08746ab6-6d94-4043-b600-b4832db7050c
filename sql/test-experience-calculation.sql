-- =====================================================
-- TEST EXPERIENCE CALCULATION FOR CS TEACHER (staff_id = 48)
-- =====================================================

-- 1. Check current experience data
SELECT 'Current Experience Data' as query_type;
SELECT 
    staff_id,
    job_title,
    organization_name,
    start_date,
    end_date,
    is_current,
    total_duration_months,
    job_category,
    organization_type
FROM staff_professional_experience 
WHERE staff_id = 48
ORDER BY start_date ASC;

-- 2. Calculate experience with months
SELECT 'Experience Calculation with Months' as query_type;
SELECT 
    staff_id,
    job_title,
    organization_name,
    start_date,
    end_date,
    is_current,
    total_duration_months,
    -- Calculate months if total_duration_months is NULL
    CASE 
        WHEN total_duration_months IS NOT NULL THEN total_duration_months
        WHEN end_date IS NOT NULL THEN 
            TIMESTAMPDIFF(MONTH, start_date, end_date)
        ELSE 
            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
    END as calculated_months,
    -- Convert to years with decimal
    ROUND(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months / 12
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date) / 12
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE()) / 12
        END, 1
    ) as experience_years
FROM staff_professional_experience 
WHERE staff_id = 48
ORDER BY start_date ASC;

-- 3. Total experience summary
SELECT 'Total Experience Summary' as query_type;
SELECT 
    staff_id,
    COUNT(*) as total_positions,
    -- Total months across all positions
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    -- Total years (rounded to 1 decimal)
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48;

-- 4. Experience by category (Teaching vs Administrative)
SELECT 'Experience by Category' as query_type;
SELECT 
    staff_id,
    CASE 
        WHEN job_category = 'teaching' OR 
             LOWER(job_title) LIKE '%teacher%' OR 
             LOWER(job_title) LIKE '%professor%' OR 
             LOWER(job_title) LIKE '%instructor%' OR 
             LOWER(job_title) LIKE '%lecturer%' OR
             organization_type IN ('school', 'college', 'university') 
        THEN 'Teaching'
        WHEN job_category = 'administrative' OR 
             LOWER(job_title) LIKE '%admin%' OR 
             LOWER(job_title) LIKE '%principal%' OR 
             LOWER(job_title) LIKE '%head%' OR 
             LOWER(job_title) LIKE '%manager%' OR 
             LOWER(job_title) LIKE '%director%' OR 
             LOWER(job_title) LIKE '%coordinator%'
        THEN 'Administrative'
        ELSE 'Other'
    END as experience_type,
    COUNT(*) as positions_count,
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years
FROM staff_professional_experience 
WHERE staff_id = 48
GROUP BY staff_id, experience_type
ORDER BY experience_type;

-- 5. Complete experience summary for API response
SELECT 'Complete Experience Summary for API' as query_type;
SELECT 
    staff_id,
    -- Total experience
    SUM(
        CASE 
            WHEN total_duration_months IS NOT NULL THEN total_duration_months
            WHEN end_date IS NOT NULL THEN 
                TIMESTAMPDIFF(MONTH, start_date, end_date)
            ELSE 
                TIMESTAMPDIFF(MONTH, start_date, CURDATE())
        END
    ) as total_months,
    ROUND(
        SUM(
            CASE 
                WHEN total_duration_months IS NOT NULL THEN total_duration_months
                WHEN end_date IS NOT NULL THEN 
                    TIMESTAMPDIFF(MONTH, start_date, end_date)
                ELSE 
                    TIMESTAMPDIFF(MONTH, start_date, CURDATE())
            END
        ) / 12, 1
    ) as total_years,
    
    -- Teaching experience
    ROUND(
        SUM(
            CASE 
                WHEN (job_category = 'teaching' OR 
                      LOWER(job_title) LIKE '%teacher%' OR 
                      LOWER(job_title) LIKE '%professor%' OR 
                      LOWER(job_title) LIKE '%instructor%' OR 
                      LOWER(job_title) LIKE '%lecturer%' OR
                      organization_type IN ('school', 'college', 'university'))
                THEN 
                    CASE 
                        WHEN total_duration_months IS NOT NULL THEN total_duration_months
                        WHEN end_date IS NOT NULL THEN 
                            TIMESTAMPDIFF(MONTH, start_date, end_date)
                        ELSE 
                            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                    END
                ELSE 0
            END
        ) / 12, 1
    ) as teaching_years,
    
    -- Administrative experience
    ROUND(
        SUM(
            CASE 
                WHEN (job_category = 'administrative' OR 
                      LOWER(job_title) LIKE '%admin%' OR 
                      LOWER(job_title) LIKE '%principal%' OR 
                      LOWER(job_title) LIKE '%head%' OR 
                      LOWER(job_title) LIKE '%manager%' OR 
                      LOWER(job_title) LIKE '%director%' OR 
                      LOWER(job_title) LIKE '%coordinator%')
                THEN 
                    CASE 
                        WHEN total_duration_months IS NOT NULL THEN total_duration_months
                        WHEN end_date IS NOT NULL THEN 
                            TIMESTAMPDIFF(MONTH, start_date, end_date)
                        ELSE 
                            TIMESTAMPDIFF(MONTH, start_date, CURDATE())
                    END
                ELSE 0
            END
        ) / 12, 1
    ) as administrative_years
FROM staff_professional_experience 
WHERE staff_id = 48;

-- 6. Update total_duration_months if NULL (run this if needed)
-- UPDATE staff_professional_experience 
-- SET total_duration_months = CASE 
--     WHEN end_date IS NOT NULL THEN 
--         TIMESTAMPDIFF(MONTH, start_date, end_date)
--     ELSE 
--         TIMESTAMPDIFF(MONTH, start_date, CURDATE())
-- END
-- WHERE total_duration_months IS NULL AND staff_id = 48;
