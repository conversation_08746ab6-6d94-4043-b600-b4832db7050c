/**
 * Test script to verify the teacher profile API works with authentication
 */

const express = require('express');
const session = require('express-session');
const fetch = require('node-fetch');

const app = express();

// Session configuration
app.use(session({
    secret: 'test-secret',
    resave: false,
    saveUninitialized: true,
    cookie: { secure: false }
}));

// Test route to simulate login
app.get('/test-login', (req, res) => {
    // Simulate principal login
    req.session.userId = 105; // Dr. <PERSON> (principal)
    req.session.userRole = 'principal';
    req.session.save((err) => {
        if (err) {
            return res.status(500).json({ error: 'Session save failed' });
        }
        res.json({ 
            message: 'Logged in as principal',
            sessionId: req.sessionID,
            userId: req.session.userId,
            userRole: req.session.userRole
        });
    });
});

// Test route to call the API
app.get('/test-api', async (req, res) => {
    try {
        // Check if logged in
        if (!req.session.userId) {
            return res.status(401).json({ error: 'Not logged in' });
        }

        // Make API call to the teacher profile endpoint
        const response = await fetch('http://localhost:3018/principal/api/teacher/profile-enhanced?teacher_id=103', {
            headers: {
                'Cookie': `connect.sid=${req.sessionID}`
            }
        });

        const data = await response.json();
        res.json({
            status: response.status,
            data: data
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(3019, () => {
    console.log('Test server running on http://localhost:3019');
    console.log('Visit http://localhost:3019/test-login to login');
    console.log('Then visit http://localhost:3019/test-api to test the API');
});
