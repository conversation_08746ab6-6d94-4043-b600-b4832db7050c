/**
 * Test script for category-wise room-wise equipment count queries
 * 
 * This script demonstrates how to use the saved SQL queries for:
 * 1. Comprehensive category-wise room-wise equipment counts
 * 2. Category-wise summary across all rooms
 * 3. Room-wise equipment summary (simplified view)
 */

const db = require('./config/database');
const SQLQueries = require('./config/sql-queries');

async function testCategoryWiseQueries() {
    console.log('🔍 Testing Category-wise Room-wise Equipment Count Queries');
    console.log('================================================================\n');

    try {
        // 1. Test comprehensive category-wise room-wise query
        console.log('1. 📊 COMPREHENSIVE CATEGORY-WISE ROOM-WISE EQUIPMENT COUNT:');
        console.log('================================================================');
        
        const [comprehensiveResults] = await db.query(SQLQueries.infrastructure.getItemCountCategoryWiseRoomWise);
        
        console.log(`Found ${comprehensiveResults.length} rooms with equipment:\n`);
        
        // Display top 5 rooms with most equipment
        comprehensiveResults.slice(0, 5).forEach(room => {
            console.log(`🏠 ${room.room_number} (ID: ${room.room_id})`);
            console.log(`   Building: ${room.building || 'N/A'} | Floor: ${room.floor || 'N/A'} | Capacity: ${room.capacity || 'N/A'}`);
            
            // Technology Equipment Summary
            const techItems = [];
            if (room.projectors > 0) techItems.push(`📽️ ${room.projectors} Projectors`);
            if (room.it_projectors > 0) techItems.push(`📽️ ${room.it_projectors} IT Projectors`);
            if (room.desktops > 0) techItems.push(`🖥️ ${room.desktops} Desktops`);
            if (room.it_desktops > 0) techItems.push(`🖥️ ${room.it_desktops} IT Desktops`);
            if (room.laptops > 0) techItems.push(`💻 ${room.laptops} Laptops`);
            if (room.it_laptops > 0) techItems.push(`💻 ${room.it_laptops} IT Laptops`);
            if (room.printers > 0) techItems.push(`🖨️ ${room.printers} Printers`);
            if (room.it_printers > 0) techItems.push(`🖨️ ${room.it_printers} IT Printers`);
            if (room.interactive_panels > 0) techItems.push(`📺 ${room.interactive_panels} Interactive Panels`);
            if (room.cameras > 0) techItems.push(`📷 ${room.cameras} Cameras`);
            if (room.microphones > 0) techItems.push(`🎤 ${room.microphones} Microphones`);
            if (room.ups_units > 0) techItems.push(`🔋 ${room.ups_units} UPS Units`);
            if (room.routers > 0) techItems.push(`📡 ${room.routers} Routers`);
            if (room.it_network > 0) techItems.push(`📡 ${room.it_network} Network Equipment`);
            
            if (techItems.length > 0) {
                console.log(`   📱 Technology: ${techItems.join(', ')}`);
            }
            
            // Electrical Equipment Summary
            const electricalItems = [];
            if (room.tube_lights > 0) electricalItems.push(`💡 ${room.tube_lights} Tube Lights`);
            if (room.ceiling_fans > 0) electricalItems.push(`🌀 ${room.ceiling_fans} Ceiling Fans`);
            if (room.power_outlets > 0) electricalItems.push(`🔌 ${room.power_outlets} Power Outlets`);
            if (room.switches > 0) electricalItems.push(`🔘 ${room.switches} Switches`);
            if (room.electrical_other > 0) electricalItems.push(`⚡ ${room.electrical_other} Other Electrical`);
            
            if (electricalItems.length > 0) {
                console.log(`   ⚡ Electrical: ${electricalItems.join(', ')}`);
            }
            
            console.log(`   📊 Totals: ${room.total_inventory_items} inventory + ${room.total_it_items} IT + ${room.total_electrical_items} electrical = ${room.grand_total_equipment} total\n`);
        });

        // 2. Test room-wise equipment summary (simplified)
        console.log('\n2. 🏠 ROOM-WISE EQUIPMENT SUMMARY (SIMPLIFIED):');
        console.log('================================================================');
        
        const [roomSummary] = await db.query(SQLQueries.infrastructure.getRoomWiseEquipmentSummary);
        
        // Group by room type
        const roomTypes = {};
        roomSummary.forEach(room => {
            if (!roomTypes[room.room_type]) {
                roomTypes[room.room_type] = [];
            }
            roomTypes[room.room_type].push(room);
        });
        
        Object.entries(roomTypes).forEach(([type, rooms]) => {
            console.log(`\n📋 ${type.toUpperCase()}S:`);
            rooms.slice(0, 3).forEach(room => {
                console.log(`   • ${room.room_number}: ${room.total_equipment} items (${room.inventory_count} inv + ${room.it_count} IT + ${room.electrical_count} elec)`);
            });
            if (rooms.length > 3) {
                console.log(`   ... and ${rooms.length - 3} more ${type.toLowerCase()}s`);
            }
        });

        // 3. Calculate overall statistics
        console.log('\n3. 📈 OVERALL STATISTICS:');
        console.log('================================================================');
        
        const totalRooms = comprehensiveResults.length;
        const totalInventoryItems = comprehensiveResults.reduce((sum, room) => sum + room.total_inventory_items, 0);
        const totalITItems = comprehensiveResults.reduce((sum, room) => sum + room.total_it_items, 0);
        const totalElectricalItems = comprehensiveResults.reduce((sum, room) => sum + room.total_electrical_items, 0);
        const grandTotal = comprehensiveResults.reduce((sum, room) => sum + room.grand_total_equipment, 0);
        
        // Technology category totals
        const totalProjectors = comprehensiveResults.reduce((sum, room) => sum + room.projectors + room.it_projectors, 0);
        const totalDesktops = comprehensiveResults.reduce((sum, room) => sum + room.desktops + room.it_desktops, 0);
        const totalLaptops = comprehensiveResults.reduce((sum, room) => sum + room.laptops + room.it_laptops, 0);
        const totalPrinters = comprehensiveResults.reduce((sum, room) => sum + room.printers + room.it_printers, 0);
        const totalUPS = comprehensiveResults.reduce((sum, room) => sum + room.ups_units, 0);
        const totalInteractivePanels = comprehensiveResults.reduce((sum, room) => sum + room.interactive_panels, 0);
        const totalCameras = comprehensiveResults.reduce((sum, room) => sum + room.cameras, 0);
        
        // Electrical category totals
        const totalTubeLights = comprehensiveResults.reduce((sum, room) => sum + room.tube_lights, 0);
        const totalCeilingFans = comprehensiveResults.reduce((sum, room) => sum + room.ceiling_fans, 0);
        const totalPowerOutlets = comprehensiveResults.reduce((sum, room) => sum + room.power_outlets, 0);
        const totalSwitches = comprehensiveResults.reduce((sum, room) => sum + room.switches, 0);
        
        console.log(`📊 Room Statistics:`);
        console.log(`   • Total Rooms with Equipment: ${totalRooms}`);
        console.log(`   • Total Equipment Items: ${grandTotal}`);
        console.log(`     - Inventory Items: ${totalInventoryItems}`);
        console.log(`     - IT Items: ${totalITItems}`);
        console.log(`     - Electrical Items: ${totalElectricalItems}`);
        
        console.log(`\n📱 Technology Equipment:`);
        console.log(`   • Projectors: ${totalProjectors}`);
        console.log(`   • Desktop Computers: ${totalDesktops}`);
        console.log(`   • Laptops: ${totalLaptops}`);
        console.log(`   • Printers: ${totalPrinters}`);
        console.log(`   • UPS Units: ${totalUPS}`);
        console.log(`   • Interactive Panels: ${totalInteractivePanels}`);
        console.log(`   • Cameras: ${totalCameras}`);
        
        console.log(`\n⚡ Electrical Equipment:`);
        console.log(`   • Tube Lights: ${totalTubeLights}`);
        console.log(`   • Ceiling Fans: ${totalCeilingFans}`);
        console.log(`   • Power Outlets: ${totalPowerOutlets}`);
        console.log(`   • Switches: ${totalSwitches}`);

        console.log('\n✅ ALL QUERIES EXECUTED SUCCESSFULLY!');
        console.log('\n🎯 USAGE EXAMPLES:');
        console.log('================================================================');
        console.log('// Use in your application:');
        console.log('const SQLQueries = require("./config/sql-queries");');
        console.log('');
        console.log('// Get comprehensive category-wise room-wise data:');
        console.log('const [results] = await db.query(SQLQueries.infrastructure.getItemCountCategoryWiseRoomWise);');
        console.log('');
        console.log('// Get simplified room-wise summary:');
        console.log('const [summary] = await db.query(SQLQueries.infrastructure.getRoomWiseEquipmentSummary);');
        console.log('');
        console.log('// Get category-wise totals across all rooms:');
        console.log('const [categories] = await db.query(SQLQueries.infrastructure.getCategoryWiseSummaryAllRooms);');

        process.exit(0);

    } catch (error) {
        console.error('❌ Error executing queries:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testCategoryWiseQueries()
        .then(() => {
            console.log('\n🎉 Test completed successfully!');
        })
        .catch((error) => {
            console.error('❌ Test failed:', error);
        });
}

module.exports = { testCategoryWiseQueries };
