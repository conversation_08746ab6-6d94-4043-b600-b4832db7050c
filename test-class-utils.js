const classUtils = require('./utils/class-utils');

async function testClassUtils() {
  try {
    console.log('Testing getAllClassesWithSections...');
    const classesWithSections = await classUtils.getAllClassesWithSections();
    console.log('Classes with sections:', JSON.stringify(classesWithSections, null, 2));
    
    console.log('\nTesting getAllClassSections...');
    const allSections = await classUtils.getAllClassSections();
    console.log('All sections:', allSections);
    
    if (allSections.length > 0) {
      console.log('\nTesting getClassSectionById...');
      const sectionId = allSections[0].id;
      const section = await classUtils.getClassSectionById(sectionId);
      console.log(`Section with ID ${sectionId}:`, section);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing class utils:', error);
    process.exit(1);
  }
}

testClassUtils();
