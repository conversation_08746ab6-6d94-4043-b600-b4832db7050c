/**
 * <PERSON><PERSON><PERSON> to test the computer teacher view
 * This script simulates a request to the teacher classes page for a computer teacher
 */

const db = require('./config/database');
const teacherController = require('./controllers/teacher-controller');

async function testComputerTeacherView() {
  try {
    console.log('Testing computer teacher view...');
    
    // Find a computer teacher
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_specialization ts ON u.id = ts.teacher_id
      WHERE u.role = 'teacher' 
      AND ts.specialization = 'Computer Science'
      LIMIT 1
    `);
    
    if (computerTeachers.length === 0) {
      console.log('No computer teachers found');
      return;
    }
    
    const teacherId = computerTeachers[0].id;
    console.log(`Testing view for computer teacher: ${computerTeachers[0].full_name} (ID: ${teacherId})`);
    
    // Create mock request and response objects
    const req = {
      session: {
        userId: teacherId,
        userRole: 'teacher',
        user: {
          id: teacherId,
          role: 'teacher'
        }
      },
      isComputerTeacher: true
    };
    
    const res = {
      render: (view, data) => {
        console.log(`\nRendering view: ${view}`);
        console.log('Data passed to view:');
        console.log('- isComputerTeacher:', data.isComputerTeacher);
        console.log('- Number of classes:', data.classes.length);
        
        if (data.classes.length > 0) {
          const firstClass = data.classes[0];
          console.log('\nFirst class details:');
          console.log('- Class name:', firstClass.class_name);
          console.log('- Primary subjects:', firstClass.primary_subjects ? firstClass.primary_subjects.length : 0);
          console.log('- Secondary subjects:', firstClass.secondary_subjects ? firstClass.secondary_subjects.length : 0);
          
          if (firstClass.primary_subjects && firstClass.primary_subjects.length > 0) {
            console.log('\nPrimary subjects:');
            firstClass.primary_subjects.forEach(subject => {
              console.log(`- ${subject.subject_name} (${subject.subject_code})`);
            });
          }
          
          if (firstClass.secondary_subjects && firstClass.secondary_subjects.length > 0) {
            console.log('\nSecondary subjects:');
            firstClass.secondary_subjects.forEach(subject => {
              console.log(`- ${subject.subject_name} (${subject.subject_code})`);
            });
          }
        }
      },
      redirect: (url) => {
        console.log(`Redirecting to: ${url}`);
      },
      status: (code) => {
        console.log(`Status code: ${code}`);
        return {
          render: (view, data) => {
            console.log(`Rendering error view: ${view}`);
            console.log('Error message:', data.message);
          }
        };
      }
    };
    
    // Call the controller method
    await teacherController.getClasses(req, res);
    
    console.log('\nTest completed');
  } catch (error) {
    console.error('Error testing computer teacher view:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testComputerTeacherView();
