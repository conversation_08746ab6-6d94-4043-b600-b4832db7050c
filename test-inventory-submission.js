const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function login() {
    try {
        const response = await fetch('http://localhost:3018/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123'
            })
        });

        // Log status and headers
        console.log('Login response status:', response.status);
        console.log('Login response headers:', response.headers.raw());
        const text = await response.text();
        console.log('Login response body:', text);

        if (!response.ok) {
            throw new Error(`Login failed: ${response.status}`);
        }

        // Get the session cookie
        const cookies = response.headers.get('set-cookie');
        if (!cookies) {
            throw new Error('No session cookie received');
        }

        return cookies;
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
}

async function testInventorySubmission() {
    try {
        // First login to get session cookie
        const cookies = await login();
        console.log('Login successful, got cookies:', cookies);

        // Create form data
        const formData = new FormData();

        // Basic Information
        formData.append('name', 'Test Laptop Dell XPS 15');
        formData.append('category_id', '1'); // Assuming 1 is the ID for Laptop category
        formData.append('description', 'Test laptop for inventory management system');

        // Technical Details
        formData.append('serial_number', 'SN123456789');
        formData.append('model', 'XPS 15 9520');
        formData.append('manufacturer', 'Dell');

        // Purchase Information
        formData.append('purchase_date', new Date().toISOString().split('T')[0]);
        formData.append('purchase_cost', '1299.99');
        formData.append('warranty_expiry', new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);

        // Status and Location
        formData.append('status', 'available');
        formData.append('location', 'IT Department');
        formData.append('notes', 'Test item for system verification');

        // Network Information
        formData.append('hostname', 'DELL-XPS-15-001');
        formData.append('ip_address', '*************');
        formData.append('mac_address', '00:1A:2B:3C:4D:5E');

        // Laptop Condition
        formData.append('physical_damage', 'None');
        formData.append('keyboard_condition', 'Working');
        formData.append('touchpad_condition', 'Working');
        formData.append('hdmi_port_condition', 'Working');
        formData.append('ethernet_wifi_condition', 'Both Working');
        formData.append('vga_port_condition', 'Not Available');
        formData.append('usb_port_condition', 'All Working');
        formData.append('speaker_port_condition', 'Working');
        formData.append('speakers_condition', 'Working');
        formData.append('display_condition', 'Perfect');
        formData.append('cd_drive_condition', 'Not Available');
        formData.append('webcam_condition', 'Working');
        formData.append('charger_port_condition', 'Both Working');
        formData.append('os_drivers_condition', 'Fully Functional');
        formData.append('laptop_bag_condition', 'Good Condition');

        // Add test image
        const imagePath = path.join(__dirname, 'public/uploads/inventory/inventory-1745483731638-989080097.png');
        formData.append('item_image', fs.createReadStream(imagePath));

        // Submit the form
        const response = await fetch('http://localhost:3018/admin/inventory/items/add', {
            method: 'POST',
            body: formData,
            headers: {
                'Cookie': cookies
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.text();
        console.log('Form submitted successfully!');
        console.log('Response:', result);

    } catch (error) {
        console.error('Error submitting form:', error);
    }
}

// Run the test
testInventorySubmission(); 