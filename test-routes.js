// Submit test endpoint
router.post('/submit/:attemptId', checkAuthenticated, async (req, res) => {
    try {
        const { attemptId } = req.params;
        console.log(`Processing test submission for attemptId: ${attemptId}, user: ${req.session.userId}`);

        // Validate that the attempt belongs to the user
        const [attempts] = await db.query(
            `SELECT ea.*, e.passing_marks, e.exam_name, e.exam_id
             FROM exam_attempts ea
             JOIN exams e ON ea.exam_id = e.exam_id
             WHERE ea.attempt_id = ? AND ea.user_id = ?`,
            [attemptId, req.session.userId]
        );

        if (attempts.length === 0) {
            console.error(`No attempt found for attemptId: ${attemptId}, user: ${req.session.userId}`);
            req.session.flashError = 'Unauthorized attempt';
            return res.redirect('/tests');
        }

        const attempt = attempts[0];
        console.log(`Found attempt: ${JSON.stringify(attempt)}`);

        // Calculate score
        const [questions] = await db.query(`
            SELECT q.*, s.section_id
            FROM questions q
            JOIN sections s ON q.section_id = s.section_id
            WHERE s.exam_id = ?
        `, [attempt.exam_id]);

        const [userAnswers] = await db.query(
            'SELECT * FROM user_answers WHERE attempt_id = ?',
            [attemptId]
        );

        console.log(`Found ${questions.length} questions and ${userAnswers.length} user answers`);

        // Convert to a map for easy lookup
        const answersMap = {};
        userAnswers.forEach(answer => {
            answersMap[answer.question_id] = answer.selected_option_id || answer.answer_text || answer.essay_answer || '';
        });

        let totalQuestions = questions.length;
        let correctAnswers = 0;

        // Track section-wise performance
        const sectionPerformance = {};

        // Get all option IDs from user answers
        const optionIds = userAnswers
            .filter(answer => answer.selected_option_id)
            .map(answer => answer.selected_option_id);

        // Get all options in a single query
        let optionsMap = {};
        if (optionIds.length > 0) {
            const [allOptions] = await db.query(
                'SELECT id, option_text FROM options WHERE id IN (?)',
                [optionIds]
            );

            // Create a map of option ID to option text
            allOptions.forEach(option => {
                optionsMap[option.id] = option.option_text;
            });
        }

        // Now process each question
        for (const question of questions) {
            const userAnswer = answersMap[question.question_id];
            let isCorrect = false;

            if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') {
                // For MCQ, check if the selected option text matches the correct answer
                if (userAnswer) {
                    const optionId = parseInt(userAnswer);
                    if (!isNaN(optionId)) {
                        const selectedOptionText = optionsMap[optionId];
                        if (selectedOptionText && question.correct_answer) {
                            // Compare the option text with the correct answer
                            isCorrect = selectedOptionText.toLowerCase() === question.correct_answer.toLowerCase();
                            console.log(`Question ${question.question_id}: Selected option text: "${selectedOptionText}", Correct answer: "${question.correct_answer}", isCorrect: ${isCorrect}`);
                        }
                    }
                }
            } else {
                // For other question types, direct comparison
                isCorrect = userAnswer && userAnswer.toString() === question.correct_answer.toString();
            }

            if (isCorrect) {
                correctAnswers++;
            }

            // Track section performance
            if (!sectionPerformance[question.section_id]) {
                sectionPerformance[question.section_id] = {
                    totalQuestions: 0,
                    correctAnswers: 0
                };
            }

            sectionPerformance[question.section_id].totalQuestions++;
            if (isCorrect) {
                sectionPerformance[question.section_id].correctAnswers++;
            }
        }

        const scorePercentage = (correctAnswers / totalQuestions) * 100;
        const passed = scorePercentage >= attempt.passing_marks;

        console.log(`Score: ${scorePercentage.toFixed(2)}%, Passed: ${passed}`);

        // Calculate test duration
        const now = new Date();
        const startTime = new Date(attempt.start_time);
        const durationMs = now - startTime;

        // Format duration as HH:MM:SS
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
        const durationFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        const durationSeconds = Math.floor(durationMs / 1000);

        console.log(`Duration: ${durationFormatted} (${durationSeconds} seconds)`);

        // Count the number of attempts for this test
        const [attemptCounts] = await db.query(
            'SELECT COUNT(*) as attempt_count FROM exam_attempts WHERE exam_id = ? AND user_id = ?',
            [attempt.exam_id, req.session.userId]
        );

        const attemptNumber = attemptCounts[0].attempt_count;
        console.log(`Attempt number: ${attemptNumber}`);

        // Begin transaction
        await db.query('START TRANSACTION');

        try {
            console.log('Starting database transaction...');

            // Clear all bookmarks
            await db.query(
                'UPDATE user_answers SET is_bookmarked = 0 WHERE attempt_id = ?',
                [attemptId]
            );
            console.log('Bookmarks cleared successfully');

            // Update attempt status
            await db.query(
                'UPDATE exam_attempts SET status = ?, total_score = ?, end_time = NOW() WHERE attempt_id = ?',
                ['completed', scorePercentage, attemptId]
            );
            console.log('Attempt status updated successfully');

            // Insert attempt statistics
            try {
                await db.query(
                    `INSERT INTO attempt_statistics
                     (user_id, exam_id, attempt_id, attempt_number, duration_seconds, duration_formatted, total_questions, correct_answers)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [req.session.userId, attempt.exam_id, attemptId, attemptNumber, durationSeconds, durationFormatted, totalQuestions, correctAnswers]
                );
                console.log('Attempt statistics inserted successfully');
            } catch (statError) {
                console.error('Error inserting attempt statistics:', statError);
                // Continue with the transaction even if this fails
            }

            // Insert section-wise performance
            try {
                for (const sectionId in sectionPerformance) {
                    const sectionData = sectionPerformance[sectionId];
                    const sectionScore = (sectionData.correctAnswers / sectionData.totalQuestions) * 100;

                    // Categorize performance
                    let performanceCategory;
                    if (sectionScore < 30) {
                        performanceCategory = 'weak';
                    } else if (sectionScore < 60) {
                        performanceCategory = 'medium';
                    } else {
                        performanceCategory = 'strong';
                    }

                    await db.query(
                        `INSERT INTO user_performance
                         (user_id, exam_id, attempt_id, section_id, score_percentage, performance_category)
                         VALUES (?, ?, ?, ?, ?, ?)`,
                        [req.session.userId, attempt.exam_id, attemptId, sectionId, sectionScore, performanceCategory]
                    );
                    console.log(`Section performance for section ${sectionId} inserted successfully`);
                }
            } catch (perfError) {
                console.error('Error inserting section performance:', perfError);
                // Continue with the transaction even if this fails
            }

            // Commit transaction
            await db.query('COMMIT');
            console.log('Transaction committed successfully');

            req.session.flashSuccess = 'Test submitted successfully!';
            res.redirect(`/tests/results/${attemptId}`);
        } catch (error) {
            // Rollback on error
            await db.query('ROLLBACK');
            console.error('Transaction error, rollback performed:', error);
            throw error;
        }
    } catch (error) {
        console.error('Error submitting test:', error);
        req.session.flashError = `Error submitting test: ${error.message}`;
        res.redirect('/tests');
    }
});