const express = require('express');
const app = express();
const cors = require('cors');
const PORT = process.env.PORT || 3018;

// Enable CORS with specific options
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Parse JSON bodies
app.use(express.json());

// Parse URL-encoded bodies
app.use(express.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

app.get('/', (req, res) => {
    res.send('Hello World!');
});

app.get('/users', (req, res) => {
    db.query('SELECT * FROM users', (err, results) => {
        if (err) {
            return res.status(500).send(err);
        }
        res.send(`
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
            <link rel="stylesheet" type="text/css" href="/styles.css">
            <nav class="bg-gradient-to-r from-blue-600 to-blue-800 shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h2 class="text-white text-xl font-bold">Admin Dashboard</h2>oa
                        </div>
                        <!-- Desktop menu -->
                        <div class="hidden md:flex items-center">
                            <div class="ml-10 flex items-baseline space-x-4">
                                <a href="/admin-dashboard" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                                <a href="/users" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Users</a>
                                <a href="/tests" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Tests</a>
                                <a href="/questions" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Questions</a>
                                <!-- Profile dropdown -->
                                <div class="relative">
                                    <button id="accountDropdown" class="flex items-center text-sm font-medium text-white bg-blue-700 hover:bg-blue-600 rounded-full px-4 py-2 focus:outline-none">
                                        <span>${req.session.user.username}</span>
                                        <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div class="dropdown-content hidden absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                        <div class="py-1">
                                            <a href="/account" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Account</a>
                                            <a href="/logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="container mx-auto px-4 py-6">
                <h2 class="text-2xl font-bold mb-4">Users</h2>
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2">Username</th>
                            <th class="py-2">Phone Number</th>
                            <th class="py-2">Date of Birth</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(user => `
                            <tr>
                                <td class="border px-4 py-2">${user.username}</td>
                                <td class="border px-4 py-2">${user.phone_number}</td>
                                <td class="border px-4 py-2">${user.dob}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Toggle dropdown on click
                    document.getElementById('accountDropdown').addEventListener('click', function(e) {
                        e.preventDefault();
                        document.querySelector('.dropdown-content').classList.toggle('hidden');
                    });
                });
            </script>
        `);
    });
});

// Handle create new test endpoint (both paths)
app.post('/admin/tests/add', (req, res) => {
    try {
        const { exam_name } = req.body;

        // Log the received data
        console.log('Received create test request:', { exam_name });

        // Validate input
        if (!exam_name) {
            return res.status(400).json({
                success: false,
                message: 'Exam name is required'
            });
        }

        // For testing environment, return success with a dummy exam ID
        const examId = Date.now(); // Dummy exam ID
        res.json({
            success: true,
            examId: examId, // Client expects examId
            exam_id: examId, // Also include exam_id for consistency
            message: 'Test created successfully'
        });
    } catch (error) {
        console.error('Error creating test:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Internal server error'
        });
    }
});

// Handle save draft endpoint (old path)
app.post('/tests/admin/save-draft', (req, res) => {
    try {
        const { exam_name, instructions, duration, passing_score, sections, exam_id } = req.body;

        // Log the received data
        console.log('Received draft data (old path):', {
            exam_id,
            exam_name,
            instructions,
            duration,
            passing_score,
            sections_count: sections ? sections.length : 0
        });

        // Validate input
        if (!exam_name) {
            return res.status(400).json({
                success: false,
                message: 'Exam name is required'
            });
        }

        // For testing environment, return success with the processed data
        res.json({
            success: true,
            message: 'Draft saved successfully',
            exam_id: exam_id || Date.now(), // Use provided exam_id or generate a new one
            data: {
                exam_name,
                instructions: instructions || '',
                duration: duration || 60,
                passing_score: passing_score || 40,
                sections_count: sections ? sections.length : 0
            }
        });
    } catch (error) {
        console.error('Error handling draft save:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Internal server error'
        });
    }
});

// Handle save draft endpoint (new path) - No authentication required for testing
app.post('/admin/tests/save-draft', (req, res) => {
    // Log the request headers for debugging
    console.log('Request headers:', req.headers);
    try {
        const { exam_name, instructions, duration, passing_score, sections, exam_id } = req.body;

        // Log the received data
        console.log('Received draft data (new path):', {
            exam_id,
            exam_name,
            instructions,
            duration,
            passing_score,
            sections_count: sections ? sections.length : 0
        });

        // Validate input
        if (!exam_name) {
            return res.status(400).json({
                success: false,
                message: 'Exam name is required'
            });
        }

        // Log the request body for debugging
        console.log('Request body:', req.body);

        // Generate a new exam_id if one wasn't provided
        const generatedExamId = exam_id || Date.now();

        console.log('Using exam_id:', generatedExamId, '(generated:', !exam_id, ')');

        // For testing environment, return success with the processed data
        res.json({
            success: true,
            message: 'Draft saved successfully',
            exam_id: generatedExamId, // Use the generated or provided exam_id
            data: {
                exam_name,
                instructions: instructions || '',
                duration: duration || 60,
                passing_score: passing_score || 40,
                sections_count: sections ? sections.length : 0
            }
        });
    } catch (error) {
        console.error('Error handling draft save:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Internal server error'
        });
    }
});

app.listen(PORT, () => {
    console.log(`Test server is running on http://localhost:${PORT}`);
}).on('error', (err) => {
    console.error('Test server failed to start:', err);
});