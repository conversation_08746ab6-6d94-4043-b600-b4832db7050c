# Student View Test Cases

This directory contains test cases and procedures to verify that all required functionality is available and working correctly in the student view of the application.

## Files

- `student_view_checklist.md`: A comprehensive checklist of all features that should be available in the student view.
- `student_view_test_script.js`: An automated test script using Puppeteer to verify student view functionality.
- `student_view_database_check.sql`: SQL script to check and create necessary database tables for student-related activities.
- `student_view_manual_test_procedure.md`: Step-by-step procedure for manually testing all student view functionality.

## How to Use These Files

### Checklist

The `student_view_checklist.md` file contains a comprehensive list of all features that should be available in the student view. Use this checklist to:

1. Verify that all required functionality is implemented
2. Track progress during development
3. Ensure nothing is missed during testing

### Automated Testing

The `student_view_test_script.js` file contains an automated test script using Puppeteer. To use this script:

1. Install dependencies:
   ```
   npm install puppeteer
   ```

2. Run the script:
   ```
   node test_cases/student_view_test_script.js
   ```

3. The script will:
   - Log in as a student
   - Navigate through all student view pages
   - Take screenshots of each page
   - Generate a test report in `student_view_test_report.md`

### Database Setup

The `student_view_database_check.sql` file contains SQL statements to check and create necessary database tables for student-related activities. To use this script:

1. Run the script against your MySQL database:
   ```
   mysql -u root exam_prep_platform < test_cases/student_view_database_check.sql
   ```

2. The script will:
   - Create the database if it doesn't exist
   - Create all necessary tables if they don't exist
   - Insert sample data for testing if the tables are empty

### Manual Testing

The `student_view_manual_test_procedure.md` file contains a step-by-step procedure for manually testing all student view functionality. To use this procedure:

1. Follow each step in the procedure
2. Document any issues or missing functionality
3. Use the checklist to ensure all required functionality has been tested

## Test Coverage

These test cases cover the following areas of the student view:

1. **Dashboard**
   - Test statistics
   - Upcoming tests
   - Recent activity
   - Today's schedule
   - Upcoming practicals
   - Calendar events

2. **Tests**
   - Upcoming tests
   - Completed tests
   - Test taking
   - Test results

3. **Assignments**
   - Pending assignments
   - Submitted assignments
   - Graded assignments
   - Assignment submission

4. **Practicals**
   - Scheduled practicals
   - Practical details
   - Practical submission
   - Practical records

5. **Results**
   - Exam results
   - Assignment results
   - Practical results
   - Detailed result view

6. **Groups**
   - Class groups
   - Study groups
   - Activity groups
   - Group joining
   - Group details

7. **Calendar**
   - Test assignments
   - Practicals
   - Assignments
   - Event details

8. **Profile**
   - Personal information
   - Profile image
   - Academic information

9. **General UI/UX**
   - Navigation
   - Mobile responsiveness
   - Notifications
   - Session management

## Reporting Issues

When reporting issues found during testing, include the following information:

1. The specific feature or functionality that is not working
2. Steps to reproduce the issue
3. Expected behavior
4. Actual behavior
5. Screenshots or error messages
6. Browser and device information

## Updating Test Cases

These test cases should be updated whenever new functionality is added to the student view. To update the test cases:

1. Add new items to the checklist
2. Update the automated test script to include the new functionality
3. Update the database script if new tables are required
4. Update the manual test procedure to include steps for testing the new functionality
