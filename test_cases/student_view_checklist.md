# Student View Test Case Checklist

This checklist ensures that all required functionality is available and working correctly in the student view of the application.

## Dashboard

### Test Statistics
- [ ] Assigned tests count is displayed
- [ ] Completed tests count is displayed
- [ ] Average score is displayed
- [ ] Learning plans count is displayed
- [ ] Upcoming practicals count is displayed

### Upcoming Tests
- [ ] List of upcoming tests is displayed
- [ ] Each test shows name, due date, and attempts used/max attempts
- [ ] "Take Test" button is available for each test
- [ ] "View All Tests" link works correctly

### Recent Activity
- [ ] Recent activity list is displayed
- [ ] Each activity shows type, date, and description
- [ ] Activities are sorted by date (newest first)

### Today's Schedule
- [ ] Today's class schedule is displayed
- [ ] Each class shows subject, time, teacher, and room
- [ ] Classes are sorted by start time

### Upcoming Practicals
- [ ] List of upcoming practicals is displayed
- [ ] Each practical shows name, date, and subject
- [ ] "View Details" button is available for each practical

### Calendar Events
- [ ] Calendar widget is displayed on dashboard
- [ ] Events are color-coded by type (test, practical, assignment)
- [ ] Clicking on a date shows events for that day

## Tests

### Upcoming Tests
- [ ] List of upcoming tests is displayed
- [ ] Each test shows name, duration, end date, and attempts used/max
- [ ] "Take Test" button is available for each test with remaining attempts
- [ ] "Request Access" button is shown when max attempts are used

### Completed Tests
- [ ] List of completed tests is displayed
- [ ] Each test shows name, highest score, attempts used, and completion date
- [ ] "View Results" button is available for each completed test

### All Tests
- [ ] Complete list of all tests (both upcoming and completed) is displayed
- [ ] Each test shows appropriate status (upcoming, in progress, completed)
- [ ] Appropriate action button is shown based on test status

### Test Taking
- [ ] Instructions page is displayed before starting test
- [ ] Timer is displayed and counts down during test
- [ ] Questions are displayed with all options
- [ ] Navigation between questions works correctly
- [ ] Question palette shows answered/unanswered status
- [ ] Submit button works correctly
- [ ] Confirmation dialog appears before submission

### Test Results
- [ ] Test summary shows score, passing marks, and status
- [ ] Questions are displayed with correct/incorrect indicators
- [ ] Correct answers are shown for incorrect responses
- [ ] Explanations are displayed where available

## Assignments

### Pending Assignments
- [ ] List of pending assignments is displayed
- [ ] Each assignment shows title, due date, and subject
- [ ] "View Details" button is available for each assignment
- [ ] "Submit" button is available for each assignment

### Submitted Assignments
- [ ] List of submitted assignments is displayed
- [ ] Each assignment shows submission date and status
- [ ] "View Submission" button is available for each assignment

### Graded Assignments
- [ ] List of graded assignments is displayed
- [ ] Each assignment shows grade and feedback
- [ ] "View Feedback" button is available for each assignment

### Assignment Submission
- [ ] Assignment details are displayed
- [ ] File upload functionality works correctly
- [ ] Text submission functionality works correctly
- [ ] Submit button works correctly
- [ ] Confirmation dialog appears before submission

## Practicals

### Scheduled Practicals
- [ ] List of scheduled practicals is displayed
- [ ] Each practical shows date, time, subject, and location
- [ ] "View Details" button is available for each practical

### Practical Details
- [ ] Practical information is displayed (description, requirements, etc.)
- [ ] Submission instructions are clearly shown
- [ ] "Submit" button is available if submission is required

### Practical Submission
- [ ] Submission form is displayed
- [ ] File upload functionality works correctly
- [ ] Text submission functionality works correctly
- [ ] Submit button works correctly
- [ ] Confirmation dialog appears before submission

### Practical Records
- [ ] List of submitted practicals is displayed
- [ ] Each record shows submission date and status
- [ ] "View Submission" button is available for each record

## Results

### Exam Results
- [ ] List of exam results is displayed
- [ ] Each result shows exam name, date, score, and status
- [ ] "View Details" button is available for each result

### Assignment Results
- [ ] List of assignment results is displayed
- [ ] Each result shows assignment name, date, score, and status
- [ ] "View Details" button is available for each result

### Practical Results
- [ ] List of practical results is displayed
- [ ] Each result shows practical name, date, score, and status
- [ ] "View Details" button is available for each result

### Detailed Result View
- [ ] Detailed information about the result is displayed
- [ ] Feedback from teacher/instructor is shown
- [ ] Score breakdown is displayed where applicable

## Groups

### Class Groups
- [ ] List of class groups is displayed
- [ ] Each group shows name, description, and member count
- [ ] "View Details" button is available for each group

### Study Groups
- [ ] List of study groups is displayed
- [ ] Each group shows name, description, and member count
- [ ] "View Details" button is available for each group

### Activity Groups
- [ ] List of activity groups is displayed
- [ ] Each group shows name, description, and member count
- [ ] "View Details" button is available for each group

### Group Joining
- [ ] "Join Group" button is available
- [ ] Group search functionality works correctly
- [ ] Join request process works correctly

### Group Details
- [ ] Group information is displayed
- [ ] Member list is shown
- [ ] Group activities/resources are displayed
- [ ] Leave group option is available for joined groups

## Calendar

### Test Assignments
- [ ] Test assignments are displayed on calendar
- [ ] Each test shows name and due date
- [ ] Clicking on a test shows details in modal

### Practicals
- [ ] Scheduled practicals are displayed on calendar
- [ ] Each practical shows name, date, and time
- [ ] Clicking on a practical shows details in modal

### Assignments
- [ ] Assignments are displayed on calendar
- [ ] Each assignment shows name and due date
- [ ] Clicking on an assignment shows details in modal

### Event Details Modal
- [ ] Modal displays detailed information about the event
- [ ] "View Details" button links to the full event page
- [ ] Modal can be closed

## Profile

### Personal Information
- [ ] Name, email, and other personal details are displayed
- [ ] "Edit Profile" button is available
- [ ] Edit functionality works correctly

### Profile Image
- [ ] Profile image is displayed
- [ ] Image upload/change functionality works correctly
- [ ] Default icon with initial letter is shown when no image is set

### Academic Information
- [ ] Enrolled classes are displayed
- [ ] Assigned subjects are displayed
- [ ] Academic statistics are shown (if applicable)

## General UI/UX

### Navigation
- [ ] Sidebar navigation is displayed correctly
- [ ] Current page is highlighted in navigation
- [ ] All navigation links work correctly

### Mobile Responsiveness
- [ ] All pages display correctly on mobile devices
- [ ] Forms are usable on mobile devices
- [ ] Navigation works correctly on mobile devices

### Notifications
- [ ] Notification bell is displayed with unread count
- [ ] Notifications list is displayed when bell is clicked
- [ ] Toast notifications appear for system messages

### Session Management
- [ ] 15-minute inactivity timeout works correctly
- [ ] Countdown appears after 5 seconds of inactivity
- [ ] Session timeout is disabled during test-taking
