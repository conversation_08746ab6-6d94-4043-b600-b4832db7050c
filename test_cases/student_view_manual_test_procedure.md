# Student View Manual Test Procedure

This document outlines the step-by-step procedure to manually test all functionality in the student view of the application.

## Prerequisites

1. Ensure the application is running at http://localhost:3018
2. Ensure the database is properly set up with the necessary tables
3. Ensure there is a student account with the following credentials:
   - Email: <EMAIL>
   - Password: Merit123#

## Test Procedure

### 1. Login

1. Navigate to http://localhost:3018/login
2. Enter the student credentials:
   - Email: <EMAIL>
   - Password: Merit123#
3. Click the "Login" button
4. Verify that you are redirected to the student dashboard

### 2. Dashboard

1. Verify that the dashboard displays the following sections:
   - Test statistics (assigned tests, completed tests, average score)
   - Upcoming tests
   - Recent activity
   - Today's schedule
   - Upcoming practicals
   - Calendar events
2. Check that all data is displayed correctly in each section
3. Verify that all links in the dashboard work correctly

### 3. Tests

1. Click on "Tests" in the sidebar navigation
2. Verify that the tests page displays the following tabs:
   - Upcoming Tests
   - Completed Tests
   - All Tests
3. Check that each tab displays the appropriate tests
4. For an upcoming test:
   - Click the "Take Test" button
   - Verify that the test instructions page is displayed
   - Click "Start Test"
   - Verify that the test interface is displayed with timer
   - Answer a few questions
   - Click "Submit" to complete the test
   - Verify that the test results are displayed
5. For a completed test:
   - Click the "View Results" button
   - Verify that the test results are displayed correctly

### 4. Assignments

1. Click on "Assignments" in the sidebar navigation
2. Verify that the assignments page displays the following tabs:
   - Pending
   - Submitted
   - Graded
3. Check that each tab displays the appropriate assignments
4. For a pending assignment:
   - Click the "View Details" button
   - Verify that the assignment details are displayed
   - Click "Submit Assignment"
   - Fill in the submission form
   - Upload a file if required
   - Click "Submit"
   - Verify that the assignment is moved to the "Submitted" tab
5. For a submitted assignment:
   - Click the "View Submission" button
   - Verify that the submission details are displayed correctly

### 5. Practicals

1. Click on "Lab Practicals" in the sidebar navigation
2. Verify that the practicals page displays a list of scheduled practicals
3. Click the "View Details" button for a practical
4. Verify that the practical details are displayed correctly
5. If submission is required:
   - Click "Submit"
   - Fill in the submission form
   - Upload a file if required
   - Click "Submit"
   - Verify that the submission is recorded
6. Click on "Submissions" tab
7. Verify that all submitted practicals are displayed correctly

### 6. Results

1. Click on "Results" in the sidebar navigation
2. Verify that the results page displays the following tabs:
   - Exams
   - Assignments
   - Practicals
3. Check that each tab displays the appropriate results
4. Click the "View Details" button for a result
5. Verify that the detailed result information is displayed correctly

### 7. Groups

1. Click on "Groups" in the sidebar navigation
2. Verify that the groups page displays the following tabs:
   - Class Groups
   - Study Groups
   - Activity Groups
3. Check that each tab displays the appropriate groups
4. Click the "Join Group" button
5. Verify that the group search functionality works correctly
6. Join a group and verify that it appears in the appropriate tab
7. Click the "View Details" button for a group
8. Verify that the group details are displayed correctly
9. Test the "Leave Group" functionality

### 8. Calendar

1. Click on "Calendar" in the sidebar navigation
2. Verify that the calendar displays events for the current month
3. Check that events are color-coded by type (test, practical, assignment)
4. Click on a date with events
5. Verify that the events for that date are displayed
6. Click on an event
7. Verify that the event details modal is displayed
8. Click the "View Details" button in the modal
9. Verify that you are redirected to the full event page

### 9. Profile

1. Click on your profile name/image in the navigation
2. Click "My Profile" in the dropdown
3. Verify that your personal information is displayed correctly
4. Click "Edit Profile"
5. Make changes to your profile information
6. Click "Save Changes"
7. Verify that the changes are saved correctly
8. Test the profile image upload functionality:
   - Click "Change Profile Picture"
   - Upload a new image
   - Verify that the new image is displayed
   - Test removing the profile image
   - Verify that the default icon with initial letter is displayed

### 10. General UI/UX

1. Verify that the sidebar navigation is displayed correctly
2. Check that the current page is highlighted in the navigation
3. Test all navigation links to ensure they work correctly
4. Test the mobile responsiveness:
   - Resize the browser window to mobile size
   - Verify that all pages display correctly
   - Check that forms are usable on mobile
   - Verify that navigation works correctly on mobile
5. Test the notification functionality:
   - Verify that the notification bell is displayed
   - Click the bell to display notifications
   - Check that toast notifications appear for system messages
6. Test the session management:
   - Leave the application idle for 10 minutes
   - Verify that the inactivity countdown appears
   - Check that you are logged out after 15 minutes of inactivity
   - Verify that the timeout is disabled during test-taking

## Test Completion

After completing all the tests, document any issues or missing functionality in the student view. Use the checklist provided in `student_view_checklist.md` to ensure all required functionality has been tested.

## Additional Notes

- If any functionality is not working as expected, take screenshots and document the issue
- Pay special attention to any error messages or console errors
- Test all functionality with different data to ensure it works in all scenarios
- Verify that all data is displayed correctly and formatted appropriately
