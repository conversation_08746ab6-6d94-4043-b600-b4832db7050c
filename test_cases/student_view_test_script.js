/**
 * Student View Test Script
 * 
 * This script tests all functionality in the student view to ensure
 * that each test case item is available and working correctly.
 * 
 * Run this script with:
 * node test_cases/student_view_test_script.js
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  baseUrl: 'http://localhost:3018',
  studentCredentials: {
    email: '<EMAIL>',
    password: 'Merit123#'
  },
  screenshotDir: path.join(__dirname, 'screenshots'),
  reportFile: path.join(__dirname, 'student_view_test_report.md')
};

// Ensure screenshot directory exists
if (!fs.existsSync(config.screenshotDir)) {
  fs.mkdirSync(config.screenshotDir, { recursive: true });
}

// Initialize report
let reportContent = `# Student View Test Report\n\nGenerated on: ${new Date().toLocaleString()}\n\n`;

// Main test function
async function runTests() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  
  try {
    // Login as student
    await login(page);
    
    // Test dashboard
    await testDashboard(page);
    
    // Test tests section
    await testTests(page);
    
    // Test assignments section
    await testAssignments(page);
    
    // Test practicals section
    await testPracticals(page);
    
    // Test results section
    await testResults(page);
    
    // Test groups section
    await testGroups(page);
    
    // Test calendar
    await testCalendar(page);
    
    // Test profile
    await testProfile(page);
    
    // Test general UI/UX
    await testGeneralUI(page);
    
    // Save report
    fs.writeFileSync(config.reportFile, reportContent);
    console.log(`Test report saved to ${config.reportFile}`);
    
  } catch (error) {
    console.error('Test failed:', error);
    reportContent += `\n## Error\n\n${error.message}\n\n`;
    fs.writeFileSync(config.reportFile, reportContent);
  } finally {
    await browser.close();
  }
}

// Login function
async function login(page) {
  reportContent += `## Login\n\n`;
  
  await page.goto(`${config.baseUrl}/login`);
  await page.waitForSelector('form');
  
  // Take screenshot of login page
  await takeScreenshot(page, 'login');
  
  // Fill login form
  await page.type('input[name="email"]', config.studentCredentials.email);
  await page.type('input[name="password"]', config.studentCredentials.password);
  
  // Submit form
  await Promise.all([
    page.click('button[type="submit"]'),
    page.waitForNavigation()
  ]);
  
  // Check if login was successful
  const url = page.url();
  const isLoggedIn = url.includes('/student/dashboard');
  
  if (isLoggedIn) {
    reportContent += `- ✅ Login successful\n`;
  } else {
    reportContent += `- ❌ Login failed\n`;
    throw new Error('Login failed');
  }
}

// Dashboard test
async function testDashboard(page) {
  reportContent += `\n## Dashboard\n\n`;
  
  await page.goto(`${config.baseUrl}/student/dashboard`);
  await page.waitForSelector('.container');
  
  // Take screenshot of dashboard
  await takeScreenshot(page, 'dashboard');
  
  // Check test statistics
  const statsExists = await page.evaluate(() => {
    return document.querySelector('.stats-container') !== null;
  });
  
  reportContent += `### Test Statistics\n`;
  reportContent += `- ${statsExists ? '✅' : '❌'} Statistics section exists\n`;
  
  // Check upcoming tests
  const upcomingTestsExists = await page.evaluate(() => {
    return document.querySelector('.upcoming-tests') !== null;
  });
  
  reportContent += `\n### Upcoming Tests\n`;
  reportContent += `- ${upcomingTestsExists ? '✅' : '❌'} Upcoming tests section exists\n`;
  
  // Add more checks for other dashboard elements
}

// Tests section test
async function testTests(page) {
  reportContent += `\n## Tests\n\n`;
  
  await page.goto(`${config.baseUrl}/tests`);
  await page.waitForSelector('.container');
  
  // Take screenshot of tests page
  await takeScreenshot(page, 'tests');
  
  // Check if tests are displayed
  const testsExist = await page.evaluate(() => {
    return document.querySelector('table') !== null;
  });
  
  reportContent += `- ${testsExist ? '✅' : '❌'} Tests are displayed\n`;
  
  // Add more checks for tests functionality
}

// Assignments section test
async function testAssignments(page) {
  reportContent += `\n## Assignments\n\n`;
  
  await page.goto(`${config.baseUrl}/student/assignments`);
  await page.waitForSelector('.container');
  
  // Take screenshot of assignments page
  await takeScreenshot(page, 'assignments');
  
  // Check if assignments tabs exist
  const tabsExist = await page.evaluate(() => {
    return document.querySelector('[data-tab="pending"]') !== null;
  });
  
  reportContent += `- ${tabsExist ? '✅' : '❌'} Assignment tabs are displayed\n`;
  
  // Add more checks for assignments functionality
}

// Practicals section test
async function testPracticals(page) {
  reportContent += `\n## Practicals\n\n`;
  
  await page.goto(`${config.baseUrl}/student/practicals`);
  await page.waitForSelector('.container');
  
  // Take screenshot of practicals page
  await takeScreenshot(page, 'practicals');
  
  // Check if practicals are displayed
  const practicalsExist = await page.evaluate(() => {
    return document.querySelector('.practicals-list') !== null;
  });
  
  reportContent += `- ${practicalsExist ? '✅' : '❌'} Practicals are displayed\n`;
  
  // Add more checks for practicals functionality
}

// Results section test
async function testResults(page) {
  reportContent += `\n## Results\n\n`;
  
  await page.goto(`${config.baseUrl}/student/results`);
  await page.waitForSelector('.container');
  
  // Take screenshot of results page
  await takeScreenshot(page, 'results');
  
  // Check if results tabs exist
  const tabsExist = await page.evaluate(() => {
    return document.querySelector('[data-tab="exams"]') !== null;
  });
  
  reportContent += `- ${tabsExist ? '✅' : '❌'} Result tabs are displayed\n`;
  
  // Add more checks for results functionality
}

// Groups section test
async function testGroups(page) {
  reportContent += `\n## Groups\n\n`;
  
  await page.goto(`${config.baseUrl}/student/groups`);
  await page.waitForSelector('.container');
  
  // Take screenshot of groups page
  await takeScreenshot(page, 'groups');
  
  // Check if group tabs exist
  const tabsExist = await page.evaluate(() => {
    return document.querySelector('[data-tab="class"]') !== null;
  });
  
  reportContent += `- ${tabsExist ? '✅' : '❌'} Group tabs are displayed\n`;
  
  // Add more checks for groups functionality
}

// Calendar test
async function testCalendar(page) {
  reportContent += `\n## Calendar\n\n`;
  
  await page.goto(`${config.baseUrl}/student/calendar`);
  await page.waitForSelector('.container');
  
  // Take screenshot of calendar page
  await takeScreenshot(page, 'calendar');
  
  // Check if calendar is displayed
  const calendarExists = await page.evaluate(() => {
    return document.querySelector('.calendar') !== null;
  });
  
  reportContent += `- ${calendarExists ? '✅' : '❌'} Calendar is displayed\n`;
  
  // Add more checks for calendar functionality
}

// Profile test
async function testProfile(page) {
  reportContent += `\n## Profile\n\n`;
  
  await page.goto(`${config.baseUrl}/student/profile`);
  await page.waitForSelector('.container');
  
  // Take screenshot of profile page
  await takeScreenshot(page, 'profile');
  
  // Check if profile information is displayed
  const profileExists = await page.evaluate(() => {
    return document.querySelector('.profile-info') !== null;
  });
  
  reportContent += `- ${profileExists ? '✅' : '❌'} Profile information is displayed\n`;
  
  // Add more checks for profile functionality
}

// General UI/UX test
async function testGeneralUI(page) {
  reportContent += `\n## General UI/UX\n\n`;
  
  // Check sidebar navigation
  await page.goto(`${config.baseUrl}/student/dashboard`);
  
  const sidebarExists = await page.evaluate(() => {
    return document.querySelector('.sidebar') !== null;
  });
  
  reportContent += `### Navigation\n`;
  reportContent += `- ${sidebarExists ? '✅' : '❌'} Sidebar navigation is displayed\n`;
  
  // Add more checks for general UI/UX
}

// Helper function to take screenshots
async function takeScreenshot(page, name) {
  const screenshotPath = path.join(config.screenshotDir, `${name}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  reportContent += `![${name}](screenshots/${name}.png)\n\n`;
}

// Run the tests
runTests().catch(console.error);
