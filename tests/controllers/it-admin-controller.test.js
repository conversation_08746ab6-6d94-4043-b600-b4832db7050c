const { mockRequest, mockResponse } = require('../setup');
const itAdminController = require('../../controllers/it-admin-controller');
const db = require('../../config/database');

// Mock the database module
jest.mock('../../config/database', () => ({
  query: jest.fn()
}));

describe('IT Admin Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getDashboard', () => {
    it('should render the IT admin dashboard with correct data', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('totalDevices')) {
          return [[{ totalDevices: 50, availableDevices: 35, devicesInRepair: 15 }]];
        } else if (query.includes('activeIssues')) {
          return [[{ activeIssues: 8 }]];
        } else if (query.includes('recentIssues')) {
          return [[
            { id: 1, device_name: 'Laptop 101', description: 'Screen flickering', status: 'open', created_at: '2023-12-01T10:00:00Z' },
            { id: 2, device_name: 'Desktop 202', description: 'Not booting', status: 'in_progress', created_at: '2023-11-28T14:30:00Z' }
          ]];
        }
        return [[]];
      });

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await itAdminController.getDashboard(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('it-admin/dashboard', expect.objectContaining({
        title: 'IT Admin Dashboard',
        layout: 'it-admin',
        currentPage: 'dashboard',
        stats: expect.objectContaining({
          totalDevices: 50,
          availableDevices: 35,
          devicesInRepair: 15,
          activeIssues: 8
        }),
        recentIssues: expect.arrayContaining([
          expect.objectContaining({ device_name: 'Laptop 101' }),
          expect.objectContaining({ device_name: 'Desktop 202' })
        ]),
        systemStatus: expect.objectContaining({
          serverLoad: '32%',
          memoryUsage: '45%',
          diskSpace: '28%'
        })
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await itAdminController.getDashboard(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Error loading dashboard',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'it-admin'
      }));
    });
  });

  describe('getSystemMonitoring', () => {
    it('should render the system monitoring page with correct data', async () => {
      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await itAdminController.getSystemMonitoring(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('it-admin/system-monitoring', expect.objectContaining({
        title: 'System Monitoring',
        layout: 'it-admin',
        currentPage: 'system-monitoring',
        systemData: expect.objectContaining({
          cpu: expect.objectContaining({
            usage: '32%',
            temperature: '45°C',
            cores: 4
          }),
          memory: expect.objectContaining({
            total: '16GB',
            used: '7.2GB',
            free: '8.8GB',
            usage: '45%'
          }),
          disk: expect.objectContaining({
            total: '500GB',
            used: '140GB',
            free: '360GB',
            usage: '28%'
          }),
          network: expect.objectContaining({
            status: 'operational',
            upload: '2.5 Mbps',
            download: '45 Mbps',
            activeConnections: 24
          })
        })
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock an error by making the function throw
      jest.spyOn(itAdminController, 'getSystemMonitoring').mockImplementationOnce(() => {
        throw new Error('System error');
      });

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function - this will throw an error
      try {
        await itAdminController.getSystemMonitoring(req, res);
      } catch (error) {
        // We expect an error to be thrown
        expect(error.message).toBe('System error');
      }
    });
  });
});
