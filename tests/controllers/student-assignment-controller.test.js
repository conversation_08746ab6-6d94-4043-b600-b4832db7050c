const { mockRequest, mockResponse } = require('../setup');
const studentAssignmentController = require('../../controllers/student-assignment-controller');
const db = require('../../config/database');

// Mock the database module
jest.mock('../../config/database', () => ({
  query: jest.fn()
}));

describe('Student Assignment Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getStudents', () => {
    it('should render the students page with correct data', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT u.id, u.username, u.email')) {
          return [[
            { id: 1, username: 'student1', email: '<EMAIL>', role: 'student', is_active: 1 },
            { id: 2, username: 'student2', email: '<EMAIL>', role: 'student', is_active: 1 }
          ]];
        } else if (query.includes('SELECT id, name, description FROM classes')) {
          return [[
            { id: 1, name: 'Class 11A', description: 'Science' },
            { id: 2, name: 'Class 12B', description: 'Commerce' }
          ]];
        }
        return [[]];
      });

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.getStudents(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('admin/students/index', expect.objectContaining({
        title: 'Student Management',
        layout: 'admin',
        currentPage: 'students',
        students: expect.arrayContaining([
          expect.objectContaining({ username: 'student1' }),
          expect.objectContaining({ username: 'student2' })
        ]),
        classes: expect.arrayContaining([
          expect.objectContaining({ name: 'Class 11A' }),
          expect.objectContaining({ name: 'Class 12B' })
        ])
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.getStudents(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Failed to fetch students',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'admin'
      }));
    });
  });

  describe('getAssignClass', () => {
    it('should render the assign class form with correct data', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id, username, email FROM users')) {
          return [[{ id: 1, username: 'student1', email: '<EMAIL>' }]];
        } else if (query.includes('SELECT id, name, description FROM classes')) {
          return [[
            { id: 1, name: 'Class 11A', description: 'Science' },
            { id: 2, name: 'Class 12B', description: 'Commerce' }
          ]];
        } else if (query.includes('SELECT sc.class_id, c.name as class_name')) {
          return [[
            { class_id: 1, class_name: 'Class 11A' }
          ]];
        }
        return [[]];
      });

      // Create mock request and response
      const req = mockRequest({}, {}, { id: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.getAssignClass(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('admin/students/assign-class', expect.objectContaining({
        title: 'Assign Class',
        layout: 'admin',
        currentPage: 'students',
        student: expect.objectContaining({
          id: 1,
          username: 'student1',
          email: '<EMAIL>'
        }),
        classes: expect.arrayContaining([
          expect.objectContaining({ name: 'Class 11A' }),
          expect.objectContaining({ name: 'Class 12B' })
        ]),
        studentClasses: [1]
      }));
    });

    it('should redirect if student not found', async () => {
      // Mock empty database response
      db.query.mockResolvedValue([[]]);

      // Create mock request and response
      const req = mockRequest({ flashError: null }, {}, { id: 999 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.getAssignClass(req, res);

      // Assertions
      expect(req.session.flashError).toBe('Student not found');
      expect(res.redirect).toHaveBeenCalledWith('/admin/students');
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest({}, {}, { id: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.getAssignClass(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Failed to fetch student class data',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'admin'
      }));
    });
  });

  describe('updateClassAssignments', () => {
    it('should update class assignments and return success response', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id FROM users')) {
          return [[{ id: 1 }]];
        } else {
          return [{ affectedRows: 1 }];
        }
      });

      // Create mock request and response
      const req = mockRequest({}, { classIds: [1, 2] }, { id: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.updateClassAssignments(req, res);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Class assignments updated successfully'
      });
    });

    it('should return error if classIds is not an array', async () => {
      // Create mock request and response
      const req = mockRequest({}, { classIds: 'invalid' }, { id: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.updateClassAssignments(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid class selection'
      });
    });

    it('should return error if student not found', async () => {
      // Mock empty database response
      db.query.mockResolvedValue([[]]);

      // Create mock request and response
      const req = mockRequest({}, { classIds: [1, 2] }, { id: 999 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.updateClassAssignments(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Student not found'
      });
    });

    it('should handle database errors', async () => {
      // Mock database error
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id FROM users')) {
          return [[{ id: 1 }]];
        } else if (query === 'START TRANSACTION') {
          return [{}];
        } else {
          throw new Error('Database error');
        }
      });

      // Create mock request and response
      const req = mockRequest({}, { classIds: [1, 2] }, { id: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentAssignmentController.updateClassAssignments(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Failed to update class assignments'
      });
    });
  });
});
