const { mockRequest, mockResponse } = require('../setup');
const studentController = require('../../controllers/student-controller');
const db = require('../../config/database');

// Mock the database module
jest.mock('../../config/database', () => ({
  query: jest.fn()
}));

describe('Student Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getDashboard', () => {
    it('should render the student dashboard with correct data', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('assignedTests')) {
          return [[{ assignedTests: 5, completedTests: 3, averageScore: 85.5 }]];
        } else if (query.includes('learningPlans')) {
          return [[{ learningPlans: 2 }]];
        } else if (query.includes('upcoming')) {
          return [[
            { id: 1, exam_name: 'Math Test', subject: 'Mathematics', end_date: '2023-12-31', max_attempts: 3, attempts_used: 1 },
            { id: 2, exam_name: 'Science Quiz', subject: 'Science', end_date: '2023-12-25', max_attempts: 2, attempts_used: 0 }
          ]];
        } else if (query.includes('activity')) {
          return [[
            { type: 'test_completed', exam_name: 'Math Test', score: 90, created_at: '2023-12-01T10:00:00Z' },
            { type: 'test_assigned', exam_name: 'Science Quiz', created_at: '2023-11-28T14:30:00Z' }
          ]];
        } else if (query.includes('schedule')) {
          return [[
            { start_time: '09:00', end_time: '10:30', subject: 'Mathematics', teacher_name: 'John Doe', room: '101', type: 'theory' },
            { start_time: '11:00', end_time: '12:30', subject: 'Science', teacher_name: 'Jane Smith', room: '102', type: 'practical' }
          ]];
        }
        return [[]];
      });

      // Create mock request and response
      const req = mockRequest({ userId: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentController.getDashboard(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('student/dashboard', expect.objectContaining({
        title: 'Student Dashboard',
        layout: 'student',
        currentPage: 'dashboard',
        stats: expect.objectContaining({
          assignedTests: 5,
          completedTests: 3,
          averageScore: '85.5%',
          learningPlans: 2
        }),
        upcomingTests: expect.arrayContaining([
          expect.objectContaining({ exam_name: 'Math Test' }),
          expect.objectContaining({ exam_name: 'Science Quiz' })
        ]),
        recentActivity: expect.arrayContaining([
          expect.objectContaining({ type: 'test_completed' }),
          expect.objectContaining({ type: 'test_assigned' })
        ]),
        todaySchedule: expect.arrayContaining([
          expect.objectContaining({ subject: 'Mathematics' }),
          expect.objectContaining({ subject: 'Science' })
        ])
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest({ userId: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentController.getDashboard(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Error loading dashboard',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'student'
      }));
    });
  });

  describe('getActivity', () => {
    it('should render the student activity page with correct data', async () => {
      // Mock database response
      db.query.mockResolvedValue([[
        { type: 'test_completed', exam_name: 'Math Test', score: 90, created_at: '2023-12-01T10:00:00Z' },
        { type: 'test_assigned', exam_name: 'Science Quiz', created_at: '2023-11-28T14:30:00Z' },
        { type: 'plan_completed', plan_name: 'Physics Basics', created_at: '2023-11-25T09:15:00Z' }
      ]]);

      // Create mock request and response
      const req = mockRequest({ userId: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentController.getActivity(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('student/activity', expect.objectContaining({
        title: 'Activity History',
        layout: 'student',
        currentPage: 'activity',
        activity: expect.arrayContaining([
          expect.objectContaining({ type: 'test_completed' }),
          expect.objectContaining({ type: 'test_assigned' }),
          expect.objectContaining({ type: 'plan_completed' })
        ])
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest({ userId: 1 });
      const res = mockResponse();

      // Call the controller function
      await studentController.getActivity(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Error loading activity history',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'student'
      }));
    });
  });
});
