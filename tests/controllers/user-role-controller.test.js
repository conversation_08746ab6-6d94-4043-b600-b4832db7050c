const { mockRequest, mockResponse } = require('../setup');
const userRoleController = require('../../controllers/user-role-controller');
const db = require('../../config/database');

// Mock the database module
jest.mock('../../config/database', () => ({
  query: jest.fn()
}));

describe('User Role Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    it('should render the users page with correct data', async () => {
      // Mock database response
      db.query.mockResolvedValue([[
        { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin', is_active: 1 },
        { id: 2, username: 'teacher1', email: '<EMAIL>', role: 'teacher', is_active: 1 },
        { id: 3, username: 'student1', email: '<EMAIL>', role: 'student', is_active: 1 }
      ]]);

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await userRoleController.getUsers(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('admin/users/index', expect.objectContaining({
        title: 'User Management',
        layout: 'admin',
        currentPage: 'users',
        users: expect.arrayContaining([
          expect.objectContaining({ username: 'admin' }),
          expect.objectContaining({ username: 'teacher1' }),
          expect.objectContaining({ username: 'student1' })
        ])
      }));
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest();
      const res = mockResponse();

      // Call the controller function
      await userRoleController.getUsers(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Failed to fetch users',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'admin'
      }));
    });
  });

  describe('getAssignRole', () => {
    it('should render the assign role form with correct data', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id, username, email, role FROM users')) {
          return [[{ id: 2, username: 'teacher1', email: '<EMAIL>', role: 'teacher' }]];
        } else if (query.includes('SELECT role_id, role_name, description FROM roles')) {
          return [[
            { role_id: 1, role_name: 'admin', description: 'Administrator' },
            { role_id: 2, role_name: 'teacher', description: 'Teacher' },
            { role_id: 3, role_name: 'student', description: 'Student' }
          ]];
        }
        return [[]];
      });

      // Create mock request and response
      const req = mockRequest({}, {}, { id: 2 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.getAssignRole(req, res);

      // Assertions
      expect(res.render).toHaveBeenCalledTimes(1);
      expect(res.render).toHaveBeenCalledWith('admin/users/assign-role', expect.objectContaining({
        title: 'Assign Role',
        layout: 'admin',
        currentPage: 'users',
        user: expect.objectContaining({
          id: 2,
          username: 'teacher1',
          email: '<EMAIL>',
          role: 'teacher'
        }),
        roles: expect.arrayContaining([
          expect.objectContaining({ role_name: 'admin' }),
          expect.objectContaining({ role_name: 'teacher' }),
          expect.objectContaining({ role_name: 'student' })
        ]),
        currentRole: 'teacher'
      }));
    });

    it('should redirect if user not found', async () => {
      // Mock empty database response
      db.query.mockResolvedValue([[]]);

      // Create mock request and response
      const req = mockRequest({ flashError: null }, {}, { id: 999 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.getAssignRole(req, res);

      // Assertions
      expect(req.session.flashError).toBe('User not found');
      expect(res.redirect).toHaveBeenCalledWith('/admin/users');
    });

    it('should handle errors and render error page', async () => {
      // Mock database error
      db.query.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const req = mockRequest({}, {}, { id: 2 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.getAssignRole(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.render).toHaveBeenCalledWith('error', expect.objectContaining({
        title: 'Error',
        message: 'Failed to fetch user role data',
        error: expect.objectContaining({
          status: 500
        }),
        layout: 'admin'
      }));
    });
  });

  describe('updateRole', () => {
    it('should update user role and return success response', async () => {
      // Mock database responses
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id FROM users')) {
          return [[{ id: 2, role: 'teacher' }]];
        } else {
          return [{ affectedRows: 1 }];
        }
      });

      // Create mock request and response
      const req = mockRequest({}, { role: 'student' }, { id: 2 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.updateRole(req, res);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'User role updated successfully'
      });
    });

    it('should return error if role is missing', async () => {
      // Create mock request and response
      const req = mockRequest({}, {}, { id: 2 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.updateRole(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Role is required'
      });
    });

    it('should return error if user not found', async () => {
      // Mock empty database response
      db.query.mockResolvedValue([[]]);

      // Create mock request and response
      const req = mockRequest({}, { role: 'student' }, { id: 999 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.updateRole(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'User not found'
      });
    });

    it('should handle database errors', async () => {
      // Mock database error
      db.query.mockImplementation((query, params) => {
        if (query.includes('SELECT id FROM users')) {
          return [[{ id: 2, role: 'teacher' }]];
        } else {
          throw new Error('Database error');
        }
      });

      // Create mock request and response
      const req = mockRequest({}, { role: 'student' }, { id: 2 });
      const res = mockResponse();

      // Call the controller function
      await userRoleController.updateRole(req, res);

      // Assertions
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Failed to update user role'
      });
    });
  });
});
