const request = require('supertest');
const express = require('express');
const studentRoutes = require('../../routes/student-routes');
const studentController = require('../../controllers/student-controller');
const { mockSession, mockAuth } = require('../setup');

// Mock the student controller
jest.mock('../../controllers/student-controller', () => ({
  getDashboard: jest.fn((req, res) => res.status(200).send('Dashboard')),
  getActivity: jest.fn((req, res) => res.status(200).send('Activity'))
}));

// Mock the student instruction plan controller
jest.mock('../../controllers/student-instruction-plan-controller', () => ({
  getInstructionPlans: jest.fn((req, res) => res.status(200).send('Instruction Plans')),
  getInstructionPlanDetails: jest.fn((req, res) => res.status(200).send('Instruction Plan Details')),
  markPlanCompleted: jest.fn((req, res) => res.status(200).send('Plan Completed'))
}));

// Mock the authentication middleware
jest.mock('../../middleware/auth', () => ({
  checkAuthenticated: (req, res, next) => next(),
  checkStudent: (req, res, next) => next()
}));

describe('Student Routes', () => {
  let app;

  beforeEach(() => {
    // Create a new Express app for each test
    app = express();
    app.use(express.json());
    app.use(mockSession);
    app.use('/student', studentRoutes);
  });

  afterEach(() => {
    // Clear all mocks after each test
    jest.clearAllMocks();
  });

  describe('GET /student/dashboard', () => {
    it('should call the getDashboard controller', async () => {
      const response = await request(app).get('/student/dashboard');
      
      expect(response.status).toBe(200);
      expect(response.text).toBe('Dashboard');
      expect(studentController.getDashboard).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /student/activity', () => {
    it('should call the getActivity controller', async () => {
      const response = await request(app).get('/student/activity');
      
      expect(response.status).toBe(200);
      expect(response.text).toBe('Activity');
      expect(studentController.getActivity).toHaveBeenCalledTimes(1);
    });
  });

  describe('GET /student/exams', () => {
    it('should render the exams page', async () => {
      // Mock the render function
      const mockRender = jest.fn();
      app.use('/student/exams', (req, res) => {
        res.render = mockRender;
        studentRoutes.handle(req, res);
      });

      const response = await request(app).get('/student/exams');
      
      // Since we're mocking the render function, we can't check the response
      // But we can check if the render function was called with the correct arguments
      expect(mockRender).toHaveBeenCalledWith('student/exams/index', {
        title: 'My Exams',
        layout: 'student',
        currentPage: 'exams'
      });
    });
  });
});
