// Test setup file
const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// Create a test database connection
const createTestDbConnection = async () => {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.TEST_DB_NAME || 'exam_prep_platform_test',
    port: process.env.DB_PORT || 3306
  });
  
  return connection;
};

// Mock session middleware
const mockSession = (req, res, next) => {
  req.session = {
    userId: 1,
    userRole: 'admin',
    user: {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin'
    },
    flashSuccess: null,
    flashError: null
  };
  next();
};

// Mock authentication middleware
const mockAuth = (req, res, next) => {
  req.isAuthenticated = () => true;
  req.user = {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin'
  };
  next();
};

// Mock response object
const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.render = jest.fn().mockReturnValue(res);
  res.redirect = jest.fn().mockReturnValue(res);
  res.locals = {};
  return res;
};

// Mock request object
const mockRequest = (sessionData = {}, body = {}, params = {}, query = {}) => {
  return {
    session: {
      userId: 1,
      userRole: 'admin',
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin'
      },
      ...sessionData
    },
    body,
    params,
    query,
    ip: '127.0.0.1',
    isApiRequest: false
  };
};

module.exports = {
  createTestDbConnection,
  mockSession,
  mockAuth,
  mockResponse,
  mockRequest
};
