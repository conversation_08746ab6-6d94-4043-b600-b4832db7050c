const db = require('./config/database');
const fs = require('fs');
const path = require('path');

async function updateQuestionType() {
    try {
        console.log('Updating question_type enum to include fill_up...');
        
        // Read the SQL file
        const sql = fs.readFileSync(
            path.join(__dirname, 'database/add-fillup-type.sql'), 
            'utf8'
        );
        
        // Execute the SQL
        await db.query(sql);
        
        console.log('Successfully updated question_type enum to include fill_up!');
        process.exit(0);
    } catch (error) {
        console.error('Error updating question_type enum:', error);
        process.exit(1);
    }
}

// Run the function
updateQuestionType();
