const db = require('./config/database');

async function updateTeacherTables() {
  try {
    // Add class_section_id column to teacher_lectures table if it doesn't exist
    const [lectureColumns] = await db.query(`
      SHOW COLUMNS FROM teacher_lectures WHERE Field = 'class_section_id'
    `);
    
    if (lectureColumns.length === 0) {
      await db.query(`
        ALTER TABLE teacher_lectures
        ADD COLUMN class_section_id INT,
        ADD FOREIGN KEY (class_section_id) REFERENCES class_sections(id)
      `);
      console.log('Added class_section_id column to teacher_lectures table');
    } else {
      console.log('class_section_id column already exists in teacher_lectures table');
    }
    
    // Add class_section_id column to teacher_practicals table if it doesn't exist
    const [practicalColumns] = await db.query(`
      SHOW COLUMNS FROM teacher_practicals WHERE Field = 'class_section_id'
    `);
    
    if (practicalColumns.length === 0) {
      await db.query(`
        ALTER TABLE teacher_practicals
        ADD COLUMN class_section_id INT,
        ADD FOREIGN KEY (class_section_id) REFERENCES class_sections(id)
      `);
      console.log('Added class_section_id column to teacher_practicals table');
    } else {
      console.log('class_section_id column already exists in teacher_practicals table');
    }
    
    // Add class_section_id column to student_practical_records table if it doesn't exist
    const [recordColumns] = await db.query(`
      SHOW COLUMNS FROM student_practical_records WHERE Field = 'class_section_id'
    `);
    
    if (recordColumns.length === 0) {
      await db.query(`
        ALTER TABLE student_practical_records
        ADD COLUMN class_section_id INT,
        ADD FOREIGN KEY (class_section_id) REFERENCES class_sections(id)
      `);
      console.log('Added class_section_id column to student_practical_records table');
    } else {
      console.log('class_section_id column already exists in student_practical_records table');
    }
    
    // Update existing records to associate with class sections
    // First, get all class sections
    const [classSections] = await db.query(`
      SELECT cs.id, cs.class_id, cs.trade_id, cs.section,
             c.name as class_name, t.name as trade_name,
             CONCAT('Class ', c.name, '-', cs.section) as general_name,
             CONCAT('Class ', c.name, '-', cs.section, ' (', t.name, ')') as full_name
      FROM class_sections cs
      JOIN classes c ON cs.class_id = c.id
      LEFT JOIN trades t ON cs.trade_id = t.id
    `);
    
    // Update teacher_lectures
    const [lectures] = await db.query(`
      SELECT id, class_name FROM teacher_lectures WHERE class_section_id IS NULL
    `);
    
    for (const lecture of lectures) {
      // Try to find a matching class section
      const matchingSection = classSections.find(cs => 
        lecture.class_name === cs.general_name || lecture.class_name === cs.full_name
      );
      
      if (matchingSection) {
        await db.query(`
          UPDATE teacher_lectures SET class_section_id = ? WHERE id = ?
        `, [matchingSection.id, lecture.id]);
        console.log(`Updated lecture ${lecture.id} with class section ${matchingSection.id}`);
      } else {
        console.log(`Could not find matching class section for lecture ${lecture.id}: ${lecture.class_name}`);
      }
    }
    
    // Update teacher_practicals
    const [practicals] = await db.query(`
      SELECT id, class_name FROM teacher_practicals WHERE class_section_id IS NULL
    `);
    
    for (const practical of practicals) {
      // Try to find a matching class section
      const matchingSection = classSections.find(cs => 
        practical.class_name === cs.general_name || practical.class_name === cs.full_name
      );
      
      if (matchingSection) {
        await db.query(`
          UPDATE teacher_practicals SET class_section_id = ? WHERE id = ?
        `, [matchingSection.id, practical.id]);
        console.log(`Updated practical ${practical.id} with class section ${matchingSection.id}`);
      } else {
        console.log(`Could not find matching class section for practical ${practical.id}: ${practical.class_name}`);
      }
    }
    
    // Update student_practical_records
    const [records] = await db.query(`
      SELECT id, class_name FROM student_practical_records WHERE class_section_id IS NULL
    `);
    
    for (const record of records) {
      // Try to find a matching class section
      const matchingSection = classSections.find(cs => 
        record.class_name === cs.general_name || record.class_name === cs.full_name
      );
      
      if (matchingSection) {
        await db.query(`
          UPDATE student_practical_records SET class_section_id = ? WHERE id = ?
        `, [matchingSection.id, record.id]);
        console.log(`Updated record ${record.id} with class section ${matchingSection.id}`);
      } else {
        console.log(`Could not find matching class section for record ${record.id}: ${record.class_name}`);
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating teacher tables:', error);
    process.exit(1);
  }
}

updateTeacherTables();
