const bcrypt = require('bcrypt');
const mysql = require('mysql2/promise');

async function updatePassword() {
  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'exam_prep_platform'
  });

  try {
    // Hash the password
    const password = 'Merit123#';
    const saltRounds = 10;
    const hash = await bcrypt.hash(password, saltRounds);

    // Update the user's password
    const [result] = await connection.execute(
      'UPDATE users SET password = ? WHERE email = ?',
      [hash, '<EMAIL>']
    );

    console.log(`Password <NAME_EMAIL>`);
    console.log(`Rows affected: ${result.affectedRows}`);
  } catch (error) {
    console.error('Error updating password:', error);
  } finally {
    // Close the connection
    await connection.end();
  }
}

updatePassword();
