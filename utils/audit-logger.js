/**
 * Audit Logger Utility
 *
 * This utility provides functions for comprehensive audit logging of:
 * - Database operations
 * - JavaScript variables
 * - User actions
 * - System events
 */

const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Create a separate pool for audit logging to avoid circular dependencies
let auditPool;
try {
    auditPool = mysql.createPool({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        port: process.env.DB_PORT,
        waitForConnections: true,
        connectionLimit: 2,
        queueLimit: 0
    });
    console.log('Audit logger database pool created');
} catch (error) {
    console.error('Failed to create audit logger database pool:', error);
}

// Ensure audit log directory exists
const auditLogDir = path.join(__dirname, '../logs/audit');
const ensureLogDir = async () => {
    try {
        await fs.mkdir(auditLogDir, { recursive: true });
    } catch (error) {
        console.error('Failed to create audit log directory:', error);
    }
};

// Initialize log directory
ensureLogDir();

/**
 * Log database operation for audit purposes
 * @param {string} query - SQL query
 * @param {Array} params - Query parameters
 * @param {number} duration - Query execution time in ms
 * @param {Object} result - Query result (optional)
 * @param {Error} error - Error object if query failed (optional)
 * @param {Object} user - User information (optional)
 */
const logDatabaseOperation = async (query, params, duration, result, error = null, user = null) => {
    try {
        // Don't log audit logging operations to avoid infinite recursion
        if (query.includes('INSERT INTO logs') || query.includes('INSERT INTO audit_logs')) {
            return;
        }

        // Determine query type
        const queryType = query.trim().split(' ')[0].toUpperCase();

        // Get the table name (simple extraction, might not work for complex queries)
        let tableName = 'unknown';
        if (queryType === 'SELECT') {
            const fromMatch = query.match(/FROM\\s+[`'\"]?([\\w_]+)[`'\"]?/i);
            if (fromMatch && fromMatch[1]) {
                tableName = fromMatch[1];
            }
        } else if (queryType === 'INSERT') {
            const intoMatch = query.match(/INTO\\s+[`'\"]?([\\w_]+)[`'\"]?/i);
            if (intoMatch && intoMatch[1]) {
                tableName = intoMatch[1];
            }
        } else if (queryType === 'UPDATE') {
            const updateMatch = query.match(/UPDATE\\s+[`'\"]?([\\w_]+)[`'\"]?/i);
            if (updateMatch && updateMatch[1]) {
                tableName = updateMatch[1];
            }
        } else if (queryType === 'DELETE') {
            const fromMatch = query.match(/FROM\\s+[`'\"]?([\\w_]+)[`'\"]?/i);
            if (fromMatch && fromMatch[1]) {
                tableName = fromMatch[1];
            }
        }

        // Determine if this is a sensitive operation (modify data)
        const isSensitiveOperation = ['INSERT', 'UPDATE', 'DELETE', 'TRUNCATE', 'DROP', 'ALTER'].includes(queryType);

        // Determine log level based on operation type and result
        let level = isSensitiveOperation ? 'warning' : 'info';
        if (error) {
            level = 'error';
        } else if (duration > 1000) {
            level = 'warning';
        }

        // Create audit log entry
        const auditEntry = {
            timestamp: new Date().toISOString(),
            operation: `DB_${queryType}`,
            target: tableName,
            user: user ? {
                id: user.id || null,
                username: user.username || null,
                role: user.role || null
            } : null,
            details: {
                query: query,
                params: params || [],
                duration: duration,
                error: error ? {
                    message: error.message,
                    code: error.code
                } : null,
                // Include affected rows count for data modification operations
                affectedRows: result && result.affectedRows ? result.affectedRows : null,
                // Include row count for SELECT operations
                rowCount: result && Array.isArray(result) ? result.length : null
            }
        };

        // Write to audit log file
        const logFileName = `db_audit_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.log`;
        const logFilePath = path.join(auditLogDir, logFileName);

        await fs.appendFile(
            logFilePath,
            JSON.stringify(auditEntry) + '\\n'
        );

        // Also log to database if it's a sensitive operation or there was an error
        if (isSensitiveOperation || error || duration > 1000) {
            try {
                if (auditPool) {
                    await auditPool.query(
                    `INSERT INTO logs (timestamp, level, category, operation, details, status, error_message, user_id)
                     VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        level,
                        'database',
                        `${queryType} Operation`,
                        `${queryType} on ${tableName}${error ? ' (FAILED)' : ''}`,
                        `Query: ${query}\\nParams: ${JSON.stringify(params || [])}\\nDuration: ${duration}ms${result && result.affectedRows ? '\\nAffected Rows: ' + result.affectedRows : ''}`,
                        error ? 'error' : (duration > 1000 ? 'warning' : 'success'),
                        error ? error.message : null,
                        user ? user.id : null
                    ]
                    );
                }
            } catch (dbError) {
                console.error('Failed to log database operation to logs table:', dbError);
            }
        }
    } catch (logError) {
        console.error('Failed to log database operation for audit:', logError);
    }
};

/**
 * Log JavaScript variable for audit purposes
 * @param {string} variableName - Name of the variable
 * @param {any} variableValue - Value of the variable
 * @param {string} context - Context where the variable is used
 * @param {Object} user - User information (optional)
 * @param {string} level - Log level (default: 'info')
 */
const logJavaScriptVariable = async (variableName, variableValue, context, user = null, level = 'info') => {
    try {
        // Create audit log entry
        const auditEntry = {
            timestamp: new Date().toISOString(),
            operation: 'JS_VARIABLE',
            target: variableName,
            context: context,
            user: user ? {
                id: user.id || null,
                username: user.username || null,
                role: user.role || null
            } : null,
            details: {
                value: typeof variableValue === 'object' ? JSON.stringify(variableValue) : String(variableValue),
                type: typeof variableValue
            }
        };

        // Write to audit log file
        const logFileName = `js_audit_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.log`;
        const logFilePath = path.join(auditLogDir, logFileName);

        await fs.appendFile(
            logFilePath,
            JSON.stringify(auditEntry) + '\\n'
        );

        // Always log to database for better audit trail
        // But identify sensitive data for masking
        const isSensitiveData = variableName.toLowerCase().includes('password') ||
                               variableName.toLowerCase().includes('token') ||
                               variableName.toLowerCase().includes('secret');

        // Always log to database
            try {
                // Mask sensitive values in the log
                let valueToLog = variableValue;
                if (variableName.toLowerCase().includes('password') ||
                    variableName.toLowerCase().includes('token') ||
                    variableName.toLowerCase().includes('secret')) {
                    valueToLog = '********';
                }

                if (auditPool) {
                    await auditPool.query(
                    `INSERT INTO logs (timestamp, level, category, operation, details, status, user_id)
                     VALUES (NOW(), ?, ?, ?, ?, ?, ?)`,
                    [
                        level,
                        'javascript',
                        'Variable Access',
                        `Access to ${variableName}`,
                        `Context: ${context}\\nVariable: ${variableName}\\nType: ${typeof variableValue}\\nValue: ${typeof valueToLog === 'object' ? JSON.stringify(valueToLog) : String(valueToLog)}`,
                        'success',
                        user ? user.id : null
                    ]
                    );
                }
            } catch (dbError) {
                console.error('Failed to log JavaScript variable to logs table:', dbError);
            }
        }
    } catch (logError) {
        console.error('Failed to log JavaScript variable for audit:', logError);
    }
};

/**
 * Log user action for audit purposes
 * @param {Object} req - Express request object
 * @param {string} action - Action performed
 * @param {string} target - Target of the action
 * @param {Object} details - Additional details
 * @param {string} level - Log level (default: 'info')
 */
const logUserAction = async (req, action, target, details = {}, level = 'info') => {
    try {
        const user = req.session && req.session.userId ? {
            id: req.session.userId,
            username: req.session.username || 'unknown',
            role: req.session.userRole || 'unknown'
        } : null;

        // Create audit log entry
        const auditEntry = {
            timestamp: new Date().toISOString(),
            operation: 'USER_ACTION',
            action: action,
            target: target,
            user: user,
            request: {
                method: req.method,
                path: req.path,
                ip: req.ip,
                userAgent: req.get('user-agent')
            },
            details: details
        };

        // Write to audit log file
        const logFileName = `user_audit_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.log`;
        const logFilePath = path.join(auditLogDir, logFileName);

        await fs.appendFile(
            logFilePath,
            JSON.stringify(auditEntry) + '\\n'
        );

        // Also log to database
        try {
            if (auditPool) {
                await auditPool.query(
                `INSERT INTO logs (timestamp, level, category, operation, details, status, user_id, ip_address, request_method, request_uri, user_agent)
                 VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    level,
                    'user',
                    action,
                    `${action} on ${target}`,
                    JSON.stringify(details),
                    'success',
                    user ? user.id : null,
                    req.ip,
                    req.method,
                    req.originalUrl,
                    req.get('user-agent')
                ]
                );
            }
        } catch (dbError) {
            console.error('Failed to log user action to logs table:', dbError);
        }
    } catch (logError) {
        console.error('Failed to log user action for audit:', logError);
    }
};

module.exports = {
    logDatabaseOperation,
    logJavaScriptVariable,
    logUserAction
};
