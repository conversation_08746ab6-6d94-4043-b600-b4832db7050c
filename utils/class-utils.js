const db = require('../config/database');

/**
 * Get all classes with their trades and sections
 * @returns {Promise<Array>} Array of classes with trades and sections
 */
async function getAllClassesWithSections() {
  try {
    const [classes] = await db.query(`
      SELECT c.id, CONCAT(c.grade, ' ', t.name) as name, c.grade as description
      FROM classes c
      LEFT JOIN trades t ON c.trade_id = t.id
      ORDER BY c.grade, t.name
    `);

    const [trades] = await db.query(`
      SELECT t.id, t.name, t.description
      FROM trades t
      ORDER BY t.name
    `);

    // Check if class_sections table exists
    const [classSecExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_sections'
    `);

    let sections = [];

    if (classSecExists[0].table_exists > 0) {
      // Use the class_sections table if it exists
      [sections] = await db.query(`
        SELECT cs.id, cs.class_id, cs.trade_id, cs.section,
               CONCAT(c.grade, ' ', t.name) as class_name, t.name as trade_name
        FROM class_sections cs
        JOIN classes c ON cs.class_id = c.id
        LEFT JOIN trades t ON cs.trade_id = t.id
        ORDER BY c.grade, t.name, cs.section
      `);
    } else {
      // If class_sections doesn't exist, use the classrooms table
      [sections] = await db.query(`
        SELECT
          cr.id,
          cr.class_id,
          cr.trade_id,
          cr.section,
          CONCAT(c.grade, ' ', t.name) as class_name,
          t.name as trade_name
        FROM classrooms cr
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        WHERE cr.is_active = TRUE
        GROUP BY cr.class_id, cr.trade_id, cr.section
        ORDER BY c.grade, t.name, cr.section
      `);
    }

    // Group sections by class and trade
    const classesWithSections = classes.map(cls => {
      const classSections = sections.filter(s => s.class_id === cls.id);

      // Group by trade
      const tradeGroups = {};
      classSections.forEach(section => {
        const tradeId = section.trade_id;
        if (!tradeGroups[tradeId]) {
          tradeGroups[tradeId] = {
            id: tradeId,
            name: section.trade_name,
            sections: []
          };
        }
        tradeGroups[tradeId].sections.push({
          id: section.id,
          section: section.section,
          displayName: `${cls.name} ${section.section}`
        });
      });

      return {
        id: cls.id,
        name: cls.name,
        description: cls.description,
        trades: Object.values(tradeGroups)
      };
    });

    return classesWithSections;
  } catch (error) {
    console.error('Error getting classes with sections:', error);
    throw error;
  }
}

/**
 * Get all class sections as a flat list
 * @returns {Promise<Array>} Array of class sections
 */
async function getAllClassSections() {
  try {
    // Check if class_sections table exists
    const [classSecExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_sections'
    `);

    if (classSecExists[0].table_exists > 0) {
      // Use the class_sections table if it exists
      const [sections] = await db.query(`
        SELECT cs.id, cs.class_id, cs.trade_id, cs.section,
               CONCAT(c.grade, ' ', t.name, ' ', cs.section) as class_name,
               t.name as trade_name,
               CONCAT(c.grade, ' ', t.name, ' ', cs.section) as display_name
        FROM class_sections cs
        JOIN classes c ON cs.class_id = c.id
        LEFT JOIN trades t ON cs.trade_id = t.id
        ORDER BY c.grade, t.name, cs.section
      `);

      return sections;
    } else {
      // If class_sections doesn't exist, use the classrooms table
      const [sections] = await db.query(`
        SELECT
          cr.id,
          cr.class_id,
          cr.trade_id,
          cr.section,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) as class_name,
          t.name as trade_name,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) as display_name
        FROM classrooms cr
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        WHERE cr.is_active = TRUE
        GROUP BY cr.class_id, cr.trade_id, cr.section
        ORDER BY c.grade, t.name, cr.section
      `);

      return sections;
    }
  } catch (error) {
    console.error('Error getting class sections:', error);
    throw error;
  }
}

/**
 * Get class section by ID
 * @param {number} id - Class section ID
 * @returns {Promise<Object>} Class section object
 */
async function getClassSectionById(id) {
  try {
    // Check if class_sections table exists
    const [classSecExists] = await db.query(`
      SELECT COUNT(*) as table_exists
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'class_sections'
    `);

    if (classSecExists[0].table_exists > 0) {
      // Use the class_sections table if it exists
      const [sections] = await db.query(`
        SELECT cs.id, cs.class_id, cs.trade_id, cs.section,
               CONCAT(c.grade, ' ', t.name, ' ', cs.section) as class_name,
               t.name as trade_name,
               CONCAT(c.grade, ' ', t.name, ' ', cs.section) as display_name
        FROM class_sections cs
        JOIN classes c ON cs.class_id = c.id
        LEFT JOIN trades t ON cs.trade_id = t.id
        WHERE cs.id = ?
      `, [id]);

      return sections[0] || null;
    } else {
      // If class_sections doesn't exist, use the classrooms table
      const [sections] = await db.query(`
        SELECT
          cr.id,
          cr.class_id,
          cr.trade_id,
          cr.section,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) as class_name,
          t.name as trade_name,
          CONCAT(c.grade, ' ', t.name, ' ', cr.section) as display_name
        FROM classrooms cr
        JOIN classes c ON cr.class_id = c.id
        LEFT JOIN trades t ON cr.trade_id = t.id
        WHERE cr.id = ? AND cr.is_active = TRUE
      `, [id]);

      return sections[0] || null;
    }
  } catch (error) {
    console.error('Error getting class section by ID:', error);
    throw error;
  }
}

module.exports = {
  getAllClassesWithSections,
  getAllClassSections,
  getClassSectionById
};
