/**
 * Format a date in DD-MMM-YYYY format (e.g., 29-Mar-2025)
 * @param {Date|string} date - The date to format
 * @returns {string} The formatted date string
 */
function formatDate(date) {
    if (!date) return '';

    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return '';

    const day = String(dateObj.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[dateObj.getMonth()];
    const year = dateObj.getFullYear();

    return `${day}-${month}-${year}`;
}

/**
 * Format a date and time in DD-MMM-YYYY HH:MM:SS format (e.g., 29-Mar-2025 14:30:45)
 * @param {Date|string} date - The date to format
 * @returns {string} The formatted date and time string
 */
function formatDateTime(date) {
    if (!date) return '';

    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return '';

    const day = String(dateObj.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[dateObj.getMonth()];
    const year = dateObj.getFullYear();

    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');

    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}

module.exports = {
    formatDate,
    formatDateTime
};
