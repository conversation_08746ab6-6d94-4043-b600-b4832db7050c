/**
 * Utility functions for date and time formatting
 */

// Format a date as DD-MM-YYYY
exports.formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  
  return `${day}-${month}-${year}`;
};

// Format a time as HH:MM AM/PM
exports.formatTime = (time) => {
  if (!time) return '';
  
  // If time is already a string in HH:MM:SS format, parse it
  if (typeof time === 'string') {
    const [hours, minutes] = time.split(':');
    const h = parseInt(hours, 10);
    const m = parseInt(minutes, 10);
    
    const ampm = h >= 12 ? 'PM' : 'AM';
    const hour = h % 12 || 12;
    
    return `${hour}:${String(m).padStart(2, '0')} ${ampm}`;
  }
  
  // If time is a Date object
  const d = new Date(time);
  const hours = d.getHours();
  const minutes = d.getMinutes();
  
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const hour = hours % 12 || 12;
  
  return `${hour}:${String(minutes).padStart(2, '0')} ${ampm}`;
};

// Format a date as YYYY-MM-DD (for input fields)
exports.formatDateForInput = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  
  return `${year}-${month}-${day}`;
};

// Format a time as HH:MM (for input fields)
exports.formatTimeForInput = (time) => {
  if (!time) return '';
  
  // If time is already a string in HH:MM:SS format, parse it
  if (typeof time === 'string') {
    return time.substring(0, 5);
  }
  
  // If time is a Date object
  const d = new Date(time);
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  return `${hours}:${minutes}`;
};

// Get day of week from date
exports.getDayOfWeek = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  
  return days[d.getDay()];
};

// Get date range for a week
exports.getWeekDateRange = (date) => {
  const d = date ? new Date(date) : new Date();
  const day = d.getDay();
  
  // Calculate the start of the week (Monday)
  const startDate = new Date(d);
  startDate.setDate(d.getDate() - day + (day === 0 ? -6 : 1));
  
  // Calculate the end of the week (Sunday)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);
  
  return {
    start: startDate,
    end: endDate
  };
};

// Format a date range as "DD MMM - DD MMM YYYY"
exports.formatDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return '';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  const startDay = start.getDate();
  const startMonth = months[start.getMonth()];
  const endDay = end.getDate();
  const endMonth = months[end.getMonth()];
  const endYear = end.getFullYear();
  
  return `${startDay} ${startMonth} - ${endDay} ${endMonth} ${endYear}`;
};
