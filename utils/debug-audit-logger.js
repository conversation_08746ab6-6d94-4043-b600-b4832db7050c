/**
 * Debug Audit Logger Utility
 *
 * This utility provides functions for comprehensive audit logging of:
 * - JavaScript variables
 * - User actions
 *
 * With additional debugging information to troubleshoot database logging issues.
 */

const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Create a separate pool for audit logging to avoid circular dependencies
let auditPool;
try {
    auditPool = mysql.createPool({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'exam_prep_platform',
        port: process.env.DB_PORT || 3306,
        waitForConnections: true,
        connectionLimit: 2,
        queueLimit: 0
    });
    console.log('Debug audit logger database pool created with config:', {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        database: process.env.DB_NAME || 'exam_prep_platform',
        port: process.env.DB_PORT || 3306
    });

    // Test the database connection
    auditPool.query('SELECT 1 + 1 AS result')
        .then(([rows]) => {
            console.log('Debug audit logger database connection test successful:', rows[0].result);
        })
        .catch(error => {
            console.error('Debug audit logger database connection test failed:', error);
        });
} catch (error) {
    console.error('Failed to create debug audit logger database pool:', error);
}

// Ensure audit log directory exists
const auditLogDir = path.join(__dirname, '../logs/audit');
const ensureLogDir = async () => {
    try {
        await fs.mkdir(auditLogDir, { recursive: true });
    } catch (error) {
        console.error('Failed to create audit log directory:', error);
    }
};

// Initialize log directory
ensureLogDir();

/**
 * Log JavaScript variable for audit purposes with debug information
 * @param {string} variableName - Name of the variable
 * @param {any} variableValue - Value of the variable
 * @param {string} context - Context where the variable is used
 * @param {Object} user - User information (optional)
 * @param {string} level - Log level (default: 'info')
 * @param {string} route - Route information (optional)
 */
const logJavaScriptVariable = async (variableName, variableValue, context, user = null, level = 'info', route = 'unknown') => {
    console.log('Debug audit logger: logJavaScriptVariable called with:', {
        variableName,
        valueType: typeof variableValue,
        context,
        user: user ? { id: user.id, username: user.username } : null,
        level,
        route
    });

    try {
        // Create audit log entry
        const auditEntry = {
            timestamp: new Date().toISOString(),
            operation: 'JS_VARIABLE',
            target: variableName,
            context: context,
            route: route,
            user: user ? {
                id: user.id || null,
                username: user.username || null,
                role: user.role || null
            } : null,
            details: {
                value: typeof variableValue === 'object' ? JSON.stringify(variableValue) : String(variableValue),
                type: typeof variableValue
            }
        };

        // Write to audit log file
        const logFileName = `js_audit_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.log`;
        const logFilePath = path.join(auditLogDir, logFileName);

        await fs.appendFile(
            logFilePath,
            JSON.stringify(auditEntry) + '\\n'
        );

        console.log('Debug audit logger: Successfully wrote to file:', logFilePath);

        // Check if this is sensitive data for masking
        const isSensitiveData = variableName.toLowerCase().includes('password') ||
                               variableName.toLowerCase().includes('token') ||
                               variableName.toLowerCase().includes('secret');

        // Always log to database
        try {
            // Mask sensitive values in the log
            let valueToLog = variableValue;
            if (isSensitiveData) {
                valueToLog = '********';
            }

            console.log('Debug audit logger: Attempting to log to database, auditPool exists:', !!auditPool);

            if (auditPool) {
                const details = `Route: ${route}\\nContext: ${context}\\nVariable: ${variableName}\\nType: ${typeof variableValue}\\nValue: ${typeof valueToLog === 'object' ? JSON.stringify(valueToLog) : String(valueToLog)}\\nTimestamp: ${new Date().toISOString()}`;

                console.log('Debug audit logger: Preparing to insert with params:', {
                    level,
                    category: 'javascript',
                    operation: `Access to ${variableName}`,
                    detailsPreview: details.substring(0, 50) + '...',
                    status: 'success',
                    userId: user ? user.id : null
                });

                // Use a direct connection to avoid any issues with the pool
                const connection = await auditPool.getConnection();

                try {
                    await connection.query(
                        `INSERT INTO logs (timestamp, level, category, operation, details, status, user_id)
                         VALUES (NOW(), ?, ?, ?, ?, ?, ?)`,
                        [
                            level,
                            'javascript',
                            'Variable Access',
                            `Access to ${variableName}`,
                            details,
                            'success',
                            (user && user.id && !isNaN(parseInt(user.id))) ? parseInt(user.id) : null
                        ]
                    );

                    console.log('Debug audit logger: Successfully inserted into database');
                } finally {
                    connection.release();
                }
            } else {
                console.error('Debug audit logger: Cannot log to database - auditPool is not available');
            }
        } catch (dbError) {
            console.error('Debug audit logger: Failed to log to database:', dbError);
        }
    } catch (logError) {
        console.error('Debug audit logger: Failed to log JavaScript variable:', logError);
    }
};

/**
 * Log user action for audit purposes with debug information
 * @param {Object} req - Express request object
 * @param {string} action - Action performed
 * @param {string} target - Target of the action
 * @param {Object} details - Additional details
 * @param {string} level - Log level (default: 'info')
 */
const logUserAction = async (req, action, target, details = {}, level = 'info') => {
    console.log('Debug audit logger: logUserAction called with:', {
        action,
        target,
        level,
        url: req.originalUrl
    });

    try {
        const user = req.session && req.session.userId ? {
            id: req.session.userId,
            username: req.session.username || 'unknown',
            role: req.session.userRole || 'unknown'
        } : null;

        // Get route information
        const route = req.originalUrl || req.url || 'unknown';

        // Create audit log entry
        const auditEntry = {
            timestamp: new Date().toISOString(),
            operation: 'USER_ACTION',
            action: action,
            target: target,
            route: route,
            user: user,
            request: {
                method: req.method,
                path: req.path,
                ip: req.ip,
                userAgent: req.get('user-agent')
            },
            details: details
        };

        // Write to audit log file
        const logFileName = `user_audit_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.log`;
        const logFilePath = path.join(auditLogDir, logFileName);

        await fs.appendFile(
            logFilePath,
            JSON.stringify(auditEntry) + '\\n'
        );

        console.log('Debug audit logger: Successfully wrote user action to file:', logFilePath);

        // Also log to database
        try {
            console.log('Debug audit logger: Attempting to log user action to database, auditPool exists:', !!auditPool);

            if (auditPool) {
                const detailsStr = `Route: ${route}\\nDetails: ${JSON.stringify(details)}\\nTimestamp: ${new Date().toISOString()}`;

                console.log('Debug audit logger: Preparing to insert user action with params:', {
                    level,
                    category: 'user',
                    action,
                    target,
                    detailsPreview: detailsStr.substring(0, 50) + '...',
                    status: 'success',
                    userId: user ? user.id : null
                });

                // Use a direct connection to avoid any issues with the pool
                const connection = await auditPool.getConnection();

                try {
                    await connection.query(
                        `INSERT INTO logs (timestamp, level, category, operation, details, status, user_id, ip_address, request_method, request_uri, user_agent)
                         VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            level,
                            'user',
                            action,
                            `${action} on ${target}`,
                            detailsStr,
                            'success',
                            (user && user.id && !isNaN(parseInt(user.id))) ? parseInt(user.id) : null,
                            req.ip,
                            req.method,
                            req.originalUrl,
                            req.get('user-agent')
                        ]
                    );

                    console.log('Debug audit logger: Successfully inserted user action into database');
                } finally {
                    connection.release();
                }
            } else {
                console.error('Debug audit logger: Cannot log user action to database - auditPool is not available');
            }
        } catch (dbError) {
            console.error('Debug audit logger: Failed to log user action to database:', dbError);
        }
    } catch (logError) {
        console.error('Debug audit logger: Failed to log user action:', logError);
    }
};

module.exports = {
    logJavaScriptVariable,
    logUserAction
};
