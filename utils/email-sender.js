const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');
const ejs = require('ejs');

// Create a transporter
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
    }
});

/**
 * Send an email with a report attachment
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email address
 * @param {string} options.subject - Email subject
 * @param {string} options.text - Plain text email body
 * @param {string} options.html - HTML email body
 * @param {string} options.attachmentPath - Path to the attachment
 * @param {string} options.attachmentName - Name of the attachment
 * @returns {Promise<Object>} - Nodemailer response
 */
async function sendEmail(options) {
    const { to, subject, text, html, attachmentPath, attachmentName } = options;
    
    // Prepare email options
    const mailOptions = {
        from: process.env.SMTP_FROM || 'Exam Prep Platform <<EMAIL>>',
        to,
        subject,
        text,
        html
    };
    
    // Add attachment if provided
    if (attachmentPath) {
        mailOptions.attachments = [
            {
                filename: attachmentName || path.basename(attachmentPath),
                path: attachmentPath
            }
        ];
    }
    
    // Send email
    return transporter.sendMail(mailOptions);
}

/**
 * Send a report via email
 * @param {Object} options - Report email options
 * @param {string} options.to - Recipient email address
 * @param {Object} options.report - Report details
 * @param {string} options.report.name - Report name
 * @param {string} options.report.type - Report type
 * @param {string} options.report.format - Report format
 * @param {string} options.report.filePath - Path to the report file
 * @param {string} options.userName - Name of the user receiving the report
 * @returns {Promise<Object>} - Nodemailer response
 */
async function sendReportEmail(options) {
    const { to, report, userName } = options;
    
    // Load email template
    const templatePath = path.join(__dirname, '../views/emails/report_email.ejs');
    let templateContent;
    
    try {
        templateContent = await fs.readFile(templatePath, 'utf8');
    } catch (error) {
        // Use default template if file doesn't exist
        templateContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
                <h2 style="color: #3b82f6;">Your Report is Ready</h2>
                <p>Hello <%= userName %>,</p>
                <p>Your requested report "<%= report.name %>" is ready and attached to this email.</p>
                <p>Report Details:</p>
                <ul>
                    <li><strong>Report Type:</strong> <%= report.type %></li>
                    <li><strong>Format:</strong> <%= report.format.toUpperCase() %></li>
                    <li><strong>Generated On:</strong> <%= new Date().toLocaleString() %></li>
                </ul>
                <p>If you have any questions about this report, please contact the administrator.</p>
                <p>Thank you for using the Exam Prep Platform!</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="font-size: 12px; color: #666;">This is an automated email. Please do not reply to this message.</p>
            </div>
        `;
    }
    
    // Render the template
    const html = ejs.render(templateContent, { 
        userName: userName || 'User',
        report,
        date: new Date().toLocaleString()
    });
    
    // Send email
    return sendEmail({
        to,
        subject: `Your Report: ${report.name}`,
        text: `Your report "${report.name}" is ready and attached to this email.`,
        html,
        attachmentPath: report.filePath,
        attachmentName: path.basename(report.filePath)
    });
}

module.exports = {
    sendEmail,
    sendReportEmail
};
