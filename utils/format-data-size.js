/**
 * Format bytes to human-readable size
 * @param {number} bytes - The size in bytes
 * @param {number} decimals - Number of decimal places to show
 * @returns {string} Formatted size with unit
 */
function formatDataSize(bytes, decimals = 2) {
    if (bytes === null || bytes === undefined || isNaN(bytes)) {
        return 'N/A';
    }
    
    bytes = parseInt(bytes);
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

module.exports = formatDataSize;
