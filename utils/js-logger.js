/**
 * JavaScript Value Logger
 * 
 * This module provides a function to log JavaScript values to the database
 * for debugging and auditing purposes.
 */

const mysql = require('mysql2/promise');
const config = require('../config/database');

// Create database connection pool
let jsLoggerPool;
try {
    jsLoggerPool = mysql.createPool({
        host: config.host,
        user: config.user,
        password: config.password,
        database: config.database,
        port: config.port,
        waitForConnections: true,
        connectionLimit: 5,
        queueLimit: 0
    });
    console.log('JS Logger database pool created with config:', {
        host: config.host,
        user: config.user,
        database: config.database,
        port: config.port
    });
} catch (error) {
    console.error('Failed to create JS Logger database pool:', error);
}

/**
 * Log JavaScript values to the database
 * 
 * @param {string} variableName - Name of the variable
 * @param {*} variableValue - Value of the variable
 * @param {Object} options - Additional options
 * @param {string} options.route - Current route
 * @param {string} options.context - Context (e.g., controller, view)
 * @param {Object} options.user - User information
 * @param {Object} options.req - Express request object
 * @returns {Promise} - Promise that resolves when the log is saved
 */
async function logjsvalues(variableName, variableValue, options = {}) {
    try {
        const { route = 'unknown', context = 'unknown', user, req } = options;
        
        // Create a safe copy of the value for logging
        let valueToLog = variableValue;
        let valueType = typeof variableValue;
        
        // Convert objects and arrays to strings
        if (valueType === 'object') {
            try {
                valueToLog = JSON.stringify(variableValue);
            } catch (error) {
                valueToLog = '[Object conversion error]';
                console.error('Error converting object to string:', error);
            }
        } else {
            valueToLog = String(variableValue);
        }
        
        // Truncate very long values
        if (valueToLog && valueToLog.length > 10000) {
            valueToLog = valueToLog.substring(0, 10000) + '... [truncated]';
        }
        
        // Log to console for immediate feedback
        console.log(`[JS-LOGGER] ${route} | ${context} | ${variableName} (${valueType}):`, 
            valueType === 'object' ? '[Object in DB]' : valueToLog);
        
        // Save to database
        if (jsLoggerPool) {
            await jsLoggerPool.query(
                `INSERT INTO jsvalues (
                    route, 
                    context, 
                    variable_name, 
                    variable_type, 
                    variable_value, 
                    user_id, 
                    ip_address, 
                    user_agent
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    route,
                    context,
                    variableName,
                    valueType,
                    valueToLog,
                    user && typeof user.id === 'number' ? user.id : null,
                    req ? req.ip : null,
                    req ? req.get('user-agent') : null
                ]
            );
        }
        
        return true;
    } catch (error) {
        console.error('Error in logjsvalues:', error);
        return false;
    }
}

module.exports = {
    logjsvalues
};
