const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Create a separate pool for logging to avoid circular dependencies
let logPool;
try {
    logPool = mysql.createPool({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        port: process.env.DB_PORT,
        waitForConnections: true,
        connectionLimit: 2,
        queueLimit: 0
    });
    console.log('Logger database pool created');
} catch (error) {
    console.error('Failed to create logger database pool:', error);
}

// Function to log errors
const logError = async (error, req) => {
    try {
        const timestamp = new Date().toISOString();
        const errorLog = `[${timestamp}] ERROR: ${error.message}\n`;

        // Log to database if pool is available
        if (logPool) {
            try {
                await logPool.query(
                    'INSERT INTO logs (timestamp, user_id, operation, details, status, error_message, ip_address, request_method, request_uri, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                    [new Date(), (req.session?.userId && !isNaN(parseInt(req.session.userId))) ? parseInt(req.session.userId) : null, req.method, JSON.stringify(req.body), 'error', error.message, req.ip, req.method, req.originalUrl, req.get('user-agent')]
                );
            } catch (dbError) {
                console.error('Failed to log error to database:', dbError);
            }
        }

        // Log to error file
        await fs.appendFile(
            path.join(__dirname, '../logs/error.log'),
            errorLog
        );
    } catch (logError) {
        console.error('Failed to log error:', logError);
    }
};

// Function to log database queries
const logQuery = async (query, params, duration, userId = null) => {
    try {
        const timestamp = new Date().toISOString();
        const queryLog = `[${timestamp}] QUERY (${duration}ms) - User: ${userId || 'system'}\nQuery: ${query}\nParams: ${JSON.stringify(params)}\n\n`;

        // Log to query file
        await fs.appendFile(
            path.join(__dirname, '../logs/query.log'),
            queryLog
        );

        // Log to database if duration exceeds threshold (e.g., 1000ms)
        if (duration > 1000) {
            await db.query(
                'INSERT INTO query_logs (timestamp, user_id, query, params, duration) VALUES (?, ?, ?, ?, ?)',
                [new Date(), userId, query, JSON.stringify(params), duration]
            );
        }
    } catch (logError) {
        console.error('Failed to log query:', logError);
    }
};

// Function to log general activity
const logActivity = async (activity, userId = null) => {
    try {
        const timestamp = new Date().toISOString();
        const activityLog = `[${timestamp}] ACTIVITY - User: ${userId || 'system'} - ${activity}\n`;

        // Log to activity file
        await fs.appendFile(
            path.join(__dirname, '../logs/activity.log'),
            activityLog
        );
    } catch (logError) {
        console.error('Failed to log activity:', logError);
    }
};

// Function to log events to the logs table
const logEvent = async (req, level, category, operation, details, status = 'success', errorMessage = null) => {
    try {
        // Log to database if pool is available
        if (logPool) {
            try {
                await logPool.query(
                    `INSERT INTO logs (timestamp, user_id, level, category, operation, details, status, error_message, ip_address, request_method, request_uri, user_agent)
                     VALUES (NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        (req.session?.userId && !isNaN(parseInt(req.session.userId))) ? parseInt(req.session.userId) : null,
                        level,
                        category,
                        operation,
                        details,
                        status,
                        errorMessage,
                        req.ip,
                        req.method,
                        req.originalUrl,
                        req.get('user-agent')
                    ]
                );
            } catch (dbError) {
                console.error('Failed to log event to database:', dbError);
                // Log to file as fallback
                try {
                    const timestamp = new Date().toISOString();
                    const eventLog = `[${timestamp}] ${level.toUpperCase()} - ${category} - ${operation} - ${details}\n`;
                    await fs.appendFile(
                        path.join(__dirname, '../logs/events.log'),
                        eventLog
                    );
                } catch (fileError) {
                    console.error('Failed to log event to file:', fileError);
                }
                return false;
            }
        } else {
            // Log to file if database is not available
            try {
                const timestamp = new Date().toISOString();
                const eventLog = `[${timestamp}] ${level.toUpperCase()} - ${category} - ${operation} - ${details}\n`;
                await fs.appendFile(
                    path.join(__dirname, '../logs/events.log'),
                    eventLog
                );
            } catch (fileError) {
                console.error('Failed to log event to file:', fileError);
                return false;
            }
        }

        console.log(`Event logged: [${level}] ${category} - ${operation}`);
        return true;
    } catch (error) {
        console.error('Failed to log event:', error);
        return false;
    }
};

module.exports = {
    logError,
    logQuery,
    logActivity,
    logEvent
};
