const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const ejs = require('ejs');

/**
 * Generate a PDF from an EJS template
 * @param {Object} data - Data to pass to the template
 * @param {string} templatePath - Path to the EJS template
 * @param {string} outputPath - Path to save the PDF
 * @returns {Promise<string>} - Path to the generated PDF
 */
async function generatePDF(data, templatePath, outputPath) {
    try {
        // Render the EJS template
        const templateContent = await fs.readFile(templatePath, 'utf8');
        const html = ejs.render(templateContent, data);

        // Create a temporary HTML file
        const tempHtmlPath = path.join(__dirname, '../temp', `${Date.now()}.html`);
        await fs.mkdir(path.dirname(tempHtmlPath), { recursive: true });
        await fs.writeFile(tempHtmlPath, html);

        // Launch puppeteer
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        const page = await browser.newPage();

        // Set viewport size to ensure images are properly rendered
        await page.setViewport({
            width: 1600,
            height: 2000,
            deviceScaleFactor: 1
        });

        // Wait for fonts to load
        await page.evaluateHandle('document.fonts.ready');

        // JavaScript should be enabled by default, no need to explicitly enable it

        // Add custom styles to ensure proper rendering
        await page.addStyleTag({
            content: `
                body { line-height: 1 !important; font-family: Arial, sans-serif !important; }
                p { margin-top: 0.1em !important; margin-bottom: 0.1em !important; }
                img { display: block !important; max-width: 80% !important; height: auto !important; margin: 5px auto !important; page-break-inside: avoid !important; object-fit: contain !important; }
                .instruction-content { line-height: 0.8 !important; font-size: 0.9em !important; }
                .instruction-content p { margin: 0 !important; padding: 0 !important; line-height: 0.8 !important; }
                pre { white-space: pre-wrap !important; }
                .question { margin-bottom: 15px !important; page-break-inside: avoid !important; }
                .question-image { page-break-inside: avoid !important; text-align: center !important; }
                .section { margin-bottom: 15px !important; }
                .header { margin-bottom: 15px !important; }
                .instructions { margin-bottom: 5px !important; padding: 3px !important; }
                .section-title { margin-bottom: 10px !important; }
                .question-header { margin-bottom: 5px !important; }
                .answer-space { height: 60px !important; }
                .footer { margin-top: 15px !important; padding-top: 5px !important; }
            `
        });

        // Load the HTML file
        await page.goto(`file://${tempHtmlPath}`, { waitUntil: 'networkidle0' });

        // Wait for images to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Add script to check if all images are loaded
        await page.evaluate(() => {
            return new Promise((resolve) => {
                const images = document.querySelectorAll('img');
                let loadedImages = 0;

                if (images.length === 0) {
                    resolve();
                    return;
                }

                const checkAllImagesLoaded = () => {
                    loadedImages++;
                    if (loadedImages === images.length) {
                        resolve();
                    }
                };

                images.forEach(img => {
                    if (img.complete) {
                        checkAllImagesLoaded();
                    } else {
                        img.addEventListener('load', checkAllImagesLoaded);
                        img.addEventListener('error', checkAllImagesLoaded);
                    }
                });
            });
        });

        // Generate PDF
        await page.pdf({
            path: outputPath,
            format: 'A4',
            printBackground: true,
            preferCSSPageSize: true,
            margin: {
                top: '15mm',
                right: '10mm',
                bottom: '15mm',
                left: '10mm'
            },
            scale: 1.0,
            displayHeaderFooter: true,
            headerTemplate: ' ',
            footerTemplate: `
                <div style="width: 100%; font-size: 8px; text-align: center; color: #777; padding: 0 20px;">
                    Page <span class="pageNumber"></span> of <span class="totalPages"></span> | For offline use only
                </div>
            `,
            timeout: 60000 // Increase timeout to 60 seconds
        });

        // Close browser and clean up
        await browser.close();
        await fs.unlink(tempHtmlPath);

        return outputPath;
    } catch (error) {
        console.error('Error generating PDF:', error);
        throw error;
    }
}

module.exports = { generatePDF };
