/**
 * PDF Utilities for generating vouchers and reports
 */
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

/**
 * Ensure directory exists
 * @param {string} dirPath - Directory path
 * @returns {Promise<void>}
 */
async function ensureDirectoryExists(dirPath) {
    try {
        if (!(await existsAsync(dirPath))) {
            await mkdirAsync(dirPath, { recursive: true });
        }
    } catch (error) {
        if (error.code !== 'EEXIST') {
            throw error;
        }
    }
}

/**
 * Generate a unique filename for a PDF
 * @param {string} prefix - Prefix for the filename
 * @param {string} id - ID to include in the filename
 * @returns {string} - Generated filename
 */
function generatePdfFilename(prefix, id) {
    const timestamp = Date.now();
    return `${prefix}_${id}_${timestamp}.pdf`;
}

/**
 * Get the path for a PDF file
 * @param {string} filename - Filename
 * @returns {Object} - Object containing outputDir and outputPath
 */
function getPdfFilePath(filename) {
    const outputDir = path.join(__dirname, '../public/uploads/vouchers');
    const outputPath = path.join(outputDir, filename);
    return { outputDir, outputPath };
}

/**
 * Format a date for display in vouchers
 * @param {Date|string} date - The date to format
 * @returns {string} The formatted date string
 */
function formatVoucherDate(date) {
  if (!date) return 'N/A';
  const d = new Date(date);
  if (isNaN(d.getTime())) return 'N/A';
  return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
}

/**
 * Add common header elements to a PDF document
 * @param {Object} doc - The PDFKit document
 * @param {string} title - The title to display
 * @param {string} logoPath - Path to the logo image
 */
function addPdfHeader(doc, title, logoPath) {
  // Add logo if available
  if (fs.existsSync(logoPath)) {
    doc.image(logoPath, 50, 45, { width: 50 });
  }

  // Add header
  doc.fontSize(18).text('Senior Secondary Residential School for Meritorious Students', { align: 'center' });
  doc.fontSize(14).text('IT Inventory Management System', { align: 'center' });
  doc.moveDown();
  doc.fontSize(16).text(title, { align: 'center' });
  doc.moveDown();
}

/**
 * Add signature lines to a PDF document
 * @param {Object} doc - The PDFKit document
 * @param {Object} options - Options for the signature lines
 */
function addSignatureLines(doc, options) {
  const {
    leftTitle = 'Signature',
    rightTitle = 'Signature',
    leftName = '',
    rightName = '',
    leftDate = '',
    rightDate = ''
  } = options;

  doc.moveDown();
  doc.fontSize(12).text('Signatures', { underline: true });
  doc.moveDown(0.5);

  // Draw signature lines
  const signatureY = doc.y + 40;
  doc.moveTo(50, signatureY).lineTo(250, signatureY).stroke();
  doc.moveTo(300, signatureY).lineTo(500, signatureY).stroke();

  doc.fontSize(10).text(leftTitle, 50, signatureY + 10);
  doc.text(rightTitle, 300, signatureY + 10);

  doc.text(`Name: ${leftName}`, 50, signatureY + 30);
  doc.text(`Name: ${rightName}`, 300, signatureY + 30);

  doc.text(`Date: ${leftDate}`, 50, signatureY + 50);
  doc.text(`Date: ${rightDate}`, 300, signatureY + 50);
}

/**
 * Add terms and conditions to a PDF document
 * @param {Object} doc - The PDFKit document
 * @param {string} type - The type of voucher ('loan' or 'return')
 */
function addTermsAndConditions(doc, type = 'loan') {
  doc.moveDown();
  doc.fontSize(10).text('Terms and Conditions:', { underline: true });

  if (type === 'loan') {
    doc.text('1. The borrower is responsible for the proper care and use of the equipment.');
    doc.text('2. Any damage or loss must be reported immediately to the IT department.');
    doc.text('3. The equipment must be returned in the same condition as when it was issued.');
    doc.text('4. The equipment must be returned on or before the expected return date.');
    doc.text('5. The school reserves the right to recall the equipment at any time if needed.');
  } else {
    doc.text('1. This document confirms the return of the equipment to the IT department.');
    doc.text('2. The condition of the returned equipment has been verified by IT staff.');
    doc.text('3. Any damage noted during return has been documented above.');
    doc.text('4. The borrower is relieved of responsibility for this equipment as of the return date.');
    doc.text('5. This voucher serves as proof of return for the borrower\'s records.');
  }
}

/**
 * Add a footer to a PDF document
 * @param {Object} doc - The PDFKit document
 * @param {string} text - The footer text
 */
function addFooter(doc, text) {
  const pageHeight = doc.page.height;
  doc.fontSize(8).text(text, 50, pageHeight - 50, { align: 'center' });
}

module.exports = {
    ensureDirectoryExists,
    generatePdfFilename,
    getPdfFilePath,
    formatVoucherDate,
    addPdfHeader,
    addSignatureLines,
    addTermsAndConditions,
    addFooter
};
