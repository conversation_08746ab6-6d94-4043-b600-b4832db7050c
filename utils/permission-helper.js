/**
 * Permission Helper
 * 
 * Utility functions to check user permissions
 */

const db = require('../config/database');

/**
 * Check if a user has a specific permission
 * 
 * @param {number} userId - The user ID
 * @param {string} permissionName - The permission name to check
 * @returns {Promise<boolean>} - True if the user has the permission, false otherwise
 */
async function hasPermission(userId, permissionName) {
    try {
        // Get user's role
        const [users] = await db.query('SELECT role FROM users WHERE id = ?', [userId]);
        
        if (users.length === 0) {
            return false;
        }

        const userRole = users[0].role;

        // Admin users have all permissions
        if (userRole === 'admin') {
            return true;
        }

        // Get role ID
        const [roles] = await db.query('SELECT role_id FROM roles WHERE role_name = ?', [userRole]);
        
        if (roles.length === 0) {
            return false;
        }

        const roleId = roles[0].role_id;

        // Check if role has the required permission
        const [permissions] = await db.query(`
            SELECT rp.* 
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.permission_id
            WHERE rp.role_id = ? AND p.permission_name = ?
        `, [roleId, permissionName]);

        return permissions.length > 0;
    } catch (error) {
        console.error('Error checking permission:', error);
        return false;
    }
}

/**
 * Get all permissions for a user
 * 
 * @param {number} userId - The user ID
 * @returns {Promise<Array>} - Array of permission names
 */
async function getUserPermissions(userId) {
    try {
        // Get user's role
        const [users] = await db.query('SELECT role FROM users WHERE id = ?', [userId]);
        
        if (users.length === 0) {
            return [];
        }

        const userRole = users[0].role;

        // Admin users have all permissions
        if (userRole === 'admin') {
            const [allPermissions] = await db.query('SELECT permission_name FROM permissions');
            return allPermissions.map(p => p.permission_name);
        }

        // Get role ID
        const [roles] = await db.query('SELECT role_id FROM roles WHERE role_name = ?', [userRole]);
        
        if (roles.length === 0) {
            return [];
        }

        const roleId = roles[0].role_id;

        // Get all permissions for this role
        const [permissions] = await db.query(`
            SELECT p.permission_name 
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.permission_id
            WHERE rp.role_id = ?
        `, [roleId]);

        return permissions.map(p => p.permission_name);
    } catch (error) {
        console.error('Error getting user permissions:', error);
        return [];
    }
}

module.exports = {
    hasPermission,
    getUserPermissions
};
