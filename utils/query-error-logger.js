/**
 * Utility for logging database query errors
 */

const db = require('../config/database');

/**
 * Log a database query error
 * @param {Error} error - The error object
 * @param {string} query - The SQL query that caused the error
 * @param {Array|Object} params - The parameters passed to the query
 * @param {Object} req - Express request object (optional)
 * @param {string} route - The route where the error occurred (optional)
 * @returns {Promise<void>}
 */
async function logQueryError(error, query, params, req = null, route = null) {
    try {
        // Extract error details
        const errorCode = error.code || error.errno || 'UNKNOWN';
        const errorMessage = error.message || 'Unknown error';
        const stackTrace = error.stack || '';
        
        // Extract request details if available
        const userId = req?.session?.userId || null;
        const ipAddress = req?.ip || req?.connection?.remoteAddress || null;
        const userAgent = req?.headers?.['user-agent'] || null;
        const routePath = route || req?.originalUrl || req?.url || null;
        
        // Format parameters for storage
        const paramsString = typeof params === 'object' ? JSON.stringify(params) : String(params);
        
        // Insert into query_error_logs table
        await db.query(
            `INSERT INTO query_error_logs 
            (error_code, error_message, query, parameters, route, user_id, ip_address, user_agent, stack_trace) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [errorCode, errorMessage, query, paramsString, routePath, userId, ipAddress, userAgent, stackTrace]
        );
        
        console.error(`[QUERY ERROR] ${errorCode}: ${errorMessage} - Query: ${query}`);
    } catch (logError) {
        // If logging fails, just output to console
        console.error('Failed to log query error:', logError);
        console.error('Original error:', error);
        console.error('Query:', query);
        console.error('Parameters:', params);
    }
}

module.exports = {
    logQueryError
};
