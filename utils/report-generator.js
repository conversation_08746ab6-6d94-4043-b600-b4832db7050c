const ExcelJS = require('exceljs');
const fs = require('fs').promises;
const path = require('path');
const { generatePDF } = require('./pdf-generator');
const ejs = require('ejs');
const db = require('../config/database');
const { createObjectCsvWriter } = require('csv-writer');

/**
 * Generate a report based on the specified type and format
 * @param {Object} options - Report generation options
 * @param {string} options.reportType - Type of report to generate
 * @param {string} options.format - Format of the report (pdf, excel, csv, json)
 * @param {Object} options.filters - Filters to apply to the report
 * @param {number} options.userId - ID of the user generating the report
 * @returns {Promise<Object>} - Report details including path and metadata
 */
async function generateReport(options) {
    const { reportType, format, filters, userId } = options;
    
    // Get report data based on type and filters
    const reportData = await getReportData(reportType, filters);
    
    // Generate report name
    const timestamp = Date.now();
    const reportName = `${reportType}_${timestamp}`;
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../public/uploads/reports');
    await fs.mkdir(reportsDir, { recursive: true });
    
    // Generate report in the specified format
    let filePath;
    let fileUrl;
    
    switch (format) {
        case 'pdf':
            filePath = await generatePDFReport(reportName, reportType, reportData, reportsDir);
            fileUrl = `/uploads/reports/${path.basename(filePath)}`;
            break;
        case 'excel':
            filePath = await generateExcelReport(reportName, reportType, reportData, reportsDir);
            fileUrl = `/uploads/reports/${path.basename(filePath)}`;
            break;
        case 'csv':
            filePath = await generateCSVReport(reportName, reportType, reportData, reportsDir);
            fileUrl = `/uploads/reports/${path.basename(filePath)}`;
            break;
        case 'json':
            filePath = await generateJSONReport(reportName, reportType, reportData, reportsDir);
            fileUrl = `/uploads/reports/${path.basename(filePath)}`;
            break;
        default:
            throw new Error(`Unsupported format: ${format}`);
    }
    
    // Save report metadata to database
    const reportId = await saveReportMetadata({
        name: getReportTitle(reportType),
        type: reportType,
        format,
        file_path: filePath,
        file_url: fileUrl,
        user_id: userId,
        filters: JSON.stringify(filters)
    });
    
    return {
        id: reportId,
        name: getReportTitle(reportType),
        type: reportType,
        format,
        filePath,
        fileUrl
    };
}

/**
 * Get report data based on type and filters
 * @param {string} reportType - Type of report
 * @param {Object} filters - Filters to apply
 * @returns {Promise<Object>} - Report data
 */
async function getReportData(reportType, filters) {
    const { start_date, end_date, user_id, exam_id, category_id } = filters;
    
    // Build date filter condition
    let dateCondition = '';
    const dateParams = [];
    
    if (start_date && end_date) {
        dateCondition = 'AND DATE(created_at) BETWEEN ? AND ?';
        dateParams.push(start_date, end_date);
    } else if (start_date) {
        dateCondition = 'AND DATE(created_at) >= ?';
        dateParams.push(start_date);
    } else if (end_date) {
        dateCondition = 'AND DATE(created_at) <= ?';
        dateParams.push(end_date);
    }
    
    // Get report data based on type
    switch (reportType) {
        case 'user_performance': {
            let userCondition = '';
            if (user_id) {
                userCondition = 'AND ea.user_id = ?';
            }
            
            let examCondition = '';
            if (exam_id) {
                examCondition = 'AND ea.exam_id = ?';
            }
            
            const query = `
                SELECT 
                    u.username,
                    u.email,
                    e.exam_name,
                    ea.total_score,
                    ea.status,
                    ea.attempt_date,
                    ea.start_time,
                    ea.end_time,
                    (SELECT COUNT(*) FROM user_answers ua WHERE ua.attempt_id = ea.attempt_id) as answered_questions,
                    (SELECT COUNT(*) FROM questions q JOIN sections s ON q.section_id = s.section_id WHERE s.exam_id = e.exam_id) as total_questions,
                    CASE
                        WHEN ea.status != 'completed' THEN 'In Progress'
                        WHEN ea.total_score >= e.passing_marks THEN 'Pass'
                        ELSE 'Fail'
                    END as result
                FROM exam_attempts ea
                JOIN users u ON ea.user_id = u.id
                JOIN exams e ON ea.exam_id = e.exam_id
                WHERE 1=1
                ${userCondition}
                ${examCondition}
                ${dateCondition}
                ORDER BY ea.attempt_date DESC
            `;
            
            const params = [];
            if (user_id) params.push(user_id);
            if (exam_id) params.push(exam_id);
            params.push(...dateParams);
            
            const [rows] = await db.query(query, params);
            
            // Calculate summary statistics
            const totalAttempts = rows.length;
            const completedAttempts = rows.filter(row => row.status === 'completed').length;
            const passedAttempts = rows.filter(row => row.result === 'Pass').length;
            const averageScore = rows.length > 0 
                ? rows.reduce((sum, row) => sum + (row.total_score || 0), 0) / rows.length 
                : 0;
            
            return {
                title: 'User Performance Report',
                summary: {
                    totalAttempts,
                    completedAttempts,
                    passedAttempts,
                    averageScore: averageScore.toFixed(2),
                    passRate: completedAttempts > 0 
                        ? ((passedAttempts / completedAttempts) * 100).toFixed(2) 
                        : 0
                },
                data: rows
            };
        }
        
        case 'test_analytics': {
            let examCondition = '';
            if (exam_id) {
                examCondition = 'AND e.exam_id = ?';
            }
            
            const query = `
                SELECT 
                    e.exam_id,
                    e.exam_name,
                    e.status,
                    e.passing_marks,
                    COUNT(DISTINCT ea.attempt_id) as total_attempts,
                    COUNT(DISTINCT ea.user_id) as unique_users,
                    AVG(ea.total_score) as average_score,
                    COUNT(DISTINCT CASE WHEN ea.status = 'completed' AND ea.total_score >= e.passing_marks THEN ea.attempt_id END) as passed_attempts,
                    COUNT(DISTINCT CASE WHEN ea.status = 'completed' THEN ea.attempt_id END) as completed_attempts,
                    MIN(ea.total_score) as min_score,
                    MAX(ea.total_score) as max_score,
                    AVG(TIMESTAMPDIFF(MINUTE, ea.start_time, ea.end_time)) as avg_completion_time
                FROM exams e
                LEFT JOIN exam_attempts ea ON e.exam_id = ea.exam_id
                WHERE e.is_active = 1
                ${examCondition}
                ${dateCondition}
                GROUP BY e.exam_id
                ORDER BY total_attempts DESC
            `;
            
            const params = [];
            if (exam_id) params.push(exam_id);
            params.push(...dateParams);
            
            const [rows] = await db.query(query, params);
            
            // Calculate summary statistics
            const totalExams = rows.length;
            const totalAttempts = rows.reduce((sum, row) => sum + row.total_attempts, 0);
            const averageAttemptsPerExam = totalExams > 0 ? totalAttempts / totalExams : 0;
            const averageScore = rows.reduce((sum, row) => sum + (row.average_score || 0), 0) / (rows.length || 1);
            
            return {
                title: 'Test Analytics Report',
                summary: {
                    totalExams,
                    totalAttempts,
                    averageAttemptsPerExam: averageAttemptsPerExam.toFixed(2),
                    averageScore: averageScore.toFixed(2)
                },
                data: rows
            };
        }
        
        case 'question_analytics': {
            let examCondition = '';
            if (exam_id) {
                examCondition = 'AND s.exam_id = ?';
            }
            
            let categoryCondition = '';
            if (category_id) {
                categoryCondition = 'AND qcm.category_id = ?';
            }
            
            const query = `
                SELECT 
                    q.question_id,
                    q.question_text,
                    q.question_type,
                    q.marks,
                    s.section_name,
                    e.exam_name,
                    COUNT(DISTINCT ua.attempt_id) as attempt_count,
                    SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
                    COUNT(DISTINCT ua.attempt_id) - SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) as incorrect_count,
                    (SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) / COUNT(DISTINCT ua.attempt_id)) * 100 as correct_percentage,
                    AVG(TIMESTAMPDIFF(SECOND, ua.start_time, ua.end_time)) as avg_time_spent
                FROM questions q
                JOIN sections s ON q.section_id = s.section_id
                JOIN exams e ON s.exam_id = e.exam_id
                LEFT JOIN question_category_mappings qcm ON q.question_id = qcm.question_id
                LEFT JOIN user_answers ua ON q.question_id = ua.question_id
                LEFT JOIN exam_attempts ea ON ua.attempt_id = ea.attempt_id
                WHERE q.is_deleted = 0
                ${examCondition}
                ${categoryCondition}
                ${dateCondition}
                GROUP BY q.question_id
                ORDER BY correct_percentage ASC
            `;
            
            const params = [];
            if (exam_id) params.push(exam_id);
            if (category_id) params.push(category_id);
            params.push(...dateParams);
            
            const [rows] = await db.query(query, params);
            
            // Calculate summary statistics
            const totalQuestions = rows.length;
            const averageCorrectPercentage = rows.reduce((sum, row) => sum + (row.correct_percentage || 0), 0) / (rows.length || 1);
            const hardestQuestions = [...rows].sort((a, b) => a.correct_percentage - b.correct_percentage).slice(0, 5);
            const easiestQuestions = [...rows].sort((a, b) => b.correct_percentage - a.correct_percentage).slice(0, 5);
            
            return {
                title: 'Question Analytics Report',
                summary: {
                    totalQuestions,
                    averageCorrectPercentage: averageCorrectPercentage.toFixed(2),
                    hardestQuestions: hardestQuestions.map(q => ({ 
                        id: q.question_id, 
                        text: q.question_text.substring(0, 50) + '...',
                        correctPercentage: q.correct_percentage.toFixed(2)
                    })),
                    easiestQuestions: easiestQuestions.map(q => ({ 
                        id: q.question_id, 
                        text: q.question_text.substring(0, 50) + '...',
                        correctPercentage: q.correct_percentage.toFixed(2)
                    }))
                },
                data: rows
            };
        }
        
        case 'category_performance': {
            let categoryCondition = '';
            if (category_id) {
                categoryCondition = 'AND qcm.category_id = ?';
            }
            
            let examCondition = '';
            if (exam_id) {
                examCondition = 'AND s.exam_id = ?';
            }
            
            const query = `
                SELECT 
                    c.category_id,
                    c.name as category_name,
                    COUNT(DISTINCT q.question_id) as question_count,
                    COUNT(DISTINCT ua.attempt_id) as attempt_count,
                    SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
                    COUNT(DISTINCT ua.id) - SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) as incorrect_count,
                    (SUM(CASE WHEN ua.is_correct = 1 THEN 1 ELSE 0 END) / COUNT(DISTINCT ua.id)) * 100 as correct_percentage
                FROM categories c
                JOIN question_category_mappings qcm ON c.category_id = qcm.category_id
                JOIN questions q ON qcm.question_id = q.question_id
                JOIN sections s ON q.section_id = s.section_id
                LEFT JOIN user_answers ua ON q.question_id = ua.question_id
                LEFT JOIN exam_attempts ea ON ua.attempt_id = ea.attempt_id
                WHERE q.is_deleted = 0
                ${categoryCondition}
                ${examCondition}
                ${dateCondition}
                GROUP BY c.category_id
                ORDER BY correct_percentage ASC
            `;
            
            const params = [];
            if (category_id) params.push(category_id);
            if (exam_id) params.push(exam_id);
            params.push(...dateParams);
            
            const [rows] = await db.query(query, params);
            
            // Calculate summary statistics
            const totalCategories = rows.length;
            const averageCorrectPercentage = rows.reduce((sum, row) => sum + (row.correct_percentage || 0), 0) / (rows.length || 1);
            const weakestCategories = [...rows].sort((a, b) => a.correct_percentage - b.correct_percentage).slice(0, 3);
            const strongestCategories = [...rows].sort((a, b) => b.correct_percentage - a.correct_percentage).slice(0, 3);
            
            return {
                title: 'Category Performance Report',
                summary: {
                    totalCategories,
                    averageCorrectPercentage: averageCorrectPercentage.toFixed(2),
                    weakestCategories: weakestCategories.map(c => ({ 
                        name: c.category_name, 
                        correctPercentage: c.correct_percentage.toFixed(2)
                    })),
                    strongestCategories: strongestCategories.map(c => ({ 
                        name: c.category_name, 
                        correctPercentage: c.correct_percentage.toFixed(2)
                    }))
                },
                data: rows
            };
        }
        
        case 'user_activity': {
            let userCondition = '';
            if (user_id) {
                userCondition = 'AND user_id = ?';
            }
            
            const query = `
                SELECT 
                    l.log_id,
                    l.user_id,
                    u.username,
                    l.operation,
                    l.category,
                    l.details,
                    l.status,
                    l.timestamp,
                    l.ip_address,
                    l.request_method,
                    l.request_uri
                FROM logs l
                JOIN users u ON l.user_id = u.id
                WHERE 1=1
                ${userCondition}
                ${dateCondition}
                ORDER BY l.timestamp DESC
                LIMIT 1000
            `;
            
            const params = [];
            if (user_id) params.push(user_id);
            params.push(...dateParams);
            
            const [rows] = await db.query(query, params);
            
            // Calculate summary statistics
            const totalLogs = rows.length;
            const uniqueUsers = new Set(rows.map(row => row.user_id)).size;
            const operationCounts = rows.reduce((counts, row) => {
                counts[row.operation] = (counts[row.operation] || 0) + 1;
                return counts;
            }, {});
            
            return {
                title: 'User Activity Report',
                summary: {
                    totalLogs,
                    uniqueUsers,
                    operationCounts
                },
                data: rows
            };
        }
        
        case 'system_logs': {
            const query = `
                SELECT 
                    l.log_id,
                    l.user_id,
                    u.username,
                    l.level,
                    l.operation,
                    l.category,
                    l.details,
                    l.status,
                    l.error_message,
                    l.timestamp,
                    l.ip_address,
                    l.request_method,
                    l.request_uri
                FROM logs l
                LEFT JOIN users u ON l.user_id = u.id
                WHERE 1=1
                ${dateCondition}
                ORDER BY l.timestamp DESC
                LIMIT 1000
            `;
            
            const [rows] = await db.query(query, dateParams);
            
            // Calculate summary statistics
            const totalLogs = rows.length;
            const errorLogs = rows.filter(row => row.level === 'error').length;
            const warningLogs = rows.filter(row => row.level === 'warning').length;
            const infoLogs = rows.filter(row => row.level === 'info').length;
            
            return {
                title: 'System Logs Report',
                summary: {
                    totalLogs,
                    errorLogs,
                    warningLogs,
                    infoLogs,
                    errorPercentage: totalLogs > 0 ? ((errorLogs / totalLogs) * 100).toFixed(2) : 0
                },
                data: rows
            };
        }
        
        default:
            throw new Error(`Unsupported report type: ${reportType}`);
    }
}

/**
 * Generate a PDF report
 * @param {string} reportName - Name of the report
 * @param {string} reportType - Type of report
 * @param {Object} reportData - Report data
 * @param {string} outputDir - Output directory
 * @returns {Promise<string>} - Path to the generated PDF
 */
async function generatePDFReport(reportName, reportType, reportData, outputDir) {
    const templatePath = path.join(__dirname, '../views/reports', `${reportType}_template.ejs`);
    const outputPath = path.join(outputDir, `${reportName}.pdf`);
    
    // Check if template exists, if not use default template
    let templateContent;
    try {
        templateContent = await fs.readFile(templatePath, 'utf8');
    } catch (error) {
        // Use default template
        templateContent = await fs.readFile(path.join(__dirname, '../views/reports/default_template.ejs'), 'utf8');
    }
    
    // Render the template
    const html = ejs.render(templateContent, { 
        report: reportData,
        title: reportData.title,
        generatedAt: new Date().toLocaleString()
    });
    
    // Create a temporary HTML file
    const tempHtmlPath = path.join(__dirname, '../temp', `${Date.now()}.html`);
    await fs.mkdir(path.dirname(tempHtmlPath), { recursive: true });
    await fs.writeFile(tempHtmlPath, html);
    
    // Generate PDF
    await generatePDF({ html }, null, outputPath);
    
    // Clean up temporary file
    await fs.unlink(tempHtmlPath);
    
    return outputPath;
}

/**
 * Generate an Excel report
 * @param {string} reportName - Name of the report
 * @param {string} reportType - Type of report
 * @param {Object} reportData - Report data
 * @param {string} outputDir - Output directory
 * @returns {Promise<string>} - Path to the generated Excel file
 */
async function generateExcelReport(reportName, reportType, reportData, outputDir) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Report');
    
    // Add title
    worksheet.mergeCells('A1:H1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = reportData.title;
    titleCell.font = { size: 16, bold: true };
    titleCell.alignment = { horizontal: 'center' };
    
    // Add generated date
    worksheet.mergeCells('A2:H2');
    const dateCell = worksheet.getCell('A2');
    dateCell.value = `Generated on: ${new Date().toLocaleString()}`;
    dateCell.font = { size: 12, italic: true };
    dateCell.alignment = { horizontal: 'center' };
    
    // Add summary section
    worksheet.addRow([]);
    worksheet.addRow(['Summary']);
    worksheet.lastRow.font = { bold: true };
    
    // Add summary data
    Object.entries(reportData.summary).forEach(([key, value]) => {
        if (typeof value === 'object') {
            // Skip complex summary objects
            return;
        }
        worksheet.addRow([formatKey(key), value]);
    });
    
    // Add data section
    worksheet.addRow([]);
    worksheet.addRow(['Data']);
    worksheet.lastRow.font = { bold: true };
    
    // Add headers
    if (reportData.data.length > 0) {
        const headers = Object.keys(reportData.data[0]);
        worksheet.addRow(headers.map(formatKey));
        worksheet.lastRow.font = { bold: true };
        
        // Add data rows
        reportData.data.forEach(row => {
            worksheet.addRow(headers.map(header => row[header]));
        });
    }
    
    // Auto-fit columns
    worksheet.columns.forEach(column => {
        column.width = 20;
    });
    
    // Save workbook
    const outputPath = path.join(outputDir, `${reportName}.xlsx`);
    await workbook.xlsx.writeFile(outputPath);
    
    return outputPath;
}

/**
 * Generate a CSV report
 * @param {string} reportName - Name of the report
 * @param {string} reportType - Type of report
 * @param {Object} reportData - Report data
 * @param {string} outputDir - Output directory
 * @returns {Promise<string>} - Path to the generated CSV file
 */
async function generateCSVReport(reportName, reportType, reportData, outputDir) {
    const outputPath = path.join(outputDir, `${reportName}.csv`);
    
    if (reportData.data.length === 0) {
        // Create empty CSV file with headers
        await fs.writeFile(outputPath, 'No data available', 'utf8');
        return outputPath;
    }
    
    // Define CSV headers
    const headers = Object.keys(reportData.data[0]).map(key => ({
        id: key,
        title: formatKey(key)
    }));
    
    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
        path: outputPath,
        header: headers
    });
    
    // Write data
    await csvWriter.writeRecords(reportData.data);
    
    return outputPath;
}

/**
 * Generate a JSON report
 * @param {string} reportName - Name of the report
 * @param {string} reportType - Type of report
 * @param {Object} reportData - Report data
 * @param {string} outputDir - Output directory
 * @returns {Promise<string>} - Path to the generated JSON file
 */
async function generateJSONReport(reportName, reportType, reportData, outputDir) {
    const outputPath = path.join(outputDir, `${reportName}.json`);
    
    // Add metadata
    const jsonData = {
        title: reportData.title,
        generatedAt: new Date().toISOString(),
        summary: reportData.summary,
        data: reportData.data
    };
    
    // Write JSON file
    await fs.writeFile(outputPath, JSON.stringify(jsonData, null, 2), 'utf8');
    
    return outputPath;
}

/**
 * Save report metadata to database
 * @param {Object} metadata - Report metadata
 * @returns {Promise<number>} - ID of the saved report
 */
async function saveReportMetadata(metadata) {
    const { name, type, format, file_path, file_url, user_id, filters } = metadata;
    
    const [result] = await db.query(
        `INSERT INTO reports (name, type, format, file_path, file_url, user_id, filters, generated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
        [name, type, format, file_path, file_url, user_id, filters]
    );
    
    return result.insertId;
}

/**
 * Get report title based on type
 * @param {string} reportType - Type of report
 * @returns {string} - Report title
 */
function getReportTitle(reportType) {
    const titles = {
        user_performance: 'User Performance Report',
        test_analytics: 'Test Analytics Report',
        question_analytics: 'Question Analytics Report',
        category_performance: 'Category Performance Report',
        user_activity: 'User Activity Report',
        system_logs: 'System Logs Report'
    };
    
    return titles[reportType] || 'Report';
}

/**
 * Format a key for display
 * @param {string} key - Key to format
 * @returns {string} - Formatted key
 */
function formatKey(key) {
    return key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
}

module.exports = {
    generateReport
};
