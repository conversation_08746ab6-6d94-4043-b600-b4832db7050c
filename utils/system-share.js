const { exec } = require('child_process');
const os = require('os');
const path = require('path');
const fs = require('fs').promises;

/**
 * Share a file using system apps
 * @param {string} filePath - Path to the file to share
 * @returns {Promise<string>} - Result message
 */
async function shareFile(filePath) {
    // Check if file exists
    try {
        await fs.access(filePath);
    } catch (error) {
        throw new Error(`File not found: ${filePath}`);
    }
    
    const platform = os.platform();
    
    if (platform === 'darwin') {
        // macOS
        return shareOnMac(filePath);
    } else if (platform === 'win32') {
        // Windows
        return shareOnWindows(filePath);
    } else {
        throw new Error(`Sharing not supported on platform: ${platform}`);
    }
}

/**
 * Share a file on macOS
 * @param {string} filePath - Path to the file to share
 * @returns {Promise<string>} - Result message
 */
function shareOnMac(filePath) {
    return new Promise((resolve, reject) => {
        // Use AppleScript to open the sharing dialog
        const script = `
            tell application "Finder"
                set theFile to POSIX file "${filePath}" as alias
                open theFile
            end tell
        `;
        
        exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error sharing file: ${error.message}`);
                reject(new Error(`Failed to share file: ${error.message}`));
                return;
            }
            
            if (stderr) {
                console.error(`Error sharing file: ${stderr}`);
                reject(new Error(`Failed to share file: ${stderr}`));
                return;
            }
            
            resolve('File opened with default application on macOS');
        });
    });
}

/**
 * Share a file on Windows
 * @param {string} filePath - Path to the file to share
 * @returns {Promise<string>} - Result message
 */
function shareOnWindows(filePath) {
    return new Promise((resolve, reject) => {
        // Use PowerShell to open the file with default application
        const command = `powershell -Command "Start-Process '${filePath}'`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error sharing file: ${error.message}`);
                reject(new Error(`Failed to share file: ${error.message}`));
                return;
            }
            
            if (stderr) {
                console.error(`Error sharing file: ${stderr}`);
                reject(new Error(`Failed to share file: ${stderr}`));
                return;
            }
            
            resolve('File opened with default application on Windows');
        });
    });
}

module.exports = {
    shareFile
};
