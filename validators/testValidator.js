const Joi = require('joi');

const testSchema = Joi.object({
    exam_name: Joi.string().required().min(3).max(100),
    instructions: Joi.string().required().min(180).max(250),
    duration: Joi.number().required().min(1),
    passing_score: Joi.number().required().min(0).max(100),
    sections: Joi.array().items(
        Joi.object({
            name: Joi.string().required(),
            questions: Joi.array().items(
                Joi.object({
                    type: Joi.string().valid('mcq', 'true_false', 'short_answer').required(),
                    text: Joi.string().required(),
                    solution: Joi.string().allow(''),
                    options: Joi.when('type', {
                        is: 'mcq',
                        then: Joi.array().items(
                            Joi.object({
                                text: Joi.string().required(),
                                is_correct: Joi.boolean().required()
                            })
                        ).min(2).required()
                    }),
                    correct_answer: Joi.when('type', {
                        switch: [
                            {
                                is: 'true_false',
                                then: Joi.string().valid('true', 'false').required()
                            },
                            {
                                is: 'short_answer',
                                then: Joi.string().required()
                            }
                        ]
                    })
                })
            ).min(1)
        })
    ).min(1)
});

const validateTestData = (data) => testSchema.validate(data);

module.exports = {
    validateTestData
};
