const db = require('./config/database');

async function verifyChanges() {
    try {
        console.log('Verifying database counts...');
        
        // Get total number of essays
        const [essaysCount] = await db.query(`
            SELECT COUNT(*) as count
            FROM essays
        `);
        
        console.log('Essays count:', essaysCount[0].count);
        
        // Get total number of questions linked to essays
        const [linkedQuestionsCount] = await db.query(`
            SELECT COUNT(*) as count
            FROM questions
            WHERE essay_id IS NOT NULL
        `);
        
        console.log('Linked questions count:', linkedQuestionsCount[0].count);
        
        // Get sample essays with linked questions
        const [essays] = await db.query(`
            SELECT e.essay_id, e.title, COUNT(q.question_id) as question_count
            FROM essays e
            LEFT JOIN questions q ON e.essay_id = q.essay_id
            GROUP BY e.essay_id
            LIMIT 5
        `);
        
        console.log('Sample essays with question counts:');
        essays.forEach(essay => {
            console.log(`- Essay ID ${essay.essay_id}: "${essay.title}" has ${essay.question_count} linked questions`);
        });
        
        process.exit(0);
    } catch (error) {
        console.error('Error verifying changes:', error);
        process.exit(1);
    }
}

verifyChanges();
