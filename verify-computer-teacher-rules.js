/**
 * <PERSON><PERSON><PERSON> to verify that the computer teacher rules have been properly implemented
 */

const db = require('./config/database');

async function verifyComputerTeacherRules() {
  try {
    console.log('Verifying computer teacher rules implementation...');
    
    // 1. Check if the required tables exist
    console.log('\n=== Checking Required Tables ===');
    
    const requiredTables = [
      'teacher_specialization',
      'subject_category',
      'lab_incharge',
      'class_incharge'
    ];
    
    for (const table of requiredTables) {
      const [tableExists] = await db.query(`
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = ?
      `, [table]);
      
      if (tableExists[0].table_exists > 0) {
        console.log(`✅ Table ${table} exists`);
      } else {
        console.log(`❌ Table ${table} does not exist`);
      }
    }
    
    // 2. Check if computer teachers are only teaching computer subjects
    console.log('\n=== Checking Computer Teacher Subject Assignments ===');
    
    // Get computer teachers
    const [computerTeachers] = await db.query(`
      SELECT DISTINCT u.id, u.full_name
      FROM users u
      JOIN teacher_specialization ts ON u.id = ts.teacher_id
      WHERE u.role = 'teacher' 
      AND ts.specialization = 'Computer Science'
    `);
    
    if (computerTeachers.length === 0) {
      console.log('No computer teachers found');
    } else {
      console.log(`Found ${computerTeachers.length} computer teachers`);
      
      // Check their subject assignments
      for (const teacher of computerTeachers) {
        console.log(`\nChecking subjects for ${teacher.full_name} (ID: ${teacher.id}):`);
        
        const [subjects] = await db.query(`
          SELECT s.id, s.name, s.code
          FROM teacher_subjects ts
          JOIN subjects s ON ts.subject_id = s.id
          WHERE ts.teacher_id = ?
        `, [teacher.id]);
        
        if (subjects.length === 0) {
          console.log('  No subjects assigned');
        } else {
          let allComputerSubjects = true;
          
          for (const subject of subjects) {
            const isComputerSubject = 
              subject.name.toLowerCase().includes('computer') || 
              subject.name.toLowerCase().includes('programming') ||
              subject.code.includes('CS') ||
              subject.code.toLowerCase().includes('comp');
            
            if (isComputerSubject) {
              console.log(`  ✅ ${subject.name} (${subject.code}) - Computer subject`);
            } else {
              console.log(`  ❌ ${subject.name} (${subject.code}) - NOT a computer subject`);
              allComputerSubjects = false;
            }
          }
          
          if (allComputerSubjects) {
            console.log('  ✅ Teacher is only teaching computer subjects');
          } else {
            console.log('  ❌ Teacher is teaching non-computer subjects');
          }
        }
      }
    }
    
    // 3. Check if computer teachers are not class incharge
    console.log('\n=== Checking Class Incharge Assignments ===');
    
    if (computerTeachers.length > 0) {
      for (const teacher of computerTeachers) {
        console.log(`\nChecking class incharge assignments for ${teacher.full_name} (ID: ${teacher.id}):`);
        
        const [classIncharge] = await db.query(`
          SELECT ci.id, c.name, c.grade, c.trade, c.section, ci.session
          FROM class_incharge ci
          JOIN classes c ON ci.class_id = c.id
          WHERE ci.teacher_id = ?
        `, [teacher.id]);
        
        if (classIncharge.length === 0) {
          console.log(`  ✅ Teacher is not assigned as class incharge`);
        } else {
          console.log(`  ❌ Teacher is assigned as class incharge for ${classIncharge.length} classes:`);
          
          for (const assignment of classIncharge) {
            console.log(`    - ${assignment.name} (${assignment.grade} ${assignment.trade} ${assignment.section}) - Session: ${assignment.session}`);
          }
        }
      }
    }
    
    // 4. Check if computer teachers are lab incharge
    console.log('\n=== Checking Lab Incharge Assignments ===');
    
    if (computerTeachers.length > 0) {
      for (const teacher of computerTeachers) {
        console.log(`\nChecking lab incharge assignments for ${teacher.full_name} (ID: ${teacher.id}):`);
        
        const [labIncharge] = await db.query(`
          SELECT li.id, l.name, li.session
          FROM lab_incharge li
          JOIN labs l ON li.lab_id = l.id
          WHERE li.teacher_id = ?
        `, [teacher.id]);
        
        if (labIncharge.length === 0) {
          console.log(`  ❌ Teacher is not assigned as lab incharge`);
        } else {
          console.log(`  ✅ Teacher is assigned as lab incharge for ${labIncharge.length} labs:`);
          
          for (const assignment of labIncharge) {
            console.log(`    - ${assignment.name} - Session: ${assignment.session}`);
          }
        }
      }
    }
    
    // 5. Check if all inchargeships have session column
    console.log('\n=== Checking Session Column in Incharge Tables ===');
    
    // Check class_incharge table
    const [classInchargeColumns] = await db.query(`
      SHOW COLUMNS FROM class_incharge LIKE 'session'
    `);
    
    if (classInchargeColumns.length > 0) {
      console.log('✅ class_incharge table has session column');
    } else {
      console.log('❌ class_incharge table does not have session column');
    }
    
    // Check lab_incharge table
    const [labInchargeColumns] = await db.query(`
      SHOW COLUMNS FROM lab_incharge LIKE 'session'
    `);
    
    if (labInchargeColumns.length > 0) {
      console.log('✅ lab_incharge table has session column');
    } else {
      console.log('❌ lab_incharge table does not have session column');
    }
    
    console.log('\nVerification completed');
    return true;
  } catch (error) {
    console.error('Error during verification:', error);
    return false;
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  verifyComputerTeacherRules()
    .then(() => {
      console.log('Verification completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Verification failed:', err);
      process.exit(1);
    });
}

module.exports = verifyComputerTeacherRules;
