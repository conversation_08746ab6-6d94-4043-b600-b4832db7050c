<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - Meritorious EP Admin</title>
  <link rel="stylesheet" href="/styles.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <link rel="stylesheet" href="/css/session-timer.css">
  <script src="/js/session-timer.js"></script>
  <script src="/js/chat-icon.js"></script>
  <%- style %>
  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #e9d5ff;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a855f7;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <!-- Admin Navbar -->
  <%- include('./partials/admin-navbar', {
    currentPage: locals.currentPage || '',
    user: locals.user || null,
    notificationCount: locals.notificationCount || 0
  }) %>

  <!-- Main Content -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <% if (locals.pageTitle) { %>
      <h1 class="text-2xl font-bold text-gray-800 mb-6"><%= pageTitle %></h1>
    <% } %>

    <% if (locals.flashSuccess) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
      </div>
    <% } %>

    <% if (locals.flashError) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashError %></span>
      </div>
    <% } %>

    <%- body %>
  </main>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>

  <!-- Common Scripts -->
  <script>
    // Flash message auto-dismiss
    document.addEventListener('DOMContentLoaded', function() {
      const flashMessages = document.querySelectorAll('[role="alert"]');
      flashMessages.forEach(message => {
        setTimeout(() => {
          message.style.opacity = '0';
          message.style.transition = 'opacity 1s';
          setTimeout(() => {
            message.remove();
          }, 1000);
        }, 5000);
      });
    });
  </script>

  <%- script %>
</body>
</html>