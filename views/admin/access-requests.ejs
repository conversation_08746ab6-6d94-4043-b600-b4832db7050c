<!-- Access Requests Admin Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Test Access Requests</h2>
      <a href="/admin/dashboard" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Dashboard
      </a>
    </div>

    <!-- Quick Filters -->
    <div class="flex flex-wrap gap-2 mb-6">
      <a href="/admin/access-requests" class="<%= !status ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800' %> px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 hover:text-white transition">
        All (<%= stats.total %>)
      </a>
      <a href="/admin/access-requests?status=pending" class="<%= status === 'pending' ? 'bg-yellow-600 text-white' : 'bg-gray-100 text-gray-800' %> px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700 hover:text-white transition">
        Pending (<%= stats.pending %>)
      </a>
      <a href="/admin/access-requests?status=approved" class="<%= status === 'approved' ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-800' %> px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 hover:text-white transition">
        Approved (<%= stats.approved %>)
      </a>
      <a href="/admin/access-requests?status=rejected" class="<%= status === 'rejected' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-800' %> px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 hover:text-white transition">
        Rejected (<%= stats.rejected %>)
      </a>
    </div>

    <!-- Requests Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processed</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (requests.length === 0) { %>
            <tr>
              <td colspan="7" class="px-6 py-4 text-center text-gray-500">No access requests found</td>
            </tr>
          <% } else { %>
            <% requests.forEach(request => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900"><%= request.username %></div>
                      <div class="text-sm text-gray-500"><%= request.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= request.exam_name %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900"><%= request.message || 'No message provided' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (request.status === 'pending') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                  <% } else if (request.status === 'approved') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Approved</span>
                    <div class="text-xs text-gray-500 mt-1">+<%= request.additional_attempts %> attempts</div>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= formatDateTime(request.requested_at) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if (request.processed_at) { %>
                    <%= formatDateTime(request.processed_at) %>
                    <div class="text-xs text-gray-500 mt-1">by <%= request.admin_username || 'Unknown' %></div>
                  <% } else { %>
                    -
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <% if (request.status === 'pending') { %>
                    <div class="flex space-x-2">
                      <button onclick="showApproveModal('<%= request.id %>', '<%= request.username %>', '<%= request.exam_name %>')" class="text-green-600 hover:text-green-900">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                      <button onclick="showRejectModal('<%= request.id %>', '<%= request.username %>', '<%= request.exam_name %>')" class="text-red-600 hover:text-red-900">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                      </button>
                    </div>
                  <% } else { %>
                    -
                  <% } %>
                </td>
              </tr>
            <% }); %>
          <% } %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
      <div class="flex justify-between items-center mt-6">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium"><%= (pagination.page - 1) * pagination.perPage + 1 %></span> to
          <span class="font-medium"><%= Math.min(pagination.page * pagination.perPage, pagination.totalItems) %></span> of
          <span class="font-medium"><%= pagination.totalItems %></span> results
        </div>
        <div class="flex space-x-2">
          <% if (pagination.page > 1) { %>
            <a href="/admin/access-requests?page=<%= pagination.page - 1 %><%= status ? '&status=' + status : '' %>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </a>
          <% } %>
          <% if (pagination.page < pagination.totalPages) { %>
            <a href="/admin/access-requests?page=<%= pagination.page + 1 %><%= status ? '&status=' + status : '' %>" class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Next
            </a>
          <% } %>
        </div>
      </div>
    <% } %>
  </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Approve Access Request</h3>
      <p class="text-sm text-gray-600 mb-4">
        You are about to approve the access request for <span id="approveUserName" class="font-semibold"></span> for the test <span id="approveTestName" class="font-semibold"></span>.
      </p>
      <form id="approveForm" action="/admin/access-requests/approve" method="POST" onsubmit="return validateApproveForm()">
        <input type="hidden" id="approveRequestId" name="requestId">
        <div class="mb-4">
          <label for="additionalAttempts" class="block text-sm font-medium text-gray-700 mb-1">Additional Attempts to Grant</label>
          <div class="flex items-center">
            <button type="button" onclick="decrementAttempts()" class="px-3 py-2 bg-gray-200 rounded-l-md border border-gray-300 hover:bg-gray-300">
              <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
              </svg>
            </button>
            <input type="number" id="additionalAttempts" name="additionalAttempts" min="1" value="1"
              class="w-20 text-center border-t border-b border-gray-300 py-2 focus:ring-blue-500 focus:border-blue-500"
              onchange="validateAttempts()" onkeyup="validateAttempts()">
            <button type="button" onclick="incrementAttempts()" class="px-3 py-2 bg-gray-200 rounded-r-md border border-gray-300 hover:bg-gray-300">
              <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"></path>
              </svg>
            </button>
          </div>
          <p class="text-xs text-gray-500 mt-1">Number of additional attempts the user will be granted</p>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" onclick="closeApproveModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            Approve
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Access Request</h3>
      <p class="text-sm text-gray-600 mb-4">
        You are about to reject the access request for <span id="rejectUserName" class="font-semibold"></span> for the test <span id="rejectTestName" class="font-semibold"></span>.
      </p>
      <form id="rejectForm" action="/admin/access-requests/reject" method="POST">
        <input type="hidden" id="rejectRequestId" name="requestId">
        <div class="mb-4">
          <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Reason (Optional)</label>
          <textarea id="reason" name="reason" rows="3" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500"></textarea>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" onclick="closeRejectModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Reject
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Error Details Modal -->
<div id="errorDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-hidden">
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Error Details</h3>
        <button type="button" onclick="closeErrorModal()" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="overflow-auto max-h-96">
        <pre id="errorDetails" class="bg-gray-100 p-4 rounded text-sm font-mono whitespace-pre-wrap overflow-x-auto"></pre>
      </div>
      <div class="mt-6 flex justify-end">
        <button type="button" onclick="closeErrorModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  function showApproveModal(requestId, userName, testName) {
    document.getElementById('approveRequestId').value = requestId;
    document.getElementById('approveUserName').textContent = userName;
    document.getElementById('approveTestName').textContent = testName;
    document.getElementById('additionalAttempts').value = 1; // Reset to default value
    document.getElementById('approveModal').classList.remove('hidden');
  }

  function closeApproveModal() {
    document.getElementById('approveModal').classList.add('hidden');
  }

  function incrementAttempts() {
    const input = document.getElementById('additionalAttempts');
    const currentValue = parseInt(input.value) || 0;
    input.value = currentValue + 1;
    validateAttempts();
  }

  function decrementAttempts() {
    const input = document.getElementById('additionalAttempts');
    const currentValue = parseInt(input.value) || 2;
    if (currentValue > 1) {
      input.value = currentValue - 1;
    }
    validateAttempts();
  }

  function validateAttempts() {
    const input = document.getElementById('additionalAttempts');
    let value = parseInt(input.value);

    // Ensure it's a number and at least 1
    if (isNaN(value) || value < 1) {
      input.value = 1;
    } else {
      // Round to nearest integer
      input.value = Math.round(value);
    }
  }

  function validateApproveForm() {
    validateAttempts();
    const attempts = parseInt(document.getElementById('additionalAttempts').value);

    if (isNaN(attempts) || attempts < 1) {
      alert('Please enter a valid number of attempts (minimum 1)');
      return false;
    }

    return true;
  }

  function showRejectModal(requestId, userName, testName) {
    document.getElementById('rejectRequestId').value = requestId;
    document.getElementById('rejectUserName').textContent = userName;
    document.getElementById('rejectTestName').textContent = testName;
    document.getElementById('rejectModal').classList.remove('hidden');
  }

  function closeRejectModal() {
    document.getElementById('rejectModal').classList.add('hidden');
  }

  // Close modals when clicking outside
  document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeApproveModal();
    }
  });

  document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeRejectModal();
    }
  });

  // Close modals on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeApproveModal();
      closeRejectModal();
      closeErrorModal();
    }
  });

  // Error modal functions
  function showErrorModal(errorDetails) {
    document.getElementById('errorDetails').textContent = errorDetails;
    document.getElementById('errorDetailsModal').classList.remove('hidden');
  }

  function closeErrorModal() {
    document.getElementById('errorDetailsModal').classList.add('hidden');
  }

  // Check URL parameters for error flag
  document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('showError') === 'true') {
      // Check if there's error details in session storage
      const errorDetails = '<%= locals.lastError %>';
      if (errorDetails && errorDetails !== '') {
        showErrorModal(errorDetails);
      }
    }
  });
</script>
