<!-- Access Requests Admin Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Test Access Requests</h2>
      <a href="/admin/dashboard" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Dashboard
      </a>
    </div>

    <!-- Quick Filters -->
    <div class="flex flex-wrap gap-3 mb-6">
      <a href="/admin/access-requests"
         class="<%= !status ? 'bg-blue-100 text-blue-800 border-blue-300' : 'bg-gray-100 text-gray-800 border-gray-300' %> px-3 py-1 rounded-full text-sm border">
        All (<%= stats?.total || 0 %>)
      </a>
      <a href="/admin/access-requests?status=pending"
         class="<%= status === 'pending' ? 'bg-yellow-100 text-yellow-800 border-yellow-300' : 'bg-gray-100 text-gray-800 border-gray-300' %> px-3 py-1 rounded-full text-sm border">
        Pending (<%= stats?.pending || 0 %>)
      </a>
      <a href="/admin/access-requests?status=approved"
         class="<%= status === 'approved' ? 'bg-green-100 text-green-800 border-green-300' : 'bg-gray-100 text-gray-800 border-gray-300' %> px-3 py-1 rounded-full text-sm border">
        Approved (<%= stats?.approved || 0 %>)
      </a>
      <a href="/admin/access-requests?status=rejected"
         class="<%= status === 'rejected' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-gray-100 text-gray-800 border-gray-300' %> px-3 py-1 rounded-full text-sm border">
        Rejected (<%= stats?.rejected || 0 %>)
      </a>
    </div>

    <!-- Access Requests Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (requests && requests.length > 0) { %>
            <% requests.forEach(request => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900"><%= request.username %></div>
                      <div class="text-sm text-gray-500"><%= request.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= request.exam_name %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= formatDateTime(request.requested_at) %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    <%= request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                       request.status === 'approved' ? 'bg-green-100 text-green-800' :
                       'bg-red-100 text-red-800' %>">
                    <%= request.status.charAt(0).toUpperCase() + request.status.slice(1) %>
                  </span>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900 max-w-xs truncate">
                    <%= request.message || 'No message provided' %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <% if (request.status === 'pending') { %>
                    <div class="flex space-x-2">
                      <button onclick="openApproveModal('<%= request.id %>', '<%= request.username %>', '<%= request.exam_name %>')"
                              class="text-green-600 hover:text-green-900" title="Approve Request">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                      <button onclick="openRejectModal('<%= request.id %>', '<%= request.username %>', '<%= request.exam_name %>')"
                              class="text-red-600 hover:text-red-900" title="Reject Request">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                      </button>
                    </div>
                  <% } else { %>
                    <span class="text-gray-500">
                      <% if (request.processed_at) { %>
                        Processed by <%= request.admin_username || 'Unknown' %> on <%= formatDateTime(request.processed_at) %>
                      <% } else { %>
                        No processing information available
                      <% } %>
                    </span>
                  <% } %>
                </td>
              </tr>
            <% }) %>
          <% } else { %>
            <tr>
              <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                No access requests found.
              </td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if (pagination && pagination.totalPages > 1) { %>
      <div class="flex justify-between items-center mt-6">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium"><%= (pagination.page - 1) * pagination.perPage + 1 %></span> to
          <span class="font-medium"><%= Math.min(pagination.page * pagination.perPage, pagination.totalItems) %></span> of
          <span class="font-medium"><%= pagination.totalItems %></span> results
        </div>
        <div class="flex space-x-2">
          <% if (pagination.page > 1) { %>
            <a href="/admin/access-requests?page=<%= pagination.page - 1 %><%= status ? '&status=' + status : '' %>"
               class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </a>
          <% } %>
          <% if (pagination.page < pagination.totalPages) { %>
            <a href="/admin/access-requests?page=<%= pagination.page + 1 %><%= status ? '&status=' + status : '' %>"
               class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Next
            </a>
          <% } %>
        </div>
      </div>
    <% } %>
  </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Approve Access Request</h3>
      <button type="button" onclick="closeApproveModal()" class="text-gray-400 hover:text-gray-500">
        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <p class="mb-4 text-sm text-gray-600">
      You are about to approve the access request from <span id="approveUserPlaceholder" class="font-semibold"></span>
      for the test: <span id="approveTestPlaceholder" class="font-semibold"></span>
    </p>

    <form id="approveForm" action="/admin/access-requests/approve" method="POST" class="space-y-4">
      <input type="hidden" id="approveRequestId" name="requestId" value="">

      <div>
        <label for="additionalAttempts" class="block text-sm font-medium text-gray-700 mb-1">Additional Attempts to Grant</label>
        <input type="number" id="additionalAttempts" name="additionalAttempts" min="1" value="1"
               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeApproveModal()"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
          Approve
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Reject Access Request</h3>
      <button type="button" onclick="closeRejectModal()" class="text-gray-400 hover:text-gray-500">
        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <p class="mb-4 text-sm text-gray-600">
      You are about to reject the access request from <span id="rejectUserPlaceholder" class="font-semibold"></span>
      for the test: <span id="rejectTestPlaceholder" class="font-semibold"></span>
    </p>

    <form id="rejectForm" action="/admin/access-requests/reject" method="POST" class="space-y-4">
      <input type="hidden" id="rejectRequestId" name="requestId" value="">

      <div>
        <label for="rejectReason" class="block text-sm font-medium text-gray-700 mb-1">Reason (Optional)</label>
        <textarea id="rejectReason" name="reason" rows="3"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Provide a reason for rejecting this request..."></textarea>
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeRejectModal()"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
          Reject
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Approve Modal Functions
  function openApproveModal(requestId, username, testName) {
    document.getElementById('approveRequestId').value = requestId;
    document.getElementById('approveUserPlaceholder').textContent = username;
    document.getElementById('approveTestPlaceholder').textContent = testName;
    document.getElementById('approveModal').classList.remove('hidden');
  }

  function closeApproveModal() {
    document.getElementById('approveModal').classList.add('hidden');
  }

  // Reject Modal Functions
  function openRejectModal(requestId, username, testName) {
    document.getElementById('rejectRequestId').value = requestId;
    document.getElementById('rejectUserPlaceholder').textContent = username;
    document.getElementById('rejectTestPlaceholder').textContent = testName;
    document.getElementById('rejectModal').classList.remove('hidden');
  }

  function closeRejectModal() {
    document.getElementById('rejectModal').classList.add('hidden');
    document.getElementById('rejectReason').value = '';
  }
</script>
