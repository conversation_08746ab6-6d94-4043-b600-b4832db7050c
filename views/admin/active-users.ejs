<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Active Users</h1>
        <a href="/admin/dashboard" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded">
            Back to Dashboard
        </a>
    </div>

    <% if (locals.flashSuccess) { %>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <%= flashSuccess %>
        </div>
    <% } %>

    <% if (locals.flashError) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <%= flashError %>
        </div>
    <% } %>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-purple-600 text-white">
            <h2 class="text-xl font-semibold">Currently Logged In Users</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Login Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Activity</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (activeUsers.length === 0) { %>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">No active users found</td>
                        </tr>
                    <% } else { %>
                        <% activeUsers.forEach(user => { %>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <% if (user.profile_image) { %>
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-full" src="<%= user.profile_image %>" alt="<%= user.username %>">
                                            </div>
                                        <% } else { %>
                                            <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                                                <span class="text-gray-500 font-medium"><%= user.username.charAt(0).toUpperCase() %></span>
                                            </div>
                                        <% } %>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><%= user.username %></div>
                                            <div class="text-sm text-gray-500"><%= user.email %></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (user.role === 'admin') { %>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                            Admin
                                        </span>
                                    <% } else if (user.role === 'teacher') { %>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            Teacher
                                        </span>
                                    <% } else { %>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Student
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= user.ip_address %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= new Date(user.login_time).toLocaleString() %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= new Date(user.last_activity).toLocaleString() %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <% if (user.role !== 'admin' || (locals.currentUser && locals.currentUser.id === user.user_id)) { %>
                                        <form action="/admin/users/logout/<%= user.id %>" method="POST" onsubmit="return confirm('Are you sure you want to log out this user?')">
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                Force Logout
                                            </button>
                                        </form>
                                    <% } %>
                                </td>
                            </tr>
                        <% }); %>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>
