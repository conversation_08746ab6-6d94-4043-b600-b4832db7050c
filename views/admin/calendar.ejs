<!-- Calendar view uses the admin layout, no header include needed -->

<div class="container mx-auto px-4 py-8">
  <!-- Flash Messages -->
  <% if (typeof flashSuccess !== 'undefined' && flashSuccess) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 flash-message" role="alert">
      <span class="block sm:inline"><%= flashSuccess %></span>
      <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.remove()">
          <title>Close</title>
          <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
        </svg>
      </span>
    </div>
  <% } %>

  <% if (typeof flashError !== 'undefined' && flashError) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4 flash-message" role="alert">
      <span class="block sm:inline"><%= flashError %></span>
      <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.remove()">
          <title>Close</title>
          <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
        </svg>
      </span>
    </div>
  <% } %>
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold">Academic Calendar</h2>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <button id="prev-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h3 id="current-month" class="text-xl font-semibold mx-4"></h3>
          <button id="next-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
        <button id="today-btn" class="px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 transition">
          Today
        </button>
      </div>

      <div class="overflow-x-auto">
        <div class="calendar-grid grid grid-cols-7 gap-1">
          <!-- Calendar header -->
          <div class="text-center font-semibold py-2 bg-gray-100">Sun</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Mon</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Tue</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Wed</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Thu</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Fri</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Sat</div>

          <!-- Calendar days will be inserted here by JavaScript -->
          <div id="calendar-days" class="contents"></div>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 flex flex-wrap gap-4">
        <!-- Tests -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-indigo-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Test</span>
        </div>

        <!-- Assignments -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-orange-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Assignment</span>
        </div>

        <!-- Practicals -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Practical</span>
        </div>

        <!-- Holidays -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">National Holiday</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-amber-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Festival</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Public Holiday</span>
        </div>
      </div>
    </div>
  </div>

  <% if (isAdmin) { %>
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold">Manage Holidays</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Add Holiday Form -->
        <div>
          <form id="add-holiday-form" class="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 class="text-lg font-semibold mb-4">Add New Holiday</h3>
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label for="holiday_date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="holiday_date" name="holiday_date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input type="text" id="description" name="description" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
              <div>
                <label for="holiday_type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select id="holiday_type" name="holiday_type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                  <option value="Public Holiday">Public Holiday</option>
                  <option value="National Holiday">National Holiday</option>
                  <option value="Festival">Festival</option>
                </select>
              </div>
            </div>
            <div class="mt-4 flex justify-end">
              <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">Add Holiday</button>
            </div>
          </form>
        </div>

        <!-- Import Holidays Form -->
        <div>
          <form id="import-holidays-form" action="/admin/calendar/import-holidays" method="POST" enctype="multipart/form-data" class="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 class="text-lg font-semibold mb-4">Import Holidays from Excel</h3>
            <div class="mb-4">
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center" id="dropZone">
                <input type="file" name="holidaysFile" id="holidaysFileInput" accept=".xlsx,.xls" class="hidden" onchange="updateFileName(this)">
                <label for="holidaysFileInput" class="cursor-pointer">
                  <div class="flex flex-col items-center justify-center">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                    <p class="text-gray-500 mb-2">Drag and drop your file here or click to browse</p>
                    <p class="text-gray-400 text-sm">Accepted formats: Excel (.xlsx, .xls)</p>
                    <p class="text-gray-400 text-sm">Maximum file size: 5MB</p>
                  </div>
                </label>
                <div id="fileInfo" class="mt-4 hidden">
                  <p class="text-sm text-gray-600">Selected file: <span id="fileName" class="font-medium"></span></p>
                </div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <button type="button" id="downloadTemplateBtn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">
                Download Template
              </button>
              <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">
                Import Holidays
              </button>
            </div>
          </form>
        </div>
      </div>

      <h3 class="text-lg font-semibold mb-4">All Holidays</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
          <thead>
            <tr>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody>
            <% holidays.forEach(holiday => {
              const holidayDate = new Date(holiday.holiday_date);
              const formattedDate = holidayDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
            %>
              <tr>
                <td class="py-2 px-4 border-b border-gray-200"><%= formattedDate %></td>
                <td class="py-2 px-4 border-b border-gray-200"><%= holiday.description %></td>
                <td class="py-2 px-4 border-b border-gray-200"><%= holiday.holiday_type %></td>
                <td class="py-2 px-4 border-b border-gray-200">
                  <span class="px-2 py-1 rounded-full text-xs font-semibold <%= holiday.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                    <%= holiday.is_active ? 'Active' : 'Inactive' %>
                  </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">
                  <button class="toggle-status-btn px-2 py-1 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition mr-2" data-id="<%= holiday.id %>" data-status="<%= holiday.is_active ? '0' : '1' %>">
                    <%= holiday.is_active ? 'Deactivate' : 'Activate' %>
                  </button>
                  <button class="edit-holiday-btn px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition" data-id="<%= holiday.id %>" data-description="<%= holiday.description %>" data-type="<%= holiday.holiday_type %>">
                    Edit
                  </button>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <% } %>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 id="modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="modal-content" class="space-y-4">
      <!-- Content will be populated by JavaScript -->
    </div>
    <div class="mt-6 flex justify-end">
      <a id="view-details-btn" href="#" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
        View Details
      </a>
    </div>
  </div>
</div>

<!-- Edit Holiday Modal -->
<% if (isAdmin) { %>
<div id="edit-holiday-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
    <h3 class="text-lg font-semibold mb-4">Edit Holiday</h3>
    <form id="edit-holiday-form">
      <input type="hidden" id="edit-holiday-id">
      <div class="mb-4">
        <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <input type="text" id="edit-description" name="description" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
      </div>
      <div class="mb-4">
        <label for="edit-holiday-type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
        <select id="edit-holiday-type" name="holiday_type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
          <option value="Public Holiday">Public Holiday</option>
          <option value="National Holiday">National Holiday</option>
          <option value="Festival">Festival</option>
        </select>
      </div>
      <div class="flex justify-end space-x-3">
        <button type="button" id="close-edit-modal-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">Save Changes</button>
      </div>
    </form>
  </div>
</div>
<% } %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');
    const eventModal = document.getElementById('event-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const viewDetailsBtn = document.getElementById('view-details-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Data from server
    const holidays = <%- JSON.stringify(holidays || []) %>;

    // Initialize calendar
    renderCalendar(currentMonth, currentYear);

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderCalendar(currentMonth, currentYear);
    });

    todayBtn.addEventListener('click', () => {
      const today = new Date();
      currentMonth = today.getMonth();
      currentYear = today.getFullYear();
      renderCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      eventModal.classList.add('hidden');
    });

    // Render calendar
    function renderCalendar(month, year) {
      // Clear previous calendar
      calendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthEl.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        calendarDays.appendChild(dayEl);
      }

      // Current month's days
      for (let i = 1; i <= daysInMonth; i++) {
        const dayEl = createDayElement(i, '', month, year);
        calendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        calendarDays.appendChild(dayEl);
      }
    }

    // Create day element
    function createDayElement(day, extraClasses, month, year) {
      const today = new Date();
      const isToday = day === today.getDate() && month === today.getMonth() && year === today.getFullYear();

      const dayEl = document.createElement('div');

      // Add special styling for today's date
      if (isToday) {
        dayEl.className = `min-h-[100px] border-2 border-blue-500 p-1 bg-blue-50 ${extraClasses}`;
      } else {
        dayEl.className = `min-h-[100px] border p-1 ${extraClasses}`;
      }

      // Add day number
      const dayNumber = document.createElement('div');

      // Apply special styling for today's date
      if (isToday) {
        // Create a container for the today indicator
        const todayContainer = document.createElement('div');
        todayContainer.className = 'flex justify-between items-center mb-1';

        // Add "TODAY" label
        const todayLabel = document.createElement('span');
        todayLabel.className = 'text-xs font-semibold text-blue-700 bg-blue-100 px-2 py-0.5 rounded-sm';
        todayLabel.textContent = 'TODAY';
        todayContainer.appendChild(todayLabel);

        // Style the day number
        dayNumber.className = 'font-bold text-xl text-blue-700 rounded-full bg-white w-8 h-8 flex items-center justify-center shadow-sm';
        dayNumber.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)';
        dayNumber.textContent = day;

        todayContainer.appendChild(dayNumber);
        dayEl.appendChild(todayContainer);

        // Return early since we've already appended the day number
        return dayEl;
      } else {
        dayNumber.className = 'text-right text-sm';
      }

      dayNumber.textContent = day;
      dayEl.appendChild(dayNumber);

      // Format date string for comparison
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // Add events container
      const eventsContainer = document.createElement('div');
      eventsContainer.className = 'mt-1 space-y-1';

      // Add holidays for this day
      const dayHolidays = holidays.filter(h => {
        if (!h.holiday_date) return false;

        // Fix for timezone issue - create a date object from the holiday date
        // and compare the year, month, and day directly
        const holidayDate = new Date(h.holiday_date);
        return holidayDate.getFullYear() === year &&
               holidayDate.getMonth() === month &&
               holidayDate.getDate() === day;
      });

      if (dayHolidays.length > 0) {
        dayHolidays.forEach(holiday => {
          const holidayEl = document.createElement('div');

          // Determine color based on holiday type
          let bgColor = 'bg-blue-200'; // Default for Public Holiday
          if (holiday.holiday_type === 'National Holiday') {
            bgColor = 'bg-red-200';
          } else if (holiday.holiday_type === 'Festival') {
            bgColor = 'bg-amber-200';
          }

          holidayEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer font-semibold`;
          holidayEl.textContent = holiday.description;

          // Add click event to show modal
          holidayEl.addEventListener('click', () => showHolidayModal(holiday));

          eventsContainer.appendChild(holidayEl);
        });

        // Add a special background color to the day cell for holidays
        dayEl.classList.add('bg-gray-50');
      }

      // Only append the events container if there are events
      if (dayHolidays.length > 0) {
        dayEl.appendChild(eventsContainer);
      }

      return dayEl;
    }

    // Show holiday modal
    function showHolidayModal(holiday) {
      modalTitle.textContent = holiday.description;

      // Format date - ensure correct timezone handling
      const holidayDate = new Date(holiday.holiday_date);
      // Add a day to compensate for timezone issues if needed
      // This ensures the date shown in the modal matches the actual holiday date
      const utcDate = new Date(Date.UTC(
        holidayDate.getFullYear(),
        holidayDate.getMonth(),
        holidayDate.getDate()
      ));

      const formattedDate = utcDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Determine holiday type badge color
      let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
      if (holiday.holiday_type === 'National Holiday') {
        badgeColor = 'bg-red-100 text-red-800';
      } else if (holiday.holiday_type === 'Festival') {
        badgeColor = 'bg-amber-100 text-amber-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Type</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
            ${holiday.holiday_type}
          </span>
        </div>
      `;

      // Hide view details button for holidays
      viewDetailsBtn.style.display = 'none';

      // Show modal
      eventModal.classList.remove('hidden');
    }
  });
</script>

<!-- SheetJS library for Excel file generation -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

<!-- Auto-hide flash messages after 5 seconds -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('.flash-message');
    if (flashMessages.length > 0) {
      setTimeout(() => {
        flashMessages.forEach(message => {
          message.style.transition = 'opacity 0.5s ease-out';
          message.style.opacity = '0';
          setTimeout(() => {
            message.remove();
          }, 500);
        });
      }, 5000);
    }
  });
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // File upload handling for holidays import
    const dropZone = document.getElementById('dropZone');
    const holidaysFileInput = document.getElementById('holidaysFileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      dropZone.classList.add('border-purple-500', 'bg-purple-50');
    }

    function unhighlight() {
      dropZone.classList.remove('border-purple-500', 'bg-purple-50');
    }

    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;

      if (files.length > 0) {
        holidaysFileInput.files = files;
        updateFileName(holidaysFileInput);
      }
    }

    // Update file name display
    function updateFileName(input) {
      if (input.files && input.files[0]) {
        fileName.textContent = input.files[0].name;
        fileInfo.classList.remove('hidden');
      }
    }

    // Generate and download template
    downloadTemplateBtn.addEventListener('click', function() {
      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();

      // Define headers and sample data
      const headers = ['Date (YYYY-MM-DD)', 'Description', 'Type (Public Holiday/National Holiday/Festival)'];
      const sampleData = [
        ['2025-01-06', 'Guru Gobind Singh Jayanti', 'Festival'],
        ['2025-01-26', 'Republic Day', 'National Holiday'],
        ['2025-02-03', 'Vasanth Panchami', 'Festival'],
        ['2025-02-12', 'Guru Ravidas Jayanti', 'Festival'],
        ['2025-02-26', 'Maha Sivaratri', 'Festival'],
        ['2025-03-14', 'Holi', 'Festival'],
        ['2025-03-31', 'Idul Fitr', 'Festival'],
        ['2025-04-06', 'Ram Navami', 'Festival'],
        ['2025-04-10', 'Mahavir Jayanti', 'Festival'],
        ['2025-04-13', 'Vaisakh', 'Festival'],
        ['2025-04-14', 'Dr Ambedkar Jayanti', 'National Holiday']
      ];

      // Combine headers and data
      const wsData = [headers, ...sampleData];

      // Create worksheet
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // Set column widths
      const colWidths = [{ wch: 20 }, { wch: 40 }, { wch: 30 }];
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Holidays');

      // Generate and download file
      XLSX.writeFile(wb, 'holidays_import_template.xlsx');
    });
  });

  // Function to update file name (used in onchange attribute)
  function updateFileName(input) {
    if (input.files && input.files[0]) {
      const fileInfo = document.getElementById('fileInfo');
      const fileName = document.getElementById('fileName');
      fileName.textContent = input.files[0].name;
      fileInfo.classList.remove('hidden');
    }
  }
</script>

<!-- Calendar view uses the admin layout, no footer include needed -->
