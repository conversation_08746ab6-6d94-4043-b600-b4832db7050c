<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Chat Monitoring</h1>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Private Chats -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-purple-600 text-white p-4">
                <h2 class="text-xl font-semibold">Private Chats</h2>
            </div>

            <div class="p-4">
                <div class="mb-4">
                    <input type="text" id="privateSearchInput" placeholder="Search private chats..." class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>

                <div class="overflow-y-auto max-h-96">
                    <% if (privateChats && privateChats.length > 0) { %>
                        <div class="divide-y" id="privateChatsContainer">
                            <% privateChats.forEach(chat => { %>
                                <a href="/admin/chat/private/<%= chat.user1_id %>/<%= chat.user2_id %>" class="block p-4 hover:bg-gray-50 transition">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <div class="flex justify-between items-start">
                                                <h3 class="font-medium text-gray-900">
                                                    <%= chat.user1_name %> & <%= chat.user2_name %>
                                                </h3>
                                                <% if (chat.last_activity) { %>
                                                    <span class="text-xs text-gray-500">
                                                        <%= new Date(chat.last_activity).toLocaleDateString() %>
                                                    </span>
                                                <% } %>
                                            </div>
                                            <div class="flex justify-between mt-1">
                                                <span class="text-sm text-gray-500">
                                                    <%= chat.message_count %> messages
                                                </span>
                                                <span class="text-xs text-gray-500">
                                                    Last activity: <%= chat.last_activity ? new Date(chat.last_activity).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A' %>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="p-4 text-center text-gray-500">
                            No private chats found
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Group Chats -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-purple-600 text-white p-4">
                <h2 class="text-xl font-semibold">Group Chats</h2>
            </div>

            <div class="p-4">
                <div class="mb-4">
                    <input type="text" id="groupSearchInput" placeholder="Search group chats..." class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>

                <div class="overflow-y-auto max-h-96">
                    <% if (groupChats && groupChats.length > 0) { %>
                        <div class="divide-y" id="groupChatsContainer">
                            <% groupChats.forEach(group => { %>
                                <a href="/admin/chat/group/<%= group.group_id %>" class="block p-4 hover:bg-gray-50 transition">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center">
                                            <span class="text-purple-600 font-bold text-lg"><%= group.name.charAt(0).toUpperCase() %></span>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-start">
                                                <h3 class="font-medium text-gray-900"><%= group.name %></h3>
                                                <% if (group.last_activity) { %>
                                                    <span class="text-xs text-gray-500">
                                                        <%= new Date(group.last_activity).toLocaleDateString() %>
                                                    </span>
                                                <% } %>
                                            </div>
                                            <div class="flex justify-between mt-1">
                                                <span class="text-sm text-gray-500">
                                                    <%= group.message_count %> messages
                                                </span>
                                                <span class="text-xs text-gray-500">
                                                    Last activity: <%= group.last_activity ? new Date(group.last_activity).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A' %>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="p-4 text-center text-gray-500">
                            No group chats found
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Private chats search
        const privateSearchInput = document.getElementById('privateSearchInput');
        const privateChatsContainer = document.getElementById('privateChatsContainer');
        const privateChats = privateChatsContainer ? Array.from(privateChatsContainer.children) : [];

        if (privateSearchInput && privateChats.length > 0) {
            privateSearchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                privateChats.forEach(chat => {
                    const text = chat.textContent.toLowerCase();
                    chat.style.display = text.includes(searchTerm) ? 'block' : 'none';
                });
            });
        }

        // Group chats search
        const groupSearchInput = document.getElementById('groupSearchInput');
        const groupChatsContainer = document.getElementById('groupChatsContainer');
        const groupChats = groupChatsContainer ? Array.from(groupChatsContainer.children) : [];

        if (groupSearchInput && groupChats.length > 0) {
            groupSearchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                groupChats.forEach(chat => {
                    const text = chat.textContent.toLowerCase();
                    chat.style.display = text.includes(searchTerm) ? 'block' : 'none';
                });
            });
        }
    });
</script>

