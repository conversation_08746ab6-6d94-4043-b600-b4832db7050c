<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <a href="/admin/chat/monitor" class="mr-3 text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <h1 class="text-2xl font-bold text-gray-800">Chat between <%= user1.username %> and <%= user2.username %></h1>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-purple-600 text-white p-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <% if (user1.profile_image) { %>
                        <img src="<%= user1.profile_image %>" alt="<%= user1.username %>" class="w-10 h-10 rounded-full object-cover">
                    <% } else { %>
                        <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-xl font-bold text-white"><%= user1.username.charAt(0).toUpperCase() %></span>
                        </div>
                    <% } %>
                    <div class="ml-2">
                        <div class="font-medium"><%= user1.username %></div>
                        <div class="text-xs text-purple-200"><%= user1.role %></div>
                    </div>
                </div>

                <div class="text-gray-300">•</div>

                <div class="flex items-center">
                    <% if (user2.profile_image) { %>
                        <img src="<%= user2.profile_image %>" alt="<%= user2.username %>" class="w-10 h-10 rounded-full object-cover">
                    <% } else { %>
                        <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-xl font-bold text-white"><%= user2.username.charAt(0).toUpperCase() %></span>
                        </div>
                    <% } %>
                    <div class="ml-2">
                        <div class="font-medium"><%= user2.username %></div>
                        <div class="text-xs text-purple-200"><%= user2.role %></div>
                    </div>
                </div>
            </div>

            <div>
                <button id="exportChatBtn" class="px-3 py-1 bg-white text-purple-600 rounded-md text-sm hover:bg-purple-100 transition">
                    Export Chat
                </button>
            </div>
        </div>

        <div id="messageContainer" class="h-[600px] overflow-y-auto p-4 space-y-4">
            <% if (messages && messages.length > 0) { %>
                <% messages.forEach(message => { %>
                    <div class="message">
                        <div class="flex items-start">
                            <% if (message.sender_image) { %>
                                <img src="<%= message.sender_image %>" alt="<%= message.sender_name %>" class="w-8 h-8 rounded-full object-cover mr-2">
                            <% } else { %>
                                <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-2">
                                    <span class="text-sm font-bold text-white"><%= message.sender_name.charAt(0).toUpperCase() %></span>
                                </div>
                            <% } %>

                            <div class="max-w-[70%]">
                                <div class="text-xs text-gray-500 mb-1">
                                    <%= message.sender_name %> • <%= new Date(message.created_at).toLocaleString() %>
                                </div>

                                <div class="px-4 py-2 rounded-lg bg-gray-100 text-gray-800 rounded-tl-none">
                                    <% if (message.attachment_url) { %>
                                        <% if (message.attachment_type && message.attachment_type.startsWith('image/')) { %>
                                            <img src="<%= message.attachment_url %>" alt="Attachment" class="max-w-full rounded mb-2">
                                        <% } else if (message.attachment_type && message.attachment_type.startsWith('video/')) { %>
                                            <video controls class="max-w-full rounded mb-2">
                                                <source src="<%= message.attachment_url %>" type="<%= message.attachment_type %>">
                                                Your browser does not support the video tag.
                                            </video>
                                        <% } else if (message.attachment_type && message.attachment_type.startsWith('audio/')) { %>
                                            <audio controls class="max-w-full mb-2">
                                                <source src="<%= message.attachment_url %>" type="<%= message.attachment_type %>">
                                                Your browser does not support the audio tag.
                                            </audio>
                                        <% } else { %>
                                            <div class="bg-gray-200 p-2 rounded flex items-center mb-2">
                                                <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <a href="<%= message.attachment_url %>" target="_blank" class="text-blue-600 hover:underline">
                                                    Attachment
                                                </a>
                                            </div>
                                        <% } %>
                                    <% } %>

                                    <p><%= message.message %></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center text-gray-500 my-8">
                    No messages found in this conversation.
                </div>
            <% } %>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Scroll to bottom of messages
        const messageContainer = document.getElementById('messageContainer');
        messageContainer.scrollTop = messageContainer.scrollHeight;

        // Export chat
        const exportChatBtn = document.getElementById('exportChatBtn');
        exportChatBtn.addEventListener('click', function() {
            // Prepare chat data
            const chatData = [];

            <% if (messages && messages.length > 0) { %>
                <% messages.forEach(message => { %>
                    chatData.push({
                        sender: '<%= message.sender_name %>',
                        timestamp: '<%= new Date(message.created_at).toLocaleString() %>',
                        message: '<%= message.message %>',
                        hasAttachment: <%= message.attachment_url ? 'true' : 'false' %>
                    });
                <% }); %>
            <% } %>

            // Create CSV content
            let csvContent = 'data:text/csv;charset=utf-8,';
            csvContent += 'Sender,Timestamp,Message,Has Attachment\n';

            chatData.forEach(row => {
                csvContent += `${row.sender},${row.timestamp},"${row.message.replace(/"/g, '""')}",${row.hasAttachment}\n`;
            });

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'chat_<%= user1.username %>_<%= user2.username %>_export.csv');
            document.body.appendChild(link);

            // Trigger download
            link.click();
            document.body.removeChild(link);
        });
    });
</script>

