<!-- Add Class Page -->
<div class="container px-6 py-8 mx-auto">
  <div class="flex items-center mb-6">
    <a href="/admin/classes" class="text-primary hover:text-primary-dark mr-4">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
      </svg>
    </a>
    <h1 class="text-2xl font-semibold text-gray-800">Add New Class</h1>
  </div>

  <div class="bg-white shadow-md rounded-lg p-6 max-w-3xl">
    <form id="addClassForm" class="space-y-6">
      <div>
        <label for="className" class="block text-sm font-medium text-gray-700">Class Name <span class="text-red-500">*</span></label>
        <input 
          type="text" 
          id="className" 
          name="className" 
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" 
          placeholder="Enter class name"
          required
        >
      </div>
      
      <div>
        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
        <textarea 
          id="description" 
          name="description" 
          rows="3" 
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50" 
          placeholder="Enter class description (optional)"
        ></textarea>
      </div>
      
      <div>
        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
        <select 
          id="status" 
          name="status" 
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
        >
          <option value="1">Active</option>
          <option value="0">Inactive</option>
        </select>
      </div>
      
      <div class="pt-4">
        <button 
          type="button" 
          onclick="submitForm()" 
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Create Class
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  function submitForm() {
    // Get form values
    const className = document.getElementById('className').value.trim();
    const description = document.getElementById('description').value.trim();
    const status = document.getElementById('status').value;
    
    // Validate
    if (!className) {
      showToast('error', 'Error', 'Class name is required');
      return;
    }
    
    // Prepare data
    const data = {
      name: className,
      description: description,
      is_active: status === "1"
    };
    
    // Send request
    fetch('/admin/classes/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast('success', 'Success', data.message || 'Class added successfully');
        // Redirect to classes list after a short delay
        setTimeout(() => {
          window.location.href = '/admin/classes';
        }, 1500);
      } else {
        showToast('error', 'Error', data.message || 'Failed to add class');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showToast('error', 'Error', 'An unexpected error occurred');
    });
  }
</script> 