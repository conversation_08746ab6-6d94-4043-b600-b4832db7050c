<!-- Classes List Page -->
<div class="container px-6 py-8 mx-auto">
  <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-semibold text-gray-800">Classes</h1>
    <a href="/admin/classes/add" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors duration-200 flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      Add New Class
    </a>
  </div>

  <!-- Classes Table -->
  <div class="bg-white shadow-md rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (classes && classes.length > 0) { %>
            <% classes.forEach(classItem => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= classItem.id %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= classItem.name %></td>
                <td class="px-6 py-4 text-sm text-gray-500"><%= classItem.description || 'N/A' %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= classItem.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                    <%= classItem.is_active ? 'Active' : 'Inactive' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center space-x-3">
                  <a href="/admin/classes/edit/<%= classItem.id %>" class="text-indigo-600 hover:text-indigo-900">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </a>
                  <button 
                    onclick="confirmDelete(<%= classItem.id %>, '<%= classItem.name %>')" 
                    class="text-red-600 hover:text-red-900"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </td>
              </tr>
            <% }) %>
          <% } else { %>
            <tr>
              <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No classes found. Add a new class to get started.</td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden">
  <div class="fixed inset-0 bg-black opacity-50"></div>
  <div class="relative z-10 flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
        <p class="text-gray-700 mb-6">Are you sure you want to delete class "<span id="deleteClassName"></span>"? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
          <button 
            onclick="closeDeleteModal()" 
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            id="confirmDeleteBtn"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  let classIdToDelete = null;

  function confirmDelete(id, name) {
    classIdToDelete = id;
    document.getElementById('deleteClassName').textContent = name;
    document.getElementById('deleteModal').classList.remove('hidden');
    
    document.getElementById('confirmDeleteBtn').onclick = () => {
      deleteClass(id);
    };
  }

  function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    classIdToDelete = null;
  }

  function deleteClass(id) {
    fetch(`/admin/classes/delete/${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      closeDeleteModal();
      if (data.success) {
        showToast('success', 'Success', data.message || 'Class deleted successfully');
        // Remove the row from the table
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        showToast('error', 'Error', data.message || 'Failed to delete class');
      }
    })
    .catch(error => {
      closeDeleteModal();
      console.error('Error:', error);
      showToast('error', 'Error', 'An unexpected error occurred');
    });
  }
</script> 