<!-- Admin Dashboard -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
  <!-- Total Users Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-indigo-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm"><%= __('admin.users') %></p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.users %></h3>
        </div>
      </div>
    </div>
  </div>



  <!-- Total Tests Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-purple-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm"><%= __('admin.exams') %></p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.tests %></h3>
          <p class="text-xs text-gray-500 mt-1">Active: <%= stats.activeTests %> | Deleted: <%= stats.deletedTests %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Total Questions Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-blue-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm"><%= __('admin.questions') %></p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.questions %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Attempts Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-green-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm"><%= __('admin.testAttempts') %></p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.attempts %></h3>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Recent Activity -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden lg:col-span-2">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold"><%= __('admin.recentActivity') %></h2>
    </div>
    <div class="p-6">
      <% if (recentActivity && recentActivity.length > 0) { %>
        <div class="space-y-4">
          <% recentActivity.forEach(activity => { %>
            <div class="flex items-start">
              <% if (activity.type === 'user_registered') { %>
                <div class="bg-indigo-100 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-gray-800">New user registered: <span class="font-semibold"><%= activity.subject %></span></p>
                  <p class="text-gray-500 text-sm"><%= formatDateTime(activity.timestamp) %></p>
                </div>
              <% } else if (activity.type === 'test_created') { %>
                <div class="bg-purple-100 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-gray-800">New test created: <span class="font-semibold"><%= activity.subject %></span></p>
                  <p class="text-gray-500 text-sm"><%= formatDateTime(activity.timestamp) %></p>
                </div>
              <% } %>
            </div>
          <% }); %>
        </div>
      <% } else { %>
        <div class="text-center py-8 text-gray-500">
          <p>No recent activity found.</p>
        </div>
      <% } %>
    </div>
  </div>

  <!-- System Status -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold"><%= __('admin.systemStatus') %></h2>
    </div>
    <div class="p-6">
      <div class="space-y-4">
        <div class="flex justify-between items-center">
          <span class="text-gray-700"><%= __('common.status') %>:</span>
          <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-semibold"><%= systemStatus.status %></span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-700"><%= __('admin.lastBackup') %>:</span>
          <span class="text-gray-800"><%= systemStatus.lastBackup %></span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-700"><%= __('admin.activeUsers') %>:</span>
          <a href="/admin/users/active" class="text-indigo-600 hover:text-indigo-800 font-medium">
            <%= systemStatus.activeUsers %> <span class="text-xs">(View Details)</span>
          </a>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-700"><%= __('admin.serverLoad') %>:</span>
          <span class="text-gray-800"><%= systemStatus.serverLoad %></span>
        </div>
      </div>

      <div class="mt-6">
        <a href="/admin/settings/backup" class="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded-md hover:bg-purple-700 transition">
          <%= __('admin.backupNow') %>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-purple-600 text-white p-4">
    <h2 class="text-xl font-semibold"><%= __('admin.quickActions') %></h2>
  </div>
  <div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <a href="/admin/tests/add" class="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
        <span class="text-purple-800 font-medium"><%= __('admin.createTest') %></span>
      </a>

      <a href="/admin/users/add" class="bg-indigo-50 hover:bg-indigo-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-indigo-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
        </svg>
        <span class="text-indigo-800 font-medium"><%= __('admin.addUser') %></span>
      </a>

      <a href="/admin/questions/add" class="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-blue-800 font-medium"><%= __('admin.addQuestion') %></span>
      </a>

      <a href="/admin/reports" class="bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="text-green-800 font-medium"><%= __('admin.viewReports') %></span>
      </a>

      <a href="/admin/users/pending-approvals" class="bg-amber-50 hover:bg-amber-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-amber-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
        </svg>
        <span class="text-amber-800 font-medium">Pending Approvals</span>
        <% if (locals.pendingApprovals && pendingApprovals > 0) { %>
          <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full mt-1"><%= pendingApprovals %></span>
        <% } %>
      </a>

      <a href="/admin/access-requests" class="bg-yellow-50 hover:bg-yellow-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
        </svg>
        <span class="text-yellow-800 font-medium">Access Requests</span>
        <% if (locals.pendingAccessRequests && pendingAccessRequests > 0) { %>
          <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full mt-1"><%= pendingAccessRequests %></span>
        <% } %>
      </a>
    </div>
  </div>
</div>