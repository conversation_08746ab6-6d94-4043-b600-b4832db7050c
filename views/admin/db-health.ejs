

<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/admin/error-logs" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Error Logs
        </a>
    </div>

    <!-- Database Status Summary -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Database Status Summary</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <% 
                const connections = dbStatus.find(item => item.Variable_name === 'Threads_connected');
                const maxConnections = dbStatus.find(item => item.Variable_name === 'max_connections');
                const uptime = dbStatus.find(item => item.Variable_name === 'Uptime');
                const questions = dbStatus.find(item => item.Variable_name === 'Questions');
                const slowQueries = dbStatus.find(item => item.Variable_name === 'Slow_queries');
                
                // Calculate uptime in days, hours, minutes
                const uptimeSeconds = parseInt(uptime ? uptime.Value : 0);
                const uptimeDays = Math.floor(uptimeSeconds / 86400);
                const uptimeHours = Math.floor((uptimeSeconds % 86400) / 3600);
                const uptimeMinutes = Math.floor((uptimeSeconds % 3600) / 60);
                
                // Calculate connection percentage
                const connectionPercentage = connections && maxConnections ? 
                    Math.round((parseInt(connections.Value) / parseInt(maxConnections.Value)) * 100) : 0;
                
                // Determine connection status color
                let connectionColor = 'green';
                if (connectionPercentage > 80) connectionColor = 'red';
                else if (connectionPercentage > 50) connectionColor = 'yellow';
                %>
                
                <!-- Connections -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Active Connections</h3>
                    <div class="flex items-end">
                        <p class="text-2xl font-bold text-gray-900"><%= connections ? connections.Value : 'N/A' %></p>
                        <p class="text-sm text-gray-500 ml-2">/ <%= maxConnections ? maxConnections.Value : 'N/A' %></p>
                    </div>
                    <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-<%= connectionColor %>-500 h-2.5 rounded-full" style="width: <%= connectionPercentage %>%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1"><%= connectionPercentage %>% of maximum connections</p>
                </div>
                
                <!-- Uptime -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Database Uptime</h3>
                    <p class="text-2xl font-bold text-gray-900"><%= uptimeDays %> days</p>
                    <p class="text-sm text-gray-500"><%= uptimeHours %> hours, <%= uptimeMinutes %> minutes</p>
                </div>
                
                <!-- Queries -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Total Queries</h3>
                    <p class="text-2xl font-bold text-gray-900"><%= questions ? parseInt(questions.Value).toLocaleString() : 'N/A' %></p>
                    <p class="text-sm text-gray-500">Since last restart</p>
                </div>
                
                <!-- Slow Queries -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Slow Queries</h3>
                    <p class="text-2xl font-bold text-gray-900"><%= slowQueries ? parseInt(slowQueries.Value).toLocaleString() : 'N/A' %></p>
                    <p class="text-sm text-gray-500">Since last restart</p>
                </div>
            </div>
            
            <div class="mt-6">
                <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Database Tables -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Database Tables</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engine</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rows</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Size</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Index Size</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Update</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% tableStatus.forEach(table => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <%= table.Name %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= table.Engine || 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= table.Rows ? parseInt(table.Rows).toLocaleString() : 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= formatDataSize(table.Data_length) %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= formatDataSize(table.Index_length) %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= table.Update_time ? formatDateTime(table.Update_time) : 'N/A' %>
                            </td>
                        </tr>
                    <% }) %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Active Processes -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Active Database Processes</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Host</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Database</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Command</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Info</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (processList.length === 0) { %>
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">No active processes</td>
                        </tr>
                    <% } else { %>
                        <% processList.forEach(process => { %>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.Id %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.User %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.Host %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.db || 'N/A' %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.Command %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.Time %> sec
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= process.State || 'N/A' %>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                    <div class="max-h-20 overflow-y-auto">
                                        <pre class="text-xs"><%= process.Info || 'N/A' %></pre>
                                    </div>
                                </td>
                            </tr>
                        <% }) %>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- InnoDB Status -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">InnoDB Engine Status</h2>
        </div>
        <div class="p-6">
            <div class="bg-gray-50 p-4 rounded-md overflow-auto">
                <pre class="text-xs text-gray-800 whitespace-pre-wrap"><%= engineStatus ? engineStatus.Status : 'InnoDB status information not available' %></pre>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to format data size
    function formatDataSize(bytes) {
        if (!bytes) return 'N/A';
        
        bytes = parseInt(bytes);
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 Bytes';
        const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
        return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
    }
    
    // Refresh button functionality
    document.getElementById('refreshBtn').addEventListener('click', function() {
        location.reload();
    });
</script>

<%- include('../partials/footer') %>
