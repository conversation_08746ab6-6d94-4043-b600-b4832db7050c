<!DOCTYPE html>
<html lang="<%= locals.req && req.locale ? req.locale : 'en' %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> | <%= __('app.name') %></title>
  <link rel="stylesheet" href="/css/tailwind.css">
  <link rel="stylesheet" href="/css/custom.css">
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/floating-chat.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col <%= locals.userId ? 'logged-in' : '' %>">

  <div class="flash-container fixed top-4 right-4 z-50">
    <% if (locals.flashSuccess) { %>
      <div class="flash-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>

    <% if (locals.flashError) { %>
      <div class="flash-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashError %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>
  </div>

  <main class="flex-grow">

<%- include('../partials/admin-navbar') %>

<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/admin/error-logs" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Error Logs
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800">Log ID: <%= log.log_id || log.id %></h2>
                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                    <%= log.category || '' %><%= log.level ? (log.category ? ' (' + log.level + ')' : log.level) : 'Error' %>
                </span>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Timestamp</h3>
                    <p class="text-base text-gray-900"><%= formatDateTime(log.timestamp) %></p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">User</h3>
                    <p class="text-base text-gray-900"><%= log.user_username || 'System' %> (ID: <%= log.user_id || 'N/A' %>)</p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">IP Address</h3>
                    <p class="text-base text-gray-900"><%= log.ip_address || 'N/A' %></p>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-1">Route</h3>
                    <p class="text-base text-gray-900"><%= log.route || 'N/A' %></p>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-500 mb-2">Error Details</h3>
                <div class="bg-gray-50 p-4 rounded-md overflow-auto">
                    <div class="grid grid-cols-1 gap-4">
                        <% if (log.error_message) { %>
                            <div>
                                <h4 class="text-sm font-semibold text-gray-700">Error Message</h4>
                                <pre class="mt-1 text-sm text-gray-800 bg-gray-100 p-3 rounded overflow-x-auto whitespace-pre-wrap"><%= log.error_message %></pre>
                            </div>
                        <% } %>

                        <% if (log.operation) { %>
                            <div>
                                <h4 class="text-sm font-semibold text-gray-700">Operation</h4>
                                <p class="mt-1 text-sm text-gray-800"><%= log.operation %></p>
                            </div>
                        <% } %>

                        <% if (log.details) { %>
                            <div>
                                <h4 class="text-sm font-semibold text-gray-700">Details</h4>
                                <% try { %>
                                    <% const parsedDetails = JSON.parse(log.details); %>
                                    <div class="grid grid-cols-1 gap-2 mt-1">
                                        <% Object.entries(parsedDetails).forEach(([key, value]) => { %>
                                            <div>
                                                <h5 class="text-xs font-semibold text-gray-600"><%= key %></h5>
                                                <% if (typeof value === 'object' && value !== null) { %>
                                                    <pre class="text-sm text-gray-800 bg-gray-100 p-2 rounded overflow-x-auto"><%= JSON.stringify(value, null, 2) %></pre>
                                                <% } else if (key === 'stack' || key === 'stacktrace') { %>
                                                    <pre class="text-sm text-gray-800 bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap"><%= value %></pre>
                                                <% } else { %>
                                                    <p class="text-sm text-gray-800"><%= value %></p>
                                                <% } %>
                                            </div>
                                        <% }) %>
                                    </div>
                                <% } catch (e) { %>
                                    <pre class="mt-1 text-sm text-gray-800 bg-gray-100 p-3 rounded overflow-x-auto whitespace-pre-wrap"><%= log.details %></pre>
                                <% } %>
                            </div>
                        <% } %>

                        <% if (log.status) { %>
                            <div>
                                <h4 class="text-sm font-semibold text-gray-700">Status</h4>
                                <p class="mt-1 text-sm text-gray-800"><%= log.status %></p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>

            <% if (log.additional_data) { %>
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">Additional Data</h3>
                    <div class="bg-gray-50 p-4 rounded-md">
                        <pre class="text-sm text-gray-800 whitespace-pre-wrap"><%= typeof log.additional_data === 'string' ? log.additional_data : JSON.stringify(log.additional_data, null, 2) %></pre>
                    </div>
                </div>
            <% } %>

            <div class="mt-8 flex justify-between">
                <a href="/admin/error-logs" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition">
                    Back to Error Logs
                </a>

                <div class="flex space-x-2">
                    <button onclick="window.print()" class="bg-blue-100 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-200 transition flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print
                    </button>
                    <button onclick="copyToClipboard()" class="bg-green-100 text-green-700 px-4 py-2 rounded-md hover:bg-green-200 transition flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                        </svg>
                        Copy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function copyToClipboard() {
        // Create a formatted string with all log details
        const logData = {
            id: '<%= log.log_id || log.id %>',
            category: '<%= log.category || "" %>',
            level: '<%= log.level || "" %>',
            timestamp: '<%= formatDateTime(log.timestamp) %>',
            user: '<%= log.user_username || "System" %> (ID: <%= log.user_id || "N/A" %>)',
            ip_address: '<%= log.ip_address || "N/A" %>',
            route: '<%= log.route || "N/A" %>',
            details: `<%= log.details ? log.details.replace(/\n/g, "\\n").replace(/"/g, '\\"') : "No details available" %>`
        };

        const formattedData = JSON.stringify(logData, null, 2);

        // Create a temporary textarea element to copy the text
        const textarea = document.createElement('textarea');
        textarea.value = formattedData;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);

        // Show a notification
        alert('Log details copied to clipboard');
    }
</script>

</main>

<!-- Footer -->
<footer class="bg-gray-800 text-white py-6 mt-auto">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div>
                <h3 class="text-lg font-semibold">Meritorious EP</h3>
                <p class="text-sm text-gray-400">© <%= new Date().getFullYear() %> All rights reserved</p>
            </div>
            <div class="mt-4 md:mt-0">
                <p class="text-sm text-gray-400">Build your future with confidence</p>
            </div>
        </div>
    </div>
</footer>

<script>
    // Close flash messages
    document.querySelectorAll('.close-flash').forEach(function(element) {
        element.addEventListener('click', function() {
            this.parentElement.style.display = 'none';
        });
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        document.querySelectorAll('.flash-success, .flash-error').forEach(function(element) {
            element.style.display = 'none';
        });
    }, 5000);

    // Language switcher functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to set a cookie
        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }

        // Add event listeners to language switcher buttons
        document.querySelectorAll('.language-option').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                const lang = this.getAttribute('data-lang');
                setCookie('lang', lang, 365); // Set cookie for 1 year
                window.location.reload(); // Reload the page to apply the new language
            });
        });
    });
</script>

</body>
</html>
