<!DOCTYPE html>
<html lang="<%= locals.req && req.locale ? req.locale : 'en' %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> | <%= __('app.name') %></title>
  <link rel="stylesheet" href="/css/tailwind.css">
  <link rel="stylesheet" href="/css/custom.css">
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/floating-chat.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col <%= locals.userId ? 'logged-in' : '' %>">

  <div class="flash-container fixed top-4 right-4 z-50">
    <% if (locals.flashSuccess) { %>
      <div class="flash-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>

    <% if (locals.flashError) { %>
      <div class="flash-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashError %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>
  </div>

  <main class="flex-grow">


<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2"><%= pageTitle %></h1>
        <p class="text-gray-600">View and analyze system errors and database issues</p>
    </div>

    <!-- Tabs -->
    <div class="mb-6 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
            <li class="mr-2">
                <a href="#system-logs" class="inline-block p-4 border-b-2 border-blue-600 rounded-t-lg active text-blue-600" aria-current="page">
                    System Error Logs
                </a>
            </li>
            <li class="mr-2">
                <a href="#db-errors" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Database Errors
                </a>
            </li>
            <li class="mr-2">
                <a href="#query-errors" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Query Errors
                </a>
            </li>
            <li>
                <a href="/admin/error-logs/db-health" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Database Health
                </a>
            </li>
        </ul>
    </div>

    <!-- Filter Form -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form action="/admin/error-logs" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Log Type</label>
                <select id="type" name="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="all" <%= filters.logType === 'all' ? 'selected' : '' %>>All Errors</option>
                    <% logTypes.forEach(type => { %>
                        <option value="<%= type.category %>" <%= filters.logType === type.category ? 'selected' : '' %>><%= type.category %></option>
                    <% }) %>
                </select>
            </div>
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="search" name="search" value="<%= filters.search %>" placeholder="Search in logs..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="startDate" name="startDate" value="<%= filters.startDate %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="endDate" name="endDate" value="<%= filters.endDate %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- System Error Logs -->
        <div id="system-logs" class="tab-pane active">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">System Error Logs</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing <%= logs.length %> of <%= pagination.totalItems || 0 %> error logs</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (logs.length === 0) { %>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No error logs found</td>
                                </tr>
                            <% } else { %>
                                <% logs.forEach(log => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(log.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                <%= log.category || log.level || 'Error' %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= log.user_username || 'System' %>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                            <%
                                                let errorMessage = '';
                                                if (log.error_message) {
                                                    errorMessage = log.error_message;
                                                } else if (log.details) {
                                                    try {
                                                        const details = JSON.parse(log.details);
                                                        if (details.message) {
                                                            errorMessage = details.message;
                                                        } else {
                                                            errorMessage = log.details;
                                                        }
                                                    } catch (e) {
                                                        errorMessage = log.details;
                                                    }
                                                } else if (log.status) {
                                                    errorMessage = log.status;
                                                } else if (log.operation) {
                                                    errorMessage = log.operation;
                                                }

                                                if (errorMessage) {
                                                    if (errorMessage.length > 100) {
                                                        errorMessage = errorMessage.substring(0, 100) + '...';
                                                    }
                                                    %>
                                                    <%= errorMessage %>
                                                <% } else { %>
                                                    <span class="text-gray-400">No details available</span>
                                                <% }
                                            %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="/admin/error-logs/view/<%= log.log_id || log.id %>" class="text-blue-600 hover:text-blue-900">View Details</a>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <% if (pagination && pagination.totalPages > 1) { %>
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium"><%= logs.length > 0 ? (pagination.page - 1) * pagination.perPage + 1 : 0 %></span> to <span class="font-medium"><%= Math.min(pagination.page * pagination.perPage, pagination.totalItems || 0) %></span> of <span class="font-medium"><%= pagination.totalItems || 0 %></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <% if (pagination.page > 1) { %>
                                        <a href="?page=<%= pagination.page - 1 %>&type=<%= filters.logType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Previous</span>
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    <% } %>

                                    <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                        <a href="?page=<%= i %>&type=<%= filters.logType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <%= pagination.page === i ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50' %>">
                                            <%= i %>
                                        </a>
                                    <% } %>

                                    <% if (pagination.page < pagination.totalPages) { %>
                                        <a href="?page=<%= pagination.page + 1 %>&type=<%= filters.logType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Next</span>
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    <% } %>
                                </nav>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Database Errors -->
        <div id="db-errors" class="tab-pane hidden">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Database Errors</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing last 100 database-related errors</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Context</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variable</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (dbErrors.length === 0) { %>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No database errors found</td>
                                </tr>
                            <% } else { %>
                                <% dbErrors.forEach(error => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(error.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                <%= error.context %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= error.variable_name %>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                            <div class="max-h-20 overflow-y-auto">
                                                <pre class="text-xs"><%= error.variable_value %></pre>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= error.user_id || 'System' %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Query Errors -->
        <div id="query-errors" class="tab-pane hidden">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">SQL Query Errors</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing last 100 SQL query errors</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error Code</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error Message</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Query</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (queryErrors.length === 0) { %>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No query errors found</td>
                                </tr>
                            <% } else { %>
                                <% queryErrors.forEach(error => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(error.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                <%= error.error_code %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">
                                            <%= error.error_message %>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">
                                            <div class="max-h-20 overflow-y-auto">
                                                <pre class="text-xs bg-gray-100 p-2 rounded"><%= error.query %></pre>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= error.route || 'Unknown' %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('a[href^="#"]');
        const tabContents = document.querySelectorAll('.tab-pane');

        // Show the tab content based on the URL hash or default to first tab
        function showTab(tabId) {
            tabContents.forEach(content => {
                content.classList.add('hidden');
                if (content.id === tabId.substring(1)) {
                    content.classList.remove('hidden');
                }
            });

            tabs.forEach(tab => {
                tab.classList.remove('border-blue-600', 'text-blue-600');
                tab.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                if (tab.getAttribute('href') === tabId) {
                    tab.classList.add('border-blue-600', 'text-blue-600');
                    tab.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                }
            });
        }

        // Set initial tab based on URL hash or default to first tab
        const initialTab = window.location.hash || '#system-logs';
        showTab(initialTab);

        // Handle tab clicks
        tabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const tabId = this.getAttribute('href');
                    showTab(tabId);
                    window.location.hash = tabId;
                }
            });
        });

        // Handle hash change
        window.addEventListener('hashchange', function() {
            const tabId = window.location.hash || '#system-logs';
            showTab(tabId);
        });
    });
</script>

</main>

<!-- Footer -->
<footer class="bg-gray-800 text-white py-6 mt-auto">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div>
                <h3 class="text-lg font-semibold">Meritorious EP</h3>
                <p class="text-sm text-gray-400">© <%= new Date().getFullYear() %> All rights reserved</p>
            </div>
            <div class="mt-4 md:mt-0">
                <p class="text-sm text-gray-400">Build your future with confidence</p>
            </div>
        </div>
    </div>
</footer>

<script>
    // Close flash messages
    document.querySelectorAll('.close-flash').forEach(function(element) {
        element.addEventListener('click', function() {
            this.parentElement.style.display = 'none';
        });
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        document.querySelectorAll('.flash-success, .flash-error').forEach(function(element) {
            element.style.display = 'none';
        });
    }, 5000);

    // Mobile menu functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Mobile users submenu toggle
        const mobileUsersToggle = document.getElementById('mobile-users-toggle');
        const mobileUsersMenu = document.getElementById('mobile-users-menu');

        if (mobileUsersToggle && mobileUsersMenu) {
            mobileUsersToggle.addEventListener('click', function() {
                mobileUsersMenu.classList.toggle('hidden');
            });
        }

        // Mobile settings submenu toggle
        const mobileSettingsToggle = document.getElementById('mobile-settings-toggle');
        const mobileSettingsMenu = document.getElementById('mobile-settings-menu');

        if (mobileSettingsToggle && mobileSettingsMenu) {
            mobileSettingsToggle.addEventListener('click', function() {
                mobileSettingsMenu.classList.toggle('hidden');
            });
        }
    });

    // Language switcher functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Desktop language dropdown toggle
        const languageMenuButton = document.getElementById('language-menu-button-navbar');
        const languageDropdown = document.querySelector('.language-dropdown-navbar');

        if (languageMenuButton && languageDropdown) {
            languageMenuButton.addEventListener('click', function() {
                languageDropdown.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!languageMenuButton.contains(event.target) && !languageDropdown.contains(event.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        }

        // Function to set a cookie
        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }

        // Add event listeners to language switcher buttons
        document.querySelectorAll('.language-option').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                const lang = this.getAttribute('data-lang');
                setCookie('lang', lang, 365); // Set cookie for 1 year
                window.location.reload(); // Reload the page to apply the new language
            });
        });
    });
</script>

</body>
</html>
