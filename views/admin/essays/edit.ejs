<!-- Edit Essay Form -->
<div class="container mx-auto px-4 py-8">
  <div class="mb-6">
    <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
  </div>

  <div class="bg-white shadow-md rounded-lg p-6">
    <form action="/admin/essays/<%= essay.essay_id %>" method="POST" enctype="multipart/form-data" class="space-y-6">
      <div>
        <label for="title" class="block text-sm font-medium text-gray-700">Essay Title</label>
        <input type="text" id="title" name="title" value="<%= essay.title %>"
               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
               required>
        <p class="mt-1 text-sm text-gray-500">
          Give your essay a descriptive title. This will be used to identify the essay when linking to questions.
        </p>
      </div>

      <div>
        <label for="content" class="block text-sm font-medium text-gray-700">Essay Content</label>
        <textarea id="content" name="content" rows="15" 
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" 
                  required><%= essay.content %></textarea>
        <p class="mt-1 text-sm text-gray-500">
          Enter the full text of the essay or passage. This will be displayed to students when answering linked questions.
        </p>
      </div>

      <div>
        <label for="pdf_file" class="block text-sm font-medium text-gray-700">Upload PDF (Optional)</label>
        <div class="mt-1 flex items-center">
          <input type="file" id="pdf_file" name="pdf_file" accept="application/pdf"
                 class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
        </div>
        <% if (essay.pdf_path) { %>
          <div class="mt-2 flex items-center">
            <span class="text-sm text-gray-500 mr-2">Current PDF:</span>
            <a href="<%= essay.pdf_path %>" target="_blank" class="text-blue-600 hover:text-blue-900 text-sm">
              <svg class="h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              View Current PDF
            </a>
            <span class="text-sm text-gray-500 ml-2">(Upload a new file to replace)</span>
          </div>
        <% } %>
        <p class="mt-1 text-sm text-gray-500">
          You can optionally upload a PDF version of the essay. This is useful for essays with complex formatting or images.
        </p>
      </div>

      <div class="flex justify-end space-x-3">
        <a href="/admin/essays" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">Cancel</a>
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
          Update Essay
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Add rich text editor if needed
  document.addEventListener('DOMContentLoaded', function() {
    // You can initialize a rich text editor here if desired
  });
</script>
