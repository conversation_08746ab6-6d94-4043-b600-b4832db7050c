<!-- Essay Management Index -->
<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
    <a href="/admin/essays/add" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
      Add New Essay
    </a>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <!-- Total Essays Card -->
    <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
      <div class="flex items-center">
        <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
          <svg class="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div class="ml-4">
          <div class="text-sm font-medium text-gray-500">Total Essays</div>
          <div class="text-lg font-semibold text-gray-900"><%= pagination.totalItems %></div>
        </div>
      </div>
    </div>
    
    <!-- Essays with PDF Card -->
    <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
      <div class="flex items-center">
        <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
          <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="ml-4">
          <div class="text-sm font-medium text-gray-500">Essays with PDF</div>
          <div class="text-lg font-semibold text-gray-900">
            <%= essays.filter(essay => essay.pdf_path).length %>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Linked Questions Card -->
    <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
      <div class="flex items-center">
        <div class="flex-shrink-0 bg-purple-100 rounded-full p-3">
          <svg class="h-6 w-6 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="ml-4">
          <div class="text-sm font-medium text-gray-500">Linked Questions</div>
          <div class="text-lg font-semibold text-gray-900">
            <span id="linkedQuestionsCount">-</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Essays List -->
  <div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PDF</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (essays.length === 0) { %>
          <tr>
            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
              No essays found. <a href="/admin/essays/add" class="text-blue-600 hover:underline">Add your first essay</a>.
            </td>
          </tr>
        <% } else { %>
          <% essays.forEach(essay => { %>
            <tr>
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900"><%= essay.title %></div>
                <div class="text-xs text-gray-500">
                  <%= essay.content.length > 100 ? essay.content.substring(0, 100) + '...' : essay.content %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><%= essay.creator_name %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">
                  <%= new Date(essay.created_at).toLocaleDateString('en-US', { 
                    day: '2-digit', 
                    month: 'short', 
                    year: 'numeric' 
                  }) %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <% if (essay.pdf_path) { %>
                  <a href="<%= essay.pdf_path %>" target="_blank" class="text-blue-600 hover:text-blue-900">
                    <svg class="h-5 w-5 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    View PDF
                  </a>
                <% } else { %>
                  <span class="text-gray-400">No PDF</span>
                <% } %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="/admin/essays/<%= essay.essay_id %>/view" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                <a href="/admin/essays/<%= essay.essay_id %>/edit" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                <button 
                  onclick="confirmDelete(<%= essay.essay_id %>)" 
                  class="text-red-600 hover:text-red-900">Delete</button>
              </td>
            </tr>
          <% }); %>
        <% } %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <% if (pagination.totalPages > 1) { %>
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
      <div class="flex-1 flex justify-between sm:hidden">
        <% if (pagination.page > 1) { %>
          <a href="?page=<%= pagination.page - 1 %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Previous
          </a>
        <% } %>
        <% if (pagination.page < pagination.totalPages) { %>
          <a href="?page=<%= pagination.page + 1 %>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Next
          </a>
        <% } %>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Showing <span class="font-medium"><%= ((pagination.page - 1) * pagination.perPage) + 1 %></span> to 
            <span class="font-medium"><%= Math.min(pagination.page * pagination.perPage, pagination.totalItems) %></span> of 
            <span class="font-medium"><%= pagination.totalItems %></span> results
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <!-- First Page -->
            <a href="?page=1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">First</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="11 17 6 12 11 7"></polyline>
                <polyline points="18 17 13 12 18 7"></polyline>
              </svg>
            </a>
            
            <!-- Previous Page -->
            <% if (pagination.page > 1) { %>
              <a href="?page=<%= pagination.page - 1 %>" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
            <% } else { %>
              <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </span>
            <% } %>
            
            <!-- Page Numbers -->
            <% 
              let startPage = Math.max(1, pagination.page - 2);
              let endPage = Math.min(pagination.totalPages, pagination.page + 2);
              
              if (startPage > 1) { %>
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
            <% } %>
            
            <% for (let i = startPage; i <= endPage; i++) { %>
              <% if (i === pagination.page) { %>
                <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                  <%= i %>
                </span>
              <% } else { %>
                <a href="?page=<%= i %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <%= i %>
                </a>
              <% } %>
            <% } %>
            
            <% if (endPage < pagination.totalPages) { %>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
            <% } %>
            
            <!-- Next Page -->
            <% if (pagination.page < pagination.totalPages) { %>
              <a href="?page=<%= pagination.page + 1 %>" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
            <% } else { %>
              <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </span>
            <% } %>
            
            <!-- Last Page -->
            <a href="?page=<%= pagination.totalPages %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Last</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="13 7 18 12 13 17"></polyline>
                <polyline points="6 7 11 12 6 17"></polyline>
              </svg>
            </a>
          </nav>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
      <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              Delete Essay
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500">
                Are you sure you want to delete this essay? This action cannot be undone.
                Any questions linked to this essay will be unlinked.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <form id="deleteForm" method="POST">
          <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
        </form>
        <button type="button" onclick="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Fetch linked questions count
  fetch('/admin/api/essays/linked-count')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        document.getElementById('linkedQuestionsCount').textContent = data.count;
      }
    })
    .catch(error => {
      console.error('Error fetching linked questions count:', error);
    });

  // Delete confirmation modal
  function confirmDelete(essayId) {
    const modal = document.getElementById('deleteModal');
    const form = document.getElementById('deleteForm');
    
    form.action = `/admin/essays/${essayId}/delete`;
    modal.classList.remove('hidden');
  }
  
  function closeModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
  }
</script>
