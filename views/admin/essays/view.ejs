<!-- View Essay -->
<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold"><%= essay.title %></h1>
    <div class="flex space-x-2">
      <a href="/admin/essays/<%= essay.essay_id %>/edit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
        Edit Essay
      </a>
      <button onclick="confirmDelete(<%= essay.essay_id %>)" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
        Delete Essay
      </button>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Essay Details -->
    <div class="md:col-span-2">
      <div class="bg-white shadow-md rounded-lg p-6">
        <div class="mb-4">
          <h2 class="text-lg font-semibold text-gray-800 mb-2">Essay Content</h2>
          <div class="prose max-w-none">
            <% const formattedContent = essay.content.replace(/\n/g, '<br>'); %>
            <%- formattedContent %>
          </div>
        </div>

        <% if (essay.pdf_path) { %>
          <div class="mt-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-2">PDF Version</h2>
            <div class="border rounded-lg overflow-hidden">
              <iframe src="<%= essay.pdf_path %>" width="100%" height="500" class="border-0"></iframe>
            </div>
            <div class="mt-2">
              <a href="<%= essay.pdf_path %>" target="_blank" class="text-blue-600 hover:text-blue-900">
                <svg class="h-5 w-5 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open PDF in new tab
              </a>
            </div>
          </div>
        <% } %>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="md:col-span-1">
      <!-- Essay Metadata -->
      <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Essay Details</h2>
        <div class="space-y-3">
          <div>
            <span class="text-sm font-medium text-gray-500">Created By</span>
            <p class="text-sm text-gray-900"><%= essay.creator_name %></p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-500">Created Date</span>
            <p class="text-sm text-gray-900">
              <%= formatDateTime(essay.created_at) %>
            </p>
          </div>
          <div>
            <span class="text-sm font-medium text-gray-500">Last Updated</span>
            <p class="text-sm text-gray-900">
              <%= formatDateTime(essay.updated_at) %>
            </p>
          </div>
        </div>
      </div>

      <!-- Linked Questions -->
      <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-800">Linked Questions</h2>
          <a href="/admin/questions/add?essay_id=<%= essay.essay_id %>" class="text-blue-600 hover:text-blue-900 text-sm">
            <svg class="h-4 w-4 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Question
          </a>
        </div>

        <% if (questions && questions.length > 0) { %>
          <ul class="divide-y divide-gray-200">
            <% questions.forEach(question => { %>
              <li class="py-3">
                <a href="/admin/questions/<%= question.question_id %>/edit" class="block hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <div class="truncate">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        <%= question.question_text.length > 50 ? question.question_text.substring(0, 50) + '...' : question.question_text %>
                      </p>
                      <p class="text-xs text-gray-500">
                        Type: <%= question.question_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) %>
                      </p>
                    </div>
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </a>
              </li>
            <% }); %>
          </ul>
        <% } else { %>
          <div class="text-center py-4">
            <p class="text-sm text-gray-500">No questions linked to this essay yet.</p>
            <a href="/admin/questions/add?essay_id=<%= essay.essay_id %>" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-900">
              <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Create a question
            </a>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
      <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              Delete Essay
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500">
                Are you sure you want to delete this essay? This action cannot be undone.
                <% if (questions && questions.length > 0) { %>
                  <strong>This will unlink <%= questions.length %> question<%= questions.length > 1 ? 's' : '' %> currently associated with this essay.</strong>
                <% } %>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <form id="deleteForm" method="POST" action="/admin/essays/<%= essay.essay_id %>/delete">
          <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
        </form>
        <button type="button" onclick="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Delete confirmation modal
  function confirmDelete(essayId) {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('hidden');
  }

  function closeModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
  }
</script>
