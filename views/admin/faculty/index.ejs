<!-- Faculty Operations Page -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold">Faculty Operations</h1>
          <p class="text-purple-100 mt-1">Manage and view faculty member details</p>
        </div>
        <div class="flex space-x-3">
          <a href="/admin/users/add" class="bg-white text-purple-600 px-4 py-2 rounded-md hover:bg-purple-50 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Faculty
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <form id="filterForm" action="/admin/faculty" method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Faculty</label>
            <div class="relative">
              <input type="text"
                     id="search"
                     name="search"
                     value="<%= query.search || '' %>"
                     placeholder="Search by name, email, or subjects..."
                     class="w-full border border-gray-300 rounded-md shadow-sm pl-10 pr-4 py-2 focus:ring-purple-500 focus:border-purple-500">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
              <option value="">All Status</option>
              <option value="online" <%= query.status === 'online' ? 'selected' : '' %>>Online</option>
              <option value="offline" <%= query.status === 'offline' ? 'selected' : '' %>>Offline</option>
              <option value="active" <%= query.status === 'active' ? 'selected' : '' %>>Active</option>
              <option value="blocked" <%= query.status === 'blocked' ? 'selected' : '' %>>Blocked</option>
            </select>
          </div>

          <!-- Sort By -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <select id="sort" name="sort" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
              <option value="name_asc" <%= query.sort === 'name_asc' ? 'selected' : '' %>>Name (A-Z)</option>
              <option value="name_desc" <%= query.sort === 'name_desc' ? 'selected' : '' %>>Name (Z-A)</option>
              <option value="created_desc" <%= query.sort === 'created_desc' ? 'selected' : '' %>>Newest First</option>
              <option value="created_asc" <%= query.sort === 'created_asc' ? 'selected' : '' %>>Oldest First</option>
            </select>
          </div>
        </div>

        <!-- Quick Filters -->
        <div class="pt-4">
          <h3 class="text-sm font-medium text-gray-700 mb-2">Quick Filters</h3>
          <div class="flex flex-wrap gap-2 mb-4">
            <a href="/admin/faculty<%= query?.search ? '?search=' + query.search : '' %>" class="px-3 py-1 rounded-full <%= !query?.status ? 'bg-purple-700 text-white' : 'bg-purple-100 text-purple-800 hover:bg-purple-200' %> text-sm font-medium">
              All Faculty
            </a>
            <a href="/admin/faculty?status=online<%= query?.search ? '&search=' + query.search : '' %>" class="px-3 py-1 rounded-full <%= query?.status === 'online' ? 'bg-green-700 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200' %> text-sm font-medium">
              Online
            </a>
            <a href="/admin/faculty?status=active<%= query?.search ? '&search=' + query.search : '' %>" class="px-3 py-1 rounded-full <%= query?.status === 'active' ? 'bg-blue-700 text-white' : 'bg-blue-100 text-blue-800 hover:bg-blue-200' %> text-sm font-medium">
              Active
            </a>
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-between items-center pt-2">
          <div class="text-sm text-gray-600">
            Total Faculty: <%= pagination.totalFaculty %>
          </div>
          <div class="space-x-2">
            <button type="button"
                    onclick="clearFilters()"
                    class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition">
              Clear Filters
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Faculty List -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Faculty Members</h2>
        <div class="text-sm text-gray-500">
          <% if (pagination && pagination.totalPages > 1) { %>
            Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
          <% } %>
        </div>
      </div>

      <% if (faculty && faculty.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Faculty</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subjects</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% faculty.forEach(member => { %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <% if (member.profile_image) { %>
                          <img class="h-10 w-10 rounded-full object-cover" src="<%= member.profile_image %>" alt="<%= member.username %>">
                        <% } else { %>
                          <div class="h-10 w-10 rounded-full bg-purple-200 flex items-center justify-center">
                            <span class="text-purple-600 font-semibold text-sm">
                              <%= member.username.substring(0, 2).toUpperCase() %>
                            </span>
                          </div>
                        <% } %>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900"><%= member.username %></div>
                        <div class="text-sm text-gray-500"><%= member.name || member.username %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900"><%= member.email %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <%= member.subjects || 'Not assigned' %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                      <% if (member.is_blocked) { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Blocked</span>
                      <% } else if (member.is_online) { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Online</span>
                      <% } else { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Offline</span>
                      <% } %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%
                      const createdDate = new Date(member.created_at);
                      const day = String(createdDate.getDate()).padStart(2, '0');
                      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                      const month = monthNames[createdDate.getMonth()];
                      const year = createdDate.getFullYear();
                    %>
                    <%= `${day}-${month}-${year}` %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <!-- View Details button -->
                      <button id="viewFacultyBtn-<%= member.id %>" 
                              class="view-faculty-btn text-blue-600 hover:text-blue-900 p-1 rounded" 
                              data-faculty-id="<%= member.id %>" 
                              title="View Faculty Details">
                        <i class="fas fa-eye"></i>
                      </button>
                      
                      <!-- Edit button -->
                      <a href="/admin/users/<%= member.id %>/edit" class="text-indigo-600 hover:text-indigo-900 p-1 rounded" title="Edit Faculty">
                        <i class="fas fa-edit"></i>
                      </a>
                      
                      <!-- Profile button -->
                      <a href="/admin/users/<%= member.id %>" class="text-green-600 hover:text-green-900 p-1 rounded" title="View Profile">
                        <i class="fas fa-user"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <% if (pagination && pagination.totalPages > 1) { %>
          <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              <%
                const start = (pagination.currentPage - 1) * pagination.limit + 1;
                const end = Math.min(pagination.currentPage * pagination.limit, pagination.totalFaculty);
              %>
              Showing <%= start %> to <%= end %> of <%= pagination.totalFaculty %> faculty members
            </div>
            <div class="space-x-1">
              <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                <a href="?page=<%= i %>&search=<%= query.search || '' %>&status=<%= query.status || '' %>&sort=<%= query.sort || '' %>"
                   class="inline-flex items-center px-3 py-1 border rounded-md <%= pagination.currentPage === i ? 'bg-purple-600 text-white border-purple-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50' %>">
                  <%= i %>
                </a>
              <% } %>
            </div>
          </div>
        <% } %>
      <% } else { %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No faculty found</h3>
          <p class="mt-1 text-sm text-gray-500">
            <%= (query && query.search) ? 'No faculty match your search criteria.' : 'Get started by adding a new faculty member.' %>
          </p>
          <div class="mt-6">
            <a href="/admin/users/add" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Faculty Member
            </a>
          </div>
        </div>
      <% } %>
    </div>
  </div>
</div>

<!-- Faculty Details Modal -->
<div id="facultyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mx-4">
    <!-- Modal Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="facultyModalTitle" class="text-xl font-semibold">Faculty Details</h3>
      <button id="closeFacultyModalBtn" class="text-white hover:text-gray-200">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div id="facultyModalContent" class="p-6">
      <!-- Loading content will be inserted here -->
    </div>

    <!-- Modal Footer -->
    <div id="facultyModalFooter" class="bg-gray-50 px-6 py-4 rounded-b-lg flex justify-end space-x-3">
      <!-- Footer buttons will be inserted here -->
    </div>
  </div>
</div>

<script>
// Add event listeners for real-time filtering
document.querySelectorAll('#search, #status, #sort').forEach(element => {
    element.addEventListener('change', function() {
        document.getElementById('filterForm').submit();
    });
});

// Add debounced search for text input
let searchTimeout;
const searchInput = document.getElementById('search');

searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        document.getElementById('filterForm').submit();
    }, 500); // Wait 500ms after user stops typing
});

function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('status').value = '';
    document.getElementById('sort').value = 'name_asc';
    document.getElementById('filterForm').submit();
}

// Event delegation for faculty action buttons using jQuery
$(document).ready(function() {
  // View faculty details button
  $(document).on('click', '.view-faculty-btn', function() {
    const facultyId = $(this).data('faculty-id');
    openFacultyModal(facultyId);
  });

  // Close modal button
  $(document).on('click', '#closeFacultyModalBtn', function() {
    closeFacultyModal();
  });

  // Close modal when clicking outside
  $(document).on('click', '#facultyModal', function(e) {
    if (e.target === this) {
      closeFacultyModal();
    }
  });
});

// Faculty modal functions
function openFacultyModal(facultyId) {
  const modal = document.getElementById('facultyModal');
  const modalTitle = document.getElementById('facultyModalTitle');
  const modalContent = document.getElementById('facultyModalContent');
  const modalFooter = document.getElementById('facultyModalFooter');

  // Set modal title
  modalTitle.innerHTML = '<i class="fas fa-user-tie mr-2"></i>Faculty Details';

  // Show loading
  modalContent.innerHTML = '<div class="flex justify-center items-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-purple-600"></i></div>';

  // Show modal
  modal.classList.remove('hidden');

  // Fetch faculty data
  fetch(`/admin/faculty/${facultyId}`)
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        const faculty = result.faculty;
        modalContent.innerHTML = generateFacultyViewContent(faculty);
        modalFooter.innerHTML = `
          <button id="closeFacultyModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
          <a href="/admin/users/${faculty.id}/edit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
            Edit Faculty
          </a>
        `;
      } else {
        modalContent.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <p class="text-gray-600">${result.message || 'Error loading faculty details'}</p>
          </div>
        `;
        modalFooter.innerHTML = `
          <button id="closeFacultyModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
        `;
      }
    })
    .catch(error => {
      console.error('Error fetching faculty details:', error);
      modalContent.innerHTML = `
        <div class="text-center py-8">
          <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
          <p class="text-gray-600">Error loading faculty details. Please try again.</p>
        </div>
      `;
      modalFooter.innerHTML = `
        <button id="closeFacultyModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
          Close
        </button>
      `;
    });
}

function closeFacultyModal() {
  document.getElementById('facultyModal').classList.add('hidden');
}

function generateFacultyViewContent(faculty) {
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  return `
    <div class="space-y-6">
      <!-- Faculty Profile Header -->
      <div class="flex items-center space-x-4 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg">
        <div class="flex-shrink-0">
          ${faculty.profile_image ?
            `<img class="h-20 w-20 rounded-full object-cover border-4 border-white shadow-lg" src="${faculty.profile_image}" alt="${faculty.username}">` :
            `<div class="h-20 w-20 rounded-full bg-purple-200 flex items-center justify-center border-4 border-white shadow-lg">
              <span class="text-purple-600 text-2xl font-semibold">${faculty.username.substring(0, 2).toUpperCase()}</span>
            </div>`
          }
        </div>
        <div class="flex-1">
          <h3 class="text-2xl font-bold text-gray-900">${faculty.username}</h3>
          <p class="text-lg text-gray-600">${faculty.name || faculty.username}</p>
          <div class="flex items-center space-x-4 mt-2">
            ${faculty.is_online ?
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>Online</span>' :
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><span class="w-2 h-2 mr-1 bg-gray-500 rounded-full"></span>Offline</span>'
            }
            ${faculty.is_blocked ?
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Blocked</span>' :
              '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Active</span>'
            }
          </div>
        </div>
      </div>

      <!-- Faculty Information Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Personal Information -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-user text-purple-600 mr-2"></i>
            Personal Information
          </h4>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Full Name</dt>
              <dd class="text-sm text-gray-900">${faculty.name || faculty.username}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Email Address</dt>
              <dd class="text-sm text-gray-900">${faculty.email}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
              <dd class="text-sm text-gray-900">${formatDate(faculty.date_of_birth)}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Bio</dt>
              <dd class="text-sm text-gray-900">${faculty.bio || 'No bio provided'}</dd>
            </div>
          </dl>
        </div>

        <!-- Professional Information -->
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-briefcase text-purple-600 mr-2"></i>
            Professional Information
          </h4>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Role</dt>
              <dd class="text-sm text-gray-900">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Teacher
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Subjects</dt>
              <dd class="text-sm text-gray-900">${faculty.subjects || 'Not assigned'}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Joined Date</dt>
              <dd class="text-sm text-gray-900">${formatDate(faculty.created_at)}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Login</dt>
              <dd class="text-sm text-gray-900">${formatDateTime(faculty.last_login)}</dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="bg-white border border-gray-200 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <i class="fas fa-info-circle text-purple-600 mr-2"></i>
          Additional Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">${faculty.lecture_count || 0}</div>
            <div class="text-sm text-blue-800">Total Lectures</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">${faculty.practical_count || 0}</div>
            <div class="text-sm text-green-800">Total Practicals</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">${faculty.student_count || 0}</div>
            <div class="text-sm text-purple-800">Students Taught</div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeFacultyModal();
    }
});
</script>
