
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <a href="/admin/groups" class="mr-3 text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <h1 class="text-2xl font-bold text-gray-800">Create Group</h1>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <form action="/admin/groups/create" method="POST" class="p-6">
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Group Information</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Group Name <span class="text-red-500">*</span></label>
                        <input type="text" id="name" name="name" required class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="description" name="description" rows="3" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"></textarea>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Group Type</h2>

                <div class="flex items-center mb-4">
                    <input type="checkbox" id="isRoleGroup" name="isRoleGroup" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <label for="isRoleGroup" class="ml-2 block text-sm text-gray-900">
                        This is a role-based group
                    </label>
                </div>

                <div id="roleTypeContainer" class="hidden">
                    <label for="roleType" class="block text-sm font-medium text-gray-700 mb-1">Role Type</label>
                    <select id="roleType" name="roleType" class="w-full md:w-1/3 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <option value="">Select a role</option>
                        <option value="admin">Admin</option>
                        <option value="teacher">Teacher</option>
                        <option value="student">Student</option>
                    </select>
                    <p class="mt-2 text-sm text-gray-500">
                        All users with this role will be automatically added to this group.
                    </p>
                </div>
            </div>

            <div id="membersSection" class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Group Members</h2>

                <div class="mb-4">
                    <label for="memberSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
                    <div class="flex">
                        <input type="text" id="memberSearch" placeholder="Search by username or email" class="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                    </div>
                </div>

                <div id="searchResults" class="mb-4 max-h-60 overflow-y-auto hidden border rounded-md p-2"></div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Selected Members</label>
                    <div id="selectedMembers" class="border rounded-md p-4 min-h-[100px]">
                        <p class="text-gray-500 text-sm">No members selected yet.</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-end">
                <a href="/admin/groups" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition mr-2">
                    Cancel
                </a>
                <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition">
                    Create Group
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const isRoleGroupCheckbox = document.getElementById('isRoleGroup');
        const roleTypeContainer = document.getElementById('roleTypeContainer');
        const membersSection = document.getElementById('membersSection');
        // No search button anymore, using real-time search
        const memberSearch = document.getElementById('memberSearch');
        const searchResults = document.getElementById('searchResults');
        const selectedMembers = document.getElementById('selectedMembers');

        // Toggle role type and members section based on checkbox
        isRoleGroupCheckbox.addEventListener('change', function() {
            if (this.checked) {
                roleTypeContainer.classList.remove('hidden');
                membersSection.classList.add('hidden');
            } else {
                roleTypeContainer.classList.add('hidden');
                membersSection.classList.remove('hidden');
            }
        });

        // Real-time user search
        let searchTimeout;
        memberSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            if (searchTerm.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                // Make AJAX call to search users
                fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`)
                    .then(response => response.json())
                    .then(data => {
                        // Debug the response
                        console.log('API Response:', data);

                        if (!data.success) {
                            throw new Error(data.message || 'Error searching users');
                        }

                        const users = data.users;

                        // Debug the users array
                        console.log('Users array:', users);

                        // Display results
                        searchResults.classList.remove('hidden');

                        if (users.length === 0) {
                            searchResults.innerHTML = '<p class="text-gray-500 text-sm p-2">No users found</p>';
                            return;
                        }

                        searchResults.innerHTML = '';
                        users.forEach(user => {
                            // Debug each user object
                            console.log('Processing user:', user);

                            const userElement = document.createElement('div');
                            userElement.className = 'flex justify-between items-center p-2 hover:bg-gray-50';

                            userElement.innerHTML = `
                                <div>
                                    <div class="font-medium">${user.username || 'Unknown User'}</div>
                                    <div class="text-sm text-gray-500">
                                        <strong>Email:</strong> ${user.email || 'No email available'}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 ml-2">
                                            ${user.role || 'user'}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        ID: ${user.id || 0}
                                    </div>
                                </div>
                                <button type="button" class="add-member-btn text-purple-600 hover:text-purple-800 px-2 py-1">
                                    Add
                                </button>
                            `;

                            userElement.querySelector('.add-member-btn').addEventListener('click', function() {
                                addMember(user);
                            });

                            searchResults.appendChild(userElement);
                        });
                })
                .catch(error => {
                    console.error('Error searching users:', error);
                    searchResults.innerHTML = '<p class="text-red-500 text-sm p-2">Error searching users</p>';
                });
            }, 300);
        });

        function addMember(user) {
            // Ensure user has all required properties
            const userId = user.id || 0;
            const username = user.username || 'Unknown User';
            const email = user.email || 'No email available';
            const role = user.role || 'user';

            // Check if user is already added
            if (document.querySelector(`input[name="members[]"][value="${userId}"]`)) {
                return;
            }

            // Remove "No members" message if present
            const noMembersMsg = selectedMembers.querySelector('p.text-gray-500');
            if (noMembersMsg) {
                noMembersMsg.remove();
            }

            // Create member element
            const memberElement = document.createElement('div');
            memberElement.className = 'flex justify-between items-center p-2 border-b last:border-b-0';
            memberElement.innerHTML = `
                <div>
                    <div class="font-medium">${username}</div>
                    <div class="text-sm text-gray-500">
                        ${email}
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-2">
                            ${role}
                        </span>
                    </div>
                </div>
                <button type="button" class="remove-member-btn text-red-600 hover:text-red-800 px-2 py-1">
                    Remove
                </button>
                <input type="hidden" name="members[]" value="${userId}">
            `;

            memberElement.querySelector('.remove-member-btn').addEventListener('click', function() {
                memberElement.remove();

                // Add "No members" message if no members left
                if (selectedMembers.querySelectorAll('input[name="members[]"]').length === 0) {
                    selectedMembers.innerHTML = '<p class="text-gray-500 text-sm">No members selected yet.</p>';
                }

                // Log the current members for debugging
                console.log('Current members:', Array.from(selectedMembers.querySelectorAll('input[name="members[]"]')).map(input => input.value));
            });

            selectedMembers.appendChild(memberElement);
        }
    });
</script>


