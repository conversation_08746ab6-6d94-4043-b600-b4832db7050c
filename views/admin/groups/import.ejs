

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Import Groups</h1>
        <a href="/admin/groups" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded">
            <i class="fas fa-arrow-left mr-2"></i> Back to Groups
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Instructions</h2>
        <p class="mb-4">Upload a CSV or Excel file containing group information to import multiple groups at once.</p>
        
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">File Format</h3>
            <p class="mb-2">Your file should include the following columns:</p>
            <ul class="list-disc pl-6 mb-4">
                <li><strong>name</strong> (required) - The name of the group</li>
                <li><strong>description</strong> (optional) - A description of the group</li>
                <li><strong>members</strong> (optional) - A comma-separated list of email addresses of users to add to the group</li>
            </ul>
            <p class="mb-2 text-sm text-gray-600">Note: Users must already exist in the system to be added to groups.</p>
        </div>
        
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Download Templates</h3>
            <div class="flex flex-wrap gap-4">
                <a href="/admin/groups/import/template/csv" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded inline-flex items-center">
                    <i class="fas fa-file-csv mr-2"></i> Download CSV Template
                </a>
                <button onclick="generateGroupImportTemplate()" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded inline-flex items-center">
                    <i class="fas fa-file-excel mr-2"></i> Download Excel Template
                </button>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Upload File</h2>
        
        <form action="/admin/groups/import" method="POST" enctype="multipart/form-data" class="space-y-4">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center" id="dropZone">
                <input type="file" name="file" id="fileInput" accept=".csv,.xlsx,.xls" class="hidden" onchange="updateFileName(this)">
                <label for="fileInput" class="cursor-pointer">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                        <p class="text-gray-500 mb-2">Drag and drop your file here or click to browse</p>
                        <p class="text-gray-400 text-sm">Accepted formats: CSV, Excel (.xlsx, .xls)</p>
                        <p class="text-gray-400 text-sm">Maximum file size: 5MB</p>
                    </div>
                </label>
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                        <span id="fileName" class="text-blue-500"></span>
                        <button type="button" onclick="clearFile()" class="ml-2 text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-6 rounded">
                    <i class="fas fa-upload mr-2"></i> Import Groups
                </button>
            </div>
        </form>
    </div>
</div>

<script src="/js/xlsx.full.min.js"></script>
<script src="/js/generate-group-template.js"></script>
<script>
    // File upload handling
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropZone.classList.add('border-blue-500');
        dropZone.classList.add('bg-blue-50');
    }
    
    function unhighlight() {
        dropZone.classList.remove('border-blue-500');
        dropZone.classList.remove('bg-blue-50');
    }
    
    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            updateFileName(fileInput);
        }
    }
    
    function updateFileName(input) {
        if (input.files && input.files[0]) {
            fileName.textContent = input.files[0].name;
            fileInfo.classList.remove('hidden');
        }
    }
    
    function clearFile() {
        fileInput.value = '';
        fileInfo.classList.add('hidden');
    }
</script>

<%- include('../../partials/admin-footer') %>
