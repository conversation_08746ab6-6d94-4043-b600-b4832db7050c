<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Manage Groups</h1>

    <!-- Advanced Filters and Search Section -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="p-6">
            <form id="filterForm" action="/admin/groups" method="GET" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <div class="relative">
                            <input type="text" name="search" value="<%= locals.query?.search || '' %>"
                                placeholder="Search by name or description..."
                                class="w-full border border-gray-300 rounded-md shadow-sm pl-10 pr-4 py-2 focus:ring-purple-500 focus:border-purple-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Group Type</label>
                        <select name="type" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">All Types</option>
                            <option value="custom" <%= locals.query?.type === 'custom' ? 'selected' : '' %>>Custom Groups</option>
                            <option value="role" <%= locals.query?.type === 'role' ? 'selected' : '' %>>Role-based Groups</option>
                        </select>
                    </div>

                    <!-- Member Count Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Member Count</label>
                        <select name="member_count" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Any</option>
                            <option value="0" <%= locals.query?.member_count === '0' ? 'selected' : '' %>>Empty (0)</option>
                            <option value="1-5" <%= locals.query?.member_count === '1-5' ? 'selected' : '' %>>Small (1-5)</option>
                            <option value="6-20" <%= locals.query?.member_count === '6-20' ? 'selected' : '' %>>Medium (6-20)</option>
                            <option value="21+" <%= locals.query?.member_count === '21+' ? 'selected' : '' %>>Large (21+)</option>
                        </select>
                    </div>
                </div>

                <!-- Date Range Filter -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                        <div class="flex items-center border border-gray-300 rounded-md shadow-sm focus-within:border-purple-500 focus-within:ring-1 focus-within:ring-purple-500">
                            <input type="date" name="date_from" id="date_from" value="<%= locals.query?.date_from || '' %>"
                                class="w-1/2 border-0 focus:ring-0 py-2 px-3" placeholder="From">
                            <span class="text-gray-500 px-2">to</span>
                            <input type="date" name="date_to" id="date_to" value="<%= locals.query?.date_to || '' %>"
                                class="w-1/2 border-0 focus:ring-0 py-2 px-3" placeholder="To">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Filter by creation date</p>
                    </div>

                    <!-- Reset Button -->
                    <div class="md:col-span-2 flex items-end justify-end">
                        <a href="/admin/groups" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                            Reset Filters
                        </a>
                    </div>
                </div>

                <!-- Quick Filters -->
                <div class="pt-2">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Quick Filters</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <a href="/admin/groups" class="px-2 py-1 rounded-full <%= !locals.query?.type ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
                            All Groups
                        </a>
                        <a href="/admin/groups?type=custom" class="px-2 py-1 rounded-full <%= locals.query?.type === 'custom' ? 'bg-purple-700 text-white' : 'bg-purple-100 text-purple-800 hover:bg-purple-200' %> text-xs font-medium">
                            Custom Groups
                        </a>
                        <a href="/admin/groups?type=role" class="px-2 py-1 rounded-full <%= locals.query?.type === 'role' ? 'bg-blue-700 text-white' : 'bg-blue-100 text-blue-800 hover:bg-blue-200' %> text-xs font-medium">
                            Role-based Groups
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800">All Groups</h2>
                    <p class="text-sm text-gray-500 mt-1">Manage user groups and role-based groups</p>
                </div>
                <div class="flex space-x-3">
                    <a href="/admin/groups/import" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg>
                        Import Groups
                    </a>
                    <a href="/admin/groups/create" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Group
                    </a>
                </div>
            </div>

            <% if (groups && groups.length > 0) { %>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Members
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Created By
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Created At
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% groups.forEach(group => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                                                <span class="text-purple-600 font-bold text-lg"><%= group.name.charAt(0).toUpperCase() %></span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><%= group.name %></div>
                                                <div class="text-sm text-gray-500"><%= group.description || 'No description' %></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <% if (group.is_role_group) { %>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Role Group (<%= group.role_type %>)
                                            </span>
                                        <% } else { %>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Custom Group
                                            </span>
                                        <% } %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= group.member_count || 0 %> members
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= group.creator_name || 'System' %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <% if (typeof formatDate === 'function') { %>
                                            <%= formatDate(group.created_at) %>
                                        <% } else { %>
                                            <%
                                                const createdDate = new Date(group.created_at);
                                                const day = String(createdDate.getDate()).padStart(2, '0');
                                                const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                                const month = monthNames[createdDate.getMonth()];
                                                const year = createdDate.getFullYear();
                                            %>
                                            <%= `${day}-${month}-${year}` %>
                                        <% } %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <a href="/admin/groups/<%= group.group_id %>" class="text-indigo-600 hover:text-indigo-900" title="View Group Details">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            <% if (!group.is_role_group) { %>
                                            <a href="/admin/groups/<%= group.group_id %>/edit" class="text-blue-600 hover:text-blue-900" title="Edit Group">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </a>
                                                <button
                                                    class="delete-group text-red-600 hover:text-red-900"
                                                    data-id="<%= group.group_id %>"
                                                    data-name="<%= group.name %>"
                                                    title="Delete Group"
                                                >
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            <% } %>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No groups found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new group.</p>
                    <div class="mt-6 flex space-x-4 justify-center">
                        <a href="/admin/groups/import" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                            </svg>
                            Import Groups
                        </a>
                        <a href="/admin/groups/create" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create Group
                        </a>
                    </div>
                </div>
            <% } %>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div class="mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Confirm Deletion</h3>
            <p class="text-gray-500 mt-2">Are you sure you want to delete the group "<span id="deleteGroupName"></span>"? This action cannot be undone.</p>
        </div>

        <div class="flex justify-end space-x-3">
            <button id="cancelDeleteBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition">
                Cancel
            </button>
            <form id="deleteForm" method="POST" action="">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete group
        const deleteButtons = document.querySelectorAll('.delete-group');
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteGroupName = document.getElementById('deleteGroupName');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const name = this.dataset.name;

                deleteForm.action = `/admin/groups/${id}/delete`;
                deleteGroupName.textContent = name;
                deleteModal.classList.remove('hidden');
            });
        });

        cancelDeleteBtn.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });

        // Set up date range picker
        const dateFromInput = document.getElementById('date_from');
        const dateToInput = document.getElementById('date_to');

        if (dateFromInput && dateToInput) {
            // Set min date on the "to" date when "from" date changes
            dateFromInput.addEventListener('change', function() {
                if (this.value) {
                    dateToInput.min = this.value;

                    // If "to" date is before "from" date, reset it
                    if (dateToInput.value && dateToInput.value < this.value) {
                        dateToInput.value = this.value;
                    }

                    // Auto-submit the form when both dates are set
                    if (dateToInput.value) {
                        document.getElementById('filterForm').submit();
                    }
                }
            });

            // Set max date on the "from" date when "to" date changes
            dateToInput.addEventListener('change', function() {
                if (this.value) {
                    dateFromInput.max = this.value;

                    // If "from" date is after "to" date, reset it
                    if (dateFromInput.value && dateFromInput.value > this.value) {
                        dateFromInput.value = this.value;
                    }

                    // Auto-submit the form when both dates are set
                    if (dateFromInput.value) {
                        document.getElementById('filterForm').submit();
                    }
                }
            });
        }

        // Handle real-time filter changes
        const filterForm = document.getElementById('filterForm');
        if (filterForm) {
            const filterInputs = filterForm.querySelectorAll('select');

            filterInputs.forEach(input => {
                input.addEventListener('change', () => {
                    filterForm.submit();
                });
            });

            // Debounced search input
            const searchInput = filterForm.querySelector('input[name="search"]');
            if (searchInput) {
                let timeout = null;
                searchInput.addEventListener('input', () => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        filterForm.submit();
                    }, 500);
                });
            }
        }
    });
</script>

<%- include('../../partials/admin-footer') %>
