<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <a href="/admin/groups" class="mr-3 text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <h1 class="text-2xl font-bold text-gray-800">Group Details</h1>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Group Info -->
        <div class="md:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-purple-600 text-white p-4">
                    <div class="flex items-center">
                        <div class="bg-white rounded-full w-12 h-12 flex items-center justify-center">
                            <span class="text-purple-600 font-bold text-xl"><%= group.name.charAt(0).toUpperCase() %></span>
                        </div>
                        <div class="ml-3">
                            <h2 class="text-xl font-semibold"><%= group.name %></h2>
                            <% if (group.is_role_group) { %>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Role Group (<%= group.role_type %>)
                                </span>
                            <% } %>
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Description</h3>
                        <p class="mt-1 text-sm text-gray-900"><%= group.description || 'No description provided' %></p>
                    </div>

                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Created By</h3>
                        <p class="mt-1 text-sm text-gray-900"><%= group.creator_name || 'System' %></p>
                    </div>

                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Created At</h3>
                        <p class="mt-1 text-sm text-gray-900">
                            <% if (typeof formatDateTime === 'function') { %>
                                <%= formatDateTime(group.created_at) %>
                            <% } else { %>
                                <%
                                    const createdDate = new Date(group.created_at);
                                    const day = String(createdDate.getDate()).padStart(2, '0');
                                    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                    const month = monthNames[createdDate.getMonth()];
                                    const year = createdDate.getFullYear();
                                    const hours = String(createdDate.getHours()).padStart(2, '0');
                                    const minutes = String(createdDate.getMinutes()).padStart(2, '0');
                                    const seconds = String(createdDate.getSeconds()).padStart(2, '0');
                                %>
                                <%= `${day}-${month}-${year} ${hours}:${minutes}:${seconds}` %>
                            <% } %>
                        </p>
                    </div>

                    <% if (!group.is_role_group) { %>
                        <div class="mt-6 space-y-3">
                            <button id="generateInviteBtn" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                                Generate Invite Link
                            </button>

                            <button id="deleteGroupBtn" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete Group
                            </button>
                        </div>
                    <% } else { %>
                        <div class="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        Role-based groups cannot be deleted. They are automatically managed by the system.
                                    </p>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Group Members -->
        <div class="md:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-purple-600 text-white p-4 flex justify-between items-center">
                    <h2 class="text-xl font-semibold">Members (<%= members.length %>)</h2>
                    <% if (!group.is_role_group) { %>
                        <button id="addMembersBtn" class="bg-white text-purple-600 px-3 py-1 rounded-md text-sm hover:bg-purple-50 transition">
                            Add Members
                        </button>
                    <% } %>
                </div>

                <div class="p-4">
                    <% if (members && members.length > 0) { %>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            User
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Role
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <% if (!group.is_role_group) { %>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        <% } %>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <% members.forEach(member => { %>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <img class="h-10 w-10 rounded-full object-cover" src="<%= member.profile_image || '/img/default-avatar.png' %>" alt="<%= member.username %>">
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900"><%= member.username %></div>
                                                        <div class="text-sm text-gray-500"><%= member.email %></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><%= member.role %></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <% if (member.is_admin) { %>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                        Admin
                                                    </span>
                                                <% } else { %>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                        Member
                                                    </span>
                                                <% } %>
                                            </td>
                                            <% if (!group.is_role_group) { %>
                                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div class="flex justify-end space-x-2">
                                                        <% if (!member.is_admin) { %>
                                                            <button class="make-admin text-blue-600 hover:text-blue-900" data-id="<%= member.id %>">
                                                                Make Admin
                                                            </button>
                                                        <% } %>
                                                        <button class="remove-member text-red-600 hover:text-red-900" data-id="<%= member.id %>" data-name="<%= member.username %>">
                                                            Remove
                                                        </button>
                                                    </div>
                                                </td>
                                            <% } %>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } else { %>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No members</h3>
                            <p class="mt-1 text-sm text-gray-500">This group doesn't have any members yet.</p>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div class="mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Confirm Deletion</h3>
            <p class="text-gray-500 mt-2">Are you sure you want to delete this group? This action cannot be undone.</p>
        </div>

        <div class="flex justify-end space-x-3">
            <button id="cancelDeleteBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition">
                Cancel
            </button>
            <form id="deleteForm" method="POST" action="/admin/groups/<%= group.group_id %>/delete">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Add Members Modal -->
<div id="addMemberModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Add Members</h3>
            <button id="closeAddMemberBtn" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mb-4">
            <label for="userSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
            <input type="text" id="userSearch" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500" placeholder="Search by username or email">
        </div>

        <div id="searchResults" class="max-h-60 overflow-y-auto mb-4"></div>

        <div class="flex justify-end">
            <button id="cancelAddMemberBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition mr-2">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Generate Invite Link Modal -->
<div id="inviteLinkModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Generate Invite Link</h3>
            <button id="closeInviteLinkBtn" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="mb-4">
            <label for="expiresIn" class="block text-sm font-medium text-gray-700 mb-1">Expires In (hours)</label>
            <input type="number" id="expiresIn" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500" value="24" min="1">
        </div>

        <div class="mb-4">
            <label for="maxUses" class="block text-sm font-medium text-gray-700 mb-1">Max Uses (0 for unlimited)</label>
            <input type="number" id="maxUses" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500" value="0" min="0">
        </div>

        <div id="inviteLinkContainer" class="hidden mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Invite Link</label>
            <div class="flex">
                <input id="inviteLink" type="text" readonly class="flex-1 border rounded-l-md py-2 px-3 bg-gray-50 focus:outline-none">
                <button id="copyLinkBtn" class="bg-purple-600 text-white px-4 rounded-r-md hover:bg-purple-700">
                    Copy
                </button>
            </div>
        </div>

        <div class="flex justify-end">
            <button id="cancelInviteLinkBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition mr-2">
                Cancel
            </button>
            <button id="generateLinkBtn" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                Generate Link
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const groupId = <%= group.group_id %>;

        // Delete group
        const deleteGroupBtn = document.getElementById('deleteGroupBtn');
        const deleteModal = document.getElementById('deleteModal');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

        if (deleteGroupBtn) {
            deleteGroupBtn.addEventListener('click', function() {
                deleteModal.classList.remove('hidden');
            });
        }

        if (cancelDeleteBtn) {
            cancelDeleteBtn.addEventListener('click', function() {
                deleteModal.classList.add('hidden');
            });
        }

        // Add Members Modal
        const addMembersBtn = document.getElementById('addMembersBtn');
        const addMemberModal = document.getElementById('addMemberModal');
        const closeAddMemberBtn = document.getElementById('closeAddMemberBtn');
        const cancelAddMemberBtn = document.getElementById('cancelAddMemberBtn');
        const userSearch = document.getElementById('userSearch');
        const searchResults = document.getElementById('searchResults');

        if (addMembersBtn) {
            addMembersBtn.addEventListener('click', function() {
                addMemberModal.classList.remove('hidden');
                userSearch.focus();
            });
        }

        if (closeAddMemberBtn) {
            closeAddMemberBtn.addEventListener('click', function() {
                addMemberModal.classList.add('hidden');
                userSearch.value = '';
                searchResults.innerHTML = '';
            });
        }

        if (cancelAddMemberBtn) {
            cancelAddMemberBtn.addEventListener('click', function() {
                addMemberModal.classList.add('hidden');
                userSearch.value = '';
                searchResults.innerHTML = '';
            });
        }

        // User search
        let searchTimeout;
        if (userSearch) {
            userSearch.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    searchResults.innerHTML = '';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    fetch(`/admin/groups/${groupId}/search/users?q=${encodeURIComponent(query)}`)
                        .then(response => response.json())
                        .then(users => {
                            if (users.length === 0) {
                                searchResults.innerHTML = '<p class="text-gray-600 p-2">No users found</p>';
                                return;
                            }

                            let html = '<ul class="divide-y divide-gray-200">';
                            users.forEach(user => {
                                html += `
                                    <li class="py-2 px-2 hover:bg-gray-50 cursor-pointer flex justify-between items-center">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8">
                                                <img class="h-8 w-8 rounded-full object-cover" src="${user.profile_image || '/img/default-avatar.png'}" alt="${user.username}">
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">${user.username}</div>
                                                <div class="text-xs text-gray-500">${user.email}</div>
                                            </div>
                                        </div>
                                        <button class="add-user-btn text-sm bg-purple-600 text-white px-2 py-1 rounded hover:bg-purple-700" data-id="${user.id}">
                                            Add
                                        </button>
                                    </li>
                                `;
                            });
                            html += '</ul>';
                            searchResults.innerHTML = html;

                            // Add event listeners to add buttons
                            document.querySelectorAll('.add-user-btn').forEach(btn => {
                                btn.addEventListener('click', function() {
                                    const userId = this.dataset.id;
                                    addMember(userId);
                                });
                            });
                        })
                        .catch(error => {
                            console.error('Error searching users:', error);
                            searchResults.innerHTML = '<p class="text-red-600 p-2">Error searching users</p>';
                        });
                }, 300);
            });
        }

        // Add member function
        function addMember(userId) {
            fetch(`/admin/groups/${groupId}/members`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: userId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Member added successfully');
                    // Reload the page to show the new member
                    window.location.reload();
                } else {
                    alert('Error adding member: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error adding member:', error);
                alert('Error adding member');
            });
        }

        // Generate Invite Link Modal
        const generateInviteBtn = document.getElementById('generateInviteBtn');
        const inviteLinkModal = document.getElementById('inviteLinkModal');
        const closeInviteLinkBtn = document.getElementById('closeInviteLinkBtn');
        const cancelInviteLinkBtn = document.getElementById('cancelInviteLinkBtn');
        const generateLinkBtn = document.getElementById('generateLinkBtn');
        const inviteLinkContainer = document.getElementById('inviteLinkContainer');
        const inviteLink = document.getElementById('inviteLink');
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        const expiresIn = document.getElementById('expiresIn');
        const maxUses = document.getElementById('maxUses');

        if (generateInviteBtn) {
            generateInviteBtn.addEventListener('click', function() {
                inviteLinkModal.classList.remove('hidden');
            });
        }

        if (closeInviteLinkBtn) {
            closeInviteLinkBtn.addEventListener('click', function() {
                inviteLinkModal.classList.add('hidden');
            });
        }

        if (cancelInviteLinkBtn) {
            cancelInviteLinkBtn.addEventListener('click', function() {
                inviteLinkModal.classList.add('hidden');
            });
        }

        if (generateLinkBtn) {
            generateLinkBtn.addEventListener('click', async function() {
                try {
                    const response = await fetch(`/admin/groups/${groupId}/invite`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            expiresIn: expiresIn.value,
                            maxUses: maxUses.value === '0' ? null : maxUses.value
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        inviteLink.value = data.inviteLink;
                        inviteLinkContainer.classList.remove('hidden');
                    } else {
                        alert('Failed to generate invite link: ' + data.message);
                    }
                } catch (error) {
                    console.error('Error generating invite link:', error);
                    alert('Failed to generate invite link');
                }
            });
        }

        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', function() {
                inviteLink.select();
                document.execCommand('copy');
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = 'Copy';
                }, 2000);
            });
        }

        // Make admin and remove member buttons
        document.querySelectorAll('.make-admin').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.id;
                if (confirm('Are you sure you want to make this user an admin?')) {
                    fetch(`/admin/groups/${groupId}/members/${userId}/admin`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ is_admin: true })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error making admin:', error);
                        alert('Error making admin');
                    });
                }
            });
        });

        document.querySelectorAll('.remove-member').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.id;
                const username = this.dataset.name;
                if (confirm(`Are you sure you want to remove ${username} from the group?`)) {
                    fetch(`/admin/groups/${groupId}/members/${userId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error removing member:', error);
                        alert('Error removing member');
                    });
                }
            });
        });
    });
</script>

