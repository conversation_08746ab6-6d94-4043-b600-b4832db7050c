<!-- Create Help Article Page -->
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="text-sm mb-6">
        <ol class="list-none p-0 inline-flex">
            <li class="flex items-center">
                <a href="/admin/dashboard" class="text-blue-600 hover:text-blue-800">Dashboard</a>
                <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg>
            </li>
            <li class="flex items-center">
                <a href="/help/admin" class="text-blue-600 hover:text-blue-800">Help Articles</a>
                <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg>
            </li>
            <li>
                <span class="text-gray-500">Create Article</span>
            </li>
        </ol>
    </nav>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
    </div>

    <!-- Create Article Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <form action="/help/admin/create" method="POST" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           value="<%= formData.title || '' %>">
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                    <div class="flex items-center">
                        <span class="text-gray-500 mr-2">/help/article/</span>
                        <input type="text" id="slug" name="slug" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               value="<%= formData.slug || '' %>">
                    </div>
                    <p class="mt-1 text-sm text-gray-500">URL-friendly name (e.g., "how-to-create-an-exam")</p>
                </div>

                <!-- Categories -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Categories</label>
                    <select id="category" name="category" multiple
                            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>"><%= category.name %></option>
                        <% }); %>
                    </select>
                    <p class="mt-1 text-sm text-gray-500">Hold Ctrl/Cmd to select multiple categories</p>
                </div>

                <!-- Content -->
                <div class="col-span-2">
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content</label>
                    <textarea id="content" name="content" rows="15" required
                              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><%= formData.content || '' %></textarea>
                    <p class="mt-1 text-sm text-gray-500">HTML formatting is supported</p>
                </div>

                <!-- Published Status -->
                <div class="col-span-2">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_published" name="is_published" value="1"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                               <%= formData.is_published ? 'checked' : '' %>>
                        <label for="is_published" class="ml-2 block text-sm text-gray-700">
                            Publish this article
                        </label>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-4">
                <a href="/help/admin" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                    Create Article
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // Auto-generate slug from title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-')     // Replace spaces with hyphens
            .replace(/-+/g, '-');     // Replace multiple hyphens with single hyphen
        
        document.getElementById('slug').value = slug;
    });
    
    // Basic WYSIWYG editor for content
    // Note: In a real implementation, you might want to use a proper WYSIWYG editor like TinyMCE or CKEditor
</script>
