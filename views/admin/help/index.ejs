<!-- Admin Help Articles Management Page -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/help/admin/create" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition">
            Create New Article
        </a>
    </div>

    <!-- Articles Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <% if (articles.length === 0) { %>
            <div class="p-6 text-center">
                <p class="text-gray-600">No help articles found.</p>
                <a href="/help/admin/create" class="mt-4 inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                    Create Your First Article
                </a>
            </div>
        <% } else { %>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Category
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Views
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Updated
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% articles.forEach(article => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <%= article.title %>
                                </div>
                                <div class="text-sm text-gray-500">
                                    /help/article/<%= article.slug %>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <%= article.category || 'Uncategorized' %>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (article.is_published) { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Published
                                    </span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Draft
                                    </span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= article.view_count %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= new Date(article.updated_at).toLocaleDateString() %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="/help/article/<%= article.slug %>" class="text-indigo-600 hover:text-indigo-900" target="_blank">
                                        View
                                    </a>
                                    <a href="/help/admin/<%= article.article_id %>/edit" class="text-blue-600 hover:text-blue-900">
                                        Edit
                                    </a>
                                    <button onclick="confirmDelete(<%= article.article_id %>)" class="text-red-600 hover:text-red-900">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <% if (pagination.totalPages > 1) { %>
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            Showing <%= (pagination.currentPage - 1) * pagination.perPage + 1 %> to 
                            <%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems) %> of 
                            <%= pagination.totalItems %> articles
                        </div>
                        <div class="flex space-x-2">
                            <% if (pagination.currentPage > 1) { %>
                                <a href="/help/admin?page=<%= pagination.currentPage - 1 %>" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                                    Previous
                                </a>
                            <% } %>
                            <% if (pagination.currentPage < pagination.totalPages) { %>
                                <a href="/help/admin?page=<%= pagination.currentPage + 1 %>" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                                    Next
                                </a>
                            <% } %>
                        </div>
                    </div>
                </div>
            <% } %>
        <% } %>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
        <p class="text-gray-600 mb-6">Are you sure you want to delete this help article? This action cannot be undone.</p>
        <div class="flex justify-end space-x-4">
            <button onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                Cancel
            </button>
            <form id="deleteForm" method="POST" action="">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    function confirmDelete(articleId) {
        const modal = document.getElementById('deleteModal');
        const form = document.getElementById('deleteForm');
        
        form.action = `/help/admin/${articleId}/delete`;
        modal.classList.remove('hidden');
    }
    
    function closeModal() {
        const modal = document.getElementById('deleteModal');
        modal.classList.add('hidden');
    }
</script>
