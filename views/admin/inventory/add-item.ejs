<!-- Add Inventory Item Form -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Add New Inventory Item</h2>
        <a href="/admin/inventory/items" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Items
        </a>
    </div>

    <form action="/admin/inventory/items/add" method="POST" class="space-y-6" enctype="multipart/form-data">
        <!-- Basic Information -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                    <input type="text" id="name" name="name" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select id="category_id" name="category_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Select Category</option>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>"><%= category.name %></option>
                        <% }); %>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"></textarea>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Technical Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-1">Serial Number</label>
                    <input type="text" id="serial_number" name="serial_number" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="model" class="block text-sm font-medium text-gray-700 mb-1">Model</label>
                    <input type="text" id="model" name="model" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Manufacturer</label>
                    <input type="text" id="manufacturer" name="manufacturer" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Purchase Information -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Purchase Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-1">Purchase Date</label>
                    <input type="date" id="purchase_date" name="purchase_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="purchase_cost" class="block text-sm font-medium text-gray-700 mb-1">Purchase Cost</label>
                    <input type="number" id="purchase_cost" name="purchase_cost" step="0.01" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="warranty_expiry" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry</label>
                    <input type="date" id="warranty_expiry" name="warranty_expiry" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Status and Location -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Status and Location</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="available">Available</option>
                        <option value="assigned">Assigned</option>
                        <option value="maintenance">In Maintenance</option>
                        <option value="retired">Retired</option>
                    </select>
                </div>
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <input type="text" id="location" name="location" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"></textarea>
                </div>
            </div>
        </div>

        <!-- Image Upload -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Item Image</h3>
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label for="item_image" class="block text-sm font-medium text-gray-700 mb-1">Upload Image</label>
                    <input type="file" id="item_image" name="item_image" accept="image/*" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <p class="mt-1 text-sm text-gray-500">Upload an image of the item (optional, max 10MB)</p>
                </div>
                <div id="image_preview_container" class="hidden mt-2">
                    <p class="text-sm font-medium text-gray-700 mb-1">Preview:</p>
                    <img id="image_preview" src="#" alt="Image Preview" class="max-h-40 rounded-md border border-gray-300">
                </div>
                <input type="hidden" id="image" name="image" value="">
                <input type="hidden" name="keep_existing_image" value="yes">
            </div>
        </div>

        <!-- Network Information (for computers, network devices, etc.) -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Network Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="hostname" class="block text-sm font-medium text-gray-700 mb-1">Hostname</label>
                    <input type="text" id="hostname" name="hostname" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                    <input type="text" id="ip_address" name="ip_address" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="mac_address" class="block text-sm font-medium text-gray-700 mb-1">MAC Address</label>
                    <input type="text" id="mac_address" name="mac_address" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Laptop Condition (only shown for laptop category) -->
        <div id="laptop_condition_section" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Laptop Condition</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="physical_damage" class="block text-sm font-medium text-gray-700 mb-1">Physical Damage</label>
                    <select id="physical_damage" name="physical_damage" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="None">None</option>
                        <option value="Minor">Minor</option>
                        <option value="Moderate">Moderate</option>
                        <option value="Severe">Severe</option>
                    </select>
                </div>

                <div>
                    <label for="keyboard_condition" class="block text-sm font-medium text-gray-700 mb-1">Keyboard</label>
                    <select id="keyboard_condition" name="keyboard_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Partially Working">Partially Working</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="touchpad_condition" class="block text-sm font-medium text-gray-700 mb-1">Touchpad</label>
                    <select id="touchpad_condition" name="touchpad_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Partially Working">Partially Working</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="hdmi_port_condition" class="block text-sm font-medium text-gray-700 mb-1">HDMI Port</label>
                    <select id="hdmi_port_condition" name="hdmi_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Not Working">Not Working</option>
                        <option value="Not Available">Not Available</option>
                    </select>
                </div>

                <div>
                    <label for="ethernet_wifi_condition" class="block text-sm font-medium text-gray-700 mb-1">Ethernet Port & WiFi</label>
                    <select id="ethernet_wifi_condition" name="ethernet_wifi_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Both Working">Both Working</option>
                        <option value="Only Ethernet Working">Only Ethernet Working</option>
                        <option value="Only WiFi Working">Only WiFi Working</option>
                        <option value="Neither Working">Neither Working</option>
                    </select>
                </div>

                <div>
                    <label for="vga_port_condition" class="block text-sm font-medium text-gray-700 mb-1">VGA Port</label>
                    <select id="vga_port_condition" name="vga_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Not Working">Not Working</option>
                        <option value="Not Available">Not Available</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="usb_port_condition" class="block text-sm font-medium text-gray-700 mb-1">USB Ports</label>
                    <select id="usb_port_condition" name="usb_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="All Working">All Working</option>
                        <option value="Some Working">Some Working</option>
                        <option value="None Working">None Working</option>
                    </select>
                </div>

                <div>
                    <label for="speaker_port_condition" class="block text-sm font-medium text-gray-700 mb-1">Speaker Port</label>
                    <select id="speaker_port_condition" name="speaker_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="speakers_condition" class="block text-sm font-medium text-gray-700 mb-1">Speakers</label>
                    <select id="speakers_condition" name="speakers_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Partially Working">Partially Working</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="display_condition" class="block text-sm font-medium text-gray-700 mb-1">Display</label>
                    <select id="display_condition" name="display_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Perfect">Perfect</option>
                        <option value="Minor Issues">Minor Issues</option>
                        <option value="Major Issues">Major Issues</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="cd_drive_condition" class="block text-sm font-medium text-gray-700 mb-1">CD Drive</label>
                    <select id="cd_drive_condition" name="cd_drive_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Not Working">Not Working</option>
                        <option value="Not Available">Not Available</option>
                    </select>
                </div>

                <div>
                    <label for="webcam_condition" class="block text-sm font-medium text-gray-700 mb-1">Web Camera</label>
                    <select id="webcam_condition" name="webcam_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working">Working</option>
                        <option value="Not Working">Not Working</option>
                        <option value="Not Available">Not Available</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="charger_port_condition" class="block text-sm font-medium text-gray-700 mb-1">Charger & Charging Port</label>
                    <select id="charger_port_condition" name="charger_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Both Working">Both Working</option>
                        <option value="Only Port Working">Only Port Working</option>
                        <option value="Only Charger Working">Only Charger Working</option>
                        <option value="Neither Working">Neither Working</option>
                    </select>
                </div>

                <div>
                    <label for="os_drivers_condition" class="block text-sm font-medium text-gray-700 mb-1">OS & Drivers</label>
                    <select id="os_drivers_condition" name="os_drivers_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Fully Functional">Fully Functional</option>
                        <option value="OS Working, Some Drivers Missing">OS Working, Some Drivers Missing</option>
                        <option value="OS Issues">OS Issues</option>
                        <option value="Not Working">Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="laptop_bag_condition" class="block text-sm font-medium text-gray-700 mb-1">Laptop Bag</label>
                    <select id="laptop_bag_condition" name="laptop_bag_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Good Condition">Good Condition</option>
                        <option value="Fair Condition">Fair Condition</option>
                        <option value="Poor Condition">Poor Condition</option>
                        <option value="Not Available">Not Available</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="/admin/inventory/items" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Save Item</button>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        // Track image upload status
        let isUploadingImage = false;

        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            const nameInput = document.getElementById('name');
            if (!nameInput.value.trim()) {
                alert('Item name is required');
                nameInput.focus();
                return;
            }

            // Check if an image is currently being uploaded
            if (isUploadingImage) {
                alert('Please wait for the image upload to complete before submitting the form.');
                return;
            }

            // Create FormData object
            const formData = new FormData(form);

            // Show loading state
            const submitButton = form.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = 'Saving...';

            // Submit the form using fetch
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(() => {
                // Redirect to items list on success
                window.location.href = '/admin/inventory/items';
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving item. Please try again.');
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        });

        // Image preview
        const imageInput = document.getElementById('item_image');
        const imagePreview = document.getElementById('image_preview');
        const imagePreviewContainer = document.getElementById('image_preview_container');

        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                
                // Validate file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size too large. Maximum size is 10MB.');
                    this.value = '';
                    return;
                }

                // Validate file type
                if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
                    alert('Please select a valid image file (JPEG, JPG, or PNG).');
                    this.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });

        // Show/hide laptop condition section based on category selection
        const categorySelect = document.getElementById('category_id');
        const laptopConditionSection = document.getElementById('laptop_condition_section');

        function toggleLaptopConditionSection() {
            const selectedCategory = categorySelect.options[categorySelect.selectedIndex].text.toLowerCase();
            if (selectedCategory.includes('laptop')) {
                laptopConditionSection.style.display = 'block';
            } else {
                laptopConditionSection.style.display = 'none';
            }
        }

        // Initial check
        toggleLaptopConditionSection();

        // Listen for changes
        categorySelect.addEventListener('change', toggleLaptopConditionSection);
    });
</script>
