

<!-- Hardware Condition Check Form -->
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold text-gray-800">Hardware Condition Check</h2>
            <a href="<%= transaction ? '/admin/inventory/transactions' : '/admin/inventory/items' %>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back
            </a>
        </div>

        <!-- Item <PERSON> -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Item Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm font-medium text-gray-500">Item Name</p>
                    <p class="text-sm text-gray-900"><%= item.name %></p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Serial Number</p>
                    <p class="text-sm text-gray-900"><%= item.serial_number || 'N/A' %></p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Category</p>
                    <p class="text-sm text-gray-900"><%= item.category_name || 'Uncategorized' %></p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Status</p>
                    <p class="text-sm text-gray-900"><%= item.status %></p>
                </div>
                <% if (transaction) { %>
                <div class="md:col-span-2">
                    <p class="text-sm font-medium text-gray-500">Transaction</p>
                    <p class="text-sm text-gray-900">
                        Received on <%= new Date(transaction.received_date).toLocaleDateString() %>
                        <%= transaction.condition_on_return ? `(General condition: ${transaction.condition_on_return})` : '' %>
                    </p>
                </div>
                <% } %>
            </div>
        </div>

        <!-- Condition Check Form -->
        <form id="conditionForm" class="space-y-6">
            <input type="hidden" id="itemId" value="<%= item.item_id %>">
            <% if (transaction) { %>
            <input type="hidden" id="transactionId" value="<%= transaction.transaction_id %>">
            <% } %>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Hardware Condition</h3>
                <p class="text-sm text-gray-500 mb-4">Please check the condition of each component and provide any relevant notes.</p>

                <div class="space-y-6">
                    <% parts.forEach(part => {
                        // Find existing condition data for this part if any
                        const existingCondition = conditions.find(c => c.part_id === part.part_id);
                    %>
                    <div class="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="condition_<%= part.part_id %>" class="block text-sm font-medium text-gray-700 mb-1"><%= part.display_name %></label>
                                <select id="condition_<%= part.part_id %>"
                                        data-part-id="<%= part.part_id %>"
                                        class="condition-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                    <option value="Working" <%= existingCondition && existingCondition.condition_value === 'Working' ? 'selected' : '' %>>Working</option>
                                    <option value="Partially Working" <%= existingCondition && existingCondition.condition_value === 'Partially Working' ? 'selected' : '' %>>Partially Working</option>
                                    <option value="Not Working" <%= existingCondition && existingCondition.condition_value === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                                    <option value="Not Available" <%= existingCondition && existingCondition.condition_value === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                                    <option value="Damaged" <%= existingCondition && existingCondition.condition_value === 'Damaged' ? 'selected' : '' %>>Damaged</option>
                                </select>
                            </div>
                            <div class="md:col-span-2">
                                <label for="notes_<%= part.part_id %>" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                                <textarea id="notes_<%= part.part_id %>"
                                          data-part-id="<%= part.part_id %>"
                                          class="condition-notes w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                          rows="1"><%= existingCondition ? existingCondition.notes || '' : '' %></textarea>
                            </div>
                        </div>
                    </div>
                    <% }); %>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <a href="<%= transaction ? '/admin/inventory/transactions' : '/admin/inventory/items' %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
                <button type="submit" id="saveButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Save Condition Data</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const conditionForm = document.getElementById('conditionForm');
        const itemId = document.getElementById('itemId').value;
        const transactionId = document.getElementById('transactionId')?.value;
        const saveButton = document.getElementById('saveButton');

        conditionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveButton.disabled = true;
            saveButton.textContent = 'Saving...';

            // Collect condition data
            const conditionSelects = document.querySelectorAll('.condition-select');
            const conditionData = [];

            conditionSelects.forEach(select => {
                const partId = select.dataset.partId;
                const value = select.value;
                const notes = document.getElementById(`notes_${partId}`).value;

                conditionData.push({
                    partId: partId,
                    value: value,
                    notes: notes
                });
            });

            // Send data to server
            fetch(`/admin/inventory/condition/item/${itemId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    conditions: conditionData,
                    transactionId: transactionId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Log success message and redirect
                    console.log('Condition data saved successfully');
                    window.location.href = transactionId
                        ? '/admin/inventory/transactions'
                        : `/admin/inventory/items/${itemId}`;
                } else {
                    console.error('Error saving condition data:', data.message);
                    saveButton.disabled = false;
                    saveButton.textContent = 'Save Condition Data';
                }
            })
            .catch(error => {
                console.error('Error saving condition data:', error);
                console.error('Error saving condition data. Please try again.');
                saveButton.disabled = false;
                saveButton.textContent = 'Save Condition Data';
            });
        });
    });
</script>

<%- include('../../partials/admin-footer') %>
