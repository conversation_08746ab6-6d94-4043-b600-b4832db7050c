<!-- Edit Inventory Item Form -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Edit Inventory Item</h2>
        <a href="/admin/inventory/items" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Items
        </a>
    </div>

    <form action="/admin/inventory/items/<%= item.item_id %>/edit" method="POST" class="space-y-6" enctype="multipart/form-data">
        <!-- Basic Information -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                    <input type="text" id="name" name="name" value="<%= item.name %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select id="category_id" name="category_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Select Category</option>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>" <%= item.category_id == category.category_id ? 'selected' : '' %>><%= category.name %></option>
                        <% }); %>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"><%= item.description || '' %></textarea>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Technical Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-1">Serial Number</label>
                    <input type="text" id="serial_number" name="serial_number" value="<%= item.serial_number || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="model" class="block text-sm font-medium text-gray-700 mb-1">Model</label>
                    <input type="text" id="model" name="model" value="<%= item.model || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Manufacturer</label>
                    <input type="text" id="manufacturer" name="manufacturer" value="<%= item.manufacturer || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Purchase Information -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Purchase Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-1">Purchase Date</label>
                    <input type="date" id="purchase_date" name="purchase_date" value="<%= item.purchase_date ? item.purchase_date.split(' ')[0] : '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="purchase_cost" class="block text-sm font-medium text-gray-700 mb-1">Purchase Cost</label>
                    <input type="number" id="purchase_cost" name="purchase_cost" step="0.01" min="0" value="<%= item.purchase_cost || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="warranty_expiry" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry</label>
                    <input type="date" id="warranty_expiry" name="warranty_expiry" value="<%= item.warranty_expiry ? item.warranty_expiry.split(' ')[0] : '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Status and Location -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Status and Location</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="available" <%= item.status === 'available' ? 'selected' : '' %>>Available</option>
                        <option value="assigned" <%= item.status === 'assigned' ? 'selected' : '' %>>Assigned</option>
                        <option value="maintenance" <%= item.status === 'maintenance' ? 'selected' : '' %>>In Maintenance</option>
                        <option value="retired" <%= item.status === 'retired' ? 'selected' : '' %>>Retired</option>
                    </select>
                </div>
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <input type="text" id="location" name="location" value="<%= item.location || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"><%= item.notes || '' %></textarea>
                </div>
            </div>
        </div>

        <!-- Image Upload -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Item Images</h3>
            <div class="grid grid-cols-1 gap-4">
                <!-- Current Images -->
                <% if (item.images && item.images.length > 0) { %>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-700 mb-2">Current Images:</p>
                    <div class="flex flex-wrap gap-4">
                        <% item.images.forEach((image, index) => { %>
                            <div class="relative group">
                                <img src="<%= image.image_url %>" alt="Item Image <%= index + 1 %>"
                                     class="w-24 h-24 object-cover rounded-md border border-gray-300 <%= image.is_primary ? 'ring-2 ring-blue-500' : '' %>">
                                <% if (image.is_primary) { %>
                                    <span class="absolute top-0 right-0 bg-blue-500 text-white text-xs px-1 rounded-bl-md">Primary</span>
                                <% } %>
                                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-1 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity">
                                    <% if (!image.is_primary) { %>
                                    <button type="button" class="text-xs" onclick="setPrimaryImage(<%= image.image_id %>)">
                                        Set Primary
                                    </button>
                                    <% } %>
                                    <button type="button" class="text-xs text-red-300" onclick="deleteImage(<%= image.image_id %>)">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                </div>
                <% } %>

                <!-- Upload New Image -->
                <div>
                    <label for="item_image" class="block text-sm font-medium text-gray-700 mb-1">Upload New Image</label>
                    <input type="file" id="item_image" name="item_image" accept="image/*" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <p class="mt-1 text-sm text-gray-500">Upload a new image of the item (optional, max 10MB)</p>
                    <p class="mt-1 text-xs text-gray-500">The new image will be set as the primary image.</p>
                </div>

                <!-- Preview for new image -->
                <div id="image_preview_container" class="hidden mt-2">
                    <p class="text-sm font-medium text-gray-700 mb-1">New Image Preview:</p>
                    <img id="image_preview" src="#" alt="Image Preview" class="max-h-40 rounded-md border border-gray-300">
                </div>

                <!-- Hidden field for new image URL -->
                <input type="hidden" id="image" name="image" value="">
                <input type="hidden" name="keep_existing_image" value="yes">
            </div>
        </div>

        <!-- Network Information (for computers, network devices, etc.) -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Network Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <%
                let networkInfo = {};
                if (item.network_info) {
                    try {
                        networkInfo = typeof item.network_info === 'string' ? JSON.parse(item.network_info) : item.network_info;
                    } catch (e) {
                        networkInfo = {};
                    }
                }
                %>
                <div>
                    <label for="hostname" class="block text-sm font-medium text-gray-700 mb-1">Hostname</label>
                    <input type="text" id="hostname" name="hostname" value="<%= networkInfo.hostname || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                    <input type="text" id="ip_address" name="ip_address" value="<%= networkInfo.ip_address || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="mac_address" class="block text-sm font-medium text-gray-700 mb-1">MAC Address</label>
                    <input type="text" id="mac_address" name="mac_address" value="<%= networkInfo.mac_address || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
            </div>
        </div>

        <!-- Laptop Condition (only shown for laptop category) -->
        <div id="laptop_condition_section" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Laptop Condition</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="physical_damage" class="block text-sm font-medium text-gray-700 mb-1">Physical Damage</label>
                    <select id="physical_damage" name="physical_damage" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="None" <%= item.physical_damage === 'None' ? 'selected' : '' %>>None</option>
                        <option value="Minor" <%= item.physical_damage === 'Minor' ? 'selected' : '' %>>Minor</option>
                        <option value="Moderate" <%= item.physical_damage === 'Moderate' ? 'selected' : '' %>>Moderate</option>
                        <option value="Severe" <%= item.physical_damage === 'Severe' ? 'selected' : '' %>>Severe</option>
                    </select>
                </div>

                <div>
                    <label for="keyboard_condition" class="block text-sm font-medium text-gray-700 mb-1">Keyboard</label>
                    <select id="keyboard_condition" name="keyboard_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.keyboard_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Partially Working" <%= item.keyboard_condition === 'Partially Working' ? 'selected' : '' %>>Partially Working</option>
                        <option value="Not Working" <%= item.keyboard_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="touchpad_condition" class="block text-sm font-medium text-gray-700 mb-1">Touchpad</label>
                    <select id="touchpad_condition" name="touchpad_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.touchpad_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Partially Working" <%= item.touchpad_condition === 'Partially Working' ? 'selected' : '' %>>Partially Working</option>
                        <option value="Not Working" <%= item.touchpad_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="hdmi_port_condition" class="block text-sm font-medium text-gray-700 mb-1">HDMI Port</label>
                    <select id="hdmi_port_condition" name="hdmi_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.hdmi_port_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Not Working" <%= item.hdmi_port_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                        <option value="Not Available" <%= item.hdmi_port_condition === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                    </select>
                </div>

                <div>
                    <label for="ethernet_wifi_condition" class="block text-sm font-medium text-gray-700 mb-1">Ethernet Port & WiFi</label>
                    <select id="ethernet_wifi_condition" name="ethernet_wifi_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Both Working" <%= item.ethernet_wifi_condition === 'Both Working' ? 'selected' : '' %>>Both Working</option>
                        <option value="Only Ethernet Working" <%= item.ethernet_wifi_condition === 'Only Ethernet Working' ? 'selected' : '' %>>Only Ethernet Working</option>
                        <option value="Only WiFi Working" <%= item.ethernet_wifi_condition === 'Only WiFi Working' ? 'selected' : '' %>>Only WiFi Working</option>
                        <option value="Neither Working" <%= item.ethernet_wifi_condition === 'Neither Working' ? 'selected' : '' %>>Neither Working</option>
                    </select>
                </div>

                <div>
                    <label for="vga_port_condition" class="block text-sm font-medium text-gray-700 mb-1">VGA Port</label>
                    <select id="vga_port_condition" name="vga_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.vga_port_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Not Working" <%= item.vga_port_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                        <option value="Not Available" <%= item.vga_port_condition === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="usb_port_condition" class="block text-sm font-medium text-gray-700 mb-1">USB Ports</label>
                    <select id="usb_port_condition" name="usb_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="All Working" <%= item.usb_port_condition === 'All Working' ? 'selected' : '' %>>All Working</option>
                        <option value="Some Working" <%= item.usb_port_condition === 'Some Working' ? 'selected' : '' %>>Some Working</option>
                        <option value="None Working" <%= item.usb_port_condition === 'None Working' ? 'selected' : '' %>>None Working</option>
                    </select>
                </div>

                <div>
                    <label for="speaker_port_condition" class="block text-sm font-medium text-gray-700 mb-1">Speaker Port</label>
                    <select id="speaker_port_condition" name="speaker_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.speaker_port_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Not Working" <%= item.speaker_port_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="speakers_condition" class="block text-sm font-medium text-gray-700 mb-1">Speakers</label>
                    <select id="speakers_condition" name="speakers_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.speakers_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Partially Working" <%= item.speakers_condition === 'Partially Working' ? 'selected' : '' %>>Partially Working</option>
                        <option value="Not Working" <%= item.speakers_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="display_condition" class="block text-sm font-medium text-gray-700 mb-1">Display</label>
                    <select id="display_condition" name="display_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Perfect" <%= item.display_condition === 'Perfect' ? 'selected' : '' %>>Perfect</option>
                        <option value="Minor Issues" <%= item.display_condition === 'Minor Issues' ? 'selected' : '' %>>Minor Issues</option>
                        <option value="Major Issues" <%= item.display_condition === 'Major Issues' ? 'selected' : '' %>>Major Issues</option>
                        <option value="Not Working" <%= item.display_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="cd_drive_condition" class="block text-sm font-medium text-gray-700 mb-1">CD Drive</label>
                    <select id="cd_drive_condition" name="cd_drive_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.cd_drive_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Not Working" <%= item.cd_drive_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                        <option value="Not Available" <%= item.cd_drive_condition === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                    </select>
                </div>

                <div>
                    <label for="webcam_condition" class="block text-sm font-medium text-gray-700 mb-1">Web Camera</label>
                    <select id="webcam_condition" name="webcam_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Working" <%= item.webcam_condition === 'Working' ? 'selected' : '' %>>Working</option>
                        <option value="Not Working" <%= item.webcam_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                        <option value="Not Available" <%= item.webcam_condition === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="charger_port_condition" class="block text-sm font-medium text-gray-700 mb-1">Charger & Charging Port</label>
                    <select id="charger_port_condition" name="charger_port_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Both Working" <%= item.charger_port_condition === 'Both Working' ? 'selected' : '' %>>Both Working</option>
                        <option value="Only Port Working" <%= item.charger_port_condition === 'Only Port Working' ? 'selected' : '' %>>Only Port Working</option>
                        <option value="Only Charger Working" <%= item.charger_port_condition === 'Only Charger Working' ? 'selected' : '' %>>Only Charger Working</option>
                        <option value="Neither Working" <%= item.charger_port_condition === 'Neither Working' ? 'selected' : '' %>>Neither Working</option>
                    </select>
                </div>

                <div>
                    <label for="os_drivers_condition" class="block text-sm font-medium text-gray-700 mb-1">OS & Drivers</label>
                    <select id="os_drivers_condition" name="os_drivers_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Fully Functional" <%= item.os_drivers_condition === 'Fully Functional' ? 'selected' : '' %>>Fully Functional</option>
                        <option value="OS Working, Some Drivers Missing" <%= item.os_drivers_condition === 'OS Working, Some Drivers Missing' ? 'selected' : '' %>>OS Working, Some Drivers Missing</option>
                        <option value="OS Issues" <%= item.os_drivers_condition === 'OS Issues' ? 'selected' : '' %>>OS Issues</option>
                        <option value="Not Working" <%= item.os_drivers_condition === 'Not Working' ? 'selected' : '' %>>Not Working</option>
                    </select>
                </div>

                <div>
                    <label for="laptop_bag_condition" class="block text-sm font-medium text-gray-700 mb-1">Laptop Bag</label>
                    <select id="laptop_bag_condition" name="laptop_bag_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Good Condition" <%= item.laptop_bag_condition === 'Good Condition' ? 'selected' : '' %>>Good Condition</option>
                        <option value="Fair Condition" <%= item.laptop_bag_condition === 'Fair Condition' ? 'selected' : '' %>>Fair Condition</option>
                        <option value="Poor Condition" <%= item.laptop_bag_condition === 'Poor Condition' ? 'selected' : '' %>>Poor Condition</option>
                        <option value="Not Available" <%= item.laptop_bag_condition === 'Not Available' ? 'selected' : '' %>>Not Available</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="/admin/inventory/items" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Update Item</button>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const nameInput = document.getElementById('name');
            if (!nameInput.value.trim()) {
                e.preventDefault();
                alert('Item name is required');
                nameInput.focus();
            }
        });

        // Image preview
        const imageInput = document.getElementById('item_image');
        const imagePreview = document.getElementById('image_preview');
        const imagePreviewContainer = document.getElementById('image_preview_container');
        const imageField = document.getElementById('image');

        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.classList.remove('hidden');

                    // Compress and upload the image
                    compressAndUploadImage(file);
                };

                reader.readAsDataURL(file);
            }
        });

        // Function to compress image before uploading
        function compressAndUploadImage(file) {
            // If file is not too large or not an image type we can compress, upload directly
            if (file.size <= 1024 * 1024 || !file.type.match(/image\/(jpeg|jpg|png)/i)) {
                uploadImage(file);
                return;
            }

            // Create an image element for compression
            const img = new Image();
            img.onload = function() {
                // Create a canvas element
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;

                // Calculate new dimensions (max 1200px width/height)
                const maxDimension = 1200;
                if (width > height && width > maxDimension) {
                    height = Math.round(height * (maxDimension / width));
                    width = maxDimension;
                } else if (height > maxDimension) {
                    width = Math.round(width * (maxDimension / height));
                    height = maxDimension;
                }

                // Set canvas dimensions and draw image
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to blob with reduced quality
                canvas.toBlob(function(blob) {
                    if (blob) {
                        console.log('Original size:', file.size, 'Compressed size:', blob.size);
                        // If compression actually made it smaller
                        if (blob.size < file.size) {
                            // Create a new File object from the blob
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg',
                                lastModified: new Date().getTime()
                            });
                            uploadImage(compressedFile);
                        } else {
                            // Use original if compression didn't help
                            uploadImage(file);
                        }
                    } else {
                        // Fallback to original file if compression fails
                        console.log('Compression failed, using original file');
                        uploadImage(file);
                    }
                }, 'image/jpeg', 0.7); // 70% quality JPEG
            };

            // Handle errors in image loading
            img.onerror = function() {
                console.error('Error loading image for compression');
                uploadImage(file); // Fallback to original upload
            };

            // Load the image from the file
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('Error reading file for compression');
                uploadImage(file); // Fallback to original upload
            };
            reader.readAsDataURL(file);
        }

        // Function to upload image using base64 encoding instead of FormData
        function uploadImage(file) {
            // Validate file size before attempting upload
            const maxSizeInBytes = 10 * 1024 * 1024; // 10MB limit for base64
            if (file.size > maxSizeInBytes) {
                alert(`File size too large. Maximum size is 10MB. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
                // Reset the file input
                imageInput.value = '';
                imagePreviewContainer.classList.add('hidden');
                return;
            }

            // Show loading indicator or message
            const loadingMessage = document.createElement('div');
            loadingMessage.id = 'upload-loading';
            loadingMessage.innerText = 'Uploading image...';
            loadingMessage.className = 'mt-2 text-sm text-blue-600';
            imagePreviewContainer.appendChild(loadingMessage);

            // Read file as base64
            const reader = new FileReader();
            reader.onload = function(e) {
                const base64Data = e.target.result;

                // Prepare the data to send - send the full data URL
                const uploadData = {
                    image: base64Data,
                    filename: file.name,
                    mimetype: file.type
                };

                console.log('Uploading file as base64:', file.name, 'Size:', file.size, 'Type:', file.type);

                // Create an abort controller to handle timeouts
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                // Make the fetch request with JSON payload
                fetch('/admin/inventory/images/upload-base64', {
                    method: 'POST',
                    body: JSON.stringify(uploadData),
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        return response.text().then(text => {
                            try {
                                // Try to parse as JSON first
                                const data = JSON.parse(text);
                                throw new Error(data.message || `HTTP error! Status: ${response.status}`);
                            } catch (e) {
                                // If not JSON, return the text or status
                                throw new Error(text || `HTTP error! Status: ${response.status}`);
                            }
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // Remove loading message
                    const loadingEl = document.getElementById('upload-loading');
                    if (loadingEl) loadingEl.remove();

                    if (data.success) {
                        // Set the image URL to the hidden field
                        imageField.value = data.imageUrl;
                        console.log('Image uploaded successfully:', data.imageUrl);

                        // Show success message
                        const successMessage = document.createElement('div');
                        successMessage.innerText = 'Image uploaded successfully';
                        successMessage.className = 'mt-2 text-sm text-green-600';
                        imagePreviewContainer.appendChild(successMessage);

                        // Remove success message after 3 seconds
                        setTimeout(() => {
                            successMessage.remove();
                        }, 3000);
                    } else {
                        console.error('Failed to upload image:', data.message);
                        alert('Failed to upload image: ' + data.message);

                        // Reset the file input and hide preview on error
                        imageInput.value = '';
                        imagePreviewContainer.classList.add('hidden');
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    // Remove loading message
                    const loadingEl = document.getElementById('upload-loading');
                    if (loadingEl) loadingEl.remove();

                    console.error('Error uploading image:', error);

                    let errorMessage = error.message;
                    if (error.name === 'AbortError') {
                        errorMessage = 'Upload timed out. Please try a smaller image or check your connection.';
                    }

                    alert('Error uploading image: ' + errorMessage);

                    // Reset the file input and hide preview on error
                    imageInput.value = '';
                    imagePreviewContainer.classList.add('hidden');
                });
            };

            reader.onerror = function() {
                console.error('Error reading file as base64');
                alert('Error reading file. Please try again with a different image.');
                // Reset the file input and hide preview on error
                imageInput.value = '';
                imagePreviewContainer.classList.add('hidden');
            };

            // Start reading the file as a data URL (base64)
            reader.readAsDataURL(file);
        }

        // Show/hide laptop condition section based on category selection
        const categorySelect = document.getElementById('category_id');
        const laptopConditionSection = document.getElementById('laptop_condition_section');

        function toggleLaptopConditionSection() {
            // Check if the selected category is for laptops
            // This assumes that the laptop category has a specific ID or name
            // You may need to adjust this logic based on your actual category structure
            const selectedCategory = categorySelect.options[categorySelect.selectedIndex].text.toLowerCase();
            if (selectedCategory.includes('laptop')) {
                laptopConditionSection.style.display = 'block';
            } else {
                laptopConditionSection.style.display = 'none';
            }
        }

        // Initial check
        toggleLaptopConditionSection();

        // Listen for changes
        categorySelect.addEventListener('change', toggleLaptopConditionSection);

        // Image management functions
        window.setPrimaryImage = function(imageId) {
            if (!confirm('Set this image as the primary image?')) {
                return;
            }

            fetch(`/admin/inventory/images/set-primary/${imageId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the updated primary image
                    window.location.reload();
                } else {
                    alert('Failed to set primary image: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error setting primary image:', error);
                alert('Error setting primary image. Please try again.');
            });
        };

        window.deleteImage = function(imageId) {
            if (!confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
                return;
            }

            fetch(`/admin/inventory/images/delete/${imageId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the updated images
                    window.location.reload();
                } else {
                    alert('Failed to delete image: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting image:', error);
                alert('Error deleting image. Please try again.');
            });
        };
    });
</script>
