<!-- Inventory Dashboard -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">IT Inventory Overview</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- Total Items Card -->
        <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div class="flex items-center">
                <div class="rounded-full bg-blue-100 p-3 mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-blue-600 font-medium">Total Items</p>
                    <p class="text-2xl font-bold text-blue-800"><%= stats.itemCount %></p>
                </div>
            </div>
        </div>
        
        <!-- Categories Card -->
        <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <div class="flex items-center">
                <div class="rounded-full bg-purple-100 p-3 mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-purple-600 font-medium">Categories</p>
                    <p class="text-2xl font-bold text-purple-800"><%= stats.categoryCount %></p>
                </div>
            </div>
        </div>
        
        <!-- Status Card -->
        <div class="bg-green-50 rounded-lg p-4 border border-green-200">
            <div class="flex items-center">
                <div class="rounded-full bg-green-100 p-3 mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm text-green-600 font-medium">Available Items</p>
                    <p class="text-2xl font-bold text-green-800">
                        <% 
                            let availableCount = 0;
                            stats.statusCounts.forEach(status => {
                                if (status.status === 'available') {
                                    availableCount = status.count;
                                }
                            });
                        %>
                        <%= availableCount %>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Status Distribution -->
    <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Item Status Distribution</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <% 
                const statusColors = {
                    'available': 'bg-green-500',
                    'assigned': 'bg-blue-500',
                    'maintenance': 'bg-yellow-500',
                    'retired': 'bg-gray-500'
                };
                
                const statusLabels = {
                    'available': 'Available',
                    'assigned': 'Assigned',
                    'maintenance': 'In Maintenance',
                    'retired': 'Retired'
                };
                
                // Initialize counts
                const statusData = {
                    'available': 0,
                    'assigned': 0,
                    'maintenance': 0,
                    'retired': 0
                };
                
                // Fill in actual counts
                stats.statusCounts.forEach(status => {
                    statusData[status.status] = status.count;
                });
                
                // Calculate total for percentages
                const totalItems = stats.itemCount;
            %>
            
            <% Object.keys(statusData).forEach(status => { %>
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-600"><%= statusLabels[status] %></span>
                        <span class="text-sm font-bold"><%= statusData[status] %></span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="<%= statusColors[status] %> h-2.5 rounded-full" style="width: <%= totalItems > 0 ? (statusData[status] / totalItems * 100) : 0 %>%"></div>
                    </div>
                </div>
            <% }); %>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Quick Actions</h3>
        <div class="flex flex-wrap gap-3">
            <a href="/admin/inventory/items/add" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add New Item
            </a>
            <a href="/admin/inventory/transactions/issue" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Issue Item
            </a>
            <a href="/admin/inventory/transactions/receive" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
                Receive Item
            </a>
            <a href="/admin/inventory/categories" class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                Manage Categories
            </a>
            <a href="/admin/inventory/reports" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Generate Reports
            </a>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Recent Transactions</h2>
        <a href="/admin/inventory/transactions" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</a>
    </div>
    
    <% if (recentTransactions && recentTransactions.length > 0) { %>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% recentTransactions.forEach(transaction => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm"><%= transaction.item_name %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Issued</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Received</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.issued_to_name %>
                                <% } else { %>
                                    <%= transaction.issued_to_name %> (Returned)
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.issued_date %>
                                <% } else { %>
                                    <%= transaction.received_date %>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue' && !transaction.received_date) { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Outstanding</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } %>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="text-center py-4">
            <p class="text-gray-500">No recent transactions found.</p>
        </div>
    <% } %>
</div>
