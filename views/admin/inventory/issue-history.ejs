<!-- Item Issue History -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Issue History: <%= item.name %></h2>
        <div class="flex space-x-2">
            <a href="/admin/inventory/items/<%= item.item_id %>" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Item
            </a>
        </div>
    </div>

    <!-- Item Status Badge -->
    <div class="mb-6">
        <% if (item.status === 'available') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">Available</span>
        <% } else if (item.status === 'assigned') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
        <% } else if (item.status === 'maintenance') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">In Maintenance</span>
        <% } else if (item.status === 'retired') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
        <% } %>
    </div>

    <!-- Item Basic Info -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
            <span class="text-sm font-medium text-gray-500">Serial Number</span>
            <p class="text-sm text-gray-900"><%= item.serial_number || 'Not specified' %></p>
        </div>
        <div>
            <span class="text-sm font-medium text-gray-500">Model</span>
            <p class="text-sm text-gray-900"><%= item.model || 'Not specified' %></p>
        </div>
        <div>
            <span class="text-sm font-medium text-gray-500">Category</span>
            <p class="text-sm text-gray-900"><%= item.category_name || 'Uncategorized' %></p>
        </div>
    </div>

    <!-- Issue History Table -->
    <% if (transactions && transactions.length > 0) { %>
        <h3 class="text-lg font-medium text-gray-700 mb-4">Loan History</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued To</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued By</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Return</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% transactions.forEach(transaction => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm">
                                <div class="font-medium"><%= transaction.issued_to_name %></div>
                                <% if (transaction.issued_to_full_name) { %>
                                    <div class="text-xs text-gray-500"><%= transaction.issued_to_full_name %></div>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= transaction.issued_by_name %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.issued_date %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.expected_return_date || 'Not specified' %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.received_date || 'Not returned' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue' && !transaction.received_date) { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Outstanding</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <div class="flex space-x-2">
                                    <% if (transaction.transaction_type === 'issue') { %>
                                        <button class="generate-voucher-btn text-indigo-600 hover:text-indigo-900" title="Generate Loan Voucher" data-transaction-id="<%= transaction.transaction_id %>">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="text-center py-4">
            <p class="text-gray-500">No issue history found for this item.</p>
        </div>
    <% } %>
</div>

<!-- Include the loan voucher JavaScript -->
<script src="/js/inventory/loan-voucher.js"></script>
