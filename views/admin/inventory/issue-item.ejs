<!-- Issue Inventory Item Form -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Issue Inventory Item</h2>
        <a href="/admin/inventory/transactions" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Transactions
        </a>
    </div>

    <form action="/admin/inventory/transactions/issue" method="POST" class="space-y-6">
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Issue Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Item *</label>
                    <select id="item_id" name="item_id" required class="search-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Select Item</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= query && query.item == item.item_id ? 'selected' : '' %>>
                                <%= item.name %> (<%= item.serial_number || 'No S/N' %><%= item.model ? ', ' + item.model : '' %>)
                            </option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="issued_to" class="block text-sm font-medium text-gray-700 mb-1">Issue To *</label>
                    <select id="issued_to" name="issued_to" required class="search-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Select User</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>"><%= user.username %> (<%= user.name %>)</option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="expected_return_date" class="block text-sm font-medium text-gray-700 mb-1">Expected Return Date</label>
                    <input type="date" id="expected_return_date" name="expected_return_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>
                <div>
                    <label for="condition_on_issue" class="block text-sm font-medium text-gray-700 mb-1">Condition on Issue</label>
                    <select id="condition_on_issue" name="condition_on_issue" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="Excellent">Excellent</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"></textarea>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="/admin/inventory/transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Issue Item</button>
        </div>
    </form>
</div>

<!-- Include search-select CSS and JS -->
<link rel="stylesheet" href="/css/search-select.css">
<script src="/js/search-select.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize search-select for item dropdown
        initSearchSelect('#item_id', {
            placeholder: 'Search for an item...',
            noResultsText: 'No items found',
            minSearchLength: 1,
            maxResults: 15
        });

        // Initialize search-select for user dropdown
        initSearchSelect('#issued_to', {
            placeholder: 'Search for a user...',
            noResultsText: 'No users found',
            minSearchLength: 1,
            maxResults: 15
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const itemInput = document.getElementById('item_id');
            const userInput = document.getElementById('issued_to');

            if (!itemInput.value) {
                e.preventDefault();
                alert('Please select an item');
                return;
            }

            if (!userInput.value) {
                e.preventDefault();
                alert('Please select a user');
                return;
            }
        });

        // Set minimum date for expected return date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('expected_return_date').min = today;
    });
</script>
