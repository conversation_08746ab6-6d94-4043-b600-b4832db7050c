<!-- Inventory Items List -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-0">Inventory Items</h2>
        <a href="/admin/inventory/items/add" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add New Item
        </a>
    </div>

    <!-- Quick Filters -->
    <div class="mb-4">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Quick Filters</h3>
        <div class="flex flex-wrap gap-2 mb-4">
            <a href="/admin/inventory/items" class="<%= !query.status && !query.category ? 'bg-blue-200 text-blue-800 border-blue-400 shadow-sm' : 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                All Items
                <span class="ml-1 px-1.5 py-0.5 <%= !query.status && !query.category ? 'bg-blue-100 text-blue-900' : 'bg-white text-blue-600' %> rounded-full text-xs font-semibold"><%= pagination.totalItems %></span>
            </a>

            <%
                // Define status colors and labels with brighter active states
                const statusColors = {
                    'available': {
                        active: { bg: 'bg-green-200', text: 'text-green-800', border: 'border-green-400', counter: 'bg-green-100 text-green-900' },
                        inactive: { bg: 'bg-green-50', text: 'text-green-600', border: 'border-green-200', hover: 'hover:bg-green-100 hover:text-green-700 hover:border-green-300', counter: 'bg-white text-green-600' }
                    },
                    'assigned': {
                        active: { bg: 'bg-blue-200', text: 'text-blue-800', border: 'border-blue-400', counter: 'bg-blue-100 text-blue-900' },
                        inactive: { bg: 'bg-blue-50', text: 'text-blue-600', border: 'border-blue-200', hover: 'hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300', counter: 'bg-white text-blue-600' }
                    },
                    'maintenance': {
                        active: { bg: 'bg-yellow-200', text: 'text-yellow-800', border: 'border-yellow-400', counter: 'bg-yellow-100 text-yellow-900' },
                        inactive: { bg: 'bg-yellow-50', text: 'text-yellow-600', border: 'border-yellow-200', hover: 'hover:bg-yellow-100 hover:text-yellow-700 hover:border-yellow-300', counter: 'bg-white text-yellow-600' }
                    },
                    'retired': {
                        active: { bg: 'bg-gray-200', text: 'text-gray-800', border: 'border-gray-400', counter: 'bg-gray-100 text-gray-900' },
                        inactive: { bg: 'bg-gray-50', text: 'text-gray-600', border: 'border-gray-200', hover: 'hover:bg-gray-100 hover:text-gray-700 hover:border-gray-300', counter: 'bg-white text-gray-600' }
                    }
                };

                const statusLabels = {
                    'available': 'Available',
                    'assigned': 'Assigned',
                    'maintenance': 'In Maintenance',
                    'retired': 'Retired'
                };

                // Initialize counts
                const statusData = {
                    'available': 0,
                    'assigned': 0,
                    'maintenance': 0,
                    'retired': 0
                };

                // Fill in actual counts
                if (statusCounts) {
                    statusCounts.forEach(status => {
                        statusData[status.status] = status.count;
                    });
                }
            %>

            <% Object.keys(statusData).forEach(status => {
                const isActive = query.status === status;
                const style = isActive ? statusColors[status].active : statusColors[status].inactive;
            %>
                <a href="/admin/inventory/items?status=<%= status %>"
                   class="<%= style.bg %> <%= style.text %> <%= style.border %> <%= !isActive ? style.hover : 'shadow-sm' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                    <%= statusLabels[status] %>
                    <span class="ml-1 px-1.5 py-0.5 <%= isActive ? style.counter : 'bg-white text-' + style.text.split('-')[1] %> rounded-full text-xs font-semibold"><%= statusData[status] %></span>
                </a>
            <% }); %>
        </div>

        <% if (categoryCounts && categoryCounts.length > 0) { %>
            <h4 class="text-sm font-medium text-gray-600 mb-2">Top Categories</h4>
            <div class="flex flex-wrap gap-2 mb-4">
                <% categoryCounts.forEach(category => {
                    const isActive = query.category == category.category_id;
                %>
                    <a href="/admin/inventory/items?category=<%= category.category_id %>"
                       class="<%= isActive ? 'bg-purple-200 text-purple-800 border-purple-400 shadow-sm' : 'bg-purple-50 text-purple-600 border-purple-200 hover:bg-purple-100 hover:text-purple-700 hover:border-purple-300' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                        <%= category.name %>
                        <span class="ml-1 px-1.5 py-0.5 <%= isActive ? 'bg-purple-100 text-purple-900' : 'bg-white text-purple-600' %> rounded-full text-xs font-semibold"><%= category.count %></span>
                    </a>
                <% }); %>
            </div>
        <% } %>
    </div>

    <!-- Advanced Filters -->
    <div class="mb-6">
        <form action="/admin/inventory/items" method="GET" class="bg-gray-50 p-4 rounded-lg border border-gray-200 <%= (query.search || query.category || query.status) ? 'border-blue-300 bg-blue-50' : '' %>">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-md font-medium <%= (query.search || query.category || query.status) ? 'text-blue-700' : 'text-gray-700' %>">Advanced Filters</h3>
                <button type="button" id="toggleFilters" class="<%= (query.search || query.category || query.status) ? 'text-blue-700 hover:text-blue-900' : 'text-blue-600 hover:text-blue-800' %> text-sm font-medium flex items-center transition-colors">
                    <span id="toggleText">Show Filters</span>
                    <svg id="toggleIcon" class="w-4 h-4 ml-1 transform rotate-0 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>

            <div id="filterFields" class="hidden">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-3">
                    <div>
                        <label for="search" class="block text-sm font-medium <%= query.search ? 'text-blue-700' : 'text-gray-700' %> mb-1">Search</label>
                        <input type="text" id="search" name="search" value="<%= query.search || '' %>" placeholder="Name, Serial, Model..."
                               class="w-full rounded-md <%= query.search ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium <%= query.category ? 'text-blue-700' : 'text-gray-700' %> mb-1">Category</label>
                        <select id="category" name="category"
                                class="w-full rounded-md <%= query.category ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                            <option value="">All Categories</option>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.category_id %>" <%= query.category == category.category_id ? 'selected' : '' %>><%= category.name %></option>
                            <% }); %>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium <%= query.status ? 'text-blue-700' : 'text-gray-700' %> mb-1">Status</label>
                        <select id="status" name="status"
                                class="w-full rounded-md <%= query.status ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                            <option value="">All Statuses</option>
                            <option value="available" <%= query.status === 'available' ? 'selected' : '' %>>Available</option>
                            <option value="assigned" <%= query.status === 'assigned' ? 'selected' : '' %>>Assigned</option>
                            <option value="maintenance" <%= query.status === 'maintenance' ? 'selected' : '' %>>In Maintenance</option>
                            <option value="retired" <%= query.status === 'retired' ? 'selected' : '' %>>Retired</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition-all">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Apply Filters
                        </button>
                        <a href="/admin/inventory/items" class="ml-2 inline-flex items-center px-4 py-2 <%= (query.search || query.category || query.status) ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' %> rounded-md transition-all">
                            <%= (query.search || query.category || query.status) ? 'Reset Filters' : 'Clear' %>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Items Table -->
    <% if (items && items.length > 0) { %>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% items.forEach(item => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm">
                                <% if (item.image) { %>
                                    <img src="<%= item.image %>" alt="<%= item.name %>" class="w-12 h-12 object-cover rounded-md border border-gray-300">
                                <% } else { %>
                                    <div class="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm font-medium"><%= item.name %></td>
                            <td class="py-3 px-4 text-sm"><%= item.category_name || 'Uncategorized' %></td>
                            <td class="py-3 px-4 text-sm"><%= item.serial_number || 'N/A' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (item.status === 'available') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                                <% } else if (item.status === 'assigned') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
                                <% } else if (item.status === 'maintenance') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Maintenance</span>
                                <% } else if (item.status === 'retired') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= item.location || 'N/A' %></td>
                            <td class="py-3 px-4 text-sm">
                                <div class="flex space-x-2">
                                    <a href="/admin/inventory/items/<%= item.item_id %>" class="text-blue-600 hover:text-blue-900" title="View Details">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                    <a href="/admin/inventory/items/<%= item.item_id %>/edit" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <% if (item.status === 'available') { %>
                                        <a href="/admin/inventory/transactions/issue?item=<%= item.item_id %>" class="text-green-600 hover:text-green-900" title="Issue Item">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                            </svg>
                                        </a>
                                    <% } %>
                                    <button onclick="confirmDelete('<%= item.item_id %>', '<%= item.name %>')" class="text-red-600 hover:text-red-900" title="Delete">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <% if (pagination.totalPages > 1) { %>
            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-gray-500">
                    Showing <%= (pagination.currentPage - 1) * pagination.perPage + 1 %> to <%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems) %> of <%= pagination.totalItems %> items
                </div>
                <div class="flex space-x-1">
                    <% if (pagination.currentPage > 1) { %>
                        <a href="/admin/inventory/items?page=<%= pagination.currentPage - 1 %><%= query.search ? `&search=${query.search}` : '' %><%= query.category ? `&category=${query.category}` : '' %><%= query.status ? `&status=${query.status}` : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Previous</a>
                    <% } %>

                    <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                        <a href="/admin/inventory/items?page=<%= i %><%= query.search ? `&search=${query.search}` : '' %><%= query.category ? `&category=${query.category}` : '' %><%= query.status ? `&status=${query.status}` : '' %>" class="px-3 py-1 rounded-md <%= pagination.currentPage === i ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' %>"><%= i %></a>
                    <% } %>

                    <% if (pagination.currentPage < pagination.totalPages) { %>
                        <a href="/admin/inventory/items?page=<%= pagination.currentPage + 1 %><%= query.search ? `&search=${query.search}` : '' %><%= query.category ? `&category=${query.category}` : '' %><%= query.status ? `&status=${query.status}` : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Next</a>
                    <% } %>
                </div>
            </div>
        <% } %>
    <% } else { %>
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No items found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new inventory item.</p>
            <div class="mt-6">
                <a href="/admin/inventory/items/add" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Item
                </a>
            </div>
        </div>
    <% } %>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-500 mb-4">Are you sure you want to delete <span id="deleteItemName" class="font-semibold"></span>? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</button>
            <form id="deleteForm" method="POST" action="">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    function confirmDelete(itemId, itemName) {
        document.getElementById('deleteItemName').textContent = itemName;
        document.getElementById('deleteForm').action = `/admin/inventory/items/${itemId}/delete`;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('deleteModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteModal();
        }
    });

    // Toggle advanced filters
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('toggleFilters');
        const filterFields = document.getElementById('filterFields');
        const toggleText = document.getElementById('toggleText');
        const toggleIcon = document.getElementById('toggleIcon');

        // Check if there are any active advanced filters
        const hasActiveFilters = <%= (query.search || query.category || query.status) ? 'true' : 'false' %>;

        // If there are active filters, show the filter fields by default
        if (hasActiveFilters) {
            filterFields.classList.remove('hidden');
            toggleText.textContent = 'Hide Filters';
            toggleIcon.classList.add('rotate-180');
        }

        toggleButton.addEventListener('click', function() {
            filterFields.classList.toggle('hidden');
            if (filterFields.classList.contains('hidden')) {
                toggleText.textContent = 'Show Filters';
                toggleIcon.classList.remove('rotate-180');
            } else {
                toggleText.textContent = 'Hide Filters';
                toggleIcon.classList.add('rotate-180');
            }
        });
    });
</script>
