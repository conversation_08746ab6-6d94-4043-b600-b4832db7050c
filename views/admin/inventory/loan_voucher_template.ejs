<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loan Voucher - <%= transaction.item_name %></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ddd;
        }
        .school-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            text-transform: uppercase;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            font-size: 14px;
            color: #555;
        }
        .info-value {
            font-size: 14px;
        }
        .terms {
            margin: 30px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .terms-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .terms-list {
            margin: 0;
            padding-left: 20px;
        }
        .terms-item {
            margin-bottom: 5px;
            font-size: 12px;
        }
        .signatures {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin-top: 50px;
        }
        .signature-line {
            border-top: 1px solid #000;
            margin-top: 40px;
            padding-top: 5px;
            text-align: center;
            font-size: 14px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
        .voucher-id {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 12px;
            color: #777;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 10px;
        }
        .logo {
            height: 60px;
            width: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="voucher-id">Voucher #: LV-<%= transaction.transaction_id %></div>

    <div class="header">
        <% if (siteSettings && siteSettings.site_logo) { %>
            <div class="logo-container">
                <img src="<%= siteSettings.site_logo %>" alt="School Logo" class="logo">
            </div>
        <% } %>
        <div class="school-name">Senior Secondary Residential School for Meritorious Students, Ludhiana</div>
        <div>IT Inventory Management System</div>
    </div>

    <div class="document-title">Equipment Loan Voucher</div>

    <div class="section">
        <div class="section-title">Equipment Details</div>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Item Name:</div>
                <div class="info-value"><%= transaction.item_name %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Serial Number:</div>
                <div class="info-value"><%= transaction.serial_number || 'Not specified' %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Model:</div>
                <div class="info-value"><%= transaction.model || 'Not specified' %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Manufacturer:</div>
                <div class="info-value"><%= transaction.manufacturer || 'Not specified' %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Condition on Issue:</div>
                <div class="info-value"><%= transaction.condition_on_issue || 'Not specified' %></div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Loan Details</div>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Issued To:</div>
                <div class="info-value"><%= transaction.issued_to_full_name || transaction.issued_to_name %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Email:</div>
                <div class="info-value"><%= transaction.issued_to_email || 'Not specified' %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Issued By:</div>
                <div class="info-value"><%= transaction.issued_by_full_name || transaction.issued_by_name %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Issue Date:</div>
                <div class="info-value"><%= transaction.issued_date_formatted %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Expected Return Date:</div>
                <div class="info-value"><%= transaction.expected_return_date_formatted %></div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div class="info-value">
                    <% if (!transaction.received_date) { %>
                        Outstanding
                    <% } else { %>
                        Returned on <%= transaction.received_date_formatted %>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <% if (transaction.notes) { %>
    <div class="section">
        <div class="section-title">Notes</div>
        <div><%= transaction.notes %></div>
    </div>
    <% } %>

    <div class="terms">
        <div class="terms-title">Terms and Conditions:</div>
        <ol class="terms-list">
            <li class="terms-item">The borrower is responsible for the proper care and use of the equipment.</li>
            <li class="terms-item">The equipment must be returned in the same condition as when it was issued, normal wear and tear excepted.</li>
            <li class="terms-item">Any damage or loss must be reported immediately to the IT department.</li>
            <li class="terms-item">The borrower may be held financially responsible for any damage or loss due to negligence.</li>
            <li class="terms-item">The equipment must be returned by the expected return date or an extension must be requested.</li>
            <li class="terms-item">The equipment remains the property of the school and must be returned upon request.</li>
            <li class="terms-item">The equipment is for educational/official use only and should not be used for personal purposes.</li>
        </ol>
    </div>

    <div class="signatures">
        <div class="signature-line">Issuing Officer's Signature</div>
        <div class="signature-line">Borrower's Signature</div>
    </div>

    <div class="footer">
        This document was generated on <%= new Date().toLocaleString() %> and serves as an official record of equipment loan.
    </div>
</body>
</html>
