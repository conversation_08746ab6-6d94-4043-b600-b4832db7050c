<!-- Receive Inventory Item Form -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Receive Inventory Item</h2>
        <a href="/admin/inventory/transactions" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Transactions
        </a>
    </div>

    <% if (transactions && transactions.length > 0) { %>
        <form action="/admin/inventory/transactions/receive" method="POST" class="space-y-6">
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Select Item to Receive</h3>
                <div class="mb-4">
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-1">Outstanding Items *</label>
                    <select id="transaction_id" name="transaction_id" required class="search-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Select Item</option>
                        <% transactions.forEach(transaction => { %>
                            <option value="<%= transaction.transaction_id %>">
                                <%= transaction.item_name %> - Issued to <%= transaction.issued_to_name %> on <%= transaction.issued_date %>
                                <% if (transaction.expected_return_date && transaction.expected_return_date !== 'Not specified') { %>
                                    (Expected return: <%= transaction.expected_return_date %>)
                                <% } %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div id="itemDetails" class="hidden border border-gray-200 rounded-md p-4 mb-4 bg-white">
                    <h4 class="font-medium text-gray-700 mb-2">Item Details</h4>
                    <div id="selectedItemDetails" class="text-sm text-gray-600">
                        <!-- Details will be populated by JavaScript -->
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="condition_on_return" class="block text-sm font-medium text-gray-700 mb-1">Overall Condition on Return</label>
                        <select id="condition_on_return" name="condition_on_return" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                            <option value="Excellent">Excellent</option>
                            <option value="Good" selected>Good</option>
                            <option value="Fair">Fair</option>
                            <option value="Poor">Poor</option>
                            <option value="Damaged">Damaged</option>
                        </select>
                    </div>
                    <div>
                        <div class="flex items-center h-full mt-6">
                            <input type="checkbox" id="require_condition_check" name="require_condition_check" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                            <label for="require_condition_check" class="ml-2 block text-sm text-gray-700">Perform detailed condition check</label>
                        </div>
                    </div>
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Any notes about the condition or return process"></textarea>
                    </div>
                </div>
            </div>

            <div id="quick_condition_assessment" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Quick Condition Assessment</h3>
                <p class="text-sm text-gray-500 mb-4">Select any issues with the item. A detailed condition check will be performed after submission.</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="physical_damage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Physical Damage</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="screen_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Screen Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="keyboard_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Keyboard Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="battery_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Battery Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="connectivity_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Connectivity Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="software_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Software Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="port_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Port Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="audio_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Audio Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="quick_condition[]" value="other_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Other Issues</span>
                        </label>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="quick_condition_notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Details</label>
                    <textarea id="quick_condition_notes" name="quick_condition_notes" rows="2" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Provide more details about any issues selected above"></textarea>
                </div>

                <div class="mt-4">
                    <label class="flex items-center text-sm font-medium text-blue-700">
                        <input type="checkbox" name="create_issue_ticket" id="create_issue_ticket" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2">Create an issue ticket for this item</span>
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <a href="/admin/inventory/transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Receive Item</button>
            </div>
        </form>
    <% } else { %>
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No outstanding items</h3>
            <p class="mt-1 text-sm text-gray-500">There are no items currently issued that need to be received.</p>
            <div class="mt-6">
                <a href="/admin/inventory/transactions" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Transactions
                </a>
            </div>
        </div>
    <% } %>
</div>

<!-- Include search-select CSS and JS -->
<link rel="stylesheet" href="/css/search-select.css">
<script src="/js/search-select.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize search-select for transaction dropdown
        initSearchSelect('#transaction_id', {
            placeholder: 'Search for an item to receive...',
            noResultsText: 'No outstanding items found',
            minSearchLength: 1,
            maxResults: 15
        });

        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const transactionInput = document.getElementById('transaction_id');

                if (!transactionInput.value) {
                    e.preventDefault();
                    alert('Please select an item to receive');
                    return;
                }

                // Check if issue ticket should be created
                const createIssueTicket = document.getElementById('create_issue_ticket');
                if (createIssueTicket && createIssueTicket.checked) {
                    // Ensure at least one condition issue is selected
                    const conditionCheckboxes = document.querySelectorAll('input[name="quick_condition[]"]');
                    let hasIssue = false;

                    conditionCheckboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            hasIssue = true;
                        }
                    });

                    if (!hasIssue) {
                        e.preventDefault();
                        alert('Please select at least one issue to create an issue ticket');
                        return;
                    }

                    // Ensure notes are provided
                    const quickConditionNotes = document.getElementById('quick_condition_notes');
                    if (!quickConditionNotes.value.trim()) {
                        e.preventDefault();
                        alert('Please provide details about the issues in the Additional Details field');
                        quickConditionNotes.focus();
                        return;
                    }
                }
            });
        }

        // Show item details when selected
        const transactionSelect = document.getElementById('transaction_id');
        if (transactionSelect) {
            transactionSelect.addEventListener('change', function() {
                const itemDetails = document.getElementById('itemDetails');
                const selectedItemDetails = document.getElementById('selectedItemDetails');

                if (this.value) {
                    // Get the selected option text
                    const selectedOption = Array.from(this.options).find(option => option.value === this.value);
                    if (selectedOption) {
                        selectedItemDetails.textContent = selectedOption.text;
                        itemDetails.classList.remove('hidden');
                    }
                } else {
                    itemDetails.classList.add('hidden');
                }
            });
        }

        // Toggle issue ticket creation based on condition
        const conditionSelect = document.getElementById('condition_on_return');
        const createIssueTicket = document.getElementById('create_issue_ticket');
        const quickConditionCheckboxes = document.querySelectorAll('input[name="quick_condition[]"]');

        if (conditionSelect && createIssueTicket) {
            conditionSelect.addEventListener('change', function() {
                if (this.value === 'Poor' || this.value === 'Damaged') {
                    createIssueTicket.checked = true;
                }
            });
        }

        // Auto-check create issue ticket if any condition issue is selected
        if (quickConditionCheckboxes.length > 0 && createIssueTicket) {
            quickConditionCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    let anyChecked = false;
                    quickConditionCheckboxes.forEach(cb => {
                        if (cb.checked) {
                            anyChecked = true;
                        }
                    });

                    if (anyChecked) {
                        createIssueTicket.checked = true;
                    }
                });
            });
        }
    });
</script>
