<!-- Inventory Reports -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-0"><%= pageTitle %></h2>
        <div class="flex space-x-2">
            <div class="inline-block relative">
                <select id="reportType" onchange="changeReportType()" class="block appearance-none w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 pr-8 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                    <option value="items" <%= reportType === 'items' ? 'selected' : '' %>>Items Report</option>
                    <option value="transactions" <%= reportType === 'transactions' ? 'selected' : '' %>>Transactions Report</option>
                    <option value="categories" <%= reportType === 'categories' ? 'selected' : '' %>>Categories Report</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                </div>
            </div>
            <button onclick="printReport()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print Report
            </button>
            <button onclick="exportCSV()" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Export CSV
            </button>
        </div>
    </div>
    
    <div id="reportContent" class="overflow-x-auto">
        <% if (reportType === 'items') { %>
            <!-- Items Report -->
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Category</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Serial Number</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Model</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Purchase Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Purchase Cost</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Location</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% reportData.forEach(item => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm"><%= item.name %></td>
                            <td class="py-3 px-4 text-sm"><%= item.category_name || 'Uncategorized' %></td>
                            <td class="py-3 px-4 text-sm"><%= item.serial_number || 'N/A' %></td>
                            <td class="py-3 px-4 text-sm"><%= item.model || 'N/A' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (item.status === 'available') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                                <% } else if (item.status === 'assigned') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
                                <% } else if (item.status === 'maintenance') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Maintenance</span>
                                <% } else if (item.status === 'retired') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= item.purchase_date %></td>
                            <td class="py-3 px-4 text-sm"><%= item.purchase_cost ? `$${item.purchase_cost}` : 'N/A' %></td>
                            <td class="py-3 px-4 text-sm"><%= item.location || 'N/A' %></td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        <% } else if (reportType === 'transactions') { %>
            <!-- Transactions Report -->
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Item</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Type</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">User</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Issued Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Return Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Condition</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% reportData.forEach(transaction => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm"><%= transaction.item_name %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Issued</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Received</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.issued_to_name %>
                                <% } else { %>
                                    <%= transaction.issued_to_name %>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= transaction.issued_date %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.received_date %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue' && transaction.received_date === 'Not returned') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Outstanding</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.condition_on_issue || 'Not specified' %>
                                <% } else { %>
                                    <%= transaction.condition_on_return || 'Not specified' %>
                                <% } %>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        <% } else if (reportType === 'categories') { %>
            <!-- Categories Report -->
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Description</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Items Count</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% reportData.forEach(category => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm"><%= category.name %></td>
                            <td class="py-3 px-4 text-sm"><%= category.description || '-' %></td>
                            <td class="py-3 px-4 text-sm"><%= category.item_count %></td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        <% } %>
    </div>
</div>

<script>
    function changeReportType() {
        const reportType = document.getElementById('reportType').value;
        window.location.href = `/admin/inventory/reports?type=${reportType}`;
    }
    
    function printReport() {
        window.print();
    }
    
    function exportCSV() {
        const reportType = document.getElementById('reportType').value;
        const table = document.querySelector('table');
        const rows = table.querySelectorAll('tr');
        
        let csvContent = "data:text/csv;charset=utf-8,";
        
        // Get headers
        const headers = [];
        const headerCells = rows[0].querySelectorAll('th');
        headerCells.forEach(cell => {
            headers.push(cell.innerText);
        });
        csvContent += headers.join(',') + '\r\n';
        
        // Get data rows
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');
            const rowData = [];
            
            cells.forEach(cell => {
                // Clean up the text (remove any commas that might break the CSV)
                let text = cell.innerText.replace(/,/g, ' ');
                // Remove any newlines
                text = text.replace(/\n/g, ' ');
                // If the text contains a comma or quotes, wrap it in quotes
                if (text.includes(',') || text.includes('"')) {
                    text = `"${text.replace(/"/g, '""')}"`;
                }
                rowData.push(text);
            });
            
            csvContent += rowData.join(',') + '\r\n';
        }
        
        // Create a download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `inventory_${reportType}_report.csv`);
        document.body.appendChild(link);
        
        // Trigger download
        link.click();
        
        // Clean up
        document.body.removeChild(link);
    }
</script>

<style>
    @media print {
        body * {
            visibility: hidden;
        }
        #reportContent, #reportContent * {
            visibility: visible;
        }
        #reportContent {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .bg-green-100, .bg-blue-100, .bg-yellow-100, .bg-gray-100 {
            background-color: transparent !important;
            color: black !important;
        }
    }
</style>
