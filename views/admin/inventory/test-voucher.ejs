<!-- Test Loan Voucher -->
<div class="bg-white shadow-md rounded-lg p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Test Loan Voucher Generation</h2>

    <p class="mb-4">Click the button below to test the loan voucher generation with animation:</p>

    <div class="flex space-x-4">
        <button class="generate-voucher-btn bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md flex items-center" data-transaction-id="1">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
            Generate Test Voucher
        </button>
    </div>

    <div class="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 class="text-lg font-medium text-gray-800 mb-2">Direct Download</h3>
        <p class="mb-4">You can also download the voucher directly without animation:</p>
        <a href="/admin/inventory/transactions/1/loan-voucher" class="text-indigo-600 hover:text-indigo-800 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Download Directly
        </a>
    </div>
</div>

<!-- Include the loan voucher JavaScript -->
<script src="/js/inventory/loan-voucher.js"></script>
