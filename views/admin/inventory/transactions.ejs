<!-- Inventory Transactions List -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-0">Inventory Transactions</h2>
        <div class="flex space-x-2">
            <a href="/admin/inventory/transactions/issue" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Issue Item
            </a>
            <a href="/admin/inventory/transactions/receive" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
                Receive Item
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-6">
        <form action="/admin/inventory/transactions" method="GET" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
                    <select id="type" name="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Types</option>
                        <option value="issue" <%= query.type === 'issue' ? 'selected' : '' %>>Issue</option>
                        <option value="receive" <%= query.type === 'receive' ? 'selected' : '' %>>Receive</option>
                    </select>
                </div>
                <div>
                    <label for="item" class="block text-sm font-medium text-gray-700 mb-1">Item</label>
                    <select id="item" name="item" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Items</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= query.item == item.item_id ? 'selected' : '' %>><%= item.name %></option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="user" class="block text-sm font-medium text-gray-700 mb-1">User</label>
                    <select id="user" name="user" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">All Users</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>" <%= query.user == user.id ? 'selected' : '' %>><%= user.username %></option>
                        <% }); %>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                    <a href="/admin/inventory/transactions" class="ml-2 inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Transactions Table -->
    <% if (transactions && transactions.length > 0) { %>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% transactions.forEach(transaction => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm"><%= transaction.item_name %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Issued</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Received</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.issued_to_name %> (by <%= transaction.issued_by_name %>)
                                <% } else { %>
                                    <%= transaction.issued_to_name %> (received by <%= transaction.received_by_name %>)
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= transaction.issued_date %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.received_date || 'Not returned' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue' && !transaction.received_date) { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Outstanding</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <div class="flex space-x-2">
                                    <% if (transaction.transaction_type === 'issue') { %>
                                        <button class="generate-voucher-btn text-indigo-600 hover:text-indigo-900" title="Generate Loan Voucher" data-transaction-id="<%= transaction.transaction_id %>">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    <% } %>
                                    <% if (transaction.transaction_type === 'issue' && !transaction.received_date) { %>
                                        <a href="/admin/inventory/transactions/receive?transaction=<%= transaction.transaction_id %>" class="text-green-600 hover:text-green-900" title="Receive Item">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                                            </svg>
                                        </a>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <% if (pagination.totalPages > 1) { %>
            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-gray-500">
                    Showing <%= (pagination.currentPage - 1) * pagination.perPage + 1 %> to <%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems) %> of <%= pagination.totalItems %> transactions
                </div>
                <div class="flex space-x-1">
                    <% if (pagination.currentPage > 1) { %>
                        <a href="/admin/inventory/transactions?page=<%= pagination.currentPage - 1 %><%= query.type ? `&type=${query.type}` : '' %><%= query.item ? `&item=${query.item}` : '' %><%= query.user ? `&user=${query.user}` : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Previous</a>
                    <% } %>

                    <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                        <a href="/admin/inventory/transactions?page=<%= i %><%= query.type ? `&type=${query.type}` : '' %><%= query.item ? `&item=${query.item}` : '' %><%= query.user ? `&user=${query.user}` : '' %>" class="px-3 py-1 rounded-md <%= pagination.currentPage === i ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' %>"><%= i %></a>
                    <% } %>

                    <% if (pagination.currentPage < pagination.totalPages) { %>
                        <a href="/admin/inventory/transactions?page=<%= pagination.currentPage + 1 %><%= query.type ? `&type=${query.type}` : '' %><%= query.item ? `&item=${query.item}` : '' %><%= query.user ? `&user=${query.user}` : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Next</a>
                    <% } %>
                </div>
            </div>
        <% } %>
    <% } else { %>
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by issuing an inventory item.</p>
            <div class="mt-6">
                <a href="/admin/inventory/transactions/issue" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    Issue Item
                </a>
            </div>
        </div>
    <% } %>
</div>

<!-- Include the loan voucher JavaScript -->
<script src="/js/inventory/loan-voucher.js"></script>
