<!-- View Inventory Item Details -->
<div class="bg-white shadow-md rounded-lg p-6 mb-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Item Details: <%= item.name %></h2>
        <div class="flex space-x-2">
            <a href="/admin/inventory/items/<%= item.item_id %>/edit" class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit
            </a>
            <a href="/admin/inventory/items" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back
            </a>
        </div>
    </div>

    <!-- Item Status Badge -->
    <div class="mb-6">
        <% if (item.status === 'available') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">Available</span>
        <% } else if (item.status === 'assigned') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
        <% } else if (item.status === 'maintenance') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">In Maintenance</span>
        <% } else if (item.status === 'retired') { %>
            <span class="px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
        <% } %>
    </div>

    <!-- Item Details -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <% if (item.images && item.images.length > 0) { %>
        <!-- Item Images -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Item Images</h3>

            <!-- Primary Image -->
            <div class="flex justify-center mb-4">
                <img id="main-image" src="<%= item.images[0].image_url %>" alt="<%= item.name %>" class="max-h-64 rounded-md border border-gray-300">
            </div>

            <!-- Thumbnails -->
            <% if (item.images.length > 1) { %>
            <div class="flex flex-wrap justify-center gap-2 mt-2">
                <% item.images.forEach((image, index) => { %>
                    <img src="<%= image.image_url %>" alt="<%= item.name %> - Image <%= index + 1 %>"
                         class="w-16 h-16 object-cover rounded-md border border-gray-300 cursor-pointer <%= image.is_primary ? 'ring-2 ring-blue-500' : '' %>"
                         onclick="showImage('<%= image.image_url %>')">
                <% }); %>
            </div>
            <% } %>
        </div>
        <% } else if (item.image) { %>
        <!-- Legacy Item Image -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Item Image</h3>
            <div class="flex justify-center">
                <img src="<%= item.image %>" alt="<%= item.name %>" class="max-h-64 rounded-md border border-gray-300">
            </div>
        </div>
        <% } %>
        <!-- Basic Information -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.name %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.category_name || 'Uncategorized' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Location</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.location || 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Added By</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.created_by_name || 'Unknown' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Added On</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.created_at %></dd>
                </div>
                <% if (item.description) { %>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900"><%= item.description %></dd>
                    </div>
                <% } %>
            </dl>
        </div>

        <!-- Technical Details -->
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Technical Details</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Serial Number</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.serial_number || 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Model</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.model || 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Manufacturer</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.manufacturer || 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Purchase Date</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.purchase_date || 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Purchase Cost</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.purchase_cost ? `$${item.purchase_cost}` : 'Not specified' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Warranty Expiry</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= item.warranty_expiry || 'Not specified' %></dd>
                </div>
                <% if (item.notes) { %>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Notes</dt>
                        <dd class="mt-1 text-sm text-gray-900"><%= item.notes %></dd>
                    </div>
                <% } %>

                <% if (item.network_info) {
                    let networkInfo;
                    try {
                        networkInfo = typeof item.network_info === 'string' ? JSON.parse(item.network_info) : item.network_info;
                    } catch (e) {
                        networkInfo = {};
                    }
                    if (networkInfo) { %>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Network Information</dt>
                        <div class="mt-1 grid grid-cols-1 md:grid-cols-3 gap-2">
                            <% if (networkInfo.hostname) { %>
                            <div>
                                <span class="text-xs font-medium text-gray-500">Hostname:</span>
                                <p class="text-sm text-gray-900"><%= networkInfo.hostname %></p>
                            </div>
                            <% } %>
                            <% if (networkInfo.ip_address) { %>
                            <div>
                                <span class="text-xs font-medium text-gray-500">IP Address:</span>
                                <p class="text-sm text-gray-900"><%= networkInfo.ip_address %></p>
                            </div>
                            <% } %>
                            <% if (networkInfo.mac_address) { %>
                            <div>
                                <span class="text-xs font-medium text-gray-500">MAC Address:</span>
                                <p class="text-sm text-gray-900"><%= networkInfo.mac_address %></p>
                            </div>
                            <% } %>
                        </div>
                    </div>
                <% } } %>
            </dl>
        </div>
    </div>

    <% if (typeof conditionData !== 'undefined' && conditionData.length > 0) { %>
    <!-- Hardware Condition Details -->
    <div class="mt-6">
        <h3 class="text-lg font-medium text-gray-700 mb-4">Hardware Condition</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <%
            // Group condition data by part type
            const partGroups = {};
            conditionData.forEach(condition => {
                const partName = condition.part_name;
                if (!partGroups[partName]) {
                    partGroups[partName] = condition;
                }
            });
            %>

            <% Object.values(partGroups).forEach(condition => { %>
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2"><%= condition.display_name %></h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Condition</dt>
                        <dd class="text-sm text-gray-900"><%= condition.condition_value %></dd>
                    </div>
                    <% if (condition.notes) { %>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Notes</dt>
                        <dd class="text-sm text-gray-900"><%= condition.notes %></dd>
                    </div>
                    <% } %>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Checked</dt>
                        <dd class="text-sm text-gray-900"><%= new Date(condition.checked_at).toLocaleString() %> by <%= condition.checked_by_name || 'Unknown' %></dd>
                    </div>
                </dl>
            </div>
            <% }); %>
        </div>
    </div>
    <% } else if (item.category_name && item.category_name.toLowerCase().includes('laptop')) { %>
    <!-- Laptop Condition Details (Legacy) -->
    <div class="mt-6">
        <h3 class="text-lg font-medium text-gray-700 mb-4">Laptop Condition</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <!-- Physical Condition -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Physical Condition</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Physical Damage</dt>
                        <dd class="text-sm text-gray-900"><%= item.physical_damage || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Input Devices -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Input Devices</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Keyboard</dt>
                        <dd class="text-sm text-gray-900"><%= item.keyboard_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Touchpad</dt>
                        <dd class="text-sm text-gray-900"><%= item.touchpad_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Display & Camera -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Display & Camera</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Display</dt>
                        <dd class="text-sm text-gray-900"><%= item.display_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Web Camera</dt>
                        <dd class="text-sm text-gray-900"><%= item.webcam_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Ports -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Ports</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">HDMI Port</dt>
                        <dd class="text-sm text-gray-900"><%= item.hdmi_port_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">VGA Port</dt>
                        <dd class="text-sm text-gray-900"><%= item.vga_port_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">USB Ports</dt>
                        <dd class="text-sm text-gray-900"><%= item.usb_port_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Audio -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Audio</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Speaker Port</dt>
                        <dd class="text-sm text-gray-900"><%= item.speaker_port_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Speakers</dt>
                        <dd class="text-sm text-gray-900"><%= item.speakers_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Connectivity -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Connectivity</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Ethernet & WiFi</dt>
                        <dd class="text-sm text-gray-900"><%= item.ethernet_wifi_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Power -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Power</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Charger & Charging Port</dt>
                        <dd class="text-sm text-gray-900"><%= item.charger_port_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Software -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Software</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">OS & Drivers</dt>
                        <dd class="text-sm text-gray-900"><%= item.os_drivers_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>

            <!-- Accessories -->
            <div>
                <h4 class="text-md font-medium text-gray-700 mb-2">Accessories</h4>
                <dl class="grid grid-cols-1 gap-y-2">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">CD Drive</dt>
                        <dd class="text-sm text-gray-900"><%= item.cd_drive_condition || 'Not specified' %></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Laptop Bag</dt>
                        <dd class="text-sm text-gray-900"><%= item.laptop_bag_condition || 'Not specified' %></dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
    <% } %>

    <!-- Quick Actions -->
    <div class="mt-6 flex flex-wrap gap-3">
        <% if (item.status === 'available') { %>
            <a href="/admin/inventory/transactions/issue?item=<%= item.item_id %>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Issue Item
            </a>
        <% } else if (item.status === 'assigned') { %>
            <a href="/admin/inventory/transactions/receive" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
                Receive Item
            </a>
        <% } %>

        <a href="/admin/inventory/condition/check/<%= item.item_id %>" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Check Condition
        </a>

        <button onclick="confirmDelete('<%= item.item_id %>', '<%= item.name %>')" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete Item
        </button>
    </div>
</div>

<!-- Transaction History -->
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Transaction History</h2>
        <a href="/admin/inventory/items/<%= item.item_id %>/issue-history" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            View Issue History
        </a>
    </div>

    <% if (transactions && transactions.length > 0) { %>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return Date</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <% transactions.forEach(transaction => { %>
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Issued</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Received</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <%= transaction.issued_to_name %> (by <%= transaction.issued_by_name %>)
                                <% } else { %>
                                    <%= transaction.issued_to_name %> (received by <%= transaction.received_by_name %>)
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= transaction.issued_date %></td>
                            <td class="py-3 px-4 text-sm"><%= transaction.received_date || 'Not returned' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue' && !transaction.received_date) { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Outstanding</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } %>
                            </td>
                            <td class="py-3 px-4 text-sm"><%= transaction.notes || '-' %></td>
                            <td class="py-3 px-4 text-sm">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <button class="generate-voucher-btn text-indigo-600 hover:text-indigo-900" title="Generate Loan Voucher" data-transaction-id="<%= transaction.transaction_id %>">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                <% } %>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="text-center py-4">
            <p class="text-gray-500">No transaction history found for this item.</p>
        </div>
    <% } %>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-500 mb-4">Are you sure you want to delete <span id="deleteItemName" class="font-semibold"></span>? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</button>
            <form id="deleteForm" method="POST" action="">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    function confirmDelete(itemId, itemName) {
        document.getElementById('deleteItemName').textContent = itemName;
        document.getElementById('deleteForm').action = `/admin/inventory/items/${itemId}/delete`;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('deleteModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteModal();
        }
    });
</script>

<!-- Image Gallery Script -->
<script>
    function showImage(imageUrl) {
        const mainImage = document.getElementById('main-image');
        if (mainImage) {
            mainImage.src = imageUrl;
        }
    }
</script>

<!-- Include the loan voucher JavaScript -->
<script src="/js/inventory/loan-voucher.js"></script>
