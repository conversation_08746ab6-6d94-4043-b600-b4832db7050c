

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">IT Issue Management</h1>
        <div class="flex space-x-2">
            <a href="/issues" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Issue Tracker
            </a>
            <a href="/issues/report" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Report New Issue
            </a>
        </div>
    </div>
    
    <!-- Issue Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- Status Stats -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-lg font-medium text-gray-700 mb-4">Issues by Status</h2>
            <div class="space-y-3">
                <% 
                let openCount = 0;
                let inProgressCount = 0;
                let resolvedCount = 0;
                let closedCount = 0;
                
                statusStats.forEach(stat => {
                    if (stat.status === 'open') openCount = stat.count;
                    if (stat.status === 'in_progress') inProgressCount = stat.count;
                    if (stat.status === 'resolved') resolvedCount = stat.count;
                    if (stat.status === 'closed') closedCount = stat.count;
                });
                
                const totalCount = openCount + inProgressCount + resolvedCount + closedCount;
                %>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Open</span>
                        <span class="text-sm text-gray-500"><%= openCount %> (<%= totalCount > 0 ? Math.round(openCount / totalCount * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <%= totalCount > 0 ? (openCount / totalCount * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">In Progress</span>
                        <span class="text-sm text-gray-500"><%= inProgressCount %> (<%= totalCount > 0 ? Math.round(inProgressCount / totalCount * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: <%= totalCount > 0 ? (inProgressCount / totalCount * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Resolved</span>
                        <span class="text-sm text-gray-500"><%= resolvedCount %> (<%= totalCount > 0 ? Math.round(resolvedCount / totalCount * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: <%= totalCount > 0 ? (resolvedCount / totalCount * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Closed</span>
                        <span class="text-sm text-gray-500"><%= closedCount %> (<%= totalCount > 0 ? Math.round(closedCount / totalCount * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gray-500 h-2 rounded-full" style="width: <%= totalCount > 0 ? (closedCount / totalCount * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div class="pt-2 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">Total</span>
                        <span class="text-sm font-medium text-gray-700"><%= totalCount %></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Priority Stats -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-lg font-medium text-gray-700 mb-4">Issues by Priority</h2>
            <div class="space-y-3">
                <% 
                let criticalCount = 0;
                let highCount = 0;
                let mediumCount = 0;
                let lowCount = 0;
                
                priorityStats.forEach(stat => {
                    if (stat.priority === 'critical') criticalCount = stat.count;
                    if (stat.priority === 'high') highCount = stat.count;
                    if (stat.priority === 'medium') mediumCount = stat.count;
                    if (stat.priority === 'low') lowCount = stat.count;
                });
                
                const priorityTotal = criticalCount + highCount + mediumCount + lowCount;
                %>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Critical</span>
                        <span class="text-sm text-gray-500"><%= criticalCount %> (<%= priorityTotal > 0 ? Math.round(criticalCount / priorityTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-red-600 h-2 rounded-full" style="width: <%= priorityTotal > 0 ? (criticalCount / priorityTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">High</span>
                        <span class="text-sm text-gray-500"><%= highCount %> (<%= priorityTotal > 0 ? Math.round(highCount / priorityTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-orange-500 h-2 rounded-full" style="width: <%= priorityTotal > 0 ? (highCount / priorityTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Medium</span>
                        <span class="text-sm text-gray-500"><%= mediumCount %> (<%= priorityTotal > 0 ? Math.round(mediumCount / priorityTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: <%= priorityTotal > 0 ? (mediumCount / priorityTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Low</span>
                        <span class="text-sm text-gray-500"><%= lowCount %> (<%= priorityTotal > 0 ? Math.round(lowCount / priorityTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: <%= priorityTotal > 0 ? (lowCount / priorityTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div class="pt-2 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">Total</span>
                        <span class="text-sm font-medium text-gray-700"><%= priorityTotal %></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Type Stats -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-lg font-medium text-gray-700 mb-4">Issues by Type</h2>
            <div class="space-y-3">
                <% 
                let hardwareCount = 0;
                let softwareCount = 0;
                let networkCount = 0;
                let otherCount = 0;
                
                typeStats.forEach(stat => {
                    if (stat.issue_type === 'hardware') hardwareCount = stat.count;
                    if (stat.issue_type === 'software') softwareCount = stat.count;
                    if (stat.issue_type === 'network') networkCount = stat.count;
                    if (stat.issue_type === 'other') otherCount = stat.count;
                });
                
                const typeTotal = hardwareCount + softwareCount + networkCount + otherCount;
                %>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Hardware</span>
                        <span class="text-sm text-gray-500"><%= hardwareCount %> (<%= typeTotal > 0 ? Math.round(hardwareCount / typeTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: <%= typeTotal > 0 ? (hardwareCount / typeTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Software</span>
                        <span class="text-sm text-gray-500"><%= softwareCount %> (<%= typeTotal > 0 ? Math.round(softwareCount / typeTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: <%= typeTotal > 0 ? (softwareCount / typeTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Network</span>
                        <span class="text-sm text-gray-500"><%= networkCount %> (<%= typeTotal > 0 ? Math.round(networkCount / typeTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-indigo-500 h-2 rounded-full" style="width: <%= typeTotal > 0 ? (networkCount / typeTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium text-gray-700">Other</span>
                        <span class="text-sm text-gray-500"><%= otherCount %> (<%= typeTotal > 0 ? Math.round(otherCount / typeTotal * 100) : 0 %>%)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gray-500 h-2 rounded-full" style="width: <%= typeTotal > 0 ? (otherCount / typeTotal * 100) : 0 %>%"></div>
                    </div>
                </div>
                
                <div class="pt-2 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">Total</span>
                        <span class="text-sm font-medium text-gray-700"><%= typeTotal %></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Unassigned Issues -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-gray-700">Unassigned Issues</h2>
            <a href="/issues/list?status=open" class="text-blue-600 hover:text-blue-900 text-sm">View All Open Issues</a>
        </div>
        
        <% if (unassignedIssues.length === 0) { %>
            <p class="text-gray-500">No unassigned issues.</p>
        <% } else { %>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reported By</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assign To</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <% unassignedIssues.forEach(issue => { %>
                            <tr>
                                <td class="py-2 px-4 text-sm text-gray-900">#<%= issue.issue_id %></td>
                                <td class="py-2 px-4 text-sm text-gray-900">
                                    <a href="/issues/<%= issue.issue_id %>" class="text-blue-600 hover:text-blue-900">
                                        <div class="flex items-center">
                                            <% if (issue.issue_type === 'hardware') { %>
                                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                                </svg>
                                            <% } else if (issue.issue_type === 'software') { %>
                                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                                </svg>
                                            <% } else if (issue.issue_type === 'network') { %>
                                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                                                </svg>
                                            <% } else { %>
                                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            <% } %>
                                            <%= issue.title %>
                                        </div>
                                        <% if (issue.item_name) { %>
                                            <span class="text-xs text-gray-500">Item: <%= issue.item_name %></span>
                                        <% } %>
                                    </a>
                                </td>
                                <td class="py-2 px-4 text-sm">
                                    <% if (issue.priority === 'critical') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Critical</span>
                                    <% } else if (issue.priority === 'high') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">High</span>
                                    <% } else if (issue.priority === 'medium') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                                    <% } else if (issue.priority === 'low') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Low</span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm text-gray-900"><%= issue.reported_by_name %></td>
                                <td class="py-2 px-4 text-sm text-gray-500"><%= issue.created_at %></td>
                                <td class="py-2 px-4 text-sm">
                                    <form action="/issues/<%= issue.issue_id %>/assign" method="POST" class="flex">
                                        <select name="assigned_to" class="text-sm rounded-l-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                            <option value="">-- Select --</option>
                                            <% adminUsers.forEach(user => { %>
                                                <option value="<%= user.id %>"><%= user.username %></option>
                                            <% }); %>
                                        </select>
                                        <button type="submit" class="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } %>
    </div>
    
    <!-- Quick Links -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-medium text-gray-700 mb-4">Quick Links</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="/issues/list?status=open" class="p-4 bg-blue-50 rounded-lg border border-blue-100 hover:bg-blue-100">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-full mr-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Open Issues</h3>
                        <p class="text-lg font-bold text-blue-600"><%= openCount %></p>
                    </div>
                </div>
            </a>
            
            <a href="/issues/list?status=in_progress" class="p-4 bg-yellow-50 rounded-lg border border-yellow-100 hover:bg-yellow-100">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-full mr-3">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">In Progress</h3>
                        <p class="text-lg font-bold text-yellow-600"><%= inProgressCount %></p>
                    </div>
                </div>
            </a>
            
            <a href="/issues/list?priority=critical" class="p-4 bg-red-50 rounded-lg border border-red-100 hover:bg-red-100">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-full mr-3">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Critical Issues</h3>
                        <p class="text-lg font-bold text-red-600"><%= criticalCount %></p>
                    </div>
                </div>
            </a>
            
            <a href="/issues/list?type=hardware" class="p-4 bg-purple-50 rounded-lg border border-purple-100 hover:bg-purple-100">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-full mr-3">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Hardware Issues</h3>
                        <p class="text-lg font-bold text-purple-600"><%= hardwareCount %></p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

<%- include('../../partials/admin-footer') %>
