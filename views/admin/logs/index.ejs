<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <div class="flex space-x-2">
            <a href="/admin/test-log" class="px-4 py-2 bg-blue-500 rounded-md text-white hover:bg-blue-600">
                <i class="fas fa-plus mr-2"></i> Create Test Log
            </a>
            <a href="/admin/dashboard" class="px-4 py-2 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Count Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <!-- Total Logs Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="rounded-full bg-blue-100 p-3 mr-4">
                    <i class="fas fa-clipboard-list text-blue-500"></i>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-500">Total Logs</div>
                    <div class="text-lg font-semibold text-gray-900"><%= stats.totalLogsUnfiltered || 0 %></div>
                </div>
            </div>
        </div>

        <!-- Levels Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="rounded-full bg-green-100 p-3 mr-4">
                    <i class="fas fa-layer-group text-green-500"></i>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-500">Log Levels</div>
                    <div class="text-lg font-semibold text-gray-900"><%= levels?.length || 0 %></div>
                </div>
            </div>
        </div>

        <!-- Categories Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="rounded-full bg-purple-100 p-3 mr-4">
                    <i class="fas fa-tags text-purple-500"></i>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-500">Categories</div>
                    <div class="text-lg font-semibold text-gray-900"><%= categories?.length || 0 %></div>
                </div>
            </div>
        </div>

        <!-- Error Logs Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="rounded-full bg-red-100 p-3 mr-4">
                    <i class="fas fa-exclamation-triangle text-red-500"></i>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-500">Error Logs</div>
                    <div class="text-lg font-semibold text-gray-900">
                        <%
                            const errorCount = stats.levelStats?.find(l => l.level === 'error')?.count || 0;
                        %>
                        <%= errorCount %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <form id="filterForm" action="/admin/logs" method="GET" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <div class="relative">
                        <input type="text" id="search" name="search" value="<%= query.search %>" placeholder="Search logs..." class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select id="category" name="category" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Categories</option>
                        <% if (categories && categories.length > 0) { %>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.category %>" <%= query.category === category.category ? 'selected' : '' %>><%= category.category %></option>
                            <% }) %>
                        <% } %>
                    </select>
                </div>

                <!-- Level Filter -->
                <div>
                    <label for="level" class="block text-sm font-medium text-gray-700 mb-1">Level</label>
                    <select id="level" name="level" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Levels</option>
                        <% if (levels && levels.length > 0) { %>
                            <% levels.forEach(level => { %>
                                <option value="<%= level.level %>" <%= query.level === level.level ? 'selected' : '' %>><%= level.level.charAt(0).toUpperCase() + level.level.slice(1) %></option>
                            <% }) %>
                        <% } %>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Date Range -->
                <div>
                    <label for="fromDate" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" id="fromDate" name="fromDate" value="<%= query.fromDate %>" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label for="toDate" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    <input type="date" id="toDate" name="toDate" value="<%= query.toDate %>" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- User Filter -->
                <div>
                    <label for="userId" class="block text-sm font-medium text-gray-700 mb-1">User</label>
                    <select id="userId" name="userId" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Users</option>
                        <% if (users && users.length > 0) { %>
                            <% users.forEach(user => { %>
                                <option value="<%= user.id %>" <%= query.userId === user.id.toString() ? 'selected' : '' %>><%= user.username %></option>
                            <% }) %>
                        <% } %>
                    </select>
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select id="sort" name="sort" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="timestamp_desc" <%= query.sort === 'timestamp_desc' ? 'selected' : '' %>>Newest First</option>
                        <option value="timestamp_asc" <%= query.sort === 'timestamp_asc' ? 'selected' : '' %>>Oldest First</option>
                        <option value="level_asc" <%= query.sort === 'level_asc' ? 'selected' : '' %>>Level (A-Z)</option>
                        <option value="level_desc" <%= query.sort === 'level_desc' ? 'selected' : '' %>>Level (Z-A)</option>
                        <option value="category_asc" <%= query.sort === 'category_asc' ? 'selected' : '' %>>Category (A-Z)</option>
                        <option value="category_desc" <%= query.sort === 'category_desc' ? 'selected' : '' %>>Category (Z-A)</option>
                    </select>
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                    <a href="/admin/logs" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                        <i class="fas fa-times mr-2"></i> Clear Filters
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Quick Filters -->
    <div class="bg-white rounded-lg shadow-md p-3 mb-4">
        <h2 class="text-sm font-semibold mb-2">Quick Filters by Level</h2>
        <div class="flex flex-wrap gap-1">
            <a href="/admin/logs<%= query?.search ? '?search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= !query?.level ? 'bg-purple-600 text-white' : 'bg-purple-100 text-purple-800 hover:bg-purple-200' %> text-xs font-medium">
                All (<%= stats.totalLogsUnfiltered || 0 %>)
            </a>

            <% if (levels && levels.length > 0) { %>
                <% levels.forEach(level => { %>
                    <%
                        const levelCount = stats.levelStats?.find(l => l.level === level.level)?.count || 0;
                        const isActive = query.level === level.level;
                        const baseUrl = '/admin/logs?level=' + level.level;
                        const url = query.search ? baseUrl + '&search=' + query.search : baseUrl;
                    %>
                    <%
                        let levelColorClasses = '';
                        if (level.level === 'info') {
                            levelColorClasses = isActive ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800 hover:bg-blue-200';
                        } else if (level.level === 'warning') {
                            levelColorClasses = isActive ? 'bg-yellow-600 text-white' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
                        } else if (level.level === 'error') {
                            levelColorClasses = isActive ? 'bg-red-600 text-white' : 'bg-red-100 text-red-800 hover:bg-red-200';
                        } else if (level.level === 'debug') {
                            levelColorClasses = isActive ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200';
                        }
                    %>
                    <a href="<%= url %>" class="px-2 py-1 rounded-full <%= levelColorClasses %> text-xs font-medium">
                        <%= level.level.charAt(0).toUpperCase() + level.level.slice(1) %> (<%= levelCount %>)
                    </a>
                <% }) %>
            <% } %>
        </div>
    </div>
                <div class="flex items-center space-x-2">
                    <label for="perPage" class="text-sm font-medium text-gray-700">Show:</label>
                    <select id="perPage" name="perPage" onchange="applyFilters()" class="px-2 py-1 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="10" <%= pagination.perPage === 10 ? 'selected' : '' %>>10</option>
                        <option value="25" <%= pagination.perPage === 25 ? 'selected' : '' %>>25</option>
                        <option value="50" <%= pagination.perPage === 50 ? 'selected' : '' %>>50</option>
                        <option value="100" <%= pagination.perPage === 100 ? 'selected' : '' %>>100</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- Filter Actions -->
    <div class="flex justify-between items-center mb-4">
        <div class="text-sm text-gray-600">
            <% if (Object.keys(query).some(k => k !== 'page' && k !== 'perPage' && query[k])) { %>
                Filtered Logs: <%= pagination.totalItems %> (of <%= stats.totalLogsUnfiltered || 0 %> total)
            <% } else { %>
                Total Logs: <%= stats.totalLogsUnfiltered || 0 %>
            <% } %>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operation</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (logs && logs.length > 0) { %>
                        <% logs.forEach((log, index) => { %>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= (pagination.currentPage - 1) * pagination.perPage + index + 1 %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%
                                        const date = new Date(log.timestamp);
                                        const formattedDate = date.toLocaleDateString('en-GB', {
                                            day: '2-digit',
                                            month: 'short',
                                            year: 'numeric'
                                        });
                                        const formattedTime = date.toLocaleTimeString('en-GB', {
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            second: '2-digit'
                                        });
                                    %>
                                    <div><%= formattedDate %></div>
                                    <div class="text-xs text-gray-400"><%= formattedTime %></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <%
                                        let levelClass = '';
                                        switch(log.level) {
                                            case 'error':
                                                levelClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'warning':
                                                levelClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                            case 'info':
                                                levelClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'debug':
                                                levelClass = 'bg-gray-100 text-gray-800';
                                                break;
                                            default:
                                                levelClass = 'bg-gray-100 text-gray-800';
                                        }
                                    %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= levelClass %>">
                                        <%= log.level ? log.level.charAt(0).toUpperCase() + log.level.slice(1) : 'Unknown' %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= log.category || 'N/A' %>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                    <%= log.operation || 'N/A' %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= log.username || 'System' %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="showLogDetails('<%= log.log_id %>')" class="text-blue-600 hover:text-blue-900">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                </td>
                            </tr>
                        <% }) %>
                    <% } else { %>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                No logs found. <%= Object.keys(query).some(k => k !== 'page' && k !== 'perPage' && query[k]) ? 'Try clearing some filters.' : '' %>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
            Showing <span class="font-medium"><%= logs.length ? (pagination.currentPage - 1) * pagination.perPage + 1 : 0 %></span> to <span class="font-medium"><%= logs.length ? (pagination.currentPage - 1) * pagination.perPage + logs.length : 0 %></span> of <span class="font-medium"><%= pagination.totalItems %></span> logs
            <% if (Object.keys(query).some(k => k !== 'page' && k !== 'perPage' && query[k])) { %>
                (filtered from <span class="font-medium"><%= stats.totalLogsUnfiltered || 0 %></span> total records)
            <% } %>
        </div>
        <div class="flex space-x-1">
            <% if (pagination.totalPages > 1) { %>
                <!-- First Page -->
                <a href="<%= pagination.baseUrl %>?page=1<%= Object.entries(query).filter(([key]) => key !== 'page').map(([key, value]) => `&${key}=${value}`).join('') %>" class="px-2 py-1 border border-gray-300 rounded-md <%= pagination.currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50' %>">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                    </svg>
                </a>

                <!-- Previous Page -->
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.currentPage - 1 %><%= Object.entries(query).filter(([key]) => key !== 'page').map(([key, value]) => `&${key}=${value}`).join('') %>" class="px-2 py-1 border border-gray-300 rounded-md <%= pagination.currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50' %>">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>

                <%
                    let startPage = Math.max(1, pagination.currentPage - 2);
                    let endPage = Math.min(pagination.totalPages, startPage + 4);

                    if (endPage - startPage < 4) {
                        startPage = Math.max(1, endPage - 4);
                    }

                    for (let i = startPage; i <= endPage; i++) {
                %>
                    <a href="<%= pagination.baseUrl %>?page=<%= i %><%= Object.entries(query).filter(([key]) => key !== 'page').map(([key, value]) => `&${key}=${value}`).join('') %>" class="px-2 py-1 border border-gray-300 rounded-md text-xs <%= pagination.currentPage === i ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50' %>">
                        <%= i %>
                    </a>
                <% } %>

                <!-- Next Page -->
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.currentPage + 1 %><%= Object.entries(query).filter(([key]) => key !== 'page').map(([key, value]) => `&${key}=${value}`).join('') %>" class="px-2 py-1 border border-gray-300 rounded-md <%= pagination.currentPage === pagination.totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50' %>">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>

                <!-- Last Page -->
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.totalPages %><%= Object.entries(query).filter(([key]) => key !== 'page').map(([key, value]) => `&${key}=${value}`).join('') %>" class="px-2 py-1 border border-gray-300 rounded-md <%= pagination.currentPage === pagination.totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50' %>">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                    </svg>
                </a>
            <% } %>
        </div>
    </div>
</div>

<!-- Log Details Modal -->
<div id="logDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800">Log Details</h2>
                <button onclick="closeLogDetailsModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="logDetailsContent" class="space-y-4">
                <div class="flex justify-center items-center h-32">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function showLogDetails(logId) {
        const modal = document.getElementById('logDetailsModal');
        const content = document.getElementById('logDetailsContent');

        modal.classList.remove('hidden');
        content.innerHTML = '<div class="flex justify-center items-center h-32"><div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div></div>';

        // Fetch log details
        fetch(`/admin/api/logs/${logId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const log = data.log;
                    const timestamp = new Date(log.timestamp).toLocaleString();

                    let levelClass = '';
                    switch(log.level) {
                        case 'error':
                            levelClass = 'bg-red-100 text-red-800';
                            break;
                        case 'warning':
                            levelClass = 'bg-yellow-100 text-yellow-800';
                            break;
                        case 'info':
                            levelClass = 'bg-blue-100 text-blue-800';
                            break;
                        case 'debug':
                            levelClass = 'bg-gray-100 text-gray-800';
                            break;
                        default:
                            levelClass = 'bg-gray-100 text-gray-800';
                    }

                    let html = `
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Log ID</p>
                                <p class="text-base">${log.log_id}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Timestamp</p>
                                <p class="text-base">${timestamp}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Level</p>
                                <p class="text-base">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${levelClass}">
                                        ${log.level ? log.level.charAt(0).toUpperCase() + log.level.slice(1) : 'Unknown'}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Category</p>
                                <p class="text-base">${log.category || 'N/A'}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">User</p>
                                <p class="text-base">${log.username || 'System'}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Status</p>
                                <p class="text-base">${log.status || 'N/A'}</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <p class="text-sm font-medium text-gray-500">Operation</p>
                            <p class="text-base">${log.operation || 'N/A'}</p>
                        </div>

                        <div class="mt-4">
                            <p class="text-sm font-medium text-gray-500">Details</p>
                            <pre class="mt-1 p-3 bg-gray-50 rounded-md text-sm overflow-x-auto">${log.details || 'N/A'}</pre>
                        </div>

                        ${log.error_message ? `
                            <div class="mt-4">
                                <p class="text-sm font-medium text-gray-500">Error Message</p>
                                <pre class="mt-1 p-3 bg-red-50 text-red-700 rounded-md text-sm overflow-x-auto">${log.error_message}</pre>
                            </div>
                        ` : ''}

                        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">IP Address</p>
                                <p class="text-base">${log.ip_address || 'N/A'}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Request Method</p>
                                <p class="text-base">${log.request_method || 'N/A'}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Request URI</p>
                                <p class="text-base">${log.request_uri || 'N/A'}</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <p class="text-sm font-medium text-gray-500">User Agent</p>
                            <p class="text-base">${log.user_agent || 'N/A'}</p>
                        </div>
                    `;

                    content.innerHTML = html;
                } else {
                    content.innerHTML = '<div class="text-center text-red-500">Failed to load log details</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching log details:', error);
                content.innerHTML = '<div class="text-center text-red-500">An error occurred while loading log details</div>';
            });
    }

    function closeLogDetailsModal() {
        const modal = document.getElementById('logDetailsModal');
        modal.classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('logDetailsModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeLogDetailsModal();
        }
    });

    // Real-time filtering
    document.addEventListener('DOMContentLoaded', function() {
        // Handle filter form submission
        const filterForm = document.getElementById('filterForm');
        const filterInputs = filterForm.querySelectorAll('input, select');

        filterInputs.forEach(input => {
            if (input.type === 'date') {
                input.addEventListener('change', () => {
                    applyFilters();
                });
            } else if (input.type === 'text') {
                // Debounced search input
                let timeout = null;
                input.addEventListener('input', () => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        applyFilters();
                    }, 500);
                });
            } else {
                input.addEventListener('change', () => {
                    applyFilters();
                });
            }
        });
    });

    function applyFilters() {
        const form = document.getElementById('filterForm');
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        // Add current page size to URL if it exists
        const perPage = document.getElementById('perPage');
        if (perPage) {
            params.append('perPage', perPage.value);
        }

        // Reset to page 1 when filtering
        params.set('page', '1');

        window.location.href = `${window.location.pathname}?${params.toString()}`;
    }
</script>

<%- include('../../partials/footer') %>
