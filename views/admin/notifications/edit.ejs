<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <a href="/admin/notifications" class="mr-3 text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </a>
        <h1 class="text-2xl font-bold text-gray-800">Edit Admin Notification</h1>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <form action="/admin/notifications/<%= notification.notification_id %>/edit" method="POST" class="p-6">
            <div class="mb-4">
                <label for="title" class="block text-gray-700 text-sm font-medium mb-2">Title</label>
                <input type="text" id="title" name="title" value="<%= notification.title %>" required class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
            </div>

            <div class="mb-4">
                <label for="message" class="block text-gray-700 text-sm font-medium mb-2">Message</label>
                <textarea id="message" name="message" rows="4" required class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600"><%= notification.message %></textarea>
            </div>

            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" name="isActive" <%= notification.is_active ? 'checked' : '' %> class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <span class="ml-2 text-gray-700">Active</span>
                </label>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="startDate" class="block text-gray-700 text-sm font-medium mb-2">Start Date (Optional)</label>
                    <input type="datetime-local" id="startDate" name="startDate" value="<%= notification.start_date ? new Date(notification.start_date).toISOString().slice(0, 16) : '' %>" class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>

                <div>
                    <label for="endDate" class="block text-gray-700 text-sm font-medium mb-2">End Date (Optional)</label>
                    <input type="datetime-local" id="endDate" name="endDate" value="<%= notification.end_date ? new Date(notification.end_date).toISOString().slice(0, 16) : '' %>" class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600">
                </div>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                    Update Notification
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');

        // Set min date for end date based on start date
        startDateInput.addEventListener('change', function() {
            if (startDateInput.value) {
                endDateInput.min = startDateInput.value;
            } else {
                endDateInput.removeAttribute('min');
            }
        });

        // Set max date for start date based on end date
        endDateInput.addEventListener('change', function() {
            if (endDateInput.value) {
                startDateInput.max = endDateInput.value;
            } else {
                startDateInput.removeAttribute('max');
            }
        });

        // Initial check
        if (startDateInput.value) {
            endDateInput.min = startDateInput.value;
        }

        if (endDateInput.value) {
            startDateInput.max = endDateInput.value;
        }
    });
</script>

