<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Admin Notifications</h1>
        <a href="/admin/notifications/create" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
            Create Notification
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created By
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date Range
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created At
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (notifications && notifications.length > 0) { %>
                        <% notifications.forEach(notification => { %>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><%= notification.title %></div>
                                    <div class="text-sm text-gray-500 truncate max-w-xs"><%= notification.message %></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><%= notification.creator_name %></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= notification.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                                        <%= notification.is_active ? 'Active' : 'Inactive' %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (notification.start_date || notification.end_date) { %>
                                        <%= notification.start_date ? new Date(notification.start_date).toLocaleDateString() : 'Any' %>
                                        to
                                        <%= notification.end_date ? new Date(notification.end_date).toLocaleDateString() : 'Any' %>
                                    <% } else { %>
                                        Always
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= new Date(notification.created_at).toLocaleDateString() %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <a href="/admin/notifications/<%= notification.notification_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit Notification">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <button
                                            class="toggle-status text-<%= notification.is_active ? 'yellow' : 'green' %>-600 hover:text-<%= notification.is_active ? 'yellow' : 'green' %>-900"
                                            data-id="<%= notification.notification_id %>"
                                            data-active="<%= notification.is_active ? 'true' : 'false' %>"
                                            title="<%= notification.is_active ? 'Deactivate' : 'Activate' %> Notification"
                                        >
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <% if (notification.is_active) { %>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                <% } else { %>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                <% } %>
                                            </svg>
                                        </button>
                                        <button
                                            class="delete-notification text-red-600 hover:text-red-900"
                                            data-id="<%= notification.notification_id %>"
                                            data-title="<%= notification.title %>"
                                            title="Delete Notification"
                                        >
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    <% } else { %>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                                No notifications found
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <div class="mb-4">
            <h3 class="text-xl font-semibold text-gray-900">Confirm Deletion</h3>
            <p class="text-gray-500 mt-2">Are you sure you want to delete the notification "<span id="deleteNotificationTitle"></span>"? This action cannot be undone.</p>
        </div>

        <div class="flex justify-end space-x-3">
            <button id="cancelDeleteBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition">
                Cancel
            </button>
            <form id="deleteForm" method="POST" action="">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle notification status
        const toggleButtons = document.querySelectorAll('.toggle-status');
        toggleButtons.forEach(button => {
            button.addEventListener('click', async function() {
                const id = this.dataset.id;
                const isActive = this.dataset.active === 'true';

                try {
                    const response = await fetch(`/admin/notifications/${id}/toggle`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Update button text and data attribute
                        this.textContent = isActive ? 'Activate' : 'Deactivate';
                        this.dataset.active = isActive ? 'false' : 'true';

                        // Update button color
                        this.classList.remove(`text-${isActive ? 'yellow' : 'green'}-600`);
                        this.classList.remove(`hover:text-${isActive ? 'yellow' : 'green'}-900`);
                        this.classList.add(`text-${isActive ? 'green' : 'yellow'}-600`);
                        this.classList.add(`hover:text-${isActive ? 'green' : 'yellow'}-900`);

                        // Update status badge
                        const statusBadge = this.closest('tr').querySelector('td:nth-child(3) span');
                        statusBadge.textContent = isActive ? 'Inactive' : 'Active';
                        statusBadge.classList.remove(isActive ? 'bg-green-100' : 'bg-gray-100');
                        statusBadge.classList.remove(isActive ? 'text-green-800' : 'text-gray-800');
                        statusBadge.classList.add(isActive ? 'bg-gray-100' : 'bg-green-100');
                        statusBadge.classList.add(isActive ? 'text-gray-800' : 'text-green-800');

                        // Show success message
                        ToastNotifications.success(`Notification ${isActive ? 'deactivated' : 'activated'} successfully`);
                    } else {
                        ToastNotifications.error(data.message || 'Failed to update notification status');
                    }
                } catch (error) {
                    console.error('Error updating notification status:', error);
                    ToastNotifications.error('Failed to update notification status');
                }
            });
        });

        // Delete notification
        const deleteButtons = document.querySelectorAll('.delete-notification');
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteNotificationTitle = document.getElementById('deleteNotificationTitle');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const title = this.dataset.title;

                deleteForm.action = `/admin/notifications/${id}/delete`;
                deleteNotificationTitle.textContent = title;
                deleteModal.classList.remove('hidden');
            });
        });

        cancelDeleteBtn.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    });
</script>

