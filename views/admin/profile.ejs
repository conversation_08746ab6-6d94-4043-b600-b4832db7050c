<!-- Admin Profile Page -->
<div class="min-h-screen bg-gradient-to-br from-purple-600 to-purple-800">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-800">Administrator Profile</h1>
            <p class="text-gray-600 mt-2">System Administration Dashboard</p>
          </div>
          <div class="flex space-x-3">
            <button id="edit-profile-btn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-edit mr-2"></i>Edit Profile
            </button>
            <button id="change-password-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-key mr-2"></i>Change Password
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Personal Information Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6">
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>
          <div class="p-6">
            <!-- Profile Image -->
            <div class="flex flex-col items-center mb-6">
              <div class="relative">
                <% if (user && user.profile_image) { %>
                  <img src="<%= user.profile_image %>" alt="Admin Photo" class="w-32 h-32 rounded-full object-cover border-4 border-purple-600 shadow-lg">
                <% } else { %>
                  <div class="w-32 h-32 rounded-full bg-purple-600 flex items-center justify-center text-white text-4xl font-bold shadow-lg">
                    <%= user && user.name ? user.name.charAt(0).toUpperCase() : 'A' %>
                  </div>
                <% } %>
                <button class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                  <i class="fas fa-camera text-purple-600"></i>
                </button>
              </div>
              <h3 class="text-xl font-bold text-gray-800"><%= user ? user.name : 'Administrator' %></h3>
              <p class="text-purple-600 font-semibold">System Administrator</p>
              <span class="mt-2 px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                <i class="fas fa-shield-alt mr-1"></i>Admin Access
              </span>
            </div>

            <!-- Contact Information -->
            <div class="space-y-4">
              <div class="flex items-center">
                <i class="fas fa-envelope text-purple-600 w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.email : '<EMAIL>' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user text-purple-600 w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.username : 'admin' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-calendar text-purple-600 w-5"></i>
                <span class="ml-3 text-gray-700">
                  Joined: <%= user && user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A' %>
                </span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-clock text-purple-600 w-5"></i>
                <span class="ml-3 text-gray-700">
                  Last Login: <%= user && user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never' %>
                </span>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">About</h4>
              <p class="text-gray-600 text-sm leading-relaxed">
                <%= user && user.bio ? user.bio : 'System administrator responsible for managing the educational platform, user accounts, and technical infrastructure. Ensuring smooth operations and data security.' %>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- System Overview -->
      <div class="lg:col-span-2">
        <div class="space-y-6">
          <!-- System Statistics -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6">
              <h2 class="text-xl font-semibold">System Overview</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg">
                  <div class="flex items-center">
                    <div class="bg-blue-500 rounded-full p-3">
                      <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-semibold text-gray-800">Total Users</h3>
                      <p class="text-2xl font-bold text-blue-600" id="total-users">Loading...</p>
                    </div>
                  </div>
                </div>
                <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
                  <div class="flex items-center">
                    <div class="bg-green-500 rounded-full p-3">
                      <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-semibold text-gray-800">Active Users</h3>
                      <p class="text-2xl font-bold text-green-600" id="active-users">Loading...</p>
                    </div>
                  </div>
                </div>
                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg">
                  <div class="flex items-center">
                    <div class="bg-yellow-500 rounded-full p-3">
                      <i class="fas fa-database text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-semibold text-gray-800">Database Size</h3>
                      <p class="text-2xl font-bold text-yellow-600" id="db-size">Loading...</p>
                    </div>
                  </div>
                </div>
                <div class="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg">
                  <div class="flex items-center">
                    <div class="bg-red-500 rounded-full p-3">
                      <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                      <h3 class="text-lg font-semibold text-gray-800">System Alerts</h3>
                      <p class="text-2xl font-bold text-red-600" id="system-alerts">Loading...</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Administrative Details -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6">
              <h2 class="text-xl font-semibold">Administrative Information</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Role</h4>
                  <p class="text-gray-800 font-medium">System Administrator</p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Access Level</h4>
                  <p class="text-gray-800 font-medium">Full System Access</p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Department</h4>
                  <p class="text-gray-800 font-medium">IT Administration</p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Permissions</h4>
                  <p class="text-gray-800 font-medium">All Modules</p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Phone</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.phone ? user.phone : 'Not provided' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Institution</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.institution ? user.institution : 'School System' %></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6">
              <h2 class="text-xl font-semibold">Quick Actions</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/admin/dashboard" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-tachometer-alt text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Dashboard</span>
                </a>
                <a href="/admin/users" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-users text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Users</span>
                </a>
                <a href="/admin/students/data" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-graduation-cap text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Students</span>
                </a>
                <a href="/admin/settings" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-cog text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Settings</span>
                </a>
                <a href="/admin/reports" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-chart-bar text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Reports</span>
                </a>
                <a href="/admin/logs" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-file-alt text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Logs</span>
                </a>
                <a href="/admin/inventory" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-boxes text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Inventory</span>
                </a>
                <a href="/admin/help" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-question-circle text-purple-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Help</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Profile Modal -->
<div id="edit-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Edit Profile</h3>
        <button id="close-edit-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="edit-profile-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input type="text" name="name" value="<%= user ? user.name : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input type="email" name="email" value="<%= user ? user.email : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input type="tel" name="phone" value="<%= user ? user.phone : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent"><%= user ? user.bio : '' %></textarea>
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-edit" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-purple-600 to-purple-800 text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Change Password</h3>
        <button id="close-password-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="change-password-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
          <input type="password" name="current_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
          <input type="password" name="new_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
          <input type="password" name="confirm_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-password" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">Update Password</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Load system statistics
  loadSystemStats();

  // Modal functionality
  const editProfileBtn = document.getElementById('edit-profile-btn');
  const changePasswordBtn = document.getElementById('change-password-btn');
  const editModal = document.getElementById('edit-profile-modal');
  const passwordModal = document.getElementById('change-password-modal');

  // Edit Profile Modal
  editProfileBtn.addEventListener('click', () => {
    editModal.classList.remove('hidden');
  });

  document.getElementById('close-edit-modal').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  document.getElementById('cancel-edit').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  // Change Password Modal
  changePasswordBtn.addEventListener('click', () => {
    passwordModal.classList.remove('hidden');
  });

  document.getElementById('close-password-modal').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  document.getElementById('cancel-password').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  // Form submissions
  document.getElementById('edit-profile-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    try {
      const response = await fetch('/api/profile/update', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert('Profile updated successfully!');
        editModal.classList.add('hidden');
        location.reload();
      } else {
        alert('Failed to update profile');
      }
    } catch (error) {
      alert('Error updating profile');
    }
  });

  document.getElementById('change-password-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    if (formData.get('new_password') !== formData.get('confirm_password')) {
      alert('New passwords do not match');
      return;
    }

    try {
      const response = await fetch('/api/profile/change-password', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert('Password changed successfully!');
        passwordModal.classList.add('hidden');
        e.target.reset();
      } else {
        alert('Failed to change password');
      }
    } catch (error) {
      alert('Error changing password');
    }
  });

  // Close modals on outside click
  editModal.addEventListener('click', (e) => {
    if (e.target === editModal) {
      editModal.classList.add('hidden');
    }
  });

  passwordModal.addEventListener('click', (e) => {
    if (e.target === passwordModal) {
      passwordModal.classList.add('hidden');
    }
  });
});

async function loadSystemStats() {
  try {
    const response = await fetch('/api/admin/stats');
    if (response.ok) {
      const stats = await response.json();
      document.getElementById('total-users').textContent = stats.totalUsers || '0';
      document.getElementById('active-users').textContent = stats.activeUsers || '0';
      document.getElementById('db-size').textContent = stats.dbSize || '0 MB';
      document.getElementById('system-alerts').textContent = stats.systemAlerts || '0';
    } else {
      // Fallback values
      document.getElementById('total-users').textContent = '150';
      document.getElementById('active-users').textContent = '125';
      document.getElementById('db-size').textContent = '2.5 GB';
      document.getElementById('system-alerts').textContent = '3';
    }
  } catch (error) {
    console.error('Error loading stats:', error);
    // Fallback values
    document.getElementById('total-users').textContent = '150';
    document.getElementById('active-users').textContent = '125';
    document.getElementById('db-size').textContent = '2.5 GB';
    document.getElementById('system-alerts').textContent = '3';
  }
}
</script>