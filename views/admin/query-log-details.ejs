<%- include('../partials/admin-header') %>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Query Log Details</h1>
        <a href="/admin/query-logs" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Query Logs
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Query Information</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about the database query execution</p>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Log ID</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.log_id %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Timestamp</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= formatDateTime(log.timestamp) %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Query Type</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            <%= log.query_type || 'UNKNOWN' %>
                        </span>
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Duration</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <% if (log.duration > 1000) { %>
                            <span class="text-red-600 font-medium"><%= log.duration %> ms</span>
                        <% } else if (log.duration > 500) { %>
                            <span class="text-yellow-600"><%= log.duration %> ms</span>
                        <% } else { %>
                            <%= log.duration %> ms
                        <% } %>
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">User</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.user_username || log.user_id || 'System' %></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <% if (log.status === 'error') { %>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Error
                            </span>
                        <% } else if (log.status === 'slow') { %>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Slow
                            </span>
                        <% } else { %>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                Success
                            </span>
                        <% } %>
                    </dd>
                </div>
                <% if (log.table_name) { %>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Table</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.table_name %></dd>
                </div>
                <% } %>
                <% if (log.affected_rows) { %>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Affected Rows</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.affected_rows %></dd>
                </div>
                <% } %>
                <% if (log.route) { %>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Route</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.route %></dd>
                </div>
                <% } %>
                <% if (log.ip_address) { %>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= log.ip_address %></dd>
                </div>
                <% } %>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Query</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <div class="bg-gray-100 p-3 rounded-md overflow-x-auto">
                            <pre class="text-xs"><%= log.query %></pre>
                        </div>
                    </dd>
                </div>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Parameters</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <div class="bg-gray-100 p-3 rounded-md overflow-x-auto">
                            <pre class="text-xs"><%= typeof log.params === 'object' ? JSON.stringify(log.params, null, 2) : log.params %></pre>
                        </div>
                    </dd>
                </div>
                <% if (log.result) { %>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Result</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <div class="bg-gray-100 p-3 rounded-md overflow-x-auto max-h-60">
                            <pre class="text-xs"><%= typeof log.result === 'object' ? JSON.stringify(log.result, null, 2) : log.result %></pre>
                        </div>
                    </dd>
                </div>
                <% } %>
                <% if (log.error_message) { %>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Error Message</dt>
                    <dd class="mt-1 text-sm text-red-600">
                        <div class="bg-red-50 p-3 rounded-md overflow-x-auto">
                            <pre class="text-xs"><%= log.error_message %></pre>
                        </div>
                    </dd>
                </div>
                <% } %>
            </dl>
        </div>
    </div>
</div>

<%- include('../partials/admin-footer') %>
