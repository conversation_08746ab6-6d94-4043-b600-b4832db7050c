const querystring = require('querystring');

<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2"><%= pageTitle %></h1>
        <p class="text-gray-600">View and analyze database queries executed in the application</p>
    </div>

    <!-- Tabs -->
    <div class="mb-6 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
            <li class="mr-2">
                <a href="#all-queries" class="inline-block p-4 border-b-2 border-blue-600 rounded-t-lg active text-blue-600" aria-current="page">
                    All Queries
                </a>
            </li>
            <li class="mr-2"> <!-- i amd -->
                <a href="#slow-queries" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Slow Queries
                </a>
            </li>
            <li class="mr-2">
                <a href="#recent-queries" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Recent Queries
                </a>
            </li>
            <li>
                <a href="/admin/error-logs" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300">
                    Error Logs
                </a>
            </li>
        </ul>
    </div>

    <!-- Filter Form -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form action="/admin/query-logs" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Query Type</label>
                <select id="type" name="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="all" <%= filters.queryType === 'all' ? 'selected' : '' %>>All Queries</option>
                    <% queryTypes.forEach(type => { %>
                        <option value="<%= type.query_type %>" <%= filters.queryType === type.query_type ? 'selected' : '' %>><%= type.query_type %> (<%= type.count %>)</option>
                    <% }) %>
                </select>
            </div>
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="search" name="search" value="<%= filters.search %>" placeholder="Search in queries..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="startDate" name="startDate" value="<%= filters.startDate %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="endDate" name="endDate" value="<%= filters.endDate %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label for="minDuration" class="block text-sm font-medium text-gray-700 mb-1">Min Duration (ms)</label>
                <input type="number" id="minDuration" name="minDuration" value="<%= filters.minDuration %>" min="0" step="100" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- All Queries -->
        <div id="all-queries" class="tab-pane active">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Database Queries</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing <%= logs.length %> of <%= pagination.totalItems || 0 %> queries</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Query</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (logs.length === 0) { %>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No query logs found</td>
                                </tr>
                            <% } else { %>
                                <% logs.forEach(log => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(log.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <%= log.query_type || 'UNKNOWN' %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <% if (log.duration > 1000) { %>
                                                <span class="text-red-600 font-medium"><%= log.duration %> ms</span>
                                            <% } else if (log.duration > 500) { %>
                                                <span class="text-yellow-600"><%= log.duration %> ms</span>
                                            <% } else { %>
                                                <%= log.duration %> ms
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                            <div class="max-h-10 overflow-hidden">
                                                <code class="text-xs"><%= log.query %></code>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= log.user_username || log.user_id || 'System' %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <% if (log.status === 'error') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Error
                                                </span>
                                            <% } else if (log.status === 'slow') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    Slow
                                                </span>
                                            <% } else { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Success
                                                </span>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <% if (pagination && pagination.totalPages > 1) { %>
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium"><%= logs.length > 0 ? (pagination.page - 1) * pagination.perPage + 1 : 0 %></span> to <span class="font-medium"><%= Math.min(pagination.page * pagination.perPage, pagination.totalItems || 0) %></span> of <span class="font-medium"><%= pagination.totalItems || 0 %></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <% if (pagination.page > 1) { %>
                                        <a href="?page=<%= pagination.page - 1 %>&type=<%= filters.queryType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>&minDuration=<%= filters.minDuration %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Previous</span>
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    <% } %>

                                    <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                        <a href="?page=<%= i %>&type=<%= filters.queryType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>&minDuration=<%= filters.minDuration %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <%= pagination.page === i ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50' %>">
                                            <%= i %>
                                        </a>
                                    <% } %>

                                    <% if (pagination.page < pagination.totalPages) { %>
                                        <a href="?page=<%= pagination.page + 1 %>&type=<%= filters.queryType %>&search=<%= filters.search %>&startDate=<%= filters.startDate %>&endDate=<%= filters.endDate %>&minDuration=<%= filters.minDuration %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Next</span>
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    <% } %>
                                </nav>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Slow Queries -->
        <div id="slow-queries" class="tab-pane hidden">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Slow Queries (> 1000ms)</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing top <%= slowQueries.length %> slowest queries</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Query</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (slowQueries.length === 0) { %>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No slow queries found</td>
                                </tr>
                            <% } else { %>
                                <% slowQueries.forEach(log => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(log.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <%= log.query_type || 'UNKNOWN' %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                                            <%= log.duration %> ms
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                            <div class="max-h-10 overflow-hidden">
                                                <code class="text-xs"><%= log.query %></code>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= log.user_username || log.user_id || 'System' %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recent Queries -->
        <div id="recent-queries" class="tab-pane hidden">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Queries</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Showing last <%= recentQueries.length %> queries</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Query</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (recentQueries.length === 0) { %>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No recent queries found</td>
                                </tr>
                            <% } else { %>
                                <% recentQueries.forEach(log => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= formatDateTime(log.timestamp) %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <%= log.query_type || 'UNKNOWN' %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <% if (log.duration > 1000) { %>
                                                <span class="text-red-600 font-medium"><%= log.duration %> ms</span>
                                            <% } else if (log.duration > 500) { %>
                                                <span class="text-yellow-600"><%= log.duration %> ms</span>
                                            <% } else { %>
                                                <%= log.duration %> ms
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                                            <div class="max-h-10 overflow-hidden">
                                                <code class="text-xs"><%= log.query %></code>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= log.user_username || log.user_id || 'System' %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <% if (log.status === 'error') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Error
                                                </span>
                                            <% } else if (log.status === 'slow') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    Slow
                                                </span>
                                            <% } else { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Success
                                                </span>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('a[href^="#"]');
        const tabContents = document.querySelectorAll('.tab-pane');

        // Show the tab content based on the URL hash or default to first tab
        function showTab(tabId) {
            tabContents.forEach(content => {
                content.classList.add('hidden');
                if (content.id === tabId.substring(1)) {
                    content.classList.remove('hidden');
                }
            });

            tabs.forEach(tab => {
                tab.classList.remove('border-blue-600', 'text-blue-600');
                tab.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');

                if (tab.getAttribute('href') === tabId) {
                    tab.classList.add('border-blue-600', 'text-blue-600');
                    tab.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
                }
            });
        }

        // Set initial tab based on URL hash or default to first tab
        const initialTab = window.location.hash || '#all-queries';
        showTab(initialTab);

        // Handle tab clicks
        tabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const tabId = this.getAttribute('href');
                    showTab(tabId);
                    window.location.hash = tabId;
                }
            });
        });

        // Handle hash change
        window.addEventListener('hashchange', function() {
            const tabId = window.location.hash || '#all-queries';
            showTab(tabId);
        });
    });
</script>

<%- include('../partials/admin-footer') %>
