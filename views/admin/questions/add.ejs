<!-- Add these scripts in the head section, in exactly this order -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Add Chosen CSS and JS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>
<style>
    /* Toggle Switch Styles */
    .toggle-checkbox:checked {
        right: 0;
        border-color: #68D391;
    }
    .toggle-checkbox:checked + .toggle-label {
        background-color: #68D391;
    }
    .toggle-label {
        transition: background-color 0.3s ease;
    }
    .toggle-checkbox {
        transition: all 0.3s ease;
        right: 4px;
    }
</style>
<!-- Add Question Form -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/admin/questions" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            Back to Question Bank
        </a>
    </div>

    <% if (error) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <%= error %>
        </div>
    <% } %>

    <div class="bg-white shadow-md rounded-lg p-6">
        <form action="/admin/questions/add" method="POST" onsubmit="return validateForm()">
            <!-- Exam Selection -->
            <div class="mb-4">
                <label for="exam_id" class="block text-gray-700 text-sm font-bold mb-2">Exam (Optional)</label>
                <select id="exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Select Exam</option>
                    <% exams.forEach(exam => { %>
                        <option value="<%= exam.exam_id %>" <%= formData && formData.exam_id == exam.exam_id ? 'selected' : '' %>>
                            <%= exam.exam_name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Selection -->
            <div class="mb-4">
                <label for="section_id" class="block text-gray-700 text-sm font-bold mb-2">Section (Optional)</label>
                <select id="section_id" name="section_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Select Section</option>
                    <% if (typeof sections !== 'undefined' && sections.length > 0) { %>
                        <% sections.forEach(section => { %>
                            <option value="<%= section.section_id %>" <%= formData && formData.section_id == section.section_id ? 'selected' : '' %>>
                                <%= section.section_name %>
                            </option>
                        <% }); %>
                    <% } %>
                </select>
            </div>

            <!-- Question Type -->
            <div class="mb-4">
                <label for="question_type" class="block text-gray-700 text-sm font-bold mb-2">Question Type</label>
                <select id="question_type" name="question_type" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    <option value="">Select Question Type</option>
                    <option value="multiple_choice" <%= formData && formData.question_type === 'multiple_choice' ? 'selected' : '' %>>Multiple Choice</option>
                    <option value="true_false" <%= formData && formData.question_type === 'true_false' ? 'selected' : '' %>>True/False</option>
                    <option value="fill_up" <%= formData && formData.question_type === 'fill_up' ? 'selected' : '' %>>Fill in the Blank</option>
                    <option value="essay" <%= formData && formData.question_type === 'essay' ? 'selected' : '' %>>Essay</option>
                </select>
            </div>

            <!-- Essay Selection (shown for all question types) -->
            <div class="mb-4" id="essaySelectionContainer">
                <div class="flex justify-between items-center mb-2">
                    <label for="essay_id" class="block text-gray-700 text-sm font-bold">Link Questions to Essay (Optional)</label>
                    <a href="/admin/essays/add" class="bg-blue-500 hover:bg-blue-600 text-white text-sm px-3 py-1 rounded" target="_blank">
                        <i class="fas fa-plus mr-1"></i> Create New Essay
                    </a>
                </div>
                <select id="essay_id" name="essay_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">None (No Essay)</option>
                    <% if (essays && essays.length > 0) { %>
                        <% essays.forEach(essay => { %>
                            <option value="<%= essay.essay_id %>" <%= selectedEssayId == essay.essay_id ? 'selected' : '' %>>
                                <%= essay.title %> (<%= essay.question_count %> questions, <%= essay.formatted_date %>)
                            </option>
                        <% }); %>
                    <% } else { %>
                        <option value="" disabled>No essays available - create one first</option>
                    <% } %>
                </select>
                <p class="mt-1 text-sm text-gray-500">
                    Link this question to an essay that students must read before answering.
                </p>
            </div>

            <!-- Question Text -->
            <div class="mb-4">
                <label for="question_text" class="block text-gray-700 text-sm font-bold mb-2">Question Text</label>
                <textarea id="question_text" name="question_text" rows="4" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Enter your question here..." required><%= formData ? formData.question_text : '' %></textarea>
            </div>

            <!-- Preview Section -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2">Preview:</label>
                <div id="math_preview" class="p-4 border rounded-md bg-gray-50"></div>
            </div>

            <!-- Multiple Choice Options (shown only for MCQ) -->
            <fieldset id="optionsContainer" class="mb-4 hidden">
                <legend class="block text-gray-700 text-sm font-bold mb-2">Options</legend>
                <div id="optionsList">
                    <% if (formData && formData.option_text) { %>
                        <% formData.option_text.forEach((option, index) => { %>
                            <div class="option-row mb-2">
                                <div class="input-group">
                                    <div class="input-group-text">
                                        <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                            <input type="checkbox" name="correct_option[]" value="<%= index %>"
                                                id="correct_option_<%= index %>"
                                                <%= formData.correct_option == index ? 'checked' : '' %>
                                                class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer">
                                            <label for="correct_option_<%= index %>" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                                        </div>
                                    </div>
                                    <input type="text" class="form-control placeholder-green" name="option_text[]"
                                        value="<%= option %>" placeholder="Option text..." required>
                                    <button type="button" class="btn btn-danger remove-option">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <% }); %>
                    <% } %>
                </div>
                <button type="button" class="btn btn-success mt-2" id="addOption">Add Option</button>
            </fieldset>

            <!-- True/False Options (shown only for True/False) -->
            <div id="true_false_options" class="mb-4 hidden">
                <label class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <div class="flex space-x-4">
                    <div class="flex items-center">
                        <input type="radio" id="true" name="correct_answer" value="true" class="mr-2">
                        <label for="true">True</label>
                    </div>
                    <div class="flex items-center">
                        <input type="radio" id="false" name="correct_answer" value="false" class="mr-2">
                        <label for="false">False</label>
                    </div>
                </div>
            </div>

            <!-- Fill in the Blank Options -->
            <div id="fill_up_options" class="mb-4 hidden">
                <label for="fill_up_answer" class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <input type="text" id="fill_up_answer" name="fill_up_answer" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Enter the correct answer">
                <p class="mt-1 text-sm text-gray-500">
                    Enter the exact text that should fill in the blank. For multiple possible answers, separate them with a pipe symbol (|).
                    <br>Example: "Paris|paris|The capital of France"
                </p>
            </div>

            <!-- Essay Options -->
            <div id="essay_options" class="mb-4 hidden">
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                This is an essay question. Students will provide a written response that will need to be manually graded.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="min_word_count" class="block text-gray-700 text-sm font-bold mb-2">Minimum Word Count</label>
                        <input type="number" id="min_word_count" name="min_word_count" min="0" value="<%= formData && formData.min_word_count ? formData.min_word_count : 0 %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                    <div>
                        <label for="max_word_count" class="block text-gray-700 text-sm font-bold mb-2">Maximum Word Count</label>
                        <input type="number" id="max_word_count" name="max_word_count" min="0" value="<%= formData && formData.max_word_count ? formData.max_word_count : 500 %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                </div>
            </div>

            <!-- Solution Text -->
            <div class="mb-4">
                <label for="solution_text" class="block text-gray-700 text-sm font-bold mb-2">Solution/Explanation (Optional)</label>
                <textarea id="solution_text" name="solution_text" rows="3" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><%= formData ? formData.solution_text : '' %></textarea>
            </div>

            <!-- Marks -->
            <div class="mb-4">
                <label for="marks" class="block text-gray-700 text-sm font-bold mb-2">Marks</label>
                <input type="number" step="0.01" id="marks" name="marks" value="<%= formData && formData.marks ? formData.marks : 1.00 %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                <p class="mt-1 text-sm text-gray-500">Marks awarded for correct answer (default: 1.00)</p>
            </div>

            <!-- Negative Marks -->
            <div class="mb-4">
                <label for="negative_marks" class="block text-gray-700 text-sm font-bold mb-2">Negative Marks</label>
                <input type="number" step="0.01" id="negative_marks" name="negative_marks" value="<%= formData && formData.negative_marks ? formData.negative_marks : 0.00 %>" min="0" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                <p class="mt-1 text-sm text-gray-500">Marks deducted for incorrect answer (default: 0.00)</p>
            </div>

            <!-- Categories Selection -->
            <div class="mb-4">
                <label for="category_ids" class="block text-gray-700 text-sm font-bold mb-2">Categories</label>
                <select id="category_ids" name="category_ids[]" multiple class="chosen-select w-full">
                    <% categories.forEach(category => { %>
                        <option value="<%= category.category_id %>"
                            <%= formData && formData.category_ids && formData.category_ids.includes(category.category_id.toString()) ? 'selected' : '' %>>
                            <%= category.name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Add Question
                </button>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for dynamic form elements -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize math preview
        const questionInput = document.getElementById('question_text');
        const solutionInput = document.getElementById('solution_text');
        const previewDiv = document.getElementById('math_preview');

        if (questionInput && previewDiv) {
            updatePreview(); // Initial preview

            // Update preview on input
            questionInput.addEventListener('input', updatePreview);
            if (solutionInput) {
                solutionInput.addEventListener('input', updatePreview);
            }
        }

        // Exam and Section handling
        const examSelect = document.getElementById('exam_id');
        const sectionSelect = document.getElementById('section_id');

        examSelect.addEventListener('change', async function() {
            const examId = this.value;

            if (examId) {
                // Enable section select
                sectionSelect.disabled = false;
                sectionSelect.innerHTML = '<option value="">Loading sections...</option>';

                // Fetch sections for the selected exam
                try {
                    const response = await fetch(`/admin/questions/get-sections/${examId}`);
                    const sections = await response.json();

                    sectionSelect.innerHTML = '<option value="">Select a Section</option>';
                    sections.forEach(section => {
                        const option = document.createElement('option');
                        option.value = section.section_id;
                        option.textContent = section.section_name;
                        sectionSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('Error fetching sections:', error);
                    sectionSelect.innerHTML = '<option value="">Error loading sections</option>';
                }
            } else {
                // Disable section select if no exam is selected
                sectionSelect.disabled = true;
                sectionSelect.innerHTML = '<option value="">Select an Exam First</option>';
            }
        });

        // Question Type handling
        const questionTypeSelect = document.getElementById('question_type');
        const optionsContainer = document.getElementById('optionsContainer');
        const optionsList = document.getElementById('optionsList');
        const addOptionBtn = document.getElementById('addOption');

        questionTypeSelect.addEventListener('change', function() {
            const questionType = this.value;

            // Hide all option sections first
            optionsContainer.classList.add('hidden');
            document.getElementById('true_false_options').classList.add('hidden');
            document.getElementById('fill_up_options').classList.add('hidden');
            document.getElementById('essay_options').classList.add('hidden');

            // Show the appropriate section based on question type
            if (questionType === 'multiple_choice') {
                optionsContainer.classList.remove('hidden');
            } else if (questionType === 'true_false') {
                document.getElementById('true_false_options').classList.remove('hidden');
            } else if (questionType === 'fill_up') {
                document.getElementById('fill_up_options').classList.remove('hidden');
            } else if (questionType === 'essay') {
                document.getElementById('essay_options').classList.remove('hidden');
            }
        });

        // Add Option button for MCQ
        addOptionBtn.addEventListener('click', function() {
            const optionRow = document.createElement('div');
            optionRow.className = 'option-row mb-2';
            optionRow.innerHTML = `
                <div class="input-group">
                    <div class="input-group-text">
                        <div class="relative inline-block w-10 mr-2 align-middle select-none">
                            <input type="checkbox" name="correct_option[]" value="${optionsList.children.length}" id="correct_option_${optionsList.children.length}" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer">
                            <label for="correct_option_${optionsList.children.length}" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                        </div>
                    </div>
                    <input type="text" class="form-control" name="option_text[]" required>
                    <button type="button" class="btn btn-danger remove-option">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            optionsList.appendChild(optionRow);
            updateOptionIndexes();
        });

        // Remove option
        optionsList.addEventListener('click', function(e) {
            if (e.target.closest('.remove-option')) {
                e.target.closest('.option-row').remove();
                updateOptionIndexes();
            }
        });

        // Update option indexes when options are added/removed
        function updateOptionIndexes() {
            const options = optionsList.querySelectorAll('.option-row');
            options.forEach((option, index) => {
                const checkbox = option.querySelector('input[type="checkbox"]');
                if (checkbox) {
                    checkbox.value = index;
                    checkbox.id = `correct_option_${index}`;
                    const label = option.querySelector('label');
                    if (label) {
                        label.setAttribute('for', `correct_option_${index}`);
                    }
                }
            });
        }
    });
</script>

<script>
    function validateForm() {
        // Clear any existing error messages
        clearErrors();

        const questionType = document.getElementById('question_type').value;
        const questionText = document.getElementById('question_text').value.trim();
        const examId = document.getElementById('exam_id').value; // Get the exam ID
        let isValid = true;
        let errorMessages = [];

        // Basic validation
        if (!questionText) {
            errorMessages.push('Question text is required');
            isValid = false;
        }

        if (!questionType) {
            errorMessages.push('Question type is required');
            isValid = false;
        }

        // If an exam is selected, validate the section
        if (examId && !document.getElementById('section_id').value) {
            errorMessages.push('Section is required if an exam is selected');
            isValid = false;
        }

        // MCQ specific validation
        if (questionType === 'multiple_choice') {
            const options = Array.from(document.querySelectorAll('input[name="option_text[]"]'))
                .filter(option => option.value.trim());
            const correctAnswer = document.querySelector('input[name="correct_option[]"]:checked');

            if (options.length < 2) {
                errorMessages.push('At least 2 options are required for MCQ');
                isValid = false;
            }

            if (!correctAnswer) {
                errorMessages.push('Please select the correct answer');
                isValid = false;
            }
        }

        // True/False specific validation
        if (questionType === 'true_false') {
            const correctAnswer = document.querySelector('input[name="correct_answer"]:checked');
            if (!correctAnswer) {
                errorMessages.push('Please select either True or False');
                isValid = false;
            }
        }

        // Display errors if any
        if (!isValid) {
            displayErrors(errorMessages);
        }

        return isValid;
    }

    // Add event listener to the form
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form[action="/admin/questions/add"]');
        if (form) {
            form.addEventListener('submit', function(event) {
                console.log('Form submission triggered');
                const isValid = validateForm();
                if (!isValid) {
                    console.log('Form validation failed, preventing submission');
                    event.preventDefault();
                } else {
                    console.log('Form validation passed, allowing submission');
                }
            });
        }
    });

    // Question Type handling
    document.getElementById('question_type').addEventListener('change', function() {
        const optionsContainer = document.getElementById('optionsContainer');
        const trueFalseOptions = document.getElementById('true_false_options');
        const fillUpOptions = document.getElementById('fill_up_options');
        const essayOptions = document.getElementById('essay_options');

        // Hide all options first
        optionsContainer.classList.add('hidden');
        trueFalseOptions.classList.add('hidden');
        fillUpOptions.classList.add('hidden');
        essayOptions.classList.add('hidden');

        // Show relevant options based on selection
        if (this.value === 'multiple_choice') {
            optionsContainer.classList.remove('hidden');
        } else if (this.value === 'true_false') {
            trueFalseOptions.classList.remove('hidden');
        } else if (this.value === 'fill_up') {
            fillUpOptions.classList.remove('hidden');
        } else if (this.value === 'essay') {
            essayOptions.classList.remove('hidden');
        }
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for jQuery to load before running any jQuery-dependent code
        window.addEventListener('load', function() {
            if (typeof jQuery != 'undefined') {
                // Initialize Chosen
                jQuery(document).ready(function($) {
                    $('.chosen-select').chosen({
                        width: '100%',
                        placeholder_text_multiple: 'Select categories',
                        no_results_text: 'No results found',
                        allow_single_deselect: true,
                        search_contains: true,
                        max_selected_options: 5
                    });
                });
            } else {
                console.error('jQuery is not loaded');
            }
        });

        // Form validation
        const form = document.querySelector('form[action="/admin/questions/add"]');

        if (form) {
            form.addEventListener('submit', function(event) {
                event.preventDefault(); // Prevent default submission

                if (validateForm()) {
                    this.submit(); // Submit if validation passes
                }
            });
        }
    });

    function validateForm() {
        // Clear any existing error messages
        clearErrors();

        const questionType = document.getElementById('question_type').value;
        const questionText = document.getElementById('question_text').value.trim();
        let isValid = true;
        let errorMessages = [];

        // Basic validation
        if (!questionText) {
            errorMessages.push('Question text is required');
            isValid = false;
        }

        if (!questionType) {
            errorMessages.push('Question type is required');
            isValid = false;
        }

        // MCQ specific validation
        if (questionType === 'multiple_choice') {
            const options = Array.from(document.querySelectorAll('input[name="option_text[]"]'))
                .filter(option => option.value.trim());
            const correctAnswer = document.querySelector('input[name="correct_option[]"]:checked');

            if (options.length < 2) {
                errorMessages.push('At least 2 options are required for MCQ');
                isValid = false;
            }

            if (!correctAnswer) {
                errorMessages.push('Please select the correct answer');
                isValid = false;
            }
        }

        // True/False specific validation
        if (questionType === 'true_false') {
            const correctAnswer = document.querySelector('input[name="correct_answer"]:checked');
            if (!correctAnswer) {
                errorMessages.push('Please select either True or False');
                isValid = false;
            }
        }

        // Essay specific validation
        if (questionType === 'essay') {
            const minWordCount = parseInt(document.getElementById('min_word_count').value) || 0;
            const maxWordCount = parseInt(document.getElementById('max_word_count').value) || 0;

            if (minWordCount < 0) {
                errorMessages.push('Minimum word count cannot be negative');
                isValid = false;
            }

            if (maxWordCount < minWordCount) {
                errorMessages.push('Maximum word count must be greater than or equal to minimum word count');
                isValid = false;
            }
        }

        // Display errors if any
        if (!isValid) {
            displayErrors(errorMessages);
        }

        return isValid;
    }

    function displayErrors(messages) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'error-container bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';

        const errorList = document.createElement('ul');
        errorList.className = 'list-disc pl-5';

        messages.forEach(message => {
            const li = document.createElement('li');
            li.textContent = message;
            errorList.appendChild(li);
        });

        errorContainer.appendChild(errorList);

        const form = document.querySelector('form[action="/admin/questions/add"]');
        form.insertBefore(errorContainer, form.firstChild);
    }

    function clearErrors() {
        const existingErrors = document.querySelectorAll('.error-container');
        existingErrors.forEach(error => error.remove());
    }

    // Function to update the preview
    function updatePreview() {
        const questionText = document.getElementById('question_text').value;
        const solutionText = document.getElementById('solution_text') ? document.getElementById('solution_text').value : '';
        const previewDiv = document.getElementById('math_preview');

        if (!previewDiv) return;

        let previewContent = '<h3>Question Preview:</h3>';
        // Convert line breaks to <br> tags for proper display
        const formattedQuestionText = questionText.replace(/\n/g, '<br>');
        previewContent += `<div class="math-content question-preview">${formattedQuestionText}</div>`;

        if (solutionText) {
            previewContent += '<h3 class="mt-4">Solution Preview:</h3>';
            // Convert line breaks to <br> tags for proper display
            const formattedSolutionText = solutionText.replace(/\n/g, '<br>');
            previewContent += `<div class="math-content solution-preview">${formattedSolutionText}</div>`;
        }

        previewDiv.innerHTML = previewContent;

        // Render math if MathJax is available
        if (typeof MathJax !== 'undefined') {
            MathJax.typeset([previewDiv]);
        }
    }

    // Question type toggle handler
    document.getElementById('question_type').addEventListener('change', function() {
        const optionsContainer = document.getElementById('optionsContainer');
        const trueFalseOptions = document.getElementById('true_false_options');

        optionsContainer.classList.add('hidden');
        trueFalseOptions.classList.add('hidden');

        if (this.value === 'multiple_choice') {
            optionsContainer.classList.remove('hidden');
        } else if (this.value === 'true_false') {
            trueFalseOptions.classList.remove('hidden');
        }
    });

    // Add these styles for better Select2 appearance
    const select2Styles = document.createElement('style');
    select2Styles.textContent = `
        .select2-container--classic .select2-selection--multiple {
            border: 1px solid #e2e8f0 !important;
            border-radius: 0.375rem !important;
            min-height: 38px !important;
            background-color: white !important;
        }

        .select2-container--classic.select2-container--focus .select2-selection--multiple {
            border-color: #3b82f6 !important;
            outline: none !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5) !important;
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice {
            background-color: #e5e7eb !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.25rem !important;
            padding: 2px 8px !important;
            margin: 4px !important;
        }

        .select2-container--classic .select2-search--inline .select2-search__field {
            margin-top: 7px !important;
            font-size: 0.875rem !important;
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
            margin-right: 5px !important;
            color: #4b5563 !important;
        }

        .select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #1f2937 !important;
        }

        .select2-container--classic .select2-dropdown {
            border-color: #e2e8f0 !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }

        .select2-container--classic .select2-results__option--highlighted[aria-selected] {
            background-color: #3b82f6 !important;
        }
    `;
    document.head.appendChild(select2Styles);
</script>

<script>
$(document).ready(function() {
    $('.chosen-select').chosen({
        width: '100%',
        placeholder_text_multiple: 'Select categories',
        no_results_text: 'No results found',
        allow_single_deselect: true,
        search_contains: true,
        max_selected_options: 5
    });
});
</script>

