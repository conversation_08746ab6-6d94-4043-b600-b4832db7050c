<!-- Question Categories -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/admin/questions" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            Back to Question Bank
        </a>
    </div>

    <% if (error) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <%= error %>
        </div>
    <% } %>

    <% if (success) { %>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <%= success %>
        </div>
    <% } %>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Add Category Form -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Add New Category</h2>
            <form action="/admin/questions/categories/add" method="POST">
                <div class="mb-4">
                    <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Category Name</label>
                    <input type="text" id="name" name="name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>
                <div class="mb-4">
                    <label for="description" class="block text-gray-700 text-sm font-bold mb-2">Description (Optional)</label>
                    <textarea id="description" name="description" rows="3" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Add Category
                    </button>
                </div>
            </form>
        </div>

        <!-- Categories List -->
        <div class="bg-white shadow-md rounded-lg p-6 md:col-span-2">
            <h2 class="text-lg font-semibold mb-4">Existing Categories</h2>
            
            <% if (categories && categories.length > 0) { %>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% categories.forEach(category => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= category.name %></td>
                                    <td class="px-6 py-4 text-sm text-gray-500"><%= category.description || '-' %></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= category.question_count %></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button" onclick="openEditModal('<%= category.category_id %>', '<%= category.name %>', '<%= category.description || '' %>')" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                            <form action="/admin/questions/categories/<%= category.category_id %>/delete" method="POST" onsubmit="return confirm('Are you sure you want to delete this category?');">
                                                <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <p class="text-gray-500">No categories found. Add your first category to get started.</p>
            <% } %>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Edit Category</h3>
            <button type="button" onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="editCategoryForm" action="" method="POST">
            <div class="mb-4">
                <label for="edit_name" class="block text-gray-700 text-sm font-bold mb-2">Category Name</label>
                <input type="text" id="edit_name" name="name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
            </div>
            <div class="mb-4">
                <label for="edit_description" class="block text-gray-700 text-sm font-bold mb-2">Description (Optional)</label>
                <textarea id="edit_description" name="description" rows="3" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
            </div>
            <div class="flex justify-end">
                <button type="button" onclick="closeEditModal()" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Update Category
                </button>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for modal functionality -->
<script>
    function openEditModal(categoryId, name, description) {
        document.getElementById('editCategoryForm').action = `/admin/questions/categories/${categoryId}/update`;
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_description').value = description;
        document.getElementById('editModal').classList.remove('hidden');
    }
    
    function closeEditModal() {
        document.getElementById('editModal').classList.add('hidden');
    }
    
    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('editModal');
        const modalContent = document.querySelector('#editModal > div');
        
        if (modal && !modal.classList.contains('hidden') && !modalContent.contains(event.target)) {
            closeEditModal();
        }
    });
</script> 