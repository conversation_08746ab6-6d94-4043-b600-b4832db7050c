<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Question Category Mappings</h1>
        <a href="/admin/questions" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded">
            Back to Questions
        </a>
    </div>

    <% if (locals.flashSuccess) { %>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <%= flashSuccess %>
        </div>
    <% } %>

    <% if (locals.flashError) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <%= flashError %>
        </div>
    <% } %>

    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Add Category Mapping</h2>
        <form action="/admin/questions/category-mappings/add" method="POST" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="question_id" class="block text-sm font-medium text-gray-700 mb-1">Question</label>
                    <select name="question_id" id="question_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                        <option value="">Select a question</option>
                        <% questions.forEach(question => { %>
                            <option value="<%= question.question_id %>">
                                <%= question.question_text.substring(0, 50) %><%= question.question_text.length > 50 ? '...' : '' %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select name="category_id" id="category_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                        <option value="">Select a category</option>
                        <%
                        // Group categories by type
                        const categoryGroups = {
                            'Academic Subjects': [],
                            'Professional Fields': [],
                            'Skill-Based': [],
                            'Languages': [],
                            'Test Types': [],
                            'Question Format': [],
                            'Difficulty Level': [],
                            'Cognitive Level': [],
                            'Other': []
                        };

                        // Categorize each category
                        categories.forEach(category => {
                            // Academic subjects
                            if (['Mathematics', 'Science', 'Chemistry', 'Biology', 'Physics', 'Computer Science',
                                 'Environmental Science', 'Astronomy', 'Geology', 'Statistics', 'Calculus',
                                 'Trigonometry', 'Algebra', 'Geometry', 'World History', 'Ancient History',
                                 'Modern History', 'History', 'Geography', 'Political Science', 'Economics',
                                 'Sociology', 'Psychology', 'Philosophy', 'Literature', 'Grammar', 'Vocabulary',
                                 'Civics'].includes(category.name)) {
                                categoryGroups['Academic Subjects'].push(category);
                            }
                            // Professional fields
                            else if (['Business Management', 'Marketing', 'Finance', 'Accounting', 'Law',
                                     'Medicine', 'Engineering', 'Architecture', 'Agriculture',
                                     'Information Technology'].includes(category.name)) {
                                categoryGroups['Professional Fields'].push(category);
                            }
                            // Skill-based
                            else if (['Critical Thinking', 'Logical Reasoning', 'Quantitative Reasoning',
                                     'Verbal Reasoning', 'Data Interpretation', 'Reading Comprehension',
                                     'Writing Skills', 'Problem Solving', 'Decision Making',
                                     'Creative Thinking'].includes(category.name)) {
                                categoryGroups['Skill-Based'].push(category);
                            }
                            // Languages
                            else if (['Language', 'English', 'Hindi', 'Punjabi', 'French', 'Spanish',
                                     'German', 'Chinese', 'Japanese', 'Arabic', 'Russian'].includes(category.name)) {
                                categoryGroups['Languages'].push(category);
                            }
                            // Test types
                            else if (['Aptitude Test', 'IQ Test', 'Personality Assessment', 'Career Assessment',
                                     'Entrance Exam', 'Certification Exam', 'Competitive Exam', 'Diagnostic Test',
                                     'Practice Test', 'Self-Assessment'].includes(category.name)) {
                                categoryGroups['Test Types'].push(category);
                            }
                            // Question format
                            else if (['Multiple Choice', 'True/False', 'Fill in the Blank', 'Short Answer',
                                     'Essay', 'Matching', 'Ordering', 'Diagram Labeling', 'Case Study',
                                     'Problem Set'].includes(category.name)) {
                                categoryGroups['Question Format'].push(category);
                            }
                            // Difficulty level
                            else if (['Beginner', 'Intermediate', 'Advanced', 'Expert'].includes(category.name)) {
                                categoryGroups['Difficulty Level'].push(category);
                            }
                            // Cognitive level
                            else if (['Knowledge', 'Comprehension', 'Application', 'Analysis',
                                     'Synthesis', 'Evaluation'].includes(category.name)) {
                                categoryGroups['Cognitive Level'].push(category);
                            }
                            // Other
                            else {
                                categoryGroups['Other'].push(category);
                            }
                        });

                        // Display categories by group
                        Object.keys(categoryGroups).forEach(groupName => {
                            if (categoryGroups[groupName].length > 0) {
                        %>
                            <optgroup label="<%= groupName %>">
                                <% categoryGroups[groupName].forEach(category => { %>
                                    <option value="<%= category.category_id %>"><%= category.name %></option>
                                <% }); %>
                            </optgroup>
                        <%
                            }
                        });
                        %>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded">
                        Add Mapping
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold">Existing Category Mappings</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categories</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (mappings.length === 0) { %>
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-gray-500">No category mappings found</td>
                        </tr>
                    <% } else { %>
                        <%
                        // Group mappings by question
                        const questionMappings = {};
                        mappings.forEach(mapping => {
                            if (!questionMappings[mapping.question_id]) {
                                questionMappings[mapping.question_id] = {
                                    question_id: mapping.question_id,
                                    question_text: mapping.question_text,
                                    categories: []
                                };
                            }
                            questionMappings[mapping.question_id].categories.push({
                                mapping_id: mapping.mapping_id,
                                category_id: mapping.category_id,
                                category_name: mapping.category_name
                            });
                        });

                        // Display mappings by question
                        Object.values(questionMappings).forEach(item => {
                        %>
                            <tr>
                                <td class="px-6 py-4 whitespace-normal">
                                    <div class="text-sm text-gray-900">
                                        <%= item.question_text.substring(0, 100) %><%= item.question_text.length > 100 ? '...' : '' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        <% item.categories.forEach(category => { %>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                <%= category.category_name %>
                                            </span>
                                        <% }); %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <% item.categories.forEach(category => { %>
                                            <form action="/admin/questions/category-mappings/<%= category.mapping_id %>/delete" method="POST" onsubmit="return confirm('Are you sure you want to delete this mapping?')">
                                                <button type="submit" class="text-red-600 hover:text-red-900">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </form>
                                        <% }); %>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>
