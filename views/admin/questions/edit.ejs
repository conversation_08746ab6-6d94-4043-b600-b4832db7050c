<!-- Add these at the top of the file in the head section -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
<link rel="stylesheet" href="/css/image-styles.css">
<style>
    /* Toggle Switch Styles */
    .toggle-checkbox:checked {
        right: 0;
        border-color: #68D391;
    }
    .toggle-checkbox:checked + .toggle-label {
        background-color: #68D391;
    }
    .toggle-label {
        transition: background-color 0.3s ease;
    }
    .toggle-checkbox {
        transition: all 0.3s ease;
        right: 4px;
    }
</style>

<!-- Edit Question Form -->
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6"><%= pageTitle %></h1>

        <form method="POST" action="/admin/questions/<%= question.question_id %>/edit" id="questionEditForm">
            <!-- Exam Selection -->
            <div class="mb-4">
                <label for="exam_id" class="block text-gray-700 text-sm font-bold mb-2">Exam (Optional)</label>
                <select id="exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Select an Exam</option>
                    <option value="null">None (No Exam)</option>
                    <% exams.forEach(exam => { %>
                        <option value="<%= exam.exam_id %>" <%= question.exam_id === exam.exam_id ? 'selected' : '' %>>
                            <%= exam.exam_name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Selection -->
            <div class="mb-4">
                <label for="section_id" class="block text-gray-700 text-sm font-bold mb-2">Section (Optional)</label>
                <select id="section_id" name="section_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">Select a Section</option>
                    <option value="null">None (No Section)</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section_id %>" <%= section.section_id == question.section_id ? 'selected' : '' %>><%= section.section_name %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Question Type -->
            <div class="mb-4">
                <label for="question_type" class="block text-gray-700 text-sm font-bold mb-2">Question Type</label>
                <select id="question_type" name="question_type" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    <option value="">Select Question Type</option>
                    <option value="multiple_choice" <%= question.question_type === 'multiple_choice' ? 'selected' : '' %>>Multiple Choice</option>
                    <option value="true_false" <%= question.question_type === 'true_false' ? 'selected' : '' %>>True/False</option>
                    <option value="fill_up" <%= question.question_type === 'fill_up' ? 'selected' : '' %>>Fill in the Blank</option>
                    <option value="essay" <%= question.question_type === 'essay' ? 'selected' : '' %>>Essay</option>
                </select>
            </div>

            <!-- Essay Selection -->
            <div class="mb-4" id="essaySelectionContainer">
                <div class="flex justify-between items-center mb-2">
                    <label for="essay_id" class="block text-gray-700 text-sm font-bold">Link Questions to Essay (Optional)</label>
                    <a href="/admin/essays/add" class="bg-blue-500 hover:bg-blue-600 text-white text-sm px-3 py-1 rounded" target="_blank">
                        <i class="fas fa-plus mr-1"></i> Create New Essay
                    </a>
                </div>
                <select id="essay_id" name="essay_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">None (No Essay)</option>
                    <%
                    // Ensure essays is defined
                    const essaysList = typeof essays !== 'undefined' ? essays : [];
                    if (essaysList && essaysList.length > 0) { %>
                        <% essaysList.forEach(essay => { %>
                            <option value="<%= essay.essay_id %>" <%= question.essay_id == essay.essay_id ? 'selected' : '' %>>
                                <%= essay.title %> (<%= essay.question_count %> questions, <%= essay.formatted_date %>)
                            </option>
                        <% }); %>
                    <% } else { %>
                        <option value="" disabled>No essays available - create one first</option>
                    <% } %>
                </select>
                <p class="mt-1 text-sm text-gray-500">
                    Link this question to an essay that students must read before answering.
                </p>
            </div>

            <!-- Categories Selection -->
            <div class="mb-4">
                <label for="category_ids" class="block text-gray-700 text-sm font-bold mb-2">Categories</label>
                <select id="category_ids" name="category_ids[]" multiple class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <% categories.forEach(category => { %>
                        <option value="<%= category.category_id %>"
                                <%= question.category_ids.includes(category.category_id) ? 'selected' : '' %>>
                            <%= category.name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Question Text -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Question Text
                    <span class="text-sm text-gray-500">(Use LaTeX for math: e.g., $\int x^2 dx$)</span>
                </label>
                <textarea
                    name="question_text"
                    id="question_text"
                    rows="4"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    onInput="updatePreview()"
                ><%= question.question_text %></textarea>

                <div class="mt-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Preview:</label>
                    <div id="math_preview" class="p-4 border rounded-md bg-gray-50"></div>
                </div>
            </div>

            <!-- Question Image -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Question Image (Optional)
                </label>
                <div class="flex items-start space-x-4">
                    <div class="flex-1">
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md question-drop-zone" tabindex="0">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="question_image" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                        <span>Upload an image</span>
                                        <input id="question_image" name="question_image" type="file" class="sr-only" accept="image/*" onchange="previewQuestionImage(this)">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    PNG, JPG, GIF up to 5MB
                                </p>
                                <p class="text-xs text-indigo-600 mt-2">
                                    You can also paste an image directly (Ctrl+V or ⌘+V)
                                </p>
                            </div>
                        </div>
                        <input type="hidden" name="image_id" id="question_image_id" value="<%= question.image_id || '' %>">
                    </div>
                    <div class="w-1/3" id="question_image_preview_container" style="<%= question.image_id ? '' : 'display: none;' %>">
                        <% if (question.image_path) { %>
                            <img id="question_image_preview" src="<%= question.image_path %>" alt="Question image preview" class="question-image max-h-40 rounded border">
                        <% } else { %>
                            <img id="question_image_preview" src="" alt="Question image preview" class="question-image max-h-40 rounded border">
                        <% } %>
                        <button type="button" class="mt-2 text-sm text-red-600 hover:text-red-800" onclick="removeQuestionImage()">Remove image</button>
                    </div>
                </div>
            </div>

            <!-- Solution Text -->
            <div class="mb-4">
                <label for="solution_text" class="block text-gray-700 text-sm font-bold mb-2">Solution Text (Optional)</label>
                <textarea
                    id="solution_text"
                    name="solution_text"
                    rows="3"
                    class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    onInput="updatePreview()"
                ><%= question.solution_text || '' %></textarea>
            </div>

            <!-- Solution Image -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Solution Image (Optional)
                </label>
                <div class="flex items-start space-x-4">
                    <div class="flex-1">
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md solution-drop-zone" tabindex="0">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="solution_image" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                        <span>Upload an image</span>
                                        <input id="solution_image" name="solution_image" type="file" class="sr-only" accept="image/*" onchange="previewSolutionImage(this)">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    PNG, JPG, GIF up to 5MB
                                </p>
                                <p class="text-xs text-indigo-600 mt-2">
                                    You can also paste an image directly (Ctrl+V or ⌘+V)
                                </p>
                            </div>
                        </div>
                        <input type="hidden" name="solution_image_id" id="solution_image_id" value="<%= question.solution_image_id || '' %>">
                    </div>
                    <div class="w-1/3" id="solution_image_preview_container" style="<%= question.solution_image_id ? '' : 'display: none;' %>">
                        <% if (question.solution_image_path) { %>
                            <img id="solution_image_preview" src="<%= question.solution_image_path %>" alt="Solution image preview" class="solution-image max-h-40 rounded border">
                        <% } else { %>
                            <img id="solution_image_preview" src="" alt="Solution image preview" class="solution-image max-h-40 rounded border">
                        <% } %>
                        <button type="button" class="mt-2 text-sm text-red-600 hover:text-red-800" onclick="removeSolutionImage()">Remove image</button>
                    </div>
                </div>
            </div>

            <!-- MCQ Options -->
            <div id="mcq_options" class="mb-4 <%= question.question_type === 'multiple_choice' ? '' : 'hidden' %>">
                <label class="block text-gray-700 text-sm font-bold mb-2">Options</label>
                <div class="space-y-2" id="optionsContainer">
                    <% if (question.options && Array.isArray(question.options)) { %>
                        <% question.options.forEach((option, index) => { %>
                            <div class="flex items-center space-x-2">
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox"
                                           name="correct_option[]"
                                           value="<%= index %>"
                                           <%= option.is_correct ? 'checked' : '' %>
                                           id="correct_option_<%= index %>"
                                           class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                                           data-required="true">
                                    <label for="correct_option_<%= index %>" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                                </div>
                                <input type="text"
                                       name="option_text[]"
                                       value="<%= option.option_text %>"
                                       class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                       data-required="true">
                                <button type="button"
                                       class="option-image-btn text-blue-600 hover:text-blue-800 px-2"
                                       onclick="toggleOptionImageUpload(<%= index %>)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </button>
                                <button type="button"
                                        class="delete-option text-red-600 hover:text-red-800 px-2"
                                        onclick="deleteOption(this)">×</button>
                            </div>
                            <div class="option-image-container ml-8 mb-4 hidden" id="option_image_container_<%= index %>">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-1">
                                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md option-drop-zone" tabindex="0" data-index="<%= index %>">
                                            <div class="space-y-1 text-center">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                <div class="flex text-sm text-gray-600">
                                                    <label for="option_image_<%= index %>" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                                        <span>Upload an image</span>
                                                        <input id="option_image_<%= index %>" name="option_image_<%= index %>" type="file" class="sr-only" accept="image/*" onchange="previewOptionImage(this, <%= index %>)">
                                                    </label>
                                                    <p class="pl-1">or drag and drop</p>
                                                </div>
                                                <p class="text-xs text-gray-500">
                                                    PNG, JPG, GIF up to 5MB
                                                </p>
                                                <p class="text-xs text-indigo-600 mt-2">
                                                    You can also paste an image directly (Ctrl+V or ⌘+V)
                                                </p>
                                            </div>
                                        </div>
                                        <input type="hidden" name="option_image_id_<%= index %>" id="option_image_id_<%= index %>" value="<%= option.image_id || '' %>">
                                    </div>
                                    <div class="w-1/3" id="option_image_preview_container_<%= index %>" style="<%= option.image_id ? '' : 'display: none;' %>">
                                        <% if (option.image_path) { %>
                                            <img id="option_image_preview_<%= index %>" src="<%= option.image_path %>" alt="Option image preview" class="question-image max-h-40 rounded border">
                                        <% } else { %>
                                            <img id="option_image_preview_<%= index %>" src="" alt="Option image preview" class="question-image max-h-40 rounded border">
                                        <% } %>
                                        <button type="button" class="mt-2 text-sm text-red-600 hover:text-red-800" onclick="removeOptionImage(<%= index %>)">Remove image</button>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    <% } %>
                </div>
                <button type="button"
                        id="add_option"
                        class="mt-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Add Option
                </button>
            </div>

            <!-- True/False Options -->
            <div id="true_false_options" class="mb-4 <%= question.question_type === 'true_false' ? '' : 'hidden' %>">
                <label class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio"
                               name="true_false_answer"
                               value="true"
                               <%= question.correct_answer === 'true' || question.correct_answer === true ? 'checked' : '' %>
                               class="mr-2"
                               data-required="true">
                        <span>True</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio"
                               name="true_false_answer"
                               value="false"
                               <%= question.correct_answer === 'false' || question.correct_answer === false || !question.correct_answer ? 'checked' : '' %>
                               class="mr-2"
                               data-required="true">
                        <span>False</span>
                    </label>
                </div>
            </div>

            <!-- Fill in the Blank Options -->
            <div id="fill_up_options" class="mb-4 <%= question.question_type === 'fill_up' ? '' : 'hidden' %>">
                <label for="fill_up_answer" class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                <input type="text"
                       id="fill_up_answer"
                       name="fill_up_answer"
                       value="<%= question.question_type === 'fill_up' ? question.correct_answer : '' %>"
                       class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                       placeholder="Enter the correct answer"
                       data-required="true">
                <p class="mt-1 text-sm text-gray-500">
                    Enter the exact text that should fill in the blank. For multiple possible answers, separate them with a pipe symbol (|).
                    <br>Example: "Paris|paris|The capital of France"
                </p>
            </div>

            <!-- Marks -->
            <div class="mb-4">
                <label for="marks" class="block text-gray-700 text-sm font-bold mb-2">Marks</label>
                <input type="number" step="0.01" id="marks" name="marks" value="<%= question.marks || 1.00 %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                <p class="mt-1 text-sm text-gray-500">Marks awarded for correct answer (default: 1.00)</p>
            </div>

            <!-- Negative Marks -->
            <div class="mb-4">
                <label for="negative_marks" class="block text-gray-700 text-sm font-bold mb-2">Negative Marks</label>
                <input type="number" step="0.01" id="negative_marks" name="negative_marks" value="<%= question.negative_marks || 0.00 %>" min="0" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                <p class="mt-1 text-sm text-gray-500">Marks deducted for incorrect answer (default: 0.00)</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Update Question
                </button>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for dynamic form elements -->
<script>
// Image handling functions
function previewQuestionImage(input) {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'question');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeQuestionImage() {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');
    const imageIdInput = document.getElementById('question_image_id');

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('question_image');
    if (fileInput) fileInput.value = '';
}

function previewSolutionImage(input) {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'solution');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeSolutionImage() {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');
    const imageIdInput = document.getElementById('solution_image_id');

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('solution_image');
    if (fileInput) fileInput.value = '';
}

function previewOptionImage(input, index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'option', index);
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeOptionImage(index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);
    const imageIdInput = document.getElementById(`option_image_id_${index}`);

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById(`option_image_${index}`);
    if (fileInput) fileInput.value = '';
}

function toggleOptionImageUpload(index) {
    const container = document.getElementById(`option_image_container_${index}`);
    if (container) {
        container.classList.toggle('hidden');
    }
}

function uploadImage(file, type, index = null) {
    const formData = new FormData();
    formData.append('image', file);

    fetch('/admin/images/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (type === 'question') {
                document.getElementById('question_image_id').value = data.image.id;
            } else if (type === 'solution') {
                document.getElementById('solution_image_id').value = data.image.id;
            } else if (type === 'option' && index !== null) {
                document.getElementById(`option_image_id_${index}`).value = data.image.id;
            }
        } else {
            alert('Failed to upload image: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error uploading image:', error);
        alert('Error uploading image. Please try again.');
    });
}

// Function to handle pasted images
function handlePastedImage(e, type, index = null) {
    if (e.clipboardData && e.clipboardData.items) {
        const items = e.clipboardData.items;

        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                const file = items[i].getAsFile();

                if (type === 'question') {
                    // Display the pasted image in the question image preview
                    const preview = document.getElementById('question_image_preview');
                    const container = document.getElementById('question_image_preview_container');

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        container.style.display = 'block';

                        // Upload the image to the server
                        uploadImage(file, 'question');
                    };
                    reader.readAsDataURL(file);
                } else if (type === 'solution') {
                    // Display the pasted image in the solution image preview
                    const preview = document.getElementById('solution_image_preview');
                    const container = document.getElementById('solution_image_preview_container');

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        container.style.display = 'block';

                        // Upload the image to the server
                        uploadImage(file, 'solution');
                    };
                    reader.readAsDataURL(file);
                } else if (type === 'option' && index !== null) {
                    // Make sure the option image container is visible
                    const container = document.getElementById(`option_image_container_${index}`);
                    if (container) {
                        container.classList.remove('hidden');
                    }

                    // Display the pasted image in the option image preview
                    const preview = document.getElementById(`option_image_preview_${index}`);
                    const previewContainer = document.getElementById(`option_image_preview_container_${index}`);

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        previewContainer.style.display = 'block';

                        // Upload the image to the server
                        uploadImage(file, 'option', index);
                    };
                    reader.readAsDataURL(file);
                }

                break;
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize math preview
    initializeMathPreview();

    // Initialize exam and section handling
    initializeExamSectionHandling();

    // Initialize question type handling
    initializeQuestionTypeHandling();

    // Initialize form validation
    initializeFormValidation();

    // Setup paste event listeners for image upload
    document.addEventListener('paste', function(e) {
        // Determine which section is currently active/focused
        const activeElement = document.activeElement;

        if (activeElement && activeElement.closest('.question-drop-zone')) {
            handlePastedImage(e, 'question');
        } else if (activeElement && activeElement.closest('.solution-drop-zone')) {
            handlePastedImage(e, 'solution');
        } else {
            // Check if any option container is active
            const optionContainer = activeElement && activeElement.closest('.option-drop-zone');
            if (optionContainer) {
                const index = optionContainer.dataset.index;
                if (index) {
                    handlePastedImage(e, 'option', parseInt(index));
                }
            }
        }
    });

    // Initialize Chosen for categories
    if (typeof jQuery != 'undefined') {
        $('#category_ids').chosen({
            width: '100%',
            placeholder_text_multiple: 'Select categories'
        });
    }
});

function initializeMathPreview() {
    const questionInput = document.getElementById('question_text');
    const solutionInput = document.getElementById('solution_text');
    const previewDiv = document.getElementById('math_preview');

    if (questionInput && previewDiv) {
        updatePreview(); // Initial preview
        questionInput.addEventListener('input', updatePreview);
        if (solutionInput) {
            solutionInput.addEventListener('input', updatePreview);
        }
    }
}

function updatePreview() {
    const questionText = document.getElementById('question_text').value;
    const solutionText = document.getElementById('solution_text')?.value || '';
    const previewDiv = document.getElementById('math_preview');

    if (!previewDiv) return;

    let previewContent = '<h3>Question Preview:</h3>';
    // Convert line breaks to <br> tags for proper display
    const formattedQuestionText = questionText.replace(/\n/g, '<br>');
    previewContent += `<div class="math-content question-preview">${formattedQuestionText}</div>`;

    if (solutionText) {
        previewContent += '<h3 class="mt-4">Solution Preview:</h3>';
        // Convert line breaks to <br> tags for proper display
        const formattedSolutionText = solutionText.replace(/\n/g, '<br>');
        previewContent += `<div class="math-content solution-preview">${formattedSolutionText}</div>`;
    }

    previewDiv.innerHTML = previewContent;

    if (typeof renderMathInElement === 'function') {
        renderMathInElement(previewDiv, {
            delimiters: [
                {left: "\\[", right: "\\]", display: true},
                {left: "\\(", right: "\\)", display: false},
                {left: "$$", right: "$$", display: true},
                {left: "$", right: "$", display: false}
            ],
            throwOnError: false,
            output: 'html',
            strict: false
        });
    }
}

function initializeExamSectionHandling() {
    const examSelect = document.getElementById('exam_id');
    const sectionSelect = document.getElementById('section_id');

    if (examSelect && sectionSelect) {
        examSelect.addEventListener('change', function() {
            updateSections(this.value);
        });

        // Trigger section load if exam is pre-selected
        if (examSelect.value && examSelect.value !== 'null') {
            updateSections(examSelect.value);
        }
    }
}

async function updateSections(examId) {
    const sectionSelect = document.getElementById('section_id');
    if (!sectionSelect) return;

    // If exam is 'null' or empty, disable section selection
    if (!examId || examId === 'null') {
        sectionSelect.innerHTML = '<option value="">Select an Exam First</option>';
        sectionSelect.innerHTML += '<option value="null">None (No Section)</option>';
        sectionSelect.disabled = true;
        return;
    }

    // Exam is selected, fetch and populate sections
    sectionSelect.disabled = true;
    sectionSelect.innerHTML = '<option value="">Loading sections...</option>';

    try {
        const response = await fetch(`/admin/questions/get-sections/${examId}`);
        const sections = await response.json();

        sectionSelect.innerHTML = '<option value="">Select a Section</option>';
        sectionSelect.innerHTML += '<option value="null">None (No Section)</option>';

        sections.forEach(section => {
            const option = document.createElement('option');
            option.value = section.section_id;
            option.textContent = section.section_name;
            option.selected = section.section_id == '<%= question.section_id %>';
            sectionSelect.appendChild(option);
        });

        sectionSelect.disabled = false;
    } catch (error) {
        console.error('Error fetching sections:', error);
        sectionSelect.innerHTML = '<option value="">Error loading sections</option>';
        sectionSelect.innerHTML += '<option value="null">None (No Section)</option>';
    }
}

function initializeQuestionTypeHandling() {
    const questionTypeSelect = document.getElementById('question_type');
    const mcqOptions = document.getElementById('mcq_options');
    const trueFalseOptions = document.getElementById('true_false_options');

    if (questionTypeSelect) {
        questionTypeSelect.addEventListener('change', function() {
            updateQuestionTypeOptions(this.value);
        });

        // Set initial state
        updateQuestionTypeOptions(questionTypeSelect.value);
    }

    // Add option button for MCQ
    const addOptionButton = document.getElementById('add_option');
    if (addOptionButton) {
        addOptionButton.addEventListener('click', addNewOption);
    }
}

function updateQuestionTypeOptions(questionType) {
    const mcqOptions = document.getElementById('mcq_options');
    const trueFalseOptions = document.getElementById('true_false_options');
    const fillUpOptions = document.getElementById('fill_up_options');

    // Hide all options first
    if (mcqOptions) mcqOptions.classList.add('hidden');
    if (trueFalseOptions) trueFalseOptions.classList.add('hidden');
    if (fillUpOptions) fillUpOptions.classList.add('hidden');

    // Reset required attributes for all form elements
    resetRequiredAttributes();

    // Show the appropriate section based on question type and set required attributes
    if (questionType === 'multiple_choice' && mcqOptions) {
        mcqOptions.classList.remove('hidden');
        // Set required attributes for MCQ options
        setRequiredForMCQ(true);
    } else if (questionType === 'true_false' && trueFalseOptions) {
        trueFalseOptions.classList.remove('hidden');
        // Set required attributes for True/False options
        setRequiredForTrueFalse(true);
    } else if (questionType === 'fill_up' && fillUpOptions) {
        fillUpOptions.classList.remove('hidden');
        // Set required attributes for Fill-up options
        setRequiredForFillUp(true);
    }
}

function resetRequiredAttributes() {
    // Reset MCQ options
    const mcqInputs = document.querySelectorAll('#mcq_options input[data-required="true"]');
    mcqInputs.forEach(input => {
        input.removeAttribute('required');
    });

    // Reset True/False options
    const tfInputs = document.querySelectorAll('#true_false_options input[data-required="true"]');
    tfInputs.forEach(input => {
        input.removeAttribute('required');
    });

    // Reset Fill-up options
    const fillUpInput = document.querySelector('#fill_up_answer[data-required="true"]');
    if (fillUpInput) {
        fillUpInput.removeAttribute('required');
    }
}

function setRequiredForMCQ(isRequired) {
    const optionTexts = document.querySelectorAll('#mcq_options input[name="option_text[]"][data-required="true"]');
    optionTexts.forEach(input => {
        if (isRequired) {
            input.setAttribute('required', '');
        } else {
            input.removeAttribute('required');
        }
    });
}

function setRequiredForTrueFalse(isRequired) {
    const tfOptions = document.querySelectorAll('#true_false_options input[name="true_false_answer"][data-required="true"]');
    tfOptions.forEach(input => {
        if (isRequired) {
            input.setAttribute('required', '');
        } else {
            input.removeAttribute('required');
        }
    });
}

function setRequiredForFillUp(isRequired) {
    const fillUpInput = document.querySelector('#fill_up_answer[data-required="true"]');
    if (fillUpInput) {
        if (isRequired) {
            fillUpInput.setAttribute('required', '');
        } else {
            fillUpInput.removeAttribute('required');
        }
    }
}

function addNewOption() {
    const container = document.getElementById('optionsContainer');
    if (!container) return;

    const optionCount = container.querySelectorAll('.flex.items-center').length;

    const optionDiv = document.createElement('div');
    optionDiv.className = 'flex items-center space-x-2';

    optionDiv.innerHTML = `
        <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" name="correct_option[]" value="${optionCount}" id="correct_option_${optionCount}" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" data-required="true">
            <label for="correct_option_${optionCount}" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
        </div>
        <input type="text" name="option_text[]" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" data-required="true">
        <button type="button" class="option-image-btn text-blue-600 hover:text-blue-800 px-2" onclick="toggleOptionImageUpload(${optionCount})">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
        </button>
        <button type="button" class="delete-option text-red-600 hover:text-red-800 px-2" onclick="deleteOption(this)">×</button>
    `;

    // Set required attribute if question type is multiple choice
    if (document.getElementById('question_type').value === 'multiple_choice') {
        optionDiv.querySelectorAll('input[data-required="true"]').forEach(input => {
            input.setAttribute('required', '');
        });
    }

    container.appendChild(optionDiv);
}

function deleteOption(button) {
    const container = document.getElementById('optionsContainer');
    const optionDiv = button.parentElement;
    const index = Array.from(container.children).indexOf(optionDiv);

    // Also remove the option image container if it exists
    const imageContainer = document.getElementById(`option_image_container_${index}`);
    if (imageContainer) {
        imageContainer.remove();
    }

    optionDiv.remove();

    // Update indices for remaining options
    container.querySelectorAll('.flex.items-center').forEach((div, i) => {
        const checkbox = div.querySelector('input[type="checkbox"]');
        const checkboxLabel = div.querySelector('label');
        const text = div.querySelector('input[type="text"]');

        // Update checkbox ID and value
        checkbox.id = `correct_option_${i}`;
        checkbox.value = i;

        // Update label for attribute
        checkboxLabel.setAttribute('for', `correct_option_${i}`);

        // Update image button onclick attribute
        const imageBtn = div.querySelector('.option-image-btn');
        if (imageBtn) {
            imageBtn.setAttribute('onclick', `toggleOptionImageUpload(${i})`);
        }
    });
}

function initializeFormValidation() {
    const form = document.getElementById('questionEditForm');
    if (!form) {
        console.error('Question edit form not found');
        return;
    }

    console.log('Form validation initialized');

    form.addEventListener('submit', function(e) {
        console.log('Form submission started');
        const questionType = document.getElementById('question_type').value;
        console.log('Question type:', questionType);

        // Update required attributes based on question type before validation
        resetRequiredAttributes();
        if (questionType === 'multiple_choice') {
            setRequiredForMCQ(true);
        } else if (questionType === 'true_false') {
            setRequiredForTrueFalse(true);
        }

        // Validate MCQ options
        if (questionType === 'multiple_choice') {
            const options = Array.from(document.querySelectorAll('input[name="option_text[]"]'))
                .filter(input => input.value.trim());

            console.log('MCQ options count:', options.length);

            if (options.length < 2) {
                e.preventDefault();
                // Use confirmation dialog instead of alert
                if (window.confirmationDialog) {
                    window.confirmationDialog.show({
                        title: 'Validation Error',
                        message: 'Multiple choice questions must have at least 2 options.',
                        confirmText: 'OK',
                        cancelText: null,
                        type: 'info'
                    });
                } else {
                    // Fallback to alert if confirmation dialog is not available
                    alert('Multiple choice questions must have at least 2 options.');
                }
                return;
            }

            const hasCorrectOption = document.querySelector('input[name="correct_option[]"]:checked');
            console.log('Has correct option selected:', !!hasCorrectOption);

            if (!hasCorrectOption) {
                e.preventDefault();
                // Use confirmation dialog instead of alert
                if (window.confirmationDialog) {
                    window.confirmationDialog.show({
                        title: 'Validation Error',
                        message: 'Please select a correct option for the multiple choice question.',
                        confirmText: 'OK',
                        cancelText: null,
                        type: 'info'
                    });
                } else {
                    // Fallback to alert if confirmation dialog is not available
                    alert('Please select a correct option for the multiple choice question.');
                }
                return;
            }
        }

        // Validate True/False option
        if (questionType === 'true_false') {
            const hasTrueFalseAnswer = document.querySelector('input[name="true_false_answer"]:checked');
            console.log('Has true/false answer selected:', !!hasTrueFalseAnswer);

            if (!hasTrueFalseAnswer) {
                e.preventDefault();
                alert('Please select either True or False as the correct answer.');
                return;
            }
        }

        console.log('Form validation passed, submitting form...');

        // Add a hidden field to indicate form was submitted via JavaScript
        const jsSubmitField = document.createElement('input');
        jsSubmitField.type = 'hidden';
        jsSubmitField.name = 'js_submit';
        jsSubmitField.value = 'true';
        form.appendChild(jsSubmitField);

        // Log form data for debugging
        const formData = new FormData(form);
        console.log('Form data entries:');
        for (let [key, value] of formData.entries()) {
            if (key === 'question_text' || key === 'solution_text') {
                console.log(key + ':', value.substring(0, 50) + '...');
            } else {
                console.log(key + ':', value);
            }
        }

        // Debug image IDs specifically
        console.log('Image IDs before submission:');
        console.log('Question image ID:', document.getElementById('question_image_id').value);
        console.log('Solution image ID:', document.getElementById('solution_image_id').value);

        // Make sure image IDs are included in the form
        if (!formData.has('image_id') && document.getElementById('question_image_id').value) {
            // Create a hidden input field for the image ID
            const imageIdField = document.createElement('input');
            imageIdField.type = 'hidden';
            imageIdField.name = 'image_id';
            imageIdField.value = document.getElementById('question_image_id').value;
            form.appendChild(imageIdField);

            console.log('Added hidden image_id field with value:', imageIdField.value);
        }

        if (!formData.has('solution_image_id') && document.getElementById('solution_image_id').value) {
            // Create a hidden input field for the solution image ID
            const solutionImageIdField = document.createElement('input');
            solutionImageIdField.type = 'hidden';
            solutionImageIdField.name = 'solution_image_id';
            solutionImageIdField.value = document.getElementById('solution_image_id').value;
            form.appendChild(solutionImageIdField);

            console.log('Added hidden solution_image_id field with value:', solutionImageIdField.value);
        }
    });
}
// Image handling functions
function previewQuestionImage(input) {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'question');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeQuestionImage() {
    const preview = document.getElementById('question_image_preview');
    const container = document.getElementById('question_image_preview_container');
    const imageIdInput = document.getElementById('question_image_id');

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('question_image');
    fileInput.value = '';
}

function previewSolutionImage(input) {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'solution');
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeSolutionImage() {
    const preview = document.getElementById('solution_image_preview');
    const container = document.getElementById('solution_image_preview_container');
    const imageIdInput = document.getElementById('solution_image_id');

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById('solution_image');
    fileInput.value = '';
}

function previewOptionImage(input, index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';

            // Upload the image to the server
            uploadImage(input.files[0], 'option', index);
        };

        reader.readAsDataURL(input.files[0]);
    }
}

function removeOptionImage(index) {
    const preview = document.getElementById(`option_image_preview_${index}`);
    const container = document.getElementById(`option_image_preview_container_${index}`);
    const imageIdInput = document.getElementById(`option_image_id_${index}`);

    preview.src = '';
    container.style.display = 'none';
    imageIdInput.value = '';

    // Reset the file input
    const fileInput = document.getElementById(`option_image_${index}`);
    fileInput.value = '';
}

function toggleOptionImageUpload(index) {
    const container = document.getElementById(`option_image_container_${index}`);
    if (container) {
        container.classList.toggle('hidden');
    }
}

function uploadImage(file, type, index = null) {
    const formData = new FormData();
    formData.append('image', file);

    fetch('/admin/images/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (type === 'question') {
                document.getElementById('question_image_id').value = data.image.id;
            } else if (type === 'solution') {
                document.getElementById('solution_image_id').value = data.image.id;
            } else if (type === 'option' && index !== null) {
                document.getElementById(`option_image_id_${index}`).value = data.image.id;
            }
        } else {
            alert('Failed to upload image: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error uploading image:', error);
        alert('Error uploading image. Please try again.');
    });
}
</script>

<style>
    .preview-section {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        margin: 1rem 0;
        padding: 1rem;
    }

    .math-content {
        line-height: 1.6;
    }

    .math-content .katex {
        font-size: 1.1em;
    }

    .math-content .katex-display {
        margin: 0.5em 0;
        overflow-x: auto;
        overflow-y: hidden;
    }

    /* Ensure math doesn't break layout */
    #math_preview {
        max-width: 100%;
        overflow-x: auto;
    }

    /* Add some padding around inline math */
    .katex-html {
        padding: 0 2px;
    }
</style>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
