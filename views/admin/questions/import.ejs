<!-- Import Questions -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/admin/questions" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            Back to Question Bank
        </a>
    </div>

    <% /* Hide any direct references to newExamId that might be in the page */ %>
    <% if (typeof newExamId === 'undefined') { %>
        <% var newExamId = null; %>
    <% } %>

    <% if (error) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <%= error %>
        </div>
    <% } %>

    <% if (success) { %>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <%= success %>
        </div>
    <% } %>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="mb-4 bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 class="text-lg font-medium text-blue-800 mb-2">Need a sample file?</h3>
            <p class="text-sm text-blue-600 mb-3">Download a sample Excel file with the correct format for importing questions.</p>
            <button type="button" id="generateSampleBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Download Sample Excel File
            </button>
        </div>
        <form action="/admin/questions/preview-import" method="POST" enctype="multipart/form-data" id="importForm">
            <!-- Import Format Selection -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2">Import Format</label>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" name="import_format" value="csv" class="form-radio" checked>
                        <span class="ml-2">CSV</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="import_format" value="excel" class="form-radio">
                        <span class="ml-2">Excel (XLS/XLSX)</span>
                    </label>
                </div>
            </div>

            <!-- Import Options -->
            <div class="mb-6">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Import Method</label>
                    <div class="flex flex-col space-y-2">
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_method" value="existing" class="form-radio" checked>
                            <span class="ml-2">Import into existing exam/section</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_method" value="new" class="form-radio">
                            <span class="ml-2">Create new exam/sections from Excel sheets</span>
                        </label>
                    </div>
                </div>

                <!-- Existing Exam/Section Selection -->
                <div id="existingExamSection">
                    <div class="mb-4">
                        <label for="exam_id" class="block text-gray-700 text-sm font-bold mb-2">Exam</label>
                        <select id="exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">Select an Exam</option>
                            <% if (locals.exams) { %>
                                <% exams.forEach(exam => { %>
                                    <option value="<%= exam.exam_id %>"><%= exam.exam_name %></option>
                                <% }); %>
                            <% } %>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="section_id" class="block text-gray-700 text-sm font-bold mb-2">Section</label>
                        <select id="section_id" name="section_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" disabled>
                            <option value="">Select an Exam First</option>
                        </select>
                    </div>
                </div>

                <!-- New Exam Input -->
                <div id="newExamSection" class="mb-4 hidden">
                    <div class="mb-4">
                        <label for="new_exam_name" class="block text-gray-700 text-sm font-bold mb-2">New Exam Name</label>
                        <input type="text" id="new_exam_name" name="new_exam_name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Enter new exam name" readonly>
                        <p class="text-sm text-gray-600 mt-1">The first sheet should contain exam and section introductions with columns "title" and "introduction". The first row should have "test introduction" in the title column and the exam instructions in the introduction column. Subsequent rows should have section names in the title column and their introductions in the introduction column. Section names will be taken from the names of all other sheets. Each sheet (except the first) should contain questions in the format described below.</p>
                    </div>
                </div>
            </div>

            <!-- File Upload Section -->
            <div id="fileUploadSection" class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Upload File</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-blue-500 transition-colors duration-200">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-600">
                            <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload a file</span>
                                <input id="file" name="file" type="file" class="sr-only" accept=".csv,.xlsx,.xls">
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">
                            CSV, XLSX, or XLS up to 10MB
                        </p>
                    </div>
                </div>
                <div id="filePreview" class="mt-2 hidden">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900" id="fileName"></p>
                                <p class="text-xs text-gray-500" id="fileSize"></p>
                            </div>
                        </div>
                        <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>



            <!-- Format Examples -->
            <div class="mb-6">
                <h3 class="text-sm font-bold text-gray-700 mb-2">Import Format Examples:</h3>

                <!-- Excel Multi-Sheet Format -->
                <div class="bg-gray-50 p-4 rounded border border-gray-200 mb-4">
                    <h4 class="text-sm font-semibold mb-2">Excel Multi-Sheet Format (for creating new exams):</h4>
                    <ul class="list-disc pl-5 text-xs mb-4">
                        <li><strong>Sheet 1:</strong> Introduction sheet with columns "title" and "introduction"</li>
                        <li>First row should have "test introduction" in title column and exam instructions in introduction column</li>
                        <li>Subsequent rows should have section names in title column and their introductions in introduction column</li>
                        <li><strong>Sheet 2, 3, etc.:</strong> Question sheets - Each sheet name becomes a section name</li>
                        <li>Each question sheet should follow the column format below</li>
                    </ul>
                    <h4 class="text-sm font-semibold mb-2">First Sheet Format Example:</h4>
                    <div class="overflow-x-auto mb-4">
                    <table class="min-w-full text-xs">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="px-4 py-2 border">title</th>
                                <th class="px-4 py-2 border">introduction</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="px-4 py-2 border">test introduction</td>
                                <td class="px-4 py-2 border">This exam tests your knowledge of various subjects...</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border">Mathematics</td>
                                <td class="px-4 py-2 border">This section covers algebra, geometry, and calculus...</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border">Science</td>
                                <td class="px-4 py-2 border">This section covers physics, chemistry, and biology...</td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    <h4 class="text-sm font-semibold mb-2">Column Format for CSV/Excel:</h4>
                    <div class="overflow-x-auto">
                    <table class="min-w-full text-xs">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="px-4 py-2 border">Column</th>
                                <th class="px-4 py-2 border">Required</th>
                                <th class="px-4 py-2 border">Description</th>
                                <th class="px-4 py-2 border">Example</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="px-4 py-2 border font-medium">question_text</td>
                                <td class="px-4 py-2 border">Yes</td>
                                <td class="px-4 py-2 border">The question text</td>
                                <td class="px-4 py-2 border">What is the capital of France?</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border font-medium">question_type</td>
                                <td class="px-4 py-2 border">Yes</td>
                                <td class="px-4 py-2 border">Type: multiple_choice, true_false, or essay</td>
                                <td class="px-4 py-2 border">multiple_choice</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border font-medium">options</td>
                                <td class="px-4 py-2 border">For multiple_choice</td>
                                <td class="px-4 py-2 border">Comma-separated options</td>
                                <td class="px-4 py-2 border">London,Paris,Berlin,Madrid</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border font-medium">correct_answer</td>
                                <td class="px-4 py-2 border">Yes</td>
                                <td class="px-4 py-2 border">Option index (0-based) or true/false. For example, if "Paris" is the 2nd option (index 1), enter "1"</td>
                                <td class="px-4 py-2 border">1</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border font-medium">solution_text</td>
                                <td class="px-4 py-2 border">No</td>
                                <td class="px-4 py-2 border">Explanation for the correct answer</td>
                                <td class="px-4 py-2 border">Paris is the capital of France.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 border font-medium">marks</td>
                                <td class="px-4 py-2 border">No</td>
                                <td class="px-4 py-2 border">Points awarded for correct answer (default: 1)</td>
                                <td class="px-4 py-2 border">2</td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                </div>


            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" id="previewButton" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Preview Import
                </button>
                <button type="submit" id="directImportButton" class="hidden bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ml-2">
                    Import Without Preview
                </button>
            </div>
        </form>
    </div>
</div>

<!-- SheetJS library for Excel file generation -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

<!-- Sample Excel Generator -->
<script src="/js/generate-sample-excel.js"></script>

<!-- Initialize ToastNotifications if not already defined -->
<script>
    // Define a fallback ToastNotifications object if it's not defined
    if (typeof ToastNotifications === 'undefined') {
        console.warn('ToastNotifications not found, creating fallback');
        window.ToastNotifications = {
            init: function() { console.log('Fallback ToastNotifications initialized'); },
            show: function(message) { console.log('ToastNotification:', message); },
            success: function(message) { console.log('Success:', message); },
            error: function(message) { console.error('Error:', message); },
            info: function(message) { console.info('Info:', message); },
            warning: function(message) { console.warn('Warning:', message); }
        };
    }

    // Define newExamId as a global variable with a default value of null
    // This ensures it's always defined even if the server doesn't provide it
    var newExamId = null;
    <% if (typeof newExamId !== 'undefined' && newExamId !== null) { %>
        newExamId = <%= newExamId %>;
    <% } %>
</script>

<!-- JavaScript for dynamic form elements -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if a new exam was created and show a notification
        // We're using the global newExamId variable defined earlier
        console.log('New exam ID check:', newExamId);
        console.log('New exam ID type:', typeof newExamId);

        // Get the container for adding success messages
        const container = document.querySelector('.container');

        if (newExamId) {
            console.log('New exam created with ID:', newExamId);

            // Create and show a custom confirmation dialog
            const confirmationDialog = document.createElement('div');
            confirmationDialog.id = 'exam-created-dialog';
            confirmationDialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            confirmationDialog.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Success</h3>
                        <button id="close-dialog" class="text-gray-400 hover:text-gray-500">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="mb-5">
                        <p class="text-gray-700 mb-3">New exam created with ID: <span class="font-semibold">${newExamId}</span></p>
                        <p class="text-gray-700">You can now edit this exam to add more details or publish it.</p>
                    </div>
                    <div class="flex justify-end">
                        <a href="/tests/admin/${newExamId}/edit" class="mr-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Edit Exam
                        </a>
                        <button id="ok-button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            OK
                        </button>
                    </div>
                </div>
            `;

            // Add the dialog to the page
            document.body.appendChild(confirmationDialog);

            // Set up event listeners for the dialog buttons
            document.getElementById('close-dialog').addEventListener('click', function() {
                document.getElementById('exam-created-dialog').remove();
            });

            document.getElementById('ok-button').addEventListener('click', function() {
                document.getElementById('exam-created-dialog').remove();
            });

            // Also add a visible success message with a link to the new exam (for when dialog is closed)
            const successContainer = document.querySelector('.bg-green-100');
            if (successContainer) {
                console.log('Found success container, adding exam link');
                const examLink = document.createElement('p');
                examLink.innerHTML = `<a href="/tests/admin/${newExamId}/edit" class="text-blue-600 underline">Click here to edit the new exam</a>`;
                successContainer.appendChild(examLink);
            } else {
                console.log('Success container not found');

                // Create a success message if it doesn't exist
                const newSuccessContainer = document.createElement('div');
                newSuccessContainer.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4';
                newSuccessContainer.innerHTML = `
                    <p>New exam created successfully!</p>
                    <p><a href="/tests/admin/${newExamId}/edit" class="text-blue-600 underline">Click here to edit the new exam</a></p>
                `;

                // Insert at the top of the container
                if (container.firstChild) {
                    container.insertBefore(newSuccessContainer, container.firstChild);
                } else {
                    container.appendChild(newSuccessContainer);
                }
            }
        } else {
            console.log('No new exam ID received or it is null/undefined');
        }
        const examSelect = document.getElementById('exam_id');
        const sectionSelect = document.getElementById('section_id');
        const importFormatRadios = document.getElementsByName('import_format');
        const importMethodRadios = document.getElementsByName('import_method');
        const existingExamSection = document.getElementById('existingExamSection');
        const newExamSection = document.getElementById('newExamSection');
        const fileUploadSection = document.getElementById('fileUploadSection');

        // Handle import format changes
        importFormatRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Enable new exam option only for Excel imports
                importMethodRadios.forEach(r => {
                    if (r.value === 'new') r.disabled = (this.value !== 'excel');
                });

                // If new exam is selected but format is not Excel, switch to existing exam
                if (this.value !== 'excel' && document.querySelector('input[name="import_method"][value="new"]').checked) {
                    importMethodRadios[0].checked = true;
                    existingExamSection.classList.remove('hidden');
                    newExamSection.classList.add('hidden');
                }
            });
        });

        // Handle import method changes
        importMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'existing') {
                    existingExamSection.classList.remove('hidden');
                    newExamSection.classList.add('hidden');
                    examSelect.setAttribute('required', '');
                    sectionSelect.setAttribute('required', '');
                    document.getElementById('new_exam_name').removeAttribute('required');
                } else {
                    existingExamSection.classList.add('hidden');
                    newExamSection.classList.remove('hidden');
                    examSelect.removeAttribute('required');
                    sectionSelect.removeAttribute('required');
                    document.getElementById('new_exam_name').setAttribute('required', '');
                }
            });
        });

        // Initialize the form based on the selected import format
        const selectedFormat = document.querySelector('input[name="import_format"]:checked').value;
        importMethodRadios.forEach(r => {
            if (r.value === 'new') r.disabled = (selectedFormat !== 'excel');
        });

        // Handle exam selection changes
        examSelect.addEventListener('change', function() {
            const examId = this.value;

            if (examId) {
                sectionSelect.disabled = false;
                sectionSelect.innerHTML = '<option value="">Loading sections...</option>';

                fetch(`/admin/questions/get-sections/${examId}`)
                    .then(response => response.json())
                    .then(sections => {
                        sectionSelect.innerHTML = '<option value="">Select a Section</option>';
                        sections.forEach(section => {
                            const option = document.createElement('option');
                            option.value = section.section_id;
                            option.textContent = section.section_name;
                            sectionSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching sections:', error);
                        sectionSelect.innerHTML = '<option value="">Error loading sections</option>';
                    });
            } else {
                sectionSelect.disabled = true;
                sectionSelect.innerHTML = '<option value="">Select an Exam First</option>';
            }
        });

        const fileInput = document.getElementById('file');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const removeFile = document.getElementById('removeFile');
        // We don't have an element with ID 'import_format', we have radio buttons with name 'import_format'
        // So we'll use the radio buttons directly

        // Handle file selection
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Check file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB');
                    fileInput.value = '';
                    return;
                }

                // Format file size
                const size = file.size < 1024 * 1024
                    ? (file.size / 1024).toFixed(2) + ' KB'
                    : (file.size / (1024 * 1024)).toFixed(2) + ' MB';

                fileName.textContent = file.name;
                fileSize.textContent = size;
                filePreview.classList.remove('hidden');

                // If 'Create new exam' is selected, use the filename as the exam name
                const createNewExamSelected = document.querySelector('input[name="import_method"][value="new"]').checked;
                if (createNewExamSelected) {
                    // Get the filename without extension
                    const fileNameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
                    // Set the new exam name field value
                    document.getElementById('new_exam_name').value = fileNameWithoutExt;
                }
            }
        });

        // Handle file removal
        removeFile.addEventListener('click', function() {
            fileInput.value = '';
            filePreview.classList.add('hidden');
        });

        // Note: We already have event listeners for the import format radio buttons above
        // So we don't need to add another event listener here

        // Handle drag and drop
        const dropZone = fileInput.parentElement.parentElement;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        // Handle form submission
        const importForm = document.getElementById('importForm');
        const previewButton = document.getElementById('previewButton');
        const directImportButton = document.getElementById('directImportButton');

        // Handle preview button click
        previewButton.addEventListener('click', function(e) {
            e.preventDefault();
            importForm.action = '/admin/questions/preview-import';
            importForm.submit();
        });

        // Show direct import button only for Excel files with 'Create new exam' selected
        document.querySelectorAll('input[name="import_format"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const isExcel = this.value === 'excel';
                const isNewExam = document.querySelector('input[name="import_method"][value="new"]').checked;
                directImportButton.classList.toggle('hidden', !(isExcel && isNewExam));
            });
        });

        document.querySelectorAll('input[name="import_method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const isExcel = document.querySelector('input[name="import_format"][value="excel"]').checked;
                const isNewExam = this.value === 'new';
                directImportButton.classList.toggle('hidden', !(isExcel && isNewExam));
            });
        });

        // Handle direct import button click
        directImportButton.addEventListener('click', function(e) {
            e.preventDefault();
            importForm.action = '/admin/questions/import';

            // Check if we should use AJAX for the submission
            const useAjax = true; // Set to true to enable AJAX submission

            if (useAjax) {
                e.preventDefault();

                // Create FormData object from the form
                const formData = new FormData(importForm);

                // Show loading message
                ToastNotifications.info('Importing questions...');

                // Submit the form via AJAX
                fetch('/admin/questions/import', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        ToastNotifications.success(data.message);

                        // If a new exam was created, show the confirmation dialog
                        if (data.newExamId) {
                            // Create and show a custom confirmation dialog
                            const confirmationDialog = document.createElement('div');
                            confirmationDialog.id = 'exam-created-dialog';
                            confirmationDialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                            confirmationDialog.innerHTML = `
                                <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-lg font-medium text-gray-900">Success</h3>
                                        <button id="close-dialog" class="text-gray-400 hover:text-gray-500">
                                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="mb-5">
                                        <p class="text-gray-700 mb-3">New exam created with ID: <span class="font-semibold">${data.newExamId}</span></p>
                                        <p class="text-gray-700">You can now edit this exam to add more details or publish it.</p>
                                    </div>
                                    <div class="flex justify-end">
                                        <a href="/tests/admin/${data.newExamId}/edit" class="mr-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            Edit Exam
                                        </a>
                                        <button id="ok-button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            OK
                                        </button>
                                    </div>
                                </div>
                            `;

                            // Add the dialog to the page
                            document.body.appendChild(confirmationDialog);

                            // Set up event listeners for the dialog buttons
                            document.getElementById('close-dialog').addEventListener('click', function() {
                                document.getElementById('exam-created-dialog').remove();
                            });

                            document.getElementById('ok-button').addEventListener('click', function() {
                                document.getElementById('exam-created-dialog').remove();
                            });
                        }
                    } else {
                        // Show error message
                        ToastNotifications.error(data.message || 'Error importing questions');
                    }
                })
                .catch(error => {
                    console.error('Error importing questions:', error);
                    ToastNotifications.error('Error importing questions: ' + error.message);
                });
            } else {
                // Use traditional form submission
                importForm.submit();
            }
        });

        // Initialize the sample Excel generator button
        const generateSampleBtn = document.getElementById('generateSampleBtn');
        if (generateSampleBtn) {
            generateSampleBtn.addEventListener('click', function() {
                try {
                    const result = generateSampleExcel();
                    if (result) {
                        ToastNotifications.success('Sample Excel file generated successfully. Check your downloads folder.');
                    }
                } catch (error) {
                    console.error('Error generating sample Excel file:', error);
                    ToastNotifications.error('Error generating sample Excel file. Please try again.');
                }
            });
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropZone.classList.add('border-blue-500');
        }

        function unhighlight(e) {
            dropZone.classList.remove('border-blue-500');
        }

        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const file = dt.files[0];

            if (file) {
                fileInput.files = dt.files;
                const event = new Event('change');
                fileInput.dispatchEvent(event);
            }
        }
    });
</script>
