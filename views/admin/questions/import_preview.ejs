<!-- Import Preview -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/admin/questions/import" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            Back to Import
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Import Summary</h2>
        
        <!-- File Information -->
        <div class="mb-6">
            <div class="flex items-center mb-2">
                <svg class="h-6 w-6 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-lg font-medium">File Details</h3>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-600">File Name:</p>
                        <p class="font-medium"><%= previewData.fileName %></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">File Size:</p>
                        <p class="font-medium"><%= previewData.fileSize %></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Exam Information -->
        <div class="mb-6">
            <div class="flex items-center mb-2">
                <svg class="h-6 w-6 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 class="text-lg font-medium">Exam Details</h3>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-sm text-gray-600">Exam Name:</p>
                        <p class="font-medium"><%= previewData.examName %></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Total Sections:</p>
                        <p class="font-medium"><%= previewData.totalSections %></p>
                    </div>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-600">Total Questions:</p>
                    <p class="font-medium"><%= previewData.totalQuestions %></p>
                </div>
                <% if (previewData.instructions) { %>
                <div>
                    <p class="text-sm text-gray-600">Instructions:</p>
                    <p class="text-sm mt-1 bg-white p-2 rounded border border-gray-200 max-h-24 overflow-y-auto">
                        <%= previewData.instructions.length > 200 ? previewData.instructions.substring(0, 200) + '...' : previewData.instructions %>
                    </p>
                </div>
                <% } %>
            </div>
        </div>
        
        <!-- Sections Information -->
        <div class="mb-6">
            <div class="flex items-center mb-2">
                <svg class="h-6 w-6 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                <h3 class="text-lg font-medium">Sections</h3>
            </div>
            <div class="bg-gray-50 rounded-lg overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Introduction</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <% previewData.sections.forEach(section => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= section.name %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= section.questionCount %></td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate"><%= section.introduction || 'No introduction' %></td>
                        </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Confirmation Buttons -->
        <div class="flex justify-end space-x-4">
            <a href="/admin/questions/import" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                Cancel
            </a>
            <form action="/admin/questions/import" method="POST">
                <input type="hidden" name="confirm_import" value="true">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Confirm Import
                </button>
            </form>
        </div>
    </div>
</div>
