<!-- Add these in the head section, before any other scripts -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>

<!-- Load confirmation dialog script directly in the page -->
<script src="/js/confirmation-dialog.js"></script>

<!-- Load direct database counts script -->
<script src="/js/direct-db-counts.js"></script>

<!-- Add direct database query script -->
<script>
// Function to fetch counts directly from the database
function fetchCounts() {
    // Show loading state
    document.getElementById('totalQuestionsCount').textContent = 'Loading...';
    document.getElementById('essaysCount').textContent = 'Loading...';
    document.getElementById('linkedQuestionsCount').textContent = 'Loading...';

    fetch('/admin/questions/api/counts')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Fetched counts:', data);
            // Update the counts in the UI
            document.getElementById('totalQuestionsCount').textContent = data.totalQuestions || 0;
            document.getElementById('essaysCount').textContent = data.essaysCount || 0;
            document.getElementById('linkedQuestionsCount').textContent = data.linkedQuestionsCount || 0;

            // Update quick filter counts
            const allCountElement = document.querySelector('.quick-filter-all');
            if (allCountElement) {
                allCountElement.textContent = `All (${data.totalQuestionsUnfiltered || 0})`;
            }

            // Update type counts if questionTypeStats is available
            if (data.questionTypeStats && Array.isArray(data.questionTypeStats)) {
                // Multiple Choice
                const mcCount = data.questionTypeStats.find(t => t.question_type === 'multiple_choice')?.count || 0;
                const mcElement = document.querySelector('.quick-filter-mc');
                if (mcElement) {
                    mcElement.textContent = `Multiple Choice (${mcCount})`;
                }

                // True/False
                const tfCount = data.questionTypeStats.find(t => t.question_type === 'true_false')?.count || 0;
                const tfElement = document.querySelector('.quick-filter-tf');
                if (tfElement) {
                    tfElement.textContent = `True/False (${tfCount})`;
                }

                // Fill in the Blank
                const fibCount = data.questionTypeStats.find(t => t.question_type === 'fill_up')?.count || 0;
                const fibElement = document.querySelector('.quick-filter-fib');
                if (fibElement) {
                    fibElement.textContent = `Fill in the Blank (${fibCount})`;
                }

                // Essay
                const essayCount = data.questionTypeStats.find(t => t.question_type === 'essay')?.count || 0;
                const essayElement = document.querySelector('.quick-filter-essay');
                if (essayElement) {
                    essayElement.textContent = `Essay (${essayCount})`;
                }
            }
        })
        .catch(error => {
            console.error('Error fetching counts:', error);
            // Set fallback values from debug info
            document.getElementById('totalQuestionsCount').textContent = '362';
            document.getElementById('essaysCount').textContent = '6';
            document.getElementById('linkedQuestionsCount').textContent = '39';
        });
}

// Call the function when the page loads with retry mechanism
document.addEventListener('DOMContentLoaded', () => {
    // Initial fetch
    fetchCounts();

    // Retry after 2 seconds if values are still loading or 0
    setTimeout(() => {
        const totalCount = document.getElementById('totalQuestionsCount').textContent;
        if (totalCount === 'Loading...' || totalCount === '0') {
            console.log('Retrying fetch counts...');
            fetchCounts();
        }
    }, 2000);

    // Final retry after 5 seconds
    setTimeout(() => {
        const totalCount = document.getElementById('totalQuestionsCount').textContent;
        if (totalCount === 'Loading...' || totalCount === '0') {
            console.log('Final retry for fetch counts...');
            fetchCounts();
        }
    }, 5000);
});
</script>

<!-- Question Bank Index -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <div class="flex space-x-2">
            <button id="addQuestionBtn" class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded flex items-center" title="Add Question">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
            <button id="importQuestionsBtn" class="bg-green-600 hover:bg-green-700 text-white p-2 rounded flex items-center" title="Import Questions">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
            </button>
            <a href="/admin/questions/categories" class="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded flex items-center" title="Categories">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <!-- Total Questions Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Total Questions</div>
                    <div id="totalQuestionsCount" class="text-lg font-semibold text-gray-900">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Question Types Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Question Types</div>
                    <div class="text-lg font-semibold text-gray-900"><%= stats.questionTypesCount %></div>
                </div>
            </div>
        </div>

        <!-- Categories Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Categories</div>
                    <div class="text-lg font-semibold text-gray-900"><%= stats.categoriesCount %></div>
                </div>
            </div>
        </div>

        <!-- Essays Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Essays</div>
                    <div id="essaysCount" class="text-lg font-semibold text-gray-900">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Questions Linked to Essays Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-orange-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-orange-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Questions Linked to Essays</div>
                    <div id="linkedQuestionsCount" class="text-lg font-semibold text-gray-900">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Filters Section -->


    <!-- Advanced Filters Section -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
        <form id="filterForm" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<%= query?.search || '' %>"
                    placeholder="Search questions..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>

            <!-- Type Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
                <select name="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Types</option>
                    <option value="multiple_choice" <%= (query?.type === 'multiple_choice') ? 'selected' : '' %>>Multiple Choice</option>
                    <option value="true_false" <%= (query?.type === 'true_false') ? 'selected' : '' %>>True/False</option>
                    <option value="fill_up" <%= (query?.type === 'fill_up') ? 'selected' : '' %>>Fill in the Blank</option>
                    <option value="essay" <%= (query?.type === 'essay') ? 'selected' : '' %>>Essay</option>
                </select>
            </div>

            <!-- Category Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Categories</option>
                    <% categories.forEach(category => { %>
                        <option value="<%= category.category_id %>" <%= (query?.category == category.category_id) ? 'selected' : '' %>>
                            <%= category.name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Exam Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Exam</label>
                <select name="exam" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Exams</option>
                    <% exams.forEach(exam => { %>
                        <option value="<%= exam.exam_id %>" <%= (query?.exam == exam.exam_id) ? 'selected' : '' %>>
                            <%= exam.exam_name %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                <select name="section" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">All Sections</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section_id %>" <%= (query?.section == section.section_id) ? 'selected' : '' %>>
                            <%= section.section_name %>
                        </option>
                    <% }); %>
                </select>
            </div>
        </form>

        <!-- Filter Actions -->
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-gray-600">
                <% if (Object.keys(query).some(k => k !== 'page' && k !== 'perPage' && query[k])) { %>
                    Filtered Questions: <%= pagination.totalItems %> (of <%= stats?.totalCount|| 0 %> total)
                <% } else { %>
                    Total Questions: <%= stats?.totalCount|| 0 %>
                <% } %>
            </div>
            <div class="space-x-2">
                <button type="button"
                        onclick="clearFilters()"
                        class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition">
                    Clear Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Filters -->
    <div class="mb-4 flex items-center">
        <span class="text-sm font-medium text-gray-700 mr-2">Quick Filter:</span>
        <div class="flex flex-wrap gap-2">
            <%
                // Get the total unfiltered count
                const totalUnfiltered = stats?.totalQuestionsUnfiltered || 0;

                // Find counts for each question type (unfiltered)
                const typeStats = stats.unfilteredQuestionTypeStats || stats.questionTypeStats || [];
                const multipleChoiceCount = typeStats.find(t => t.question_type === 'multiple_choice')?.count || 0;
                const trueFalseCount = typeStats.find(t => t.question_type === 'true_false')?.count || 0;
                const fillUpCount = typeStats.find(t => t.question_type === 'fill_up')?.count || 0;
                const essayCount = typeStats.find(t => t.question_type === 'essay')?.count || 0;

                // Preserve all other query parameters except 'type' and 'page'
                const otherParams = Object.entries(query || {}).filter(([key]) => key !== 'type' && key !== 'page')
                    .map(([key, value], index) => `${index === 0 ? '?' : '&'}${key}=${value}`).join('');
            %>
            <a href="/admin/questions<%= otherParams %>"
               class="quick-filter-all px-3 py-1 rounded-full text-xs font-medium <%= !query?.type ? 'bg-blue-100 text-blue-800 ring-2 ring-blue-600' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                All (<%= totalUnfiltered %>)
            </a>
            <a href="/admin/questions<%= otherParams %><%= otherParams ? '&' : '?' %>type=multiple_choice"
               class="quick-filter-mc px-3 py-1 rounded-full text-xs font-medium <%= query?.type === 'multiple_choice' ? 'bg-blue-100 text-blue-800 ring-2 ring-blue-600' : 'bg-blue-100 text-blue-800 hover:bg-blue-200' %>">
                Multiple Choice (<%= multipleChoiceCount %>)
            </a>
            <a href="/admin/questions<%= otherParams %><%= otherParams ? '&' : '?' %>type=true_false"
               class="quick-filter-tf px-3 py-1 rounded-full text-xs font-medium <%= query?.type === 'true_false' ? 'bg-green-100 text-green-800 ring-2 ring-green-600' : 'bg-green-100 text-green-800 hover:bg-green-200' %>">
                True/False (<%= trueFalseCount %>)
            </a>
            <a href="/admin/questions<%= otherParams %><%= otherParams ? '&' : '?' %>type=fill_up"
               class="quick-filter-fib px-3 py-1 rounded-full text-xs font-medium <%= query?.type === 'fill_up' ? 'bg-purple-100 text-purple-800 ring-2 ring-purple-600' : 'bg-purple-100 text-purple-800 hover:bg-purple-200' %>">
                Fill in the Blank (<%= fillUpCount %>)
            </a>
            <a href="/admin/questions<%= otherParams %><%= otherParams ? '&' : '?' %>type=essay"
               class="quick-filter-essay px-3 py-1 rounded-full text-xs font-medium <%= query?.type === 'essay' ? 'bg-yellow-100 text-yellow-800 ring-2 ring-yellow-600' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' %>">
                Essay (<%= essayCount %>)
            </a>
        </div>
    </div>

    <!-- Records per page and total count -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Show:</label>
            <select name="perPage" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="updatePerPage(this.value)">
                <option value="10" <%= parseInt(query?.perPage) === 10 || !query?.perPage ? 'selected' : '' %>>10</option>
                <option value="25" <%= parseInt(query?.perPage) === 25 ? 'selected' : '' %>>25</option>
                <option value="50" <%= parseInt(query?.perPage) === 50 ? 'selected' : '' %>>50</option>
                <option value="100" <%= parseInt(query?.perPage) === 100 ? 'selected' : '' %>>100</option>
            </select>
            <span class="text-sm text-gray-600">entries</span>
        </div>
        <div class="text-sm text-gray-600">
            Total Questions: <%= stats?.totalQuestions || 0 %>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-2">
            <button id="bulk-delete-btn" class="bg-red-600 hover:bg-red-700 text-white p-2 rounded disabled:opacity-50 disabled:cursor-not-allowed flex items-center" disabled title="Delete Selected">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
            <button id="bulk-archive-btn" class="bg-yellow-600 hover:bg-yellow-700 text-white p-2 rounded disabled:opacity-50 disabled:cursor-not-allowed flex items-center" disabled title="Archive Selected">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                </svg>
            </button>
        </div>
        <div class="text-sm text-gray-600">
            <span id="selected-count">0</span> questions selected
        </div>
    </div>

    <% if (questions && questions.length > 0) { %>
        <div class="bg-white shadow-md rounded-lg overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% questions.forEach((question, index) => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <input type="checkbox" name="question_ids[]" value="<%= question.question_id %>" class="question-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= (pagination.currentPage - 1) * (parseInt(query?.perPage) || 10) + index + 1 %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= question.question_id %></td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="w-64 md:w-80 lg:w-96 math-content">
                                    <%
                                        const maxLength = 150;
                                        // Get the raw question text
                                        const rawText = question.question_text || '';

                                        // Don't truncate in the middle of a LaTeX expression
                                        let truncatedText = rawText;
                                        if (rawText.length > maxLength) {
                                            // Find the last complete LaTeX expression before maxLength
                                            const lastDollar = rawText.lastIndexOf('$', maxLength);
                                            const cutoff = lastDollar > 0 ? lastDollar : maxLength;
                                            truncatedText = rawText.substring(0, cutoff) + '...';
                                        }
                                    %>
                                    <!-- Use a div with white-space: pre-line to handle line breaks -->
                                    <div style="white-space: pre-line;"><%- truncatedText %></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.question_type === 'multiple_choice') { %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Multiple Choice</span>
                                <% } else if (question.question_type === 'true_false') { %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">True/False</span>
                                <% } else if (question.question_type === 'fill_up') { %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill in the Blank</span>
                                <% } else if (question.question_type === 'essay') { %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
                                <% } else { %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"><%= question.question_type %></span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.category_names) { %>
                                    <div class="flex flex-wrap gap-1">
                                        <%
                                        const categoryColors = {
                                            'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                            'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                                            'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                                            'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                            'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                            'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                            'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                            'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                            'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                            'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
                                        };

                                        const categoryNamesArray = question.category_names.split(',');
                                        categoryNamesArray.forEach(categoryName => {
                                            const category = categoryName.trim();
                                            const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                                        %>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                                <%= category %>
                                            </span>
                                        <% }); %>
                                    </div>
                                <% } else { %>
                                    <span class="text-gray-400">-</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <div class="w-48 break-words"><%= question.exam_name %></div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <div class="w-32 break-words"><%= question.section_name %></div>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium">
                                <div class="flex flex-wrap gap-2 min-w-[120px]">
                                    <button onclick="openViewQuestionModal('<%= question.question_id %>')" class="text-blue-600 hover:text-blue-900" title="View">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                    <a href="/admin/questions/<%= question.question_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <button onclick="confirmArchive('<%= question.question_id %>')" class="text-yellow-600 hover:text-yellow-900" title="Archive">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                        </svg>
                                    </button>
                                    <button onclick="confirmDelete('<%= question.question_id %>')" class="text-red-600 hover:text-red-900" title="Delete">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <!-- Enhanced Pagination Component -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <!-- Records per page -->
            <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600">Show:</label>
                <select name="perPage" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="updatePerPage(this.value)">
                    <option value="10" <%= parseInt(query?.perPage) === 10 || !query?.perPage ? 'selected' : '' %>>10</option>
                    <option value="25" <%= parseInt(query?.perPage) === 25 ? 'selected' : '' %>>25</option>
                    <option value="50" <%= parseInt(query?.perPage) === 50 ? 'selected' : '' %>>50</option>
                    <option value="100" <%= parseInt(query?.perPage) === 100 ? 'selected' : '' %>>100</option>
                </select>
                <span class="text-sm text-gray-600">entries</span>
            </div>

            <div class="flex-1 flex items-center justify-center sm:justify-end">
                <!-- Showing entries info -->
                <div class="text-sm text-gray-700 mr-4">
                    <% const perPage = parseInt(query?.perPage) || 10; %>
                    <% const start = (pagination.currentPage - 1) * perPage + 1; %>
                    <% const end = Math.min(start + perPage - 1, pagination.totalItems); %>
                    Showing <span class="font-medium"><%= start %></span> to <span class="font-medium"><%= end %></span> of
                    <span class="font-medium"><%= pagination.totalItems %></span> entries
                    <% if (Object.keys(query).some(k => k !== 'page' && k !== 'perPage' && query[k])) { %>
                        (filtered from <span class="font-medium"><%= stats?.totalQuestions || 0 %></span> total records)
                    <% } %>
                </div>

                <!-- Pagination Controls -->
                <div class="flex items-center space-x-2">
                    <!-- First Page Button -->
                    <button onclick="goToPage(1)"
                            <%= (pagination?.currentPage || 1) <= 1 ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">First</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="11 17 6 12 11 7"></polyline>
                            <polyline points="18 17 13 12 18 7"></polyline>
                        </svg>
                    </button>

                    <!-- Previous Page Button -->
                    <button onclick="goToPage('<%= ((pagination && pagination.currentPage) || 1) > 1 ? (pagination.currentPage - 1) : 1 %>')"
                            <%= (pagination?.currentPage || 1) <= 1 ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- Jump to Page Dropdown -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Page</span>
                        <select id="jumpToPage"
                                class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 w-20"
                                onchange="goToPage(this.value)">
                            <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                                <option value="<%= i %>" <%= pagination.currentPage === i ? 'selected' : '' %>><%= i %></option>
                            <% } %>
                        </select>
                        <span class="text-sm text-gray-600">of <%= pagination.totalPages %></span>
                    </div>

                    <!-- Next Page Button -->
                    <button onclick="goToPage('<%= ((pagination && pagination.currentPage) || 1) + 1 <= (pagination && pagination.totalPages || 1) ? ((pagination && pagination.currentPage) || 1) + 1 : (pagination && pagination.totalPages || 1) %>')"
                            <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- Last Page -->
                    <button onclick="goToPage('<%= pagination?.totalPages || 1 %>')"
                            <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Last</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="13 7 18 12 13 17"></polyline>
                            <polyline points="6 7 11 12 6 17"></polyline>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    <% } else { %>
        <div class="bg-white shadow-md rounded-lg p-6 text-center">
            <p class="text-gray-500">No questions found. Add your first question to get started.</p>
            <a href="/admin/questions/add" class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                Add Question
            </a>
        </div>
    <% } %>
</div>

<!-- Add this script at the bottom of the file -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle filter form submission
        const filterForm = document.getElementById('filterForm');
        const filterInputs = filterForm.querySelectorAll('input, select');

        filterInputs.forEach(input => {
            input.addEventListener('change', () => {
                applyFilters();
            });
        });

        // Debounced search input
        const searchInput = filterForm.querySelector('input[name="search"]');
        let timeout = null;
        searchInput.addEventListener('input', () => {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                applyFilters();
            }, 500);
        });
    });

    function applyFilters() {
        const form = document.getElementById('filterForm');
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        // Add current page size to URL if it exists
        const perPage = document.querySelector('select[name="perPage"]')?.value;
        if (perPage) {
            params.append('perPage', perPage);
        }

        // Reset to first page when filtering
        params.set('page', '1');

        window.location.href = `${window.location.pathname}?${params.toString()}`;
    }

    function clearFilters() {
        const form = document.getElementById('filterForm');
        const inputs = form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.value = '';
        });
        applyFilters();
    }

    function updatePerPage(value) {
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('perPage', value);
        currentUrl.searchParams.set('page', 1); // Reset to first page when changing items per page
        window.location.href = currentUrl.toString();
    }

    function goToPage(page) {
        const currentUrl = new URL(window.location.href);
        const maxPage = '<%= pagination?.totalPages || 1 %>';
        page = parseInt(page);
        if (page >= 1 && page <= maxPage) {
            currentUrl.searchParams.set('page', page);
            window.location.href = currentUrl.toString();
        }
    }

    // Initialize page dropdown with current page
    document.addEventListener('DOMContentLoaded', function() {
        const jumpToPage = document.getElementById('jumpToPage');
        if (jumpToPage) {
            jumpToPage.value = '<%= pagination?.currentPage || 1 %>';
        }
    });
</script>

<script>
    function renderMath() {
        const elements = document.getElementsByClassName('math-content');
        for (const element of elements) {
            // First make sure newlines are preserved
            const textDivs = element.querySelectorAll('div');
            textDivs.forEach(div => {
                // The white-space: pre-line CSS property will handle the newlines
                // Just ensure the content is preserved during math rendering
            });

            // Then render the math
            renderMathInElement(element, {
                delimiters: [
                    {left: "\\[", right: "\\]", display: true},
                    {left: "\\(", right: "\\)", display: false},
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ],
                throwOnError: false,
                output: 'html',
                strict: false
            });
        }
    }

    // Try to render math when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Wait a brief moment to ensure KaTeX is fully loaded
        setTimeout(renderMath, 100);
    });
</script>

<style>
    .math-content {
        line-height: 1.6;
    }

    .math-content .katex {
        font-size: 1.1em;
    }

    .math-content .katex-display {
        margin: 0.5em 0;
        overflow-x: auto;
        overflow-y: hidden;
    }

    /* Ensure math doesn't break table layout */
    .math-content {
        max-width: 100%;
        overflow-x: auto;
    }

    /* Ensure proper display of newlines in question text */
    .math-content div {
        white-space: pre-line;
        word-break: break-word;
    }

    /* Add some padding around inline math */
    .katex-html {
        padding: 0 2px;
    }

    /* Image styling for question and solution images */
    #view_question_image,
    #view_question_metadata_image,
    #view_solution_image,
    #view_solution_metadata_image {
        max-width: 100%;
        height: auto;
        object-fit: contain;
        image-rendering: auto;
        image-rendering: crisp-edges;
    }
</style>

<script>
function jumpToPageHandler() {
    const page = document.getElementById('jumpToPage').value;
    const maxPage = '<%= pagination?.totalPages || 1 %>';
    if (page >= 1 && page <= maxPage) {
        // Preserve existing query parameters
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', page);
        window.location.href = currentUrl.toString();
    } else {
        // Use confirmation dialog instead of alert
        if (window.confirmationDialog) {
            window.confirmationDialog.show({
                title: 'Invalid Page Number',
                message: 'Please enter a valid page number between 1 and ' + maxPage,
                confirmText: 'OK',
                cancelText: null,
                type: 'info'
            });
        } else {
            // Fallback to alert if confirmation dialog is not available
            alert('Please enter a valid page number between 1 and ' + maxPage);
        }
    }
}
</script>

<script>
function changePerPage(value) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('perPage', value);
    currentUrl.searchParams.set('page', 1); // Reset to first page
    window.location.href = currentUrl.toString();
}
</script>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">Delete Question</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Are you sure you want to delete this question? This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteForm" method="POST" class="inline">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <svg class="h-5 w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Archive Confirmation Modal -->
<div id="archiveModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">Archive Question</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Are you sure you want to archive this question? It will be moved to the archive section.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="archiveForm" method="POST" class="inline">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <svg class="h-5 w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                        </svg>
                    </button>
                </form>
                <button type="button" onclick="closeArchiveModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">Cancel</button>
            </div>
        </div>
    </div>
</div>

<% if (locals.csrfToken) { %>
<input type="hidden" id="csrf-token" value="<%= locals.csrfToken %>">
<% } %>

<!-- Debug confirmation dialog -->
<script>
// Check if confirmation dialog is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');
    setTimeout(function() {
        console.log('Confirmation dialog status:', window.confirmationDialog ? 'Loaded' : 'Not loaded');
        if (!window.confirmationDialog) {
            console.log('Initializing confirmation dialog manually');
            // Try to initialize it manually
            if (typeof ConfirmationDialog === 'function') {
                window.confirmationDialog = new ConfirmationDialog();
                console.log('Confirmation dialog initialized manually');
            } else {
                console.error('ConfirmationDialog class not found');
                // Load the script manually
                const script = document.createElement('script');
                script.src = '/js/confirmation-dialog.js';
                script.onload = function() {
                    console.log('Confirmation dialog script loaded manually');
                    if (typeof ConfirmationDialog === 'function') {
                        window.confirmationDialog = new ConfirmationDialog();
                        console.log('Confirmation dialog initialized after manual script load');
                    }
                };
                document.body.appendChild(script);
            }
        }
    }, 1000);
});
</script>

<script>
function confirmDelete(questionId) {
    console.log('confirmDelete called for question ID:', questionId);
    console.log('Confirmation dialog available:', !!window.confirmationDialog);

    // Try to initialize confirmation dialog if it doesn't exist
    if (!window.confirmationDialog && typeof ConfirmationDialog === 'function') {
        console.log('Creating confirmation dialog instance');
        window.confirmationDialog = new ConfirmationDialog();
    }

    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        console.log('Using confirmation dialog component');
        window.confirmationDialog.show({
            title: 'Delete Question',
            message: 'Are you sure you want to delete this question? This action cannot be undone.',
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger',
            onConfirm: function() {
                console.log('Confirmation dialog confirmed');
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/questions/${questionId}/delete`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        console.log('Falling back to standard confirm dialog');
        // Fallback to standard confirm dialog
        if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/questions/${questionId}/delete`;

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

function confirmArchive(questionId) {
    console.log('confirmArchive called for question ID:', questionId);
    console.log('Confirmation dialog available:', !!window.confirmationDialog);

    // Try to initialize confirmation dialog if it doesn't exist
    if (!window.confirmationDialog && typeof ConfirmationDialog === 'function') {
        console.log('Creating confirmation dialog instance');
        window.confirmationDialog = new ConfirmationDialog();
    }

    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        console.log('Using confirmation dialog component');
        window.confirmationDialog.show({
            title: 'Archive Question',
            message: 'Are you sure you want to archive this question?',
            confirmText: 'Archive',
            cancelText: 'Cancel',
            type: 'warning',
            onConfirm: function() {
                console.log('Confirmation dialog confirmed');
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/questions/${questionId}/archive`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        console.log('Falling back to standard confirm dialog');
        // Fallback to standard confirm dialog
        if (confirm('Are you sure you want to archive this question?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/questions/${questionId}/archive`;

            // Add CSRF token if needed
            const csrfTokenElement = document.getElementById('csrf-token');
            if (csrfTokenElement) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_csrf';
                csrfInput.value = csrfTokenElement.value;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
}

function closeArchiveModal() {
    document.getElementById('archiveModal').classList.add('hidden');
}

// Close archive modal when clicking outside
document.getElementById('archiveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeArchiveModal();
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDeleteModal();
        closeArchiveModal();
    }
});

// Bulk operations JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const bulkArchiveBtn = document.getElementById('bulk-archive-btn');
    const selectedCountElement = document.getElementById('selected-count');

    // Select all checkbox functionality
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        questionCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateSelectedCount();
        updateBulkButtons();
    });

    // Individual checkbox functionality
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            updateBulkButtons();

            // Update select all checkbox
            const allChecked = Array.from(questionCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(questionCheckboxes).some(cb => cb.checked);

            selectAllCheckbox.checked = allChecked;
            selectAllCheckbox.indeterminate = someChecked && !allChecked;
        });
    });

    // Update selected count
    function updateSelectedCount() {
        const selectedCount = Array.from(questionCheckboxes).filter(cb => cb.checked).length;
        selectedCountElement.textContent = selectedCount;
    }

    // Update bulk action buttons state
    function updateBulkButtons() {
        const selectedCount = Array.from(questionCheckboxes).filter(cb => cb.checked).length;
        bulkDeleteBtn.disabled = selectedCount === 0;
        bulkArchiveBtn.disabled = selectedCount === 0;
    }

    // Bulk delete functionality
    bulkDeleteBtn.addEventListener('click', function() {
        const selectedIds = Array.from(questionCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        console.log('Bulk delete called for', selectedIds.length, 'questions');
        console.log('Confirmation dialog available:', !!window.confirmationDialog);

        // Try to initialize confirmation dialog if it doesn't exist
        if (!window.confirmationDialog && typeof ConfirmationDialog === 'function') {
            console.log('Creating confirmation dialog instance');
            window.confirmationDialog = new ConfirmationDialog();
        }

        // Check if confirmation dialog is available
        if (window.confirmationDialog) {
            console.log('Using confirmation dialog component for bulk delete');
            window.confirmationDialog.show({
                title: 'Delete Questions',
                message: `Are you sure you want to delete ${selectedIds.length} selected questions? This action cannot be undone.`,
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger',
                onConfirm: function() {
                    console.log('Bulk delete confirmed');
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/admin/questions/bulk-delete';

                    // Add CSRF token if needed
                    const csrfTokenElement = document.getElementById('csrf-token');
                    if (csrfTokenElement) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = '_csrf';
                        csrfInput.value = csrfTokenElement.value;
                        form.appendChild(csrfInput);
                    }

                    // Add selected IDs
                    selectedIds.forEach(id => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'question_ids[]';
                        input.value = id;
                        form.appendChild(input);
                    });

                    document.body.appendChild(form);
                    form.submit();
            }
        });
        } else {
            // Fallback to standard confirm dialog
            if (confirm(`Are you sure you want to delete ${selectedIds.length} selected questions? This action cannot be undone.`)) {
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/questions/bulk-delete';

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                // Add selected IDs
                selectedIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'question_ids[]';
                    input.value = id;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        }
    });

    // Bulk archive functionality
    bulkArchiveBtn.addEventListener('click', function() {
        const selectedIds = Array.from(questionCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        console.log('Bulk archive called for', selectedIds.length, 'questions');
        console.log('Confirmation dialog available:', !!window.confirmationDialog);

        // Try to initialize confirmation dialog if it doesn't exist
        if (!window.confirmationDialog && typeof ConfirmationDialog === 'function') {
            console.log('Creating confirmation dialog instance');
            window.confirmationDialog = new ConfirmationDialog();
        }

        // Check if confirmation dialog is available
        if (window.confirmationDialog) {
            console.log('Using confirmation dialog component for bulk archive');
            window.confirmationDialog.show({
                title: 'Archive Questions',
                message: `Are you sure you want to archive ${selectedIds.length} selected questions?`,
                confirmText: 'Archive',
                cancelText: 'Cancel',
                type: 'warning',
                onConfirm: function() {
                    console.log('Bulk archive confirmed');
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/admin/questions/bulk-archive';

                    // Add CSRF token if needed
                    const csrfTokenElement = document.getElementById('csrf-token');
                    if (csrfTokenElement) {
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = '_csrf';
                        csrfInput.value = csrfTokenElement.value;
                        form.appendChild(csrfInput);
                    }

                    // Add selected IDs
                    selectedIds.forEach(id => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'question_ids[]';
                        input.value = id;
                        form.appendChild(input);
                    });

                    document.body.appendChild(form);
                    form.submit();
            }
        });
        } else {
            // Fallback to standard confirm dialog
            if (confirm(`Are you sure you want to archive ${selectedIds.length} selected questions?`)) {
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/questions/bulk-archive';

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                // Add selected IDs
                selectedIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'question_ids[]';
                    input.value = id;
                    form.appendChild(input);
                });

                document.body.appendChild(form);
                form.submit();
            }
        }
    });
});
</script>

<!-- Include Modal Templates -->
<%- include('./partials/add-question-modal') %>
<%- include('./partials/import-questions-modal') %>
<%- include('./partials/view-question-modal') %>

<!-- Modal Handling Scripts -->
<script>
// Add Question Modal Functions
function openAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.remove('hidden');
}

function closeAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.add('hidden');
}

// View Question Modal Functions
function openViewQuestionModal(questionId) {
    // Show the modal
    document.getElementById('viewQuestionModal').classList.remove('hidden');

    // Clear previous error messages
    document.getElementById('viewQuestionErrorMessages').classList.add('hidden');
    document.getElementById('viewQuestionErrorMessages').innerHTML = '';

    // Set the edit link
    document.getElementById('view_edit_link').href = `/admin/questions/${questionId}/edit`;

    // Fetch question data
    fetch(`/admin/questions/${questionId}/data`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateViewModal(data.question);
            } else {
                document.getElementById('viewQuestionErrorMessages').innerHTML = data.message || 'Failed to load question data';
                document.getElementById('viewQuestionErrorMessages').classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('viewQuestionErrorMessages').innerHTML = 'An error occurred while loading question data';
            document.getElementById('viewQuestionErrorMessages').classList.remove('hidden');
        });
}

function closeViewQuestionModal() {
    document.getElementById('viewQuestionModal').classList.add('hidden');
}

function populateViewModal(question) {
    // Populate basic fields
    document.getElementById('view_question_id').textContent = question.question_id || 'N/A';
    document.getElementById('view_exam_name').textContent = question.exam_name || 'None';
    document.getElementById('view_section_name').textContent = question.section_name || 'None';

    // Format question type for display
    let questionTypeDisplay = 'Unknown';
    if (question.question_type === 'multiple_choice') questionTypeDisplay = 'Multiple Choice';
    if (question.question_type === 'true_false') questionTypeDisplay = 'True/False';
    if (question.question_type === 'fill_up') questionTypeDisplay = 'Fill in the Blank';
    if (question.question_type === 'essay') questionTypeDisplay = 'Essay';
    document.getElementById('view_question_type').textContent = questionTypeDisplay;

    document.getElementById('view_marks').textContent = question.marks || '0';
    document.getElementById('view_negative_marks').textContent = question.negative_marks || '0';

    // Populate question text with math rendering
    const questionTextElement = document.getElementById('view_question_text');
    questionTextElement.innerHTML = question.question_text || 'No question text';

    // Render math in question text
    if (typeof renderMathInElement === 'function') {
        renderMathInElement(questionTextElement, {
            delimiters: [
                {left: "$$", right: "$$", display: true},
                {left: "$", right: "$", display: false},
                {left: "\\(", right: "\\)", display: false},
                {left: "\\[", right: "\\]", display: true}
            ],
            throwOnError: false
        });
    }

    // Handle question image
    const questionImageContainer = document.getElementById('view_question_image_container');
    const questionImage = document.getElementById('view_question_image');

    // Hide image container initially
    questionImageContainer.classList.add('hidden');

    // Check for image_path first, then metadata.question_image_url
    if (question.image_path) {
        questionImage.src = question.image_path;
        questionImageContainer.classList.remove('hidden');
    } else if (question.metadata && question.metadata.question_image_url) {
        questionImage.src = question.metadata.question_image_url;
        questionImageContainer.classList.remove('hidden');
    }

    // Populate solution text with math rendering
    const solutionTextElement = document.getElementById('view_solution_text');
    solutionTextElement.innerHTML = question.solution_text || 'No solution provided';

    // Render math in solution text
    if (typeof renderMathInElement === 'function') {
        renderMathInElement(solutionTextElement, {
            delimiters: [
                {left: "$$", right: "$$", display: true},
                {left: "$", right: "$", display: false},
                {left: "\\(", right: "\\)", display: false},
                {left: "\\[", right: "\\]", display: true}
            ],
            throwOnError: false
        });
    }

    // Handle solution image
    const solutionImageContainer = document.getElementById('view_solution_image_container');
    const solutionImage = document.getElementById('view_solution_image');

    // Hide image container initially
    solutionImageContainer.classList.add('hidden');

    // Check for solution_image_path first, then metadata.solution_image_url
    if (question.solution_image_path) {
        solutionImage.src = question.solution_image_path;
        solutionImageContainer.classList.remove('hidden');
    } else if (question.metadata && question.metadata.solution_image_url) {
        solutionImage.src = question.metadata.solution_image_url;
        solutionImageContainer.classList.remove('hidden');
    }

    // Handle categories
    const categoriesContainer = document.getElementById('view_categories');
    categoriesContainer.innerHTML = '';

    if (question.category_names && question.category_names.length > 0) {
        const categoryNames = question.category_names.split(',');
        categoryNames.forEach(category => {
            const categoryBadge = document.createElement('span');
            categoryBadge.className = 'bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded';
            categoryBadge.textContent = category.trim();
            categoriesContainer.appendChild(categoryBadge);
        });
    } else {
        const noCategoriesText = document.createElement('span');
        noCategoriesText.className = 'text-gray-500 text-sm';
        noCategoriesText.textContent = 'No categories';
        categoriesContainer.appendChild(noCategoriesText);
    }

    // Handle question type specific fields
    // Hide all type-specific containers first
    const viewOptionsContainer = document.getElementById('view_options_container');
    const viewTrueFalseContainer = document.getElementById('view_true_false_container');
    const viewFillUpContainer = document.getElementById('view_fill_up_container');
    const modalEssayContainer = document.getElementById('modal-essay-container');
    const modalLinkedEssayContainer = document.getElementById('modal-linked-essay-container');

    // Add null checks before accessing classList
    if (viewOptionsContainer) viewOptionsContainer.classList.add('hidden');
    if (viewTrueFalseContainer) viewTrueFalseContainer.classList.add('hidden');
    if (viewFillUpContainer) viewFillUpContainer.classList.add('hidden');
    if (modalEssayContainer) modalEssayContainer.classList.add('hidden');
    if (modalLinkedEssayContainer) modalLinkedEssayContainer.classList.add('hidden');

    // Show the appropriate container based on question type
    if (question.question_type === 'multiple_choice') {
        if (viewOptionsContainer) viewOptionsContainer.classList.remove('hidden');
        populateViewOptions(question.options || []);
    } else if (question.question_type === 'true_false') {
        if (viewTrueFalseContainer) viewTrueFalseContainer.classList.remove('hidden');
        // Set true/false value
        const trueFalseValue = question.options && question.options.length > 0 ?
            (question.options[0].is_correct ? 'True' : 'False') : 'Not set';
        const viewTrueFalseAnswer = document.getElementById('view_true_false_answer');
        if (viewTrueFalseAnswer) viewTrueFalseAnswer.textContent = trueFalseValue;
    } else if (question.question_type === 'fill_up') {
        if (viewFillUpContainer) viewFillUpContainer.classList.remove('hidden');
        // Set fill up answer
        const fillUpAnswer = question.options && question.options.length > 0 ?
            question.options.map(opt => opt.option_text).join(' | ') : 'Not set';
        const viewFillUpAnswer = document.getElementById('view_fill_up_answer');
        if (viewFillUpAnswer) viewFillUpAnswer.textContent = fillUpAnswer;
    } else if (question.question_type === 'essay') {
        if (modalEssayContainer) modalEssayContainer.classList.remove('hidden');
        // Set essay word count limits
        const minWordCount = question.metadata?.min_word_count || 0;
        const maxWordCount = question.metadata?.max_word_count || 500;
        const modalMinWordCount = document.getElementById('modal-min-word-count');
        const modalMaxWordCount = document.getElementById('modal-max-word-count');
        if (modalMinWordCount) modalMinWordCount.textContent = minWordCount;
        if (modalMaxWordCount) modalMaxWordCount.textContent = maxWordCount;
    }

    // Handle linked essay (for all question types)
    if (question.essay_id) {
        if (modalLinkedEssayContainer) modalLinkedEssayContainer.classList.remove('hidden');
        const modalLinkedEssayTitle = document.getElementById('modal-linked-essay-title');
        if (modalLinkedEssayTitle) modalLinkedEssayTitle.textContent = question.essay_title || 'Essay #' + question.essay_id;
    }
}

function populateViewOptions(options) {
    const optionsContainer = document.getElementById('view_options');
    optionsContainer.innerHTML = '';

    if (options && options.length > 0) {
        options.forEach((option, index) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'flex items-start';

            const optionContent = document.createElement('div');
            optionContent.className = `p-2 rounded ${option.is_correct ? 'bg-green-100 border border-green-300' : 'bg-gray-100'} flex-grow`;

            const optionText = document.createElement('div');
            optionText.className = 'flex items-center';

            // Add a checkmark for correct option
            if (option.is_correct) {
                const checkmark = document.createElement('span');
                checkmark.className = 'text-green-600 mr-2';
                checkmark.innerHTML = `
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                `;
                optionText.appendChild(checkmark);
            }

            // Option letter (A, B, C, etc.)
            const optionLetter = document.createElement('span');
            optionLetter.className = 'font-bold mr-2';
            optionLetter.textContent = String.fromCharCode(65 + index) + '.';
            optionText.appendChild(optionLetter);

            // Option text content
            const textContent = document.createElement('span');
            textContent.innerHTML = option.option_text;
            optionText.appendChild(textContent);

            optionContent.appendChild(optionText);
            optionDiv.appendChild(optionContent);
            optionsContainer.appendChild(optionDiv);
        });
    } else {
        const noOptionsText = document.createElement('p');
        noOptionsText.className = 'text-gray-500';
        noOptionsText.textContent = 'No options available';
        optionsContainer.appendChild(noOptionsText);
    }
}

function submitAddQuestionForm(event) {
    event.preventDefault();

    // Clear previous error messages
    const errorMessages = document.getElementById('errorMessages');
    errorMessages.innerHTML = '';
    errorMessages.classList.add('hidden');

    // Get form data
    const form = document.getElementById('addQuestionForm');
    const formData = new FormData(form);

    // Submit form via AJAX
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            if (window.confirmationDialog) {
                window.confirmationDialog.show({
                    title: 'Success',
                    message: 'Question added successfully!',
                    confirmText: 'OK',
                    cancelText: null,
                    type: 'success',
                    onConfirm: function() {
                        // Reload the page to show the new question
                        window.location.reload();
                    }
                });
            } else {
                alert('Question added successfully!');
                window.location.reload();
            }
        } else {
            // Show error message
            errorMessages.innerHTML = data.message || 'Failed to add question. Please check your inputs.';
            errorMessages.classList.remove('hidden');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorMessages.innerHTML = 'An error occurred while submitting the form.';
        errorMessages.classList.remove('hidden');
    });

    return false;
}

// Import Questions Modal Functions
function openImportQuestionsModal() {
    document.getElementById('importQuestionsModal').classList.remove('hidden');
}

function closeImportQuestionsModal() {
    document.getElementById('importQuestionsModal').classList.add('hidden');
}

function submitImportQuestionsForm(event) {
    event.preventDefault();

    // Clear previous error messages
    const errorMessages = document.getElementById('importErrorMessages');
    errorMessages.innerHTML = '';
    errorMessages.classList.add('hidden');

    // Get form data
    const form = document.getElementById('importQuestionsForm');
    const formData = new FormData(form);

    // Submit form via AJAX
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            if (window.confirmationDialog) {
                window.confirmationDialog.show({
                    title: 'Success',
                    message: `${data.count || 'Multiple'} questions imported successfully!`,
                    confirmText: 'OK',
                    cancelText: null,
                    type: 'success',
                    onConfirm: function() {
                        // Reload the page to show the new questions
                        window.location.reload();
                    }
                });
            } else {
                alert(`${data.count || 'Multiple'} questions imported successfully!`);
                window.location.reload();
            }
        } else {
            // Show error message
            errorMessages.innerHTML = data.message || 'Failed to import questions. Please check your inputs.';
            errorMessages.classList.remove('hidden');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorMessages.innerHTML = 'An error occurred while submitting the form.';
        errorMessages.classList.remove('hidden');
    });

    return false;
}

// Add event listeners to buttons
document.addEventListener('DOMContentLoaded', function() {
    // Add Question button
    document.getElementById('addQuestionBtn').addEventListener('click', openAddQuestionModal);

    // Import Questions button
    document.getElementById('importQuestionsBtn').addEventListener('click', openImportQuestionsModal);

    // Initialize dynamic form elements for Add Question modal
    initAddQuestionFormHandlers();

    // Initialize dynamic form elements for Import Questions modal
    initImportQuestionsFormHandlers();
});

// Initialize Add Question form handlers
function initAddQuestionFormHandlers() {
    const questionTypeSelect = document.getElementById('question_type');
    const optionsContainer = document.getElementById('optionsContainer');
    const addOptionBtn = document.getElementById('addOptionBtn');
    const optionsList = document.getElementById('optionsList');
    const examSelect = document.getElementById('exam_id');
    const sectionSelect = document.getElementById('section_id');

    // Question type change handler
    if (questionTypeSelect) {
        questionTypeSelect.addEventListener('change', function() {
            const questionType = this.value;

            // Hide all option sections first
            optionsContainer.classList.add('hidden');
            document.getElementById('true_false_options').classList.add('hidden');
            document.getElementById('fill_up_options').classList.add('hidden');
            document.getElementById('essay_options').classList.add('hidden');

            // Show the appropriate section based on question type
            if (questionType === 'multiple_choice') {
                optionsContainer.classList.remove('hidden');
            } else if (questionType === 'true_false') {
                document.getElementById('true_false_options').classList.remove('hidden');
            } else if (questionType === 'fill_up') {
                document.getElementById('fill_up_options').classList.remove('hidden');
            } else if (questionType === 'essay') {
                document.getElementById('essay_options').classList.remove('hidden');
            }
        });
    }

    // Add Option button for MCQ
    if (addOptionBtn && optionsList) {
        addOptionBtn.addEventListener('click', function() {
            const optionRow = document.createElement('div');
            optionRow.className = 'option-row mb-2';
            optionRow.innerHTML = `
                <div class="flex items-center">
                    <input type="radio" name="correct_option" value="${optionsList.children.length}" required class="mr-2">
                    <input type="text" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" name="option_text[]" placeholder="Option text..." required>
                    <button type="button" class="ml-2 text-red-600 hover:text-red-800 remove-option">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            `;
            optionsList.appendChild(optionRow);

            // Add event listener to the remove button
            const removeButton = optionRow.querySelector('.remove-option');
            removeButton.addEventListener('click', function() {
                optionsList.removeChild(optionRow);
                updateOptionIndexes();
            });
        });
    }

    // Function to update option indexes
    function updateOptionIndexes() {
        const optionRows = optionsList.querySelectorAll('.option-row');
        optionRows.forEach((row, index) => {
            const radio = row.querySelector('input[type="radio"]');
            radio.value = index;
        });
    }

    // Exam change handler
    if (examSelect && sectionSelect) {
        examSelect.addEventListener('change', function() {
            const examId = this.value;
            if (examId) {
                // Fetch sections for the selected exam
                fetch(`/admin/exams/${examId}/sections`)
                    .then(response => response.json())
                    .then(data => {
                        // Clear current options
                        sectionSelect.innerHTML = '<option value="">Select Section</option>';

                        // Add new options
                        data.forEach(section => {
                            const option = document.createElement('option');
                            option.value = section.section_id;
                            option.textContent = section.section_name;
                            sectionSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching sections:', error);
                    });
            } else {
                // Clear sections if no exam is selected
                sectionSelect.innerHTML = '<option value="">Select Section</option>';
            }
        });
    }
}

// Initialize Import Questions form handlers
function initImportQuestionsFormHandlers() {
    const importFormatRadios = document.querySelectorAll('input[name="import_format"]');
    const fileUploadSection = document.getElementById('fileUploadSection');
    const jsonInputSection = document.getElementById('jsonInputSection');
    const jsonFormatExample = document.getElementById('jsonFormatExample');
    const csvFormatExample = document.getElementById('csvFormatExample');
    const excelFormatExample = document.getElementById('excelFormatExample');
    const importMethodRadios = document.querySelectorAll('input[name="import_method"]');
    const existingExamSection = document.getElementById('existingExamSection');
    const newExamSection = document.getElementById('newExamSection');
    const examSelect = document.getElementById('import_exam_id');
    const sectionSelect = document.getElementById('import_section_id');
    const fileInput = document.getElementById('questions_file');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');

    // Handle import format changes
    if (importFormatRadios.length > 0) {
        importFormatRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Hide all format examples
                jsonFormatExample.classList.add('hidden');
                csvFormatExample.classList.add('hidden');
                excelFormatExample.classList.add('hidden');

                if (this.value === 'json') {
                    fileUploadSection.classList.add('hidden');
                    jsonInputSection.classList.remove('hidden');
                    jsonFormatExample.classList.remove('hidden');
                } else if (this.value === 'csv') {
                    fileUploadSection.classList.remove('hidden');
                    jsonInputSection.classList.add('hidden');
                    csvFormatExample.classList.remove('hidden');
                } else if (this.value === 'excel') {
                    fileUploadSection.classList.remove('hidden');
                    jsonInputSection.classList.add('hidden');
                    excelFormatExample.classList.remove('hidden');
                }
            });
        });
    }

    // Handle import method changes
    if (importMethodRadios.length > 0) {
        importMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'existing') {
                    existingExamSection.classList.remove('hidden');
                    newExamSection.classList.add('hidden');
                } else {
                    existingExamSection.classList.add('hidden');
                    newExamSection.classList.remove('hidden');
                }
            });
        });
    }

    // Handle file selection
    if (fileInput && filePreview) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Check file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB');
                    fileInput.value = '';
                    return;
                }

                // Format file size
                const size = file.size < 1024 * 1024
                    ? (file.size / 1024).toFixed(2) + ' KB'
                    : (file.size / (1024 * 1024)).toFixed(2) + ' MB';

                fileName.textContent = file.name;
                fileSize.textContent = size;
                filePreview.classList.remove('hidden');
            }
        });
    }

    // Handle file removal
    if (removeFile && filePreview) {
        removeFile.addEventListener('click', function() {
            fileInput.value = '';
            filePreview.classList.add('hidden');
        });
    }

    // Exam change handler
    if (examSelect && sectionSelect) {
        examSelect.addEventListener('change', function() {
            const examId = this.value;
            if (examId) {
                // Fetch sections for the selected exam
                fetch(`/admin/exams/${examId}/sections`)
                    .then(response => response.json())
                    .then(data => {
                        // Clear current options
                        sectionSelect.innerHTML = '<option value="">Select Section</option>';

                        // Add new options
                        data.forEach(section => {
                            const option = document.createElement('option');
                            option.value = section.section_id;
                            option.textContent = section.section_name;
                            sectionSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching sections:', error);
                    });
            } else {
                // Clear sections if no exam is selected
                sectionSelect.innerHTML = '<option value="">Select Section</option>';
            }
        });
    }
}
</script>
