<!-- Add Question Modal -->
<div id="addQuestionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-xl font-semibold">Add New Question</h3>
            <button onclick="closeAddQuestionModal()" class="text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="p-6">
            <form id="addQuestionForm" action="/admin/questions/add" method="POST" onsubmit="return submitAddQuestionForm(event)">
                <!-- Exam Selection -->
                <div class="mb-4">
                    <label for="exam_id" class="block text-gray-700 text-sm font-bold mb-2">Exam (Optional)</label>
                    <select id="exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Select Exam</option>
                        <% exams.forEach(exam => { %>
                            <option value="<%= exam.exam_id %>"><%= exam.exam_name %></option>
                        <% }); %>
                    </select>
                </div>

                <!-- Section Selection (populated based on exam) -->
                <div class="mb-4">
                    <label for="section_id" class="block text-gray-700 text-sm font-bold mb-2">Section (Optional)</label>
                    <select id="section_id" name="section_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Select Section</option>
                    </select>
                </div>

                <!-- Question Type -->
                <div class="mb-4">
                    <label for="question_type" class="block text-gray-700 text-sm font-bold mb-2">Question Type</label>
                    <select id="question_type" name="question_type" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                        <option value="">Select Type</option>
                        <option value="multiple_choice">Multiple Choice</option>
                        <option value="true_false">True/False</option>
                        <option value="fill_up">Fill in the Blank</option>
                        <option value="short_answer">Short Answer</option>
                        <option value="essay">Essay</option>
                    </select>
                </div>

                <!-- Question Text -->
                <div class="mb-4">
                    <label for="question_text" class="block text-gray-700 text-sm font-bold mb-2">Question Text</label>
                    <textarea id="question_text" name="question_text" rows="4" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Enter your question here..." required></textarea>
                </div>

                <!-- Question Image -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Question Image (Optional)</label>
                    <div class="flex items-start space-x-4">
                        <div class="flex-1">
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md question-drop-zone" tabindex="0">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="question_image" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                            <span>Upload an image</span>
                                            <input id="question_image" name="question_image" type="file" class="sr-only" accept="image/*" onchange="previewQuestionImage(this)">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        PNG, JPG, GIF up to 5MB
                                    </p>
                                    <p class="text-xs text-indigo-600 mt-2">
                                        You can also paste an image directly (Ctrl+V or ⌘+V)
                                    </p>
                                </div>
                            </div>
                            <input type="hidden" name="image_id" id="question_image_id" value="">
                        </div>
                        <div id="question_image_preview_container" class="w-32 h-32 border border-gray-300 rounded-md overflow-hidden hidden">
                            <img id="question_image_preview" src="" alt="Question image preview" class="w-full h-full object-contain">
                            <button type="button" onclick="removeQuestionImage()" class="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 focus:outline-none">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Preview Section -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Preview:</label>
                    <div id="math_preview" class="p-4 border rounded-md bg-gray-50"></div>
                </div>

                <!-- Multiple Choice Options (shown only for MCQ) -->
                <fieldset id="optionsContainer" class="mb-4 hidden">
                    <legend class="block text-gray-700 text-sm font-bold mb-2">Options</legend>
                    <div id="optionsList">
                        <div class="option-row mb-4" data-index="0">
                            <div class="flex items-center">
                                <input type="radio" name="correct_option" value="0" required class="mr-2">
                                <input type="text" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" name="option_text[]" placeholder="Option text..." required>
                                <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="toggleOptionImageUpload(0)">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                                <button type="button" class="ml-2 text-red-600 hover:text-red-800 remove-option">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            <!-- Option Image Upload (hidden by default) -->
                            <div id="option_image_container_0" class="mt-2 hidden">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-1">
                                        <div class="flex justify-center px-4 py-3 border border-gray-300 border-dashed rounded-md option-drop-zone" data-index="0">
                                            <div class="space-y-1 text-center">
                                                <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                <div class="flex text-xs text-gray-600">
                                                    <label for="option_image_0" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500">
                                                        <span>Upload image</span>
                                                        <input id="option_image_0" type="file" class="sr-only" accept="image/*" onchange="previewOptionImage(this, 0)">
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" name="option_image_id[]" id="option_image_id_0" value="">
                                    </div>
                                    <div id="option_image_preview_container_0" class="w-20 h-20 border border-gray-300 rounded-md overflow-hidden hidden">
                                        <img id="option_image_preview_0" src="" alt="Option image preview" class="w-full h-full object-contain">
                                        <button type="button" onclick="removeOptionImage(0)" class="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 focus:outline-none">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" id="addOptionBtn" class="mt-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-3 rounded text-sm">
                        Add Option
                    </button>
                </fieldset>

                <!-- True/False Options (shown only for T/F) -->
                <div id="true_false_options" class="mb-4 hidden">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="true_false_answer" value="true" class="form-radio" required>
                            <span class="ml-2">True</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="true_false_answer" value="false" class="form-radio" required>
                            <span class="ml-2">False</span>
                        </label>
                    </div>
                </div>

                <!-- Fill in the Blank Options (shown only for fill-up) -->
                <div id="fill_up_options" class="mb-4 hidden">
                    <label for="correct_answer" class="block text-gray-700 text-sm font-bold mb-2">Correct Answer</label>
                    <input type="text" id="correct_answer" name="correct_answer" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <p class="text-sm text-gray-500 mt-1">For multiple acceptable answers, separate with commas.</p>
                </div>

                <!-- Essay Options (shown only for essay) -->
                <div id="essay_options" class="mb-4 hidden">
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    This is an essay question. Students will provide a written response that will need to be manually graded.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="min_word_count" class="block text-gray-700 text-sm font-bold mb-2">Minimum Word Count</label>
                            <input type="number" id="min_word_count" name="min_word_count" min="0" value="0" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div>
                            <label for="max_word_count" class="block text-gray-700 text-sm font-bold mb-2">Maximum Word Count</label>
                            <input type="number" id="max_word_count" name="max_word_count" min="0" value="500" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                    </div>
                </div>

                <!-- Solution Text -->
                <div class="mb-4">
                    <label for="solution_text" class="block text-gray-700 text-sm font-bold mb-2">Solution/Explanation (Optional)</label>
                    <textarea id="solution_text" name="solution_text" rows="3" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Explain the solution..."></textarea>
                </div>

                <!-- Solution Image -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Solution Image (Optional)</label>
                    <div class="flex items-start space-x-4">
                        <div class="flex-1">
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md solution-drop-zone" tabindex="0">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="solution_image" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                            <span>Upload an image</span>
                                            <input id="solution_image" name="solution_image" type="file" class="sr-only" accept="image/*" onchange="previewSolutionImage(this)">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        PNG, JPG, GIF up to 5MB
                                    </p>
                                    <p class="text-xs text-indigo-600 mt-2">
                                        You can also paste an image directly (Ctrl+V or ⌘+V)
                                    </p>
                                </div>
                            </div>
                            <input type="hidden" name="solution_image_id" id="solution_image_id" value="">
                        </div>
                        <div id="solution_image_preview_container" class="w-32 h-32 border border-gray-300 rounded-md overflow-hidden hidden">
                            <img id="solution_image_preview" src="" alt="Solution image preview" class="w-full h-full object-contain">
                            <button type="button" onclick="removeSolutionImage()" class="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 focus:outline-none">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Marks -->
                <div class="mb-4">
                    <label for="marks" class="block text-gray-700 text-sm font-bold mb-2">Marks</label>
                    <input type="number" id="marks" name="marks" min="0" step="0.5" value="1" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <!-- Negative Marks -->
                <div class="mb-4">
                    <label for="negative_marks" class="block text-gray-700 text-sm font-bold mb-2">Negative Marks</label>
                    <input type="number" id="negative_marks" name="negative_marks" min="0" step="0.5" value="0" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <!-- Categories Selection -->
                <div class="mb-4">
                    <label for="category_ids" class="block text-gray-700 text-sm font-bold mb-2">Categories</label>
                    <select id="category_ids" name="category_ids[]" multiple class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>"><%= category.name %></option>
                        <% }); %>
                    </select>
                </div>

                <!-- Essay Selection (shown for all question types) -->
                <div class="mb-4" id="essaySelectionContainer">
                    <div class="flex justify-between items-center mb-2">
                        <label for="essay_id" class="block text-gray-700 text-sm font-bold">Link to Essay (Optional)</label>
                        <a href="/admin/essays/add" class="bg-blue-500 hover:bg-blue-600 text-white text-sm px-3 py-1 rounded" target="_blank">
                            <i class="fas fa-plus mr-1"></i> Create New Essay
                        </a>
                    </div>
                    <select id="essay_id" name="essay_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">None (No Essay)</option>
                        <% if (essays && essays.length > 0) { %>
                            <% essays.forEach(essay => { %>
                                <option value="<%= essay.essay_id %>">
                                    <%= essay.title %> (<%= essay.question_count %> questions, <%= essay.formatted_date %>)
                                </option>
                            <% }); %>
                        <% } else { %>
                            <option value="" disabled>No essays available - create one first</option>
                        <% } %>
                    </select>
                    <p class="mt-1 text-sm text-gray-500">
                        Link this question to an essay that students must read before answering.
                    </p>
                </div>

                <!-- Error Messages -->
                <div id="errorMessages" class="mb-4 hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"></div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="button" onclick="closeAddQuestionModal()" class="mr-2 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Cancel
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Add Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
