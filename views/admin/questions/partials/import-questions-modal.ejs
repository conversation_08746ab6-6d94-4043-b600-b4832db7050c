<!-- Import Questions Modal -->
<div id="importQuestionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-xl font-semibold">Import Questions</h3>
            <button onclick="closeImportQuestionsModal()" class="text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="p-6">
            <form id="importQuestionsForm" action="/admin/questions/import" method="POST" enctype="multipart/form-data" onsubmit="return submitImportQuestionsForm(event)">
                <!-- Import Format Selection -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Import Format</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_format" value="json" class="form-radio" checked>
                            <span class="ml-2">JSON</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_format" value="csv" class="form-radio">
                            <span class="ml-2">CSV</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_format" value="excel" class="form-radio">
                            <span class="ml-2">Excel</span>
                        </label>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div id="fileUploadSection" class="mb-4 hidden">
                    <label for="questions_file" class="block text-gray-700 text-sm font-bold mb-2">Upload File</label>
                    <div class="flex items-center justify-center w-full">
                        <label class="flex flex-col w-full h-32 border-4 border-dashed hover:bg-gray-100 hover:border-gray-300 cursor-pointer">
                            <div class="flex flex-col items-center justify-center pt-7">
                                <svg class="w-10 h-10 text-gray-400 group-hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="pt-1 text-sm tracking-wider text-gray-400 group-hover:text-gray-600">
                                    Attach a file
                                </p>
                            </div>
                            <input type="file" id="questions_file" name="questions_file" class="opacity-0" />
                        </label>
                    </div>
                    <div id="filePreview" class="mt-2 p-2 border rounded hidden">
                        <div class="flex justify-between items-center">
                            <div>
                                <span id="fileName" class="text-sm font-medium"></span>
                                <span id="fileSize" class="text-xs text-gray-500 ml-2"></span>
                            </div>
                            <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- JSON Data -->
                <div id="jsonInputSection" class="mb-4">
                    <label for="questions_json" class="block text-gray-700 text-sm font-bold mb-2">Questions JSON</label>
                    <textarea id="questions_json" name="questions_json" rows="10" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline font-mono text-sm"></textarea>
                    <p class="text-sm text-gray-500 mt-1">Paste your questions in JSON format.</p>
                </div>

                <!-- Format Examples -->
                <div class="mb-6">
                    <h3 class="text-sm font-bold text-gray-700 mb-2">Import Format Examples:</h3>
                    
                    <!-- JSON Format Example -->
                    <div id="jsonFormatExample" class="bg-gray-50 p-3 rounded text-xs font-mono overflow-x-auto">
                        <pre>[
  {
    "question_type": "multiple_choice",
    "question_text": "What is the capital of France?",
    "options": ["London", "Paris", "Berlin", "Madrid"],
    "correct_answer": 1,
    "solution_text": "Paris is the capital of France.",
    "marks": 1,
    "categories": ["Geography", "Europe"]
  },
  {
    "question_type": "true_false",
    "question_text": "The Earth is flat.",
    "correct_answer": false,
    "solution_text": "The Earth is approximately spherical.",
    "marks": 1,
    "categories": ["Science"]
  }
]</pre>
                    </div>
                    
                    <!-- CSV Format Example -->
                    <div id="csvFormatExample" class="bg-gray-50 p-3 rounded text-xs font-mono overflow-x-auto hidden">
                        <pre>question_type,question_text,options,correct_answer,solution_text,marks,categories
multiple_choice,"What is the capital of France?","London,Paris,Berlin,Madrid",1,"Paris is the capital of France.",1,"Geography,Europe"
true_false,"The Earth is flat.",,,false,"The Earth is approximately spherical.",1,Science</pre>
                    </div>
                    
                    <!-- Excel Format Example -->
                    <div id="excelFormatExample" class="bg-gray-50 p-3 rounded text-xs font-mono overflow-x-auto hidden">
                        <p>Excel file should have the same columns as the CSV example.</p>
                    </div>
                </div>

                <!-- Import Method -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Import Method</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_method" value="existing" class="form-radio" checked>
                            <span class="ml-2">Add to Existing Exam</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="import_method" value="new" class="form-radio">
                            <span class="ml-2">Create New Exam</span>
                        </label>
                    </div>
                </div>

                <!-- Existing Exam Section -->
                <div id="existingExamSection" class="mb-4">
                    <div class="mb-4">
                        <label for="import_exam_id" class="block text-gray-700 text-sm font-bold mb-2">Select Exam</label>
                        <select id="import_exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">Select Exam</option>
                            <% exams.forEach(exam => { %>
                                <option value="<%= exam.exam_id %>"><%= exam.exam_name %></option>
                            <% }); %>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label for="import_section_id" class="block text-gray-700 text-sm font-bold mb-2">Select Section</label>
                        <select id="import_section_id" name="section_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">Select Section</option>
                        </select>
                    </div>
                </div>

                <!-- New Exam Section -->
                <div id="newExamSection" class="mb-4 hidden">
                    <div class="mb-4">
                        <label for="new_exam_name" class="block text-gray-700 text-sm font-bold mb-2">New Exam Name</label>
                        <input type="text" id="new_exam_name" name="new_exam_name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                    
                    <div class="mb-4">
                        <label for="new_section_name" class="block text-gray-700 text-sm font-bold mb-2">New Section Name</label>
                        <input type="text" id="new_section_name" name="new_section_name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                </div>

                <!-- Error Messages -->
                <div id="importErrorMessages" class="mb-4 hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"></div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="button" onclick="closeImportQuestionsModal()" class="mr-2 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Cancel
                    </button>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Import Questions
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
