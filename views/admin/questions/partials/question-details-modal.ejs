<!-- Question Details Modal -->
<div id="questionDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
    <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Question Details</h3>
      <button onclick="closeQuestionDetailsModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <span id="modal-question-type" class="px-2 py-1 text-xs font-medium rounded-full"></span>
          <span id="modal-marks-badge" class="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800"></span>
          <span id="modal-negative-marks-badge" class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 hidden"></span>
        </div>
      </div>

      <!-- Question Text -->
      <div class="mb-6">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Question</h4>
        <div id="modal-question-text" class="text-gray-700 bg-gray-50 p-4 rounded-lg whitespace-pre-line"></div>
      </div>

      <!-- Options (for MCQ) -->
      <div id="modal-options-container" class="mb-6 hidden">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Options</h4>
        <div id="modal-options-list" class="space-y-2"></div>
      </div>

      <!-- True/False Answer -->
      <div id="modal-true-false-container" class="mb-6 hidden">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Answer</h4>
        <div class="space-y-2 ml-4">
          <div class="flex items-center py-1">
            <div class="flex items-center h-5">
              <input type="radio" id="modal-true-option" disabled class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
            </div>
            <div class="ml-3 text-sm">
              <label id="modal-true-label" class="font-medium text-gray-700">True</label>
            </div>
          </div>
          <div class="flex items-center py-1">
            <div class="flex items-center h-5">
              <input type="radio" id="modal-false-option" disabled class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
            </div>
            <div class="ml-3 text-sm">
              <label id="modal-false-label" class="font-medium text-gray-700">False</label>
            </div>
          </div>
        </div>
      </div>

      <!-- Fill in the Blank Answer -->
      <div id="modal-fill-up-container" class="mb-6 hidden">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Correct Answer(s)</h4>
        <div id="modal-fill-up-answer" class="text-gray-700 bg-gray-50 p-4 rounded-lg"></div>
      </div>

      <!-- Essay Question -->
      <div id="modal-essay-container" class="mb-6 hidden">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Essay Question</h4>
        <div class="text-gray-700 bg-gray-50 p-4 rounded-lg">
          <p class="text-sm italic">This is an essay question. Students will provide a written response that will need to be manually graded.</p>
          <div class="mt-3 grid grid-cols-2 gap-4">
            <div>
              <p class="text-sm font-medium text-gray-600">Minimum Word Count:</p>
              <p id="modal-min-word-count" class="text-sm font-bold">0</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">Maximum Word Count:</p>
              <p id="modal-max-word-count" class="text-sm font-bold">500</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Linked Essay -->
      <div id="modal-linked-essay-container" class="mb-6 hidden">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Linked Essay</h4>
        <div class="text-gray-700 bg-gray-50 p-4 rounded-lg">
          <div class="flex items-center">
            <svg class="h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span id="modal-linked-essay-title" class="font-medium">No essay linked</span>
          </div>
        </div>
      </div>

      <!-- Solution -->
      <div id="modal-solution-container" class="mb-6">
        <h4 class="text-lg font-medium text-gray-800 mb-2">Solution/Explanation</h4>
        <div id="modal-solution-text" class="text-gray-700 bg-gray-50 p-4 rounded-lg whitespace-pre-line"></div>
      </div>
    </div>
  </div>
</div>
