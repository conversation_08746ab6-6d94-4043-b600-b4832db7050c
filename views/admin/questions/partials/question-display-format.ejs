<!-- Uniform Question Display Format -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-4 question-item" data-question-id="<%= question.question_id %>" data-position="<%= questionNumber %>">
  <div class="bg-blue-50 p-3 border-b border-blue-100 flex justify-between items-center">
    <div class="flex items-center">
      <div class="question-drag-handle cursor-move px-1 mr-1 text-gray-400 hover:text-gray-600">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
        </svg>
      </div>
      <span class="font-semibold text-gray-800 mr-2 bg-gray-200 px-2 py-1 rounded-md">Q<%= questionNumber %></span>
      <span class="text-sm text-blue-600"><%= question.marks || 1 %> mark<%= (question.marks && question.marks > 1) ? 's' : '' %></span>
      <% if (question.negative_marks && question.negative_marks > 0) { %>
        <span class="ml-2 text-sm text-red-600">-<%= question.negative_marks %> mark<%= question.negative_marks > 1 ? 's' : '' %></span>
      <% } %>
    </div>
    <div class="flex items-center space-x-2 flex-wrap gap-1">
      <!-- Question Type Badge -->
      <% if (question.question_type === 'multiple_choice') { %>
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Multiple Choice</span>
      <% } else if (question.question_type === 'true_false') { %>
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">True/False</span>
      <% } else if (question.question_type === 'essay') { %>
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
      <% } else if (question.question_type === 'fill_up') { %>
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill-up</span>
      <% } %>

      <!-- Category Badges (if available) -->
      <% if (question.category_names) { %>
        <%
        const categoryColors = {
          'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
          'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
          'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
          'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
          'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
          'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
          'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
          'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
          'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
          'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
        };

        let categoryNamesArray = [];
        if (typeof question.category_names === 'string') {
          categoryNamesArray = question.category_names.split(',');
        } else if (Array.isArray(question.category_names)) {
          categoryNamesArray = question.category_names;
        }

        categoryNamesArray.forEach(categoryName => {
          if (!categoryName) return;
          const category = categoryName.trim();
          const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
        %>
          <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
            <%= category %>
          </span>
        <% }); %>
      <% } %>
    </div>
  </div>

  <div class="p-4">

    <!-- Question Text -->
    <div class="mb-4">
      <p class="text-gray-800 whitespace-pre-line question-text"><%- question.question_text.replace(/\n/g, '<br>') %></p>
      <% if (question.image_path || (question.metadata && question.metadata.question_image_url)) { %>
        <div class="mt-2">
          <img src="<%= question.image_path || (question.metadata && question.metadata.question_image_url) %>" alt="Question Image" class="question-image">
        </div>
      <% } %>
    </div>

    <!-- Options for Multiple Choice -->
    <% if (question.question_type === 'multiple_choice' && question.options) { %>
      <div class="space-y-2 ml-4 mb-4">
        <% question.options.forEach((option, optionIndex) => { %>
          <div class="flex items-center py-1">
            <div class="flex items-center h-5">
              <input
                type="radio"
                disabled
                <%= option.is_correct ? 'checked' : '' %>
                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
              >
            </div>
            <div class="ml-3 text-sm">
              <label class="font-medium text-gray-700 <%= option.is_correct ? 'text-green-600 font-semibold' : '' %>">
                <%= option.option_text %>
                <% if (option.is_correct && showCorrect) { %>
                  <span class="ml-2 text-xs text-green-600">(Correct)</span>
                <% } %>
              </label>
            </div>
          </div>
        <% }); %>
      </div>
    <% } else if (question.question_type === 'true_false') { %>
      <div class="space-y-2 ml-4 mb-4">
        <div class="flex items-center py-1">
          <div class="flex items-center h-5">
            <input
              type="radio"
              disabled
              <%= question.correct_answer === 'true' ? 'checked' : '' %>
              class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
            >
          </div>
          <div class="ml-3 text-sm">
            <label class="font-medium text-gray-700 <%= question.correct_answer === 'true' && showCorrect ? 'text-green-600 font-semibold' : '' %>">
              True
              <% if (question.correct_answer === 'true' && showCorrect) { %>
                <span class="ml-2 text-xs text-green-600">(Correct)</span>
              <% } %>
            </label>
          </div>
        </div>
        <div class="flex items-center py-1">
          <div class="flex items-center h-5">
            <input
              type="radio"
              disabled
              <%= question.correct_answer === 'false' ? 'checked' : '' %>
              class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
            >
          </div>
          <div class="ml-3 text-sm">
            <label class="font-medium text-gray-700 <%= question.correct_answer === 'false' && showCorrect ? 'text-green-600 font-semibold' : '' %>">
              False
              <% if (question.correct_answer === 'false' && showCorrect) { %>
                <span class="ml-2 text-xs text-green-600">(Correct)</span>
              <% } %>
            </label>
          </div>
        </div>
      </div>
    <% } else if (question.question_type === 'fill_up' && question.options) { %>
      <div class="ml-4 py-2 mb-4">
        <div class="text-sm font-medium text-gray-700">
          <span class="text-gray-600">Correct Answer<%= question.options.length > 1 ? 's' : '' %>:</span>
          <% if (showCorrect) { %>
            <span class="ml-2 text-green-600 font-semibold">
              <%= question.options.map(opt => opt.option_text || '').filter(Boolean).join(' | ') %>
            </span>
          <% } else { %>
            <span class="ml-2 text-gray-500">[Hidden]</span>
          <% } %>
        </div>
      </div>
    <% } else if (question.question_type === 'essay') { %>
      <div class="ml-4 py-2 mb-4">
        <div class="text-sm text-gray-600 italic">
          Essay question - no predefined answer
        </div>
      </div>
    <% } %>

    <!-- Solution (if available and should be shown) -->
    <% if (question.solution_text && showSolution) { %>
      <div class="mt-4 pt-3 border-t border-gray-200">
        <h4 class="text-sm font-medium text-gray-700">Solution:</h4>
        <p class="text-sm text-gray-600 mt-1 whitespace-pre-line"><%- question.solution_text.replace(/\n/g, '<br>') %></p>
        <% if (question.solution_image_path || (question.metadata && question.metadata.solution_image_url)) { %>
          <div class="mt-2">
            <img src="<%= question.solution_image_path || (question.metadata && question.metadata.solution_image_url) %>" alt="Solution Image" class="solution-image">
          </div>
        <% } %>
      </div>
    <% } %>

    <!-- Action Buttons -->
    <% if (showActions) { %>
      <div class="mt-4 pt-3 border-t border-gray-200 flex justify-end">
        <button type="button" class="delete-question-btn p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full"
                data-question-id="<%= question.question_id %>" title="Delete Question">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      </div>
    <% } %>
  </div>
</div>
