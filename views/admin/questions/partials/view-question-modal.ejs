<!-- View Question Modal -->
<div id="viewQuestionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-xl font-semibold">Question Details</h3>
            <button onclick="closeViewQuestionModal()" class="text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="p-6">
            <div id="viewQuestionErrorMessages" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden"></div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div>
                    <!-- Question ID -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Question ID</h4>
                        <p id="view_question_id" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Exam -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Exam</h4>
                        <p id="view_exam_name" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Section -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Section</h4>
                        <p id="view_section_name" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Question Type -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Question Type</h4>
                        <p id="view_question_type" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Marks -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Marks</h4>
                        <p id="view_marks" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Negative Marks -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Negative Marks</h4>
                        <p id="view_negative_marks" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Categories -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Categories</h4>
                        <div id="view_categories" class="flex flex-wrap gap-2"></div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Question Text -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Question Text</h4>
                        <div id="view_question_text" class="text-gray-900 bg-gray-100 p-3 rounded math-content"></div>
                        <!-- Question Image -->
                        <div id="view_question_image_container" class="mt-3 hidden">
                            <img id="view_question_image" src="" alt="Question Image" class="question-image">
                        </div>
                    </div>

                    <!-- Options (for MCQ) -->
                    <div id="view_options_container" class="mb-4 hidden">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Options</h4>
                        <div id="view_options" class="space-y-2"></div>
                    </div>

                    <!-- True/False Answer -->
                    <div id="view_true_false_container" class="mb-4 hidden">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Correct Answer</h4>
                        <p id="view_true_false_answer" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Fill in the Blank Answer -->
                    <div id="view_fill_up_container" class="mb-4 hidden">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Correct Answer(s)</h4>
                        <p id="view_fill_up_answer" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Essay Question Details -->
                    <div id="modal-essay-container" class="mb-4 hidden">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Essay Word Count Limits</h4>
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <span class="text-xs text-gray-600">Minimum:</span>
                                <p id="modal-min-word-count" class="text-gray-900 bg-gray-100 p-2 rounded">0</p>
                            </div>
                            <div>
                                <span class="text-xs text-gray-600">Maximum:</span>
                                <p id="modal-max-word-count" class="text-gray-900 bg-gray-100 p-2 rounded">500</p>
                            </div>
                        </div>
                    </div>

                    <!-- Linked Essay -->
                    <div id="modal-linked-essay-container" class="mb-4 hidden">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Linked Essay</h4>
                        <p id="modal-linked-essay-title" class="text-gray-900 bg-gray-100 p-2 rounded"></p>
                    </div>

                    <!-- Solution -->
                    <div class="mb-4">
                        <h4 class="text-sm font-bold text-gray-700 mb-1">Solution</h4>
                        <div id="view_solution_text" class="text-gray-900 bg-gray-100 p-3 rounded math-content"></div>
                        <!-- Solution Image -->
                        <div id="view_solution_image_container" class="mt-3 hidden">
                            <img id="view_solution_image" src="" alt="Solution Image" class="solution-image">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end mt-6">
                <a id="view_edit_link" href="#" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mr-2 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
                <button onclick="closeViewQuestionModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
