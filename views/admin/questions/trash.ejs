<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Deleted Questions</h1>
        <div class="flex space-x-2">
            <a href="/admin/questions" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                Back to Questions
            </a>
        </div>
    </div>

    <!-- Records per page and total count -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Show:</label>
            <select name="perPage" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="updatePerPage(this.value)">
                <option value="10" <%= parseInt(query?.perPage) === 10 || !query?.perPage ? 'selected' : '' %>>10</option>
                <option value="25" <%= parseInt(query?.perPage) === 25 ? 'selected' : '' %>>25</option>
                <option value="50" <%= parseInt(query?.perPage) === 50 ? 'selected' : '' %>>50</option>
                <option value="100" <%= parseInt(query?.perPage) === 100 ? 'selected' : '' %>>100</option>
            </select>
            <span class="text-sm text-gray-600">entries</span>
        </div>
        <div class="text-sm text-gray-600">
            Total Deleted Questions: <%= pagination.totalItems || 0 %>
        </div>
    </div>

    <!-- Questions Table -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted At</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (questions && questions.length > 0) { %>
                        <% questions.forEach((question, index) => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= (pagination.currentPage - 1) * (parseInt(query?.perPage) || 10) + index + 1 %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= question.question_id %></td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <div class="max-w-xs truncate"><%= question.question_text %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.question_type === 'multiple_choice') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">MCQ</span>
                                <% } else if (question.question_type === 'true_false') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">True/False</span>
                                <% } else if (question.question_type === 'essay') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Essay</span>
                                <% } else if (question.question_type === 'fill_up') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Fill-up</span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><%= question.question_type %></span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.category_names) { %>
                                    <div class="flex flex-wrap gap-1">
                                        <%
                                        const categoryColors = {
                                            'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                            'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                                            'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                                            'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                            'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                            'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                            'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                            'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                            'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                            'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
                                        };

                                        const categoryNamesArray = question.category_names.split(',');
                                        categoryNamesArray.forEach(categoryName => {
                                            const category = categoryName.trim();
                                            const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                                        %>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                                <%= category %>
                                            </span>
                                        <% }); %>
                                    </div>
                                <% } else { %>
                                    <span class="text-gray-400">None</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.exam_name) { %>
                                    <%= question.exam_name %>
                                <% } else { %>
                                    <span class="text-gray-400">None</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (question.section_name) { %>
                                    <%= question.section_name %>
                                <% } else { %>
                                    <span class="text-gray-400">None</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= new Date(question.deleted_at).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <form action="/admin/questions/restore/<%= question.question_id %>" method="POST" class="inline">
                                        <button type="submit" class="text-green-600 hover:text-green-900">Restore</button>
                                    </form>
                                    <button onclick="confirmPermanentDelete(<%= question.question_id %>)" class="text-red-600 hover:text-red-900">Delete Permanently</button>
                                </div>
                            </td>
                        </tr>
                        <% }); %>
                    <% } else { %>
                        <tr>
                            <td colspan="9" class="px-6 py-4 text-center text-sm text-gray-500">No deleted questions found</td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
    <div class="flex justify-between items-center mt-6">
        <div class="text-sm text-gray-600">
            Showing <%= Math.min((pagination.currentPage - 1) * pagination.perPage + 1, pagination.totalItems) %> to <%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems) %> of <%= pagination.totalItems %> entries
        </div>
        <div class="flex space-x-1">
            <% if (pagination.currentPage > 1) { %>
                <a href="<%= pagination.baseUrl %>?page=1&perPage=<%= pagination.perPage %>" class="px-3 py-1 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M7.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L3.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </a>
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.currentPage - 1 %>&perPage=<%= pagination.perPage %>" class="px-3 py-1 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
            <% } %>

            <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                <a href="<%= pagination.baseUrl %>?page=<%= i %>&perPage=<%= pagination.perPage %>" class="px-3 py-1 rounded border <%= i === pagination.currentPage ? 'bg-blue-600 text-white' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50' %>">
                    <%= i %>
                </a>
            <% } %>

            <% if (pagination.currentPage < pagination.totalPages) { %>
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.currentPage + 1 %>&perPage=<%= pagination.perPage %>" class="px-3 py-1 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
                <a href="<%= pagination.baseUrl %>?page=<%= pagination.totalPages %>&perPage=<%= pagination.perPage %>" class="px-3 py-1 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 6.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M12.293 15.707a1 1 0 010-1.414L16.586 10l-4.293-3.293a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
            <% } %>
        </div>
    </div>
    <% } %>
</div>

<!-- Permanent Delete Confirmation Modal -->
<div id="permanentDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-8 max-w-md w-full">
        <h2 class="text-xl font-bold mb-4">Confirm Permanent Deletion</h2>
        <p class="mb-6 text-gray-700">Are you sure you want to permanently delete this question? This action cannot be undone.</p>
        <div class="flex justify-end space-x-4">
            <button onclick="closePermanentDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">Cancel</button>
            <form id="permanentDeleteForm" action="/admin/questions/permanent-delete/0" method="POST">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Delete Permanently</button>
            </form>
        </div>
    </div>
</div>

<script>
function updatePerPage(perPage) {
    window.location.href = '<%= pagination.baseUrl %>?page=1&perPage=' + perPage;
}

function confirmPermanentDelete(questionId) {
    document.getElementById('permanentDeleteForm').action = `/admin/questions/permanent-delete/${questionId}`;
    document.getElementById('permanentDeleteModal').classList.remove('hidden');
}

function closePermanentDeleteModal() {
    document.getElementById('permanentDeleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('permanentDeleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePermanentDeleteModal();
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePermanentDeleteModal();
    }
});
</script>

