
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <div class="flex space-x-2">
            <a href="/admin/repair/history/send" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                Send Item for Repair
            </a>
            <a href="/admin/repair/history/receive" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
                </svg>
                Receive Item
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
        <form action="/admin/repair/history" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="sent" <%= query.status === 'sent' ? 'selected' : '' %>>Sent</option>
                    <option value="in_progress" <%= query.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                    <option value="completed" <%= query.status === 'completed' ? 'selected' : '' %>>Completed</option>
                    <option value="cancelled" <%= query.status === 'cancelled' ? 'selected' : '' %>>Cancelled</option>
                </select>
            </div>

            <div>
                <label for="vendor" class="block text-sm font-medium text-gray-700 mb-1">Vendor</label>
                <select id="vendor" name="vendor" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    <option value="">All Vendors</option>
                    <% vendors.forEach(vendor => { %>
                        <option value="<%= vendor.vendor_id %>" <%= query.vendor == vendor.vendor_id ? 'selected' : '' %>><%= vendor.name %></option>
                    <% }); %>
                </select>
            </div>

            <div>
                <label for="item" class="block text-sm font-medium text-gray-700 mb-1">Item</label>
                <select id="item" name="item" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    <option value="">All Items</option>
                    <% items.forEach(item => { %>
                        <option value="<%= item.item_id %>" <%= query.item == item.item_id ? 'selected' : '' %>><%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %></option>
                    <% }); %>
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">Apply Filters</button>
                <a href="/admin/repair/history" class="ml-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md">Reset</a>
            </div>
        </form>
    </div>

    <% if (repairs.length === 0) { %>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <p class="text-gray-600">No repair history found. Use the buttons above to send or receive items for repair.</p>
        </div>
    <% } else { %>
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return Date</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% repairs.forEach(repair => { %>
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><%= repair.item_name %></div>
                                <div class="text-xs text-gray-500"><%= repair.serial_number || 'No S/N' %></div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><%= repair.vendor_name %></div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><%= repair.sent_date %></div>
                                <div class="text-xs text-gray-500">By <%= repair.sent_by_name %></div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <% if (repair.returned_date) { %>
                                    <div class="text-sm text-gray-900"><%= repair.returned_date %></div>
                                    <div class="text-xs text-gray-500">By <%= repair.received_by_name || 'N/A' %></div>
                                <% } else { %>
                                    <div class="text-sm text-gray-500">Not returned yet</div>
                                    <% if (repair.expected_return_date) { %>
                                        <div class="text-xs text-gray-500">Expected: <%= repair.expected_return_date %></div>
                                    <% } %>
                                <% } %>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <% if (repair.status === 'sent') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Sent</span>
                                <% } else if (repair.status === 'in_progress') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">In Progress</span>
                                <% } else if (repair.status === 'completed') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                <% } else if (repair.status === 'cancelled') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>
                                <% } %>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="/admin/repair/history/<%= repair.repair_id %>" class="text-blue-600 hover:text-blue-900">View</a>
                                    <% if (repair.status === 'sent') { %>
                                        <form action="/admin/repair/history/<%= repair.repair_id %>/status" method="POST" class="inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="text-blue-600 hover:text-blue-900">Mark In Progress</button>
                                        </form>
                                    <% } %>
                                    <% if (repair.status === 'sent' || repair.status === 'in_progress') { %>
                                        <a href="/admin/repair/history/receive?repair_id=<%= repair.repair_id %>" class="text-green-600 hover:text-green-900">Receive</a>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <% if (pagination.totalPages > 1) { %>
            <div class="flex justify-center mt-6">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <% if (pagination.currentPage > 1) { %>
                        <a href="/admin/repair/history?page=<%= pagination.currentPage - 1 %>&status=<%= query.status || '' %>&vendor=<%= query.vendor || '' %>&item=<%= query.item || '' %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    <% } %>

                    <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                        <a href="/admin/repair/history?page=<%= i %>&status=<%= query.status || '' %>&vendor=<%= query.vendor || '' %>&item=<%= query.item || '' %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <%= pagination.currentPage === i ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50' %>">
                            <%= i %>
                        </a>
                    <% } %>

                    <% if (pagination.currentPage < pagination.totalPages) { %>
                        <a href="/admin/repair/history?page=<%= pagination.currentPage + 1 %>&status=<%= query.status || '' %>&vendor=<%= query.vendor || '' %>&item=<%= query.item || '' %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    <% } %>
                </nav>
            </div>
        <% } %>
    <% } %>
</div>

<%- include('../../partials/admin-footer') %>
