

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/admin/repair/history" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Repair History
        </a>
    </div>

    <% if (repairs.length === 0) { %>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <p class="text-gray-600">No items are currently out for repair.</p>
            <a href="/admin/repair/history/send" class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">Send Item for Repair</a>
        </div>
    <% } else { %>
        <div class="bg-white rounded-lg shadow-md p-6">
            <form action="/admin/repair/history/receive" method="POST">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="repair_id" class="block text-sm font-medium text-gray-700 mb-1">Select Item to Receive *</label>
                        <select id="repair_id" name="repair_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                            <option value="">-- Select Item --</option>
                            <% repairs.forEach(repair => { %>
                                <option value="<%= repair.repair_id %>">
                                    <%= repair.item_name %> <%= repair.serial_number ? `(${repair.serial_number})` : '' %> -
                                    Sent to <%= repair.vendor_name %> on <%= repair.sent_date %>
                                    <% if (repair.expected_return_date && repair.expected_return_date !== 'Not specified') { %>
                                        (Expected return: <%= repair.expected_return_date %>)
                                    <% } %>
                                </option>
                            <% }); %>
                        </select>
                    </div>

                    <div id="itemDetails" class="hidden md:col-span-2 border border-gray-200 rounded-md p-4 mb-4 bg-gray-50">
                        <h4 class="font-medium text-gray-700 mb-2">Item Details</h4>
                        <div id="selectedItemDetails" class="text-sm text-gray-600">
                            <!-- Details will be populated by JavaScript -->
                        </div>
                    </div>

                    <div>
                        <label for="returned_date" class="block text-sm font-medium text-gray-700 mb-1">Return Date *</label>
                        <input type="date" id="returned_date" name="returned_date" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="<%= new Date().toISOString().split('T')[0] %>">
                    </div>

                    <div>
                        <label for="repair_cost" class="block text-sm font-medium text-gray-700 mb-1">Repair Cost</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">₹</span>
                            </div>
                            <input type="number" id="repair_cost" name="repair_cost" step="0.01" min="0" class="w-full pl-7 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="0.00">
                        </div>
                    </div>

                    <div class="md:col-span-2">
                        <label for="repair_details" class="block text-sm font-medium text-gray-700 mb-1">Repair Details</label>
                        <textarea id="repair_details" name="repair_details" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Details of repairs performed"></textarea>
                    </div>

                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Any additional notes about this repair"></textarea>
                    </div>

                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" id="require_condition_check" name="require_condition_check" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                            <label for="require_condition_check" class="ml-2 block text-sm text-gray-700">Perform detailed condition check after receiving</label>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <a href="/admin/repair/history" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md mr-2">Cancel</a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md">Receive Item</button>
                </div>
            </form>
        </div>
    <% } %>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const repairInput = document.getElementById('repair_id');

                if (!repairInput.value) {
                    e.preventDefault();
                    alert('Please select an item to receive');
                    repairInput.focus();
                }
            });
        }

        // Show item details when selected
        const repairSelect = document.getElementById('repair_id');
        if (repairSelect) {
            repairSelect.addEventListener('change', function() {
                const itemDetails = document.getElementById('itemDetails');
                const selectedItemDetails = document.getElementById('selectedItemDetails');

                if (this.value) {
                    // Get the selected option text
                    const selectedText = this.options[this.selectedIndex].text;
                    selectedItemDetails.textContent = selectedText;
                    itemDetails.classList.remove('hidden');
                } else {
                    itemDetails.classList.add('hidden');
                }
            });
        }
    });
</script>

<%- include('../../partials/admin-footer') %>
