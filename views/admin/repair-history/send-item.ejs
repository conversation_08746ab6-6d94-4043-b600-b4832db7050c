<%- include('../../partials/admin-header') %>

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/admin/repair/history" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Repair History
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form action="/admin/repair/history/send" method="POST">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Select Item *</label>
                    <select id="item_id" name="item_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">-- Select Item --</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>"><%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %> - <%= item.model || 'No Model' %></option>
                        <% }); %>
                    </select>
                </div>

                <div class="md:col-span-2">
                    <label for="vendor_id" class="block text-sm font-medium text-gray-700 mb-1">Select Vendor *</label>
                    <select id="vendor_id" name="vendor_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">-- Select Vendor --</option>
                        <% vendors.forEach(vendor => { %>
                            <option value="<%= vendor.vendor_id %>"><%= vendor.name %> <%= vendor.specialization ? `(${vendor.specialization})` : '' %></option>
                        <% }); %>
                    </select>
                </div>

                <div>
                    <label for="sent_date" class="block text-sm font-medium text-gray-700 mb-1">Sent Date *</label>
                    <input type="date" id="sent_date" name="sent_date" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" value="<%= new Date().toISOString().split('T')[0] %>">
                </div>

                <div>
                    <label for="expected_return_date" class="block text-sm font-medium text-gray-700 mb-1">Expected Return Date</label>
                    <input type="date" id="expected_return_date" name="expected_return_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>

                <div class="md:col-span-2">
                    <label for="issue_description" class="block text-sm font-medium text-gray-700 mb-1">Issue Description *</label>
                    <textarea id="issue_description" name="issue_description" rows="4" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Describe the issue with the item"></textarea>
                </div>

                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Any additional notes about this repair"></textarea>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <a href="/admin/repair/history" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md mr-2">Cancel</a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">Send for Repair</button>
            </div>
        </form>
    </div>
</div>

<%- include('../../partials/admin-footer') %>
