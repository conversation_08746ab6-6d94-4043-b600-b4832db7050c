

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <div class="flex space-x-2">
            <a href="/admin/repair/history" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Repair History
            </a>
            <% if (repair.status === 'sent' || repair.status === 'in_progress') { %>
                <a href="/admin/repair/history/receive?repair_id=<%= repair.repair_id %>" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd" />
                    </svg>
                    Receive Item
                </a>
            <% } %>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Repair Details -->
        <div class="md:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Repair Information</h2>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Status</h3>
                    <p class="text-base text-gray-900">
                        <% if (repair.status === 'sent') { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Sent</span>
                        <% } else if (repair.status === 'in_progress') { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">In Progress</span>
                        <% } else if (repair.status === 'completed') { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                        <% } else if (repair.status === 'cancelled') { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>
                        <% } %>
                    </p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Item</h3>
                    <p class="text-base text-gray-900"><%= repair.item_name %></p>
                    <p class="text-sm text-gray-500"><%= repair.serial_number || 'No S/N' %> - <%= repair.model || 'No Model' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Vendor</h3>
                    <p class="text-base text-gray-900"><%= repair.vendor_name %></p>
                    <p class="text-sm text-gray-500"><%= repair.contact_person || '' %> <%= repair.phone ? `(${repair.phone})` : '' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Sent Date</h3>
                    <p class="text-base text-gray-900"><%= repair.sent_date %></p>
                    <p class="text-sm text-gray-500">By <%= repair.sent_by_name %></p>
                </div>

                <% if (repair.returned_date) { %>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Return Date</h3>
                        <p class="text-base text-gray-900"><%= repair.returned_date %></p>
                        <p class="text-sm text-gray-500">By <%= repair.received_by_name || 'N/A' %></p>
                    </div>
                <% } else if (repair.expected_return_date) { %>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Expected Return Date</h3>
                        <p class="text-base text-gray-900"><%= repair.expected_return_date %></p>
                    </div>
                <% } %>

                <% if (repair.repair_cost) { %>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Repair Cost</h3>
                        <p class="text-base text-gray-900">₹<%= parseFloat(repair.repair_cost).toFixed(2) %></p>
                    </div>
                <% } %>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Issue Description</h3>
                    <p class="text-base text-gray-900 whitespace-pre-line"><%= repair.issue_description %></p>
                </div>

                <% if (repair.repair_details) { %>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-500">Repair Details</h3>
                        <p class="text-base text-gray-900 whitespace-pre-line"><%= repair.repair_details %></p>
                    </div>
                <% } %>

                <% if (repair.notes) { %>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Notes</h3>
                        <p class="text-base text-gray-900 whitespace-pre-line"><%= repair.notes %></p>
                    </div>
                <% } %>
            </div>

            <% if (repair.status === 'sent' || repair.status === 'in_progress') { %>
                <div class="bg-white rounded-lg shadow-md p-6 mt-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Update Status</h2>

                    <form action="/admin/repair/history/<%= repair.repair_id %>/status" method="POST">
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">New Status</label>
                            <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                <option value="sent" <%= repair.status === 'sent' ? 'selected' : '' %>>Sent</option>
                                <option value="in_progress" <%= repair.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Status Notes</label>
                            <textarea id="notes" name="notes" rows="2" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Reason for status change"></textarea>
                        </div>

                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">Update Status</button>
                    </form>
                </div>
            <% } %>
        </div>

        <!-- Hardware Condition Data -->
        <div class="md:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Hardware Condition History</h2>

                <% if (Object.keys(conditionData).length === 0) { %>
                    <div class="text-center py-4">
                        <p class="text-gray-500">No condition data available for this item.</p>
                    </div>
                <% } else { %>
                    <div class="space-y-6">
                        <% Object.keys(conditionData).forEach(checkDate => { %>
                            <div class="border border-gray-200 rounded-md p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <h3 class="text-md font-medium text-gray-800">Condition Check: <%= checkDate %></h3>
                                    <span class="text-sm text-gray-500">
                                        By <%= conditionData[checkDate][0].checked_by_name || 'Unknown' %>
                                    </span>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <% conditionData[checkDate].forEach(condition => { %>
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 h-5 w-5 mt-0.5">
                                                <% if (condition.condition === 'good') { %>
                                                    <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                    </svg>
                                                <% } else if (condition.condition === 'fair') { %>
                                                    <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                <% } else if (condition.condition === 'poor') { %>
                                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                    </svg>
                                                <% } else { %>
                                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                                    </svg>
                                                <% } %>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900"><%= condition.display_name %></p>
                                                <p class="text-sm text-gray-500">
                                                    <%= condition.condition.charAt(0).toUpperCase() + condition.condition.slice(1) %>
                                                    <% if (condition.notes) { %>
                                                        - <%= condition.notes %>
                                                    <% } %>
                                                </p>
                                            </div>
                                        </div>
                                    <% }); %>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../../partials/admin-footer') %>
