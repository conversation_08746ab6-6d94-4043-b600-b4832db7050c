

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <a href="/admin/repair/vendors/add" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add New Vendor
        </a>
    </div>

    <% if (vendors.length === 0) { %>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <p class="text-gray-600">No repair vendors found. Click the button above to add a new vendor.</p>
        </div>
    <% } else { %>
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialization</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Repairs</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% vendors.forEach(vendor => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><%= vendor.name %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><%= vendor.contact_person || 'Not specified' %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><%= vendor.specialization || 'General' %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><%= vendor.repair_count || 0 %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (vendor.is_active) { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="/admin/repair/vendors/<%= vendor.vendor_id %>" class="text-blue-600 hover:text-blue-900">View</a>
                                    <a href="/admin/repair/vendors/<%= vendor.vendor_id %>/edit" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    <% if (vendor.repair_count === 0) { %>
                                        <form action="/admin/repair/vendors/<%= vendor.vendor_id %>/delete" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this vendor?');">
                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                        </form>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } %>
</div>

<%- include('../../partials/admin-footer') %>
