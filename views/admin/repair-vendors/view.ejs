

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <div class="flex space-x-2">
            <a href="/admin/repair/vendors" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Vendors
            </a>
            <a href="/admin/repair/vendors/<%= vendor.vendor_id %>/edit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                Edit Vendor
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Vendor Details -->
        <div class="md:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Vendor Information</h2>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Vendor Name</h3>
                    <p class="text-base text-gray-900"><%= vendor.name %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Contact Person</h3>
                    <p class="text-base text-gray-900"><%= vendor.contact_person || 'Not specified' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Phone</h3>
                    <p class="text-base text-gray-900"><%= vendor.phone || 'Not specified' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Email</h3>
                    <p class="text-base text-gray-900"><%= vendor.email || 'Not specified' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Specialization</h3>
                    <p class="text-base text-gray-900"><%= vendor.specialization || 'General' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Address</h3>
                    <p class="text-base text-gray-900 whitespace-pre-line"><%= vendor.address || 'Not specified' %></p>
                </div>

                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-500">Status</h3>
                    <p class="text-base text-gray-900">
                        <% if (vendor.is_active) { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                        <% } else { %>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                        <% } %>
                    </p>
                </div>

                <div>
                    <h3 class="text-sm font-medium text-gray-500">Notes</h3>
                    <p class="text-base text-gray-900 whitespace-pre-line"><%= vendor.notes || 'No notes' %></p>
                </div>
            </div>
        </div>

        <!-- Repair History -->
        <div class="md:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Repair History</h2>
                    <a href="/admin/repair/history/send?vendor=<%= vendor.vendor_id %>" class="bg-green-600 hover:bg-green-700 text-white font-medium py-1 px-3 rounded-md text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        Send Item for Repair
                    </a>
                </div>

                <% if (repairs.length === 0) { %>
                    <div class="text-center py-4">
                        <p class="text-gray-500">No repair history found for this vendor.</p>
                    </div>
                <% } else { %>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return Date</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <% repairs.forEach(repair => { %>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><%= repair.item_name %></div>
                                            <div class="text-xs text-gray-500"><%= repair.serial_number || 'No S/N' %></div>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm text-gray-900"><%= repair.sent_date %></div>
                                            <div class="text-xs text-gray-500">By <%= repair.sent_by_name %></div>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <% if (repair.returned_date) { %>
                                                <div class="text-sm text-gray-900"><%= repair.returned_date %></div>
                                                <div class="text-xs text-gray-500">By <%= repair.received_by_name || 'N/A' %></div>
                                            <% } else { %>
                                                <div class="text-sm text-gray-500">Not returned yet</div>
                                                <% if (repair.expected_return_date) { %>
                                                    <div class="text-xs text-gray-500">Expected: <%= repair.expected_return_date %></div>
                                                <% } %>
                                            <% } %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <% if (repair.status === 'sent') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Sent</span>
                                            <% } else if (repair.status === 'in_progress') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">In Progress</span>
                                            <% } else if (repair.status === 'completed') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                            <% } else if (repair.status === 'cancelled') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>
                                            <% } %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                            <a href="/admin/repair/history/<%= repair.repair_id %>" class="text-blue-600 hover:text-blue-900">View</a>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../../partials/admin-footer') %>
