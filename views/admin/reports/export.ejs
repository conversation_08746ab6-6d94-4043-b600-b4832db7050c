<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Export Reports</h1>
        <a href="/admin/reports" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            Back to Reports Dashboard
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Generate and Export Reports</h2>
        <p class="text-gray-600 mb-6">Select the type of report you want to generate and the export format.</p>

        <form id="reportForm" action="/admin/reports/generate" method="POST" class="space-y-6">
            <!-- Report Type Selection -->
            <div>
                <label for="report_type" class="block text-gray-700 text-sm font-bold mb-2">Report Type</label>
                <select id="report_type" name="report_type" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    <option value="">Select Report Type</option>
                    <option value="user_performance">User Performance Report</option>
                    <option value="test_analytics">Test Analytics Report</option>
                    <option value="question_analytics">Question Analytics Report</option>
                    <option value="category_performance">Category Performance Report</option>
                    <option value="user_activity">User Activity Report</option>
                    <option value="system_logs">System Logs Report</option>
                </select>
            </div>

            <!-- Date Range Selection -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="start_date" class="block text-gray-700 text-sm font-bold mb-2">Start Date</label>
                    <input type="date" id="start_date" name="start_date" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
                <div>
                    <label for="end_date" class="block text-gray-700 text-sm font-bold mb-2">End Date</label>
                    <input type="date" id="end_date" name="end_date" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
            </div>

            <!-- Additional Filters (shown/hidden based on report type) -->
            <div id="userFilters" class="hidden">
                <label for="user_id" class="block text-gray-700 text-sm font-bold mb-2">Select User</label>
                <select id="user_id" name="user_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">All Users</option>
                    <% users.forEach(user => { %>
                        <option value="<%= user.id %>"><%= user.username %> (<%= user.email %>)</option>
                    <% }); %>
                </select>
            </div>

            <div id="testFilters" class="hidden">
                <label for="exam_id" class="block text-gray-700 text-sm font-bold mb-2">Select Test</label>
                <select id="exam_id" name="exam_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">All Tests</option>
                    <% exams.forEach(exam => { %>
                        <option value="<%= exam.exam_id %>"><%= exam.exam_name %></option>
                    <% }); %>
                </select>
            </div>

            <div id="categoryFilters" class="hidden">
                <label for="category_id" class="block text-gray-700 text-sm font-bold mb-2">Select Category</label>
                <select id="category_id" name="category_id" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">All Categories</option>
                    <% categories.forEach(category => { %>
                        <option value="<%= category.category_id %>"><%= category.name %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Export Format Selection -->
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">Export Format</label>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="flex items-center">
                        <input type="radio" id="format_pdf" name="export_format" value="pdf" class="mr-2" checked>
                        <label for="format_pdf" class="flex items-center">
                            <svg class="w-6 h-6 mr-2 text-red-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                                <path d="M320 464C328.8 464 336 456.8 336 448V416H384V448C384 483.3 355.3 512 320 512H64C28.65 512 0 483.3 0 448V416H48V448C48 456.8 55.16 464 64 464H320zM256 160C238.3 160 224 145.7 224 128V48H64C55.16 48 48 55.16 48 64V192H0V64C0 28.65 28.65 0 64 0H229.5C246.5 0 262.7 6.743 274.7 18.75L365.3 109.3C377.3 121.3 384 137.5 384 154.5V192H336V160H256zM88 224C118.9 224 144 249.1 144 280C144 310.9 118.9 336 88 336H80V368H88C136.6 368 176 328.6 176 280C176 231.4 136.6 192 88 192H56C42.75 192 32 202.8 32 216V408C32 421.3 42.75 432 56 432H80V400H56V224H88zM224 224H288V256H224V304H288V336H224V400H288V432H224C210.7 432 200 421.3 200 408V248C200 234.8 210.7 224 224 224zM352 224V432H320V368H312C298.7 368 288 357.3 288 344V312C288 298.8 298.7 288 312 288H320V224H352zM320 320H312V336H320V320z"/>
                            </svg>
                            PDF
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="radio" id="format_excel" name="export_format" value="excel" class="mr-2">
                        <label for="format_excel" class="flex items-center">
                            <svg class="w-6 h-6 mr-2 text-green-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                                <path d="M224 128L224 0H48C21.49 0 0 21.49 0 48v416C0 490.5 21.49 512 48 512h288c26.51 0 48-21.49 48-48V160h-127.1C238.3 160 224 145.7 224 128zM272.1 264.4L224 344l48.99 79.61C279.6 434.3 271.9 448 259.4 448h-26.43c-5.557 0-10.71-2.883-13.63-7.617L192 396l-27.31 44.38C161.8 445.1 156.6 448 151.1 448H124.6c-12.52 0-20.19-13.73-13.63-24.39L160 344L111 264.4C104.4 253.7 112.1 240 124.6 240h26.43c5.557 0 10.71 2.883 13.63 7.613L192 292l27.31-44.39C222.2 242.9 227.4 240 232.9 240h26.43C271.9 240 279.6 253.7 272.1 264.4zM256 0v128h128L256 0z"/>
                            </svg>
                            Excel
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="radio" id="format_csv" name="export_format" value="csv" class="mr-2">
                        <label for="format_csv" class="flex items-center">
                            <svg class="w-6 h-6 mr-2 text-gray-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                                <path d="M224 128L224 0H48C21.49 0 0 21.49 0 48v416C0 490.5 21.49 512 48 512h288c26.51 0 48-21.49 48-48V160h-127.1C238.3 160 224 145.7 224 128zM272.1 264.4L224 344l48.99 79.61C279.6 434.3 271.9 448 259.4 448h-26.43c-5.557 0-10.71-2.883-13.63-7.617L192 396l-27.31 44.38C161.8 445.1 156.6 448 151.1 448H124.6c-12.52 0-20.19-13.73-13.63-24.39L160 344L111 264.4C104.4 253.7 112.1 240 124.6 240h26.43c5.557 0 10.71 2.883 13.63 7.613L192 292l27.31-44.39C222.2 242.9 227.4 240 232.9 240h26.43C271.9 240 279.6 253.7 272.1 264.4zM256 0v128h128L256 0z"/>
                            </svg>
                            CSV
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="radio" id="format_json" name="export_format" value="json" class="mr-2">
                        <label for="format_json" class="flex items-center">
                            <svg class="w-6 h-6 mr-2 text-yellow-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                                <path d="M0 64C0 28.65 28.65 0 64 0H229.5C246.5 0 262.7 6.743 274.7 18.75L365.3 109.3C377.3 121.3 384 137.5 384 154.5V448C384 483.3 355.3 512 320 512H64C28.65 512 0 483.3 0 448V64zM336 448V160H256C238.3 160 224 145.7 224 128V48H64C55.16 48 48 55.16 48 64V448C48 456.8 55.16 464 64 464H320C328.8 464 336 456.8 336 448zM80 224C80 202.3 97.53 184 120 184H168C190.1 184 208 202.3 208 224C208 245.7 190.1 264 168 264H152V304H168C190.1 304 208 322.3 208 344C208 365.7 190.1 384 168 384H120C97.53 384 80 365.7 80 344C80 330.7 85.03 318.7 93.41 310.2C85.03 301.7 80 289.7 80 276V224zM120 344H168V304H120V344zM120 224V264H168V224H120zM280 240C280 231.2 287.2 224 296 224C304.8 224 312 231.2 312 240V376C312 389.3 301.3 400 288 400H248C239.2 400 232 392.8 232 384C232 375.2 239.2 368 248 368H280V240z"/>
                            </svg>
                            JSON
                        </label>
                    </div>
                </div>
            </div>

            <!-- Delivery Options -->
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">Delivery Options</label>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <input type="checkbox" id="download" name="delivery_options" value="download" class="mr-2" checked>
                        <label for="download" class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                <path d="M480 352h-133.5l-45.25 45.25C289.2 409.3 273.1 416 256 416s-33.16-6.656-45.25-18.75L165.5 352H32c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h448c17.67 0 32-14.33 32-32v-96C512 366.3 497.7 352 480 352zM432 456c-13.2 0-24-10.8-24-24c0-13.2 10.8-24 24-24s24 10.8 24 24C456 445.2 445.2 456 432 456zM233.4 374.6C239.6 380.9 247.8 384 256 384s16.38-3.125 22.62-9.375l128-128c12.49-12.5 12.49-32.75 0-45.25c-12.5-12.5-32.76-12.5-45.25 0L288 274.8V32c0-17.67-14.33-32-32-32C238.3 0 224 14.33 224 32v242.8L150.6 201.4c-12.49-12.5-32.75-12.5-45.25 0c-12.49 12.5-12.49 32.75 0 45.25L233.4 374.6z"/>
                            </svg>
                            Download
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="email" name="delivery_options" value="email" class="mr-2">
                        <label for="email" class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                <path d="M464 64C490.5 64 512 85.49 512 112C512 127.1 504.9 141.3 492.8 150.4L275.2 313.6C263.8 322.1 248.2 322.1 236.8 313.6L19.2 150.4C7.113 141.3 0 127.1 0 112C0 85.49 21.49 64 48 64H464zM217.6 339.2C240.4 356.3 271.6 356.3 294.4 339.2L512 176V384C512 419.3 483.3 448 448 448H64C28.65 448 0 419.3 0 384V176L217.6 339.2z"/>
                            </svg>
                            Email
                        </label>
                    </div>
                    <div id="emailField" class="ml-7 hidden">
                        <input type="email" id="email_address" name="email_address" placeholder="Enter email address" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="share" name="delivery_options" value="share" class="mr-2">
                        <label for="share" class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                <path d="M503.7 226.2l-176 151.1c-15.38 13.3-39.69 2.545-39.69-18.16V272.1C132.9 274.3 66.06 312.8 111.4 457.8c5.031 16.09-14.41 28.56-28.06 18.62C39.59 444.6 0 383.8 0 322.3c0-152.2 127.4-184.4 288-186.3V56.02c0-20.67 24.28-31.46 39.69-18.16l176 151.1C514.8 199.4 514.8 216.6 503.7 226.2z"/>
                            </svg>
                            Share via System
                        </label>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4">
                <button type="button" id="showReportBtn" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                    </svg>
                    Show Report
                </button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Generate Report
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Reports Section -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4">Recent Reports</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Format</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated On</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (recentReports && recentReports.length > 0) { %>
                        <% recentReports.forEach(report => { %>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap"><%= report.name %></td>
                                <td class="px-6 py-4 whitespace-nowrap"><%= report.type %></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        <%= report.format === 'pdf' ? 'bg-red-100 text-red-800' :
                                           report.format === 'excel' ? 'bg-green-100 text-green-800' :
                                           report.format === 'csv' ? 'bg-gray-100 text-gray-800' :
                                           'bg-yellow-100 text-yellow-800' %>">
                                        <%= report.format.toUpperCase() %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap"><%= report.generated_at %></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex space-x-2">
                                        <a href="/admin/reports/view/<%= report.id %>" class="text-purple-600 hover:text-purple-900" title="View Report">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                        <a href="/admin/reports/download/<%= report.id %>" class="text-blue-600 hover:text-blue-900" title="Download Report">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                        <a href="/admin/reports/email/<%= report.id %>" class="text-green-600 hover:text-green-900" title="Email Report">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                            </svg>
                                        </a>
                                        <a href="/admin/reports/share/<%= report.id %>" class="text-indigo-600 hover:text-indigo-900" title="Share Report">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                                            </svg>
                                        </a>
                                        <button onclick="deleteReport(<%= report.id %>)" class="text-red-600 hover:text-red-900" title="Delete Report">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    <% } else { %>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">No reports generated yet</td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const reportTypeSelect = document.getElementById('report_type');
        const userFilters = document.getElementById('userFilters');
        const testFilters = document.getElementById('testFilters');
        const categoryFilters = document.getElementById('categoryFilters');
        const emailCheckbox = document.getElementById('email');
        const emailField = document.getElementById('emailField');

        // Show/hide filters based on report type
        reportTypeSelect.addEventListener('change', function() {
            const reportType = this.value;

            // Hide all filters first
            userFilters.classList.add('hidden');
            testFilters.classList.add('hidden');
            categoryFilters.classList.add('hidden');

            // Show relevant filters based on report type
            if (reportType === 'user_performance' || reportType === 'user_activity') {
                userFilters.classList.remove('hidden');
            }

            if (reportType === 'test_analytics' || reportType === 'user_performance') {
                testFilters.classList.remove('hidden');
            }

            if (reportType === 'question_analytics' || reportType === 'category_performance') {
                categoryFilters.classList.remove('hidden');
                testFilters.classList.remove('hidden');
            }
        });

        // Show/hide email field based on email checkbox
        emailCheckbox.addEventListener('change', function() {
            if (this.checked) {
                emailField.classList.remove('hidden');
            } else {
                emailField.classList.add('hidden');
            }
        });

        // Form validation
        document.getElementById('reportForm').addEventListener('submit', function(e) {
            const reportType = reportTypeSelect.value;
            const emailChecked = emailCheckbox.checked;
            const emailAddress = document.getElementById('email_address').value;

            if (!reportType) {
                e.preventDefault();
                alert('Please select a report type');
                return;
            }

            if (emailChecked && !emailAddress) {
                e.preventDefault();
                alert('Please enter an email address for email delivery');
                return;
            }

            if (emailChecked && !validateEmail(emailAddress)) {
                e.preventDefault();
                alert('Please enter a valid email address');
                return;
            }
        });

        // Email validation function
        function validateEmail(email) {
            const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }

        // Handle Show Report button click
        document.getElementById('showReportBtn').addEventListener('click', function() {
            const reportType = reportTypeSelect.value;
            const emailChecked = emailCheckbox.checked;
            const emailAddress = document.getElementById('email_address').value;

            if (!reportType) {
                alert('Please select a report type');
                return;
            }

            if (emailChecked && !emailAddress) {
                alert('Please enter an email address for email delivery');
                return;
            }

            if (emailChecked && !validateEmail(emailAddress)) {
                alert('Please enter a valid email address');
                return;
            }

            // Create a hidden form for submission
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/reports/generate';
            form.style.display = 'none';

            // Add all form fields
            const formData = new FormData(document.getElementById('reportForm'));
            formData.append('show_report', 'true'); // Add a flag to indicate this is a show report request

            for (const [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }

            // Submit the form
            document.body.appendChild(form);
            form.submit();
        });

        // Delete report confirmation
        window.deleteReport = function(reportId) {
            if (confirm('Are you sure you want to delete this report?')) {
                fetch(`/admin/reports/delete/${reportId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Error deleting report: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the report');
                });
            }
        };
    });
</script>
