<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <!-- Total Attempts Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Total Attempts</div>
                    <div class="text-lg font-semibold text-gray-900"><%= attemptStats.total_attempts %></div>
                </div>
            </div>
        </div>

        <!-- Completed Attempts Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Completed</div>
                    <div class="text-lg font-semibold text-gray-900"><%= statusStats.completed_count %></div>
                </div>
            </div>
        </div>

        <!-- In Progress Attempts Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">In Progress</div>
                    <div class="text-lg font-semibold text-gray-900"><%= statusStats.in_progress_count %></div>
                </div>
            </div>
        </div>

        <!-- Pass Attempts Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Pass</div>
                    <div class="text-lg font-semibold text-gray-900"><%= resultStats.pass_count %></div>
                </div>
            </div>
        </div>

        <!-- Fail Attempts Card -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 rounded-full p-3">
                    <svg class="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">Fail</div>
                    <div class="text-lg font-semibold text-gray-900"><%= resultStats.fail_count %></div>
                </div>
            </div>
        </div>
    </div>

    

    <!-- Advanced Filters -->
    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Advanced Filters</h2>
        </div>
        <div class="p-6">
            <form action="" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <!-- Exam Filter -->
                <div>
                    <label for="exam_id" class="block text-sm font-medium text-gray-700 mb-1">Exam</label>
                    <select id="exam_id" name="exam_id" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Exams</option>
                        <% exams.forEach(exam => { %>
                            <option value="<%= exam.exam_id %>" <%= filters.exam_id == exam.exam_id ? 'selected' : '' %>><%= exam.exam_name %></option>
                        <% }); %>
                    </select>
                </div>

                <!-- User Filter -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">User</label>
                    <select id="user_id" name="user_id" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Users</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>" <%= filters.user_id == user.id ? 'selected' : '' %>><%= user.username %> (<%= user.name %>)</option>
                        <% }); %>
                    </select>
                </div>

                <!-- Date Range Filters -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="<%= filters.start_date %>" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="<%= filters.end_date %>" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Statuses</option>
                        <option value="completed" <%= filters.status === 'completed' ? 'selected' : '' %>>Completed</option>
                        <option value="in_progress" <%= filters.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                        <option value="abandoned" <%= filters.status === 'abandoned' ? 'selected' : '' %>>Abandoned</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="lg:col-span-5 flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Apply Filters
                    </button>
                    <a href="/admin/reports" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Status Filter Badges -->
    <div class="mb-4 flex space-x-2">
        <span class="text-sm font-medium text-gray-700 mr-2">Quick Filter:</span>
        <a href="/admin/reports<%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').map((k, i) => `${i === 0 ? '?' : '&'}${k}=${filters[k]}`).join('') %>"
           class="px-3 py-1 rounded-full text-xs font-medium <%= !filters.status && !filters.result_type ? 'bg-blue-100 text-blue-800 ring-2 ring-blue-600' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
            All (<%= attemptStats.total_attempts %>)
        </a>
        <a href="/admin/reports<%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').map((k, i) => `${i === 0 ? '?' : '&'}${k}=${filters[k]}`).join('') %><%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').length > 0 ? '&' : '?' %>result_type=pass"
           class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 <%= filters.result_type === 'pass' ? 'ring-2 ring-green-600' : '' %>">
            PASS (<%= resultStats.pass_count %>)
        </a>
        <a href="/admin/reports<%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').map((k, i) => `${i === 0 ? '?' : '&'}${k}=${filters[k]}`).join('') %><%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').length > 0 ? '&' : '?' %>result_type=fail"
           class="px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 <%= filters.result_type === 'fail' ? 'ring-2 ring-red-600' : '' %>">
            FAIL (<%= resultStats.fail_count %>)
        </a>
        <a href="/admin/reports<%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').map((k, i) => `${i === 0 ? '?' : '&'}${k}=${filters[k]}`).join('') %><%= Object.keys(filters).filter(k => k !== 'status' && k !== 'result_type').length > 0 ? '&' : '?' %>status=in_progress"
           class="px-3 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800 hover:bg-gray-300 <%= filters.status === 'in_progress' ? 'ring-2 ring-gray-600' : '' %>">
            TBD (<%= resultStats.tbd_count %>)
        </a>
    </div>

    <!-- Recent Attempts Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">Recent Attempts</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seq</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Result</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="recentAttemptsBody">
                    <% recentAttempts.forEach((attempt, index) => {
                        // Format date as DD-MMM-YYYY HH:MM:SS
                        const attemptDate = new Date(attempt.attempt_date);
                        const formattedDate = `${('0' + attemptDate.getDate()).slice(-2)}-${attemptDate.toLocaleString('default', { month: 'short' })}-${attemptDate.getFullYear()} ${('0' + attemptDate.getHours()).slice(-2)}:${('0' + attemptDate.getMinutes()).slice(-2)}:${('0' + attemptDate.getSeconds()).slice(-2)}`;
                        // Calculate sequence number based on pagination
                        const seqNum = (pagination.currentPage - 1) * (parseInt(query?.perPage) || 10) + index + 1;
                    %>
                        <tr>
                            <td class="px-6 py-4"><%= seqNum %></td>
                            <td class="px-6 py-4"><%= attempt.username %></td>
                            <td class="px-6 py-4"><%= attempt.exam_name %></td>
                            <td class="px-6 py-4">
                                <% if (attempt.status === 'completed') { %>
                                    <%= attempt.total_marks_obtained ? Math.round(attempt.total_marks_obtained * 100) / 100 : 0 %>/<%= attempt.total_possible_marks ? Math.round(attempt.total_possible_marks * 100) / 100 : 0 %> (<%= attempt.total_score %>%)
                                    <small class="text-gray-500">(Pass: <%= attempt.passing_marks %>%)</small>
                                <% } else { %>
                                    In Progress
                                <% } %>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    <%= attempt.pass_status === 'pass' ? 'bg-green-100 text-green-800' :
                                       attempt.pass_status === 'fail' ? 'bg-red-100 text-red-800' :
                                       'bg-gray-200 text-gray-800' %>">
                                    <%= attempt.pass_status.toUpperCase() %>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    <%= attempt.status === 'completed' ? 'bg-green-100 text-green-800' : attempt.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                    <%= attempt.status %>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <%= attempt.attempted_questions || 0 %>/<%= attempt.total_questions || 0 %>
                                <% if (attempt.total_questions > 0) { %>
                                    (<%= Math.round((attempt.attempted_questions / attempt.total_questions) * 100) %>%)
                                <% } %>
                            </td>
                            <td class="px-6 py-4"><%= formattedDate %></td>
                            <td class="px-6 py-4">
                                <button class="text-blue-600 hover:text-blue-900" onclick="toggleSectionDetails(<%= attempt.attempt_id %>)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                        <!-- Section-wise details row -->
                        <tr id="section-details-<%= attempt.attempt_id %>" class="hidden bg-gray-50">
                            <td colspan="9" class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <h4 class="font-semibold mb-2">Section-wise Attempt Details:</h4>
                                    <% if (attempt.sectionStats && attempt.sectionStats.length > 0) { %>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <% attempt.sectionStats.forEach(section => { %>
                                                <div class="border rounded p-3 bg-white">
                                                    <h5 class="font-medium"><%= section.section_name || 'Unnamed Section' %></h5>
                                                    <p>Attempted: <%= section.attempted_questions || 0 %>/<%= section.total_questions || 0 %> questions</p>
                                                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: <%= section.attempt_percentage || 0 %>%"></div>
                                                    </div>
                                                    <p class="text-xs mt-1"><%= section.attempt_percentage || 0 %>% attempted</p>

                                                    <div class="mt-3 pt-2 border-t border-gray-200">
                                                        <p><span class="text-purple-600 font-medium">Marks:</span> <%= parseFloat(section.marks_obtained || 0).toFixed(2) %>/<%= parseFloat(section.total_marks || 0).toFixed(2) %></p>
                                                        <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                                            <div class="bg-purple-600 h-2.5 rounded-full" style="width: <%= section.marks_percentage || 0 %>%"></div>
                                                        </div>
                                                        <p class="text-xs mt-1 text-purple-600"><%= section.marks_percentage || 0 %>% marks</p>
                                                    </div>
                                                </div>
                                            <% }); %>
                                        </div>
                                    <% } else { %>
                                        <p>No section data available</p>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <!-- Enhanced Pagination Component -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <!-- Records per page -->
            <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600">Show:</label>
                <select name="perPage" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="updatePerPage(this.value)">
                    <option value="10" <%= parseInt(query?.perPage) === 10 || !query?.perPage ? 'selected' : '' %>>10</option>
                    <option value="25" <%= parseInt(query?.perPage) === 25 ? 'selected' : '' %>>25</option>
                    <option value="50" <%= parseInt(query?.perPage) === 50 ? 'selected' : '' %>>50</option>
                    <option value="100" <%= parseInt(query?.perPage) === 100 ? 'selected' : '' %>>100</option>
                </select>
                <span class="text-sm text-gray-600">entries</span>
            </div>

            <div class="flex-1 flex items-center justify-center sm:justify-end">
                <!-- Showing entries info -->
                <div class="text-sm text-gray-700 mr-4">
                    <% const perPage = parseInt(query?.perPage) || 10; %>
                    <% const start = (pagination.currentPage - 1) * perPage + 1; %>
                    <% const end = Math.min(start + perPage - 1, pagination.totalItems); %>
                    Showing <span class="font-medium"><%= start %></span> to <span class="font-medium"><%= end %></span> of
                    <span class="font-medium"><%= pagination.totalItems %></span> entries
                    <% if (Object.keys(filters).some(k => filters[k] && k !== 'page' && k !== 'perPage')) { %>
                        (filtered from <span class="font-medium"><%= attemptStats.total_attempts %></span> total records)
                    <% } %>
                </div>

                <!-- Pagination Controls -->
                <div class="flex items-center space-x-2">
                    <!-- First Page Button -->
                    <button onclick="goToPage(1)"
                            <%= (pagination?.currentPage || 1) <= 1 ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">First</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="11 17 6 12 11 7"></polyline>
                            <polyline points="18 17 13 12 18 7"></polyline>
                        </svg>
                    </button>

                    <!-- Previous Page Button -->
                    <button onclick="goToPage('<%= ((pagination && pagination.currentPage) || 1) > 1 ? (pagination.currentPage - 1) : 1 %>')"
                            <%= (pagination?.currentPage || 1) <= 1 ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- Jump to Page Dropdown -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Page</span>
                        <select id="jumpToPage"
                                class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 w-20"
                                onchange="goToPage(this.value)">
                            <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                                <option value="<%= i %>" <%= pagination.currentPage === i ? 'selected' : '' %>><%= i %></option>
                            <% } %>
                        </select>
                        <span class="text-sm text-gray-600">of <%= pagination.totalPages %></span>
                    </div>

                    <!-- Next Page Button -->
                    <button onclick="goToPage('<%= ((pagination && pagination.currentPage) || 1) + 1 <= (pagination && pagination.totalPages || 1) ? ((pagination && pagination.currentPage) || 1) + 1 : (pagination && pagination.totalPages || 1) %>')"
                            <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- Last Page -->
                    <button onclick="goToPage('<%= pagination?.totalPages || 1 %>')"
                            <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'disabled' : '' %>
                            class="relative inline-flex items-center px-2 py-2 rounded-md border <%= (pagination?.currentPage || 1) >= (pagination?.totalPages || 1) ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                        <span class="sr-only">Last</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="13 7 18 12 13 17"></polyline>
                            <polyline points="6 7 11 12 6 17"></polyline>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for toggling section details -->
    <script>
        function toggleSectionDetails(attemptId) {
            const detailsRow = document.getElementById(`section-details-${attemptId}`);
            if (detailsRow.classList.contains('hidden')) {
                detailsRow.classList.remove('hidden');
            } else {
                detailsRow.classList.add('hidden');
            }
        }
    </script>

    <!-- JavaScript for pagination and filtering -->
    <script>
        function updatePerPage(value) {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('perPage', value);
            currentUrl.searchParams.set('page', 1); // Reset to first page when changing items per page
            window.location.href = currentUrl.toString();
        }

        function goToPage(page) {
            const currentUrl = new URL(window.location.href);
            const maxPage = '<%= pagination?.totalPages || 1 %>';
            page = parseInt(page);
            if (page >= 1 && page <= maxPage) {
                currentUrl.searchParams.set('page', page);
                window.location.href = currentUrl.toString();
            }
        }

        // Initialize page dropdown with current page
        document.addEventListener('DOMContentLoaded', function() {
            const jumpToPage = document.getElementById('jumpToPage');
            if (jumpToPage) {
                jumpToPage.value = '<%= pagination?.currentPage || 1 %>';
            }
        });
    </script>
</div>
