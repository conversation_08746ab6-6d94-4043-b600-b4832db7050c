<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">View Report: <%= report.name %></h1>
        <div class="flex space-x-2">
            <a href="/admin/reports/download/<%= report.id %>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Download
            </a>
            <a href="/admin/reports/export" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                Back to Reports
            </a>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase">Report Type</h3>
                <p class="mt-1"><%= report.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></p>
            </div>
            <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase">Format</h3>
                <p class="mt-1">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        <%= report.format === 'pdf' ? 'bg-red-100 text-red-800' : 
                           report.format === 'excel' ? 'bg-green-100 text-green-800' : 
                           report.format === 'csv' ? 'bg-gray-100 text-gray-800' : 
                           'bg-yellow-100 text-yellow-800' %>">
                        <%= report.format.toUpperCase() %>
                    </span>
                </p>
            </div>
            <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase">Generated On</h3>
                <p class="mt-1"><%= new Date(report.generated_at).toLocaleString() %></p>
            </div>
        </div>

        <div class="border-t border-gray-200 pt-4">
            <h2 class="text-lg font-semibold mb-4">Report Content</h2>
            
            <% if (contentType === 'json') { %>
                <div class="bg-gray-800 text-white p-4 rounded-lg overflow-auto max-h-[600px]">
                    <pre class="whitespace-pre-wrap"><%= content %></pre>
                </div>
            <% } else if (contentType === 'csv') { %>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <% 
                            const lines = content.trim().split('\n');
                            const headers = lines[0].split(',');
                        %>
                        <thead class="bg-gray-50">
                            <tr>
                                <% headers.forEach(header => { %>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <%= header.replace(/"/g, '') %>
                                    </th>
                                <% }); %>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% for (let i = 1; i < lines.length; i++) { %>
                                <tr>
                                    <% 
                                        // Handle CSV parsing with quoted values containing commas
                                        let row = [];
                                        let inQuote = false;
                                        let currentValue = '';
                                        
                                        for (let j = 0; j < lines[i].length; j++) {
                                            const char = lines[i][j];
                                            
                                            if (char === '"') {
                                                inQuote = !inQuote;
                                            } else if (char === ',' && !inQuote) {
                                                row.push(currentValue.replace(/"/g, ''));
                                                currentValue = '';
                                            } else {
                                                currentValue += char;
                                            }
                                        }
                                        
                                        // Add the last value
                                        row.push(currentValue.replace(/"/g, ''));
                                        
                                        // If simple split works and gives the right number of columns, use that instead
                                        const simpleSplit = lines[i].split(',');
                                        if (simpleSplit.length === headers.length) {
                                            row = simpleSplit;
                                        }
                                    %>
                                    
                                    <% row.forEach(cell => { %>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%= cell.replace(/"/g, '') %>
                                        </td>
                                    <% }); %>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="bg-gray-100 p-4 rounded-lg overflow-auto max-h-[600px]">
                    <pre class="whitespace-pre-wrap"><%= content %></pre>
                </div>
            <% } %>
        </div>
    </div>
</div>
