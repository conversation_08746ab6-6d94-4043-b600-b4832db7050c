<%- contentFor('body') %>

<div class="container mx-auto px-4 py-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-2"><%= pageTitle %></h1>
    <p class="text-gray-600">This page shows the database schema and entity relationships between different tables in the system.</p>
  </div>

  <!-- Schema Diagram Controls -->
  <div class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="flex flex-wrap items-center justify-between gap-4">
      <div>
        <h2 class="text-lg font-semibold text-gray-800">Database Schema Diagram</h2>
        <p class="text-sm text-gray-600">Visualize how entities like teachers, students, and instruction plans are interlinked</p>
      </div>
      <div class="flex gap-2">
        <button id="expandAll" class="px-3 py-1.5 bg-purple-600 text-white rounded hover:bg-purple-700 transition">
          Expand All
        </button>
        <button id="collapseAll" class="px-3 py-1.5 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
          Collapse All
        </button>
      </div>
    </div>
  </div>

  <!-- Schema Diagram -->
  <div class="grid grid-cols-1 gap-6">
    <% Object.keys(tableCategories).forEach(category => { %>
      <div class="bg-white rounded-lg shadow-md overflow-hidden schema-category">
        <div class="bg-purple-600 text-white px-4 py-3 cursor-pointer category-header flex justify-between items-center">
          <h3 class="text-lg font-semibold"><%= category %></h3>
          <svg class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
        <div class="category-content p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% tableCategories[category].forEach(tableName => { %>
              <% if (tableColumns[tableName]) { %>
                <div class="border border-gray-300 rounded-lg overflow-hidden schema-table">
                  <div class="bg-gray-100 px-3 py-2 font-semibold text-gray-800 border-b border-gray-300 flex justify-between items-center cursor-pointer table-header">
                    <span><%= tableName %></span>
                    <svg class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </div>
                  <div class="table-content">
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <% tableColumns[tableName].forEach(column => { %>
                            <tr class="<%= column.column_key === 'PRI' ? 'bg-yellow-50' : (column.column_key === 'MUL' ? 'bg-blue-50' : '') %>">
                              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 font-medium">
                                <%= column.column_name %>
                                <% if (column.column_key === 'PRI') { %>
                                  <span class="ml-1 text-xs text-yellow-600">(PK)</span>
                                <% } else if (column.column_key === 'MUL') { %>
                                  <span class="ml-1 text-xs text-blue-600">(FK)</span>
                                <% } %>
                              </td>
                              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500"><%= column.column_type %></td>
                              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                <% if (column.column_key === 'PRI') { %>
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Primary</span>
                                <% } else if (column.column_key === 'MUL') { %>
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Foreign</span>
                                <% } else if (column.column_key === 'UNI') { %>
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Unique</span>
                                <% } %>
                              </td>
                            </tr>
                          <% }); %>
                        </tbody>
                      </table>
                    </div>
                    
                    <% if (relationships[tableName] && relationships[tableName].length > 0) { %>
                      <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Relationships:</h4>
                        <ul class="space-y-1">
                          <% relationships[tableName].forEach(rel => { %>
                            <li class="text-xs text-gray-600">
                              <span class="font-medium"><%= rel.fromColumn %></span> → 
                              <span class="text-blue-600 font-medium"><%= rel.toTable %>.<%= rel.toColumn %></span>
                            </li>
                          <% }); %>
                        </ul>
                      </div>
                    <% } %>
                  </div>
                </div>
              <% } %>
            <% }); %>
          </div>
        </div>
      </div>
    <% }); %>
  </div>
</div>

<%- contentFor('script') %>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Toggle category content
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
      header.addEventListener('click', function() {
        const content = this.nextElementSibling;
        const arrow = this.querySelector('svg');
        
        content.classList.toggle('hidden');
        arrow.classList.toggle('rotate-180');
      });
    });
    
    // Toggle table content
    const tableHeaders = document.querySelectorAll('.table-header');
    tableHeaders.forEach(header => {
      header.addEventListener('click', function() {
        const content = this.nextElementSibling;
        const arrow = this.querySelector('svg');
        
        content.classList.toggle('hidden');
        arrow.classList.toggle('rotate-180');
      });
    });
    
    // Expand all button
    document.getElementById('expandAll').addEventListener('click', function() {
      document.querySelectorAll('.category-content, .table-content').forEach(el => {
        el.classList.remove('hidden');
      });
      document.querySelectorAll('.category-header svg, .table-header svg').forEach(arrow => {
        arrow.classList.remove('rotate-180');
      });
    });
    
    // Collapse all button
    document.getElementById('collapseAll').addEventListener('click', function() {
      document.querySelectorAll('.category-content, .table-content').forEach(el => {
        el.classList.add('hidden');
      });
      document.querySelectorAll('.category-header svg, .table-header svg').forEach(arrow => {
        arrow.classList.add('rotate-180');
      });
    });
    
    // Initially expand all categories but collapse tables
    document.querySelectorAll('.category-content').forEach(el => {
      el.classList.remove('hidden');
    });
    document.querySelectorAll('.table-content').forEach(el => {
      el.classList.add('hidden');
    });
    document.querySelectorAll('.table-header svg').forEach(arrow => {
      arrow.classList.add('rotate-180');
    });
  });
</script>

<%- contentFor('style') %>
<style>
  .schema-table {
    transition: all 0.3s ease;
  }
  
  .schema-table:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .category-header, .table-header {
    transition: background-color 0.2s ease;
  }
  
  .category-header:hover {
    background-color: #7c3aed;
  }
  
  .table-header:hover {
    background-color: #f3f4f6;
  }
</style>
