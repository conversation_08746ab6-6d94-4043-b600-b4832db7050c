<!-- General Settings Page -->
<div class="bg-white shadow-md rounded-lg p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">General Settings</h2>

    <form action="/admin/settings" method="POST" class="space-y-6">
        <!-- Site Information Section -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Site Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                    <input type="text" id="site_name" name="site_name" value="<%= settings.site_name || '' %>" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                
                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                    <input type="text" id="site_description" name="site_description" value="<%= settings.site_description || '' %>" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
            </div>
        </div>
        
        <!-- Contact Information Section -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Contact Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                    <input type="email" id="contact_email" name="contact_email" value="<%= settings.contact_email || '' %>" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                
                <div>
                    <label for="support_phone" class="block text-sm font-medium text-gray-700 mb-1">Support Phone</label>
                    <input type="text" id="support_phone" name="support_phone" value="<%= settings.support_phone || '' %>" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
            </div>
        </div>
        
        <!-- Language Settings Section -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-4">Language Settings</h3>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" name="default_language" value="en" <%= (settings.default_language === 'en' || !settings.default_language) ? 'checked' : '' %> 
                            class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-gray-700">English</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="default_language" value="pa" <%= settings.default_language === 'pa' ? 'checked' : '' %> 
                            class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-gray-700">ਪੰਜਾਬੀ</span>
                    </label>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Available Languages</label>
                <div class="flex flex-col space-y-2">
                    <label class="inline-flex items-center">
                        <input type="checkbox" name="languages[]" value="en" checked disabled 
                            class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-gray-700">English (Default)</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="checkbox" name="languages[]" value="pa" <%= settings.languages && settings.languages.includes('pa') ? 'checked' : '' %> 
                            class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-gray-700">ਪੰਜਾਬੀ</span>
                    </label>
                </div>
                <p class="mt-2 text-sm text-gray-500">You can switch languages using the language selector in the top right corner of the page.</p>
            </div>
        </div>
        
        <!-- System Settings Section -->
        <div class="border-b border-gray-200 pb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-4">System Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-1">Timezone</label>
                    <select id="timezone" name="timezone" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="Asia/Kolkata" <%= settings.timezone === 'Asia/Kolkata' ? 'selected' : '' %>>Asia/Kolkata (IST)</option>
                        <option value="UTC" <%= settings.timezone === 'UTC' ? 'selected' : '' %>>UTC</option>
                    </select>
                </div>
                
                <div>
                    <label for="date_format" class="block text-sm font-medium text-gray-700 mb-1">Date Format</label>
                    <select id="date_format" name="date_format" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="DD-MM-YYYY" <%= settings.date_format === 'DD-MM-YYYY' ? 'selected' : '' %>>DD-MM-YYYY</option>
                        <option value="MM-DD-YYYY" <%= settings.date_format === 'MM-DD-YYYY' ? 'selected' : '' %>>MM-DD-YYYY</option>
                        <option value="YYYY-MM-DD" <%= settings.date_format === 'YYYY-MM-DD' ? 'selected' : '' %>>YYYY-MM-DD</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" name="maintenance_mode" value="1" <%= settings.maintenance_mode === '1' || settings.maintenance_mode === 1 ? 'checked' : '' %> 
                        class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <span class="ml-2 text-gray-700">Maintenance Mode</span>
                </label>
                <p class="mt-1 text-sm text-gray-500">When enabled, only administrators can access the site.</p>
            </div>
        </div>
        
        <!-- Submit Button -->
        <div class="flex justify-end">
            <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
                Save Settings
            </button>
        </div>
    </form>
</div>
