<!-- Logo Upload Page -->
<div class="bg-white shadow-md rounded-lg p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Upload Site Logo</h2>

    <p class="mb-6 text-gray-600">
        Upload a logo for your site. This logo will appear across the site and in generated PDFs.
        For best results, use a square image (1:1 ratio) with dimensions of 40x40 pixels.
    </p>

    <!-- Current Logo Display -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Current Logo</h3>

        <% if (currentLogo) { %>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <img src="<%= currentLogo %>" alt="Current Site Logo" class="h-20 w-20 object-cover border border-gray-200 p-2 bg-white rounded">
                    <span class="ml-4 text-sm text-gray-500">This logo appears in the site header and PDF documents.</span>
                </div>

                <form action="/admin/settings/logo/delete" method="POST" onsubmit="return confirm('Are you sure you want to remove the current logo?');">
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Remove Logo
                    </button>
                </form>
            </div>
        <% } else { %>
            <div class="text-center py-8 bg-gray-100 rounded-lg border border-dashed border-gray-300">
                <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <p class="mt-2 text-gray-500">No logo has been uploaded yet.</p>
            </div>
        <% } %>
    </div>

    <!-- Upload Form -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Upload New Logo</h3>

        <form action="/admin/settings/logo" method="POST" enctype="multipart/form-data" class="space-y-4">
            <div>
                <label for="logo" class="block text-sm font-medium text-gray-700 mb-1">Select Logo File</label>
                <input type="file" id="logo" name="logo" accept="image/png, image/jpeg, image/gif, image/svg+xml"
                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100" required>
                <p class="mt-1 text-sm text-gray-500">Accepted formats: PNG, JPEG, GIF, SVG. Maximum size: 5MB.</p>
            </div>

            <div class="pt-2">
                <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12"></path>
                    </svg>
                    Upload Logo
                </button>
            </div>
        </form>
    </div>

    <!-- Logo Preview -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Logo Preview</h3>

        <div class="space-y-6">
            <!-- Header Preview -->
            <div>
                <h4 class="text-md font-medium text-gray-600 mb-2">Header Preview</h4>
                <div class="bg-purple-600 p-4 rounded-lg flex items-center">
                    <div class="bg-white p-2 rounded-md mr-4" id="header-preview">
                        <% if (currentLogo) { %>
                            <img src="<%= currentLogo %>" alt="Logo Preview" class="h-10 w-10 object-cover rounded">
                        <% } else { %>
                            <div class="text-purple-600 font-bold text-xl">Exam Prep Platform</div>
                        <% } %>
                    </div>
                    <div class="text-white">Header Navigation</div>
                </div>
            </div>

            <!-- PDF Preview -->
            <div>
                <h4 class="text-md font-medium text-gray-600 mb-2">PDF Document Preview</h4>
                <div class="bg-white border border-gray-300 p-4 rounded-lg">
                    <div class="border-b border-gray-200 pb-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <% if (currentLogo) { %>
                                <img src="<%= currentLogo %>" alt="Logo Preview" class="h-12 w-12 object-cover rounded mr-4">
                            <% } else { %>
                                <div class="text-gray-800 font-bold text-xl mr-4">Exam Prep Platform</div>
                            <% } %>
                            <div class="text-gray-600 text-sm">Senior Secondary Residential School for Meritorious Students, Ludhiana</div>
                        </div>
                        <div class="text-gray-500 text-sm">Document #12345</div>
                    </div>
                    <div class="py-4">
                        <div class="text-xl font-bold text-center text-gray-800 mb-4">SAMPLE DOCUMENT</div>
                        <div class="h-32 bg-gray-100 rounded-lg"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logo Guidelines -->
    <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 class="text-lg font-medium text-blue-700 mb-2">Logo Guidelines</h3>
        <ul class="list-disc pl-5 text-blue-600 space-y-1">
            <li>Use a square image with 1:1 aspect ratio</li>
            <li>Recommended dimensions: 40x40 pixels</li>
            <li>Use a transparent PNG for best results</li>
            <li>Keep the file size under 5MB</li>
            <li>Use a logo that is clearly visible on both light and dark backgrounds</li>
            <li>The logo will be displayed in the site header and on PDF documents</li>
        </ul>
    </div>
</div>

<!-- Logo Preview Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const logoInput = document.getElementById('logo');
        const headerPreview = document.getElementById('header-preview');
        const originalHeaderContent = headerPreview.innerHTML;

        logoInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    headerPreview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="h-10 w-10 object-cover rounded">`;
                };
                reader.readAsDataURL(file);
            } else {
                headerPreview.innerHTML = originalHeaderContent;
            }
        });
    });
</script>
