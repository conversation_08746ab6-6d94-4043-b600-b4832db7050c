<!-- Assign Subjects Form -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-purple-600 text-white p-4">
    <h2 class="text-xl font-semibold">Assign Subjects to Student</h2>
  </div>
  <div class="p-6">
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900">Student Information</h3>
      <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p class="text-sm text-gray-500">Username</p>
          <p class="text-base font-medium text-gray-900"><%= student.username %></p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Email</p>
          <p class="text-base font-medium text-gray-900"><%= student.email %></p>
        </div>
      </div>
    </div>

    <form id="subjectForm" class="space-y-6">
      <div>
        <label for="subjects" class="block text-sm font-medium text-gray-700">Select Subjects</label>
        <p class="text-xs text-gray-500 mb-2">You can select multiple subjects by holding Ctrl (or Cmd on Mac) while clicking</p>
        <select id="subjects" name="subjects" multiple class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md" size="8">
          <% subjects.forEach(subject => { %>
            <option value="<%= subject.id %>" <%= studentSubjects.includes(subject.id) ? 'selected' : '' %>>
              <%= subject.name %> <%= subject.description ? `- ${subject.description}` : '' %>
            </option>
          <% }); %>
        </select>
      </div>

      <div class="flex justify-end space-x-3">
        <a href="/admin/students" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
          Cancel
        </a>
        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
          Save Subject Assignments
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const subjectForm = document.getElementById('subjectForm');
    const subjectsSelect = document.getElementById('subjects');
    
    subjectForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Get selected subject IDs
      const selectedSubjects = Array.from(subjectsSelect.selectedOptions).map(option => option.value);
      
      // Send AJAX request to update subject assignments
      fetch('/admin/students/<%= student.id %>/assign-subjects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ subjectIds: selectedSubjects }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          showToast('success', 'Success', data.message);
          
          // Redirect to students list after a delay
          setTimeout(() => {
            window.location.href = '/admin/students';
          }, 1500);
        } else {
          // Show error message
          showToast('error', 'Error', data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Error', 'An error occurred while updating subject assignments');
      });
    });
  });
</script>
