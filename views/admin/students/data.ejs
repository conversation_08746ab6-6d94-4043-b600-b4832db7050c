<!-- Student Data Management Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <!-- Header -->
  <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Student Data Management</h1>
        <p class="text-blue-100 mt-1">Comprehensive student information with advanced filtering</p>
      </div>
      <div class="flex flex-wrap gap-3">
        <a href="/admin/students/import" class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
          <i class="fas fa-upload mr-2"></i>Import Students
        </a>
        <a href="/admin/students/import/template" class="bg-blue-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-400 transition-colors">
          <i class="fas fa-download mr-2"></i>Download Template
        </a>
        <button id="exportBtn" class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-400 transition-colors">
          <i class="fas fa-file-excel mr-2"></i>Export Data
        </button>
        <button id="bulkDeleteBtn" class="bg-red-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-trash mr-2"></i>Delete Selected
        </button>
        <button id="bulkEditBtn" class="bg-yellow-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-yellow-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-edit mr-2"></i>Edit Selected
        </button>
        <button id="trashBtn" class="bg-gray-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-400 transition-colors">
          <i class="fas fa-trash-restore mr-2"></i>Trash
        </button>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  <% if (flashSuccess) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-check-circle mr-2"></i><%= flashSuccess %>
    </div>
  <% } %>
  <% if (flashError) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-exclamation-circle mr-2"></i><%= flashError %>
    </div>
  <% } %>
  <% if (flashInfo) { %>
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-info-circle mr-2"></i><%= flashInfo %>
    </div>
  <% } %>

  <!-- Filters Section -->
  <div class="bg-gray-50 border-b">
    <!-- Filter Header -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Filters & Search</h3>
        <button type="button" id="toggleFilters" class="flex items-center text-blue-600 hover:text-blue-800 transition-colors">
          <span class="mr-2">Advanced Filters</span>
          <i class="fas fa-chevron-down transition-transform duration-200" id="filterToggleIcon"></i>
        </button>
      </div>
    </div>

    <div class="p-6">
      <form id="filterForm" method="GET" class="space-y-4">
        <!-- Quick Filters -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-gray-700">Quick Filters</h4>
          <div class="flex flex-wrap gap-2">
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium" data-filter="all">
              <i class="fas fa-users mr-2"></i>All Students (<%= pagination.totalStudents %>)
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 transition-all duration-200 font-medium" data-filter="male">
              <i class="fas fa-mars mr-2"></i>Male Students
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-pink-300 bg-pink-50 text-pink-700 hover:bg-pink-100 transition-all duration-200 font-medium" data-filter="female">
              <i class="fas fa-venus mr-2"></i>Female Students
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-orange-300 bg-orange-50 text-orange-700 hover:bg-orange-100 transition-all duration-200 font-medium" data-filter="bpl">
              <i class="fas fa-hand-holding-heart mr-2"></i>BPL Students
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-purple-300 bg-purple-50 text-purple-700 hover:bg-purple-100 transition-all duration-200 font-medium" data-filter="disability">
              <i class="fas fa-wheelchair mr-2"></i>Students with Disability
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-green-300 bg-green-50 text-green-700 hover:bg-green-100 transition-all duration-200 font-medium" data-filter="science">
              <i class="fas fa-flask mr-2"></i>Science Stream
            </button>
            <button type="button" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-indigo-300 bg-indigo-50 text-indigo-700 hover:bg-indigo-100 transition-all duration-200 font-medium" data-filter="commerce">
              <i class="fas fa-chart-line mr-2"></i>Commerce Stream
            </button>
            <button type="button" id="viewTrashBtn" class="quick-filter px-4 py-2 text-sm rounded-full border-2 border-red-300 bg-red-50 text-red-700 hover:bg-red-100 transition-all duration-200 font-medium" data-filter="trash">
              <i class="fas fa-trash-restore mr-2"></i>View Trash
            </button>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div id="advancedFilters" class="hidden space-y-4">
          <div class="border-t border-gray-200 pt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Advanced Filters</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        <!-- Search -->
        <div class="lg:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input type="text" name="search" id="searchInput" value="<%= filters.search %>"
                 placeholder="Name, ID, Father's Name, Roll No..."
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Class -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
          <select name="class" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All Classes</option>
            <% classes.forEach(cls => { %>
              <option value="<%= cls %>" <%= filters.class === cls ? 'selected' : '' %>><%= cls %></option>
            <% }); %>
          </select>
        </div>

        <!-- Section -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Section</label>
          <select name="section" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All Sections</option>
            <% sections.forEach(section => { %>
              <option value="<%= section %>" <%= filters.section === section ? 'selected' : '' %>><%= section %></option>
            <% }); %>
          </select>
        </div>

        <!-- Session -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Session</label>
          <select name="session" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All Sessions</option>
            <% sessions.forEach(session => { %>
              <option value="<%= session %>" <%= filters.session === session ? 'selected' : '' %>><%= session %></option>
            <% }); %>
          </select>
        </div>

        <!-- Gender -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
          <select name="gender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All Genders</option>
            <option value="Male" <%= filters.gender === 'Male' ? 'selected' : '' %>>Male</option>
            <option value="Female" <%= filters.gender === 'Female' ? 'selected' : '' %>>Female</option>
            <option value="Other" <%= filters.gender === 'Other' ? 'selected' : '' %>>Other</option>
          </select>
        </div>

        <!-- Stream -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Stream</label>
          <select name="stream" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All Streams</option>
            <% streams.forEach(stream => { %>
              <option value="<%= stream %>" <%= filters.stream === stream ? 'selected' : '' %>><%= stream %></option>
            <% }); %>
          </select>
        </div>

        <!-- BPL -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">BPL Status</label>
          <select name="bpl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All</option>
            <option value="Yes" <%= filters.bpl === 'Yes' ? 'selected' : '' %>>BPL</option>
            <option value="No" <%= filters.bpl === 'No' ? 'selected' : '' %>>Non-BPL</option>
          </select>
        </div>

        <!-- Disability -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Disability</label>
          <select name="disability" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">All</option>
            <option value="Yes" <%= filters.disability === 'Yes' ? 'selected' : '' %>>With Disability</option>
            <option value="No" <%= filters.disability === 'No' ? 'selected' : '' %>>Without Disability</option>
          </select>
        </div>

        <!-- Records per page -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Per Page</label>
          <select name="limit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="25" <%= pagination.limit === 25 ? 'selected' : '' %>>25</option>
            <option value="50" <%= pagination.limit === 50 ? 'selected' : '' %>>50</option>
            <option value="100" <%= pagination.limit === 100 ? 'selected' : '' %>>100</option>
            <option value="200" <%= pagination.limit === 200 ? 'selected' : '' %>>200</option>
          </select>
            </div>

            <!-- Filter Actions -->
            <div class="flex space-x-3 mt-4">
              <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                <i class="fas fa-filter mr-2"></i>Apply Filters
              </button>
              <button type="button" id="clearFilters" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
                <i class="fas fa-times mr-2"></i>Clear All
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Results Summary -->
  <div class="px-6 py-3 bg-blue-50 border-b">
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-600">
        Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
        <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
        of <%= pagination.totalStudents %> students
      </div>
      <div class="text-sm text-gray-600">
        Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <div class="overflow-x-auto max-w-full">
    <div class="inline-block min-w-full align-middle">
      <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
          </th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UdiseCode</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Father Name</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mother Name</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DOB</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stream</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Caste Category</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BPL</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disability</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Religion</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Medium</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Height</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admission No</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admission Date</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">District</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Village/Ward</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gram Panchayat</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pin Code</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roll No</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IFSC Code</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column1</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Holder Code</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Holder</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Holder Name</th>
          <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (students && students.length > 0) { %>
          <% students.forEach((student, index) => { %>
            <tr class="hover:bg-gray-50">
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <input type="checkbox" class="student-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="<%= student.id %>">
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.sno || ((pagination.currentPage - 1) * pagination.limit + index + 1) %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-blue-600"><%= student.student_id || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.udise_code || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= student.name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.father_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.mother_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '-' %>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full <%= student.gender === 'Male' ? 'bg-blue-100 text-blue-800' : student.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 'bg-gray-100 text-gray-800' %>">
                  <%= student.gender || '-' %>
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.class || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.section || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.stream || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.trade || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.caste_category_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full <%= student.bpl === 'Yes' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800' %>">
                  <%= student.bpl || 'No' %>
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full <%= student.disability === 'Yes' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' %>">
                  <%= student.disability || 'No' %>
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.religion_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.medium_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.height || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.weight || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.admission_no || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '-' %>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.state_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.district_name || '-' %></td>
              <td class="px-3 py-4 text-sm text-gray-900 max-w-xs truncate" title="<%= student.cur_address || '-' %>">
                <%= student.cur_address || '-' %>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.village_ward || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.gram_panchayat || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.pin_code || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.roll_no || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.contact_no || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.ifsc_code || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.bank_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.column1 || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.account_holder_code || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.account_holder || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900"><%= student.account_holder_name || '-' %></td>
              <td class="px-3 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button id="viewBtn-<%= student.id %>" class="view-student-btn text-blue-600 hover:text-blue-900" data-student-id="<%= student.id %>">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button id="editBtn-<%= student.id %>" class="edit-student-btn text-green-600 hover:text-green-900" data-student-id="<%= student.id %>">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button id="deleteBtn-<%= student.id %>" class="delete-student-btn text-red-600 hover:text-red-900" data-student-id="<%= student.id %>">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          <% }); %>
        <% } else { %>
          <tr>
            <td colspan="37" class="px-6 py-12 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No students found</h3>
                <p class="text-gray-500">Try adjusting your filters or import student data.</p>
              </div>
            </td>
          </tr>
        <% } %>
      </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <% if (pagination.totalPages > 1) { %>
    <div class="px-6 py-4 bg-gray-50 border-t">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
          <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
          of <%= pagination.totalStudents %> results
        </div>
        <div class="flex space-x-1">
          <!-- Previous Page -->
          <% if (pagination.hasPrev) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage - 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
              <i class="fas fa-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            const startPage = Math.max(1, pagination.currentPage - 2);
            const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
          %>

          <% if (startPage > 1) { %>
            <a href="?<%= new URLSearchParams({...filters, page: 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">1</a>
            <% if (startPage > 2) { %>
              <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
            <% } %>
          <% } %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === pagination.currentPage) { %>
              <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
                <%= i %>
              </span>
            <% } else { %>
              <a href="?<%= new URLSearchParams({...filters, page: i, limit: pagination.limit}).toString() %>"
                 class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <% if (endPage < pagination.totalPages) { %>
            <% if (endPage < pagination.totalPages - 1) { %>
              <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
            <% } %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.totalPages, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
              <%= pagination.totalPages %>
            </a>
          <% } %>

          <!-- Next Page -->
          <% if (pagination.hasNext) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage + 1, limit: pagination.limit}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
              <i class="fas fa-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Import Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="bg-blue-600 text-white p-4 rounded-t-lg">
        <h3 class="text-lg font-medium">Import Students</h3>
      </div>
      <form action="/admin/students/import" method="POST" enctype="multipart/form-data" class="p-6">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Select Session</label>
          <select name="session" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Choose Session</option>
            <% sessions.forEach(session => { %>
              <option value="<%= session %>"><%= session %></option>
            <% }); %>
            <option value="2024-25">2024-25</option>
            <option value="2025-26">2025-26</option>
          </select>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Upload File</label>
          <input type="file" name="file" accept=".csv,.xlsx,.xls" required
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <p class="text-xs text-gray-500 mt-1">Supported formats: CSV, Excel (.xlsx, .xls)</p>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" id="closeImportModalBtn"
                  class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Import
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Student View/Edit Modal -->
<div id="studentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
      <div class="bg-blue-600 text-white p-4 rounded-t-lg">
        <h3 id="studentModalTitle" class="text-lg font-medium">Student Details</h3>
      </div>
      <div id="studentModalContent" class="p-6">
        <!-- Content will be loaded dynamically -->
      </div>
      <div id="studentModalFooter" class="px-6 py-4 bg-gray-50 rounded-b-lg">
        <!-- Footer buttons will be loaded dynamically -->
      </div>
    </div>
  </div>
</div>

<!-- Bulk Delete Modal -->
<div id="bulkDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="bg-red-600 text-white p-4 rounded-t-lg">
        <h3 class="text-lg font-medium">Confirm Bulk Delete</h3>
      </div>
      <div class="p-6">
        <div class="flex items-center mb-4">
          <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-3"></i>
          <div>
            <p class="text-gray-900 font-medium">Are you sure you want to delete <span id="bulkDeleteCount" class="font-bold text-red-600"></span> students?</p>
            <p class="text-gray-600 text-sm mt-1">This action cannot be undone.</p>
          </div>
        </div>
        <form id="bulkDeleteForm" action="/admin/students/bulk-delete" method="POST">
          <input type="hidden" id="bulkDeleteIds" name="studentIds">
          <div class="flex justify-end space-x-3">
            <button type="button" id="closeBulkDeleteModalBtn" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              Delete Students
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Bulk Edit Modal -->
<div id="bulkEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
      <div class="bg-yellow-600 text-white p-4 rounded-t-lg">
        <h3 class="text-lg font-medium">Bulk Edit Students</h3>
      </div>
      <div class="p-6">
        <p class="text-gray-600 mb-4">Editing <span id="bulkEditCount" class="font-bold"></span> students. Only fill in the fields you want to update.</p>
        <form id="bulkEditForm" action="/admin/students/bulk-edit" method="POST" class="space-y-4">
          <input type="hidden" id="bulkEditIds" name="studentIds">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
              <input type="text" name="class" placeholder="Leave empty to keep current" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Section</label>
              <input type="text" name="section" placeholder="Leave empty to keep current" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Stream</label>
              <select name="stream" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                <option value="">Keep current</option>
                <option value="Science">Science</option>
                <option value="Commerce">Commerce</option>
                <option value="Arts">Arts</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Session</label>
              <select name="session" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                <option value="">Keep current</option>
                <option value="2024-25">2024-25</option>
                <option value="2025-26">2025-26</option>
              </select>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" id="closeBulkEditModalBtn" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
              Update Students
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Single Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
      <div class="bg-red-600 text-white p-4 rounded-t-lg">
        <h3 class="text-lg font-medium">Confirm Delete</h3>
      </div>
      <div class="p-6">
        <div class="flex items-center mb-4">
          <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-3"></i>
          <div>
            <p class="text-gray-900 font-medium">Are you sure you want to delete this student?</p>
            <p class="text-gray-600 text-sm mt-1">This action cannot be undone.</p>
          </div>
        </div>
        <form id="deleteForm" action="/admin/students/delete" method="POST">
          <input type="hidden" id="deleteIds" name="studentIds">
          <div class="flex justify-end space-x-3">
            <button type="button" id="closeDeleteModalBtn" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              Delete Student
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Trash Modal -->
<div id="trashModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-screen overflow-y-auto">
      <div class="bg-red-600 text-white p-4 rounded-t-lg">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium">
            <i class="fas fa-trash-restore mr-2"></i>Deleted Students
          </h3>
          <button id="closeTrashModalBtn" class="text-white hover:text-gray-200">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
      </div>
      <div id="trashModalContent" class="p-6">
        <!-- Content will be loaded dynamically -->
        <div class="flex justify-center items-center py-8">
          <i class="fas fa-spinner fa-spin text-2xl text-red-600"></i>
          <span class="ml-2 text-gray-600">Loading deleted students...</span>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Filter toggle functionality
  const toggleFilters = document.getElementById('toggleFilters');
  const advancedFilters = document.getElementById('advancedFilters');
  const filterToggleIcon = document.getElementById('filterToggleIcon');

  toggleFilters.addEventListener('click', function() {
    if (advancedFilters.classList.contains('hidden')) {
      advancedFilters.classList.remove('hidden');
      filterToggleIcon.classList.add('rotate-180');
    } else {
      advancedFilters.classList.add('hidden');
      filterToggleIcon.classList.remove('rotate-180');
    }
  });

  // Bulk selection functionality
  const selectAllCheckbox = document.getElementById('selectAll');
  const studentCheckboxes = document.querySelectorAll('.student-checkbox');
  const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
  const bulkEditBtn = document.getElementById('bulkEditBtn');

  // Select all functionality
  selectAllCheckbox.addEventListener('change', function() {
    studentCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    updateBulkButtons();
  });

  // Individual checkbox functionality
  studentCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      updateSelectAllState();
      updateBulkButtons();
    });
  });

  function updateSelectAllState() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    selectAllCheckbox.checked = checkedBoxes.length === studentCheckboxes.length;
    selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < studentCheckboxes.length;
  }

  function updateBulkButtons() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const hasSelection = checkedBoxes.length > 0;

    bulkDeleteBtn.disabled = !hasSelection;
    bulkEditBtn.disabled = !hasSelection;

    if (hasSelection) {
      bulkDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
      bulkEditBtn.classList.remove('opacity-50', 'cursor-not-allowed');
      // Change to icon when selected
      bulkDeleteBtn.innerHTML = '<i class="fas fa-trash-alt text-lg"></i>';
      bulkEditBtn.innerHTML = '<i class="fas fa-edit text-lg"></i>';
      bulkDeleteBtn.title = `Delete ${checkedBoxes.length} selected students`;
      bulkEditBtn.title = `Edit ${checkedBoxes.length} selected students`;
    } else {
      bulkDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
      bulkEditBtn.classList.add('opacity-50', 'cursor-not-allowed');
      // Change back to text when not selected
      bulkDeleteBtn.innerHTML = '<i class="fas fa-trash mr-2"></i>Delete Selected';
      bulkEditBtn.innerHTML = '<i class="fas fa-edit mr-2"></i>Edit Selected';
      bulkDeleteBtn.title = 'Select students to delete';
      bulkEditBtn.title = 'Select students to edit';
    }
  }

  // Bulk delete functionality
  bulkDeleteBtn.addEventListener('click', function() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    if (checkedBoxes.length === 0) return;

    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    openBulkDeleteModal(studentIds);
  });

  // Bulk edit functionality
  bulkEditBtn.addEventListener('click', function() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    if (checkedBoxes.length === 0) return;

    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    openBulkEditModal(studentIds);
  });

  // Quick filters functionality
  const quickFilters = document.querySelectorAll('.quick-filter');
  const filterForm = document.getElementById('filterForm');

  quickFilters.forEach(filter => {
    filter.addEventListener('click', function() {
      const filterType = this.dataset.filter;

      // Reset all filters
      const inputs = filterForm.querySelectorAll('input, select');
      inputs.forEach(input => {
        if (input.name !== 'limit') {
          input.value = '';
        }
      });

      // Apply specific filter
      switch(filterType) {
        case 'male':
          document.querySelector('select[name="gender"]').value = 'Male';
          break;
        case 'female':
          document.querySelector('select[name="gender"]').value = 'Female';
          break;
        case 'bpl':
          document.querySelector('select[name="bpl"]').value = 'Yes';
          break;
        case 'disability':
          document.querySelector('select[name="disability"]').value = 'Yes';
          break;
        case 'science':
          document.querySelector('select[name="stream"]').value = 'Science';
          break;
        case 'commerce':
          document.querySelector('select[name="stream"]').value = 'Commerce';
          break;
      }

      // Submit form
      filterForm.submit();
    });
  });

  // Clear filters
  document.getElementById('clearFilters').addEventListener('click', function() {
    const inputs = filterForm.querySelectorAll('input, select');
    inputs.forEach(input => {
      if (input.name !== 'limit') {
        input.value = '';
      }
    });
    filterForm.submit();
  });

  // Auto-submit on filter change
  const filterInputs = filterForm.querySelectorAll('select:not([name="limit"])');
  filterInputs.forEach(input => {
    input.addEventListener('change', function() {
      filterForm.submit();
    });
  });

  // Search with debounce
  let searchTimeout;
  document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      filterForm.submit();
    }, 500);
  });

  // Export functionality
  document.getElementById('exportBtn').addEventListener('click', function() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '/admin/students/data?' + params.toString();
  });

  // Trash functionality
  document.getElementById('trashBtn').addEventListener('click', function() {
    openTrashModal();
  });

  document.getElementById('viewTrashBtn').addEventListener('click', function() {
    openTrashModal();
  });
});

// Event delegation for student action buttons
$(document).on('click', '.view-student-btn', function() {
  const studentId = $(this).data('student-id');
  openStudentModal(studentId, 'view');
});

$(document).on('click', '.edit-student-btn', function() {
  const studentId = $(this).data('student-id');
  openStudentModal(studentId, 'edit');
});

$(document).on('click', '.delete-student-btn', function() {
  const studentId = $(this).data('student-id');
  openDeleteModal([studentId]);
});

// Event delegation for modal buttons
$(document).on('click', '#closeStudentModalBtn', function() {
  closeStudentModal();
});

$(document).on('click', '#saveStudentBtn', function() {
  const studentId = $(this).data('student-id');
  saveStudent(studentId);
});

// Event delegation for all modal close buttons
$(document).on('click', '#closeBulkDeleteModalBtn', function() {
  closeBulkDeleteModal();
});

$(document).on('click', '#closeBulkEditModalBtn', function() {
  closeBulkEditModal();
});

$(document).on('click', '#closeDeleteModalBtn', function() {
  closeDeleteModal();
});

$(document).on('click', '#closeTrashModalBtn', function() {
  closeTrashModal();
});

$(document).on('click', '#closeImportModalBtn', function() {
  closeImportModal();
});

// Import modal functions
function openImportModal() {
  document.getElementById('importModal').classList.remove('hidden');
}

function closeImportModal() {
  document.getElementById('importModal').classList.add('hidden');
}

// Student action functions (now handled by event delegation above)

// Modal functions
function openStudentModal(studentId, mode) {
  const modal = document.getElementById('studentModal');
  const modalTitle = document.getElementById('studentModalTitle');
  const modalContent = document.getElementById('studentModalContent');
  const modalFooter = document.getElementById('studentModalFooter');

  // Set modal title
  modalTitle.textContent = mode === 'view' ? 'View Student Details' : 'Edit Student Details';

  // Show loading
  modalContent.innerHTML = '<div class="flex justify-center items-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i></div>';

  // Show modal
  modal.classList.remove('hidden');

  // Fetch student data
  fetch(`/admin/students/${studentId}`)
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        const student = result.student;
        if (mode === 'view') {
          modalContent.innerHTML = generateViewContent(student);
          modalFooter.innerHTML = `
            <button id="closeStudentModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
              Close
            </button>
          `;
        } else {
          modalContent.innerHTML = generateEditContent(student);
          modalFooter.innerHTML = `
            <button id="closeStudentModalBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 mr-2">
              Cancel
            </button>
            <button id="saveStudentBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" data-student-id="${studentId}">
              Save Changes
            </button>
          `;
        }
      } else {
        modalContent.innerHTML = '<div class="text-red-600 text-center py-4">Student not found</div>';
      }
    })
    .catch(error => {
      modalContent.innerHTML = '<div class="text-red-600 text-center py-4">Error loading student data</div>';
      console.error('Error:', error);
    });
}

function openBulkDeleteModal(studentIds) {
  const modal = document.getElementById('bulkDeleteModal');
  const studentCount = document.getElementById('bulkDeleteCount');
  const studentIdsList = document.getElementById('bulkDeleteIds');

  studentCount.textContent = studentIds.length;
  studentIdsList.value = studentIds.join(',');

  modal.classList.remove('hidden');
}

function openBulkEditModal(studentIds) {
  const modal = document.getElementById('bulkEditModal');
  const studentCount = document.getElementById('bulkEditCount');
  const studentIdsList = document.getElementById('bulkEditIds');

  studentCount.textContent = studentIds.length;
  studentIdsList.value = studentIds.join(',');

  modal.classList.remove('hidden');
}

function openDeleteModal(studentIds) {
  const modal = document.getElementById('deleteModal');
  const studentCount = document.getElementById('deleteCount');
  const studentIdsList = document.getElementById('deleteIds');

  studentCount.textContent = studentIds.length;
  studentIdsList.value = studentIds.join(',');

  modal.classList.remove('hidden');
}

// Modal close functions
function closeStudentModal() {
  document.getElementById('studentModal').classList.add('hidden');
}

function closeBulkDeleteModal() {
  document.getElementById('bulkDeleteModal').classList.add('hidden');
}

function closeBulkEditModal() {
  document.getElementById('bulkEditModal').classList.add('hidden');
}

function closeDeleteModal() {
  document.getElementById('deleteModal').classList.add('hidden');
}

function closeTrashModal() {
  document.getElementById('trashModal').classList.add('hidden');
}

function openTrashModal() {
  const modal = document.getElementById('trashModal');
  const modalContent = document.getElementById('trashModalContent');

  // Show loading
  modalContent.innerHTML = `
    <div class="flex justify-center items-center py-8">
      <i class="fas fa-spinner fa-spin text-2xl text-red-600"></i>
      <span class="ml-2 text-gray-600">Loading deleted students...</span>
    </div>
  `;

  // Show modal
  modal.classList.remove('hidden');

  // Fetch trash data
  fetch('/admin/students/trash-data')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        modalContent.innerHTML = generateTrashContent(data.students);
      } else {
        modalContent.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Trash</h3>
            <p class="text-gray-500">${data.message || 'Unable to load deleted students'}</p>
          </div>
        `;
      }
    })
    .catch(error => {
      console.error('Error:', error);
      modalContent.innerHTML = `
        <div class="text-center py-8">
          <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Connection Error</h3>
          <p class="text-gray-500">Unable to connect to server</p>
        </div>
      `;
    });
}

// Content generation functions
function generateViewContent(student) {
  return `
    <div class="space-y-6">
      <!-- Personal Information -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
          <i class="fas fa-user mr-2"></i>Personal Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-id-card text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Student ID:</span>
              <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${student.student_id || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-user text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Name:</span>
              <span class="ml-2">${student.name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-male text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Father's Name:</span>
              <span class="ml-2">${student.father_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-female text-pink-600 w-5 mr-2"></i>
              <span class="font-medium">Mother's Name:</span>
              <span class="ml-2">${student.mother_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-calendar text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">DOB:</span>
              <span class="ml-2">${student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '-'}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-venus-mars text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Gender:</span>
              <span class="ml-2 px-2 py-1 rounded text-sm ${student.gender === 'Male' ? 'bg-blue-100 text-blue-800' : student.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 'bg-gray-100 text-gray-800'}">${student.gender || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-pray text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Religion:</span>
              <span class="ml-2">${student.religion_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-users text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Caste Category:</span>
              <span class="ml-2">${student.caste_category_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-language text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Medium:</span>
              <span class="ml-2">${student.medium_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-ruler text-blue-600 w-5 mr-2"></i>
              <span class="font-medium">Height:</span>
              <span class="ml-2">${student.height || '-'}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Academic Information -->
      <div class="bg-green-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-green-800 mb-3 flex items-center">
          <i class="fas fa-graduation-cap mr-2"></i>Academic Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-school text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Class:</span>
              <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">${student.class || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-list text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Section:</span>
              <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">${student.section || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-stream text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Stream:</span>
              <span class="ml-2">${student.stream || '-'}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-hashtag text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Roll No:</span>
              <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">${student.roll_no || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-tools text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Trade:</span>
              <span class="ml-2">${student.trade || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-calendar-plus text-green-600 w-5 mr-2"></i>
              <span class="font-medium">Admission Date:</span>
              <span class="ml-2">${student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '-'}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact & Address -->
      <div class="bg-purple-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-purple-800 mb-3 flex items-center">
          <i class="fas fa-address-book mr-2"></i>Contact & Address
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-phone text-purple-600 w-5 mr-2"></i>
              <span class="font-medium">Contact:</span>
              <span class="ml-2">${student.contact_no || '-'}</span>
            </div>
            <div class="flex items-start">
              <i class="fas fa-home text-purple-600 w-5 mr-2 mt-1"></i>
              <div>
                <span class="font-medium">Address:</span>
                <div class="ml-2 text-sm text-gray-700">${student.cur_address || '-'}</div>
              </div>
            </div>
            <div class="flex items-center">
              <i class="fas fa-map-marker-alt text-purple-600 w-5 mr-2"></i>
              <span class="font-medium">Village/Ward:</span>
              <span class="ml-2">${student.village_ward || '-'}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-mail-bulk text-purple-600 w-5 mr-2"></i>
              <span class="font-medium">Pin Code:</span>
              <span class="ml-2">${student.pin_code || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-map text-purple-600 w-5 mr-2"></i>
              <span class="font-medium">State:</span>
              <span class="ml-2">${student.state_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-city text-purple-600 w-5 mr-2"></i>
              <span class="font-medium">District:</span>
              <span class="ml-2">${student.district_name || '-'}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="bg-orange-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-orange-800 mb-3 flex items-center">
          <i class="fas fa-info-circle mr-2"></i>Additional Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-hand-holding-heart text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">BPL Status:</span>
              <span class="ml-2 px-2 py-1 rounded text-sm ${student.bpl === 'Yes' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}">${student.bpl || 'No'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-wheelchair text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">Disability:</span>
              <span class="ml-2 px-2 py-1 rounded text-sm ${student.disability === 'Yes' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">${student.disability || 'No'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-weight text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">Weight:</span>
              <span class="ml-2">${student.weight || '-'}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center">
              <i class="fas fa-university text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">Bank:</span>
              <span class="ml-2">${student.bank_name || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-code text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">IFSC:</span>
              <span class="ml-2">${student.ifsc_code || '-'}</span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-credit-card text-orange-600 w-5 mr-2"></i>
              <span class="font-medium">Account Holder:</span>
              <span class="ml-2">${student.account_holder_name || '-'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function generateEditContent(student) {
  return `
    <form id="editStudentForm" class="space-y-6">
      <!-- Personal Information -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
          <i class="fas fa-user mr-2"></i>Personal Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-id-card mr-1"></i>Student ID
            </label>
            <input type="text" name="student_id" value="${student.student_id || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed" readonly>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-user mr-1"></i>Name *
            </label>
            <input type="text" name="name" value="${student.name || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-male mr-1"></i>Father's Name
            </label>
            <input type="text" name="father_name" value="${student.father_name || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-female mr-1"></i>Mother's Name
            </label>
            <input type="text" name="mother_name" value="${student.mother_name || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-calendar mr-1"></i>Date of Birth
            </label>
            <input type="date" name="dob" value="${student.dob ? student.dob.split('T')[0] : ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-venus-mars mr-1"></i>Gender
            </label>
            <select name="gender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">Select Gender</option>
              <option value="Male" ${student.gender === 'Male' ? 'selected' : ''}>Male</option>
              <option value="Female" ${student.gender === 'Female' ? 'selected' : ''}>Female</option>
              <option value="Other" ${student.gender === 'Other' ? 'selected' : ''}>Other</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Academic Information -->
      <div class="bg-green-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-green-800 mb-3 flex items-center">
          <i class="fas fa-graduation-cap mr-2"></i>Academic Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-school mr-1"></i>Class
            </label>
            <input type="text" name="class" value="${student.class || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-list mr-1"></i>Section
            </label>
            <input type="text" name="section" value="${student.section || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-stream mr-1"></i>Stream
            </label>
            <select name="stream" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
              <option value="">Select Stream</option>
              <option value="Science" ${student.stream === 'Science' ? 'selected' : ''}>Science</option>
              <option value="Commerce" ${student.stream === 'Commerce' ? 'selected' : ''}>Commerce</option>
              <option value="Arts" ${student.stream === 'Arts' ? 'selected' : ''}>Arts</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-hashtag mr-1"></i>Roll Number
            </label>
            <input type="text" name="roll_no" value="${student.roll_no || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-tools mr-1"></i>Trade
            </label>
            <input type="text" name="trade" value="${student.trade || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-calendar-plus mr-1"></i>Admission Date
            </label>
            <input type="date" name="admission_date" value="${student.admission_date ? student.admission_date.split('T')[0] : ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
          </div>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="bg-purple-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-purple-800 mb-3 flex items-center">
          <i class="fas fa-address-book mr-2"></i>Contact Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-phone mr-1"></i>Contact Number
            </label>
            <input type="text" name="contact_no" value="${student.contact_no || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-mail-bulk mr-1"></i>Pin Code
            </label>
            <input type="text" name="pin_code" value="${student.pin_code || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
          </div>
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-home mr-1"></i>Address
            </label>
            <textarea name="cur_address" rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">${student.cur_address || ''}</textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-map-marker-alt mr-1"></i>Village/Ward
            </label>
            <input type="text" name="village_ward" value="${student.village_ward || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-building mr-1"></i>Gram Panchayat
            </label>
            <input type="text" name="gram_panchayat" value="${student.gram_panchayat || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="bg-orange-50 rounded-lg p-4">
        <h4 class="text-lg font-semibold text-orange-800 mb-3 flex items-center">
          <i class="fas fa-info-circle mr-2"></i>Additional Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-hand-holding-heart mr-1"></i>BPL Status
            </label>
            <select name="bpl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
              <option value="">Select BPL Status</option>
              <option value="Yes" ${student.bpl === 'Yes' ? 'selected' : ''}>Yes</option>
              <option value="No" ${student.bpl === 'No' ? 'selected' : ''}>No</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-wheelchair mr-1"></i>Disability
            </label>
            <select name="disability" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
              <option value="">Select Disability Status</option>
              <option value="Yes" ${student.disability === 'Yes' ? 'selected' : ''}>Yes</option>
              <option value="No" ${student.disability === 'No' ? 'selected' : ''}>No</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-ruler mr-1"></i>Height (cm)
            </label>
            <input type="number" name="height" value="${student.height || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <i class="fas fa-weight mr-1"></i>Weight (kg)
            </label>
            <input type="number" name="weight" value="${student.weight || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
          </div>
        </div>
      </div>
    </form>
  `;
}

// Save student function
function saveStudent(studentId) {
  const form = document.getElementById('editStudentForm');
  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());

  fetch(`/admin/students/${studentId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      closeStudentModal();
      location.reload(); // Refresh the page to show updated data
    } else {
      alert('Error saving student: ' + result.message);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error saving student');
  });
}

function generateTrashContent(students) {
  if (!students || students.length === 0) {
    return `
      <div class="text-center py-8">
        <i class="fas fa-trash text-4xl text-gray-300 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Deleted Students</h3>
        <p class="text-gray-500">The trash is empty. No students have been deleted.</p>
      </div>
    `;
  }

  let tableContent = `
    <div class="mb-4">
      <h4 class="text-lg font-medium text-gray-900 mb-2">
        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
        Found ${students.length} deleted student${students.length !== 1 ? 's' : ''}
      </h4>
      <p class="text-sm text-gray-600">These students have been moved to trash and can be restored if needed.</p>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted At</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
  `;

  students.forEach(student => {
    const deletedAt = student.deleted_at ? new Date(student.deleted_at).toLocaleDateString('en-IN') : '-';
    tableContent += `
      <tr class="hover:bg-gray-50">
        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${student.student_id || '-'}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${student.name || '-'}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${student.class || '-'}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${student.section || '-'}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
          <span class="px-2 py-1 text-xs rounded-full ${student.gender === 'Male' ? 'bg-blue-100 text-blue-800' : student.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 'bg-gray-100 text-gray-800'}">
            ${student.gender || '-'}
          </span>
        </td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${student.contact_no || '-'}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${deletedAt}</td>
        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
          <div class="flex space-x-2">
            <button class="view-student-btn text-blue-600 hover:text-blue-900" data-student-id="${student.id}" title="View Details">
              <i class="fas fa-eye"></i>
            </button>
            <button class="edit-student-btn text-green-600 hover:text-green-900" data-student-id="${student.id}" title="Edit Student">
              <i class="fas fa-edit"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  });

  tableContent += `
        </tbody>
      </table>
    </div>
  `;

  return tableContent;
}

// Update import button to open modal
document.addEventListener('DOMContentLoaded', function() {
  const importBtn = document.querySelector('a[href="/admin/students/import"]');
  if (importBtn) {
    importBtn.addEventListener('click', function(e) {
      e.preventDefault();
      openImportModal();
    });
  }
});

// Functions are now handled by event delegation - no need for global scope
</script>
