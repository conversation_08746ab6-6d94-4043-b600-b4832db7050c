<!-- Student Import Results Page -->
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Import Results</h1>
                    <p class="text-gray-600 mt-1">Student data import completed</p>
                </div>
                <div class="flex space-x-3">
                    <a href="/admin/students/import"
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                        <i class="fas fa-upload mr-2"></i>
                        Import More
                    </a>
                    <a href="/admin/students/manage"
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                        <i class="fas fa-users mr-2"></i>
                        View Students
                    </a>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        <% if (typeof flashSuccess !== 'undefined' && flashSuccess) { %>
            <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <%= flashSuccess %>
                </div>
            </div>
        <% } %>

        <% if (typeof flashError !== 'undefined' && flashError) { %>
            <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <%= flashError %>
                </div>
            </div>
        <% } %>

        <% if (typeof flashInfo !== 'undefined' && flashInfo) { %>
            <div class="bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    <%= flashInfo %>
                </div>
            </div>
        <% } %>

        <!-- Import Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Import Summary</h2>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <div class="text-3xl font-bold text-green-600 mb-2">
                        <%= locals.results ? (locals.results.imported || 0) : 0 %>
                    </div>
                    <div class="text-lg font-semibold text-green-800">Students Imported</div>
                    <div class="text-sm text-green-600">New records created</div>
                </div>

                <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="text-3xl font-bold text-blue-600 mb-2">
                        <%= locals.results ? (locals.results.updated || 0) : 0 %>
                    </div>
                    <div class="text-lg font-semibold text-blue-800">Students Updated</div>
                    <div class="text-sm text-blue-600">Existing records modified</div>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <div class="text-3xl font-bold text-purple-600 mb-2">
                        <%= locals.results ? (locals.results.usersCreated || 0) : 0 %>
                    </div>
                    <div class="text-lg font-semibold text-purple-800">User Accounts Created</div>
                    <div class="text-sm text-purple-600">Student login accounts</div>
                </div>

                <div class="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                    <div class="text-3xl font-bold text-red-600 mb-2">
                        <%= locals.results ? (locals.results.skipped || 0) : 0 %>
                    </div>
                    <div class="text-lg font-semibold text-red-800">Records Skipped</div>
                    <div class="text-sm text-red-600">Due to errors</div>
                </div>
            </div>
        </div>

        <!-- User Creation Information -->
        <% if (locals.results && locals.results.usersCreated > 0) { %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-user-plus text-green-500 mr-2"></i>
                Student User Accounts Created
            </h2>

            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p class="text-green-800 text-sm mb-2">
                    <strong><%= locals.results.usersCreated %></strong> student user accounts have been automatically created with the following details:
                </p>
                <ul class="text-green-700 text-sm space-y-1">
                    <li>• <strong>Username:</strong> Student ID (lowercase)</li>
                    <li>• <strong>Password:</strong> Student ID + "123" (e.g., STU001123)</li>
                    <li>• <strong>Role:</strong> Student</li>
                    <li>• <strong>Email:</strong> Generated automatically if contact number provided</li>
                </ul>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="text-sm font-medium text-blue-800 mb-2">Important Notes:</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• Students can now log in to the platform using their credentials</li>
                    <li>• Advise students to change their default passwords after first login</li>
                    <li>• User accounts are automatically assigned to the "student" role group</li>
                    <li>• If a user account already existed, it was not recreated</li>
                </ul>
            </div>
        </div>
        <% } %>

        <!-- User Creation Errors -->
        <% if (locals.results && locals.results.userCreationErrors && locals.results.userCreationErrors.length > 0) { %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-exclamation-triangle text-orange-500 mr-2"></i>
                User Creation Errors (<%= locals.results.userCreationErrors.length %>)
            </h2>

            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                <p class="text-orange-800 text-sm">
                    Some user accounts could not be created. Students were imported successfully, but manual user creation may be required.
                </p>
            </div>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                <% locals.results.userCreationErrors.forEach((error, index) => { %>
                    <div class="flex items-start p-3 bg-orange-50 border border-orange-200 rounded-lg">
                        <div class="flex-shrink-0">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                                <span class="text-orange-600 font-semibold text-xs"><%= index + 1 %></span>
                            </div>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm text-orange-800"><%= error %></p>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Error Details -->
        <% if (errors && errors.length > 0) { %>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    Import Errors (<%= errors.length %>)
                </h2>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p class="text-red-800 text-sm">
                        The following errors occurred during import. Please review and correct the data before re-importing.
                    </p>
                </div>

                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <% errors.forEach((error, index) => { %>
                        <div class="flex items-start p-3 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                    <span class="text-red-600 font-semibold text-xs"><%= index + 1 %></span>
                                </div>
                            </div>
                            <div class="ml-3 flex-1">
                                <p class="text-sm text-red-800"><%= error %></p>
                            </div>
                        </div>
                    <% }); %>
                </div>

                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 class="text-sm font-medium text-yellow-800 mb-2">Common Issues:</h3>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• Missing required fields (Name, Student ID, Class, Session, Gender)</li>
                        <li>• Invalid date formats (use YYYY-MM-DD)</li>
                        <li>• Duplicate Student IDs</li>
                        <li>• Invalid gender values (must be Male, Female, or Other)</li>
                        <li>• Invalid numeric values for height/weight</li>
                    </ul>
                </div>
            </div>
        <% } %>

        <!-- Next Steps -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Next Steps</h2>

            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-users text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">View Imported Students</h3>
                        <p class="text-sm text-gray-600">Review the imported student data and make any necessary adjustments.</p>
                        <a href="/admin/students/manage" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Go to Student Management →
                        </a>
                    </div>
                </div>

                <% if (errors && errors.length > 0) { %>
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-edit text-red-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Fix Import Errors</h3>
                        <p class="text-sm text-gray-600">Correct the errors in your data file and re-import the failed records.</p>
                        <a href="/admin/students/import" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Import Again →
                        </a>
                    </div>
                </div>
                <% } %>

                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-classroom text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Assign Classrooms</h3>
                        <p class="text-sm text-gray-600">Assign students to specific classrooms if not already done during import.</p>
                        <a href="/principal/infrastructure" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Manage Classrooms →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clear flash messages -->
<%
if (typeof flashSuccess !== 'undefined') delete flashSuccess;
if (typeof flashError !== 'undefined') delete flashError;
if (typeof flashInfo !== 'undefined') delete flashInfo;
%>
