<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Import Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">Student Import Test Page</h1>
        
        <!-- Test Import Button -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Import Functionality</h2>
            <button id="testImportBtn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                <i class="fas fa-test-tube mr-2"></i>
                Run Test Import
            </button>
            <div id="testResults" class="mt-4 hidden"></div>
        </div>

        <!-- Debug Info -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
            <button id="debugBtn" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                <i class="fas fa-bug mr-2"></i>
                Get Debug Info
            </button>
            <div id="debugResults" class="mt-4 hidden"></div>
        </div>

        <!-- Manual Import Form -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Manual Import Test</h2>
            <form id="manualImportForm" enctype="multipart/form-data">
                <div class="mb-4">
                    <label for="session" class="block text-sm font-medium text-gray-700 mb-2">Session</label>
                    <select id="session" name="session" class="w-full px-3 py-2 border border-gray-300 rounded">
                        <option value="2024-25">2024-25</option>
                        <option value="2023-24">2023-24</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="file" class="block text-sm font-medium text-gray-700 mb-2">File</label>
                    <input type="file" id="file" name="file" accept=".csv,.xlsx,.xls" class="w-full px-3 py-2 border border-gray-300 rounded">
                </div>
                <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    <i class="fas fa-upload mr-2"></i>
                    Test Manual Import
                </button>
            </form>
            <div id="manualResults" class="mt-4 hidden"></div>
        </div>
    </div>

    <script>
        // Test Import
        document.getElementById('testImportBtn').addEventListener('click', async function() {
            const btn = this;
            const results = document.getElementById('testResults');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Running Test...';
            
            try {
                const response = await fetch('/admin/students/test-import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                results.innerHTML = `
                    <div class="p-4 rounded ${data.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        <h3 class="font-semibold">${data.success ? 'Success' : 'Error'}</h3>
                        <p>${data.message}</p>
                        ${data.results ? `
                            <div class="mt-2">
                                <p>Imported: ${data.results.imported}</p>
                                <p>Updated: ${data.results.updated}</p>
                                <p>Skipped: ${data.results.skipped}</p>
                                <p>Users Created: ${data.results.usersCreated}</p>
                                <p>Errors: ${data.results.errors.length}</p>
                            </div>
                        ` : ''}
                        ${data.error ? `<p class="mt-2 text-sm">Error: ${data.error}</p>` : ''}
                    </div>
                `;
                results.classList.remove('hidden');
                
            } catch (error) {
                results.innerHTML = `
                    <div class="p-4 rounded bg-red-100 text-red-800">
                        <h3 class="font-semibold">Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
                results.classList.remove('hidden');
            }
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-test-tube mr-2"></i>Run Test Import';
        });

        // Debug Info
        document.getElementById('debugBtn').addEventListener('click', async function() {
            const btn = this;
            const results = document.getElementById('debugResults');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Getting Debug Info...';
            
            try {
                const response = await fetch('/admin/students/debug');
                const data = await response.json();
                
                results.innerHTML = `
                    <div class="p-4 rounded bg-blue-100 text-blue-800">
                        <h3 class="font-semibold">Debug Information</h3>
                        <pre class="mt-2 text-sm overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                results.classList.remove('hidden');
                
            } catch (error) {
                results.innerHTML = `
                    <div class="p-4 rounded bg-red-100 text-red-800">
                        <h3 class="font-semibold">Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
                results.classList.remove('hidden');
            }
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-bug mr-2"></i>Get Debug Info';
        });

        // Manual Import
        document.getElementById('manualImportForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const results = document.getElementById('manualResults');
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/admin/students/import', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.redirected) {
                    results.innerHTML = `
                        <div class="p-4 rounded bg-blue-100 text-blue-800">
                            <h3 class="font-semibold">Redirected</h3>
                            <p>Form submitted successfully. Redirected to: ${response.url}</p>
                        </div>
                    `;
                } else {
                    const text = await response.text();
                    results.innerHTML = `
                        <div class="p-4 rounded bg-gray-100 text-gray-800">
                            <h3 class="font-semibold">Response</h3>
                            <pre class="mt-2 text-sm overflow-auto">${text}</pre>
                        </div>
                    `;
                }
                results.classList.remove('hidden');
                
            } catch (error) {
                results.innerHTML = `
                    <div class="p-4 rounded bg-red-100 text-red-800">
                        <h3 class="font-semibold">Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
                results.classList.remove('hidden');
            }
        });
    </script>
</body>
</html>
