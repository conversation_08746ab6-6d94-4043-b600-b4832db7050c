<!-- Student Import Page -->
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Import Students</h1>
                    <p class="text-gray-600 mt-1">Upload CSV or Excel files to import student data</p>
                </div>
                <div class="flex space-x-3">
                    <a href="/admin/students/import/template"
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                        <i class="fas fa-download mr-2"></i>
                        Download Template
                    </a>
                    <a href="/admin/students/import/sample"
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                        <i class="fas fa-file-csv mr-2"></i>
                        Sample Data
                    </a>
                    <a href="/admin/students/manage"
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                        <i class="fas fa-users mr-2"></i>
                        Manage Students
                    </a>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        <% if (typeof flashSuccess !== 'undefined' && flashSuccess) { %>
            <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <%= flashSuccess %>
                </div>
            </div>
        <% } %>

        <% if (typeof flashError !== 'undefined' && flashError) { %>
            <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <%= flashError %>
                </div>
            </div>
        <% } %>

        <% if (typeof flashInfo !== 'undefined' && flashInfo) { %>
            <div class="bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    <%= flashInfo %>
                </div>
            </div>
        <% } %>

        <!-- Import Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Upload Student Data</h2>

            <form action="/admin/students/import" method="POST" enctype="multipart/form-data" id="importForm">
                <div class="space-y-6">
                    <!-- Hidden Session (will use session from CSV or default) -->
                    <input type="hidden" id="session" name="session" value="2024-25">

                    <!-- File Upload -->
                    <div>
                        <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                            Select File (CSV or Excel) <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload a file</span>
                                        <input id="file" name="file" type="file" class="sr-only" accept=".csv,.xlsx,.xls" required>
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">CSV, XLSX, XLS up to 10MB</p>
                            </div>
                        </div>
                        <div id="fileInfo" class="mt-2 text-sm text-gray-600 hidden"></div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                                id="submitBtn">
                            <i class="fas fa-upload mr-2"></i>
                            Import Students
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Instructions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Import Instructions</h2>

            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">1</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Download Template</h3>
                        <p class="text-sm text-gray-600">Download the CSV template to see the required format and column headers.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">2</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Prepare Your Data</h3>
                        <p class="text-sm text-gray-600">Fill in the template with your student data. Required fields: Name, Student ID, Class, Gender. Session will be taken from CSV or set to current session.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">3</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-gray-900">Upload File</h3>
                        <p class="text-sm text-gray-600">Upload your CSV or Excel file. The system will validate and import the data.</p>
                    </div>
                </div>
            </div>

            <!-- Field Information -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Required Fields</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-red-600">* Name:</span> Student's full name
                    </div>
                    <div>
                        <span class="font-medium text-red-600">* Student ID:</span> Unique identifier
                    </div>
                    <div>
                        <span class="font-medium text-red-600">* Class:</span> Student's class (e.g., 10, 11, 12)
                    </div>
                    <div>
                        <span class="font-medium text-red-600">* Gender:</span> Male, Female, or Other
                    </div>
                    <div>
                        <span class="font-medium text-gray-600">Session:</span> Academic session (optional, defaults to 2024-25)
                    </div>
                </div>

                <h3 class="text-sm font-medium text-gray-900 mb-3 mt-4">Optional Fields</h3>
                <div class="text-sm text-gray-600">
                    <p>Father Name, Mother Name, DOB, Section, Stream, Trade, Caste Category, BPL Status, Disability, Religion, Medium, Height, Weight, Admission Details, Address, Contact Information, Bank Details, Room Number, etc.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    const fileInfo = document.getElementById('fileInfo');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('importForm');

    // Function to show messages to user
    function showMessage(message, type = 'info') {
        // Remove any existing message
        const existingMessage = document.querySelector('.dynamic-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create new message element
        const messageDiv = document.createElement('div');
        messageDiv.className = 'dynamic-message mb-6 px-4 py-3 rounded-lg';

        let bgColor, textColor, icon;
        switch (type) {
            case 'success':
                bgColor = 'bg-green-50 border border-green-200';
                textColor = 'text-green-800';
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                bgColor = 'bg-red-50 border border-red-200';
                textColor = 'text-red-800';
                icon = 'fas fa-exclamation-circle';
                break;
            case 'info':
            default:
                bgColor = 'bg-blue-50 border border-blue-200';
                textColor = 'text-blue-800';
                icon = 'fas fa-info-circle';
                break;
        }

        messageDiv.className += ` ${bgColor} ${textColor}`;
        messageDiv.innerHTML = `
            <div class="flex items-center">
                <i class="${icon} mr-2"></i>
                ${message}
            </div>
        `;

        // Insert message after the header
        const header = document.querySelector('.bg-white.rounded-lg.shadow-sm.border.border-gray-200.p-6.mb-8');
        header.insertAdjacentElement('afterend', messageDiv);

        // Auto-remove success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }
    }

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileInfo.innerHTML = `
                <div class="flex items-center text-green-600">
                    <i class="fas fa-file mr-2"></i>
                    <span>Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                </div>
            `;
            fileInfo.classList.remove('hidden');
        } else {
            fileInfo.classList.add('hidden');
        }
    });

    // Form submit handler - Use AJAX to prevent page refresh
    form.addEventListener('submit', async function(e) {
        e.preventDefault(); // Always prevent default form submission

        const sessionSelect = document.getElementById('session');

        if (!fileInput.files[0]) {
            showMessage('Please select a file to upload', 'error');
            fileInput.focus();
            return;
        }

        // Validate file type
        const file = fileInput.files[0];
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

        if (!allowedTypes.includes(fileExtension)) {
            showMessage('Please select a valid CSV or Excel file', 'error');
            fileInput.focus();
            return;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Importing...';
        submitBtn.disabled = true;

        try {
            // Create FormData for AJAX submission
            const formData = new FormData();
            formData.append('session', sessionSelect.value);
            formData.append('file', file);

            console.log('Starting AJAX import...');

            const response = await fetch('/admin/students/import', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
                }
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (response.ok) {
                // Try to parse as JSON first
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const result = await response.json();
                    console.log('Import JSON response:', result);

                    if (result.success) {
                        const message = `Import completed successfully! Imported: ${result.results.imported}, Updated: ${result.results.updated}, Users Created: ${result.results.usersCreated}`;
                        showMessage(message, 'success');

                        // Redirect to results page after showing success message
                        setTimeout(() => {
                            window.location.href = result.redirectUrl || '/admin/students/import/results';
                        }, 3000);
                    } else {
                        showMessage(result.message || 'Import failed', 'error');
                    }
                } else {
                    // Handle text/HTML response (fallback)
                    const result = await response.text();
                    console.log('Import text response:', result);
                    showMessage('Import completed successfully!', 'success');

                    // Redirect to results page after a short delay
                    setTimeout(() => {
                        window.location.href = '/admin/students/import/results';
                    }, 2000);
                }
            } else {
                // Handle error response
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const errorData = await response.json();
                    console.error('Import error JSON:', errorData);
                    showMessage(errorData.message || `Import failed: ${response.status} ${response.statusText}`, 'error');
                } else {
                    const errorText = await response.text();
                    console.error('Import error text:', errorText);
                    showMessage(`Import failed: ${response.status} ${response.statusText}`, 'error');
                }
            }

        } catch (error) {
            console.error('Import error:', error);
            showMessage(`Import failed: ${error.message}`, 'error');
        } finally {
            // Reset button state
            submitBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Import Students';
            submitBtn.disabled = false;
        }
    });

    // Drag and drop functionality
    const dropZone = document.querySelector('.border-dashed');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        dropZone.classList.add('border-blue-400', 'bg-blue-50');
    }

    function unhighlight(e) {
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
    }

    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    }
});
</script>

<!-- Flash messages will be cleared by the route after rendering -->
