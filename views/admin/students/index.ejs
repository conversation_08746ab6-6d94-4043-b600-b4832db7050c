<!-- Student Management -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-purple-600 text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Student Management</h2>
    <div class="flex space-x-2">
      <a href="/admin/users" class="bg-white text-purple-600 px-3 py-1 rounded text-sm hover:bg-purple-100 transition">
        <i class="fas fa-users mr-1"></i> All Users
      </a>
    </div>
  </div>
  <div class="p-6">
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Students</h3>
        <div class="flex space-x-2">
          <div class="relative">
            <input type="text" id="searchInput" placeholder="Search students..." class="border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
            <button id="clearSearch" class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table id="studentsTable" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% if (students && students.length > 0) { %>
              <% students.forEach(student => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <% if (student.profile_image) { %>
                          <img class="h-10 w-10 rounded-full" src="<%= student.profile_image %>" alt="<%= student.username %>">
                        <% } else { %>
                          <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                            <span class="text-purple-600 font-medium"><%= student.username.charAt(0).toUpperCase() %></span>
                          </div>
                        <% } %>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900"><%= student.username %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500"><%= student.email %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= student.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                      <%= student.is_active ? 'Active' : 'Inactive' %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <a href="/admin/students/<%= student.id %>/assign-class" class="text-indigo-600 hover:text-indigo-900">
                        <i class="fas fa-school mr-1"></i> Assign Class
                      </a>
                      <a href="/admin/students/<%= student.id %>/assign-subjects" class="text-green-600 hover:text-green-900">
                        <i class="fas fa-book mr-1"></i> Assign Subjects
                      </a>
                    </div>
                  </td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                  No students found
                </td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const clearSearch = document.getElementById('clearSearch');
    const table = document.getElementById('studentsTable');
    const rows = table.querySelectorAll('tbody tr');
    
    // Search functionality
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      
      rows.forEach(row => {
        const username = row.querySelector('td:first-child').textContent.toLowerCase();
        const email = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        if (username.includes(searchTerm) || email.includes(searchTerm)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
    
    // Clear search
    clearSearch.addEventListener('click', function() {
      searchInput.value = '';
      rows.forEach(row => {
        row.style.display = '';
      });
    });
  });
</script>
