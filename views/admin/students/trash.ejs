<!-- Student Trash Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <!-- Header -->
  <div class="bg-gradient-to-r from-red-600 to-red-700 text-white p-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Student Trash</h1>
        <p class="text-red-100 mt-1">Manage deleted students - restore or permanently delete</p>
      </div>
      <div class="flex flex-wrap gap-3">
        <a href="/admin/students/data" class="bg-white text-red-600 px-4 py-2 rounded-lg font-medium hover:bg-red-50 transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Back to Students
        </a>
        <button id="bulkRestoreBtn" class="bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-undo mr-2"></i>Restore Selected
        </button>
        <button id="bulkPermanentDeleteBtn" class="bg-red-800 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-trash-alt mr-2"></i>Delete Forever
        </button>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  <% if (flashSuccess) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-check-circle mr-2"></i><%= flashSuccess %>
    </div>
  <% } %>
  <% if (flashError) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded">
      <i class="fas fa-exclamation-circle mr-2"></i><%= flashError %>
    </div>
  <% } %>

  <!-- Search Section -->
  <div class="p-6 bg-gray-50 border-b">
    <form method="GET" class="flex items-center space-x-4">
      <div class="flex-1">
        <input type="text" name="search" value="<%= filters.search %>" 
               placeholder="Search deleted students..." 
               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
      </div>
      <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
        <i class="fas fa-search mr-2"></i>Search
      </button>
      <% if (filters.search) { %>
        <a href="/admin/students/trash" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
          <i class="fas fa-times mr-2"></i>Clear
        </a>
      <% } %>
    </form>
  </div>

  <!-- Results Summary -->
  <div class="px-6 py-3 bg-red-50 border-b">
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-600">
        <i class="fas fa-trash text-red-500 mr-2"></i>
        Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
        <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
        of <%= pagination.totalStudents %> deleted students
      </div>
      <div class="text-sm text-gray-600">
        Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted On</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (students && students.length > 0) { %>
          <% students.forEach((student, index) => { %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="student-checkbox rounded border-gray-300 text-red-600 focus:ring-red-500" value="<%= student.id %>">
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <%= student.student_id || '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.name || '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.class || '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.section || '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= student.contact_no || '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= student.updated_at ? new Date(student.updated_at).toLocaleDateString('en-IN') : '-' %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button onclick="restoreStudent(<%= student.id %>)" class="text-green-600 hover:text-green-900" title="Restore">
                    <i class="fas fa-undo"></i>
                  </button>
                  <button onclick="permanentDeleteStudent(<%= student.id %>)" class="text-red-600 hover:text-red-900" title="Delete Forever">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </td>
            </tr>
          <% }); %>
        <% } else { %>
          <tr>
            <td colspan="8" class="px-6 py-12 text-center text-gray-500">
              <div class="flex flex-col items-center">
                <i class="fas fa-trash text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No deleted students found</h3>
                <p class="text-gray-500">The trash is empty.</p>
              </div>
            </td>
          </tr>
        <% } %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <% if (pagination.totalPages > 1) { %>
    <div class="px-6 py-4 bg-gray-50 border-t">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to
          <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalStudents) %>
          of <%= pagination.totalStudents %> results
        </div>
        <div class="flex space-x-1">
          <!-- Previous Page -->
          <% if (pagination.hasPrev) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage - 1}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
              <i class="fas fa-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            const startPage = Math.max(1, pagination.currentPage - 2);
            const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
          %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === pagination.currentPage) { %>
              <span class="px-3 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-md">
                <%= i %>
              </span>
            <% } else { %>
              <a href="?<%= new URLSearchParams({...filters, page: i}).toString() %>"
                 class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <!-- Next Page -->
          <% if (pagination.hasNext) { %>
            <a href="?<%= new URLSearchParams({...filters, page: pagination.currentPage + 1}).toString() %>"
               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
              <i class="fas fa-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              <i class="fas fa-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Bulk selection functionality
  const selectAllCheckbox = document.getElementById('selectAll');
  const studentCheckboxes = document.querySelectorAll('.student-checkbox');
  const bulkRestoreBtn = document.getElementById('bulkRestoreBtn');
  const bulkPermanentDeleteBtn = document.getElementById('bulkPermanentDeleteBtn');

  // Select all functionality
  selectAllCheckbox.addEventListener('change', function() {
    studentCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    updateBulkButtons();
  });

  // Individual checkbox functionality
  studentCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      updateSelectAllState();
      updateBulkButtons();
    });
  });

  function updateSelectAllState() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    selectAllCheckbox.checked = checkedBoxes.length === studentCheckboxes.length;
    selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < studentCheckboxes.length;
  }

  function updateBulkButtons() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const hasSelection = checkedBoxes.length > 0;
    
    bulkRestoreBtn.disabled = !hasSelection;
    bulkPermanentDeleteBtn.disabled = !hasSelection;
    
    if (hasSelection) {
      bulkRestoreBtn.classList.remove('opacity-50', 'cursor-not-allowed');
      bulkPermanentDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
      bulkRestoreBtn.classList.add('opacity-50', 'cursor-not-allowed');
      bulkPermanentDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
  }

  // Bulk restore functionality
  bulkRestoreBtn.addEventListener('click', function() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    if (checkedBoxes.length === 0) return;
    
    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    if (confirm(`Are you sure you want to restore ${studentIds.length} students?`)) {
      // Create form and submit
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/admin/students/bulk-restore';
      
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = 'studentIds';
      input.value = studentIds.join(',');
      
      form.appendChild(input);
      document.body.appendChild(form);
      form.submit();
    }
  });

  // Bulk permanent delete functionality
  bulkPermanentDeleteBtn.addEventListener('click', function() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    if (checkedBoxes.length === 0) return;
    
    const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
    if (confirm(`Are you sure you want to PERMANENTLY delete ${studentIds.length} students? This action cannot be undone!`)) {
      // Create form and submit
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/admin/students/permanent-delete';
      
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = 'studentIds';
      input.value = studentIds.join(',');
      
      form.appendChild(input);
      document.body.appendChild(form);
      form.submit();
    }
  });
});

// Individual student actions
function restoreStudent(id) {
  if (confirm('Are you sure you want to restore this student?')) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/students/restore';
    
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'studentIds';
    input.value = id;
    
    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
  }
}

function permanentDeleteStudent(id) {
  if (confirm('Are you sure you want to PERMANENTLY delete this student? This action cannot be undone!')) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/students/permanent-delete';
    
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'studentIds';
    input.value = id;
    
    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
  }
}
</script>
