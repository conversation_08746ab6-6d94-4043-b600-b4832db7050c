<!-- Test Assignments Admin Page -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Test Assignments</h1>
    <button id="createAssignmentBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      Create Assignment
    </button>
  </div>

  <!-- Quick Filters Section -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-700 mb-4">Quick Filters</h3>
      <div class="flex flex-wrap gap-2 mb-4">
        <a href="/admin/test-assignments<%= query?.search ? '?search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= !query?.status || query?.status === 'all' ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
          All Assignments (<%= stats.totalCount %>)
        </a>
        <a href="/admin/test-assignments?status=active<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'active' ? 'bg-green-700 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200' %> text-xs font-medium">
          Active Assignments (<%= stats.activeCount %>)
        </a>
        <a href="/admin/test-assignments?status=expired<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'expired' ? 'bg-red-700 text-white' : 'bg-red-100 text-red-800 hover:bg-red-200' %> text-xs font-medium">
          Expired/Inactive Assignments (<%= stats.inactiveCount %>)
        </a>
      </div>
    </div>
  </div>

  <!-- Advanced Filters and Search Section -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <form id="filterForm" action="/admin/test-assignments" method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search -->
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div class="relative">
              <input type="text"
                     id="search"
                     name="search"
                     value="<%= query.search || '' %>"
                     placeholder="Search by exam, user, or group"
                     class="w-full border border-gray-300 rounded-md shadow-sm pl-10 pr-4 py-2 focus:ring-blue-500 focus:border-blue-500">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- Type Filter -->
          <div>
            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Assignment Type</label>
            <select id="type"
                    name="type"
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
              <option value="all" <%= query.type === 'all' ? 'selected' : '' %>>All Types</option>
              <option value="user" <%= query.type === 'user' ? 'selected' : '' %>>User Assignments</option>
              <option value="group" <%= query.type === 'group' ? 'selected' : '' %>>Group Assignments</option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status"
                    name="status"
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
              <option value="all" <%= query.status === 'all' ? 'selected' : '' %>>All Statuses</option>
              <option value="active" <%= query.status === 'active' ? 'selected' : '' %>>Active</option>
              <option value="expired" <%= query.status === 'expired' ? 'selected' : '' %>>Expired</option>
            </select>
          </div>

          <!-- Sort Filter -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <select id="sort"
                    name="sort"
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
              <option value="newest" <%= query.sort === 'newest' ? 'selected' : '' %>>Newest First</option>
              <option value="oldest" <%= query.sort === 'oldest' ? 'selected' : '' %>>Oldest First</option>
              <option value="exam_name" <%= query.sort === 'exam_name' ? 'selected' : '' %>>Exam Name</option>
              <option value="end_date" <%= query.sort === 'end_date' ? 'selected' : '' %>>End Date</option>
            </select>
          </div>
        </div>

        <!-- Apply Filters Button -->
        <div class="flex justify-end">
          <button type="submit"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
            Apply Filters
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Assignments Table -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Attempts</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resumable</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned By</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (assignments.length === 0) { %>
            <tr>
              <td colspan="7" class="px-6 py-4 text-center text-gray-500">No assignments found</td>
            </tr>
          <% } else { %>
            <% assignments.forEach(assignment => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= assignment.exam_name %></div>
                  <div class="text-sm text-gray-500">Duration: <%= assignment.duration %> minutes</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (assignment.user_id) { %>
                    <div class="text-sm font-medium text-gray-900"><%= assignment.user_username %></div>
                    <div class="text-sm text-gray-500"><%= assignment.user_email %></div>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">User</span>
                  <% } else if (assignment.group_id) { %>
                    <div class="text-sm font-medium text-gray-900"><%= assignment.group_name %></div>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Group</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= assignment.max_attempts %></div>
                  <div class="text-sm text-gray-500">Used: <%= assignment.total_attempts || 0 %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (assignment.is_resumable === 1) { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">No</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (assignment.end_datetime) { %>
                    <div class="text-sm text-gray-900"><%= formatDateTime(assignment.end_datetime) %></div>
                    <%
                      const now = new Date();
                      const endDate = new Date(assignment.end_datetime);
                      const isExpired = endDate < now;
                    %>
                    <% if (isExpired) { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Expired</span>
                    <% } else { %>
                      <%
                        const diffTime = Math.abs(endDate - now);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      %>
                      <% if (diffDays <= 3) { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Ending soon</span>
                      <% } %>
                    <% } %>
                  <% } else { %>
                    <div class="text-sm text-gray-500">No end date</div>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (assignment.is_active === 1) { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= assignment.assigned_by_username || 'Unknown' %></div>
                  <div class="text-sm text-gray-500"><%= formatDateTime(assignment.assigned_at) %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <!-- Toggle Status Button -->
                    <button onclick="toggleStatus('<%= assignment.assignment_id %>', <%= assignment.is_active %>)"
                            class="<%= assignment.is_active === 1 ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' %>"
                            title="<%= assignment.is_active === 1 ? 'Deactivate' : 'Activate' %>">
                      <% if (assignment.is_active === 1) { %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      <% } else { %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      <% } %>
                    </button>

                    <!-- Delete Button -->
                    <button onclick="confirmDelete('<%= assignment.assignment_id %>', '<%= assignment.exam_name %>')" class="text-red-600 hover:text-red-900" title="Delete">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium"><%= (pagination.currentPage - 1) * pagination.perPage + 1 %></span> to
          <span class="font-medium"><%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalAssignments) %></span> of
          <span class="font-medium"><%= pagination.totalAssignments %></span> assignments
        </div>
        <div class="flex space-x-2">
          <% if (pagination.currentPage > 1) { %>
            <a href="/admin/test-assignments?page=<%= pagination.currentPage - 1 %><%= query.search ? '&search=' + query.search : '' %><%= query.type && query.type !== 'all' ? '&type=' + query.type : '' %><%= query.status && query.status !== 'all' ? '&status=' + query.status : '' %><%= query.sort && query.sort !== 'newest' ? '&sort=' + query.sort : '' %>"
               class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </a>
          <% } %>
          <% if (pagination.currentPage < pagination.totalPages) { %>
            <a href="/admin/test-assignments?page=<%= pagination.currentPage + 1 %><%= query.search ? '&search=' + query.search : '' %><%= query.type && query.type !== 'all' ? '&type=' + query.type : '' %><%= query.status && query.status !== 'all' ? '&status=' + query.status : '' %><%= query.sort && query.sort !== 'newest' ? '&sort=' + query.sort : '' %>"
               class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Next
            </a>
          <% } %>
        </div>
      </div>
    <% } %>
  </div>
</div>

<!-- Create Assignment Modal -->
<div id="createAssignmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create Test Assignment</h3>
        <button type="button" onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-500">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form id="assignmentForm" onsubmit="return false;">
        <!-- Exam Selection -->
        <div class="mb-4">
          <label for="exam_id" class="block text-sm font-medium text-gray-700 mb-1">Select Exam *</label>
          <select id="exam_id" name="exam_id" required class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Select an exam</option>
            <% exams.forEach(exam => { %>
              <option value="<%= exam.exam_id %>"><%= exam.exam_name %> (<%= exam.status === 'published' ? 'Published' : 'Scheduled' %><%= exam.end_date ? `, Ends: ${formatDate(exam.end_date)}` : '' %>)</option>
            <% }); %>
          </select>
          <p class="mt-1 text-xs text-gray-500">Note: Only published or scheduled tests that are not archived and have not expired are available for assignment.</p>
        </div>

        <!-- Assignment Type -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Assignment Type *</label>
          <div class="flex space-x-4">
            <label class="inline-flex items-center">
              <input type="radio" name="assignment_type" value="user" checked class="form-radio h-4 w-4 text-blue-600">
              <span class="ml-2 text-gray-700">Assign to User</span>
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="assignment_type" value="group" class="form-radio h-4 w-4 text-blue-600">
              <span class="ml-2 text-gray-700">Assign to Group</span>
            </label>
          </div>
        </div>

        <!-- User Selection (shown by default) -->
        <div id="userSelection" class="mb-4">
          <label for="user_ids" class="block text-sm font-medium text-gray-700 mb-1">Select Users *</label>
          <select id="user_ids" name="user_ids[]" multiple class="chosen-select w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
            <% users.forEach(user => { %>
              <option value="<%= user.id %>"><%= user.username %> (<%= user.email %>)</option>
            <% }); %>
          </select>
          <p class="mt-1 text-xs text-gray-500">You can select multiple users</p>
        </div>

        <!-- Group Selection (hidden by default) -->
        <div id="groupSelection" class="mb-4 hidden">
          <label for="group_ids" class="block text-sm font-medium text-gray-700 mb-1">Select Groups *</label>
          <select id="group_ids" name="group_ids[]" multiple class="chosen-select w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
            <% groups.forEach(group => { %>
              <option value="<%= group.group_id %>"><%= group.name %></option>
            <% }); %>
          </select>
          <p class="mt-1 text-xs text-gray-500">You can select multiple groups</p>
        </div>

        <!-- Max Attempts -->
        <div class="mb-4">
          <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-1">Max Attempts</label>
          <input type="number" id="max_attempts" name="max_attempts" min="1" value="1" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
          <p class="mt-1 text-sm text-gray-500">Number of times a user can take this test</p>
        </div>

        <!-- Resumable Option -->
        <div class="mb-4">
          <div class="flex items-center space-x-2">
            <input type="checkbox" id="is_resumable" name="is_resumable" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_resumable" class="text-sm font-medium text-gray-700">Allow students to resume test</label>
          </div>
          <p class="mt-1 text-sm text-gray-500 ml-6">If checked, students can pause and resume the test later. Otherwise, they must complete the test in one sitting.</p>
        </div>

        <!-- End Date/Time -->
        <div class="mb-4">
          <label for="end_datetime" class="block text-sm font-medium text-gray-700 mb-1">End Date/Time</label>
          <input type="datetime-local" id="end_datetime" name="end_datetime" min="<%= new Date().toISOString().slice(0, 16) %>" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
          <p class="mt-1 text-sm text-gray-500">Leave blank for no end date, or select a future date/time</p>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" onclick="closeCreateModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            Cancel
          </button>
          <button type="button" id="submitAssignmentBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Create Assignment
          </button>
        </div>
        <div id="formErrorMessage" class="mt-3 text-red-500 text-sm hidden"></div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
      <p class="text-sm text-gray-600 mb-4">
        Are you sure you want to delete the assignment for <span id="deleteExamName" class="font-semibold"></span>?
        This action cannot be undone.
      </p>
      <form id="deleteForm" method="POST">
        <div class="flex justify-end space-x-3">
          <button type="button" onclick="closeDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  // Inline implementation of toast-notifications.js
  const ToastNotifications = {
    init: function() {
      // Create toast container if it doesn't exist
      if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
        document.body.appendChild(toastContainer);
      }
    },

    show: function(message, type = 'info', duration = 5000) {
      this.init();

      // Create toast element
      const toast = document.createElement('div');
      toast.className = `flex items-center p-3 rounded shadow-lg transition-all transform translate-x-0 max-w-xs ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' :
        'bg-blue-600'
      }`;

      // Add content
      toast.innerHTML = `
        <div class="flex-grow text-white text-sm">
          ${message}
        </div>
        <button class="ml-2 text-white focus:outline-none" onclick="this.parentElement.remove()">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      `;

      // Add to container
      const container = document.getElementById('toast-container');
      container.appendChild(toast);

      // Auto remove after duration
      setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s';
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, duration);
    },

    success: function(message, duration = 5000) {
      this.show(message, 'success', duration);
    },

    error: function(message, duration = 5000) {
      this.show(message, 'error', duration);
    },

    info: function(message, duration = 5000) {
      this.show(message, 'info', duration);
    },

    warning: function(message, duration = 5000) {
      this.show(message, 'warning', duration);
    }
  };

  // Initialize toast notifications when the DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    ToastNotifications.init();
  });

  // Direct CDN links for chosen library
  // Load CSS
  document.write('<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">');
  // Load JS
  document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"><\/script>');

  // Initialize chosen when document is ready
  $(document).ready(function() {
    // Initialize the exam select element
    const examSelect = document.getElementById('exam_id');
    if (examSelect) {
      console.log('Exam select found, options count:', examSelect.options.length);
      // Log all available options
      for (let i = 0; i < examSelect.options.length; i++) {
        console.log(`Option ${i}: value=${examSelect.options[i].value}, text=${examSelect.options[i].text}`);
      }

      // Add change event listener
      examSelect.addEventListener('change', function() {
        console.log('Exam select changed to:', this.value, this.options[this.selectedIndex]?.text);
      });
    } else {
      console.error('Exam select element not found');
    }

    setTimeout(function() {
      try {
        // Ensure select elements have the correct name attribute
        $('#user_ids').attr('name', 'user_ids[]');
        $('#group_ids').attr('name', 'group_ids[]');

        // Initialize chosen
        $('.chosen-select').chosen({
          width: '100%',
          search_contains: true,
          placeholder_text_multiple: 'Select options'
        });

        console.log('Chosen library initialized successfully');
        console.log('User select name:', $('#user_ids').attr('name'));
        console.log('Group select name:', $('#group_ids').attr('name'));
      } catch (e) {
        console.error('Error initializing chosen library:', e);
        // Fallback to standard select
        $('.chosen-select').each(function() {
          $(this).after('<p class="text-xs text-red-500 mt-1">Hold Ctrl/Cmd to select multiple options</p>');
        });
      }
    }, 500); // Small delay to ensure library is loaded
  });

  // Prepare form and validate before submission
  function prepareAndValidateForm() {
    // Ensure chosen select values are properly included in the form submission
    try {
      // For user selection
      if (document.querySelector('input[name="assignment_type"]:checked').value === 'user') {
        // Make sure the hidden select has the selected values
        const selectedUsers = $('#user_ids').val();
        console.log('Selected users before submission:', selectedUsers);

        // Ensure the select element has the correct name attribute
        $('#user_ids').attr('name', 'user_ids[]');

        // Update chosen if available
        if ($.fn.chosen && $('#user_ids').data('chosen')) {
          $('#user_ids').trigger('chosen:updated');
        }
      } else {
        // For group selection
        const selectedGroups = $('#group_ids').val();
        console.log('Selected groups before submission:', selectedGroups);

        // Ensure the select element has the correct name attribute
        $('#group_ids').attr('name', 'group_ids[]');

        // Update chosen if available
        if ($.fn.chosen && $('#group_ids').data('chosen')) {
          $('#group_ids').trigger('chosen:updated');
        }
      }
    } catch (e) {
      console.error('Error preparing form:', e);
    }

    // Call the validation function
    return validateForm();
  }

  // Form validation
  function validateForm() {
    console.log('Validating form...');
    const formErrorMessage = document.getElementById('formErrorMessage');
    formErrorMessage.classList.add('hidden');

    // Check if an exam is selected - more lenient approach
    const examSelect = document.getElementById('exam_id');
    const selectedIndex = examSelect.selectedIndex;
    const examId = examSelect.value;
    const examText = examSelect.options[selectedIndex]?.text || 'None';
    console.log('Exam validation - ID:', examId, 'Text:', examText, 'Index:', selectedIndex);

    // Consider it valid if either:
    // 1. It has a non-empty value
    // 2. It has a selected index > 0 (not the placeholder)
    // 3. The selected text is not the placeholder
    if (selectedIndex > 0 || (examText && examText !== 'Select an exam' && examText !== 'None')) {
      console.log('Exam validation passed');
      return true;
    }

    // Only show error if truly nothing is selected
    formErrorMessage.textContent = 'Please select an exam';
    formErrorMessage.classList.remove('hidden');
    ToastNotifications.error('Please select an exam');
    console.error('Exam validation failed');
    return false;

    // Check assignment type and corresponding selection
    const assignmentType = document.querySelector('input[name="assignment_type"]:checked').value;
    console.log('Assignment type:', assignmentType);

    if (assignmentType === 'user') {
      // For user selection
      try {
        const selectedUsers = $('#user_ids').val();
        console.log('Selected users:', selectedUsers);

        if (!selectedUsers || selectedUsers.length === 0) {
          ToastNotifications.error('Please select at least one user');
          return false;
        }

        // Ensure the select element has a name attribute
        const userSelect = document.getElementById('user_ids');
        if (userSelect && !userSelect.hasAttribute('name')) {
          userSelect.setAttribute('name', 'user_ids[]');
          console.log('Added name attribute to user_ids select');
        }
      } catch (e) {
        console.error('Error validating user selection:', e);
        ToastNotifications.error('Error validating user selection');
        return false;
      }
    } else {
      // For group selection
      try {
        const selectedGroups = $('#group_ids').val();
        console.log('Selected groups:', selectedGroups);

        if (!selectedGroups || selectedGroups.length === 0) {
          ToastNotifications.error('Please select at least one group');
          return false;
        }

        // Ensure the select element has a name attribute
        const groupSelect = document.getElementById('group_ids');
        if (groupSelect && !groupSelect.hasAttribute('name')) {
          groupSelect.setAttribute('name', 'group_ids[]');
          console.log('Added name attribute to group_ids select');
        }
      } catch (e) {
        console.error('Error validating group selection:', e);
        ToastNotifications.error('Error validating group selection');
        return false;
      }
    }

    // Validate end datetime if provided
    const endDatetime = document.getElementById('end_datetime').value;
    console.log('End datetime:', endDatetime);
    if (endDatetime) {
      const endDate = new Date(endDatetime);
      const now = new Date();

      if (endDate <= now) {
        ToastNotifications.error('End date must be in the future');
        return false;
      }
    }

    console.log('Form validation passed');
    return true;
  }



  // Toggle between user and group selection
  document.querySelectorAll('input[name="assignment_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
      if (this.value === 'user') {
        document.getElementById('userSelection').classList.remove('hidden');
        document.getElementById('groupSelection').classList.add('hidden');
        try {
          $('#group_ids').val([]);
          if ($.fn.chosen) {
            $('#group_ids').trigger('chosen:updated');
          }
        } catch (e) {
          console.error('Error updating group_ids:', e);
        }
      } else {
        document.getElementById('userSelection').classList.add('hidden');
        document.getElementById('groupSelection').classList.remove('hidden');
        try {
          $('#user_ids').val([]);
          if ($.fn.chosen) {
            $('#user_ids').trigger('chosen:updated');
          }
        } catch (e) {
          console.error('Error updating user_ids:', e);
        }
      }
    });
  });

  // Create Assignment Modal
  document.getElementById('createAssignmentBtn').addEventListener('click', function() {
    // Reset form before showing
    const form = document.getElementById('assignmentForm');
    form.reset();

    // Reset error message
    const formErrorMessage = document.getElementById('formErrorMessage');
    formErrorMessage.textContent = '';
    formErrorMessage.classList.add('hidden');

    // Reset select elements
    const examSelect = document.getElementById('exam_id');
    if (examSelect) {
      // Select the first option (which should be the empty one)
      examSelect.selectedIndex = 0;
      console.log('Reset exam select to:', examSelect.value);
    }

    // Reset chosen selects
    try {
      $('#user_ids').val([]);
      $('#group_ids').val([]);

      if ($.fn.chosen) {
        $('#user_ids').trigger('chosen:updated');
        $('#group_ids').trigger('chosen:updated');
      }
    } catch (e) {
      console.error('Error resetting chosen selects:', e);
    }

    // Show the modal
    document.getElementById('createAssignmentModal').classList.remove('hidden');
  });

  function closeCreateModal() {
    document.getElementById('createAssignmentModal').classList.add('hidden');
  }

  // Delete Confirmation Modal
  function confirmDelete(assignmentId, examName) {
    document.getElementById('deleteExamName').textContent = examName;
    document.getElementById('deleteForm').action = `/admin/test-assignments/${assignmentId}/delete`;
    document.getElementById('deleteModal').classList.remove('hidden');
  }

  function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
  }

  // Toggle Assignment Status
  function toggleStatus(assignmentId, currentStatus) {
    fetch(`/admin/test-assignments/${assignmentId}/toggle-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Reload the page to show updated status
        window.location.reload();
      } else {
        alert(data.message || 'Error toggling status');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('An error occurred while toggling status');
    });
  }

  // Close modals when clicking outside
  document.getElementById('createAssignmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeCreateModal();
    }
  });

  document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeDeleteModal();
    }
  });

  // Close modals on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeCreateModal();
      closeDeleteModal();
    }
  });

  // Auto-submit form when filters change
  document.querySelectorAll('#type, #status, #sort').forEach(select => {
    select.addEventListener('change', function() {
      document.getElementById('filterForm').submit();
    });
  });

  // AJAX form submission
  document.getElementById('submitAssignmentBtn').addEventListener('click', function() {
    // Show any form error messages
    const formErrorMessage = document.getElementById('formErrorMessage');
    formErrorMessage.classList.add('hidden');

    // Validate form
    if (!validateForm()) {
      return false;
    }

    // Create a new FormData object manually instead of from the form
    const formData = new FormData();

    // Get and validate exam_id - ULTRA SIMPLIFIED APPROACH
    const examSelect = document.getElementById('exam_id');

    // Get the selected option directly from the DOM
    const selectedIndex = examSelect.selectedIndex;
    const selectedOption = examSelect.options[selectedIndex];

    // Get the value and text
    const examId = selectedOption ? selectedOption.value : '';
    const examText = selectedOption ? selectedOption.text : 'None';

    console.log('Selected exam option:', selectedOption);
    console.log('Selected exam ID:', examId);
    console.log('Selected exam text:', examText);

    // Simple validation - just check if something is selected
    if (selectedIndex <= 0 || !examId || examId === '') {
      formErrorMessage.textContent = 'Please select an exam';
      formErrorMessage.classList.remove('hidden');
      console.error('No exam selected');
      return false;
    }

    // Use the actual value from the option
    const finalExamId = examId;
    console.log('Using exam ID for submission:', finalExamId);

    // Add basic form fields
    formData.append('exam_id', finalExamId); // Use the final exam ID value
    console.log('Adding exam_id to form data:', finalExamId);

    formData.append('assignment_type', document.querySelector('input[name="assignment_type"]:checked').value);
    formData.append('max_attempts', document.getElementById('max_attempts').value || '1');

    // Add end_datetime if provided
    const endDatetime = document.getElementById('end_datetime').value;
    if (endDatetime) {
      formData.append('end_datetime', endDatetime);
    }

    // Add is_resumable
    formData.append('is_resumable', document.getElementById('is_resumable').checked ? 'on' : 'off');

    // Add user or group IDs based on assignment type
    const assignmentType = document.querySelector('input[name="assignment_type"]:checked').value;

    if (assignmentType === 'user') {
      const selectedUsers = $('#user_ids').val();
      console.log('Selected users:', selectedUsers);

      if (selectedUsers && selectedUsers.length > 0) {
        // Add each selected user
        selectedUsers.forEach(userId => {
          formData.append('user_ids[]', userId);
        });
      } else {
        console.error('No users selected');
        formErrorMessage.textContent = 'Please select at least one user';
        formErrorMessage.classList.remove('hidden');
        ToastNotifications.error('Please select at least one user');
        return false;
      }
    } else {
      const selectedGroups = $('#group_ids').val();
      console.log('Selected groups:', selectedGroups);

      if (selectedGroups && selectedGroups.length > 0) {
        // Add each selected group
        selectedGroups.forEach(groupId => {
          formData.append('group_ids[]', groupId);
        });
      } else {
        console.error('No groups selected');
        formErrorMessage.textContent = 'Please select at least one group';
        formErrorMessage.classList.remove('hidden');
        ToastNotifications.error('Please select at least one group');
        return false;
      }
    }

    // Log all form data for debugging
    console.log('Final form data:');
    for (const pair of formData.entries()) {
      console.log(pair[0] + ': ' + pair[1]);
    }



    // Submit form via AJAX - SIMPLIFIED APPROACH
    console.log('Submitting form via AJAX...');

    // Create headers for fetch
    const headers = {
      'X-Requested-With': 'XMLHttpRequest'
    };

    // Double check exam_id is in the form data
    if (!formData.has('exam_id') || !formData.get('exam_id') === '') {
      console.error('exam_id is missing or empty in form data');
      formErrorMessage.textContent = 'Please select an exam';
      formErrorMessage.classList.remove('hidden');
      ToastNotifications.error('Please select an exam');
      return false;
    }

    // Log all form data for debugging
    console.log('Final form data before submission:');
    for (const pair of formData.entries()) {
      console.log(`${pair[0]}: ${pair[1]}`);
    }

    // Use a more reliable approach - convert FormData to URLSearchParams
    const params = new URLSearchParams();
    for (const pair of formData.entries()) {
      params.append(pair[0], pair[1]);
    }

    fetch('/admin/test-assignments/create', {
      method: 'POST',
      body: params,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => {
      console.log('Response status:', response.status);
      console.log('Response headers:', [...response.headers.entries()]);

      if (!response.ok) {
        console.error('Response not OK:', response.status, response.statusText);
        return response.text().then(text => {
          console.log('Error response body:', text);
          try {
            // Try to parse as JSON
            return JSON.parse(text);
          } catch (e) {
            // If not JSON, throw with the text
            throw new Error(`Server error: ${text}`);
          }
        });
      }
      return response.json().catch(err => {
        console.error('Error parsing JSON response:', err);
        throw new Error('Invalid JSON response from server');
      });
    })
    .then(data => {
      console.log('Response data:', data);
      if (data.success) {
        // Show success message
        ToastNotifications.success(data.message || 'Assignment created successfully');

        // Close modal and reload page
        closeCreateModal();
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        // Show error message
        const errorMsg = data.message || 'Error creating assignment';
        console.error('Error from server:', errorMsg);
        formErrorMessage.textContent = errorMsg;
        formErrorMessage.classList.remove('hidden');
        ToastNotifications.error(errorMsg);
      }
    })
    .catch(error => {
      console.error('Fetch error:', error);
      formErrorMessage.textContent = 'An error occurred while creating the assignment: ' + error.message;
      formErrorMessage.classList.remove('hidden');
      ToastNotifications.error('An error occurred while creating the assignment');
    });
  });
</script>
