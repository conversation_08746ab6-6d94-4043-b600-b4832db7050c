<!-- Test Creation Interface - Responsive Design -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test</title>
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/styles.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- <PERSON>sen for better dropdowns -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>
    <!-- Custom Chosen styles -->
    <link href="/css/chosen-custom.css" rel="stylesheet" />
    <!-- Instructions Overlay styles -->
    <link href="/css/instructions-overlay.css" rel="stylesheet" />
    <!-- SortableJS for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <!-- Toastify JS for notifications -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <style>
        /* Toast animations */
        .toast-message {
            opacity: 0;
            transform: translateX(100%);
            animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Toggle switch styles */
        .toggle-checkbox { opacity: 0; position: absolute; }
        .toggle-label {
            position: relative;
            display: inline-block;
            width: 3rem;
            height: 1.5rem;
            border-radius: 9999px;
            background-color: #e5e7eb;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-checkbox:checked + .toggle-label { background-color: #4ade80; }
        .toggle-label::after {
            content: '';
            position: absolute;
            left: 0.25rem;
            top: 0.25rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: white;
            transition: all 0.3s;
        }
        .toggle-checkbox:checked + .toggle-label::after { transform: translateX(1.5rem); }

        /* Form validation styles */
        .field-error {
            border-color: #ef4444 !important;
            background-color: #fef2f2 !important;
        }
        .field-success {
            border-color: #10b981 !important;
            background-color: #ecfdf5 !important;
        }

        /* MCQ option styles */
        .option-wrapper {
            position: relative;
            transition: all 0.2s;
        }
        .option-wrapper:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .option-wrapper.selected {
            border-color: #10b981 !important;
            background-color: rgba(16, 185, 129, 0.05);
        }
        .option-wrapper.selected::after {
            content: '✓';
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            color: #10b981;
            font-weight: bold;
        }

        /* Drag handle styles */
        .drag-handle {
            cursor: grab;
        }
        .drag-handle:active {
            cursor: grabbing;
        }

        /* Section tab scrolling */
        .tabs-container {
            display: flex;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        .tabs-container::-webkit-scrollbar {
            height: 6px;
        }
        .tabs-container::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        .tabs-container::-webkit-scrollbar-thumb {
            background-color: #cbd5e1;
            border-radius: 3px;
        }

        /* Active step indicators */
        .step-indicator.active .step-circle {
            background-color: #6366f1;
            color: white;
        }
        .step-indicator.completed .step-circle {
            background-color: #10b981;
            color: white;
        }
        .step-indicator.active .step-label,
        .step-indicator.completed .step-label {
            color: #4f46e5;
            font-weight: 500;
        }

        /* Step arrows */
        .step-arrow {
            transition: all 0.3s ease;
        }
        .step-indicator.active ~ .step-indicator .step-arrow,
        .step-indicator.active .step-arrow {
            color: #6366f1;
        }
        .step-indicator.completed ~ .step-indicator .step-arrow,
        .step-indicator.completed .step-arrow {
            color: #10b981;
        }

        /* Smooth transitions */
        .fade-transition {
            transition: opacity 0.3s, transform 0.3s;
        }
        .fade-enter {
            opacity: 0;
            transform: translateY(10px);
        }
        .fade-enter-active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Tooltip styles */
        .tooltip {
            position: relative;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #1f2937;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2"></div>

    <!-- Instructions Overlay -->
    <div id="instructionsOverlay" class="instructions-overlay">
        <div class="instructions-content">
            <div class="instructions-header">
                <h2>Test Instructions</h2>
                <button type="button" class="instructions-close" id="closeInstructionsBtn">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="instructions-body">
                <div id="instructionsContent"></div>
            </div>
            <div class="instructions-footer">
                <button type="button" class="btn-primary" id="confirmInstructionsBtn">Got it</button>
            </div>
        </div>
    </div>

    <div class="container mx-auto p-4 max-w-6xl">
        <div class="bg-white shadow-sm rounded-lg mb-6">
            <div class="p-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-6">Create New Test</h1>

                <form id="testForm" class="space-y-8">
                    <!-- Progress Steps -->
                    <div class="relative mb-10">
                        <!-- Progress Bar Background -->
                        <div class="absolute top-1/2 left-0 right-0 h-1 bg-gray-200 -translate-y-1/2"></div>

                        <!-- Progress Bar Fill -->
                        <div id="progress-bar" class="absolute top-1/2 left-0 h-1 bg-indigo-500 -translate-y-1/2 transition-all duration-300" style="width: 0%"></div>

                        <!-- Step Indicators -->
                        <div class="relative flex justify-between">
                            <!-- Arrow connectors -->
                            <div class="absolute top-1/2 left-[12.5%] right-[87.5%] -translate-y-1/2 flex justify-center">
                                <svg class="w-6 h-6 text-gray-300 step-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="absolute top-1/2 left-[37.5%] right-[62.5%] -translate-y-1/2 flex justify-center">
                                <svg class="w-6 h-6 text-gray-300 step-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="absolute top-1/2 left-[62.5%] right-[37.5%] -translate-y-1/2 flex justify-center">
                                <svg class="w-6 h-6 text-gray-300 step-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="absolute top-1/2 left-[87.5%] right-[12.5%] -translate-y-1/2 flex justify-center">
                                <svg class="w-6 h-6 text-gray-300 step-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <div class="step-indicator active" data-step="1">
                                <div class="flex flex-col items-center">
                                    <div class="step-circle w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mb-2">
                                        <span>1</span>
                                    </div>
                                    <span class="step-label text-sm text-gray-600">Basic Info</span>
                                </div>
                            </div>

                            <div class="step-indicator" data-step="2">
                                <div class="flex flex-col items-center">
                                    <div class="step-circle w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mb-2">
                                        <span>2</span>
                                    </div>
                                    <span class="step-label text-sm text-gray-600">Instructions</span>
                                </div>
                            </div>

                            <div class="step-indicator" data-step="3">
                                <div class="flex flex-col items-center">
                                    <div class="step-circle w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mb-2">
                                        <span>3</span>
                                    </div>
                                    <span class="step-label text-sm text-gray-600">Sections</span>
                                </div>
                            </div>

                            <div class="step-indicator" data-step="4">
                                <div class="flex flex-col items-center">
                                    <div class="step-circle w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mb-2">
                                        <span>4</span>
                                    </div>
                                    <span class="step-label text-sm text-gray-600">Preview</span>
                                </div>
                            </div>

                            <div class="step-indicator" data-step="5">
                                <div class="flex flex-col items-center">
                                    <div class="step-circle w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 mb-2">
                                        <span>5</span>
                                    </div>
                                    <span class="step-label text-sm text-gray-600">Publish</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Steps Content -->
                    <div id="form-steps" class="space-y-6">
                        <!-- Step 1: Basic Info -->
                        <div id="step1" class="form-step">
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="p-5 border-b border-gray-200">
                                    <h2 class="text-lg font-semibold text-gray-800">Test Details</h2>
                                </div>
                                <div class="p-5 space-y-5">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                        <div>
                                            <label for="testNameField" class="block text-sm font-medium text-gray-700 mb-1">Test Name*</label>
                                            <input type="text" id="testNameField" name="exam_name" required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                                placeholder="Enter a descriptive name">
                                            <input type="hidden" id="examId" name="examId" value="<%= typeof examId !== 'undefined' ? examId : '' %>">
                                        </div>

                                        <div>
                                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Categories*</label>
                                            <select id="category" name="category[]" multiple required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm chosen-category" data-placeholder="Select or create categories">
                                            </select>
                                            <p class="mt-1 text-xs text-gray-500">Select multiple categories or type to create new ones</p>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                                        <div>
                                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">Duration (minutes)*</label>
                                            <input type="number" id="duration" name="duration" required min="1" value="60"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                        </div>

                                        <div>
                                            <label for="passing_score" class="block text-sm font-medium text-gray-700 mb-1">Passing Score (%)*</label>
                                            <input type="number" id="passing_score" name="passing_score" required min="0" max="100" value="40"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                        </div>

                                        <div class="flex flex-col space-y-3 mt-8">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox" id="randomizeQuestions" name="randomize_questions" class="toggle-checkbox">
                                                <label for="randomizeQuestions" class="toggle-label"></label>
                                                <span class="text-sm text-gray-700">Randomize Questions</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox" id="isResumable" name="is_resumable" class="toggle-checkbox">
                                                <label for="isResumable" class="toggle-label"></label>
                                                <span class="text-sm text-gray-700">Allow test to be paused and resumed</span>
                                                <div class="tooltip ml-1">
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span class="tooltip-text">If enabled, users can pause the test and resume later. The timer will pause when the test is paused. If disabled, the timer will continue running even if the user leaves the page.</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Test Description*</label>
                                        <textarea id="description" name="description" rows="3" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            placeholder="Provide a brief description of what this test covers"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Instructions -->
                        <div id="step2" class="form-step hidden">
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                                    <h2 class="text-lg font-semibold text-gray-800">Test Instructions</h2>
                                    <button type="button" id="viewInstructionsBtn" class="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Preview Instructions
                                    </button>
                                </div>
                                <div class="p-5 space-y-4">
                                    <div>
                                        <div class="flex justify-between items-center mb-1">
                                            <label for="instructions" class="block text-sm font-medium text-gray-700">Instructions for Test Takers*</label>
                                            <span id="wordCount" class="text-xs text-gray-500">0 words (aim for 180-250)</span>
                                        </div>
                                        <textarea id="instructions" name="instructions" rows="10" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            placeholder="Enter detailed instructions for test takers. Include information about test format, rules, and any other important details."></textarea>
                                        <p class="mt-1 text-xs text-gray-500">Write clear, comprehensive instructions to help test takers understand what to expect.</p>
                                    </div>

                                    <div class="bg-blue-50 p-4 rounded-md">
                                        <h3 class="text-sm font-medium text-blue-800 mb-2">Instruction Tips:</h3>
                                        <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
                                            <li>Explain the test structure and number of sections</li>
                                            <li>Clarify how much time is allocated for each section/question</li>
                                            <li>Include information about scoring and negative marking (if applicable)</li>
                                            <li>Specify any materials or resources allowed during the test</li>
                                            <li>Provide information about test validation and submission process</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Sections -->
                        <div id="step3" class="form-step hidden">
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm mb-6">
                                <div class="flex justify-between items-center p-5 border-b border-gray-200">
                                    <h2 class="text-lg font-semibold text-gray-800">Test Sections</h2>
                                    <button type="button" id="addSectionBtn" class="px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors duration-200 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                        Add Section
                                    </button>
                                </div>

                                <div class="p-5">
                                    <!-- Section Stats Summary -->
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                                        <div class="bg-indigo-50 p-3 rounded-md shadow-sm">
                                            <span class="text-xs font-medium text-indigo-500 block">Total Sections</span>
                                            <span id="totalSectionsCount" class="text-lg font-semibold text-indigo-700">0</span>
                                        </div>
                                        <div class="bg-blue-50 p-3 rounded-md shadow-sm">
                                            <span class="text-xs font-medium text-blue-500 block">Total Questions</span>
                                            <span id="totalQuestionsCount" class="text-lg font-semibold text-blue-700">0</span>
                                        </div>
                                        <div class="bg-green-50 p-3 rounded-md shadow-sm">
                                            <span class="text-xs font-medium text-green-500 block">Total Marks</span>
                                            <span id="totalMarksCount" class="text-lg font-semibold text-green-700">0</span>
                                        </div>
                                        <div class="bg-purple-50 p-3 rounded-md shadow-sm">
                                            <span class="text-xs font-medium text-purple-500 block">Passing Score</span>
                                            <span id="testPassingScore" class="text-lg font-semibold text-purple-700">0</span>
                                        </div>
                                    </div>

                                    <!-- Section Tabs -->
                                    <div id="sectionTabs" class="tabs-container border-b border-gray-200 mb-4 pb-1"></div>

                                    <!-- Sections Content Area -->
                                    <div id="sectionsContainer" class="mt-6 space-y-6"></div>
                                </div>
                            </div>

                            <!-- Import Questions Panel -->
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="p-5 border-b border-gray-200">
                                    <h2 class="text-lg font-semibold text-gray-800">Import Questions</h2>
                                </div>
                                <div class="p-5">
                                    <div class="flex flex-col md:flex-row gap-4">
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-600 mb-3">
                                                Quickly add questions by importing them from a file. Supported formats: XLSX, CSV
                                            </p>
                                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-500 transition-colors">
                                                <input type="file" id="importQuestionsFile" name="importFile" class="hidden" accept=".xlsx,.csv">
                                                <label for="importQuestionsFile" class="cursor-pointer">
                                                    <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                    </svg>
                                                    <p class="mt-2 text-sm text-gray-600">Drag and drop a file here, or click to select</p>
                                                </label>
                                                <p id="selectedFileName" class="mt-2 text-sm text-indigo-600 hidden"></p>
                                            </div>
                                            <div class="mt-3 flex justify-end">
                                                <button type="button" id="importQuestionsBtn" class="px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors duration-200 flex items-center" disabled>
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                                    </svg>
                                                    Import Questions
                                                </button>
                                            </div>
                                        </div>

                                        <div class="flex-1">
                                            <h3 class="text-sm font-medium text-gray-700 mb-2">File Format Requirements:</h3>
                                            <ul class="list-disc pl-5 text-xs text-gray-600 space-y-1 mb-3">
                                                <li>First row must contain column headers</li>
                                                <li>Required columns: Question Text, Question Type, Correct Answer</li>
                                                <li>For MCQ questions, include Options column (separate with | character)</li>
                                                <li>Optional columns: Solution, Marks, Negative Marks, Category</li>
                                            </ul>
                                            <a href="/admin/tests/download-sample-template" class="text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md shadow-sm transition-colors duration-200 flex items-center w-fit">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                                </svg>
                                                Download Template
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Preview -->
                        <div id="step4" class="form-step hidden">
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                                    <h2 class="text-lg font-semibold text-gray-800">Test Preview</h2>
                                    <div class="flex space-x-2">
                                        <button type="button" id="viewPreviewInstructionsBtn" class="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View Instructions
                                        </button>
                                        <button type="button" id="downloadPreviewPdfBtn" class="px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors duration-200 flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                            </svg>
                                            Download PDF
                                        </button>
                                    </div>
                                </div>
                                <div class="p-5">
                                    <div id="testPreview" class="space-y-6">
                                        <!-- Preview content will be generated dynamically -->
                                        <div class="text-center p-10 text-gray-500">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            <p class="mt-2">Preview will be generated based on your test configuration</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 5: Publish -->
                        <div id="step5" class="form-step hidden">
                            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                                <div class="p-5 border-b border-gray-200">
                                    <h2 class="text-lg font-semibold text-gray-800">Publish Test</h2>
                                </div>
                                <div class="p-5 space-y-5">
                                    <div class="p-4 bg-green-50 rounded-md border border-green-200">
                                        <div class="flex justify-between items-start mb-2">
                                            <h3 class="text-md font-medium text-green-800">Test Summary</h3>
                                            <button type="button" id="downloadPublishPdfBtn" class="px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors duration-200 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                                </svg>
                                                Download PDF
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <p class="text-sm text-green-700"><span class="font-medium">Name:</span> <span id="summary-name">-</span></p>
                                                <p class="text-sm text-green-700"><span class="font-medium">Duration:</span> <span id="summary-duration">-</span> minutes</p>
                                                <p class="text-sm text-green-700"><span class="font-medium">Passing Score:</span> <span id="summary-passing">-</span>%</p>
                                            </div>
                                            <div>
                                                <p class="text-sm text-green-700"><span class="font-medium">Total Sections:</span> <span id="summary-sections">-</span></p>
                                                <p class="text-sm text-green-700"><span class="font-medium">Total Questions:</span> <span id="summary-questions">-</span></p>
                                                <p class="text-sm text-green-700"><span class="font-medium">Total Marks:</span> <span id="summary-marks">-</span></p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-4">
                                        <div class="flex items-center space-x-2">
                                            <input type="checkbox" id="is_resumable" name="is_resumable" class="toggle-checkbox">
                                            <label for="is_resumable" class="toggle-label"></label>
                                            <span class="text-sm font-medium text-gray-700">Allow students to resume test</span>
                                            <span class="ml-1 text-xs text-gray-500">(Students can pause and resume the test later)</span>
                                        </div>

                                        <div class="flex items-center space-x-2">
                                            <input type="checkbox" id="publishNow" name="publish_now" class="toggle-checkbox">
                                            <label for="publishNow" class="toggle-label"></label>
                                            <span class="text-sm font-medium text-gray-700">Publish test immediately</span>
                                        </div>

                                        <div id="schedulePublish" class="hidden">
                                            <label for="publish_date" class="block text-sm font-medium text-gray-700 mb-1">Schedule Publication Date</label>
                                            <input type="datetime-local" id="publish_date" name="publish_date"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                        </div>

                                        <div class="pt-4 mt-4 border-t border-gray-200">
                                            <h3 class="text-md font-medium text-gray-800 mb-3">Access Settings</h3>

                                            <div class="space-y-3">
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="requirePasscode" name="require_passcode" class="toggle-checkbox">
                                                    <label for="requirePasscode" class="toggle-label"></label>
                                                    <span class="text-sm font-medium text-gray-700">Require passcode for access</span>
                                                </div>

                                                <div id="passcodeField" class="hidden">
                                                    <label for="test_passcode" class="block text-sm font-medium text-gray-700 mb-1">Test Passcode</label>
                                                    <input type="text" id="test_passcode" name="test_passcode"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                </div>

                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="limitAttempts" name="limit_attempts" class="toggle-checkbox">
                                                    <label for="limitAttempts" class="toggle-label"></label>
                                                    <span class="text-sm font-medium text-gray-700">Limit number of attempts</span>
                                                </div>

                                                <div id="attemptsField" class="hidden">
                                                    <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-1">Maximum Attempts</label>
                                                    <input type="number" id="max_attempts" name="max_attempts" min="1" value="1"
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between pt-4 border-t border-gray-200">
                        <button type="button" id="prevStepBtn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </button>

                        <div class="flex space-x-3">
                            <button type="button" id="saveDraftBtn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                                </svg>
                                Save Draft
                            </button>

                            <button type="button" id="nextStepBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors duration-200 flex items-center">
                                <span id="nextButtonText">Next Step</span>
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>

                            <button type="submit" id="submitBtn" class="hidden px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200 flex items-center">
                                <span>Publish Test</span>
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Templates -->
    <!-- Section Tab Template -->
    <template id="sectionTabTemplate">
        <button type="button" class="section-tab px-4 py-2 text-sm font-medium text-gray-700 hover:text-indigo-600 border-b-2 border-transparent hover:border-indigo-500 transition-colors duration-200 flex items-center space-x-2 relative" draggable="true">
            <span class="drag-handle cursor-grab">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
                </svg>
            </span>
            <span class="section-number"></span>
            <span class="question-count bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">0</span>
            <span class="remove-section-wrapper ml-2">
                <button type="button" class="remove-section">
                    <svg class="w-4 h-4 text-red-500 hover:text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </span>
        </button>
    </template>

    <!-- Section Content Template -->
    <template id="sectionContentTemplate">
        <div class="section-content bg-white border border-gray-200 rounded-lg shadow-sm hidden">
            <div class="p-5 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Section Name*</label>
                        <input type="text" name="sections[][name]" required
                            class="section-title w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                            placeholder="Enter section name">
                    </div>
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="block text-sm font-medium text-gray-700">Section Instructions</label>
                            <span class="instruction-word-count text-xs text-gray-500">0 words</span>
                        </div>
                        <textarea name="sections[][instructions]" rows="3"
                            class="section-instructions w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                            placeholder="Enter instructions for this section"></textarea>
                    </div>
                </div>
            </div>

            <div class="p-5 space-y-4">
                <div class="flex flex-wrap items-center gap-4 mb-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-700">Questions: <span class="question-count">0</span></span>
                    </div>

                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-700">Total Marks: <span class="section-marks">0</span></span>
                    </div>

                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-purple-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-700">Passing Marks:</span>
                        <input type="number" name="sections[][passing_marks]" min="0" value="0"
                            class="section-passing-marks ml-2 w-16 text-center px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                    </div>

                    <div class="ml-auto">
                        <button type="button" class="add-question-btn px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Add Question
                        </button>
                    </div>
                </div>

                <div class="flex flex-wrap gap-2 mb-4">
                    <div class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        MCQ: <span class="mcq-count ml-1">0</span>
                    </div>
                    <div class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        True/False: <span class="tf-count ml-1">0</span>
                    </div>
                    <div class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Short Answer: <span class="sa-count ml-1">0</span>
                    </div>
                    <div class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                        </svg>
                        Essay: <span class="essay-count ml-1">0</span>
                    </div>
                </div>

                <div class="questions-container space-y-4">
                    <!-- Questions will be added here -->
                    <div class="empty-section-message text-center py-8 rounded-lg border border-dashed border-gray-300 bg-gray-50">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-500">No questions in this section yet</p>
                        <p class="mt-1 text-xs text-gray-500">Click "Add Question" to get started</p>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Question Template -->
    <template id="questionTemplate">
        <div class="question-container bg-white border border-gray-200 rounded-lg shadow-sm p-5">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center space-x-2">
                    <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full flex items-center">
                        Question <span class="question-number ml-1">1</span>
                    </span>
                    <span class="question-type-badge bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Multiple Choice</span>
                    <span class="category-badge bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full hidden"></span>
                </div>
                <div class="flex items-center space-x-2">
                    <button type="button" class="remove-question text-red-500 hover:text-red-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Question Type*</label>
                    <select name="sections[][questions][][type]" required class="question-type w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <option value="">Select Type</option>
                        <option value="mcq" selected>Multiple Choice</option>
                        <option value="true_false">True/False</option>
                        <option value="short_answer">Short Answer</option>
                        <option value="essay">Essay</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Marks*</label>
                    <input type="number" name="sections[][questions][][marks]" min="0" value="1" required
                        class="question-marks w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Negative Marks</label>
                    <input type="number" name="sections[][questions][][negative_marks]" min="0" value="0"
                        class="negative-marks w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Question Text*</label>
                <textarea name="sections[][questions][][text]" required rows="3"
                    class="question-text w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                    placeholder="Enter your question"></textarea>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Question Image (Optional)</label>
                <div class="flex items-center space-x-2">
                    <input type="text" name="sections[][questions][][image_url]" class="question-image-url hidden">
                    <button type="button" class="upload-question-image px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Upload Image
                    </button>
                    <button type="button" class="remove-question-image px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors duration-200 flex items-center hidden">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Remove Image
                    </button>
                </div>
                <div class="question-image-preview mt-2 hidden">
                    <img src="" alt="Question image preview" class="max-h-40 border border-gray-300 rounded-md">
                </div>
                <p class="mt-1 text-xs text-gray-500">Upload an image to include with this question (max 2MB)</p>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Categories (Optional)</label>
                <select name="sections[][questions][][category][]" multiple
                    class="question-category w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm chosen-category" data-placeholder="Select or create categories">
                </select>
                <p class="mt-1 text-xs text-gray-500">Select multiple categories or type to create new ones</p>
            </div>

            <div class="answer-container mb-4">
                <!-- Answer options will be added based on question type -->
            </div>

            <!-- Essay Linking Container - Will be populated for all question types -->
            <div class="essay-linking-wrapper mb-4">
                <!-- Essay linking component will be added here -->
            </div>

            <div class="solution-container">
                <label class="block text-sm font-medium text-gray-700 mb-1">Solution/Explanation (Optional)</label>
                <textarea name="sections[][questions][][solution]" rows="2"
                    class="question-solution w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                    placeholder="Explain the solution or provide additional information"></textarea>

                <div class="mt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Solution Image (Optional)</label>
                    <div class="flex items-center space-x-2">
                        <input type="text" name="sections[][questions][][solution_image_url]" class="solution-image-url hidden">
                        <button type="button" class="upload-solution-image px-3 py-1.5 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors duration-200 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Upload Image
                        </button>
                        <button type="button" class="remove-solution-image px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors duration-200 flex items-center hidden">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Remove Image
                        </button>
                    </div>
                    <div class="solution-image-preview mt-2 hidden">
                        <img src="" alt="Solution image preview" class="max-h-40 border border-gray-300 rounded-md">
                    </div>
                </div>
            </div>

            <!-- Hidden field for essay ID -->
            <input type="hidden" name="sections[][questions][][essay_id]" class="question-essay-id" value="">
        </div>
    </template>

    <!-- Answer Option Templates -->
    <template id="mcqOptionsTemplate">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <label class="block text-sm font-medium text-gray-700">Options*</label>
                <span class="text-xs font-medium text-blue-600">Select all correct options</span>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 options-grid">
                <!-- 4 default options will be added -->
            </div>
            <div class="mt-3 flex justify-end">
                <button type="button" class="add-option-btn px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors duration-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Option
                </button>
            </div>
        </div>
    </template>

    <template id="mcqOptionItemTemplate">
        <div class="option-wrapper relative border border-gray-200 rounded-md p-3 hover:border-green-300 transition-colors duration-200">
            <div class="flex items-center">
                <input type="checkbox" class="correct-option h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded mr-3">
                <input type="text" class="option-text w-full border-0 p-0 focus:ring-0 bg-transparent border-b border-gray-300"
                    placeholder="Enter option text" required>
            </div>
        </div>
    </template>

    <template id="truefalseTemplate">
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Correct Answer*</label>
            <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                    <input type="radio" class="form-radio h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" name="correct_option" value="true">
                    <span class="ml-2 text-sm">True</span>
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" class="form-radio h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" name="correct_option" value="false">
                    <span class="ml-2 text-sm">False</span>
                </label>
            </div>
        </div>
    </template>

    <template id="shortAnswerTemplate">
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Correct Answer*</label>
            <input type="text" class="correct-answer w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                placeholder="Enter correct answer" required>
        </div>
    </template>

    <!-- Essay Question Settings Template -->
    <template id="essayTemplate">
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <label class="block text-sm font-medium text-gray-700">Essay Question Settings</label>
                <span class="text-xs text-gray-500">(Student will write a long-form answer)</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Word Count</label>
                    <input type="number" class="min-word-count w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                        placeholder="Optional minimum word count" min="0">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Maximum Word Count</label>
                    <input type="number" class="max-word-count w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                        placeholder="Optional maximum word count" min="0">
                </div>
            </div>
            <!-- Essay linking component will be added separately -->
        </div>
    </template>

    <!-- Essay Linking Component Template (reusable across all question types) -->
    <template id="essayLinkingTemplate">
        <div class="mb-4 essay-linking-container">
            <label class="block text-sm font-medium text-gray-700 mb-1">Link to Essay</label>
            <div class="flex items-center space-x-2">
                <select class="linked-essay w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                    <option value="">Select an essay to link (optional)</option>
                    <!-- Essays will be loaded dynamically -->
                </select>
                <button type="button" class="fetch-essays-btn px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
                <button type="button" class="clear-essay-btn px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors duration-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Link this question to an essay that students must read before answering. Use the clear button to remove the link.</p>
        </div>
    </template>

    <!-- Include JavaScript -->
    <script src="/js/confirmation-dialog.js"></script>
    <script src="/js/admin/test-creator.js"></script>
</body>
</html>
