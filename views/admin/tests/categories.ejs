<!-- Test Categories Management -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Test Categories</h2>
      <button id="add-category-btn" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Category
      </button>
    </div>

    <% if (categories && categories.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tests</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% categories.forEach(category => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= category.name %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500"><%= category.description || 'No description' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= category.test_count %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="edit-category-btn text-indigo-600 hover:text-indigo-900 mr-3" data-id="<%= category.category_id %>" data-name="<%= category.name %>" data-description="<%= category.description || '' %>">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button class="delete-category-btn text-red-600 hover:text-red-900" data-id="<%= category.category_id %>" data-name="<%= category.name %>">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <p class="text-gray-500">No categories found. Create your first category.</p>
      </div>
    <% } %>
  </div>
</div>

<!-- Add Category Modal -->
<div id="add-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="px-6 py-4 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Add New Category</h3>
    </div>
    <form action="/admin/tests/categories/add" method="POST">
      <div class="px-6 py-4">
        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input type="text" id="name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea id="description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>
      </div>
      <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
        <button type="button" id="add-cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
        <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700">Save</button>
      </div>
    </form>
  </div>
</div>

<!-- Edit Category Modal -->
<div id="edit-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="px-6 py-4 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Edit Category</h3>
    </div>
    <form id="edit-category-form" action="/admin/tests/categories/0/update" method="POST">
      <div class="px-6 py-4">
        <div class="mb-4">
          <label for="edit-name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input type="text" id="edit-name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea id="edit-description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>
      </div>
      <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
        <button type="button" id="edit-cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
        <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700">Update</button>
      </div>
    </form>
  </div>
</div>

<!-- Delete Category Modal -->
<div id="delete-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="px-6 py-4 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Delete Category</h3>
    </div>
    <div class="px-6 py-4">
      <p class="text-gray-700">Are you sure you want to delete the category <span id="delete-category-name" class="font-semibold"></span>?</p>
      <p class="text-gray-500 mt-2">This action cannot be undone. Tests associated with this category will have their category reference removed.</p>
    </div>
    <div class="px-6 py-3 bg-gray-50 flex justify-end rounded-b-lg">
      <button type="button" id="delete-cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 mr-2">Cancel</button>
      <form id="delete-category-form" action="/admin/tests/categories/0/delete" method="POST">
        <button type="submit" class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700">Delete</button>
      </form>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add Category Modal
    const addCategoryBtn = document.getElementById('add-category-btn');
    const addCategoryModal = document.getElementById('add-category-modal');
    const addCancelBtn = document.getElementById('add-cancel-btn');

    addCategoryBtn.addEventListener('click', function() {
      addCategoryModal.classList.remove('hidden');
    });

    addCancelBtn.addEventListener('click', function() {
      addCategoryModal.classList.add('hidden');
    });

    // Edit Category Modal
    const editCategoryBtns = document.querySelectorAll('.edit-category-btn');
    const editCategoryModal = document.getElementById('edit-category-modal');
    const editCancelBtn = document.getElementById('edit-cancel-btn');
    const editCategoryForm = document.getElementById('edit-category-form');
    const editNameInput = document.getElementById('edit-name');
    const editDescriptionInput = document.getElementById('edit-description');

    editCategoryBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const categoryId = this.getAttribute('data-id');
        const categoryName = this.getAttribute('data-name');
        const categoryDescription = this.getAttribute('data-description');

        editCategoryForm.action = `/admin/tests/categories/${categoryId}/update`;
        editNameInput.value = categoryName;
        editDescriptionInput.value = categoryDescription;
        
        editCategoryModal.classList.remove('hidden');
      });
    });

    editCancelBtn.addEventListener('click', function() {
      editCategoryModal.classList.add('hidden');
    });

    // Delete Category Modal
    const deleteCategoryBtns = document.querySelectorAll('.delete-category-btn');
    const deleteCategoryModal = document.getElementById('delete-category-modal');
    const deleteCancelBtn = document.getElementById('delete-cancel-btn');
    const deleteCategoryForm = document.getElementById('delete-category-form');
    const deleteCategoryName = document.getElementById('delete-category-name');

    deleteCategoryBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const categoryId = this.getAttribute('data-id');
        const categoryName = this.getAttribute('data-name');

        deleteCategoryForm.action = `/admin/tests/categories/${categoryId}/delete`;
        deleteCategoryName.textContent = categoryName;
        
        deleteCategoryModal.classList.remove('hidden');
      });
    });

    deleteCancelBtn.addEventListener('click', function() {
      deleteCategoryModal.classList.add('hidden');
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === addCategoryModal) {
        addCategoryModal.classList.add('hidden');
      }
      if (event.target === editCategoryModal) {
        editCategoryModal.classList.add('hidden');
      }
      if (event.target === deleteCategoryModal) {
        deleteCategoryModal.classList.add('hidden');
      }
    });
  });
</script>
