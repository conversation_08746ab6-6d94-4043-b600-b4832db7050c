<!-- Include ToastNotifications script directly -->
<script src="/js/toast-notifications.js"></script>
<link rel="stylesheet" href="/css/image-styles.css">

<!-- Include image upload scripts -->
<script src="/js/admin/question-image-upload.js"></script>
<script src="/js/admin/add-question-modal.js"></script>

<!-- Include SortableJS for drag and drop functionality -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<!-- Initialize ToastNotifications -->
<script>
  // Define a fallback ToastNotifications object if it's not defined
  if (typeof ToastNotifications === 'undefined') {
    console.warn('ToastNotifications not found, creating fallback');
    window.ToastNotifications = {
      init: function() { console.log('Fallback ToastNotifications initialized'); },
      show: function(message) { console.log('ToastNotification:', message); },
      success: function(message) { console.log('Success:', message); },
      error: function(message) { console.error('Error:', message); },
      info: function(message) { console.info('Info:', message); },
      warning: function(message) { console.warn('Warning:', message); }
    };
  }

  // Make sure ToastNotifications is initialized
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof ToastNotifications.init === 'function') {
      ToastNotifications.init();
      console.log('ToastNotifications initialized');
    }
  });
</script>

<!-- Test Edit Page -->
<% var globalQuestionCounter = 1; // Initialize global question counter at the top of the page %>
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Edit Test: <%= test.exam_name %></h2>
      <a href="/tests/admin" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Tests
      </a>
    </div>

    <!-- Test Edit Form -->
    <form id="testEditForm" action="/tests/admin/<%= test.exam_id %>/edit" method="POST" class="space-y-6">
      <div class="bg-white rounded-lg shadow p-6 mb-6 border-l-4 border-blue-500">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Test Details</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="exam_name" class="block text-sm font-medium text-gray-700 mb-1">Test Name</label>
            <input type="text" id="exam_name" name="exam_name" value="<%= test.exam_name %>" required
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>

          <div>
            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">Duration (minutes)</label>
            <input type="number" id="duration" name="duration" value="<%= test.duration || 60 %>" required min="1"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>

          <div>
            <label for="passing_marks" class="block text-sm font-medium text-gray-700 mb-1">Passing Marks</label>
            <input type="number" id="passing_marks" name="passing_marks" value="<%= test.passing_marks || '' %>" min="0"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>

          <div>
            <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-1">Maximum Attempts</label>
            <input type="number" id="max_attempts" name="max_attempts" value="<%= test.max_attempts || 1 %>" min="1"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <p class="text-xs text-gray-500 mt-1">Maximum number of times a user can attempt this test</p>
            <p class="text-xs text-gray-500 mt-1">Total Questions: <%= totalQuestionsCount %></p>
          </div>

          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" disabled>
              <option value="draft" <%= test.status === 'draft' ? 'selected' : '' %>>Draft</option>
              <option value="published" <%= test.status === 'published' ? 'selected' : '' %>>Published</option>
              <option value="archived" <%= test.status === 'archived' ? 'selected' : '' %>>Archived</option>
            </select>
            <p class="text-xs text-gray-500 mt-1">Status cannot be changed directly. Use the publish or archive actions.</p>
          </div>
        </div>

        <div class="mt-4">
          <label for="instructions" class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
          <textarea id="instructions" name="instructions" rows="4"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"><%= test.instructions || '' %></textarea>
        </div>
      </div>

      <!-- Sections -->
      <div class="bg-white rounded-lg shadow p-6 mb-6 border-l-4 border-purple-500">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-800">Sections (<%= sections.length %>)</h3>
          <div class="flex space-x-2">
            <button type="button" id="reorderSectionsBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
              </svg>
              Reorder
            </button>
            <button type="button" id="updateAllQuestionNumbersBtn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Update Numbers
            </button>
            <button type="button" id="addSectionBtn" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Section
            </button>
          </div>
        </div>

        <% if (sections && sections.length > 0) { %>
          <!-- Tabbed Interface -->
          <div class="mb-4">
            <div class="border-b border-gray-200">
              <nav class="-mb-px flex space-x-2 overflow-x-auto" aria-label="Sections" id="sectionTabs">
                <% sections.forEach((section, index) => { %>
                  <div class="section-tab-wrapper flex items-center">
                    <div class="drag-handle cursor-move px-1 mr-1 text-gray-400 hover:text-gray-600">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
                      </svg>
                    </div>
                    <button type="button" class="section-tab <%= index === 0 ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm" data-section-id="<%= section.section_id %>" data-position="<%= index + 1 %>">
                      <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full mr-1 section-number"><%= index + 1 %></span>
                      <%= section.section_name %>
                      <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"><%= section.question_count || 0 %></span>
                    </button>
                  </div>
                <% }); %>
              </nav>
            </div>
          </div>

          <!-- Section Content -->
          <div id="sectionsContainer">
            <% sections.forEach((section, index) => { %>
              <div class="section-content <%= index === 0 ? '' : 'hidden' %>" data-section-id="<%= section.section_id %>">
                <div class="flex justify-between items-center mb-4">
                  <div>
                    <h4 class="text-md font-medium text-gray-800"><%= section.section_name %></h4>
                  </div>
                  <div class="flex space-x-2">
                    <a href="/tests/admin/sections/<%= section.section_id %>/questions" class="text-blue-600 hover:text-blue-800" title="Manage Questions">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                      </svg>
                    </a>
                    <button type="button" class="text-indigo-600 hover:text-indigo-800 edit-section-btn" data-section-id="<%= section.section_id %>" data-section-name="<%= section.section_name %>" data-section-instructions="<%= section.instructions || '' %>" data-section-passing-marks="<%= section.passing_marks || '' %>" data-section-total-marks="<%= section.total_marks || 0 %>" title="Edit Section">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                    <button type="button" class="text-red-600 hover:text-red-800 delete-section-btn" data-section-id="<%= section.section_id %>" title="Delete Section">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Section Statistics -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <%
                    // Calculate total marks
                    let totalMarks = 0;

                    // Fetch the actual marks from the database
                    // This will be done in the route handler
                    // For now, we'll use the question_count as an estimate
                    if (section.total_marks) {
                      totalMarks = parseFloat(section.total_marks);
                    } else if (section.question_count > 0) {
                      // Estimate total marks based on question count
                      totalMarks = section.question_count;
                    }
                  %>

                  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <div>
                      <h4 class="text-sm font-medium text-gray-700">Total Questions:</h4>
                      <p class="text-sm"><%= section.question_count %></p>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-700">Total Marks:</h4>
                      <p class="text-sm"><%= totalMarks %></p>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-700">Passing Marks:</h4>
                      <p class="text-sm section-passing-marks" data-section-id="<%= section.section_id %>"><%= section.passing_marks || 'Not set' %></p>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-700">Question Types:</h4>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <% if (section.mcq_count > 0) { %>
                          <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">MCQ: <%= section.mcq_count %></span>
                        <% } %>
                        <% if (section.true_false_count > 0) { %>
                          <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">T/F: <%= section.true_false_count %></span>
                        <% } %>
                        <% if (section.fill_up_count > 0) { %>
                          <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill-up: <%= section.fill_up_count %></span>
                        <% } %>
                        <% if (section.essay_count > 0) { %>
                          <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay: <%= section.essay_count %></span>
                        <% } %>
                      </div>
                    </div>
                  </div>

                  <% if (section.instructions) { %>
                    <div class="mt-3 pt-3 border-t border-gray-200">
                      <h4 class="text-sm font-medium text-gray-700">Section Instructions:</h4>
                      <p class="text-sm text-gray-600 mt-1 whitespace-pre-line cursor-pointer hover:text-blue-600 section-instructions"
                         data-section-id="<%= section.section_id %>"
                         data-instructions="<%= section.instructions.replace(/"/g, '&quot;') %>">
                        <%= section.instructions.length > 100 ? section.instructions.substring(0, 100) + '... (click to view full)' : section.instructions %>
                      </p>
                    </div>
                  <% } %>
                </div>

                <% if (section.question_count > 0) { %>
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                      <p class="text-sm text-gray-600">This section contains <%= section.question_count %> questions.</p>
                      <div class="flex space-x-2">
                        <button type="button" class="reorder-questions-btn inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800" data-section-id="<%= section.section_id %>">
                          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                          </svg>
                          Reorder
                        </button>
                        <a href="/tests/admin/sections/<%= section.section_id %>/questions" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                          Manage Questions
                          <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                          </svg>
                        </a>
                      </div>
                    </div>

                    <% if (section.questions && section.questions.length > 0) { %>
                      <div class="space-y-2 mt-3 pt-3 border-t border-gray-200 questions-container">
                        <%
                        // Group questions by essay_id
                        const essayGroups = {};
                        const nonEssayQuestions = [];

                        // Global question counter is already initialized at the top of the page

                        section.questions.forEach(question => {
                          if (question.essay_id) {
                            if (!essayGroups[question.essay_id]) {
                              essayGroups[question.essay_id] = {
                                essay: {
                                  id: question.essay_id,
                                  title: question.essay_title,
                                  content: question.essay_content
                                },
                                questions: []
                              };
                            }
                            essayGroups[question.essay_id].questions.push(question);
                          } else {
                            nonEssayQuestions.push(question);
                          }
                        });

                        // Display essay groups first
                        Object.values(essayGroups).forEach(group => {
                        %>
                          <div class="essay-group border-l-4 border-yellow-400 pl-4 pb-2 mb-4">
                            <!-- Essay Content - Displayed only once -->
                            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-4">
                              <div class="bg-yellow-50 p-3 border-b border-yellow-100">
                                <div class="flex justify-between items-center">
                                  <h4 class="text-md font-semibold text-gray-800"><%= group.essay.title %></h4>
                                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay/Passage</span>
                                </div>
                              </div>
                              <div class="p-4">
                                <div class="prose max-w-none text-sm text-gray-700 whitespace-pre-line">
                                  <%- group.essay.content ? group.essay.content.replace(/\n/g, '<br>') : '' %>
                                </div>
                              </div>
                            </div>

                            <!-- Questions related to this essay -->
                            <div class="ml-4 space-y-4">
                              <h5 class="text-sm font-medium text-gray-700 mb-2">Questions based on this passage:</h5>
                              <% group.questions.forEach((question, questionIndex) => {
                                // Include the question display partial
                                const showCorrect = true; // Show correct answers in edit mode
                                const showSolution = true; // Show solutions in edit mode
                                const showActions = true; // Show action buttons in edit mode
                                const questionNumber = globalQuestionCounter++;
                              %>
                                <%- include('../questions/partials/question-display-format', {
                                  question,
                                  questionNumber,
                                  showCorrect,
                                  showSolution,
                                  showActions
                                }) %>
                              <% }); %>
                            </div>
                          </div>
                        <% }); %>

                        <% if (nonEssayQuestions.length > 0) { %>
                          <div class="mt-4">
                            <h5 class="text-sm font-medium text-gray-700 mb-4">Other Questions:</h5>
                            <div class="space-y-4 questions-container">
                              <% nonEssayQuestions.forEach((question, questionIndex) => {
                                // Include the question display partial
                                const showCorrect = true; // Show correct answers in edit mode
                                const showSolution = true; // Show solutions in edit mode
                                const showActions = true; // Show action buttons in edit mode
                                const questionNumber = globalQuestionCounter++;
                              %>
                                <%- include('../questions/partials/question-display-format', {
                                  question,
                                  questionNumber,
                                  showCorrect,
                                  showSolution,
                                  showActions
                                }) %>
                              <% }); %>
                            </div>
                          </div>
                        <% } %>
                      </div>
                    <% } %>
                  </div>
                <% } else { %>
                  <div class="border border-gray-200 rounded-lg p-4 text-center">
                    <p class="text-sm text-gray-600">No questions added to this section yet.</p>
                    <a href="/tests/admin/sections/<%= section.section_id %>/questions" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                      Add Questions
                      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </a>
                  </div>
                <% } %>
              </div>
            <% }); %>
          </div>
        <% } else { %>
          <div class="text-center py-8 text-gray-500" id="noSectionsMessage">
            <p>No sections added yet. Click "Add Section" to create your first section.</p>
          </div>
        <% } %>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-4">
        <a href="/tests/admin" class="px-6 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">Cancel</a>
        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<!-- Add Section Modal -->
<div id="addSectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Add Section</h3>

    <form id="addSectionForm" class="space-y-4">
      <input type="hidden" id="examId" value="<%= test.exam_id %>">
      <div>
        <label for="sectionName" class="block text-sm font-medium text-gray-700">Section Name</label>
        <input type="text" id="sectionName" name="sectionName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
      </div>

      <div>
        <label for="sectionInstructions" class="block text-sm font-medium text-gray-700">Section Instructions</label>
        <textarea id="sectionInstructions" name="sectionInstructions" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Instructions specific to this section (optional)"></textarea>
        <p class="text-xs text-gray-500 mt-1">Provide specific instructions for this section if needed.</p>
      </div>

      <div>
        <label for="sectionPassingMarks" class="block text-sm font-medium text-gray-700">Passing Marks</label>
        <input type="number" id="sectionPassingMarks" name="sectionPassingMarks" min="0" step="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Minimum marks required to pass this section">
        <p class="text-xs text-gray-500 mt-1">Must be less than or equal to total marks for this section.</p>
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeAddSectionModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">
          Add Section
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Edit Section Modal -->
<div id="editSectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Section</h3>

    <form id="editSectionForm" class="space-y-4">
      <input type="hidden" id="editSectionId">
      <div>
        <label for="editSectionName" class="block text-sm font-medium text-gray-700">Section Name</label>
        <input type="text" id="editSectionName" name="editSectionName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
      </div>

      <div>
        <label for="editSectionInstructions" class="block text-sm font-medium text-gray-700">Section Instructions</label>
        <textarea id="editSectionInstructions" name="editSectionInstructions" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Instructions specific to this section (optional)"></textarea>
        <p class="text-xs text-gray-500 mt-1">Provide specific instructions for this section if needed.</p>
      </div>

      <div>
        <label for="editSectionPassingMarks" class="block text-sm font-medium text-gray-700">Passing Marks</label>
        <input type="number" id="editSectionPassingMarks" name="editSectionPassingMarks" min="0" step="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Minimum marks required to pass this section">
        <p class="text-xs text-gray-500 mt-1">Must be less than or equal to total marks for this section.</p>
        <input type="hidden" id="editSectionTotalMarks" name="editSectionTotalMarks">
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeEditSectionModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
          Update Section
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Delete Section Confirmation Modal -->
<div id="deleteSectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Section</h3>
    <p class="mb-4">Are you sure you want to delete this section? This will also delete all questions in this section.</p>

    <form id="deleteSectionForm" class="space-y-4">
      <input type="hidden" id="deleteSectionId">

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeDeleteSectionModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
          Delete Section
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Add Section Modal
  function openAddSectionModal() {
    document.getElementById('addSectionModal').classList.remove('hidden');
  }

  function closeAddSectionModal() {
    document.getElementById('addSectionModal').classList.add('hidden');
    document.getElementById('sectionName').value = '';
    document.getElementById('sectionInstructions').value = '';
    document.getElementById('sectionPassingMarks').value = '';
  }

  // Edit Section Modal
  function openEditSectionModal(sectionId, sectionName, instructions, passingMarks, totalMarks) {
    document.getElementById('editSectionId').value = sectionId;
    document.getElementById('editSectionName').value = sectionName;
    document.getElementById('editSectionInstructions').value = instructions || '';
    document.getElementById('editSectionPassingMarks').value = passingMarks || '';
    document.getElementById('editSectionTotalMarks').value = totalMarks || 0;

    // Set max value for passing marks
    const passingMarksInput = document.getElementById('editSectionPassingMarks');
    passingMarksInput.max = totalMarks || 0;

    document.getElementById('editSectionModal').classList.remove('hidden');
  }

  function closeEditSectionModal() {
    document.getElementById('editSectionModal').classList.add('hidden');
  }

  // Delete Section Modal
  function openDeleteSectionModal(sectionId) {
    document.getElementById('deleteSectionId').value = sectionId;
    document.getElementById('deleteSectionModal').classList.remove('hidden');
  }

  function closeDeleteSectionModal() {
    document.getElementById('deleteSectionModal').classList.add('hidden');
  }

  // Delete Question Modal Functions
  function openDeleteQuestionModal(questionId) {
    document.getElementById('deleteQuestionId').value = questionId;
    document.getElementById('deleteQuestionModal').classList.remove('hidden');
  }

  function closeDeleteQuestionModal() {
    document.getElementById('deleteQuestionModal').classList.add('hidden');
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Initialize SortableJS for section tabs
    const sectionTabsEl = document.getElementById('sectionTabs');
    if (sectionTabsEl) {
      new Sortable(sectionTabsEl, {
        animation: 150,
        handle: '.drag-handle',
        onEnd: function(evt) {
          updateSectionOrder();
        }
      });
    }

    // Initialize SortableJS for questions in each section
    document.querySelectorAll('.section-content').forEach(sectionContent => {
      const sectionId = sectionContent.getAttribute('data-section-id');

      // Find all question containers in this section
      const questionContainers = sectionContent.querySelectorAll('.questions-container');

      questionContainers.forEach(container => {
        new Sortable(container, {
          animation: 150,
          handle: '.question-drag-handle',
          onEnd: function(evt) {
            updateQuestionOrder(sectionId);
          }
        });
      });
    });

    // Add click event listener for delete question buttons
    document.querySelectorAll('.delete-question-btn').forEach(button => {
      button.addEventListener('click', function() {
        const questionId = this.getAttribute('data-question-id');
        openDeleteQuestionModal(questionId);
      });
    });

    // Delete Question Form Submit
    document.getElementById('deleteQuestionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const questionId = document.getElementById('deleteQuestionId').value;

      fetch(`/admin/questions/${questionId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Show success toast notification
          if (typeof ToastNotifications !== 'undefined') {
            ToastNotifications.success('Question deleted successfully');
          } else {
            alert('Question deleted successfully');
          }

          // Close the modal
          closeDeleteQuestionModal();

          // Reload the page to reflect the changes
          window.location.reload();
        } else {
          // Show error toast notification
          if (typeof ToastNotifications !== 'undefined') {
            ToastNotifications.error(data.message || 'Error deleting question');
          } else {
            alert('Error deleting question: ' + (data.message || 'Unknown error'));
          }
        }
      })
      .catch(error => {
        console.error('Error deleting question:', error);
        if (typeof ToastNotifications !== 'undefined') {
          ToastNotifications.error('Error deleting question. Please try again.');
        } else {
          alert('Error deleting question. Please try again.');
        }
      });
    });

    // Add click event listener for view question buttons
    document.querySelectorAll('.view-question-btn').forEach(button => {
      button.addEventListener('click', function() {
        const questionData = {
          questionId: this.getAttribute('data-question-id'),
          questionText: this.getAttribute('data-question-text'),
          questionType: this.getAttribute('data-question-type'),
          marks: this.getAttribute('data-marks'),
          negativeMarks: this.getAttribute('data-negative-marks'),
          solution: this.getAttribute('data-solution'),
          options: this.getAttribute('data-options')
        };
        openQuestionDetailsModal(questionData);
      });
    });

    // Add click event listener for section instructions
    document.querySelectorAll('.section-instructions').forEach(element => {
      element.addEventListener('click', function() {
        const instructions = this.getAttribute('data-instructions');
        if (instructions) {
          document.getElementById('instructionsModalContent').textContent = instructions;
          document.getElementById('instructionsModal').classList.remove('hidden');
        }
      });
    });
    // Add Section Button
    document.getElementById('addSectionBtn').addEventListener('click', openAddSectionModal);

    // Reorder Sections Button
    document.getElementById('reorderSectionsBtn').addEventListener('click', openReorderSectionsModal);

    // Update All Question Numbers Button
    document.getElementById('updateAllQuestionNumbersBtn').addEventListener('click', function() {
      updateAllQuestionNumbersAcrossSections();
    });

    // Reorder Questions Buttons
    document.querySelectorAll('.reorder-questions-btn').forEach(button => {
      button.addEventListener('click', function() {
        const sectionId = this.getAttribute('data-section-id');
        openReorderQuestionsModal(sectionId);
      });
    });

    // Edit Section Buttons
    document.querySelectorAll('.edit-section-btn').forEach(button => {
      button.addEventListener('click', function() {
        const sectionId = this.getAttribute('data-section-id');
        const sectionName = this.getAttribute('data-section-name');
        const sectionInstructions = this.getAttribute('data-section-instructions');
        const passingMarks = this.getAttribute('data-section-passing-marks');
        const totalMarks = this.getAttribute('data-section-total-marks');
        openEditSectionModal(sectionId, sectionName, sectionInstructions, passingMarks, totalMarks);
      });
    });

    // Delete Section Buttons
    document.querySelectorAll('.delete-section-btn').forEach(button => {
      button.addEventListener('click', function() {
        const sectionId = this.getAttribute('data-section-id');
        openDeleteSectionModal(sectionId);
      });
    });

    // Add Section Form Submit
    document.getElementById('addSectionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const examId = document.getElementById('examId').value;
      const sectionName = document.getElementById('sectionName').value;
      const sectionInstructions = document.getElementById('sectionInstructions').value;
      const sectionPassingMarks = document.getElementById('sectionPassingMarks').value;

      fetch(`/tests/admin/sections/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          exam_id: examId,
          section_name: sectionName,
          instructions: sectionInstructions,
          passing_marks: sectionPassingMarks || null
        }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Reload the page to show the new section
          window.location.reload();
        } else {
          alert('Error adding section: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error adding section:', error);
        alert('Error adding section. Please try again.');
      });
    });

    // Edit Section Form Submit
    document.getElementById('editSectionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const sectionId = document.getElementById('editSectionId').value;
      const sectionName = document.getElementById('editSectionName').value;
      const sectionInstructions = document.getElementById('editSectionInstructions').value;
      const sectionPassingMarks = document.getElementById('editSectionPassingMarks').value;
      const sectionTotalMarks = document.getElementById('editSectionTotalMarks').value;

      // Validate passing marks
      if (sectionPassingMarks && parseFloat(sectionPassingMarks) > parseFloat(sectionTotalMarks)) {
        if (typeof ToastNotifications !== 'undefined') {
          ToastNotifications.error('Passing marks cannot be greater than total marks');
        } else {
          alert('Passing marks cannot be greater than total marks');
        }
        return;
      }

      fetch(`/admin/sections/${sectionId}/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section_name: sectionName,
          instructions: sectionInstructions,
          passing_marks: sectionPassingMarks || null
        }),
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
          // Update the section in the UI without reloading the page
          updateSectionInUI(sectionId, sectionName, sectionInstructions, sectionPassingMarks);
          closeEditSectionModal();

          // Show toast notification if available
          if (typeof ToastNotifications !== 'undefined') {
            if (data.toast) {
              ToastNotifications[data.toast.type](data.toast.message);
            } else {
              ToastNotifications.success('Section updated successfully');
            }
          } else {
            alert(data.message || 'Section updated successfully');
          }
        } else {
          // Show error toast notification if available
          if (typeof ToastNotifications !== 'undefined') {
            if (data.toast) {
              ToastNotifications[data.toast.type](data.toast.message);
            } else {
              ToastNotifications.error('Error updating section: ' + data.message);
            }
          } else {
            alert('Error updating section: ' + data.message);
          }
        }
      })
      .catch(error => {
        if (typeof ToastNotifications !== 'undefined') {
          ToastNotifications.error('Error updating section. Please try again.');
        } else {
          alert('Error updating section. Please try again.');
        }
      });
    });

    // Delete Section Form Submit
    document.getElementById('deleteSectionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const sectionId = document.getElementById('deleteSectionId').value;

      fetch(`/admin/sections/${sectionId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Reload the page to show the updated sections list
          window.location.reload();
        } else {
          // Use confirmation dialog instead of alert
          if (window.confirmationDialog) {
            window.confirmationDialog.show({
              title: 'Error',
              message: 'Error deleting section: ' + data.message,
              confirmText: 'OK',
              cancelText: null,
              type: 'error'
            });
          } else {
            // Fallback to alert if confirmation dialog is not available
            alert('Error deleting section: ' + data.message);
          }
        }
      })
      .catch(error => {
        console.error('Error deleting section:', error);
        // Use confirmation dialog instead of alert
        if (window.confirmationDialog) {
          window.confirmationDialog.show({
            title: 'Error',
            message: 'Error deleting section. Please try again.',
            confirmText: 'OK',
            cancelText: null,
            type: 'error'
          });
        } else {
          // Fallback to console.error if confirmation dialog is not available
          console.error('Error deleting section. Please try again.');
        }
      });
    });

    // Close modals when clicking outside
    document.querySelectorAll('#addSectionModal, #editSectionModal, #deleteSectionModal').forEach(modal => {
      modal.addEventListener('click', function(e) {
        if (e.target === this) {
          this.classList.add('hidden');
        }
      });
    });

    // Section Tabs
    document.querySelectorAll('.section-tab').forEach(tab => {
      tab.addEventListener('click', function() {
        const sectionId = this.getAttribute('data-section-id');

        // Update active tab
        document.querySelectorAll('.section-tab').forEach(t => {
          t.classList.remove('border-purple-500', 'text-purple-600');
          t.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        });

        this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        this.classList.add('border-purple-500', 'text-purple-600');

        // Show selected section content, hide others
        document.querySelectorAll('.section-content').forEach(content => {
          if (content.getAttribute('data-section-id') === sectionId) {
            content.classList.remove('hidden');
          } else {
            content.classList.add('hidden');
          }
        });
      });
    });
  });

  // Instructions Modal Functions
  function closeInstructionsModal() {
    document.getElementById('instructionsModal').classList.add('hidden');
  }

  // Question Details Modal Functions
  function openQuestionDetailsModal(questionData) {
    // Reset modal content
    document.getElementById('modal-question-text').textContent = '';
    document.getElementById('modal-options-list').innerHTML = '';
    document.getElementById('modal-solution-text').textContent = '';
    document.getElementById('modal-question-type').textContent = '';
    document.getElementById('modal-marks-badge').textContent = '';
    document.getElementById('modal-negative-marks-badge').classList.add('hidden');

    // Hide all question type specific containers
    document.getElementById('modal-options-container').classList.add('hidden');
    document.getElementById('modal-true-false-container').classList.add('hidden');
    document.getElementById('modal-fill-up-container').classList.add('hidden');
    document.getElementById('modal-essay-container').classList.add('hidden');

    // Set question text
    document.getElementById('modal-question-text').textContent = questionData.questionText;

    // Set question type
    let questionTypeText = '';
    let questionTypeBgClass = '';
    let questionTypeTextClass = '';

    switch(questionData.questionType) {
      case 'multiple_choice':
        questionTypeText = 'Multiple Choice';
        questionTypeBgClass = 'bg-blue-100';
        questionTypeTextClass = 'text-blue-800';
        break;
      case 'true_false':
        questionTypeText = 'True/False';
        questionTypeBgClass = 'bg-green-100';
        questionTypeTextClass = 'text-green-800';
        break;
      case 'fill_up':
        questionTypeText = 'Fill in the Blank';
        questionTypeBgClass = 'bg-purple-100';
        questionTypeTextClass = 'text-purple-800';
        break;
      case 'essay':
        questionTypeText = 'Essay';
        questionTypeBgClass = 'bg-yellow-100';
        questionTypeTextClass = 'text-yellow-800';
        break;
      default:
        questionTypeText = questionData.questionType;
        questionTypeBgClass = 'bg-gray-100';
        questionTypeTextClass = 'text-gray-800';
    }

    const typeElement = document.getElementById('modal-question-type');
    typeElement.textContent = questionTypeText;
    typeElement.className = `px-2 py-1 text-xs font-medium rounded-full ${questionTypeBgClass} ${questionTypeTextClass}`;

    // Set marks
    document.getElementById('modal-marks-badge').textContent = questionData.marks + ' mark' + (questionData.marks > 1 ? 's' : '');

    // Set negative marks
    if (questionData.negativeMarks > 0) {
      const negativeMarksElement = document.getElementById('modal-negative-marks-badge');
      negativeMarksElement.textContent = '-' + questionData.negativeMarks + ' mark' + (questionData.negativeMarks > 1 ? 's' : '');
      negativeMarksElement.classList.remove('hidden');
    }

    // Set solution
    document.getElementById('modal-solution-text').textContent = questionData.solution || 'No solution provided';

    // Handle question type specific content
    if (questionData.questionType === 'multiple_choice') {
      document.getElementById('modal-options-container').classList.remove('hidden');

      // Parse options if they're a string
      let options = questionData.options;
      if (typeof options === 'string') {
        try {
          options = JSON.parse(options.replace(/&quot;/g, '"'));
        } catch (e) {
          console.error('Error parsing options:', e);
          options = [];
        }
      }

      // Create options list
      const optionsList = document.getElementById('modal-options-list');
      optionsList.innerHTML = '';

      options.forEach((option, index) => {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'flex items-center py-1';

        const isCorrect = option.is_correct || false;

        optionDiv.innerHTML = `
          <div class="flex items-center h-5">
            <input type="radio" disabled ${isCorrect ? 'checked' : ''} class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
          </div>
          <div class="ml-3 text-sm">
            <label class="font-medium ${isCorrect ? 'text-green-600 font-semibold' : 'text-gray-700'}">
              ${option.option_text || option.text || ''}
              ${isCorrect ? '<span class="ml-2 text-xs text-green-600">(Correct)</span>' : ''}
            </label>
          </div>
        `;

        optionsList.appendChild(optionDiv);
      });
    } else if (questionData.questionType === 'true_false') {
      document.getElementById('modal-true-false-container').classList.remove('hidden');

      // Determine correct answer
      let correctAnswer = 'Not specified';

      if (questionData.correctAnswer) {
        correctAnswer = questionData.correctAnswer === 'true' ? 'true' : 'false';
      } else if (questionData.options && questionData.options.length > 0) {
        const options = typeof questionData.options === 'string' ?
          JSON.parse(questionData.options.replace(/&quot;/g, '"')) :
          questionData.options;

        if (options[0] && options[0].is_correct !== undefined) {
          correctAnswer = options[0].is_correct ? 'true' : 'false';
        }
      }

      // Set radio buttons
      document.getElementById('modal-true-option').checked = correctAnswer === 'true';
      document.getElementById('modal-false-option').checked = correctAnswer === 'false';

      // Style labels
      const trueLabel = document.getElementById('modal-true-label');
      const falseLabel = document.getElementById('modal-false-label');

      trueLabel.className = correctAnswer === 'true' ? 'font-medium text-green-600 font-semibold' : 'font-medium text-gray-700';
      falseLabel.className = correctAnswer === 'false' ? 'font-medium text-green-600 font-semibold' : 'font-medium text-gray-700';

      // Add correct indicator
      if (correctAnswer === 'true') {
        trueLabel.innerHTML = 'True <span class="ml-2 text-xs text-green-600">(Correct)</span>';
        falseLabel.textContent = 'False';
      } else {
        trueLabel.textContent = 'True';
        falseLabel.innerHTML = 'False <span class="ml-2 text-xs text-green-600">(Correct)</span>';
      }
    } else if (questionData.questionType === 'fill_up') {
      document.getElementById('modal-fill-up-container').classList.remove('hidden');

      // Determine correct answers
      let correctAnswers = 'Not specified';
      if (questionData.options && questionData.options.length > 0) {
        const options = typeof questionData.options === 'string' ?
          JSON.parse(questionData.options.replace(/&quot;/g, '"')) :
          questionData.options;

        correctAnswers = options.map(opt => opt.option_text || opt.text || '').filter(Boolean).join(' | ');
      }

      document.getElementById('modal-fill-up-answer').textContent = correctAnswers;
    } else if (questionData.questionType === 'essay') {
      document.getElementById('modal-essay-container').classList.remove('hidden');
    }

    // Show the modal
    document.getElementById('questionDetailsModal').classList.remove('hidden');
  }

  function closeQuestionDetailsModal() {
    document.getElementById('questionDetailsModal').classList.add('hidden');
  }

  // Function to update section in UI without reloading the page
  function updateSectionInUI(sectionId, sectionName, instructions, passingMarks) {
    // Update section tab
    const sectionTab = document.querySelector(`.section-tab[data-section-id="${sectionId}"]`);
    if (sectionTab) {
      // Keep the existing content (section number and question count) but update the name
      const tabContent = sectionTab.innerHTML;
      const tabContentParts = tabContent.split('</span>');
      if (tabContentParts.length >= 3) {
        // First part is the section number, last part is the question count
        sectionTab.innerHTML = tabContentParts[0] + '</span> ' + sectionName + ' ' + tabContentParts[tabContentParts.length - 1];
      }
    }

    // Update section content
    const sectionContent = document.querySelector(`.section-content[data-section-id="${sectionId}"]`);
    if (sectionContent) {
      // Update section name in the header
      const sectionHeader = sectionContent.querySelector('h4');
      if (sectionHeader) sectionHeader.textContent = sectionName;

      // Update passing marks
      const passingMarksElement = sectionContent.querySelector(`.section-passing-marks[data-section-id="${sectionId}"]`);
      if (passingMarksElement) {
        passingMarksElement.textContent = passingMarks || 'Not set';
        console.log('Updated passing marks display to:', passingMarksElement.textContent);
      } else {
        console.log('Passing marks element not found');
        // Fallback to the old method
        const passingMarksElements = sectionContent.querySelectorAll('h4');
        passingMarksElements.forEach(element => {
          if (element.textContent.includes('Passing Marks')) {
            const passingMarksText = element.nextElementSibling;
            if (passingMarksText) {
              passingMarksText.textContent = passingMarks || 'Not set';
              console.log('Updated passing marks display (fallback) to:', passingMarksText.textContent);
            }
          }
        });
      }

      // Update or add instructions
      console.log('Updating instructions for section', sectionId, 'with value:', instructions);

      // First, try to find the section-instructions element
      const instructionsElement = sectionContent.querySelector(`.section-instructions[data-section-id="${sectionId}"]`);
      const instructionsContainer = sectionContent.querySelector('.mt-3.pt-3.border-t');

      if (instructions) {
        // Encode HTML entities for the data attribute
        const encodedInstructions = instructions.replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#039;');

        // Prepare the display text (truncated if needed)
        const displayText = instructions.length > 100 ?
          instructions.substring(0, 100) + '... (click to view full)' :
          instructions;

        if (instructionsElement) {
          // Update existing instructions element
          console.log('Found existing instructions element, updating it');
          instructionsElement.setAttribute('data-instructions', encodedInstructions);
          instructionsElement.textContent = displayText;
        } else if (instructionsContainer) {
          // Create new instructions element in existing container
          console.log('Found instructions container but no element, creating new element');
          const instructionsText = document.createElement('p');
          instructionsText.className = 'text-sm text-gray-600 mt-1 whitespace-pre-line cursor-pointer hover:text-blue-600 section-instructions';
          instructionsText.setAttribute('data-section-id', sectionId);
          instructionsText.setAttribute('data-instructions', encodedInstructions);
          instructionsText.textContent = displayText;

          // Clear existing content and add new element
          instructionsContainer.innerHTML = '';

          const instructionsHeader = document.createElement('h4');
          instructionsHeader.className = 'text-sm font-medium text-gray-700';
          instructionsHeader.textContent = 'Section Instructions:';

          instructionsContainer.appendChild(instructionsHeader);
          instructionsContainer.appendChild(instructionsText);
        } else {
          // Add new instructions section
          console.log('No instructions container found, creating new one');
          const statsContainer = sectionContent.querySelector('.mb-4.p-3.bg-gray-50');
          if (statsContainer) {
            const instructionsDiv = document.createElement('div');
            instructionsDiv.className = 'mt-3 pt-3 border-t border-gray-200';

            const instructionsHeader = document.createElement('h4');
            instructionsHeader.className = 'text-sm font-medium text-gray-700';
            instructionsHeader.textContent = 'Section Instructions:';

            const instructionsText = document.createElement('p');
            instructionsText.className = 'text-sm text-gray-600 mt-1 whitespace-pre-line cursor-pointer hover:text-blue-600 section-instructions';
            instructionsText.setAttribute('data-section-id', sectionId);
            instructionsText.setAttribute('data-instructions', encodedInstructions);
            instructionsText.textContent = displayText;

            instructionsDiv.appendChild(instructionsHeader);
            instructionsDiv.appendChild(instructionsText);
            statsContainer.appendChild(instructionsDiv);
            console.log('Added new instructions container and element');
          } else {
            console.log('Stats container not found, cannot add instructions');
          }
        }
      } else if (instructionsContainer) {
        // Remove instructions if empty
        instructionsContainer.remove();
        console.log('Removed instructions container (empty instructions)');
      }
    }
  }

  // Reorder Sections Modal Functions
  function openReorderSectionsModal() {
    const sectionOrderList = document.getElementById('sectionOrderList');
    sectionOrderList.innerHTML = '';

    // Get all section tabs
    const sectionTabs = document.querySelectorAll('.section-tab');

    // Create list items for each section
    sectionTabs.forEach((tab, index) => {
      const sectionId = tab.getAttribute('data-section-id');
      const sectionName = tab.textContent.trim();
      const position = index + 1;

      const listItem = document.createElement('li');
      listItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';
      listItem.setAttribute('data-section-id', sectionId);
      listItem.setAttribute('data-position', position);

      listItem.innerHTML = `
        <div class="flex items-center">
          <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full mr-2">${position}</span>
          <span class="text-gray-800">${sectionName}</span>
        </div>
        <div class="flex space-x-1">
          <button type="button" class="move-up-btn p-1 text-gray-500 hover:text-gray-700 ${index === 0 ? 'opacity-50 cursor-not-allowed' : ''}" ${index === 0 ? 'disabled' : ''}>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
          </button>
          <button type="button" class="move-down-btn p-1 text-gray-500 hover:text-gray-700 ${index === sectionTabs.length - 1 ? 'opacity-50 cursor-not-allowed' : ''}" ${index === sectionTabs.length - 1 ? 'disabled' : ''}>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
      `;

      sectionOrderList.appendChild(listItem);
    });

    // Update the order numbers and attach event listeners
    updateSectionOrderNumbers();

    document.getElementById('reorderSectionsModal').classList.remove('hidden');
  }

  function closeReorderSectionsModal() {
    document.getElementById('reorderSectionsModal').classList.add('hidden');
  }

  function updateSectionOrderNumbers() {
    const items = document.querySelectorAll('#sectionOrderList li');

    items.forEach((item, index) => {
      const position = index + 1;
      item.setAttribute('data-position', position);

      // Update position number in UI
      const positionSpan = item.querySelector('.bg-purple-100');
      if (positionSpan) {
        positionSpan.textContent = position;
      }

      // Update move up/down button states
      const moveUpBtn = item.querySelector('.move-up-btn');
      const moveDownBtn = item.querySelector('.move-down-btn');

      if (moveUpBtn) {
        if (index === 0) {
          moveUpBtn.classList.add('opacity-50', 'cursor-not-allowed');
          moveUpBtn.setAttribute('disabled', '');
          // Remove event listener if it exists
          moveUpBtn.removeEventListener('click', moveItemUp);
        } else {
          moveUpBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          moveUpBtn.removeAttribute('disabled');
          // Remove existing event listener to prevent duplicates
          moveUpBtn.removeEventListener('click', moveItemUp);
          // Add event listener
          moveUpBtn.addEventListener('click', moveItemUp);
        }
      }

      if (moveDownBtn) {
        if (index === items.length - 1) {
          moveDownBtn.classList.add('opacity-50', 'cursor-not-allowed');
          moveDownBtn.setAttribute('disabled', '');
          // Remove event listener if it exists
          moveDownBtn.removeEventListener('click', moveItemDown);
        } else {
          moveDownBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          moveDownBtn.removeAttribute('disabled');
          // Remove existing event listener to prevent duplicates
          moveDownBtn.removeEventListener('click', moveItemDown);
          // Add event listener
          moveDownBtn.addEventListener('click', moveItemDown);
        }
      }
    });
  }

  // Function to move an item up in the section order list
  function moveItemUp(event) {
    const listItem = event.currentTarget.closest('li');
    const prevItem = listItem.previousElementSibling;

    if (prevItem) {
      const sectionOrderList = document.getElementById('sectionOrderList');
      sectionOrderList.insertBefore(listItem, prevItem);
      updateSectionOrderNumbers();
      saveSectionOrder();
    }
  }

  // Function to move an item down in the section order list
  function moveItemDown(event) {
    const listItem = event.currentTarget.closest('li');
    const nextItem = listItem.nextElementSibling;

    if (nextItem) {
      const sectionOrderList = document.getElementById('sectionOrderList');
      sectionOrderList.insertBefore(nextItem, listItem);
      updateSectionOrderNumbers();
      saveSectionOrder();
    }
  }

  function saveSectionOrder() {
    const items = document.querySelectorAll('#sectionOrderList li');
    const newOrder = [];

    items.forEach((item, index) => {
      const sectionId = item.getAttribute('data-section-id');
      const position = index + 1; // Ensure continuous sequence

      newOrder.push({
        section_id: sectionId,
        position: position
      });
    });

    // Update the section tabs in the main UI
    // This function will also send the updated order to the server if needed
    updateSectionTabsOrder(newOrder);

    // Show success message
    ToastNotifications.success('Section order updated successfully');
  }

  function updateSectionTabsOrder(newOrder) {
    // Get the section tabs container
    const sectionTabsContainer = document.getElementById('sectionTabs');
    if (!sectionTabsContainer) return;

    // Create a map of section IDs to their wrapper elements
    const sectionTabMap = {};
    document.querySelectorAll('.section-tab-wrapper').forEach(wrapper => {
      const tab = wrapper.querySelector('.section-tab');
      if (tab) {
        const sectionId = tab.getAttribute('data-section-id');
        sectionTabMap[sectionId] = wrapper;
      }
    });

    // Reorder the tabs based on the new order
    newOrder.forEach((section, index) => {
      const wrapper = sectionTabMap[section.section_id];
      if (wrapper) {
        // Update the position attribute with continuous sequence
        const continuousPosition = index + 1;
        const tab = wrapper.querySelector('.section-tab');
        if (tab) {
          tab.setAttribute('data-position', continuousPosition);

          // Update the section number with continuous sequence
          const numberSpan = tab.querySelector('.section-number');
          if (numberSpan) {
            numberSpan.textContent = continuousPosition;
          }
        }

        // Move the wrapper to the end of the container
        sectionTabsContainer.appendChild(wrapper);
      }
    });

    // Update the database with the new continuous sequence
    const updatedOrder = newOrder.map((section, index) => {
      const continuousPosition = index + 1;
      console.log(`Section ID ${section.section_id}: Original position ${section.position}, New position ${continuousPosition}`);
      return {
        section_id: section.section_id,
        position: continuousPosition // Ensure continuous sequence
      };
    });

    // Send the updated order to the server if it's different from the original
    if (JSON.stringify(updatedOrder) !== JSON.stringify(newOrder)) {
      fetch('/tests/admin/sections/reorder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sections: updatedOrder })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          console.log('Section sequence updated successfully');
          // After successfully updating section order, update all question numbers across sections
          updateAllQuestionNumbersAcrossSections();
        } else {
          console.error('Failed to update section sequence:', data.message);
        }
      })
      .catch(error => {
        console.error('Error updating section sequence:', error);
      });
    } else {
      // Even if no server update is needed, still update question numbers
      updateAllQuestionNumbersAcrossSections();
    }
  }

  // Function to update all question numbers across all sections to maintain continuous sequence
  function updateAllQuestionNumbersAcrossSections() {
    console.log('Updating all question numbers across sections...');

    // Get all sections in their current order
    const sectionTabs = document.querySelectorAll('.section-tab');
    const orderedSectionIds = [];

    sectionTabs.forEach(tab => {
      const sectionId = tab.getAttribute('data-section-id');
      if (sectionId) {
        orderedSectionIds.push(sectionId);
      }
    });

    console.log('Ordered section IDs:', orderedSectionIds);

    // Collect all questions from all sections
    let globalQuestionNumber = 1;
    const allQuestionUpdates = [];

    // Process each section in order
    orderedSectionIds.forEach(sectionId => {
      const questionsContainer = document.querySelector(`.section-content[data-section-id="${sectionId}"] .questions-container`);
      if (!questionsContainer) return;

      const questionItems = questionsContainer.querySelectorAll('.question-item');
      const sectionQuestionUpdates = [];

      // Update question numbers in this section
      questionItems.forEach(item => {
        const questionId = item.getAttribute('data-question-id');
        if (!questionId) return;

        // Update the position attribute
        item.setAttribute('data-position', globalQuestionNumber);

        // Update the question number display
        const numberSpan = item.querySelector('.font-semibold.text-gray-800');
        if (numberSpan) {
          numberSpan.textContent = 'Q' + globalQuestionNumber;
        }

        // Add to updates array for database update
        sectionQuestionUpdates.push({
          question_id: questionId,
          position: globalQuestionNumber
        });

        globalQuestionNumber++;
      });

      // If we have updates for this section, send them to the server
      if (sectionQuestionUpdates.length > 0) {
        allQuestionUpdates.push({
          section_id: sectionId,
          updates: sectionQuestionUpdates
        });
      }
    });

    console.log('All question updates:', allQuestionUpdates);

    // Send all updates to the server
    if (allQuestionUpdates.length > 0) {
      // For each section, update its questions
      allQuestionUpdates.forEach(sectionUpdate => {
        fetch('/tests/admin/questions/reorder', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            section_id: sectionUpdate.section_id,
            questions: sectionUpdate.updates
          })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            console.log(`Questions in section ${sectionUpdate.section_id} updated successfully`);
          } else {
            console.error(`Failed to update questions in section ${sectionUpdate.section_id}:`, data.message);
          }
        })
        .catch(error => {
          console.error(`Error updating questions in section ${sectionUpdate.section_id}:`, error);
        });
      });

      // Show success message
      ToastNotifications.success('All question numbers updated successfully');
    }
  }

  // Reorder Questions Modal Functions
  function openReorderQuestionsModal(sectionId) {
    const questionOrderList = document.getElementById('questionOrderList');
    questionOrderList.innerHTML = '';

    // Store the section ID in the modal for reference
    document.getElementById('reorderQuestionsModal').setAttribute('data-section-id', sectionId);

    // Get all questions in the section
    const questionsContainer = document.querySelector(`.section-content[data-section-id="${sectionId}"] .questions-container`);
    if (!questionsContainer) return;

    const questionItems = questionsContainer.querySelectorAll('.question-item');

    // Create list items for each question
    questionItems.forEach((item, index) => {
      const questionId = item.getAttribute('data-question-id');

      // Get the actual question text (not just the question number)
      const questionNumberEl = item.querySelector('.font-semibold.text-gray-800');
      const questionTextEl = item.querySelector('.question-text');

      // Extract the question text and truncate it if needed
      let questionText = '';
      if (questionTextEl) {
        // Get the actual question text content
        questionText = questionTextEl.textContent.trim();
        // Remove any HTML tags that might be present
        questionText = questionText.replace(/<[^>]*>/g, '');
        // Truncate to first 50 characters if longer
        if (questionText.length > 50) {
          questionText = questionText.substring(0, 50) + '...';
        }
      }

      // Log for debugging
      console.log('Question text extracted:', questionText);

      // Fallback to question number if no text found
      const displayText = questionText || (questionNumberEl ? questionNumberEl.textContent : `Question ${index + 1}`);
      const position = index + 1;

      const listItem = document.createElement('li');
      listItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';
      listItem.setAttribute('data-question-id', questionId);
      listItem.setAttribute('data-position', position);

      listItem.innerHTML = `
        <div class="flex items-center flex-1 min-w-0">
          <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-2 flex-shrink-0">${position}</span>
          <span class="text-gray-800 truncate">${displayText}</span>
        </div>
        <div class="flex space-x-1">
          <button type="button" class="move-up-btn p-1 text-gray-500 hover:text-gray-700 ${index === 0 ? 'opacity-50 cursor-not-allowed' : ''}" ${index === 0 ? 'disabled' : ''}>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
          </button>
          <button type="button" class="move-down-btn p-1 text-gray-500 hover:text-gray-700 ${index === questionItems.length - 1 ? 'opacity-50 cursor-not-allowed' : ''}" ${index === questionItems.length - 1 ? 'disabled' : ''}>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
      `;

      questionOrderList.appendChild(listItem);
    });

    // Update the order numbers and attach event listeners
    updateQuestionOrderNumbers();

    document.getElementById('reorderQuestionsModal').classList.remove('hidden');
  }

  function closeReorderQuestionsModal() {
    document.getElementById('reorderQuestionsModal').classList.add('hidden');
  }

  function updateQuestionOrderNumbers() {
    const items = document.querySelectorAll('#questionOrderList li');

    items.forEach((item, index) => {
      const position = index + 1;
      item.setAttribute('data-position', position);

      // Update position number in UI
      const positionSpan = item.querySelector('.bg-blue-100');
      if (positionSpan) {
        positionSpan.textContent = position;
      }

      // Update move up/down button states
      const moveUpBtn = item.querySelector('.move-up-btn');
      const moveDownBtn = item.querySelector('.move-down-btn');

      if (moveUpBtn) {
        if (index === 0) {
          moveUpBtn.classList.add('opacity-50', 'cursor-not-allowed');
          moveUpBtn.setAttribute('disabled', '');
          // Remove event listener if it exists
          moveUpBtn.removeEventListener('click', moveQuestionUp);
        } else {
          moveUpBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          moveUpBtn.removeAttribute('disabled');
          // Remove existing event listener to prevent duplicates
          moveUpBtn.removeEventListener('click', moveQuestionUp);
          // Add event listener
          moveUpBtn.addEventListener('click', moveQuestionUp);
        }
      }

      if (moveDownBtn) {
        if (index === items.length - 1) {
          moveDownBtn.classList.add('opacity-50', 'cursor-not-allowed');
          moveDownBtn.setAttribute('disabled', '');
          // Remove event listener if it exists
          moveDownBtn.removeEventListener('click', moveQuestionDown);
        } else {
          moveDownBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          moveDownBtn.removeAttribute('disabled');
          // Remove existing event listener to prevent duplicates
          moveDownBtn.removeEventListener('click', moveQuestionDown);
          // Add event listener
          moveDownBtn.addEventListener('click', moveQuestionDown);
        }
      }
    });
  }

  // Function to move a question up in the order list
  function moveQuestionUp(event) {
    const listItem = event.currentTarget.closest('li');
    const prevItem = listItem.previousElementSibling;

    if (prevItem) {
      const questionOrderList = document.getElementById('questionOrderList');
      questionOrderList.insertBefore(listItem, prevItem);
      updateQuestionOrderNumbers();
      saveQuestionOrder();
    }
  }

  // Function to move a question down in the order list
  function moveQuestionDown(event) {
    const listItem = event.currentTarget.closest('li');
    const nextItem = listItem.nextElementSibling;

    if (nextItem) {
      const questionOrderList = document.getElementById('questionOrderList');
      questionOrderList.insertBefore(nextItem, listItem);
      updateQuestionOrderNumbers();
      saveQuestionOrder();
    }
  }

  function saveQuestionOrder() {
    const modal = document.getElementById('reorderQuestionsModal');
    const sectionId = modal.getAttribute('data-section-id');
    const items = document.querySelectorAll('#questionOrderList li');
    const newOrder = [];

    items.forEach((item, index) => {
      const questionId = item.getAttribute('data-question-id');
      const position = index + 1; // Ensure continuous sequence

      newOrder.push({
        question_id: questionId,
        position: position
      });
    });

    // Update the question numbers in the main UI
    // This function will also send the updated order to the server
    updateQuestionNumbersInUI(sectionId, newOrder);

    // After updating the current section, update all question numbers across sections
    // to ensure continuous sequence across the entire test
    setTimeout(() => {
      updateAllQuestionNumbersAcrossSections();
    }, 500); // Small delay to ensure the current section update completes first

    // Show success message
    ToastNotifications.success('Question order updated successfully');
  }

  function updateQuestionNumbersInUI(sectionId, newOrder) {
    // Get the questions container
    const questionsContainer = document.querySelector(`.section-content[data-section-id="${sectionId}"] .questions-container`);
    if (!questionsContainer) return;

    // Create a map of question IDs to their elements
    const questionMap = {};
    questionsContainer.querySelectorAll('.question-item').forEach(item => {
      const questionId = item.getAttribute('data-question-id');
      questionMap[questionId] = item;
    });

    // Reorder the questions based on the new order
    newOrder.forEach((question, index) => {
      const item = questionMap[question.question_id];
      if (item) {
        // Update the position attribute with the continuous sequence number
        const continuousPosition = index + 1;
        item.setAttribute('data-position', continuousPosition);

        // Update the question number with the continuous sequence
        const numberSpan = item.querySelector('.font-semibold.text-gray-800');
        if (numberSpan) {
          numberSpan.textContent = 'Q' + continuousPosition;
        }

        // Move the item to the end of the container
        questionsContainer.appendChild(item);
      }
    });

    // Update the database with the new continuous sequence
    const updatedOrder = newOrder.map((question, index) => {
      const continuousPosition = index + 1;
      console.log(`Question ID ${question.question_id}: Original position ${question.position}, New position ${continuousPosition}`);
      return {
        question_id: question.question_id,
        position: continuousPosition // Ensure continuous sequence
      };
    });

    // Send the updated order to the server
    fetch('/tests/admin/questions/reorder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        section_id: sectionId,
        questions: updatedOrder
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        console.log('Question sequence updated successfully');
      } else {
        console.error('Failed to update question sequence:', data.message);
      }
    })
    .catch(error => {
      console.error('Error updating question sequence:', error);
    });
  }
</script>

<!-- Section Instructions Modal -->
<div id="instructionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Section Instructions</h3>
      <button type="button" onclick="closeInstructionsModal()" class="text-gray-400 hover:text-gray-500">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg">
      <p id="instructionsModalContent" class="text-gray-700 whitespace-pre-line"></p>
    </div>

    <div class="mt-6 flex justify-end">
      <button type="button" onclick="closeInstructionsModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
        Close
      </button>
    </div>
  </div>
</div>

<!-- Question Details Modal -->
<%- include('../questions/partials/question-details-modal') %>

<!-- Reorder Sections Modal -->
<div id="reorderSectionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Reorder Sections</h3>
      <button type="button" onclick="closeReorderSectionsModal()" class="text-gray-400 hover:text-gray-500">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="mb-4">
      <p class="text-sm text-gray-600">Use the up and down arrows to reorder sections. The order will be saved automatically.</p>
    </div>

    <ul id="sectionOrderList" class="space-y-2 mb-4">
      <!-- Sections will be populated here via JavaScript -->
    </ul>

    <div class="flex justify-end">
      <button type="button" onclick="closeReorderSectionsModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
        Close
      </button>
    </div>
  </div>
</div>

<!-- Reorder Questions Modal -->
<div id="reorderQuestionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">Reorder Questions</h3>
      <button type="button" onclick="closeReorderQuestionsModal()" class="text-gray-400 hover:text-gray-500">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="mb-4">
      <p class="text-sm text-gray-600">Use the up and down arrows to reorder questions. The order will be saved automatically.</p>
    </div>

    <ul id="questionOrderList" class="space-y-2 mb-4">
      <!-- Questions will be populated here via JavaScript -->
    </ul>

    <div class="flex justify-end">
      <button type="button" onclick="closeReorderQuestionsModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
        Close
      </button>
    </div>
  </div>
</div>

<!-- Delete Question Confirmation Modal -->
<div id="deleteQuestionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Question</h3>
    <p class="mb-4">Are you sure you want to delete this question? This action cannot be undone.</p>

    <form id="deleteQuestionForm" class="space-y-4">
      <input type="hidden" id="deleteQuestionId">

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeDeleteQuestionModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
          Delete Question
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Handle main form submission via AJAX
  document.addEventListener('DOMContentLoaded', function() {
    const testEditForm = document.getElementById('testEditForm');
    if (testEditForm) {
      testEditForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;
        submitButton.textContent = 'Saving...';
        submitButton.disabled = true;

        // Collect form data
        const formData = new FormData(this);

        // Send AJAX request
        fetch(this.action, {
          method: 'POST',
          body: new URLSearchParams(formData),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          if (data.success) {
            // Show success toast
            ToastNotifications.success(data.message || 'Test updated successfully');
          } else {
            // Show error toast
            ToastNotifications.error(data.message || 'Error updating test');
          }
        })
        .catch(error => {
          // Reset button state
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Show error toast
          console.error('Error updating test:', error);
          ToastNotifications.error('An error occurred while updating the test');
        });
      });
    }
  });
</script>