<!-- Admin Tests Index -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800"><%= __('admin.exams') %></h2>
      <div class="flex space-x-2">
        <a href="/admin/tests/trash" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Trash
        </a>
        <a href="/admin/tests/add" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <%= __('admin.addNew') %>
        </a>
      </div>
    </div>

    <!-- Removed Bulk Actions Bar from here -->

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
      <!-- Total Tests Card -->
      <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
            <svg class="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">Total Tests</div>
            <div class="text-lg font-semibold text-gray-900"><%= totalTests %></div>
            <div class="text-xs text-gray-500 mt-1">Active: <%= activeTests %> | Deleted: <%= deletedTests %></div>
          </div>
        </div>
      </div>

      <!-- Creators Card -->
      <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-green-100 rounded-full p-3">
            <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">Test Creators</div>
            <div class="text-lg font-semibold text-gray-900"><%= stats.creator_count %></div>
          </div>
        </div>
      </div>

      <!-- Published Tests Card -->
      <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-purple-100 rounded-full p-3">
            <svg class="h-6 w-6 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">Published Tests</div>
            <div class="text-lg font-semibold text-gray-900"><%= stats.published_count %></div>
          </div>
        </div>
      </div>

      <!-- Draft Tests Card -->
      <div class="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-yellow-100 rounded-full p-3">
            <svg class="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">Draft Tests</div>
            <div class="text-lg font-semibold text-gray-900"><%= stats.draft_count %></div>
          </div>
        </div>
      </div>

      <!-- Sections Card -->
      <div class="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-red-100 rounded-full p-3">
            <svg class="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">Total Sections</div>
            <div class="text-lg font-semibold text-gray-900"><%= stats.section_count %></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <form id="filterForm" action="/admin/tests" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.search') %></label>
          <input type="text" name="search" value="<%= query?.search || '' %>"
                 placeholder="<%= __('common.search') %> <%= __('admin.exams') %>..."
                 class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.status') %></label>
          <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value=""><%= __('common.all') %> <%= __('common.status') %></option>
            <option value="draft" <%= (query?.status === 'draft') ? 'selected' : '' %>>Draft</option>
            <option value="published" <%= (query?.status === 'published') ? 'selected' : '' %>>Published</option>
            <option value="scheduled" <%= (query?.status === 'scheduled') ? 'selected' : '' %>>Scheduled</option>
            <option value="archived" <%= (query?.status === 'archived') ? 'selected' : '' %>>Archived</option>
          </select>
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <select name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Categories</option>
            <% categories.forEach(category => { %>
              <option value="<%= category.category_id %>" <%= (query?.category == category.category_id) ? 'selected' : '' %>>
                <%= category.name %>
              </option>
            <% }); %>
          </select>
        </div>

        <!-- Created By Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
          <select name="created_by" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Creators</option>
            <% if (creators && creators.length > 0) { %>
              <% creators.forEach(creator => { %>
                <option value="<%= creator.id %>" <%= (query?.created_by == creator.id) ? 'selected' : '' %>>
                  <%= creator.username %>
                </option>
              <% }); %>
            <% } %>
          </select>
        </div>

        <!-- Assignment Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Assignment Status</label>
          <select name="assignment_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Tests</option>
            <option value="assigned" <%= (query?.assignment_status === 'assigned') ? 'selected' : '' %>>Assigned</option>
            <option value="unassigned" <%= (query?.assignment_status === 'unassigned') ? 'selected' : '' %>>Unassigned</option>
          </select>
        </div>

        <!-- Date Range Filter -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <div class="flex items-center border border-gray-300 rounded-md shadow-sm focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
            <input type="date" name="date_from" id="date_from" value="<%= query?.date_from || '' %>"
                   class="w-1/2 border-0 focus:ring-0 py-2 px-3" placeholder="From">
            <span class="text-gray-500 px-2">to</span>
            <input type="date" name="date_to" id="date_to" value="<%= query?.date_to || '' %>"
                   class="w-1/2 border-0 focus:ring-0 py-2 px-3" placeholder="To">
          </div>
          <p class="mt-1 text-xs text-gray-500">Filter by creation date</p>
        </div>

        <!-- Reset Button -->
        <div class="md:col-span-4 flex justify-end space-x-2 mt-4">
          <a href="/admin/tests" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            Reset
          </a>
        </div>
      </form>
    </div>

    <!-- Status Quick Filters -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <h3 class="text-lg font-medium text-gray-700 mb-4">Quick Filters by Status</h3>
      <div class="flex flex-wrap gap-2 mb-4">
        <a href="/admin/tests<%= query?.search ? '?search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= !query?.status ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
          All Status
        </a>
        <a href="/admin/tests?status=published<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'published' ? 'bg-green-700 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200' %> text-xs font-medium">
          Published (<%= stats.published_count %>)
        </a>
        <a href="/admin/tests?status=draft<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'draft' ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
          Draft (<%= stats.draft_count %>)
        </a>
        <a href="/admin/tests?status=scheduled<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'scheduled' ? 'bg-yellow-700 text-white' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' %> text-xs font-medium">
          Scheduled (<%= stats.scheduled_count || 0 %>)
        </a>
        <a href="/admin/tests?status=archived<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'archived' ? 'bg-red-700 text-white' : 'bg-red-100 text-red-800 hover:bg-red-200' %> text-xs font-medium">
          Archived (<%= stats.archived_count || 0 %>)
        </a>
        <a href="/admin/tests/trash" class="px-2 py-1 rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 text-xs font-medium">
          Trash (<%= deletedTests %>)
        </a>
      </div>

      <!-- Assignment Status Quick Filters -->
      <h3 class="text-lg font-medium text-gray-700 mb-4 mt-6">Quick Filters by Assignment</h3>
      <div class="flex flex-wrap gap-2 mb-4">
        <a href="/admin/tests<%= query?.search ? '?search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= !query?.assignment_status ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
          All Tests
        </a>
        <a href="/admin/tests?assignment_status=assigned<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.assignment_status === 'assigned' ? 'bg-green-900 text-white' : 'bg-green-800 text-white hover:bg-green-900' %> text-xs font-medium">
          Assigned Tests
        </a>
        <a href="/admin/tests?assignment_status=unassigned<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.assignment_status === 'unassigned' ? 'bg-red-900 text-white' : 'bg-red-800 text-white hover:bg-red-900' %> text-xs font-medium">
          Unassigned Tests
        </a>
      </div>

      <!-- Bulk Actions and Selected Items Counter -->
      <div class="mt-4 bg-gray-50 p-3 rounded-lg border border-gray-200 flex items-center justify-between">
        <div class="flex space-x-2 items-center">
          <button id="bulk-archive-btn" onclick="confirmBulkArchive()" class="bg-yellow-600 text-white p-2 rounded-md hover:bg-yellow-700 transition flex items-center opacity-50 cursor-not-allowed" disabled title="Archive Selected">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
            </svg>
          </button>
          <button id="bulk-delete-btn" onclick="confirmBulkDelete()" class="bg-red-600 text-white p-2 rounded-md hover:bg-red-700 transition flex items-center opacity-50 cursor-not-allowed" disabled title="Delete Selected">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
        <span class="text-sm text-gray-600">Selected: <span id="selected-count">0</span></span>
      </div>
    </div>

    <% if (tests && tests.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-3 py-3 text-center">
                <input type="checkbox" id="select-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seq</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sections</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Marks</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passing Marks</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignment</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% tests.forEach((test, index) => { %>
              <tr>
                <td class="px-3 py-4 whitespace-nowrap text-center">
                  <input type="checkbox" class="item-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" value="<%= test.exam_id %>">
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= (pagination.page - 1) * parseInt(query?.perPage || 10) + index + 1 %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-gray-900"><%= test.exam_name %></div>
                  <div class="text-xs text-gray-500 mb-2"><%= test.description ? test.description.substring(0, 50) + '...' : '' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= test.duration %> mins</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= test.section_count || 0 %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <%
                    let totalMarks = 0;
                    if (test.sections && test.sections.length > 0) {
                      test.sections.forEach(section => {
                        // Calculate total marks for each section
                        if (section.question_count) {
                          totalMarks += section.question_count;
                        }
                      });
                    }
                  %>
                  <div class="text-sm text-gray-500"><%= totalMarks %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= test.passing_marks || 0 %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= test.creator_name || 'System' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">
                    <% if (typeof formatDateTime === 'function') { %>
                      <%= formatDateTime(test.created_at) %>
                    <% } else { %>
                      <%
                        const createdDate = new Date(test.created_at);
                        const day = String(createdDate.getDate()).padStart(2, '0');
                        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                        const month = monthNames[createdDate.getMonth()];
                        const year = createdDate.getFullYear();
                        const hours = String(createdDate.getHours()).padStart(2, '0');
                        const minutes = String(createdDate.getMinutes()).padStart(2, '0');
                        const seconds = String(createdDate.getSeconds()).padStart(2, '0');
                      %>
                      <%= `${day}-${month}-${year} ${hours}:${minutes}:${seconds}` %>
                    <% } %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (test.display_status === 'archived') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Archived</span>
                  <% } else if (test.display_status === 'scheduled') { %>
                    <div class="flex flex-col">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Scheduled</span>
                      <span class="text-xs text-gray-500 mt-1">
                        <% if (typeof formatDateTime === 'function') { %>
                          <%= formatDateTime(test.publish_date) %>
                        <% } else { %>
                          <%
                            const pubDate = new Date(test.publish_date);
                            const pubDay = String(pubDate.getDate()).padStart(2, '0');
                            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                            const pubMonth = monthNames[pubDate.getMonth()];
                            const pubYear = pubDate.getFullYear();
                            const pubHours = String(pubDate.getHours()).padStart(2, '0');
                            const pubMinutes = String(pubDate.getMinutes()).padStart(2, '0');
                            const pubSeconds = String(pubDate.getSeconds()).padStart(2, '0');
                          %>
                          <%= `${pubDay}-${pubMonth}-${pubYear} ${pubHours}:${pubMinutes}:${pubSeconds}` %>
                        <% } %>
                      </span>
                    </div>
                  <% } else if (test.display_status === 'published') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Published</span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Draft</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (test.assignment_status === 'assigned') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800 text-white">Assigned</span>
                    <% if (test.assignment_count > 0) { %>
                      <span class="text-xs text-gray-500 block mt-1"><%= test.assignment_count %> assignment(s)</span>
                    <% } %>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800 text-white">Unassigned</span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <% if (test.display_status !== 'published' && test.display_status !== 'archived' && test.display_status !== 'scheduled') { %>
                      <button onclick="confirmPublish('<%= test.exam_id %>', '<%= test.exam_name %>')" class="text-green-600 hover:text-green-900" title="Publish Test">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                    <% } %>
                    <% if ((test.display_status === 'published' || test.display_status === 'scheduled') && test.display_status !== 'archived') { %>
                      <button onclick="confirmArchive('<%= test.exam_id %>', '<%= test.exam_name %>')" class="text-yellow-600 hover:text-yellow-900" title="Archive Test">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                        </svg>
                      </button>
                      <button onclick="generatePDF('<%= test.exam_id %>')" class="text-gray-600 hover:text-gray-900" title="Generate PDF">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                      </button>
                    <% } %>
                    <a href="/tests/admin/<%= test.exam_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit Test">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </a>
                    <a href="/tests/admin/<%= test.exam_id %>/preview" class="text-blue-600 hover:text-blue-900" title="Preview Test">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </a>
                    <% if (test.display_status === 'published' || test.display_status === 'scheduled') { %>
                    <button onclick="viewAssignedUsers('<%= test.exam_id %>', '<%= test.exam_name %>')" class="text-purple-600 hover:text-purple-900" title="View Assigned Users/Groups">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                      </svg>
                    </button>
                    <% } %>
                    <button onclick="confirmDelete('<%= test.exam_id %>', '<%= test.exam_name %>')" class="text-red-600 hover:text-red-900" title="Delete Test">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No tests found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new test.</p>
        <div class="mt-6">
          <a href="/admin/tests/add" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Test
          </a>
        </div>
      </div>
    <% } %>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Delete Test</h3>
      <button onclick="closeDeleteModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <p class="mb-4">Are you sure you want to delete <span id="testName" class="font-semibold"></span>? This action cannot be undone.</p>

      <div class="flex justify-end space-x-3">
        <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Cancel
        </button>
        <form id="deleteForm" method="POST" action="">
          <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">
            Delete
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Publish Modal -->
<!-- Old publish modal removed -->

<script>
  // Make sure the confirmation dialog is initialized
  document.addEventListener('DOMContentLoaded', function() {
    // Check if the confirmation dialog exists
    if (!window.confirmationDialog) {
      console.log('Confirmation dialog not found, creating a simple implementation');

      // Create a simple confirmation dialog implementation
      window.confirmationDialog = {
        show: function(options) {
          console.log('Showing confirmation dialog with options:', options);

          // Create modal element
          const modal = document.createElement('div');
          modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[9999]';

          // Set background color based on type
          let headerBgColor = 'bg-blue-600';
          let buttonBgColor = 'bg-blue-600';
          let buttonHoverColor = 'hover:bg-blue-700';
          let buttonRingColor = 'focus:ring-blue-500';

          if (options.type === 'danger') {
            headerBgColor = 'bg-red-600';
            buttonBgColor = 'bg-red-600';
            buttonHoverColor = 'hover:bg-red-700';
            buttonRingColor = 'focus:ring-red-500';
          } else if (options.type === 'warning') {
            headerBgColor = 'bg-yellow-600';
            buttonBgColor = 'bg-yellow-600';
            buttonHoverColor = 'hover:bg-yellow-700';
            buttonRingColor = 'focus:ring-yellow-500';
          }

          modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
              <div class="${headerBgColor} text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
                <h3 class="text-xl font-semibold">${options.title || 'Confirm Action'}</h3>
                <button class="text-white close-modal">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
              <p class="mb-6">${options.message || 'Are you sure you want to perform this action?'}</p>
              <div class="flex justify-end space-x-3">
                <button class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 close-modal">
                  ${options.cancelText || 'Cancel'}
                </button>
                <button class="px-4 py-2 ${buttonBgColor} text-white rounded-md ${buttonHoverColor} focus:outline-none focus:ring-2 ${buttonRingColor} confirm-action">
                  ${options.confirmText || 'Confirm'}
                </button>
              </div>
            </div>
          `;

          document.body.appendChild(modal);

          // Add event listeners
          modal.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
              modal.remove();
              if (typeof options.onCancel === 'function') {
                options.onCancel();
              }
            });
          });

          modal.querySelector('.confirm-action').addEventListener('click', () => {
            modal.remove();
            if (typeof options.onConfirm === 'function') {
              options.onConfirm();
            }
          });
        }
      };
    }
  });

  function confirmDelete(testId, testName) {
    console.log('Confirm delete called for:', testId, testName);

    // Create a custom confirmation dialog
    const options = {
      title: 'Delete Test',
      message: `Are you sure you want to delete "${testName}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger',
      onConfirm: function() {
        console.log('Delete confirmed for:', testId);
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/tests/admin/${testId}/delete`;

        // Add CSRF token if needed
        const csrfTokenElement = document.getElementById('csrf-token');
        if (csrfTokenElement) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = '_csrf';
          csrfInput.value = csrfTokenElement.value;
          form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
      }
    };

    // Use our custom dialog implementation
    if (window.confirmationDialog) {
      window.confirmationDialog.show(options);
    } else {
      // Fallback to browser confirm
      if (confirm(`Are you sure you want to delete "${testName}"? This action cannot be undone.`)) {
        options.onConfirm();
      }
    }
  }

  function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
  }

  // Close modal when clicking outside
  document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeDeleteModal();
    }
  });

  // Close modal on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeDeleteModal();
      closePublishModal();
    }
  });

  // Old publish function removed - using the new one below

  // Close publish modal when clicking outside
  document.getElementById('publishModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closePublishModal();
    }
  });
</script>

<!-- Enhanced Pagination Component -->
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <!-- Records per page -->
    <div class="flex items-center space-x-2">
        <label class="text-sm text-gray-600">Show:</label>
        <select name="perPage" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="updatePerPage(this.value)">
            <option value="10" <%= parseInt(query?.perPage) === 10 || !query?.perPage ? 'selected' : '' %>>10</option>
            <option value="25" <%= parseInt(query?.perPage) === 25 ? 'selected' : '' %>>25</option>
            <option value="50" <%= parseInt(query?.perPage) === 50 ? 'selected' : '' %>>50</option>
            <option value="100" <%= parseInt(query?.perPage) === 100 ? 'selected' : '' %>>100</option>
        </select>
        <span class="text-sm text-gray-600">entries</span>
    </div>

    <div class="flex-1 flex items-center justify-center sm:justify-end">
        <!-- Showing entries info -->
        <div class="text-sm text-gray-700 mr-4">
            <% const perPage = parseInt(query?.perPage) || 10; %>
            <% const start = (pagination.page - 1) * perPage + 1; %>
            <% const end = Math.min(start + perPage - 1, filteredTests); %>
            Showing <span class="font-medium"><%= start %></span> to <span class="font-medium"><%= end %></span> of
            <span class="font-medium"><%= filteredTests %></span> filtered entries
            (total: <span class="font-medium"><%= totalTests %></span>, active: <span class="font-medium"><%= activeTests %></span>, deleted: <span class="font-medium"><%= deletedTests %></span>)
        </div>

        <!-- Pagination Controls -->
        <div class="flex items-center space-x-2">
            <!-- First Page Button -->
            <button onclick="goToPage(1)"
                    <%= pagination.page <= 1 ? 'disabled' : '' %>
                    class="relative inline-flex items-center px-2 py-2 rounded-md border <%= pagination.page <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                <span class="sr-only">First</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="11 17 6 12 11 7"></polyline>
                    <polyline points="18 17 13 12 18 7"></polyline>
                </svg>
            </button>

            <!-- Previous Page Button -->
            <button onclick="goToPage('<%= pagination.page > 1 ? pagination.page - 1 : 1 %>')"
                    <%= pagination.page <= 1 ? 'disabled' : '' %>
                    class="relative inline-flex items-center px-2 py-2 rounded-md border <%= pagination.page <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
            </button>

            <!-- Jump to Page Dropdown -->
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Page</span>
                <select id="jumpToPage"
                        class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 w-20"
                        onchange="goToPage(this.value)">
                    <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                        <option value="<%= i %>" <%= pagination.page === i ? 'selected' : '' %>><%= i %></option>
                    <% } %>
                </select>
                <span class="text-sm text-gray-600">of <%= pagination.totalPages %></span>
            </div>

            <!-- Next Page Button -->
            <button onclick="goToPage('<%= pagination.page + 1 <= pagination.totalPages ? pagination.page + 1 : pagination.totalPages %>')"
                    <%= pagination.page >= pagination.totalPages ? 'disabled' : '' %>
                    class="relative inline-flex items-center px-2 py-2 rounded-md border <%= pagination.page >= pagination.totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
            </button>

            <!-- Last Page -->
            <button onclick="goToPage('<%= pagination.totalPages %>')"
                    <%= pagination.page >= pagination.totalPages ? 'disabled' : '' %>
                    class="relative inline-flex items-center px-2 py-2 rounded-md border <%= pagination.page >= pagination.totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' %>">
                <span class="sr-only">Last</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="13 7 18 12 13 17"></polyline>
                    <polyline points="6 7 11 12 6 17"></polyline>
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Include bulk operations script -->
<script src="/js/bulk-operations.js"></script>

<script>
  // Set up date range picker
  document.addEventListener('DOMContentLoaded', function() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');

    if (dateFromInput && dateToInput) {
      // Set min date on the "to" date when "from" date changes
      dateFromInput.addEventListener('change', function() {
        if (this.value) {
          dateToInput.min = this.value;

          // If "to" date is before "from" date, reset it
          if (dateToInput.value && dateToInput.value < this.value) {
            dateToInput.value = this.value;
          }

          // Auto-submit the form when both dates are set
          if (dateToInput.value) {
            document.getElementById('filterForm').submit();
          }
        }
      });

      // Set max date on the "from" date when "to" date changes
      dateToInput.addEventListener('change', function() {
        if (this.value) {
          dateFromInput.max = this.value;

          // If "from" date is after "to" date, reset it
          if (dateFromInput.value && dateFromInput.value > this.value) {
            dateFromInput.value = this.value;
          }

          // Auto-submit the form when both dates are set
          if (dateFromInput.value) {
            document.getElementById('filterForm').submit();
          }
        }
      });
    }
  });

  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded, setting up filter handlers');
    // Handle real-time filter changes
    const filterForm = document.getElementById('filterForm');
    const filterInputs = filterForm.querySelectorAll('input:not([type="date"]), select');

    console.log('Found non-date filter inputs:', filterInputs.length);

    filterInputs.forEach(input => {
      console.log('Setting up handler for input:', input.name, 'type:', input.type);

      if (input.type === 'text') {
        // Debounced search input
        let timeout = null;
        input.addEventListener('input', () => {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            console.log('Text input changed after debounce:', input.name, input.value);
            applyFilters();
          }, 500);
        });
      } else {
        input.addEventListener('change', () => {
          console.log('Other input changed:', input.name, input.value);
          applyFilters();
        });
      }
    });

    // Date inputs are handled separately with the applyDateFilters function
    console.log('Date inputs are handled by the Filter by Date button');
  });

  function applyFilters() {
    console.log('Applying filters');
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    // Log all form data for debugging
    for (let pair of formData.entries()) {
      console.log(pair[0] + ': ' + pair[1]);
    }

    const params = new URLSearchParams(formData);

    // Add current page size to URL if it exists
    const perPage = document.querySelector('select[name="perPage"]');
    if (perPage) {
      params.append('perPage', perPage.value);
    }

    // Reset to page 1 when filtering
    params.set('page', '1');

    const queryString = params.toString();
    console.log('Query string:', queryString);

    window.location.href = `${window.location.pathname}?${queryString}`;
  }

  function updatePerPage(value) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('perPage', value);
    currentUrl.searchParams.set('page', 1); // Reset to first page when changing items per page
    window.location.href = currentUrl.toString();
  }

  function goToPage(page) {
    const currentUrl = new URL(window.location.href);
    const maxPage = '<%= pagination?.totalPages || 1 %>';
    page = parseInt(page);
    if (page >= 1 && page <= maxPage) {
      currentUrl.searchParams.set('page', page);
      window.location.href = currentUrl.toString();
    }
  }

  // Initialize page dropdown with current page
  document.addEventListener('DOMContentLoaded', function() {
    const jumpToPage = document.getElementById('jumpToPage');
    if (jumpToPage) {
      jumpToPage.value = '<%= pagination?.page || 1 %>';
    }
  });

  function confirmPublish(testId, testName) {
    document.getElementById('publishTestName').textContent = testName;
    document.getElementById('publishForm').action = `/tests/admin/${testId}/publish`;
    document.getElementById('publishModal').classList.remove('hidden');

    // Set minimum datetime-local to current time
    const now = new Date();

    // Format the current time for min value
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const formattedNow = `${year}-${month}-${day}T${hours}:${minutes}`;

    // Create a date for tomorrow at noon
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(12, 0, 0, 0);

    // Format tomorrow's date
    const tomorrowYear = tomorrow.getFullYear();
    const tomorrowMonth = String(tomorrow.getMonth() + 1).padStart(2, '0');
    const tomorrowDay = String(tomorrow.getDate()).padStart(2, '0');
    const tomorrowHours = String(tomorrow.getHours()).padStart(2, '0');
    const tomorrowMinutes = String(tomorrow.getMinutes()).padStart(2, '0');
    const formattedTomorrow = `${tomorrowYear}-${tomorrowMonth}-${tomorrowDay}T${tomorrowHours}:${tomorrowMinutes}`;

    // Set minimum datetime-local to current time
    document.getElementById('publish_date').min = formattedNow;

    // Set default value to tomorrow at noon
    document.getElementById('publish_date').value = formattedTomorrow;

    // Set up radio button event listeners
    document.getElementById('publish_now').checked = true;
    document.getElementById('datetime_container').classList.add('hidden');

    document.getElementById('publish_now').addEventListener('change', function() {
      if (this.checked) {
        document.getElementById('datetime_container').classList.add('hidden');
      }
    });

    document.getElementById('publish_later').addEventListener('change', function() {
      if (this.checked) {
        document.getElementById('datetime_container').classList.remove('hidden');
      }
    });
  }

  function closePublishModal() {
    document.getElementById('publishModal').classList.add('hidden');
  }

  function validatePublishForm() {
    const publishNow = document.getElementById('publish_now').checked;
    const publishLater = document.getElementById('publish_later').checked;

    if (publishNow) {
      // If publishing now, set the datetime to current time
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');

      const formattedNow = `${year}-${month}-${day}T${hours}:${minutes}`;
      document.getElementById('publish_date').value = formattedNow;
    } else if (publishLater) {
      // If publishing later, make sure a date is selected
      const publishDate = document.getElementById('publish_date').value;
      if (!publishDate) {
        alert('Please select a publish date and time');
        return false;
      }
    }

    return true;
  }

  // Close publish modal when clicking outside
  document.getElementById('publishModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closePublishModal();
    }
  });

  function confirmArchive(testId, testName) {
    console.log('Confirm archive called for:', testId, testName);

    // Create a custom confirmation dialog
    const options = {
      title: 'Archive Test',
      message: `Are you sure you want to archive "${testName}"?`,
      confirmText: 'Archive',
      cancelText: 'Cancel',
      type: 'warning',
      onConfirm: function() {
        console.log('Archive confirmed for:', testId);
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/tests/admin/${testId}/archive`;

        // Add CSRF token if needed
        const csrfTokenElement = document.getElementById('csrf-token');
        if (csrfTokenElement) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = '_csrf';
          csrfInput.value = csrfTokenElement.value;
          form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
      }
    };

    // Use our custom dialog implementation
    if (window.confirmationDialog) {
      window.confirmationDialog.show(options);
    } else {
      // Fallback to browser confirm
      if (confirm(`Are you sure you want to archive "${testName}"?`)) {
        options.onConfirm();
      }
    }
  }

  function closeArchiveModal() {
    document.getElementById('archiveModal').classList.add('hidden');
  }

  // Close archive modal when clicking outside
  document.getElementById('archiveModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeArchiveModal();
    }
  });

  function generatePDF(examId) {
    // Show loading indicator
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingOverlay.id = 'loading-overlay';
    loadingOverlay.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
        <h2 class="text-xl font-bold mb-2">Generating PDF</h2>
        <p class="text-gray-700">This may take a few moments...</p>
      </div>
    `;
    document.body.appendChild(loadingOverlay);

    // Call the API to generate the PDF
    fetch(`/tests/generate-pdf/${examId}`)
      .then(response => response.json())
      .then(data => {
        // Remove loading indicator
        document.body.removeChild(loadingOverlay);

        if (data.success && data.pdfUrl) {
          // Open the PDF in a new tab
          window.open(data.pdfUrl, '_blank');
        } else {
          // Use confirmation dialog instead of alert
          if (window.confirmationDialog) {
            window.confirmationDialog.show({
              title: 'PDF Generation Error',
              message: data.message || 'An error occurred while generating the PDF',
              confirmText: 'OK',
              cancelText: null,
              type: 'error'
            });
          } else {
            // Fallback to alert if confirmation dialog is not available
            alert(data.message || 'An error occurred while generating the PDF');
          }
        }
      })
      .catch(error => {
        // Remove loading indicator
        if (document.getElementById('loading-overlay')) {
          document.body.removeChild(loadingOverlay);
        }

        console.error('Error generating PDF:', error);
        // Use confirmation dialog instead of alert
        if (window.confirmationDialog) {
          window.confirmationDialog.show({
            title: 'PDF Generation Error',
            message: 'An error occurred while generating the PDF. Please try again.',
            confirmText: 'OK',
            cancelText: null,
            type: 'error'
          });
        } else {
          // Fallback to console.error if confirmation dialog is not available
          console.error('An error occurred while generating the PDF. Please try again.');
        }
      });
  }
</script>

<!-- Publish Test Modal -->
<div id="publishModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Publish Test</h3>
    <p class="mb-4">Are you sure you want to publish <span id="publishTestName" class="font-semibold"></span>?</p>
    <p class="mb-4 text-sm text-gray-600">Select a date and time when the test should be available to students. If you select a future date, the test will be marked as "Scheduled" until that time.</p>

    <form id="publishForm" action="" method="POST" enctype="application/x-www-form-urlencoded" class="space-y-4" onsubmit="return validatePublishForm();">
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <input type="radio" id="publish_now" name="publish_option" value="now" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
            <label for="publish_now" class="ml-2 block text-sm font-medium text-gray-700">Publish Now</label>
          </div>
          <div class="flex items-center">
            <input type="radio" id="publish_later" name="publish_option" value="later" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
            <label for="publish_later" class="ml-2 block text-sm font-medium text-gray-700">Schedule for Later</label>
          </div>
        </div>

        <div id="datetime_container" class="hidden">
          <label for="publish_date" class="block text-sm font-medium text-gray-700">Publish Date & Time</label>
          <input type="datetime-local" id="publish_date" name="publish_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closePublishModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
          Publish
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Bulk Archive Modal -->
<div id="bulkArchiveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Archive Tests</h3>
    <p class="mb-4">Are you sure you want to archive <span id="bulkArchiveCount" class="font-semibold">0</span> selected tests?</p>
    <p class="text-sm text-gray-600 mb-4">Archiving tests will make them unavailable to users but preserve all data.</p>

    <form id="bulkArchiveForm" action="/tests/admin/bulk-archive" method="POST" enctype="application/x-www-form-urlencoded" class="space-y-4">
      <input type="hidden" id="bulkArchiveItems" name="examIds" value="">

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeBulkArchiveModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 flex items-center">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
          </svg>
          Archive
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Bulk Delete Modal -->
<div id="bulkDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <div class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
      <h3 class="text-xl font-semibold">Delete Tests</h3>
      <button onclick="closeBulkDeleteModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <p class="mb-4">Are you sure you want to delete <span id="bulkDeleteCount" class="font-semibold">0</span> selected tests? This action cannot be undone.</p>

    <form id="bulkDeleteForm" action="/tests/admin/bulk-delete" method="POST" enctype="application/x-www-form-urlencoded" class="space-y-4">
      <input type="hidden" id="bulkDeleteItems" name="examIds" value="">

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeBulkDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 flex items-center">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Delete
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Archive Test Modal -->
<div id="archiveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Archive Test</h3>
    <p class="mb-4">Are you sure you want to archive <span id="archiveTestName" class="font-semibold"></span>?</p>
    <p class="text-sm text-gray-600 mb-4">Archiving a test will make it unavailable to users but preserve all data.</p>

    <form id="archiveForm" action="" method="POST" enctype="application/x-www-form-urlencoded" class="space-y-4">
      <input type="hidden" name="_method" value="PUT">
      <input type="hidden" name="status" value="archived">

      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeArchiveModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
          Cancel
        </button>
        <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 flex items-center">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
          </svg>
          Archive
        </button>
      </div>
    </form>
  </div>
</div>

<!-- View Assigned Users/Groups Modal -->
<div id="assignedUsersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
    <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Assigned Users & Groups: <span id="assignedTestName" class="font-semibold"></span></h3>
      <button onclick="closeAssignedUsersModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6 max-h-[70vh] overflow-y-auto">
      <div class="space-y-6">
        <!-- Tabs -->
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button id="usersTab" class="border-purple-500 text-purple-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="switchTab('users')">
              Users <span id="userCount" class="ml-2 py-0.5 px-2 rounded-full text-xs bg-purple-100">0</span>
            </button>
            <button id="groupsTab" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="switchTab('groups')">
              Groups <span id="groupCount" class="ml-2 py-0.5 px-2 rounded-full text-xs bg-gray-100">0</span>
            </button>
          </nav>
        </div>

        <!-- Users Tab Content -->
        <div id="usersTabContent" class="space-y-4">
          <div id="noUsersMessage" class="text-center py-8 hidden">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No users assigned</h3>
            <p class="mt-1 text-sm text-gray-500">This test has not been assigned to any users directly.</p>
          </div>
          <div id="usersList" class="space-y-4">
            <!-- User items will be inserted here dynamically -->
          </div>
        </div>

        <!-- Groups Tab Content -->
        <div id="groupsTabContent" class="space-y-4 hidden">
          <div id="noGroupsMessage" class="text-center py-8 hidden">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No groups assigned</h3>
            <p class="mt-1 text-sm text-gray-500">This test has not been assigned to any groups.</p>
          </div>
          <div id="groupsList" class="space-y-4">
            <!-- Group items will be inserted here dynamically -->
          </div>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 px-6 py-4 rounded-b-lg">
      <div class="flex justify-end">
        <button type="button" onclick="closeAssignedUsersModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Helper function to safely format dates
  function safeFormatDate(dateString) {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  // View assigned users/groups
  function viewAssignedUsers(examId, examName) {
    document.getElementById('assignedTestName').textContent = examName;
    document.getElementById('assignedUsersModal').classList.remove('hidden');

    // Reset tabs
    switchTab('users');

    // Clear previous data
    document.getElementById('usersList').innerHTML = '';
    document.getElementById('groupsList').innerHTML = '';

    // Fetch assigned users and groups
    fetch(`/admin/test-assignments/exam/${examId}/assigned`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Update user count
          document.getElementById('userCount').textContent = data.userAssignments.length;

          // Update group count
          document.getElementById('groupCount').textContent = data.groupAssignments.length;

          // Show/hide no users message
          if (data.userAssignments.length === 0) {
            document.getElementById('noUsersMessage').classList.remove('hidden');
          } else {
            document.getElementById('noUsersMessage').classList.add('hidden');

            // Populate users list
            const usersList = document.getElementById('usersList');
            data.userAssignments.forEach(assignment => {
              const userItem = document.createElement('div');
              userItem.className = 'bg-white shadow rounded-lg p-4';
              userItem.innerHTML = `
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="text-lg font-medium text-gray-900">${assignment.username}</h4>
                    <p class="text-sm text-gray-500">${assignment.email}</p>
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                      <span class="mr-2">Max Attempts:</span>
                      <span class="font-medium">${assignment.max_attempts || 1}</span>
                    </div>
                  </div>
                  <div>
                    <span class="${assignment.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-2 py-1 rounded-full text-xs font-medium">
                      ${assignment.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                ${assignment.end_datetime ? `
                <div class="mt-2 text-sm text-gray-500">
                  <span class="font-medium">End Date:</span> ${safeFormatDate(assignment.end_datetime)}
                </div>
                ` : ''}
                <div class="mt-2 text-xs text-gray-400">
                  Assigned by ${assignment.assigned_by_username || 'Unknown'} ${assignment.assigned_at ? `on ${safeFormatDate(assignment.assigned_at)}` : ''}
                </div>
              `;
              usersList.appendChild(userItem);
            });
          }

          // Show/hide no groups message
          if (data.groupAssignments.length === 0) {
            document.getElementById('noGroupsMessage').classList.remove('hidden');
          } else {
            document.getElementById('noGroupsMessage').classList.add('hidden');

            // Populate groups list
            const groupsList = document.getElementById('groupsList');
            data.groupAssignments.forEach(assignment => {
              const groupItem = document.createElement('div');
              groupItem.className = 'bg-white shadow rounded-lg p-4';
              groupItem.innerHTML = `
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="text-lg font-medium text-gray-900">${assignment.group_name}</h4>
                    <p class="text-sm text-gray-500">${assignment.group_description || 'No description'}</p>
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                      <span class="mr-2">Members:</span>
                      <span class="font-medium">${assignment.member_count}</span>
                    </div>
                    <div class="mt-1 flex items-center text-sm text-gray-500">
                      <span class="mr-2">Max Attempts:</span>
                      <span class="font-medium">${assignment.max_attempts || 1}</span>
                    </div>
                  </div>
                  <div>
                    <span class="${assignment.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-2 py-1 rounded-full text-xs font-medium">
                      ${assignment.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                ${assignment.end_datetime ? `
                <div class="mt-2 text-sm text-gray-500">
                  <span class="font-medium">End Date:</span> ${safeFormatDate(assignment.end_datetime)}
                </div>
                ` : ''}
                <div class="mt-2 text-xs text-gray-400">
                  Assigned by ${assignment.assigned_by_username || 'Unknown'} ${assignment.assigned_at ? `on ${safeFormatDate(assignment.assigned_at)}` : ''}
                </div>
              `;
              groupsList.appendChild(groupItem);
            });
          }
        } else {
          // Show message in both tabs
          document.getElementById('userCount').textContent = '0';
          document.getElementById('groupCount').textContent = '0';
          document.getElementById('noUsersMessage').classList.remove('hidden');
          document.getElementById('noGroupsMessage').classList.remove('hidden');

          // Update the message text
          const noUsersMessage = document.getElementById('noUsersMessage');
          const noGroupsMessage = document.getElementById('noGroupsMessage');

          if (noUsersMessage.querySelector('h3')) {
            noUsersMessage.querySelector('h3').textContent = 'No users assigned';
          }
          if (noUsersMessage.querySelector('p')) {
            noUsersMessage.querySelector('p').textContent = 'This test has not been assigned to any users yet.';
          }

          if (noGroupsMessage.querySelector('h3')) {
            noGroupsMessage.querySelector('h3').textContent = 'No groups assigned';
          }
          if (noGroupsMessage.querySelector('p')) {
            noGroupsMessage.querySelector('p').textContent = 'This test has not been assigned to any groups yet.';
          }
        }
      })
      .catch(error => {
        console.error('Error fetching assigned users and groups:', error);
        // Show message in both tabs
        document.getElementById('userCount').textContent = '0';
        document.getElementById('groupCount').textContent = '0';
        document.getElementById('noUsersMessage').classList.remove('hidden');
        document.getElementById('noGroupsMessage').classList.remove('hidden');

        // Update the message text
        const noUsersMessage = document.getElementById('noUsersMessage');
        const noGroupsMessage = document.getElementById('noGroupsMessage');

        if (noUsersMessage.querySelector('h3')) {
          noUsersMessage.querySelector('h3').textContent = 'No users assigned';
        }
        if (noUsersMessage.querySelector('p')) {
          noUsersMessage.querySelector('p').textContent = 'This test has not been assigned to any users yet.';
        }

        if (noGroupsMessage.querySelector('h3')) {
          noGroupsMessage.querySelector('h3').textContent = 'No groups assigned';
        }
        if (noGroupsMessage.querySelector('p')) {
          noGroupsMessage.querySelector('p').textContent = 'This test has not been assigned to any groups yet.';
        }
      });
  }

  function closeAssignedUsersModal() {
    document.getElementById('assignedUsersModal').classList.add('hidden');
  }

  function switchTab(tab) {
    if (tab === 'users') {
      document.getElementById('usersTab').classList.add('border-purple-500', 'text-purple-600');
      document.getElementById('usersTab').classList.remove('border-transparent', 'text-gray-500');
      document.getElementById('groupsTab').classList.add('border-transparent', 'text-gray-500');
      document.getElementById('groupsTab').classList.remove('border-purple-500', 'text-purple-600');

      document.getElementById('usersTabContent').classList.remove('hidden');
      document.getElementById('groupsTabContent').classList.add('hidden');
    } else {
      document.getElementById('groupsTab').classList.add('border-purple-500', 'text-purple-600');
      document.getElementById('groupsTab').classList.remove('border-transparent', 'text-gray-500');
      document.getElementById('usersTab').classList.add('border-transparent', 'text-gray-500');
      document.getElementById('usersTab').classList.remove('border-purple-500', 'text-purple-600');

      document.getElementById('groupsTabContent').classList.remove('hidden');
      document.getElementById('usersTabContent').classList.add('hidden');
    }
  }

  // Close assigned users modal when clicking outside
  document.addEventListener('DOMContentLoaded', function() {
    const assignedUsersModal = document.getElementById('assignedUsersModal');
    if (assignedUsersModal) {
      assignedUsersModal.addEventListener('click', function(e) {
        if (e.target === this) {
          closeAssignedUsersModal();
        }
      });
    }
  });
</script>
