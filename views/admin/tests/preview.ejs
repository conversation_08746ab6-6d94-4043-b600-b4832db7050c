<!-- Test Preview Page -->
<link rel="stylesheet" href="/css/image-styles.css">
<style>
  /* Custom scrollbar for instructions container */
  .instructions-container::-webkit-scrollbar {
    width: 6px;
  }
  .instructions-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
  }
  .instructions-container::-webkit-scrollbar-thumb {
    background: #c7d2fe;
    border-radius: 6px;
  }
  .instructions-container::-webkit-scrollbar-thumb:hover {
    background: #818cf8;
  }

  /* Question and solution images - using global styles from image-styles.css */

  /* Modal styles */
  .instructions-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
  }

  .instructions-modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .instructions-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .instructions-modal-body {
    padding: 1rem;
    overflow-y: auto;
    max-height: calc(90vh - 130px);
  }

  .instructions-modal-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
  }
</style>

<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <!-- Page Header with improved styling -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg shadow-sm mb-6">
      <div class="flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-800">Test Preview: <%= test.exam_name %></h2>
        <a href="/admin/tests" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Tests
        </a>
      </div>
    </div>

    <!-- Test Details Card -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6 border-l-4 border-blue-500 hover:shadow-lg transition-shadow duration-300">
      <div class="flex justify-between items-start mb-4">
        <h3 class="text-lg font-semibold text-gray-800">Test Details</h3>
        <button
          id="view-instructions-btn"
          class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 flex items-center"
          onclick="openInstructionsModal()"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          View Instructions
        </button>
      </div>
      <div class="space-y-2">
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Name:</span>
          <span class="text-gray-800"><%= test.exam_name %></span>
        </div>
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Duration:</span>
          <span class="text-gray-800"><%= test.duration %> minutes</span>
        </div>
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Passing Marks:</span>
          <span class="text-gray-800"><%= test.passing_marks || 'Not set' %></span>
        </div>
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Status:</span>
          <span class="text-gray-800">
            <% if (test.status === 'published' || (test.publish_date && new Date(test.publish_date) <= new Date())) { %>
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Published</span>
            <% } else if (test.publish_date) { %>
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Scheduled</span>
            <% } else { %>
              <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Draft</span>
            <% } %>
          </span>
        </div>
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Created By:</span>
          <span class="text-gray-800"><%= test.creator_name || 'System' %></span>
        </div>
        <div class="flex items-start">
          <span class="text-gray-600 font-medium w-32">Created On:</span>
          <span class="text-gray-800">
            <%
              const createdDate = new Date(test.created_at);
              const day = String(createdDate.getDate()).padStart(2, '0');
              const month = createdDate.toLocaleString('en-US', { month: 'short' });
              const year = createdDate.getFullYear();
              const hours = String(createdDate.getHours()).padStart(2, '0');
              const minutes = String(createdDate.getMinutes()).padStart(2, '0');
              const seconds = String(createdDate.getSeconds()).padStart(2, '0');
              const formattedDate = `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
            %>
            <%= formattedDate %>
          </span>
        </div>
      </div>
    </div>

    <!-- Tabs for Sections -->
    <div class="mb-6">
      <div class="border-b border-gray-200 bg-gray-50 rounded-t-lg p-1">
        <nav class="-mb-px flex space-x-4 overflow-x-auto" aria-label="Tabs">
          <% sections.forEach((section, index) => { %>
            <button
              class="section-tab whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm rounded-t-lg transition-all duration-200 <%= index === 0 ? 'border-purple-500 text-purple-700 bg-white shadow-sm' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-100' %>"
              data-section-id="<%= section.section_id %>"
              onclick="showSection('<%= section.section_id %>')"
            >
              Section <%= index + 1 %>: <%= section.section_name %>
            </button>
          <% }); %>
        </nav>
      </div>
    </div>

    <!-- Section Content -->
    <div class="space-y-6">
      <% sections.forEach((section, sectionIndex) => { %>
        <div id="section-<%= section.section_id %>" class="section-content <%= sectionIndex === 0 ? '' : 'hidden' %>">
          <div class="bg-white rounded-lg shadow p-6 border-l-4 border-purple-500">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Section: <%= section.section_name %></h3>

            <!-- Section Statistics -->
            <div class="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
              <%
                // Calculate question type counts and total marks
                let mcqCount = 0;
                let tfCount = 0;
                let essayCount = 0;
                let fillUpCount = 0;
                let totalMarks = 0;

                section.questions.forEach(q => {
                  if (q.question_type === 'multiple_choice') mcqCount++;
                  else if (q.question_type === 'true_false') tfCount++;
                  else if (q.question_type === 'essay') essayCount++;
                  else if (q.question_type === 'fill_up') fillUpCount++;

                  // Add marks - ensure proper conversion to number
                  totalMarks += (parseFloat(q.marks) || 1);
                });
              %>

              <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div>
                  <h4 class="text-sm font-medium text-gray-700">Total Questions:</h4>
                  <p class="text-sm"><%= section.questions.length %></p>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-700">Total Marks:</h4>
                  <p class="text-sm"><%= totalMarks %></p>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-700">Passing Marks:</h4>
                  <p class="text-sm"><%= section.passing_marks || 'Not set' %></p>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-700">Question Types:</h4>
                  <div class="flex flex-wrap gap-1 mt-1">
                    <% if (mcqCount > 0) { %>
                      <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">MCQ: <%= mcqCount %></span>
                    <% } %>
                    <% if (tfCount > 0) { %>
                      <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">T/F: <%= tfCount %></span>
                    <% } %>
                    <% if (essayCount > 0) { %>
                      <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay: <%= essayCount %></span>
                    <% } %>
                    <% if (fillUpCount > 0) { %>
                      <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill-up: <%= fillUpCount %></span>
                    <% } %>
                  </div>
                </div>
              </div>

              <% if (section.instructions) { %>
                <div class="mt-3 pt-3 border-t border-gray-200">
                  <h4 class="text-sm font-medium text-gray-700">Section Instructions:</h4>
                  <p class="text-sm text-gray-600 mt-1"><%= section.instructions %></p>
                </div>
              <% } %>
            </div>

            <% if (section.questions && section.questions.length > 0) { %>
              <div class="space-y-6">
                <%
                // Group questions by essay_id
                const essayGroups = {};
                const nonEssayQuestions = [];

                // Calculate the starting question number for this section
                let globalQuestionCounter = 1;
                for (let i = 0; i < sectionIndex; i++) {
                  if (sections[i].questions) {
                    globalQuestionCounter += sections[i].questions.length;
                  }
                }

                section.questions.forEach(question => {
                  if (question.essay_id) {
                    if (!essayGroups[question.essay_id]) {
                      essayGroups[question.essay_id] = {
                        essay: {
                          id: question.essay_id,
                          title: question.essay_title,
                          content: question.essay_content
                        },
                        questions: []
                      };
                    }
                    essayGroups[question.essay_id].questions.push(question);
                  } else {
                    nonEssayQuestions.push(question);
                  }
                });

                // Display essay groups first
                Object.values(essayGroups).forEach(group => {
                %>
                  <div class="essay-group border-l-4 border-yellow-400 pl-4 pb-2">
                    <!-- Essay Content -->
                    <div class="mb-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg shadow-sm">
                      <div class="flex justify-between items-center mb-2">
                        <h4 class="text-lg font-semibold text-gray-800"><%= group.essay.title %></h4>
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
                      </div>
                      <div class="prose max-w-none mb-4 text-gray-700 overflow-y-auto max-h-64">
                        <%- group.essay.content.replace(/\n/g, '<br>') %>
                      </div>
                    </div>

                    <% group.questions.forEach((question, questionIndex) => { %>
                      <div class="p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4 hover:shadow-md transition-shadow duration-300 hover:border-yellow-200">
                        <div class="flex justify-between mb-2">
                          <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-500 mr-2">Question <%= globalQuestionCounter + questionIndex %> (Essay-based)</span>
                            <!-- Question Marks -->
                            <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                              <%= question.marks || 1 %> mark<%= (question.marks && question.marks > 1) ? 's' : '' %>
                            </span>

                            <% if (question.negative_marks && question.negative_marks > 0) { %>
                              <span class="ml-1 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800">
                                -<%= question.negative_marks %> mark<%= question.negative_marks > 1 ? 's' : '' %>
                              </span>
                            <% } %>
                          </div>
                          <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay Question</span>

                            <% if (question.category_names) { %>
                              <%
                                const categoryColors = {
                                  'Calculus': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                  'Algebra': { bg: 'bg-green-100', text: 'text-green-800' },
                                  'Geometry': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                  'Physics': { bg: 'bg-red-100', text: 'text-red-800' },
                                  'Chemistry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                  'Biology': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                  'Computer Science': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                  'English': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                  'History': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                  'Geography': { bg: 'bg-cyan-100', text: 'text-cyan-800' },
                                  'Economics': { bg: 'bg-lime-100', text: 'text-lime-800' },
                                  'Electrostatics': { bg: 'bg-amber-100', text: 'text-amber-800' }
                                };

                                if (question.category_names) {
                                  const categoryNamesArray = question.category_names.split(',');
                                  categoryNamesArray.forEach(categoryName => {
                                    const category = categoryName.trim();
                                    const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                              %>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                      <%= category %>
                                    </span>
                              <% });
                                }
                              %>
                            <% } %>
                          </div>
                        </div>

                        <div class="mb-4">
                          <p class="text-gray-800"><%- question.question_text.replace(/\n/g, '<br>') %></p>
                          <% if (question.image_path) { %>
                            <div class="mt-2">
                              <img src="<%= question.image_path %>" alt="Question Image" class="question-image">
                            </div>
                          <% } %>
                          <% if (question.metadata && question.metadata.question_image_url) { %>
                            <div class="mt-2">
                              <img src="<%= question.metadata.question_image_url %>" alt="Question Image" class="question-image">
                            </div>
                          <% } %>
                        </div>

                        <% if (question.solution_text || question.solution_image_path) { %>
                          <div class="mt-4 pt-3 border-t border-gray-200">
                            <h5 class="text-sm font-medium text-gray-600 mb-1">Solution:</h5>
                            <% if (question.solution_text) { %>
                              <p class="text-gray-700 text-sm"><%- question.solution_text.replace(/\n/g, '<br>') %></p>
                            <% } %>
                            <% if (question.solution_image_path) { %>
                              <div class="mt-2">
                                <img src="<%= question.solution_image_path %>" alt="Solution Image" class="solution-image">
                              </div>
                            <% } %>
                            <% if (question.metadata && question.metadata.solution_image_url) { %>
                              <div class="mt-2">
                                <img src="<%= question.metadata.solution_image_url %>" alt="Solution Image" class="solution-image">
                              </div>
                            <% } %>
                          </div>
                        <% } %>
                      </div>
                    <% }); %>
                  </div>
                <%
                  // Update global question counter after essay questions
                  Object.values(essayGroups).forEach(group => {
                    globalQuestionCounter += group.questions.length;
                  });
                }); %>

                <% // Display non-essay questions
                nonEssayQuestions.forEach((question, questionIndex) => { %>
                  <div class="p-4 bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-300 hover:border-blue-200">
                    <div class="flex justify-between mb-2">
                      <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-500 mr-2">Question <%= globalQuestionCounter + questionIndex %></span>
                        <!-- Question Marks -->
                        <span class="px-2 py-0.5 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                          <%= question.marks || 1 %> mark<%= (question.marks && question.marks > 1) ? 's' : '' %>
                        </span>

                        <% if (question.negative_marks && question.negative_marks > 0) { %>
                          <span class="ml-1 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800">
                            -<%= question.negative_marks %> mark<%= question.negative_marks > 1 ? 's' : '' %>
                          </span>
                        <% } %>
                      </div>
                      <div class="flex flex-wrap gap-1">
                        <% if (question.question_type === 'multiple_choice') { %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Multiple Choice</span>
                        <% } else if (question.question_type === 'true_false') { %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">True/False</span>
                        <% } else if (question.question_type === 'fill_up') { %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill in the Blank</span>
                        <% } else if (question.question_type === 'essay') { %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
                        <% } else { %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"><%= question.question_type %></span>
                        <% } %>

                        <% if (question.category_names) { %>
                          <%
                          const categoryColors = {
                              'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                              'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                              'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                              'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                              'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                              'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                              'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                              'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                              'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
                              'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
                          };

                          const categoryNamesArray = question.category_names.split(',');
                          categoryNamesArray.forEach(categoryName => {
                              const category = categoryName.trim();
                              const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                          %>
                              <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                  <%= category %>
                              </span>
                          <% }); %>
                        <% } %>
                      </div>
                    </div>

                    <% if (question.essay_id) { %>
                    <!-- Essay Content -->
                    <div class="mb-6 p-4 bg-gray-100 border border-gray-200 rounded-lg">
                      <div class="flex justify-between items-center mb-2">
                        <h4 class="text-lg font-semibold text-gray-800"><%= question.essay_title %></h4>
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay Reference</span>
                      </div>
                      <div class="prose max-w-none mb-4 text-gray-700 overflow-y-auto max-h-64">
                        <%- question.essay_content.replace(/\n/g, '<br>') %>
                      </div>
                    </div>
                    <% } %>

                    <div class="mb-4">
                      <p class="text-gray-800"><%- question.question_text.replace(/\n/g, '<br>') %></p>
                      <% if (question.image_path) { %>
                        <div class="mt-2">
                          <img src="<%= question.image_path %>" alt="Question Image" class="question-image">
                        </div>
                      <% } %>
                      <% if (question.metadata && question.metadata.question_image_url) { %>
                        <div class="mt-2">
                          <img src="<%= question.metadata.question_image_url %>" alt="Question Image" class="question-image">
                        </div>
                      <% } %>
                    </div>

                    <% if (question.question_type === 'multiple_choice' && question.options) { %>
                      <div class="space-y-2 ml-4">
                        <% question.options.forEach((option, optionIndex) => { %>
                          <div class="flex items-center">
                            <div class="flex items-center h-5">
                              <input
                                type="radio"
                                disabled
                                <%= option.is_correct ? 'checked' : '' %>
                                class="focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300"
                              >
                            </div>
                            <div class="ml-3 text-sm">
                              <label class="font-medium text-gray-700 <%= option.is_correct ? 'text-green-600 font-semibold' : '' %>">
                                <%= option.option_text %>
                                <% if (option.is_correct) { %>
                                  <span class="ml-2 text-xs text-green-600">(Correct)</span>
                                <% } %>
                              </label>
                            </div>
                          </div>
                        <% }); %>
                      </div>
                    <% } else if (question.question_type === 'true_false') { %>
                      <div class="space-y-2 ml-4">
                        <div class="flex items-center">
                          <div class="flex items-center h-5">
                            <input
                              type="radio"
                              disabled
                              <%= question.correct_answer === 'true' ? 'checked' : '' %>
                              class="focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300"
                            >
                          </div>
                          <div class="ml-3 text-sm">
                            <label class="font-medium text-gray-700 <%= question.correct_answer === 'true' ? 'text-green-600 font-semibold' : '' %>">
                              True
                              <% if (question.correct_answer === 'true') { %>
                                <span class="ml-2 text-xs text-green-600">(Correct)</span>
                              <% } %>
                            </label>
                          </div>
                        </div>
                        <div class="flex items-center">
                          <div class="flex items-center h-5">
                            <input
                              type="radio"
                              disabled
                              <%= question.correct_answer === 'false' ? 'checked' : '' %>
                              class="focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300"
                            >
                          </div>
                          <div class="ml-3 text-sm">
                            <label class="font-medium text-gray-700 <%= question.correct_answer === 'false' ? 'text-green-600 font-semibold' : '' %>">
                              False
                              <% if (question.correct_answer === 'false') { %>
                                <span class="ml-2 text-xs text-green-600">(Correct)</span>
                              <% } %>
                            </label>
                          </div>
                        </div>
                      </div>
                    <% } else if (question.question_type === 'fill_up') { %>
                      <div class="ml-4">
                        <div class="flex items-center">
                          <span class="text-sm font-medium text-gray-600 mr-2">Correct Answer:</span>
                          <span class="text-green-600 font-semibold"><%= question.correct_answer %></span>
                        </div>
                      </div>
                    <% } %>

                    <% if (question.solution_text || question.solution_image_path) { %>
                      <div class="mt-4 pt-3 border-t border-gray-200">
                        <h5 class="text-sm font-medium text-gray-600 mb-1">Solution:</h5>
                        <% if (question.solution_text) { %>
                          <p class="text-gray-700 text-sm"><%- question.solution_text.replace(/\n/g, '<br>') %></p>
                        <% } %>
                        <% if (question.solution_image_path) { %>
                          <div class="mt-2">
                            <img src="<%= question.solution_image_path %>" alt="Solution Image" class="solution-image rounded-lg border border-gray-200">
                          </div>
                        <% } %>
                        <% if (question.metadata && question.metadata.solution_image_url) { %>
                          <div class="mt-2">
                            <img src="<%= question.metadata.solution_image_url %>" alt="Solution Image" class="solution-image rounded-lg border border-gray-200">
                          </div>
                        <% } %>
                      </div>
                    <% } %>
                  </div>
                <% }); %>
              </div>
            <% } else { %>
              <div class="text-center py-8 text-gray-500">
                <p>No questions in this section</p>
              </div>
            <% } %>
          </div>
        </div>
      <% }); %>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex justify-end space-x-4">
      <a href="/tests/admin" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
        Back to Tests
      </a>
      <a href="/tests/admin/<%= test.exam_id %>/edit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
        Edit Test
      </a>
    </div>
  </div>
</div>

<!-- Instructions Modal -->
<div id="instructions-modal" class="instructions-modal">
  <div class="instructions-modal-content">
    <div class="instructions-modal-header">
      <h3 class="text-lg font-semibold text-gray-800">Test Instructions</h3>
      <button onclick="closeInstructionsModal()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div class="instructions-modal-body instructions-container">
      <% if (test.instructions) { %>
        <p class="text-gray-700 whitespace-pre-line"><%= test.instructions %></p>
      <% } else { %>
        <p class="text-gray-500 italic">No instructions provided</p>
      <% } %>
    </div>
    <div class="instructions-modal-footer">
      <button onclick="closeInstructionsModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
        Close
      </button>
    </div>
  </div>
</div>

<script>
  function showSection(sectionId) {
    // Hide all section contents
    document.querySelectorAll('.section-content').forEach(content => {
      content.classList.add('hidden');
    });

    // Show the selected section content
    document.getElementById(`section-${sectionId}`).classList.remove('hidden');

    // Update tab styling
    document.querySelectorAll('.section-tab').forEach(tab => {
      tab.classList.remove('border-purple-500', 'text-purple-600');
      tab.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    });

    // Highlight the active tab
    document.querySelector(`.section-tab[data-section-id="${sectionId}"]`).classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    document.querySelector(`.section-tab[data-section-id="${sectionId}"]`).classList.add('border-purple-500', 'text-purple-600');
  }

  // Functions to handle the instructions modal
  function openInstructionsModal() {
    const modal = document.getElementById('instructions-modal');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal
  }

  function closeInstructionsModal() {
    const modal = document.getElementById('instructions-modal');
    modal.style.display = 'none';
    document.body.style.overflow = ''; // Restore scrolling
  }

  // Close modal when clicking outside of it
  window.addEventListener('click', function(event) {
    const modal = document.getElementById('instructions-modal');
    if (event.target === modal) {
      closeInstructionsModal();
    }
  });

  // Close modal with Escape key
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      closeInstructionsModal();
    }
  });
</script>
