<!-- Include scripts -->
<script src="/js/toast-notifications.js"></script>
<script src="/js/admin/question-image-upload.js"></script>
<script src="/js/admin/add-question-modal.js"></script>
<script src="/js/admin/section-questions.js"></script>

<!-- Section Questions Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold text-gray-800">Questions for <%= section.section_name %></h2>
        <p class="text-sm text-gray-600">Test: <%= section.exam_name %></p>

        <!-- Section Statistics -->
        <div class="mt-2 flex flex-wrap gap-2">
          <%
            // Calculate question type counts
            let mcqCount = 0;
            let tfCount = 0;
            let essayCount = 0;
            let fillUpCount = 0;
            let totalMarks = 0;

            questions.forEach(q => {
              if (q.question_type === 'multiple_choice') mcqCount++;
              else if (q.question_type === 'true_false') tfCount++;
              else if (q.question_type === 'essay') essayCount++;
              else if (q.question_type === 'fill_up') fillUpCount++;

              // Add marks
              totalMarks += (q.marks || 1);
            });
          %>

          <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
            MCQ: <%= mcqCount %>
          </span>

          <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
            True/False: <%= tfCount %>
          </span>

          <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
            Essay: <%= essayCount %>
          </span>

          <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
            Fill-up: <%= fillUpCount %>
          </span>

          <span class="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
            Total Marks: <%= totalMarks %>
          </span>

          <% if (section.passing_marks) { %>
            <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
              Passing Marks: <%= section.passing_marks %>
            </span>
          <% } %>
        </div>
      </div>
      <div class="flex space-x-3">
        <a href="/tests/admin/<%= section.exam_id %>/edit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Test
        </a>
        <button id="addQuestionBtn" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Question
        </button>
      </div>
    </div>

      <!-- Questions List -->
      <div class="bg-white rounded-lg shadow p-6 mb-6 border-l-4 border-purple-500">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Questions (<%= questions.length %>)</h3>

        <% if (questions && questions.length > 0) { %>
          <div class="space-y-4" id="questionsContainer">
            <% questions.forEach((question, index) => { %>
              <div class="border border-gray-200 rounded-lg p-4 question-item" data-question-id="<%= question.question_id %>">
                <div class="flex justify-between items-center mb-2">
                  <div class="flex items-center flex-wrap gap-1">
                    <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full mr-2"><%= index + 1 %></span>

                    <!-- Question Type Badge -->
                    <% if (question.question_type === 'multiple_choice') { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Multiple Choice</span>
                    <% } else if (question.question_type === 'true_false') { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">True/False</span>
                    <% } else if (question.question_type === 'fill_up') { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill in the Blank</span>
                    <% } else if (question.question_type === 'essay') { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
                    <% } else { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"><%= question.question_type %></span>
                    <% } %>

                    <!-- Category Badges -->
                    <% if (question.category_names) { %>
                      <%
                      const categoryColors = {
                          'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                          'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                          'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                          'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                          'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                          'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                          'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                          'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                          'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
                          'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
                      };

                      const categoryNamesArray = question.category_names.split(',');
                      categoryNamesArray.forEach(categoryName => {
                          const category = categoryName.trim();
                          const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                      %>
                          <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                              <%= category %>
                          </span>
                      <% }); %>
                    <% } %>

                    <!-- Marks Badge -->
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 ml-1">
                      <%= question.marks || 1 %> mark<%= (question.marks && question.marks > 1) ? 's' : '' %>
                    </span>

                    <% if (question.negative_marks && question.negative_marks > 0) { %>
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 ml-1">
                        -<%= question.negative_marks %> mark<%= question.negative_marks > 1 ? 's' : '' %>
                      </span>
                    <% } %>

                    <h4 class="text-md font-medium text-gray-800 ml-2"><%= question.question_text.length > 50 ? question.question_text.substring(0, 50) + '...' : question.question_text %></h4>
                  </div>
                  <div class="flex space-x-2">
                    <button class="edit-question-btn text-indigo-600 hover:text-indigo-900" title="Edit Question" data-question-id="<%= question.question_id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                    <button class="delete-question-btn text-red-600 hover:text-red-900" title="Delete Question" data-question-id="<%= question.question_id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </div>
                <% if (question.option_count > 0) { %>
                  <div class="mt-2">
                    <span class="text-xs text-gray-500"><%= question.option_count %> options</span>
                  </div>
                <% } %>
              </div>
            <% }); %>
          </div>
        <% } else { %>
          <div class="text-center py-8 text-gray-500" id="noQuestionsMessage">
            <p>No questions added yet. Click "Add Question" to create your first question.</p>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Include the Add Question Modal -->
<%- include('../../admin/questions/partials/add-question-modal', {
  exams: [{ exam_id: section.exam_id, exam_name: section.exam_name }],
  categories: categories || [],
  essays: essays || []
}) %>

<!-- All JavaScript functionality has been moved to external files -->
<script>
  // Add hidden input fields for section and exam IDs
  document.addEventListener('DOMContentLoaded', function() {
    const sectionIdInput = document.createElement('input');
    sectionIdInput.type = 'hidden';
    sectionIdInput.name = 'section_id';
    sectionIdInput.value = '<%= section.section_id %>';
    document.body.appendChild(sectionIdInput);

    const examIdInput = document.createElement('input');
    examIdInput.type = 'hidden';
    examIdInput.name = 'exam_id';
    examIdInput.value = '<%= section.exam_id %>';
    document.body.appendChild(examIdInput);
  });
</script>


