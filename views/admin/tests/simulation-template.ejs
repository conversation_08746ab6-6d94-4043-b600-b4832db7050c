<!DOCTYPE html>
<html lang="en">
<head>
    <title><%= formData.exam_name || 'Untitled Test' %></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        #startSimulation { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); padding: 15px 30px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        #startSimulation:hover { background: #0056b3; }
        #examContent { display: none; height: 100vh; display: flex; flex-direction: column; }
        #header { background: #343a40; color: white; padding: 15px; position: fixed; top: 0; width: 100%; z-index: 10; }
        #timer { font-weight: bold; color: #ffd700; }
        .tab-container { display: flex; background: #e9ecef; padding: 10px; margin-top: 70px; }
        .tab-link { padding: 10px 20px; cursor: pointer; background: #dee2e6; margin-right: 5px; border-radius: 5px 5px 0 0; }
        .tab-link.active { background: #007bff; color: white; }
        #section-content { flex-grow: 1; padding: 20px; background: white; margin: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        #question-palette { padding: 10px; background: #f8f9fa; border-top: 1px solid #dee2e6; }
        .palette-btn { margin: 5px; padding: 5px 10px; border: 1px solid #ccc; border-radius: 3px; cursor: pointer; background: white; }
        .palette-btn.active { background: #007bff; color: white; }
        #navigation { padding: 10px; background: #f8f9fa; border-top: 1px solid #dee2e6; text-align: right; }
        #navigation button { padding: 8px 15px; margin-left: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        #navigation button:hover { background: #0056b3; }
        #submitBtn { background: #28a745; }
        #submitBtn:hover { background: #218838; }
    </style>
</head>
<body>
    <button id="startSimulation">Start Simulation</button>
    <div id="examContent">
        <div id="header">
            <h1>Test Name: <%= formData.exam_name || 'Untitled Test' %></h1>
            <p>Username: <%= username || 'Guest' %></p>
            <p>Time Remaining: <span id="timer">00:00:00</span></p>
        </div>
        <div class="tab-container">
            <% formData.sections.forEach((section, index) => { %>
                <button class="tab-link" data-section="<%= index %>"><%= section.name || `Section ${index + 1}` %></button>
            <% }); %>
        </div>
        <div id="section-content"></div>
        <div id="question-palette"></div>
        <div id="navigation">
            <button id="prevBtn">Previous</button>
            <button id="nextBtn">Next</button>
            <button id="submitBtn">Submit Exam</button>
        </div>
    </div>
    <script>
        var currentSection = 0;
        var currentQuestion = 0;
        var sections = <%- JSON.stringify(formData.sections) %>;
        var duration = <%= formData.duration || 60 %> * 60; // Convert minutes to seconds
        var timeLeft = duration;

        function startSimulation() {
            document.getElementById('startSimulation').style.display = 'none';
            document.getElementById('examContent').style.display = 'flex';
            document.documentElement.requestFullscreen();
            startTimer();
            showSection(0);
        }

        function startTimer() {
            setInterval(function() {
                if (timeLeft > 0) {
                    timeLeft--;
                    updateTimerDisplay();
                } else {
                    alert('Time is up!');
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            var hours = Math.floor(timeLeft / 3600);
            var minutes = Math.floor((timeLeft % 3600) / 60);
            var seconds = timeLeft % 60;
            document.getElementById('timer').textContent =
                (hours < 10 ? '0' + hours : hours) + ':' +
                (minutes < 10 ? '0' + minutes : minutes) + ':' +
                (seconds < 10 ? '0' + seconds : seconds);
        }

        function showSection(sectionIndex) {
            currentSection = sectionIndex;
            currentQuestion = 0;
            document.querySelectorAll('.tab-link').forEach(function(tab, index) {
                tab.classList.toggle('active', index === sectionIndex);
            });
            showQuestion(currentQuestion);
            generateQuestionPalette();
        }

        function generateQuestionPalette() {
            var palette = document.getElementById('question-palette');
            palette.innerHTML = '';
            sections[currentSection].questions.forEach(function(q, index) {
                var btn = document.createElement('button');
                btn.className = 'palette-btn';
                btn.textContent = index + 1;
                btn.addEventListener('click', function() { showQuestion(index); });
                palette.appendChild(btn);
            });
            highlightPaletteButton(currentQuestion);
        }

        function highlightPaletteButton(questionIndex) {
            document.querySelectorAll('.palette-btn').forEach(function(btn, index) {
                btn.classList.toggle('active', index === questionIndex);
            });
        }

        function showQuestion(questionIndex) {
            currentQuestion = questionIndex;
            var question = sections[currentSection].questions[questionIndex];
            var contentDiv = document.getElementById('section-content');
            contentDiv.innerHTML = '<p><strong>Question ' + (questionIndex + 1) + ':</strong> ' + question.question_text.replace(/\n/g, '<br>') + '</p>';
            if (question.question_type === 'mcq') {
                question.options.forEach(function(opt, i) {
                    contentDiv.innerHTML += '<label><input type="radio" name="q' + questionIndex + '" value="' + i + '"> ' + opt.text + '</label><br>';
                });
            } else if (question.question_type === 'true_false') {
                ['True', 'False'].forEach(function(val) {
                    contentDiv.innerHTML += '<label><input type="radio" name="q' + questionIndex + '" value="' + val.toLowerCase() + '"> ' + val + '</label><br>';
                });
            } else {
                contentDiv.innerHTML += '<input type="text" name="q' + questionIndex + '" placeholder="Enter your answer">';
            }
            highlightPaletteButton(questionIndex);
        }

        document.getElementById('startSimulation').addEventListener('click', startSimulation);
        document.querySelectorAll('.tab-link').forEach(function(tab) {
            tab.addEventListener('click', function() { showSection(parseInt(tab.getAttribute('data-section'))); });
        });
        document.getElementById('prevBtn').addEventListener('click', function() {
            if (currentQuestion > 0) showQuestion(currentQuestion - 1);
        });
        document.getElementById('nextBtn').addEventListener('click', function() {
            if (currentQuestion < sections[currentSection].questions.length - 1) showQuestion(currentQuestion + 1);
        });
        document.getElementById('submitBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to submit the exam?')) {
                alert('Exam submitted (simulation).');
                window.close();
            }
        });
    </script>
</body>
</html>