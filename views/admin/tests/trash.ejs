<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Deleted Tests</h2>
      <a href="/admin/tests" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Tests
      </a>
    </div>

    <!-- Filter Section -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
      <form action="/admin/tests/trash" method="GET" class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-[200px]">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input type="text" id="search" name="search" value="<%= query.search || '' %>" placeholder="Search by name or description" class="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50">
        </div>

        <div class="flex-1 min-w-[200px]">
          <label for="created_by" class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
          <select id="created_by" name="created_by" class="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50">
            <option value="">All Creators</option>
            <% creators.forEach(creator => { %>
              <option value="<%= creator.id %>" <%= query.created_by == creator.id ? 'selected' : '' %>><%= creator.username %></option>
            <% }) %>
          </select>
        </div>

        <div class="flex-1 min-w-[200px]">
          <label for="perPage" class="block text-sm font-medium text-gray-700 mb-1">Records Per Page</label>
          <select id="perPage" name="perPage" class="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50">
            <option value="10" <%= query.perPage == 10 ? 'selected' : '' %>>10</option>
            <option value="25" <%= query.perPage == 25 ? 'selected' : '' %>>25</option>
            <option value="50" <%= query.perPage == 50 ? 'selected' : '' %>>50</option>
            <option value="100" <%= query.perPage == 100 ? 'selected' : '' %>>100</option>
          </select>
        </div>

        <div class="flex items-end w-full sm:w-auto">
          <button type="submit" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition">Apply Filters</button>
          <a href="/admin/tests/trash" class="ml-2 text-gray-600 hover:text-gray-800">Clear</a>
        </div>
      </form>
    </div>

    <!-- Tests Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seq</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Passing Marks</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (tests && tests.length > 0) { %>
            <% tests.forEach((test, index) => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= (pagination.page - 1) * parseInt(query?.perPage || 10) + index + 1 %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= test.exam_name %></div>
                  <div class="text-sm text-gray-500">
                    <% if (test.section_count > 0) { %>
                      <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full"><%= test.section_count %> sections</span>
                    <% } %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= test.duration %> min</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= test.passing_marks %>%</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= test.creator_name || 'Unknown' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    <% if (test.deleted_at) { %>
                      <%= formatDateTime(test.deleted_at) %>
                    <% } else { %>
                      N/A
                    <% } %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                    Deleted
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <form action="/tests/admin/restore" method="POST" class="inline">
                      <input type="hidden" name="examId" value="<%= test.exam_id %>">
                      <button type="submit" class="text-green-600 hover:text-green-900" title="Restore">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                      </button>
                    </form>
                    <form action="/tests/admin/permanent-delete" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to permanently delete this test? This action cannot be undone.')">
                      <input type="hidden" name="examId" value="<%= test.exam_id %>">
                      <button type="submit" class="text-red-600 hover:text-red-900" title="Permanently Delete">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
            <% }) %>
          <% } else { %>
            <tr>
              <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                No deleted tests found.
              </td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
      <div class="flex justify-between items-center mt-6">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium"><%= ((pagination.page - 1) * perPage) + 1 %></span> to
          <span class="font-medium"><%= Math.min(pagination.page * perPage, filteredTests) %></span> of
          <span class="font-medium"><%= filteredTests %></span> results
          <% if (filteredTests !== totalTests) { %>
            (filtered from <span class="font-medium"><%= totalTests %></span> total)
          <% } %>
        </div>
        <div class="flex space-x-1">
          <% if (pagination.page > 1) { %>
            <a href="/admin/tests/trash?page=1&perPage=<%= perPage %><%= query.search ? `&search=${query.search}` : '' %><%= query.created_by ? `&created_by=${query.created_by}` : '' %>" class="px-3 py-1 border rounded-md text-sm text-gray-700 hover:bg-gray-50">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
              </svg>
            </a>
            <a href="/admin/tests/trash?page=<%= pagination.page - 1 %>&perPage=<%= perPage %><%= query.search ? `&search=${query.search}` : '' %><%= query.created_by ? `&created_by=${query.created_by}` : '' %>" class="px-3 py-1 border rounded-md text-sm text-gray-700 hover:bg-gray-50">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </a>
          <% } %>

          <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
            <a href="/admin/tests/trash?page=<%= i %>&perPage=<%= perPage %><%= query.search ? `&search=${query.search}` : '' %><%= query.created_by ? `&created_by=${query.created_by}` : '' %>"
              class="px-3 py-1 border rounded-md text-sm <%= pagination.page === i ? 'bg-purple-600 text-white' : 'text-gray-700 hover:bg-gray-50' %>">
              <%= i %>
            </a>
          <% } %>

          <% if (pagination.page < pagination.totalPages) { %>
            <a href="/admin/tests/trash?page=<%= pagination.page + 1 %>&perPage=<%= perPage %><%= query.search ? `&search=${query.search}` : '' %><%= query.created_by ? `&created_by=${query.created_by}` : '' %>" class="px-3 py-1 border rounded-md text-sm text-gray-700 hover:bg-gray-50">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
            <a href="/admin/tests/trash?page=<%= pagination.totalPages %>&perPage=<%= perPage %><%= query.search ? `&search=${query.search}` : '' %><%= query.created_by ? `&created_by=${query.created_by}` : '' %>" class="px-3 py-1 border rounded-md text-sm text-gray-700 hover:bg-gray-50">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
              </svg>
            </a>
          <% } %>
        </div>
      </div>
    <% } %>
  </div>
</div>


