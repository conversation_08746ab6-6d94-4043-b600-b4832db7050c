<!-- Assign Role Form -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-purple-600 text-white p-4">
    <h2 class="text-xl font-semibold">Assign Role to User</h2>
  </div>
  <div class="p-6">
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900">User Information</h3>
      <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p class="text-sm text-gray-500">Username</p>
          <p class="text-base font-medium text-gray-900"><%= user.username %></p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Email</p>
          <p class="text-base font-medium text-gray-900"><%= user.email %></p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Current Role</p>
          <p class="text-base font-medium text-gray-900"><%= currentRole %></p>
        </div>
      </div>
    </div>

    <form id="roleForm" class="space-y-6">
      <div>
        <label for="role" class="block text-sm font-medium text-gray-700">Select Role</label>
        <select id="role" name="role" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md">
          <% roles.forEach(role => { %>
            <option value="<%= role.role_name %>" <%= currentRole === role.role_name ? 'selected' : '' %>>
              <%= role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1) %> - <%= role.description %>
            </option>
          <% }); %>
        </select>
      </div>

      <div class="flex justify-end space-x-3">
        <a href="/admin/users" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
          Cancel
        </a>
        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
          Update Role
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const roleForm = document.getElementById('roleForm');
    
    roleForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Confirm before updating role
      if (!confirm('Are you sure you want to change this user\'s role?')) {
        return;
      }
      
      const role = document.getElementById('role').value;
      
      // Send AJAX request to update role
      fetch('/admin/users/<%= user.id %>/role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          showToast('success', 'Success', data.message);
          
          // Redirect to users list after a delay
          setTimeout(() => {
            window.location.href = '/admin/users';
          }, 1500);
        } else {
          // Show error message
          showToast('error', 'Error', data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Error', 'An error occurred while updating the role');
      });
    });
  });
</script>
