<!-- Create Role Form -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">Create New Role</h2>
      <a href="/admin/users/roles" class="text-indigo-600 hover:text-indigo-900 flex items-center">
        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Roles
      </a>
    </div>

    <form action="/admin/users/roles/create" method="POST">
      <div class="mb-6">
        <label for="role_name" class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
        <input type="text" id="role_name" name="role_name" required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter role name">
      </div>

      <div class="mb-6">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea id="description" name="description" rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter role description"></textarea>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-3">Permissions</h3>
        <p class="text-sm text-gray-600 mb-4">Select the permissions for this role. Users with this role will be able to perform these actions.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <% Object.keys(permissionsByCategory).forEach(category => { %>
            <div class="bg-gray-50 p-4 rounded-md">
              <h4 class="font-medium text-gray-800 mb-3 capitalize"><%= category %> Permissions</h4>
              
              <div class="space-y-2">
                <% permissionsByCategory[category].forEach(permission => { %>
                  <div class="flex items-center">
                    <input type="checkbox" id="permission_<%= permission.permission_id %>" name="permissions[]" value="<%= permission.permission_id %>"
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="permission_<%= permission.permission_id %>" class="ml-2 block text-sm text-gray-700">
                      <%= permission.description %>
                    </label>
                  </div>
                <% }) %>
              </div>
            </div>
          <% }) %>
        </div>
      </div>

      <div class="flex justify-end">
        <a href="/admin/users/roles" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition mr-3">
          Cancel
        </a>
        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition">
          Create Role
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  // Helper function to select all permissions in a category
  function addSelectAllCheckboxes() {
    const categories = document.querySelectorAll('.bg-gray-50');
    
    categories.forEach(category => {
      const checkboxes = category.querySelectorAll('input[type="checkbox"]');
      if (checkboxes.length === 0) return;
      
      const categoryHeading = category.querySelector('h4');
      const categoryName = categoryHeading.textContent.replace(' Permissions', '').trim();
      
      const selectAllDiv = document.createElement('div');
      selectAllDiv.className = 'flex items-center mb-2';
      
      const selectAllCheckbox = document.createElement('input');
      selectAllCheckbox.type = 'checkbox';
      selectAllCheckbox.id = `select_all_${categoryName.toLowerCase()}`;
      selectAllCheckbox.className = 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded';
      
      const selectAllLabel = document.createElement('label');
      selectAllLabel.htmlFor = `select_all_${categoryName.toLowerCase()}`;
      selectAllLabel.className = 'ml-2 block text-sm font-medium text-gray-700';
      selectAllLabel.textContent = 'Select All';
      
      selectAllDiv.appendChild(selectAllCheckbox);
      selectAllDiv.appendChild(selectAllLabel);
      
      categoryHeading.after(selectAllDiv);
      
      // Add event listener to select all checkbox
      selectAllCheckbox.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
      });
      
      // Add event listeners to individual checkboxes to update select all checkbox
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          const allChecked = Array.from(checkboxes).every(cb => cb.checked);
          const someChecked = Array.from(checkboxes).some(cb => cb.checked);
          
          selectAllCheckbox.checked = allChecked;
          selectAllCheckbox.indeterminate = someChecked && !allChecked;
        });
      });
    });
  }
  
  // Call the function when the page loads
  document.addEventListener('DOMContentLoaded', addSelectAllCheckboxes);
</script>
