<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold">Edit User</h2>
    </div>
    
    <div class="p-6">
      <% if (error) { %>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <span class="block sm:inline"><%= error %></span>
        </div>
      <% } %>
      
      <form action="/admin/users/<%= user.id %>/update" method="POST" enctype="multipart/form-data">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Profile Image Upload -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Profile Image (Optional)</label>
            <div class="mt-1 flex items-center">
              <div class="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                <% if (user.profile_image) { %>
                  <img id="preview-image" src="<%= user.profile_image %>" alt="Preview" class="w-20 h-20 rounded-full object-cover">
                  <span id="preview-initials" class="text-xl font-bold text-gray-400 hidden">
                    <%= user.username.substring(0, 2).toUpperCase() %>
                  </span>
                <% } else { %>
                  <img id="preview-image" src="/images/default-avatar.png" alt="Preview" class="w-20 h-20 rounded-full object-cover hidden">
                  <span id="preview-initials" class="text-xl font-bold text-gray-400">
                    <%= user.username.substring(0, 2).toUpperCase() %>
                  </span>
                <% } %>
              </div>
              <div>
                <input type="file" id="profile_image" name="profile_image" class="hidden" accept="image/*">
                <label for="profile_image" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded cursor-pointer">
                  Change Image
                </label>
                <p class="text-sm text-gray-500 mt-1">Max size: 5MB. Formats: JPG, PNG, GIF</p>
              </div>
            </div>
          </div>
  
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <input type="text" id="username" name="username" value="<%= user.username %>" required 
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input type="email" id="email" name="email" value="<%= user.email %>" required 
                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                   title="Please enter a valid email address"
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            <p class="mt-1 text-sm text-gray-500">Enter a valid email address</p>
          </div>
          
          <div>
            <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select id="role" name="role" required 
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              <option value="student" <%= user.role === 'student' ? 'selected' : '' %>>Student</option>
              <option value="teacher" <%= user.role === 'teacher' ? 'selected' : '' %>>Teacher</option>
              <option value="admin" <%= user.role === 'admin' ? 'selected' : '' %>>Admin</option>
            </select>
          </div>
          
          <div>
            <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
            <input type="date" id="date_of_birth" name="date_of_birth" 
                   value="<%= user.date_of_birth ? user.date_of_birth.toISOString().split('T')[0] : '' %>" required 
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
          </div>
  
          <!-- New Password (Optional) -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password (Optional)</label>
            <input type="password" id="password" name="password" minlength="8"
                   pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                   title="Password must be at least 8 characters long, and include at least one uppercase letter, one number, and one special character"
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            <p class="mt-1 text-sm text-gray-500">Leave blank to keep current password. Must be at least 8 characters with one uppercase letter, one number, and one special character.</p>
          </div>
  
          <!-- Bio field - spans full width -->
          <div class="md:col-span-2">
            <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">Bio (Optional)</label>
            <textarea id="bio" name="bio" rows="4" maxlength="500"
                      class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500"><%= user.bio || '' %></textarea>
            <p class="mt-1 text-sm text-gray-500"><span id="bio-char-count">0</span>/500 characters</p>
          </div>
        </div>
        
        <div class="mt-6 flex justify-end">
          <a href="/admin/users" class="bg-gray-200 text-gray-800 py-2 px-4 rounded-md mr-2 hover:bg-gray-300 transition">Cancel</a>
          <button type="submit" class="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
  document.addEventListener('DOMContentLoaded', function() {
      const fileInput = document.getElementById('profile_image');
      const previewImage = document.getElementById('preview-image');
      const previewInitials = document.getElementById('preview-initials');
      const usernameInput = document.getElementById('username');
      const bioInput = document.getElementById('bio');
      const bioCharCount = document.getElementById('bio-char-count');
      
      // Update character count for bio
      function updateCharCount() {
          const count = bioInput.value.length;
          bioCharCount.textContent = count;
          if (count > 500) {
              bioCharCount.classList.add('text-red-500');
          } else {
              bioCharCount.classList.remove('text-red-500');
          }
      }
      
      // Initialize character count
      updateCharCount();
      
      // Add event listener for bio input
      bioInput.addEventListener('input', updateCharCount);

      fileInput.addEventListener('change', function() {
          if (this.files && this.files[0]) {
              const reader = new FileReader();
              
              reader.onload = function(e) {
                  previewImage.src = e.target.result;
                  previewImage.classList.remove('hidden');
                  previewInitials.classList.add('hidden');
              };
              
              reader.readAsDataURL(this.files[0]);
          }
      });
  
      usernameInput.addEventListener('input', function() {
          if (!fileInput.files.length && !previewImage.src.includes('data:image')) {
              const initials = this.value
                  .split(' ')
                  .map(word => word[0])
                  .join('')
                  .toUpperCase()
                  .substring(0, 2) || 'U';
              previewInitials.textContent = initials;
          }
      });
  });
  </script>