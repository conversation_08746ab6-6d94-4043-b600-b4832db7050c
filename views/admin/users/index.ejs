<!-- Admin Users Index -->
<div class="space-y-6">
  <!-- Advanced Filters and Search Section -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <form id="filterForm" action="/admin/users" method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
          <!-- Search -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.search') %></label>
            <div class="relative">
              <input type="text"
                     id="search"
                     name="search"
                     value="<%= query.search || '' %>"
                     placeholder="<%= __('admin.searchByUsernameEmail') %>"
                     class="w-full border border-gray-300 rounded-md shadow-sm pl-10 pr-4 py-2 focus:ring-purple-500 focus:border-purple-500">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- Role Filter -->
          <div>
            <label for="role" class="block text-sm font-medium text-gray-700 mb-1"><%= __('admin.role') %></label>
            <select id="role"
                    name="role"
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
              <option value=""><%= __('common.all') %> <%= __('admin.roles') %></option>
              <option value="user" <%= query.role === 'user' ? 'selected' : '' %>><%= __('admin.userRole') %></option>
              <option value="admin" <%= query.role === 'admin' ? 'selected' : '' %>><%= __('admin.adminRole') %></option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
              <option value="">All Statuses</option>
              <option value="online" <%= query.status === 'online' ? 'selected' : '' %>>Online</option>
              <option value="offline" <%= query.status === 'offline' ? 'selected' : '' %>>Offline</option>
              <option value="new" <%= query.status === 'new' ? 'selected' : '' %>>New Users</option>
              <option value="blocked" <%= query.status === 'blocked' ? 'selected' : '' %>>Blocked Users</option>
            </select>
          </div>

          <!-- From Date -->
          <div>
            <label for="fromDate" class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.fromDate') %></label>
            <input type="date"
                   id="fromDate"
                   name="fromDate"
                   value="<%= query.fromDate || '' %>"
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
          </div>

          <!-- To Date -->
          <div>
            <label for="toDate" class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.toDate') %></label>
            <input type="date"
                   id="toDate"
                   name="toDate"
                   value="<%= query.toDate || '' %>"
                   class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
          </div>

          <!-- Sort By -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-1"><%= __('common.sortBy') %></label>
            <select id="sort"
                    name="sort"
                    class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500">
              <option value="created_desc" <%= query.sort === 'created_desc' ? 'selected' : '' %>><%= __('common.newestFirst') %></option>
              <option value="created_asc" <%= query.sort === 'created_asc' ? 'selected' : '' %>><%= __('common.oldestFirst') %></option>
              <option value="username_asc" <%= query.sort === 'username_asc' ? 'selected' : '' %>><%= __('common.username') %> (A-Z)</option>
              <option value="username_desc" <%= query.sort === 'username_desc' ? 'selected' : '' %>><%= __('common.username') %> (Z-A)</option>
            </select>
          </div>
        </div>

        <!-- Quick Filters -->
        <div class="pt-4">
          <h3 class="text-sm font-medium text-gray-700 mb-2">Quick Filters</h3>
          <div class="flex flex-wrap gap-2 mb-4">
            <a href="/admin/users<%= query?.search ? '?search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= !query?.role && !query?.status ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %> text-xs font-medium">
              All Users
            </a>
            <a href="/admin/users?role=admin<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.role === 'admin' ? 'bg-purple-700 text-white' : 'bg-purple-100 text-purple-800 hover:bg-purple-200' %> text-xs font-medium">
              Admins
            </a>
            <a href="/admin/users?role=user<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.role === 'user' ? 'bg-blue-700 text-white' : 'bg-blue-100 text-blue-800 hover:bg-blue-200' %> text-xs font-medium">
              Students
            </a>
            <a href="/admin/users?status=online<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'online' ? 'bg-green-700 text-white' : 'bg-green-100 text-green-800 hover:bg-green-200' %> text-xs font-medium">
              Online
            </a>
            <a href="/admin/users?status=new<%= query?.search ? '&search=' + query.search : '' %>" class="px-2 py-1 rounded-full <%= query?.status === 'new' ? 'bg-yellow-700 text-white' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' %> text-xs font-medium">
              New Users
            </a>
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-between items-center pt-2">
          <div class="text-sm text-gray-600">
            <%= __('admin.totalUsers') %>: <%= pagination.totalUsers %>
          </div>
          <div class="space-x-2">
            <button type="button"
                    onclick="clearFilters()"
                    class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition">
              <%= __('common.clearFilters') %>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Users List -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800"><%= __('admin.allUsers') %></h2>
        <div class="flex space-x-3">
          <a href="/admin/users/roles" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
            </svg>
            <%= __('common.manage') %> <%= __('admin.roles') %>
          </a>
          <a href="/admin/groups" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Manage Groups
          </a>
          <a href="/admin/users/add" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <%= __('admin.addUser') %>
          </a>
        </div>
      </div>



<% if (users && users.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% users.forEach(user => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <% if (user.profile_image) { %>
                          <img class="h-10 w-10 rounded-full" src="<%= user.profile_image %>" alt="<%= user.username %>">
                        <% } else { %>
                          <div class="h-10 w-10 rounded-full bg-purple-200 flex items-center justify-center">
                            <span class="text-purple-600 font-semibold">
                              <%= user.username.substring(0, 2).toUpperCase() %>
                            </span>
                          </div>
                        <% } %>
                      </div>
                      <div class="ml-4">
                        <div class="flex items-center">
                          <div class="text-sm font-medium text-gray-900"><%= user.username %></div>
                          <%
                            // Check if user is new (registered within 48 hours)
                            const userCreatedAt = new Date(user.created_at);
                            const now = new Date();
                            const hoursDiff = Math.abs(now - userCreatedAt) / 36e5; // Convert to hours
                            const isNewUser = hoursDiff <= 48;
                            const isOnline = user.is_online;
                          %>
                          <% if (isNewUser) { %>
                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">New</span>
                          <% } %>
                          <% if (user.is_blocked) { %>
                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Blocked</span>
                          <% } else if (isOnline) { %>
                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Online</span>
                          <% } else { %>
                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Offline</span>
                          <% } %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500"><%= user.email %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if (user.role === 'admin') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Admin</span>
                    <% } else { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">User</span>
                    <% } %>
                  </td>

                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">
                      <% if (typeof formatDate === 'function') { %>
                        <%= formatDate(user.created_at) %>
                      <% } else { %>
                        <%
                          const createdDate = new Date(user.created_at);
                          const day = String(createdDate.getDate()).padStart(2, '0');
                          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                          const month = monthNames[createdDate.getMonth()];
                          const year = createdDate.getFullYear();
                        %>
                        <%= `${day}-${month}-${year}` %>
                      <% } %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-3">
                      <!-- View button -->
                      <a href="/admin/users/<%= user.id %>" class="text-blue-600 hover:text-blue-900" title="View User">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </a>

                      <!-- Edit button -->
                      <a href="/admin/users/<%= user.id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit User">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </a>

                      <!-- Archive button -->
                      <a href="#" onclick="confirmArchive('<%= user.id %>', '<%= user.username %>'); return false;" class="text-yellow-600 hover:text-yellow-900" title="Archive User">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                        </svg>
                      </a>

                      <!-- Delete button -->
                      <button onclick="confirmDelete('<%= user.id %>', '<%= user.username %>')" class="text-red-600 hover:text-red-900" title="Delete User">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>

                      <% if (user.id != currentUserId) { %>
                        <% if (user.is_blocked) { %>
                          <!-- Unblock button -->
                          <button onclick="confirmUnblock('<%= user.id %>', '<%= user.username %>')" class="text-green-600 hover:text-green-900" title="Unblock User">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                          </button>
                        <% } else { %>
                          <!-- Block button -->
                          <button onclick="confirmBlock('<%= user.id %>', '<%= user.username %>')" class="text-gray-600 hover:text-gray-900" title="Block User">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H8m10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                          </button>
                        <% } %>
                      <% } %>
                    </div>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <% if (pagination && pagination.totalPages > 1) { %>
          <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              <%
                const start = (pagination.currentPage - 1) * pagination.limit + 1;
                const end = Math.min(pagination.currentPage * pagination.limit, pagination.totalUsers);
              %>
              Showing <%= start %> to <%= end %> of <%= pagination.totalUsers %> users
            </div>
            <div class="space-x-1">
              <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                <a href="?page=<%= i %>&search=<%= query.search || '' %>&role=<%= query.role || '' %>&sort=<%= query.sort || '' %>"
                   class="inline-flex items-center px-3 py-1 border rounded-md <%= pagination.currentPage === i ? 'bg-purple-600 text-white border-purple-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50' %>">
                  <%= i %>
                </a>
              <% } %>
            </div>
          </div>
        <% } %>
      <% } else { %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
          <p class="mt-1 text-sm text-gray-500">
            <%= (query && query.search) ? 'No users match your search criteria.' : 'Get started by creating a new user.' %>
          </p>
          <div class="mt-6">
            <a href="/admin/users/add" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add New User
            </a>
          </div>
        </div>
      <% } %>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Delete User</h3>
      <button onclick="closeDeleteModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <p class="mb-4">Are you sure you want to delete user <span id="userName" class="font-semibold"></span>? This action cannot be undone.</p>

      <div class="flex justify-end space-x-3">
        <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Cancel
        </button>
        <form id="deleteForm" method="POST" action="">
          <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Archive Confirmation Modal -->
<div id="archiveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="bg-yellow-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Archive User</h3>
      <button onclick="closeArchiveModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <p class="mb-4">Are you sure you want to archive user <span id="archiveUserName" class="font-semibold"></span>? They will no longer be able to log in.</p>

      <div class="flex justify-end space-x-3">
        <button onclick="closeArchiveModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Cancel
        </button>
        <form id="archiveForm" method="POST" action="">
          <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
            </svg>
            Archive
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Block Confirmation Modal -->
<div id="blockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="bg-gray-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Block User</h3>
      <button onclick="closeBlockModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <p class="mb-4">Are you sure you want to block user <span id="blockUserName" class="font-semibold"></span>? They will no longer be able to log in.</p>

      <div class="flex justify-end space-x-3">
        <button onclick="closeBlockModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Cancel
        </button>
        <form id="blockForm" method="POST" action="">
          <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H8m10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Block
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Unblock Confirmation Modal -->
<div id="unblockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Unblock User</h3>
      <button onclick="closeUnblockModal()" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <p class="mb-4">Are you sure you want to unblock user <span id="unblockUserName" class="font-semibold"></span>? They will be able to log in again.</p>

      <div class="flex justify-end space-x-3">
        <button onclick="closeUnblockModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
          Cancel
        </button>
        <form id="unblockForm" method="POST" action="">
          <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Unblock
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// Add event listeners for real-time filtering
document.querySelectorAll('#search, #role, #fromDate, #toDate, #sort').forEach(element => {
    element.addEventListener('change', function() {
        document.getElementById('filterForm').submit();
    });
});

// Add debounced search for text input
let searchTimeout;
const searchInput = document.getElementById('search');

searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        // Store the current cursor position
        const cursorPosition = this.selectionStart;
        const currentValue = this.value;

        document.getElementById('filterForm').submit();

        // After form submission, restore focus and cursor position
        setTimeout(() => {
            this.focus();
            this.value = currentValue;
            this.setSelectionRange(cursorPosition, cursorPosition);
        }, 0);
    }, 500); // Wait 500ms after user stops typing
});

// Restore focus on page load if there was a search
window.addEventListener('load', function() {
    if (searchInput.value) {
        searchInput.focus();
        // Place cursor at the end of the text
        searchInput.setSelectionRange(searchInput.value.length, searchInput.value.length);
    }
});

function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('role').value = '';
    document.getElementById('fromDate').value = '';
    document.getElementById('toDate').value = '';
    document.getElementById('sort').value = 'created_desc';
    document.getElementById('filterForm').submit();
}

function confirmDelete(userId, userName) {
    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
            title: 'Delete User',
            message: `Are you sure you want to delete user ${userName}? This action cannot be undone.`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            type: 'danger',
            onConfirm: function() {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/users/${userId}/delete`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback to modal
        document.getElementById('userName').textContent = userName;
        document.getElementById('deleteForm').action = `/admin/users/${userId}/delete`;
        document.getElementById('deleteModal').classList.remove('hidden');
    }
}

function confirmArchive(userId, userName) {
    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
            title: 'Archive User',
            message: `Are you sure you want to archive user ${userName}? They will no longer be able to log in.`,
            confirmText: 'Archive',
            cancelText: 'Cancel',
            type: 'warning',
            onConfirm: function() {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/users/${userId}/archive`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback to modal
        document.getElementById('archiveUserName').textContent = userName;
        document.getElementById('archiveForm').action = `/admin/users/${userId}/archive`;
        document.getElementById('archiveModal').classList.remove('hidden');
    }
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function closeArchiveModal() {
    document.getElementById('archiveModal').classList.add('hidden');
}

function closeBlockModal() {
    document.getElementById('blockModal').classList.add('hidden');
}

function closeUnblockModal() {
    document.getElementById('unblockModal').classList.add('hidden');
}

function confirmBlock(userId, userName) {
    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
            title: 'Block User',
            message: `Are you sure you want to block user ${userName}? They will no longer be able to log in.`,
            confirmText: 'Block',
            cancelText: 'Cancel',
            type: 'warning',
            onConfirm: function() {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/users/${userId}/block`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback to modal
        document.getElementById('blockUserName').textContent = userName;
        document.getElementById('blockForm').action = `/admin/users/${userId}/block`;
        document.getElementById('blockModal').classList.remove('hidden');
    }
}

function confirmUnblock(userId, userName) {
    // Check if confirmation dialog is available
    if (window.confirmationDialog) {
        window.confirmationDialog.show({
            title: 'Unblock User',
            message: `Are you sure you want to unblock user ${userName}? They will be able to log in again.`,
            confirmText: 'Unblock',
            cancelText: 'Cancel',
            type: 'info',
            onConfirm: function() {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/users/${userId}/unblock`;

                // Add CSRF token if needed
                const csrfTokenElement = document.getElementById('csrf-token');
                if (csrfTokenElement) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfTokenElement.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    } else {
        // Fallback to modal
        document.getElementById('unblockUserName').textContent = userName;
        document.getElementById('unblockForm').action = `/admin/users/${userId}/unblock`;
        document.getElementById('unblockModal').classList.remove('hidden');
    }
}

// Close modals when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

document.getElementById('archiveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeArchiveModal();
    }
});

document.getElementById('blockModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBlockModal();
    }
});

document.getElementById('unblockModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUnblockModal();
    }
});

// Close modals on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDeleteModal();
        closeArchiveModal();
        closeBlockModal();
        closeUnblockModal();
    }
});
</script>
