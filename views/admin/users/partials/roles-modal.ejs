<!-- Roles Management Modal -->
<div id="roles-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
    <div class="bg-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 class="text-xl font-semibold">Manage Roles</h3>
      <button id="close-roles-modal" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h4 class="text-lg font-medium text-gray-900">System Roles</h4>
        <button id="add-role-btn" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add New Role
        </button>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="roles-table-body" class="bg-white divide-y divide-gray-200">
            <!-- Roles will be loaded here via JavaScript -->
            <tr>
              <td colspan="5" class="px-6 py-4 text-center text-gray-500">Loading roles...</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Add/Edit Role Modal -->
<div id="role-form-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="role-form-title" class="text-xl font-semibold">Add New Role</h3>
      <button id="close-role-form-modal" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <form id="role-form">
        <input type="hidden" id="role-id" name="role_id" value="">

        <div class="mb-4">
          <label for="role-name" class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
          <input type="text" id="role-name" name="role_name" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500" required>
        </div>

        <div class="mb-4">
          <label for="role-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea id="role-description" name="description" rows="3" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500"></textarea>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" id="cancel-role-form" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition">
            Save Role
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- View Role Users Modal -->
<div id="role-users-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
    <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="role-users-title" class="text-xl font-semibold">Users with Role</h3>
      <button id="close-role-users-modal" class="text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="p-6">
      <div id="role-users-container" class="overflow-x-auto">
        <!-- Users will be loaded here via JavaScript -->
        <p class="text-center text-gray-500">Loading users...</p>
      </div>
    </div>
  </div>
</div>

<script>
  // Load roles from the server
  function loadRoles() {
    const rolesTableBody = document.getElementById('roles-table-body');
    if (rolesTableBody) {
      rolesTableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">Loading roles...</td></tr>';

      // Fetch roles from the API
      fetch('/admin/api/roles')
        .then(response => response.json())
        .then(data => {
          if (data.success && data.roles) {
            let html = '';
            if (data.roles.length === 0) {
              html = '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No roles found. Create your first role.</td></tr>';
            } else {
              data.roles.forEach(role => {
                html += `
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">${role.role_name}</div>
                    </td>
                    <td class="px-6 py-4">
                      <div class="text-sm text-gray-500">${role.description || 'No description'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500">${role.user_count}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${role.is_system ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                        ${role.is_system ? 'System' : 'Custom'}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button class="view-users-btn text-blue-600 hover:text-blue-900 mr-3" data-role-id="${role.role_id}">View Users</button>
                      ${!role.is_system ? `
                        <button class="edit-role-btn text-indigo-600 hover:text-indigo-900 mr-3" data-role-id="${role.role_id}" data-role-name="${role.role_name}" data-description="${role.description || ''}">Edit</button>
                        <button class="delete-role-btn text-red-600 hover:text-red-900" data-role-id="${role.role_id}" data-role-name="${role.role_name}">Delete</button>
                      ` : ''}
                    </td>
                  </tr>
                `;
              });
            }
            rolesTableBody.innerHTML = html;

            // Add event listeners to the buttons
            addRoleButtonEventListeners();
          } else {
            rolesTableBody.innerHTML = `<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">${data.message || 'Failed to load roles'}</td></tr>`;
          }
        })
        .catch(error => {
          console.error('Error loading roles:', error);
          rolesTableBody.innerHTML = '<tr><td colspan="5" class="px-6 py-4 text-center text-red-500">Error loading roles. Please try again.</td></tr>';
        });
    }
  }

  // Add event listeners to role buttons
  function addRoleButtonEventListeners() {
    // View users buttons
    document.querySelectorAll('.view-users-btn').forEach(button => {
      button.addEventListener('click', function() {
        const roleId = this.getAttribute('data-role-id');
        viewRoleUsers(roleId);
      });
    });

    // Edit role buttons
    document.querySelectorAll('.edit-role-btn').forEach(button => {
      button.addEventListener('click', function() {
        const roleId = this.getAttribute('data-role-id');
        const roleName = this.getAttribute('data-role-name');
        const description = this.getAttribute('data-description');
        openRoleFormModal('edit', roleId, roleName, description);
      });
    });

    // Delete role buttons
    document.querySelectorAll('.delete-role-btn').forEach(button => {
      button.addEventListener('click', function() {
        const roleId = this.getAttribute('data-role-id');
        const roleName = this.getAttribute('data-role-name');
        confirmDeleteRole(roleId, roleName);
      });
    });
  }

  // View users with a specific role
  function viewRoleUsers(roleId) {
    const roleUsersContainer = document.getElementById('role-users-container');
    roleUsersContainer.innerHTML = '<p class="text-center text-gray-500">Loading users...</p>';

    // Show the modal
    document.getElementById('role-users-modal').classList.remove('hidden');

    // Fetch users with this role
    fetch(`/admin/api/roles/${roleId}/users`)
      .then(response => response.json())
      .then(data => {
        if (data.success && data.users) {
          if (data.users.length === 0) {
            roleUsersContainer.innerHTML = '<p class="text-center text-gray-500">No users found with this role.</p>';
          } else {
            // Set the title
            document.getElementById('role-users-title').textContent = `Users with ${data.role_name} Role`;

            // Create the table
            let html = `
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
            `;

            data.users.forEach(user => {
              html += `
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        ${user.profile_image ?
                          `<img class="h-10 w-10 rounded-full" src="${user.profile_image}" alt="${user.username}">` :
                          `<div class="h-10 w-10 rounded-full bg-purple-200 flex items-center justify-center">
                            <span class="text-purple-600 font-semibold">${user.username.substring(0, 2).toUpperCase()}</span>
                          </div>`
                        }
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.username}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">${user.email}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    ${user.is_blocked ?
                      `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Blocked</span>` :
                      (user.is_online ?
                        `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Online</span>` :
                        `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Offline</span>`
                      )
                    }
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="/admin/users/${user.id}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                    <a href="/admin/users/${user.id}/edit" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                  </td>
                </tr>
              `;
            });

            html += `
                </tbody>
              </table>
            `;

            roleUsersContainer.innerHTML = html;
          }
        } else {
          roleUsersContainer.innerHTML = `<p class="text-center text-red-500">${data.message || 'Failed to load users'}</p>`;
        }
      })
      .catch(error => {
        console.error('Error loading users:', error);
        roleUsersContainer.innerHTML = '<p class="text-center text-red-500">Error loading users. Please try again.</p>';
      });
  }

  // Open the role form modal
  function openRoleFormModal(mode, roleId = '', roleName = '', description = '') {
    const modal = document.getElementById('role-form-modal');
    const title = document.getElementById('role-form-title');
    const form = document.getElementById('role-form');
    const roleIdInput = document.getElementById('role-id');
    const roleNameInput = document.getElementById('role-name');
    const descriptionInput = document.getElementById('role-description');

    // Set the form title and values
    title.textContent = mode === 'edit' ? 'Edit Role' : 'Add New Role';
    roleIdInput.value = roleId;
    roleNameInput.value = roleName;
    descriptionInput.value = description;

    // Show the modal
    modal.classList.remove('hidden');
  }

  // Confirm delete role
  function confirmDeleteRole(roleId, roleName) {
    if (window.confirmationDialog) {
      window.confirmationDialog.show({
        title: 'Delete Role',
        message: `Are you sure you want to delete the role "${roleName}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        onConfirm: function() {
          deleteRole(roleId);
        }
      });
    } else {
      if (confirm(`Are you sure you want to delete the role "${roleName}"? This action cannot be undone.`)) {
        deleteRole(roleId);
      }
    }
  }

  // Delete a role
  function deleteRole(roleId) {
    fetch(`/admin/api/roles/${roleId}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success message
        showToast('success', data.message || 'Role deleted successfully');
        // Reload roles
        loadRoles();
      } else {
        // Show error message
        showToast('error', data.message || 'Failed to delete role');
      }
    })
    .catch(error => {
      console.error('Error deleting role:', error);
      showToast('error', 'Error deleting role. Please try again.');
    });
  }

  // Show toast notification
  function showToast(type, message) {
    if (window.ToastNotifications) {
      window.ToastNotifications[type](message);
    } else {
      console.log(`${type}:`, message);
    }
  }

  // Document ready
  document.addEventListener('DOMContentLoaded', function() {
    // Add Role button
    const addRoleBtn = document.getElementById('add-role-btn');
    if (addRoleBtn) {
      addRoleBtn.addEventListener('click', function() {
        openRoleFormModal('add');
      });
    }

    // Close Role Form Modal button
    const closeRoleFormBtn = document.getElementById('close-role-form-modal');
    if (closeRoleFormBtn) {
      closeRoleFormBtn.addEventListener('click', function() {
        document.getElementById('role-form-modal').classList.add('hidden');
      });
    }

    // Cancel Role Form button
    const cancelRoleFormBtn = document.getElementById('cancel-role-form');
    if (cancelRoleFormBtn) {
      cancelRoleFormBtn.addEventListener('click', function() {
        document.getElementById('role-form-modal').classList.add('hidden');
      });
    }

    // Close Role Users Modal button
    const closeRoleUsersBtn = document.getElementById('close-role-users-modal');
    if (closeRoleUsersBtn) {
      closeRoleUsersBtn.addEventListener('click', function() {
        document.getElementById('role-users-modal').classList.add('hidden');
      });
    }

    // Role Form submission
    const roleForm = document.getElementById('role-form');
    if (roleForm) {
      roleForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const roleId = document.getElementById('role-id').value;
        const roleName = document.getElementById('role-name').value;
        const description = document.getElementById('role-description').value;

        const url = roleId ? `/admin/api/roles/${roleId}/update` : '/admin/api/roles/create';

        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            role_name: roleName,
            description: description
          })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Hide the modal
            document.getElementById('role-form-modal').classList.add('hidden');
            // Show success message
            showToast('success', data.message || 'Role saved successfully');
            // Reload roles
            loadRoles();
          } else {
            // Show error message
            showToast('error', data.message || 'Failed to save role');
          }
        })
        .catch(error => {
          console.error('Error saving role:', error);
          showToast('error', 'Error saving role. Please try again.');
        });
      });
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
      const roleFormModal = document.getElementById('role-form-modal');
      const roleUsersModal = document.getElementById('role-users-modal');

      if (event.target === roleFormModal) {
        roleFormModal.classList.add('hidden');
      }

      if (event.target === roleUsersModal) {
        roleUsersModal.classList.add('hidden');
      }
    });
  });
</script>
