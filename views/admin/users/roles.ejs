  <!-- Role Management -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">User Roles</h2>
      <a href="/admin/users/roles/create" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Create New Role
      </a>
    </div>

    <% if (roles && roles.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% roles.forEach(role => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                    <% if (role.role_name === 'admin') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Admin</span>
                    <% } else if (role.role_name === 'teacher') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Teacher</span>
                    <% } else if (role.role_name === 'student') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Student</span>
                    <% } else { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><%= role.role_name %></span>
                    <% } %>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500"><%= role.description || 'No description' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500"><%= role.user_count %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">
                    <% if (role.is_system) { %>
                      <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">System</span>
                    <% } else { %>
                      <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Custom</span>
                    <% } %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <a href="/admin/users/roles/<%= role.role_id %>/users" class="text-blue-600 hover:text-blue-900" title="View Users">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </a>
                    <a href="/admin/users/roles/<%= role.role_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit Role">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </a>
                    <% if (!role.is_system) { %>
                      <button
                        onclick="confirmDelete('<%= role.role_id %>', '<%= role.role_name %>')"
                        class="text-red-600 hover:text-red-900"
                        title="Delete Role"
                        <%= role.user_count > 0 ? 'disabled' : '' %>
                        <%= role.user_count > 0 ? 'style="opacity: 0.5; cursor: not-allowed;"' : '' %>
                      >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    <% } %>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No roles found</h3>
      </div>
    <% } %>
  </div>
</div>

<!-- Role Information -->
<div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
  <!-- Admin Role -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-purple-600 text-white p-4">
      <h2 class="text-xl font-semibold">Admin Role</h2>
    </div>
    <div class="p-6">
      <p class="text-gray-700 mb-4">Administrators have full access to the system, including:</p>
      <ul class="list-disc list-inside space-y-2 text-gray-700">
        <li>User management (create, edit, delete users)</li>
        <li>Test management (create, edit, delete tests)</li>
        <li>Question management (create, edit, delete questions)</li>
        <li>View all reports and statistics</li>
        <li>System settings and configuration</li>
      </ul>
    </div>
  </div>

  <!-- Teacher Role -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-green-600 text-white p-4">
      <h2 class="text-xl font-semibold">Teacher Role</h2>
    </div>
    <div class="p-6">
      <p class="text-gray-700 mb-4">Teachers have limited administrative access:</p>
      <ul class="list-disc list-inside space-y-2 text-gray-700">
        <li>Create and manage their own tests</li>
        <li>Create and manage questions</li>
        <li>View student results for their tests</li>
        <li>Create and manage groups</li>
        <li>Generate reports for their tests</li>
      </ul>
    </div>
  </div>

  <!-- Student Role -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Student Role</h2>
    </div>
    <div class="p-6">
      <p class="text-gray-700 mb-4">Students have basic access to the system:</p>
      <ul class="list-disc list-inside space-y-2 text-gray-700">
        <li>View and take available tests</li>
        <li>View their own test results</li>
        <li>Manage their own profile</li>
        <li>View their notifications</li>
        <li>Cannot access admin features</li>
      </ul>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the role <span id="roleName" class="font-semibold"></span>? This action cannot be undone.</p>
    <div class="flex justify-end space-x-3">
      <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition">Cancel</button>
      <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">Delete</button>
    </div>
  </div>
</div>

<script>
  function confirmDelete(roleId, roleName) {
    document.getElementById('roleName').textContent = roleName;
    document.getElementById('deleteModal').classList.remove('hidden');

    document.getElementById('cancelDelete').onclick = function() {
      document.getElementById('deleteModal').classList.add('hidden');
    };

    document.getElementById('confirmDelete').onclick = function() {
      deleteRole(roleId);
    };
  }

  function deleteRole(roleId) {
    fetch(`/admin/users/roles/${roleId}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      document.getElementById('deleteModal').classList.add('hidden');

      if (data.success) {
        // Show success message
        showToast('success', data.message);
        // Reload page after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        // Show error message
        showToast('error', data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      document.getElementById('deleteModal').classList.add('hidden');
      showToast('error', 'An error occurred while deleting the role');
    });
  }

  function showToast(type, message) {
    const toast = document.createElement('div');
    toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-md text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 3000);
  }
</script>