<div class="py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">User Details</h1>
      <div class="flex space-x-3">
        <a href="/admin/users/<%= user.id %>/edit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit User
        </a>
        <a href="/admin/users" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Users
        </a>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 mt-6">
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6 flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
          <% if (user.profile_image) { %>
            <img class="h-16 w-16 rounded-full" src="<%= user.profile_image %>" alt="<%= user.username %>">
          <% } else { %>
            <div class="h-16 w-16 rounded-full bg-purple-200 flex items-center justify-center">
              <span class="text-purple-600 text-xl font-semibold">
                <%= user.username.substring(0, 2).toUpperCase() %>
              </span>
            </div>
          <% } %>
        </div>
        <div class="ml-5">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            <%= user.username %>
            <% if (user.is_online) { %>
              <span class="inline-flex items-center ml-2 px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>
                Online
              </span>
            <% } %>
          </h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">
            <%= user.role.charAt(0).toUpperCase() + user.role.slice(1) %> Account
          </p>
        </div>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Full name</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= user.username %></dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Email address</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= user.email %></dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Role</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                <% if (user.role === 'admin') { %>
                  bg-red-100 text-red-800
                <% } else if (user.role === 'teacher') { %>
                  bg-blue-100 text-blue-800
                <% } else { %>
                  bg-green-100 text-green-800
                <% } %>
              ">
                <%= user.role.charAt(0).toUpperCase() + user.role.slice(1) %>
              </span>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Date of birth</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <% if (user.date_of_birth) { %>
                <% if (typeof formatDate === 'function') { %>
                  <%= formatDate(user.date_of_birth) %>
                <% } else { %>
                  <%
                    const dob = new Date(user.date_of_birth);
                    const day = String(dob.getDate()).padStart(2, '0');
                    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    const month = monthNames[dob.getMonth()];
                    const year = dob.getFullYear();
                  %>
                  <%= `${day}-${month}-${year}` %>
                <% } %>
              <% } else { %>
                Not provided
              <% } %>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Joined</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <% if (typeof formatDateTime === 'function') { %>
                <%= formatDateTime(user.created_at) %>
              <% } else { %>
                <%
                  const createdDate = new Date(user.created_at);
                  const day = String(createdDate.getDate()).padStart(2, '0');
                  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                  const month = monthNames[createdDate.getMonth()];
                  const year = createdDate.getFullYear();
                  const hours = String(createdDate.getHours()).padStart(2, '0');
                  const minutes = String(createdDate.getMinutes()).padStart(2, '0');
                  const seconds = String(createdDate.getSeconds()).padStart(2, '0');
                %>
                <%= `${day}-${month}-${year} ${hours}:${minutes}:${seconds}` %>
              <% } %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Last login</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <% if (user.last_login) { %>
                <% if (typeof formatDateTime === 'function') { %>
                  <%= formatDateTime(user.last_login) %>
                <% } else { %>
                  <%
                    const loginDate = new Date(user.last_login);
                    const day = String(loginDate.getDate()).padStart(2, '0');
                    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    const month = monthNames[loginDate.getMonth()];
                    const year = loginDate.getFullYear();
                    const hours = String(loginDate.getHours()).padStart(2, '0');
                    const minutes = String(loginDate.getMinutes()).padStart(2, '0');
                    const seconds = String(loginDate.getSeconds()).padStart(2, '0');
                  %>
                  <%= `${day}-${month}-${year} ${hours}:${minutes}:${seconds}` %>
                <% } %>
              <% } else { %>
                Never
              <% } %>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Bio</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <%= user.bio || 'No bio provided' %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- User Groups -->
    <div class="mt-8">
      <h2 class="text-lg font-medium text-gray-900">Groups</h2>
      <div class="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
        <% if (userGroups && userGroups.length > 0) { %>
          <div class="px-4 py-5 sm:px-6 flex flex-wrap gap-2">
            <% userGroups.forEach(group => { %>
              <a href="/admin/groups/<%= group.group_id %>" class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium
                <% if (group.is_admin) { %>
                  bg-purple-100 text-purple-800 hover:bg-purple-200
                <% } else if (group.is_system) { %>
                  bg-blue-100 text-blue-800 hover:bg-blue-200
                <% } else { %>
                  bg-green-100 text-green-800 hover:bg-green-200
                <% } %>
              ">
                <% if (group.is_admin) { %>
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                <% } %>
                <%= group.name %>
              </a>
            <% }); %>
          </div>
        <% } else { %>
          <div class="px-4 py-5 text-center text-sm text-gray-500">
            This user is not a member of any groups.
          </div>
        <% } %>
      </div>
    </div>

    <!-- User Stats -->
    <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Test Attempts -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Test Attempts
                </dt>
                <dd>
                  <div class="text-lg font-medium text-gray-900">
                    <%= user.test_count || 0 %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Exams Taken -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Exams Taken
                </dt>
                <dd>
                  <div class="text-lg font-medium text-gray-900">
                    <%= user.test_count || 0 %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Bookmarks -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Bookmarks
                </dt>
                <dd>
                  <div class="text-lg font-medium text-gray-900">
                    <%= user.bookmark_count || 0 %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Sessions -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Active Sessions
                </dt>
                <dd>
                  <div class="text-lg font-medium text-gray-900">
                    <%= user.active_sessions || 0 %>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8">
      <h2 class="text-lg font-medium text-gray-900">Recent Activity</h2>
      <div class="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
        <% if (activity && activity.length > 0) { %>
          <ul class="divide-y divide-gray-200">
            <% activity.forEach(log => { %>
              <li class="px-4 py-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center justify-center h-8 w-8 rounded-full
                      <% if (log.level === 'error') { %>
                        bg-red-100
                      <% } else if (log.level === 'warning') { %>
                        bg-yellow-100
                      <% } else { %>
                        bg-blue-100
                      <% } %>
                    ">
                      <span class="text-sm font-medium leading-none
                        <% if (log.level === 'error') { %>
                          text-red-800
                        <% } else if (log.level === 'warning') { %>
                          text-yellow-800
                        <% } else { %>
                          text-blue-800
                        <% } %>
                      ">
                        <%= log.level.charAt(0).toUpperCase() %>
                      </span>
                    </span>
                  </div>
                  <div class="ml-3 w-0 flex-1">
                    <div class="text-sm font-medium text-gray-900">
                      <%= log.operation %>
                    </div>
                    <div class="mt-1 text-sm text-gray-500">
                      <%= log.details ? log.details.substring(0, 100) + (log.details.length > 100 ? '...' : '') : 'No details' %>
                    </div>
                    <div class="mt-2 text-xs text-gray-400">
                      <% if (typeof formatDateTime === 'function') { %>
                        <%= formatDateTime(log.timestamp) %>
                      <% } else { %>
                        <%
                          const logDate = new Date(log.timestamp);
                          const day = String(logDate.getDate()).padStart(2, '0');
                          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                          const month = monthNames[logDate.getMonth()];
                          const year = logDate.getFullYear();
                          const hours = String(logDate.getHours()).padStart(2, '0');
                          const minutes = String(logDate.getMinutes()).padStart(2, '0');
                          const seconds = String(logDate.getSeconds()).padStart(2, '0');
                        %>
                        <%= `${day}-${month}-${year} ${hours}:${minutes}:${seconds}` %>
                      <% } %>
                    </div>
                  </div>
                </div>
              </li>
            <% }); %>
          </ul>
        <% } else { %>
          <div class="px-4 py-5 text-center text-sm text-gray-500">
            No recent activity found for this user.
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

