<%- contentFor('body') %>
  <div class="bg-white/90 backdrop-blur-sm p-8 rounded-lg shadow-lg w-full max-w-md border border-white/20 auth-container">
    <h1 class="text-2xl font-bold text-center mb-8 text-gray-800">Login</h1>

    <% if (error) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= error %></span>

        <% if (typeof canForceLogout !== 'undefined' && canForceLogout === true && typeof userId !== 'undefined') { %>
          <div class="mt-3">
            <form action="/force-logout/<%= userId %>" method="POST">
              <button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-md text-sm font-medium transition-all duration-200">
                Force Logout Other Sessions and Continue
              </button>
            </form>
          </div>
        <% } %>
      </div>
    <% } %>

    <% if (typeof flashSuccess !== 'undefined' && flashSuccess) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
      </div>
    <% } %>

    <form action="/login" method="POST" class="space-y-6" id="loginForm">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700">Email or Username</label>
        <div class="relative">
          <input type="text" id="email" name="email" required autocomplete="username"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
          <div id="email-feedback" class="mt-1 text-sm text-gray-600" style="min-height: 20px; display: block;"></div>
        </div>
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
        <div class="relative">
          <input type="password" id="password" name="password" required autocomplete="current-password"
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
          <span class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer toggle-password" title="Toggle password visibility">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
            </svg>
          </span>
        </div>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input id="remember_me" name="remember_me" type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
          <label for="remember_me" class="ml-2 block text-sm text-gray-900">
            Remember me
          </label>
        </div>
        <div class="text-sm">
          <a href="/forgot-password" class="font-medium text-indigo-600 hover:text-indigo-500">
            Forgot your password?
          </a>
        </div>
      </div>

      <div>
        <button type="submit" id="loginButton"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          Sign in
        </button>
      </div>
    </form>

    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Don't have an account?
        <a href="/register" class="font-medium text-indigo-600 hover:text-indigo-500">
          Register
        </a>
      </p>
    </div>

    <div class="mt-4 text-center">
      <p class="text-sm text-gray-600">
        Want to try a demo account?
        <a href="/demo-login" class="font-medium text-indigo-600 hover:text-indigo-500">
          Use demo login
        </a>
      </p>
    </div>
  </div>

<%- contentFor('script') %>
<script>
  // Login page specific scripts can go here
</script>

<%- contentFor('style') %>
<style>
  /* Ensure login form is above any background elements */
  .auth-container {
    position: relative;
    z-index: 10;
  }
</style>