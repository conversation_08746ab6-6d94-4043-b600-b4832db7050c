<%- contentFor('body') %>
    <div class="bg-white/90 backdrop-blur-sm p-8 rounded-lg shadow-lg w-full max-w-5xl border border-white/20 auth-container">
        <h1 class="text-2xl font-bold text-center mb-8 text-gray-800"><%= __('auth.register') %></h1>

        <% if (error) { %>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><%= error %></span>
            </div>
        <% } %>

        <form action="/register" method="POST" class="space-y-6" id="registerForm">
            <!-- Form Progress Indicator -->
            <div class="mb-8">
                <div class="flex justify-between">
                    <div class="step-indicator active cursor-pointer" data-step="1">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-indigo-600 text-white flex items-center justify-center font-semibold">1</div>
                            <span class="ml-2 text-sm font-medium">Account Info</span>
                        </div>
                    </div>
                    <div class="step-indicator cursor-pointer" data-step="2">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center font-semibold">2</div>
                            <span class="ml-2 text-sm font-medium">Personal Details</span>
                        </div>
                    </div>
                    <div class="step-indicator cursor-pointer" data-step="3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center font-semibold">3</div>
                            <span class="ml-2 text-sm font-medium">Academic Info</span>
                        </div>
                    </div>
                    <div class="step-indicator cursor-pointer" data-step="4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center font-semibold">4</div>
                            <span class="ml-2 text-sm font-medium">Confirmation</span>
                        </div>
                    </div>
                </div>
                <div class="mt-2 h-1 bg-gray-200 rounded-full">
                    <div class="h-full bg-indigo-600 rounded-full" id="progress-bar" style="width: 25%;"></div>
                </div>
            </div>
            <!-- Profile Image Upload with Base64 -->
            <!-- Step 1: Account Info -->
            <div id="step1" class="form-step active">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700"><%= __('auth.username') %></label>
                    <div class="relative">
                        <input type="text" id="username" name="username" required autocomplete="username" minlength="3"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <div id="username-feedback" class="mt-1 text-sm text-gray-600" style="min-height: 20px; display: block;"></div>
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700"><%= __('auth.email') %></label>
                    <div class="relative">
                        <input type="email" id="email" name="email" required autocomplete="email"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <div id="email-feedback" class="mt-1 text-sm text-gray-600" style="min-height: 20px; display: block;"></div>
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700"><%= __('auth.password') %></label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required minlength="8" autocomplete="new-password"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <span class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" title="Toggle password visibility" data-target="password">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                        </span>
                    </div>
                    <div class="mt-2">
                        <div class="flex space-x-1">
                            <div id="strength-bar-1" class="h-1 w-1/4 bg-gray-200 rounded-sm"></div>
                            <div id="strength-bar-2" class="h-1 w-1/4 bg-gray-200 rounded-sm"></div>
                            <div id="strength-bar-3" class="h-1 w-1/4 bg-gray-200 rounded-sm"></div>
                            <div id="strength-bar-4" class="h-1 w-1/4 bg-gray-200 rounded-sm"></div>
                        </div>
                        <p id="password-strength-text" class="text-xs mt-1 text-gray-500">Password strength</p>
                    </div>
                </div>

                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700"><%= __('auth.confirmPassword') %></label>
                    <div class="relative">
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="8" autocomplete="new-password"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <span class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" title="Toggle password visibility" data-target="confirm_password">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                            </svg>
                        </span>
                    </div>
                    <div id="password-match-feedback" class="mt-1 text-sm text-gray-600" style="min-height: 20px; display: block;"></div>
                </div>

                <div>
                    <label for="date_of_birth" class="block text-sm font-medium text-gray-700"><%= __('auth.dateOfBirth') %></label>
                    <input type="text" id="date_of_birth" name="date_of_birth" required autocomplete="bday"
                        placeholder="DD-MMM-YYYY (e.g., 15-Jan-2000)"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 datepicker">
                    <small class="text-gray-500">Format: DD-MMM-YYYY (e.g., 15-Jan-2000)</small>
                    <div id="dob-feedback" class="mt-1 text-sm" style="min-height: 20px;"></div>
                </div>
                </div>

                <div class="mt-6 flex justify-between">
                    <div></div> <!-- Empty div for spacing -->
                    <button type="button" class="next-step px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Next
                    </button>
                </div>
            </div>

            <!-- Step 2: Personal Details -->
            <div id="step2" class="form-step">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="md:col-span-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Profile Image (Optional)</label>
                        <div class="mt-1 flex items-center">
                            <div class="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mr-4 border-2 border-gray-300 overflow-hidden">
                                <img id="preview-image" alt="Preview" class="w-20 h-20 rounded-full object-cover hidden">
                                <span id="preview-initials" class="text-xl font-bold text-gray-400">U</span>
                            </div>
                            <div>
                                <input type="file" id="profile_image_input" class="hidden" accept="image/jpeg,image/png,image/gif">
                                <div class="flex flex-col space-y-2">
                                    <button type="button" onclick="document.getElementById('profile_image_input').click();" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded cursor-pointer inline-flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        Upload Image
                                    </button>
                                    <button type="button" id="clear_image_button" onclick="clearProfileImage();" class="text-gray-600 hover:text-red-600 text-sm flex items-center hidden">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        Remove Image
                                    </button>
                                    <input type="hidden" id="profile_image_base64" name="profile_image_base64">
                                    <p class="text-sm text-gray-500">Max size: 5MB. Formats: JPG, PNG, GIF</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Bio -->
                    <div class="md:col-span-3">
                        <label for="bio" class="block text-sm font-medium text-gray-700">Bio (Optional)</label>
                        <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Tell us a bit about yourself" maxlength="500" onkeyup="document.getElementById('bio-char-count').textContent = this.value.length;" required></textarea>
                        <div class="flex justify-end">
                            <span id="bio-char-count" class="text-xs text-gray-500">0</span>
                            <span class="text-xs text-gray-500">/500</span>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-between">
                    <button type="button" class="prev-step px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Previous
                    </button>
                    <button type="button" class="next-step px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Next
                    </button>
                </div>
            </div>

            <!-- Step 3: Academic Info -->
            <div id="step3" class="form-step">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Current Institution -->
                    <div>
                        <label for="institution" class="block text-sm font-medium text-gray-700">Current Institution</label>
                        <input type="text" id="institution" name="institution" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <!-- Grade/Year -->
                    <div>
                        <label for="grade" class="block text-sm font-medium text-gray-700">Grade/Year</label>
                        <input type="text" id="grade" name="grade" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <!-- Field of Study -->
                    <div>
                        <label for="field_of_study" class="block text-sm font-medium text-gray-700">Field of Study</label>
                        <input type="text" id="field_of_study" name="field_of_study" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <!-- Preferred Subjects -->
                    <div>
                        <label for="preferred_subjects" class="block text-sm font-medium text-gray-700">Preferred Subjects</label>
                        <input type="text" id="preferred_subjects" name="preferred_subjects" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="e.g. Mathematics, Science, English">
                    </div>

                    <!-- Language Preference -->
                    <div>
                        <label for="language_preference" class="block text-sm font-medium text-gray-700">Language Preference</label>
                        <select id="language_preference" name="language_preference" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="English" selected>English</option>
                            <option value="Hindi">Hindi</option>
                            <option value="Punjabi">Punjabi</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6 flex justify-between">
                    <button type="button" class="prev-step px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Previous
                    </button>
                    <button type="button" class="next-step px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Next
                    </button>
                </div>
            </div>

            <!-- Step 4: Confirmation -->
            <div id="step4" class="form-step">
                <div class="grid grid-cols-1 gap-6">
                    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Registration Confirmation</h3>
                        <p class="text-gray-600 mb-4">Please review your information before submitting. All fields marked as required must be filled correctly.</p>

                        <!-- Form Preview Section -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-800 mb-2">Form Summary</h4>
                            <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-md p-4 bg-white">
                                <!-- Account Info Preview -->
                                <div class="mb-4">
                                    <h5 class="font-medium text-indigo-600 mb-2 pb-1 border-b border-gray-200">Account Information</h5>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-700">Username:</span>
                                            <span id="preview-username" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Email:</span>
                                            <span id="preview-email" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Date of Birth:</span>
                                            <span id="preview-dob" class="ml-1 text-gray-600"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Personal Details Preview -->
                                <div class="mb-4">
                                    <h5 class="font-medium text-indigo-600 mb-2 pb-1 border-b border-gray-200">Personal Details</h5>
                                    <div class="grid grid-cols-1 gap-2 text-sm">
                                        <div class="flex items-start">
                                            <span class="font-medium text-gray-700 mr-2">Profile Image:</span>
                                            <div class="flex items-center">
                                                <div id="preview-profile-container" class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                                                    <img id="preview-profile-image" alt="Profile" class="w-10 h-10 rounded-full object-cover hidden">
                                                    <span id="preview-profile-initials" class="text-sm font-bold text-gray-400">U</span>
                                                </div>
                                                <span class="text-xs text-gray-500">(Optional)</span>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Bio:</span>
                                            <p id="preview-bio" class="mt-1 text-gray-600 text-xs italic">Not provided</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Academic Info Preview -->
                                <div>
                                    <h5 class="font-medium text-indigo-600 mb-2 pb-1 border-b border-gray-200">Academic Information</h5>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-700">Institution:</span>
                                            <span id="preview-institution" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Grade/Year:</span>
                                            <span id="preview-grade" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Field of Study:</span>
                                            <span id="preview-field" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Preferred Subjects:</span>
                                            <span id="preview-subjects" class="ml-1 text-gray-600"></span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-700">Language Preference:</span>
                                            <span id="preview-language" class="ml-1 text-gray-600"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-xs text-gray-600 bg-blue-50 p-3 rounded-md mb-6">
                            <p class="font-medium">Important:</p>
                            <p>Your account will need to be approved by an administrator before you can log in. You will receive a notification once your account is approved.</p>
                        </div>

                        <div class="mb-4 text-center">
                            <button type="button" onclick="updateFormPreview()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Refresh Preview
                            </button>
                        </div>

                        <div class="mt-6 flex justify-between">
                            <button type="button" class="prev-step px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                Previous
                            </button>
                            <button type="submit" id="registerButton"
                                class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <%= __('auth.register') %>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                <%= __('auth.alreadyHaveAccount') %>
                <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                    <%= __('auth.loginHere') %>
                </a>
            </p>
        </div>
    </div>

<%- contentFor('script') %>
<!-- Form validation is handled by form-validation.js -->
<!-- Multi-step form is handled by multi-step-form.js -->

<!-- Image upload preview script with Base64 encoding -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const fileInput = document.getElementById('profile_image_input');
    const base64Input = document.getElementById('profile_image_base64');
    const previewImage = document.getElementById('preview-image');
    const previewInitials = document.getElementById('preview-initials');
    const usernameInput = document.getElementById('username');
    const bioInput = document.getElementById('bio');
    const bioCharCount = document.getElementById('bio-char-count');
    const registerForm = document.getElementById('registerForm');
    const registerButton = document.getElementById('registerButton');
    const clearImageButton = document.getElementById('clear_image_button');

    // Set initial bio character count
    if (bioInput && bioCharCount) {
        bioInput.addEventListener('input', function() {
            bioCharCount.textContent = this.value.length;
        });
        
        // Initialize character count
        bioCharCount.textContent = bioInput.value.length;
    }

    // Form fields for preview
    const emailInput = document.getElementById('email');
    const dobInput = document.getElementById('date_of_birth');
    const institutionInput = document.getElementById('institution');
    const gradeInput = document.getElementById('grade');
    const fieldInput = document.getElementById('field_of_study');
    const subjectsInput = document.getElementById('preferred_subjects');
    const languageInput = document.getElementById('language_preference');

    // Preview elements
    const previewUsername = document.getElementById('preview-username');
    const previewEmail = document.getElementById('preview-email');
    const previewDob = document.getElementById('preview-dob');
    const previewBio = document.getElementById('preview-bio');
    const previewInstitution = document.getElementById('preview-institution');
    const previewGrade = document.getElementById('preview-grade');
    const previewField = document.getElementById('preview-field');
    const previewSubjects = document.getElementById('preview-subjects');
    const previewLanguage = document.getElementById('preview-language');
    const previewProfileImage = document.getElementById('preview-profile-image');
    const previewProfileInitials = document.getElementById('preview-profile-initials');

    // Update character count for bio
    function updateCharCount() {
        if (bioInput && bioCharCount) {
            const count = bioInput.value.length;
            bioCharCount.textContent = count;
            if (count > 500) {
                bioCharCount.classList.add('text-red-500');
            } else {
                bioCharCount.classList.remove('text-red-500');
            }
        }
    }

    // Initialize character count
    updateCharCount();

    // Add event listener for bio input
    if (bioInput) {
        bioInput.addEventListener('input', function() {
            updateCharCount();

            // Update preview
            if (previewBio) {
                previewBio.textContent = this.value || 'Not provided';
            }
        });
    }

    // Define the updateFormPreview function globally so it can be called from HTML
    window.updateFormPreview = function() {
        // Update account info preview
        if (previewUsername && usernameInput) {
            previewUsername.textContent = usernameInput.value || 'Not provided';
        }

        if (previewEmail && emailInput) {
            previewEmail.textContent = emailInput.value || 'Not provided';
        }

        if (previewDob && dobInput) {
            previewDob.textContent = dobInput.value || 'Not provided';
        }

        // Update personal details preview
        if (previewBio && bioInput) {
            previewBio.textContent = bioInput.value || 'Not provided';
        }

        // Update academic info preview
        if (previewInstitution && institutionInput) {
            previewInstitution.textContent = institutionInput.value || 'Not provided';
        }

        if (previewGrade && gradeInput) {
            previewGrade.textContent = gradeInput.value || 'Not provided';
        }

        if (previewField && fieldInput) {
            previewField.textContent = fieldInput.value || 'Not provided';
        }

        if (previewSubjects && subjectsInput) {
            previewSubjects.textContent = subjectsInput.value || 'Not provided';
        }

        if (previewLanguage && languageInput) {
            const selectedOption = languageInput.options[languageInput.selectedIndex];
            previewLanguage.textContent = selectedOption ? selectedOption.text : 'Not provided';
        }
    };
    
    // For backward compatibility, define updatePreviewFields as an alias
    window.updatePreviewFields = window.updateFormPreview;

    // Handle file input change for image preview and Base64 conversion
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            handleImageUpload(this);
        });
    }

    function handleImageUpload(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Image file is too large. Maximum size is 5MB.');
                input.value = '';
                return;
            }

            // Check file type
            if (!file.type.match('image/jpeg') && !file.type.match('image/png') && !file.type.match('image/gif')) {
                alert('Only JPG, PNG, and GIF images are allowed.');
                input.value = '';
                return;
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                // Set the preview image
                const previewImage = document.getElementById('preview-image');
                const previewInitials = document.getElementById('preview-initials');
                const base64Input = document.getElementById('profile_image_base64');
                const clearButton = document.getElementById('clear_image_button');
                const previewProfileImage = document.getElementById('preview-profile-image');
                const previewProfileInitials = document.getElementById('preview-profile-initials');

                if (previewImage) {
                    previewImage.src = e.target.result;
                    previewImage.classList.remove('hidden');
                }

                if (previewInitials) {
                    previewInitials.classList.add('hidden');
                }

                // Store the Base64 data
                if (base64Input) {
                    base64Input.value = e.target.result;
                }

                // Show clear button
                if (clearButton) {
                    clearButton.classList.remove('hidden');
                }

                // Update confirmation preview
                if (previewProfileImage) {
                    previewProfileImage.src = e.target.result;
                    previewProfileImage.classList.remove('hidden');

                    if (previewProfileInitials) {
                        previewProfileInitials.classList.add('hidden');
                    }
                }
            };

            reader.readAsDataURL(file);
        }
    }

    // Add event listeners to the step navigation buttons
    const allStepButtons = document.querySelectorAll('.next-step, .prev-step, .step-indicator');
    allStepButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Use setTimeout to ensure the DOM is updated before we update the preview
            setTimeout(updateFormPreview, 100);
        });
    });

    // Add a refresh preview button click event
    const refreshPreviewButton = document.querySelector('button[onclick="updateFormPreview()"]');
    if (refreshPreviewButton) {
        refreshPreviewButton.addEventListener('click', updateFormPreview);
    }

    // Call updateFormPreview when reaching step 4
    document.querySelectorAll('.step-indicator').forEach(indicator => {
        indicator.addEventListener('click', function() {
            const step = this.getAttribute('data-step');
            if (step === '4') {
                setTimeout(updateFormPreview, 100);
            }
        });
    });

    // Initial form preview
    updateFormPreview();
});

function clearProfileImage() {
    const fileInput = document.getElementById('profile_image_input');
    const base64Input = document.getElementById('profile_image_base64');
    const previewImage = document.getElementById('preview-image');
    const previewInitials = document.getElementById('preview-initials');
    const clearButton = document.getElementById('clear_image_button');
    const previewProfileImage = document.getElementById('preview-profile-image');
    const previewProfileInitials = document.getElementById('preview-profile-initials');

    // Clear file input and base64 data
    if (fileInput) fileInput.value = '';
    if (base64Input) base64Input.value = '';

    // Hide image preview and show initials
    if (previewImage) {
        previewImage.classList.add('hidden');
        previewImage.src = '';
    }
    if (previewInitials) previewInitials.classList.remove('hidden');

    // Update confirmation preview
    if (previewProfileImage) {
        previewProfileImage.classList.add('hidden');
        previewProfileImage.src = '';
    }
    if (previewProfileInitials) previewProfileInitials.classList.remove('hidden');

    // Hide clear button
    if (clearButton) clearButton.classList.add('hidden');
}
</script>
