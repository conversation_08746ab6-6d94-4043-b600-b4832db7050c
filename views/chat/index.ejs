<%- include('../partials/header') %>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row gap-6">
        <!-- Sidebar -->
        <div class="w-full md:w-1/3 lg:w-1/4">
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div class="bg-purple-600 text-white p-4">
                    <h2 class="text-xl font-semibold">Chats</h2>
                </div>

                <!-- Tabs -->
                <div class="flex border-b">
                    <button id="privateTabBtn" class="flex-1 py-3 px-4 font-medium text-center border-b-2 border-purple-600 text-purple-600">
                        Private
                    </button>
                    <button id="groupTabBtn" class="flex-1 py-3 px-4 font-medium text-center border-b-2 border-transparent hover:text-purple-600">
                        Groups
                    </button>
                </div>

                <!-- Private Chats List -->
                <div id="privateChats" class="overflow-y-auto max-h-96">
                    <% if (privateChats && privateChats.length > 0) { %>
                        <div class="divide-y">
                            <% privateChats.forEach(chat => { %>
                                <a href="/chat/user/<%= chat.user_id %>" class="block p-4 hover:bg-gray-50 transition">
                                    <div class="flex items-center">
                                        <div class="relative flex-shrink-0">
                                            <img src="<%= chat.profile_image || '/img/default-avatar.png' %>" alt="<%= chat.username %>" class="w-12 h-12 rounded-full object-cover">
                                            <% if (chat.unread_count > 0) { %>
                                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                    <%= chat.unread_count %>
                                                </span>
                                            <% } %>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-start">
                                                <h3 class="font-medium text-gray-900"><%= chat.username %></h3>
                                                <% if (chat.last_message_time) { %>
                                                    <span class="text-xs text-gray-500">
                                                        <%= new Date(chat.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                                    </span>
                                                <% } %>
                                            </div>
                                            <p class="text-sm text-gray-500 truncate"><%= chat.last_message || 'No messages yet' %></p>
                                        </div>
                                    </div>
                                </a>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="p-4 text-center text-gray-500">
                            No private chats yet
                        </div>
                    <% } %>
                </div>

                <!-- Groups List -->
                <div id="groupChats" class="hidden overflow-y-auto max-h-96">
                    <% if (groups && groups.length > 0) { %>
                        <div class="divide-y">
                            <% groups.forEach(group => { %>
                                <a href="/chat/group/<%= group.group_id %>" class="block p-4 hover:bg-gray-50 transition">
                                    <div class="flex items-center">
                                        <div class="relative flex-shrink-0 bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center">
                                            <span class="text-purple-600 font-bold text-lg"><%= group.name.charAt(0).toUpperCase() %></span>
                                            <% if (group.unread_count > 0) { %>
                                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                    <%= group.unread_count %>
                                                </span>
                                            <% } %>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex justify-between items-start">
                                                <h3 class="font-medium text-gray-900"><%= group.name %></h3>
                                                <% if (group.last_message_time) { %>
                                                    <span class="text-xs text-gray-500">
                                                        <%= new Date(group.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                                    </span>
                                                <% } %>
                                            </div>
                                            <% if (group.last_message) { %>
                                                <p class="text-sm text-gray-500 truncate">
                                                    <% if (group.last_sender) { %>
                                                        <span class="font-medium"><%= group.last_sender %>:</span>
                                                    <% } %>
                                                    <%= group.last_message %>
                                                </p>
                                            <% } else { %>
                                                <p class="text-sm text-gray-500">No messages yet</p>
                                            <% } %>
                                        </div>
                                    </div>
                                </a>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="p-4 text-center text-gray-500">
                            No groups yet
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Groups management moved to admin/users section -->
        </div>

        <!-- Main Content -->
        <div class="w-full md:w-2/3 lg:w-3/4">
            <div class="bg-white rounded-lg shadow-md overflow-hidden h-[600px] flex flex-col">
                <div class="bg-purple-600 text-white p-4">
                    <h2 class="text-xl font-semibold">Welcome to Chat</h2>
                </div>

                <div class="flex-1 flex items-center justify-center p-8">
                    <div class="text-center">
                        <div class="mb-4">
                            <svg class="w-16 h-16 mx-auto text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">Select a chat to start messaging</h3>
                        <p class="text-gray-500 mb-6">Choose from your private chats or group conversations</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const privateTabBtn = document.getElementById('privateTabBtn');
        const groupTabBtn = document.getElementById('groupTabBtn');
        const privateChats = document.getElementById('privateChats');
        const groupChats = document.getElementById('groupChats');

        privateTabBtn.addEventListener('click', function() {
            privateTabBtn.classList.add('border-purple-600', 'text-purple-600');
            groupTabBtn.classList.remove('border-purple-600', 'text-purple-600');
            privateChats.classList.remove('hidden');
            groupChats.classList.add('hidden');
        });

        groupTabBtn.addEventListener('click', function() {
            groupTabBtn.classList.add('border-purple-600', 'text-purple-600');
            privateTabBtn.classList.remove('border-purple-600', 'text-purple-600');
            groupChats.classList.remove('hidden');
            privateChats.classList.add('hidden');
        });
    });
</script>

<%- include('../partials/footer') %>
