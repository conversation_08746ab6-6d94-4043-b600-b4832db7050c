<%- include('../partials/header') %>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md overflow-hidden h-[700px] flex flex-col">
        <!-- Chat Header -->
        <div class="bg-purple-600 text-white p-4 flex items-center justify-between">
            <div class="flex items-center">
                <a href="/chat" class="mr-3 text-white hover:text-purple-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <% if (user.profile_image) { %>
                    <img src="<%= user.profile_image %>" alt="<%= user.username %>" class="w-10 h-10 rounded-full object-cover">
                <% } else { %>
                    <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                        <span class="text-xl font-bold text-white"><%= user.username.charAt(0).toUpperCase() %></span>
                    </div>
                <% } %>
                <div class="ml-3">
                    <h2 class="text-xl font-semibold"><%= user.username %></h2>
                    <div class="text-sm text-purple-200">
                        <span class="user-status online">Online</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="messageContainer" class="flex-1 overflow-y-auto p-4 space-y-4">
            <% if (messages && messages.length > 0) { %>
                <% messages.forEach(message => { %>
                    <div class="message <%= message.sender_id == userId ? 'sent' : 'received' %>">
                        <div class="flex <%= message.sender_id == userId ? 'justify-end' : 'items-start' %>">
                            <% if (message.sender_id != userId) { %>
                                <% if (message.sender_image) { %>
                                    <img src="<%= message.sender_image %>" alt="<%= message.sender_name %>" class="w-8 h-8 rounded-full object-cover mr-2">
                                <% } else { %>
                                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-2">
                                        <span class="text-sm font-bold text-white"><%= message.sender_name.charAt(0).toUpperCase() %></span>
                                    </div>
                                <% } %>
                            <% } %>

                            <div class="max-w-[70%]">
                                <div class="px-4 py-2 rounded-lg <%= message.sender_id == userId ? 'bg-purple-600 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none' %>">
                                    <% if (message.attachment_url) { %>
                                        <% if (message.attachment_type && message.attachment_type.startsWith('image/')) { %>
                                            <img src="<%= message.attachment_url %>" alt="Attachment" class="max-w-full rounded mb-2">
                                        <% } else if (message.attachment_type && message.attachment_type.startsWith('video/')) { %>
                                            <video controls class="max-w-full rounded mb-2">
                                                <source src="<%= message.attachment_url %>" type="<%= message.attachment_type %>">
                                                Your browser does not support the video tag.
                                            </video>
                                        <% } else if (message.attachment_type && message.attachment_type.startsWith('audio/')) { %>
                                            <audio controls class="max-w-full mb-2">
                                                <source src="<%= message.attachment_url %>" type="<%= message.attachment_type %>">
                                                Your browser does not support the audio tag.
                                            </audio>
                                        <% } else { %>
                                            <div class="bg-gray-200 p-2 rounded flex items-center mb-2">
                                                <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <a href="<%= message.attachment_url %>" target="_blank" class="text-blue-600 hover:underline">
                                                    Attachment
                                                </a>
                                            </div>
                                        <% } %>
                                    <% } %>

                                    <p><%= message.message %></p>
                                </div>
                                <div class="text-xs text-gray-500 mt-1 <%= message.sender_id == userId ? 'text-right' : '' %>">
                                    <%= new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="text-center text-gray-500 my-8">
                    No messages yet. Start the conversation!
                </div>
            <% } %>
        </div>

        <!-- Message Input -->
        <div class="border-t p-4">
            <form id="messageForm" class="flex items-center">
                <button type="button" id="attachmentBtn" class="p-2 rounded-full text-gray-500 hover:text-purple-600 hover:bg-purple-100">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                </button>
                <input type="file" id="fileInput" class="hidden">
                <input type="text" id="messageInput" placeholder="Type a message..." class="flex-1 border rounded-full py-2 px-4 mx-2 focus:outline-none focus:ring-2 focus:ring-purple-600">
                <button type="submit" class="p-2 bg-purple-600 text-white rounded-full hover:bg-purple-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </form>
            <div id="attachmentPreview" class="hidden mt-2 p-2 bg-gray-100 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span id="attachmentName" class="text-sm truncate max-w-xs"></span>
                    </div>
                    <button type="button" id="removeAttachment" class="text-red-500 hover:text-red-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const socket = io({
            auth: {
                sessionId: '<%= locals.sessionID || "" %>'
            }
        });

        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const messageContainer = document.getElementById('messageContainer');
        const attachmentBtn = document.getElementById('attachmentBtn');
        const fileInput = document.getElementById('fileInput');
        const attachmentPreview = document.getElementById('attachmentPreview');
        const attachmentName = document.getElementById('attachmentName');
        const removeAttachment = document.getElementById('removeAttachment');

        let currentFile = null;

        // Scroll to bottom of messages
        function scrollToBottom() {
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }

        // Initial scroll
        scrollToBottom();

        // Handle form submission
        messageForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const message = messageInput.value.trim();
            if (!message && !currentFile) return;

            let attachmentUrl = null;
            let attachmentType = null;

            // Upload file if present
            if (currentFile) {
                const formData = new FormData();
                formData.append('file', currentFile);

                try {
                    const response = await fetch('/chat/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const data = await response.json();

                    if (data.success) {
                        attachmentUrl = data.file.url;
                        attachmentType = data.file.type;
                    } else {
                        console.error('File upload failed:', data.message);
                        return;
                    }
                } catch (error) {
                    console.error('Error uploading file:', error);
                    return;
                }
            }

            // Send message via socket
            socket.emit('message:private', {
                recipientId: <%= user.id %>,
                message,
                attachmentUrl,
                attachmentType
            });

            // Clear input and attachment
            messageInput.value = '';
            clearAttachment();

            // Focus input
            messageInput.focus();
        });

        // Handle attachment button
        attachmentBtn.addEventListener('click', function() {
            fileInput.click();
        });

        // Handle file selection
        fileInput.addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                currentFile = fileInput.files[0];
                attachmentName.textContent = currentFile.name;
                attachmentPreview.classList.remove('hidden');
            }
        });

        // Handle remove attachment
        removeAttachment.addEventListener('click', clearAttachment);

        // Clear attachment
        function clearAttachment() {
            currentFile = null;
            fileInput.value = '';
            attachmentPreview.classList.add('hidden');
        }

        // Handle incoming messages
        socket.on('message:new', function(data) {
            if ((data.senderId === <%= userId %> && data.recipientId === <%= user.id %>) ||
                (data.senderId === <%= user.id %> && data.recipientId === <%= userId %>)) {

                addMessageToUI(data);
                scrollToBottom();

                // Mark as read
                socket.emit('message:read', {
                    messageId: data.id
                });
            }
        });

        // Handle message sent confirmation
        socket.on('message:sent', function(data) {
            if (data.recipientId === <%= user.id %>) {
                // Message was sent successfully
                console.log('Message sent:', data);
            }
        });

        // Handle errors
        socket.on('error', function(data) {
            console.error('Socket error:', data.message);
            alert(data.message);
        });

        // Add message to UI
        function addMessageToUI(message) {
            const isSent = message.senderId === <%= userId %>;

            const messageEl = document.createElement('div');
            messageEl.className = `message ${isSent ? 'sent' : 'received'}`;

            let attachmentHtml = '';
            if (message.attachmentUrl) {
                if (message.attachmentType && message.attachmentType.startsWith('image/')) {
                    attachmentHtml = `<img src="${message.attachmentUrl}" alt="Attachment" class="max-w-full rounded mb-2">`;
                } else if (message.attachmentType && message.attachmentType.startsWith('video/')) {
                    attachmentHtml = `
                        <video controls class="max-w-full rounded mb-2">
                            <source src="${message.attachmentUrl}" type="${message.attachmentType}">
                            Your browser does not support the video tag.
                        </video>
                    `;
                } else if (message.attachmentType && message.attachmentType.startsWith('audio/')) {
                    attachmentHtml = `
                        <audio controls class="max-w-full mb-2">
                            <source src="${message.attachmentUrl}" type="${message.attachmentType}">
                            Your browser does not support the audio tag.
                        </audio>
                    `;
                } else {
                    attachmentHtml = `
                        <div class="bg-gray-200 p-2 rounded flex items-center mb-2">
                            <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <a href="${message.attachmentUrl}" target="_blank" class="text-blue-600 hover:underline">
                                Attachment
                            </a>
                        </div>
                    `;
                }
            }

            messageEl.innerHTML = `
                <div class="flex ${isSent ? 'justify-end' : 'items-start'}">
                    ${!isSent ? (message.senderImage ? `<img src="${message.senderImage}" alt="${message.senderName}" class="w-8 h-8 rounded-full object-cover mr-2">` : `<div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-2"><span class="text-sm font-bold text-white">${message.senderName.charAt(0).toUpperCase()}</span></div>`) : ''}

                    <div class="max-w-[70%]">
                        <div class="px-4 py-2 rounded-lg ${isSent ? 'bg-purple-600 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none'}">
                            ${attachmentHtml}
                            <p>${message.message}</p>
                        </div>
                        <div class="text-xs text-gray-500 mt-1 ${isSent ? 'text-right' : ''}">
                            ${new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                    </div>
                </div>
            `;

            messageContainer.appendChild(messageEl);
        }
    });
</script>

<%- include('../partials/footer') %>
