<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= teacher.name %> - Curriculum Vitae</title>
    
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 15px;
            background: white;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20mm;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2563eb;
        }
        
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .header h2 {
            color: #666;
            margin: 8px 0;
            font-size: 18px;
            font-weight: normal;
            font-style: italic;
        }
        
        .header .department {
            color: #888;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        
        .section-title {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 10px 15px;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px dotted #ddd;
        }
        
        .contact-label {
            font-weight: bold;
            color: #2563eb;
            min-width: 100px;
            margin-right: 10px;
        }
        
        .contact-value {
            color: #333;
            flex: 1;
        }
        
        .experience-item, .education-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 4px solid #2563eb;
            border-radius: 0 5px 5px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .item-title {
            font-weight: bold;
            color: #2563eb;
            font-size: 16px;
        }
        
        .item-date {
            color: #666;
            font-size: 12px;
            background: #e3f2fd;
            padding: 3px 8px;
            border-radius: 12px;
            white-space: nowrap;
        }
        
        .item-subtitle {
            color: #666;
            font-style: italic;
            margin-bottom: 5px;
        }
        
        .item-description {
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .skill-category {
            background: #f1f5f9;
            padding: 12px;
            border-radius: 5px;
            border-left: 3px solid #2563eb;
        }
        
        .skill-category-title {
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .skill-list {
            color: #555;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .achievement-item {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 12px;
            margin-bottom: 10px;
            border-left: 4px solid #f59e0b;
            border-radius: 0 5px 5px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .achievement-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 5px;
        }
        
        .achievement-description {
            color: #78350f;
            font-size: 13px;
        }
        
        .timeline-container {
            position: relative;
            padding-left: 20px;
        }
        
        .timeline-container::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #2563eb, #1d4ed8);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 8px;
            width: 12px;
            height: 12px;
            background: #2563eb;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #2563eb;
        }
        
        .summary-text {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #0ea5e9;
            font-style: italic;
            color: #0c4a6e;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #666;
            font-size: 12px;
        }
        
        .page-break {
            page-break-after: always;
        }
        
        @media print {
            body { margin: 0; padding: 0; }
            .cv-container { box-shadow: none; padding: 15mm; }
            .page-break { page-break-after: always; }
        }
        
        /* Animation styles for web preview */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .section {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        .section:nth-child(1) { animation-delay: 0.1s; }
        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.3s; }
        .section:nth-child(4) { animation-delay: 0.4s; }
        .section:nth-child(5) { animation-delay: 0.5s; }
        .section:nth-child(6) { animation-delay: 0.6s; }
        
        @media print {
            .section { animation: none; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header Section -->
        <div class="header">
            <h1><%= teacher.name || 'Teacher Name' %></h1>
            <h2><%= teacher.designation || 'Teacher' %></h2>
            <div class="department"><%= teacher.department || 'Academic Department' %></div>
        </div>

        <!-- Professional Summary -->
        <div class="section">
            <div class="section-title">Professional Summary</div>
            <div class="summary-text">
                <%
                let summary = '';
                if (teacher.total_experience_years) {
                    summary += `Experienced educator with ${teacher.total_experience_years} years of professional experience`;
                    if (teacher.teaching_experience_years) {
                        summary += `, including ${teacher.teaching_experience_years} years in teaching`;
                    }
                    summary += '. ';
                }
                
                if (teacher.subjects_taught) {
                    summary += `Specialized in ${teacher.subjects_taught}. `;
                }
                
                summary += 'Committed to educational excellence and student development with a proven track record of academic achievement and professional growth.';
                %>
                <%= summary %>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="section">
            <div class="section-title">Contact Information</div>
            <div class="contact-grid">
                <% if (teacher.email) { %>
                <div class="contact-item">
                    <div class="contact-label">Email:</div>
                    <div class="contact-value"><%= teacher.email %></div>
                </div>
                <% } %>
                
                <% if (teacher.phone) { %>
                <div class="contact-item">
                    <div class="contact-label">Phone:</div>
                    <div class="contact-value"><%= teacher.phone %></div>
                </div>
                <% } %>
                
                <% if (teacher.employee_id) { %>
                <div class="contact-item">
                    <div class="contact-label">Employee ID:</div>
                    <div class="contact-value"><%= teacher.employee_id %></div>
                </div>
                <% } %>
                
                <% if (teacher.date_of_birth) { %>
                <div class="contact-item">
                    <div class="contact-label">Date of Birth:</div>
                    <div class="contact-value"><%= new Date(teacher.date_of_birth).toLocaleDateString() %></div>
                </div>
                <% } %>
                
                <% if (teacher.gender) { %>
                <div class="contact-item">
                    <div class="contact-label">Gender:</div>
                    <div class="contact-value"><%= teacher.gender %></div>
                </div>
                <% } %>
                
                <% if (teacher.joining_date) { %>
                <div class="contact-item">
                    <div class="contact-label">Joining Date:</div>
                    <div class="contact-value"><%= new Date(teacher.joining_date).toLocaleDateString() %></div>
                </div>
                <% } %>
            </div>
        </div>

        <!-- Professional Experience -->
        <% if (teacher.experienceTimeline && teacher.experienceTimeline.length > 0) { %>
        <div class="section">
            <div class="section-title">Professional Experience</div>
            <div class="timeline-container">
                <% teacher.experienceTimeline.forEach(exp => { %>
                <div class="timeline-item">
                    <div class="experience-item">
                        <div class="item-header">
                            <div class="item-title"><%= exp.title || exp.position || 'Position' %></div>
                            <div class="item-date"><%= exp.duration || exp.year || 'Duration' %></div>
                        </div>
                        <div class="item-subtitle"><%= exp.institution || exp.organization || 'Organization' %></div>
                        <% if (exp.description) { %>
                        <div class="item-description"><%= exp.description %></div>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Education -->
        <% if (teacher.educationTimeline && teacher.educationTimeline.length > 0) { %>
        <div class="section">
            <div class="section-title">Educational Qualifications</div>
            <div class="timeline-container">
                <% teacher.educationTimeline.forEach(edu => { %>
                <div class="timeline-item">
                    <div class="education-item">
                        <div class="item-header">
                            <div class="item-title"><%= edu.title || edu.degree || 'Degree' %></div>
                            <div class="item-date"><%= edu.year || 'Year' %></div>
                        </div>
                        <div class="item-subtitle"><%= edu.institution || edu.university || 'Institution' %></div>
                        <% if (edu.percentage) { %>
                        <div class="item-description">Performance: <%= edu.percentage %>%</div>
                        <% } %>
                        <% if (edu.specialization) { %>
                        <div class="item-description">Specialization: <%= edu.specialization %></div>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Skills & Competencies -->
        <% if (teacher.skillsByCategory && Object.keys(teacher.skillsByCategory).length > 0) { %>
        <div class="section">
            <div class="section-title">Skills & Competencies</div>
            <div class="skills-grid">
                <% Object.entries(teacher.skillsByCategory).forEach(([category, skills]) => { %>
                <div class="skill-category">
                    <div class="skill-category-title"><%= category %></div>
                    <div class="skill-list">
                        <% if (Array.isArray(skills)) { %>
                            <%= skills.join(', ') %>
                        <% } else { %>
                            <%= skills %>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } else if (teacher.special_skills || teacher.languages_known) { %>
        <div class="section">
            <div class="section-title">Skills & Competencies</div>
            <div class="skills-grid">
                <% if (teacher.special_skills) { %>
                <div class="skill-category">
                    <div class="skill-category-title">Special Skills</div>
                    <div class="skill-list"><%= teacher.special_skills %></div>
                </div>
                <% } %>
                <% if (teacher.languages_known) { %>
                <div class="skill-category">
                    <div class="skill-category-title">Languages Known</div>
                    <div class="skill-list"><%= teacher.languages_known %></div>
                </div>
                <% } %>
            </div>
        </div>
        <% } %>

        <!-- Achievements & Recognition -->
        <% if (teacher.achievementsByCategory && Object.keys(teacher.achievementsByCategory).length > 0) { %>
        <div class="section">
            <div class="section-title">Achievements & Recognition</div>
            <% Object.entries(teacher.achievementsByCategory).forEach(([category, achievements]) => { %>
                <% if (Array.isArray(achievements)) { %>
                    <% achievements.forEach(achievement => { %>
                    <div class="achievement-item">
                        <div class="achievement-title"><%= achievement.title || achievement.name || 'Achievement' %></div>
                        <% if (achievement.description) { %>
                        <div class="achievement-description"><%= achievement.description %></div>
                        <% } %>
                        <% if (achievement.date) { %>
                        <div class="achievement-description">Date: <%= new Date(achievement.date).toLocaleDateString() %></div>
                        <% } %>
                    </div>
                    <% }); %>
                <% } %>
            <% }); %>
        </div>
        <% } else if (teacher.awards_received) { %>
        <div class="section">
            <div class="section-title">Achievements & Recognition</div>
            <div class="achievement-item">
                <div class="achievement-title">Awards & Recognition</div>
                <div class="achievement-description"><%= teacher.awards_received %></div>
            </div>
        </div>
        <% } %>

        <!-- Publications & Research -->
        <% if (teacher.publications || teacher.research_papers || teacher.conferences_attended) { %>
        <div class="section">
            <div class="section-title">Publications & Research</div>

            <% if (teacher.publications) { %>
            <div class="experience-item">
                <div class="item-title">Publications</div>
                <div class="item-description"><%= teacher.publications %></div>
            </div>
            <% } %>

            <% if (teacher.research_papers) { %>
            <div class="experience-item">
                <div class="item-title">Research Papers</div>
                <div class="item-description"><%= teacher.research_papers %></div>
            </div>
            <% } %>

            <% if (teacher.conferences_attended) { %>
            <div class="experience-item">
                <div class="item-title">Conferences Attended</div>
                <div class="item-description"><%= teacher.conferences_attended %></div>
            </div>
            <% } %>
        </div>
        <% } %>

        <!-- Professional Certifications -->
        <% if (teacher.professional_certifications) { %>
        <div class="section">
            <div class="section-title">Professional Certifications</div>
            <div class="experience-item">
                <div class="item-description"><%= teacher.professional_certifications %></div>
            </div>
        </div>
        <% } %>

        <!-- Administrative Details -->
        <div class="section">
            <div class="section-title">Administrative Information</div>
            <div class="contact-grid">
                <% if (teacher.employment_type) { %>
                <div class="contact-item">
                    <div class="contact-label">Employment Type:</div>
                    <div class="contact-value"><%= teacher.employment_type %></div>
                </div>
                <% } %>

                <% if (teacher.confirmation_date) { %>
                <div class="contact-item">
                    <div class="contact-label">Confirmation Date:</div>
                    <div class="contact-value"><%= new Date(teacher.confirmation_date).toLocaleDateString() %></div>
                </div>
                <% } %>

                <% if (teacher.performance_rating) { %>
                <div class="contact-item">
                    <div class="contact-label">Performance Rating:</div>
                    <div class="contact-value"><%= teacher.performance_rating %></div>
                </div>
                <% } %>

                <% if (teacher.office_location) { %>
                <div class="contact-item">
                    <div class="contact-label">Office Location:</div>
                    <div class="contact-value"><%= teacher.office_location %></div>
                </div>
                <% } %>

                <% if (teacher.current_salary) { %>
                <div class="contact-item">
                    <div class="contact-label">Current Salary:</div>
                    <div class="contact-value">₹<%= teacher.current_salary %></div>
                </div>
                <% } %>

                <% if (teacher.account_status) { %>
                <div class="contact-item">
                    <div class="contact-label">Account Status:</div>
                    <div class="contact-value"><%= teacher.account_status %></div>
                </div>
                <% } %>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Generated on:</strong> <%= new Date().toLocaleDateString() %> at <%= new Date().toLocaleTimeString() %></p>
            <p>Teacher Management System - Curriculum Vitae</p>
            <p>This document contains confidential information and is intended for official use only.</p>
        </div>
    </div>
</body>
</html>
