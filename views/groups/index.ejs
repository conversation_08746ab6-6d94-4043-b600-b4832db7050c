<!-- Groups List Page -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        <div class="flex space-x-3">
            <% if (locals.user && locals.user.role === 'admin') { %>
            <a href="/groups/import" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition flex items-center">
                <i class="fas fa-file-import mr-2"></i> Import Groups
            </a>
            <% } %>
            <a href="/groups/create" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition flex items-center">
                <i class="fas fa-plus mr-2"></i> Create New Group
            </a>
        </div>
    </div>

    <!-- Groups List -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% if (groups.length === 0) { %>
            <div class="col-span-3 bg-white rounded-lg shadow-md p-6 text-center">
                <p class="text-gray-600 mb-4">No groups found.</p>
                <a href="/groups/create" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                    Create Your First Group
                </a>
            </div>
        <% } else { %>
            <% groups.forEach(group => { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <h2 class="text-xl font-semibold text-gray-800">
                                <%= group.name %>
                                <% if (group.is_system) { %>
                                    <span class="ml-2 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                        System
                                    </span>
                                <% } %>
                            </h2>
                        </div>

                        <p class="text-gray-600 mb-4 line-clamp-2"><%= group.description || 'No description provided.' %></p>

                        <div class="flex justify-between items-center text-sm text-gray-500 mb-4">
                            <span><%= group.member_count %> members</span>
                            <% if (group.created_by) { %>
                                <span>Created by <%= group.creator_name %></span>
                            <% } %>
                        </div>

                        <div class="flex justify-end">
                            <a href="/groups/<%= group.group_id %>" class="text-blue-600 hover:text-blue-800">
                                View Group
                            </a>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } %>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
        <div class="mt-8 flex justify-center">
            <nav class="inline-flex rounded-md shadow">
                <% if (pagination.currentPage > 1) { %>
                    <a href="/groups?page=<%= pagination.currentPage - 1 %>" class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-l-md">
                        Previous
                    </a>
                <% } else { %>
                    <span class="px-4 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 rounded-l-md">
                        Previous
                    </span>
                <% } %>

                <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                    <% if (i === pagination.currentPage) { %>
                        <span class="px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-700">
                            <%= i %>
                        </span>
                    <% } else if (i <= 3 || i >= pagination.totalPages - 2 || Math.abs(i - pagination.currentPage) <= 1) { %>
                        <a href="/groups?page=<%= i %>" class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <%= i %>
                        </a>
                    <% } else if (i === 4 && pagination.currentPage > 5 || i === pagination.totalPages - 3 && pagination.currentPage < pagination.totalPages - 4) { %>
                        <span class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                    <% } %>
                <% } %>

                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <a href="/groups?page=<%= pagination.currentPage + 1 %>" class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-r-md">
                        Next
                    </a>
                <% } else { %>
                    <span class="px-4 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-500 rounded-r-md">
                        Next
                    </span>
                <% } %>
            </nav>
        </div>
    <% } %>
</div>
