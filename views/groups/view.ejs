<!-- View Group Page -->
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="text-sm mb-6">
        <ol class="list-none p-0 inline-flex">
            <li class="flex items-center">
                <a href="/groups" class="text-blue-600 hover:text-blue-800">Groups</a>
                <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg>
            </li>
            <li>
                <span class="text-gray-500"><%= group.name %></span>
            </li>
        </ol>
    </nav>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">
            <%= group.name %>
            <% if (group.is_system) { %>
                <span class="ml-2 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                    System
                </span>
            <% } %>
        </h1>
        <div class="flex space-x-2">
            <% if (isMember) { %>
                <% if (isAdmin && !group.is_system) { %>
                    <a href="/groups/<%= group.group_id %>/edit" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                        Edit Group
                    </a>
                <% } %>
                <form action="/groups/<%= group.group_id %>/leave" method="POST" onsubmit="return confirm('Are you sure you want to leave this group?');">
                    <button type="submit" class="px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50 transition">
                        Leave Group
                    </button>
                </form>
            <% } else { %>
                <form action="/groups/<%= group.group_id %>/join" method="POST">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                        Join Group
                    </button>
                </form>
            <% } %>
        </div>
    </div>

    <!-- Group Details -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Group Info -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">About</h2>
                    <p class="text-gray-600 mb-4"><%= group.description || 'No description provided.' %></p>
                    
                    <div class="flex flex-col sm:flex-row sm:justify-between text-sm text-gray-500">
                        <% if (group.created_by) { %>
                            <span>Created by <%= group.creator_name %></span>
                        <% } %>
                        <span>Created <%= new Date(group.created_at).toLocaleDateString() %></span>
                    </div>
                </div>
            </div>

            <!-- Assigned Exams -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-800">Assigned Exams</h2>
                        <% if (isAdmin) { %>
                            <button id="assignExamBtn" class="text-blue-600 hover:text-blue-800">
                                Assign Exam
                            </button>
                        <% } %>
                    </div>
                    
                    <% if (exams.length === 0) { %>
                        <p class="text-gray-600">No exams assigned to this group yet.</p>
                    <% } else { %>
                        <ul class="divide-y divide-gray-200">
                            <% exams.forEach(exam => { %>
                                <li class="py-4">
                                    <div class="flex justify-between">
                                        <div>
                                            <h3 class="text-md font-medium text-gray-800"><%= exam.exam_name %></h3>
                                            <p class="text-sm text-gray-600"><%= exam.description || 'No description' %></p>
                                            <% if (exam.due_date) { %>
                                                <p class="text-sm text-gray-500 mt-1">
                                                    Due: <%= new Date(exam.due_date).toLocaleDateString() %>
                                                </p>
                                            <% } %>
                                        </div>
                                        <div class="flex items-start">
                                            <a href="/tests/take/<%= exam.exam_id %>" class="text-blue-600 hover:text-blue-800 mr-4">
                                                Take Exam
                                            </a>
                                            <% if (isAdmin) { %>
                                                <button onclick="removeExam(<%= exam.exam_id %>)" class="text-red-600 hover:text-red-800">
                                                    Remove
                                                </button>
                                            <% } %>
                                        </div>
                                    </div>
                                </li>
                            <% }); %>
                        </ul>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Members List -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-800">Members (<%= members.length %>)</h2>
                        <% if (isAdmin) { %>
                            <button id="addMemberBtn" class="text-blue-600 hover:text-blue-800">
                                Add Member
                            </button>
                        <% } %>
                    </div>
                    
                    <ul class="divide-y divide-gray-200">
                        <% members.forEach(member => { %>
                            <li class="py-3 flex justify-between items-center">
                                <div class="flex items-center">
                                    <% if (member.profile_image) { %>
                                        <img src="<%= member.profile_image %>" alt="<%= member.username %>" class="w-8 h-8 rounded-full mr-3">
                                    <% } else { %>
                                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white mr-3">
                                            <%= member.username.charAt(0).toUpperCase() %>
                                        </div>
                                    <% } %>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">
                                            <%= member.username %>
                                            <% if (member.is_admin) { %>
                                                <span class="ml-1 px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 rounded">
                                                    Admin
                                                </span>
                                            <% } %>
                                        </div>
                                        <div class="text-xs text-gray-500"><%= member.email %></div>
                                    </div>
                                </div>
                                <% if (isAdmin && member.user_id !== locals.user.id) { %>
                                    <div class="flex space-x-2">
                                        <button onclick="toggleAdmin(<%= member.user_id %>, <%= !member.is_admin %>)" class="text-blue-600 hover:text-blue-800 text-sm">
                                            <%= member.is_admin ? 'Remove Admin' : 'Make Admin' %>
                                        </button>
                                        <button onclick="removeMember(<%= member.user_id %>)" class="text-red-600 hover:text-red-800 text-sm">
                                            Remove
                                        </button>
                                    </div>
                                <% } %>
                            </li>
                        <% }); %>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Member Modal -->
<div id="addMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Add Member</h3>
        <div class="mb-4">
            <label for="userSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
            <input type="text" id="userSearch" placeholder="Search by username or email"
                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div id="searchResults" class="max-h-60 overflow-y-auto mb-4"></div>
        <div class="flex justify-end space-x-4">
            <button onclick="closeAddMemberModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Assign Exam Modal -->
<div id="assignExamModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Assign Exam</h3>
        <div class="mb-4">
            <label for="examSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Exams</label>
            <input type="text" id="examSearch" placeholder="Search by exam name"
                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div id="examSearchResults" class="max-h-60 overflow-y-auto mb-4"></div>
        <div class="mb-4 hidden" id="dueDateContainer">
            <label for="dueDate" class="block text-sm font-medium text-gray-700 mb-1">Due Date (Optional)</label>
            <input type="date" id="dueDate"
                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="flex justify-end space-x-4">
            <button onclick="closeAssignExamModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                Cancel
            </button>
            <button id="assignExamSubmitBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition hidden">
                Assign
            </button>
        </div>
    </div>
</div>

<script>
    // Group ID
    const groupId = <%= group.group_id %>;
    let selectedExamId = null;
    
    // Add Member Modal
    document.getElementById('addMemberBtn')?.addEventListener('click', function() {
        document.getElementById('addMemberModal').classList.remove('hidden');
        document.getElementById('userSearch').focus();
    });
    
    function closeAddMemberModal() {
        document.getElementById('addMemberModal').classList.add('hidden');
        document.getElementById('userSearch').value = '';
        document.getElementById('searchResults').innerHTML = '';
    }
    
    // User Search
    let userSearchTimeout;
    document.getElementById('userSearch')?.addEventListener('input', function() {
        clearTimeout(userSearchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }
        
        userSearchTimeout = setTimeout(() => {
            fetch(`/groups/${groupId}/search/users?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(users => {
                    const resultsContainer = document.getElementById('searchResults');
                    
                    if (users.length === 0) {
                        resultsContainer.innerHTML = '<p class="text-gray-600 p-2">No users found</p>';
                        return;
                    }
                    
                    let html = '<ul class="divide-y divide-gray-200">';
                    users.forEach(user => {
                        html += `
                            <li class="py-2 px-2 hover:bg-gray-50 flex justify-between items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-800">${user.username}</div>
                                    <div class="text-xs text-gray-500">${user.email}</div>
                                </div>
                                <button onclick="addMember(${user.id})" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                    Add
                                </button>
                            </li>
                        `;
                    });
                    html += '</ul>';
                    
                    resultsContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error searching users:', error);
                });
        }, 300);
    });
    
    // Add Member
    function addMember(userId) {
        fetch(`/groups/${groupId}/members`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                is_admin: false
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to show new member
                window.location.reload();
            } else {
                alert(data.message || 'Error adding member');
            }
        })
        .catch(error => {
            console.error('Error adding member:', error);
            alert('Error adding member');
        });
    }
    
    // Remove Member
    function removeMember(userId) {
        if (!confirm('Are you sure you want to remove this member?')) {
            return;
        }
        
        fetch(`/groups/${groupId}/members/${userId}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update members list
                window.location.reload();
            } else {
                alert(data.message || 'Error removing member');
            }
        })
        .catch(error => {
            console.error('Error removing member:', error);
            alert('Error removing member');
        });
    }
    
    // Toggle Admin Status
    function toggleAdmin(userId, makeAdmin) {
        fetch(`/groups/${groupId}/members/${userId}/admin`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_admin: makeAdmin
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update admin status
                window.location.reload();
            } else {
                alert(data.message || 'Error updating admin status');
            }
        })
        .catch(error => {
            console.error('Error updating admin status:', error);
            alert('Error updating admin status');
        });
    }
    
    // Assign Exam Modal
    document.getElementById('assignExamBtn')?.addEventListener('click', function() {
        document.getElementById('assignExamModal').classList.remove('hidden');
        document.getElementById('examSearch').focus();
    });
    
    function closeAssignExamModal() {
        document.getElementById('assignExamModal').classList.add('hidden');
        document.getElementById('examSearch').value = '';
        document.getElementById('examSearchResults').innerHTML = '';
        document.getElementById('dueDateContainer').classList.add('hidden');
        document.getElementById('assignExamSubmitBtn').classList.add('hidden');
        selectedExamId = null;
    }
    
    // Exam Search
    let examSearchTimeout;
    document.getElementById('examSearch')?.addEventListener('input', function() {
        clearTimeout(examSearchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            document.getElementById('examSearchResults').innerHTML = '';
            return;
        }
        
        examSearchTimeout = setTimeout(() => {
            fetch(`/groups/${groupId}/search/exams?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(exams => {
                    const resultsContainer = document.getElementById('examSearchResults');
                    
                    if (exams.length === 0) {
                        resultsContainer.innerHTML = '<p class="text-gray-600 p-2">No exams found</p>';
                        return;
                    }
                    
                    let html = '<ul class="divide-y divide-gray-200">';
                    exams.forEach(exam => {
                        html += `
                            <li class="py-2 px-2 hover:bg-gray-50 cursor-pointer" onclick="selectExam(${exam.exam_id}, '${exam.exam_name}')">
                                <div class="text-sm font-medium text-gray-800">${exam.exam_name}</div>
                                <div class="text-xs text-gray-500">${exam.description || 'No description'}</div>
                            </li>
                        `;
                    });
                    html += '</ul>';
                    
                    resultsContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error searching exams:', error);
                });
        }, 300);
    });
    
    // Select Exam
    function selectExam(examId, examName) {
        selectedExamId = examId;
        document.getElementById('examSearch').value = examName;
        document.getElementById('examSearchResults').innerHTML = '';
        document.getElementById('dueDateContainer').classList.remove('hidden');
        document.getElementById('assignExamSubmitBtn').classList.remove('hidden');
    }
    
    // Assign Exam Submit
    document.getElementById('assignExamSubmitBtn')?.addEventListener('click', function() {
        if (!selectedExamId) {
            alert('Please select an exam');
            return;
        }
        
        const dueDate = document.getElementById('dueDate').value;
        
        fetch(`/groups/${groupId}/exams`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exam_id: selectedExamId,
                due_date: dueDate || null
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to show new assignment
                window.location.reload();
            } else {
                alert(data.message || 'Error assigning exam');
            }
        })
        .catch(error => {
            console.error('Error assigning exam:', error);
            alert('Error assigning exam');
        });
    });
    
    // Remove Exam
    function removeExam(examId) {
        if (!confirm('Are you sure you want to remove this exam assignment?')) {
            return;
        }
        
        fetch(`/groups/${groupId}/exams/${examId}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update assignments
                window.location.reload();
            } else {
                alert(data.message || 'Error removing exam assignment');
            }
        })
        .catch(error => {
            console.error('Error removing exam assignment:', error);
            alert('Error removing exam assignment');
        });
    }
</script>
