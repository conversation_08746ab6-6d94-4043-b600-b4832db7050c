<!-- Help Article Page -->
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="text-sm mb-6">
        <ol class="list-none p-0 inline-flex">
            <li class="flex items-center">
                <a href="/help" class="text-blue-600 hover:text-blue-800">Help Center</a>
                <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg>
            </li>
            <% if (categories.length > 0) { %>
                <li class="flex items-center">
                    <a href="/help/category/<%= categories[0].category_id %>" class="text-blue-600 hover:text-blue-800"><%= categories[0].name %></a>
                    <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                        <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                    </svg>
                </li>
            <% } %>
            <li>
                <span class="text-gray-500"><%= article.title %></span>
            </li>
        </ol>
    </nav>

    <div class="flex flex-col md:flex-row gap-8">
        <!-- Main Content -->
        <div class="md:w-3/4">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-2xl font-bold text-gray-800 mb-4"><%= article.title %></h1>
                    
                    <div class="flex items-center text-sm text-gray-500 mb-6">
                        <% if (article.author_name) { %>
                            <span>By <%= article.author_name %></span>
                            <span class="mx-2">•</span>
                        <% } %>
                        <span>Last updated: <%= new Date(article.updated_at).toLocaleDateString() %></span>
                    </div>
                    
                    <div class="prose max-w-none">
                        <%- article.content %>
                    </div>
                    
                    <!-- Article Feedback -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Was this article helpful?</h3>
                        <div class="flex space-x-4">
                            <button id="helpful-yes" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                                Yes
                            </button>
                            <button id="helpful-no" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition">
                                No
                            </button>
                        </div>
                        <div id="feedback-form" class="hidden mt-4">
                            <textarea id="feedback-comment" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Tell us more about your experience..."></textarea>
                            <button id="submit-feedback" class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition">
                                Submit Feedback
                            </button>
                        </div>
                        <div id="feedback-thanks" class="hidden mt-4 p-4 bg-green-50 text-green-800 rounded-md">
                            Thank you for your feedback!
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="md:w-1/4">
            <!-- Categories -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Categories</h3>
                <ul class="space-y-2">
                    <% categories.forEach(category => { %>
                        <li>
                            <a href="/help/category/<%= category.category_id %>" class="text-blue-600 hover:text-blue-800">
                                <%= category.name %>
                            </a>
                        </li>
                    <% }); %>
                </ul>
            </div>
            
            <!-- Related Articles -->
            <% if (relatedArticles && relatedArticles.length > 0) { %>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Related Articles</h3>
                    <ul class="space-y-3">
                        <% relatedArticles.forEach(relatedArticle => { %>
                            <li>
                                <a href="/help/article/<%= relatedArticle.slug %>" class="text-blue-600 hover:text-blue-800">
                                    <%= relatedArticle.title %>
                                </a>
                            </li>
                        <% }); %>
                    </ul>
                </div>
            <% } %>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const articleId = '<%= article.article_id %>';
        let feedbackValue = null;
        
        // Handle helpful buttons
        document.getElementById('helpful-yes').addEventListener('click', function() {
            feedbackValue = true;
            showFeedbackForm();
            this.classList.add('bg-green-100', 'border-green-500');
            document.getElementById('helpful-no').classList.remove('bg-red-100', 'border-red-500');
        });
        
        document.getElementById('helpful-no').addEventListener('click', function() {
            feedbackValue = false;
            showFeedbackForm();
            this.classList.add('bg-red-100', 'border-red-500');
            document.getElementById('helpful-yes').classList.remove('bg-green-100', 'border-green-500');
        });
        
        // Show feedback form
        function showFeedbackForm() {
            document.getElementById('feedback-form').classList.remove('hidden');
        }
        
        // Handle feedback submission
        document.getElementById('submit-feedback').addEventListener('click', function() {
            const comment = document.getElementById('feedback-comment').value;
            
            fetch('/help/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    article_id: articleId,
                    is_helpful: feedbackValue,
                    comment: comment
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('feedback-form').classList.add('hidden');
                    document.getElementById('feedback-thanks').classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error submitting feedback:', error);
            });
        });
    });
</script>
