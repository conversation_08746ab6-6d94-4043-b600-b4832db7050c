<!-- Help Category Page -->
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="text-sm mb-6">
        <ol class="list-none p-0 inline-flex">
            <li class="flex items-center">
                <a href="/help" class="text-blue-600 hover:text-blue-800">Help Center</a>
                <svg class="w-3 h-3 mx-2 fill-current text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg>
            </li>
            <li>
                <span class="text-gray-500"><%= category.name %></span>
            </li>
        </ol>
    </nav>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
        
        <!-- Search Box -->
        <form action="/help/search" method="GET" class="hidden md:flex">
            <input type="text" name="q" placeholder="Search for help..." 
                   class="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 transition">
                Search
            </button>
        </form>
    </div>

    <!-- Category Description -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <p class="text-gray-600"><%= category.description %></p>
    </div>

    <!-- Articles List -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <% if (articles.length === 0) { %>
            <div class="p-6 text-center">
                <p class="text-gray-600">No articles found in this category.</p>
            </div>
        <% } else { %>
            <ul class="divide-y divide-gray-200">
                <% articles.forEach(article => { %>
                    <li class="p-6 hover:bg-gray-50 transition">
                        <a href="/help/article/<%= article.slug %>" class="block">
                            <h3 class="text-lg font-semibold text-blue-600 hover:text-blue-800 mb-2"><%= article.title %></h3>
                            <p class="text-gray-600 line-clamp-2"><%= article.content.substring(0, 150) %>...</p>
                        </a>
                    </li>
                <% }); %>
            </ul>
        <% } %>
    </div>
</div>

<!-- Help Center Footer -->
<div class="bg-gray-100 py-8 mt-12">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Can't find what you're looking for?</h3>
            <p class="text-gray-600 mb-4">Contact our support team for assistance</p>
            <a href="mailto:<EMAIL>" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                Contact Support
            </a>
        </div>
    </div>
</div>
