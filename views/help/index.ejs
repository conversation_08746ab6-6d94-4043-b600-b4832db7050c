<!-- Help Center Home Page -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><%= pageTitle %></h1>
    </div>

    <!-- Search Box -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form action="/help/search" method="GET" class="flex flex-col md:flex-row gap-4">
            <input type="text" name="q" placeholder="Search for help..." 
                   class="flex-grow px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                Search
            </button>
        </form>
    </div>

    <!-- Popular Articles -->
    <% if (popularArticles && popularArticles.length > 0) { %>
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Popular Articles</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <ul class="divide-y divide-gray-200">
                    <% popularArticles.forEach(article => { %>
                        <li class="py-3">
                            <a href="/help/article/<%= article.slug %>" class="text-blue-600 hover:text-blue-800 font-medium">
                                <%= article.title %>
                            </a>
                        </li>
                    <% }); %>
                </ul>
            </div>
        </div>
    <% } %>

    <!-- Help Categories -->
    <div>
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Browse by Category</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <% categories.forEach(category => { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2"><%= category.name %></h3>
                        <p class="text-gray-600 mb-4"><%= category.description %></p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500"><%= category.article_count %> articles</span>
                            <a href="/help/category/<%= category.category_id %>" class="text-blue-600 hover:text-blue-800">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>
            <% }); %>
        </div>
    </div>
</div>

<!-- Help Center Footer -->
<div class="bg-gray-100 py-8 mt-12">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Can't find what you're looking for?</h3>
            <p class="text-gray-600 mb-4">Contact our support team for assistance</p>
            <a href="mailto:<EMAIL>" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition">
                Contact Support
            </a>
        </div>
    </div>
</div>
