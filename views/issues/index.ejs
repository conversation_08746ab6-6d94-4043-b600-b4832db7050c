

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-it-admin-primary">IT Issue Tracker</h1>
        <a href="/issues/report" class="inline-flex items-center px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Report New Issue
        </a>
    </div>

    <!-- Issue Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-it-admin-primary">
            <h3 class="text-lg font-medium text-gray-700">Open Issues</h3>
            <p class="text-3xl font-bold text-it-admin-primary"><%= stats.openIssues %></p>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-it-admin-accent">
            <h3 class="text-lg font-medium text-gray-700">In Progress</h3>
            <p class="text-3xl font-bold text-it-admin-accent"><%= stats.inProgressIssues %></p>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
            <h3 class="text-lg font-medium text-gray-700">Resolved</h3>
            <p class="text-3xl font-bold text-green-600"><%= stats.resolvedIssues %></p>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-gray-500">
            <h3 class="text-lg font-medium text-gray-700">Closed</h3>
            <p class="text-3xl font-bold text-gray-600"><%= stats.closedIssues %></p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white p-4 rounded-lg shadow-md mb-6">
        <h2 class="text-lg font-medium text-it-admin-primary mb-4">Quick Actions</h2>
        <div class="flex flex-wrap gap-3">
            <a href="/issues/report" class="inline-flex items-center px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary">
                <i class="fas fa-plus mr-2"></i>
                Report New Issue
            </a>
            <a href="/issues/list" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <i class="fas fa-list mr-2"></i>
                View All Issues
            </a>
            <a href="/issues/list?status=open" class="inline-flex items-center px-4 py-2 bg-it-admin-light text-it-admin-primary rounded-md hover:bg-it-admin-light">
                <i class="fas fa-exclamation-circle mr-2"></i>
                Open Issues
            </a>
            <a href="/issues/list?status=in_progress" class="inline-flex items-center px-4 py-2 bg-it-admin-light text-it-admin-accent rounded-md hover:bg-it-admin-light">
                <i class="fas fa-spinner mr-2"></i>
                In Progress Issues
            </a>
            <% if (isAdmin) { %>
                <a href="/admin/issues/dashboard" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    Admin Dashboard
                </a>
            <% } %>
        </div>
    </div>

    <!-- Recent Issues -->
    <div class="bg-white p-4 rounded-lg shadow-md">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-it-admin-primary">Recent Issues</h2>
            <div class="flex space-x-2">
                <a href="/issues/list" class="text-it-admin-primary hover:text-it-admin-secondary">
                    <i class="fas fa-external-link-alt"></i> View All
                </a>
            </div>
        </div>
        <% if (recentIssues.length === 0) { %>
            <p class="text-gray-500">No issues found.</p>
        <% } else { %>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-it-admin-primary text-white">
                        <tr>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">ID</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Title</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Priority</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Reported By</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Created</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <% recentIssues.forEach(issue => { %>
                            <tr>
                                <td class="py-2 px-4 text-sm text-gray-900">#<%= issue.issue_id %></td>
                                <td class="py-2 px-4 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <% if (issue.issue_type === 'hardware') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                            </svg>
                                        <% } else if (issue.issue_type === 'software') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                            </svg>
                                        <% } else if (issue.issue_type === 'network') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                                            </svg>
                                        <% } else { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        <% } %>
                                        <%= issue.title %>
                                    </div>
                                    <% if (issue.item_name) { %>
                                        <span class="text-xs text-gray-500">Item: <%= issue.item_name %></span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm">
                                    <% if (issue.status === 'open') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Open</span>
                                    <% } else if (issue.status === 'in_progress') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                                    <% } else if (issue.status === 'resolved') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                                    <% } else if (issue.status === 'closed') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm">
                                    <% if (issue.priority === 'critical') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Critical</span>
                                    <% } else if (issue.priority === 'high') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">High</span>
                                    <% } else if (issue.priority === 'medium') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                                    <% } else if (issue.priority === 'low') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Low</span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm text-gray-900"><%= issue.reported_by_name %></td>
                                <td class="py-2 px-4 text-sm text-gray-500"><%= issue.created_at %></td>
                                <td class="py-2 px-4 text-sm text-gray-500">
                                    <a href="/issues/<%= issue.issue_id %>" class="text-it-admin-primary hover:text-it-admin-secondary">
                                        <i class="fas fa-eye" title="View"></i>
                                    </a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-right">
                <a href="/issues/list" class="text-it-admin-primary hover:text-it-admin-secondary">View All Issues <i class="fas fa-arrow-right ml-1"></i></a>
            </div>
        <% } %>
    </div>
</div>

<%- include('../partials/footer') %>
