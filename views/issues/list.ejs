

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">IT Issues</h1>
        <a href="/issues/report" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Report New Issue
        </a>
    </div>

    <!-- Quick Filters -->
    <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Quick Filters</h3>

        <!-- Status Filters -->
        <div class="flex flex-wrap gap-2 mb-4">
            <a href="/issues/list" class="<%= !query.status && !query.priority && !query.type && !query.item ? 'bg-blue-200 text-blue-800 border-blue-400 shadow-sm' : 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                All Issues
                <span class="ml-1 px-1.5 py-0.5 <%= !query.status && !query.priority && !query.type && !query.item ? 'bg-blue-100 text-blue-900' : 'bg-white text-blue-600' %> rounded-full text-xs font-semibold"><%= pagination.totalItems %></span>
            </a>

            <%
                // Define status colors and labels with brighter active states
                const statusColors = {
                    'open': {
                        active: { bg: 'bg-blue-200', text: 'text-blue-800', border: 'border-blue-400', counter: 'bg-blue-100 text-blue-900' },
                        inactive: { bg: 'bg-blue-50', text: 'text-blue-600', border: 'border-blue-200', hover: 'hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300', counter: 'bg-white text-blue-600' }
                    },
                    'in_progress': {
                        active: { bg: 'bg-yellow-200', text: 'text-yellow-800', border: 'border-yellow-400', counter: 'bg-yellow-100 text-yellow-900' },
                        inactive: { bg: 'bg-yellow-50', text: 'text-yellow-600', border: 'border-yellow-200', hover: 'hover:bg-yellow-100 hover:text-yellow-700 hover:border-yellow-300', counter: 'bg-white text-yellow-600' }
                    },
                    'resolved': {
                        active: { bg: 'bg-green-200', text: 'text-green-800', border: 'border-green-400', counter: 'bg-green-100 text-green-900' },
                        inactive: { bg: 'bg-green-50', text: 'text-green-600', border: 'border-green-200', hover: 'hover:bg-green-100 hover:text-green-700 hover:border-green-300', counter: 'bg-white text-green-600' }
                    },
                    'closed': {
                        active: { bg: 'bg-gray-200', text: 'text-gray-800', border: 'border-gray-400', counter: 'bg-gray-100 text-gray-900' },
                        inactive: { bg: 'bg-gray-50', text: 'text-gray-600', border: 'border-gray-200', hover: 'hover:bg-gray-100 hover:text-gray-700 hover:border-gray-300', counter: 'bg-white text-gray-600' }
                    }
                };

                const statusLabels = {
                    'open': 'Open',
                    'in_progress': 'In Progress',
                    'resolved': 'Resolved',
                    'closed': 'Closed'
                };

                // Initialize counts
                const statusData = {
                    'open': 0,
                    'in_progress': 0,
                    'resolved': 0,
                    'closed': 0
                };

                // Fill in actual counts
                if (statusCounts) {
                    statusCounts.forEach(status => {
                        statusData[status.status] = status.count;
                    });
                }
            %>

            <% Object.keys(statusData).forEach(status => {
                const isActive = query.status === status;
                const style = isActive ? statusColors[status].active : statusColors[status].inactive;
            %>
                <a href="/issues/list?status=<%= status %>"
                   class="<%= style.bg %> <%= style.text %> <%= style.border %> <%= !isActive ? style.hover : 'shadow-sm' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                    <%= statusLabels[status] %>
                    <span class="ml-1 px-1.5 py-0.5 <%= isActive ? style.counter : 'bg-white text-' + style.text.split('-')[1] %> rounded-full text-xs font-semibold"><%= statusData[status] %></span>
                </a>
            <% }); %>
        </div>

        <!-- Priority Filters -->
        <% if (priorityCounts && priorityCounts.length > 0) { %>
            <h4 class="text-sm font-medium text-gray-600 mb-2">Priority</h4>
            <div class="flex flex-wrap gap-2 mb-4">
                <%
                    // Define priority colors and labels
                    const priorityColors = {
                        'critical': {
                            active: { bg: 'bg-red-200', text: 'text-red-800', border: 'border-red-400', counter: 'bg-red-100 text-red-900' },
                            inactive: { bg: 'bg-red-50', text: 'text-red-600', border: 'border-red-200', hover: 'hover:bg-red-100 hover:text-red-700 hover:border-red-300', counter: 'bg-white text-red-600' }
                        },
                        'high': {
                            active: { bg: 'bg-orange-200', text: 'text-orange-800', border: 'border-orange-400', counter: 'bg-orange-100 text-orange-900' },
                            inactive: { bg: 'bg-orange-50', text: 'text-orange-600', border: 'border-orange-200', hover: 'hover:bg-orange-100 hover:text-orange-700 hover:border-orange-300', counter: 'bg-white text-orange-600' }
                        },
                        'medium': {
                            active: { bg: 'bg-yellow-200', text: 'text-yellow-800', border: 'border-yellow-400', counter: 'bg-yellow-100 text-yellow-900' },
                            inactive: { bg: 'bg-yellow-50', text: 'text-yellow-600', border: 'border-yellow-200', hover: 'hover:bg-yellow-100 hover:text-yellow-700 hover:border-yellow-300', counter: 'bg-white text-yellow-600' }
                        },
                        'low': {
                            active: { bg: 'bg-green-200', text: 'text-green-800', border: 'border-green-400', counter: 'bg-green-100 text-green-900' },
                            inactive: { bg: 'bg-green-50', text: 'text-green-600', border: 'border-green-200', hover: 'hover:bg-green-100 hover:text-green-700 hover:border-green-300', counter: 'bg-white text-green-600' }
                        }
                    };

                    const priorityLabels = {
                        'critical': 'Critical',
                        'high': 'High',
                        'medium': 'Medium',
                        'low': 'Low'
                    };
                %>

                <% priorityCounts.forEach(priority => {
                    const isActive = query.priority === priority.priority;
                    const style = isActive ? priorityColors[priority.priority].active : priorityColors[priority.priority].inactive;
                %>
                    <a href="/issues/list?priority=<%= priority.priority %>"
                       class="<%= style.bg %> <%= style.text %> <%= style.border %> <%= !isActive ? style.hover : 'shadow-sm' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                        <%= priorityLabels[priority.priority] %>
                        <span class="ml-1 px-1.5 py-0.5 <%= isActive ? style.counter : 'bg-white text-' + style.text.split('-')[1] %> rounded-full text-xs font-semibold"><%= priority.count %></span>
                    </a>
                <% }); %>
            </div>
        <% } %>

        <!-- Issue Type Filters -->
        <% if (typeCounts && typeCounts.length > 0) { %>
            <h4 class="text-sm font-medium text-gray-600 mb-2">Issue Type</h4>
            <div class="flex flex-wrap gap-2 mb-4">
                <%
                    // Define type colors and labels
                    const typeColors = {
                        'hardware': {
                            active: { bg: 'bg-purple-200', text: 'text-purple-800', border: 'border-purple-400', counter: 'bg-purple-100 text-purple-900' },
                            inactive: { bg: 'bg-purple-50', text: 'text-purple-600', border: 'border-purple-200', hover: 'hover:bg-purple-100 hover:text-purple-700 hover:border-purple-300', counter: 'bg-white text-purple-600' }
                        },
                        'software': {
                            active: { bg: 'bg-indigo-200', text: 'text-indigo-800', border: 'border-indigo-400', counter: 'bg-indigo-100 text-indigo-900' },
                            inactive: { bg: 'bg-indigo-50', text: 'text-indigo-600', border: 'border-indigo-200', hover: 'hover:bg-indigo-100 hover:text-indigo-700 hover:border-indigo-300', counter: 'bg-white text-indigo-600' }
                        },
                        'network': {
                            active: { bg: 'bg-cyan-200', text: 'text-cyan-800', border: 'border-cyan-400', counter: 'bg-cyan-100 text-cyan-900' },
                            inactive: { bg: 'bg-cyan-50', text: 'text-cyan-600', border: 'border-cyan-200', hover: 'hover:bg-cyan-100 hover:text-cyan-700 hover:border-cyan-300', counter: 'bg-white text-cyan-600' }
                        },
                        'other': {
                            active: { bg: 'bg-gray-200', text: 'text-gray-800', border: 'border-gray-400', counter: 'bg-gray-100 text-gray-900' },
                            inactive: { bg: 'bg-gray-50', text: 'text-gray-600', border: 'border-gray-200', hover: 'hover:bg-gray-100 hover:text-gray-700 hover:border-gray-300', counter: 'bg-white text-gray-600' }
                        }
                    };

                    const typeLabels = {
                        'hardware': 'Hardware',
                        'software': 'Software',
                        'network': 'Network',
                        'other': 'Other'
                    };
                %>

                <% typeCounts.forEach(type => {
                    const isActive = query.type === type.issue_type;
                    const style = isActive ? typeColors[type.issue_type].active : typeColors[type.issue_type].inactive;
                %>
                    <a href="/issues/list?type=<%= type.issue_type %>"
                       class="<%= style.bg %> <%= style.text %> <%= style.border %> <%= !isActive ? style.hover : 'shadow-sm' %> px-3 py-1 rounded-full text-sm font-medium border transition-all duration-200">
                        <%= typeLabels[type.issue_type] %>
                        <span class="ml-1 px-1.5 py-0.5 <%= isActive ? style.counter : 'bg-white text-' + style.text.split('-')[1] %> rounded-full text-xs font-semibold"><%= type.count %></span>
                    </a>
                <% }); %>
            </div>
        <% } %>
    </div>

    <!-- Advanced Filters -->
    <div class="bg-white p-4 rounded-lg shadow-md mb-6 <%= (query.search || query.status || query.priority || query.type || query.item) ? 'border-blue-300 bg-blue-50' : 'border border-gray-200' %>">
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-md font-medium <%= (query.search || query.status || query.priority || query.type || query.item) ? 'text-blue-700' : 'text-gray-700' %>">Advanced Filters</h3>
            <button type="button" id="toggleFilters" class="<%= (query.search || query.status || query.priority || query.type || query.item) ? 'text-blue-700 hover:text-blue-900' : 'text-blue-600 hover:text-blue-800' %> text-sm font-medium flex items-center transition-colors">
                <span id="toggleText"><%= (query.search || query.status || query.priority || query.type || query.item) ? 'Hide Filters' : 'Show Filters' %></span>
                <svg id="toggleIcon" class="w-4 h-4 ml-1 transform <%= (query.search || query.status || query.priority || query.type || query.item) ? 'rotate-180' : 'rotate-0' %> transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>

        <div id="filterFields" class="<%= (query.search || query.status || query.priority || query.type || query.item) ? '' : 'hidden' %>">
            <form action="/issues/list" method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-3">
                <div>
                    <label for="status" class="block text-sm font-medium <%= query.status ? 'text-blue-700' : 'text-gray-700' %> mb-1">Status</label>
                    <select id="status" name="status" class="w-full rounded-md <%= query.status ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                        <option value="">All Statuses</option>
                        <option value="open" <%= query.status === 'open' ? 'selected' : '' %>>Open</option>
                        <option value="in_progress" <%= query.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                        <option value="resolved" <%= query.status === 'resolved' ? 'selected' : '' %>>Resolved</option>
                        <option value="closed" <%= query.status === 'closed' ? 'selected' : '' %>>Closed</option>
                    </select>
                </div>
                <div>
                    <label for="priority" class="block text-sm font-medium <%= query.priority ? 'text-blue-700' : 'text-gray-700' %> mb-1">Priority</label>
                    <select id="priority" name="priority" class="w-full rounded-md <%= query.priority ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                        <option value="">All Priorities</option>
                        <option value="critical" <%= query.priority === 'critical' ? 'selected' : '' %>>Critical</option>
                        <option value="high" <%= query.priority === 'high' ? 'selected' : '' %>>High</option>
                        <option value="medium" <%= query.priority === 'medium' ? 'selected' : '' %>>Medium</option>
                        <option value="low" <%= query.priority === 'low' ? 'selected' : '' %>>Low</option>
                    </select>
                </div>
                <div>
                    <label for="type" class="block text-sm font-medium <%= query.type ? 'text-blue-700' : 'text-gray-700' %> mb-1">Type</label>
                    <select id="type" name="type" class="w-full rounded-md <%= query.type ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                        <option value="">All Types</option>
                        <option value="hardware" <%= query.type === 'hardware' ? 'selected' : '' %>>Hardware</option>
                        <option value="software" <%= query.type === 'software' ? 'selected' : '' %>>Software</option>
                        <option value="network" <%= query.type === 'network' ? 'selected' : '' %>>Network</option>
                        <option value="other" <%= query.type === 'other' ? 'selected' : '' %>>Other</option>
                    </select>
                </div>
                <div>
                    <label for="item" class="block text-sm font-medium <%= query.item ? 'text-blue-700' : 'text-gray-700' %> mb-1">Item</label>
                    <select id="item" name="item" class="w-full rounded-md <%= query.item ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                        <option value="">All Items</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= query.item == item.item_id ? 'selected' : '' %>><%= item.name %></option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="search" class="block text-sm font-medium <%= query.search ? 'text-blue-700' : 'text-gray-700' %> mb-1">Search</label>
                    <div class="flex items-end">
                        <input type="text" id="search" name="search" value="<%= query.search || '' %>" placeholder="Search issues..." class="flex-1 rounded-l-md <%= query.search ? 'border-blue-300 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50' : 'border-gray-300' %> shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 transition-all">
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 shadow-sm transition-all">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        <% if (query.search || query.status || query.priority || query.type || query.item) { %>
                            <a href="/issues/list" class="ml-2 inline-flex items-center px-4 py-2 bg-red-100 text-red-700 hover:bg-red-200 rounded-md transition-all">
                                Reset
                            </a>
                        <% } %>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Issues List -->
    <div class="bg-white p-4 rounded-lg shadow-md">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-gray-700">Issues</h2>
            <div class="flex space-x-2">
                <button id="exportCSV" class="inline-flex items-center px-3 py-1.5 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition-colors">
                    <i class="fas fa-file-csv mr-2"></i> Export CSV
                </button>
                <button id="exportPDF" class="inline-flex items-center px-3 py-1.5 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition-colors">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </button>
            </div>
        </div>
        <% if (issues.length === 0) { %>
            <p class="text-gray-500">No issues found.</p>
        <% } else { %>
            <div class="overflow-x-auto">
                <table id="issues-table" class="min-w-full bg-white">
                    <thead class="bg-it-admin-primary text-white">
                        <tr>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">ID</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Title</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Priority</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Reported By</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Assigned To</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Created</th>
                            <th class="py-2 px-4 text-left text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <% issues.forEach(issue => { %>
                            <tr>
                                <td class="py-2 px-4 text-sm text-gray-900">#<%= issue.issue_id %></td>
                                <td class="py-2 px-4 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <% if (issue.issue_type === 'hardware') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                            </svg>
                                        <% } else if (issue.issue_type === 'software') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                            </svg>
                                        <% } else if (issue.issue_type === 'network') { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                                            </svg>
                                        <% } else { %>
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        <% } %>
                                        <%= issue.title %>
                                    </div>
                                    <% if (issue.item_name) { %>
                                        <span class="text-xs text-gray-500">Item: <%= issue.item_name %></span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm">
                                    <% if (issue.status === 'open') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Open</span>
                                    <% } else if (issue.status === 'in_progress') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                                    <% } else if (issue.status === 'resolved') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                                    <% } else if (issue.status === 'closed') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm">
                                    <% if (issue.priority === 'critical') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Critical</span>
                                    <% } else if (issue.priority === 'high') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">High</span>
                                    <% } else if (issue.priority === 'medium') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                                    <% } else if (issue.priority === 'low') { %>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Low</span>
                                    <% } %>
                                </td>
                                <td class="py-2 px-4 text-sm text-gray-900"><%= issue.reported_by_name %></td>
                                <td class="py-2 px-4 text-sm text-gray-900"><%= issue.assigned_to_name || 'Unassigned' %></td>
                                <td class="py-2 px-4 text-sm text-gray-500"><%= issue.created_at %></td>
                                <td class="py-2 px-4 text-sm text-gray-500">
                                    <div class="flex space-x-2">
                                        <a href="/issues/<%= issue.issue_id %>" class="text-it-admin-primary hover:text-it-admin-secondary">
                                            <i class="fas fa-eye" title="View"></i>
                                        </a>
                                        <% if (isAdmin) { %>
                                            <a href="/issues/<%= issue.issue_id %>/edit" class="text-indigo-600 hover:text-indigo-900">
                                                <i class="fas fa-edit" title="Edit"></i>
                                            </a>
                                            <button onclick="confirmDeleteIssue(<%= issue.issue_id %>)" class="text-red-600 hover:text-red-900 border-0 bg-transparent p-0 cursor-pointer">
                                                <i class="fas fa-trash-alt" title="Delete"></i>
                                            </button>
                                        <% } %>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <% if (pagination.totalPages > 1) { %>
                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        Showing <%= (pagination.currentPage - 1) * pagination.perPage + 1 %> to <%= Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems) %> of <%= pagination.totalItems %> issues
                    </div>
                    <div class="flex space-x-1">
                        <% if (pagination.currentPage > 1) { %>
                            <a href="/issues/list?page=<%= pagination.currentPage - 1 %>&perPage=<%= pagination.perPage %><%= query.status ? '&status=' + query.status : '' %><%= query.priority ? '&priority=' + query.priority : '' %><%= query.type ? '&type=' + query.type : '' %><%= query.item ? '&item=' + query.item : '' %><%= query.search ? '&search=' + query.search : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Previous</a>
                        <% } %>

                        <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                            <% if (i === pagination.currentPage) { %>
                                <span class="px-3 py-1 rounded-md bg-blue-600 text-white"><%= i %></span>
                            <% } else if (i === 1 || i === pagination.totalPages || (i >= pagination.currentPage - 1 && i <= pagination.currentPage + 1)) { %>
                                <a href="/issues/list?page=<%= i %>&perPage=<%= pagination.perPage %><%= query.status ? '&status=' + query.status : '' %><%= query.priority ? '&priority=' + query.priority : '' %><%= query.type ? '&type=' + query.type : '' %><%= query.item ? '&item=' + query.item : '' %><%= query.search ? '&search=' + query.search : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"><%= i %></a>
                            <% } else if (i === pagination.currentPage - 2 || i === pagination.currentPage + 2) { %>
                                <span class="px-3 py-1">...</span>
                            <% } %>
                        <% } %>

                        <% if (pagination.currentPage < pagination.totalPages) { %>
                            <a href="/issues/list?page=<%= pagination.currentPage + 1 %>&perPage=<%= pagination.perPage %><%= query.status ? '&status=' + query.status : '' %><%= query.priority ? '&priority=' + query.priority : '' %><%= query.type ? '&type=' + query.type : '' %><%= query.item ? '&item=' + query.item : '' %><%= query.search ? '&search=' + query.search : '' %>" class="px-3 py-1 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300">Next</a>
                        <% } %>
                    </div>
                </div>
            <% } %>
        <% } %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle advanced filters
        const toggleButton = document.getElementById('toggleFilters');
        const filterFields = document.getElementById('filterFields');
        const toggleText = document.getElementById('toggleText');
        const toggleIcon = document.getElementById('toggleIcon');

        if (toggleButton && filterFields) {
            toggleButton.addEventListener('click', function() {
                filterFields.classList.toggle('hidden');
                if (filterFields.classList.contains('hidden')) {
                    toggleText.textContent = 'Show Filters';
                    toggleIcon.classList.remove('rotate-180');
                } else {
                    toggleText.textContent = 'Hide Filters';
                    toggleIcon.classList.add('rotate-180');
                }
            });
        }

        // Export to CSV functionality
        const exportCSVButton = document.getElementById('exportCSV');
        if (exportCSVButton) {
            exportCSVButton.addEventListener('click', function() {
                exportTableToCSV('issues-table', 'it_issues_export.csv');
            });
        }

        // Export to PDF functionality
        const exportPDFButton = document.getElementById('exportPDF');
        if (exportPDFButton) {
            exportPDFButton.addEventListener('click', function() {
                // First create a printable version of the table
                const table = document.querySelector('table');
                const printWindow = window.open('', '_blank');

                printWindow.document.write(`
                    <html>
                    <head>
                        <title>IT Issues Export</title>
                        <style>
                            body { font-family: Arial, sans-serif; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #2c5530; color: white; }
                            tr:nth-child(even) { background-color: #f2f2f2; }
                            h1 { color: #2c5530; }
                            .status-open { color: #1e40af; font-weight: bold; }
                            .status-in-progress { color: #b45309; font-weight: bold; }
                            .status-resolved { color: #047857; font-weight: bold; }
                            .status-closed { color: #4b5563; font-weight: bold; }
                            .priority-critical { color: #dc2626; font-weight: bold; }
                            .priority-high { color: #ea580c; font-weight: bold; }
                            .priority-medium { color: #d97706; font-weight: bold; }
                            .priority-low { color: #059669; font-weight: bold; }
                        </style>
                    </head>
                    <body>
                        <h1>IT Issues Report</h1>
                        <p>Generated on: ${new Date().toLocaleString()}</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Reported By</th>
                                    <th>Assigned To</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                `);

                // Add table rows
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const id = row.cells[0].textContent;
                    const title = row.cells[1].textContent.trim();

                    // Get status with class
                    const statusCell = row.cells[2];
                    const statusSpan = statusCell.querySelector('span');
                    const status = statusSpan ? statusSpan.textContent : '';
                    const statusClass = status.toLowerCase().replace(' ', '-');

                    // Get priority with class
                    const priorityCell = row.cells[3];
                    const prioritySpan = priorityCell.querySelector('span');
                    const priority = prioritySpan ? prioritySpan.textContent : '';
                    const priorityClass = priority.toLowerCase().replace(' ', '-');

                    const reportedBy = row.cells[4].textContent;
                    const assignedTo = row.cells[5].textContent;
                    const created = row.cells[6].textContent;

                    printWindow.document.write(`
                        <tr>
                            <td>${id}</td>
                            <td>${title}</td>
                            <td class="status-${statusClass}">${status}</td>
                            <td class="priority-${priorityClass}">${priority}</td>
                            <td>${reportedBy}</td>
                            <td>${assignedTo}</td>
                            <td>${created}</td>
                        </tr>
                    `);
                });

                printWindow.document.write(`
                            </tbody>
                        </table>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.focus();

                // Print after a short delay to ensure styles are loaded
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            });
        }

        // Function to export table to CSV
        function exportTableToCSV(tableId, filename) {
            const table = document.querySelector('table');
            if (!table) return;

            let csv = [];

            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => {
                headers.push('"' + cell.textContent.trim() + '"');
            });
            csv.push(headers.join(','));

            // Get rows
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const rowData = [];
                // Skip the last column (Actions)
                for (let i = 0; i < row.cells.length - 1; i++) {
                    const cell = row.cells[i];
                    // For status and priority, get the text from the span
                    if (i === 2 || i === 3) {
                        const span = cell.querySelector('span');
                        rowData.push('"' + (span ? span.textContent.trim() : '') + '"');
                    } else {
                        rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
                    }
                }
                csv.push(rowData.join(','));
            });

            // Create CSV content
            const csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', filename);
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);
        }
    });
</script>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div id="confirmation-dialog-header" class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 id="confirmation-dialog-title" class="text-xl font-semibold">Delete Issue</h3>
            <button id="confirmation-dialog-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p id="confirmation-dialog-message" class="mb-6">Are you sure you want to delete this issue? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="confirmation-dialog-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                Cancel
            </button>
            <button id="confirmation-dialog-confirm" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </div>
</div>

<script>
    // Confirmation dialog functionality
    const confirmationDialog = document.getElementById('confirmation-dialog');
    const confirmationDialogClose = document.getElementById('confirmation-dialog-close');
    const confirmationDialogCancel = document.getElementById('confirmation-dialog-cancel');
    const confirmationDialogConfirm = document.getElementById('confirmation-dialog-confirm');
    let issueIdToDelete = null;

    function confirmDeleteIssue(issueId) {
        issueIdToDelete = issueId;
        confirmationDialog.classList.remove('hidden');
    }

    function closeConfirmationDialog() {
        confirmationDialog.classList.add('hidden');
        issueIdToDelete = null;
    }

    confirmationDialogClose.addEventListener('click', closeConfirmationDialog);
    confirmationDialogCancel.addEventListener('click', closeConfirmationDialog);
    confirmationDialog.addEventListener('click', function(e) {
        if (e.target === confirmationDialog) {
            closeConfirmationDialog();
        }
    });

    confirmationDialogConfirm.addEventListener('click', function() {
        if (issueIdToDelete) {
            // Create a form to submit the delete request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/issues/${issueIdToDelete}/delete`;

            // Add CSRF token if needed
            // const csrfInput = document.createElement('input');
            // csrfInput.type = 'hidden';
            // csrfInput.name = '_csrf';
            // csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            // form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        }
        closeConfirmationDialog();
    });
</script>

<%- include('../partials/footer') %>
