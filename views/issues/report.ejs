

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Report IT Issue</h1>
        <a href="/issues" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Issues
        </a>
    </div>

    <!-- Issue Report Form -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <form action="/issues/report" method="POST" enctype="multipart/form-data" class="space-y-6">
            <!-- Basic Information -->
            <div>
                <h2 class="text-lg font-medium text-gray-700 mb-4">Issue Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Issue Title *</label>
                        <input type="text" id="title" name="title" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Brief description of the issue">
                    </div>

                    <div>
                        <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-1">Issue Type</label>
                        <select id="issue_type" name="issue_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                            <option value="hardware">Hardware</option>
                            <option value="software">Software</option>
                            <option value="network">Network</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                        <select id="priority" name="priority" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>

                    <div class="md:col-span-2">
                        <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Related Item</label>
                        <select id="item_id" name="item_id" class="search-select w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                            <option value="">-- Select Item (Optional) --</option>
                            <% items.forEach(item => { %>
                                <option value="<%= item.item_id %>"><%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %></option>
                            <% }); %>
                        </select>
                    </div>

                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                        <textarea id="description" name="description" rows="5" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Please provide detailed information about the issue"></textarea>
                    </div>
                </div>
            </div>

            <!-- Hardware Condition Assessment (shown only when hardware issue type is selected) -->
            <div id="hardware_condition_section" class="hidden">
                <h2 class="text-lg font-medium text-gray-700 mb-4">Hardware Condition Assessment</h2>
                <p class="text-sm text-gray-500 mb-4">Select any issues with the hardware item</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="physical_damage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Physical Damage</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="screen_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Screen Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="keyboard_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Keyboard Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="battery_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Battery Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="connectivity_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Connectivity Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="software_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Software Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="port_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Port Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="audio_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Audio Issues</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="condition_issues[]" value="other_issues" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Other Issues</span>
                        </label>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="condition_notes" class="block text-sm font-medium text-gray-700 mb-1">Condition Details</label>
                    <textarea id="condition_notes" name="condition_notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Provide more details about the hardware condition issues"></textarea>
                </div>
            </div>

            <!-- Attachments -->
            <div>
                <h2 class="text-lg font-medium text-gray-700 mb-4">Attachments</h2>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="screenshots" class="block text-sm font-medium text-gray-700 mb-1">Upload Images (Optional)</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="screenshots" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB (max 5 files)</p>
                                </div>
                                <input id="screenshots" name="screenshots" type="file" class="hidden" multiple accept="image/*" />
                            </label>
                        </div>
                        <div class="mt-2 flex items-center">
                            <span id="file_count" class="text-sm text-gray-500">No files selected</span>
                            <button type="button" id="clear_files" class="ml-2 text-sm text-red-500 hover:text-red-700 hidden">Clear all</button>
                        </div>
                    </div>
                    <div id="screenshot_previews" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-2"></div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <a href="/issues" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Submit Issue</button>
            </div>
        </form>
    </div>
</div>

<!-- Include search-select CSS and JS -->
<link rel="stylesheet" href="/css/search-select.css">
<script src="/js/search-select.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize search-select for item dropdown
        initSearchSelect('#item_id', {
            placeholder: 'Search for an item...',
            noResultsText: 'No items found',
            minSearchLength: 1,
            maxResults: 15
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const titleInput = document.getElementById('title');
            const descriptionInput = document.getElementById('description');

            if (!titleInput.value.trim()) {
                e.preventDefault();
                alert('Issue title is required');
                titleInput.focus();
                return;
            }

            if (!descriptionInput.value.trim()) {
                e.preventDefault();
                alert('Issue description is required');
                descriptionInput.focus();
                return;
            }

            // Validate hardware condition details if hardware issue type is selected
            const issueType = document.getElementById('issue_type').value;
            if (issueType === 'hardware') {
                const conditionCheckboxes = document.querySelectorAll('input[name="condition_issues[]"]');
                let hasConditionIssue = false;

                conditionCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        hasConditionIssue = true;
                    }
                });

                if (!hasConditionIssue) {
                    e.preventDefault();
                    alert('Please select at least one hardware condition issue');
                    return;
                }

                const conditionNotes = document.getElementById('condition_notes');
                if (!conditionNotes.value.trim()) {
                    e.preventDefault();
                    alert('Please provide details about the hardware condition issues');
                    conditionNotes.focus();
                    return;
                }
            }
        });

        // Toggle hardware condition section based on issue type
        const issueTypeSelect = document.getElementById('issue_type');
        const hardwareConditionSection = document.getElementById('hardware_condition_section');

        function toggleHardwareSection() {
            if (issueTypeSelect.value === 'hardware') {
                hardwareConditionSection.classList.remove('hidden');
            } else {
                hardwareConditionSection.classList.add('hidden');
            }
        }

        // Initial toggle
        toggleHardwareSection();

        // Toggle on change
        issueTypeSelect.addEventListener('change', toggleHardwareSection);

        // Item selection handling
        const itemSelect = document.getElementById('item_id');
        itemSelect.addEventListener('change', function() {
            // If an item is selected and issue type is hardware, show hardware condition section
            if (this.value && issueTypeSelect.value === 'hardware') {
                hardwareConditionSection.classList.remove('hidden');
            }
        });

        // Screenshot preview
        const screenshotInput = document.getElementById('screenshots');
        const previewContainer = document.getElementById('screenshot_previews');
        const fileCountDisplay = document.getElementById('file_count');
        const clearFilesButton = document.getElementById('clear_files');
        const MAX_FILES = 5;
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

        // Clear files function
        function clearFiles() {
            screenshotInput.value = '';
            previewContainer.innerHTML = '';
            fileCountDisplay.textContent = 'No files selected';
            clearFilesButton.classList.add('hidden');
        }

        // Add clear files event listener
        clearFilesButton.addEventListener('click', clearFiles);

        // Handle file selection
        screenshotInput.addEventListener('change', function() {
            // Clear previous previews
            previewContainer.innerHTML = '';

            if (this.files && this.files.length > 0) {
                // Check if too many files are selected
                if (this.files.length > MAX_FILES) {
                    alert(`You can only upload a maximum of ${MAX_FILES} files. Please select fewer files.`);
                    clearFiles();
                    return;
                }

                // Update file count display
                fileCountDisplay.textContent = `${this.files.length} file${this.files.length > 1 ? 's' : ''} selected`;
                clearFilesButton.classList.remove('hidden');

                // Process each file
                Array.from(this.files).forEach((file, index) => {
                    // Check file type
                    if (!file.type.match('image.*')) {
                        alert(`File "${file.name}" is not an image. Only image files are allowed.`);
                        return;
                    }

                    // Check file size
                    if (file.size > MAX_FILE_SIZE) {
                        alert(`File "${file.name}" exceeds the maximum file size of 5MB.`);
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'relative group';

                        // Create image preview
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'w-full h-32 object-cover rounded-md border border-gray-300';
                        img.alt = 'Screenshot Preview';

                        // Create file name display
                        const fileNameDiv = document.createElement('div');
                        fileNameDiv.className = 'mt-1 text-xs text-gray-500 truncate';
                        fileNameDiv.textContent = file.name;

                        // Create remove button
                        const removeButton = document.createElement('button');
                        removeButton.type = 'button';
                        removeButton.className = 'absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
                        removeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';

                        // Add event listener to remove button
                        removeButton.addEventListener('click', function() {
                            // Remove this preview
                            previewDiv.remove();

                            // Create a new FileList without this file
                            // Note: FileList is immutable, so we need to reset the input
                            // This is a limitation of the File API
                            // For a complete solution, we would need to use a custom file upload solution

                            // Update file count
                            const remainingPreviews = previewContainer.querySelectorAll('.relative').length;
                            if (remainingPreviews === 0) {
                                clearFiles();
                            } else {
                                fileCountDisplay.textContent = `${remainingPreviews} file${remainingPreviews > 1 ? 's' : ''} selected`;
                            }
                        });

                        // Assemble preview
                        previewDiv.appendChild(img);
                        previewDiv.appendChild(fileNameDiv);
                        previewDiv.appendChild(removeButton);
                        previewContainer.appendChild(previewDiv);
                    };

                    reader.readAsDataURL(file);
                });
            } else {
                fileCountDisplay.textContent = 'No files selected';
                clearFilesButton.classList.add('hidden');
            }
        });
    });
</script>

<%- include('../partials/footer') %>
