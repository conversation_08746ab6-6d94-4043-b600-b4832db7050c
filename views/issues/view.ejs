

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Issue #<%= issue.issue_id %>: <%= issue.title %></h1>
        <div class="flex space-x-2">
            <a href="/issues/list" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
                All Issues
            </a>
            <a href="/issues/report" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                New Issue
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Issue Details -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h2 class="text-xl font-medium text-gray-800"><%= issue.title %></h2>
                        <div class="mt-1 flex items-center text-sm text-gray-500">
                            <span>Reported by <%= issue.reported_by_name %> on <%= issue.created_at %></span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <% if (issue.status === 'open') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-blue-100 text-blue-800">Open</span>
                        <% } else if (issue.status === 'in_progress') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                        <% } else if (issue.status === 'resolved') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-green-100 text-green-800">Resolved</span>
                        <% } else if (issue.status === 'closed') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-gray-100 text-gray-800">Closed</span>
                        <% } %>

                        <% if (issue.priority === 'critical') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-red-100 text-red-800">Critical</span>
                        <% } else if (issue.priority === 'high') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-orange-100 text-orange-800">High</span>
                        <% } else if (issue.priority === 'medium') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                        <% } else if (issue.priority === 'low') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-green-100 text-green-800">Low</span>
                        <% } %>
                    </div>
                </div>

                <% if (issue.item_name) { %>
                    <div class="mb-4 p-3 bg-gray-50 rounded-md">
                        <h3 class="text-sm font-medium text-gray-700">Related Item</h3>
                        <p class="text-sm text-gray-900"><%= issue.item_name %> <%= issue.item_serial ? `(${issue.item_serial})` : '' %></p>
                    </div>
                <% } %>

                <div class="prose max-w-none">
                    <h3 class="text-md font-medium text-gray-700 mb-2">Description</h3>
                    <div class="p-3 bg-gray-50 rounded-md">
                        <p class="whitespace-pre-line text-sm text-gray-900"><%= issue.description %></p>
                    </div>
                </div>

                <% if (issue.resolution_notes && (issue.status === 'resolved' || issue.status === 'closed')) { %>
                    <div class="mt-4 prose max-w-none">
                        <h3 class="text-md font-medium text-gray-700 mb-2">Resolution Notes</h3>
                        <div class="p-3 bg-gray-50 rounded-md">
                            <p class="whitespace-pre-line text-sm text-gray-900"><%= issue.resolution_notes %></p>
                        </div>
                    </div>
                <% } %>
            </div>

            <!-- Attachments -->
            <% if (attachments.length > 0) { %>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-lg font-medium text-gray-700 mb-4">Attachments (<%= attachments.length %>)</h2>

                    <!-- Image Gallery -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        <% attachments.forEach((attachment, index) => { %>
                            <div class="border border-gray-200 rounded-md overflow-hidden group relative">
                                <a href="<%= attachment.file_path %>" target="_blank" class="block" data-lightbox="issue-attachments" data-title="<%= attachment.file_name %> - Uploaded by <%= attachment.uploaded_by_name %> on <%= attachment.uploaded_at %>">
                                    <img src="<%= attachment.file_path %>" alt="<%= attachment.file_name %>" class="w-full h-32 object-cover group-hover:opacity-90 transition-opacity">
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="bg-black bg-opacity-50 text-white p-2 rounded-full">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="p-2">
                                        <p class="text-xs text-gray-500 truncate"><%= attachment.file_name %></p>
                                        <p class="text-xs text-gray-400">Uploaded by <%= attachment.uploaded_by_name %></p>
                                    </div>
                                </a>
                            </div>
                        <% }); %>
                    </div>
                </div>
            <% } %>

            <!-- Upload More Attachments -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-medium text-gray-700 mb-4">Add Attachments</h2>
                <form id="attachmentForm" action="/issues/<%= issue.issue_id %>/attachments" method="POST" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="screenshots" class="block text-sm font-medium text-gray-700 mb-1">Upload Screenshots</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="screenshots" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB (max 5 files)</p>
                                </div>
                                <input id="screenshots" name="screenshots" type="file" class="hidden" multiple accept="image/*" />
                            </label>
                        </div>
                        <div class="mt-2 flex items-center">
                            <span id="file_count" class="text-sm text-gray-500">No files selected</span>
                            <button type="button" id="clear_files" class="ml-2 text-sm text-red-500 hover:text-red-700 hidden">Clear all</button>
                        </div>
                    </div>
                    <div id="screenshot_previews" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-2 mb-4"></div>
                    <div id="upload_status" class="mb-4 hidden">
                        <div class="flex items-center">
                            <div class="spinner mr-2"></div>
                            <p class="text-sm text-blue-600">Uploading images...</p>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" id="uploadButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Upload</button>
                    </div>
                </form>
            </div>

            <!-- Comments -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-medium text-gray-700 mb-4">Comments (<%= comments.length %>)</h2>

                <% if (comments.length === 0) { %>
                    <p class="text-gray-500 mb-4">No comments yet.</p>
                <% } else { %>
                    <div class="space-y-4 mb-6">
                        <% comments.forEach(comment => { %>
                            <div class="p-3 bg-gray-50 rounded-md">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700"><%= comment.commented_by_name %></span>
                                    <span class="text-xs text-gray-500"><%= comment.created_at %></span>
                                </div>
                                <p class="text-sm text-gray-900 whitespace-pre-line"><%= comment.comment %></p>
                            </div>
                        <% }); %>
                    </div>
                <% } %>

                <!-- Add Comment Form -->
                <form action="/issues/<%= issue.issue_id %>/comment" method="POST">
                    <div class="mb-4">
                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-1">Add Comment</label>
                        <textarea id="comment" name="comment" rows="3" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Add your comment here..."></textarea>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Post Comment</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status and Actions -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-medium text-gray-700 mb-4">Status & Actions</h2>

                <!-- Current Status -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Current Status</h3>
                    <div class="flex items-center">
                        <% if (issue.status === 'open') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-blue-100 text-blue-800">Open</span>
                        <% } else if (issue.status === 'in_progress') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                        <% } else if (issue.status === 'resolved') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-green-100 text-green-800">Resolved</span>
                        <% } else if (issue.status === 'closed') { %>
                            <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-gray-100 text-gray-800">Closed</span>
                        <% } %>
                    </div>
                </div>

                <!-- Update Status Form -->
                <% if (isAdmin || issue.assigned_to === session.userId) { %>
                    <form action="/issues/<%= issue.issue_id %>/status" method="POST" class="mb-4">
                        <div class="mb-3">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Update Status</label>
                            <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                <option value="open" <%= issue.status === 'open' ? 'selected' : '' %>>Open</option>
                                <option value="in_progress" <%= issue.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                                <option value="resolved" <%= issue.status === 'resolved' ? 'selected' : '' %>>Resolved</option>
                                <option value="closed" <%= issue.status === 'closed' ? 'selected' : '' %>>Closed</option>
                            </select>
                        </div>

                        <div id="resolution_notes_container" class="mb-3 <%= issue.status === 'resolved' || issue.status === 'closed' ? '' : 'hidden' %>">
                            <label for="resolution_notes" class="block text-sm font-medium text-gray-700 mb-1">Resolution Notes</label>
                            <textarea id="resolution_notes" name="resolution_notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="Describe how the issue was resolved..."><%= issue.resolution_notes || '' %></textarea>
                        </div>

                        <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Update Status</button>
                    </form>
                <% } %>

                <!-- Assignment -->
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Assigned To</h3>
                    <p class="text-sm text-gray-900"><%= issue.assigned_to_name || 'Unassigned' %></p>
                </div>

                <!-- Assign Issue Form (Admin Only) -->
                <% if (isAdmin) { %>
                    <form action="/issues/<%= issue.issue_id %>/assign" method="POST">
                        <div class="mb-3">
                            <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-1">Assign To</label>
                            <select id="assigned_to" name="assigned_to" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                <option value="">-- Unassigned --</option>
                                <% adminUsers.forEach(user => { %>
                                    <option value="<%= user.id %>" <%= issue.assigned_to == user.id ? 'selected' : '' %>><%= user.username %></option>
                                <% }); %>
                            </select>
                        </div>
                        <button type="submit" class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">Update Assignment</button>
                    </form>
                <% } %>
            </div>

            <!-- Priority (Admin Only) -->
            <% if (isAdmin) { %>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-lg font-medium text-gray-700 mb-4">Priority</h2>

                    <!-- Current Priority -->
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-1">Current Priority</h3>
                        <div class="flex items-center">
                            <% if (issue.priority === 'critical') { %>
                                <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-red-100 text-red-800">Critical</span>
                            <% } else if (issue.priority === 'high') { %>
                                <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-orange-100 text-orange-800">High</span>
                            <% } else if (issue.priority === 'medium') { %>
                                <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                            <% } else if (issue.priority === 'low') { %>
                                <span class="px-3 py-1 inline-flex text-sm font-medium rounded-full bg-green-100 text-green-800">Low</span>
                            <% } %>
                        </div>
                    </div>

                    <!-- Update Priority Form -->
                    <form action="/issues/<%= issue.issue_id %>/priority" method="POST">
                        <div class="mb-3">
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Update Priority</label>
                            <select id="priority" name="priority" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                                <option value="low" <%= issue.priority === 'low' ? 'selected' : '' %>>Low</option>
                                <option value="medium" <%= issue.priority === 'medium' ? 'selected' : '' %>>Medium</option>
                                <option value="high" <%= issue.priority === 'high' ? 'selected' : '' %>>High</option>
                                <option value="critical" <%= issue.priority === 'critical' ? 'selected' : '' %>>Critical</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">Update Priority</button>
                    </form>
                </div>
            <% } %>

            <!-- Issue History -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-lg font-medium text-gray-700 mb-4">Issue History</h2>

                <% if (history.length === 0) { %>
                    <p class="text-gray-500">No history available.</p>
                <% } else { %>
                    <div class="space-y-3">
                        <% history.forEach(entry => { %>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-4 w-4 rounded-full bg-blue-500 mt-1"></div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-900">
                                        <span class="font-medium"><%= entry.changed_by_name %></span>
                                        changed <span class="font-medium"><%= entry.field_changed %></span>
                                        from "<%= entry.old_value || 'none' %>"
                                        to "<%= entry.new_value %>"
                                    </p>
                                    <p class="text-xs text-gray-500"><%= entry.changed_at %></p>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Lightbox CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">

<style>
    .spinner {
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-top-color: #3498db;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Custom lightbox styling */
    .lightbox-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .lightbox-container.active {
        opacity: 1;
        pointer-events: auto;
    }

    .lightbox-image {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }

    .lightbox-close {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 30px;
        cursor: pointer;
    }

    .lightbox-nav {
        position: absolute;
        top: 50%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        transform: translateY(-50%);
    }

    .lightbox-prev, .lightbox-next {
        color: white;
        font-size: 30px;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.5);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Status change handler for resolution notes
        const statusSelect = document.getElementById('status');
        const resolutionNotesContainer = document.getElementById('resolution_notes_container');

        if (statusSelect && resolutionNotesContainer) {
            statusSelect.addEventListener('change', function() {
                if (this.value === 'resolved' || this.value === 'closed') {
                    resolutionNotesContainer.classList.remove('hidden');
                } else {
                    resolutionNotesContainer.classList.add('hidden');
                }
            });
        }

        // Screenshot preview and upload handling
        const screenshotInput = document.getElementById('screenshots');
        const previewContainer = document.getElementById('screenshot_previews');
        const uploadButton = document.getElementById('uploadButton');
        const uploadStatus = document.getElementById('upload_status');
        const fileCountDisplay = document.getElementById('file_count');
        const clearFilesButton = document.getElementById('clear_files');
        const issueId = '<%= issue.issue_id %>';
        const MAX_FILES = 5;
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

        if (screenshotInput && previewContainer && uploadButton) {
            // Clear files function
            function clearFiles() {
                screenshotInput.value = '';
                previewContainer.innerHTML = '';
                fileCountDisplay.textContent = 'No files selected';
                clearFilesButton.classList.add('hidden');
            }

            // Add clear files event listener
            clearFilesButton.addEventListener('click', clearFiles);

            // Preview images when selected
            screenshotInput.addEventListener('change', function() {
                // Clear previous previews
                previewContainer.innerHTML = '';

                if (this.files && this.files.length > 0) {
                    // Check if too many files are selected
                    if (this.files.length > MAX_FILES) {
                        alert(`You can only upload a maximum of ${MAX_FILES} files. Please select fewer files.`);
                        clearFiles();
                        return;
                    }

                    // Update file count display
                    fileCountDisplay.textContent = `${this.files.length} file${this.files.length > 1 ? 's' : ''} selected`;
                    clearFilesButton.classList.remove('hidden');

                    // Process each file
                    Array.from(this.files).forEach((file, index) => {
                        // Check file type
                        if (!file.type.match('image.*')) {
                            alert(`File "${file.name}" is not an image. Only image files are allowed.`);
                            return;
                        }

                        // Check file size
                        if (file.size > MAX_FILE_SIZE) {
                            alert(`File "${file.name}" exceeds the maximum file size of 5MB.`);
                            return;
                        }

                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const previewDiv = document.createElement('div');
                            previewDiv.className = 'relative group';

                            // Create image preview
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'w-full h-32 object-cover rounded-md border border-gray-300';
                            img.alt = 'Screenshot Preview';

                            // Create file name display
                            const fileNameDiv = document.createElement('div');
                            fileNameDiv.className = 'mt-1 text-xs text-gray-500 truncate';
                            fileNameDiv.textContent = file.name;

                            // Create remove button
                            const removeButton = document.createElement('button');
                            removeButton.type = 'button';
                            removeButton.className = 'absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
                            removeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';

                            // Add event listener to remove button
                            removeButton.addEventListener('click', function() {
                                // Remove this preview
                                previewDiv.remove();

                                // Update file count
                                const remainingPreviews = previewContainer.querySelectorAll('.relative').length;
                                if (remainingPreviews === 0) {
                                    clearFiles();
                                } else {
                                    fileCountDisplay.textContent = `${remainingPreviews} file${remainingPreviews > 1 ? 's' : ''} selected`;
                                }
                            });

                            // Assemble preview
                            previewDiv.appendChild(img);
                            previewDiv.appendChild(fileNameDiv);
                            previewDiv.appendChild(removeButton);
                            previewContainer.appendChild(previewDiv);
                        };

                        reader.readAsDataURL(file);
                    });
                } else {
                    fileCountDisplay.textContent = 'No files selected';
                    clearFilesButton.classList.add('hidden');
                }
            });

            // Handle upload button click
            uploadButton.addEventListener('click', async function() {
                const files = screenshotInput.files;

                if (!files || files.length === 0) {
                    alert('Please select at least one image to upload');
                    return;
                }

                // Show upload status
                uploadStatus.classList.remove('hidden');
                uploadButton.disabled = true;
                uploadButton.classList.add('opacity-50', 'cursor-not-allowed');

                try {
                    const uploadPromises = [];

                    // Process each file
                    for (const file of files) {
                        if (!file.type.match('image.*')) {
                            continue;
                        }

                        // Read file as base64
                        const base64Data = await readFileAsBase64(file);

                        // Upload the file
                        uploadPromises.push(
                            uploadBase64Image(issueId, file.name, file.type, base64Data)
                        );
                    }

                    // Wait for all uploads to complete
                    const results = await Promise.all(uploadPromises);

                    // Check if all uploads were successful
                    const allSuccessful = results.every(result => result.success);

                    if (allSuccessful) {
                        // Reload the page to show the new attachments
                        window.location.reload();
                    } else {
                        // Show error message
                        const errors = results.filter(result => !result.success)
                            .map(result => result.message)
                            .join('\n');
                        alert('Some uploads failed:\n' + errors);

                        // Hide upload status
                        uploadStatus.classList.add('hidden');
                        uploadButton.disabled = false;
                        uploadButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                } catch (error) {
                    console.error('Error uploading images:', error);
                    alert('Error uploading images: ' + error.message);

                    // Hide upload status
                    uploadStatus.classList.add('hidden');
                    uploadButton.disabled = false;
                    uploadButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });

            // Function to read file as base64
            function readFileAsBase64(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        // Extract the base64 string (remove the data URL prefix)
                        const base64String = e.target.result.split(',')[1];
                        resolve(base64String);
                    };

                    reader.onerror = function(error) {
                        reject(error);
                    };

                    reader.readAsDataURL(file);
                });
            }

            // Function to upload base64 image
            function uploadBase64Image(issueId, filename, mimetype, base64Data) {
                return new Promise((resolve, reject) => {
                    // Create an abort controller to handle timeouts
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                    // Prepare the data to send
                    const uploadData = {
                        image: base64Data,
                        filename: filename,
                        mimetype: mimetype
                    };

                    // Make the fetch request with JSON payload
                    fetch(`/issues/${issueId}/attachments-base64`, {
                        method: 'POST',
                        body: JSON.stringify(uploadData),
                        signal: controller.signal,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => {
                        clearTimeout(timeoutId);

                        if (!response.ok) {
                            return response.text().then(text => {
                                try {
                                    // Try to parse as JSON first
                                    const data = JSON.parse(text);
                                    throw new Error(data.message || `HTTP error! Status: ${response.status}`);
                                } catch (e) {
                                    // If not JSON, return the text or status
                                    throw new Error(text || `HTTP error! Status: ${response.status}`);
                                }
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        resolve(data);
                    })
                    .catch(error => {
                        clearTimeout(timeoutId);

                        let errorMessage = error.message;
                        if (error.name === 'AbortError') {
                            errorMessage = 'Upload timed out. Please try a smaller image or check your connection.';
                        }

                        resolve({
                            success: false,
                            message: errorMessage
                        });
                    });
                });
            }
        }

        // Initialize lightbox
        const lightboxScript = document.createElement('script');
        lightboxScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js';
        document.body.appendChild(lightboxScript);

        lightboxScript.onload = function() {
            // Configure lightbox
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true,
                'albumLabel': 'Image %1 of %2',
                'fadeDuration': 300,
                'imageFadeDuration': 300
            });
        };
    });
</script>

<%- include('../partials/footer') %>
