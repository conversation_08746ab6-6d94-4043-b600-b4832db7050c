<%- include('../partials/it-admin/header') %>
<!-- Include the enhanced calendar today highlight script -->
<script src="/js/calendar-today-highlight.js"></script>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-it-admin-primary text-white p-4">
      <h2 class="text-xl font-semibold">Academic Calendar</h2>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <button id="prev-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h3 id="current-month" class="text-xl font-semibold mx-4"></h3>
          <button id="next-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
        <button id="today-btn" class="px-3 py-1 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">
          Today
        </button>
      </div>

      <div class="overflow-x-auto">
        <div class="calendar-grid grid grid-cols-7 gap-1">
          <!-- Calendar header -->
          <div class="text-center font-semibold py-2 bg-gray-100">Sun</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Mon</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Tue</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Wed</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Thu</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Fri</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Sat</div>

          <!-- Calendar days will be inserted here by JavaScript -->
          <div id="calendar-days" class="contents"></div>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 flex flex-wrap gap-4">
        <!-- IT Events -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">IT Maintenance</span>
        </div>

        <!-- Procurement -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-indigo-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Procurement</span>
        </div>

        <!-- Holidays -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">National Holiday</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-amber-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Festival</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Public Holiday</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 id="modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="modal-content" class="space-y-4">
      <!-- Content will be populated by JavaScript -->
    </div>
    <div class="mt-6 flex justify-end">
      <a id="view-details-btn" href="#" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-it-admin-primary">
        View Details
      </a>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');
    const eventModal = document.getElementById('event-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const viewDetailsBtn = document.getElementById('view-details-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Data from server
    const holidays = <%- JSON.stringify(holidays || []) %>;

    // Initialize calendar
    renderCalendar(currentMonth, currentYear);

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderCalendar(currentMonth, currentYear);
    });

    todayBtn.addEventListener('click', () => {
      const today = new Date();
      currentMonth = today.getMonth();
      currentYear = today.getFullYear();
      renderCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      eventModal.classList.add('hidden');
    });

    // Render calendar
    function renderCalendar(month, year) {
      // Clear previous calendar
      calendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthEl.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        calendarDays.appendChild(dayEl);
      }

      // Current month's days
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        const dayEl = createDayElement(i, isToday ? 'bg-blue-50 font-bold' : '', month, year);
        calendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        calendarDays.appendChild(dayEl);
      }
    }

    // Create day element
    function createDayElement(day, extraClasses, month, year) {
      // Format date string for comparison
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // Create events container function
      const createEventsContainer = (day, month, year) => {
        // Add events container
        const eventsContainer = document.createElement('div');
        eventsContainer.className = 'mt-1 space-y-1';

        // Add holidays for this day
        const dayHolidays = holidays.filter(h => {
          if (!h.holiday_date) return false;

          // Fix for timezone issue - create a date object from the holiday date
          // and compare the year, month, and day directly
          const holidayDate = new Date(h.holiday_date);
          return holidayDate.getFullYear() === year &&
                 holidayDate.getMonth() === month &&
                 holidayDate.getDate() === day;
        });

        if (dayHolidays.length > 0) {
          dayHolidays.forEach(holiday => {
            const holidayEl = document.createElement('div');

            // Determine color based on holiday type
            let bgColor = 'bg-blue-200'; // Default for Public Holiday
            if (holiday.holiday_type === 'National Holiday') {
              bgColor = 'bg-red-200';
            } else if (holiday.holiday_type === 'Festival') {
              bgColor = 'bg-amber-200';
            }

            holidayEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer font-semibold`;
            holidayEl.textContent = holiday.description;

            // Add click event to show modal
            holidayEl.addEventListener('click', () => showHolidayModal(holiday));

            eventsContainer.appendChild(holidayEl);
          });

          return eventsContainer;
        }

        return null;
      };

      // Use the enhanced day element function
      return createEnhancedDayElement(day, extraClasses, month, year, createEventsContainer);
    }

    // Show holiday modal
    function showHolidayModal(holiday) {
      modalTitle.textContent = holiday.description;

      // Format date
      const holidayDate = new Date(holiday.holiday_date);
      const formattedDate = holidayDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Determine holiday type badge color
      let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
      if (holiday.holiday_type === 'National Holiday') {
        badgeColor = 'bg-red-100 text-red-800';
      } else if (holiday.holiday_type === 'Festival') {
        badgeColor = 'bg-amber-100 text-amber-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Type</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
            ${holiday.holiday_type}
          </span>
        </div>
      `;

      // Hide view details button for holidays
      viewDetailsBtn.style.display = 'none';

      // Show modal
      eventModal.classList.remove('hidden');
    }
  });
</script>

<%- include('../partials/it-admin/footer') %>
