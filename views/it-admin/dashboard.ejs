<!-- IT Admin Dashboard - Futuristic Design -->
<div class="mb-8">
  <h1 class="text-2xl font-bold text-it-admin-primary mb-2">IT Inventory Dashboard</h1>
  <p class="text-gray-600">Real-time overview of all IT resources and systems</p>
</div>

<!-- Main Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <!-- Total Devices Card -->
  <div class="bg-gradient-to-br from-it-admin-light to-white rounded-lg shadow-lg overflow-hidden border border-it-admin-light transform transition-all duration-300 hover:scale-105">
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-it-admin-primary text-sm font-medium uppercase tracking-wider">Total Devices</p>
          <h3 class="text-3xl font-bold text-it-admin-primary mt-2"><%= stats?.totalDevices || 0 %></h3>
          <div class="flex items-center mt-2 text-sm">
            <span class="text-gray-600"><%= stats?.availableDevices || 0 %> available</span>
            <span class="mx-2 text-gray-400">|</span>
            <span class="text-gray-600"><%= stats?.assignedDevices || 0 %> assigned</span>
          </div>
        </div>
        <div class="bg-it-admin-primary bg-opacity-10 rounded-full p-3">
          <svg class="w-10 h-10 text-it-admin-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Issues Card -->
  <div class="bg-gradient-to-br from-red-50 to-white rounded-lg shadow-lg overflow-hidden border border-red-100 transform transition-all duration-300 hover:scale-105">
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-red-600 text-sm font-medium uppercase tracking-wider">Active Issues</p>
          <h3 class="text-3xl font-bold text-red-600 mt-2"><%= stats?.totalIssues || 0 %></h3>
          <div class="flex items-center mt-2 text-sm">
            <span class="text-gray-600"><%= stats?.openIssues || 0 %> open</span>
            <span class="mx-2 text-gray-400">|</span>
            <span class="text-gray-600"><%= stats?.inProgressIssues || 0 %> in progress</span>
            <span class="mx-2 text-gray-400">|</span>
            <span class="text-gray-600"><%= stats?.resolvedIssues || 0 %> resolved</span>
          </div>
        </div>
        <div class="bg-red-100 rounded-full p-3">
          <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Repairs Card -->
  <div class="bg-gradient-to-br from-amber-50 to-white rounded-lg shadow-lg overflow-hidden border border-amber-100 transform transition-all duration-300 hover:scale-105">
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-amber-600 text-sm font-medium uppercase tracking-wider">Repairs</p>
          <h3 class="text-3xl font-bold text-amber-600 mt-2"><%= stats?.totalRepairs || 0 %></h3>
          <div class="flex items-center mt-2 text-sm">
            <span class="text-gray-600"><%= stats?.inProgressRepairs || 0 %> in progress</span>
            <span class="mx-2 text-gray-400">|</span>
            <span class="text-gray-600"><%= stats?.completedRepairs || 0 %> completed</span>
          </div>
        </div>
        <div class="bg-amber-100 rounded-full p-3">
          <svg class="w-10 h-10 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Vendors Card -->
  <div class="bg-gradient-to-br from-blue-50 to-white rounded-lg shadow-lg overflow-hidden border border-blue-100 transform transition-all duration-300 hover:scale-105">
    <div class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-600 text-sm font-medium uppercase tracking-wider">Vendors</p>
          <h3 class="text-3xl font-bold text-blue-600 mt-2"><%= stats?.totalVendors || 0 %></h3>
          <div class="flex items-center mt-2 text-sm">
            <span class="text-gray-600">Active repair partners</span>
          </div>
        </div>
        <div class="bg-blue-100 rounded-full p-3">
          <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Device Type Breakdown -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
  <div class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-100 lg:col-span-2">
    <div class="bg-it-admin-primary text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Device Inventory Breakdown</h2>
      <span class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">Total: <%= stats?.totalDevices || 0 %></span>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Laptops -->
        <div class="bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border border-gray-100">
          <div class="flex items-center justify-between mb-3">
            <h3 class="font-semibold text-gray-700">Laptops</h3>
            <span class="text-it-admin-primary font-bold text-xl"><%= stats?.deviceTypeStats?.laptop?.count || 0 %></span>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Available:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.laptop?.available || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Assigned:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.laptop?.assigned || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">In Repair:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.laptop?.in_repair || 0 %></span>
            </div>
          </div>
        </div>

        <!-- Desktops -->
        <div class="bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border border-gray-100">
          <div class="flex items-center justify-between mb-3">
            <h3 class="font-semibold text-gray-700">Desktops</h3>
            <span class="text-it-admin-primary font-bold text-xl"><%= stats?.deviceTypeStats?.desktop?.count || 0 %></span>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Available:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.desktop?.available || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Assigned:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.desktop?.assigned || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">In Repair:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.desktop?.in_repair || 0 %></span>
            </div>
          </div>
        </div>

        <!-- Other Devices -->
        <div class="bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border border-gray-100">
          <div class="flex items-center justify-between mb-3">
            <h3 class="font-semibold text-gray-700">Other Devices</h3>
            <span class="text-it-admin-primary font-bold text-xl">
              <%= (stats?.deviceTypeStats?.tablet?.count || 0) +
                 (stats?.deviceTypeStats?.projector?.count || 0) +
                 (stats?.deviceTypeStats?.printer?.count || 0) +
                 (stats?.deviceTypeStats?.network?.count || 0) +
                 (stats?.deviceTypeStats?.other?.count || 0) %>
            </span>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Tablets:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.tablet?.count || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Projectors:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.projector?.count || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Printers:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.printer?.count || 0 %></span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Network:</span>
              <span class="font-medium"><%= stats?.deviceTypeStats?.network?.count || 0 %></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Recent Issues -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden lg:col-span-2">
    <div class="bg-it-admin-primary text-white p-4">
      <h2 class="text-xl font-semibold">Recent Issues</h2>
    </div>
    <div class="p-6">
      <% if (recentIssues && recentIssues.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reported</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% recentIssues.forEach(issue => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#<%= issue.id %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= issue.device_name || 'Unknown Device' %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= issue.description %></td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      <% if (issue.status === 'open') { %>
                        bg-red-100 text-red-800
                      <% } else if (issue.status === 'in_progress') { %>
                        bg-yellow-100 text-yellow-800
                      <% } else if (issue.status === 'resolved') { %>
                        bg-green-100 text-green-800
                      <% } %>">
                      <%= issue.status.replace('_', ' ').toUpperCase() %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(issue.created_at) %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="/it-admin/issues/<%= issue.id %>" class="text-indigo-600 hover:text-indigo-900">View</a>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
        <div class="mt-4 text-right">
          <a href="/it-admin/issues" class="text-indigo-600 hover:text-indigo-900 font-medium">View All Issues</a>
        </div>
      <% } else { %>
        <div class="text-center py-4">
          <p class="text-gray-500">No recent issues found.</p>
        </div>
      <% } %>
    </div>
  </div>


</div>

<!-- Quick Actions - Futuristic Design -->
<div class="mt-8 mb-8">
  <h2 class="text-xl font-bold text-it-admin-primary mb-4">Quick Actions</h2>
  <div class="grid grid-cols-2 gap-4">
    <a href="/it-admin/inventory/add" class="bg-white hover:bg-it-admin-light p-5 rounded-xl shadow-lg border border-gray-100 text-center transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl group">
      <div class="bg-it-admin-primary bg-opacity-10 rounded-full p-4 mx-auto w-16 h-16 flex items-center justify-center mb-3 group-hover:bg-it-admin-primary group-hover:bg-opacity-100 transition-all duration-300">
        <svg class="w-8 h-8 text-it-admin-primary group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
      </div>
      <span class="text-gray-800 font-medium block">Add Device</span>
      <span class="text-gray-500 text-xs mt-1 block">Register new hardware</span>
    </a>

    <a href="/it-admin/issues/create" class="bg-white hover:bg-it-admin-light p-5 rounded-xl shadow-lg border border-gray-100 text-center transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl group">
      <div class="bg-red-100 rounded-full p-4 mx-auto w-16 h-16 flex items-center justify-center mb-3 group-hover:bg-red-500 transition-all duration-300">
        <svg class="w-8 h-8 text-red-500 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
        </svg>
      </div>
      <span class="text-gray-800 font-medium block">Report Issue</span>
      <span class="text-gray-500 text-xs mt-1 block">Create new ticket</span>
    </a>

    <a href="/it-admin/inventory/export" class="bg-white hover:bg-it-admin-light p-5 rounded-xl shadow-lg border border-gray-100 text-center transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl group">
      <div class="bg-blue-100 rounded-full p-4 mx-auto w-16 h-16 flex items-center justify-center mb-3 group-hover:bg-blue-500 transition-all duration-300">
        <svg class="w-8 h-8 text-blue-500 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
        </svg>
      </div>
      <span class="text-gray-800 font-medium block">Export Data</span>
      <span class="text-gray-500 text-xs mt-1 block">Download inventory</span>
    </a>

    <a href="/it-admin/vendors" class="bg-white hover:bg-it-admin-light p-5 rounded-xl shadow-lg border border-gray-100 text-center transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl group">
      <div class="bg-amber-100 rounded-full p-4 mx-auto w-16 h-16 flex items-center justify-center mb-3 group-hover:bg-amber-500 transition-all duration-300">
        <svg class="w-8 h-8 text-amber-500 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
      </div>
      <span class="text-gray-800 font-medium block">Manage Vendors</span>
      <span class="text-gray-500 text-xs mt-1 block">View repair partners</span>
    </a>
  </div>
</div>

<!-- Recent Activity -->
<div class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-100 mb-8">
  <div class="bg-it-admin-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Recent Activity</h2>
    <span class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">Last 24 hours</span>
  </div>
  <div class="p-6">
    <div class="space-y-4">
      <div class="flex items-start">
        <div class="bg-blue-100 rounded-full p-2 mr-3">
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-800 font-medium">System backup completed</p>
          <p class="text-gray-500 text-sm">Today, 3:00 AM</p>
        </div>
      </div>

      <div class="flex items-start">
        <div class="bg-green-100 rounded-full p-2 mr-3">
          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-800 font-medium">5 devices returned from repair</p>
          <p class="text-gray-500 text-sm">Yesterday, 2:45 PM</p>
        </div>
      </div>

      <div class="flex items-start">
        <div class="bg-amber-100 rounded-full p-2 mr-3">
          <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-800 font-medium">3 new issues reported</p>
          <p class="text-gray-500 text-sm">Yesterday, 10:30 AM</p>
        </div>
      </div>
    </div>
  </div>
</div>
