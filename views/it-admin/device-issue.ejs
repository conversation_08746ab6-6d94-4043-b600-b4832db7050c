<h1 class="text-2xl font-bold mb-6">Issue Device</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Issue Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/transactions" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>

    <!-- Quick Filters Section -->
    <div class="p-4 border-b border-gray-200">
        <div class="mb-2 flex items-center">
            <h3 class="text-sm font-semibold text-gray-700 mr-2">Quick Filters:</h3>
            <button type="button" id="clearFilters" class="text-xs text-blue-600 hover:text-blue-800">Clear All</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Device Category Filter -->
            <div>
                <label for="categoryFilter" class="block text-xs font-medium text-gray-500 mb-1">Device Category</label>
                <select id="categoryFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Categories</option>
                    <% if (locals.categories) { %>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>"><%= category.name %></option>
                        <% }); %>
                    <% } %>
                </select>
            </div>

            <!-- Device Condition Filter -->
            <div>
                <label for="conditionFilter" class="block text-xs font-medium text-gray-500 mb-1">Condition</label>
                <select id="conditionFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">Any Condition</option>
                    <option value="Excellent">Excellent</option>
                    <option value="Good">Good</option>
                    <option value="Fair">Fair</option>
                    <option value="Poor">Poor</option>
                </select>
            </div>

            <!-- User Role/Department Filter (if available) -->
            <div>
                <label for="userTypeFilter" class="block text-xs font-medium text-gray-500 mb-1">User Type</label>
                <select id="userTypeFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Users</option>
                    <option value="student">Students</option>
                    <option value="teacher">Teachers</option>
                    <option value="admin">Administrators</option>
                    <option value="staff">Staff</option>
                </select>
            </div>

            <!-- Search Box -->
            <div>
                <label for="quickSearch" class="block text-xs font-medium text-gray-500 mb-1">Quick Search</label>
                <input type="text" id="quickSearch" placeholder="Search devices or users..." class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
            </div>
        </div>
    </div>

    <div class="p-6">
        <form action="/it-admin/transactions/issue" method="POST" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Device *</label>
                    <select id="item_id" name="item_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select Device</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= query && query.item == item.item_id ? 'selected' : '' %>>
                                <%= item.name %> (<%= item.category_name || 'No Type' %>, <%= item.serial_number || 'No S/N' %><%= item.model ? ', ' + item.model : '' %>)
                            </option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="issued_to" class="block text-sm font-medium text-gray-700 mb-1">Issue To *</label>
                    <select id="issued_to" name="issued_to" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select User</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>" <%= query && query.user == user.id ? 'selected' : '' %>>
                                <%= user.name || user.username %>
                            </option>
                        <% }); %>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="expected_return_date" class="block text-sm font-medium text-gray-700 mb-1">Expected Return Date</label>
                    <input type="date" id="expected_return_date" name="expected_return_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="condition_on_issue" class="block text-sm font-medium text-gray-700 mb-1">Condition on Issue</label>
                    <select id="condition_on_issue" name="condition_on_issue" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="Excellent">Excellent</option>
                        <option value="Good" selected>Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                    </select>
                </div>
            </div>

            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <a href="/it-admin/transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Issue Device</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for select dropdowns
        $('#item_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Device'
        });

        $('#issued_to').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select User'
        });

        // Initialize filter dropdowns with chosen
        $('#categoryFilter').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'All Categories'
        });

        $('#conditionFilter').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Any Condition'
        });

        $('#userTypeFilter').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'All Users'
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const itemInput = document.getElementById('item_id');
            const userInput = document.getElementById('issued_to');

            if (!itemInput.value) {
                e.preventDefault();
                alert('Please select a device');
                return;
            }

            if (!userInput.value) {
                e.preventDefault();
                alert('Please select a user');
                return;
            }
        });

        // Quick filter functionality
        const deviceSelect = document.getElementById('item_id');
        const userSelect = document.getElementById('issued_to');
        const categoryFilter = document.getElementById('categoryFilter');
        const conditionFilter = document.getElementById('conditionFilter');
        const userTypeFilter = document.getElementById('userTypeFilter');
        const quickSearch = document.getElementById('quickSearch');
        const clearFiltersBtn = document.getElementById('clearFilters');

        // Store original options for resetting
        const originalDeviceOptions = Array.from(deviceSelect.options).map(opt => ({
            value: opt.value,
            text: opt.text,
            html: opt.innerHTML
        }));

        const originalUserOptions = Array.from(userSelect.options).map(opt => ({
            value: opt.value,
            text: opt.text,
            html: opt.innerHTML
        }));

        // Function to apply filters
        function applyFilters() {
            // Get filter values
            const categoryValue = categoryFilter.value;
            const conditionValue = conditionFilter.value;
            const userTypeValue = userTypeFilter.value;
            const searchValue = quickSearch.value.toLowerCase();

            // Filter devices
            let deviceOptions = originalDeviceOptions;

            if (categoryValue) {
                deviceOptions = deviceOptions.filter(opt => {
                    // Extract category from option text (assuming format includes category name in parentheses)
                    const categoryMatch = opt.text.match(/\(([^,]+)/);
                    return categoryMatch && categoryMatch[1].trim() === categoryValue;
                });
            }

            if (searchValue) {
                deviceOptions = deviceOptions.filter(opt =>
                    opt.text.toLowerCase().includes(searchValue)
                );
            }

            // Filter users
            let userOptions = originalUserOptions;

            if (userTypeValue) {
                // This assumes there's some indication of user type in the option text
                // You might need to adjust this based on your actual data structure
                userOptions = userOptions.filter(opt =>
                    opt.text.toLowerCase().includes(userTypeValue.toLowerCase())
                );
            }

            if (searchValue) {
                userOptions = userOptions.filter(opt =>
                    opt.text.toLowerCase().includes(searchValue)
                );
            }

            // Update select options
            updateSelectOptions(deviceSelect, deviceOptions);
            updateSelectOptions(userSelect, userOptions);

            // Refresh chosen dropdowns
            $(deviceSelect).trigger('chosen:updated');
            $(userSelect).trigger('chosen:updated');
        }

        // Function to update select options
        function updateSelectOptions(select, options) {
            // Keep the first option (placeholder)
            const firstOption = select.options[0];

            // Remove all other options
            while (select.options.length > 1) {
                select.remove(1);
            }

            // Add filtered options
            options.forEach(opt => {
                if (opt.value) { // Skip empty value options
                    const option = document.createElement('option');
                    option.value = opt.value;
                    option.innerHTML = opt.html;
                    select.appendChild(option);
                }
            });
        }

        // Function to reset filters
        function resetFilters() {
            categoryFilter.value = '';
            conditionFilter.value = '';
            userTypeFilter.value = '';
            quickSearch.value = '';

            // Reset select options
            updateSelectOptions(deviceSelect, originalDeviceOptions);
            updateSelectOptions(userSelect, originalUserOptions);

            // Refresh chosen dropdowns
            $(categoryFilter).trigger('chosen:updated');
            $(conditionFilter).trigger('chosen:updated');
            $(userTypeFilter).trigger('chosen:updated');
            $(deviceSelect).trigger('chosen:updated');
            $(userSelect).trigger('chosen:updated');
        }

        // Add event listeners to filters
        categoryFilter.addEventListener('change', applyFilters);
        conditionFilter.addEventListener('change', applyFilters);
        userTypeFilter.addEventListener('change', applyFilters);
        quickSearch.addEventListener('input', applyFilters);
        clearFiltersBtn.addEventListener('click', resetFilters);
    });
</script>
