<h1 class="text-2xl font-bold mb-6">Receive Device</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Receive Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/transactions" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>
    <div class="p-6">
        <% if (transactions && transactions.length > 0) { %>
            <form action="/it-admin/transactions/receive" method="POST" class="space-y-6">
                <div>
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-1">Select Device to Receive *</label>
                    <select id="transaction_id" name="transaction_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select Device</option>
                        <% transactions.forEach(transaction => { %>
                            <option value="<%= transaction.transaction_id %>" <%= selectedTransactionId && selectedTransactionId == transaction.transaction_id ? 'selected' : '' %>>
                                <%= transaction.item_name %> (<%= transaction.serial_number || 'No S/N' %>) - Issued to <%= transaction.issued_to_name %> on <%= transaction.issued_date %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="condition_on_return" class="block text-sm font-medium text-gray-700 mb-1">Condition on Return</label>
                        <select id="condition_on_return" name="condition_on_return" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Excellent">Excellent</option>
                            <option value="Good" selected>Good</option>
                            <option value="Fair">Fair</option>
                            <option value="Poor">Poor</option>
                        </select>
                    </div>
                    <div>
                        <label for="create_issue_ticket" class="block text-sm font-medium text-gray-700 mb-1">Create Issue Ticket?</label>
                        <div class="flex items-center mt-2">
                            <input type="checkbox" id="create_issue_ticket" name="create_issue_ticket" value="1" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                            <label for="create_issue_ticket" class="ml-2 block text-sm text-gray-900">
                                Create an issue ticket for this device
                            </label>
                        </div>
                    </div>
                </div>

                <div id="quick_condition_section" class="hidden border p-4 rounded-md bg-gray-50 mt-4">
                    <h3 class="font-medium text-gray-700 mb-3">Select Issues (if any)</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="flex items-center">
                                <input type="checkbox" id="physical_damage" name="quick_condition[]" value="physical_damage" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="physical_damage" class="ml-2 block text-sm text-gray-900">
                                    Physical damage
                                </label>
                            </div>
                            <div class="flex items-center mt-2">
                                <input type="checkbox" id="screen_issues" name="quick_condition[]" value="screen_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="screen_issues" class="ml-2 block text-sm text-gray-900">
                                    Screen issues
                                </label>
                            </div>
                            <div class="flex items-center mt-2">
                                <input type="checkbox" id="battery_issues" name="quick_condition[]" value="battery_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="battery_issues" class="ml-2 block text-sm text-gray-900">
                                    Battery issues
                                </label>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <input type="checkbox" id="keyboard_issues" name="quick_condition[]" value="keyboard_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="keyboard_issues" class="ml-2 block text-sm text-gray-900">
                                    Keyboard/input issues
                                </label>
                            </div>
                            <div class="flex items-center mt-2">
                                <input type="checkbox" id="connectivity_issues" name="quick_condition[]" value="connectivity_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="connectivity_issues" class="ml-2 block text-sm text-gray-900">
                                    Connectivity issues
                                </label>
                            </div>
                            <div class="flex items-center mt-2">
                                <input type="checkbox" id="software_issues" name="quick_condition[]" value="software_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <label for="software_issues" class="ml-2 block text-sm text-gray-900">
                                    Software issues
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="quick_condition_notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Issue Details</label>
                        <textarea id="quick_condition_notes" name="quick_condition_notes" rows="2" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                    </div>
                </div>

                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <a href="/it-admin/transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                    <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Receive Device</button>
                </div>
            </form>
        <% } else { %>
            <div class="text-center py-8">
                <p class="text-gray-500 mb-4">No devices are currently issued out.</p>
                <a href="/it-admin/transactions" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">
                    Back to Transactions
                </a>
            </div>
        <% } %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for select dropdowns
        $('#transaction_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Device'
        });
        
        // Toggle issue ticket section
        const createIssueTicket = document.getElementById('create_issue_ticket');
        const quickConditionSection = document.getElementById('quick_condition_section');
        
        if (createIssueTicket && quickConditionSection) {
            createIssueTicket.addEventListener('change', function() {
                if (this.checked) {
                    quickConditionSection.classList.remove('hidden');
                } else {
                    quickConditionSection.classList.add('hidden');
                }
            });
        }

        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const transactionInput = document.getElementById('transaction_id');
                
                if (!transactionInput.value) {
                    e.preventDefault();
                    alert('Please select a device to receive');
                    return;
                }
            });
        }
    });
</script>
