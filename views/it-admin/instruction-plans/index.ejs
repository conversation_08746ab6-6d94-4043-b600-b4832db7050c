<%- include('../../partials/it-admin/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-gray-800 text-white p-4">
      <h2 class="text-xl font-semibold">Instruction Plans</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">All Instruction Plans</h3>

        <% if (plans && plans.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Teacher</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% plans.forEach(plan => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.title %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.teacher_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        <%= plan.status === 'published' ? 'bg-green-100 text-green-800' : 
                           plan.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
                           'bg-red-100 text-red-800' %>">
                        <%= plan.status.charAt(0).toUpperCase() + plan.status.slice(1) %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <%= new Date(plan.created_at).toLocaleDateString() %>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <a href="/it-admin/instruction-plans/<%= plan.id %>" class="text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1">
                        View
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  No instruction plans found in the system.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">System Resources</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-gray-50 p-4 rounded border border-gray-200">
            <h4 class="font-medium text-gray-700 mb-2">Storage Usage</h4>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
              <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
            </div>
            <p class="text-sm text-gray-600">45% of allocated storage used</p>
          </div>
          <div class="bg-gray-50 p-4 rounded border border-gray-200">
            <h4 class="font-medium text-gray-700 mb-2">Resource Count</h4>
            <p class="text-2xl font-bold text-gray-800">
              <%= plans.reduce((count, plan) => {
                return count + (plan.resource_count || 0);
              }, 0) %>
            </p>
            <p class="text-sm text-gray-600">Total resources across all plans</p>
          </div>
          <div class="bg-gray-50 p-4 rounded border border-gray-200">
            <h4 class="font-medium text-gray-700 mb-2">System Health</h4>
            <p class="text-sm px-2 py-1 rounded bg-green-100 text-green-800 inline-block">Operational</p>
            <p class="text-sm text-gray-600 mt-2">All systems functioning normally</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%- include('../../partials/it-admin/footer') %>
