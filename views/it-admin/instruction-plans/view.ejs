<%- include('../../partials/it-admin/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-gray-800 text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Instruction Plan Details</h2>
      <a href="/it-admin/instruction-plans" class="bg-white text-gray-800 px-3 py-1 rounded text-sm hover:bg-gray-100 transition">
        Back to Plans
      </a>
    </div>

    <div class="p-6">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-2"><%= plan.title %></h3>
        <div class="flex items-center mb-4">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
            <%= plan.status === 'published' ? 'bg-green-100 text-green-800' : 
               plan.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
               'bg-red-100 text-red-800' %>">
            <%= plan.status.charAt(0).toUpperCase() + plan.status.slice(1) %>
          </span>
          <span class="ml-4 text-gray-500">
            Created: <%= new Date(plan.created_at).toLocaleDateString() %>
          </span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h4 class="text-lg font-semibold mb-2">Subject</h4>
            <p class="text-gray-700"><%= plan.subject_name %></p>
          </div>
          
          <div>
            <h4 class="text-lg font-semibold mb-2">Teacher</h4>
            <p class="text-gray-700"><%= plan.teacher_name %></p>
          </div>
        </div>
        
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Description</h4>
          <p class="text-gray-700"><%= plan.description || 'No description provided' %></p>
        </div>
        
        <% if (resources && resources.length > 0) { %>
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Resources</h4>
          <ul class="list-disc pl-5">
            <% resources.forEach(resource => { %>
              <li class="mb-2">
                <% if (resource.resource_type === 'file') { %>
                  <a href="<%= resource.resource_path %>" 
                     class="text-blue-600 hover:underline" 
                     target="_blank">
                    <%= resource.resource_name %>
                  </a>
                <% } else if (resource.resource_type === 'link') { %>
                  <a href="<%= resource.resource_path %>" 
                     class="text-blue-600 hover:underline" 
                     target="_blank">
                    <%= resource.resource_name %> (External Link)
                  </a>
                <% } else { %>
                  <span class="font-medium"><%= resource.resource_name %></span>
                <% } %>
              </li>
            <% }); %>
          </ul>
        </div>
        <% } %>
        
        <div class="mt-8 border-t pt-6">
          <h4 class="text-lg font-semibold mb-2">System Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Storage Size:</span> 
                <%= resources.reduce((size, resource) => {
                  return size + (resource.file_size || 0);
                }, 0) / 1024 / 1024 %>MB
              </p>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Resource Count:</span> 
                <%= resources.length %>
              </p>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Last Modified:</span> 
                <%= new Date(plan.updated_at || plan.created_at).toLocaleString() %>
              </p>
            </div>
            <div>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Plan ID:</span> 
                <%= plan.id %>
              </p>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Created By:</span> 
                <%= plan.teacher_name %> (ID: <%= plan.teacher_id %>)
              </p>
              <p class="text-sm text-gray-600">
                <span class="font-medium">Database Table:</span> 
                instruction_plans
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%- include('../../partials/it-admin/footer') %>
