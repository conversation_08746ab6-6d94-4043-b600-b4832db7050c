<h1 class="text-2xl font-bold mb-6">Add New Inventory Item</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Item Details</h2>
        <a href="/it-admin/inventory" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
            <i class="fas fa-arrow-left mr-2"></i> Back to Inventory
        </a>
    </div>

    <div class="p-6">
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        Fields marked with an asterisk (*) are required.
                    </p>
                </div>
            </div>
        </div>
        <form id="add-inventory-form" action="/it-admin/inventory/add-simple" method="POST" class="space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                        <input type="text" id="name" name="name" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Category *</label>
                        <select id="category_id" name="category_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="">Select Category</option>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.category_id %>"><%= category.name %></option>
                            <% }); %>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Technical Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-1">Serial Number *</label>
                        <input type="text" id="serial_number" name="serial_number" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 mb-1">Model *</label>
                        <input type="text" id="model" name="model" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Manufacturer *</label>
                        <input type="text" id="manufacturer" name="manufacturer" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Purchase Information -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Purchase Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-1">Purchase Date *</label>
                        <input type="date" id="purchase_date" name="purchase_date" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="purchase_cost" class="block text-sm font-medium text-gray-700 mb-1">Purchase Cost *</label>
                        <input type="number" id="purchase_cost" name="purchase_cost" step="0.01" min="0" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="warranty_expiry" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry *</label>
                        <input type="date" id="warranty_expiry" name="warranty_expiry" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Status and Location -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Status and Location</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="available">Available</option>
                            <option value="assigned">Assigned</option>
                            <option value="maintenance">In Maintenance</option>
                            <option value="retired">Retired</option>
                        </select>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <input type="text" id="location" name="location" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                    </div>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Item Images</h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="item_images" class="block text-sm font-medium text-gray-700 mb-1">Upload Images</label>
                        <input type="file" id="item_images" name="item_images" multiple accept="image/*,.pdf" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-it-admin-light file:text-it-admin-primary hover:file:bg-it-admin-light">
                        <p class="mt-1 text-sm text-gray-500">Upload one or more images of the item (optional, max 5 images, 10MB each, JPG or PDF)</p>
                    </div>
                    <div id="image_previews_container" class="mt-2">
                        <p class="text-sm font-medium text-gray-700 mb-1">Previews:</p>
                        <div id="image_previews" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"></div>
                    </div>
                </div>
            </div>

            <!-- Network Information (for computers, network devices, etc.) -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Network Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="hostname" class="block text-sm font-medium text-gray-700 mb-1">Hostname</label>
                        <input type="text" id="hostname" name="hostname" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                        <input type="text" id="ip_address" name="ip_address" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="mac_address" class="block text-sm font-medium text-gray-700 mb-1">MAC Address</label>
                        <input type="text" id="mac_address" name="mac_address" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Laptop Condition (only shown for laptop category) -->
            <div id="laptop_condition_section" class="bg-gray-50 p-4 rounded-lg border border-gray-200" style="display: none;">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Laptop Condition</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="physical_damage" class="block text-sm font-medium text-gray-700 mb-1">Physical Damage</label>
                        <select id="physical_damage" name="physical_damage" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="None">None</option>
                            <option value="Minor">Minor</option>
                            <option value="Moderate">Moderate</option>
                            <option value="Severe">Severe</option>
                        </select>
                    </div>

                    <div>
                        <label for="keyboard_condition" class="block text-sm font-medium text-gray-700 mb-1">Keyboard</label>
                        <select id="keyboard_condition" name="keyboard_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Working">Working</option>
                            <option value="Partially Working">Partially Working</option>
                            <option value="Not Working">Not Working</option>
                        </select>
                    </div>

                    <div>
                        <label for="touchpad_condition" class="block text-sm font-medium text-gray-700 mb-1">Touchpad</label>
                        <select id="touchpad_condition" name="touchpad_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Working">Working</option>
                            <option value="Partially Working">Partially Working</option>
                            <option value="Not Working">Not Working</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="display_condition" class="block text-sm font-medium text-gray-700 mb-1">Display</label>
                        <select id="display_condition" name="display_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Perfect">Perfect</option>
                            <option value="Minor Issues">Minor Issues</option>
                            <option value="Major Issues">Major Issues</option>
                            <option value="Not Working">Not Working</option>
                        </select>
                    </div>

                    <div>
                        <label for="battery_condition" class="block text-sm font-medium text-gray-700 mb-1">Battery</label>
                        <select id="battery_condition" name="battery_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Good">Good</option>
                            <option value="Fair">Fair</option>
                            <option value="Poor">Poor</option>
                            <option value="Not Working">Not Working</option>
                        </select>
                    </div>

                    <div>
                        <label for="charger_condition" class="block text-sm font-medium text-gray-700 mb-1">Charger</label>
                        <select id="charger_condition" name="charger_condition" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="Included">Included</option>
                            <option value="Not Included">Not Included</option>
                            <option value="Not Working">Not Working</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <a href="/it-admin/inventory" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Save Item</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced form validation
        const form = document.getElementById('add-inventory-form');

        form.addEventListener('submit', function(e) {
            // Define required fields
            const requiredFields = [
                { id: 'name', label: 'Item Name' },
                { id: 'category_id', label: 'Category' },
                { id: 'serial_number', label: 'Serial Number' },
                { id: 'model', label: 'Model' },
                { id: 'manufacturer', label: 'Manufacturer' },
                { id: 'purchase_date', label: 'Purchase Date' },
                { id: 'warranty_expiry', label: 'Warranty Expiry' },
                { id: 'purchase_cost', label: 'Purchase Cost' }
            ];

            // Check each required field
            let hasErrors = false;
            let errorMessage = 'Please fill in the following required fields:\n';

            requiredFields.forEach(field => {
                const input = document.getElementById(field.id);
                if (!input.value || input.value.trim() === '') {
                    hasErrors = true;
                    errorMessage += `- ${field.label}\n`;
                    input.classList.add('border-red-500');
                } else {
                    input.classList.remove('border-red-500');
                }
            });

            // If there are errors, prevent form submission and show error message
            if (hasErrors) {
                e.preventDefault();
                alert(errorMessage);
                return false;
            }

            // If validation passes, show a loading message
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Saving...';

            // Allow form to submit normally
            return true;
        });

        // Multiple image preview
        const imageInput = document.getElementById('item_images');
        const imagePreviews = document.getElementById('image_previews');

        imageInput.addEventListener('change', function() {
            // Clear previous previews
            imagePreviews.innerHTML = '';

            if (this.files && this.files.length > 0) {
                // Limit to 5 images
                const maxImages = 5;
                const filesToProcess = Array.from(this.files).slice(0, maxImages);

                if (this.files.length > maxImages) {
                    alert(`You can upload a maximum of ${maxImages} images. Only the first ${maxImages} will be processed.`);
                }

                filesToProcess.forEach(file => {
                    // Check file type (image or PDF)
                    if (!file.type.match('image.*') && file.type !== 'application/pdf') {
                        alert(`File "${file.name}" is not a valid type. Only images and PDFs are allowed.`);
                        return;
                    }

                    // Check file size (10MB limit)
                    const maxSizeInBytes = 10 * 1024 * 1024;
                    if (file.size > maxSizeInBytes) {
                        alert(`File "${file.name}" is too large. Maximum size is 10MB. Your file is ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'relative';

                        if (file.type === 'application/pdf') {
                            // For PDFs, show an icon instead of preview
                            const pdfIcon = document.createElement('div');
                            pdfIcon.className = 'w-full h-32 flex items-center justify-center bg-gray-100 rounded-md border border-gray-300';
                            pdfIcon.innerHTML = '<i class="fas fa-file-pdf text-red-500 text-4xl"></i>';

                            previewDiv.appendChild(pdfIcon);
                        } else {
                            // For images, show the preview
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'w-full h-32 object-cover rounded-md border border-gray-300';
                            img.alt = 'Image Preview';

                            previewDiv.appendChild(img);
                        }

                        const fileNameDiv = document.createElement('div');
                        fileNameDiv.className = 'mt-1 text-xs text-gray-500 truncate';
                        fileNameDiv.textContent = file.name;

                        previewDiv.appendChild(fileNameDiv);
                        imagePreviews.appendChild(previewDiv);
                    };

                    reader.readAsDataURL(file);
                });
            }
        });

        // Show/hide laptop condition section based on category selection
        const categorySelect = document.getElementById('category_id');
        const laptopConditionSection = document.getElementById('laptop_condition_section');

        function toggleLaptopConditionSection() {
            // Check if the selected category is for laptops
            const selectedCategory = categorySelect.options[categorySelect.selectedIndex].text.toLowerCase();
            if (selectedCategory.includes('laptop')) {
                laptopConditionSection.style.display = 'block';
            } else {
                laptopConditionSection.style.display = 'none';
            }
        }

        // Initial check
        toggleLaptopConditionSection();

        // Listen for changes
        categorySelect.addEventListener('change', toggleLaptopConditionSection);
    });
</script>
