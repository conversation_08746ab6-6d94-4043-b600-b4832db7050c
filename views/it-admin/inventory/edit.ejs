<h1 class="text-2xl font-bold mb-6">Edit Inventory Item</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Item Details</h2>
        <a href="/it-admin/inventory/<%= item.item_id %>" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
            <i class="fas fa-arrow-left mr-2"></i> Back to Item Details
        </a>
    </div>

    <div class="p-6">
        <form id="edit-inventory-form" action="/it-admin/inventory/<%= item.item_id %>/edit" method="POST" class="space-y-6" enctype="multipart/form-data">
            <!-- Basic Information -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                        <input type="text" id="name" name="name" value="<%= item.name %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select id="category_id" name="category_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="">Select Category</option>
                            <% categories.forEach(category => { %>
                                <option value="<%= category.category_id %>" <%= item.category_id === category.category_id ? 'selected' : '' %>><%= category.name %></option>
                            <% }); %>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"><%= item.description || '' %></textarea>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Technical Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-1">Serial Number</label>
                        <input type="text" id="serial_number" name="serial_number" value="<%= item.serial_number || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 mb-1">Model</label>
                        <input type="text" id="model" name="model" value="<%= item.model || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="manufacturer" class="block text-sm font-medium text-gray-700 mb-1">Manufacturer</label>
                        <input type="text" id="manufacturer" name="manufacturer" value="<%= item.manufacturer || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Purchase Information -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Purchase Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-1">Purchase Date</label>
                        <input type="date" id="purchase_date" name="purchase_date" value="<%= item.purchase_date ? new Date(item.purchase_date).toISOString().split('T')[0] : '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="purchase_cost" class="block text-sm font-medium text-gray-700 mb-1">Purchase Cost</label>
                        <input type="number" id="purchase_cost" name="purchase_cost" value="<%= item.purchase_cost || '' %>" step="0.01" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="warranty_expiry" class="block text-sm font-medium text-gray-700 mb-1">Warranty Expiry</label>
                        <input type="date" id="warranty_expiry" name="warranty_expiry" value="<%= item.warranty_expiry ? new Date(item.warranty_expiry).toISOString().split('T')[0] : '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Status and Location -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Status and Location</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="available" <%= item.status === 'available' ? 'selected' : '' %>>Available</option>
                            <option value="assigned" <%= item.status === 'assigned' ? 'selected' : '' %>>Assigned</option>
                            <option value="maintenance" <%= item.status === 'maintenance' ? 'selected' : '' %>>In Maintenance</option>
                            <option value="retired" <%= item.status === 'retired' ? 'selected' : '' %>>Retired</option>
                        </select>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <input type="text" id="location" name="location" value="<%= item.location || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"><%= item.notes || '' %></textarea>
                    </div>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Item Images</h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="item_images" class="block text-sm font-medium text-gray-700 mb-1">Upload Images</label>
                        <input type="file" id="item_images" name="item_images" multiple accept=".jpg,.jpeg,.pdf,application/pdf,image/jpeg" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-it-admin-light file:text-it-admin-primary hover:file:bg-it-admin-light">
                        <p class="mt-1 text-sm text-gray-500">Upload one or more images of the item (optional, max 5 images, JPG or PDF only, 10MB each)</p>
                    </div>
                    <% if (item.image) { %>
                        <div class="mt-2">
                            <p class="text-sm font-medium text-gray-700 mb-1">Current Primary Image:</p>
                            <div class="relative h-40 w-full overflow-hidden rounded-md border border-gray-300">
                                <img src="<%= item.image %>" alt="Item Image" class="absolute inset-0 w-full h-full object-contain" onerror="this.onerror=null; this.src='/img/no-image.png'; this.classList.add('p-4');">
                                <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-30">
                                    <a href="<%= item.image %>" target="_blank" class="bg-white p-2 rounded-full">
                                        <i class="fas fa-search-plus text-gray-700"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="mt-2 flex items-center">
                                <input type="checkbox" id="keep_current_image" name="keep_current_image" value="1" checked class="mr-2">
                                <label for="keep_current_image" class="text-sm text-gray-700">Keep current image if no new images are uploaded</label>
                            </div>
                        </div>
                    <% } %>
                    <div id="file_previews_container" class="mt-3 hidden">
                        <p class="text-sm font-medium text-gray-700 mb-2">New File Previews:</p>
                        <div id="file_previews" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"></div>
                    </div>
                </div>
            </div>

            <!-- Network Information -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-700 mb-4">Network Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="hostname" class="block text-sm font-medium text-gray-700 mb-1">Hostname</label>
                        <input type="text" id="hostname" name="hostname" value="<%= networkInfo?.hostname || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                        <input type="text" id="ip_address" name="ip_address" value="<%= networkInfo?.ip_address || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="mac_address" class="block text-sm font-medium text-gray-700 mb-1">MAC Address</label>
                        <input type="text" id="mac_address" name="mac_address" value="<%= networkInfo?.mac_address || '' %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <a href="/it-admin/inventory/<%= item.item_id %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Update Item</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to show toast notifications
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-4 py-2 rounded shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            toast.textContent = message;
            document.body.appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 3000);
        }

        // Function to convert file to base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        // Function to validate file type (JPG or PDF only)
        function isValidFileType(file) {
            const validTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];
            return validTypes.includes(file.type);
        }

        // Function to create file preview
        function createFilePreview(file, index) {
            const previewContainer = document.getElementById('file_previews');
            const previewItem = document.createElement('div');
            previewItem.className = 'border rounded-lg overflow-hidden bg-white shadow-sm relative';
            previewItem.id = `file-preview-${index}`;

            // Create remove button
            const removeButton = document.createElement('button');
            removeButton.type = 'button';
            removeButton.className = 'absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10';
            removeButton.innerHTML = '<i class="fas fa-times"></i>';
            removeButton.onclick = function() {
                // Remove this file from the input
                const dt = new DataTransfer();
                const input = document.getElementById('item_images');
                const { files } = input;

                for (let i = 0; i < files.length; i++) {
                    if (i !== index) {
                        dt.items.add(files[i]);
                    }
                }

                input.files = dt.files;

                // Remove the preview
                previewItem.remove();

                // Hide container if empty
                if (previewContainer.children.length === 0) {
                    document.getElementById('file_previews_container').classList.add('hidden');
                }
            };

            // Create preview content based on file type
            const previewContainer = document.createElement('div');
            previewContainer.className = 'relative h-32 w-full overflow-hidden';

            if (file.type.startsWith('image/')) {
                // Image preview
                const img = document.createElement('img');
                img.className = 'absolute inset-0 w-full h-full object-contain';
                img.alt = file.name;

                // Read the file as data URL
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

                previewContainer.appendChild(img);
            } else if (file.type === 'application/pdf') {
                // PDF preview (icon)
                const pdfPreview = document.createElement('div');
                pdfPreview.className = 'w-full h-full bg-gray-100 flex items-center justify-center';
                pdfPreview.innerHTML = '<i class="fas fa-file-pdf text-red-500 text-4xl"></i>';

                previewContainer.appendChild(pdfPreview);
            }

            previewItem.appendChild(previewContainer);

            // Add file name
            const fileName = document.createElement('div');
            fileName.className = 'p-2 text-xs truncate';
            fileName.title = file.name;
            fileName.textContent = file.name;

            previewItem.appendChild(removeButton);
            previewItem.appendChild(fileName);
            previewContainer.appendChild(previewItem);

            // Show the container
            document.getElementById('file_previews_container').classList.remove('hidden');
        }

        // Add event listener for file input change
        document.getElementById('item_images').addEventListener('change', function(e) {
            const files = e.target.files;
            const previewContainer = document.getElementById('file_previews');

            // Clear previous previews
            previewContainer.innerHTML = '';
            document.getElementById('file_previews_container').classList.add('hidden');

            // Check if files are selected
            if (files.length === 0) return;

            // Check file count
            if (files.length > 5) {
                showToast('Maximum 5 files allowed', 'error');
                e.target.value = '';
                return;
            }

            // Validate and preview each file
            let hasInvalidFile = false;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Check file type
                if (!isValidFileType(file)) {
                    hasInvalidFile = true;
                    continue;
                }

                // Check file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    showToast(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                    hasInvalidFile = true;
                    continue;
                }

                // Create preview
                createFilePreview(file, i);
            }

            // If any invalid files, clear the input
            if (hasInvalidFile) {
                showToast('Only JPG and PDF files are allowed. Maximum size is 10MB.', 'error');
                e.target.value = '';
                previewContainer.innerHTML = '';
                document.getElementById('file_previews_container').classList.add('hidden');
            }
        });

        // Form submission with base64 encoding
        const form = document.getElementById('edit-inventory-form');
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Basic validation
            const nameInput = document.getElementById('name');
            if (!nameInput.value.trim()) {
                showToast('Item name is required', 'error');
                nameInput.focus();
                return;
            }

            // Show loading message
            showToast('Processing your request...', 'info');

            try {
                // Create a data object to hold all form values
                const formData = {
                    name: document.getElementById('name').value,
                    description: document.getElementById('description').value,
                    category_id: document.getElementById('category_id').value,
                    serial_number: document.getElementById('serial_number').value,
                    model: document.getElementById('model').value,
                    manufacturer: document.getElementById('manufacturer').value,
                    purchase_date: document.getElementById('purchase_date').value,
                    purchase_cost: document.getElementById('purchase_cost').value,
                    warranty_expiry: document.getElementById('warranty_expiry').value,
                    status: document.getElementById('status').value,
                    location: document.getElementById('location').value,
                    notes: document.getElementById('notes').value,

                    // Network information
                    hostname: document.getElementById('hostname').value,
                    ip_address: document.getElementById('ip_address').value,
                    mac_address: document.getElementById('mac_address').value,

                    // Keep current image checkbox
                    keep_current_image: document.getElementById('keep_current_image')?.checked || false
                };

                // Handle file uploads using base64
                const fileInput = document.getElementById('item_images');
                if (fileInput && fileInput.files.length > 0) {
                    // Validate files again
                    let allFilesValid = true;
                    const files = fileInput.files;

                    // Check file count
                    if (files.length > 5) {
                        showToast('Maximum 5 files allowed', 'error');
                        return;
                    }

                    // Validate each file
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];

                        // Check file type
                        if (!isValidFileType(file)) {
                            showToast(`File ${file.name} is not a valid type. Only JPG and PDF are allowed.`, 'error');
                            allFilesValid = false;
                            break;
                        }

                        // Check file size (max 10MB)
                        if (file.size > 10 * 1024 * 1024) {
                            showToast(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                            allFilesValid = false;
                            break;
                        }
                    }

                    if (!allFilesValid) {
                        return;
                    }

                    // Show loading message for file processing
                    showToast('Processing files, please wait...', 'info');

                    // Convert files to base64
                    const filePromises = [];
                    for (let i = 0; i < fileInput.files.length; i++) {
                        filePromises.push(fileToBase64(fileInput.files[i]));
                    }

                    // Wait for all files to be converted
                    const base64Files = await Promise.all(filePromises);

                    // Add file information
                    formData.images = base64Files.map((base64, index) => {
                        return {
                            name: fileInput.files[index].name,
                            type: fileInput.files[index].type,
                            size: fileInput.files[index].size,
                            data: base64
                        };
                    });

                    console.log(`Processed ${formData.images.length} files for upload`);
                }

                // Send the data to the server
                const itemId = '<%= item.item_id %>';
                const response = await fetch(`/it-admin/inventory/${itemId}/edit-base64`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showToast('Inventory item updated successfully', 'success');
                    // Redirect after a short delay
                    setTimeout(() => {
                        window.location.href = '/it-admin/inventory';
                    }, 1000);
                } else {
                    showToast(`Error: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('An error occurred while updating the item', 'error');
            }
        });
    });
</script>
