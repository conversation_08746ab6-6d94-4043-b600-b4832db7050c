<!-- Include SheetJS library for Excel export -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

<h1 class="text-2xl font-bold mb-6">Export Inventory Data</h1>

<div class="bg-white rounded-lg shadow p-6">
  <div class="mb-6">
    <h2 class="text-lg font-semibold mb-4">Export Options</h2>
    <% if (!hasItems) { %>
      <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
        <div class="flex items-center">
          <div class="bg-yellow-100 rounded-full p-2 mr-3">
            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-800">No Inventory Items</h3>
            <p class="text-sm text-gray-600">Please add inventory items before exporting data.</p>
          </div>
        </div>
      </div>
    <% } %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-blue-50 p-4 rounded-lg border border-blue-100 hover:shadow-md transition cursor-pointer <%= !hasItems ? 'opacity-50 cursor-not-allowed' : '' %>" id="exportCSV">
        <div class="flex items-center">
          <div class="bg-blue-100 rounded-full p-3 mr-3">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-800">CSV Export</h3>
            <p class="text-sm text-gray-600">Download as comma-separated values</p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 p-4 rounded-lg border border-green-100 hover:shadow-md transition cursor-pointer <%= !hasItems ? 'opacity-50 cursor-not-allowed' : '' %>" id="exportExcel">
        <div class="flex items-center">
          <div class="bg-green-100 rounded-full p-3 mr-3">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-800">Excel Export</h3>
            <p class="text-sm text-gray-600">Download as Excel spreadsheet</p>
          </div>
        </div>
      </div>

      <div class="bg-red-50 p-4 rounded-lg border border-red-100 hover:shadow-md transition cursor-pointer <%= !hasItems ? 'opacity-50 cursor-not-allowed' : '' %>" id="exportPDF">
        <div class="flex items-center">
          <div class="bg-red-100 rounded-full p-3 mr-3">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-800">PDF Export</h3>
            <p class="text-sm text-gray-600">Download as PDF document</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-6">
    <h2 class="text-lg font-semibold mb-4">Filter Options</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
        <select id="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
          <option value="">All Categories</option>
          <option value="laptop">Laptops</option>
          <option value="desktop">Desktops</option>
          <option value="tablet">Tablets</option>
          <option value="printer">Printers</option>
          <option value="network">Network Equipment</option>
          <option value="projector">Projectors</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
          <option value="">All Statuses</option>
          <option value="available">Available</option>
          <option value="assigned">Assigned</option>
          <option value="maintenance">In Repair</option>
          <option value="retired">Retired</option>
        </select>
      </div>

      <div class="flex items-end">
        <button id="applyFilters" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
          Apply Filters
        </button>
      </div>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table id="inventory-table" class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (items && items.length > 0) { %>
          <% items.forEach(item => { %>
            <tr class="inventory-row" data-category="<%= item.category_name?.toLowerCase() %>" data-status="<%= item.status %>">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#<%= item.item_id %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= item.name %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= item.category_name || 'Uncategorized' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= item.serial_number || 'N/A' %></td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                  <% if (item.status === 'available') { %>
                    bg-green-100 text-green-800
                  <% } else if (item.status === 'assigned') { %>
                    bg-blue-100 text-blue-800
                  <% } else if (item.status === 'maintenance') { %>
                    bg-yellow-100 text-yellow-800
                  <% } else if (item.status === 'retired') { %>
                    bg-gray-100 text-gray-800
                  <% } %>">
                  <%= item.status.charAt(0).toUpperCase() + item.status.slice(1) %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= item.assigned_to || 'N/A' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= item.purchase_date ? new Date(item.purchase_date).toLocaleDateString() : 'N/A' %></td>
            </tr>
          <% }); %>
        <% } else { %>
          <tr>
            <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">No inventory items found</td>
          </tr>
        <% } %>
      </tbody>
    </table>
  </div>
</div>

<script>
  // Function to show toast notifications
  function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white transition-opacity duration-500`;

    // Set background color based on type
    if (type === 'success') {
      toast.classList.add('bg-green-600');
    } else if (type === 'error') {
      toast.classList.add('bg-red-600');
    } else if (type === 'warning') {
      toast.classList.add('bg-yellow-600');
    } else {
      toast.classList.add('bg-blue-600');
    }

    // Set message
    toast.textContent = message;

    // Add to document
    document.body.appendChild(toast);

    // Remove after delay
    setTimeout(() => {
      toast.classList.add('opacity-0');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 500);
    }, 3000);
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Check if there are inventory items
    const hasItems = <%= hasItems %>;

    // Export to CSV
    document.getElementById('exportCSV').addEventListener('click', function() {
      if (!hasItems) {
        showToast('No inventory items to export. Please add items first.', 'warning');
        return;
      }
      // Use server-side export
      window.location.href = '/it-admin/inventory/export?format=csv';
    }, { passive: true });

    // Export to Excel
    document.getElementById('exportExcel').addEventListener('click', function() {
      if (!hasItems) {
        showToast('No inventory items to export. Please add items first.', 'warning');
        return;
      }
      // Use server-side export
      window.location.href = '/it-admin/inventory/export?format=xlsx';
    }, { passive: true });

    // Export to PDF
    document.getElementById('exportPDF').addEventListener('click', function() {
      if (!hasItems) {
        showToast('No inventory items to export. Please add items first.', 'warning');
        return;
      }
      // Use server-side export
      window.location.href = '/it-admin/inventory/export?format=pdf';
    }, { passive: true });

    // Apply filters
    document.getElementById('applyFilters').addEventListener('click', function() {
      applyFilters();
    }, { passive: true });

    // Function to export table to CSV
    function exportTableToCSV(tableId, filename) {
      const table = document.getElementById(tableId);
      const rows = table.querySelectorAll('tr');
      let csv = [];

      for (let i = 0; i < rows.length; i++) {
        const row = [], cols = rows[i].querySelectorAll('td, th');

        for (let j = 0; j < cols.length; j++) {
          // Get the text content and clean it
          let data = cols[j].textContent.replace(/(\r\n|\n|\r)/gm, '').trim();
          // Escape double quotes
          data = data.replace(/"/g, '""');
          // Add the data wrapped in quotes
          row.push('"' + data + '"');
        }

        csv.push(row.join(','));
      }

      // Download CSV file
      downloadCSV(csv.join('\n'), filename);
    }

    // Function to download CSV
    function downloadCSV(csv, filename) {
      const csvFile = new Blob([csv], {type: 'text/csv'});
      const downloadLink = document.createElement('a');

      downloadLink.download = filename;
      downloadLink.href = window.URL.createObjectURL(csvFile);
      downloadLink.style.display = 'none';

      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }

    // Function to export table to PDF
    function exportTableToPDF(tableId, filename) {
      // Show loading indicator
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white bg-blue-600 transition-opacity duration-500';
      loadingToast.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Generating PDF...';
      document.body.appendChild(loadingToast);

      // Get the table element
      const table = document.getElementById(tableId);

      // Create a new window for the PDF
      const printWindow = window.open('', '_blank');

      // Create HTML content for the PDF
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Inventory Export</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            h1 { color: #333; }
            .header { display: flex; justify-content: space-between; align-items: center; }
            .date { font-size: 14px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Inventory Export</h1>
            <div class="date">Generated: ${new Date().toLocaleString()}</div>
          </div>
          <table>
            ${table.innerHTML}
          </table>
        </body>
        </html>
      `;

      // Write the HTML to the new window
      printWindow.document.open();
      printWindow.document.write(html);
      printWindow.document.close();

      // Wait for the content to load, then print
      setTimeout(() => {
        printWindow.print();

        // Remove loading indicator
        document.body.removeChild(loadingToast);

        // Show success message
        const successToast = document.createElement('div');
        successToast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white bg-green-600 transition-opacity duration-500';
        successToast.textContent = 'PDF export generated successfully';
        document.body.appendChild(successToast);

        setTimeout(() => {
          successToast.classList.add('opacity-0');
          setTimeout(() => {
            document.body.removeChild(successToast);
          }, 500);
        }, 3000);
      }, 1000);
    }

    // Function to export table to Excel
    function exportTableToExcel(tableId, filename) {
      // Show loading indicator
      const loadingToast = document.createElement('div');
      loadingToast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white bg-blue-600 transition-opacity duration-500';
      loadingToast.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Generating Excel file...';
      document.body.appendChild(loadingToast);

      // Get the table
      const table = document.getElementById(tableId);
      const rows = table.querySelectorAll('tr');

      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.aoa_to_sheet([]);

      // Convert table to worksheet
      const data = [];

      // Get headers
      const headerRow = [];
      const headers = rows[0].querySelectorAll('th');
      headers.forEach(header => {
        headerRow.push(header.textContent.trim());
      });
      data.push(headerRow);

      // Get data rows
      for (let i = 1; i < rows.length; i++) {
        const row = [];
        const cells = rows[i].querySelectorAll('td');

        // Skip hidden rows (filtered out)
        if (rows[i].style.display === 'none') continue;

        cells.forEach(cell => {
          row.push(cell.textContent.trim());
        });

        data.push(row);
      }

      // Create worksheet from data
      const ws = XLSX.utils.aoa_to_sheet(data);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, ws, 'Inventory');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

      // Create Blob and download
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);

      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = filename;
      downloadLink.style.display = 'none';

      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Clean up
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);

      // Remove loading indicator
      document.body.removeChild(loadingToast);

      // Show success message
      const successToast = document.createElement('div');
      successToast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white bg-green-600 transition-opacity duration-500';
      successToast.textContent = 'Excel export generated successfully';
      document.body.appendChild(successToast);

      setTimeout(() => {
        successToast.classList.add('opacity-0');
        setTimeout(() => {
          document.body.removeChild(successToast);
        }, 500);
      }, 3000);
    }

    // Function to apply filters
    function applyFilters() {
      const categoryFilter = document.getElementById('category').value;
      const statusFilter = document.getElementById('status').value;
      const rows = document.querySelectorAll('.inventory-row');

      rows.forEach(row => {
        const rowCategory = row.getAttribute('data-category');
        const rowStatus = row.getAttribute('data-status');
        let showRow = true;

        if (categoryFilter && rowCategory !== categoryFilter) {
          showRow = false;
        }

        if (statusFilter && rowStatus !== statusFilter) {
          showRow = false;
        }

        row.style.display = showRow ? '' : 'none';
      });
    }
  });
</script>
