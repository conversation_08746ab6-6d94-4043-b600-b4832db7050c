<h1 class="text-2xl font-bold mb-6">Import Inventory Items</h1>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Instructions</h2>

        <div class="mb-6">
            <p class="mb-2">Follow these steps to import inventory items:</p>
            <ol class="list-decimal pl-5 space-y-2">
                <li>Download the template file (Excel or CSV)</li>
                <li>Fill in the required information for each item</li>
                <li>Save the file and upload it using the form on the right</li>
                <li>Review any errors or warnings after import</li>
            </ol>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Required Fields</h3>
            <ul class="list-disc pl-5 space-y-1">
                <li><strong>name</strong> - The name of the item</li>
            </ul>
            <p class="mt-2 text-sm text-gray-600">All other fields are optional.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Status Field</h3>
            <p class="mb-2">The status field accepts the following values:</p>
            <ul class="list-disc pl-5 space-y-1">
                <li><strong>available</strong> - Item is available for use</li>
                <li><strong>assigned</strong> - Item is currently assigned to someone</li>
                <li><strong>maintenance</strong> - Item is under maintenance or repair</li>
                <li><strong>retired</strong> - Item is no longer in use</li>
            </ul>
            <p class="mt-2 text-sm text-gray-600">The system will automatically convert similar values to these standard statuses.</p>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-medium mb-2">Download Template</h3>
            <div class="flex flex-wrap gap-4">
                <a href="/it-admin/inventory/import/template" class="bg-it-admin-primary hover:bg-it-admin-secondary text-white py-2 px-4 rounded inline-flex items-center">
                    <i class="fas fa-file-excel mr-2"></i> Download Excel Template
                </a>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Upload File</h2>

        <form action="/it-admin/inventory/import" method="POST" enctype="multipart/form-data" class="space-y-4">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center" id="dropZone">
                <input type="file" name="file" id="fileInput" accept=".csv,.xlsx,.xls" class="hidden" onchange="updateFileName(this)">
                <label for="fileInput" class="cursor-pointer">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                        <p class="text-gray-500 mb-2">Drag and drop your file here or click to browse</p>
                        <p class="text-gray-400 text-sm">Accepted formats: CSV, Excel (.xlsx, .xls)</p>
                        <p class="text-gray-400 text-sm">Maximum file size: 5MB</p>
                    </div>
                </label>
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-file-excel text-green-500 text-2xl mr-3"></i>
                            <div>
                                <p id="fileName" class="font-medium"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                            </div>
                        </div>
                        <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex justify-end">
                <a href="/it-admin/inventory" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                    Cancel
                </a>
                <button type="submit" class="bg-it-admin-primary hover:bg-it-admin-secondary text-white font-bold py-2 px-4 rounded">
                    Import Items
                </button>
            </div>
        </form>
    </div>
</div>

<% if (locals.importErrors && importErrors.length > 0) { %>
<div class="mt-6 bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4 text-red-600">Import Errors</h2>
    <div class="bg-red-50 p-4 rounded-lg">
        <ul class="list-disc pl-5 space-y-1">
            <% importErrors.forEach(error => { %>
                <li class="text-red-700"><%= error %></li>
            <% }); %>
        </ul>
    </div>
</div>
<% } %>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // File upload handling
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const removeFile = document.getElementById('removeFile');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('border-it-admin-primary');
            dropZone.classList.add('bg-it-admin-light');
        }

        function unhighlight() {
            dropZone.classList.remove('border-it-admin-primary');
            dropZone.classList.remove('bg-it-admin-light');
        }

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                updateFileName(fileInput);
            }
        }

        // Update file info when file is selected
        function updateFileName(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Format file size
                let size;
                if (file.size < 1024) {
                    size = file.size + ' bytes';
                } else if (file.size < 1024 * 1024) {
                    size = (file.size / 1024).toFixed(2) + ' KB';
                } else {
                    size = (file.size / (1024 * 1024)).toFixed(2) + ' MB';
                }

                fileName.textContent = file.name;
                fileSize.textContent = size;
                fileInfo.classList.remove('hidden');
            }
        }

        // Handle file removal
        removeFile.addEventListener('click', function() {
            fileInput.value = '';
            fileInfo.classList.add('hidden');
        });

        // Make updateFileName available globally
        window.updateFileName = updateFileName;
    });
</script>
