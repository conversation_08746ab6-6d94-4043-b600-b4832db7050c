<h1 class="text-2xl font-bold mb-6">IT Inventory Management</h1>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Desktops</h3>
                <p class="text-3xl font-bold text-it-admin-primary mt-2"><%= inventoryStats.desktop.count || 0 %></p>
            </div>
            <div class="bg-it-admin-light p-3 rounded-full">
                <i class="fas fa-desktop text-it-admin-primary text-xl"></i>
            </div>
        </div>
        <div class="mt-4 grid grid-cols-2 gap-2 text-sm">
            <div>
                <span class="text-gray-500">Available:</span>
                <span class="font-medium"><%= inventoryStats.desktop.available || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">Assigned:</span>
                <span class="font-medium"><%= inventoryStats.desktop.assigned || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">In Repair:</span>
                <span class="font-medium"><%= inventoryStats.desktop.in_repair || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">Retired:</span>
                <span class="font-medium"><%= inventoryStats.desktop.retired || 0 %></span>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Laptops</h3>
                <p class="text-3xl font-bold text-it-admin-primary mt-2"><%= inventoryStats.laptop.count || 0 %></p>
            </div>
            <div class="bg-it-admin-light p-3 rounded-full">
                <i class="fas fa-laptop text-it-admin-primary text-xl"></i>
            </div>
        </div>
        <div class="mt-4 grid grid-cols-2 gap-2 text-sm">
            <div>
                <span class="text-gray-500">Available:</span>
                <span class="font-medium"><%= inventoryStats.laptop.available || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">Assigned:</span>
                <span class="font-medium"><%= inventoryStats.laptop.assigned || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">In Repair:</span>
                <span class="font-medium"><%= inventoryStats.laptop.in_repair || 0 %></span>
            </div>
            <div>
                <span class="text-gray-500">Retired:</span>
                <span class="font-medium"><%= inventoryStats.laptop.retired || 0 %></span>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Other Devices</h3>
                <p class="text-3xl font-bold text-it-admin-primary mt-2"><%= (inventoryStats.tablet?.count || 0) + (inventoryStats.projector?.count || 0) + (inventoryStats.printer?.count || 0) + (inventoryStats.network?.count || 0) + (inventoryStats.other?.count || 0) %></p>
            </div>
            <div class="bg-it-admin-light p-3 rounded-full">
                <i class="fas fa-hdd text-it-admin-primary text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="grid grid-cols-2 gap-2 text-sm mb-3">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span class="text-gray-500">Tablets:</span>
                    <span class="font-medium ml-1"><%= inventoryStats.tablet?.count || 0 %></span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-gray-500">Projectors:</span>
                    <span class="font-medium ml-1"><%= inventoryStats.projector?.count || 0 %></span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span class="text-gray-500">Printers:</span>
                    <span class="font-medium ml-1"><%= inventoryStats.printer?.count || 0 %></span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span class="text-gray-500">Network:</span>
                    <span class="font-medium ml-1"><%= inventoryStats.network?.count || 0 %></span>
                </div>
            </div>
            <% if (inventoryStats.other?.count > 0) { %>
            <div class="flex items-center mt-2">
                <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                <span class="text-gray-500">Other:</span>
                <span class="font-medium ml-1"><%= inventoryStats.other?.count || 0 %></span>
            </div>
            <% } %>
        </div>
    </div>
</div>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Inventory Items</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/inventory/add" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-plus mr-2"></i> Add Item
            </a>
            <button id="importBtn" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-file-import mr-2"></i> Import
            </button>
            <button id="filterBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-filter mr-2"></i> Filter
            </button>
            <button id="exportBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-download mr-2"></i> Export
            </button>

            <!-- Export Modal -->
            <div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
              <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
                <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                  <h3 class="text-lg font-semibold text-gray-800">Export Inventory Data</h3>
                  <button id="closeExportModal" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <div class="p-6">
                  <div id="exportSelectionInfo" class="mb-4 bg-blue-50 p-3 rounded-lg border border-blue-100">
                    <div class="flex items-center">
                      <div class="bg-blue-100 p-2 rounded-full mr-3">
                        <i class="fas fa-info-circle text-blue-500"></i>
                      </div>
                      <p class="text-sm text-blue-700">
                        <span id="selectedItemsCount">0</span> items selected for export.
                        <span id="exportAllMessage" class="font-medium">All items will be exported if none are selected.</span>
                      </p>
                    </div>
                  </div>

                  <p class="mb-4 text-gray-600">Choose a format to export your inventory data:</p>

                  <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-it-admin-primary transition-colors cursor-pointer">
                      <div class="flex items-center">
                        <div class="bg-red-100 p-3 rounded-full mr-4">
                          <i class="far fa-file-pdf text-red-500 text-xl"></i>
                        </div>
                        <div class="flex-grow">
                          <h4 class="font-medium text-gray-800">PDF Document</h4>
                          <p class="text-sm text-gray-600">Export as a printable PDF document</p>

                          <div class="mt-3 flex items-center space-x-4">
                            <button class="export-option bg-white px-3 py-1 rounded border border-gray-300 hover:bg-gray-100 text-sm flex items-center" data-format="pdf" data-orientation="portrait">
                              <i class="fas fa-file-pdf mr-1 text-red-500"></i> Portrait
                            </button>
                            <button class="export-option bg-white px-3 py-1 rounded border border-gray-300 hover:bg-gray-100 text-sm flex items-center" data-format="pdf" data-orientation="landscape">
                              <i class="fas fa-file-pdf mr-1 text-red-500"></i> Landscape
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-it-admin-primary transition-colors cursor-pointer export-option" data-format="xlsx">
                      <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-full mr-4">
                          <i class="far fa-file-excel text-green-500 text-xl"></i>
                        </div>
                        <div>
                          <h4 class="font-medium text-gray-800">Excel Spreadsheet</h4>
                          <p class="text-sm text-gray-600">Export as an editable Excel spreadsheet</p>
                        </div>
                      </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-it-admin-primary transition-colors cursor-pointer export-option" data-format="csv">
                      <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-full mr-4">
                          <i class="far fa-file-csv text-blue-500 text-xl"></i>
                        </div>
                        <div>
                          <h4 class="font-medium text-gray-800">CSV File</h4>
                          <p class="text-sm text-gray-600">Export as a comma-separated values file</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button id="deleteSelectedBtn" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition hidden">
                <i class="fas fa-trash-alt mr-2"></i> Delete Selected
            </button>
        </div>
    </div>

    <!-- Quick Filters -->
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex flex-wrap gap-3 items-center">
            <!-- Category Quick Filters -->
            <div class="flex flex-wrap gap-2">
                <a href="/it-admin/inventory" class="<%= !filters || !filters.category ? 'bg-it-admin-primary text-white' : 'bg-gray-200 text-gray-700' %> px-3 py-1 rounded-full text-sm hover:bg-it-admin-secondary hover:text-white transition">
                    All
                </a>
                <a href="/it-admin/inventory?category=7" class="<%= filters && filters.category == 7 ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-700' %> px-3 py-1 rounded-full text-sm hover:bg-blue-600 hover:text-white transition">
                    <i class="fas fa-laptop mr-1"></i> Laptops
                </a>
                <a href="/it-admin/inventory?category=8" class="<%= filters && filters.category == 8 ? 'bg-indigo-600 text-white' : 'bg-indigo-50 text-indigo-700' %> px-3 py-1 rounded-full text-sm hover:bg-indigo-600 hover:text-white transition">
                    <i class="fas fa-desktop mr-1"></i> Desktops
                </a>
                <a href="/it-admin/inventory?category=13" class="<%= filters && filters.category == 13 ? 'bg-green-600 text-white' : 'bg-green-50 text-green-700' %> px-3 py-1 rounded-full text-sm hover:bg-green-600 hover:text-white transition">
                    <i class="fas fa-video mr-1"></i> Projectors
                </a>
                <a href="/it-admin/inventory?category=15" class="<%= filters && filters.category == 15 ? 'bg-red-600 text-white' : 'bg-red-50 text-red-700' %> px-3 py-1 rounded-full text-sm hover:bg-red-600 hover:text-white transition">
                    <i class="fas fa-print mr-1"></i> Printers
                </a>
                <a href="/it-admin/inventory?category=3" class="<%= filters && filters.category == 3 ? 'bg-purple-600 text-white' : 'bg-purple-50 text-purple-700' %> px-3 py-1 rounded-full text-sm hover:bg-purple-600 hover:text-white transition">
                    <i class="fas fa-network-wired mr-1"></i> Network
                </a>
            </div>

            <!-- Status Quick Filters -->
            <div class="flex flex-wrap gap-2 ml-auto">
                <a href="/it-admin/inventory?status=available" class="<%= filters && filters.status === 'available' ? 'bg-green-600 text-white' : 'bg-green-50 text-green-700' %> px-3 py-1 rounded-full text-sm hover:bg-green-600 hover:text-white transition">
                    <i class="fas fa-check-circle mr-1"></i> Available
                </a>
                <a href="/it-admin/inventory?status=assigned" class="<%= filters && filters.status === 'assigned' ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-700' %> px-3 py-1 rounded-full text-sm hover:bg-blue-600 hover:text-white transition">
                    <i class="fas fa-user-check mr-1"></i> Assigned
                </a>
                <a href="/it-admin/inventory?status=maintenance" class="<%= filters && filters.status === 'maintenance' ? 'bg-yellow-600 text-white' : 'bg-yellow-50 text-yellow-700' %> px-3 py-1 rounded-full text-sm hover:bg-yellow-600 hover:text-white transition">
                    <i class="fas fa-tools mr-1"></i> In Repair
                </a>
                <a href="/it-admin/inventory?status=retired" class="<%= filters && filters.status === 'retired' ? 'bg-gray-600 text-white' : 'bg-gray-200 text-gray-700' %> px-3 py-1 rounded-full text-sm hover:bg-gray-600 hover:text-white transition">
                    <i class="fas fa-archive mr-1"></i> Retired
                </a>
            </div>
        </div>

        <!-- Search Box -->
        <div class="mt-3">
            <form action="/it-admin/inventory" method="GET" class="flex">
                <input type="text" name="search" placeholder="Search inventory items..." value="<%= filters && filters.search || '' %>" class="flex-grow px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:border-it-admin-primary">
                <button type="submit" class="bg-it-admin-primary text-white px-4 py-2 rounded-r-md hover:bg-it-admin-secondary transition">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-it-admin-primary">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center">
                            <input type="checkbox" id="selectAll" class="form-checkbox h-4 w-4 text-it-admin-secondary rounded border-gray-300 focus:ring-it-admin-primary">
                            <span class="ml-2">Select</span>
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Serial Number</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Assigned To</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Last Updated</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% if (inventory && inventory.length > 0) { %>
                    <% inventory.forEach(item => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="item-checkbox form-checkbox h-4 w-4 text-it-admin-secondary rounded border-gray-300 focus:ring-it-admin-primary" data-id="<%= item.item_id %>" <%= item.status === 'assigned' ? 'disabled title="Cannot delete assigned items"' : '' %>>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <% if (item.image) { %>
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img src="<%= item.image %>" alt="<%= item.name %>" class="h-10 w-10 rounded-full object-cover">
                                        </div>
                                    <% } else { %>
                                        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-gray-100 rounded-full">
                                            <% if (item.category_name && item.category_name.toLowerCase().includes('desktop')) { %>
                                                <i class="fas fa-desktop text-gray-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('laptop')) { %>
                                                <i class="fas fa-laptop text-blue-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('tablet')) { %>
                                                <i class="fas fa-tablet-alt text-indigo-600"></i>
                                            <% } else if (item.category_name && (item.category_name.toLowerCase().includes('projector') || item.category_name.toLowerCase().includes('audio/video') || item.category_name.toLowerCase().includes('interactive'))) { %>
                                                <i class="fas fa-video text-green-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('printer')) { %>
                                                <i class="fas fa-print text-red-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('network')) { %>
                                                <i class="fas fa-network-wired text-purple-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('ups')) { %>
                                                <i class="fas fa-battery-full text-yellow-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('tv')) { %>
                                                <i class="fas fa-tv text-teal-600"></i>
                                            <% } else if (item.category_name && item.category_name.toLowerCase().includes('power')) { %>
                                                <i class="fas fa-plug text-amber-600"></i>
                                            <% } else { %>
                                                <i class="fas fa-hdd text-gray-600"></i>
                                            <% } %>
                                        </div>
                                    <% } %>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900"><%= item.name %></div>
                                        <div class="text-sm text-gray-500"><%= item.model || 'N/A' %></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><%= item.category_name || 'Uncategorized' %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><%= item.serial_number || 'N/A' %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (item.status === 'available') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                                <% } else if (item.status === 'assigned') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
                                <% } else if (item.status === 'maintenance') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Repair</span>
                                <% } else if (item.status === 'retired') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><%= item.status %></span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><%= item.assigned_username || 'N/A' %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= item.updated_at ? new Date(item.updated_at).toLocaleDateString() : 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="/it-admin/inventory/<%= item.item_id %>" class="text-it-admin-primary hover:text-it-admin-secondary" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/it-admin/inventory/<%= item.item_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <% if (item.status === 'available') { %>
                                        <a href="/it-admin/inventory/<%= item.item_id %>/issue" class="text-blue-600 hover:text-blue-900" title="Issue Item">
                                            <i class="fas fa-share"></i>
                                        </a>
                                    <% } else if (item.status === 'assigned') { %>
                                        <a href="/it-admin/inventory/<%= item.item_id %>/receive" class="text-green-600 hover:text-green-900" title="Receive Item">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                    <% } %>
                                    <a href="#" class="text-red-600 hover:text-red-900 delete-item" data-id="<%= item.item_id %>" title="Delete">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No inventory items found. Click "Add Item" to add your first inventory item.
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500">
                Showing <span class="font-medium"><%= inventory ? inventory.length : 0 %></span> items
            </div>
            <div class="flex space-x-2">
                <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition disabled:opacity-50" disabled>
                    Previous
                </button>
                <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition disabled:opacity-50" disabled>
                    Next
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div id="confirmation-dialog-header" class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 id="confirmation-dialog-title" class="text-xl font-semibold">Delete Item</h3>
            <button id="confirmation-dialog-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p id="confirmation-dialog-message" class="mb-6">Are you sure you want to delete this item? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="confirmation-dialog-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                Cancel
            </button>
            <button id="confirmation-dialog-confirm" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="import-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
        <div class="bg-it-admin-primary text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 class="text-xl font-semibold">Import Inventory Items</h3>
            <button id="close-import-modal" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Import Data Container -->
        <div id="import-data-container">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h2 class="text-lg font-semibold mb-4">Instructions</h2>

                    <div class="mb-6">
                        <p class="mb-2">Follow these steps to import inventory items:</p>
                        <ol class="list-decimal pl-5 space-y-2">
                            <li>Download the template file</li>
                            <li>Fill in the required information for each item</li>
                            <li>Save the file and upload it using the form</li>
                            <li>Preview the data before final import</li>
                        </ol>
                    </div>

                    <div class="mb-6">
                        <h3 class="text-md font-medium mb-2">Required Fields</h3>
                        <ul class="list-disc pl-5 space-y-1">
                            <li><strong>name</strong> - The name of the item</li>
                            <li><strong>category</strong> - The category of the item</li>
                            <li><strong>serial_number</strong> - The serial number of the item</li>
                            <li><strong>model</strong> - The model of the item</li>
                            <li><strong>manufacturer</strong> - The manufacturer of the item</li>
                        </ul>
                        <p class="mt-2 text-sm text-gray-600">Other fields are optional, including photo.</p>
                    </div>

                    <div class="mb-6">
                        <h3 class="text-md font-medium mb-2">Download Template</h3>
                        <div class="flex flex-wrap gap-4">
                            <a href="/it-admin/inventory/import/template" class="bg-it-admin-primary hover:bg-it-admin-secondary text-white py-2 px-4 rounded inline-flex items-center">
                                <i class="fas fa-file-excel mr-2"></i> Download Excel Template
                            </a>
                        </div>
                    </div>
                </div>

                <div>
                    <h2 class="text-lg font-semibold mb-4">Upload File</h2>

                    <form id="import-form" enctype="multipart/form-data" class="space-y-4">
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center" id="dropZone">
                            <input type="file" name="file" id="fileInput" accept=".csv,.xlsx,.xls" class="hidden" onchange="updateFileName(this)">
                            <label for="fileInput" class="cursor-pointer">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 mb-2">Drag and drop your file here or click to browse</p>
                                    <p class="text-gray-400 text-sm">Accepted formats: CSV, Excel (.xlsx, .xls)</p>
                                    <p class="text-gray-400 text-sm">Maximum file size: 5MB</p>
                                </div>
                            </label>
                            <div id="fileInfo" class="mt-4 hidden">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-excel text-green-500 text-2xl mr-3"></i>
                                        <div>
                                            <p id="fileName" class="font-medium"></p>
                                            <p id="fileSize" class="text-sm text-gray-500"></p>
                                        </div>
                                    </div>
                                    <button type="button" id="removeFile" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-it-admin-primary hover:bg-it-admin-secondary text-white font-bold py-2 px-4 rounded">
                                Preview Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Container -->
        <div id="preview-container" class="hidden">
            <h2 class="text-lg font-semibold mb-4">Preview Import Data</h2>

            <div class="mb-4 bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    <p class="text-blue-700">
                        Review the data below before importing. A total of <span id="total-items" class="font-bold">0</span> items will be imported.
                    </p>
                </div>
            </div>

            <div class="mb-4">
                <h3 class="text-md font-medium mb-2">Quick Filters</h3>
                <div class="flex flex-wrap gap-2">
                    <div class="relative">
                        <select id="category-filter" class="bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-it-admin-primary focus:border-it-admin-primary">
                            <option value="">All Categories</option>
                            <!-- Categories will be populated dynamically -->
                        </select>
                    </div>
                    <div class="relative">
                        <select id="status-filter" class="bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-it-admin-primary focus:border-it-admin-primary">
                            <option value="">All Statuses</option>
                            <option value="available">Available</option>
                            <option value="assigned">Assigned</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="retired">Retired</option>
                        </select>
                    </div>
                    <div class="relative">
                        <select id="manufacturer-filter" class="bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-it-admin-primary focus:border-it-admin-primary">
                            <option value="">All Manufacturers</option>
                            <!-- Manufacturers will be populated dynamically -->
                        </select>
                    </div>
                    <div class="relative flex-grow">
                        <input type="text" id="search-filter" placeholder="Search items..." class="w-full bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-it-admin-primary focus:border-it-admin-primary">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto mb-4">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Cost</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty Expiry</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        </tr>
                    </thead>
                    <tbody id="preview-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Preview data will be inserted here -->
                    </tbody>
                </table>
            </div>

            <form id="confirm-import-form">
                <input type="hidden" id="preview-data" name="preview_data">
                <div class="flex justify-end space-x-3">
                    <button type="button" id="back-to-upload-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Back
                    </button>
                    <button type="button" id="download-template-btn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-download mr-2"></i> Download Template
                    </button>
                    <button type="submit" id="confirm-import-btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                        <i class="fas fa-file-import mr-2"></i> Import Items
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div id="filter-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="bg-it-admin-primary text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 class="text-xl font-semibold">Filter Inventory</h3>
            <button id="filter-modal-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="filter-form" class="space-y-4">
            <div>
                <label for="filter-category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="filter-category" name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                    <option value="">All Categories</option>
                    <% if (categories && categories.length > 0) { %>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.category_id %>" <%= filters && filters.category == category.category_id ? 'selected' : '' %>><%= category.name %></option>
                        <% }); %>
                    <% } else { %>
                        <option value="desktop" <%= filters && filters.category === 'desktop' ? 'selected' : '' %>>Desktop</option>
                        <option value="laptop" <%= filters && filters.category === 'laptop' ? 'selected' : '' %>>Laptop</option>
                        <option value="tablet" <%= filters && filters.category === 'tablet' ? 'selected' : '' %>>Tablet</option>
                        <option value="printer" <%= filters && filters.category === 'printer' ? 'selected' : '' %>>Printer</option>
                        <option value="network" <%= filters && filters.category === 'network' ? 'selected' : '' %>>Network Equipment</option>
                        <option value="projector" <%= filters && filters.category === 'projector' ? 'selected' : '' %>>Projector</option>
                        <option value="other" <%= filters && filters.category === 'other' ? 'selected' : '' %>>Other</option>
                    <% } %>
                </select>
            </div>
            <div>
                <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="filter-status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                    <option value="">All Statuses</option>
                    <option value="available" <%= filters && filters.status === 'available' ? 'selected' : '' %>>Available</option>
                    <option value="assigned" <%= filters && filters.status === 'assigned' ? 'selected' : '' %>>Assigned</option>
                    <option value="maintenance" <%= filters && filters.status === 'maintenance' ? 'selected' : '' %>>In Repair</option>
                    <option value="retired" <%= filters && filters.status === 'retired' ? 'selected' : '' %>>Retired</option>
                </select>
            </div>
            <div>
                <label for="filter-search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="filter-search" name="search" value="<%= filters && filters.search %>" placeholder="Search by name, serial number, etc." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" id="filter-reset" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Reset
                </button>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Confirmation dialog functionality
        const confirmationDialog = document.getElementById('confirmation-dialog');
        const confirmationDialogClose = document.getElementById('confirmation-dialog-close');
        const confirmationDialogCancel = document.getElementById('confirmation-dialog-cancel');
        const confirmationDialogConfirm = document.getElementById('confirmation-dialog-confirm');
        let itemIdToDelete = null;

        // Delete item confirmation
        const deleteButtons = document.querySelectorAll('.delete-item');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                itemIdToDelete = this.getAttribute('data-id');
                confirmationDialog.classList.remove('hidden');
            });
        });

        function closeConfirmationDialog() {
            confirmationDialog.classList.add('hidden');
            itemIdToDelete = null;
        }

        confirmationDialogClose.addEventListener('click', closeConfirmationDialog);
        confirmationDialogCancel.addEventListener('click', closeConfirmationDialog);
        confirmationDialog.addEventListener('click', function(e) {
            if (e.target === confirmationDialog) {
                closeConfirmationDialog();
            }
        });

        confirmationDialogConfirm.addEventListener('click', function() {
            if (itemIdToDelete) {
                // Send delete request
                fetch(`/it-admin/inventory/${itemIdToDelete}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload page or remove row
                        window.location.reload();
                    } else {
                        // Show error toast
                        showToast('Error deleting item: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred while deleting the item.', 'error');
                });
            }
            closeConfirmationDialog();
        });

        // Export button functionality
        const exportBtn = document.getElementById('exportBtn');
        const exportModal = document.getElementById('exportModal');

        // Open export modal directly when clicking export button
        exportBtn.addEventListener('click', function(e) {
            e.stopPropagation();

            // Update selected items count
            const selectedIds = getSelectedItemIds();
            document.getElementById('selectedItemsCount').textContent = selectedIds.length;

            // Show the modal
            exportModal.classList.remove('hidden');
        });

        // Function to get selected item IDs
        function getSelectedItemIds() {
            const checkboxes = document.querySelectorAll('.item-checkbox:checked');
            return Array.from(checkboxes).map(checkbox => checkbox.getAttribute('data-id'));
        }

        // Function to handle export
        function handleExport(format, orientation) {
            const selectedIds = getSelectedItemIds();
            const formatName = format === 'pdf' ? 'PDF' : (format === 'xlsx' ? 'Excel' : 'CSV');

            if (selectedIds.length > 0) {
                showToast(`Generating ${formatName} export for ${selectedIds.length} selected items...`, 'success');
            } else {
                showToast(`Generating ${formatName} export for all items...`, 'success');
            }

            try {
                console.log(`Initiating ${format} export${orientation ? ' in ' + orientation + ' orientation' : ''}`);

                // Create a form to submit the export request
                const form = document.createElement('form');
                form.method = selectedIds.length > 0 ? 'POST' : 'GET';
                form.action = '/it-admin/inventory/export';
                form.target = '_blank'; // Open in new tab/window

                // Add format parameter
                const formatInput = document.createElement('input');
                formatInput.type = 'hidden';
                formatInput.name = 'format';
                formatInput.value = format;
                form.appendChild(formatInput);

                // Add orientation parameter if provided
                if (orientation) {
                    const orientationInput = document.createElement('input');
                    orientationInput.type = 'hidden';
                    orientationInput.name = 'orientation';
                    orientationInput.value = orientation;
                    form.appendChild(orientationInput);
                }

                // Add selected IDs if any
                if (selectedIds.length > 0) {
                    selectedIds.forEach(id => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'itemIds[]';
                        input.value = id;
                        form.appendChild(input);
                    });
                } else {
                    // Add current filters if any (only for GET requests)
                    const urlParams = new URLSearchParams(window.location.search);
                    for (const [key, value] of urlParams.entries()) {
                        if (key !== 'format') {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = key;
                            input.value = value;
                            form.appendChild(input);
                        }
                    }
                }

                // Submit the form
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);

                // Close the export modal if it's open
                if (exportModal) {
                    exportModal.classList.add('hidden');
                }
            } catch (error) {
                console.error(`Error exporting to ${format}:`, error);
                showToast(`Error generating ${formatName} export. Please try again.`, 'error');
            }
        }

        // We've removed the exportPDF and exportXLSX elements
        // Now we only use the export options in the modal

        // Export Modal
        const closeExportModal = document.getElementById('closeExportModal');

        // Close export modal
        if (closeExportModal) {
            closeExportModal.addEventListener('click', function() {
                exportModal.classList.add('hidden');
            });
        }

        // Close modal when clicking outside
        if (exportModal) {
            exportModal.addEventListener('click', function(e) {
                if (e.target === exportModal) {
                    exportModal.classList.add('hidden');
                }
            });
        }

        // Handle export option clicks in modal
        const exportOptions = document.querySelectorAll('.export-option');
        exportOptions.forEach(option => {
            option.addEventListener('click', function() {
                const format = this.getAttribute('data-format');
                const orientation = this.getAttribute('data-orientation');
                handleExport(format, orientation);
            });
        });

        // Filter modal functionality
        const filterBtn = document.getElementById('filterBtn');
        const filterModal = document.getElementById('filter-modal');
        const filterModalClose = document.getElementById('filter-modal-close');
        const filterForm = document.getElementById('filter-form');
        const filterReset = document.getElementById('filter-reset');

        // Open filter modal
        filterBtn.addEventListener('click', function() {
            filterModal.classList.remove('hidden');
        });

        // Close filter modal
        function closeFilterModal() {
            filterModal.classList.add('hidden');
        }

        filterModalClose.addEventListener('click', closeFilterModal);
        filterModal.addEventListener('click', function(e) {
            if (e.target === filterModal) {
                closeFilterModal();
            }
        });

        // Handle filter form submission
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(filterForm);
            const params = new URLSearchParams();

            // Add form values to URL parameters
            for (const [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            // Redirect with filter parameters
            window.location.href = `/it-admin/inventory?${params.toString()}`;
        });

        // Reset filters
        filterReset.addEventListener('click', function() {
            filterForm.reset();
        });

        // Multiple delete functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox:not([disabled])');
        const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');

        // Select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateDeleteButtonVisibility();
        });

        // Individual checkboxes
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateDeleteButtonVisibility();

                // Update select all checkbox
                const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
                const someChecked = Array.from(itemCheckboxes).some(cb => cb.checked);

                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            });
        });

        // Update delete button visibility
        function updateDeleteButtonVisibility() {
            const anyChecked = Array.from(itemCheckboxes).some(checkbox => checkbox.checked);
            if (anyChecked) {
                deleteSelectedBtn.classList.remove('hidden');
            } else {
                deleteSelectedBtn.classList.add('hidden');
            }
        }

        // Delete selected items
        deleteSelectedBtn.addEventListener('click', function() {
            const selectedItemIds = Array.from(itemCheckboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => checkbox.getAttribute('data-id'));

            if (selectedItemIds.length === 0) {
                showToast('No items selected for deletion', 'error');
                return;
            }

            // Show confirmation dialog
            document.getElementById('confirmation-dialog-title').textContent = 'Delete Selected Items';
            document.getElementById('confirmation-dialog-message').textContent =
                `Are you sure you want to delete ${selectedItemIds.length} selected item(s)? This action cannot be undone.`;

            confirmationDialog.classList.remove('hidden');

            // Override the confirm button click handler
            const originalConfirmHandler = confirmationDialogConfirm.onclick;
            confirmationDialogConfirm.onclick = function() {
                // Create a form to submit the selected IDs
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/it-admin/inventory/delete-multiple';
                form.style.display = 'none';

                // Add the selected IDs as hidden inputs
                selectedItemIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'itemIds[]';
                    input.value = id;
                    form.appendChild(input);
                });

                // Add any additional form fields if needed in the future

                // Submit the form
                document.body.appendChild(form);
                form.submit();

                closeConfirmationDialog();
            };

            // Restore original handler when dialog is closed
            const originalCloseHandler = closeConfirmationDialog;
            closeConfirmationDialog = function() {
                originalCloseHandler();
                confirmationDialogConfirm.onclick = originalConfirmHandler;
                closeConfirmationDialog = originalCloseHandler;
            };
        });

        // Toast notification function
        function showToast(message, type = 'success', duration = 3000) {
            const toast = document.createElement('div');

            // Determine background color based on type
            let bgColor;
            let icon = '';

            if (type === 'success') {
                bgColor = 'bg-green-600';
                icon = '<i class="fas fa-check-circle mr-2"></i>';
            } else if (type === 'error') {
                bgColor = 'bg-red-600';
                icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-600';
                icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
            } else {
                bgColor = 'bg-blue-600';
                icon = '<i class="fas fa-info-circle mr-2"></i>';
            }

            // Check if message contains HTML
            const isHTML = /<[a-z][\s\S]*>/i.test(message);

            // Set toast class based on content type
            toast.className = `fixed bottom-4 right-4 p-4 rounded-lg shadow-lg text-white ${bgColor} transition-opacity duration-500 z-50 ${isHTML ? 'max-w-lg' : ''}`;

            // Set content based on whether it's HTML or plain text
            if (isHTML) {
                toast.innerHTML = message;
            } else {
                toast.innerHTML = `${icon}${message}`;
            }

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.className = 'ml-2 text-white hover:text-gray-200 focus:outline-none absolute top-2 right-2';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 500);
            });

            toast.appendChild(closeBtn);
            document.body.appendChild(toast);

            // Auto-remove after specified duration
            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 500);
            }, duration);
        }
    });

    // Import Modal Functionality
    const importBtn = document.getElementById('importBtn');
    const importModal = document.getElementById('import-modal');
    const closeImportModal = document.getElementById('close-import-modal');
    const importForm = document.getElementById('import-form');
    const previewContainer = document.getElementById('preview-container');
    const importDataContainer = document.getElementById('import-data-container');
    const confirmImportBtn = document.getElementById('confirm-import-btn');
    const backToUploadBtn = document.getElementById('back-to-upload-btn');

    // Filter elements
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const manufacturerFilter = document.getElementById('manufacturer-filter');
    const searchFilter = document.getElementById('search-filter');

    // Store original preview data
    let originalPreviewData = [];

    // Open import modal
    importBtn.addEventListener('click', function() {
        importModal.classList.remove('hidden');
        previewContainer.classList.add('hidden');
        importDataContainer.classList.remove('hidden');
    });

    // Close import modal
    function closeImportModalFunc() {
        importModal.classList.add('hidden');
        // Reset form
        importForm.reset();
        document.getElementById('fileInfo').classList.add('hidden');
        previewContainer.classList.add('hidden');
        importDataContainer.classList.remove('hidden');
    }

    closeImportModal.addEventListener('click', closeImportModalFunc);

    // Close modal when clicking outside
    importModal.addEventListener('click', function(e) {
        if (e.target === importModal) {
            closeImportModalFunc();
        }
    });

    // Drag and drop functionality
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        dropZone.classList.add('border-it-admin-primary');
        dropZone.classList.add('bg-it-admin-light');
    }

    function unhighlight() {
        dropZone.classList.remove('border-it-admin-primary');
        dropZone.classList.remove('bg-it-admin-light');
    }

    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            updateFileName(fileInput);
        }
    }

    // Handle file preview
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(importForm);
        const fileInput = document.getElementById('fileInput');

        if (!fileInput.files || fileInput.files.length === 0) {
            showToast('Please select a file to import', 'error');
            return;
        }

        // Show loading state
        const submitBtn = importForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
        submitBtn.disabled = true;

        // Create a new XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/it-admin/inventory/preview-import', true);

        // Setup progress event
        xhr.upload.onprogress = function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                console.log('Upload progress: ' + percentComplete + '%');
            }
        };

        // Setup error handling
        xhr.onerror = function() {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            showToast('An error occurred during file upload. Please try again.', 'error');
        };

        // Setup timeout handling
        xhr.ontimeout = function() {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            showToast('The upload request timed out. Please try with a smaller file.', 'error');
        };

        // Setup completion
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const data = JSON.parse(xhr.responseText);

                    // Reset button
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    if (data.error) {
                        showToast(data.error, 'error');
                        return;
                    }

                    // Show preview
                    importDataContainer.classList.add('hidden');
                    previewContainer.classList.remove('hidden');

                    // Populate preview table
                    const previewTable = document.getElementById('preview-table-body');
                    previewTable.innerHTML = '';

                    data.items.forEach((item, index) => {
                        if (index < 100) { // Limit to 100 rows for performance
                            const row = document.createElement('tr');
                            row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                            // Format dates if they exist
                            const purchaseDate = item.purchase_date ? new Date(item.purchase_date).toLocaleDateString() : 'N/A';
                            const warrantyExpiry = item.warranty_expiry ? new Date(item.warranty_expiry).toLocaleDateString() : 'N/A';

                            row.innerHTML = `
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${item.name || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.category || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.serial_number || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.model || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.manufacturer || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${purchaseDate}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.purchase_cost || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${warrantyExpiry}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.status || 'available'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.location || 'N/A'}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.notes || 'N/A'}</td>
                            `;

                            previewTable.appendChild(row);
                        }
                    });

                    // Update summary
                    document.getElementById('total-items').textContent = data.items.length;

                    // Store the preview data in a hidden input for the final import
                    document.getElementById('preview-data').value = JSON.stringify(data);

                    // Populate filter dropdowns
                    populateFilterDropdowns(data.items);
                } catch (error) {
                    console.error('Error parsing response:', error);
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                    showToast('Error processing the file. Please try again.', 'error');
                }
            } else {
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
                showToast('Error: ' + xhr.status + ' ' + xhr.statusText, 'error');
            }
        };

        // Send the request
        xhr.send(formData);
    });

    // Handle back button in preview
    backToUploadBtn.addEventListener('click', function() {
        previewContainer.classList.add('hidden');
        importDataContainer.classList.remove('hidden');
    });

    // Handle download template button
    document.getElementById('download-template-btn').addEventListener('click', function() {
        window.location.href = '/it-admin/inventory/import/template';
    });

    // Handle confirm import
    document.getElementById('confirm-import-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const confirmBtn = confirmImportBtn;
        const originalBtnText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Importing...';
        confirmBtn.disabled = true;

        // Create a new XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/it-admin/inventory/import', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        // Setup error handling
        xhr.onerror = function() {
            confirmBtn.innerHTML = originalBtnText;
            confirmBtn.disabled = false;
            showToast('An error occurred during import. Please try again.', 'error');
        };

        // Setup timeout handling
        xhr.ontimeout = function() {
            confirmBtn.innerHTML = originalBtnText;
            confirmBtn.disabled = false;
            showToast('The import request timed out. Please try again.', 'error');
        };

        // Setup completion
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const data = JSON.parse(xhr.responseText);

                    confirmBtn.innerHTML = originalBtnText;
                    confirmBtn.disabled = false;

                    if (data.error) {
                        showToast(data.error, 'error');
                        return;
                    }

                    // Create a detailed import summary
                    let summaryMessage = `<div class="text-left">
                        <p class="font-bold mb-2">Import Summary:</p>
                        <ul class="list-disc pl-5 mb-3">
                            <li class="text-green-600">${data.imported} items successfully imported</li>`;

                    if (data.duplicates > 0) {
                        summaryMessage += `<li class="text-yellow-600">${data.duplicates} duplicate items skipped</li>`;
                    }

                    if (data.skipped > 0) {
                        summaryMessage += `<li class="text-red-600">${data.skipped} items skipped due to errors</li>`;
                    }

                    summaryMessage += `</ul>`;

                    // Add details about duplicate items if any
                    if (data.duplicates > 0 && data.duplicateItems && data.duplicateItems.length > 0) {
                        summaryMessage += `<p class="font-bold mt-2 mb-1">Duplicate Items:</p>
                        <div class="max-h-40 overflow-y-auto">
                            <ul class="list-disc pl-5">`;

                        data.duplicateItems.forEach(item => {
                            summaryMessage += `<li class="text-sm">${item.name} (SN: ${item.serial_number}) - Already exists as "${item.existing_item.name}"</li>`;
                        });

                        summaryMessage += `</ul>
                        </div>`;
                    }

                    // Add details about errors if any
                    if (data.errors && data.errors.length > 0) {
                        summaryMessage += `<p class="font-bold mt-2 mb-1">Errors:</p>
                        <div class="max-h-40 overflow-y-auto">
                            <ul class="list-disc pl-5">`;

                        data.errors.forEach(error => {
                            summaryMessage += `<li class="text-sm text-red-600">${error}</li>`;
                        });

                        summaryMessage += `</ul>
                        </div>`;
                    }

                    summaryMessage += `</div>`;

                    // Show detailed summary message
                    showToast(summaryMessage, data.imported > 0 ? 'success' : 'warning', 10000);

                    // Close modal and reload page
                    closeImportModalFunc();
                    setTimeout(() => {
                        window.location.reload();
                    }, 10000);
                } catch (error) {
                    console.error('Error parsing response:', error);
                    confirmBtn.innerHTML = originalBtnText;
                    confirmBtn.disabled = false;
                    showToast('Error processing the import. Please try again.', 'error');
                }
            } else {
                confirmBtn.innerHTML = originalBtnText;
                confirmBtn.disabled = false;
                showToast('Error: ' + xhr.status + ' ' + xhr.statusText, 'error');
            }
        };

        // Send the request
        xhr.send(document.getElementById('preview-data').value);
    });

    // Function to populate filter dropdowns
    function populateFilterDropdowns(items) {
        // Store original data for filtering
        originalPreviewData = items;

        // Get unique categories
        const categories = [...new Set(items.map(item => item.category).filter(Boolean))].sort();
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });

        // Get unique manufacturers
        const manufacturers = [...new Set(items.map(item => item.manufacturer).filter(Boolean))].sort();
        manufacturerFilter.innerHTML = '<option value="">All Manufacturers</option>';
        manufacturers.forEach(manufacturer => {
            const option = document.createElement('option');
            option.value = manufacturer;
            option.textContent = manufacturer;
            manufacturerFilter.appendChild(option);
        });

        // Add event listeners to filters
        categoryFilter.addEventListener('change', applyFilters);
        statusFilter.addEventListener('change', applyFilters);
        manufacturerFilter.addEventListener('change', applyFilters);
        searchFilter.addEventListener('input', applyFilters);
    }

    // Function to apply filters
    function applyFilters() {
        const categoryValue = categoryFilter.value;
        const statusValue = statusFilter.value;
        const manufacturerValue = manufacturerFilter.value;
        const searchValue = searchFilter.value.toLowerCase();

        // Filter the data
        const filteredData = originalPreviewData.filter(item => {
            // Category filter
            if (categoryValue && item.category !== categoryValue) {
                return false;
            }

            // Status filter
            if (statusValue && item.status !== statusValue) {
                return false;
            }

            // Manufacturer filter
            if (manufacturerValue && item.manufacturer !== manufacturerValue) {
                return false;
            }

            // Search filter
            if (searchValue) {
                const searchFields = [
                    item.name,
                    item.category,
                    item.serial_number,
                    item.model,
                    item.manufacturer,
                    item.location
                ].filter(Boolean).map(field => field.toLowerCase());

                return searchFields.some(field => field.includes(searchValue));
            }

            return true;
        });

        // Update the table
        updatePreviewTable(filteredData);

        // Update the count
        document.getElementById('total-items').textContent = originalPreviewData.length;
    }

    // Function to update the preview table
    function updatePreviewTable(items) {
        const previewTable = document.getElementById('preview-table-body');
        previewTable.innerHTML = '';

        items.forEach((item, index) => {
            if (index < 100) { // Limit to 100 rows for performance
                const row = document.createElement('tr');
                row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                // Format dates if they exist
                const purchaseDate = item.purchase_date ? new Date(item.purchase_date).toLocaleDateString() : 'N/A';
                const warrantyExpiry = item.warranty_expiry ? new Date(item.warranty_expiry).toLocaleDateString() : 'N/A';

                row.innerHTML = `
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${item.name || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.category || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.serial_number || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.model || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.manufacturer || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${purchaseDate}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.purchase_cost || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${warrantyExpiry}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.status || 'available'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.location || 'N/A'}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.notes || 'N/A'}</td>
                `;

                previewTable.appendChild(row);
            }
        });

        if (items.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" class="px-4 py-4 text-center text-sm text-gray-500">
                    No items match the selected filters
                </td>
            `;
            previewTable.appendChild(row);
        }
    }

    // Function to update file name and size display
    function updateFileName(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Format file size
            let size;
            if (file.size < 1024) {
                size = file.size + ' bytes';
            } else if (file.size < 1024 * 1024) {
                size = (file.size / 1024).toFixed(2) + ' KB';
            } else {
                size = (file.size / (1024 * 1024)).toFixed(2) + ' MB';
            }

            // Update file info display
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = size;
            document.getElementById('fileInfo').classList.remove('hidden');

            // Add drag and drop functionality
            const dropZone = document.getElementById('dropZone');
            dropZone.classList.add('border-it-admin-primary');

            // Add remove file functionality
            document.getElementById('removeFile').addEventListener('click', function() {
                input.value = '';
                document.getElementById('fileInfo').classList.add('hidden');
                dropZone.classList.remove('border-it-admin-primary');
            });
        }
    }
</script>
