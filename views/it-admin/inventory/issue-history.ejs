<h1 class="text-2xl font-bold mb-6">Item Issue History</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Item Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/inventory" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back to Inventory
            </a>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Name:</span>
                        <span class="w-2/3 font-medium"><%= item.name %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Category:</span>
                        <span class="w-2/3 font-medium"><%= item.category_name || 'Uncategorized' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Serial Number:</span>
                        <span class="w-2/3 font-medium"><%= item.serial_number || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Status:</span>
                        <span class="w-2/3">
                            <% if (item.status === 'available') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                            <% } else if (item.status === 'assigned') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
                            <% } else if (item.status === 'maintenance') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Repair</span>
                            <% } else if (item.status === 'retired') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
                            <% } else { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><%= item.status %></span>
                            <% } %>
                        </span>
                    </div>
                </div>
            </div>
            <div>
                <% if (item.image) { %>
                    <div class="mb-4">
                        <img src="<%= item.image %>" alt="<%= item.name %>" class="w-full h-auto rounded-lg shadow-md object-cover max-h-64">
                    </div>
                <% } else { %>
                    <div class="mb-4 bg-gray-100 rounded-lg shadow-md p-8 flex items-center justify-center">
                        <% if (item.category_name && item.category_name.toLowerCase().includes('desktop')) { %>
                            <i class="fas fa-desktop text-gray-500 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('laptop')) { %>
                            <i class="fas fa-laptop text-gray-500 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('tablet')) { %>
                            <i class="fas fa-tablet-alt text-gray-500 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('projector')) { %>
                            <i class="fas fa-projector text-gray-500 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('printer')) { %>
                            <i class="fas fa-print text-gray-500 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('network')) { %>
                            <i class="fas fa-network-wired text-gray-500 text-6xl"></i>
                        <% } else { %>
                            <i class="fas fa-hdd text-gray-500 text-6xl"></i>
                        <% } %>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<div class="bg-white rounded-lg shadow">
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Issue History</h2>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-it-admin-primary">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Transaction ID</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued To</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued By</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Expected Return</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Received Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Received By</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% if (transactions && transactions.length > 0) { %>
                    <% transactions.forEach(transaction => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= transaction.transaction_id %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_to_full_name || transaction.issued_to_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_by_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_date %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.expected_return_date || 'Not specified' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.received_date || 'Not returned' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.received_by_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (transaction.received_date) { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Returned
                                    </span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Issued
                                    </span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <% if (!transaction.received_date) { %>
                                        <a href="/it-admin/inventory/transactions/<%= transaction.transaction_id %>/loan-voucher" class="text-blue-600 hover:text-blue-900" title="Print Voucher">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    <% } %>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="9" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No transaction history found for this item.
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any JavaScript functionality here
    });
</script>
