<h1 class="text-2xl font-bold mb-6">Issue Inventory Item</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold"><%= item.name %></h2>
        <div class="flex space-x-2">
            <a href="/it-admin/inventory/<%= item.item_id %>" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>
    <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <p class="text-sm text-gray-500">Category</p>
                <p class="font-medium"><%= item.category_name || 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Serial Number</p>
                <p class="font-medium"><%= item.serial_number || 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Status</p>
                <p class="font-medium">
                    <span class="px-2 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                        Available
                    </span>
                </p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Asset Tag</p>
                <p class="font-medium"><%= item.asset_tag || 'N/A' %></p>
            </div>
        </div>
    </div>
</div>

<div class="bg-white rounded-lg shadow">
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Issue Item</h2>
    </div>
    <div class="p-4">
        <form action="/it-admin/inventory/<%= item.item_id %>/issue" method="POST" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="issued_to" class="block text-sm font-medium text-gray-700 mb-1">Issue To *</label>
                    <select id="issued_to" name="issued_to" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select User</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>"><%= user.username %> (<%= user.name || user.email %>)</option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="expected_return_date" class="block text-sm font-medium text-gray-700 mb-1">Expected Return Date</label>
                    <input type="date" id="expected_return_date" name="expected_return_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
            </div>
            
            <div>
                <label for="condition_on_issue" class="block text-sm font-medium text-gray-700 mb-1">Condition on Issue</label>
                <select id="condition_on_issue" name="condition_on_issue" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="excellent">Excellent - Like new</option>
                    <option value="good" selected>Good - Minor wear</option>
                    <option value="fair">Fair - Visible wear but functional</option>
                    <option value="poor">Poor - Significant wear, may have issues</option>
                </select>
            </div>
            
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
            </div>
            
            <div class="flex justify-end space-x-2">
                <a href="/it-admin/inventory/<%= item.item_id %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Issue Item</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for user select
        $('#issued_to').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select User'
        });
    });
</script>
