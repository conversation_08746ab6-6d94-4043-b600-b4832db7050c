<h1 class="text-2xl font-bold mb-6">Receive Inventory Item</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold"><%= item.name %></h2>
        <div class="flex space-x-2">
            <a href="/it-admin/inventory/<%= item.item_id %>" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>
    <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <p class="text-sm text-gray-500">Category</p>
                <p class="font-medium"><%= item.category_name || 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Serial Number</p>
                <p class="font-medium"><%= item.serial_number || 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Status</p>
                <p class="font-medium">
                    <span class="px-2 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800">
                        Assigned
                    </span>
                </p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Asset Tag</p>
                <p class="font-medium"><%= item.asset_tag || 'N/A' %></p>
            </div>
        </div>
    </div>
</div>

<% if (transaction) { %>
<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Current Assignment</h2>
    </div>
    <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-500">Issued To</p>
                <p class="font-medium"><%= transaction.issued_to_name || 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Issued Date</p>
                <p class="font-medium"><%= transaction.issued_date ? new Date(transaction.issued_date).toLocaleDateString() : 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Expected Return</p>
                <p class="font-medium"><%= transaction.expected_return_date ? new Date(transaction.expected_return_date).toLocaleDateString() : 'N/A' %></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Condition on Issue</p>
                <p class="font-medium"><%= transaction.condition_on_issue || 'N/A' %></p>
            </div>
            <% if (transaction.notes) { %>
            <div class="col-span-2">
                <p class="text-sm text-gray-500">Notes</p>
                <p class="font-medium"><%= transaction.notes %></p>
            </div>
            <% } %>
        </div>
    </div>
</div>
<% } %>

<div class="bg-white rounded-lg shadow">
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Receive Item</h2>
    </div>
    <div class="p-4">
        <% if (transaction) { %>
        <form action="/it-admin/inventory/<%= item.item_id %>/receive" method="POST" class="space-y-4">
            <input type="hidden" name="transaction_id" value="<%= transaction.transaction_id %>">
            
            <div>
                <label for="condition_on_return" class="block text-sm font-medium text-gray-700 mb-1">Condition on Return *</label>
                <select id="condition_on_return" name="condition_on_return" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="excellent">Excellent - Like new</option>
                    <option value="good" selected>Good - Minor wear</option>
                    <option value="fair">Fair - Visible wear but functional</option>
                    <option value="poor">Poor - Significant wear, may have issues</option>
                    <option value="damaged">Damaged - Requires repair</option>
                    <option value="non-functional">Non-functional - Not working</option>
                </select>
            </div>
            
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
            </div>
            
            <div class="p-4 bg-gray-50 rounded-md">
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="create_issue_ticket" name="create_issue_ticket" type="checkbox" value="yes" class="h-4 w-4 text-it-admin-primary border-gray-300 rounded focus:ring-it-admin-primary">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="create_issue_ticket" class="font-medium text-gray-700">Create Issue Ticket</label>
                        <p class="text-gray-500">If the item has issues, create a ticket to track repairs</p>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-2">
                <a href="/it-admin/inventory/<%= item.item_id %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Receive Item</button>
            </div>
        </form>
        <% } else { %>
        <div class="p-4 bg-yellow-50 text-yellow-800 rounded-md">
            <p>No active transaction found for this item. Please check the item status and try again.</p>
        </div>
        <div class="mt-4">
            <a href="/it-admin/inventory" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Back to Inventory</a>
        </div>
        <% } %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide issue ticket option based on condition
        const conditionSelect = document.getElementById('condition_on_return');
        const createIssueTicket = document.getElementById('create_issue_ticket');
        
        if (conditionSelect && createIssueTicket) {
            conditionSelect.addEventListener('change', function() {
                const value = this.value;
                if (value === 'damaged' || value === 'non-functional' || value === 'poor') {
                    createIssueTicket.checked = true;
                }
            });
        }
    });
</script>
