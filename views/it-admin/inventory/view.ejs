<h1 class="text-2xl font-bold mb-6">Inventory Item Details</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold"><%= item.name %></h2>
        <div class="flex space-x-2">
            <a href="/it-admin/inventory/<%= item.item_id %>/edit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition">
                <i class="fas fa-edit mr-2"></i> Edit
            </a>
            <a href="/it-admin/inventory" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Item Image -->
            <div>
                <% if (item.image) { %>
                    <div class="mb-4 relative h-64 overflow-hidden rounded-lg shadow-md">
                        <img src="<%= item.image %>" alt="<%= item.name %>" class="w-full h-full object-contain absolute inset-0" onerror="this.onerror=null; this.src='/img/no-image.png'; this.classList.add('p-4');">
                        <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-30">
                            <a href="<%= item.image %>" target="_blank" class="bg-white p-2 rounded-full">
                                <i class="fas fa-search-plus text-gray-700"></i>
                            </a>
                        </div>
                    </div>
                <% } else { %>
                    <div class="mb-4 bg-gray-100 rounded-lg shadow-md p-8 flex items-center justify-center">
                        <% if (item.category_name && item.category_name.toLowerCase().includes('desktop')) { %>
                            <i class="fas fa-desktop text-gray-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('laptop')) { %>
                            <i class="fas fa-laptop text-blue-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('tablet')) { %>
                            <i class="fas fa-tablet-alt text-indigo-600 text-6xl"></i>
                        <% } else if (item.category_name && (item.category_name.toLowerCase().includes('projector') || item.category_name.toLowerCase().includes('audio/video') || item.category_name.toLowerCase().includes('interactive'))) { %>
                            <i class="fas fa-video text-green-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('printer')) { %>
                            <i class="fas fa-print text-red-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('network')) { %>
                            <i class="fas fa-network-wired text-purple-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('ups')) { %>
                            <i class="fas fa-battery-full text-yellow-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('tv')) { %>
                            <i class="fas fa-tv text-teal-600 text-6xl"></i>
                        <% } else if (item.category_name && item.category_name.toLowerCase().includes('power')) { %>
                            <i class="fas fa-plug text-amber-600 text-6xl"></i>
                        <% } else { %>
                            <i class="fas fa-hdd text-gray-600 text-6xl"></i>
                        <% } %>
                    </div>
                <% } %>
            </div>

            <!-- Basic Information -->
            <div class="md:col-span-2">
                <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Name:</span>
                        <span class="w-2/3 font-medium"><%= item.name %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Category:</span>
                        <span class="w-2/3 font-medium"><%= item.category_name || 'Uncategorized' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Serial Number:</span>
                        <span class="w-2/3 font-medium"><%= item.serial_number || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Model:</span>
                        <span class="w-2/3 font-medium"><%= item.model || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Manufacturer:</span>
                        <span class="w-2/3 font-medium"><%= item.manufacturer || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Status:</span>
                        <span class="w-2/3">
                            <% if (item.status === 'available') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Available</span>
                            <% } else if (item.status === 'assigned') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Assigned</span>
                            <% } else if (item.status === 'maintenance') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Repair</span>
                            <% } else if (item.status === 'retired') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Retired</span>
                            <% } else { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800"><%= item.status %></span>
                            <% } %>
                        </span>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-lg font-semibold mb-4">Additional Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Purchase Date:</span>
                        <span class="w-2/3 font-medium"><%= item.purchase_date ? formatDate(item.purchase_date) : 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Warranty Expiry:</span>
                        <span class="w-2/3 font-medium"><%= item.warranty_expiry ? formatDate(item.warranty_expiry) : 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Purchase Cost:</span>
                        <span class="w-2/3 font-medium"><%= item.purchase_cost ? '₹' + item.purchase_cost : 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Location:</span>
                        <span class="w-2/3 font-medium"><%= item.location || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Created By:</span>
                        <span class="w-2/3 font-medium"><%= item.created_by_name || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Created At:</span>
                        <span class="w-2/3 font-medium"><%= formatDateTime(item.created_at) %></span>
                    </div>
                </div>
            </div>
        </div>

        <% if (item.notes) { %>
            <div class="mt-6">
                <h3 class="text-lg font-semibold mb-2">Notes</h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <%= item.notes %>
                </div>
            </div>
        <% } %>

        <% if (item.network_info) { %>
            <div class="mt-6">
                <h3 class="text-lg font-semibold mb-2">Network Information</h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <pre class="whitespace-pre-wrap"><%= item.network_info %></pre>
                </div>
            </div>
        <% } %>
    </div>
</div>

<% if (conditions && conditions.length > 0) { %>
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold">Hardware Condition</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <% conditions.forEach(condition => { %>
                    <div class="flex items-center p-3 border rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium"><%= condition.display_name %></div>
                            <div class="text-sm text-gray-500"><%= condition.notes || 'No notes' %></div>
                        </div>
                        <div>
                            <% if (condition.condition_value === 'good') { %>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-semibold">Good</span>
                            <% } else if (condition.condition_value === 'fair') { %>
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">Fair</span>
                            <% } else if (condition.condition_value === 'poor') { %>
                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-semibold">Poor</span>
                            <% } else if (condition.condition_value === 'not_applicable') { %>
                                <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-semibold">N/A</span>
                            <% } else { %>
                                <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-semibold"><%= condition.condition_value %></span>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
<% } %>

<% if (transactions && transactions.length > 0) { %>
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold">Transaction History</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-it-admin-primary">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued To</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued By</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Return Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Received By</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Condition</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% transactions.forEach(transaction => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (transaction.transaction_type === 'issue') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Issue</span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Receive</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= formatDateTime(transaction.issued_date) %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= transaction.issued_to_name || 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= transaction.issued_by_name || 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= transaction.received_date ? formatDateTime(transaction.received_date) : 'Not returned' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= transaction.received_by_name || 'N/A' %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <%= transaction.condition_on_return || transaction.condition_on_issue || 'N/A' %>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    </div>
<% } %>

<div class="flex justify-between mt-6">
    <a href="/it-admin/inventory" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
        <i class="fas fa-arrow-left mr-2"></i> Back to Inventory
    </a>
    <div class="flex space-x-2">
        <a href="/it-admin/inventory/<%= item.item_id %>/edit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition">
            <i class="fas fa-edit mr-2"></i> Edit Item
        </a>
        <% if (item.status === 'available') { %>
            <a href="/it-admin/inventory/<%= item.item_id %>/issue" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-share mr-2"></i> Issue Item
            </a>
        <% } else if (item.status === 'assigned') { %>
            <a href="/it-admin/inventory/<%= item.item_id %>/receive" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-undo mr-2"></i> Receive Item
            </a>
        <% } %>
    </div>
</div>

<!-- Image Viewer Modal -->
<div id="imageViewerModal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 hidden">
    <div class="relative max-w-4xl max-h-screen p-4">
        <button id="closeImageViewer" class="absolute top-2 right-2 text-white bg-red-500 rounded-full w-8 h-8 flex items-center justify-center">
            <i class="fas fa-times"></i>
        </button>
        <img id="modalImage" src="" alt="Full size image" class="max-w-full max-h-[90vh] object-contain">
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image viewer functionality
        const imageViewerModal = document.getElementById('imageViewerModal');
        const modalImage = document.getElementById('modalImage');
        const closeImageViewer = document.getElementById('closeImageViewer');

        // Get all image links
        const imageLinks = document.querySelectorAll('a[href$=".jpg"], a[href$=".jpeg"], a[href$=".png"], a[href$=".gif"]');

        // Add click event to each image link
        imageLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const imageUrl = this.getAttribute('href');
                modalImage.src = imageUrl;
                imageViewerModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        });

        // Close modal when clicking the close button
        closeImageViewer.addEventListener('click', function() {
            imageViewerModal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling
        });

        // Close modal when clicking outside the image
        imageViewerModal.addEventListener('click', function(e) {
            if (e.target === imageViewerModal) {
                imageViewerModal.classList.add('hidden');
                document.body.style.overflow = ''; // Restore scrolling
            }
        });

        // Close modal when pressing Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !imageViewerModal.classList.contains('hidden')) {
                imageViewerModal.classList.add('hidden');
                document.body.style.overflow = ''; // Restore scrolling
            }
        });
    });
</script>
