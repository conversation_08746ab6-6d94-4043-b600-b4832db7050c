<h1 class="text-2xl font-bold mb-6">Report IT Issue</h1>

<div class="bg-white rounded-lg shadow p-6">
    <form action="/it-admin/issues/create" method="POST" class="space-y-6" enctype="multipart/form-data">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Issue Title <span class="text-red-600">*</span></label>
                    <input type="text" id="title" name="title" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                </div>

                <div>
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Related Hardware (Optional)</label>
                    <select id="item_id" name="item_id" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                        <option value="">-- Select Hardware Item --</option>
                        <% if (items && items.length > 0) { %>
                            <% items.forEach(item => { %>
                                <option value="<%= item.item_id %>"><%= item.name %> (<%= item.serial_number || 'No S/N' %>)</option>
                            <% }); %>
                        <% } %>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Issue Type</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="issue_type" value="hardware" checked class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Hardware</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="issue_type" value="software" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Software</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="issue_type" value="network" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Network</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="issue_type" value="other" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Other</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" value="low" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Low</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" value="medium" checked class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Medium</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" value="high" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">High</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="priority" value="critical" class="text-it-admin-primary focus:ring-it-admin-primary">
                            <span class="ml-2">Critical</span>
                        </label>
                    </div>
                </div>
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Issue Description <span class="text-red-600">*</span></label>
                <textarea id="description" name="description" rows="10" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary"></textarea>
                <p class="text-sm text-gray-500 mt-1">Please provide detailed information about the issue, including steps to reproduce if applicable.</p>
            </div>
        </div>

        <!-- Attachments -->
        <div>
            <h2 class="text-lg font-medium text-gray-700 mb-4">Attachments</h2>
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label for="photos" class="block text-sm font-medium text-gray-700 mb-1">Upload Images (Optional)</label>
                    <div class="flex items-center justify-center w-full">
                        <label for="photos" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB (max 5 files)</p>
                            </div>
                            <input id="photos" name="photos" type="file" class="hidden" multiple accept="image/*" />
                        </label>
                    </div>
                    <div class="mt-2 flex items-center">
                        <span id="file_count" class="text-sm text-gray-500">No files selected</span>
                        <button type="button" id="clear_files" class="ml-2 text-sm text-red-500 hover:text-red-700 hidden">Clear all</button>
                    </div>
                </div>
                <div id="photo_previews" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-2"></div>
            </div>
        </div>

        <div class="flex justify-end space-x-3">
            <a href="/it-admin/issues" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">
                Cancel
            </a>
            <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">
                Submit Issue
            </button>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for select dropdowns
        $('#item_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Hardware Item',
            allow_single_deselect: true
        });

        // File upload preview
        const photoInput = document.getElementById('photos');
        const previewContainer = document.getElementById('photo_previews');
        const fileCountDisplay = document.getElementById('file_count');
        const clearFilesButton = document.getElementById('clear_files');
        const MAX_FILES = 5;
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

        // Handle file selection
        photoInput.addEventListener('change', function() {
            // Clear previous previews
            previewContainer.innerHTML = '';

            // Get selected files
            const files = Array.from(this.files);

            // Update file count display
            if (files.length === 0) {
                fileCountDisplay.textContent = 'No files selected';
                clearFilesButton.classList.add('hidden');
            } else {
                fileCountDisplay.textContent = `${files.length} file(s) selected`;
                clearFilesButton.classList.remove('hidden');
            }

            // Check if too many files are selected
            if (files.length > MAX_FILES) {
                alert(`You can only upload a maximum of ${MAX_FILES} files.`);
                this.value = '';
                fileCountDisplay.textContent = 'No files selected';
                clearFilesButton.classList.add('hidden');
                return;
            }

            // Process each file
            files.forEach(file => {
                // Check file type
                if (!file.type.startsWith('image/')) {
                    alert(`File "${file.name}" is not an image.`);
                    return;
                }

                // Check file size
                if (file.size > MAX_FILE_SIZE) {
                    alert(`File "${file.name}" exceeds the maximum file size of 5MB.`);
                    return;
                }

                // Create preview
                const preview = document.createElement('div');
                preview.className = 'relative border rounded-lg overflow-hidden';

                // Create image preview
                const img = document.createElement('img');
                img.className = 'w-full h-32 object-cover';
                img.alt = file.name;

                // Read file and set preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

                // Create file info
                const info = document.createElement('div');
                info.className = 'p-2 bg-gray-100 text-xs truncate';
                info.textContent = file.name;

                // Add elements to preview
                preview.appendChild(img);
                preview.appendChild(info);
                previewContainer.appendChild(preview);
            });
        });

        // Clear files button
        clearFilesButton.addEventListener('click', function() {
            photoInput.value = '';
            previewContainer.innerHTML = '';
            fileCountDisplay.textContent = 'No files selected';
            this.classList.add('hidden');
        });
    });
</script>
