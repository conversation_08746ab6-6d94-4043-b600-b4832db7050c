<h1 class="text-2xl font-bold mb-6">Edit Issue #<%= issue.id %></h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Edit Issue</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/issues/<%= issue.id %>" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back to Issue
            </a>
        </div>
    </div>
    <div class="p-6">
        <form action="/it-admin/issues/<%= issue.id %>/edit" method="POST" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                    <input type="text" id="title" name="title" value="<%= issue.title %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Related Item</label>
                    <select id="item_id" name="item_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">None</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= issue.item_id == item.item_id ? 'selected' : '' %>>
                                <%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %>
                            </option>
                        <% }); %>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-1">Issue Type *</label>
                    <select id="issue_type" name="issue_type" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="hardware" <%= issue.issue_type === 'hardware' ? 'selected' : '' %>>Hardware</option>
                        <option value="software" <%= issue.issue_type === 'software' ? 'selected' : '' %>>Software</option>
                        <option value="network" <%= issue.issue_type === 'network' ? 'selected' : '' %>>Network</option>
                        <option value="other" <%= issue.issue_type === 'other' ? 'selected' : '' %>>Other</option>
                    </select>
                </div>
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority *</label>
                    <select id="priority" name="priority" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="high" <%= issue.priority === 'high' ? 'selected' : '' %>>High</option>
                        <option value="medium" <%= issue.priority === 'medium' ? 'selected' : '' %>>Medium</option>
                        <option value="low" <%= issue.priority === 'low' ? 'selected' : '' %>>Low</option>
                    </select>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                    <select id="status" name="status" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="open" <%= issue.status === 'open' ? 'selected' : '' %>>Open</option>
                        <option value="in_progress" <%= issue.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                        <option value="resolved" <%= issue.status === 'resolved' ? 'selected' : '' %>>Resolved</option>
                        <option value="closed" <%= issue.status === 'closed' ? 'selected' : '' %>>Closed</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                <textarea id="description" name="description" rows="5" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"><%= issue.description %></textarea>
            </div>
            
            <div>
                <label for="resolution" class="block text-sm font-medium text-gray-700 mb-1">Resolution</label>
                <textarea id="resolution" name="resolution" rows="5" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"><%= issue.resolution || '' %></textarea>
                <p class="mt-1 text-sm text-gray-500">Add resolution details when the issue is resolved or closed.</p>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <a href="/it-admin/issues/<%= issue.id %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Update Issue</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for item dropdown
        $('#item_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Item'
        });
        
        // Show resolution field only for resolved or closed status
        const statusSelect = document.getElementById('status');
        const resolutionField = document.getElementById('resolution').parentNode;
        
        function updateResolutionVisibility() {
            const status = statusSelect.value;
            if (status === 'resolved' || status === 'closed') {
                resolutionField.classList.remove('hidden');
                document.getElementById('resolution').setAttribute('required', 'required');
            } else {
                resolutionField.classList.add('hidden');
                document.getElementById('resolution').removeAttribute('required');
            }
        }
        
        // Initial check
        updateResolutionVisibility();
        
        // Update on status change
        statusSelect.addEventListener('change', updateResolutionVisibility);
    });
</script>
