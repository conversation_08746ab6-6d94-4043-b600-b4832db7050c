<h1 class="text-2xl font-bold mb-6">IT Issue Tracker</h1>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Total Issues</h3>
                <p class="text-3xl font-bold text-it-admin-primary mt-2"><%= stats.totalIssues || 0 %></p>
            </div>
            <div class="bg-it-admin-light p-3 rounded-full">
                <i class="fas fa-exclamation-circle text-it-admin-primary text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Open Issues</h3>
                <p class="text-3xl font-bold text-red-600 mt-2"><%= stats.openIssues || 0 %></p>
            </div>
            <div class="bg-red-100 p-3 rounded-full">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">In Progress</h3>
                <p class="text-3xl font-bold text-yellow-600 mt-2"><%= stats.inProgressIssues || 0 %></p>
            </div>
            <div class="bg-yellow-100 p-3 rounded-full">
                <i class="fas fa-tools text-yellow-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">Resolved</h3>
                <p class="text-3xl font-bold text-green-600 mt-2"><%= stats.resolvedIssues || 0 %></p>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Issue List</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/issues/create" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-plus mr-2"></i> Report Issue
            </a>
            <button id="filter-button" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-filter mr-2"></i> Advanced Filter
            </button>
            <button id="exportCSV" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-file-csv mr-2"></i> Export CSV
            </button>
            <button id="exportPDF" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-file-pdf mr-2"></i> Export PDF
            </button>
        </div>
    </div>
    <!-- Quick Filters -->
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <form action="/it-admin/issues" method="GET" class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-[150px]">
                <label for="quick_status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <div class="flex flex-wrap gap-2">
                    <a href="/it-admin/issues?status=open" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.status === 'open' ? 'bg-red-100 text-red-800 border border-red-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Open (<%= stats.openIssues || 0 %>)
                    </a>
                    <a href="/it-admin/issues?status=in_progress" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800 border border-yellow-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        In Progress (<%= stats.inProgressIssues || 0 %>)
                    </a>
                    <a href="/it-admin/issues?status=resolved" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.status === 'resolved' ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Resolved (<%= stats.resolvedIssues || 0 %>)
                    </a>
                    <a href="/it-admin/issues?status=closed" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.status === 'closed' ? 'bg-gray-300 text-gray-800 border border-gray-400' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Closed (<%= stats.closedIssues || 0 %>)
                    </a>
                    <a href="/it-admin/issues" class="px-3 py-1 rounded-full text-sm <%= !locals.filters || !filters.status ? 'bg-it-admin-primary text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        All (<%= stats.totalIssues || 0 %>)
                    </a>
                </div>
            </div>
            <div class="flex-1 min-w-[150px]">
                <label for="quick_type" class="block text-sm font-medium text-gray-700 mb-1">Issue Type</label>
                <div class="flex flex-wrap gap-2">
                    <a href="/it-admin/issues?issue_type=hardware" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.issueType === 'hardware' ? 'bg-red-100 text-red-800 border border-red-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Hardware
                    </a>
                    <a href="/it-admin/issues?issue_type=software" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.issueType === 'software' ? 'bg-blue-100 text-blue-800 border border-blue-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Software
                    </a>
                    <a href="/it-admin/issues?issue_type=network" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.issueType === 'network' ? 'bg-purple-100 text-purple-800 border border-purple-300' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Network
                    </a>
                    <a href="/it-admin/issues?issue_type=other" class="px-3 py-1 rounded-full text-sm <%= locals.filters && filters.issueType === 'other' ? 'bg-gray-300 text-gray-800 border border-gray-400' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' %>">
                        Other
                    </a>
                </div>
            </div>
            <div class="flex-1 min-w-[200px]">
                <label for="quick_search" class="block text-sm font-medium text-gray-700 mb-1">Quick Search</label>
                <div class="flex">
                    <input type="text" id="quick_search" name="search" value="<%= locals.filters && filters.search || '' %>" placeholder="Search issues..." class="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <button type="submit" class="bg-it-admin-primary text-white px-4 py-2 rounded-r hover:bg-it-admin-secondary transition">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="overflow-x-auto">
        <table id="issues-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-it-admin-primary">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">ID</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Item</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issue Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Description</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Reported By</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Date</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% if (issues && issues.length > 0) { %>
                    <% issues.forEach(issue => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= issue.issue_id %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= issue.item_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <% if (issue.issue_type === 'hardware') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Hardware</span>
                                <% } else if (issue.issue_type === 'software') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Software</span>
                                <% } else if (issue.issue_type === 'network') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Network</span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Other</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="truncate max-w-xs"><%= issue.description %></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (issue.status === 'open') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Open</span>
                                <% } else if (issue.status === 'in_progress') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                                <% } else if (issue.status === 'resolved') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                                <% } else if (issue.status === 'closed') { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= issue.reported_by_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(issue.created_at) %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button type="button" class="text-it-admin-primary hover:text-it-admin-secondary view-issue"
                                        data-id="<%= issue.issue_id %>"
                                        data-title="<%= issue.title %>"
                                        data-item="<%= issue.item_name || 'N/A' %>"
                                        data-type="<%= issue.issue_type %>"
                                        data-priority="<%= issue.priority %>"
                                        data-status="<%= issue.status %>"
                                        data-description="<%= issue.description %>"
                                        data-reported-by="<%= issue.reported_by_name || 'N/A' %>"
                                        data-created-at="<%= formatDate(issue.created_at) %>"
                                        title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="text-indigo-600 hover:text-indigo-900 edit-issue"
                                        data-id="<%= issue.issue_id %>"
                                        data-title="<%= issue.title %>"
                                        data-item-id="<%= issue.item_id || '' %>"
                                        data-type="<%= issue.issue_type %>"
                                        data-priority="<%= issue.priority %>"
                                        data-status="<%= issue.status %>"
                                        data-description="<%= issue.description %>"
                                        data-resolution="<%= issue.resolution_notes || '' %>"
                                        title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="text-red-600 hover:text-red-900 delete-issue" data-id="<%= issue.issue_id %>" title="Delete">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="8" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No issues found. Click "Report Issue" to create a new issue.
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<!-- Filter Dialog -->
<div id="filter-dialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div class="bg-it-admin-primary text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 class="text-xl font-semibold">Filter Issues</h3>
            <button id="filter-dialog-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form action="/it-admin/issues" method="GET" class="space-y-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="open" <%= locals.filters && filters.status === 'open' ? 'selected' : '' %>>Open</option>
                    <option value="in_progress" <%= locals.filters && filters.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                    <option value="resolved" <%= locals.filters && filters.status === 'resolved' ? 'selected' : '' %>>Resolved</option>
                    <option value="closed" <%= locals.filters && filters.status === 'closed' ? 'selected' : '' %>>Closed</option>
                </select>
            </div>
            <div>
                <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-1">Issue Type</label>
                <select id="issue_type" name="issue_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Types</option>
                    <option value="hardware" <%= locals.filters && filters.issueType === 'hardware' ? 'selected' : '' %>>Hardware</option>
                    <option value="software" <%= locals.filters && filters.issueType === 'software' ? 'selected' : '' %>>Software</option>
                    <option value="network" <%= locals.filters && filters.issueType === 'network' ? 'selected' : '' %>>Network</option>
                    <option value="other" <%= locals.filters && filters.issueType === 'other' ? 'selected' : '' %>>Other</option>
                </select>
            </div>
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="search" name="search" value="<%= locals.filters && filters.search || '' %>" placeholder="Search by title, description, item, or user" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
            </div>
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <a href="/it-admin/issues" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Clear Filters
                </a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Issue Modal -->
<div id="view-issue-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="bg-it-admin-primary text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 class="text-xl font-semibold">Issue Details</h3>
            <button id="view-issue-modal-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">ID:</span>
                        <span id="view-issue-id" class="w-2/3 font-medium"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Title:</span>
                        <span id="view-issue-title" class="w-2/3 font-medium"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Item:</span>
                        <span id="view-issue-item" class="w-2/3 font-medium"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Issue Type:</span>
                        <span id="view-issue-type" class="w-2/3"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Priority:</span>
                        <span id="view-issue-priority" class="w-2/3"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Status:</span>
                        <span id="view-issue-status" class="w-2/3"></span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-4">Additional Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Reported By:</span>
                        <span id="view-issue-reported-by" class="w-2/3 font-medium"></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Reported On:</span>
                        <span id="view-issue-created-at" class="w-2/3 font-medium"></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">Description</h3>
            <div id="view-issue-description" class="bg-gray-50 p-4 rounded-lg border border-gray-200 whitespace-pre-wrap"></div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">Attachments</h3>
            <div id="view-issue-attachments" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- Attachments will be displayed here -->
                <div class="text-gray-500 italic" id="no-attachments-message">No attachments found</div>
            </div>
        </div>

        <div class="flex justify-end">
            <button id="view-issue-close-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Edit Issue Modal -->
<div id="edit-issue-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="bg-it-admin-primary text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 class="text-xl font-semibold">Edit Issue</h3>
            <button id="edit-issue-modal-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="edit-issue-form" class="space-y-6" enctype="multipart/form-data" method="post">
            <input type="hidden" id="edit-issue-id" name="id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="edit-issue-title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                    <input type="text" id="edit-issue-title" name="title" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="edit-issue-item-id" class="block text-sm font-medium text-gray-700 mb-1">Related Item</label>
                    <select id="edit-issue-item-id" name="item_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">None</option>
                        <% if (locals.items && items.length > 0) { %>
                            <% items.forEach(item => { %>
                                <option value="<%= item.item_id %>"><%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %></option>
                            <% }); %>
                        <% } %>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="edit-issue-type" class="block text-sm font-medium text-gray-700 mb-1">Issue Type *</label>
                    <select id="edit-issue-type" name="issue_type" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="hardware">Hardware</option>
                        <option value="software">Software</option>
                        <option value="network">Network</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div>
                    <label for="edit-issue-priority" class="block text-sm font-medium text-gray-700 mb-1">Priority *</label>
                    <select id="edit-issue-priority" name="priority" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                    </select>
                </div>
                <div>
                    <label for="edit-issue-status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                    <select id="edit-issue-status" name="status" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="open">Open</option>
                        <option value="in_progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
            </div>

            <div>
                <label for="edit-issue-description" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                <textarea id="edit-issue-description" name="description" rows="5" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
            </div>

            <div id="edit-issue-resolution-container">
                <label for="edit-issue-resolution" class="block text-sm font-medium text-gray-700 mb-1">Resolution</label>
                <textarea id="edit-issue-resolution" name="resolution_notes" rows="5" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                <p class="mt-1 text-sm text-gray-500">Add resolution details when the issue is resolved or closed.</p>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Attachments</label>
                <div id="edit-issue-attachments" class="mb-3 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Attachments will be displayed here -->
                </div>
                <div class="mt-2">
                    <label for="edit-issue-photos" class="block text-sm font-medium text-gray-700 mb-1">Add Attachments</label>
                    <input type="file" id="edit-issue-photos" name="photos" multiple accept=".jpg,.jpeg,.pdf,application/pdf,image/jpeg" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-it-admin-primary file:text-white hover:file:bg-it-admin-secondary">
                    <p class="mt-1 text-sm text-gray-500">You can upload multiple files (max 5). Supported formats: JPG, PDF only.</p>

                    <!-- File preview container -->
                    <div id="file-preview-container" class="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4 hidden">
                        <!-- Preview items will be added here dynamically -->
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button type="button" id="edit-issue-cancel" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</button>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Update Issue</button>
            </div>
        </form>
    </div>
</div>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <div id="confirmation-dialog-header" class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center -mx-6 -mt-6 mb-4">
            <h3 id="confirmation-dialog-title" class="text-xl font-semibold">Delete Issue</h3>
            <button id="confirmation-dialog-close" class="text-white">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p id="confirmation-dialog-message" class="mb-6">Are you sure you want to delete this issue? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="confirmation-dialog-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                Cancel
            </button>
            <button id="confirmation-dialog-confirm" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toast notification function
        function showToast(message, type = 'info') {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'px-4 py-3 rounded shadow-lg flex items-center justify-between max-w-md transform transition-all duration-300 ease-in-out translate-x-full';

            // Set background color based on type
            if (type === 'success') {
                toast.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
            } else if (type === 'error') {
                toast.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
            } else if (type === 'warning') {
                toast.classList.add('bg-yellow-100', 'text-yellow-800', 'border-l-4', 'border-yellow-500');
            } else {
                toast.classList.add('bg-blue-100', 'text-blue-800', 'border-l-4', 'border-blue-500');
            }

            // Set content
            toast.innerHTML = `
                <div class="flex items-center">
                    <span class="mr-2">
                        ${type === 'success' ? '<i class="fas fa-check-circle"></i>' :
                          type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' :
                          type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' :
                          '<i class="fas fa-info-circle"></i>'}
                    </span>
                    <span>${message}</span>
                </div>
                <button class="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // Add to container
            toastContainer.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);

            // Add close button functionality
            const closeButton = toast.querySelector('button');
            closeButton.addEventListener('click', () => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            });

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        // Get all inventory items for the edit modal
        let inventoryItems = [];
        <% if (locals.items && items.length > 0) { %>
            inventoryItems = [
                <% items.forEach(item => { %>
                    {
                        id: '<%= item.item_id %>',
                        name: '<%= item.name %> <%= item.serial_number ? `(${item.serial_number})` : '' %>'
                    },
                <% }); %>
            ];
        <% } %>

        // Filter dialog functionality
        const filterDialog = document.getElementById('filter-dialog');
        const filterButton = document.getElementById('filter-button');
        const filterDialogClose = document.getElementById('filter-dialog-close');

        // Open filter dialog
        filterButton.addEventListener('click', function() {
            filterDialog.classList.remove('hidden');
        });

        // Close filter dialog
        function closeFilterDialog() {
            filterDialog.classList.add('hidden');
        }

        filterDialogClose.addEventListener('click', closeFilterDialog);
        filterDialog.addEventListener('click', function(e) {
            if (e.target === filterDialog) {
                closeFilterDialog();
            }
        }, { passive: true });

        // View issue modal functionality
        const viewIssueModal = document.getElementById('view-issue-modal');
        const viewIssueModalClose = document.getElementById('view-issue-modal-close');
        const viewIssueCloseBtn = document.getElementById('view-issue-close-btn');
        const viewButtons = document.querySelectorAll('.view-issue');

        // Open view issue modal
        viewButtons.forEach(button => {
            button.addEventListener('click', async function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                const item = this.getAttribute('data-item');
                const type = this.getAttribute('data-type');
                const priority = this.getAttribute('data-priority');
                const status = this.getAttribute('data-status');
                const description = this.getAttribute('data-description');
                const reportedBy = this.getAttribute('data-reported-by');
                const createdAt = this.getAttribute('data-created-at');

                // Set modal content
                document.getElementById('view-issue-id').textContent = id;
                document.getElementById('view-issue-title').textContent = title;
                document.getElementById('view-issue-item').textContent = item;
                document.getElementById('view-issue-description').textContent = description;
                document.getElementById('view-issue-reported-by').textContent = reportedBy;
                document.getElementById('view-issue-created-at').textContent = createdAt;

                // Fetch and display attachments
                try {
                    const result = await fetchAttachments(id);
                    if (result.success) {
                        displayAttachmentsInViewModal(result.attachments);
                    } else {
                        // Show no attachments message
                        displayAttachmentsInViewModal([]);
                    }
                } catch (error) {
                    console.error('Error fetching attachments:', error);
                    displayAttachmentsInViewModal([]);
                }

                // Set issue type with appropriate styling
                const typeSpan = document.getElementById('view-issue-type');
                typeSpan.innerHTML = '';
                const typeElement = document.createElement('span');
                typeElement.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full';

                if (type === 'hardware') {
                    typeElement.classList.add('bg-red-100', 'text-red-800');
                    typeElement.textContent = 'Hardware';
                } else if (type === 'software') {
                    typeElement.classList.add('bg-blue-100', 'text-blue-800');
                    typeElement.textContent = 'Software';
                } else if (type === 'network') {
                    typeElement.classList.add('bg-purple-100', 'text-purple-800');
                    typeElement.textContent = 'Network';
                } else {
                    typeElement.classList.add('bg-gray-100', 'text-gray-800');
                    typeElement.textContent = 'Other';
                }

                typeSpan.appendChild(typeElement);

                // Set priority with appropriate styling
                const prioritySpan = document.getElementById('view-issue-priority');
                prioritySpan.innerHTML = '';
                const priorityElement = document.createElement('span');
                priorityElement.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full';

                if (priority === 'high') {
                    priorityElement.classList.add('bg-red-100', 'text-red-800');
                    priorityElement.textContent = 'High';
                } else if (priority === 'medium') {
                    priorityElement.classList.add('bg-yellow-100', 'text-yellow-800');
                    priorityElement.textContent = 'Medium';
                } else if (priority === 'low') {
                    priorityElement.classList.add('bg-green-100', 'text-green-800');
                    priorityElement.textContent = 'Low';
                }

                prioritySpan.appendChild(priorityElement);

                // Set status with appropriate styling
                const statusSpan = document.getElementById('view-issue-status');
                statusSpan.innerHTML = '';
                const statusElement = document.createElement('span');
                statusElement.className = 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full';

                if (status === 'open') {
                    statusElement.classList.add('bg-red-100', 'text-red-800');
                    statusElement.textContent = 'Open';
                } else if (status === 'in_progress') {
                    statusElement.classList.add('bg-yellow-100', 'text-yellow-800');
                    statusElement.textContent = 'In Progress';
                } else if (status === 'resolved') {
                    statusElement.classList.add('bg-green-100', 'text-green-800');
                    statusElement.textContent = 'Resolved';
                } else if (status === 'closed') {
                    statusElement.classList.add('bg-gray-100', 'text-gray-800');
                    statusElement.textContent = 'Closed';
                }

                statusSpan.appendChild(statusElement);

                // Show modal
                viewIssueModal.classList.remove('hidden');
            });
        });

        // Close view issue modal
        function closeViewIssueModal() {
            viewIssueModal.classList.add('hidden');
        }

        viewIssueModalClose.addEventListener('click', closeViewIssueModal);
        viewIssueCloseBtn.addEventListener('click', closeViewIssueModal);
        viewIssueModal.addEventListener('click', function(e) {
            if (e.target === viewIssueModal) {
                closeViewIssueModal();
            }
        }, { passive: true });

        // Edit issue modal functionality
        const editIssueModal = document.getElementById('edit-issue-modal');
        const editIssueModalClose = document.getElementById('edit-issue-modal-close');
        const editIssueCancel = document.getElementById('edit-issue-cancel');
        const editIssueForm = document.getElementById('edit-issue-form');
        const editButtons = document.querySelectorAll('.edit-issue');

        // Open edit issue modal
        editButtons.forEach(button => {
            button.addEventListener('click', async function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                const itemId = this.getAttribute('data-item-id');
                const type = this.getAttribute('data-type');
                const priority = this.getAttribute('data-priority');
                const status = this.getAttribute('data-status');
                const description = this.getAttribute('data-description');
                const resolution = this.getAttribute('data-resolution');

                // Set form values
                document.getElementById('edit-issue-id').value = id;
                document.getElementById('edit-issue-title').value = title;
                document.getElementById('edit-issue-item-id').value = itemId;
                document.getElementById('edit-issue-type').value = type;
                document.getElementById('edit-issue-priority').value = priority;
                document.getElementById('edit-issue-status').value = status;
                document.getElementById('edit-issue-description').value = description;
                document.getElementById('edit-issue-resolution').value = resolution;

                // Show/hide resolution field based on status
                updateResolutionVisibility();

                // Fetch and display attachments
                try {
                    const result = await fetchAttachments(id);
                    if (result.success) {
                        displayAttachmentsInEditModal(result.attachments);
                    } else {
                        // Clear attachments container
                        document.getElementById('edit-issue-attachments').innerHTML = '';
                    }
                } catch (error) {
                    console.error('Error fetching attachments:', error);
                    document.getElementById('edit-issue-attachments').innerHTML = '';
                }

                // Show modal
                editIssueModal.classList.remove('hidden');
            });
        });

        // Close edit issue modal
        function closeEditIssueModal() {
            editIssueModal.classList.add('hidden');
        }

        editIssueModalClose.addEventListener('click', closeEditIssueModal);
        editIssueCancel.addEventListener('click', closeEditIssueModal);
        editIssueModal.addEventListener('click', function(e) {
            if (e.target === editIssueModal) {
                closeEditIssueModal();
            }
        }, { passive: true });

        // Show/hide resolution field based on status
        const statusSelect = document.getElementById('edit-issue-status');
        const resolutionContainer = document.getElementById('edit-issue-resolution-container');
        const resolutionField = document.getElementById('edit-issue-resolution');

        function updateResolutionVisibility() {
            const status = statusSelect.value;
            if (status === 'resolved' || status === 'closed') {
                resolutionContainer.classList.remove('hidden');
                resolutionField.setAttribute('required', 'required');
            } else {
                resolutionContainer.classList.add('hidden');
                resolutionField.removeAttribute('required');
            }
        }

        statusSelect.addEventListener('change', updateResolutionVisibility);

        // Function to fetch attachments for an issue
        async function fetchAttachments(issueId) {
            try {
                console.log(`Fetching attachments for issue ${issueId}`);
                const response = await fetch(`/it-admin/issues/${issueId}/attachments`);
                if (!response.ok) {
                    throw new Error(`Failed to fetch attachments: ${response.status} ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Error fetching attachments:', error);
                return { success: false, attachments: [] };
            }
        }

        // Function to display attachments in the view modal
        function displayAttachmentsInViewModal(attachments) {
            const container = document.getElementById('view-issue-attachments');
            const noAttachmentsMessage = document.getElementById('no-attachments-message');

            // Clear previous attachments
            container.innerHTML = '';

            if (attachments && attachments.length > 0) {
                noAttachmentsMessage.style.display = 'none';

                attachments.forEach(attachment => {
                    const attachmentEl = document.createElement('div');
                    attachmentEl.className = 'border rounded-lg overflow-hidden bg-white shadow-sm';

                    // Display based on file type
                    if (attachment.file_type.startsWith('image/')) {
                        // Image file
                        attachmentEl.innerHTML = `
                            <div class="relative pb-[75%] overflow-hidden">
                                <img src="${attachment.file_path}" alt="${attachment.file_name}"
                                     class="absolute inset-0 w-full h-full object-cover cursor-pointer"
                                     onclick="window.open('${attachment.file_path}', '_blank')">
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    } else if (attachment.file_type === 'application/pdf') {
                        // PDF file
                        attachmentEl.innerHTML = `
                            <div class="h-32 flex items-center justify-center bg-gray-100 cursor-pointer"
                                 onclick="window.open('${attachment.file_path}', '_blank')">
                                <i class="fas fa-file-pdf text-4xl text-red-500"></i>
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    } else {
                        // Other file types
                        attachmentEl.innerHTML = `
                            <div class="h-32 flex items-center justify-center bg-gray-100">
                                <i class="fas fa-file-alt text-4xl text-gray-400"></i>
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    }

                    container.appendChild(attachmentEl);
                });
            } else {
                noAttachmentsMessage.style.display = 'block';
                container.appendChild(noAttachmentsMessage);
            }
        }

        // Function to display attachments in the edit modal
        function displayAttachmentsInEditModal(attachments) {
            const container = document.getElementById('edit-issue-attachments');

            // Clear previous attachments
            container.innerHTML = '';

            if (attachments && attachments.length > 0) {
                attachments.forEach(attachment => {
                    const attachmentEl = document.createElement('div');
                    attachmentEl.className = 'border rounded-lg overflow-hidden bg-white shadow-sm';

                    // Display based on file type
                    if (attachment.file_type.startsWith('image/')) {
                        // Image file
                        attachmentEl.innerHTML = `
                            <div class="relative pb-[75%] overflow-hidden group">
                                <img src="${attachment.file_path}" alt="${attachment.file_name}"
                                     class="absolute inset-0 w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                    <button type="button" class="p-1 bg-white rounded-full text-red-500 hover:text-red-700"
                                            onclick="deleteAttachment(${attachment.attachment_id}, this.parentNode.parentNode.parentNode)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button type="button" class="p-1 bg-white rounded-full text-blue-500 hover:text-blue-700 ml-2"
                                            onclick="window.open('${attachment.file_path}', '_blank')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    } else if (attachment.file_type === 'application/pdf') {
                        // PDF file
                        attachmentEl.innerHTML = `
                            <div class="h-32 flex items-center justify-center bg-gray-100 relative group">
                                <i class="fas fa-file-pdf text-4xl text-red-500"></i>
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                    <button type="button" class="p-1 bg-white rounded-full text-red-500 hover:text-red-700"
                                            onclick="deleteAttachment(${attachment.attachment_id}, this.parentNode.parentNode.parentNode)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button type="button" class="p-1 bg-white rounded-full text-blue-500 hover:text-blue-700 ml-2"
                                            onclick="window.open('${attachment.file_path}', '_blank')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    } else {
                        // Other file types
                        attachmentEl.innerHTML = `
                            <div class="h-32 flex items-center justify-center bg-gray-100 relative group">
                                <i class="fas fa-file-alt text-4xl text-gray-400"></i>
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                    <button type="button" class="p-1 bg-white rounded-full text-red-500 hover:text-red-700"
                                            onclick="deleteAttachment(${attachment.attachment_id}, this.parentNode.parentNode.parentNode)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-2 text-xs truncate" title="${attachment.file_name}">
                                ${attachment.file_name}
                            </div>
                        `;
                    }

                    container.appendChild(attachmentEl);
                });
            }
        }

        // Function to delete an attachment
        window.deleteAttachment = async function(attachmentId, element) {
            if (!confirm('Are you sure you want to delete this attachment?')) {
                return;
            }

            try {
                console.log(`Deleting attachment ${attachmentId}`);
                const response = await fetch(`/it-admin/issues/attachments/${attachmentId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Remove the element from the DOM
                    element.remove();
                    showToast('Attachment deleted successfully', 'success');
                } else {
                    showToast('Error deleting attachment: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast('An error occurred while deleting the attachment', 'error');
            }
        };

        // Function to convert file to base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        // Function to validate file type (JPG or PDF only)
        function isValidFileType(file) {
            const validTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];
            return validTypes.includes(file.type);
        }

        // Function to create file preview
        function createFilePreview(file, index) {
            const previewContainer = document.getElementById('file-preview-container');
            const previewItem = document.createElement('div');
            previewItem.className = 'border rounded-lg overflow-hidden bg-white shadow-sm relative';
            previewItem.id = `file-preview-${index}`;

            // Create remove button
            const removeButton = document.createElement('button');
            removeButton.type = 'button';
            removeButton.className = 'absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10';
            removeButton.innerHTML = '<i class="fas fa-times"></i>';
            removeButton.onclick = function() {
                // Remove this file from the input
                const dt = new DataTransfer();
                const input = document.getElementById('edit-issue-photos');
                const { files } = input;

                for (let i = 0; i < files.length; i++) {
                    if (i !== index) {
                        dt.items.add(files[i]);
                    }
                }

                input.files = dt.files;

                // Remove the preview
                previewItem.remove();

                // Hide container if empty
                if (previewContainer.children.length === 0) {
                    previewContainer.classList.add('hidden');
                }
            };

            // Create preview content based on file type
            if (file.type.startsWith('image/')) {
                // Image preview
                const img = document.createElement('img');
                img.className = 'w-full h-32 object-cover';
                img.alt = file.name;

                // Read the file as data URL
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

                previewItem.appendChild(img);
            } else if (file.type === 'application/pdf') {
                // PDF preview (icon)
                const pdfPreview = document.createElement('div');
                pdfPreview.className = 'w-full h-32 bg-gray-100 flex items-center justify-center';
                pdfPreview.innerHTML = '<i class="fas fa-file-pdf text-red-500 text-4xl"></i>';

                previewItem.appendChild(pdfPreview);
            }

            // Add file name
            const fileName = document.createElement('div');
            fileName.className = 'p-2 text-xs truncate';
            fileName.title = file.name;
            fileName.textContent = file.name;

            previewItem.appendChild(removeButton);
            previewItem.appendChild(fileName);
            previewContainer.appendChild(previewItem);

            // Show the container
            previewContainer.classList.remove('hidden');
        }

        // Add event listener for file input change
        document.getElementById('edit-issue-photos').addEventListener('change', function(e) {
            const files = e.target.files;
            const previewContainer = document.getElementById('file-preview-container');

            // Clear previous previews
            previewContainer.innerHTML = '';
            previewContainer.classList.add('hidden');

            // Check if files are selected
            if (files.length === 0) return;

            // Check file count
            if (files.length > 5) {
                showToast('Maximum 5 files allowed', 'error');
                e.target.value = '';
                return;
            }

            // Validate and preview each file
            let hasInvalidFile = false;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Check file type
                if (!isValidFileType(file)) {
                    hasInvalidFile = true;
                    continue;
                }

                // Check file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    showToast(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                    hasInvalidFile = true;
                    continue;
                }

                // Create preview
                createFilePreview(file, i);
            }

            // If any invalid files, clear the input
            if (hasInvalidFile) {
                showToast('Only JPG and PDF files are allowed. Maximum size is 10MB.', 'error');
                e.target.value = '';
                previewContainer.innerHTML = '';
                previewContainer.classList.add('hidden');
            }
        });

        // Handle form submission
        editIssueForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Show loading indicator
            showToast('Processing your request...', 'info');

            const id = document.getElementById('edit-issue-id').value;

            // Ensure the ID is properly formatted and URL is correct
            if (!id || id.trim() === '') {
                showToast('Error: Issue ID is missing or invalid', 'error');
                return;
            }

            const cleanId = id.trim();
            console.log("Preparing data for issue with ID:", cleanId);

            try {
                // Create a regular object instead of FormData
                const issueData = {
                    title: document.getElementById('edit-issue-title').value,
                    description: document.getElementById('edit-issue-description').value,
                    issue_type: document.getElementById('edit-issue-type').value,
                    priority: document.getElementById('edit-issue-priority').value,
                    status: document.getElementById('edit-issue-status').value
                };

                const itemIdField = document.getElementById('edit-issue-item-id');
                if (itemIdField && itemIdField.value) {
                    issueData.item_id = itemIdField.value;
                }

                const resolutionField = document.getElementById('edit-issue-resolution');
                if (resolutionField && resolutionField.value) {
                    issueData.resolution_notes = resolutionField.value;
                }

                // Handle file uploads using base64
                const fileInput = document.getElementById('edit-issue-photos');
                if (fileInput && fileInput.files.length > 0) {
                    // Validate files again
                    let allFilesValid = true;
                    const files = fileInput.files;

                    // Check file count
                    if (files.length > 5) {
                        showToast('Maximum 5 files allowed', 'error');
                        return;
                    }

                    // Validate each file
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];

                        // Check file type
                        if (!isValidFileType(file)) {
                            showToast(`File ${file.name} is not a valid type. Only JPG and PDF are allowed.`, 'error');
                            allFilesValid = false;
                            break;
                        }

                        // Check file size (max 10MB)
                        if (file.size > 10 * 1024 * 1024) {
                            showToast(`File ${file.name} is too large. Maximum size is 10MB.`, 'error');
                            allFilesValid = false;
                            break;
                        }
                    }

                    if (!allFilesValid) {
                        return;
                    }

                    // Show loading message for file processing
                    showToast('Processing files, please wait...', 'info');

                    // Convert files to base64
                    const filePromises = [];
                    for (let i = 0; i < fileInput.files.length; i++) {
                        filePromises.push(fileToBase64(fileInput.files[i]));
                    }

                    // Wait for all files to be converted
                    const base64Files = await Promise.all(filePromises);

                    // Add file information
                    issueData.photos = base64Files.map((base64, index) => {
                        return {
                            name: fileInput.files[index].name,
                            type: fileInput.files[index].type,
                            size: fileInput.files[index].size,
                            data: base64
                        };
                    });

                    console.log(`Processed ${issueData.photos.length} files for upload`);
                }

                console.log("Data prepared, sending request...");

                // Send update request with JSON data
                fetch(`/it-admin/issues/${cleanId}/edit-base64`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(issueData)
                })
                .then(response => {
                    if (!response.ok) {
                        if (response.redirected) {
                            window.location.href = response.url;
                            return Promise.reject('Redirected');
                        }
                        return response.text().then(text => {
                            try {
                                // Try to parse as JSON
                                return JSON.parse(text);
                            } catch (e) {
                                // If not JSON, throw the text
                                throw new Error('Server error: ' + text);
                            }
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && data.success) {
                        // Show success toast
                        showToast('Issue updated successfully', 'success');
                        // Close the modal
                        closeEditIssueModal();
                        // Reload page after a short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else if (data && !data.success) {
                        // Show error toast
                        showToast('Error updating issue: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (error.message && error.message.startsWith('Server error:')) {
                        showToast(error.message, 'error');
                    } else if (error !== 'Redirected') {
                        showToast('An error occurred while updating the issue.', 'error');
                    }
                });
            } catch (error) {
                console.error('Error preparing data:', error);
                showToast('Error preparing data: ' + error.message, 'error');
            }
        });

        // Confirmation dialog functionality
        const confirmationDialog = document.getElementById('confirmation-dialog');
        const confirmationDialogClose = document.getElementById('confirmation-dialog-close');
        const confirmationDialogCancel = document.getElementById('confirmation-dialog-cancel');
        const confirmationDialogConfirm = document.getElementById('confirmation-dialog-confirm');
        let issueIdToDelete = null;

        // Delete issue confirmation
        const deleteButtons = document.querySelectorAll('.delete-issue');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                issueIdToDelete = this.getAttribute('data-id');
                confirmationDialog.classList.remove('hidden');
            });
        });

        function closeConfirmationDialog() {
            confirmationDialog.classList.add('hidden');
            issueIdToDelete = null;
        }

        confirmationDialogClose.addEventListener('click', closeConfirmationDialog);
        confirmationDialogCancel.addEventListener('click', closeConfirmationDialog);
        confirmationDialog.addEventListener('click', function(e) {
            if (e.target === confirmationDialog) {
                closeConfirmationDialog();
            }
        }, { passive: true });

        confirmationDialogConfirm.addEventListener('click', function() {
            if (issueIdToDelete) {
                // Ensure the ID is properly formatted and URL is correct
                const cleanId = issueIdToDelete.trim();
                console.log("Sending request to delete issue with ID:", cleanId);

                // Send delete request
                fetch(`/it-admin/issues/${cleanId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            try {
                                // Try to parse as JSON
                                return JSON.parse(text);
                            } catch (e) {
                                // If not JSON, throw the text
                                throw new Error('Server error: ' + text);
                            }
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Show success toast
                        showToast('Issue deleted successfully', 'success');
                        // Reload page after a short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        // Show error toast
                        showToast('Error deleting issue: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (error.message && error.message.startsWith('Server error:')) {
                        showToast(error.message, 'error');
                    } else {
                        showToast('An error occurred while deleting the issue.', 'error');
                    }
                });
            }
            closeConfirmationDialog();
        });

        // Export to CSV functionality
        const exportCSVButton = document.getElementById('exportCSV');
        if (exportCSVButton) {
            exportCSVButton.addEventListener('click', function() {
                exportTableToCSV('issues-table', 'it_issues_export.csv');
            }, { passive: true });
        }

        // Export to PDF functionality
        const exportPDFButton = document.getElementById('exportPDF');
        if (exportPDFButton) {
            exportPDFButton.addEventListener('click', function() {
                // First create a printable version of the table
                const table = document.querySelector('table');
                const printWindow = window.open('', '_blank');

                printWindow.document.write(`
                    <html>
                    <head>
                        <title>IT Issues Export</title>
                        <style>
                            body { font-family: Arial, sans-serif; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #2c5530; color: white; }
                            tr:nth-child(even) { background-color: #f2f2f2; }
                            h1 { color: #2c5530; }
                            .status-open { color: #1e40af; font-weight: bold; }
                            .status-in-progress { color: #b45309; font-weight: bold; }
                            .status-resolved { color: #047857; font-weight: bold; }
                            .status-closed { color: #4b5563; font-weight: bold; }
                        </style>
                    </head>
                    <body>
                        <h1>IT Issues Report</h1>
                        <p>Generated on: ${new Date().toLocaleString()}</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Item</th>
                                    <th>Issue Type</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Reported By</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                `);

                // Add table rows
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    if (row.cells.length > 1) { // Skip empty rows
                        const id = row.cells[0].textContent;
                        const item = row.cells[1].textContent.trim();

                        // Get issue type
                        const typeCell = row.cells[2];
                        const typeSpan = typeCell.querySelector('span');
                        const type = typeSpan ? typeSpan.textContent : '';

                        const description = row.cells[3].textContent.trim();

                        // Get status with class
                        const statusCell = row.cells[4];
                        const statusSpan = statusCell.querySelector('span');
                        const status = statusSpan ? statusSpan.textContent : '';
                        const statusClass = status.toLowerCase().replace(' ', '-');

                        const reportedBy = row.cells[5].textContent;
                        const date = row.cells[6].textContent;

                        printWindow.document.write(`
                            <tr>
                                <td>${id}</td>
                                <td>${item}</td>
                                <td>${type}</td>
                                <td>${description}</td>
                                <td class="status-${statusClass}">${status}</td>
                                <td>${reportedBy}</td>
                                <td>${date}</td>
                            </tr>
                        `);
                    }
                });

                printWindow.document.write(`
                            </tbody>
                        </table>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.focus();

                // Print after a short delay to ensure styles are loaded
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            });
        }

        // Function to export table to CSV
        function exportTableToCSV(tableId, filename) {
            const table = document.querySelector('table');
            if (!table) return;

            let csv = [];

            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => {
                headers.push('"' + cell.textContent.trim() + '"');
            });
            csv.push(headers.join(','));

            // Get rows
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (row.cells.length > 1) { // Skip empty rows
                    const rowData = [];
                    // Skip the last column (Actions)
                    for (let i = 0; i < row.cells.length - 1; i++) {
                        const cell = row.cells[i];
                        // For status and issue type, get the text from the span
                        if (i === 2 || i === 4) {
                            const span = cell.querySelector('span');
                            rowData.push('"' + (span ? span.textContent.trim() : '') + '"');
                        } else {
                            rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
                        }
                    }
                    csv.push(rowData.join(','));
                }
            });

            // Create CSV content
            const csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', filename);
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);
        }

        // Toast notification function
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'} transition-opacity duration-500`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 3000);
        }
    });
</script>