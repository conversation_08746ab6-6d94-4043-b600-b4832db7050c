<h1 class="text-2xl font-bold mb-6">Issue #<%= issue.id %></h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Issue Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/issues" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back to Issues
            </a>
            <a href="/it-admin/issues/<%= issue.id %>/edit" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-edit mr-2"></i> Edit Issue
            </a>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Title:</span>
                        <span class="w-2/3 font-medium"><%= issue.title %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Item:</span>
                        <span class="w-2/3 font-medium"><%= issue.item_name || 'N/A' %></span>
                    </div>
                    <% if (issue.serial_number) { %>
                        <div class="flex">
                            <span class="w-1/3 text-gray-600">Serial Number:</span>
                            <span class="w-2/3 font-medium"><%= issue.serial_number %></span>
                        </div>
                    <% } %>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Issue Type:</span>
                        <span class="w-2/3">
                            <% if (issue.issue_type === 'hardware') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Hardware</span>
                            <% } else if (issue.issue_type === 'software') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Software</span>
                            <% } else if (issue.issue_type === 'network') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Network</span>
                            <% } else { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Other</span>
                            <% } %>
                        </span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Priority:</span>
                        <span class="w-2/3">
                            <% if (issue.priority === 'high') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">High</span>
                            <% } else if (issue.priority === 'medium') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                            <% } else if (issue.priority === 'low') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Low</span>
                            <% } %>
                        </span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Status:</span>
                        <span class="w-2/3">
                            <% if (issue.status === 'open') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Open</span>
                            <% } else if (issue.status === 'in_progress') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                            <% } else if (issue.status === 'resolved') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                            <% } else if (issue.status === 'closed') { %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>
                            <% } %>
                        </span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-4">Additional Information</h3>
                <div class="space-y-3">
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Reported By:</span>
                        <span class="w-2/3 font-medium"><%= issue.reported_by_name || 'N/A' %></span>
                    </div>
                    <div class="flex">
                        <span class="w-1/3 text-gray-600">Reported On:</span>
                        <span class="w-2/3 font-medium"><%= formatDateTime(issue.created_at) %></span>
                    </div>
                    <% if (issue.updated_at) { %>
                        <div class="flex">
                            <span class="w-1/3 text-gray-600">Last Updated:</span>
                            <span class="w-2/3 font-medium"><%= formatDateTime(issue.updated_at) %></span>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <h3 class="text-lg font-semibold mb-2">Description</h3>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 whitespace-pre-wrap"><%= issue.description %></div>
        </div>

        <% if (issue.resolution) { %>
            <div class="mt-6">
                <h3 class="text-lg font-semibold mb-2">Resolution</h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 whitespace-pre-wrap"><%= issue.resolution %></div>
            </div>
        <% } %>

        <!-- Attachments Section -->
        <div class="mt-6" id="attachments-section">
            <h3 class="text-lg font-semibold mb-2">Attachments</h3>
            <div id="attachments-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div class="text-center p-4 text-gray-500">Loading attachments...</div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch attachments for this issue
        const issueId = '<%= issue.issue_id %>';
        fetchAttachments(issueId);

        // Function to fetch attachments
        function fetchAttachments(issueId) {
            fetch(`/it-admin/issues/${issueId}/attachments`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayAttachments(data.attachments);
                    } else {
                        showAttachmentError('Error loading attachments: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching attachments:', error);
                    showAttachmentError('Error loading attachments. Please try again later.');
                });
        }

        // Function to display attachments
        function displayAttachments(attachments) {
            const container = document.getElementById('attachments-container');
            container.innerHTML = '';

            if (attachments.length === 0) {
                container.innerHTML = '<div class="text-center p-4 text-gray-500 col-span-full">No attachments found</div>';
                return;
            }

            attachments.forEach(attachment => {
                const isImage = attachment.file_type.startsWith('image/');

                const attachmentEl = document.createElement('div');
                attachmentEl.className = 'border rounded-lg overflow-hidden bg-white shadow-sm';

                let previewHtml = '';
                if (isImage) {
                    previewHtml = `
                        <a href="${attachment.file_path}" target="_blank" class="block">
                            <div class="h-32 bg-gray-100 flex items-center justify-center overflow-hidden">
                                <img src="${attachment.file_path}" alt="${attachment.file_name}" class="w-full h-full object-cover">
                            </div>
                        </a>
                    `;
                } else {
                    previewHtml = `
                        <a href="${attachment.file_path}" target="_blank" class="block">
                            <div class="h-32 bg-gray-100 flex items-center justify-center">
                                <i class="fas fa-file-alt text-4xl text-gray-400"></i>
                            </div>
                        </a>
                    `;
                }

                const fileSize = formatFileSize(attachment.file_size);
                const uploadDate = new Date(attachment.uploaded_at).toLocaleString();

                attachmentEl.innerHTML = `
                    ${previewHtml}
                    <div class="p-3">
                        <div class="text-sm font-medium text-gray-900 truncate" title="${attachment.file_name}">
                            ${attachment.file_name}
                        </div>
                        <div class="text-xs text-gray-500">
                            ${fileSize} • ${uploadDate}
                        </div>
                    </div>
                `;

                container.appendChild(attachmentEl);
            });
        }

        // Function to show attachment error
        function showAttachmentError(message) {
            const container = document.getElementById('attachments-container');
            container.innerHTML = `
                <div class="text-center p-4 text-red-500 col-span-full">
                    <p>${message}</p>
                    <button id="retry-attachments" class="mt-2 px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">
                        Retry Loading Attachments
                    </button>
                </div>
            `;

            // Add retry button functionality
            document.getElementById('retry-attachments').addEventListener('click', function() {
                container.innerHTML = '<div class="text-center p-4 text-gray-500">Loading attachments...</div>';
                fetchAttachments(issueId);
            });
        }

        // Function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    });
</script>
