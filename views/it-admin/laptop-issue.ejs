<h1 class="text-2xl font-bold mb-6">Issue Laptop</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Issue Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/laptop-transactions" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>
    <div class="p-6">
        <form action="/it-admin/laptop-transactions/issue" method="POST" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">Laptop *</label>
                    <select id="item_id" name="item_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select Laptop</option>
                        <% items.forEach(item => { %>
                            <option value="<%= item.item_id %>" <%= query && query.item == item.item_id ? 'selected' : '' %>>
                                <%= item.name %> (<%= item.serial_number || 'No S/N' %><%= item.model ? ', ' + item.model : '' %>)
                            </option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="issued_to" class="block text-sm font-medium text-gray-700 mb-1">Issue To *</label>
                    <select id="issued_to" name="issued_to" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select User</option>
                        <% users.forEach(user => { %>
                            <option value="<%= user.id %>"><%= user.username %> (<%= user.name %>)</option>
                        <% }); %>
                    </select>
                </div>
                <div>
                    <label for="expected_return_date" class="block text-sm font-medium text-gray-700 mb-1">Expected Return Date</label>
                    <input type="date" id="expected_return_date" name="expected_return_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="condition_on_issue" class="block text-sm font-medium text-gray-700 mb-1">Condition on Issue</label>
                    <select id="condition_on_issue" name="condition_on_issue" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="excellent">Excellent - Like new</option>
                        <option value="good" selected>Good - Minor wear</option>
                        <option value="fair">Fair - Visible wear but functional</option>
                        <option value="poor">Poor - Significant wear, may have issues</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <a href="/it-admin/laptop-transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Issue Laptop</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for select dropdowns
        $('#item_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Laptop'
        });
        
        $('#issued_to').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select User'
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const itemInput = document.getElementById('item_id');
            const userInput = document.getElementById('issued_to');

            if (!itemInput.value) {
                e.preventDefault();
                alert('Please select a laptop');
                return;
            }

            if (!userInput.value) {
                e.preventDefault();
                alert('Please select a user');
                return;
            }
        });

        // Set minimum date for expected return date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('expected_return_date').min = today;
    });
</script>
