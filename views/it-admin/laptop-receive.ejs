<h1 class="text-2xl font-bold mb-6">Receive Laptop</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Receive Details</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/laptop-transactions" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </a>
        </div>
    </div>
    <div class="p-6">
        <% if (transactions && transactions.length > 0) { %>
            <form action="/it-admin/laptop-transactions/receive" method="POST" class="space-y-6">
                <div>
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-1">Select Laptop to Receive *</label>
                    <select id="transaction_id" name="transaction_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        <option value="">Select Laptop</option>
                        <% transactions.forEach(transaction => { %>
                            <option value="<%= transaction.transaction_id %>" <%= selectedTransactionId && selectedTransactionId == transaction.transaction_id ? 'selected' : '' %>>
                                <%= transaction.item_name %> - Issued to <%= transaction.issued_to_name %> on <%= transaction.issued_date %>
                                <% if (transaction.expected_return_date && transaction.expected_return_date !== 'Not specified') { %>
                                    (Expected return: <%= transaction.expected_return_date %>)
                                <% } %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div id="itemDetails" class="hidden border border-gray-200 rounded-md p-4 mb-4 bg-gray-50">
                    <h4 class="font-medium text-gray-700 mb-2">Laptop Details</h4>
                    <div id="selectedItemDetails" class="text-sm text-gray-600">
                        <!-- Details will be populated by JavaScript -->
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="condition_on_return" class="block text-sm font-medium text-gray-700 mb-1">Overall Condition on Return *</label>
                        <select id="condition_on_return" name="condition_on_return" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="excellent">Excellent - Like new</option>
                            <option value="good" selected>Good - Minor wear</option>
                            <option value="fair">Fair - Visible wear but functional</option>
                            <option value="poor">Poor - Significant wear, may have issues</option>
                            <option value="damaged">Damaged - Requires repair</option>
                            <option value="non-functional">Non-functional - Not working</option>
                        </select>
                    </div>
                    <div>
                        <div class="flex items-center h-full mt-6">
                            <input type="checkbox" id="create_issue_ticket" name="create_issue_ticket" value="1" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                            <label for="create_issue_ticket" class="ml-2 block text-sm text-gray-700">Create an issue ticket for this laptop</label>
                        </div>
                    </div>
                </div>

                <div id="quick_condition_assessment" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-700 mb-4">Quick Condition Assessment</h3>
                    <p class="text-sm text-gray-500 mb-4">Select any issues with the laptop.</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="physical_damage" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Physical Damage</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="screen_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Screen Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="keyboard_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Keyboard Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="battery_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Battery Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="connectivity_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Connectivity Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="software_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Software Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="port_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Port Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="audio_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Audio Issues</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="quick_condition[]" value="other_issues" class="h-4 w-4 text-it-admin-primary focus:ring-it-admin-primary border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">Other Issues</span>
                            </label>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="quick_condition_notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Details</label>
                        <textarea id="quick_condition_notes" name="quick_condition_notes" rows="2" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Provide more details about any issues selected above"></textarea>
                    </div>
                </div>

                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <a href="/it-admin/laptop-transactions" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">Cancel</a>
                    <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">Receive Laptop</button>
                </div>
            </form>
        <% } else { %>
            <div class="text-center py-8">
                <i class="fas fa-laptop text-gray-400 text-5xl mb-4"></i>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No outstanding laptops</h3>
                <p class="mt-1 text-sm text-gray-500">There are no laptops currently issued that need to be received.</p>
                <div class="mt-6">
                    <a href="/it-admin/laptop-transactions" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-it-admin-primary hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-it-admin-primary">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Laptop Transactions
                    </a>
                </div>
            </div>
        <% } %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize chosen for transaction dropdown
        $('#transaction_id').chosen({
            width: '100%',
            search_contains: true,
            placeholder_text_single: 'Select Laptop'
        });

        // Trigger change event if a transaction is pre-selected
        const transactionSelect = document.getElementById('transaction_id');
        if (transactionSelect && transactionSelect.value) {
            // Trigger the change event after a short delay to allow Chosen to initialize
            setTimeout(() => {
                $(transactionSelect).trigger('change');
                // Also update the Chosen UI to reflect the selection
                $(transactionSelect).trigger('chosen:updated');
            }, 100);
        }

        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const transactionInput = document.getElementById('transaction_id');

                if (!transactionInput.value) {
                    e.preventDefault();
                    alert('Please select a laptop to receive');
                    return;
                }

                // Check if issue ticket should be created
                const createIssueTicket = document.getElementById('create_issue_ticket');
                if (createIssueTicket && createIssueTicket.checked) {
                    // Ensure at least one condition issue is selected
                    const conditionCheckboxes = document.querySelectorAll('input[name="quick_condition[]"]');
                    let hasIssue = false;

                    conditionCheckboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            hasIssue = true;
                        }
                    });

                    if (!hasIssue) {
                        e.preventDefault();
                        alert('Please select at least one issue to create an issue ticket');
                        return;
                    }

                    // Ensure notes are provided
                    const quickConditionNotes = document.getElementById('quick_condition_notes');
                    if (!quickConditionNotes.value.trim()) {
                        e.preventDefault();
                        alert('Please provide details about the issues in the Additional Details field');
                        quickConditionNotes.focus();
                        return;
                    }
                }
            });
        }

        // Show item details when selected
        const transactionSelect = document.getElementById('transaction_id');
        if (transactionSelect) {
            transactionSelect.addEventListener('change', function() {
                const itemDetails = document.getElementById('itemDetails');
                const selectedItemDetails = document.getElementById('selectedItemDetails');

                if (this.value) {
                    // Get the selected option text
                    const selectedOption = Array.from(this.options).find(option => option.value === this.value);
                    if (selectedOption) {
                        selectedItemDetails.textContent = selectedOption.text;
                        itemDetails.classList.remove('hidden');
                    }
                } else {
                    itemDetails.classList.add('hidden');
                }
            });
        }

        // Toggle issue ticket creation based on condition
        const conditionSelect = document.getElementById('condition_on_return');
        const createIssueTicket = document.getElementById('create_issue_ticket');
        const quickConditionCheckboxes = document.querySelectorAll('input[name="quick_condition[]"]');

        if (conditionSelect && createIssueTicket) {
            conditionSelect.addEventListener('change', function() {
                if (this.value === 'poor' || this.value === 'damaged' || this.value === 'non-functional') {
                    createIssueTicket.checked = true;
                }
            });
        }

        // Auto-check create issue ticket if any condition issue is selected
        if (quickConditionCheckboxes.length > 0 && createIssueTicket) {
            quickConditionCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    let anyChecked = false;
                    quickConditionCheckboxes.forEach(cb => {
                        if (cb.checked) {
                            anyChecked = true;
                        }
                    });

                    if (anyChecked) {
                        createIssueTicket.checked = true;
                    }
                });
            });
        }
    });
</script>
