<h1 class="text-2xl font-bold mb-6">Laptop Transactions</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Laptop Management</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/laptop-transactions/issue" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-share mr-2"></i> Issue Laptop
            </a>
            <a href="/it-admin/laptop-transactions/receive" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-undo mr-2"></i> Receive Laptop
            </a>
            <button id="exportCSV" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-file-csv mr-2"></i> Export CSV
            </button>
            <button id="exportPDF" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-file-pdf mr-2"></i> Export PDF
            </button>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table id="transactions-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-it-admin-primary">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Laptop</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Serial Number</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued To</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued By</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Expected Return</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% if (transactions && transactions.length > 0) { %>
                    <% transactions.forEach(transaction => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= transaction.item_name %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.serial_number || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_to_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_by_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_date %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.expected_return_date || 'Not specified' %></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (transaction.received_date) { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Returned
                                    </span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Issued
                                    </span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <% if (!transaction.received_date) { %>
                                        <a href="/it-admin/laptop-transactions/receive?transaction=<%= transaction.transaction_id %>" class="text-it-admin-primary hover:text-it-admin-secondary" title="Receive">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                        <a href="/it-admin/inventory/transactions/<%= transaction.transaction_id %>/loan-voucher" class="text-blue-600 hover:text-blue-900" title="Print Voucher">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    <% } %>
                                    <a href="/it-admin/inventory/items/<%= transaction.item_id %>/issue-history" class="text-indigo-600 hover:text-indigo-900" title="View History">
                                        <i class="fas fa-history"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="8" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No laptop transactions found.
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Export to CSV functionality
        const exportCSVButton = document.getElementById('exportCSV');
        if (exportCSVButton) {
            exportCSVButton.addEventListener('click', function() {
                exportTableToCSV('transactions-table', 'laptop_transactions.csv');
            });
        }
        
        // Export to PDF functionality
        const exportPDFButton = document.getElementById('exportPDF');
        if (exportPDFButton) {
            exportPDFButton.addEventListener('click', function() {
                // First create a printable version of the table
                const table = document.querySelector('table');
                const printWindow = window.open('', '_blank');
                
                printWindow.document.write(`
                    <html>
                    <head>
                        <title>Laptop Transactions Export</title>
                        <style>
                            body { font-family: Arial, sans-serif; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #2c5530; color: white; }
                            tr:nth-child(even) { background-color: #f2f2f2; }
                            h1 { color: #2c5530; }
                            .status-issued { color: #1e40af; font-weight: bold; }
                            .status-returned { color: #047857; font-weight: bold; }
                        </style>
                    </head>
                    <body>
                        <h1>Laptop Transactions Report</h1>
                        <p>Generated on: ${new Date().toLocaleString()}</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>Laptop</th>
                                    <th>Serial Number</th>
                                    <th>Issued To</th>
                                    <th>Issued By</th>
                                    <th>Issued Date</th>
                                    <th>Expected Return</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                `);
                
                // Add table rows
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    if (row.cells.length > 1) { // Skip empty rows
                        const laptop = row.cells[0].textContent.trim();
                        const serialNumber = row.cells[1].textContent.trim();
                        const issuedTo = row.cells[2].textContent.trim();
                        const issuedBy = row.cells[3].textContent.trim();
                        const issuedDate = row.cells[4].textContent.trim();
                        const expectedReturn = row.cells[5].textContent.trim();
                        
                        // Get status with class
                        const statusCell = row.cells[6];
                        const statusSpan = statusCell.querySelector('span');
                        const status = statusSpan ? statusSpan.textContent.trim() : '';
                        const statusClass = status.toLowerCase();
                        
                        printWindow.document.write(`
                            <tr>
                                <td>${laptop}</td>
                                <td>${serialNumber}</td>
                                <td>${issuedTo}</td>
                                <td>${issuedBy}</td>
                                <td>${issuedDate}</td>
                                <td>${expectedReturn}</td>
                                <td class="status-${statusClass}">${status}</td>
                            </tr>
                        `);
                    }
                });
                
                printWindow.document.write(`
                            </tbody>
                        </table>
                    </body>
                    </html>
                `);
                
                printWindow.document.close();
                printWindow.focus();
                
                // Print after a short delay to ensure styles are loaded
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            });
        }
        
        // Function to export table to CSV
        function exportTableToCSV(tableId, filename) {
            const table = document.querySelector('table');
            if (!table) return;
            
            let csv = [];
            
            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => {
                headers.push('"' + cell.textContent.trim() + '"');
            });
            csv.push(headers.join(','));
            
            // Get rows
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (row.cells.length > 1) { // Skip empty rows
                    const rowData = [];
                    // Skip the last column (Actions)
                    for (let i = 0; i < row.cells.length - 1; i++) {
                        const cell = row.cells[i];
                        // For status, get the text from the span
                        if (i === 6) {
                            const span = cell.querySelector('span');
                            rowData.push('"' + (span ? span.textContent.trim() : '') + '"');
                        } else {
                            rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
                        }
                    }
                    csv.push(rowData.join(','));
                }
            });
            
            // Create CSV content
            const csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');
            
            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            
            // Trigger download
            link.click();
            
            // Clean up
            document.body.removeChild(link);
        }
    });
</script>
