

<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">Draft Procurement Requests</h1>
                <a href="/it-admin/procurement/new" class="px-4 py-2 bg-white text-it-admin-primary rounded-md hover:bg-gray-100 transition">
                    <i class="fas fa-plus mr-2"></i> New Procurement
                </a>
            </div>
        </div>

        <div class="p-6">
            <!-- Procurement List -->
            <div class="bg-white rounded-lg border overflow-hidden">
                <% if (procurements.length === 0) { %>
                    <div class="p-6 text-center text-gray-500">
                        <i class="fas fa-file-alt text-4xl mb-2"></i>
                        <p>No draft procurement requests found</p>
                        <a href="/it-admin/procurement/new" class="mt-2 inline-block px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition">Create New Procurement</a>
                    </div>
                <% } else { %>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Step</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <% procurements.forEach(procurement => { %>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PRO-<%= procurement.request_id.toString().padStart(4, '0') %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= procurement.title %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(procurement.request_date) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(procurement.updated_at) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <%
                                                let stepName = '';
                                                switch(procurement.current_step) {
                                                    case 1: stepName = 'Request Letter'; break;
                                                    case 2: stepName = 'Quotations'; break;
                                                    case 3: stepName = 'Comparative'; break;
                                                    case 4: stepName = 'Committee'; break;
                                                    case 5: stepName = 'Payment'; break;
                                                    default: stepName = 'Unknown';
                                                }
                                            %>
                                            <%= stepName %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="/it-admin/procurement/new?draft_id=<%= procurement.request_id %>" class="text-it-admin-primary hover:text-it-admin-secondary mr-3">Continue</a>
                                            <button
                                                onclick="confirmDelete(<%= procurement.request_id %>)"
                                                class="text-red-600 hover:text-red-900">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-700 mb-6">Are you sure you want to delete this draft procurement request? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</button>
            <form id="deleteForm" method="POST">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Delete confirmation modal
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelButton = document.getElementById('cancelDelete');

        // Set form action
        deleteForm.action = `/it-admin/procurement/${id}/delete`;

        // Show modal
        modal.classList.remove('hidden');

        // Handle cancel button
        cancelButton.onclick = function() {
            modal.classList.add('hidden');
        };

        // Close modal when clicking outside
        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        };
    }
</script>


