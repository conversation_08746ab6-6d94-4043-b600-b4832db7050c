

<div class="container mx-auto px-4 py-6">
    <!-- Procurement Dashboard -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <h1 class="text-2xl font-bold">School Procurement Management</h1>
            <p class="mt-2">Manage and track all school procurement processes</p>
        </div>

        <div class="p-6">
            <!-- Quick Actions -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="/it-admin/procurement/new" class="bg-it-admin-primary text-white p-4 rounded-lg shadow hover:bg-it-admin-secondary transition flex items-center">
                        <i class="fas fa-plus-circle text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-semibold">New Procurement</h3>
                            <p class="text-sm opacity-80">Start a new procurement process</p>
                        </div>
                    </a>
                    <a href="/it-admin/procurement/drafts" class="bg-amber-600 text-white p-4 rounded-lg shadow hover:bg-amber-700 transition flex items-center">
                        <i class="fas fa-save text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-semibold">Saved Drafts</h3>
                            <p class="text-sm opacity-80">Continue from where you left off</p>
                        </div>
                    </a>
                    <a href="/it-admin/procurement/reports" class="bg-indigo-600 text-white p-4 rounded-lg shadow hover:bg-indigo-700 transition flex items-center">
                        <i class="fas fa-chart-bar text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-semibold">Reports</h3>
                            <p class="text-sm opacity-80">View procurement analytics</p>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Procurement Statistics -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Procurement Statistics</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                                <i class="fas fa-file-alt text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Total Procurements</p>
                                <p class="text-xl font-semibold"><%= stats.total_procurements || 0 %></p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-amber-100 text-amber-600 mr-4">
                                <i class="fas fa-clock text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">In Progress</p>
                                <p class="text-xl font-semibold"><%= stats.in_progress_procurements || 0 %></p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Completed</p>
                                <p class="text-xl font-semibold"><%= stats.completed_procurements || 0 %></p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                                <i class="fas fa-rupee-sign text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Total Amount</p>
                                <p class="text-xl font-semibold"><%= formatCurrency(stats.total_amount_spent || 0) %></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Procurements -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Recent Procurements</h2>
                    <a href="/it-admin/procurement/all" class="text-it-admin-primary hover:underline">View All</a>
                </div>

                <div class="bg-white rounded-lg border overflow-hidden">
                    <% if (procurements.length === 0) { %>
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-file-alt text-4xl mb-2"></i>
                            <p>No procurement requests found</p>
                            <a href="/it-admin/procurement/new" class="mt-2 inline-block px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition">Create New Procurement</a>
                        </div>
                    <% } else { %>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <% procurements.forEach(procurement => { %>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PRO-<%= procurement.request_id.toString().padStart(4, '0') %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= procurement.title %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(procurement.request_date) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= formatCurrency(procurement.total_amount || 0) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <% if (procurement.status === 'draft') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Draft</span>
                                            <% } else if (procurement.status === 'in_progress') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-amber-100 text-amber-800">In Progress</span>
                                            <% } else if (procurement.status === 'approved') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Approved</span>
                                            <% } else if (procurement.status === 'completed') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                            <% } else if (procurement.status === 'rejected') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="/it-admin/procurement/<%= procurement.request_id %>" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                                            <% if (procurement.status === 'draft') { %>
                                                <a href="/it-admin/procurement/<%= procurement.request_id %>" class="text-it-admin-primary hover:text-it-admin-secondary mr-3">Edit</a>
                                                <button
                                                    onclick="confirmDelete(<%= procurement.request_id %>)"
                                                    class="text-red-600 hover:text-red-900">
                                                    Delete
                                                </button>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-700 mb-6">Are you sure you want to delete this procurement request? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</button>
            <form id="deleteForm" method="POST">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Delete confirmation modal
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelButton = document.getElementById('cancelDelete');

        // Set form action
        deleteForm.action = `/it-admin/procurement/${id}/delete`;

        // Show modal
        modal.classList.remove('hidden');

        // Handle cancel button
        cancelButton.onclick = function() {
            modal.classList.add('hidden');
        };

        // Close modal when clicking outside
        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        };
    }
</script>

<%- include('../../partials/footer') %>
