<%- include('../../partials/header') %>

<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">All Procurement Requests</h1>
                <a href="/it-admin/procurement/new" class="px-4 py-2 bg-white text-it-admin-primary rounded-md hover:bg-gray-100 transition">
                    <i class="fas fa-plus mr-2"></i> New Procurement
                </a>
            </div>
        </div>                                                                                                                                                                                                                       

        <div class="p-6">
            <!-- Filters -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Filters</h2>
                <form action="/it-admin/procurement/all" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            <option value="">All Statuses</option>
                            <option value="draft" <%= filters.status === 'draft' ? 'selected' : '' %>>Draft</option>
                            <option value="in_progress" <%= filters.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                            <option value="approved" <%= filters.status === 'approved' ? 'selected' : '' %>>Approved</option>
                            <option value="completed" <%= filters.status === 'completed' ? 'selected' : '' %>>Completed</option>
                            <option value="rejected" <%= filters.status === 'rejected' ? 'selected' : '' %>>Rejected</option>
                        </select>
                    </div>
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" id="search" name="search" value="<%= filters.search %>" placeholder="Search by title or description" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="dateFrom" class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                        <input type="date" id="dateFrom" name="dateFrom" value="<%= filters.dateFrom %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div>
                        <label for="dateTo" class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                        <input type="date" id="dateTo" name="dateTo" value="<%= filters.dateTo %>" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                    <div class="md:col-span-4 flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                        <a href="/it-admin/procurement/all" class="ml-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
                            <i class="fas fa-times mr-2"></i> Clear Filters
                        </a>
                    </div>
                </form>
            </div>

            <!-- Procurement List -->
            <div class="bg-white rounded-lg border overflow-hidden">
                <% if (procurements.length === 0) { %>
                    <div class="p-6 text-center text-gray-500">
                        <i class="fas fa-file-alt text-4xl mb-2"></i>
                        <p>No procurement requests found</p>
                        <a href="/it-admin/procurement/new" class="mt-2 inline-block px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary transition">Create New Procurement</a>
                    </div>
                <% } else { %>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Step</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <% procurements.forEach(procurement => { %>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PRO-<%= procurement.request_id.toString().padStart(4, '0') %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= procurement.title %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(procurement.request_date) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= formatCurrency(procurement.total_amount || 0) %></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <% if (procurement.status === 'draft') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Draft</span>
                                            <% } else if (procurement.status === 'in_progress') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-amber-100 text-amber-800">In Progress</span>
                                            <% } else if (procurement.status === 'approved') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Approved</span>
                                            <% } else if (procurement.status === 'completed') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                            <% } else if (procurement.status === 'rejected') { %>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                                            <% } %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <% 
                                                let stepName = '';
                                                switch(procurement.current_step) {
                                                    case 1: stepName = 'Request Letter'; break;
                                                    case 2: stepName = 'Quotations'; break;
                                                    case 3: stepName = 'Comparative'; break;
                                                    case 4: stepName = 'Committee'; break;
                                                    case 5: stepName = 'Payment'; break;
                                                    default: stepName = 'Unknown';
                                                }
                                            %>
                                            <%= stepName %>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="/it-admin/procurement/<%= procurement.request_id %>" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                                            <% if (procurement.status === 'draft') { %>
                                                <a href="/it-admin/procurement/<%= procurement.request_id %>" class="text-it-admin-primary hover:text-it-admin-secondary mr-3">Edit</a>
                                                <button 
                                                    onclick="confirmDelete(<%= procurement.request_id %>)" 
                                                    class="text-red-600 hover:text-red-900">
                                                    Delete
                                                </button>
                                            <% } %>
                                            <a href="/it-admin/procurement/<%= procurement.request_id %>/pdf" class="text-gray-600 hover:text-gray-900 ml-3">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
        <p class="text-gray-700 mb-6">Are you sure you want to delete this procurement request? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</button>
            <form id="deleteForm" method="POST">
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </form>
        </div>
    </div>
</div>

<script>
    // Delete confirmation modal
    function confirmDelete(id) {
        const modal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelButton = document.getElementById('cancelDelete');
        
        // Set form action
        deleteForm.action = `/it-admin/procurement/${id}/delete`;
        
        // Show modal
        modal.classList.remove('hidden');
        
        // Handle cancel button
        cancelButton.onclick = function() {
            modal.classList.add('hidden');
        };
        
        // Close modal when clicking outside
        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        };
    }
</script>

<%- include('../../partials/footer') %>
