<script>
/**
 * Procurement System - Main JavaScript
 * This file contains the core functionality for the procurement system.
 */

// ===== GLOBAL VARIABLES =====
window.selectedItems = [];
window.selectedVendors = [];
window.itemsData = [];
window.quotationData = {};

// Local references for convenience
var selectedItems = window.selectedItems;
var selectedVendors = window.selectedVendors;
var itemsData = window.itemsData;

// ===== UTILITY FUNCTIONS =====

// Safe DOM utility to prevent null reference errors
window.safeDOM = {
    getElementById: function(id) {
        try {
            return document.getElementById(id);
        } catch (error) {
            console.error('Error getting element by ID:', id, error);
            return null;
        }
    },
    querySelector: function(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.error('Error querying selector:', selector, error);
            return null;
        }
    },
    querySelectorAll: function(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            console.error('Error querying all selector:', selector, error);
            return [];
        }
    },
    getValue: function(id) {
        try {
            const element = document.getElementById(id);
            return element ? element.value : '';
        } catch (error) {
            console.error('Error getting value for element:', id, error);
            return '';
        }
    },
    setValue: function(id, value) {
        try {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting value for element:', id, error);
            return false;
        }
    }
};

// For backward compatibility
window.safeGetElementById = window.safeDOM.getElementById;

// Global error handler
window.addEventListener('error', function(event) {
    console.error('Global error caught:', event.error || event.message);
    event.preventDefault();
    return true;
});

// ===== CORE FUNCTIONS =====

// Add item to the procurement request
window.addItem = function(item) {
    console.log('Adding item:', item);
    if (!item) return null;

    // Check if item already exists
    const existingIndex = window.selectedItems.findIndex(i => i && i.id === item.id);
    if (existingIndex >= 0) {
        // Update existing item
        window.selectedItems[existingIndex] = {
            ...window.selectedItems[existingIndex],
            ...item
        };
        return window.selectedItems[existingIndex];
    } else {
        // Add new item
        window.selectedItems.push(item);
        return item;
    }
};

// Add vendor to the procurement request
window.addVendor = function(vendor) {
    console.log('Adding vendor:', vendor);
    if (!vendor) return null;

    // Check if vendor already exists
    const existingIndex = window.selectedVendors.findIndex(v => v && v.id === vendor.id);
    if (existingIndex >= 0) {
        // Update existing vendor
        window.selectedVendors[existingIndex] = {
            ...window.selectedVendors[existingIndex],
            ...vendor
        };
        return window.selectedVendors[existingIndex];
    } else {
        // Add new vendor
        window.selectedVendors.push(vendor);
        return vendor;
    }
};

// Initialize with sample data if needed
function ensureInitialData() {
    console.log('Ensuring initial data is available...');

    // Add sample item if none exist
    if (window.selectedItems.length === 0) {
        window.addItem({
            id: 'initial_item_1',
            name: 'Sample Item',
            quantity: 1
        });
    }

    // Add sample vendor if none exist
    if (window.selectedVendors.length === 0) {
        window.addVendor({
            id: 'initial_vendor_1',
            name: 'Sample Vendor',
            address: 'Sample Address'
        });
    }
}

// Capture quotation prices for comparative analysis
window.captureQuotationPricesForComparative = function() {
    console.log('Capturing quotation prices for comparative analysis...');

    // Data structure to hold vendor, item, and price information
    const capturedData = {
        vendors: [...window.selectedVendors],
        items: [...window.selectedItems],
        prices: {}
    };

    // Initialize prices structure
    capturedData.items.forEach(item => {
        if (item && item.id) {
            capturedData.prices[item.id] = {};
        }
    });

    // Capture price data from quotationData
    if (window.quotationData) {
        for (const vendorId in window.quotationData) {
            if (window.quotationData[vendorId] && window.quotationData[vendorId].items) {
                for (const itemId in window.quotationData[vendorId].items) {
                    const priceData = window.quotationData[vendorId].items[itemId];
                    if (!priceData) continue;

                    if (!capturedData.prices[itemId]) {
                        capturedData.prices[itemId] = {};
                    }

                    capturedData.prices[itemId][vendorId] = {
                        unitPrice: priceData.unitPrice || 0,
                        totalPrice: priceData.totalPrice || 0
                    };
                }
            }
        }
    }

    // Store globally
    window.capturedQuotationData = capturedData;

    return capturedData;
};

// Get quotation data for comparative analysis
window.getQuotationDataForComparative = function() {
    return window.captureQuotationPricesForComparative();
};

// ===== INITIALIZATION =====

// Main initialization function
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded event fired');

    // Initialize data
    ensureInitialData();

    // Get DOM elements using safeDOM
    const stepIndicators = window.safeDOM.querySelectorAll('.step-indicator');
    const stepContents = window.safeDOM.querySelectorAll('.step-content');
    const prevBtn = window.safeDOM.getElementById('prev-step');
    const nextBtn = window.safeDOM.getElementById('next-step');
    const submitBtn = window.safeDOM.getElementById('submit-btn');
    const saveDraftBtn = window.safeDOM.getElementById('save-draft-btn');

    // Initialize current step
    let currentStep = 1;

    // Log initialization status
    if (!stepIndicators || stepIndicators.length === 0) {
        console.warn('No step indicators found');
    }

    if (!stepContents || stepContents.length === 0) {
        console.warn('No step contents found');
    }

    if (!prevBtn) {
        console.warn('Previous button not found');
    }

    if (!nextBtn) {
        console.warn('Next button not found');
    }

    // Set up step navigation
    if (stepIndicators && stepIndicators.length > 0) {
        stepIndicators.forEach((indicator, index) => {
            if (indicator) {
                indicator.addEventListener('click', function() {
                    try {
                        const step = index + 1;

                        // Only allow clicking on completed steps or the next available step
                        if (step <= currentStep) {
                            currentStep = step;
                            updateUI();

                            // Initialize step-specific functionality
                            if (step === 3 && window.initStep3) {
                                window.initStep3();
                            } else if (step === 4) {
                                // Capture quotation data for step 4
                                window.captureQuotationPricesForComparative();

                                // Update comparative table if available
                                if (typeof updateComparativeTable === 'function') {
                                    setTimeout(updateComparativeTable, 300);
                                }
                            }

                            window.scrollTo(0, 0);
                        }
                    } catch (error) {
                        console.error('Error in step indicator click handler:', error);
                    }
                });
            }
        });
    }

    // Set up navigation buttons
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (validateStep()) {
                // Special handling for step transitions
                if (currentStep === 3) {
                    // When moving from Step 3 to Step 4, capture the quotation data
                    window.captureQuotationPricesForComparative();
                }

                currentStep++;
                updateUI();
                window.scrollTo(0, 0);
            }
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            currentStep--;
            updateUI();
            window.scrollTo(0, 0);
        });
    }

    // Set up save draft button
    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', function() {
            saveDraft();
        });
    }

    // Set up submit button
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            if (validateStep()) {
                submitForm();
            }
        });
    }

    // Initialize UI
    updateUI();

    // Function to update the UI based on the current step
    function updateUI() {
        try {
            // Update step indicators
            if (stepIndicators && stepIndicators.length > 0) {
                stepIndicators.forEach((indicator, index) => {
                    if (!indicator) return;

                    const step = index + 1;

                    // Add or remove active class
                    if (step === currentStep) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }

                    // Add or remove completed class
                    if (step < currentStep) {
                        indicator.classList.add('completed');
                    } else {
                        indicator.classList.remove('completed');
                    }
                });
            }

            // Update step contents
            if (stepContents && stepContents.length > 0) {
                stepContents.forEach((content, index) => {
                    if (!content) return;

                    const step = index + 1;

                    // Show or hide step content
                    if (step === currentStep) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            }

            // Update navigation buttons
            if (prevBtn) {
                if (currentStep > 1) {
                    prevBtn.classList.remove('hidden');
                } else {
                    prevBtn.classList.add('hidden');
                }
            }

            if (nextBtn) {
                if (currentStep < (stepContents ? stepContents.length : 6)) {
                    nextBtn.classList.remove('hidden');
                    submitBtn.classList.add('hidden');
                } else {
                    nextBtn.classList.add('hidden');
                    submitBtn.classList.remove('hidden');
                }
            }

            // Update current step input
            const currentStepInput = window.safeDOM.getElementById('current_step');
            if (currentStepInput) {
                currentStepInput.value = currentStep;
            }

            // Initialize step-specific functionality
            if (currentStep === 3 && window.initStep3) {
                window.initStep3();
            }
        } catch (error) {
            console.error('Error updating UI:', error);
        }
    }

    // Function to validate the current step
    function validateStep() {
        try {
            // Get the current step content
            const currentStepContent = stepContents ? stepContents[currentStep - 1] : null;
            if (!currentStepContent) return true;

            // Get all required fields in the current step
            const requiredFields = currentStepContent.querySelectorAll('[required]');
            if (!requiredFields || requiredFields.length === 0) return true;

            // Check if all required fields are filled
            let isValid = true;
            requiredFields.forEach(field => {
                if (!field.value) {
                    isValid = false;
                    field.classList.add('border-red-500');

                    // Add error message if not already present
                    const errorMessage = field.nextElementSibling;
                    if (!errorMessage || !errorMessage.classList.contains('error-message')) {
                        const message = document.createElement('p');
                        message.className = 'error-message text-red-500 text-sm mt-1';
                        message.textContent = 'This field is required';
                        field.parentNode.insertBefore(message, field.nextSibling);
                    }
                } else {
                    field.classList.remove('border-red-500');

                    // Remove error message if present
                    const errorMessage = field.nextElementSibling;
                    if (errorMessage && errorMessage.classList.contains('error-message')) {
                        errorMessage.remove();
                    }
                }
            });

            return isValid;
        } catch (error) {
            console.error('Error validating step:', error);
            return true; // Allow progression on error
        }
    }

    // Function to save draft
    function saveDraft() {
        try {
            console.log('Saving draft...');

            // Create FormData object
            const form = window.safeDOM.getElementById('procurementForm');
            if (!form) {
                console.error('Form not found');
                return;
            }

            const formData = new FormData(form);

            // Add current step
            formData.append('current_step', currentStep);

            // Add JSON data for selected items and vendors
            const jsonData = {
                selected_items: window.selectedItems,
                selected_vendors: window.selectedVendors
            };

            formData.append('json_data', JSON.stringify(jsonData));

            // Show loading toast
            if (typeof showToast === 'function') {
                showToast('info', 'Saving draft...', 0);
            }

            // Send the form data
            fetch('/it-admin/procurement/save-draft', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success toast
                    if (typeof showToast === 'function') {
                        showToast('success', 'Draft saved successfully');
                    }

                    // Update request ID if provided
                    if (data.request_id) {
                        window.safeDOM.setValue('request_id', data.request_id);
                    }
                } else {
                    // Show error toast
                    if (typeof showToast === 'function') {
                        showToast('error', data.message || 'Error saving draft');
                    }
                }
            })
            .catch(error => {
                console.error('Error saving draft:', error);

                // Show error toast
                if (typeof showToast === 'function') {
                    showToast('error', 'Error saving draft: ' + error.message);
                }
            });
        } catch (error) {
            console.error('Error in saveDraft function:', error);
        }
    }

    // Function to submit the form
    function submitForm() {
        try {
            console.log('Submitting form...');

            // Save draft first
            saveDraft();

            // Get request ID
            const requestId = window.safeDOM.getValue('request_id');
            if (!requestId) {
                console.error('Request ID not found');
                return;
            }

            // Show loading toast
            if (typeof showToast === 'function') {
                showToast('info', 'Submitting request...', 0);
            }

            // Submit the form
            fetch(`/it-admin/procurement/${requestId}/submit`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success toast
                    if (typeof showToast === 'function') {
                        showToast('success', 'Request submitted successfully');
                    }

                    // Redirect to procurement dashboard
                    setTimeout(() => {
                        window.location.href = '/it-admin/procurement';
                    }, 1500);
                } else {
                    // Show error toast
                    if (typeof showToast === 'function') {
                        showToast('error', data.message || 'Error submitting request');
                    }
                }
            })
            .catch(error => {
                console.error('Error submitting form:', error);

                // Show error toast
                if (typeof showToast === 'function') {
                    showToast('error', 'Error submitting request: ' + error.message);
                }
            });
        } catch (error) {
            console.error('Error in submitForm function:', error);
        }
    }

    console.log('Procurement form initialized successfully');
});
</script>

<!-- Include Step 3 JavaScript -->
<script src="/js/procurement-step3.js"></script>

<!-- Toast notification functions -->
<script>
// Toast notification functions
function showToast(type, message, duration = 3000) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col items-end space-y-2';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    const toastId = 'toast-' + Date.now();
    toast.id = toastId;

    // Set toast type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            break;
        case 'error':
            bgColor = 'bg-red-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
            break;
        case 'warning':
            bgColor = 'bg-yellow-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>';
            break;
        case 'info':
        default:
            bgColor = 'bg-blue-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            break;
    }

    // Set toast content
    toast.className = `${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-lg flex items-center justify-between min-w-[300px] max-w-md transform transition-all duration-300 ease-in-out opacity-0 translate-x-4`;
    toast.innerHTML = `
        <div class="flex items-center">
            ${icon}
            <span>${message}</span>
        </div>
        <button type="button" class="ml-4 text-white hover:text-gray-200 focus:outline-none" onclick="hideToast('${toastId}')">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Show toast with animation
    setTimeout(() => {
        toast.classList.remove('opacity-0', 'translate-x-4');
        toast.classList.add('opacity-100', 'translate-x-0');
    }, 10);

    // Auto-hide toast after duration (if not 0)
    if (duration > 0) {
        setTimeout(() => {
            hideToast(toastId);
        }, duration);
    }

    return toastId;
}

function hideToast(toastId) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    // Hide toast with animation
    toast.classList.remove('opacity-100', 'translate-x-0');
    toast.classList.add('opacity-0', 'translate-x-4');

    // Remove toast after animation
    setTimeout(() => {
        toast.remove();
    }, 300);
}
</script>

<!-- HTML Content -->
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <h1 class="text-2xl font-bold">New Procurement Request</h1>
            <p class="mt-2">Create a new school procurement request</p>
        </div>

        <!-- Form Container -->
        <div class="flex justify-end mb-4 px-6 pt-6">
            <button type="button" id="topSaveBtn" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50">
                <i class="fas fa-save mr-2"></i> Save Draft
            </button>
        </div>

        <form id="procurementForm" class="p-6 pt-0" enctype="multipart/form-data">
            <input type="hidden" name="request_id" id="request_id" value="">
            <input type="hidden" name="current_step" id="current_step" value="1">

            <!-- Step content will be included here -->
            <!-- Each step should be wrapped in a div with class "form-step" -->

            <!-- Navigation -->
            <div class="flex justify-between mt-8">
                <button type="button" id="prev-step" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 hidden">
                    <i class="fas fa-arrow-left mr-2"></i> Previous
                </button>
                <div>
                    <button type="button" id="save-draft-btn" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 mr-2">
                        <i class="fas fa-save mr-2"></i> Save Draft
                    </button>
                    <button type="button" id="next-step" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                        Next <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                    <button type="button" id="submit-btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50 hidden">
                        <i class="fas fa-check mr-2"></i> Submit
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Toast container for notifications -->
<div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>
