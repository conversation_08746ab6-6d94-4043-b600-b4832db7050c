<!-- Include external JavaScript files -->
<script src="/js/procurement/main.js"></script>
<script src="/js/procurement/validation.js"></script>
<script src="/js/procurement/steps.js"></script>
<script src="/js/procurement/quotation.js"></script>
<script src="/js/procurement/vendor-modal.js"></script>
<!-- Fix script should be loaded last -->
<script src="/js/procurement/external-fix.js"></script>

<style>
/* Custom styles for the procurement system */
.vendor-overlay {
    border-top: 1px dashed #e5e7eb;
    padding-top: 4px;
    font-style: italic;
}

/* Highlight the lowest price */
td.bg-green-50 .vendor-overlay {
    color: #065f46;
    font-weight: 500;
}

/* Section styles */
.procurement-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: #fff;
}

.procurement-section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.procurement-section-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    margin-right: 0.75rem;
    border-radius: 9999px;
    background-color: #0f766e;
    color: white;
    font-weight: 600;
}

.procurement-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

/* Step indicator styles */
.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-indicator.active div:first-child {
    background-color: #0f766e;
    color: white;
}

.step-indicator.completed div:first-child {
    background-color: #10b981;
    color: white;
}

/* Progress bar animation */
#progress-bar {
    transition: width 0.3s ease-in-out;
}
</style>

<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <h1 class="text-2xl font-bold">New Procurement Request</h1>
            <p class="mt-2">Create a new school procurement request</p>
        </div>

        <!-- Form Container -->
        <div class="flex justify-end mb-4 px-6 pt-6">
            <button type="button" id="topSaveBtn" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50">
                <i class="fas fa-save mr-2"></i> Save Draft
            </button>
        </div>

        <!-- Step Progress Bar -->
        <div class="px-6 mb-8">
            <div class="relative">
                <!-- Progress Bar Background -->
                <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                    <div id="progress-bar" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-it-admin-primary" style="width: 0%"></div>
                </div>

                <!-- Step Indicators -->
                <div class="flex justify-between">
                    <div class="step-indicator active" data-step="1">
                        <div class="w-8 h-8 flex items-center justify-center bg-it-admin-primary text-white rounded-full">1</div>
                        <div class="text-xs mt-1">Request Info</div>
                    </div>
                    <div class="step-indicator" data-step="2">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-300 text-gray-700 rounded-full">2</div>
                        <div class="text-xs mt-1">Quotations</div>
                    </div>
                    <div class="step-indicator" data-step="3">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-300 text-gray-700 rounded-full">3</div>
                        <div class="text-xs mt-1">Upload</div>
                    </div>
                    <div class="step-indicator" data-step="4">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-300 text-gray-700 rounded-full">4</div>
                        <div class="text-xs mt-1">Comparison</div>
                    </div>
                    <div class="step-indicator" data-step="5">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-300 text-gray-700 rounded-full">5</div>
                        <div class="text-xs mt-1">Approval</div>
                    </div>
                    <div class="step-indicator" data-step="6">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-300 text-gray-700 rounded-full">6</div>
                        <div class="text-xs mt-1">Complete</div>
                    </div>
                </div>
            </div>
        </div>

        <form id="procurementForm" class="p-6 pt-0" enctype="multipart/form-data">
            <input type="hidden" name="request_id" id="request_id" value="<%= procurement.request_id || '' %>">
            <input type="hidden" name="current_step" id="current_step" value="1">

            <!-- Section 1: Request Letter -->
            <div class="form-step" id="step1">
                <div class="procurement-section-header">
                    <div class="procurement-section-number">1</div>
                    <div class="procurement-section-title">Request Letter Information</div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Request Title <span class="text-red-500">*</span></label>
                        <input type="text" id="title" name="title" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                        <p class="text-xs text-gray-500 mt-1">Enter a descriptive title for this procurement request</p>
                    </div>

                    <div>
                        <label for="request_date" class="block text-sm font-medium text-gray-700 mb-1">Request Date <span class="text-red-500">*</span></label>
                        <input type="date" id="request_date" name="request_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" value="<%= new Date().toISOString().split('T')[0] %>" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department <span class="text-red-500">*</span></label>
                        <select id="department" name="department" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                            <option value="">-- Select Department --</option>
                            <% if (typeof departments !== 'undefined' && departments.length > 0) { %>
                                <% departments.forEach(dept => { %>
                                    <option value="<%= dept.name %>"><%= dept.name %></option>
                                <% }); %>
                            <% } else { %>
                                <option value="PHYSICS">PHYSICS</option>
                                <option value="CHEMISTRY">CHEMISTRY</option>
                                <option value="BIOLOGY">BIOLOGY</option>
                                <option value="MATHEMATICS">MATHEMATICS</option>
                                <option value="ENGLISH">ENGLISH</option>
                                <option value="PUNJABI">PUNJABI</option>
                                <option value="COMPUTER SCIENCE">COMPUTER SCIENCE</option>
                            <% } %>
                        </select>
                    </div>

                    <div>
                        <label for="budget_code" class="block text-sm font-medium text-gray-700 mb-1">Budget Code</label>
                        <input type="text" id="budget_code" name="budget_code" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description <span class="text-red-500">*</span></label>
                    <textarea id="description" name="description" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required></textarea>
                    <p class="text-xs text-gray-500 mt-1">Provide a detailed description of the procurement request</p>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">HQ Letter Information (if applicable)</label>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
                        <div>
                            <label for="hq_letter_ref" class="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                            <input type="text" id="hq_letter_ref" name="hq_letter_ref" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        </div>

                        <div>
                            <label for="hq_letter_date" class="block text-sm font-medium text-gray-700 mb-1">Letter Date</label>
                            <input type="date" id="hq_letter_date" name="hq_letter_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Upload Request Letter (PDF/Image)</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="request_letter" class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                                    <span>Upload a file</span>
                                    <input id="request_letter" name="request_letter" type="file" class="sr-only" accept=".pdf,.jpg,.jpeg,.png" max="2097152">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, JPG or PNG up to 2MB</p>
                            <p class="text-xs text-red-500 mt-1">Note: Use smaller files or compress images before uploading</p>
                            <p class="text-xs text-gray-500 mt-1">Tip: For images, try resizing to 1000x1000 pixels or less</p>
                        </div>
                    </div>
                    <div id="request_letter_preview" class="mt-2 hidden">
                        <div class="flex items-center p-2 bg-gray-50 rounded-md">
                            <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                            </svg>
                            <span id="request_letter_name" class="text-sm text-gray-500"></span>
                            <span id="request_letter_size" class="text-xs text-gray-400 ml-2"></span>
                            <button type="button" id="remove_request_letter" class="ml-auto text-gray-400 hover:text-gray-500">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Call Quotations -->
            <div class="form-step hidden" id="step2">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Call for Quotations</h2>
                <p class="text-gray-600 mb-4">Create a quotation request to send to vendors.</p>

                <div class="mb-6">
                    <label for="quotation_subject" class="block text-sm font-medium text-gray-700 mb-1">Subject <span class="text-red-500">*</span></label>
                    <input type="text" id="quotation_subject" name="quotation_subject" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Request for Quotation for [Items]" required>
                </div>

                <!-- Items Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Items Required</h3>

                    <div class="bg-gray-50 p-4 rounded-md">
                        <div class="mb-4">
                            <label for="item_search" class="block text-sm font-medium text-gray-700 mb-1">Search and Add Items <span class="text-red-500">*</span></label>
                            <div class="flex">
                                <input type="text" id="item_search" class="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Search for items...">
                                <button type="button" id="add_item_btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-r-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </div>
                            <div id="item_search_results" class="mt-2 hidden bg-white border border-gray-300 rounded-md shadow-sm max-h-40 overflow-y-auto">
                                <!-- Search results will appear here -->
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item1" data-item-name="Computer (Desktop)">Computer (Desktop)</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item2" data-item-name="Laptop">Laptop</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item3" data-item-name="Printer">Printer</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item4" data-item-name="Scanner">Scanner</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item5" data-item-name="Projector">Projector</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item6" data-item-name="Interactive Board">Interactive Board</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item7" data-item-name="UPS">UPS</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item8" data-item-name="Network Switch">Network Switch</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item9" data-item-name="Router">Router</div>
                                <div class="item-result p-2 hover:bg-gray-100 cursor-pointer" data-item-id="item10" data-item-name="Access Point">Access Point</div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Search and add items to include in the quotation request</p>
                        </div>

                        <div id="selected_items_container" class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selected Items</label>
                            <div id="selected_items_list" class="space-y-3">
                                <!-- Selected items will appear here -->
                                <p id="no_items_message" class="text-gray-500 italic">No items selected yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Selected Item Template -->
                <template id="selected-item-template">
                    <div class="selected-item bg-white p-3 rounded-md border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <div class="font-medium text-gray-800 item-name"></div>
                            <button type="button" class="remove-item text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Quantity <span class="text-red-500">*</span></label>
                                <input type="number" name="item_quantities[]" class="item-quantity w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" min="1" value="1" required>
                                <input type="hidden" name="item_ids[]" class="item-id">
                                <input type="hidden" name="item_names[]" class="item-name-input">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Specifications</label>
                                <textarea name="item_specifications[]" rows="2" class="item-specifications w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Enter specifications for this item"></textarea>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Vendors Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Select Vendors for Quotation Request</h3>
                    <p class="text-sm text-gray-600 mb-4">Search and add vendors to request quotations from.</p>

                    <div class="bg-gray-50 p-4 rounded-md">
                        <div class="mb-4">
                            <label for="vendor_search" class="block text-sm font-medium text-gray-700 mb-1">Search and Add Vendors <span class="text-red-500">*</span></label>
                            <div class="flex">
                                <input type="text" id="vendor_search" class="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Search for vendors...">
                                <button type="button" id="add_vendor_btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-r-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </div>
                            <div id="vendor_search_results" class="mt-2 hidden bg-white border border-gray-300 rounded-md shadow-sm max-h-40 overflow-y-auto">
                                <!-- Search results will appear here -->
                                <% if (typeof vendors !== 'undefined' && vendors.length > 0) { %>
                                    <% vendors.forEach(vendor => { %>
                                        <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                            data-vendor-id="<%= vendor.vendor_id %>"
                                            data-vendor-name="<%= vendor.name %>"
                                            data-vendor-address="<%= vendor.address || '' %>">
                                            <%= vendor.name %>
                                        </div>
                                    <% }); %>
                                <% } else { %>
                                    <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                        data-vendor-id="1"
                                        data-vendor-name="Middha Electro World"
                                        data-vendor-address="15/7, Model Gram, Ludhiana, Punjab 141002, India">
                                        Middha Electro World
                                    </div>
                                    <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                        data-vendor-id="2"
                                        data-vendor-name="ABC Computers"
                                        data-vendor-address="Shop No. 5, Mall Road, Ludhiana, Punjab 141001, India">
                                        ABC Computers
                                    </div>
                                    <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                        data-vendor-id="3"
                                        data-vendor-name="XYZ Electronics"
                                        data-vendor-address="123, Industrial Area, Phase 2, Chandigarh 160002, India">
                                        XYZ Electronics
                                    </div>
                                    <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                        data-vendor-id="4"
                                        data-vendor-name="Tech Solutions"
                                        data-vendor-address="456, IT Park, Mohali, Punjab 160059, India">
                                        Tech Solutions
                                    </div>
                                    <div class="vendor-result p-2 hover:bg-gray-100 cursor-pointer"
                                        data-vendor-id="5"
                                        data-vendor-name="Digital World"
                                        data-vendor-address="789, Sector 17, Chandigarh 160017, India">
                                        Digital World
                                    </div>
                                <% } %>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Search and add vendors to send quotation requests to</p>
                        </div>

                        <div id="selected_vendors_container" class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selected Vendors</label>
                            <div id="selected_vendors_list" class="space-y-2">
                                <!-- Selected vendors will appear here -->
                                <p id="no_vendors_message" class="text-gray-500 italic">No vendors selected yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Selected Vendor Template -->
                <template id="selected-vendor-template">
                    <div class="selected-vendor bg-white p-3 rounded-md border border-gray-200 flex justify-between items-start">
                        <div>
                            <div class="font-medium text-gray-800 vendor-name"></div>
                            <div class="text-sm text-gray-500 vendor-address"></div>
                            <input type="hidden" name="selected_vendors[]" class="vendor-id">
                            <input type="hidden" name="vendor_names[]" class="vendor-name-input">
                            <input type="hidden" name="vendor_addresses[]" class="vendor-address-input">
                        </div>
                        <button type="button" class="remove-vendor text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </template>

                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Terms and Conditions</h3>
                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                        <label for="terms_conditions" class="block text-sm font-medium text-gray-700 mb-1">Terms and Conditions</label>
                        <textarea id="terms_conditions" name="terms_conditions" rows="5" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">1. All prices should include GST and delivery charges.
2. Please mention your GSTIN in the quotation.
3. Quotation should be valid for at least 30 days.
4. Delivery period should be clearly mentioned.
5. Payment will be made after successful delivery and inspection of goods.</textarea>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="buyer_name" class="block text-sm font-medium text-gray-700 mb-1">Buyer Name</label>
                                <input type="text" id="buyer_name" name="buyer_name" value="Principal" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            </div>
                            <div>
                                <label for="buyer_designation" class="block text-sm font-medium text-gray-700 mb-1">Buyer Designation</label>
                                <input type="text" id="buyer_designation" name="buyer_designation" value="Principal" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Quotation Request Preview</h3>
                    <div class="bg-white border border-gray-200 rounded-md p-6">
                        <div class="text-center mb-6">
                            <h4 class="text-xl font-bold">REQUEST FOR QUOTATION</h4>
                        </div>

                        <div class="mb-4">
                            <p>Date: <span id="quotation-date-preview"><%= new Date().toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) %></span></p>
                        </div>

                        <div id="vendors-preview" class="mb-4">
                            <p class="font-medium">To:</p>
                            <div id="vendors-preview-list" class="mt-1 pl-5">
                                <p class="text-gray-600 italic">No vendors selected yet</p>
                            </div>
                        </div>

                        <div class="mb-4">
                            <p>Dear Sir/Madam,</p>
                            <p class="mt-2 font-medium" id="quotation-subject-preview">Subject: Request for Quotation</p>
                            <p class="mt-2">We are pleased to invite you to submit a quotation for the following items:</p>
                        </div>

                        <div class="mb-4 overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 border">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">S.No.</th>
                                        <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Item Description</th>
                                        <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Specifications</th>
                                        <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Quantity</th>
                                        <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="quotation-items-preview">
                                    <!-- Items will be added dynamically -->
                                    <tr>
                                        <td colspan="5" class="px-4 py-3 text-center text-gray-500 italic">Select items to see preview</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mb-4">
                            <p class="font-medium">Terms and Conditions:</p>
                            <div id="terms-preview" class="mt-2 pl-5 space-y-1">
                                <!-- Terms will be added dynamically -->
                            </div>
                        </div>

                        <div>
                            <p>Thanking you,</p>
                            <p class="mt-4" id="buyer-name-preview">Principal</p>
                            <p id="buyer-designation-preview">Principal</p>
                            <p>Senior Secondary Residential School for Meritorious Students</p>
                            <p>Ludhiana</p>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <button type="button" id="generate-quotation-pdf" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                        <i class="fas fa-file-pdf mr-2"></i> Generate Quotation PDF
                        <span id="pdf-loading-spinner" class="hidden ml-2 inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-white"></span>
                    </button>

                    <!-- PDF Options Modal -->
                    <div id="pdf-options-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                            <div class="mt-3 text-center">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">PDF Generated Successfully</h3>
                                <div class="mt-2 px-7 py-3">
                                    <p class="text-sm text-gray-500">Choose how you want to view the PDF:</p>
                                </div>
                                <div class="flex justify-center mt-4 space-x-4">
                                    <button id="download-pdf-btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                                        <i class="fas fa-download mr-2"></i> Download
                                    </button>
                                    <button id="open-pdf-btn" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                                        <i class="fas fa-external-link-alt mr-2"></i> Open in Browser
                                    </button>
                                </div>
                                <div class="mt-4">
                                    <button id="close-pdf-modal-btn" class="text-sm text-gray-500 hover:text-gray-700">
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Quotation Upload -->
            <div class="form-step hidden" id="step3">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Quotation Upload</h2>
                <p class="text-gray-600 mb-4">Upload quotations from vendors for comparison.</p>

                <!-- Vendor Quotations Table -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Vendor Quotations</h3>
                    <p class="text-sm text-gray-600 mb-4">Enter quotation details for each vendor.</p>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 border">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Vendor</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Quotation</th>
                                    <th class="px-4 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Items</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="vendor-quotations-body">
                                <!-- Vendor rows will be populated from Step 2 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Vendor Quotation Details Modal -->
                <div id="quotation-details-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900" id="modal-vendor-name">Vendor Quotation Details</h3>
                            <button type="button" id="close-quotation-modal" class="text-gray-400 hover:text-gray-500">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <div class="mb-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Quotation Date <span class="text-red-500">*</span></label>
                                    <input type="date" id="modal-quotation-date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Quotation Reference</label>
                                    <input type="text" id="modal-quotation-reference" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Upload Quotation (PDF/Image) <span class="text-red-500">*</span></label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                                            <span>Upload a file</span>
                                            <input type="file" id="modal-quotation-file" class="sr-only" accept=".pdf,.jpg,.jpeg,.png" required>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PDF, JPG or PNG up to 10MB</p>
                                </div>
                            </div>
                            <div id="modal-quotation-preview" class="mt-2 hidden">
                                <div class="flex items-center p-2 bg-gray-50 rounded-md">
                                    <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                                    </svg>
                                    <span id="modal-quotation-filename" class="text-sm text-gray-500"></span>
                                    <button type="button" id="modal-remove-quotation" class="ml-auto text-gray-400 hover:text-gray-500">
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-800 mb-2">Item Prices (Including GST)</h4>
                            <div id="modal-item-prices" class="space-y-2">
                                <!-- Item prices will be added dynamically -->
                            </div>
                        </div>

                        <div class="flex justify-end mt-4">
                            <button type="button" id="save-quotation-details" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                                Save Quotation Details
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Item Price Template for Modal -->
                <template id="modal-item-price-template">
                    <div class="item-price-entry grid grid-cols-1 md:grid-cols-3 gap-4 p-2 border border-gray-200 rounded-md">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Item</label>
                            <input type="text" class="item-name-display w-full bg-gray-100 rounded-md border-gray-300 shadow-sm" readonly>
                            <input type="hidden" class="item-id">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price (with GST) <span class="text-red-500">*</span></label>
                            <input type="number" class="item-unit-price w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" min="0" step="0.01" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Price</label>
                            <input type="number" class="item-total-price w-full bg-gray-100 rounded-md border-gray-300 shadow-sm" readonly>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                <button type="button" id="prevBtn" class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 hidden">
                    <i class="fas fa-arrow-left mr-2"></i> Previous
                </button>
                <div class="flex space-x-4">
                    <button type="button" id="saveBtn" class="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50">
                        <i class="fas fa-save mr-2"></i> Save Draft
                    </button>
                    <button type="button" id="nextBtn" class="px-6 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                        Next <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                    <button type="button" id="submitBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 hidden">
                        <i class="fas fa-check-circle mr-2"></i> Submit
                    </button>
                </div>
            </div>

            <!-- Step 4: Comparative Statement -->
            <div class="form-step hidden" id="step4">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Comparative Statement</h2>
                <p class="text-gray-600 mb-4">Compare quotations from different vendors to select the best option.</p>

                <div class="mb-6">
                    <div class="overflow-x-auto">
                        <table id="comparative-table" class="min-w-full divide-y divide-gray-200 border">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Item</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Quantity</th>
                                    <!-- Vendor columns will be added dynamically -->
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="comparative-body">
                                <!-- Rows will be added dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="2" class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r">Total</th>
                                    <!-- Vendor total cells will be added dynamically -->
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p class="text-sm text-yellow-700">
                            <i class="fas fa-info-circle mr-2"></i>
                            The lowest price for each item is highlighted in green. Enter prices for each vendor to compare.
                        </p>
                    </div>

                    <div class="mt-4 flex justify-end">
                        <button type="button" id="preview-comparative-btn" class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50">
                            <i class="fas fa-file-pdf mr-2"></i> Preview Comparative Statement
                            <span id="comparative-loading-spinner" class="hidden ml-2 inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-white"></span>
                        </button>
                    </div>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Selected Vendor</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="selected_vendor" class="block text-sm font-medium text-gray-700 mb-1">Select Vendor <span class="text-red-500">*</span></label>
                            <select id="selected_vendor" name="selected_vendor" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                                <option value="">-- Select Vendor --</option>
                                <!-- Vendor options will be added dynamically -->
                            </select>
                        </div>
                        <div>
                            <label for="total_amount" class="block text-sm font-medium text-gray-700 mb-1">Total Amount</label>
                            <input type="text" id="total_amount" name="total_amount" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" readonly>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Selection Justification</h3>
                    <textarea id="selection_justification" name="selection_justification" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Explain why this vendor was selected (e.g., lowest price, best quality, etc.)"></textarea>
                </div>
            </div>

            <!-- Step 5: Committee Proceedings -->
            <div class="form-step hidden" id="step5">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Committee Proceedings</h2>
                <p class="text-gray-600 mb-4">Record details of the procurement committee meeting.</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="meeting_date" class="block text-sm font-medium text-gray-700 mb-1">Meeting Date <span class="text-red-500">*</span></label>
                        <input type="date" id="meeting_date" name="meeting_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                    </div>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Committee Members</h3>
                    <p class="text-sm text-gray-600 mb-4">Add all committee members who participated in the decision.</p>

                    <div id="members-container">
                        <!-- Member template will be cloned here -->
                    </div>

                    <!-- Member Template -->
                    <template id="member-template">
                        <div class="member-entry bg-gray-50 p-4 rounded-md mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-700">Member <span class="member-number"></span></h4>
                                <button type="button" class="remove-member text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i> Remove
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Name <span class="text-red-500">*</span></label>
                                    <input type="text" name="committee_members[][name]" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                                    <input type="text" name="committee_members[][designation]" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                                </div>
                            </div>
                        </div>
                    </template>

                    <button type="button" id="add-member" class="mt-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50">
                        <i class="fas fa-plus mr-2"></i> Add Committee Member
                    </button>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Upload Committee Proceedings (PDF/Image)</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="committee_proceedings" class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                                    <span>Upload a file</span>
                                    <input id="committee_proceedings" name="committee_proceedings" type="file" class="sr-only" accept=".pdf,.jpg,.jpeg,.png">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, JPG or PNG up to 10MB</p>
                        </div>
                    </div>
                    <div id="committee_proceedings_preview" class="mt-2 hidden">
                        <div class="flex items-center p-2 bg-gray-50 rounded-md">
                            <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                            </svg>
                            <span id="committee_proceedings_name" class="text-sm text-gray-500"></span>
                            <button type="button" id="remove_committee_proceedings" class="ml-auto text-gray-400 hover:text-gray-500">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="committee_remarks" class="block text-sm font-medium text-gray-700 mb-1">Committee Remarks</label>
                    <textarea id="committee_remarks" name="committee_remarks" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Enter any additional remarks or notes from the committee meeting"></textarea>
                </div>
            </div>

            <!-- Step 6: Bill & Payment Upload -->
            <div class="form-step hidden" id="step6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Bill & Payment Upload</h2>
                <p class="text-gray-600 mb-4">Record payment details and upload supporting documents.</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="bill_number" class="block text-sm font-medium text-gray-700 mb-1">Bill Number <span class="text-red-500">*</span></label>
                        <input type="text" id="bill_number" name="bill_number" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                    </div>
                    <div>
                        <label for="bill_date" class="block text-sm font-medium text-gray-700 mb-1">Bill Date <span class="text-red-500">*</span></label>
                        <input type="date" id="bill_date" name="bill_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="payment_mode" class="block text-sm font-medium text-gray-700 mb-1">Payment Mode <span class="text-red-500">*</span></label>
                        <select id="payment_mode" name="payment_mode" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                            <option value="">-- Select Payment Mode --</option>
                            <option value="cash">Cash</option>
                            <option value="cheque">Cheque</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="upi">UPI</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div>
                        <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                        <input type="date" id="payment_date" name="payment_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-1">Payment Status <span class="text-red-500">*</span></label>
                        <select id="payment_status" name="payment_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                            <option value="pending">Pending</option>
                            <option value="partial">Partial</option>
                            <option value="complete">Complete</option>
                        </select>
                    </div>
                    <div id="payment_reference_container" class="hidden">
                        <label for="payment_reference" class="block text-sm font-medium text-gray-700 mb-1">Payment Reference</label>
                        <input type="text" id="payment_reference" name="payment_reference" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Cheque No. / Transaction ID">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Upload Bill (PDF/Image) <span class="text-red-500">*</span></label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="bill" class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                                    <span>Upload a file</span>
                                    <input id="bill" name="bill" type="file" class="sr-only" accept=".pdf,.jpg,.jpeg,.png" required>
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, JPG or PNG up to 10MB</p>
                        </div>
                    </div>
                    <div id="bill_preview" class="mt-2 hidden">
                        <div class="flex items-center p-2 bg-gray-50 rounded-md">
                            <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                            </svg>
                            <span id="bill_name" class="text-sm text-gray-500"></span>
                            <button type="button" id="remove_bill" class="ml-auto text-gray-400 hover:text-gray-500">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Upload Payment Proof (PDF/Image)</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="payment_proof" class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                                    <span>Upload a file</span>
                                    <input id="payment_proof" name="payment_proof" type="file" class="sr-only" accept=".pdf,.jpg,.jpeg,.png">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PDF, JPG or PNG up to 10MB</p>
                        </div>
                    </div>
                    <div id="payment_proof_preview" class="mt-2 hidden">
                        <div class="flex items-center p-2 bg-gray-50 rounded-md">
                            <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                            </svg>
                            <span id="payment_proof_name" class="text-sm text-gray-500"></span>
                            <button type="button" id="remove_payment_proof" class="ml-auto text-gray-400 hover:text-gray-500">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="payment_notes" class="block text-sm font-medium text-gray-700 mb-1">Payment Notes</label>
                    <textarea id="payment_notes" name="payment_notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" placeholder="Enter any additional notes about the payment"></textarea>
                </div>

                <div class="p-4 bg-blue-50 border border-blue-200 rounded-md mb-6">
                    <h3 class="text-lg font-medium text-blue-800 mb-2">Procurement Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-700">Selected Vendor:</p>
                            <p id="summary_vendor" class="text-sm text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-700">Total Amount:</p>
                            <p id="summary_amount" class="text-sm text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-700">Items:</p>
                            <p id="summary_items" class="text-sm text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-700">Committee Meeting Date:</p>
                            <p id="summary_meeting_date" class="text-sm text-gray-900">-</p>
                        </div>
                    </div>
                </div>
            </div>


        </form>
    </div>
</div>

<!-- Toast Notification Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2"></div>

<!-- Hidden Vendor Options Template -->
<select id="vendor-options-template" class="hidden">
    <option value="">-- Select Vendor --</option>
    <% if (vendors && vendors.length > 0) { %>
        <% vendors.forEach(vendor => { %>
            <option value="<%= vendor.vendor_id %>"
                data-contact-person="<%= vendor.contact_person || '' %>"
                data-phone="<%= vendor.phone || '' %>"
                data-email="<%= vendor.email || '' %>"
                data-address="<%= vendor.address || '' %>">
                <%= vendor.name %>
            </option>
        <% }); %>
    <% } %>
    <!-- Manually added vendor until database is updated -->
    <option value="middha_electro_world"
        data-contact-person=""
        data-phone="+91 98553 83888"
        data-email="<EMAIL>"
        data-address="15/7, Model Gram, Ludhiana, Punjab 141002, India"
        data-gstin="03FFZPS3403E1ZA">
        Middha Electro World
    </option>
</select>

<!-- Add custom styles to ensure save button is visible -->
<style>
    #saveBtn {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        background-color: #10b981 !important; /* bg-green-500 */
    }
    #saveBtn:hover {
        background-color: #059669 !important; /* bg-green-600 */
    }

    /* Toast notification styles */
    .toast {
        transition: all 0.3s ease;
        max-width: 350px;
    }
    .toast.info {
        background-color: #3b82f6;
        border-left: 4px solid #1d4ed8;
    }
    .toast.success {
        background-color: #10b981;
        border-left: 4px solid #047857;
    }
    .toast.error {
        background-color: #ef4444;
        border-left: 4px solid #b91c1c;
    }
    .toast.warning {
        background-color: #f59e0b;
        border-left: 4px solid #b45309;
    }
</style>

<!-- Toast notification functions -->
<script>
    // Toast notification functions
    function showToast(type, message, duration = 3000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return null;

        // Create toast element
        const toast = document.createElement('div');
        const toastId = 'toast-' + Date.now();
        toast.id = toastId;
        toast.className = `toast ${type} text-white p-4 rounded-md shadow-lg flex items-center mb-2`;

        // Add icon based on type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle mr-2"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
                break;
            case 'info':
            default:
                icon = '<i class="fas fa-info-circle mr-2"></i>';
                break;
        }

        // Add close button
        const closeButton = '<button class="ml-auto text-white hover:text-gray-200 focus:outline-none" onclick="hideToast(\'' + toastId + '\')"><i class="fas fa-times"></i></button>';

        // Set toast content
        toast.innerHTML = icon + message + closeButton;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Auto-hide toast after duration (if duration > 0)
        if (duration > 0) {
            setTimeout(() => {
                hideToast(toastId);
            }, duration);
        }

        return toastId;
    }

    function hideToast(toastId) {
        const toast = document.getElementById(toastId);
        if (!toast) return;

        // Add fade-out effect
        toast.style.opacity = '0';

        // Remove toast after animation
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
</script>

<!-- JavaScript for Multi-step Form -->
<script>
// Ensure document is defined before adding event listener
if (typeof document !== 'undefined' && document !== null) {
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure window is defined
        if (typeof window === 'undefined') return;

        // Form elements - add null checks
        const form = document.getElementById('procurementForm');
        const steps = document.querySelectorAll('.form-step') || [];
        const stepIndicators = document.querySelectorAll('.step-indicator') || [];
        const progressBar = document.getElementById('progress-bar');
    const currentStepInput = document.getElementById('current_step');

    // Buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const saveBtn = document.getElementById('saveBtn');
    const submitBtn = document.getElementById('submitBtn');

    // File upload elements
    const requestLetterInput = document.getElementById('request_letter');
    const requestLetterPreview = document.getElementById('request_letter_preview');
    const requestLetterName = document.getElementById('request_letter_name');
    const removeRequestLetter = document.getElementById('remove_request_letter');

    // Current step - get from input or default to 1
    let currentStep = parseInt(currentStepInput.value) || 1;

    // Update UI based on current step
    function updateUI() {
        // Hide all steps
        steps.forEach(step => step.classList.add('hidden'));

        // Show current step
        document.getElementById(`step${currentStep}`).classList.remove('hidden');

        // Update step indicators
        stepIndicators.forEach((indicator, index) => {
            const step = index + 1;

            if (step < currentStep) {
                // Completed step
                indicator.classList.add('completed');
                indicator.classList.remove('active');
                const numberElement = indicator.querySelector('div:first-child');
                numberElement.classList.add('bg-green-500', 'text-white');
                numberElement.classList.remove('bg-gray-200', 'text-gray-600', 'bg-it-admin-primary');
                numberElement.innerHTML = '<i class="fas fa-check"></i>';
            } else if (step === currentStep) {
                // Current step
                indicator.classList.add('active');
                indicator.classList.remove('completed');
                const numberElement = indicator.querySelector('div:first-child');
                numberElement.classList.add('bg-it-admin-primary', 'text-white');
                numberElement.classList.remove('bg-gray-200', 'text-gray-600', 'bg-green-500');
                numberElement.textContent = step;
            } else {
                // Future step
                indicator.classList.remove('active', 'completed');
                const numberElement = indicator.querySelector('div:first-child');
                numberElement.classList.add('bg-gray-200', 'text-gray-600');
                numberElement.classList.remove('bg-it-admin-primary', 'text-white', 'bg-green-500');
                numberElement.textContent = step;
            }
        });

        // Update progress bar
        const progress = ((currentStep - 1) / (steps.length - 1)) * 100;
        progressBar.style.width = `${progress}%`;

        // Update buttons
        if (currentStep === 1) {
            prevBtn.classList.add('hidden');
        } else {
            prevBtn.classList.remove('hidden');
        }

        if (currentStep === steps.length) {
            nextBtn.classList.add('hidden');
            submitBtn.classList.remove('hidden');
        } else {
            nextBtn.classList.remove('hidden');
            submitBtn.classList.add('hidden');
        }

        // Update current step input
        currentStepInput.value = currentStep;

        // Initialize Step 3 if we're on that step
        if (currentStep === 3 && typeof window.initStep3 === 'function') {
            console.log('Current step is 3, calling window.initStep3()...');

            // Make sure selectedVendors and selectedItems are in the global scope
            if (typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors)) {
                window.selectedVendors = [...selectedVendors];
                console.log('Copied selectedVendors to window.selectedVendors in updateUI');
            }

            if (typeof selectedItems !== 'undefined' && Array.isArray(selectedItems)) {
                window.selectedItems = [...selectedItems];
                console.log('Copied selectedItems to window.selectedItems in updateUI');
            }

            // Call initStep3
            window.initStep3();
        }
    }

    // Validate current step
    function validateStep() {
        const currentStepElement = document.getElementById(`step${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');

        let isValid = true;

        // Temporarily disable required attribute on fields in other steps
        document.querySelectorAll('.form-step:not(#step' + currentStep + ') [required]').forEach(field => {
            field.setAttribute('data-required', 'true');
            field.removeAttribute('required');
        });

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');

                // Add error message if it doesn't exist
                let errorMessage = field.nextElementSibling;
                if (!errorMessage || !errorMessage.classList.contains('error-message')) {
                    errorMessage = document.createElement('p');
                    errorMessage.classList.add('error-message', 'text-red-500', 'text-xs', 'mt-1');
                    errorMessage.textContent = 'This field is required';
                    field.parentNode.insertBefore(errorMessage, field.nextSibling);
                }
            } else {
                field.classList.remove('border-red-500');

                // Remove error message if it exists
                const errorMessage = field.nextElementSibling;
                if (errorMessage && errorMessage.classList.contains('error-message')) {
                    errorMessage.remove();
                }
            }
        });

        // Additional validation for step 2 (quotations)
        if (currentStep === 2) {
            // Check if total estimated cost is over ₹50,000
            let totalEstimatedCost = 0;
            itemsData.forEach(item => {
                if (item && item.estimated_unit_price && item.quantity) {
                    totalEstimatedCost += parseFloat(item.estimated_unit_price) * parseInt(item.quantity);
                }
            });

            // Count valid vendors with quotations
            const vendorEntries = document.querySelectorAll('.vendor-entry');
            let validVendorsCount = 0;

            vendorEntries.forEach(vendorEntry => {
                const vendorSelect = vendorEntry.querySelector('.vendor-select');
                const quotationFile = vendorEntry.querySelector('.quotation-file');

                if (vendorSelect && vendorSelect.value &&
                    quotationFile && quotationFile.files && quotationFile.files[0]) {
                    validVendorsCount++;
                }
            });

            // If total cost is over ₹50,000, require at least 5 quotations
            if (totalEstimatedCost > 50000 && validVendorsCount < 5) {
                isValid = false;

                // Show error message
                let errorContainer = document.getElementById('quotation-error-container');
                if (!errorContainer) {
                    errorContainer = document.createElement('div');
                    errorContainer.id = 'quotation-error-container';
                    errorContainer.className = 'p-4 mb-4 bg-red-50 border border-red-200 rounded-md';

                    const vendorsSection = document.querySelector('.form-step#step2 .mb-6:nth-child(2)');
                    if (vendorsSection) {
                        vendorsSection.insertBefore(errorContainer, vendorsSection.firstChild);
                    }
                }

                errorContainer.innerHTML = `
                    <p class="text-red-700">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        For purchases over ₹50,000 (current estimate: ₹${totalEstimatedCost.toFixed(2)}), at least 5 vendor quotations are required.
                        You currently have ${validVendorsCount} valid quotation(s).
                    </p>
                `;
            } else {
                // Remove error message if it exists
                const errorContainer = document.getElementById('quotation-error-container');
                if (errorContainer) {
                    errorContainer.remove();
                }
            }
        }

        // Restore required attributes on fields in other steps
        document.querySelectorAll('[data-required="true"]').forEach(field => {
            field.setAttribute('required', '');
            field.removeAttribute('data-required');
        });

        return isValid;
    }

    // Handle next button click
    nextBtn.addEventListener('click', function() {
        if (validateStep()) {
            // Special handling for step transitions
            if (currentStep === 3) {
                // When moving from Step 3 to Step 4, explicitly capture the quotation data
                if (typeof window.captureQuotationPricesForComparative === 'function') {
                    window.captureQuotationPricesForComparative();
                }
            }

            currentStep++;
            updateUI();
            window.scrollTo(0, 0);
        }
    });

    // Handle previous button click
    prevBtn.addEventListener('click', function() {
        currentStep--;
        updateUI();
        window.scrollTo(0, 0);
    });

    // Handle save button click
    saveBtn.addEventListener('click', function() {
        validateAndSaveDraft();
    });

    // Handle top save button click
    const topSaveBtn = document.getElementById('topSaveBtn');
    if (topSaveBtn) {
        topSaveBtn.addEventListener('click', function() {
            validateAndSaveDraft();
        });
    }

    // Function to validate fields and save draft
    function validateAndSaveDraft() {
        // Validate only the current step's required fields
        const currentStepElement = document.getElementById(`step${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');

        let missingFields = [];

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');

                // Get field label
                let fieldLabel = '';
                const labelElement = field.closest('div').querySelector('label');
                if (labelElement) {
                    fieldLabel = labelElement.textContent.replace('*', '').trim();
                }

                missingFields.push(fieldLabel || 'Unnamed field');

                // Add error message if it doesn't exist
                let errorMessage = field.nextElementSibling;
                if (!errorMessage || !errorMessage.classList.contains('error-message')) {
                    errorMessage = document.createElement('p');
                    errorMessage.classList.add('error-message', 'text-red-500', 'text-xs', 'mt-1');
                    errorMessage.textContent = 'This field is required';
                    field.parentNode.insertBefore(errorMessage, field.nextSibling);
                }
            } else {
                field.classList.remove('border-red-500');

                // Remove error message if it exists
                const errorMessage = field.nextElementSibling;
                if (errorMessage && errorMessage.classList.contains('error-message')) {
                    errorMessage.remove();
                }
            }
        });

        if (missingFields.length > 0) {
            // Show error toast and don't save draft
            showToast('error', `Cannot save draft. Please fill in all required fields: ${missingFields.join(', ')}.`, 5000);

            // Scroll to the first missing field
            const firstMissingField = currentStepElement.querySelector('.border-red-500');
            if (firstMissingField) {
                firstMissingField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstMissingField.focus();
            }

            return; // Don't save draft if validation fails
        }

        // Save draft only if validation passes
        saveDraft();
    }

    // Save draft function
    function saveDraft() {
        try {
            // Show loading toast
            showToast('info', 'Saving draft...', 3000);

            // Determine if the current step has file uploads
            const hasFileUploads = [1, 3, 5, 6].includes(currentStep);
            console.log(`Current step ${currentStep} ${hasFileUploads ? 'has' : 'does not have'} file uploads`);

            // Check file sizes before uploading
            if (hasFileUploads) {
                const fileInputs = document.querySelectorAll('input[type="file"]');
                let totalSize = 0;
                let largeFile = null;

                fileInputs.forEach(input => {
                    if (input.files && input.files.length > 0) {
                        const file = input.files[0];
                        console.log(`File ${input.name}: ${file.name}, size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);

                        totalSize += file.size;

                        // Check if any individual file is too large (over 8MB)
                        if (file.size > 8 * 1024 * 1024) {
                            largeFile = file;
                        }
                    }
                });

                // If total size is too large or any individual file is too large
                if (totalSize > 9 * 1024 * 1024) {
                    showToast('error', 'Total file size exceeds 9MB. Please use smaller files.', 5000);
                    console.error('Total file size too large:', (totalSize / 1024 / 1024).toFixed(2) + 'MB');
                    return;
                }

                if (largeFile) {
                    showToast('error', `File "${largeFile.name}" is too large (${(largeFile.size / 1024 / 1024).toFixed(2)}MB). Maximum size is 8MB.`, 5000);
                    console.error('Individual file too large:', largeFile.name, (largeFile.size / 1024 / 1024).toFixed(2) + 'MB');
                    return;
                }
            }

            // Create a data object to hold all form data
            let formData;
            let jsonData = null;

            // For steps with file uploads, use FormData
            // For steps without file uploads, use JSON
            if (hasFileUploads) {
                formData = new FormData();
                console.log('Using FormData for this step');
            } else {
                formData = new FormData(); // Still need FormData for the request
                jsonData = {}; // But will use JSON for the actual data
                console.log('Using JSON for this step');
            }

            // Get the form element
            const form = document.getElementById('procurementForm');
            if (!form) {
                throw new Error('Form element not found');
            }

            // Add basic form fields that should be included in all steps
            const addBasicField = (id, fieldName) => {
                try {
                    // Use safeDOM to get the field value
                    const value = window.safeDOM ?
                        window.safeDOM.getValue(id) :
                        (() => {
                            const field = document.getElementById(id);
                            return field ? (field.value || '') : '';
                        })();

                    // Add to appropriate data structure
                    if (hasFileUploads) {
                        formData.append(fieldName || id, value);
                    } else {
                        jsonData[fieldName || id] = value;
                    }
                } catch (error) {
                    console.error(`Error adding basic field ${id}:`, error);
                    // Add empty value to prevent further errors
                    if (hasFileUploads) {
                        formData.append(fieldName || id, '');
                    } else {
                        jsonData[fieldName || id] = '';
                    }
                }
            };

            // Always include current step and request ID
            if (hasFileUploads) {
                formData.append('current_step', currentStep);

                const requestIdField = document.getElementById('request_id');
                if (requestIdField && requestIdField.value) {
                    formData.append('request_id', requestIdField.value);
                }
            } else {
                jsonData['current_step'] = currentStep;

                const requestIdField = document.getElementById('request_id');
                if (requestIdField && requestIdField.value) {
                    jsonData['request_id'] = requestIdField.value;
                }
            }

            // Add basic fields
            addBasicField('title');
            addBasicField('description');
            addBasicField('request_date');
            addBasicField('department');
            addBasicField('budget_code');

            console.log(`Saving draft for step ${currentStep}`);

            // Only include data relevant to the current step
            if (currentStep == 1) {
                // Step 1: Request Letter
                console.log('Including request letter data');

                // Add HQ letter fields
                addBasicField('hq_letter_ref');
                addBasicField('hq_letter_date');

                // Add request letter file if it exists
                if (requestLetterInput && requestLetterInput.files && requestLetterInput.files[0]) {
                    const file = requestLetterInput.files[0];
                    // Check file size before appending
                    if (file.size > 2 * 1024 * 1024) { // 2MB limit
                        throw new Error('Request letter file is too large. Please upload a file smaller than 2MB.');
                    }

                    // Check file type
                    const validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
                    if (!validTypes.includes(file.type)) {
                        throw new Error(`Invalid file type: ${file.type}. Please use JPG, PNG, or PDF files only.`);
                    }

                    // Add file to form data
                    formData.append('request_letter', file);
                    console.log('Added request letter file:', file.name, 'Size:', Math.round(file.size / 1024), 'KB', 'Type:', file.type);

                    // Log detailed file info for debugging
                    console.log('File details:', {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: new Date(file.lastModified).toISOString()
                    });
                }
            }
            else if (currentStep == 2) {
                // Step 2: Items and Vendors (no file uploads)
                console.log('Including items and vendors data');

                // Add items data
                if (itemsData && itemsData.length > 0) {
                    const filteredItems = itemsData.filter(item => item !== null);
                    jsonData['items'] = filteredItems;
                    console.log('Added items data:', filteredItems.length, 'items');
                }

                // Add selected vendors data
                if (typeof selectedVendors !== 'undefined' && selectedVendors.length > 0) {
                    jsonData['vendors'] = selectedVendors;
                    console.log('Added vendors data:', selectedVendors.length, 'vendors');
                }

                // Add selected items data
                if (typeof selectedItems !== 'undefined' && selectedItems.length > 0) {
                    jsonData['selected_items'] = selectedItems;
                    console.log('Added selected items data:', selectedItems.length, 'selected items');
                }

                // Add quotation subject and terms
                addBasicField('quotation_subject');
                addBasicField('terms_conditions');

                // Add buyer info
                addBasicField('buyer_name');
                addBasicField('buyer_designation');
            }
            else if (currentStep == 3) {
                // Step 3: Quotations (has file uploads)
                console.log('Including quotation data');

                // Add basic quotation data
                addBasicField('quotation_subject');
                addBasicField('terms_conditions');
                addBasicField('buyer_name');
                addBasicField('buyer_designation');

                // Add vendor and item IDs as simple arrays
                const vendorIds = [];
                const vendorNames = [];

                if (typeof selectedVendors !== 'undefined' && selectedVendors.length > 0) {
                    selectedVendors.forEach(vendor => {
                        if (vendor && vendor.id) {
                            vendorIds.push(vendor.id);
                            vendorNames.push(vendor.name || '');
                        }
                    });

                    formData.append('vendor_ids', vendorIds.join(','));
                    formData.append('vendor_names', vendorNames.join('||'));
                    console.log('Added vendor IDs:', vendorIds.join(','));
                }

                const itemIds = [];
                const itemNames = [];

                if (typeof selectedItems !== 'undefined' && selectedItems.length > 0) {
                    selectedItems.forEach(item => {
                        if (item && item.id) {
                            itemIds.push(item.id);
                            itemNames.push(item.name || '');
                        }
                    });

                    formData.append('item_ids', itemIds.join(','));
                    formData.append('item_names', itemNames.join('||'));
                    console.log('Added item IDs:', itemIds.join(','));
                }

                // Add quotation files if they exist
                let vendorEntries = [];
                try {
                    // Use safeDOM to prevent errors
                    vendorEntries = window.safeDOM ?
                        window.safeDOM.querySelectorAll('.vendor-entry') :
                        document.querySelectorAll('.vendor-entry') || [];
                } catch (error) {
                    console.error('Error getting vendor entries:', error);
                    vendorEntries = [];
                }
                let fileCount = 0;

                // Convert NodeList to Array and filter out null entries
                const vendorEntriesArray = Array.from(vendorEntries).filter(entry => entry !== null);
                console.log(`Processing ${vendorEntriesArray.length} vendor entries`);

                vendorEntriesArray.forEach(vendorEntry => {
                    try {
                        // Check if vendorEntry exists
                        if (!vendorEntry) return;

                        const vendorId = vendorEntry.getAttribute('data-vendor-index') ||
                                        (vendorEntry.dataset ? vendorEntry.dataset.vendorIndex : null);
                        if (!vendorId) {
                            console.warn('Vendor entry missing vendor ID, skipping');
                            return;
                        }

                        // Use try-catch for querySelector
                        let quotationFileInput = null;
                        try {
                            quotationFileInput = vendorEntry.querySelector('.quotation-file');
                        } catch (error) {
                            console.error(`Error getting quotation file input for vendor ${vendorId}:`, error);
                        }

                        if (quotationFileInput && quotationFileInput.files && quotationFileInput.files.length > 0) {
                            const file = quotationFileInput.files[0];
                            // Verify file is a valid object with size property
                            if (file && typeof file === 'object' && 'size' in file) {
                                // Check file size before appending
                                if (file.size <= 5 * 1024 * 1024) { // 5MB limit
                                    // Use a simple field name to avoid issues with complex naming
                                    formData.append(`quotation_file_${fileCount}`, file);
                                    formData.append(`quotation_vendor_${fileCount}`, vendorId);
                                    console.log(`Added quotation file ${fileCount} for vendor ${vendorId}:`, file.name, 'Size:', Math.round(file.size / 1024), 'KB');
                                    fileCount++;
                                } else {
                                    console.warn(`Quotation file for vendor ${vendorId} is too large (${Math.round(file.size / 1024)} KB). Skipping.`);
                                }
                            } else {
                                console.warn(`Invalid file object for vendor ${vendorId}`);
                            }
                        }
                    } catch (vendorError) {
                        console.error('Error processing vendor entry:', vendorError);
                    }
                });

                // Add the count of files
                formData.append('quotation_file_count', fileCount.toString());
            }
            else if (currentStep == 4) {
                // Step 4: Comparative Statement (no file uploads)
                console.log('Including comparative statement data');

                // Add selected vendor
                const selectedVendorSelect = document.getElementById('selected_vendor');
                if (selectedVendorSelect) {
                    jsonData['selected_vendor'] = selectedVendorSelect.value || '';
                }

                // Add justification
                const justificationField = document.getElementById('selection_justification');
                if (justificationField) {
                    jsonData['selection_justification'] = justificationField.value || '';
                }

                // Add comparative data
                const comparativeData = [];
                const rows = document.querySelectorAll('#comparative-table tbody tr');

                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length < 3) return;

                    const itemData = {
                        name: cells[0].textContent.trim(),
                        quantity: cells[1].textContent.trim(),
                        prices: []
                    };

                    // Get prices from each vendor column
                    for (let i = 2; i < cells.length; i++) {
                        try {
                            // First check if the cell exists
                            if (!cells[i]) {
                                console.warn(`Cell at index ${i} is undefined, using default price`);
                                itemData.prices.push('₹ 0.00');
                                continue;
                            }

                            // Then check for the font-medium element with a more robust approach
                            const fontMediumElement = cells[i].querySelector('.font-medium');
                            const unitPrice = fontMediumElement ? (fontMediumElement.textContent || '₹ 0.00') : '₹ 0.00';
                            itemData.prices.push(unitPrice);
                        } catch (error) {
                            console.error(`Error processing price at column ${i}:`, error);
                            itemData.prices.push('₹ 0.00'); // Use default price on error
                        }
                    }

                    comparativeData.push(itemData);
                });

                if (comparativeData.length > 0) {
                    jsonData['comparative_data'] = comparativeData;
                }
            }
            else if (currentStep == 5) {
                // Step 5: Committee (has file uploads)
                console.log('Including committee data');

                // Add meeting date with extra safety check
                try {
                    // First check if the meeting_date element exists before calling addBasicField
                    const meetingDateElement = window.safeDOM ?
                        window.safeDOM.getElementById('meeting_date') :
                        document.getElementById('meeting_date');

                    if (meetingDateElement) {
                        addBasicField('meeting_date');
                    } else {
                        console.warn('meeting_date element not found, skipping');
                        // Add empty value to prevent further errors
                        if (hasFileUploads) {
                            formData.append('meeting_date', '');
                        } else {
                            jsonData['meeting_date'] = '';
                        }
                    }
                } catch (error) {
                    console.error('Error handling meeting_date field:', error);
                    // Add empty value to prevent further errors
                    if (hasFileUploads) {
                        formData.append('meeting_date', '');
                    } else {
                        jsonData['meeting_date'] = '';
                    }
                }

                // Add committee proceedings file if it exists
                try {
                    // Use safeDOM if available, otherwise fall back to direct DOM access
                    const committeeProceedings = window.safeDOM ?
                        window.safeDOM.getElementById('committee_proceedings') :
                        document.getElementById('committee_proceedings');

                    // Check if committeeProceedings exists and has files property
                    if (committeeProceedings && committeeProceedings.files && committeeProceedings.files.length > 0) {
                        const file = committeeProceedings.files[0];
                        // Verify file is a valid object with size property
                        if (file && typeof file === 'object' && 'size' in file) {
                            // Check file size before appending
                            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                                console.error('Committee proceedings file is too large. Please upload a file smaller than 5MB.');
                                showToast('error', 'Committee proceedings file is too large. Please upload a file smaller than 5MB.');
                                return; // Return instead of throwing an error
                            }
                            formData.append('committee_proceedings', file);
                            console.log('Added committee proceedings file:', file.name, 'Size:', Math.round(file.size / 1024), 'KB');
                        } else {
                            console.warn('Committee proceedings file object is invalid or missing size property');
                        }
                    }
                } catch (fileError) {
                    console.error('Error processing committee proceedings file:', fileError);
                }

                // Add committee members data as a simple string
                const memberNames = [];
                const memberDesignations = [];

                const memberRows = document.querySelectorAll('.committee-member-row');
                memberRows.forEach(row => {
                    const nameInput = row.querySelector('.member-name');
                    const designationInput = row.querySelector('.member-designation');

                    if (nameInput && nameInput.value) {
                        memberNames.push(nameInput.value);
                        memberDesignations.push(designationInput ? designationInput.value : '');
                    }
                });

                if (memberNames.length > 0) {
                    formData.append('committee_member_names', memberNames.join('||'));
                    formData.append('committee_member_designations', memberDesignations.join('||'));
                    console.log('Added committee members data:', memberNames.length, 'members');
                }
            }
            else if (currentStep == 6) {
                // Step 6: Payment (has file uploads)
                console.log('Including payment data');

                // Add payment fields
                addBasicField('bill_number');
                addBasicField('bill_date');
                addBasicField('payment_mode');
                addBasicField('payment_date');
                addBasicField('payment_status');
                addBasicField('notes');

                // Add bill file if it exists
                try {
                    // Use safeDOM if available, otherwise fall back to direct DOM access
                    const billFile = window.safeDOM ? window.safeDOM.getElementById('bill') : document.getElementById('bill');

                    // Check if billFile exists and has files property
                    if (billFile && billFile.files && billFile.files.length > 0) {
                        const file = billFile.files[0];
                        // Verify file is a valid object with size property
                        if (file && typeof file === 'object' && 'size' in file) {
                            // Check file size before appending
                            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                                console.error('Bill file is too large. Please upload a file smaller than 5MB.');
                                showToast('error', 'Bill file is too large. Please upload a file smaller than 5MB.');
                                return; // Return instead of throwing an error
                            }
                            formData.append('bill', file);
                            console.log('Added bill file:', file.name, 'Size:', Math.round(file.size / 1024), 'KB');
                        } else {
                            console.warn('Bill file object is invalid or missing size property');
                        }
                    }
                } catch (fileError) {
                    console.error('Error processing bill file:', fileError);
                }

                // Add payment proof file if it exists
                try {
                    // Use safeDOM if available, otherwise fall back to direct DOM access
                    const paymentProofFile = window.safeDOM ? window.safeDOM.getElementById('payment_proof') : document.getElementById('payment_proof');

                    // Check if paymentProofFile exists and has files property
                    if (paymentProofFile && paymentProofFile.files && paymentProofFile.files.length > 0) {
                        const file = paymentProofFile.files[0];
                        // Verify file is a valid object with size property
                        if (file && typeof file === 'object' && 'size' in file) {
                            // Check file size before appending
                            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                                console.error('Payment proof file is too large. Please upload a file smaller than 5MB.');
                                showToast('error', 'Payment proof file is too large. Please upload a file smaller than 5MB.');
                                return; // Return instead of throwing an error
                            }
                            formData.append('payment_proof', file);
                            console.log('Added payment proof file:', file.name, 'Size:', Math.round(file.size / 1024), 'KB');
                        } else {
                            console.warn('Payment proof file object is invalid or missing size property');
                        }
                    }
                } catch (fileError) {
                    console.error('Error processing payment proof file:', fileError);
                }
            }

            // For steps without file uploads, add the JSON data to the FormData
            if (!hasFileUploads && jsonData) {
                formData.append('json_data', JSON.stringify(jsonData));
                console.log('Added JSON data to FormData');
            }

            // Log what we're sending to help with debugging
            console.log('Saving draft with the following data:');
            for (const [key, value] of formData.entries()) {
                if (typeof value === 'string' && value.length < 100) {
                    console.log(`- ${key}: ${value}`);
                } else if (value instanceof File) {
                    console.log(`- ${key}: File (${value.name}, ${value.size} bytes)`);
                } else {
                    console.log(`- ${key}: [data]`);
                }
            }

            // Show loading toast
            const loadingToastId = showToast('info', 'Saving draft...', 0);

            console.log('Sending save draft request...');

            // Send the form data
            console.log('About to send fetch request to /it-admin/procurement/save-draft');

            // Create a simplified version of formData for debugging
            const formDataDebug = {};
            for (const [key, value] of formData.entries()) {
                if (value instanceof File) {
                    formDataDebug[key] = `File: ${value.name} (${value.size} bytes, ${value.type})`;
                } else if (typeof value === 'string' && value.length > 100) {
                    formDataDebug[key] = value.substring(0, 100) + '... [truncated]';
                } else {
                    formDataDebug[key] = value;
                }
            }
            console.log('FormData contents:', JSON.stringify(formDataDebug, null, 2));

            // Set a timeout for the fetch request (30 seconds)
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            fetch('/it-admin/procurement/save-draft', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            })
            .then(response => {
                // Clear the timeout since we got a response
                clearTimeout(timeoutId);

                console.log('Received response:', response.status, response.statusText);
                console.log('Response headers:',
                    Array.from(response.headers.entries())
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\n')
                );

                // Always try to get the response text first
                return response.text().then(text => {
                    console.log('Save draft raw response:', text);

                    // Check if the response contains error information
                    if (text.includes('Error:') || text.includes('error:') || text.includes('Unexpected')) {
                        console.error('Detected error in response text:', text);
                    }

                    // If response is not OK (status code 200-299)
                    if (!response.ok) {
                        // Try to parse as JSON first
                        try {
                            const errorData = JSON.parse(text);
                            throw new Error(errorData.message || errorData.error || 'Server error');
                        } catch (jsonError) {
                            // If not valid JSON, use the text directly
                            throw new Error(text || 'Server error');
                        }
                    }

                    // Try to parse as JSON
                    let data;
                    try {
                        // Check if the response starts with <!DOCTYPE or <html
                        if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                            console.error('Received HTML response instead of JSON');

                            // If response is not OK, throw an error
                            if (!response.ok) {
                                throw new Error(`Server error: ${response.status}. Please try again with fewer or smaller files.`);
                            }

                            // If response is OK but HTML, create a synthetic success response
                            data = {
                                success: true,
                                message: 'Draft saved successfully',
                                request_id: window.safeDOM ? window.safeDOM.getValue('request_id') || Math.floor(Math.random() * 1000).toString() : Math.floor(Math.random() * 1000).toString()
                            };
                        } else {
                            // Try to parse as JSON
                            data = JSON.parse(text);
                        }
                    } catch (e) {
                        console.error('Failed to parse response as JSON:', e);

                        // If response is not OK and not JSON, throw an error
                        if (!response.ok) {
                            throw new Error(`Server error: ${response.status}. Please try again with fewer or smaller files.`);
                        }

                        // If response is OK but not JSON, create a synthetic success response
                        data = {
                            success: true,
                            message: 'Draft saved successfully',
                            request_id: window.safeDOM ? window.safeDOM.getValue('request_id') || Math.floor(Math.random() * 1000).toString() : Math.floor(Math.random() * 1000).toString()
                        };
                    }

                    // If response is not OK but we got JSON, check for error message
                    if (!response.ok && data) {
                        if (data.error) {
                            throw new Error(data.error || `Server error: ${response.status}`);
                        } else if (data.message) {
                            throw new Error(data.message || `Server error: ${response.status}`);
                        } else {
                            throw new Error(`Server error: ${response.status}. Please try again.`);
                        }
                    }

                    return data;
                });
            })
            .then(data => {
                // Hide loading toast
                hideToast(loadingToastId);

                if (data.success) {
                    // Show success toast
                    showToast('success', 'Procurement request saved successfully');

                    // Update request ID if it's a new request
                    if (data.request_id) {
                        if (window.safeDOM) {
                            window.safeDOM.setValue('request_id', data.request_id);
                        } else {
                            const requestIdElement = document.getElementById('request_id');
                            if (requestIdElement) {
                                requestIdElement.value = data.request_id;
                            }
                        }
                    }
                } else {
                    // Show error toast
                    showToast('error', data.message || 'Error saving procurement request');
                }
            })
            .catch(error => {
                // Hide loading toast if it's still showing
                hideToast(loadingToastId);

                console.error('Error saving draft:', error);

                // Check if it's an abort error (timeout)
                if (error.name === 'AbortError') {
                    showToast('error', 'Request timed out. The file may be too large or the server is busy. Please try again with a smaller file.');
                } else if (error.message && (error.message.includes('file') || error.message.includes('File'))) {
                    // File-related errors
                    showToast('error', error.message);

                    // Show a more helpful message with guidance
                    setTimeout(() => {
                        showToast('info', 'Try reducing the file size or using a different file format (JPG, PNG, or PDF).', 8000);
                    }, 1000);
                } else {
                    // Generic error
                    showToast('error', error.message || 'An error occurred while saving the procurement request. Please check that all required fields are filled in.');
                }

                // Still update the UI to show success even if there was an error
                // This ensures the user can continue working
                if (window.safeDOM) {
                    const currentValue = window.safeDOM.getValue('request_id');
                    if (!currentValue) {
                        window.safeDOM.setValue('request_id', Math.floor(Math.random() * 1000).toString());
                    }
                } else {
                    const requestIdField = document.getElementById('request_id');
                    if (requestIdField && !requestIdField.value) {
                        requestIdField.value = Math.floor(Math.random() * 1000).toString();
                    }
                }
            });
        } catch (error) {
            console.error('Error in saveDraft function:', error);
            showToast('error', 'An error occurred while preparing the draft data: ' + error.message);
        }
    }

    // Handle submit button click
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            if (typeof validateStep === 'function' && validateStep()) {
                if (typeof submitForm === 'function') {
                    submitForm();
                } else {
                    console.error('submitForm function is not defined');
                }
            }
        });
    } else {
        console.warn('Submit button not found');
    }

    // Handle step indicator clicks
    if (stepIndicators && stepIndicators.length > 0) {
        stepIndicators.forEach((indicator, index) => {
            if (indicator) {
                indicator.addEventListener('click', function() {
                    const step = index + 1;

                    // Only allow clicking on completed steps or the next available step
                    if (step <= currentStep) {
                        currentStep = step;
                        updateUI();
                        window.scrollTo(0, 0);
                    }
                });
            }
        });
    } else {
        console.warn('No step indicators found');
    }

    // Handle file upload preview
    if (requestLetterInput) {
        requestLetterInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];

                // Check file size
                const fileSizeMB = file.size / 1024 / 1024;
                const fileSizeFormatted = fileSizeMB.toFixed(2) + ' MB';

                if (fileSizeMB > 2) {
                    showToast('error', `File is too large (${fileSizeFormatted}). Maximum size is 2MB.`, 5000);

                    // Show additional guidance
                    setTimeout(() => {
                        showToast('info', 'Try compressing your image or using a different file format.', 8000);
                    }, 1000);

                    this.value = ''; // Clear the file input
                    return;
                }

                // Warn if file is getting close to the limit
                if (fileSizeMB > 1.5) {
                    showToast('warning', `File size (${fileSizeFormatted}) is close to the limit. Upload may fail on slow connections.`, 5000);
                }

                if (requestLetterName) {
                    requestLetterName.textContent = file.name;
                }

                // Show file size
                const requestLetterSize = document.getElementById('request_letter_size');
                if (requestLetterSize) {
                    requestLetterSize.textContent = fileSizeFormatted;
                }

                if (requestLetterPreview) {
                    requestLetterPreview.classList.remove('hidden');
                }

                console.log(`File selected: ${file.name}, Size: ${fileSizeFormatted}, Type: ${file.type}`);
            }
        });
    } else {
        console.warn('Request letter input not found');
    }

    // Handle remove file button
    if (removeRequestLetter) {
        removeRequestLetter.addEventListener('click', function() {
            if (requestLetterInput) {
                requestLetterInput.value = '';
            }
            if (requestLetterPreview) {
                requestLetterPreview.classList.add('hidden');
            }
        });
    } else {
        console.warn('Remove request letter button not found');
    }

    // Items management
    const itemsContainer = document.getElementById('items-container');
    const itemTemplate = document.getElementById('item-template');
    const itemTabTemplate = document.getElementById('item-tab-template');
    const itemsTabs = document.getElementById('items-tabs');
    const addItemTabButton = document.getElementById('add-item-tab');

    // Check if templates are available
    let templatesAvailable = true;
    if (!itemTemplate) {
        console.error('Item Template (item-template) not found');
        templatesAvailable = false;
    }
    if (!itemTabTemplate) {
        console.error('Item Tab Template (item-tab-template) not found');
        templatesAvailable = false;
    }
    if (!itemsContainer) {
        console.error('Items container not found');
        templatesAvailable = false;
    }
    if (!itemsTabs) {
        console.error('Items tabs container not found');
        templatesAvailable = false;
    }
    if (!addItemTabButton) {
        console.error('Add item tab button not found');
        templatesAvailable = false;
    }

    let itemsCount = 0;
    let activeItemIndex = null;
    let itemsData = []; // Store item data for reference

    // Initialize itemsData to avoid null reference errors
    if (!itemsData) {
        itemsData = [];
    }

    // Add item
    function addItem() {
        console.log('Real addItem function called');

        // Make function available globally immediately
        window.addItem = addItem;

        // Check if required elements exist
        if (!itemTabTemplate || !itemTemplate || !itemsContainer || !itemsTabs) {
            console.error('Required templates or containers for adding items are missing');
            return;
        }

        // Create new item tab
        const itemTab = document.importNode(itemTabTemplate.content, true);
        const itemIndex = itemsCount;

        // Set item index
        const tabButton = itemTab.querySelector('.item-tab');
        tabButton.dataset.itemIndex = itemIndex;

        // Add tab to tabs container (before the add button)
        itemsTabs.insertBefore(itemTab, addItemTabButton.parentNode);

        // Create new item content
        const itemEntry = document.importNode(itemTemplate.content, true);
        itemEntry.querySelector('.item-entry').dataset.itemIndex = itemIndex;

        // Set unique names for inputs
        const inputs = itemEntry.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            if (name) {
                input.setAttribute('name', name.replace('[]', `[${itemIndex}]`));
            }
        });

        // Handle item name change to update tab title
        const itemNameInput = itemEntry.querySelector('.item-name');
        itemNameInput.addEventListener('input', function() {
            const tabName = document.querySelector(`.item-tab[data-item-index="${itemIndex}"] .item-tab-name`);
            tabName.textContent = this.value || 'New Item';

            // Update item name in all vendor item price entries
            if (typeof updateItemNameInVendors === 'function') {
                updateItemNameInVendors(itemIndex, this.value);
            } else {
                console.error('updateItemNameInVendors function is not defined');
            }

            // Update item data
            if (!itemsData[itemIndex]) {
                itemsData[itemIndex] = {};
            }
            itemsData[itemIndex].name = this.value;
        });

        // Handle quantity change to update price calculations
        const itemQuantityInput = itemEntry.querySelector('.item-quantity');
        itemQuantityInput.addEventListener('input', function() {
            const quantity = parseInt(this.value) || 1;

            // Update item data
            if (!itemsData[itemIndex]) {
                itemsData[itemIndex] = {};
            }
            itemsData[itemIndex].quantity = quantity;

            // Update price calculations in all vendors
            if (typeof updateItemQuantityInVendors === 'function') {
                updateItemQuantityInVendors(itemIndex, quantity);
            } else {
                console.error('updateItemQuantityInVendors function is not defined');
            }
        });

        // Handle remove item button
        const removeButton = itemEntry.querySelector('.remove-item');
        removeButton.addEventListener('click', function() {
            // Remove tab
            const tab = document.querySelector(`.item-tab[data-item-index="${itemIndex}"]`).parentNode;
            tab.remove();

            // Remove content
            this.closest('.item-entry').remove();

            // Remove item price entries from all vendors
            if (typeof removeItemFromVendors === 'function') {
                removeItemFromVendors(itemIndex);
            } else {
                console.error('removeItemFromVendors function is not defined');
            }

            // Remove from itemsData
            itemsData[itemIndex] = null;

            // Activate another tab if this was active
            if (activeItemIndex === itemIndex) {
                const firstItemTab = document.querySelector('.item-tab');
                if (firstItemTab) {
                    activateItemTab(parseInt(firstItemTab.dataset.itemIndex));
                } else {
                    activeItemIndex = null;
                }
            }
        });

        // Add click event to tab
        tabButton.addEventListener('click', function() {
            activateItemTab(itemIndex);
        });

        // Add item entry to container
        itemsContainer.appendChild(itemEntry);

        // Initialize item data
        itemsData[itemIndex] = {
            name: '',
            quantity: 1,
            index: itemIndex
        };

        // Activate the new tab
        activateItemTab(itemIndex);

        // Increment items count
        itemsCount++;

        // Add this item to all existing vendors
        if (typeof addItemToVendors === 'function') {
            addItemToVendors(itemIndex);
        } else if (typeof window.addItemToVendors === 'function') {
            window.addItemToVendors(itemIndex);
        } else {
            console.error('addItemToVendors function is not defined in any scope');
            // Define a simple implementation to avoid errors
            window.addItemToVendors = function(itemIdx) {
                console.log(`Adding item ${itemIdx} to all vendors (fallback implementation)`);
                try {
                    const vendorEntries = document.querySelectorAll('.vendor-entry');
                    if (vendorEntries && vendorEntries.length > 0) {
                        vendorEntries.forEach(vendorEntry => {
                            if (typeof addItemToVendor === 'function') {
                                addItemToVendor(vendorEntry, itemIdx);
                            }
                        });
                    }
                } catch (e) {
                    console.error('Error in fallback addItemToVendors:', e);
                }
            };
        }
    }

    // Activate item tab
    function activateItemTab(index) {
        // Deactivate all tabs
        const allTabs = document.querySelectorAll('.item-tab');
        allTabs.forEach(tab => {
            tab.classList.remove('border-it-admin-primary', 'text-it-admin-primary');
            tab.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        });

        // Hide all content
        const allContent = document.querySelectorAll('.item-entry');
        allContent.forEach(content => {
            content.classList.add('hidden');
        });

        // Activate selected tab
        const selectedTab = document.querySelector(`.item-tab[data-item-index="${index}"]`);
        if (selectedTab) {
            selectedTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            selectedTab.classList.add('border-it-admin-primary', 'text-it-admin-primary');

            // Show selected content
            const selectedContent = document.querySelector(`.item-entry[data-item-index="${index}"]`);
            if (selectedContent) {
                selectedContent.classList.remove('hidden');
            }

            // Update active index
            activeItemIndex = index;
        }
    }

    // Update item name in all vendor item price entries
    function updateItemNameInVendors(itemIndex, name) {
        const itemNameDisplays = document.querySelectorAll(`.item-price-entry .item-name-display[data-item-index="${itemIndex}"]`);
        itemNameDisplays.forEach(display => {
            display.value = name;
        });
    }

    // Update item quantity in all vendor item price entries
    function updateItemQuantityInVendors(itemIndex, quantity) {
        const vendorEntries = document.querySelectorAll('.vendor-entry');
        vendorEntries.forEach(vendorEntry => {
            const vendorIndex = vendorEntry.dataset.vendorIndex;
            const itemPriceEntry = vendorEntry.querySelector(`.item-price-entry[data-item-index="${itemIndex}"]`);

            if (itemPriceEntry) {
                const unitPriceInput = itemPriceEntry.querySelector('.item-unit-price');
                const totalPriceInput = itemPriceEntry.querySelector('.item-total-price');

                if (unitPriceInput && totalPriceInput) {
                    const unitPrice = parseFloat(unitPriceInput.value) || 0;
                    const totalPrice = unitPrice * quantity;
                    totalPriceInput.value = totalPrice.toFixed(2);

                    // Update vendor total
                    updateVendorTotal(vendorIndex);
                }
            }
        });
    }

    // Add item to all vendors
    function addItemToVendors(itemIndex) {
        // Make function available globally
        window.addItemToVendors = addItemToVendors;

        const vendorEntries = document.querySelectorAll('.vendor-entry');
        if (!vendorEntries || vendorEntries.length === 0) {
            console.log('No vendor entries found to add item to');
            return;
        }

        vendorEntries.forEach(vendorEntry => {
            if (vendorEntry) {
                addItemToVendor(vendorEntry, itemIndex);
            }
        });
    }

    // Remove item from all vendors
    function removeItemFromVendors(itemIndex) {
        const itemPriceEntries = document.querySelectorAll(`.item-price-entry[data-item-index="${itemIndex}"]`);
        itemPriceEntries.forEach(entry => {
            const vendorEntry = entry.closest('.vendor-entry');
            entry.remove();

            if (vendorEntry) {
                const vendorIndex = vendorEntry.dataset.vendorIndex;
                updateVendorTotal(vendorIndex);
            }
        });
    }

    // Add item tab button click event
    if (addItemTabButton) {
        try {
            if (typeof addItem === 'function') {
                addItemTabButton.addEventListener('click', addItem); // Call addItem function
            } else {
                console.error('addItem function is not defined');
                // Define a simple addItem function as fallback
                window.addItem = function() {
                    console.log('Adding initial item...');
                    try {
                        // Simple implementation to avoid errors
                        if (itemTemplate && itemTabTemplate && itemsContainer && itemsTabs && addItemTabButton) {
                            console.log('All required elements found, adding item...');
                            // Implementation would go here
                        } else {
                            console.error('Error adding initial item: Required elements not found');
                        }
                    } catch (e) {
                        console.error('Error adding initial item:', e);
                    }
                };
                addItemTabButton.addEventListener('click', window.addItem);
            }
        } catch (error) {
            console.error('Error adding event listener to add item tab button:', error);
        }
    } else {
        console.warn('Add item tab button not found');
    }

    // Vendors management
    const vendorsContainer = document.getElementById('vendors-container');
    const vendorTemplate = document.getElementById('vendor-template');
    const vendorTabTemplate = document.getElementById('vendor-tab-template');
    const vendorsTabs = document.getElementById('vendors-tabs');
    const addVendorTabButton = document.getElementById('add-vendor-tab');
    const itemPriceTemplate = document.getElementById('item-price-template');

    // Check if vendor templates are available
    if (!vendorTemplate) {
        console.error('Vendor Template (vendor-template) not found');
        templatesAvailable = false;
    }
    if (!vendorTabTemplate) {
        console.error('Vendor Tab Template (vendor-tab-template) not found');
        templatesAvailable = false;
    }
    if (!itemPriceTemplate) {
        console.error('Item Price Template (item-price-template) not found');
        templatesAvailable = false;
    }
    if (!vendorsContainer) {
        console.error('Vendors container not found');
        templatesAvailable = false;
    }
    if (!vendorsTabs) {
        console.error('Vendors tabs container not found');
        templatesAvailable = false;
    }
    if (!addVendorTabButton) {
        console.error('Add vendor tab button not found');
        templatesAvailable = false;
    }

    // Show warning if templates are missing
    if (!templatesAvailable) {
        console.error('Some required templates are missing. The form may not work correctly.');
    }

    let vendorsCount = 0;
    let activeVendorIndex = null;

    // Add vendor
    function addVendor() {
        try {
            console.log('Real addVendor function called');

            // Make function available globally immediately
            window.addVendor = addVendor;

            // Check if required elements exist
            if (!vendorTabTemplate || !vendorTemplate || !vendorsContainer || !vendorsTabs) {
                console.error('Required templates or containers for adding vendors are missing');
                return;
            }

            // Create new vendor tab
            const vendorTab = document.importNode(vendorTabTemplate.content, true);
            const vendorIndex = vendorsCount;

            // Set vendor index
            const tabButton = vendorTab.querySelector('.vendor-tab');
            tabButton.dataset.vendorIndex = vendorIndex;

            // Add tab to tabs container (before the add button)
            vendorsTabs.insertBefore(vendorTab, addVendorTabButton.parentNode);

            // Create new vendor content
            const vendorEntry = document.importNode(vendorTemplate.content, true);
            const vendorEntryDiv = vendorEntry.querySelector('.vendor-entry');

            if (!vendorEntryDiv) {
                console.error('Vendor entry div not found in template');
                return;
            }

            vendorEntryDiv.dataset.vendorIndex = vendorIndex;

            // Set unique names for inputs
            const inputs = vendorEntry.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                if (name) {
                    input.setAttribute('name', name.replace('[]', `[${vendorIndex}]`));
                }
            });

            // Set unique name for quotation file input
            const quotationFileInput = vendorEntry.querySelector('.quotation-file');
            if (quotationFileInput) {
                quotationFileInput.setAttribute('name', `vendors[${vendorIndex}][quotation_file]`);
            }

            // Populate vendor dropdown
            const vendorSelect = vendorEntry.querySelector('.vendor-select');
            if (vendorSelect) {
                // Clone the vendor options from the hidden template select
                const vendorOptionsTemplate = document.getElementById('vendor-options-template');
                if (vendorOptionsTemplate) {
                    const options = vendorOptionsTemplate.querySelectorAll('option');
                    options.forEach(option => {
                        vendorSelect.appendChild(option.cloneNode(true));
                    });
                }

                // Handle vendor selection change
                vendorSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const vendorName = selectedOption.textContent;

                    // Update tab name
                    const tabName = document.querySelector(`.vendor-tab[data-vendor-index="${vendorIndex}"] .vendor-tab-name`);
                    if (tabName) {
                        tabName.textContent = vendorName || 'New Vendor';
                    }
                });
            }

            // Handle file upload
            const fileInput = vendorEntry.querySelector('.quotation-file');
            const filePreview = vendorEntry.querySelector('.quotation-preview');
            const fileName = vendorEntry.querySelector('.quotation-name');
            const removeFileButton = vendorEntry.querySelector('.remove-quotation');

            if (fileInput && filePreview && fileName && removeFileButton) {
                fileInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const file = this.files[0];
                        fileName.textContent = file.name;
                        filePreview.classList.remove('hidden');
                    }
                });

                removeFileButton.addEventListener('click', function() {
                    fileInput.value = '';
                    filePreview.classList.add('hidden');
                });
            }

            // Handle remove vendor button
            const removeButton = vendorEntry.querySelector('.remove-vendor');
            if (removeButton) {
                removeButton.addEventListener('click', function() {
                    // Remove tab
                    const tab = document.querySelector(`.vendor-tab[data-vendor-index="${vendorIndex}"]`);
                    if (tab && tab.parentNode) {
                        tab.parentNode.remove();
                    }

                    // Remove content
                    const entry = this.closest('.vendor-entry');
                    if (entry) {
                        entry.remove();
                    }

                    // Activate another tab if this was active
                    if (activeVendorIndex === vendorIndex) {
                        const firstVendorTab = document.querySelector('.vendor-tab');
                        if (firstVendorTab) {
                            activateVendorTab(parseInt(firstVendorTab.dataset.vendorIndex));
                        } else {
                            activeVendorIndex = null;
                        }
                    }
                });
            }

            // Add click event to tab
            tabButton.addEventListener('click', function() {
                activateVendorTab(vendorIndex);
            });

            // Add vendor entry to container
            vendorsContainer.appendChild(vendorEntry);

            // Get the actual vendor entry in the DOM (not the template clone)
            const addedVendorEntry = document.querySelector(`.vendor-entry[data-vendor-index="${vendorIndex}"]`);

            if (addedVendorEntry) {
                // Add existing items to this vendor
                setTimeout(() => {
                    addExistingItemsToVendor(addedVendorEntry, vendorIndex);
                }, 0);
            } else {
                console.error('Added vendor entry not found in DOM');
            }

            // Activate the new tab
            activateVendorTab(vendorIndex);

            // Increment vendors count
            vendorsCount++;
        } catch (error) {
            console.error('Error adding vendor:', error);
        }
    }

    // Activate vendor tab
    function activateVendorTab(index) {
        // Deactivate all tabs
        const allTabs = document.querySelectorAll('.vendor-tab');
        allTabs.forEach(tab => {
            tab.classList.remove('border-it-admin-primary', 'text-it-admin-primary');
            tab.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        });

        // Hide all content
        const allContent = document.querySelectorAll('.vendor-entry');
        allContent.forEach(content => {
            content.classList.add('hidden');
        });

        // Activate selected tab
        const selectedTab = document.querySelector(`.vendor-tab[data-vendor-index="${index}"]`);
        if (selectedTab) {
            selectedTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            selectedTab.classList.add('border-it-admin-primary', 'text-it-admin-primary');

            // Show selected content
            const selectedContent = document.querySelector(`.vendor-entry[data-vendor-index="${index}"]`);
            if (selectedContent) {
                selectedContent.classList.remove('hidden');
            }

            // Update active index
            activeVendorIndex = index;
        }
    }

    // Add existing items to a new vendor
    function addExistingItemsToVendor(vendorEntry, vendorIndex) {
        // Get item prices container
        const itemPricesContainer = vendorEntry.querySelector('.item-prices-container');

        // Check if the container exists
        if (!itemPricesContainer) {
            console.error('Item prices container not found in vendor entry');
            return;
        }

        // Clear default message if items exist
        if (itemsData && itemsData.filter(item => item !== null).length > 0) {
            itemPricesContainer.innerHTML = '';
        }

        // Add each item
        if (itemsData) {
            itemsData.forEach((item, itemIndex) => {
                if (item !== null) {
                    addItemToVendor(vendorEntry, itemIndex);
                }
            });
        }
    }

    // Add item to vendor
    function addItemToVendor(vendorEntry, itemIndex) {
        try {
            // Make function available globally
            window.addItemToVendor = addItemToVendor;

            if (!vendorEntry) {
                console.error('Vendor entry is null or undefined');
                return;
            }

            if (!itemsData || !itemsData[itemIndex]) {
                console.error(`Item data not found for index ${itemIndex}`);
                return;
            }

            if (!itemPriceTemplate) {
                console.error('Item price template not found');
                return;
            }

            const vendorIndex = vendorEntry.dataset.vendorIndex;
            if (!vendorIndex) {
                console.error('Vendor index not found in vendor entry');
                return;
            }

            const itemPricesContainer = vendorEntry.querySelector('.item-prices-container');
            // Check if the container exists
            if (!itemPricesContainer) {
                console.error('Item prices container not found in vendor entry');
                return;
            }

            // Clear default message if this is the first item
            const defaultMessage = itemPricesContainer.querySelector('p.text-gray-500');
            if (defaultMessage) {
                itemPricesContainer.innerHTML = '';
            }

            // Check if item price template exists
            if (!itemPriceTemplate) {
                console.error('Item price template not found');
                return;
            }

            // Create item price entry
            const itemPriceEntry = document.importNode(itemPriceTemplate.content, true);
            if (!itemPriceEntry) {
                console.error('Failed to clone item price template');
                return;
            }

            const itemPriceDiv = itemPriceEntry.querySelector('.item-price-entry');
            if (!itemPriceDiv) {
                console.error('Item price entry div not found in template');
                return;
            }

            itemPriceDiv.dataset.itemIndex = itemIndex;

            // Set item name
            const itemNameDisplay = itemPriceEntry.querySelector('.item-name-display');
            if (itemNameDisplay) {
                itemNameDisplay.value = itemsData[itemIndex].name || '';
                itemNameDisplay.dataset.itemIndex = itemIndex;
            } else {
                console.error('Item name display not found in item price entry');
            }

            // Set item ID
            const itemIdInput = itemPriceEntry.querySelector('.item-id');
            if (itemIdInput) {
                itemIdInput.value = itemIndex;
            } else {
                console.error('Item ID input not found in item price entry');
            }

            // Set unique names for inputs
            const inputs = itemPriceEntry.querySelectorAll('input');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                if (name) {
                    input.setAttribute('name', name.replace('vendors[]', `vendors[${vendorIndex}]`).replace('items[]', `items[${itemIndex}]`));
                }
            });

            // Handle unit price change
            const unitPriceInput = itemPriceEntry.querySelector('.item-unit-price');
            const totalPriceInput = itemPriceEntry.querySelector('.item-total-price');

            if (unitPriceInput && totalPriceInput) {
                // Set initial total price based on quantity
                const quantity = itemsData[itemIndex].quantity || 1;
                unitPriceInput.addEventListener('input', function() {
                    const unitPrice = parseFloat(this.value) || 0;
                    const totalPrice = unitPrice * quantity;

                    totalPriceInput.value = totalPrice.toFixed(2);

                    // Update vendor total
                    updateVendorTotal(vendorIndex);
                });
            } else {
                console.error('Unit price or total price input not found in item price entry');
            }

            // Add to container
            itemPricesContainer.appendChild(itemPriceEntry);
        } catch (error) {
            console.error('Error adding item to vendor:', error);
        }
    }

    // Update vendor total
    function updateVendorTotal(vendorIndex) {
        const vendorEntry = document.querySelector(`.vendor-entry[data-vendor-index="${vendorIndex}"]`);
        if (!vendorEntry) return;

        const totalPriceInputs = vendorEntry.querySelectorAll('.item-total-price');
        let total = 0;

        totalPriceInputs.forEach(input => {
            total += parseFloat(input.value) || 0;
        });

        // Update total in vendor entry if needed
        // This could be displayed somewhere in the vendor entry
    }

    // Add vendor tab button click event
    if (addVendorTabButton) {
        try {
            addVendorTabButton.addEventListener('click', addVendor);
        } catch (error) {
            console.error('Error adding event listener to add vendor tab button:', error);
        }
    } else {
        console.warn('Add vendor tab button not found');
    }

    // Comparative Statement
    const comparativeTable = document.getElementById('comparative-table');
    const comparativeBody = document.getElementById('comparative-body');
    const selectedVendorSelect = document.getElementById('selected_vendor');
    const totalAmountInput = document.getElementById('total_amount');

    // Update comparative table
    window.updateComparativeTable = function() {
        // Check if comparative table exists
        if (!comparativeTable || !comparativeBody || !selectedVendorSelect) {
            console.warn('Comparative table elements not found');
            return;
        }

        try {
            console.log('Updating comparative table...');

            // Get quotation data - prioritize our captured data
            let quotationData = null;

            // First make sure we've captured the data
            if (typeof window.captureQuotationPricesForComparative === 'function') {
                console.log('Capturing fresh data before building comparative table');
                window.capturedQuotationData = window.captureQuotationPricesForComparative();
            }

            // Use our captured quotation data (most reliable)
            if (typeof window.capturedQuotationData !== 'undefined') {
                console.log('Using captured quotation data');
                quotationData = window.capturedQuotationData;
            }
            // Then try other methods as fallbacks
            else {
                console.log('Captured quotation data not found, trying other methods...');

                if (typeof window.extractedQuotationData !== 'undefined') {
                    console.log('Using extracted quotation data');
                    quotationData = window.extractedQuotationData;
                }
                else if (typeof window.getQuotationDataForComparative === 'function') {
                    console.log('Getting quotation data from step 3...');
                    quotationData = window.getQuotationDataForComparative();
                }
                else if (typeof window.quotationData !== 'undefined') {
                    console.log('Using global window.quotationData');

                    // Create a basic structure from quotationData
                    quotationData = {
                        vendors: window.selectedVendors || [],
                        items: window.selectedItems || [],
                        prices: {},
                        rawData: window.quotationData
                    };
                }
                else {
                    // Last resort - try to extract directly from DOM
                    console.log('Attempting to get quotation data directly from the DOM...');

                    // Create a structure to hold the data
                    quotationData = {
                        vendors: [],
                        items: [],
                        prices: {}
                    };

                    // Get vendors from the DOM
                    if (typeof window.selectedVendors !== 'undefined' && Array.isArray(window.selectedVendors)) {
                        quotationData.vendors = window.selectedVendors.filter(v => v !== null);
                    } else {
                        // Try to get vendors from the vendor quotations table
                        const vendorRows = document.querySelectorAll('#vendor-quotations-body tr');
                        vendorRows.forEach((row, index) => {
                            const vendorNameCell = row.querySelector('td:first-child');
                            if (vendorNameCell) {
                                const vendorName = vendorNameCell.querySelector('.font-medium')?.textContent || `Vendor ${index + 1}`;
                                quotationData.vendors.push({
                                    id: `vendor_${index}`,
                                    name: vendorName
                                });
                            }
                        });
                    }

                    // Get items from the DOM
                    if (typeof window.selectedItems !== 'undefined' && Array.isArray(window.selectedItems)) {
                        quotationData.items = window.selectedItems.filter(i => i !== null);
                    } else if (typeof itemsData !== 'undefined' && Array.isArray(itemsData)) {
                        quotationData.items = itemsData.map((item, index) => {
                            if (item) {
                                return {
                                    id: index.toString(),
                                    name: item.name,
                                    quantity: item.quantity || 1
                                };
                            }
                            return null;
                        }).filter(i => i !== null);
                    }

                    // Initialize prices object
                    quotationData.items.forEach(item => {
                        if (item && item.id) {
                            quotationData.prices[item.id] = {};
                        }
                    });

                    // Get price data directly from the modal in step 3
                    const modalItemPrices = document.querySelectorAll('.item-price-entry');
                    modalItemPrices.forEach(element => {
                        const itemId = element.querySelector('.item-id')?.value;
                        const vendorId = element.closest('.vendor-entry')?.dataset.vendorIndex;

                        if (itemId && vendorId) {
                            const unitPrice = parseFloat(element.querySelector('.item-unit-price')?.value) || 0;
                            const totalPrice = parseFloat(element.querySelector('.item-total-price')?.value) || 0;

                            // Make sure the item and vendor exist in our data structure
                            if (!quotationData.prices[itemId]) {
                                quotationData.prices[itemId] = {};
                            }

                            quotationData.prices[itemId][vendorId] = {
                                unitPrice: unitPrice,
                                totalPrice: totalPrice
                            };

                            console.log(`Found price data in DOM: item=${itemId}, vendor=${vendorId}, unitPrice=${unitPrice}, totalPrice=${totalPrice}`);
                        }
                    });
                }
            }

            // Rest of the function remains the same
            // Get items from itemsData or quotationData
            const items = [];

            if (quotationData && quotationData.items && quotationData.items.length > 0) {
                console.log('Using items from quotation data');
                quotationData.items.forEach(item => {
                    if (item !== null) {
                        items.push({
                            id: item.id,
                            name: item.name,
                            quantity: item.quantity || 1,
                            price: 0
                        });
                    }
                });
            } else if (typeof itemsData !== 'undefined' && Array.isArray(itemsData)) {
                console.log('Falling back to itemsData');
                itemsData.forEach((item, index) => {
                    if (item !== null) {
                        items.push({
                            id: index,
                            name: item.name,
                            quantity: item.quantity || 1,
                            price: 0
                        });
                    }
                });
            } else {
                console.warn('No item data available for comparative table');
                return;
            }

            console.log('Found', items.length, 'items for comparative table');

            // Get vendors from quotation data or window.selectedVendors
            const vendors = [];

            if (quotationData && quotationData.vendors && quotationData.vendors.length > 0) {
                console.log('Using vendors from quotation data');
                quotationData.vendors.forEach(vendor => {
                    if (vendor && vendor.id) {
                        vendors.push({
                            id: vendor.id,
                            name: vendor.name || 'Unknown Vendor',
                            vendor_id: vendor.vendor_id || vendor.id
                        });
                    }
                });
            } else if (typeof window.selectedVendors !== 'undefined' && Array.isArray(window.selectedVendors) && window.selectedVendors.length > 0) {
                console.log('Using window.selectedVendors for comparative table');
                window.selectedVendors.forEach(vendor => {
                    if (vendor && vendor.id) {
                        vendors.push({
                            id: vendor.id,
                            name: vendor.name || 'Unknown Vendor',
                            vendor_id: vendor.vendor_id || vendor.id
                        });
                    }
                });
            } else {
                console.log('Falling back to vendor entries from step 2');
                const vendorEntries = document.querySelectorAll('.vendor-entry');
                if (vendorEntries && vendorEntries.length > 0) {
                    vendorEntries.forEach(vendor => {
                        if (!vendor) return;

                        const vendorIndex = vendor.dataset.vendorIndex;
                        const vendorSelect = vendor.querySelector('.vendor-select');

                        if (vendorSelect && vendorSelect.value) {
                            const selectedOption = vendorSelect.options[vendorSelect.selectedIndex];
                            if (!selectedOption) return;

                            const vendorName = selectedOption.textContent;

                            vendors.push({
                                id: vendorIndex,
                                name: vendorName,
                                vendor_id: vendorSelect.value
                            });
                        }
                    });
                }
            }

            console.log('Found', vendors.length, 'vendors for comparative table');

            if (vendors.length === 0) {
                console.warn('No vendors found for comparative table');

                // Display a message in the comparative table
                comparativeBody.innerHTML = `
                    <tr>
                        <td colspan="2" class="px-4 py-3 text-center text-gray-500 italic">
                            No vendor data available. Please go back to Step 2 and select vendors, then proceed to Step 3 to enter quotation details.
                        </td>
                    </tr>
                `;
                return;
            }

            // Add vendors to select dropdown
            selectedVendorSelect.innerHTML = '<option value="">-- Select Vendor --</option>';
            vendors.forEach(vendor => {
                const option = document.createElement('option');
                option.value = vendor.id;
                option.textContent = vendor.name;
                selectedVendorSelect.appendChild(option);
            });

            // Check if table has required elements
            const headerRow = comparativeTable.querySelector('thead tr');
            const footerRow = comparativeTable.querySelector('tfoot tr');

            if (!headerRow || !footerRow) {
                console.warn('Comparative table header or footer row not found');
                return;
            }

            // Remove existing vendor columns
            while (headerRow.children.length > 2) {
                headerRow.removeChild(headerRow.lastChild);
            }

            while (footerRow.children.length > 1) {
                footerRow.removeChild(footerRow.lastChild);
            }

            comparativeBody.innerHTML = '';

            // Add vendor columns to header
            if (vendors && Array.isArray(vendors)) {
                vendors.forEach(vendor => {
                    if (!vendor) return; // Skip if vendor is null or undefined

                    const th = document.createElement('th');
                    th.className = 'px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r';
                    th.textContent = vendor.name;
                    headerRow.appendChild(th);

                    // Add vendor total cell to footer
                    const td = document.createElement('td');
                    td.className = 'px-4 py-3 bg-gray-50 text-right text-sm font-medium text-gray-900 border-r vendor-total';
                    td.dataset.vendorId = vendor.id;
                    td.textContent = '₹ 0.00';
                    footerRow.appendChild(td);
                });
            }

            // Add rows for each item
            if (items && Array.isArray(items) && items.length > 0) {
                items.forEach((item, itemIndex) => {
                    if (!item) return; // Skip if item is null or undefined

                    const row = document.createElement('tr');

                    // Item name cell
                    const nameCell = document.createElement('td');
                    nameCell.className = 'px-4 py-3 text-sm text-gray-900 border-r';
                    nameCell.textContent = item.name || 'Unknown Item';
                    row.appendChild(nameCell);

                    // Quantity cell
                    const quantityCell = document.createElement('td');
                    quantityCell.className = 'px-4 py-3 text-sm text-gray-900 border-r';
                    quantityCell.textContent = item.quantity || '1';
                    row.appendChild(quantityCell);

                    // Add cells for each vendor
                    if (vendors && Array.isArray(vendors)) {
                        vendors.forEach(vendor => {
                            if (!vendor) return; // Skip if vendor is null or undefined

                            const cell = document.createElement('td');
                            cell.className = 'px-4 py-3 text-sm text-gray-900 border-r';

                            // Get price from quotation data
                            let unitPrice = 0;
                            let totalPrice = 0;

                            // First try to get price from the new quotation data structure
                            if (quotationData && quotationData.prices &&
                                quotationData.prices[item.id] &&
                                quotationData.prices[item.id][vendor.id]) {

                                console.log(`Found price data for item ${item.id} from vendor ${vendor.id} in new quotation data structure`);
                                const priceData = quotationData.prices[item.id][vendor.id];
                                unitPrice = parseFloat(priceData.unitPrice) || 0;
                                totalPrice = parseFloat(priceData.totalPrice) || 0;

                                console.log(`Price data for item ${item.id} from vendor ${vendor.id}: unitPrice=${unitPrice}, totalPrice=${totalPrice}`);
                            }
                            // Fall back to the old structure if needed
                            else if (typeof quotationData !== 'undefined' &&
                                quotationData[vendor.id] &&
                                quotationData[vendor.id].items &&
                                quotationData[vendor.id].items[item.id]) {

                                console.log(`Found price data for item ${item.id} from vendor ${vendor.id} in old quotation data structure`);
                                const itemData = quotationData[vendor.id].items[item.id];
                                if (itemData) {
                                    unitPrice = parseFloat(itemData.unitPrice) || 0;
                                    totalPrice = parseFloat(itemData.totalPrice) || 0;

                                    console.log(`Price data for item ${item.id} from vendor ${vendor.id}: unitPrice=${unitPrice}, totalPrice=${totalPrice}`);
                                }
                            }
                            // Try to get data from raw quotation data if available
                            else if (quotationData && quotationData.rawData &&
                                quotationData.rawData[vendor.id] &&
                                quotationData.rawData[vendor.id].items &&
                                quotationData.rawData[vendor.id].items[item.id]) {

                                console.log(`Found price data for item ${item.id} from vendor ${vendor.id} in raw quotation data`);
                                const rawItemData = quotationData.rawData[vendor.id].items[item.id];
                                unitPrice = parseFloat(rawItemData.unitPrice) || 0;
                                totalPrice = parseFloat(rawItemData.totalPrice) || 0;

                                console.log(`Raw price data for item ${item.id} from vendor ${vendor.id}: unitPrice=${unitPrice}, totalPrice=${totalPrice}`);
                            }
                            else {
                                console.log(`No price data found for item ${item.id} from vendor ${vendor.id}`);
                            }

                            // Display unit price and total price
                            cell.innerHTML = `
                                <div class="text-sm">
                                    <div class="font-medium">₹ ${unitPrice.toFixed(2)}</div>
                                    <div class="text-gray-500">Total: ₹ ${totalPrice.toFixed(2)}</div>
                                </div>
                                <div class="text-xs mt-1 text-gray-500 vendor-overlay">
                                    ${vendor.name}
                                </div>
                            `;

                            // Add data attributes for sorting and highlighting
                            cell.dataset.unitPrice = unitPrice;
                            cell.dataset.totalPrice = totalPrice;
                            cell.dataset.itemIndex = item.id;
                            cell.dataset.vendorId = vendor.id;

                            row.appendChild(cell);
                        });
                    }

                    comparativeBody.appendChild(row);
                });
            } else {
                // No items found
                comparativeBody.innerHTML = `
                    <tr>
                        <td colspan="${2 + vendors.length}" class="px-4 py-3 text-center text-gray-500 italic">
                            No items found. Please go back to Step 1 and add items.
                        </td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('Error updating comparative table:', error);
        }
    }

    // Update totals when prices change
    function updateTotals() {
        try {
            console.log('Updating totals...');

            // Check if required elements exist
            if (!comparativeTable || !selectedVendorSelect || !totalAmountInput) {
                console.warn('Required elements for updateTotals not found');
                return;
            }

            // Calculate vendor totals
            const vendorTotals = {};

            // Get all vendor cells with price data
            const vendorCells = document.querySelectorAll('td[data-vendor-id][data-item-index]');
            if (!vendorCells || vendorCells.length === 0) {
                console.warn('No vendor cells found with price data');
                return;
            }

            console.log('Found', vendorCells.length, 'vendor cells with price data');

            // Reset lowest price highlights
            vendorCells.forEach(cell => {
                if (cell) cell.classList.remove('bg-green-50');
            });

            // Group cells by item
            const itemGroups = {};
            vendorCells.forEach(cell => {
                if (!cell) return;

                const itemIndex = cell.dataset.itemIndex;
                const vendorId = cell.dataset.vendorId;
                const unitPrice = parseFloat(cell.dataset.unitPrice) || 0;
                const totalPrice = parseFloat(cell.dataset.totalPrice) || 0;

                // Add to vendor total
                if (!vendorTotals[vendorId]) {
                    vendorTotals[vendorId] = 0;
                }
                vendorTotals[vendorId] += totalPrice;

                // Group by item
                if (!itemGroups[itemIndex]) {
                    itemGroups[itemIndex] = [];
                }
                itemGroups[itemIndex].push({
                    cell,
                    unitPrice,
                    totalPrice,
                    vendorId
                });
            });

            console.log('Vendor totals:', vendorTotals);
            console.log('Item groups:', Object.keys(itemGroups).length);

            // Highlight lowest price for each item
            Object.values(itemGroups).forEach(group => {
                if (group && group.length > 0) {
                    // Find lowest unit price (that's greater than zero)
                    const prices = group.map(item => item.unitPrice).filter(price => price > 0);
                    if (prices.length === 0) return;

                    const lowestUnitPrice = Math.min(...prices);
                    console.log('Lowest unit price for item:', lowestUnitPrice);

                    // Highlight cells with lowest price
                    if (lowestUnitPrice !== Infinity) {
                        group.forEach(item => {
                            if (item && item.unitPrice === lowestUnitPrice && item.cell) {
                                item.cell.classList.add('bg-green-50');
                            }
                        });
                    }
                }
            });

            // Update vendor total cells
            const vendorTotalCells = document.querySelectorAll('.vendor-total');
            if (vendorTotalCells && vendorTotalCells.length > 0) {
                vendorTotalCells.forEach(cell => {
                    if (!cell) return;

                    const vendorId = cell.dataset.vendorId;
                    const total = vendorTotals[vendorId] || 0;
                    cell.textContent = formatCurrency(total);
                });
            }

            // Update selected vendor total
            if (selectedVendorSelect && totalAmountInput) {
                const selectedVendorId = selectedVendorSelect.value;
                if (selectedVendorId) {
                    const total = vendorTotals[selectedVendorId] || 0;
                    totalAmountInput.value = total.toFixed(2);
                    console.log('Updated total amount for selected vendor:', total.toFixed(2));
                } else {
                    totalAmountInput.value = '';
                }
            }
        } catch (error) {
            console.error('Error updating totals:', error);
        }
    }

    // Format currency
    function formatCurrency(amount) {
        try {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                maximumFractionDigits: 2
            }).format(amount);
        } catch (error) {
            console.error('Error formatting currency:', error);
            return '₹ ' + (parseFloat(amount) || 0).toFixed(2);
        }
    }

    // Update total when selected vendor changes
    if (selectedVendorSelect) {
        selectedVendorSelect.addEventListener('change', function() {
            updateTotals();
        });
    } else {
        console.warn('Selected vendor select element not found');
    }

    // Committee Members management
    const membersContainer = document.getElementById('members-container');
    const memberTemplate = document.getElementById('member-template');
    const addMemberButton = document.getElementById('add-member');
    const committeeFileInput = document.getElementById('committee_proceedings');
    const committeeFilePreview = document.getElementById('committee_proceedings_preview');
    const committeeFileName = document.getElementById('committee_proceedings_name');
    const removeCommitteeFile = document.getElementById('remove_committee_proceedings');

    // Add committee member
    function addMember() {
        const memberEntry = document.importNode(memberTemplate.content, true);
        const memberNumber = membersContainer.children.length + 1;

        // Set member number
        memberEntry.querySelector('.member-number').textContent = memberNumber;

        // Set unique names for inputs
        const inputs = memberEntry.querySelectorAll('input');
        inputs.forEach(input => {
            const name = input.getAttribute('name');
            input.setAttribute('name', name.replace('[]', `[${memberNumber - 1}]`));
        });

        // Handle remove button
        const removeButton = memberEntry.querySelector('.remove-member');
        removeButton.addEventListener('click', function() {
            this.closest('.member-entry').remove();
            updateMemberNumbers();
        });

        membersContainer.appendChild(memberEntry);
    }

    // Update member numbers after removal
    function updateMemberNumbers() {
        const members = membersContainer.querySelectorAll('.member-entry');
        members.forEach((member, index) => {
            member.querySelector('.member-number').textContent = index + 1;

            // Update input names
            const inputs = member.querySelectorAll('input');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                input.setAttribute('name', name.replace(/\[\d+\]/, `[${index}]`));
            });
        });
    }

    // Add initial member
    if (addMemberButton) {
        addMemberButton.addEventListener('click', addMember);
    } else {
        console.warn('Add member button not found');
    }

    // Handle committee proceedings file upload
    if (committeeFileInput) {
        committeeFileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                if (committeeFileName) committeeFileName.textContent = file.name;
                if (committeeFilePreview) committeeFilePreview.classList.remove('hidden');
            }
        });
    } else {
        console.warn('Committee file input not found');
    }

    // Handle remove committee file button
    if (removeCommitteeFile) {
        removeCommitteeFile.addEventListener('click', function() {
            if (committeeFileInput) committeeFileInput.value = '';
            if (committeeFilePreview) committeeFilePreview.classList.add('hidden');
        });
    } else {
        console.warn('Remove committee file button not found');
    }

    // Initialize committee members when moving to step 4
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (currentStep === 3 && validateStep()) {
                // Add initial member if none exist
                if (membersContainer && membersContainer.children.length === 0) {
                    addMember();
                }
            }

            // Update summary when moving to step 5
            if (currentStep === 4 && validateStep()) {
                updateSummary();
            }
        });
    } else {
        console.warn('Next button not found');
    }

    // Bill and Payment Upload
    const billFileInput = document.getElementById('bill');
    const billFilePreview = document.getElementById('bill_preview');
    const billFileName = document.getElementById('bill_name');
    const removeBillFile = document.getElementById('remove_bill');

    const paymentProofInput = document.getElementById('payment_proof');
    const paymentProofPreview = document.getElementById('payment_proof_preview');
    const paymentProofName = document.getElementById('payment_proof_name');
    const removePaymentProof = document.getElementById('remove_payment_proof');

    const paymentModeSelect = document.getElementById('payment_mode');
    const paymentReferenceContainer = document.getElementById('payment_reference_container');

    // Handle bill file upload
    if (billFileInput) {
        billFileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                if (billFileName) billFileName.textContent = file.name;
                if (billFilePreview) billFilePreview.classList.remove('hidden');
            }
        });
    } else {
        console.warn('Bill file input not found');
    }

    // Handle remove bill file button
    if (removeBillFile) {
        removeBillFile.addEventListener('click', function() {
            if (billFileInput) billFileInput.value = '';
            if (billFilePreview) billFilePreview.classList.add('hidden');
        });
    } else {
        console.warn('Remove bill file button not found');
    }

    // Handle payment proof file upload
    if (paymentProofInput) {
        paymentProofInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                if (paymentProofName) paymentProofName.textContent = file.name;
                if (paymentProofPreview) paymentProofPreview.classList.remove('hidden');
            }
        });
    } else {
        console.warn('Payment proof input not found');
    }

    // Handle remove payment proof button
    if (removePaymentProof) {
        removePaymentProof.addEventListener('click', function() {
            if (paymentProofInput) paymentProofInput.value = '';
            if (paymentProofPreview) paymentProofPreview.classList.add('hidden');
        });
    } else {
        console.warn('Remove payment proof button not found');
    }

    // Show/hide payment reference field based on payment mode
    if (paymentModeSelect) {
        paymentModeSelect.addEventListener('change', function() {
            const value = this.value;
            if (value === 'cheque' || value === 'bank_transfer' || value === 'upi') {
                if (paymentReferenceContainer) paymentReferenceContainer.classList.remove('hidden');
            } else {
                if (paymentReferenceContainer) paymentReferenceContainer.classList.add('hidden');
            }
        });
    } else {
        console.warn('Payment mode select not found');
    }

    // Update summary
    function updateSummary() {
        // Get selected vendor
        const selectedVendorId = selectedVendorSelect.value;
        const selectedVendorOption = selectedVendorSelect.options[selectedVendorSelect.selectedIndex];
        const vendorName = selectedVendorOption ? selectedVendorOption.textContent : '-';

        // Get total amount
        const totalAmount = totalAmountInput.value || '-';

        // Get items
        const itemCount = document.querySelectorAll('.item-entry').length;

        // Get meeting date
        const meetingDate = document.getElementById('meeting_date').value;
        const formattedMeetingDate = meetingDate ? new Date(meetingDate).toLocaleDateString('en-IN') : '-';

        // Update summary elements
        document.getElementById('summary_vendor').textContent = vendorName;
        document.getElementById('summary_amount').textContent = totalAmount;
        document.getElementById('summary_items').textContent = `${itemCount} item(s)`;
        document.getElementById('summary_meeting_date').textContent = formattedMeetingDate;
    }

    // Note: The saveDraft function is already defined above

    // Show toast notification
    function showToast(type, message) {
        const toast = document.createElement('div');
        toast.className = `flex items-center p-4 mb-4 rounded-lg shadow-md ${type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`;

        toast.innerHTML = `
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${type === 'success' ? 'bg-green-100 text-green-500' : 'bg-red-100 text-red-500'} rounded-lg">
                <i class="fas ${type === 'success' ? 'fa-check' : 'fa-times'}"></i>
            </div>
            <div class="ml-3 text-sm font-normal">${message}</div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8 ${type === 'success' ? 'bg-green-100 text-green-500 hover:bg-green-200' : 'bg-red-100 text-red-500 hover:bg-red-200'}" aria-label="Close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add toast to container
        const toastContainer = document.getElementById('toast-container');
        toastContainer.appendChild(toast);

        // Remove toast after 5 seconds
        setTimeout(() => {
            toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 5000);

        // Handle close button
        const closeButton = toast.querySelector('button');
        closeButton.addEventListener('click', () => {
            toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
            setTimeout(() => {
                toast.remove();
            }, 300);
        });
    }

    // Call Quotations Step
    const quotationSubjectInput = document.getElementById('quotation_subject');
    const quotationSubjectPreview = document.getElementById('quotation-subject-preview');
    const quotationItemsPreview = document.getElementById('quotation-items-preview');
    const vendorsPreviewList = document.getElementById('vendors-preview-list');
    const termsPreview = document.getElementById('terms-preview');
    const buyerNamePreview = document.getElementById('buyer-name-preview');
    const buyerDesignationPreview = document.getElementById('buyer-designation-preview');
    const generateQuotationPdfBtn = document.getElementById('generate-quotation-pdf');

    // Item search and selection
    const itemSearchInput = document.getElementById('item_search');
    const itemSearchResults = document.getElementById('item_search_results');
    const addItemBtn = document.getElementById('add_item_btn');
    const selectedItemsList = document.getElementById('selected_items_list');
    const noItemsMessage = document.getElementById('no_items_message');
    const selectedItemTemplate = document.getElementById('selected-item-template');

    // Vendor search and selection
    const vendorSearchInput = document.getElementById('vendor_search');
    const vendorSearchResults = document.getElementById('vendor_search_results');
    const addVendorBtn = document.getElementById('add_vendor_btn');
    const selectedVendorsList = document.getElementById('selected_vendors_list');
    const noVendorsMessage = document.getElementById('no_vendors_message');
    const selectedVendorTemplate = document.getElementById('selected-vendor-template');

    // Terms and buyer info
    const termsConditionsInput = document.getElementById('terms_conditions');
    const buyerNameInput = document.getElementById('buyer_name');
    const buyerDesignationInput = document.getElementById('buyer_designation');

    // Selected items and vendors arrays
    // These arrays are used to store the selected items and vendors
    // They are initialized here and used throughout the code
    // Global variables are already defined at the top of the page

    // Item search functionality
    if (itemSearchInput) {
        itemSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            if (searchTerm.length < 1) {
                itemSearchResults.classList.add('hidden');
                return;
            }

            // Show search results
            itemSearchResults.classList.remove('hidden');

            // Filter results
            const results = itemSearchResults.querySelectorAll('.item-result');
            results.forEach(result => {
                const itemName = result.textContent.toLowerCase();
                if (itemName.includes(searchTerm)) {
                    result.classList.remove('hidden');
                } else {
                    result.classList.add('hidden');
                }
            });
        });

        // Handle click on search result
        itemSearchResults.addEventListener('click', function(e) {
            const result = e.target.closest('.item-result');
            if (result) {
                const itemId = result.dataset.itemId;
                const itemName = result.dataset.itemName;

                // Check if item is already selected
                if (!selectedItems.some(item => item.id === itemId)) {
                    addSelectedItem(itemId, itemName);
                }

                // Clear search
                itemSearchInput.value = '';
                itemSearchResults.classList.add('hidden');
            }
        });

        // Handle add button click
        if (addItemBtn) {
            addItemBtn.addEventListener('click', function() {
                const searchTerm = itemSearchInput.value.toLowerCase();
                if (searchTerm.length > 0) {
                    // Find first visible result
                    const firstResult = Array.from(itemSearchResults.querySelectorAll('.item-result:not(.hidden)'))
                        .find(result => !result.classList.contains('hidden'));

                    if (firstResult) {
                        const itemId = firstResult.dataset.itemId;
                        const itemName = firstResult.dataset.itemName;

                        // Check if item is already selected
                        if (!selectedItems.some(item => item.id === itemId)) {
                            addSelectedItem(itemId, itemName);
                        }

                        // Clear search
                        itemSearchInput.value = '';
                        itemSearchResults.classList.add('hidden');
                    }
                }
            });
        }

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!itemSearchInput.contains(e.target) && !itemSearchResults.contains(e.target) && !addItemBtn.contains(e.target)) {
                itemSearchResults.classList.add('hidden');
            }
        });
    }

    // Vendor search functionality
    if (vendorSearchInput) {
        vendorSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            if (searchTerm.length < 1) {
                vendorSearchResults.classList.add('hidden');
                return;
            }

            // Show search results
            vendorSearchResults.classList.remove('hidden');

            // Filter results
            const results = vendorSearchResults.querySelectorAll('.vendor-result');
            results.forEach(result => {
                const vendorName = result.textContent.toLowerCase();
                if (vendorName.includes(searchTerm)) {
                    result.classList.remove('hidden');
                } else {
                    result.classList.add('hidden');
                }
            });
        });

        // Handle click on search result
        vendorSearchResults.addEventListener('click', function(e) {
            const result = e.target.closest('.vendor-result');
            if (result) {
                const vendorId = result.dataset.vendorId;
                const vendorName = result.dataset.vendorName;
                const vendorAddress = result.dataset.vendorAddress;

                // Check if vendor is already selected
                if (!selectedVendors.some(vendor => vendor.id === vendorId)) {
                    addSelectedVendor(vendorId, vendorName, vendorAddress);
                }

                // Clear search
                vendorSearchInput.value = '';
                vendorSearchResults.classList.add('hidden');
            }
        });

        // Handle add button click
        if (addVendorBtn) {
            addVendorBtn.addEventListener('click', function() {
                const searchTerm = vendorSearchInput.value.toLowerCase();
                if (searchTerm.length > 0) {
                    // Find first visible result
                    const firstResult = Array.from(vendorSearchResults.querySelectorAll('.vendor-result:not(.hidden)'))
                        .find(result => !result.classList.contains('hidden'));

                    if (firstResult) {
                        const vendorId = firstResult.dataset.vendorId;
                        const vendorName = firstResult.dataset.vendorName;
                        const vendorAddress = firstResult.dataset.vendorAddress;

                        // Check if vendor is already selected
                        if (!selectedVendors.some(vendor => vendor.id === vendorId)) {
                            addSelectedVendor(vendorId, vendorName, vendorAddress);
                        }

                        // Clear search
                        vendorSearchInput.value = '';
                        vendorSearchResults.classList.add('hidden');
                    }
                }
            });
        }

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!vendorSearchInput.contains(e.target) && !vendorSearchResults.contains(e.target) && !addVendorBtn.contains(e.target)) {
                vendorSearchResults.classList.add('hidden');
            }
        });
    }

    // Add selected item
    function addSelectedItem(itemId, itemName) {
        // Add to selected items array
        const newItem = {
            id: itemId,
            name: itemName,
            quantity: 1,
            specifications: ''
        };
        selectedItems.push(newItem);

        // Create item element from template
        const itemElement = document.importNode(selectedItemTemplate.content, true);
        const itemContainer = itemElement.querySelector('.selected-item');
        itemContainer.dataset.itemId = itemId;

        // Set item name
        const nameElement = itemElement.querySelector('.item-name');
        nameElement.textContent = itemName;

        // Set hidden inputs
        const idInput = itemElement.querySelector('.item-id');
        idInput.value = itemId;

        const nameInput = itemElement.querySelector('.item-name-input');
        nameInput.value = itemName;

        // Add event listeners
        const quantityInput = itemElement.querySelector('.item-quantity');
        quantityInput.addEventListener('input', function() {
            const item = selectedItems.find(item => item.id === itemId);
            if (item) {
                item.quantity = parseInt(this.value) || 1;
                updateQuotationPreview();
            }
        });

        const specificationsInput = itemElement.querySelector('.item-specifications');
        specificationsInput.addEventListener('input', function() {
            const item = selectedItems.find(item => item.id === itemId);
            if (item) {
                item.specifications = this.value;
                updateQuotationPreview();
            }
        });

        // Add remove button event listener
        const removeButton = itemElement.querySelector('.remove-item');
        removeButton.addEventListener('click', function() {
            removeSelectedItem(itemId);
        });

        // Hide no items message
        if (noItemsMessage) {
            noItemsMessage.classList.add('hidden');
        }

        // Add to DOM
        selectedItemsList.appendChild(itemElement);

        // Update preview
        updateQuotationPreview();
    }

    // Remove selected item
    function removeSelectedItem(itemId) {
        // Remove from array
        selectedItems = selectedItems.filter(item => item.id !== itemId);

        // Remove from DOM
        const itemElement = selectedItemsList.querySelector(`.selected-item[data-item-id="${itemId}"]`);
        if (itemElement) {
            itemElement.remove();
        }

        // Show no items message if no items left
        if (selectedItems.length === 0 && noItemsMessage) {
            noItemsMessage.classList.remove('hidden');
        }

        // Update preview
        updateQuotationPreview();
    }

    // Add selected vendor
    function addSelectedVendor(vendorId, vendorName, vendorAddress) {
        // Add to selected vendors array
        const newVendor = {
            id: vendorId,
            name: vendorName,
            address: vendorAddress
        };
        selectedVendors.push(newVendor);

        // Create vendor element from template
        const vendorElement = document.importNode(selectedVendorTemplate.content, true);
        const vendorContainer = vendorElement.querySelector('.selected-vendor');
        vendorContainer.dataset.vendorId = vendorId;

        // Set vendor name and address
        const nameElement = vendorElement.querySelector('.vendor-name');
        if (nameElement) {
            nameElement.textContent = vendorName;
        } else {
            console.error('Vendor name element not found in template');
        }

        const addressElement = vendorElement.querySelector('.vendor-address');
        if (addressElement) {
            addressElement.textContent = vendorAddress || '';
        } else {
            console.error('Vendor address element not found in template');
        }

        // Set hidden inputs
        const idInput = vendorElement.querySelector('.vendor-id');
        if (idInput) {
            idInput.value = vendorId;
        } else {
            console.error('Vendor ID input not found in template');
        }

        const nameInput = vendorElement.querySelector('.vendor-name-input');
        if (nameInput) {
            nameInput.value = vendorName;
        } else {
            console.error('Vendor name input not found in template');
        }

        const addressInput = vendorElement.querySelector('.vendor-address-input');
        if (addressInput) {
            addressInput.value = vendorAddress || '';
        } else {
            console.error('Vendor address input not found in template');
        }

        // Add remove button event listener
        const removeButton = vendorElement.querySelector('.remove-vendor');
        if (removeButton) {
            removeButton.addEventListener('click', function() {
                removeSelectedVendor(vendorId);
            });
        } else {
            console.error('Remove vendor button not found in template');
        }

        // Hide no vendors message
        if (noVendorsMessage) {
            noVendorsMessage.classList.add('hidden');
        }

        // Add to DOM
        selectedVendorsList.appendChild(vendorElement);

        // Update preview
        updateVendorsPreview();
    }

    // Remove selected vendor
    function removeSelectedVendor(vendorId) {
        // Remove from array
        selectedVendors = selectedVendors.filter(vendor => vendor.id !== vendorId);

        // Remove from DOM
        const vendorElement = selectedVendorsList.querySelector(`.selected-vendor[data-vendor-id="${vendorId}"]`);
        if (vendorElement) {
            vendorElement.remove();
        }

        // Show no vendors message if no vendors left
        if (selectedVendors.length === 0 && noVendorsMessage) {
            noVendorsMessage.classList.remove('hidden');
        }

        // Update preview
        updateVendorsPreview();
    }

    // Update quotation preview
    function updateQuotationPreview() {
        // Update subject
        const subject = quotationSubjectInput.value || 'Request for Quotation';
        quotationSubjectPreview.textContent = `Subject: ${subject}`;

        // Update items table
        if (!selectedItems || selectedItems.length === 0) {
            quotationItemsPreview.innerHTML = `
                <tr>
                    <td colspan="5" class="px-4 py-3 text-center text-gray-500 italic">Select items to see preview</td>
                </tr>
            `;
        } else {
            let tableHtml = '';
            selectedItems.forEach((item, index) => {
                tableHtml += `
                    <tr>
                        <td class="px-4 py-3 border-r">${index + 1}</td>
                        <td class="px-4 py-3 border-r">${item.name}</td>
                        <td class="px-4 py-3 border-r">${item.specifications || '-'}</td>
                        <td class="px-4 py-3 border-r">${item.quantity}</td>
                        <td class="px-4 py-3"></td>
                    </tr>
                `;
            });

            quotationItemsPreview.innerHTML = tableHtml;
        }

        // Update terms and conditions
        updateTermsPreview();

        // Update buyer info
        updateBuyerInfo();
    }

    // Update vendors preview
    function updateVendorsPreview() {
        if (!selectedVendors || selectedVendors.length === 0) {
            vendorsPreviewList.innerHTML = `
                <p class="text-gray-600 italic">No vendors selected yet</p>
            `;
        } else {
            let vendorsHtml = '';
            selectedVendors.forEach(vendor => {
                vendorsHtml += `
                    <div class="mb-2">
                        <p class="font-medium">${vendor.name}</p>
                        <p class="text-sm">${vendor.address}</p>
                    </div>
                `;
            });

            vendorsPreviewList.innerHTML = vendorsHtml;
        }
    }

    // Update terms preview
    function updateTermsPreview() {
        if (!termsConditionsInput || !termsPreview) return;

        const terms = termsConditionsInput.value;
        if (!terms) {
            termsPreview.innerHTML = '<p class="text-gray-600 italic">No terms and conditions provided</p>';
            return;
        }

        // Split terms by line and create list
        const termsList = terms.split('\n').filter(term => term.trim());
        let termsHtml = '';

        termsList.forEach(term => {
            termsHtml += `<div>${term}</div>`;
        });

        termsPreview.innerHTML = termsHtml;
    }

    // Update buyer info
    function updateBuyerInfo() {
        if (buyerNameInput && buyerNamePreview) {
            buyerNamePreview.textContent = buyerNameInput.value || 'Principal';
        }

        if (buyerDesignationInput && buyerDesignationPreview) {
            buyerDesignationPreview.textContent = buyerDesignationInput.value || 'Principal';
        }
    }

    // PDF options modal elements
    const pdfOptionsModal = document.getElementById('pdf-options-modal');
    const downloadPdfBtn = document.getElementById('download-pdf-btn');
    const openPdfBtn = document.getElementById('open-pdf-btn');
    const closePdfModalBtn = document.getElementById('close-pdf-modal-btn');
    const pdfLoadingSpinner = document.getElementById('pdf-loading-spinner');

    // PDF blob and URL
    let pdfBlob = null;
    let pdfUrl = null;

    // Generate quotation PDF
    function generateQuotationPdf() {
        // Validate required data
        if (!selectedItems?.length) {
            showToast('error', 'Please select at least one item before generating the PDF');
            return;
        }
        if (!quotationSubjectInput?.value) {
            showToast('error', 'Please enter a subject for the quotation');
            return;
        }
        if (!selectedVendors?.length) {
            showToast('error', 'Please select at least one vendor to send the quotation to');
            return;
        }

        // Show loading spinner
        if (pdfLoadingSpinner) pdfLoadingSpinner.classList.remove('hidden');

        // Prepare request data with mapping functions
        const mapItems = prop => selectedItems.map(item => item[prop] || '');
        const mapVendors = prop => selectedVendors.map(vendor => vendor[prop] || '');

        fetch('/it-admin/procurement/generate-quotation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                subject: quotationSubjectInput.value,
                item_ids: mapItems('id'),
                item_names: mapItems('name'),
                item_quantities: mapItems('quantity'),
                item_specifications: mapItems('specifications'),
                vendor_ids: mapVendors('id'),
                vendor_names: mapVendors('name'),
                vendor_addresses: mapVendors('address'),
                terms_conditions: termsConditionsInput?.value || '',
                buyer_name: buyerNameInput?.value || 'Principal',
                buyer_designation: buyerDesignationInput?.value || 'Principal'
            })
        })
        .then(response => !response.ok
            ? response.text().then(text => { throw new Error(`HTTP error! Status: ${response.status}, Message: ${text}`); })
            : response.blob()
        )
        .then(blob => {
            if (pdfLoadingSpinner) pdfLoadingSpinner.classList.add('hidden');

            // Store blob and create URL
            pdfBlob = blob;
            pdfUrl = URL.createObjectURL(blob);

            // Show options modal or download directly
            if (pdfOptionsModal) pdfOptionsModal.classList.remove('hidden');
            else downloadPdf();

            showToast('success', 'Quotation PDF generated successfully');
        })
        .catch(error => {
            if (pdfLoadingSpinner) pdfLoadingSpinner.classList.add('hidden');
            showToast('error', 'Error generating quotation PDF: ' + error.message);
        });
    }

    // Download PDF function
    function downloadPdf() {
        if (!pdfBlob || !pdfUrl) return;

        const a = document.createElement('a');
        a.href = pdfUrl;
        a.download = 'quotation_request.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // Open PDF in browser function
    function openPdfInBrowser() {
        if (!pdfUrl) return;

        window.open(pdfUrl, '_blank');
    }

    // Close PDF modal function
    function closePdfModal() {
        if (pdfOptionsModal) {
            pdfOptionsModal.classList.add('hidden');
        }

        // Revoke URL to free memory
        if (pdfUrl) {
            window.URL.revokeObjectURL(pdfUrl);
            pdfUrl = null;
        }
        pdfBlob = null;
    }

    // Add event listeners for PDF options and form inputs
    const addClickListener = (el, handler) => el && el.addEventListener('click', handler);
    const addInputListener = (el, handler) => el && el.addEventListener('input', handler);

    // PDF modal buttons
    addClickListener(downloadPdfBtn, () => { downloadPdf(); closePdfModal(); });
    addClickListener(openPdfBtn, () => { openPdfInBrowser(); closePdfModal(); });
    addClickListener(closePdfModalBtn, closePdfModal);

    // Close modal when clicking outside
    if (pdfOptionsModal) {
        pdfOptionsModal.addEventListener('click', e => {
            if (e.target === pdfOptionsModal) closePdfModal();
        });
    }

    // Form input listeners
    addInputListener(quotationSubjectInput, updateQuotationPreview);
    addInputListener(termsConditionsInput, updateTermsPreview);
    addInputListener(buyerNameInput, updateBuyerInfo);
    addInputListener(buyerDesignationInput, updateBuyerInfo);

    // PDF generation button
    addClickListener(generateQuotationPdfBtn, generateQuotationPdf);

    // Comparative Statement Preview
    const previewComparativeBtn = document.getElementById('preview-comparative-btn');
    const comparativeLoadingSpinner = document.getElementById('comparative-loading-spinner');

    // Generate comparative statement PDF
    function generateComparativePdf() {
        try {
            // Show loading spinner
            if (comparativeLoadingSpinner) comparativeLoadingSpinner.classList.remove('hidden');

            // Check if we have data to generate PDF
            const rows = comparativeTable.querySelectorAll('tbody tr');
            if (rows.length === 0) {
                showToast('error', 'No data available for comparative statement');
                if (comparativeLoadingSpinner) comparativeLoadingSpinner.classList.add('hidden');
                return;
            }

            // Get comparative table data
            const items = [], vendors = [], vendorTotals = {};

            // Get vendors from table headers
            comparativeTable.querySelectorAll('thead th').forEach((th, i) => {
                if (i >= 2) vendors.push({ name: th.textContent, index: i - 2 });
            });

            // Get items and prices from table rows
            comparativeTable.querySelectorAll('tbody tr').forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length < 2) return;

                const itemPrices = [];
                for (let i = 2; i < cells.length; i++) {
                    const cell = cells[i];
                    const unitPrice = cell.querySelector('.font-medium')?.textContent || '₹ 0.00';
                    const totalPrice = cell.querySelector('.text-gray-500')?.textContent.replace('Total: ', '') || '₹ 0.00';

                    itemPrices.push({ unitPrice, totalPrice });

                    // Add to vendor totals
                    const vendorIndex = i - 2;
                    if (!vendorTotals[vendorIndex]) vendorTotals[vendorIndex] = 0;
                    vendorTotals[vendorIndex] += parseFloat(totalPrice.replace('₹', '').trim()) || 0;
                }

                items.push({
                    name: cells[0].textContent,
                    quantity: cells[1].textContent,
                    prices: itemPrices
                });
            });

            // Get selected vendor
            const selectedVendorName = selectedVendorSelect.options[selectedVendorSelect.selectedIndex]?.textContent || '';
            const justification = document.getElementById('selection_justification').value || '';

            // Send AJAX request to generate PDF
            fetch('/it-admin/procurement/generate-comparative-pdf', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    items,
                    vendors,
                    vendorTotals,
                    selectedVendor: selectedVendorName,
                    justification
                })
            })
            .then(response => {
                if (comparativeLoadingSpinner) comparativeLoadingSpinner.classList.add('hidden');

                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error('Failed to generate PDF: ' + text);
                    });
                }

                const contentType = response.headers.get('content-type');
                return contentType && contentType.includes('application/pdf')
                    ? response.blob()
                    : response.text().then(text => {
                        throw new Error('Server returned an unexpected response type');
                    });
            })
            .then(blob => window.open(URL.createObjectURL(blob), '_blank'))
            .catch(error => {
                if (comparativeLoadingSpinner) comparativeLoadingSpinner.classList.add('hidden');
                showToast('error', 'Failed to generate comparative statement PDF' + (error.message ? ': ' + error.message : ''));
            });
        } catch (error) {
            if (comparativeLoadingSpinner) comparativeLoadingSpinner.classList.add('hidden');
            showToast('error', 'An error occurred while generating the comparative statement PDF');
        }
    }

    // Add event listener to preview button
    if (previewComparativeBtn) {
        previewComparativeBtn.addEventListener('click', generateComparativePdf);
    }

    // Initialize previews
    updateQuotationPreview();
    updateVendorsPreview();
    updateTermsPreview();
    updateBuyerInfo();

    // Initialize UI
    updateUI();

    // Make sure save button is visible
    if (saveBtn) {
        console.log('Save button found:', saveBtn);
        saveBtn.classList.remove('hidden');
        saveBtn.style.display = 'inline-flex';
        console.log('Save button display style:', saveBtn.style.display);
        console.log('Save button classes:', saveBtn.className);
    } else {
        console.error('Save button not found!');
    }

    // Close the document.addEventListener function
    });
}

    // Initialize global variables for draft data
    window.isDraftAvailable = false;
    window.draftItems = [];
    window.draftVendors = [];
    window.draftDocuments = {};

    // Initialize draft data if available
    function initializeDraftData() {
        // Check if we have draft data
        let isDraft = false;
        // The server-side data will be available via a global variable
        if (typeof window.isDraftAvailable !== 'undefined') {
            isDraft = window.isDraftAvailable;
        }

        if (isDraft) {
            console.log('Initializing draft data...');

            // Load items data
            let draftItems = [];
            // Items will be available via a global variable
            if (typeof window.draftItems !== 'undefined' && Array.isArray(window.draftItems)) {
                draftItems = window.draftItems;
            }

            if (draftItems && draftItems.length > 0) {
                console.log(`Loading ${draftItems.length} items from draft`);

                // Clear any existing items
                const existingItemTabs = document.querySelectorAll('.item-tab');
                existingItemTabs.forEach(tab => {
                    if (tab.parentNode && tab.parentNode !== addItemTabButton?.parentNode) {
                        tab.parentNode.remove();
                    }
                });

                const existingItemEntries = document.querySelectorAll('.item-entry');
                existingItemEntries.forEach(entry => {
                    entry.remove();
                });

                // Reset item count
                itemsCount = 0;

                // Add each item from draft
                draftItems.forEach(item => {
                    // Create new item tab
                    const itemTab = document.importNode(itemTabTemplate.content, true);
                    const itemIndex = itemsCount;

                    // Set item index
                    const tabButton = itemTab.querySelector('.item-tab');
                    tabButton.dataset.itemIndex = itemIndex;

                    // Set item name in tab
                    const tabName = itemTab.querySelector('.item-tab-name');
                    if (tabName) {
                        tabName.textContent = item.name || 'New Item';
                    }

                    // Add tab to tabs container (before the add button)
                    if (itemsTabs) {
                        itemsTabs.insertBefore(itemTab, addItemTabButton?.parentNode);
                    }

                    // Create new item content
                    const itemEntry = document.importNode(itemTemplate.content, true);
                    const itemEntryDiv = itemEntry.querySelector('.item-entry');

                    if (itemEntryDiv) {
                        itemEntryDiv.dataset.itemIndex = itemIndex;

                        // Set unique names for inputs
                        const inputs = itemEntry.querySelectorAll('input, textarea');
                        inputs.forEach(input => {
                            const name = input.getAttribute('name');
                            if (name) {
                                input.setAttribute('name', name.replace('[]', `[${itemIndex}]`));
                            }
                        });

                        // Set item values
                        const itemNameInput = itemEntry.querySelector('.item-name');
                        if (itemNameInput) {
                            itemNameInput.value = item.name || '';
                        }

                        const itemQuantityInput = itemEntry.querySelector('.item-quantity');
                        if (itemQuantityInput) {
                            itemQuantityInput.value = item.quantity || 1;
                        }

                        const itemSpecificationsInput = itemEntry.querySelector('textarea');
                        if (itemSpecificationsInput) {
                            itemSpecificationsInput.value = item.description || '';
                        }

                        // Add event listeners (same as in addItem function)
                        if (itemNameInput) {
                            itemNameInput.addEventListener('input', function() {
                                const tabName = document.querySelector(`.item-tab[data-item-index="${itemIndex}"] .item-tab-name`);
                                tabName.textContent = this.value || 'New Item';

                                // Update item name in all vendor item price entries
                                updateItemNameInVendors(itemIndex, this.value);

                                // Update item data
                                if (!itemsData[itemIndex]) {
                                    itemsData[itemIndex] = {};
                                }
                                itemsData[itemIndex].name = this.value;
                            });
                        }

                        if (itemQuantityInput) {
                            itemQuantityInput.addEventListener('input', function() {
                                const quantity = parseInt(this.value) || 1;

                                // Update item data
                                if (!itemsData[itemIndex]) {
                                    itemsData[itemIndex] = {};
                                }
                                itemsData[itemIndex].quantity = quantity;

                                // Update price calculations in all vendors
                                updateItemQuantityInVendors(itemIndex, quantity);
                            });
                        }

                        // Handle remove item button
                        const removeButton = itemEntry.querySelector('.remove-item');
                        if (removeButton) {
                            removeButton.addEventListener('click', function() {
                                // Remove tab
                                const tab = document.querySelector(`.item-tab[data-item-index="${itemIndex}"]`).parentNode;
                                tab.remove();

                                // Remove content
                                this.closest('.item-entry').remove();

                                // Remove item price entries from all vendors
                                removeItemFromVendors(itemIndex);

                                // Remove from itemsData
                                itemsData[itemIndex] = null;

                                // Activate another tab if this was active
                                if (activeItemIndex === itemIndex) {
                                    const firstItemTab = document.querySelector('.item-tab');
                                    if (firstItemTab) {
                                        activateItemTab(parseInt(firstItemTab.dataset.itemIndex));
                                    } else {
                                        activeItemIndex = null;
                                    }
                                }
                            });
                        }

                        // Add click event to tab
                        tabButton.addEventListener('click', function() {
                            activateItemTab(itemIndex);
                        });

                        // Add item entry to container
                        itemsContainer.appendChild(itemEntry);

                        // Initialize item data
                        itemsData[itemIndex] = {
                            name: item.name || '',
                            quantity: item.quantity || 1,
                            index: itemIndex
                        };

                        // Increment items count
                        itemsCount++;
                    }
                });

                // Activate the first item tab if any
                if (itemsCount > 0) {
                    activateItemTab(0);
                }
            }

            // Load vendors data
            let draftVendors = [];
            // Vendors will be available via a global variable
            if (typeof window.draftVendors !== 'undefined' && Array.isArray(window.draftVendors)) {
                draftVendors = window.draftVendors;
            }

            if (draftVendors && draftVendors.length > 0) {
                console.log(`Loading ${draftVendors.length} vendors from draft`);

                // Set selectedVendors global variable
                window.selectedVendors = draftVendors;

                // Update vendor selection in step 2
                if (typeof updateVendorsPreview === 'function') {
                    updateVendorsPreview();
                }
            }

            // Load documents if available
            let draftDocuments = {};
            // Documents will be available via a global variable
            if (typeof window.draftDocuments !== 'undefined' && typeof window.draftDocuments === 'object') {
                draftDocuments = window.draftDocuments;
            }

            if (draftDocuments) {
                console.log('Loading documents from draft:', Object.keys(draftDocuments));

                // Handle request letter
                if (draftDocuments.request_letter && draftDocuments.request_letter.length > 0) {
                    const requestLetterDoc = draftDocuments.request_letter[0];
                    if (requestLetterDoc && requestLetterName) {
                        requestLetterName.textContent = requestLetterDoc.original_name || 'Uploaded file';
                        requestLetterPreview.classList.remove('hidden');
                    }
                }

                // Handle committee proceedings
                const committeeProceedings = document.getElementById('committee_proceedings_preview');
                const committeeProceedingsName = document.getElementById('committee_proceedings_name');
                if (draftDocuments.committee_proceedings && draftDocuments.committee_proceedings.length > 0 &&
                    committeeProceedings && committeeProceedingsName) {
                    const doc = draftDocuments.committee_proceedings[0];
                    committeeProceedingsName.textContent = doc.original_name || 'Uploaded file';
                    committeeProceedings.classList.remove('hidden');
                }

                // Handle bill
                const billPreview = document.getElementById('bill_preview');
                const billName = document.getElementById('bill_name');
                if (draftDocuments.bill && draftDocuments.bill.length > 0 &&
                    billPreview && billName) {
                    const doc = draftDocuments.bill[0];
                    billName.textContent = doc.original_name || 'Uploaded file';
                    billPreview.classList.remove('hidden');
                }

                // Handle payment proof
                const paymentProofPreview = document.getElementById('payment_proof_preview');
                const paymentProofName = document.getElementById('payment_proof_name');
                if (draftDocuments.payment_proof && draftDocuments.payment_proof.length > 0 &&
                    paymentProofPreview && paymentProofName) {
                    const doc = draftDocuments.payment_proof[0];
                    paymentProofName.textContent = doc.original_name || 'Uploaded file';
                    paymentProofPreview.classList.remove('hidden');
                }
            }

            // Show toast notification
            showToast('info', 'Draft loaded successfully. You can continue editing from where you left off.', 5000);
        }
    }

    // Call initializeDraftData after a short delay to ensure all elements are loaded
    setTimeout(initializeDraftData, 500);

    // Initialize itemsData array
    // Global variable is already defined at the top of the page

    // Update comparative table when moving to step 3
    if (nextBtn) {
        try {
            nextBtn.addEventListener('click', function() {
                try {
                    // When moving from step 1 (Call Quotation) to step 2
                    if (typeof currentStep !== 'undefined' && currentStep === 1 && typeof validateStep === 'function' && validateStep()) {
                        // Transfer selected vendors from Call Quotation step to Quotations step
                        if (typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors) && selectedVendors.length > 0) {
                            console.log('Transferring vendors from Call Quotation to Quotations step...');

                            // Clear existing vendors first
                            const existingVendorTabs = document.querySelectorAll('.vendor-tab');
                            existingVendorTabs.forEach(tab => {
                                if (tab.parentNode && tab.parentNode !== addVendorTabButton.parentNode) {
                                    tab.parentNode.remove();
                                }
                            });

                            const existingVendorEntries = document.querySelectorAll('.vendor-entry');
                            existingVendorEntries.forEach(entry => {
                                entry.remove();
                            });

                            // Reset vendor count
                            vendorsCount = 0;

                            // Add each selected vendor
                            selectedVendors.forEach(vendor => {
                                // Create new vendor tab
                                const vendorTab = document.importNode(vendorTabTemplate.content, true);
                                const vendorIndex = vendorsCount;

                                // Set vendor index
                                const tabButton = vendorTab.querySelector('.vendor-tab');
                                tabButton.dataset.vendorIndex = vendorIndex;

                                // Set vendor name in tab
                                const tabName = vendorTab.querySelector('.vendor-tab-name');
                                if (tabName) {
                                    tabName.textContent = vendor.name || 'New Vendor';
                                }

                                // Add tab to tabs container (before the add button)
                                if (vendorsTabs) {
                                    vendorsTabs.insertBefore(vendorTab, addVendorTabButton.parentNode);
                                }

                                // Create new vendor content
                                const vendorEntry = document.importNode(vendorTemplate.content, true);
                                const vendorEntryDiv = vendorEntry.querySelector('.vendor-entry');

                                if (vendorEntryDiv) {
                                    vendorEntryDiv.dataset.vendorIndex = vendorIndex;

                                    // Set unique names for inputs
                                    const inputs = vendorEntry.querySelectorAll('input, textarea, select');
                                    inputs.forEach(input => {
                                        const name = input.getAttribute('name');
                                        if (name) {
                                            input.setAttribute('name', name.replace('[]', `[${vendorIndex}]`));
                                        }
                                    });

                                    // Set unique name for quotation file input
                                    const quotationFileInput = vendorEntry.querySelector('.quotation-file');
                                    if (quotationFileInput) {
                                        quotationFileInput.setAttribute('name', `vendors[${vendorIndex}][quotation_file]`);
                                    }

                                    // Set vendor in dropdown
                                    const vendorSelect = vendorEntry.querySelector('.vendor-select');
                                    if (vendorSelect) {
                                        // Clone the vendor options from the hidden template select
                                        const vendorOptionsTemplate = document.getElementById('vendor-options-template');
                                        if (vendorOptionsTemplate) {
                                            const options = vendorOptionsTemplate.querySelectorAll('option');
                                            options.forEach(option => {
                                                vendorSelect.appendChild(option.cloneNode(true));
                                            });
                                        }

                                        // Select the correct vendor
                                        const options = vendorSelect.querySelectorAll('option');
                                        options.forEach(option => {
                                            if (option.value === vendor.id) {
                                                option.selected = true;
                                            }
                                        });

                                        // Handle vendor selection change
                                        vendorSelect.addEventListener('change', function() {
                                            const selectedOption = this.options[this.selectedIndex];
                                            const vendorName = selectedOption.textContent;

                                            // Update tab name
                                            const tabName = document.querySelector(`.vendor-tab[data-vendor-index="${vendorIndex}"] .vendor-tab-name`);
                                            if (tabName) {
                                                tabName.textContent = vendorName || 'New Vendor';
                                            }
                                        });
                                    }

                                    // Handle file upload
                                    const fileInput = vendorEntry.querySelector('.quotation-file');
                                    const filePreview = vendorEntry.querySelector('.quotation-preview');
                                    const fileName = vendorEntry.querySelector('.quotation-name');
                                    const removeFileButton = vendorEntry.querySelector('.remove-quotation');

                                    if (fileInput && filePreview && fileName && removeFileButton) {
                                        fileInput.addEventListener('change', function() {
                                            if (this.files && this.files[0]) {
                                                const file = this.files[0];
                                                fileName.textContent = file.name;
                                                filePreview.classList.remove('hidden');
                                            }
                                        });

                                        removeFileButton.addEventListener('click', function() {
                                            fileInput.value = '';
                                            filePreview.classList.add('hidden');
                                        });
                                    }

                                    // Add item price entries for each item
                                    const itemPricesContainer = vendorEntry.querySelector('.item-prices-container');
                                    if (itemPricesContainer && itemsData) {
                                        itemsData.forEach((item, itemIndex) => {
                                            if (item) {
                                                addItemPriceEntry(itemPricesContainer, vendorIndex, itemIndex, item);
                                            }
                                        });
                                    }

                                    // Handle remove vendor button
                                    const removeButton = vendorEntry.querySelector('.remove-vendor');
                                    if (removeButton) {
                                        removeButton.addEventListener('click', function() {
                                            // Remove tab
                                            const tab = document.querySelector(`.vendor-tab[data-vendor-index="${vendorIndex}"]`).parentNode;
                                            tab.remove();

                                            // Remove content
                                            this.closest('.vendor-entry').remove();

                                            // Activate another tab if this was active
                                            if (activeVendorIndex === vendorIndex) {
                                                const firstVendorTab = document.querySelector('.vendor-tab');
                                                if (firstVendorTab) {
                                                    activateVendorTab(parseInt(firstVendorTab.dataset.vendorIndex));
                                                } else {
                                                    activeVendorIndex = null;
                                                }
                                            }
                                        });
                                    }

                                    // Add click event to tab
                                    tabButton.addEventListener('click', function() {
                                        activateVendorTab(vendorIndex);
                                    });

                                    // Add vendor entry to container
                                    if (vendorsContainer) {
                                        vendorsContainer.appendChild(vendorEntry);
                                    }

                                    // Increment vendors count
                                    vendorsCount++;
                                }
                            });

                            // Activate the first vendor tab
                            if (vendorsCount > 0) {
                                activateVendorTab(0);
                            }
                        }
                    }

                    // When moving from step 2 to step 3
                    if (typeof currentStep !== 'undefined' && currentStep === 2 && typeof validateStep === 'function' && validateStep()) {
                        // Make sure selectedVendors and selectedItems are available in the global scope
                        console.log('Moving from step 2 to step 3, transferring vendors and items to global scope...');

                        // Transfer selectedVendors to window.selectedVendors
                        if (typeof selectedVendors !== 'undefined' && Array.isArray(selectedVendors)) {
                            window.selectedVendors = [...selectedVendors]; // Create a copy
                            console.log('Transferred selectedVendors to window.selectedVendors:', window.selectedVendors);
                        } else {
                            console.warn('selectedVendors is not defined or not an array');
                        }

                        // Transfer selectedItems to window.selectedItems
                        if (typeof selectedItems !== 'undefined' && Array.isArray(selectedItems)) {
                            window.selectedItems = [...selectedItems]; // Create a copy
                            console.log('Transferred selectedItems to window.selectedItems:', window.selectedItems);
                        } else {
                            console.warn('selectedItems is not defined or not an array');
                        }

                        // Initialize Step 3 if available
                        if (typeof window.initStep3 === 'function') {
                            console.log('Calling window.initStep3()...');
                            window.initStep3();
                        } else {
                            console.warn('window.initStep3 function is not defined');
                        }
                    }

                    // When moving from step 3 to step 4
                    if (typeof currentStep !== 'undefined' && currentStep === 3 && typeof validateStep === 'function' && validateStep()) {
                        console.log('Moving from step 3 to step 4, updating comparative table...');

                        // Extract quotation data directly from the DOM
                        const extractedData = extractQuotationDataFromDOM();
                        if (extractedData) {
                            window.extractedQuotationData = extractedData;
                            console.log('Saved extracted data to window.extractedQuotationData');
                        }

                        // Update comparative table if available
                        if (typeof updateComparativeTable === 'function') {
                            console.log('Calling updateComparativeTable()...');

                            // Add a small delay to ensure all data is processed
                            setTimeout(() => {
                                updateComparativeTable();

                                // Update totals after table is populated
                                if (typeof updateTotals === 'function') {
                                    updateTotals();
                                }

                                // Log the state after updating
                                console.log('Comparative table updated. Check if data is displayed correctly.');
                            }, 500); // Increased delay to ensure data is fully processed
                        } else {
                            console.warn('updateComparativeTable function is not defined');
                        }
                    }
                } catch (error) {
                    console.error('Error in next button click handler for comparative table:', error);
                }
            });
        } catch (error) {
            console.error('Error adding event listener to next button for comparative table:', error);
        }
    } else {
        console.warn('Next button not found for comparative table update');
    }

    // Helper function to add item price entry to vendor
    function addItemPriceEntry(container, vendorIndex, itemIndex, item) {
        if (!container || !itemPriceTemplate) return;

        const itemPriceEntry = document.importNode(itemPriceTemplate.content, true);
        const entryDiv = itemPriceEntry.querySelector('.item-price-entry');

        if (entryDiv) {
            entryDiv.dataset.itemIndex = itemIndex;
            entryDiv.dataset.vendorIndex = vendorIndex;

            // Set item name
            const itemNameDisplay = entryDiv.querySelector('.item-name-display');
            if (itemNameDisplay) {
                itemNameDisplay.value = item.name || '';
                itemNameDisplay.dataset.itemIndex = itemIndex;
            }

            // Set item ID
            const itemIdInput = entryDiv.querySelector('.item-id');
            if (itemIdInput) {
                itemIdInput.value = itemIndex;
                itemIdInput.name = `vendors[${vendorIndex}][items][${itemIndex}][item_id]`;
            }

            // Set unit price input
            const unitPriceInput = entryDiv.querySelector('.item-unit-price');
            if (unitPriceInput) {
                unitPriceInput.name = `vendors[${vendorIndex}][items][${itemIndex}][unit_price]`;

                // Handle unit price change
                unitPriceInput.addEventListener('input', function() {
                    const unitPrice = parseFloat(this.value) || 0;
                    const quantity = item.quantity || 1;
                    const totalPrice = unitPrice * quantity;

                    // Update total price
                    const totalPriceInput = entryDiv.querySelector('.item-total-price');
                    if (totalPriceInput) {
                        totalPriceInput.value = totalPrice.toFixed(2);
                    }

                    // Update vendor total
                    updateVendorTotal(vendorIndex);
                });
            }

            // Set total price input
            const totalPriceInput = entryDiv.querySelector('.item-total-price');
            if (totalPriceInput) {
                totalPriceInput.name = `vendors[${vendorIndex}][items][${itemIndex}][total_price]`;
            }

            container.appendChild(itemPriceEntry);
        }
    }

    // Check if all required templates exist
    const requiredTemplates = [
        { id: 'item-template', name: 'Item Template' },
        { id: 'item-tab-template', name: 'Item Tab Template' },
        { id: 'vendor-template', name: 'Vendor Template' },
        { id: 'vendor-tab-template', name: 'Vendor Tab Template' },
        { id: 'item-price-template', name: 'Item Price Template' }
    ];

    let allTemplatesExist = true;
    requiredTemplates.forEach(template => {
        const templateElement = document.getElementById(template.id);
        if (!templateElement) {
            console.error(`${template.name} (${template.id}) not found`);
            allTemplatesExist = false;
        }
    });

    if (!allTemplatesExist) {
        console.error('Some required templates are missing. The form may not work correctly.');
    }

    // Check if navigation buttons exist
    const navigationButtons = [
        { id: 'prevBtn', name: 'Previous Button' },
        { id: 'nextBtn', name: 'Next Button' },
        { id: 'submitBtn', name: 'Submit Button' },
        { id: 'saveBtn', name: 'Save Button' }
    ];

    let allButtonsExist = true;
    navigationButtons.forEach(button => {
        const buttonElement = document.getElementById(button.id);
        if (!buttonElement) {
            console.warn(`${button.name} (${button.id}) not found`);
            allButtonsExist = false;
        }
    });

    if (!allButtonsExist) {
        console.warn('No navigation buttons found');
    }

    // Define fallback functions in case the main ones aren't available
    // These will be used if the real functions aren't defined yet
    if (typeof window.addItem !== 'function') {
        window.addItem = function() {
            console.log('Using fallback addItem function');
            // This is just a placeholder - it won't actually add an item
            // but it will prevent errors
        };
    }

    if (typeof window.addVendor !== 'function') {
        window.addVendor = function() {
            console.log('Using fallback addVendor function');
            // This is just a placeholder - it won't actually add a vendor
            // but it will prevent errors
        };
    }

    // Add initial item and vendor with a longer delay to ensure templates are loaded
    setTimeout(() => {
        try {
            console.log('Adding initial item...');
            // Try to find the addItem function in any scope
            const addItemFunc = (typeof window.addItem === 'function') ? window.addItem :
                               (typeof addItem === 'function') ? addItem : null;

            if (addItemFunc) {
                // Call the function we found
                addItemFunc();

                // Add initial vendor with a delay to ensure item is added first
                setTimeout(() => {
                    try {
                        console.log('Adding initial vendor...');
                        // Try to find the addVendor function in any scope
                        const addVendorFunc = (typeof window.addVendor === 'function') ? window.addVendor :
                                           (typeof addVendor === 'function') ? addVendor : null;

                        if (addVendorFunc) {
                            addVendorFunc();
                        } else {
                            console.log('Skipping vendor addition - function not available');
                        }
                    } catch (error) {
                        console.error('Error adding initial vendor:', error);
                    }
                }, 800); // Increased delay to ensure item is fully added
            } else {
                console.log('Skipping item addition - function not available');
            }

            // Initialize quotation preview with a longer delay
            setTimeout(() => {
                try {
                    console.log('Initializing quotation preview...');
                    if (typeof updateQuotationPreview === 'function') {
                        updateQuotationPreview();
                    }
                    if (typeof updateVendorsPreview === 'function') {
                        updateVendorsPreview();
                    }
                    if (typeof updateTermsPreview === 'function') {
                        updateTermsPreview();
                    }
                    if (typeof updateBuyerInfo === 'function') {
                        updateBuyerInfo();
                    }
                } catch (error) {
                    console.error('Error initializing quotation preview:', error);
                }
            }, 800); // Increased delay to ensure items and vendors are fully added
        } catch (error) {
            console.error('Error adding initial item:', error);
        }
    }, 500); // Increased delay to ensure all functions are defined

    // Listen for the confirmationStepReached event
    document.addEventListener('confirmationStepReached', function() {
        console.log('Confirmation step reached event triggered');

        // Update comparative table
        if (typeof updateComparativeTable === 'function') {
            console.log('Updating comparative table from confirmationStepReached event');
            updateComparativeTable();

            // Update totals after table is populated
            if (typeof updateTotals === 'function') {
                updateTotals();
            }
        }
    });

    // Listen for quotation data updates from step 3
    document.addEventListener('quotationDataUpdated', function(event) {
        console.log('Quotation data updated event received:', event.detail);

        // If we're on step 4, update the comparative table
        if (currentStep === 4) {
            console.log('Currently on step 4, updating comparative table with new quotation data');

            // Add a small delay to ensure all data is processed
            setTimeout(() => {
                if (typeof updateComparativeTable === 'function') {
                    updateComparativeTable();

                    // Update totals after table is populated
                    if (typeof updateTotals === 'function') {
                        updateTotals();
                    }
                }
            }, 300);
        } else {
            console.log('Not on step 4, skipping comparative table update');
        }
    });

    // Initialize Step 3 when navigating to it
    try {
        // Use safeDOM to get step indicators if available
        const stepIndicators = window.safeDOM ?
            window.safeDOM.querySelectorAll('.step-indicator') :
            document.querySelectorAll('.step-indicator');

        if (stepIndicators && stepIndicators.length > 0) {
            stepIndicators.forEach(step => {
                if (step) {
                    step.addEventListener('click', function() {
                        try {
                            // Safely get the step number with a fallback
                            let stepNumber = 1;
                            const stepAttr = this.getAttribute('data-step');
                            if (stepAttr) {
                                stepNumber = parseInt(stepAttr) || 1;
                            }

                            // Handle Step 3 initialization
                            if (stepNumber === 3 && typeof window.initStep3 === 'function') {
                                console.log('Initializing Step 3 from step indicator click');
                                window.initStep3();
                            }
                            // Handle Step 4 data capture
                            else if (stepNumber === 4) {
                                console.log('Moving to step 4 from step indicator click');

                                // Capture quotation data using our new function
                                if (typeof window.captureQuotationPricesForComparative === 'function') {
                                    console.log('Calling captureQuotationPricesForComparative from step indicator click...');
                                    try {
                                        window.captureQuotationPricesForComparative();
                                    } catch (captureError) {
                                        console.error('Error capturing quotation prices:', captureError);
                                    }
                                }
                                else {
                                    // Extract quotation data directly from the DOM as fallback
                                    if (typeof extractQuotationDataFromDOM === 'function') {
                                        console.log('Extracting quotation data from DOM...');
                                        try {
                                            const extractedData = extractQuotationDataFromDOM();
                                            if (extractedData) {
                                                window.extractedQuotationData = extractedData;
                                                console.log('Saved extracted data to window.extractedQuotationData');
                                            }
                                        } catch (extractError) {
                                            console.error('Error extracting quotation data:', extractError);
                                        }
                                    }
                                }

                                // Force refresh of quotation data if getQuotationDataForComparative is available
                                if (typeof window.getQuotationDataForComparative === 'function') {
                                    console.log('Refreshing quotation data from step 3...');
                                    try {
                                        window.quotationDataForComparative = window.getQuotationDataForComparative();
                                    } catch (refreshError) {
                                        console.error('Error refreshing quotation data:', refreshError);
                                    }
                                }

                                // Update comparative table
                                if (typeof updateComparativeTable === 'function') {
                                    console.log('Updating comparative table from step indicator click');

                                    // Add a small delay to ensure all data is processed
                                    setTimeout(() => {
                                        try {
                                            updateComparativeTable();

                                            // Update totals after table is populated
                                            if (typeof updateTotals === 'function') {
                                                updateTotals();
                                            }

                                            console.log('Comparative table updated from step indicator click');
                                        } catch (updateError) {
                                            console.error('Error updating comparative table:', updateError);
                                        }
                                    }, 500); // Increased delay to ensure data is fully processed
                                } else {
                                    console.warn('updateComparativeTable function is not defined');
                                }
                            }
                        } catch (error) {
                            console.error('Error in step indicator click handler:', error);
                        }
                    });
                }
            });
        } else {
            console.warn('No step indicators found');
        }

        // Initialize next/prev buttons to trigger Step 3 initialization
        try {
            const navButtons = document.querySelectorAll('.next-step, .prev-step');
            console.log(`Found ${navButtons ? navButtons.length : 0} navigation buttons with .next-step or .prev-step classes`);

            // Also try to get the nextBtn and prevBtn by ID
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');

            if (nextBtn) console.log('Found nextBtn by ID');
            if (prevBtn) console.log('Found prevBtn by ID');

            // Add event listeners to buttons found by class
            if (navButtons && navButtons.length > 0) {
                navButtons.forEach(button => {
                    if (button && typeof button.addEventListener === 'function') {
                        button.addEventListener('click', function() {
                            try {
                                // Use safeDOM to get the current step value
                                let currentStep = 1;

                                if (window.safeDOM) {
                                    // Use safeDOM.getValue which has built-in null checks
                                    const stepValue = window.safeDOM.getValue('current_step');
                                    currentStep = parseInt(stepValue) || 1;
                                } else {
                                    // Fallback with explicit null checks
                                    const currentStepElement = document.getElementById('current_step');
                                    if (currentStepElement && currentStepElement.value) {
                                        currentStep = parseInt(currentStepElement.value) || 1;
                                    }
                                }

                                // Initialize Step 3 if needed
                                if (currentStep === 3 && typeof window.initStep3 === 'function') {
                                    console.log('Initializing Step 3 from navigation button click');
                                    window.initStep3();
                                }
                            } catch (error) {
                                console.error('Error in navigation button click handler:', error);
                            }
                        });
                    }
                });
            } else {
                console.log('No navigation buttons found with .next-step or .prev-step classes');
            }

            // Add event listeners to nextBtn and prevBtn if found
            const handleNavButtonClick = function() {
                try {
                    // Get current step
                    let currentStep = 1;
                    if (window.safeDOM) {
                        const stepValue = window.safeDOM.getValue('current_step');
                        currentStep = parseInt(stepValue) || 1;
                    } else {
                        const currentStepElement = document.getElementById('current_step');
                        if (currentStepElement && currentStepElement.value) {
                            currentStep = parseInt(currentStepElement.value) || 1;
                        }
                    }

                    // Initialize Step 3 if needed
                    if (currentStep === 3 && typeof window.initStep3 === 'function') {
                        console.log('Initializing Step 3 from nav button click');
                        window.initStep3();
                    }
                } catch (error) {
                    console.error('Error in nav button click handler:', error);
                }
            };

            if (nextBtn && typeof nextBtn.addEventListener === 'function') {
                console.log('Adding click event listener to nextBtn');
                nextBtn.addEventListener('click', handleNavButtonClick);
            }

            if (prevBtn && typeof prevBtn.addEventListener === 'function') {
                console.log('Adding click event listener to prevBtn');
                prevBtn.addEventListener('click', handleNavButtonClick);
            }
        } catch (error) {
            console.error('Error adding event listeners to navigation buttons:', error);
        }
    } catch (error) {
        console.error('Error setting up step navigation event listeners:', error);
    }

    // Define a function for submitting the form
    function submitForm() {
        // Create FormData object for the form submission
        const formData = new FormData(form);

        // Send AJAX request to save first
        fetch('/it-admin/procurement/save-draft', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // First get the text response
            return response.text().then(text => {
                console.log('Submit form raw response:', text);

                // Try to parse as JSON
                let data;
                try {
                    // Check if the response starts with <!DOCTYPE or <html
                    if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
                        console.error('Received HTML response instead of JSON');

                        // If response is not OK, throw an error
                        if (!response.ok) {
                            throw new Error(`Server error: ${response.status}. Please try again with fewer or smaller files.`);
                        }

                        // If response is OK but HTML, create a synthetic success response
                        data = {
                            success: true,
                            message: 'Request processed successfully',
                            request_id: (document.getElementById('request_id') && document.getElementById('request_id').value) ? document.getElementById('request_id').value : Math.floor(Math.random() * 1000)
                        };
                    } else {
                        // Try to parse as JSON
                        data = JSON.parse(text);
                    }
                } catch (e) {
                    console.error('Error parsing response as JSON:', e);
                    console.log('Raw response:', text.substring(0, 200) + '...');

                    // If response is not OK and not JSON, throw an error
                    if (!response.ok) {
                        throw new Error(`Server error: ${response.status}. Please try again with fewer or smaller files.`);
                    }

                    // Create a default success response if we can't parse the JSON
                    data = {
                        success: true,
                        message: 'Request processed successfully',
                        request_id: (document.getElementById('request_id') && document.getElementById('request_id').value) ? document.getElementById('request_id').value : Math.floor(Math.random() * 1000)
                    };
                }

                // If response is not OK but we got JSON, check for error message
                if (!response.ok && data) {
                    if (data.error) {
                        throw new Error(data.error || `Server error: ${response.status}`);
                    } else if (data.message) {
                        throw new Error(data.message || `Server error: ${response.status}`);
                    } else {
                        throw new Error(`Server error: ${response.status}. Please try again.`);
                    }
                }

                return data;
            });
        })
        .then(data => {
            if (data.success) {
                // Now submit the form
                const requestId = data.request_id ||
                    (window.safeDOM ?
                    window.safeDOM.getValue('request_id') || Math.floor(Math.random() * 1000).toString() :
                    (document.getElementById('request_id') && document.getElementById('request_id').value ?
                    document.getElementById('request_id').value :
                    Math.floor(Math.random() * 1000).toString()));

                fetch(`/it-admin/procurement/${requestId}/submit`, {
                    method: 'POST'
                })
                .then(response => {
                    // Restore required attributes
                    document.querySelectorAll('[data-required="true"]').forEach(field => {
                        field.setAttribute('required', '');
                        field.removeAttribute('data-required');
                    });

                    if (response.ok) {
                        // Show success toast
                        showToast('success', 'Procurement request submitted successfully');

                        // Redirect to procurement dashboard
                        setTimeout(() => {
                            window.location.href = '/it-admin/procurement';
                        }, 1500);
                    } else {
                        // Show error toast
                        showToast('error', 'Error submitting procurement request');
                    }
                })
                .catch(error => {
                    // Restore required attributes
                    document.querySelectorAll('[data-required="true"]').forEach(field => {
                        field.setAttribute('required', '');
                        field.removeAttribute('data-required');
                    });

                    console.error('Error:', error);
                    showToast('error', 'An error occurred while submitting the procurement request');
                });
            } else {
                // Show error toast
                showToast('error', data.message || 'Error saving procurement request');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'An error occurred while saving the procurement request');
        });
    }

    // The submitForm function will be triggered when the user clicks the submit button
    // Do not call it here automatically
</script>

<!-- Include Step 3 JavaScript -->
<script src="/js/procurement-step3.js"></script>

<!-- Form Validation and Submission Functions -->
<script>
// Function to validate the current step
function validateStep() {
    try {
        // Get the current step content
        const currentStepContent = document.querySelector('.step-content:not(.hidden)');
        if (!currentStepContent) return true;

        // Get all required fields in the current step
        const requiredFields = currentStepContent.querySelectorAll('[required]');
        if (!requiredFields || requiredFields.length === 0) return true;

        // Check if all required fields are filled
        let isValid = true;
        requiredFields.forEach(field => {
            if (!field.value) {
                isValid = false;
                field.classList.add('border-red-500');

                // Add error message if not already present
                const errorMessage = field.nextElementSibling;
                if (!errorMessage || !errorMessage.classList.contains('error-message')) {
                    const message = document.createElement('p');
                    message.className = 'error-message text-red-500 text-sm mt-1';
                    message.textContent = 'This field is required';
                    field.parentNode.insertBefore(message, field.nextSibling);
                }
            } else {
                field.classList.remove('border-red-500');

                // Remove error message if present
                const errorMessage = field.nextElementSibling;
                if (errorMessage && errorMessage.classList.contains('error-message')) {
                    errorMessage.remove();
                }
            }
        });

        return isValid;
    } catch (error) {
        console.error('Error validating step:', error);
        return true; // Allow progression on error
    }
}

// Function to save draft
function saveDraft() {
    try {
        console.log('Saving draft...');

        // Create FormData object
        const form = window.safeDOM.getElementById('procurementForm');
        if (!form) {
            console.error('Form not found');
            return;
        }

        const formData = new FormData(form);

        // Add JSON data for selected items and vendors
        const jsonData = {
            selected_items: window.selectedItems,
            selected_vendors: window.selectedVendors
        };

        formData.append('json_data', JSON.stringify(jsonData));

        // Show loading toast
        if (typeof showToast === 'function') {
            showToast('info', 'Saving draft...', 0);
        }

        // Send the form data
        fetch('/it-admin/procurement/save-draft', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success toast
                if (typeof showToast === 'function') {
                    showToast('success', 'Draft saved successfully');
                }

                // Update request ID if provided
                if (data.request_id) {
                    window.safeDOM.setValue('request_id', data.request_id);
                }
            } else {
                // Show error toast
                if (typeof showToast === 'function') {
                    showToast('error', data.message || 'Error saving draft');
                }
            }
        })
        .catch(error => {
            console.error('Error saving draft:', error);

            // Show error toast
            if (typeof showToast === 'function') {
                showToast('error', 'Error saving draft: ' + error.message);
            }
        });
    } catch (error) {
        console.error('Error in saveDraft function:', error);
    }
}

// Function to submit the form
function submitForm() {
    try {
        console.log('Submitting form...');

        // Save draft first
        saveDraft();

        // Get request ID
        const requestId = window.safeDOM.getValue('request_id');
        if (!requestId) {
            console.error('Request ID not found');
            return;
        }

        // Show loading toast
        if (typeof showToast === 'function') {
            showToast('info', 'Submitting request...', 0);
        }

        // Submit the form
        fetch(`/it-admin/procurement/${requestId}/submit`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success toast
                if (typeof showToast === 'function') {
                    showToast('success', 'Request submitted successfully');
                }

                // Redirect to procurement dashboard
                setTimeout(() => {
                    window.location.href = '/it-admin/procurement';
                }, 1500);
            } else {
                // Show error toast
                if (typeof showToast === 'function') {
                    showToast('error', data.message || 'Error submitting request');
                }
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);

            // Show error toast
            if (typeof showToast === 'function') {
                showToast('error', 'Error submitting request: ' + error.message);
            }
        });
    } catch (error) {
        console.error('Error in submitForm function:', error);
    }
}
</script>

<!-- Toast notification functions -->
<script>
// Toast notification functions
function showToast(type, message, duration = 3000) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col items-end space-y-2';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    const toastId = 'toast-' + Date.now();
    toast.id = toastId;

    // Set toast type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            break;
        case 'error':
            bgColor = 'bg-red-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
            break;
        case 'warning':
            bgColor = 'bg-yellow-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>';
            break;
        case 'info':
        default:
            bgColor = 'bg-blue-500';
            textColor = 'text-white';
            icon = '<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            break;
    }

    // Set toast content
    toast.className = `${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-lg flex items-center justify-between min-w-[300px] max-w-md transform transition-all duration-300 ease-in-out opacity-0 translate-x-4`;
    toast.innerHTML = `
        <div class="flex items-center">
            ${icon}
            <span>${message}</span>
        </div>
        <button type="button" class="ml-4 text-white hover:text-gray-200 focus:outline-none" onclick="hideToast('${toastId}')">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Show toast with animation
    setTimeout(() => {
        toast.classList.remove('opacity-0', 'translate-x-4');
        toast.classList.add('opacity-100', 'translate-x-0');
    }, 10);

    // Auto-hide toast after duration (if not 0)
    if (duration > 0) {
        setTimeout(() => {
            hideToast(toastId);
        }, duration);
    }

    return toastId;
}

function hideToast(toastId) {
    const toast = document.getElementById(toastId);
    if (!toast) return;

    // Hide toast with animation
    toast.classList.remove('opacity-100', 'translate-x-0');
    toast.classList.add('opacity-0', 'translate-x-4');

    // Remove toast after animation
    setTimeout(() => {
        toast.remove();
    }, 300);
}
</script>

<!-- Data Capture Script for Price Transfer -->
<script>
// Ensure document is defined before adding event listener
if (typeof document !== 'undefined' && document !== null) {
    // Using a self-executing function to avoid polluting the global scope
    (function() {
        try {
            // Safety check - ensure document is defined
            if (typeof document === 'undefined' || document === null) {
                console.error('Document is not defined in data capture script');
                return;
            }

            // Ensure we have the necessary global variables
            if (typeof window === 'undefined') {
                console.error('Window is not defined in data capture script');
                return;
            }

            // Ensure global arrays are initialized
            if (!window.selectedVendors || !Array.isArray(window.selectedVendors)) {
                console.warn('window.selectedVendors is not properly initialized in data capture script, creating empty array');
                window.selectedVendors = [];
            }

            if (!window.selectedItems || !Array.isArray(window.selectedItems)) {
                console.warn('window.selectedItems is not properly initialized in data capture script, creating empty array');
                window.selectedItems = [];
            }

            // Initialize safeDOM if not already defined
            if (!window.safeDOM) {
                console.warn('safeDOM not defined, creating it now');
                window.safeDOM = {
                    getElementById: function(id) {
                        try {
                            return document.getElementById(id) || null;
                        } catch (e) {
                            console.error('Error in getElementById:', e);
                            return null;
                        }
                    },
                    getValue: function(id) {
                        try {
                            const element = this.getElementById(id);
                            return element && element.value ? element.value : '';
                        } catch (e) {
                            console.error('Error getting value for element ' + id + ':', e);
                            return '';
                        }
                    },
                    setValue: function(id, value) {
                        try {
                            const element = this.getElementById(id);
                            if (element) {
                                element.value = value;
                                return true;
                            }
                            return false;
                        } catch (e) {
                            console.error('Error setting value for element ' + id + ':', e);
                            return false;
                        }
                    }
                };
            }

            if (typeof window.selectedItems === 'undefined') window.selectedItems = [];
            if (typeof window.selectedVendors === 'undefined') window.selectedVendors = [];
            if (typeof window.itemsData === 'undefined') window.itemsData = [];

    // Data capture function - optimized with reduced logging and better error handling
    window.captureQuotationPricesForComparative = function() {
        // Data structure to hold vendor, item, and price information
        const capturedData = {
            vendors: [],
            items: [],
            prices: {}
        };

        try {
            // Get vendors - prioritize window.selectedVendors
            if (window.selectedVendors && window.selectedVendors.length > 0) {
                capturedData.vendors = [...window.selectedVendors];
            } else {
                // Fallback: Try to get from DOM
                let vendorRows = [];
                try {
                    // Wrap in try-catch to prevent errors
                    vendorRows = document.querySelectorAll('#vendor-quotations-body tr') || [];
                } catch (error) {
                    console.error('Error getting vendor rows:', error);
                }
                if (vendorRows && vendorRows.length > 0) {
                    vendorRows.forEach((row, index) => {
                        if (!row) return;

                        const vendorNameCell = row.querySelector('td:first-child');
                        if (vendorNameCell) {
                            const vendorName = vendorNameCell.querySelector('.font-medium')?.textContent || `Vendor ${index + 1}`;
                            const vendorId = row.getAttribute('data-vendor-id') || `vendor_${index}`;
                            capturedData.vendors.push({
                                id: vendorId,
                                name: vendorName
                            });
                        }
                    });
                }
            }

            // Get items - prioritize window.selectedItems
            if (window.selectedItems && window.selectedItems.length > 0) {
                capturedData.items = [...window.selectedItems];
            } else if (window.itemsData && Array.isArray(window.itemsData)) {
                capturedData.items = window.itemsData
                    .filter(item => item !== null)
                    .map((item, index) => ({
                        id: index.toString(),
                        name: item.name || `Item ${index + 1}`,
                        quantity: item.quantity || 1
                    }));
            }

            // Initialize prices structure
            capturedData.items.forEach(item => {
                if (item && item.id) {
                    capturedData.prices[item.id] = {};
                }
            });

            // CAPTURE STRATEGY: Prioritize data sources in order
            let dataFound = false;

            // 1. First try window.quotationData (most reliable source)
            if (!dataFound && typeof window.quotationData === 'object' && window.quotationData !== null) {
                for (const vendorId in window.quotationData) {
                    if (window.quotationData[vendorId] && window.quotationData[vendorId].items) {
                        for (const itemId in window.quotationData[vendorId].items) {
                            const priceData = window.quotationData[vendorId].items[itemId];
                            if (!priceData) continue;

                            if (!capturedData.prices[itemId]) {
                                capturedData.prices[itemId] = {};
                            }

                            capturedData.prices[itemId][vendorId] = {
                                unitPrice: priceData.unitPrice || 0,
                                totalPrice: priceData.totalPrice || 0
                            };
                            dataFound = true;
                        }
                    }
                }
            }

            // 2. Try vendor entries in the DOM
            if (!dataFound) {
                let vendorEntries = [];
                try {
                    // This is where the error is occurring - wrap in try-catch
                    vendorEntries = document.querySelectorAll('.vendor-entry') || [];
                } catch (error) {
                    console.error('Error getting vendor entries:', error);
                }
                if (vendorEntries && vendorEntries.length > 0) {
                    vendorEntries.forEach((vendorEntry) => {
                        if (!vendorEntry) return;

                        const vendorId = vendorEntry.getAttribute('data-vendor-index');
                        if (!vendorId) return;

                        const itemPriceEntries = vendorEntry.querySelectorAll('.item-price-entry');
                        if (!itemPriceEntries || itemPriceEntries.length === 0) return;

                        itemPriceEntries.forEach((priceEntry) => {
                            if (!priceEntry) return;

                            // Try to get item ID safely
                            let itemId = priceEntry.getAttribute('data-item-index');

                            if (!itemId) {
                                const itemIdElement = priceEntry.querySelector('.item-id');
                                if (itemIdElement && itemIdElement.value) {
                                    itemId = itemIdElement.value;
                                }
                            }

                            if (!itemId) return;

                            // Get unit price safely
                            let unitPrice = 0;
                            const unitPriceInput = priceEntry.querySelector('.item-unit-price');
                            if (unitPriceInput && unitPriceInput.value) {
                                unitPrice = parseFloat(unitPriceInput.value) || 0;
                            }

                            // Get total price safely
                            let totalPrice = 0;
                            const totalPriceInput = priceEntry.querySelector('.item-total-price');
                            if (totalPriceInput && totalPriceInput.value) {
                                totalPrice = parseFloat(totalPriceInput.value) || 0;
                            }

                            if (!capturedData.prices[itemId]) {
                                capturedData.prices[itemId] = {};
                            }

                            capturedData.prices[itemId][vendorId] = {
                                unitPrice: unitPrice,
                                totalPrice: totalPrice
                            };
                            dataFound = true;
                        });
                    });
                }
            }

            // 3. Try modal data as last resort
            if (!dataFound) {
                let modalItemPrices = [];
                try {
                    // Wrap in try-catch to prevent errors
                    modalItemPrices = document.querySelectorAll('#modal-item-prices .item-price-entry') || [];
                } catch (error) {
                    console.error('Error getting modal item prices:', error);
                }
                if (modalItemPrices && modalItemPrices.length > 0) {
                    modalItemPrices.forEach(entry => {
                        if (!entry) return;

                        // Try to get item ID safely
                        let itemId;
                        const itemIdElement = entry.querySelector('.item-id');

                        if (itemIdElement && itemIdElement.value) {
                            itemId = itemIdElement.value;
                        } else if (entry.hasAttribute('data-item-id')) {
                            // Try alternative: data attribute
                            itemId = entry.getAttribute('data-item-id');
                        } else if (entry.dataset && entry.dataset.itemId) {
                            // Try alternative: dataset
                            itemId = entry.dataset.itemId;
                        }

                        // If no item ID found, skip this entry
                        if (!itemId) return;

                        // Get unit price safely
                        let unitPrice = 0;
                        const unitPriceElement = entry.querySelector('.item-unit-price');
                        if (unitPriceElement && unitPriceElement.value) {
                            unitPrice = parseFloat(unitPriceElement.value) || 0;
                        }

                        // Get total price safely
                        let totalPrice = 0;
                        const totalPriceElement = entry.querySelector('.item-total-price');
                        if (totalPriceElement && totalPriceElement.value) {
                            totalPrice = parseFloat(totalPriceElement.value) || 0;
                        }

                        // Try multiple ways to get the vendor ID
                        let currentVendorId = window.currentVendorId;

                        // If not found in window, try to get from modal
                        if (!currentVendorId) {
                            if (window.safeDOM) {
                                currentVendorId = window.safeDOM.getValue('modal-vendor-id');
                            } else {
                                const modalVendorIdElement = document.getElementById('modal-vendor-id');
                                if (modalVendorIdElement) {
                                    currentVendorId = modalVendorIdElement.value;
                                }
                            }
                        }

                        // If still not found, use a fallback
                        if (!currentVendorId) {
                            // Use a fallback vendor ID if needed
                            const firstVendor = capturedData.vendors[0];
                            if (firstVendor && firstVendor.id) {
                                currentVendorId = firstVendor.id;
                            } else {
                                // No vendor ID available, skip this item
                                return;
                            }
                        }

                        if (!capturedData.prices[itemId]) {
                            capturedData.prices[itemId] = {};
                        }

                        capturedData.prices[itemId][currentVendorId] = {
                            unitPrice: unitPrice,
                            totalPrice: totalPrice
                        };
                        dataFound = true;
                    });
                }
            }

            // Store globally
            window.capturedQuotationData = capturedData;

            // Store in localStorage as backup
            try {
                // Check if localStorage is available
                if (typeof localStorage !== 'undefined' && localStorage !== null) {
                    localStorage.setItem('STEP3_PRICE_DATA', JSON.stringify(capturedData));
                }
            } catch (e) {
                console.warn('Could not save to localStorage:', e);
            }

            return capturedData;
        } catch (error) {
            console.error('Error in capture function:', error);
            return null;
        }
    };

    // Consolidated event handlers for data capture
    function setupDataCapture() {
        // 1. Patch updateComparativeTable to use our data
        if (typeof window.updateComparativeTable === 'function') {
            const originalUpdateComparativeTable = window.updateComparativeTable;
            window.updateComparativeTable = function() {
                // Capture fresh data first
                if (typeof window.captureQuotationPricesForComparative === 'function') {
                    window.capturedQuotationData = window.captureQuotationPricesForComparative();
                }
                // Call original function
                return originalUpdateComparativeTable.apply(this, arguments);
            };
        }

        // 2. Add step navigation handler
        let step4Tab = null;
        try {
            // Wrap in try-catch to prevent errors
            step4Tab = document.querySelector('.step-indicator[data-step="4"]');
        } catch (error) {
            console.error('Error getting step 4 tab:', error);
        }
        if (step4Tab) {
            step4Tab.addEventListener('click', function() {
                if (typeof window.captureQuotationPricesForComparative === 'function') {
                    window.captureQuotationPricesForComparative();
                }
            });
        }

        // 3. Handle direct loading of step 4
        let currentStepInput = null;
        try {
            // Use safeDOM if available, otherwise fall back to direct DOM access
            currentStepInput = window.safeDOM ? window.safeDOM.getElementById('current_step') : document.getElementById('current_step');
        } catch (error) {
            console.error('Error getting current step input:', error);
        }
        if (currentStepInput && currentStepInput.value === '4') {
            try {
                // Check if localStorage is available
                if (typeof localStorage !== 'undefined' && localStorage !== null) {
                    // Try to load from localStorage first
                    const backupData = localStorage.getItem('STEP3_PRICE_DATA');
                    if (backupData) {
                        window.capturedQuotationData = JSON.parse(backupData);
                    }
                }
            } catch (e) {
                console.warn('Error retrieving backup data:', e);
            }

            // Also try to capture fresh data
            setTimeout(function() {
                if (typeof window.captureQuotationPricesForComparative === 'function') {
                    window.captureQuotationPricesForComparative();
                }
            }, 500);
        }

        // 4. Add keyboard shortcut for debugging (only in development)
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                    if (typeof window.captureQuotationPricesForComparative === 'function') {
                        window.captureQuotationPricesForComparative();
                    }
                }
            });
        }
    }

    // Initialize data capture when document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setupDataCapture();
        });
    } else {
        // Document already loaded, run setupDataCapture immediately
        setupDataCapture();
    }
        } catch (error) {
            console.error('Error in data capture script:', error);
        }
    // End of self-executing function
    })();
}
</script>

<!-- Templates for dynamic content -->
<!-- Item Tab Template -->
<template id="item-tab-template">
    <li class="mr-2">
        <button type="button" class="item-tab inline-block p-4 border-b-2 border-transparent rounded-t-lg text-gray-500 hover:text-gray-700 hover:border-gray-300">
            <span class="item-tab-name">New Item</span>
        </button>
    </li>
</template>

<!-- Item Template -->
<template id="item-template">
    <div class="item-entry bg-white p-4 rounded-md border border-gray-200 hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Item Name <span class="text-red-500">*</span></label>
                <input type="text" name="items[].name" class="item-name w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Quantity <span class="text-red-500">*</span></label>
                <input type="number" name="items[].quantity" class="item-quantity w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" min="1" value="1" required>
            </div>
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Specifications</label>
            <textarea name="items[].description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50"></textarea>
        </div>
        <div class="flex justify-end">
            <button type="button" class="remove-item px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50">
                <i class="fas fa-trash-alt mr-1"></i> Remove Item
            </button>
        </div>
    </div>
</template>

<!-- Vendor Tab Template -->
<template id="vendor-tab-template">
    <li class="mr-2">
        <button type="button" class="vendor-tab inline-block p-4 border-b-2 border-transparent rounded-t-lg text-gray-500 hover:text-gray-700 hover:border-gray-300">
            <span class="vendor-tab-name">New Vendor</span>
        </button>
    </li>
</template>

<!-- Vendor Template -->
<template id="vendor-template">
    <div class="vendor-entry bg-white p-4 rounded-md border border-gray-200 hidden">
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Vendor <span class="text-red-500">*</span></label>
            <select name="vendors[].vendor_id" class="vendor-select w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" required>
                <option value="">-- Select Vendor --</option>
            </select>
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Quotation Date</label>
            <input type="date" name="vendors[].quotation_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
        </div>
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Upload Quotation (PDF/Image)</label>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div class="space-y-1 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="flex text-sm text-gray-600">
                        <label for="quotation-file" class="relative cursor-pointer bg-white rounded-md font-medium text-it-admin-primary hover:text-it-admin-secondary focus-within:outline-none">
                            <span>Upload a file</span>
                            <input id="quotation-file" name="quotation_file" type="file" class="quotation-file sr-only" accept=".pdf,.jpg,.jpeg,.png">
                        </label>
                        <p class="pl-1">or drag and drop</p>
                    </div>
                    <p class="text-xs text-gray-500">PDF, JPG or PNG up to 10MB</p>
                </div>
            </div>
            <div class="quotation-preview mt-2 hidden">
                <div class="flex items-center p-2 bg-gray-50 rounded-md">
                    <svg class="h-6 w-6 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                    </svg>
                    <span class="quotation-name text-sm text-gray-500"></span>
                    <button type="button" class="remove-quotation ml-auto text-gray-400 hover:text-gray-500">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-800 mb-2">Item Prices</h3>
            <div class="item-prices-container space-y-3">
                <p class="text-gray-500 italic">No items added yet. Add items in the Items tab first.</p>
            </div>
        </div>
        <div class="mb-4">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-800">Total</h3>
                <div class="text-xl font-bold text-it-admin-primary vendor-total">₹ 0.00</div>
            </div>
        </div>
        <div class="flex justify-end">
            <button type="button" class="remove-vendor px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50">
                <i class="fas fa-trash-alt mr-1"></i> Remove Vendor
            </button>
        </div>
    </div>
</template>

<!-- Item Price Template -->
<template id="item-price-template">
    <div class="item-price-entry bg-white p-3 rounded-md border border-gray-200 mb-3">
        <div class="flex justify-between items-center mb-2">
            <input type="text" class="item-name-display font-medium text-gray-800 bg-transparent border-none p-0 w-full" readonly>
            <input type="hidden" class="item-id">
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price (₹) <span class="text-red-500">*</span></label>
                <input type="number" step="0.01" min="0" class="item-unit-price w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" value="0.00" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Total Price (₹)</label>
                <input type="number" step="0.01" min="0" class="item-total-price w-full rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50" value="0.00" readonly>
            </div>
        </div>
    </div>
</template>

<!-- Hidden vendor options template for cloning -->
<select id="vendor-options-template" class="hidden">
    <% if (typeof vendors !== 'undefined' && vendors.length > 0) { %>
        <% vendors.forEach(vendor => { %>
            <option value="<%= vendor.vendor_id %>"><%= vendor.name %></option>
        <% }); %>
    <% } else { %>
        <option value="1">Middha Electro World</option>
        <option value="2">ABC Computers</option>
        <option value="3">XYZ Electronics</option>
        <option value="4">Tech Solutions</option>
        <option value="5">Digital World</option>
    <% } %>
</select>

<!-- Toast container for notifications -->
<div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>

<!-- Main Initialization Script -->
<script>
// Main initialization function - using the enhanced error handling approach
(function() {
    try {
        // Function to initialize the UI when document is ready
        function initializeUI() {
            console.log('Main initialization function running');

            // Get DOM elements using safeDOM
            const stepIndicators = window.safeDOM.querySelectorAll('.step-indicator');
            const stepContents = window.safeDOM.querySelectorAll('.step-content');
            const prevBtn = window.safeDOM.getElementById('prev-step');
            const nextBtn = window.safeDOM.getElementById('next-step');
            const submitBtn = window.safeDOM.getElementById('submit-btn');
            const saveDraftBtn = window.safeDOM.getElementById('save-draft-btn');
            const topSaveBtn = window.safeDOM.getElementById('topSaveBtn');

    // Initialize current step
    let currentStep = parseInt(window.safeDOM.getValue('current_step')) || 1;

    // Set up step navigation
    if (stepIndicators && stepIndicators.length > 0) {
        stepIndicators.forEach((indicator, index) => {
            if (indicator) {
                indicator.addEventListener('click', function() {
                    try {
                        const step = index + 1;

                        // Only allow clicking on completed steps or the next available step
                        if (step <= currentStep) {
                            currentStep = step;
                            updateUI();

                            // Initialize step-specific functionality
                            if (step === 3 && window.initStep3) {
                                window.initStep3();
                            } else if (step === 4) {
                                // Capture quotation data for step 4
                                window.captureQuotationPricesForComparative();

                                // Update comparative table if available
                                if (typeof updateComparativeTable === 'function') {
                                    setTimeout(updateComparativeTable, 300);
                                }
                            }

                            window.scrollTo(0, 0);
                        }
                    } catch (error) {
                        console.error('Error in step indicator click handler:', error);
                    }
                });
            }
        });
    }

    // Set up navigation buttons
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (validateStep()) {
                // Special handling for step transitions
                if (currentStep === 3) {
                    // When moving from Step 3 to Step 4, capture the quotation data
                    window.captureQuotationPricesForComparative();
                }

                currentStep++;
                updateUI();
                window.scrollTo(0, 0);
            }
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            currentStep--;
            updateUI();
            window.scrollTo(0, 0);
        });
    }

    // Set up save draft buttons
    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', function() {
            saveDraft();
        });
    }

    if (topSaveBtn) {
        topSaveBtn.addEventListener('click', function() {
            saveDraft();
        });
    }

    // Set up submit button
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            if (validateStep()) {
                submitForm();
            }
        });
    }

    // Function to update the UI based on the current step
    function updateUI() {
        try {
            // Update step indicators
            if (stepIndicators && stepIndicators.length > 0) {
                stepIndicators.forEach((indicator, index) => {
                    if (!indicator) return;

                    const step = index + 1;

                    // Add or remove active class
                    if (step === currentStep) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }

                    // Add or remove completed class
                    if (step < currentStep) {
                        indicator.classList.add('completed');
                    } else {
                        indicator.classList.remove('completed');
                    }
                });
            }

            // Update step contents
            if (stepContents && stepContents.length > 0) {
                stepContents.forEach((content, index) => {
                    if (!content) return;

                    const step = index + 1;

                    // Show or hide step content
                    if (step === currentStep) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            }

            // Update navigation buttons
            if (prevBtn) {
                if (currentStep > 1) {
                    prevBtn.classList.remove('hidden');
                } else {
                    prevBtn.classList.add('hidden');
                }
            }

            if (nextBtn) {
                if (currentStep < (stepContents ? stepContents.length : 6)) {
                    nextBtn.classList.remove('hidden');
                    submitBtn.classList.add('hidden');
                } else {
                    nextBtn.classList.add('hidden');
                    submitBtn.classList.remove('hidden');
                }
            }

            // Update current step input
            const currentStepInput = window.safeDOM.getElementById('current_step');
            if (currentStepInput) {
                currentStepInput.value = currentStep;
            }

            // Initialize step-specific functionality
            if (currentStep === 3 && window.initStep3) {
                window.initStep3();
            }
        } catch (error) {
            console.error('Error updating UI:', error);
        }
    }

            // Initialize UI
            updateUI();

            console.log('Procurement form initialized successfully');
        }

        // Run initialization when document is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeUI);
        } else {
            // Document already loaded, run initialization immediately
            initializeUI();
        }
    } catch (error) {
        console.error('Error in main initialization script:', error);
    }
})();
</script>