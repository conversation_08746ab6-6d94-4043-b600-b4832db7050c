

<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 bg-it-admin-primary text-white">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">Procurement Reports</h1>
                <a href="/it-admin/procurement" class="px-4 py-2 bg-white text-it-admin-primary rounded-md hover:bg-gray-100 transition">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <div class="p-6">
            <!-- Monthly Procurement Statistics -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Monthly Procurement Statistics</h2>
                <div class="bg-white rounded-lg border overflow-hidden">
                    <% if (monthlyStats.length === 0) { %>
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-chart-bar text-4xl mb-2"></i>
                            <p>No monthly statistics available</p>
                        </div>
                    <% } else { %>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Number of Procurements</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <% monthlyStats.forEach(stat => { %>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <%= new Date(stat.month + '-01').toLocaleDateString('en-US', { year: 'numeric', month: 'long' }) %>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= stat.count %></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= formatCurrency(stat.total_amount || 0) %></td>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Department Statistics -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Department Statistics</h2>
                <div class="bg-white rounded-lg border overflow-hidden">
                    <% if (departmentStats.length === 0) { %>
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-building text-4xl mb-2"></i>
                            <p>No department statistics available</p>
                        </div>
                    <% } else { %>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Number of Procurements</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <% departmentStats.forEach(stat => { %>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= stat.department || 'Unspecified' %></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= stat.count %></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= formatCurrency(stat.total_amount || 0) %></td>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Vendor Statistics -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Top Vendors</h2>
                <div class="bg-white rounded-lg border overflow-hidden">
                    <% if (vendorStats.length === 0) { %>
                        <div class="p-6 text-center text-gray-500">
                            <i class="fas fa-store text-4xl mb-2"></i>
                            <p>No vendor statistics available</p>
                        </div>
                    <% } else { %>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Number of Procurements</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <% vendorStats.forEach(stat => { %>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= stat.vendor_name %></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= stat.request_count %></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= formatCurrency(stat.total_amount || 0) %></td>
                                        </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Export Options -->
            <div class="flex justify-end">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition mr-2">
                    <i class="fas fa-file-excel mr-2"></i> Export to Excel
                </button>
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">
                    <i class="fas fa-file-pdf mr-2"></i> Export to PDF
                </button>
            </div>
        </div>
    </div>
</div>

<%- include('../../partials/footer') %>
