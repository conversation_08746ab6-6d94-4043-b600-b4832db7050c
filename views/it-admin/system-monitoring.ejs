<%- include('../partials/it-admin/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-2">System Monitoring</h1>
    <p class="text-gray-600">Monitor server performance and system health</p>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- CPU Usage Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-700">CPU Usage</h2>
        <div class="text-it-admin-primary">
          <i class="fas fa-microchip text-xl"></i>
        </div>
      </div>
      <div class="flex items-end">
        <div class="text-3xl font-bold text-gray-800"><%= systemData.cpu.usage %>%</div>
        <div class="ml-2 text-sm text-gray-500 mb-1">of <%= systemData.cpu.cores %> cores</div>
      </div>
      <div class="mt-4 w-full bg-gray-200 rounded-full h-2.5">
        <div class="bg-it-admin-primary h-2.5 rounded-full" style="width: <%= systemData.cpu.usage %>%"></div>
      </div>
    </div>

    <!-- Memory Usage Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-700">Memory Usage</h2>
        <div class="text-it-admin-primary">
          <i class="fas fa-memory text-xl"></i>
        </div>
      </div>
      <div class="flex items-end">
        <div class="text-3xl font-bold text-gray-800"><%= systemData.memory.used %> GB</div>
        <div class="ml-2 text-sm text-gray-500 mb-1">of <%= systemData.memory.total %> GB</div>
      </div>
      <div class="mt-4 w-full bg-gray-200 rounded-full h-2.5">
        <% const memoryPercentage = (parseFloat(systemData.memory.used) / parseFloat(systemData.memory.total)) * 100; %>
        <div class="bg-it-admin-primary h-2.5 rounded-full" style="width: <%= memoryPercentage %>%"></div>
      </div>
    </div>

    <!-- Disk Usage Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-700">Disk Usage</h2>
        <div class="text-it-admin-primary">
          <i class="fas fa-hdd text-xl"></i>
        </div>
      </div>
      <div class="flex items-end">
        <div class="text-3xl font-bold text-gray-800"><%= systemData.disk.used %> GB</div>
        <div class="ml-2 text-sm text-gray-500 mb-1">of <%= systemData.disk.total %> GB</div>
      </div>
      <div class="mt-4 w-full bg-gray-200 rounded-full h-2.5">
        <% const diskPercentage = (parseFloat(systemData.disk.used) / parseFloat(systemData.disk.total)) * 100; %>
        <div class="bg-it-admin-primary h-2.5 rounded-full" style="width: <%= diskPercentage %>%"></div>
      </div>
    </div>

    <!-- Network Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-700">Network</h2>
        <div class="text-it-admin-primary">
          <i class="fas fa-network-wired text-xl"></i>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-sm text-gray-500">Upload</p>
          <p class="text-xl font-bold text-gray-800"><%= systemData.network.upload %></p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Download</p>
          <p class="text-xl font-bold text-gray-800"><%= systemData.network.download %></p>
        </div>
      </div>
      <div class="mt-2">
        <p class="text-sm text-gray-500">Active Connections</p>
        <p class="text-lg font-semibold text-gray-800"><%= systemData.network.activeConnections %></p>
      </div>
    </div>
  </div>

  <!-- System Health Section -->
  <div class="mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4">System Health</h2>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uptime</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Check</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center text-it-admin-primary">
                  <i class="fas fa-server"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">Web Server</div>
                  <div class="text-sm text-gray-500">Apache</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Running
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              5 days, 7 hours
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              2 minutes ago
            </td>
          </tr>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center text-it-admin-primary">
                  <i class="fas fa-database"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">Database</div>
                  <div class="text-sm text-gray-500">MySQL</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Running
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              5 days, 7 hours
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              2 minutes ago
            </td>
          </tr>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center text-it-admin-primary">
                  <i class="fas fa-code"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">Application</div>
                  <div class="text-sm text-gray-500">Node.js</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Running
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              2 days, 3 hours
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              2 minutes ago
            </td>
          </tr>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center text-it-admin-primary">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">Firewall</div>
                  <div class="text-sm text-gray-500">System Firewall</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Active
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              5 days, 7 hours
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              2 minutes ago
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Recent System Logs -->
  <div>
    <h2 class="text-xl font-bold text-gray-800 mb-4">Recent System Logs</h2>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <div>
          <select class="rounded-md border-gray-300 shadow-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50 text-sm">
            <option>All Logs</option>
            <option>Error Logs</option>
            <option>Warning Logs</option>
            <option>Info Logs</option>
          </select>
        </div>
        <div>
          <button class="px-4 py-2 bg-it-admin-primary text-white rounded-md hover:bg-it-admin-secondary focus:outline-none focus:ring-2 focus:ring-it-admin-primary focus:ring-opacity-50 text-sm">
            <i class="fas fa-download mr-2"></i> Export Logs
          </button>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01 14:32:15</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                  ERROR
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Database</td>
              <td class="px-6 py-4 text-sm text-gray-500">Connection timeout after 30 seconds</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01 14:30:22</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                  WARNING
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Application</td>
              <td class="px-6 py-4 text-sm text-gray-500">High memory usage detected (85%)</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01 14:28:45</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  INFO
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Web Server</td>
              <td class="px-6 py-4 text-sm text-gray-500">Server restarted successfully</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01 14:25:10</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  INFO
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">System</td>
              <td class="px-6 py-4 text-sm text-gray-500">Scheduled backup completed successfully</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01 14:20:33</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                  WARNING
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Firewall</td>
              <td class="px-6 py-4 text-sm text-gray-500">Multiple failed login attempts detected from IP ************</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Showing 5 of 256 logs
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">Previous</button>
          <button class="px-3 py-1 bg-it-admin-primary text-white rounded-md text-sm hover:bg-it-admin-secondary">Next</button>
        </div>
      </div>
    </div>
  </div>
</div>

<%- include('../partials/it-admin/footer') %>
