<h1 class="text-2xl font-bold mb-6">Device Transactions</h1>

<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold">Device Management</h2>
        <div class="flex space-x-2">
            <a href="/it-admin/transactions/issue" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-share mr-2"></i> Issue Device
            </a>
            <a href="/it-admin/transactions/receive" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
                <i class="fas fa-undo mr-2"></i> Receive Device
            </a>
            <button id="exportCSV" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-file-csv mr-2"></i> Export CSV
            </button>
            <button id="exportPDF" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
                <i class="fas fa-file-pdf mr-2"></i> Export PDF
            </button>
        </div>
    </div>
    <!-- Quick Filters Section -->
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <div class="mb-2 flex items-center justify-between">
            <h3 class="text-sm font-semibold text-gray-700">Quick Filters:</h3>
            <button type="button" id="clearFilters" class="text-xs text-blue-600 hover:text-blue-800">Clear All</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Device Type Filter -->
            <div>
                <label for="deviceTypeFilter" class="block text-xs font-medium text-gray-500 mb-1">Device Type</label>
                <select id="deviceTypeFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Types</option>
                    <% if (locals.categories && categories.length > 0) { %>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.name %>"><%= category.name %></option>
                        <% }); %>
                    <% } %>
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="statusFilter" class="block text-xs font-medium text-gray-500 mb-1">Status</label>
                <select id="statusFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Status</option>
                    <option value="Issued">Issued</option>
                    <option value="Returned">Returned</option>
                </select>
            </div>

            <!-- Issued To Filter -->
            <div>
                <label for="issuedToFilter" class="block text-xs font-medium text-gray-500 mb-1">Issued To</label>
                <input type="text" id="issuedToFilter" placeholder="Filter by user..." class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
            </div>

            <!-- Date Range Filter -->
            <div>
                <label for="dateFilter" class="block text-xs font-medium text-gray-500 mb-1">Date Range</label>
                <select id="dateFilter" class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">Last 3 Months</option>
                </select>
            </div>

            <!-- Search Box -->
            <div>
                <label for="quickSearch" class="block text-xs font-medium text-gray-500 mb-1">Quick Search</label>
                <input type="text" id="quickSearch" placeholder="Search anything..." class="w-full rounded-md border-gray-300 shadow-sm text-sm focus:border-it-admin-primary focus:ring focus:ring-it-admin-primary focus:ring-opacity-50">
            </div>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table id="transactions-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-it-admin-primary">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Device</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Serial Number</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued To</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued By</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Issued Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Expected Return</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% if (transactions && transactions.length > 0) { %>
                    <% transactions.forEach(transaction => { %>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= transaction.item_name %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.category_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.serial_number || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_to_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_by_name || 'N/A' %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.issued_date %></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= transaction.expected_return_date || 'Not specified' %></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <% if (transaction.received_date) { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Returned
                                    </span>
                                <% } else { %>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        Issued
                                    </span>
                                <% } %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <% if (!transaction.received_date) { %>
                                        <a href="/it-admin/transactions/receive?transaction=<%= transaction.transaction_id %>" class="text-it-admin-primary hover:text-it-admin-secondary" title="Receive">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                        <a href="#" class="text-blue-600 hover:text-blue-900 generate-loan-voucher-btn" title="Print Loan Voucher" data-transaction-id="<%= transaction.transaction_id %>">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    <% } else { %>
                                        <a href="#" class="text-green-600 hover:text-green-900 generate-return-voucher-btn" title="Print Return Voucher" data-transaction-id="<%= transaction.transaction_id %>">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    <% } %>
                                    <a href="/it-admin/inventory/items/<%= transaction.item_id %>/issue-history" class="text-indigo-600 hover:text-indigo-900" title="View History">
                                        <i class="fas fa-history"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                <% } else { %>
                    <tr>
                        <td colspan="9" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No device transactions found.
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize filter elements
        const deviceTypeFilter = document.getElementById('deviceTypeFilter');
        const statusFilter = document.getElementById('statusFilter');
        const issuedToFilter = document.getElementById('issuedToFilter');
        const dateFilter = document.getElementById('dateFilter');
        const quickSearch = document.getElementById('quickSearch');
        const clearFiltersBtn = document.getElementById('clearFilters');

        // Get all table rows
        const tableRows = document.querySelectorAll('#transactions-table tbody tr');
        console.log('Found ' + tableRows.length + ' table rows');

        // Store original table state
        const originalTableState = [];

        // Initialize the original table state
        tableRows.forEach(row => {
            // Skip rows that are just placeholders (like "No transactions found")
            if (row.cells.length > 1) {
                originalTableState.push({
                    element: row,
                    visible: true,
                    // Store text content for faster filtering
                    data: {
                        device: row.cells[0]?.textContent.trim().toLowerCase() || '',
                        type: row.cells[1]?.textContent.trim().toLowerCase() || '',
                        serialNumber: row.cells[2]?.textContent.trim().toLowerCase() || '',
                        issuedTo: row.cells[3]?.textContent.trim().toLowerCase() || '',
                        issuedBy: row.cells[4]?.textContent.trim().toLowerCase() || '',
                        issuedDate: row.cells[5]?.textContent.trim() || '',
                        expectedReturn: row.cells[6]?.textContent.trim().toLowerCase() || '',
                        status: row.cells[7]?.querySelector('span')?.textContent.trim() || ''
                    }
                });
            }
        });

        console.log('Initialized ' + originalTableState.length + ' rows for filtering');

        // Function to apply filters
        function applyFilters() {
            console.log('Applying filters...');
            const deviceType = deviceTypeFilter.value.toLowerCase();
            const status = statusFilter.value;
            const issuedTo = issuedToFilter.value.toLowerCase();
            const date = dateFilter.value;
            const search = quickSearch.value.toLowerCase();

            console.log('Filter values:', { deviceType, status, issuedTo, date, search });

            // Reset visibility
            originalTableState.forEach(item => {
                item.visible = true;
            });

            // Apply device type filter
            if (deviceType) {
                originalTableState.forEach(item => {
                    if (!item.data.type.includes(deviceType)) {
                        item.visible = false;
                    }
                });
            }

            // Apply status filter
            if (status) {
                originalTableState.forEach(item => {
                    if (!item.visible) return;

                    if (item.data.status !== status) {
                        item.visible = false;
                    }
                });
            }

            // Apply issued to filter
            if (issuedTo) {
                originalTableState.forEach(item => {
                    if (!item.visible) return;

                    if (!item.data.issuedTo.includes(issuedTo)) {
                        item.visible = false;
                    }
                });
            }

            // Apply date filter
            if (date) {
                const today = new Date();
                const oneDay = 24 * 60 * 60 * 1000; // milliseconds in a day

                originalTableState.forEach(item => {
                    if (!item.visible) return;

                    const dateText = item.data.issuedDate;
                    if (!dateText) return;

                    try {
                        // Try to parse the date (handle different formats)
                        let rowDate;

                        // Check if date is in MM/DD/YYYY format
                        if (dateText.includes('/')) {
                            const dateParts = dateText.split('/');
                            if (dateParts.length === 3) {
                                rowDate = new Date(dateParts[2], dateParts[0] - 1, dateParts[1]);
                            }
                        }
                        // Check if date is in DD-Mon-YYYY format (e.g., 19-May-2025)
                        else if (dateText.includes('-')) {
                            const months = {
                                'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
                                'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
                            };

                            const dateParts = dateText.split('-');
                            if (dateParts.length === 3) {
                                const day = parseInt(dateParts[0]);
                                const month = months[dateParts[1]];
                                const year = parseInt(dateParts[2]);

                                if (!isNaN(day) && month !== undefined && !isNaN(year)) {
                                    rowDate = new Date(year, month, day);
                                }
                            }
                        }

                        if (!rowDate || isNaN(rowDate.getTime())) {
                            return; // Skip if date couldn't be parsed
                        }

                        const diffDays = Math.round(Math.abs((today - rowDate) / oneDay));

                        if (date === 'today' && diffDays > 0) {
                            item.visible = false;
                        } else if (date === 'week' && diffDays > 7) {
                            item.visible = false;
                        } else if (date === 'month' && diffDays > 30) {
                            item.visible = false;
                        } else if (date === 'quarter' && diffDays > 90) {
                            item.visible = false;
                        }
                    } catch (e) {
                        console.error('Error parsing date:', e);
                    }
                });
            }

            // Apply quick search
            if (search) {
                originalTableState.forEach(item => {
                    if (!item.visible) return;

                    // Check all data fields
                    const allData = Object.values(item.data).join(' ').toLowerCase();
                    if (!allData.includes(search)) {
                        item.visible = false;
                    }
                });
            }

            // Update visibility
            let visibleCount = 0;
            originalTableState.forEach(item => {
                item.element.style.display = item.visible ? '' : 'none';
                if (item.visible) visibleCount++;
            });

            console.log('Filter applied: ' + visibleCount + ' rows visible');

            // Show "no results" message if all rows are hidden
            const tbody = document.querySelector('#transactions-table tbody');
            let noResultsRow = document.querySelector('#no-results-row');

            if (visibleCount === 0) {
                if (!noResultsRow) {
                    noResultsRow = document.createElement('tr');
                    noResultsRow.id = 'no-results-row';
                    noResultsRow.innerHTML = `
                        <td colspan="9" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                            No matching transactions found. Try adjusting your filters.
                        </td>
                    `;
                    tbody.appendChild(noResultsRow);
                } else {
                    noResultsRow.style.display = '';
                }
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }

        // Function to reset filters
        function resetFilters() {
            console.log('Resetting filters');
            deviceTypeFilter.value = '';
            statusFilter.value = '';
            issuedToFilter.value = '';
            dateFilter.value = '';
            quickSearch.value = '';

            // Reset table visibility
            originalTableState.forEach(item => {
                item.visible = true;
                item.element.style.display = '';
            });

            // Hide "no results" message
            const noResultsRow = document.querySelector('#no-results-row');
            if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }

            console.log('Filters reset, all rows visible');
        }

        // Add event listeners
        if (deviceTypeFilter) deviceTypeFilter.addEventListener('change', applyFilters);
        if (statusFilter) statusFilter.addEventListener('change', applyFilters);
        if (issuedToFilter) issuedToFilter.addEventListener('input', applyFilters);
        if (dateFilter) dateFilter.addEventListener('change', applyFilters);
        if (quickSearch) quickSearch.addEventListener('input', applyFilters);
        if (clearFiltersBtn) clearFiltersBtn.addEventListener('click', resetFilters);

        // Export to CSV functionality
        const exportCSVButton = document.getElementById('exportCSV');
        if (exportCSVButton) {
            exportCSVButton.addEventListener('click', function() {
                exportTableToCSV('transactions-table', 'device_transactions.csv');
            });
        }

        // Export to PDF functionality
        const exportPDFButton = document.getElementById('exportPDF');
        if (exportPDFButton) {
            exportPDFButton.addEventListener('click', function() {
                // First create a printable version of the table
                const table = document.querySelector('table');
                const printWindow = window.open('', '_blank');

                printWindow.document.write(`
                    <html>
                    <head>
                        <title>Device Transactions Export</title>
                        <style>
                            body { font-family: Arial, sans-serif; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #2c5530; color: white; }
                            tr:nth-child(even) { background-color: #f2f2f2; }
                            h1 { color: #2c5530; }
                            .status-issued { color: #1e40af; font-weight: bold; }
                            .status-returned { color: #047857; font-weight: bold; }
                        </style>
                    </head>
                    <body>
                        <h1>Device Transactions Report</h1>
                        <p>Generated on: ${new Date().toLocaleString()}</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>Device</th>
                                    <th>Type</th>
                                    <th>Serial Number</th>
                                    <th>Issued To</th>
                                    <th>Issued By</th>
                                    <th>Issued Date</th>
                                    <th>Expected Return</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                `);

                // Add visible table rows only
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    if (row.style.display !== 'none' && row.cells.length > 1 && row.id !== 'no-results-row') {
                        const device = row.cells[0].textContent.trim();
                        const type = row.cells[1].textContent.trim();
                        const serialNumber = row.cells[2].textContent.trim();
                        const issuedTo = row.cells[3].textContent.trim();
                        const issuedBy = row.cells[4].textContent.trim();
                        const issuedDate = row.cells[5].textContent.trim();
                        const expectedReturn = row.cells[6].textContent.trim();

                        // Get status with class
                        const statusCell = row.cells[7];
                        const statusSpan = statusCell.querySelector('span');
                        const status = statusSpan ? statusSpan.textContent.trim() : '';
                        const statusClass = status.toLowerCase();

                        printWindow.document.write(`
                            <tr>
                                <td>${device}</td>
                                <td>${type}</td>
                                <td>${serialNumber}</td>
                                <td>${issuedTo}</td>
                                <td>${issuedBy}</td>
                                <td>${issuedDate}</td>
                                <td>${expectedReturn}</td>
                                <td class="status-${statusClass}">${status}</td>
                            </tr>
                        `);
                    }
                });

                printWindow.document.write(`
                            </tbody>
                        </table>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.focus();

                // Print after a short delay to ensure styles are loaded
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            });
        }

        // Function to export table to CSV
        function exportTableToCSV(tableId, filename) {
            const table = document.querySelector('table');
            if (!table) return;

            let csv = [];

            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => {
                headers.push('"' + cell.textContent.trim() + '"');
            });
            csv.push(headers.join(','));

            // Get visible rows only
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (row.style.display !== 'none' && row.cells.length > 1 && row.id !== 'no-results-row') {
                    const rowData = [];
                    // Skip the last column (Actions)
                    for (let i = 0; i < row.cells.length - 1; i++) {
                        const cell = row.cells[i];
                        // For status, get the text from the span
                        if (i === 7) {
                            const span = cell.querySelector('span');
                            rowData.push('"' + (span ? span.textContent.trim() : '') + '"');
                        } else {
                            rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
                        }
                    }
                    csv.push(rowData.join(','));
                }
            });

            // Create CSV content
            const csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', filename);
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);
        }
    });
</script>

<!-- Include the voucher JavaScript files -->
<script src="/js/inventory/return-voucher.js"></script>
<script src="/js/inventory/loan-voucher.js"></script>
