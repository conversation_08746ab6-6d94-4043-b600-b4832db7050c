<h1 class="text-2xl font-bold mb-6">Add Repair Vendor</h1>

<div class="bg-white rounded-lg shadow p-6">
  <form action="/it-admin/vendors/add" method="POST" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Vendor Name <span class="text-red-600">*</span></label>
          <input type="text" id="name" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
        </div>
        
        <div>
          <label for="contact_person" class="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
          <input type="text" id="contact_person" name="contact_person" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
        </div>
        
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
          <input type="text" id="phone" name="phone" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
        </div>
        
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
          <input type="email" id="email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
        </div>
      </div>
      
      <div class="space-y-4">
        <div>
          <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
          <textarea id="address" name="address" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary"></textarea>
        </div>
        
        <div>
          <label for="specialization" class="block text-sm font-medium text-gray-700 mb-1">Specialization</label>
          <select id="specialization" name="specialization" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary">
            <option value="">-- Select Specialization --</option>
            <option value="Laptops">Laptops</option>
            <option value="Desktops">Desktops</option>
            <option value="Printers">Printers</option>
            <option value="Networking">Networking Equipment</option>
            <option value="Projectors">Projectors</option>
            <option value="General">General IT Repairs</option>
            <option value="Other">Other</option>
          </select>
        </div>
        
        <div>
          <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea id="notes" name="notes" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-it-admin-primary"></textarea>
        </div>
      </div>
    </div>
    
    <div class="flex justify-end space-x-3">
      <a href="/it-admin/vendors" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">
        Cancel
      </a>
      <button type="submit" class="px-4 py-2 bg-it-admin-primary text-white rounded hover:bg-it-admin-secondary transition">
        Add Vendor
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize chosen for select dropdowns if needed
    if ($.fn.chosen) {
      $('#specialization').chosen({
        width: '100%',
        search_contains: true,
        placeholder_text_single: 'Select Specialization',
        allow_single_deselect: true
      });
    }
  });
</script>
