<h1 class="text-2xl font-bold mb-6">Repair Vendors</h1>

<div class="bg-white rounded-lg shadow p-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <p class="text-gray-600">Manage repair partners and service providers</p>
    </div>
    <div>
      <a href="/it-admin/vendors/add" class="bg-it-admin-primary text-white px-4 py-2 rounded hover:bg-it-admin-secondary transition">
        <i class="fas fa-plus mr-2"></i> Add Vendor
      </a>
    </div>
  </div>
  
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialization</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Repairs</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% if (vendors && vendors.length > 0) { %>
          <% vendors.forEach(vendor => { %>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#<%= vendor.vendor_id %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= vendor.name %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= vendor.contact_person || 'N/A' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= vendor.phone || 'N/A' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= vendor.specialization || 'General' %></td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= vendor.repair_count || 0 %></td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                  <% if (vendor.is_active) { %>
                    bg-green-100 text-green-800
                  <% } else { %>
                    bg-gray-100 text-gray-800
                  <% } %>">
                  <%= vendor.is_active ? 'Active' : 'Inactive' %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <a href="/it-admin/vendors/<%= vendor.vendor_id %>" class="text-it-admin-primary hover:text-it-admin-secondary" title="View">
                    <i class="fas fa-eye"></i>
                  </a>
                  <a href="/it-admin/vendors/<%= vendor.vendor_id %>/edit" class="text-indigo-600 hover:text-indigo-900" title="Edit">
                    <i class="fas fa-edit"></i>
                  </a>
                  <% if (vendor.repair_count == 0) { %>
                    <button class="text-red-600 hover:text-red-900 delete-vendor" data-id="<%= vendor.vendor_id %>" title="Delete">
                      <i class="fas fa-trash"></i>
                    </button>
                  <% } %>
                </div>
              </td>
            </tr>
          <% }); %>
        <% } else { %>
          <tr>
            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">No vendors found</td>
          </tr>
        <% } %>
      </tbody>
    </table>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
      <p class="text-gray-600 mt-2">Are you sure you want to delete this vendor? This action cannot be undone.</p>
    </div>
    <div class="flex justify-end space-x-3">
      <button id="cancelDelete" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">
        Cancel
      </button>
      <form id="deleteVendorForm" method="POST">
        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">
          Delete
        </button>
      </form>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-vendor');
    const deleteConfirmationModal = document.getElementById('deleteConfirmationModal');
    const cancelDeleteButton = document.getElementById('cancelDelete');
    const deleteVendorForm = document.getElementById('deleteVendorForm');
    
    // Show delete confirmation modal
    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const vendorId = this.getAttribute('data-id');
        deleteVendorForm.action = `/it-admin/vendors/${vendorId}/delete`;
        deleteConfirmationModal.classList.remove('hidden');
      }, { passive: true });
    });
    
    // Hide delete confirmation modal
    cancelDeleteButton.addEventListener('click', function() {
      deleteConfirmationModal.classList.add('hidden');
    }, { passive: true });
    
    // Close modal when clicking outside
    deleteConfirmationModal.addEventListener('click', function(e) {
      if (e.target === deleteConfirmationModal) {
        deleteConfirmationModal.classList.add('hidden');
      }
    }, { passive: true });
  });
</script>
