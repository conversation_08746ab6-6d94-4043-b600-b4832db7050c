<h1 class="text-2xl font-bold mb-6">Vendor Details</h1>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Vendor Information Card -->
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold text-it-admin-primary">Vendor Information</h2>
      <span class="px-2 py-1 text-xs font-semibold rounded-full
        <% if (vendor.is_active) { %>
          bg-green-100 text-green-800
        <% } else { %>
          bg-gray-100 text-gray-800
        <% } %>">
        <%= vendor.is_active ? 'Active' : 'Inactive' %>
      </span>
    </div>
    
    <div class="space-y-4">
      <div>
        <h3 class="text-sm font-medium text-gray-500">Vendor Name</h3>
        <p class="text-gray-900"><%= vendor.name %></p>
      </div>
      
      <div>
        <h3 class="text-sm font-medium text-gray-500">Contact Person</h3>
        <p class="text-gray-900"><%= vendor.contact_person || 'Not specified' %></p>
      </div>
      
      <div>
        <h3 class="text-sm font-medium text-gray-500">Phone</h3>
        <p class="text-gray-900"><%= vendor.phone || 'Not specified' %></p>
      </div>
      
      <div>
        <h3 class="text-sm font-medium text-gray-500">Email</h3>
        <p class="text-gray-900"><%= vendor.email || 'Not specified' %></p>
      </div>
      
      <div>
        <h3 class="text-sm font-medium text-gray-500">Address</h3>
        <p class="text-gray-900"><%= vendor.address || 'Not specified' %></p>
      </div>
      
      <div>
        <h3 class="text-sm font-medium text-gray-500">Specialization</h3>
        <p class="text-gray-900"><%= vendor.specialization || 'General' %></p>
      </div>
      
      <% if (vendor.notes) { %>
        <div>
          <h3 class="text-sm font-medium text-gray-500">Notes</h3>
          <p class="text-gray-900"><%= vendor.notes %></p>
        </div>
      <% } %>
    </div>
    
    <div class="mt-6 flex space-x-3">
      <a href="/it-admin/vendors/<%= vendor.vendor_id %>/edit" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition">
        <i class="fas fa-edit mr-2"></i> Edit
      </a>
      <a href="/it-admin/vendors" class="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition">
        <i class="fas fa-arrow-left mr-2"></i> Back to List
      </a>
    </div>
  </div>
  
  <!-- Repair History Card -->
  <div class="bg-white rounded-lg shadow p-6 lg:col-span-2">
    <h2 class="text-lg font-semibold text-it-admin-primary mb-4">Repair History</h2>
    
    <% if (repairs && repairs.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% repairs.forEach(repair => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#<%= repair.repair_id %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= repair.item_name %><br>
                  <span class="text-xs text-gray-500"><%= repair.serial_number || 'No S/N' %></span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(repair.sent_date) %></td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    <% if (repair.status === 'sent') { %>
                      bg-blue-100 text-blue-800
                    <% } else if (repair.status === 'in_progress') { %>
                      bg-yellow-100 text-yellow-800
                    <% } else if (repair.status === 'completed') { %>
                      bg-green-100 text-green-800
                    <% } else if (repair.status === 'cancelled') { %>
                      bg-red-100 text-red-800
                    <% } %>">
                    <%= repair.status.replace('_', ' ').toUpperCase() %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDate(repair.received_date) %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/it-admin/repair/history/<%= repair.repair_id %>" class="text-indigo-600 hover:text-indigo-900">View</a>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-4">
        <p class="text-gray-500">No repair history found for this vendor.</p>
      </div>
    <% } %>
  </div>
</div>
