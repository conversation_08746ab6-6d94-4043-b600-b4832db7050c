<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - <%= typeof __ !== 'undefined' ? __('app.name') : 'Exam Prep Platform' %></title>
  <!-- Tailwind CSS -->
  <link rel="stylesheet" href="/styles.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <!-- Toastify Library -->
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script src="/js/toast-notifications.js"></script>
  <link rel="stylesheet" href="/css/app.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="/js/app.js"></script>
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #e9d5ff;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a855f7;
    }

    /* Background overlay */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.4));
      z-index: -1;
    }

    /* Form container animation */
    .auth-container {
      animation: fadeIn 0.8s ease-in-out;
      position: relative;
      z-index: 10;
      backdrop-filter: blur(5px);
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Prevent any background text from overlapping with content */
    .min-h-screen::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      z-index: 1;
    }

    /* Language selector styles */
    #language-menu-button-auth {
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      border-radius: 9999px;
      padding: 0.5rem 0.75rem;
    }

    #language-menu-button-auth:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #language-menu-button-auth:active {
      transform: translateY(0);
    }

    /* Language overlay animation */
    #language-overlay {
      transition: background-color 0.3s ease;
    }

    #language-overlay > div {
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    /* Language option hover effects */
    .language-option {
      transition: all 0.2s ease;
    }

    .language-option:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Multi-step form styles */
    .step-indicator.active .w-8 {
      background-color: #4f46e5;
      color: white;
    }

    .step-indicator.completed .w-8 {
      background-color: #10b981;
      color: white;
    }

    .step-indicator.completed span {
      color: #10b981;
    }

    .step-indicator {
      transition: all 0.2s ease-in-out;
    }

    .step-indicator:hover {
      opacity: 0.9;
    }

    .form-step {
      transition: all 0.3s ease-in-out;
    }
  </style>
  <%- defineContent('style') %>
</head>
<body class="bg-gray-100 min-h-screen">

<div class="min-h-screen flex flex-col items-center justify-center bg-cover bg-center relative" style="background-image: url('/images/examprepgu.png'); position: relative; overflow: hidden;">
  <!-- Language Switcher Button in Top Right Corner -->
  <div class="absolute top-4 right-4 z-10">
    <button type="button" class="flex items-center text-white bg-black/30 hover:bg-black/40 px-3 py-1.5 rounded-md transition" id="language-menu-button-auth">
      <% if (typeof currentLanguage !== 'undefined') { %>
        <% if (currentLanguage === 'en') { %>
          <span class="mr-1">English</span>
        <% } else if (currentLanguage === 'pa') { %>
          <span class="mr-1">ਪੰਜਾਬੀ</span>
        <% } %>
      <% } else { %>
        <span class="mr-1">English</span>
      <% } %>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <!-- Language Overlay Modal -->
  <div id="language-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden transform scale-95 opacity-0 transition-all duration-300">
      <div class="flex justify-between items-center border-b px-6 py-4">
        <h3 class="text-lg font-medium text-gray-900">Select Language</h3>
        <button id="close-language-overlay" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="px-6 py-4">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <a href="/language/switch/en" class="language-option flex items-center p-3 rounded-lg hover:bg-gray-50 transition <%= currentLanguage === 'en' ? 'bg-indigo-50 border border-indigo-200' : '' %>">
            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-indigo-100 text-indigo-500">
              <span class="text-lg font-bold">EN</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900">English</p>
              <p class="text-xs text-gray-500">English</p>
            </div>
            <% if (currentLanguage === 'en') { %>
              <div class="ml-auto">
                <svg class="h-5 w-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              </div>
            <% } %>
          </a>

          <a href="/language/switch/pa" class="language-option flex items-center p-3 rounded-lg hover:bg-gray-50 transition <%= currentLanguage === 'pa' ? 'bg-indigo-50 border border-indigo-200' : '' %>">
            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-indigo-100 text-indigo-500">
              <span class="text-lg font-bold">ਪੰ</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900">ਪੰਜਾਬੀ</p>
              <p class="text-xs text-gray-500">Punjabi</p>
            </div>
            <% if (currentLanguage === 'pa') { %>
              <div class="ml-auto">
                <svg class="h-5 w-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              </div>
            <% } %>
          </a>

          <!-- Additional languages can be added here -->
          <a href="#" class="language-option flex items-center p-3 rounded-lg hover:bg-gray-50 transition">
            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-500">
              <span class="text-lg font-bold">हि</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900">हिन्दी</p>
              <p class="text-xs text-gray-500">Hindi</p>
            </div>
          </a>

          <a href="#" class="language-option flex items-center p-3 rounded-lg hover:bg-gray-50 transition">
            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-500">
              <span class="text-lg font-bold">ગુ</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-900">ગુજરાતી</p>
              <p class="text-xs text-gray-500">Gujarati</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div class="mb-8 text-center">
    <!-- Site Logo -->
    <% if (locals.siteSettings && siteSettings.site_logo) { %>
      <div class="flex justify-center mb-4">
        <img src="<%= siteSettings.site_logo %>" alt="<%= typeof __ !== 'undefined' ? __('app.name') : 'Exam Prep Platform' %>" class="h-20 w-20 object-cover rounded-md border-2 border-white/50 shadow-lg">
      </div>
    <% } %>
    <h1 class="text-3xl font-bold text-white drop-shadow-lg"><%= typeof __ !== 'undefined' ? __('app.name') : 'Exam Prep Platform' %></h1>
    <p class="text-white text-xl mt-2 drop-shadow-md">Senior Secondary Residential School for Meritorious Students, Ludhiana</p>
  </div>

  <%- body %>
</div>

<%- contentFor('script') %>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Language overlay functionality
    const languageButton = document.getElementById('language-menu-button-auth');
    const languageOverlay = document.getElementById('language-overlay');
    const closeLanguageOverlay = document.getElementById('close-language-overlay');

    if (languageButton && languageOverlay) {
      // Show overlay when language button is clicked
      languageButton.addEventListener('click', function(e) {
        e.preventDefault();
        document.body.style.overflow = 'hidden'; // Prevent scrolling when overlay is open

        // First display the overlay with flex
        languageOverlay.style.display = 'flex';

        // Force a reflow to ensure the transition works
        void languageOverlay.offsetWidth;

        // Add animation classes
        setTimeout(() => {
          languageOverlay.querySelector('div').classList.add('scale-100', 'opacity-100');
        }, 10);
      });

      // Close overlay when close button is clicked
      if (closeLanguageOverlay) {
        closeLanguageOverlay.addEventListener('click', function() {
          closeLanguageModal();
        });
      }

      // Close overlay when clicking outside the modal
      languageOverlay.addEventListener('click', function(e) {
        if (e.target === languageOverlay) {
          closeLanguageModal();
        }
      });

      // Close overlay when ESC key is pressed
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !languageOverlay.classList.contains('hidden')) {
          closeLanguageModal();
        }
      });

      function closeLanguageModal() {
        document.body.style.overflow = ''; // Restore scrolling

        // First remove the scale and opacity classes for animation
        const modalContent = languageOverlay.querySelector('div');
        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        // After animation completes, hide the overlay
        setTimeout(() => {
          languageOverlay.style.display = 'none';
        }, 300); // Match this with the CSS transition duration
      }
    }
  });
</script>

<%- defineContent('script') %>
</body>
</html>
