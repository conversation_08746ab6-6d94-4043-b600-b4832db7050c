<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - <%= __('app.name') %></title>
  <link rel="stylesheet" href="/styles.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Toastify Library -->
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script src="/js/toast-notifications.js"></script>
  <link rel="stylesheet" href="/css/app.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <script src="/js/app.js"></script>
  <%- style %>
  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #e9d5ff;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a855f7;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col <%= locals.isLoggedIn ? 'logged-in' : '' %>" data-user-id="<%= locals.userId || '' %>">
  <%- body %>

  <!-- WebSocket Client Script -->
  <script src="/js/websocket-client.js"></script>
  <%- script %>
</body>
</html>