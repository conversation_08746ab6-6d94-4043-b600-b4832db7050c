<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | IT Admin</title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/it-admin.css">
    <link rel="stylesheet" href="/css/chosen.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/js/chosen.jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <%- style %>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col it-admin-scrollbar">
    <!-- Header -->
    <header class="bg-it-admin-primary text-white shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="/it-admin/dashboard" class="text-xl font-bold">IT Admin Portal</a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button id="notification-btn" class="text-white hover:text-it-admin-light">
                        <i class="fas fa-bell"></i>
                        <% if (typeof notificationCount !== 'undefined' && notificationCount > 0) { %>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                <%= notificationCount %>
                            </span>
                        <% } %>
                    </button>
                    <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-20">
                        <div class="p-3 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-700">Notifications</h3>
                        </div>
                        <div id="notification-list" class="max-h-96 overflow-y-auto">
                            <div class="p-4 text-center text-gray-500">
                                <p>Loading notifications...</p>
                            </div>
                        </div>
                        <div class="p-2 border-t border-gray-200 text-center">
                            <a href="/notifications" class="text-it-admin-primary hover:text-it-admin-secondary text-sm">View All</a>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <button id="profile-dropdown-btn" class="flex items-center space-x-2 text-white hover:text-it-admin-light">
                        <% if (user && user.profile_image) { %>
                            <img src="<%= user.profile_image %>" alt="Profile" class="h-8 w-8 rounded-full object-cover">
                        <% } else { %>
                            <div class="h-8 w-8 rounded-full bg-it-admin-accent flex items-center justify-center">
                                <span class="text-white font-semibold"><%= user.username ? user.username.charAt(0).toUpperCase() : 'U' %></span>
                            </div>
                        <% } %>
                        <span><%= user.username || 'User' %></span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div id="profile-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20">
                        <a href="/profile" class="block px-4 py-2 text-gray-700 hover:bg-it-admin-light">
                            <i class="fas fa-user mr-2"></i> Profile
                        </a>
                        <a href="/profile/settings" class="block px-4 py-2 text-gray-700 hover:bg-it-admin-light">
                            <i class="fas fa-cog mr-2"></i> Settings
                        </a>
                        <div class="border-t border-gray-200 my-1"></div>
                        <a href="/logout" class="block px-4 py-2 text-gray-700 hover:bg-it-admin-light">
                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex flex-1">
        <!-- Sidebar -->
        <aside class="bg-it-admin-primary text-white w-64 min-h-screen hidden md:block">
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="/it-admin/dashboard" class="block py-2 px-4 rounded <%= currentPage === 'dashboard' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/inventory" class="block py-2 px-4 rounded <%= currentPage === 'inventory' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-laptop mr-2"></i> IT Inventory
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/transactions" class="block py-2 px-4 rounded <%= currentPage === 'transactions' || currentPage === 'laptop-transactions' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-exchange-alt mr-2"></i> Transactions
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/vendors" class="block py-2 px-4 rounded <%= currentPage === 'vendors' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-building mr-2"></i> Repair Vendors
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/procurement" class="block py-2 px-4 rounded <%= currentPage === 'procurement' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-shopping-cart mr-2"></i> Procurement
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/system-monitoring" class="block py-2 px-4 rounded <%= currentPage === 'system-monitoring' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-chart-line mr-2"></i> System Monitoring
                        </a>
                    </li>
                    <li>
                        <a href="/it-admin/calendar" class="block py-2 px-4 rounded <%= currentPage === 'calendar' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-calendar-alt mr-2"></i> Calendar
                        </a>
                    </li>

                    <li class="pt-4 border-t border-it-admin-accent">
                        <a href="/it-admin/issues" class="block py-2 px-4 rounded <%= currentPage === 'issues' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-exclamation-circle mr-2"></i> IT Issue Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="/help" class="block py-2 px-4 rounded <%= currentPage === 'help' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                            <i class="fas fa-question-circle mr-2"></i> Help
                        </a>
                    </li>
                </ul>


            </nav>
        </aside>

        <!-- Mobile Sidebar Toggle -->
        <div class="md:hidden fixed bottom-4 right-4 z-10">
            <button id="mobile-sidebar-toggle" class="bg-it-admin-primary text-white p-3 rounded-full shadow-lg">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Sidebar -->
        <div id="mobile-sidebar" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-20 hidden">
            <div class="bg-it-admin-primary text-white w-64 min-h-screen">
                <div class="flex justify-between items-center p-4 border-b border-it-admin-accent">
                    <h2 class="text-xl font-bold">Menu</h2>
                    <button id="close-mobile-sidebar" class="text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <nav class="p-4">
                    <ul class="space-y-2">
                        <li>
                            <a href="/it-admin/dashboard" class="block py-2 px-4 rounded <%= currentPage === 'dashboard' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/inventory" class="block py-2 px-4 rounded <%= currentPage === 'inventory' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-laptop mr-2"></i> IT Inventory
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/transactions" class="block py-2 px-4 rounded <%= currentPage === 'transactions' || currentPage === 'laptop-transactions' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-exchange-alt mr-2"></i> Transactions
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/vendors" class="block py-2 px-4 rounded <%= currentPage === 'vendors' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-building mr-2"></i> Repair Vendors
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/procurement" class="block py-2 px-4 rounded <%= currentPage === 'procurement' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-shopping-cart mr-2"></i> Procurement
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/system-monitoring" class="block py-2 px-4 rounded <%= currentPage === 'system-monitoring' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-chart-line mr-2"></i> System Monitoring
                            </a>
                        </li>
                        <li>
                            <a href="/it-admin/calendar" class="block py-2 px-4 rounded <%= currentPage === 'calendar' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-calendar-alt mr-2"></i> Calendar
                            </a>
                        </li>

                        <li class="pt-4 border-t border-it-admin-accent">
                            <a href="/it-admin/issues" class="block py-2 px-4 rounded <%= currentPage === 'issues' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-exclamation-circle mr-2"></i> IT Issue Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="block py-2 px-4 rounded <%= currentPage === 'help' ? 'bg-it-admin-secondary' : 'hover:bg-it-admin-hover' %>">
                                <i class="fas fa-question-circle mr-2"></i> Help
                            </a>
                        </li>
                    </ul>


                </nav>
            </div>
        </div>

        <!-- Content -->
        <main class="flex-1 p-4">
            <%- body %>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-it-admin-primary text-white py-4">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; <%= new Date().getFullYear() %> Senior Secondary Residential School for Meritorious Students, Ludhiana. All rights reserved.</p>
        </div>
    </footer>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script>
        // Notification dropdown toggle
        const notificationBtn = document.getElementById('notification-btn');
        const notificationDropdown = document.getElementById('notification-dropdown');

        if (notificationBtn && notificationDropdown) {
            notificationBtn.addEventListener('click', function() {
                notificationDropdown.classList.toggle('hidden');
                // Load notifications
                loadNotifications();
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!notificationBtn.contains(event.target) && !notificationDropdown.contains(event.target)) {
                    notificationDropdown.classList.add('hidden');
                }
            });
        }

        // Profile dropdown toggle
        const profileDropdownBtn = document.getElementById('profile-dropdown-btn');
        const profileDropdown = document.getElementById('profile-dropdown');

        if (profileDropdownBtn && profileDropdown) {
            profileDropdownBtn.addEventListener('click', function() {
                profileDropdown.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!profileDropdownBtn.contains(event.target) && !profileDropdown.contains(event.target)) {
                    profileDropdown.classList.add('hidden');
                }
            });
        }

        // Mobile sidebar toggle
        const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const closeMobileSidebar = document.getElementById('close-mobile-sidebar');

        if (mobileSidebarToggle && mobileSidebar && closeMobileSidebar) {
            mobileSidebarToggle.addEventListener('click', function() {
                mobileSidebar.classList.remove('hidden');
            });

            closeMobileSidebar.addEventListener('click', function() {
                mobileSidebar.classList.add('hidden');
            });

            // Close sidebar when clicking outside
            mobileSidebar.addEventListener('click', function(event) {
                if (event.target === mobileSidebar) {
                    mobileSidebar.classList.add('hidden');
                }
            });
        }

        // Load notifications function
        function loadNotifications() {
            const notificationList = document.getElementById('notification-list');

            if (notificationList) {
                fetch('/notifications/api')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.notifications.length > 0) {
                            let html = '';

                            data.notifications.forEach(notification => {
                                const date = new Date(notification.created_at);
                                const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

                                html += `
                                    <div class="p-3 border-b border-gray-200 ${notification.is_read ? '' : 'bg-blue-50'}">
                                        <div class="flex justify-between items-start">
                                            <h4 class="text-sm font-semibold text-gray-700">${notification.title}</h4>
                                            <span class="text-xs text-gray-500">${formattedDate}</span>
                                        </div>
                                        <p class="text-sm text-gray-600 mt-1">${notification.message}</p>
                                        ${notification.is_read ? '' : `
                                            <button class="mark-read-btn text-xs text-blue-600 hover:text-blue-800 mt-2" data-id="${notification.id}">
                                                Mark as read
                                            </button>
                                        `}
                                    </div>
                                `;
                            });

                            notificationList.innerHTML = html;

                            // Add event listeners to mark as read buttons
                            const markReadBtns = document.querySelectorAll('.mark-read-btn');
                            markReadBtns.forEach(btn => {
                                btn.addEventListener('click', function() {
                                    const notificationId = this.getAttribute('data-id');
                                    markNotificationAsRead(notificationId);
                                });
                            });
                        } else {
                            notificationList.innerHTML = `
                                <div class="p-4 text-center text-gray-500">
                                    <p>No notifications</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error loading notifications:', error);
                        notificationList.innerHTML = `
                            <div class="p-4 text-center text-gray-500">
                                <p>Error loading notifications</p>
                            </div>
                        `;
                    });
            }
        }

        // Mark notification as read function
        function markNotificationAsRead(notificationId) {
            fetch(`/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload notifications
                        loadNotifications();

                        // Update notification count
                        const notificationCount = document.querySelector('#notification-btn span');
                        if (notificationCount) {
                            const count = parseInt(notificationCount.textContent) - 1;
                            if (count > 0) {
                                notificationCount.textContent = count;
                            } else {
                                notificationCount.remove();
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                });
        }

        // Show toast if available
        <% if (locals.toast && locals.toast.type && locals.toast.message) { %>
            showToast('<%= locals.toast.message %>', '<%= locals.toast.type %>');
        <% } else if (locals.flashSuccess) { %>
            showToast('<%= locals.flashSuccess %>', 'success');
        <% } else if (locals.flashError) { %>
            showToast('<%= locals.flashError %>', 'error');
        <% } %>

        // Toast function
        function showToast(message, type = 'info') {
            Toastify({
                text: message,
                duration: 3000,
                close: true,
                gravity: "bottom",
                position: "right",
                backgroundColor: type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6',
                stopOnFocus: true
            }).showToast();
        }
    </script>
    <%- script %>
</body>
</html>
