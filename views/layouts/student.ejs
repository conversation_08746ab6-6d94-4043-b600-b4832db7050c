<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | Student Portal</title>
    <!-- Tailwind CSS - Production Build -->
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/student.css">
    <link rel="stylesheet" href="/css/chosen.min.css">
    <!-- Toastify CSS via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/js/chosen.jquery.min.js"></script>
    <!-- Toastify JS via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Chart.js via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/common.js"></script>
    <%- typeof style !== 'undefined' ? style : '' %>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Header section removed as per requirement to only keep sidebar for student view -->

    <!-- Main Content -->
    <div class="flex flex-1">
        <!-- Sidebar -->
        <aside class="bg-student-secondary text-white w-64 min-h-screen hidden md:block">
            <!-- Student Class Info -->
            <div id="student-class-info" class="p-4 border-b border-student-secondary">
                <div class="text-center">
                    <h3 class="text-lg font-semibold" id="student-full-class"></h3>
                    <p class="text-sm text-gray-300" id="student-session-year"></p>
                </div>
            </div>
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="/student/dashboard" class="block py-2 px-4 rounded <%= currentPage === 'dashboard' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="/student/instruction-plans" class="block py-2 px-4 rounded <%= currentPage === 'instruction-plans' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-book mr-2"></i> Learning Plans
                        </a>
                    </li>
                    <li>
                        <a href="http://localhost:3018/tests" class="block py-2 px-4 rounded <%= currentPage === 'exams' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-file-alt mr-2"></i> Tests
                        </a>
                    </li>
                    <li>
                        <a href="/student/assignments" class="block py-2 px-4 rounded <%= currentPage === 'assignments' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-tasks mr-2"></i> Assignments
                        </a>
                    </li>
                    <li>
                        <a href="/student/practicals" class="block py-2 px-4 rounded <%= currentPage === 'practicals' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-flask mr-2"></i> Lab Practicals
                        </a>
                    </li>
                    <li>
                        <a href="/student/results" class="block py-2 px-4 rounded <%= currentPage === 'results' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-award mr-2"></i> Results
                        </a>
                    </li>
                    <li>
                        <a href="/student/subjects" class="block py-2 px-4 rounded <%= currentPage === 'subjects' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-book-open mr-2"></i> My Subjects
                        </a>
                    </li>
                    <li>
                        <a href="/student/groups" class="block py-2 px-4 rounded <%= currentPage === 'groups' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-users mr-2"></i> Groups
                        </a>
                    </li>
                    <li>
                        <a href="/student/progress" class="block py-2 px-4 rounded <%= currentPage === 'progress' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-chart-line mr-2"></i> My Progress
                        </a>
                    </li>
                    <li>
                        <a href="/student/calendar" class="block py-2 px-4 rounded <%= currentPage === 'calendar' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-calendar-alt mr-2"></i> Calendar
                        </a>
                    </li>
                    <li class="pt-4 border-t border-student-secondary">
                        <a href="/notifications" class="block py-2 px-4 rounded <%= currentPage === 'notifications' ? 'bg-student-hover' : 'hover:bg-student-hover' %> relative">
                            <i class="fas fa-bell mr-2"></i> Notifications
                            <% if (notificationCount > 0) { %>
                                <span class="absolute right-2 top-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                    <%= notificationCount %>
                                </span>
                            <% } %>
                        </a>
                    </li>
                    <li>
                        <a href="/issues" class="block py-2 px-4 rounded <%= currentPage === 'issues' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-exclamation-circle mr-2"></i> Report Issue
                        </a>
                    </li>
                    <li>
                        <a href="/help" class="block py-2 px-4 rounded <%= currentPage === 'help' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-question-circle mr-2"></i> Help
                        </a>
                    </li>
                    <li class="pt-4 border-t border-student-secondary">
                        <a href="/profile" class="block py-2 px-4 rounded <%= currentPage === 'profile' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-user mr-2"></i> Profile
                        </a>
                    </li>
                    <li>
                        <a href="/profile/settings" class="block py-2 px-4 rounded <%= currentPage === 'settings' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                            <i class="fas fa-cog mr-2"></i> Settings
                        </a>
                    </li>
                    <li>
                        <a href="/logout" class="block py-2 px-4 rounded hover:bg-student-hover">
                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Calendar Button (Fixed Position) -->
        <div class="fixed bottom-4 left-4 z-10">
            <a href="/student/calendar" class="bg-student-primary text-white p-3 rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-calendar-alt"></i>
            </a>
        </div>

        <!-- Mobile Sidebar Toggle -->
        <div class="md:hidden fixed bottom-4 right-4 z-10">
            <button id="mobile-sidebar-toggle" class="bg-student-primary text-white p-3 rounded-full shadow-lg">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Sidebar -->
        <div id="mobile-sidebar" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-20 hidden">
            <div class="bg-student-secondary text-white w-64 min-h-screen">
                <div class="flex justify-between items-center p-4 border-b border-student-secondary">
                    <h2 class="text-xl font-bold">Menu</h2>
                    <button id="close-mobile-sidebar" class="text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <!-- Student Class Info (Mobile) -->
                <div id="mobile-student-class-info" class="p-4 border-b border-student-secondary">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold" id="mobile-student-full-class"></h3>
                        <p class="text-sm text-gray-300" id="mobile-student-session-year"></p>
                    </div>
                </div>
                <nav class="p-4">
                    <ul class="space-y-2">
                        <li>
                            <a href="/student/dashboard" class="block py-2 px-4 rounded <%= currentPage === 'dashboard' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="/student/instruction-plans" class="block py-2 px-4 rounded <%= currentPage === 'instruction-plans' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-book mr-2"></i> Learning Plans
                            </a>
                        </li>
                        <li>
                            <a href="http://localhost:3018/tests" class="block py-2 px-4 rounded <%= currentPage === 'exams' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-file-alt mr-2"></i> Tests
                            </a>
                        </li>
                        <li>
                            <a href="/student/assignments" class="block py-2 px-4 rounded <%= currentPage === 'assignments' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-tasks mr-2"></i> Assignments
                            </a>
                        </li>
                        <li>
                            <a href="/student/practicals" class="block py-2 px-4 rounded <%= currentPage === 'practicals' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-flask mr-2"></i> Lab Practicals
                            </a>
                        </li>
                        <li>
                            <a href="/student/results" class="block py-2 px-4 rounded <%= currentPage === 'results' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-award mr-2"></i> Results
                            </a>
                        </li>
                        <li>
                            <a href="/student/subjects" class="block py-2 px-4 rounded <%= currentPage === 'subjects' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-book-open mr-2"></i> My Subjects
                            </a>
                        </li>
                        <li>
                            <a href="/student/groups" class="block py-2 px-4 rounded <%= currentPage === 'groups' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-users mr-2"></i> Groups
                            </a>
                        </li>
                        <li>
                            <a href="/student/progress" class="block py-2 px-4 rounded <%= currentPage === 'progress' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-chart-line mr-2"></i> My Progress
                            </a>
                        </li>
                        <li>
                            <a href="/student/calendar" class="block py-2 px-4 rounded <%= currentPage === 'calendar' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-calendar-alt mr-2"></i> Calendar
                            </a>
                        </li>
                        <li class="pt-4 border-t border-student-secondary">
                            <a href="/notifications" class="block py-2 px-4 rounded <%= currentPage === 'notifications' ? 'bg-student-hover' : 'hover:bg-student-hover' %> relative">
                                <i class="fas fa-bell mr-2"></i> Notifications
                                <% if (notificationCount > 0) { %>
                                    <span class="absolute right-2 top-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                        <%= notificationCount %>
                                    </span>
                                <% } %>
                            </a>
                        </li>
                        <li>
                            <a href="/issues" class="block py-2 px-4 rounded <%= currentPage === 'issues' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-exclamation-circle mr-2"></i> Report Issue
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="block py-2 px-4 rounded <%= currentPage === 'help' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-question-circle mr-2"></i> Help
                            </a>
                        </li>
                        <li class="pt-4 border-t border-student-secondary">
                            <a href="/profile" class="block py-2 px-4 rounded <%= currentPage === 'profile' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                        </li>
                        <li>
                            <a href="/profile/settings" class="block py-2 px-4 rounded <%= currentPage === 'settings' ? 'bg-student-hover' : 'hover:bg-student-hover' %>">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                        </li>
                        <li>
                            <a href="/logout" class="block py-2 px-4 rounded hover:bg-student-hover">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Content -->
        <main class="flex-1 p-4">
            <%- body %>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-student-primary text-white py-4">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; <%= new Date().getFullYear() %> Senior Secondary Residential School for Meritorious Students, Ludhiana. All rights reserved.</p>
        </div>
    </footer>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script>
        // Notification and profile dropdown code removed as those elements are no longer in the layout

        // Fetch student class information
        async function fetchStudentClassInfo() {
            try {
                const response = await fetch('/api/student/class-info');
                if (!response.ok) {
                    throw new Error('Failed to fetch class information');
                }

                const data = await response.json();
                if (data.success && data.classInfo) {
                    // Display class information in sidebar
                    let fullClassName, sessionYear;

                    if (data.classInfo.full_class_name === 'Class Not Assigned') {
                        // Handle case where student doesn't have a class assigned
                        fullClassName = 'Class Not Assigned';
                        sessionYear = data.classInfo.academic_year || new Date().getFullYear();

                        // Add a message to encourage class assignment
                        console.log('Student has no class assigned. Username:', data.classInfo.username);
                    } else {
                        // Normal case with class assigned
                        fullClassName = data.classInfo.full_class_name || `${data.classInfo.grade} ${data.classInfo.trade_name || data.classInfo.name || ''} ${data.classInfo.section}`;
                        sessionYear = data.classInfo.academic_year || new Date().getFullYear();

                        // Add room number if available
                        const roomNumber = data.classInfo.room_number ? `Room: ${data.classInfo.room_number}` : '';
                    }

                    // Update desktop sidebar
                    document.getElementById('student-full-class').textContent = fullClassName;
                    document.getElementById('student-session-year').textContent = `Session: ${sessionYear}`;

                    // Add room number if available
                    if (typeof roomNumber !== 'undefined' && roomNumber) {
                        // Check if room number element exists
                        const roomElement = document.getElementById('student-room-number');
                        if (roomElement) {
                            roomElement.textContent = roomNumber;
                            roomElement.classList.remove('hidden');
                        } else {
                            // Create room number element if it doesn't exist
                            const roomDiv = document.createElement('div');
                            roomDiv.id = 'student-room-number';
                            roomDiv.className = 'text-xs text-gray-500 mt-1';
                            roomDiv.textContent = roomNumber;
                            document.getElementById('student-class-info').appendChild(roomDiv);
                        }
                    }

                    // Update mobile sidebar
                    document.getElementById('mobile-student-full-class').textContent = fullClassName;
                    document.getElementById('mobile-student-session-year').textContent = `Session: ${sessionYear}`;

                    // Add room number to mobile sidebar if available
                    if (typeof roomNumber !== 'undefined' && roomNumber) {
                        // Check if mobile room number element exists
                        const mobileRoomElement = document.getElementById('mobile-student-room-number');
                        if (mobileRoomElement) {
                            mobileRoomElement.textContent = roomNumber;
                            mobileRoomElement.classList.remove('hidden');
                        } else {
                            // Create mobile room number element if it doesn't exist
                            const mobileRoomDiv = document.createElement('div');
                            mobileRoomDiv.id = 'mobile-student-room-number';
                            mobileRoomDiv.className = 'text-xs text-gray-500 mt-1';
                            mobileRoomDiv.textContent = roomNumber;
                            document.getElementById('mobile-student-class-info').appendChild(mobileRoomDiv);
                        }
                    }
                } else {
                    // Handle error case
                    console.error('Failed to get class info:', data.message);

                    // Set default values
                    const defaultText = 'Class Not Available';
                    const defaultYear = new Date().getFullYear();

                    // Update desktop sidebar
                    document.getElementById('student-full-class').textContent = defaultText;
                    document.getElementById('student-session-year').textContent = `Session: ${defaultYear}`;

                    // Update mobile sidebar
                    document.getElementById('mobile-student-full-class').textContent = defaultText;
                    document.getElementById('mobile-student-session-year').textContent = `Session: ${defaultYear}`;
                }
            } catch (error) {
                console.error('Error fetching student class info:', error);

                // Set default values in case of error
                const defaultText = 'Class Not Available';
                const defaultYear = new Date().getFullYear();

                // Update desktop sidebar
                document.getElementById('student-full-class').textContent = defaultText;
                document.getElementById('student-session-year').textContent = `Session: ${defaultYear}`;

                // Update mobile sidebar
                document.getElementById('mobile-student-full-class').textContent = defaultText;
                document.getElementById('mobile-student-session-year').textContent = `Session: ${defaultYear}`;
            }
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', fetchStudentClassInfo);

        // Mobile sidebar toggle
        const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const closeMobileSidebar = document.getElementById('close-mobile-sidebar');

        if (mobileSidebarToggle && mobileSidebar && closeMobileSidebar) {
            mobileSidebarToggle.addEventListener('click', function() {
                mobileSidebar.classList.remove('hidden');
            });

            closeMobileSidebar.addEventListener('click', function() {
                mobileSidebar.classList.add('hidden');
            });

            // Close sidebar when clicking outside
            mobileSidebar.addEventListener('click', function(event) {
                if (event.target === mobileSidebar) {
                    mobileSidebar.classList.add('hidden');
                }
            });
        }

        // Notification functions removed as they're no longer needed

        // Show toast if available
        <% if (locals.toast && locals.toast.type && locals.toast.message) { %>
            showToast('<%= locals.toast.message %>', '<%= locals.toast.type %>');
        <% } else if (locals.flashSuccess) { %>
            showToast('<%= locals.flashSuccess %>', 'success');
        <% } else if (locals.flashError) { %>
            showToast('<%= locals.flashError %>', 'error');
        <% } %>

        // Toast function
        function showToast(message, type = 'info') {
            Toastify({
                text: message,
                duration: 3000,
                close: true,
                gravity: "bottom",
                position: "right",
                backgroundColor: type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : 'var(--student-primary)',
                stopOnFocus: true
            }).showToast();
        }
    </script>
    <%- typeof script !== 'undefined' ? script : '' %>
</body>
</html>
