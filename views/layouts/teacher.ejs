<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - <%= __('app.name') %> <%= __('teacher.dashboard') %></title>
  <!-- Tailwind CSS -->
  <link rel="stylesheet" href="/styles.css">
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Toastify Library -->
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <!-- Temporarily disabled WebSocket client -->
  <!-- <script src="/js/websocket-client.js"></script> -->
  <link rel="stylesheet" href="/css/app.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/chat-icon.js"></script>
  <script src="/js/teacher/common.js"></script>
  <script src="/js/app.js"></script>
  <!-- Chosen Library -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>

  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #dcfce7;
    }
    ::-webkit-scrollbar-thumb {
      background: #22c55e;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #16a34a;
    }

    /* Teacher theme colors - bright green */
    .bg-teacher-primary {
      background-color: #22c55e;
    }
    .bg-teacher-secondary {
      background-color: #16a34a;
    }
    .bg-teacher-accent {
      background-color: #15803d;
    }
    .text-teacher-primary {
      color: #22c55e;
    }
    .hover\:bg-teacher-primary:hover {
      background-color: #16a34a;
    }
    .hover\:bg-teacher-secondary:hover {
      background-color: #15803d;
    }

    /* Card hover effects */
    .card-hover {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Progress bars */
    .progress-container {
      width: 100%;
      background-color: #e2e8f0;
      border-radius: 9999px;
      height: 0.75rem;
    }
    .progress-bar {
      height: 100%;
      border-radius: 9999px;
      transition: width 0.5s ease;
    }

    /* Optional: Adjust math display styles */
    .katex { font-size: 1.1em; }
    .math-display { margin: 1em 0; }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col logged-in" data-user-id="<%= locals.userId || '' %>">

  <!-- Teacher Navbar -->
  <%- include('../partials/teacher-navbar', {
    currentPage: locals.currentPage || '',
    user: locals.user || null,
    notificationCount: locals.notificationCount || 0
  }) %>

  <!-- Main Content -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <% if (locals.pageTitle) { %>
      <h1 class="text-2xl font-bold text-gray-800 mb-6"><%= pageTitle %></h1>
    <% } %>

    <!-- Flash Messages -->
    <% if (locals.messages && messages.success && messages.success.length > 0) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.success.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <% if (locals.messages && messages.error && messages.error.length > 0) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.error.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <!-- Direct error message -->
    <% if (locals.error) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <p><%= error %></p>
      </div>
    <% } %>

    <%- body %>
  </main>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>

  <!-- Toast Notifications Container -->
  <div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col space-y-2"></div>

  <!-- Session Timeout Modal -->
  <div id="session-timeout-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Session Timeout</h2>
      <p class="text-gray-600 mb-6">Your session will expire in <span id="timeout-countdown">15</span> seconds due to inactivity.</p>
      <div class="flex justify-end space-x-4">
        <button id="logout-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Logout</button>
        <button id="continue-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Continue Session</button>
      </div>
    </div>
  </div>

  <!-- Toast Notification Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Show toast if available
      <% if (locals.toast && locals.toast.type && locals.toast.message) { %>
        if (typeof showToast === 'function') {
          showToast('<%= locals.toast.type %>', 'Notification', '<%= locals.toast.message %>');
        }
      <% } else if (locals.flashSuccess) { %>
        if (typeof showToast === 'function') {
          showToast('success', 'Success', '<%= locals.flashSuccess %>');
        }
      <% } else if (locals.flashError) { %>
        if (typeof showToast === 'function') {
          showToast('error', 'Error', '<%= locals.flashError %>');
        }
      <% } %>
    });
  </script>

  <!-- Confirmation Dialog -->
  <div id="confirmation-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
      <h2 id="confirmation-title" class="text-xl font-bold text-gray-800 mb-4">Confirm Action</h2>
      <p id="confirmation-message" class="text-gray-600 mb-6">Are you sure you want to proceed?</p>
      <div class="flex justify-end space-x-4">
        <button id="cancel-action" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
        <button id="confirm-action" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">Confirm</button>
      </div>
    </div>
  </div>

  <script>
    // Define the initSessionTimeout function to bridge the gap
    function initSessionTimeout() {
      // Check if initSessionTimer exists
      if (typeof initSessionTimer === 'function') {
        initSessionTimer();
      } else {
        console.warn('Session timer function not found');
      }
    }

    // Define the initConfirmationDialog function if it doesn't exist
    function initConfirmationDialog() {
      // Get the confirmation dialog elements
      const dialog = document.getElementById('confirmation-dialog');
      if (!dialog) return;

      // Close dialog function
      const closeDialog = () => {
        dialog.classList.add('hidden');
      };

      // Add event listeners to close buttons
      const closeButtons = document.querySelectorAll('#cancel-action, #confirmation-dialog .close-dialog');
      closeButtons.forEach(button => {
        if (button) button.addEventListener('click', closeDialog);
      });
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Initialize session timeout
      initSessionTimeout();

      // Initialize confirmation dialog
      initConfirmationDialog();
    });
  </script>

  <!-- Include page-specific scripts -->
  <%- script %>
</body>
</html>
