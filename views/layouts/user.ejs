<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - <%= __('app.name') %></title>
  <link rel="stylesheet" href="/styles.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/broadcast-notifications.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/chat-icon.js"></script>
  <script src="/js/unified-chat-icon.js"></script>
  <script src="/js/websocket-client.js"></script>
  <link rel="stylesheet" href="/css/session-timer.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <link rel="stylesheet" href="/css/responsive-tables.css">
  <link rel="stylesheet" href="/css/modal-styles.css">
  <link rel="stylesheet" href="/css/responsive-images.css">
  <link rel="stylesheet" href="/css/form-styles.css">
  <script src="/js/session-timer.js"></script>
  <script src="/js/global-fix.js"></script>
  <script src="/js/modal-manager.js"></script>
  <script src="/js/form-validator.js"></script>
  <link rel="stylesheet" href="/css/toast-notifications.css">
  <link rel="stylesheet" href="/css/chat-icon.css">
  <link rel="stylesheet" href="/css/unified-chat-icon.css">
  <link rel="stylesheet" href="/css/student.css">
  <%- style %>
  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #c7d2fe;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #818cf8;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col <%= locals.userId ? 'logged-in' : '' %> <%= locals.inTestMode ? 'in-test-mode' : '' %>" data-user-id="<%= locals.userId || '' %>">

  <!-- User Navbar -->
  <%- include('../partials/user-navbar', {
    currentPage: locals.currentPage || '',
    user: locals.user || null,
    notificationCount: locals.notificationCount || 0,
    isAdmin: locals.isAdmin || false
  }) %>

  <!-- Main Content -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <% if (locals.pageTitle) { %>
      <h1 class="text-2xl font-bold text-gray-800 mb-6"><%= pageTitle %></h1>
    <% } %>

    <% if (locals.flashSuccess) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
      </div>
    <% } %>

    <% if (locals.flashError) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashError %></span>
      </div>
    <% } %>

    <%- body %>
  </main>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>

  <!-- Common Scripts -->
  <script>
    // Toast notifications are now initialized in the header
  </script>

  <!-- Device Testing Tool (only loads in development mode) -->
  <script src="/js/device-testing.js"></script>

  <%- script %>


</body>
</html>