<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-purple-600 text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Your Notifications</h2>
    <% if (notifications && notifications.length > 0) { %>
      <form action="/notifications/mark-all-read" method="POST">
        <button type="submit" class="bg-white text-purple-600 px-3 py-1 rounded-md text-sm hover:bg-purple-100 transition">
          Mark All as Read
        </button>
      </form>
    <% } %>
  </div>

  <div class="p-4">
    <% if (notifications && notifications.length > 0) { %>
      <div class="space-y-4">
        <% notifications.forEach(notification => { %>
          <div class="border rounded-lg overflow-hidden <%= notification.is_read ? 'border-gray-200 bg-white' : 'border-purple-200 bg-purple-50' %>">
            <div class="p-4">
              <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                  <% if (notification.type === 'success') { %>
                    <div class="bg-green-100 p-2 rounded-full">
                      <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  <% } else if (notification.type === 'warning') { %>
                    <div class="bg-yellow-100 p-2 rounded-full">
                      <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                      </svg>
                    </div>
                  <% } else if (notification.type === 'error') { %>
                    <div class="bg-red-100 p-2 rounded-full">
                      <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  <% } else { %>
                    <div class="bg-blue-100 p-2 rounded-full">
                      <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  <% } %>
                </div>
                <div class="flex-1">
                  <p class="text-gray-800 <%= notification.is_read ? '' : 'font-semibold' %>"><%= notification.message %></p>
                  <p class="text-sm text-gray-500 mt-1"><%= notification.formatted_date || (new Date(notification.created_at).toLocaleDateString('en-US', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-') + ' ' + new Date(notification.created_at).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })) %></p>
                  <div class="flex mt-2 space-x-2">
                    <% if (notification.link) { %>
                      <a href="<%= notification.link %>" class="text-xs text-blue-600 hover:text-blue-800">View Details</a>
                    <% } %>
                    <button
                      class="text-xs text-red-600 hover:text-red-800 delete-notification-btn"
                      data-notification-id="<%= notification.notification_id %>">
                      Delete
                    </button>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <% if (!notification.is_read) { %>
                    <form action="/notifications/mark-read/<%= notification.id %>" method="POST">
                      <button type="submit" class="text-purple-600 hover:text-purple-800" title="Mark as read">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                    </form>
                  <% } %>
                  <form action="/notifications/delete/<%= notification.id %>" method="POST">
                    <button type="submit" class="text-red-600 hover:text-red-800" title="Delete notification">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        <% }); %>
      </div>
    <% } else { %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
        <p class="mt-1 text-sm text-gray-500">You don't have any notifications at the moment.</p>
      </div>
    <% } %>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add AJAX functionality for mark as read and delete buttons
    const forms = document.querySelectorAll('form[action^="/notifications/"]');

    forms.forEach(form => {
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formAction = this.getAttribute('action');

        fetch(formAction, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // If it's a delete action, remove the notification
            if (formAction.includes('/delete/')) {
              this.closest('.border').remove();
              ToastNotifications.success('Notification deleted successfully');
            }
            // If it's a mark as read action, update the styling
            else if (formAction.includes('/mark-read/')) {
              const notificationEl = this.closest('.border');
              notificationEl.classList.remove('border-purple-200', 'bg-purple-50');
              notificationEl.classList.add('border-gray-200', 'bg-white');
              this.remove(); // Remove the mark as read button
              ToastNotifications.info('Notification marked as read');
            }
            // If it's mark all as read, update all notifications
            else if (formAction === '/notifications/mark-all-read') {
              document.querySelectorAll('.border-purple-200').forEach(el => {
                el.classList.remove('border-purple-200', 'bg-purple-50');
                el.classList.add('border-gray-200', 'bg-white');
              });
              document.querySelectorAll('form[action^="/notifications/mark-read/"]').forEach(form => {
                form.remove();
              });
              ToastNotifications.success('All notifications marked as read');
            }

            // Check if there are no more notifications
            if (document.querySelectorAll('.border').length === 0) {
              location.reload(); // Reload to show the empty state
            }
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
      });
    });
  });

  // Delete notification functionality
  document.querySelectorAll('.delete-notification-btn').forEach(button => {
    button.addEventListener('click', function() {
      const notificationId = this.getAttribute('data-notification-id');

      if (confirm('Are you sure you want to delete this notification?')) {
        fetch(`/notifications/${notificationId}/delete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Remove the notification from the UI
            const notificationElement = this.closest('.border');
            if (notificationElement) {
              notificationElement.remove();

              // Check if there are any notifications left
              const remainingNotifications = document.querySelectorAll('.border').length;
              if (remainingNotifications === 0) {
                location.reload(); // Reload to show the empty state
              }
            }
          } else {
            ToastNotifications.error('Error deleting notification: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error deleting notification:', error);
          ToastNotifications.error('Error deleting notification. Please try again.');
        });
      }
    });
  });
</script>