  </main>

  <footer class="bg-gray-800 text-white py-4">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0">
          <p>&copy; <%= new Date().getFullYear() %> Meritorious Exam Prep Platform. All rights reserved.(Developed by <a href="https://github.com/charan<PERSON><PERSON>-singh" class="hover:text-purple-300"><PERSON><PERSON><PERSON><PERSON> ,Computer Science Teacher - Meritorious School Ludhiana  </a>)</p>
        </div>
        <div class="flex space-x-4">
          <a href="/admin/help" class="hover:text-purple-300">Help</a>
          <a href="/admin/settings" class="hover:text-purple-300">Settings</a>
        </div>
      </div>
    </div>
  </footer>

  <script>
    // Close flash messages
    document.querySelectorAll('.close-flash').forEach(function(element) {
      element.addEventListener('click', function() {
        this.parentElement.style.display = 'none';
      });
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
      document.querySelectorAll('.flash-success, .flash-error').forEach(function(element) {
        element.style.display = 'none';
      });
    }, 5000);

    // Language switcher functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Function to set a cookie
      function setCookie(name, value, days) {
        var expires = "";
        if (days) {
          var date = new Date();
          date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
      }

      // Add event listeners to language switcher buttons
      document.querySelectorAll('.language-option').forEach(function(element) {
        element.addEventListener('click', function(e) {
          e.preventDefault();
          const lang = this.getAttribute('data-lang');
          setCookie('lang', lang, 365); // Set cookie for 1 year
          window.location.reload(); // Reload the page to apply the new language
        });
      });
    });
  </script>
</body>
</html>
