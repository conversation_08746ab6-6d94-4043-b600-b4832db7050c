<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> | Admin Panel</title>
  <link rel="stylesheet" href="/css/tailwind.css">
  <link rel="stylesheet" href="/css/custom.css">
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/floating-chat.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <div class="flash-container fixed top-4 right-4 z-50">
    <% if (locals.flashSuccess) { %>
      <div class="flash-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashSuccess %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>

    <% if (locals.flashError) { %>
      <div class="flash-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline"><%= flashError %></span>
        <span class="absolute top-0 bottom-0 right-0 px-4 py-3 close-flash">
          <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
        </span>
      </div>
    <% } %>
  </div>

  <main class="flex-grow">
