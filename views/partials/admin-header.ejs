<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> | Admin Panel</title>
  <link rel="stylesheet" href="/css/tailwind.css">
  <link rel="stylesheet" href="/css/custom.css">
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <script src="/js/admin-notifications.js"></script>
  <script src="/js/floating-chat.js"></script>
  <script src="/socket.io/socket.io.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <%- include('./admin-navbar') %>

  <!-- Flash messages are now handled by toast notifications -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize toast notifications
      if (typeof ToastNotifications !== 'undefined') {
        ToastNotifications.init();

        // Show toast messages if they exist
        <% if (locals.toast && locals.toast.type && locals.toast.message) { %>
          ToastNotifications.<%= locals.toast.type %>('<%= locals.toast.message %>');
        <% } else if (locals.flashSuccess) { %>
          ToastNotifications.success('<%= flashSuccess %>');
        <% } else if (locals.flashError) { %>
          ToastNotifications.error('<%= flashError %>');
        <% } %>
      }
    });
  </script>

  <main class="flex-grow">
