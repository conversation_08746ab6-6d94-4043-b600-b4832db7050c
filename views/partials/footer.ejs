</main>

<!-- Footer -->
<footer class="bg-gray-800 text-white py-6 mt-auto">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div>
                <h3 class="text-lg font-semibold">Meritorious EP</h3>
                <p class="text-sm text-gray-400">© <%= new Date().getFullYear() %> All rights reserved</p>
            </div>
            <div class="mt-4 md:mt-0">
                <p class="text-sm text-gray-400">Build your future with confidence</p>
            </div>
        </div>
    </div>
</footer>

<script>
    // Close flash messages
    document.querySelectorAll('.close-flash').forEach(function(element) {
        element.addEventListener('click', function() {
            this.parentElement.style.display = 'none';
        });
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        document.querySelectorAll('.flash-success, .flash-error').forEach(function(element) {
            element.style.display = 'none';
        });
    }, 5000);

    // Language switcher functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to set a cookie
        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }

        // Add event listeners to language switcher buttons
        document.querySelectorAll('.language-option').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                const lang = this.getAttribute('data-lang');
                setCookie('lang', lang, 365); // Set cookie for 1 year
                window.location.reload(); // Reload the page to apply the new language
            });
        });
    });
</script>

</body>
</html>