<div class="language-switcher">
    <div class="relative inline-block text-left">
        <button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" id="language-menu-button" aria-expanded="false" aria-haspopup="true">
            <% if (currentLanguage === 'en') { %>
                <span>English</span>
            <% } else if (currentLanguage === 'pa') { %>
                <span>ਪੰਜਾਬੀ</span>
            <% } %>
            <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </button>
        <div class="language-dropdown hidden origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1">
            <div class="py-1" role="none">
                <a href="#" data-lang="en" class="language-option <%= currentLanguage === 'en' ? 'bg-gray-100 text-gray-900' : 'text-gray-700' %> block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left" role="menuitem">English</a>
                <a href="#" data-lang="pa" class="language-option <%= currentLanguage === 'pa' ? 'bg-gray-100 text-gray-900' : 'text-gray-700' %> block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left" role="menuitem">ਪੰਜਾਬੀ</a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const languageMenuButton = document.getElementById('language-menu-button');
        const languageDropdown = document.querySelector('.language-dropdown');

        // Function to set a cookie
        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }

        // Toggle dropdown
        languageMenuButton.addEventListener('click', function(e) {
            e.preventDefault();
            languageDropdown.classList.toggle('hidden');
        });

        // Add event listeners to language options
        document.querySelectorAll('.language-option').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                const lang = this.getAttribute('data-lang');
                setCookie('lang', lang, 365); // Set cookie for 1 year
                window.location.reload(); // Reload the page to apply the new language
            });
        });

        // Close the dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!languageMenuButton.contains(event.target) && !languageDropdown.contains(event.target)) {
                languageDropdown.classList.add('hidden');
            }
        });
    });
</script>
