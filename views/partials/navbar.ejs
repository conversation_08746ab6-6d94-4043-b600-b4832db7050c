<nav class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center">
                    <a href="/" class="text-xl font-bold text-gray-800">Meritorious EP</a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                    <% if (locals.user && user.role === 'admin') { %>
                        <a href="/admin/dashboard"
                           class="<%= (locals.navbar === 'dashboard' || locals.currentPage === 'dashboard') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                            Dashboard
                        </a>
                    <% } %>

                    <a href="/tests"
                       class="<%= (locals.navbar === 'tests' || locals.currentPage === 'tests') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                        Tests
                    </a>

                    <div class="relative inline-block text-left">
                        <a href="/profile"
                           class="<%= (locals.navbar === 'profile' || locals.currentPage === 'profile') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                            Profile
                        </a>
                        <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="profileDropdown">
                            <div class="py-1" role="none">
                                <% if (user && user.role === 'admin') { %>
                                    <a href="/admin/logs" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Logs</a>
                                <% } %>
                            </div>
                        </div>
                    </div>

                    <% if (user && user.role === 'admin') { %>
                        <div class="relative inline-block text-left">
                            <a href="#"
                               class="<%= (locals.navbar === 'reports' || locals.currentPage === 'reports') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                                Reports
                            </a>
                            <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="reportsDropdown">
                                <div class="py-1" role="none">
                                    <a href="/admin/reports"
                                       class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="reportsDropdown">
                                        System Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="relative inline-block text-left">
                            <a href="#"
                               class="<%= (locals.navbar === 'settings' || locals.currentPage === 'settings') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                                Settings
                            </a>
                            <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="settingsDropdown">
                                <div class="py-1" role="none">
                                    <% if (user && user.role === 'admin') { %>
                                        <a href="/admin/logs" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Logs</a>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } %>

                    <a href="/issues"
                       class="<%= (locals.navbar === 'issues' || locals.currentPage === 'issues' || locals.currentPage === 'report-issue') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900">
                        <span class="flex items-center">
                            <svg class="h-6 w-6 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            IT Issues
                        </span>
                    </a>

                    <a href="/notifications"
                       class="<%= (locals.navbar === 'notifications' || locals.currentPage === 'notifications') ? 'border-b-2 border-blue-500' : '' %> inline-flex items-center px-1 pt-1 text-gray-900 relative">
                        <span class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            Notifications
                        </span>
                        <% if (locals.notificationCount && notificationCount > 0) { %>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                <%= notificationCount %>
                            </span>
                        <% } %>
                    </a>
                </div>
            </div>

            <!-- Right side menu -->
            <div class="flex items-center">
                <% if (locals.user) { %>
                    <!-- User Menu -->
                    <div class="ml-3 relative">
                        <div class="flex items-center">
                            <% if (user.profile_image && user.profile_image.startsWith('/')) { %>
                                <img src="<%= user.profile_image %>" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                            <% } else if (user.profile_image) { %>
                                <img src="/uploads/profiles/<%= user.profile_image %>" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                            <% } else { %>
                                <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                    <%
                                        let initials = 'U';
                                        if (user.username) {
                                            initials = user.username.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                                        } else if (user.name) {
                                            initials = user.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                                        }
                                    %>
                                    <span class="text-sm font-bold text-white"><%= initials %></span>
                                </div>
                            <% } %>
                            <span class="ml-2 text-gray-700"><%= user.username || user.name || 'User' %></span>
                        </div>
                    </div>

                    <!-- Logout -->
                    <div class="ml-4">
                        <a href="/logout" class="text-gray-500 hover:text-gray-700">Logout</a>
                    </div>
                <% } else { %>
                    <!-- Login/Register -->
                    <a href="/login" class="text-gray-500 hover:text-gray-700 mr-4">Login</a>
                    <a href="/register" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Register</a>
                <% } %>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <% if (locals.user && user.role === 'admin') { %>
                <a href="/admin/dashboard"
                   class="<%= (locals.navbar === 'dashboard' || locals.currentPage === 'dashboard') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    Dashboard
                </a>
            <% } %>

            <a href="/tests"
               class="<%= (locals.navbar === 'tests' || locals.currentPage === 'tests') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Tests
            </a>

            <a href="/profile"
               class="<%= (locals.navbar === 'profile' || locals.currentPage === 'profile') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Profile
            </a>

            <a href="/issues"
               class="<%= (locals.navbar === 'issues' || locals.currentPage === 'issues' || locals.currentPage === 'report-issue') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium flex items-center">
                IT Issues
            </a>

            <a href="/notifications"
               class="<%= (locals.navbar === 'notifications' || locals.currentPage === 'notifications') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium flex items-center">
                Notifications
                <% if (locals.notificationCount && notificationCount > 0) { %>
                    <span class="ml-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        <%= notificationCount %>
                    </span>
                <% } %>
            </a>

            <% if (user && user.role === 'admin') { %>
                <a href="#"
                   class="<%= (locals.navbar === 'reports' || locals.currentPage === 'reports') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    Reports
                </a>
                <a href="/admin/reports"
                   class="<%= (locals.navbar === 'reports' || locals.currentPage === 'reports') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    System Reports
                </a>
                <a href="/admin/logs"
                   class="<%= (locals.navbar === 'logs' || locals.currentPage === 'logs') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                    Logs
                </a>
            <% } %>

            <a href="/notifications"
               class="<%= (locals.navbar === 'notifications' || locals.currentPage === 'notifications') ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700' %> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                Notifications
                <% if (locals.notificationCount && notificationCount > 0) { %>
                    <span class="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        <%= notificationCount %>
                    </span>
                <% } %>
            </a>
        </div>
    </div>
</nav>

<script>
    // Mobile menu toggle
    document.querySelector('.mobile-menu-button').addEventListener('click', function() {
        document.querySelector('.mobile-menu').classList.toggle('hidden');
    });
</script>