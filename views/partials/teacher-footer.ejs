  </main>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 py-4 mt-auto">
    <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
      &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
    </div>
  </footer>

  <!-- Toast Notifications Container -->
  <div id="toast-container" class="fixed top-4 right-4 z-50"></div>

  <!-- Session Timeout Modal -->
  <div id="session-timeout-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Session Timeout</h2>
      <p class="text-gray-600 mb-6">Your session will expire in <span id="timeout-countdown">15</span> seconds due to inactivity.</p>
      <div class="flex justify-end space-x-4">
        <button id="logout-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Logout</button>
        <button id="continue-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Continue Session</button>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <div id="confirmation-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Confirm Action</h2>
      <p id="confirmation-message" class="text-gray-600 mb-6">Are you sure you want to perform this action?</p>
      <div class="flex justify-end space-x-4">
        <button id="cancel-action" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
        <button id="confirm-action" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">Confirm</button>
      </div>
    </div>
  </div>

  <script>
    // Function to show toast notifications
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.className = `toast toast-${type} mb-4`;
      toast.innerHTML = `
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 ${type === 'success' ? 'text-green-500' : type === 'error' ? 'text-red-500' : 'text-blue-500'}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              ${type === 'success' 
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />'
                : type === 'error'
                  ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />'
                  : '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />'
              }
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium ${type === 'success' ? 'text-green-800' : type === 'error' ? 'text-red-800' : 'text-blue-800'}">${message}</p>
          </div>
          <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
              <button class="toast-close inline-flex rounded-md p-1.5 ${type === 'success' ? 'text-green-500 hover:bg-green-100' : type === 'error' ? 'text-red-500 hover:bg-red-100' : 'text-blue-500 hover:bg-blue-100'} focus:outline-none">
                <span class="sr-only">Dismiss</span>
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      `;
      
      const toastContainer = document.getElementById('toast-container');
      toastContainer.appendChild(toast);
      
      // Add event listener to close button
      toast.querySelector('.toast-close').addEventListener('click', () => {
        toast.remove();
      });
      
      // Auto-remove after 5 seconds
      setTimeout(() => {
        toast.remove();
      }, 5000);
    }

    // Initialize session timeout
    function initSessionTimeout() {
      const sessionTimeoutModal = document.getElementById('session-timeout-modal');
      const timeoutCountdown = document.getElementById('timeout-countdown');
      const continueBtn = document.getElementById('continue-btn');
      const logoutBtn = document.getElementById('logout-btn');
      
      if (!sessionTimeoutModal || !timeoutCountdown || !continueBtn || !logoutBtn) return;
      
      let timeoutTimer;
      let countdownTimer;
      let countdownSeconds = 15;
      
      // Reset the session timeout
      function resetSessionTimeout() {
        clearTimeout(timeoutTimer);
        timeoutTimer = setTimeout(showSessionTimeoutModal, 15 * 60 * 1000); // 15 minutes
      }
      
      // Show the session timeout modal
      function showSessionTimeoutModal() {
        sessionTimeoutModal.classList.remove('hidden');
        countdownSeconds = 15;
        timeoutCountdown.textContent = countdownSeconds;
        
        countdownTimer = setInterval(() => {
          countdownSeconds--;
          timeoutCountdown.textContent = countdownSeconds;
          
          if (countdownSeconds <= 0) {
            clearInterval(countdownTimer);
            window.location.href = '/logout';
          }
        }, 1000);
      }
      
      // Continue session
      continueBtn.addEventListener('click', () => {
        sessionTimeoutModal.classList.add('hidden');
        clearInterval(countdownTimer);
        resetSessionTimeout();
      });
      
      // Logout
      logoutBtn.addEventListener('click', () => {
        window.location.href = '/logout';
      });
      
      // Reset session timeout on user activity
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
      events.forEach(event => {
        document.addEventListener(event, resetSessionTimeout, false);
      });
      
      // Initialize session timeout
      resetSessionTimeout();
    }

    // Define the initConfirmationDialog function
    function initConfirmationDialog() {
      // Get the confirmation dialog elements
      const dialog = document.getElementById('confirmation-dialog');
      if (!dialog) return;

      // Close dialog function
      const closeDialog = () => {
        dialog.classList.add('hidden');
      };

      // Add event listeners to close buttons
      const closeButtons = document.querySelectorAll('#cancel-action, #confirmation-dialog .close-dialog');
      closeButtons.forEach(button => {
        if (button) button.addEventListener('click', closeDialog);
      });
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Initialize session timeout
      initSessionTimeout();

      // Initialize confirmation dialog
      initConfirmationDialog();
    });
  </script>
</body>
</html>
