<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - <%= __('app.name') %> <%= __('teacher.dashboard') %></title>
  <link rel="stylesheet" href="/styles.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="/js/toast-notifications.js"></script>
  <script src="/js/notification-events.js"></script>
  <script src="/js/notification-counter.js"></script>
  <link rel="stylesheet" href="/css/session-timer.css">
  <link rel="stylesheet" href="/css/site-title.css">
  <link rel="stylesheet" href="/css/spacing-utilities.css">
  <link rel="stylesheet" href="/css/responsive-tables.css">
  <link rel="stylesheet" href="/css/modal-styles.css">
  <link rel="stylesheet" href="/css/responsive-images.css">
  <link rel="stylesheet" href="/css/form-styles.css">
  <script src="/js/session-timer.js"></script>
  <script src="/js/global-fix.js"></script>
  <script src="/js/modal-manager.js"></script>
  <script src="/js/form-validator.js"></script>
  <link rel="stylesheet" href="/css/toast-notifications.css">
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/chat-icon.js"></script>
  <script src="/js/teacher/common.js"></script>

  <!-- Additional CSS -->
  <style>
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #dcfce7;
    }
    ::-webkit-scrollbar-thumb {
      background: #22c55e;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #16a34a;
    }

    /* Teacher theme colors - bright green */
    .bg-teacher-primary {
      background-color: #22c55e;
    }
    .bg-teacher-secondary {
      background-color: #16a34a;
    }
    .bg-teacher-accent {
      background-color: #15803d;
    }
    .text-teacher-primary {
      color: #22c55e;
    }
    .hover\:bg-teacher-primary:hover {
      background-color: #16a34a;
    }
    .hover\:bg-teacher-secondary:hover {
      background-color: #15803d;
    }

    /* Card hover effects */
    .card-hover {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Progress bars */
    .progress-container {
      width: 100%;
      background-color: #e2e8f0;
      border-radius: 9999px;
      height: 0.75rem;
    }
    .progress-bar {
      height: 100%;
      border-radius: 9999px;
      transition: width 0.5s ease;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col logged-in" data-user-id="<%= locals.userId || '' %>">

  <!-- Teacher Navbar -->
  <%- include('./teacher-navbar', {
    currentPage: locals.currentPage || '',
    user: locals.user || null,
    notificationCount: locals.notificationCount || 0
  }) %>

  <!-- Main Content -->
  <main class="flex-grow">
    <!-- Flash Messages -->
    <% if (locals.messages && messages.success && messages.success.length > 0) { %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.success.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <% if (locals.messages && messages.error && messages.error.length > 0) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <% messages.error.forEach(function(message) { %>
          <p><%= message %></p>
        <% }); %>
      </div>
    <% } %>

    <!-- Direct error message -->
    <% if (locals.error) { %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <p><%= error %></p>
      </div>
    <% } %>
