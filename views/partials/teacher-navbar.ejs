<!-- Teacher Navbar -->
<nav class="bg-teacher-primary text-white shadow-md">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center py-3">
      <!-- Logo and Site Title -->
      <div class="flex items-center">
        <a href="/teacher/dashboard" class="flex items-center">
          <img src="/images/logo.png" alt="Logo" class="h-8 w-auto mr-2">
          <span class="text-lg font-semibold site-title">Exam Prep Platform</span>
        </a>

        <!-- Main Navigation -->
        <div class="hidden md:flex space-x-4 ml-10">
          <a href="/teacher/dashboard" class="px-3 py-2 rounded <%= currentPage === 'dashboard' ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            Dashboard
          </a>

          <a href="/teacher/classes" class="px-3 py-2 rounded <%= currentPage === 'classes' ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
            My Classes
          </a>

          <!-- Timetable -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['timetable', 'my-timetable'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Timetable
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/timetable" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Class Timetable</a>
              <a href="/teacher/teacher-timetable" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Teacher Timetable</a>
              <a href="/teacher/my-timetable" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">My Schedule</a>
              <a href="/teacher/class-teacher-map" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Class-Teacher Map</a>
              <a href="/teacher/lecture-schedules" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Lecture Schedules</a>
              <a href="/teacher/holiday-calendar" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Holiday Calendar</a>
              <a href="/teacher/calendar" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Academic Calendar</a>
              <a href="/teacher/class-subject-summary" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Class-Subject Summary</a>
            </div>
          </div>

          <!-- Instruction Plans Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['instruction-plans', 'subject-eligibility', 'daily-instruction-plans', 'class-assignments'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              Instruction Plans
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/instruction-plans" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Instruction Plans</a>
              <a href="/teacher/daily-instruction-plans" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Daily Plans</a>
              <a href="/teacher/subject-eligibility" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Subject Eligibility</a>
              <a href="/teacher/class-assignments" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Class Assignments</a>
            </div>
          </div>

          <!-- Syllabus Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['syllabus', 'syllabus-topics'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
              Syllabus
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/syllabus" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Syllabus Overview</a>
              <a href="/teacher/syllabus/topics" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Topic Management</a>
              <a href="/teacher/syllabus/progress" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Progress Tracking</a>
            </div>
          </div>

          <!-- Practicals Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['practicals', 'lab-sessions'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>
              Practicals
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/practicals" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Lab Sessions</a>
              <a href="/teacher/practicals/schedule" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Schedule Practical</a>
              <a href="/teacher/practicals/records" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Student Records</a>
            </div>
          </div>

          <!-- Reports Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['reports', 'export-reports'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              Reports
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/reports/lectures" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Lecture Reports</a>
              <a href="/teacher/reports/syllabus" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Syllabus Reports</a>
              <a href="/teacher/reports/practicals" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Practical Reports</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/teacher/reports/export" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Export Reports</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side Navigation -->
      <div class="flex items-center space-x-4">
        <!-- Notifications -->
        <div class="relative">
          <a href="/teacher/notifications" class="text-white hover:text-gray-200 transition">
            <div class="relative">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <% if (notificationCount && notificationCount > 0) { %>
                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  <%= notificationCount > 9 ? '9+' : notificationCount %>
                </span>
              <% } %>
            </div>
          </a>
        </div>

        <!-- User Profile and Logout -->
        <div class="flex items-center space-x-4">
          <div class="relative group">
            <button class="flex items-center focus:outline-none">
              <% if (user && user.profile_image) { %>
                <img src="<%= user.profile_image %>" alt="Profile" class="h-8 w-8 rounded-full object-cover">
              <% } else { %>
                <%
                  const initials = user && user.name
                    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)
                    : (user && user.username ? user.username.substring(0, 2).toUpperCase() : 'U');
                %>
                <div class="h-8 w-8 rounded-full bg-teacher-secondary flex items-center justify-center text-white">
                  <span class="text-sm font-bold"><%= initials %></span>
                </div>
              <% } %>
              <svg class="w-4 h-4 ml-1 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute right-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/teacher/classes" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">My Classes</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/help" class="block px-4 py-2 text-gray-800 hover:bg-gray-100">Help</a>
            </div>
          </div>
          <a href="/logout" class="flex items-center space-x-2 px-3 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span class="text-sm font-medium">Logout</span>
          </a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <div class="md:hidden">
        <button id="mobile-menu-button" class="text-white focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden bg-teacher-secondary hidden">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
      <a href="/teacher/dashboard" class="block px-3 py-2 rounded <%= currentPage === 'dashboard' ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition text-white">
        Dashboard
      </a>

      <a href="/teacher/classes" class="block px-3 py-2 rounded <%= currentPage === 'classes' ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition text-white">
        My Classes
      </a>

      <!-- Timetable Dropdown (Mobile) -->
      <div x-data="{ open: false }">
        <button @click="open = !open" class="w-full flex items-center justify-between px-3 py-2 rounded <%= ['timetable', 'my-timetable'].includes(currentPage) ? 'bg-teacher-secondary' : 'hover:bg-teacher-secondary' %> transition text-white">
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Timetable
          </span>
          <svg class="w-4 h-4" :class="{'transform rotate-180': open}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div x-show="open" class="pl-6 mt-1 space-y-1">
          <a href="/teacher/timetable" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Class Timetable
          </a>
          <a href="/teacher/teacher-timetable" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Teacher Timetable
          </a>
          <a href="/teacher/my-timetable" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            My Schedule
          </a>
          <a href="/teacher/lecture-schedules" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Lecture Schedules
          </a>
          <a href="/teacher/holiday-calendar" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Holiday Calendar
          </a>
          <a href="/teacher/calendar" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Academic Calendar
          </a>
          <a href="/teacher/class-teacher-map" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Class-Teacher Map
          </a>
          <a href="/teacher/class-subject-summary" class="block px-3 py-2 rounded text-white hover:bg-teacher-secondary transition">
            Class-Subject Summary
          </a>
        </div>
      </div>

      <!-- Instruction Plans Section -->
      <div class="py-2">
        <div class="flex justify-between items-center text-white">
          <span>Instruction Plans</span>
          <button class="mobile-submenu-toggle focus:outline-none" data-target="instruction-plans-submenu">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
        <div id="instruction-plans-submenu" class="pl-4 hidden">
          <a href="/teacher/instruction-plans" class="block py-1 text-white">Instruction Plans</a>
          <a href="/teacher/daily-instruction-plans" class="block py-1 text-white">Daily Plans</a>
          <a href="/teacher/subject-eligibility" class="block py-1 text-white">Subject Eligibility</a>
          <a href="/teacher/class-assignments" class="block py-1 text-white">Class Assignments</a>
        </div>
      </div>

      <!-- Syllabus Section -->
      <div class="py-2">
        <div class="flex justify-between items-center text-white">
          <span>Syllabus</span>
          <button class="mobile-submenu-toggle focus:outline-none" data-target="syllabus-submenu">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
        <div id="syllabus-submenu" class="pl-4 hidden">
          <a href="/teacher/syllabus" class="block py-1 text-white">Syllabus Overview</a>
          <a href="/teacher/syllabus/topics" class="block py-1 text-white">Topic Management</a>
          <a href="/teacher/syllabus/progress" class="block py-1 text-white">Progress Tracking</a>
        </div>
      </div>

      <!-- Practicals Section -->
      <div class="py-2">
        <div class="flex justify-between items-center text-white">
          <span>Practicals</span>
          <button class="mobile-submenu-toggle focus:outline-none" data-target="practicals-submenu">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
        <div id="practicals-submenu" class="pl-4 hidden">
          <a href="/teacher/practicals" class="block py-1 text-white">Lab Sessions</a>
          <a href="/teacher/practicals/schedule" class="block py-1 text-white">Schedule Practical</a>
          <a href="/teacher/practicals/records" class="block py-1 text-white">Student Records</a>
        </div>
      </div>

      <!-- Reports Section -->
      <div class="py-2">
        <div class="flex justify-between items-center text-white">
          <span>Reports</span>
          <button class="mobile-submenu-toggle focus:outline-none" data-target="reports-submenu">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
        <div id="reports-submenu" class="pl-4 hidden">
          <a href="/teacher/reports/lectures" class="block py-1 text-white">Lecture Reports</a>
          <a href="/teacher/reports/syllabus" class="block py-1 text-white">Syllabus Reports</a>
          <a href="/teacher/reports/practicals" class="block py-1 text-white">Practical Reports</a>
          <div class="border-t border-gray-600 my-1"></div>
          <a href="/teacher/reports/export" class="block py-1 text-white">Export Reports</a>
        </div>
      </div>

      <div class="border-t border-gray-600 my-2"></div>
      <a href="/teacher/classes" class="block py-2 text-white">My Classes</a>
      <a href="/help" class="block py-2 text-white">Help</a>
    </div>
  </div>
</nav>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });

    // Mobile submenu toggles
    const submenuToggles = document.querySelectorAll('.mobile-submenu-toggle');
    submenuToggles.forEach(toggle => {
      toggle.addEventListener('click', function() {
        const targetId = this.getAttribute('data-target');
        const targetMenu = document.getElementById(targetId);
        targetMenu.classList.toggle('hidden');

        // Toggle icon rotation
        const icon = this.querySelector('svg');
        if (targetMenu.classList.contains('hidden')) {
          icon.style.transform = 'rotate(0deg)';
        } else {
          icon.style.transform = 'rotate(180deg)';
        }
      });
    });
  });
</script>
