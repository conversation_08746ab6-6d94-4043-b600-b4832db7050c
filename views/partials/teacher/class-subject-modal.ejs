<!-- Class-Subject-Teacher Modal -->
<div id="class-subject-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-gray-800">Class-wise Subject Lectures</h2>
      <button id="close-class-subject-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Filters -->
    <div class="mb-4 flex flex-wrap gap-2">
      <div class="w-full md:w-auto">
        <label for="modal-class-filter" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
        <select id="modal-class-filter" class="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          <option value="all">All Classes</option>
          <!-- Will be populated dynamically -->
        </select>
      </div>
      <div class="w-full md:w-auto">
        <label for="modal-subject-filter" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
        <select id="modal-subject-filter" class="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          <option value="all">All Subjects</option>
          <!-- Will be populated dynamically -->
        </select>
      </div>
    </div>

    <!-- Loading indicator -->
    <div id="class-subject-loading" class="py-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-teacher-primary"></div>
      <p class="mt-2 text-gray-600">Loading data...</p>
    </div>

    <!-- Content area -->
    <div id="class-subject-content" class="hidden">
      <!-- Table for class-subject-teacher data -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Theory Lectures</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Practical Sessions</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            </tr>
          </thead>
          <tbody id="class-subject-table-body" class="bg-white divide-y divide-gray-200">
            <!-- Will be populated dynamically -->
          </tbody>
        </table>
      </div>

      <!-- Summary section -->
      <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 class="text-lg font-medium text-gray-700 mb-2">Summary</h3>
        <div id="class-subject-summary" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Will be populated dynamically -->
        </div>
      </div>
    </div>

    <!-- No data message -->
    <div id="class-subject-no-data" class="hidden py-8 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p class="mt-2 text-gray-600">No data available for the selected filters.</p>
    </div>

    <!-- Footer -->
    <div class="mt-6 flex justify-end">
      <button id="close-class-subject-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">Close</button>
    </div>
  </div>
</div>
