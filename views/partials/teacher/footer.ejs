        </main>
    </div>
    
    <footer class="bg-teacher-dark text-white py-4">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p>&copy; <%= new Date().getFullYear() %> Exam Prep Platform. All rights reserved.</p>
                </div>
                <div class="flex space-x-4">
                    <a href="/privacy-policy" class="text-gray-300 hover:text-white">Privacy Policy</a>
                    <a href="/terms-of-service" class="text-gray-300 hover:text-white">Terms of Service</a>
                    <a href="/help" class="text-gray-300 hover:text-white">Help</a>
                </div>
            </div>
        </div>
    </footer>
    
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
        
        // Profile dropdown toggle
        document.getElementById('profile-dropdown-btn').addEventListener('click', function() {
            document.getElementById('profile-dropdown').classList.toggle('hidden');
        });
        
        // Notification dropdown toggle
        document.getElementById('notification-btn').addEventListener('click', function() {
            document.getElementById('notification-dropdown').classList.toggle('hidden');
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const profileBtn = document.getElementById('profile-dropdown-btn');
            const profileDropdown = document.getElementById('profile-dropdown');
            const notificationBtn = document.getElementById('notification-btn');
            const notificationDropdown = document.getElementById('notification-dropdown');
            
            if (!profileBtn.contains(event.target) && !profileDropdown.contains(event.target)) {
                profileDropdown.classList.add('hidden');
            }
            
            if (!notificationBtn.contains(event.target) && !notificationDropdown.contains(event.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });
        
        // Initialize Select2 for all select elements
        $(document).ready(function() {
            $('select').select2({
                width: '100%'
            });
        });
    </script>
</body>
</html>
