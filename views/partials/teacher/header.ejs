<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | Exam Prep Platform</title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/teacher.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Common JS -->
    <script src="/js/teacher/common.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <header class="bg-teacher-dark text-white shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center">
                    <a href="/teacher/dashboard" class="flex items-center">
                        <img src="/images/logo.png" alt="Logo" class="h-10 mr-2">
                        <span class="text-xl font-bold">Exam Prep Platform</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="notification-btn" class="text-white hover:text-gray-200">
                            <i class="fas fa-bell text-xl"></i>
                            <span id="notification-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">0</span>
                        </button>
                        <div id="notification-dropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10 hidden">
                            <div class="p-2 border-b border-gray-200">
                                <h3 class="text-gray-700 font-semibold">Notifications</h3>
                            </div>
                            <div id="notification-list" class="max-h-64 overflow-y-auto">
                                <div class="p-4 text-center text-gray-500">
                                    No new notifications
                                </div>
                            </div>
                            <div class="p-2 border-t border-gray-200 text-center">
                                <a href="/teacher/notifications" class="text-teacher-primary hover:text-teacher-secondary text-sm">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <button id="profile-dropdown-btn" class="flex items-center space-x-2 text-white hover:text-gray-200">
                            <div class="w-8 h-8 rounded-full bg-teacher-primary flex items-center justify-center">
                                <% if (locals.user && locals.user.profile_image) { %>
                                    <img src="<%= locals.user.profile_image %>" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                <% } else { %>
                                    <span class="text-white font-semibold"><%= locals.user ? locals.user.name.charAt(0).toUpperCase() : 'U' %></span>
                                <% } %>
                            </div>
                            <span><%= locals.user ? locals.user.name : 'User' %></span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="profile-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                            <a href="/teacher/classes" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-users mr-2"></i> My Classes
                            </a>
                            <a href="/profile" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <a href="/settings" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                            <a href="/help" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-question-circle mr-2"></i> Help
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <a href="/logout" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex flex-1">
        <!-- Sidebar -->
        <aside class="bg-teacher-dark text-white w-64 flex-shrink-0 hidden md:block">
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="/teacher/dashboard" class="block py-2 px-4 rounded-md <%= currentPage === 'dashboard' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/timetable" class="block py-2 px-4 rounded-md <%= currentPage === 'timetable' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-calendar-alt mr-2"></i> My Timetable
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/lectures" class="block py-2 px-4 rounded-md <%= currentPage === 'lectures' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-chalkboard-teacher mr-2"></i> Lectures
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/syllabus" class="block py-2 px-4 rounded-md <%= currentPage === 'syllabus' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-book mr-2"></i> Syllabus
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/practicals" class="block py-2 px-4 rounded-md <%= currentPage === 'practicals' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-flask mr-2"></i> Practicals
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/practical-records" class="block py-2 px-4 rounded-md <%= currentPage === 'practical-records' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-clipboard-list mr-2"></i> Practical Records
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/reports" class="block py-2 px-4 rounded-md <%= currentPage === 'reports' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-chart-bar mr-2"></i> Reports
                        </a>
                    </li>
                    <li>
                        <a href="/teacher/classes" class="block py-2 px-4 rounded-md <%= currentPage === 'classes' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                            <i class="fas fa-users mr-2"></i> Classes
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Mobile Navigation -->
        <div class="md:hidden bg-teacher-dark text-white w-full">
            <div class="p-4 flex justify-between items-center">
                <button id="mobile-menu-btn" class="text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-lg font-semibold"><%= pageTitle %></h1>
                <div class="w-6"></div> <!-- Spacer for alignment -->
            </div>
            <div id="mobile-menu" class="hidden">
                <nav class="p-4">
                    <ul class="space-y-2">
                        <li>
                            <a href="/teacher/dashboard" class="block py-2 px-4 rounded-md <%= currentPage === 'dashboard' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/timetable" class="block py-2 px-4 rounded-md <%= currentPage === 'timetable' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-calendar-alt mr-2"></i> My Timetable
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/lectures" class="block py-2 px-4 rounded-md <%= currentPage === 'lectures' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-chalkboard-teacher mr-2"></i> Lectures
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/syllabus" class="block py-2 px-4 rounded-md <%= currentPage === 'syllabus' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-book mr-2"></i> Syllabus
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/practicals" class="block py-2 px-4 rounded-md <%= currentPage === 'practicals' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-flask mr-2"></i> Practicals
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/practical-records" class="block py-2 px-4 rounded-md <%= currentPage === 'practical-records' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-clipboard-list mr-2"></i> Practical Records
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/reports" class="block py-2 px-4 rounded-md <%= currentPage === 'reports' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-chart-bar mr-2"></i> Reports
                            </a>
                        </li>
                        <li>
                            <a href="/teacher/classes" class="block py-2 px-4 rounded-md <%= currentPage === 'classes' ? 'bg-teacher-primary text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>">
                                <i class="fas fa-users mr-2"></i> Classes
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 p-4 md:p-6 overflow-auto">
