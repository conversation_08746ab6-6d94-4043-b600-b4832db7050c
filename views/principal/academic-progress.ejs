<!-- Academic Progress Overview -->
<div class="mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Syllabus Completion Overview</h2>

        <!-- Subject-wise Progress Summary -->
        <% if (subjectProgress && subjectProgress.length > 0) { %>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <% subjectProgress.forEach(subject => { %>
                    <div class="bg-gradient-to-br from-principal-light to-white rounded-lg p-6 border border-principal-light card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900"><%= subject.subject_name %></h3>
                            <div class="text-right">
                                <div class="text-2xl font-bold <%= subject.completion_percentage >= 80 ? 'text-green-600' : subject.completion_percentage >= 60 ? 'text-blue-600' : subject.completion_percentage >= 40 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= parseFloat(subject.completion_percentage || 0).toFixed(1) %>%
                                </div>
                                <div class="text-sm text-gray-500">
                                    <%= subject.completed_topics || 0 %>/<%= subject.total_topics || 0 %> topics
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="h-3 rounded-full transition-all duration-300 <%= subject.completion_percentage >= 80 ? 'bg-green-500' : subject.completion_percentage >= 60 ? 'bg-blue-500' : subject.completion_percentage >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                 style="width: <%= Math.min(subject.completion_percentage || 0, 100) %>%"></div>
                        </div>

                        <!-- Status Badge -->
                        <div class="mt-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= subject.completion_percentage >= 80 ? 'bg-green-100 text-green-800' : subject.completion_percentage >= 60 ? 'bg-blue-100 text-blue-800' : subject.completion_percentage >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                <% if (subject.completion_percentage >= 80) { %>
                                    <i class="fas fa-check-circle mr-1"></i> Excellent
                                <% } else if (subject.completion_percentage >= 60) { %>
                                    <i class="fas fa-thumbs-up mr-1"></i> Good
                                <% } else if (subject.completion_percentage >= 40) { %>
                                    <i class="fas fa-exclamation-triangle mr-1"></i> Average
                                <% } else { %>
                                    <i class="fas fa-times-circle mr-1"></i> Needs Attention
                                <% } %>
                            </span>
                        </div>
                    </div>
                <% }); %>
            </div>
        <% } else { %>
            <div class="text-center py-8">
                <i class="fas fa-book-open text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No subject data available</h3>
                <p class="text-sm text-gray-500">Subject progress will appear here once lectures are scheduled.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Detailed Class-wise Progress -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Class-wise Academic Progress</h2>
            <div class="flex items-center space-x-2 text-sm">
                <span class="flex items-center">
                    <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                    Excellent (80%+)
                </span>
                <span class="flex items-center">
                    <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                    Good (60-79%)
                </span>
                <span class="flex items-center">
                    <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                    Average (40-59%)
                </span>
                <span class="flex items-center">
                    <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                    Poor (<40%)
                </span>
            </div>
        </div>
    </div>

    <div class="p-6">
        <% if (classProgress && classProgress.length > 0) { %>
            <!-- Filter and Search -->
            <div class="mb-6 flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="search-classes" placeholder="Search classes or subjects..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                </div>
                <div class="flex gap-2">
                    <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="">All Status</option>
                        <option value="excellent">Excellent</option>
                        <option value="good">Good</option>
                        <option value="average">Average</option>
                        <option value="poor">Poor</option>
                    </select>
                </div>
            </div>

            <!-- Progress Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Class & Subject
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Progress
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Topics
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="progress-table-body">
                        <% classProgress.forEach(progress => { %>
                            <tr class="hover:bg-gray-50 progress-row"
                                data-class="<%= progress.class_name %>"
                                data-subject="<%= progress.subject_name %>"
                                data-status="<%= progress.completion_percentage >= 80 ? 'excellent' : progress.completion_percentage >= 60 ? 'good' : progress.completion_percentage >= 40 ? 'average' : 'poor' %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <%= progress.class_name %>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <%= progress.subject_name %>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <%= progress.teacher_name || 'Not Assigned' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= progress.completion_percentage >= 80 ? 'bg-green-500' : progress.completion_percentage >= 60 ? 'bg-blue-500' : progress.completion_percentage >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(progress.completion_percentage || 0, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(progress.completion_percentage || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <%= progress.completed_topics || 0 %>/<%= progress.total_topics || 0 %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= progress.completion_percentage >= 80 ? 'bg-green-100 text-green-800' : progress.completion_percentage >= 60 ? 'bg-blue-100 text-blue-800' : progress.completion_percentage >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (progress.completion_percentage >= 80) { %>
                                            Excellent
                                        <% } else if (progress.completion_percentage >= 60) { %>
                                            Good
                                        <% } else if (progress.completion_percentage >= 40) { %>
                                            Average
                                        <% } else { %>
                                            Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-principal-primary hover:text-principal-dark" onclick="viewDetails('<%= progress.class_name %>', '<%= progress.subject_name %>')">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-chart-line text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No progress data available</h3>
                <p class="text-sm text-gray-500">Class progress will appear here once lectures are scheduled and delivered.</p>
            </div>
        <% } %>
    </div>
</div>

<script>
    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-classes');
        const statusFilter = document.getElementById('filter-status');
        const tableBody = document.getElementById('progress-table-body');
        const rows = document.querySelectorAll('.progress-row');

        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;

            rows.forEach(row => {
                const className = row.dataset.class.toLowerCase();
                const subjectName = row.dataset.subject.toLowerCase();
                const status = row.dataset.status;

                const matchesSearch = className.includes(searchTerm) || subjectName.includes(searchTerm);
                const matchesStatus = !statusValue || status === statusValue;

                row.style.display = matchesSearch && matchesStatus ? '' : 'none';
            });
        }

        if (searchInput) searchInput.addEventListener('input', filterTable);
        if (statusFilter) statusFilter.addEventListener('change', filterTable);
    });

    function viewDetails(className, subjectName) {
        // Implement view details functionality
        console.log(`Viewing details for ${className} - ${subjectName}`);
    }

    // Auto-refresh academic progress data
    function refreshPageData() {
        fetch('/principal/api/academic-progress')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Academic progress data updated:', data.data);
                    // Update the table without full page reload if needed
                }
            })
            .catch(error => {
                console.error('Error refreshing academic progress:', error);
            });
    }

    // Start auto-refresh every 2 minutes
    startAutoRefresh(120000);
</script>
