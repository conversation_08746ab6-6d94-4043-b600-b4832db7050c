<!-- Executive Leadership Dashboard Header -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">School Leadership Overview</h1>
                <p class="text-blue-100 text-lg">Strategic insights and institutional performance metrics</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span class="font-bold">PRINCIPAL ACCESS</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Executive KPI Dashboard -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Faculty Strength -->
    <div class="bg-white rounded-xl shadow-lg p-6 card-hover border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-user-tie text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Faculty Strength</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark"><%= stats.total_teachers || 0 %></p>
                <p class="text-sm text-principal-silver">Active Educators</p>
            </div>
            <div class="text-right">
                <div class="w-12 h-12 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-white text-lg"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Body -->
    <div class="bg-white rounded-xl shadow-lg p-6 card-hover border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-users text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Student Body</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark"><%= stats.total_students || 0 %></p>
                <p class="text-sm text-principal-silver">Enrolled Learners</p>
            </div>
            <div class="text-right">
                <div class="w-12 h-12 bg-gradient-to-br from-principal-secondary to-principal-hover rounded-full flex items-center justify-center">
                    <i class="fas fa-user-graduate text-white text-lg"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Operations -->
    <div class="bg-white rounded-xl shadow-lg p-6 card-hover border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-calendar-check text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Today's Sessions</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark"><%= stats.total_lectures_today || 0 %></p>
                <p class="text-sm text-principal-silver"><%= stats.delivered_lectures || 0 %> Completed</p>
            </div>
            <div class="text-right">
                <div class="w-12 h-12 bg-gradient-to-br from-principal-accent to-principal-gold rounded-full flex items-center justify-center">
                    <i class="fas fa-clock text-white text-lg"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Academic Excellence -->
    <div class="bg-white rounded-xl shadow-lg p-6 card-hover border-l-4 border-principal-gold">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-orange-50 rounded-lg mr-3">
                        <i class="fas fa-trophy text-principal-gold text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-gold uppercase tracking-wide">Academic Progress</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark"><%= parseFloat(stats.completion_percentage || 0).toFixed(1) %>%</p>
                <p class="text-sm text-principal-silver">Curriculum Completion</p>
            </div>
            <div class="text-right">
                <div class="w-12 h-12 bg-gradient-to-br from-principal-gold to-orange-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-lg"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Executive Command Center -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Strategic Operations Monitor -->
    <div class="lg:col-span-2 bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                        <i class="fas fa-satellite-dish text-lg"></i>
                    </div>
                    <h2 class="text-xl font-bold">Strategic Operations Monitor</h2>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium">LIVE</span>
                </div>
            </div>
        </div>
        <div class="p-6">
            <% if (recentActivities && recentActivities.length > 0) { %>
                <div class="space-y-4 max-h-96 overflow-y-auto principal-scrollbar">
                    <% recentActivities.forEach(activity => { %>
                        <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-4 border-principal-accent hover:shadow-md transition-all">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center shadow-md">
                                    <i class="fas fa-bolt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center mb-1">
                                    <span class="text-xs font-bold text-principal-primary uppercase tracking-wide mr-2">OPERATION</span>
                                    <div class="w-1 h-1 bg-principal-accent rounded-full"></div>
                                </div>
                                <p class="text-sm font-semibold text-principal-dark">
                                    <%= activity.title %>
                                </p>
                                <p class="text-sm text-principal-silver">
                                    <%= activity.subtitle %> • <span class="font-medium"><%= activity.teacher_name %></span>
                                </p>
                                <p class="text-xs text-principal-silver mt-1 flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    <%= formatDate(activity.date) %>
                                </p>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold <%= getStatusBadge(activity.status) %> shadow-sm">
                                    <%= activity.status.toUpperCase() %>
                                </span>
                            </div>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900">No recent activities</h3>
                    <p class="text-sm text-gray-500">Activities will appear here as they occur.</p>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Executive Intelligence Panel -->
    <div class="space-y-6">
        <!-- Institutional Excellence Metrics -->
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
            <div class="bg-gradient-to-r from-principal-accent to-principal-gold text-white p-4 rounded-t-xl">
                <div class="flex items-center">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                        <i class="fas fa-medal text-sm"></i>
                    </div>
                    <h3 class="text-lg font-bold">Excellence Rankings</h3>
                </div>
            </div>
            <div class="p-6">
                <% if (classPerformance && classPerformance.length > 0) { %>
                    <div class="space-y-4">
                        <% classPerformance.forEach(cls => { %>
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-3 border-principal-primary">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-3">
                                        <span class="text-white text-xs font-bold"><%= cls.class_name.charAt(cls.class_name.length-1) %></span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-principal-dark"><%= cls.class_name %></p>
                                        <p class="text-xs text-principal-silver"><%= cls.total_lectures %> sessions</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center">
                                        <div class="w-12 h-2 bg-gray-200 rounded-full mr-2">
                                            <div class="h-2 rounded-full <%= cls.completion_rate >= 80 ? 'bg-green-500' : cls.completion_rate >= 60 ? 'bg-blue-500' : cls.completion_rate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(cls.completion_rate || 0, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-bold <%= getStatusColor(cls.completion_rate >= 80 ? 'excellent' : cls.completion_rate >= 60 ? 'good' : cls.completion_rate >= 40 ? 'average' : 'poor') %>">
                                            <%= parseFloat(cls.completion_rate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } else { %>
                    <p class="text-sm text-gray-500">No class data available</p>
                <% } %>
            </div>
        </div>

        <!-- Faculty Leadership Board -->
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-4 rounded-t-xl">
                <div class="flex items-center">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                        <i class="fas fa-star text-sm"></i>
                    </div>
                    <h3 class="text-lg font-bold">Faculty Leaders</h3>
                </div>
            </div>
            <div class="p-6">
                <% if (teacherPerformance && teacherPerformance.length > 0) { %>
                    <div class="space-y-4">
                        <% teacherPerformance.forEach(teacher => { %>
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-3 border-principal-accent">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-br from-principal-accent to-principal-gold rounded-full flex items-center justify-center mr-3">
                                        <span class="text-white text-xs font-bold"><%= teacher.teacher_name.split(' ').map(n => n[0]).join('') %></span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-bold text-principal-dark"><%= teacher.teacher_name %></p>
                                        <p class="text-xs text-principal-silver"><%= teacher.total_lectures %> sessions</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center">
                                        <div class="w-12 h-2 bg-gray-200 rounded-full mr-2">
                                            <div class="h-2 rounded-full <%= teacher.completion_rate >= 80 ? 'bg-green-500' : teacher.completion_rate >= 60 ? 'bg-blue-500' : teacher.completion_rate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(teacher.completion_rate || 0, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-bold <%= getStatusColor(teacher.completion_rate >= 80 ? 'excellent' : teacher.completion_rate >= 60 ? 'good' : teacher.completion_rate >= 40 ? 'average' : 'poor') %>">
                                            <%= parseFloat(teacher.completion_rate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } else { %>
                    <p class="text-sm text-gray-500">No teacher data available</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Strategic Calendar -->
<div class="mt-8 bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-dark to-principal-primary text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-calendar-alt text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Strategic Calendar</h2>
            </div>
            <a href="/principal/teacher-timetables" class="bg-principal-accent hover:bg-principal-gold text-principal-dark px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                Command Center <i class="fas fa-external-link-alt ml-1"></i>
            </a>
        </div>
    </div>
    <div class="p-6">
        <% if (upcomingEvents && upcomingEvents.length > 0) { %>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <% upcomingEvents.forEach(event => { %>
                    <div class="bg-gradient-to-br from-principal-light to-white border-2 border-principal-accent rounded-xl p-4 hover:shadow-lg transition-all transform hover:-translate-y-1">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <div class="w-6 h-6 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-bolt text-white text-xs"></i>
                                    </div>
                                    <span class="text-xs font-bold text-principal-primary uppercase tracking-wide">MISSION</span>
                                </div>
                                <p class="text-sm font-bold text-principal-dark"><%= event.title %></p>
                                <p class="text-xs text-principal-silver mt-1"><%= event.subtitle %></p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-map-marker-alt text-principal-accent text-xs mr-1"></i>
                                    <p class="text-xs font-medium text-principal-accent"><%= event.class_name %></p>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between pt-3 border-t border-principal-light">
                            <div class="flex items-center">
                                <i class="fas fa-calendar text-principal-silver text-xs mr-1"></i>
                                <span class="text-xs font-medium text-principal-silver">
                                    <%= formatDate(event.date) %>
                                </span>
                            </div>
                            <div class="bg-principal-accent text-principal-dark px-2 py-1 rounded-full">
                                <span class="text-xs font-bold">
                                    <%= formatTime(event.start_time) %>
                                </span>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        <% } else { %>
            <div class="text-center py-8">
                <i class="fas fa-calendar-plus text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No upcoming events</h3>
                <p class="text-sm text-gray-500">Scheduled events will appear here.</p>
            </div>
        <% } %>
    </div>
</div>

<script>
    // Auto-refresh dashboard data every 30 seconds
    function refreshPageData() {
        fetch('/principal/api/dashboard-stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats without full page reload
                    console.log('Dashboard stats updated:', data.data);
                }
            })
            .catch(error => {
                console.error('Error refreshing dashboard data:', error);
            });
    }

    // Start auto-refresh
    startAutoRefresh(30000); // 30 seconds

    // Initialize charts if needed
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Principal dashboard initialized');
    });
</script>
