<!-- Principal Profile Page -->
<div class="min-h-screen bg-gradient-to-br from-principal-primary to-principal-secondary">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-800">Principal Profile</h1>
            <p class="text-gray-600 mt-2">Executive Leadership Dashboard</p>
          </div>
          <div class="flex space-x-3">
            <button id="edit-profile-btn" class="bg-principal-primary hover:bg-principal-secondary text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-edit mr-2"></i>Edit Profile
            </button>
            <button id="change-password-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-key mr-2"></i>Change Password
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Personal Information Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>
          <div class="p-6">
            <!-- Profile Image -->
            <div class="flex flex-col items-center mb-6">
              <div class="relative">
                <% if (user && user.profile_image) { %>
                  <img src="<%= user.profile_image %>" alt="Principal Photo" class="w-32 h-32 rounded-full object-cover border-4 border-principal-primary shadow-lg">
                <% } else { %>
                  <div class="w-32 h-32 rounded-full bg-principal-primary flex items-center justify-center text-white text-4xl font-bold shadow-lg">
                    <%= user && user.name ? user.name.charAt(0).toUpperCase() : 'P' %>
                  </div>
                <% } %>
                <button class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                  <i class="fas fa-camera text-principal-primary"></i>
                </button>
              </div>
              <h3 class="text-xl font-bold text-gray-800"><%= user ? user.name : 'Principal Name' %></h3>
              <p class="text-principal-primary font-semibold">School Principal</p>
              <% if (user && user.current_school) { %>
              <p class="text-gray-600 text-sm mt-1"><%= user.current_school %></p>
              <% } %>
              <span class="mt-2 px-3 py-1 bg-principal-primary bg-opacity-10 text-principal-primary rounded-full text-sm font-medium">
                Executive Leadership
              </span>
            </div>

            <!-- Contact Information -->
            <div class="space-y-4">
              <div class="flex items-center">
                <i class="fas fa-envelope text-principal-primary w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.email : '<EMAIL>' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user text-principal-primary w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.username : 'principal' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-calendar text-principal-primary w-5"></i>
                <span class="ml-3 text-gray-700">
                  Joined: <%= user && user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A' %>
                </span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-clock text-principal-primary w-5"></i>
                <span class="ml-3 text-gray-700">
                  Last Login: <%= user && user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never' %>
                </span>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">About</h4>
              <p class="text-gray-600 text-sm leading-relaxed">
                <%= user && user.bio ? user.bio : 'Educational leader committed to academic excellence and student success. Dedicated to fostering a positive learning environment and driving institutional growth.' %>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Professional Details -->
      <div class="lg:col-span-2">
        <div class="space-y-6">
          <!-- Professional Experience Timeline -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Professional Experience Timeline</h2>
            </div>
            <div class="p-6">
              <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>

                <!-- Experience entries -->
                <div class="space-y-8">
                  <!-- Current Position -->
                  <div class="relative flex items-start">
                    <div class="absolute left-6 w-4 h-4 bg-principal-primary rounded-full border-4 border-white shadow-lg"></div>
                    <div class="ml-16">
                      <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-4 rounded-lg">
                        <h4 class="font-bold text-lg">Principal</h4>
                        <p class="text-principal-light"><%= user && user.current_school ? user.current_school : 'Current School' %></p>
                        <p class="text-sm mt-2">
                          <%= user && user.joining_date ? formatDate(user.joining_date) + ' - Present' : 'Present Position' %>
                        </p>
                      </div>
                      <div class="mt-3 text-gray-700">
                        <p class="text-sm">Leading the institution with focus on academic excellence, infrastructure development, and holistic student growth. Overseeing curriculum implementation, teacher development, and school administration.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Previous Experience - Vice Principal -->
                  <div class="relative flex items-start">
                    <div class="absolute left-6 w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-lg"></div>
                    <div class="ml-16">
                      <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
                        <h4 class="font-bold text-lg">Vice Principal</h4>
                        <p class="text-blue-100">Government Senior Secondary School, Ludhiana</p>
                        <p class="text-sm mt-2">2019 - 2022 (3 years)</p>
                      </div>
                      <div class="mt-3 text-gray-700">
                        <p class="text-sm">Assisted in school administration, supervised academic programs, and managed student discipline. Coordinated with Punjab School Education Board for curriculum implementation and examination processes.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Teaching Experience -->
                  <div class="relative flex items-start">
                    <div class="absolute left-6 w-4 h-4 bg-green-500 rounded-full border-4 border-white shadow-lg"></div>
                    <div class="ml-16">
                      <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
                        <h4 class="font-bold text-lg">Senior Teacher (Mathematics)</h4>
                        <p class="text-green-100">Government High School, Jalandhar</p>
                        <p class="text-sm mt-2">2016 - 2019 (3 years)</p>
                      </div>
                      <div class="mt-3 text-gray-700">
                        <p class="text-sm">Taught Mathematics to classes 9-12, developed innovative teaching methodologies, and mentored junior teachers. Achieved 95% pass rate in board examinations and received Best Teacher Award from District Education Office.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Early Career -->
                  <div class="relative flex items-start">
                    <div class="absolute left-6 w-4 h-4 bg-purple-500 rounded-full border-4 border-white shadow-lg"></div>
                    <div class="ml-16">
                      <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
                        <h4 class="font-bold text-lg">Teacher (Mathematics & Science)</h4>
                        <p class="text-purple-100">Government Middle School, Amritsar</p>
                        <p class="text-sm mt-2">2014 - 2016 (2 years)</p>
                      </div>
                      <div class="mt-3 text-gray-700">
                        <p class="text-sm">Started teaching career in rural Punjab, focusing on foundational mathematics and science education. Implemented digital learning tools and improved student engagement through practical demonstrations.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Education/Training -->
                  <div class="relative flex items-start">
                    <div class="absolute left-6 w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg"></div>
                    <div class="ml-16">
                      <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-lg">
                        <h4 class="font-bold text-lg">B.Ed & Teacher Training</h4>
                        <p class="text-orange-100">Punjab University, Chandigarh</p>
                        <p class="text-sm mt-2">2012 - 2014</p>
                      </div>
                      <div class="mt-3 text-gray-700">
                        <p class="text-sm">Completed Bachelor of Education with specialization in Mathematics and Educational Psychology. Underwent teacher training programs by Punjab Education Department and NCERT.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Administrative Details -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Administrative Information</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Current School</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.current_school ? user.current_school : 'Not specified' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Department</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.department ? user.department : 'Administration' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Total Experience</h4>
                  <p class="text-gray-800 font-medium">
                    <%= user && user.total_experience_years ? user.total_experience_years + ' years' : 'Not specified' %>
                  </p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Teaching Experience</h4>
                  <p class="text-gray-800 font-medium">
                    <%= user && user.teaching_experience_years ? user.teaching_experience_years + ' years' : 'Not specified' %>
                  </p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Phone</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.phone ? user.phone : 'Not provided' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Office Location</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.office_location ? user.office_location : 'Principal Office' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Employee ID</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.employee_id ? user.employee_id : 'Not assigned' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Joining Date</h4>
                  <p class="text-gray-800 font-medium">
                    <%= user && user.joining_date ? formatDate(user.joining_date) : 'Not specified' %>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Educational Qualifications -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Educational Qualifications</h2>
            </div>
            <div class="p-6">
              <div class="space-y-6">
                <!-- Class 10 -->
                <div class="border-l-4 border-blue-500 pl-4">
                  <h4 class="font-semibold text-gray-800 mb-2">Class 10 (Secondary)</h4>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Board:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_10_board ? user.class_10_board : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Year:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_10_year ? user.class_10_year : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Percentage:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_10_percentage ? user.class_10_percentage + '%' : 'Not specified' %></span>
                    </div>
                  </div>
                </div>

                <!-- Class 12 -->
                <div class="border-l-4 border-green-500 pl-4">
                  <h4 class="font-semibold text-gray-800 mb-2">Class 12 (Higher Secondary)</h4>
                  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Board:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_12_board ? user.class_12_board : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Year:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_12_year ? user.class_12_year : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Stream:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_12_stream ? user.class_12_stream : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Percentage:</span>
                      <span class="ml-2 font-medium"><%= user && user.class_12_percentage ? user.class_12_percentage + '%' : 'Not specified' %></span>
                    </div>
                  </div>
                </div>

                <!-- Graduation -->
                <div class="border-l-4 border-purple-500 pl-4">
                  <h4 class="font-semibold text-gray-800 mb-2">Graduation</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Degree:</span>
                      <span class="ml-2 font-medium"><%= user && user.graduation_degree ? user.graduation_degree : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">University:</span>
                      <span class="ml-2 font-medium"><%= user && user.graduation_university ? user.graduation_university : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Year:</span>
                      <span class="ml-2 font-medium"><%= user && user.graduation_year ? user.graduation_year : 'Not specified' %></span>
                    </div>
                    <div>
                      <span class="text-gray-500">Percentage:</span>
                      <span class="ml-2 font-medium"><%= user && user.graduation_percentage ? user.graduation_percentage + '%' : 'Not specified' %></span>
                    </div>
                  </div>
                </div>

                <!-- Post Graduation -->
                <% if (user && (user.post_graduation_degree || user.phd_subject)) { %>
                <div class="border-l-4 border-orange-500 pl-4">
                  <h4 class="font-semibold text-gray-800 mb-2">Higher Education</h4>
                  <div class="space-y-3">
                    <% if (user.post_graduation_degree) { %>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span class="text-gray-500">Post Graduation:</span>
                        <span class="ml-2 font-medium"><%= user.post_graduation_degree %></span>
                      </div>
                      <div>
                        <span class="text-gray-500">University:</span>
                        <span class="ml-2 font-medium"><%= user.post_graduation_university || 'Not specified' %></span>
                      </div>
                    </div>
                    <% } %>
                    <% if (user.phd_subject) { %>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span class="text-gray-500">PhD Subject:</span>
                        <span class="ml-2 font-medium"><%= user.phd_subject %></span>
                      </div>
                      <div>
                        <span class="text-gray-500">PhD University:</span>
                        <span class="ml-2 font-medium"><%= user.phd_university || 'Not specified' %></span>
                      </div>
                    </div>
                    <% } %>
                  </div>
                </div>
                <% } %>

                <!-- Professional Certifications -->
                <% if (user && user.professional_certifications) { %>
                <div class="border-l-4 border-red-500 pl-4">
                  <h4 class="font-semibold text-gray-800 mb-2">Professional Certifications</h4>
                  <p class="text-sm text-gray-700"><%= user.professional_certifications %></p>
                </div>
                <% } %>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Quick Actions</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/principal/dashboard" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-tachometer-alt text-principal-primary text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Dashboard</span>
                </a>
                <a href="/principal/students" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-users text-principal-primary text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Students</span>
                </a>
                <a href="/principal/teacher-management" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-chalkboard-teacher text-principal-primary text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Teachers</span>
                </a>
                <a href="/principal/infrastructure" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-building text-principal-primary text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Infrastructure</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Profile Modal -->
<div id="edit-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Edit Profile</h3>
        <button id="close-edit-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="edit-profile-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input type="text" name="name" value="<%= user ? user.name : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input type="email" name="email" value="<%= user ? user.email : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input type="tel" name="phone" value="<%= user ? user.phone : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Current School</label>
          <input type="text" name="current_school" value="<%= user ? user.current_school : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent"><%= user ? user.bio : '' %></textarea>
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-edit" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-principal-primary text-white rounded-lg hover:bg-principal-secondary transition-colors">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Change Password</h3>
        <button id="close-password-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="change-password-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
          <input type="password" name="current_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
          <input type="password" name="new_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
          <input type="password" name="confirm_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-password" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-principal-primary text-white rounded-lg hover:bg-principal-secondary transition-colors">Update Password</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {

  // Modal functionality
  const editProfileBtn = document.getElementById('edit-profile-btn');
  const changePasswordBtn = document.getElementById('change-password-btn');
  const editModal = document.getElementById('edit-profile-modal');
  const passwordModal = document.getElementById('change-password-modal');

  // Edit Profile Modal
  editProfileBtn.addEventListener('click', () => {
    editModal.classList.remove('hidden');
  });

  document.getElementById('close-edit-modal').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  document.getElementById('cancel-edit').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  // Change Password Modal
  changePasswordBtn.addEventListener('click', () => {
    passwordModal.classList.remove('hidden');
  });

  document.getElementById('close-password-modal').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  document.getElementById('cancel-password').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  // Form submissions
  document.getElementById('edit-profile-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    try {
      const response = await fetch('/api/profile/update', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert('Profile updated successfully!');
        editModal.classList.add('hidden');
        location.reload();
      } else {
        alert('Failed to update profile');
      }
    } catch (error) {
      alert('Error updating profile');
    }
  });

  document.getElementById('change-password-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    if (formData.get('new_password') !== formData.get('confirm_password')) {
      console.error('New passwords do not match');
      return;
    }

    try {
      const response = await fetch('/api/profile/change-password', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        console.log('Password changed successfully!');
        passwordModal.classList.add('hidden');
        e.target.reset();
      } else {
        console.error('Failed to change password');
      }
    } catch (error) {
      console.error('Error changing password');
    }
  });

  // Close modals on outside click
  editModal.addEventListener('click', (e) => {
    if (e.target === editModal) {
      editModal.classList.add('hidden');
    }
  });

  passwordModal.addEventListener('click', (e) => {
    if (e.target === passwordModal) {
      passwordModal.classList.add('hidden');
    }
  });
});


</script>