<!-- Reports & Analytics Overview -->
<div class="mb-8">
    <div class="bg-gradient-principal text-white rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-2">Reports & Analytics Center</h2>
        <p class="text-principal-light">Comprehensive school performance reports and data insights</p>
    </div>
</div>

<!-- Quick Report Actions -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="generateReport('academic')">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-graduation-cap text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Academic Report</p>
                <p class="text-lg font-bold text-gray-900">Generate</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="generateReport('attendance')">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-calendar-check text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Attendance Report</p>
                <p class="text-lg font-bold text-gray-900">Generate</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="generateReport('teacher')">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                <i class="fas fa-chalkboard-teacher text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Teacher Report</p>
                <p class="text-lg font-bold text-gray-900">Generate</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="generateReport('performance')">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                <i class="fas fa-chart-line text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Performance Report</p>
                <p class="text-lg font-bold text-gray-900">Generate</p>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Academic Reports -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Academic Reports</h3>
            <p class="text-sm text-gray-500">Syllabus completion and academic progress</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Syllabus Completion Report</h4>
                        <p class="text-xs text-gray-500">Class-wise and subject-wise completion status</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Subject Progress Analysis</h4>
                        <p class="text-xs text-gray-500">Detailed subject-wise progress tracking</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Academic Calendar Report</h4>
                        <p class="text-xs text-gray-500">Scheduled vs actual lecture delivery</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Performance Reports -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Teacher Performance</h3>
            <p class="text-sm text-gray-500">Faculty performance and productivity metrics</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Teacher Efficiency Report</h4>
                        <p class="text-xs text-gray-500">Lecture completion rates and timeliness</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Workload Distribution</h4>
                        <p class="text-xs text-gray-500">Teacher workload and class assignments</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Professional Development</h4>
                        <p class="text-xs text-gray-500">Training and skill development tracking</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Analytics Reports -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Student Analytics</h3>
            <p class="text-sm text-gray-500">Student performance and engagement metrics</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Attendance Analysis</h4>
                        <p class="text-xs text-gray-500">Student attendance patterns and trends</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Performance Trends</h4>
                        <p class="text-xs text-gray-500">Student academic performance over time</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Engagement Metrics</h4>
                        <p class="text-xs text-gray-500">Student participation and activity levels</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Infrastructure Reports -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Infrastructure & Resources</h3>
            <p class="text-sm text-gray-500">Facility utilization and resource management</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Classroom Utilization</h4>
                        <p class="text-xs text-gray-500">Room usage and scheduling efficiency</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Resource Allocation</h4>
                        <p class="text-xs text-gray-500">Equipment and material distribution</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-principal-primary transition-colors cursor-pointer">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Maintenance Schedule</h4>
                        <p class="text-xs text-gray-500">Facility maintenance and upkeep tracking</p>
                    </div>
                    <button class="text-principal-primary hover:text-principal-dark">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Report Generator -->
<div class="mt-8 bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">Custom Report Generator</h2>
        <p class="text-sm text-gray-500">Create customized reports with specific parameters</p>
    </div>
    <div class="p-6">
        <form id="customReportForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="">Select report type</option>
                        <option value="academic">Academic Performance</option>
                        <option value="attendance">Attendance Analysis</option>
                        <option value="teacher">Teacher Performance</option>
                        <option value="infrastructure">Infrastructure Usage</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="week">Last Week</option>
                        <option value="month">Last Month</option>
                        <option value="quarter">Last Quarter</option>
                        <option value="year">Last Year</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="pdf">PDF</option>
                        <option value="excel">Excel</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="btn-principal px-6 py-2 rounded-lg font-medium">
                    <i class="fas fa-file-export mr-2"></i>
                    Generate Report
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function generateReport(type) {
        // Show loading state
        const button = event.target.closest('.card-hover');
        const originalContent = button.innerHTML;
        button.innerHTML = '<div class="flex items-center justify-center"><div class="spinner-principal mr-2"></div>Generating...</div>';

        // Simulate report generation
        setTimeout(() => {
            button.innerHTML = originalContent;
            console.log(`${type.charAt(0).toUpperCase() + type.slice(1)} report generated successfully!`);
        }, 2000);
    }

    document.getElementById('customReportForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitButton = e.target.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<div class="spinner-principal mr-2"></div>Generating...';
        submitButton.disabled = true;

        // Simulate custom report generation
        setTimeout(() => {
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
            console.log('Custom report generated successfully!');
        }, 3000);
    });

    // Auto-refresh functionality
    function refreshPageData() {
        console.log('Refreshing reports data...');
    }

    // Start auto-refresh every 10 minutes
    startAutoRefresh(600000);
</script>
