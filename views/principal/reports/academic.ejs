<!-- Academic Reports Command Center -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Academic Intelligence Reports</h1>
                <p class="text-blue-100 text-lg">Comprehensive academic performance analytics and insights</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-chart-line mr-2"></i>
                    <span class="font-bold">ACADEMIC INTEL</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Syllabus Completion -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-book-open text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Syllabus Progress</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">87.5%</p>
                <p class="text-sm text-principal-silver">Overall Completion</p>
            </div>
        </div>
    </div>

    <!-- Class Performance -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-users text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Class Average</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">92.3%</p>
                <p class="text-sm text-principal-silver">Performance Score</p>
            </div>
        </div>
    </div>

    <!-- Subject Analysis -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-microscope text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Subject Analysis</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">15</p>
                <p class="text-sm text-principal-silver">Subjects Tracked</p>
            </div>
        </div>
    </div>
</div>

<!-- Academic Performance Dashboard -->
<div class="bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-chart-bar text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Academic Performance Analytics</h2>
            </div>
            <div class="flex items-center space-x-4">
                <select class="bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 text-sm">
                    <option value="all">All Classes</option>
                    <option value="grade1">Grade 1</option>
                    <option value="grade2">Grade 2</option>
                    <option value="grade3">Grade 3</option>
                </select>
                <button class="bg-principal-accent hover:bg-principal-gold text-principal-dark px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- Subject-wise Performance -->
        <div class="space-y-4">
            <% 
            const subjects = [
                { name: 'Mathematics', completion: 95, performance: 88, trend: 'up' },
                { name: 'English', completion: 92, performance: 91, trend: 'up' },
                { name: 'Science', completion: 89, performance: 85, trend: 'stable' },
                { name: 'Social Studies', completion: 87, performance: 89, trend: 'up' },
                { name: 'Hindi', completion: 84, performance: 82, trend: 'down' },
                { name: 'Computer Science', completion: 91, performance: 94, trend: 'up' }
            ];
            %>
            <% subjects.forEach(subject => { %>
                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-4 border-principal-primary hover:shadow-md transition-all">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-book text-white"></i>
                        </div>
                        <div>
                            <p class="text-lg font-bold text-principal-dark"><%= subject.name %></p>
                            <p class="text-sm text-principal-silver">Academic Subject</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <!-- Syllabus Completion -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Syllabus</p>
                            <div class="flex items-center mt-1">
                                <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                                    <div class="h-2 bg-principal-primary rounded-full" style="width: <%= subject.completion %>%"></div>
                                </div>
                                <span class="text-sm font-bold text-principal-dark"><%= subject.completion %>%</span>
                            </div>
                        </div>
                        <!-- Performance Score -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Performance</p>
                            <div class="flex items-center mt-1">
                                <div class="w-16 h-2 bg-gray-200 rounded-full mr-2">
                                    <div class="h-2 bg-principal-secondary rounded-full" style="width: <%= subject.performance %>%"></div>
                                </div>
                                <span class="text-sm font-bold text-principal-dark"><%= subject.performance %>%</span>
                            </div>
                        </div>
                        <!-- Trend -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Trend</p>
                            <div class="mt-1">
                                <i class="fas fa-arrow-<%= subject.trend === 'up' ? 'up text-green-500' : subject.trend === 'down' ? 'down text-red-500' : 'right text-yellow-500' %>"></i>
                            </div>
                        </div>
                    </div>
                </div>
            <% }); %>
        </div>
    </div>
</div>

<!-- Class-wise Performance -->
<div class="mt-8 bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-accent to-principal-gold text-white p-6 rounded-t-xl">
        <div class="flex items-center">
            <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                <i class="fas fa-users-cog text-lg"></i>
            </div>
            <h2 class="text-xl font-bold">Class Performance Matrix</h2>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% for(let grade = 1; grade <= 6; grade++) { %>
                <% for(let section of ['A', 'B']) { %>
                    <div class="bg-gradient-to-br from-principal-light to-white border-2 border-principal-accent rounded-xl p-4 hover:shadow-lg transition-all">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-2">
                                    <span class="text-white text-xs font-bold"><%= grade %><%= section %></span>
                                </div>
                                <span class="text-sm font-bold text-principal-dark">Grade <%= grade %>-<%= section %></span>
                            </div>
                            <span class="text-xs font-bold text-principal-primary"><%= Math.floor(Math.random() * 10) + 25 %> Students</span>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between text-xs">
                                <span class="text-principal-silver">Overall Score</span>
                                <span class="font-bold text-principal-dark"><%= Math.floor(Math.random() * 20) + 80 %>%</span>
                            </div>
                            <div class="w-full h-2 bg-gray-200 rounded-full">
                                <div class="h-2 bg-gradient-to-r from-principal-primary to-principal-secondary rounded-full" style="width: <%= Math.floor(Math.random() * 20) + 80 %>%"></div>
                            </div>
                        </div>
                    </div>
                <% } %>
            <% } %>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Academic Reports initialized');
        
        // Auto-refresh every 5 minutes
        startAutoRefresh(300000);
    });

    function refreshPageData() {
        console.log('Refreshing academic reports data...');
    }
</script>
