<!-- Attendance Intelligence Center -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Attendance Intelligence Center</h1>
                <p class="text-blue-100 text-lg">Real-time attendance monitoring and analytics</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-user-check mr-2"></i>
                    <span class="font-bold">ATTENDANCE INTEL</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Overview -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Today's Attendance -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-calendar-day text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Today</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">94.2%</p>
                <p class="text-sm text-principal-silver">Present Rate</p>
            </div>
        </div>
    </div>

    <!-- Weekly Average -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-calendar-week text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Weekly Avg</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">91.8%</p>
                <p class="text-sm text-principal-silver">This Week</p>
            </div>
        </div>
    </div>

    <!-- Monthly Trend -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-calendar-alt text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Monthly</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">89.5%</p>
                <p class="text-sm text-principal-silver">This Month</p>
            </div>
        </div>
    </div>

    <!-- Absentee Alerts -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-red-50 rounded-lg mr-3">
                        <i class="fas fa-exclamation-triangle text-red-500 text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-red-500 uppercase tracking-wide">Alerts</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">7</p>
                <p class="text-sm text-principal-silver">Chronic Absentees</p>
            </div>
        </div>
    </div>
</div>

<!-- Class-wise Attendance -->
<div class="bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-users text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Class-wise Attendance Analysis</h2>
            </div>
            <div class="flex items-center space-x-4">
                <select class="bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 text-sm">
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
                <button class="bg-principal-accent hover:bg-principal-gold text-principal-dark px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
            </div>
        </div>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <% for(let grade = 1; grade <= 6; grade++) { %>
                <% for(let section of ['A', 'B']) { %>
                    <% 
                    const totalStudents = Math.floor(Math.random() * 10) + 25;
                    const presentStudents = Math.floor(totalStudents * (0.85 + Math.random() * 0.15));
                    const attendanceRate = ((presentStudents / totalStudents) * 100).toFixed(1);
                    %>
                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-4 border-principal-primary hover:shadow-md transition-all">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-4">
                                <span class="text-white text-sm font-bold"><%= grade %><%= section %></span>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-principal-dark">Grade <%= grade %>-<%= section %></p>
                                <p class="text-sm text-principal-silver"><%= totalStudents %> Total Students</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-6">
                            <!-- Present Count -->
                            <div class="text-center">
                                <p class="text-xs font-semibold text-principal-silver uppercase">Present</p>
                                <p class="text-2xl font-bold text-green-600"><%= presentStudents %></p>
                            </div>
                            <!-- Absent Count -->
                            <div class="text-center">
                                <p class="text-xs font-semibold text-principal-silver uppercase">Absent</p>
                                <p class="text-2xl font-bold text-red-600"><%= totalStudents - presentStudents %></p>
                            </div>
                            <!-- Attendance Rate -->
                            <div class="text-center">
                                <p class="text-xs font-semibold text-principal-silver uppercase">Rate</p>
                                <div class="flex items-center mt-1">
                                    <div class="w-16 h-3 bg-gray-200 rounded-full mr-2">
                                        <div class="h-3 rounded-full <%= attendanceRate >= 90 ? 'bg-green-500' : attendanceRate >= 80 ? 'bg-yellow-500' : 'bg-red-500' %>" style="width: <%= attendanceRate %>%"></div>
                                    </div>
                                    <span class="text-sm font-bold text-principal-dark"><%= attendanceRate %>%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>
            <% } %>
        </div>
    </div>
</div>

<!-- Attendance Trends -->
<div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Weekly Trends -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-principal-accent to-principal-gold text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-chart-line text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Weekly Attendance Trends</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% 
                const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
                %>
                <% days.forEach((day, index) => { %>
                    <% const rate = Math.floor(Math.random() * 15) + 85; %>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-principal-dark"><%= day %></span>
                        <div class="flex items-center">
                            <div class="w-32 h-2 bg-gray-200 rounded-full mr-3">
                                <div class="h-2 bg-gradient-to-r from-principal-primary to-principal-secondary rounded-full" style="width: <%= rate %>%"></div>
                            </div>
                            <span class="text-sm font-bold text-principal-dark w-12"><%= rate %>%</span>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>

    <!-- Chronic Absentees -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-user-times text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Attendance Alerts</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% 
                const alerts = [
                    { name: 'Rahul Sharma', class: '5-A', rate: 65, days: 12 },
                    { name: 'Priya Patel', class: '3-B', rate: 72, days: 8 },
                    { name: 'Amit Kumar', class: '6-A', rate: 68, days: 10 },
                    { name: 'Sneha Singh', class: '4-B', rate: 70, days: 9 }
                ];
                %>
                <% alerts.forEach(student => { %>
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                        <div>
                            <p class="text-sm font-bold text-gray-800"><%= student.name %></p>
                            <p class="text-xs text-gray-600">Class <%= student.class %></p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-red-600"><%= student.rate %>%</p>
                            <p class="text-xs text-gray-600"><%= student.days %> days absent</p>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Attendance Reports initialized');
        
        // Auto-refresh every 2 minutes
        startAutoRefresh(120000);
    });

    function refreshPageData() {
        console.log('Refreshing attendance data...');
    }
</script>
