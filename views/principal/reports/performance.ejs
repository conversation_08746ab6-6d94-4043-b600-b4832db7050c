<!-- Performance Intelligence Center -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Performance Intelligence Center</h1>
                <p class="text-blue-100 text-lg">Comprehensive performance analytics and strategic insights</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-trophy mr-2"></i>
                    <span class="font-bold">PERFORMANCE INTEL</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Overall Performance -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-chart-line text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Overall</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">88.7%</p>
                <p class="text-sm text-principal-silver">School Average</p>
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-medal text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Excellence</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">156</p>
                <p class="text-sm text-principal-silver">Top Performers</p>
            </div>
        </div>
    </div>

    <!-- Improvement Rate -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-arrow-up text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Growth</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">+12.3%</p>
                <p class="text-sm text-principal-silver">This Quarter</p>
            </div>
        </div>
    </div>

    <!-- At-Risk Students -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-red-50 rounded-lg mr-3">
                        <i class="fas fa-exclamation-triangle text-red-500 text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-red-500 uppercase tracking-wide">At-Risk</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">23</p>
                <p class="text-sm text-principal-silver">Need Support</p>
            </div>
        </div>
    </div>
</div>

<!-- Subject Performance Analysis -->
<div class="bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-microscope text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Subject Performance Matrix</h2>
            </div>
            <div class="flex items-center space-x-4">
                <select class="bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 text-sm">
                    <option value="all">All Grades</option>
                    <option value="primary">Primary (1-3)</option>
                    <option value="middle">Middle (4-6)</option>
                </select>
                <button class="bg-principal-accent hover:bg-principal-gold text-principal-dark px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Export Analysis
                </button>
            </div>
        </div>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <% 
            const subjects = [
                { name: 'Mathematics', average: 92, highest: 98, lowest: 76, trend: 'up', difficulty: 'high' },
                { name: 'English', average: 89, highest: 96, lowest: 72, trend: 'up', difficulty: 'medium' },
                { name: 'Science', average: 87, highest: 95, lowest: 68, trend: 'stable', difficulty: 'high' },
                { name: 'Social Studies', average: 91, highest: 97, lowest: 78, trend: 'up', difficulty: 'low' },
                { name: 'Hindi', average: 85, highest: 94, lowest: 65, trend: 'down', difficulty: 'medium' },
                { name: 'Computer Science', average: 94, highest: 99, lowest: 82, trend: 'up', difficulty: 'low' }
            ];
            %>
            <% subjects.forEach(subject => { %>
                <div class="flex items-center justify-between p-4 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-4 border-principal-primary hover:shadow-md transition-all">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-book text-white"></i>
                        </div>
                        <div>
                            <p class="text-lg font-bold text-principal-dark"><%= subject.name %></p>
                            <p class="text-sm text-principal-silver">
                                Difficulty: 
                                <span class="<%= subject.difficulty === 'high' ? 'text-red-600' : subject.difficulty === 'medium' ? 'text-yellow-600' : 'text-green-600' %> font-medium">
                                    <%= subject.difficulty.toUpperCase() %>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <!-- Average Score -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Average</p>
                            <p class="text-2xl font-bold text-principal-dark"><%= subject.average %>%</p>
                        </div>
                        <!-- Score Range -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Range</p>
                            <p class="text-sm text-principal-dark">
                                <span class="text-green-600 font-bold"><%= subject.highest %>%</span> - 
                                <span class="text-red-600 font-bold"><%= subject.lowest %>%</span>
                            </p>
                        </div>
                        <!-- Performance Bar -->
                        <div class="text-center">
                            <p class="text-xs font-semibold text-principal-silver uppercase">Performance</p>
                            <div class="flex items-center mt-1">
                                <div class="w-20 h-3 bg-gray-200 rounded-full mr-2">
                                    <div class="h-3 rounded-full <%= subject.average >= 90 ? 'bg-green-500' : subject.average >= 80 ? 'bg-blue-500' : subject.average >= 70 ? 'bg-yellow-500' : 'bg-red-500' %>" style="width: <%= subject.average %>%"></div>
                                </div>
                                <i class="fas fa-arrow-<%= subject.trend === 'up' ? 'up text-green-500' : subject.trend === 'down' ? 'down text-red-500' : 'right text-yellow-500' %>"></i>
                            </div>
                        </div>
                    </div>
                </div>
            <% }); %>
        </div>
    </div>
</div>

<!-- Grade-wise Performance -->
<div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Top Performers -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-principal-accent to-principal-gold text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-crown text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Excellence Board</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% 
                const topPerformers = [
                    { name: 'Aarav Sharma', class: '6-A', score: 98.5, rank: 1 },
                    { name: 'Diya Patel', class: '5-B', score: 97.8, rank: 2 },
                    { name: 'Arjun Kumar', class: '6-B', score: 97.2, rank: 3 },
                    { name: 'Kavya Singh', class: '5-A', score: 96.9, rank: 4 },
                    { name: 'Rohan Gupta', class: '4-A', score: 96.5, rank: 5 }
                ];
                %>
                <% topPerformers.forEach(student => { %>
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-white rounded-lg border-l-4 border-yellow-500">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-xs font-bold">#<%= student.rank %></span>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-gray-800"><%= student.name %></p>
                                <p class="text-xs text-gray-600">Class <%= student.class %></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-yellow-600"><%= student.score %>%</p>
                            <div class="flex items-center">
                                <% for(let i = 0; i < 5; i++) { %>
                                    <i class="fas fa-star text-yellow-400 text-xs"></i>
                                <% } %>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>

    <!-- Improvement Tracking -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-chart-line text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Most Improved</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% 
                const improved = [
                    { name: 'Ravi Mehta', class: '4-B', improvement: '+15.2%', from: 72, to: 87 },
                    { name: 'Anita Joshi', class: '3-A', improvement: '+12.8%', from: 68, to: 81 },
                    { name: 'Vikram Singh', class: '5-A', improvement: '+11.5%', from: 75, to: 87 },
                    { name: 'Pooja Sharma', class: '6-A', improvement: '+10.3%', from: 82, to: 92 },
                    { name: 'Karan Patel', class: '4-A', improvement: '****%', from: 79, to: 89 }
                ];
                %>
                <% improved.forEach(student => { %>
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-up text-white text-xs"></i>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-gray-800"><%= student.name %></p>
                                <p class="text-xs text-gray-600">Class <%= student.class %></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-green-600"><%= student.improvement %></p>
                            <p class="text-xs text-gray-600"><%= student.from %>% → <%= student.to %>%</p>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Performance Reports initialized');
        
        // Auto-refresh every 5 minutes
        startAutoRefresh(300000);
    });

    function refreshPageData() {
        console.log('Refreshing performance data...');
    }
</script>
