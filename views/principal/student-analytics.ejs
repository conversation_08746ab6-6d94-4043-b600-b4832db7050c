<!-- Student Analytics Overview -->
<div class="mb-8">
    <!-- Student Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Students</p>
                    <p class="text-2xl font-bold text-gray-900"><%= studentStats.total_students || 0 %></p>
                    <p class="text-xs text-gray-500">Enrolled</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-user-check text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Students</p>
                    <p class="text-2xl font-bold text-gray-900"><%= studentStats.active_students || 0 %></p>
                    <p class="text-xs text-gray-500">Last 7 days</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-user-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Inactive Students</p>
                    <p class="text-2xl font-bold text-gray-900"><%= studentStats.inactive_students || 0 %></p>
                    <p class="text-xs text-gray-500">Need attention</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-percentage text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Engagement Rate</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= studentStats.total_students > 0 ? ((studentStats.active_students / studentStats.total_students) * 100).toFixed(1) : 0 %>%
                    </p>
                    <p class="text-xs text-gray-500">Weekly average</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Student Analytics Dashboard -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Enrollment Trends -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Enrollment Trends</h3>
            <p class="text-sm text-gray-500">Student enrollment over time</p>
        </div>
        <div class="p-6">
            <canvas id="enrollmentChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Class Distribution -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Class Distribution</h3>
            <p class="text-sm text-gray-500">Students by class and section</p>
        </div>
        <div class="p-6">
            <canvas id="classDistributionChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Performance Overview</h3>
            <p class="text-sm text-gray-500">Student performance metrics</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Excellent (90%+)</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="h-2 bg-green-500 rounded-full" style="width: 25%"></div>
                        </div>
                        <span class="text-sm text-gray-600">25%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Good (75-89%)</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="h-2 bg-blue-500 rounded-full" style="width: 40%"></div>
                        </div>
                        <span class="text-sm text-gray-600">40%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Average (60-74%)</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="h-2 bg-yellow-500 rounded-full" style="width: 25%"></div>
                        </div>
                        <span class="text-sm text-gray-600">25%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Below Average (<60%)</span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="h-2 bg-red-500 rounded-full" style="width: 10%"></div>
                        </div>
                        <span class="text-sm text-gray-600">10%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Overview -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Attendance Overview</h3>
            <p class="text-sm text-gray-500">Student attendance patterns</p>
        </div>
        <div class="p-6">
            <canvas id="attendanceChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Recent Student Activities -->
<div class="mt-8 bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Recent Student Activities</h2>
            <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-download mr-2"></i>
                Export Report
            </button>
        </div>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <!-- Sample activity items -->
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user-plus text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">New student enrollment</p>
                        <p class="text-xs text-gray-500">5 new students enrolled in Class 11</p>
                    </div>
                </div>
                <span class="text-xs text-gray-400">2 hours ago</span>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-check-circle text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">High attendance recorded</p>
                        <p class="text-xs text-gray-500">Class 12-A achieved 98% attendance today</p>
                    </div>
                </div>
                <span class="text-xs text-gray-400">4 hours ago</span>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-sm"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Low attendance alert</p>
                        <p class="text-xs text-gray-500">Class 11-B attendance below 75%</p>
                    </div>
                </div>
                <span class="text-xs text-gray-400">6 hours ago</span>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize charts
        initializeCharts();
    });

    function initializeCharts() {
        // Enrollment Trends Chart
        const enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');
        new Chart(enrollmentCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'New Enrollments',
                    data: [12, 19, 15, 25, 22, 30],
                    borderColor: '#991b1b',
                    backgroundColor: 'rgba(153, 27, 27, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Class Distribution Chart
        const classCtx = document.getElementById('classDistributionChart').getContext('2d');
        new Chart(classCtx, {
            type: 'doughnut',
            data: {
                labels: ['Class 11', 'Class 12'],
                datasets: [{
                    data: [60, 40],
                    backgroundColor: ['#991b1b', '#dc2626'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Attendance Chart
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(attendanceCtx, {
            type: 'bar',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                datasets: [{
                    label: 'Attendance %',
                    data: [85, 92, 88, 94, 87, 90],
                    backgroundColor: '#991b1b',
                    borderColor: '#7f1d1d',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    // Auto-refresh functionality
    function refreshPageData() {
        // Refresh student analytics data
        console.log('Refreshing student analytics data...');
    }

    // Start auto-refresh every 5 minutes
    startAutoRefresh(300000);
</script>
