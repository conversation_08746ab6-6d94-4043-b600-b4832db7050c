<!-- Strategic Calendar Command Center -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Strategic Calendar Command</h1>
                <p class="text-blue-100 text-lg">Real-time faculty scheduling and operational coordination</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-satellite mr-2"></i>
                    <span class="font-bold">LIVE MONITORING</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mission Control Dashboard -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Today's Operations -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-rocket text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Today's Missions</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">
                    <%= timetables.filter(t => new Date(t.date).toDateString() === new Date().toDateString()).length %>
                </p>
                <p class="text-sm text-principal-silver">Active Sessions</p>
            </div>
        </div>
    </div>

    <!-- Faculty Deployment -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-users-cog text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Faculty Deployed</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">
                    <%= [...new Set(timetables.map(t => t.teacher_name))].length %>
                </p>
                <p class="text-sm text-principal-silver">Active Educators</p>
            </div>
        </div>
    </div>

    <!-- Operational Status -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-shield-check text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Mission Status</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">
                    <%= Math.round((timetables.filter(t => t.status === 'delivered').length / timetables.length) * 100) || 0 %>%
                </p>
                <p class="text-sm text-principal-silver">Completion Rate</p>
            </div>
        </div>
    </div>

    <!-- Strategic Alerts -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-red-50 rounded-lg mr-3">
                        <i class="fas fa-exclamation-triangle text-red-500 text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-red-500 uppercase tracking-wide">Priority Alerts</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">
                    <%= timetables.filter(t => t.status === 'pending' && new Date(t.date) < new Date()).length %>
                </p>
                <p class="text-sm text-principal-silver">Overdue Missions</p>
            </div>
        </div>
    </div>
</div>

<!-- Strategic Operations Timeline -->
<div class="bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-dark to-principal-primary text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-calendar-alt text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Strategic Operations Timeline</h2>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium">REAL-TIME</span>
                </div>
                <button class="bg-principal-accent hover:bg-principal-gold text-principal-dark px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Intel
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <% if (timetables && timetables.length > 0) { %>
            <!-- Filter Controls -->
            <div class="mb-6 flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="search-operations" placeholder="Search operations, faculty, or subjects..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                </div>
                <div class="flex gap-2">
                    <select id="filter-status" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="">All Status</option>
                        <option value="delivered">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <select id="filter-date" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                        <option value="">All Dates</option>
                        <option value="today">Today</option>
                        <option value="tomorrow">Tomorrow</option>
                        <option value="week">This Week</option>
                    </select>
                </div>
            </div>

            <!-- Operations Timeline -->
            <div class="space-y-4 max-h-96 overflow-y-auto principal-scrollbar">
                <% timetables.forEach(operation => { %>
                    <% 
                        const operationDate = new Date(operation.date);
                        const isToday = operationDate.toDateString() === new Date().toDateString();
                        const isPast = operationDate < new Date() && !isToday;
                        const statusColor = operation.status === 'delivered' ? 'green' : operation.status === 'pending' ? 'yellow' : 'red';
                    %>
                    <div class="operation-item bg-gradient-to-r from-principal-light to-white border-l-4 <%= isToday ? 'border-principal-accent' : isPast ? 'border-red-500' : 'border-principal-primary' %> rounded-lg p-4 hover:shadow-md transition-all"
                         data-teacher="<%= operation.teacher_name.toLowerCase() %>"
                         data-subject="<%= operation.subject_name.toLowerCase() %>"
                         data-status="<%= operation.status %>"
                         data-date="<%= operation.date %>">
                        
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                <!-- Mission Icon -->
                                <div class="w-12 h-12 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center shadow-md">
                                    <i class="fas fa-<%= isToday ? 'bolt' : isPast ? 'history' : 'clock' %> text-white"></i>
                                </div>
                                
                                <!-- Mission Details -->
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="text-xs font-bold text-principal-primary uppercase tracking-wide mr-2">
                                            <%= isToday ? 'ACTIVE MISSION' : isPast ? 'COMPLETED MISSION' : 'SCHEDULED MISSION' %>
                                        </span>
                                        <div class="w-1 h-1 bg-principal-accent rounded-full"></div>
                                    </div>
                                    
                                    <h3 class="text-lg font-bold text-principal-dark mb-1"><%= operation.topic %></h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                        <div class="flex items-center">
                                            <i class="fas fa-user-tie text-principal-accent mr-2"></i>
                                            <span class="font-medium text-principal-dark"><%= operation.teacher_name %></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-book text-principal-secondary mr-2"></i>
                                            <span class="text-principal-silver"><%= operation.subject_name %></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-map-marker-alt text-principal-gold mr-2"></i>
                                            <span class="text-principal-silver"><%= operation.class_name %></span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center mt-2 text-sm">
                                        <i class="fas fa-calendar text-principal-silver mr-2"></i>
                                        <span class="text-principal-silver mr-4">
                                            <%= new Date(operation.date).toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' }) %>
                                        </span>
                                        <i class="fas fa-clock text-principal-silver mr-2"></i>
                                        <span class="text-principal-silver">
                                            <%= new Date(`2000-01-01T${operation.start_time}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }) %>
                                            - 
                                            <%= new Date(`2000-01-01T${operation.end_time}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }) %>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Mission Status -->
                            <div class="text-right">
                                <div class="mb-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold <%= operation.status === 'delivered' ? 'bg-green-100 text-green-800' : operation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %> shadow-sm">
                                        <i class="fas fa-<%= operation.status === 'delivered' ? 'check-circle' : operation.status === 'pending' ? 'clock' : 'times-circle' %> mr-1"></i>
                                        <%= operation.status.toUpperCase() %>
                                    </span>
                                </div>
                                <% if (isToday) { %>
                                    <div class="leadership-badge px-2 py-1 rounded-full text-xs">
                                        <i class="fas fa-satellite-dish mr-1"></i>
                                        PRIORITY
                                    </div>
                                <% } %>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-satellite-dish text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-principal-dark">No Operations Scheduled</h3>
                <p class="text-sm text-principal-silver">Strategic operations will appear here once scheduled.</p>
            </div>
        <% } %>
    </div>
</div>

<script>
    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-operations');
        const statusFilter = document.getElementById('filter-status');
        const dateFilter = document.getElementById('filter-date');
        const operationItems = document.querySelectorAll('.operation-item');

        function filterOperations() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            const dateValue = dateFilter.value;

            operationItems.forEach(item => {
                const teacher = item.dataset.teacher;
                const subject = item.dataset.subject;
                const status = item.dataset.status;
                const date = new Date(item.dataset.date);
                const today = new Date();
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);

                const matchesSearch = teacher.includes(searchTerm) || subject.includes(searchTerm);
                const matchesStatus = !statusValue || status === statusValue;
                
                let matchesDate = true;
                if (dateValue === 'today') {
                    matchesDate = date.toDateString() === today.toDateString();
                } else if (dateValue === 'tomorrow') {
                    matchesDate = date.toDateString() === tomorrow.toDateString();
                } else if (dateValue === 'week') {
                    const weekFromNow = new Date(today);
                    weekFromNow.setDate(weekFromNow.getDate() + 7);
                    matchesDate = date >= today && date <= weekFromNow;
                }

                item.style.display = matchesSearch && matchesStatus && matchesDate ? '' : 'none';
            });
        }

        if (searchInput) searchInput.addEventListener('input', filterOperations);
        if (statusFilter) statusFilter.addEventListener('change', filterOperations);
        if (dateFilter) dateFilter.addEventListener('change', filterOperations);
    });

    // Auto-refresh functionality
    function refreshPageData() {
        window.location.reload();
    }

    // Start auto-refresh every 2 minutes
    startAutoRefresh(120000);
</script>
