<!-- Change Password Page -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/profile" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            <%= __('common.back') %> <%= __('profile.myProfile') %>
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 max-w-md mx-auto">
        <form action="/profile/change-password" method="POST">
            <!-- Current Password -->
            <div class="mb-4">
                <label for="current_password" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.currentPassword') %></label>
                <div class="relative">
                    <input type="password" id="current_password" name="current_password" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    <span class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" title="Toggle password visibility" data-target="current_password">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                    </span>
                </div>
            </div>

            <!-- New Password -->
            <div class="mb-4">
                <label for="new_password" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.newPassword') %></label>
                <div class="relative">
                    <input type="password" id="new_password" name="new_password" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required minlength="8">
                    <span class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" title="Toggle password visibility" data-target="new_password">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                    </span>
                </div>
                <p class="text-sm text-gray-500 mt-1">Minimum 8 characters</p>
            </div>

            <!-- Confirm New Password -->
            <div class="mb-6">
                <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.confirmNewPassword') %></label>
                <div class="relative">
                    <input type="password" id="confirm_password" name="confirm_password" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required minlength="8">
                    <span class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer" title="Toggle password visibility" data-target="confirm_password">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-icon">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5 text-gray-400 eye-slash-icon hidden">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                        </svg>
                    </span>
                </div>
            </div>

            <!-- Password Requirements -->
            <div class="mb-6 bg-gray-50 p-4 rounded border border-gray-200">
                <h3 class="text-sm font-bold text-gray-700 mb-2">Password Requirements:</h3>
                <ul class="text-sm text-gray-600 list-disc pl-5 space-y-1">
                    <li id="min-length" class="requirement">Minimum 8 characters</li>
                    <li id="uppercase" class="requirement">At least one uppercase letter</li>
                    <li id="number" class="requirement">At least one number</li>
                    <li id="special-char" class="requirement">At least one special character</li>
                    <li id="match" class="requirement">Passwords must match</li>
                </ul>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" disabled>
                    <%= __('profile.changePassword') %>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Password validation script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');
        const submitButton = form.querySelector('button[type="submit"]');
        const requirements = {
            minLength: document.getElementById('min-length'),
            uppercase: document.getElementById('uppercase'),
            number: document.getElementById('number'),
            specialChar: document.getElementById('special-char'),
            match: document.getElementById('match')
        };

        // Initially disable the submit button
        submitButton.disabled = true;

        // Toggle password visibility
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordField = document.getElementById(targetId);
                const eyeIcon = this.querySelector('.eye-icon');
                const eyeSlashIcon = this.querySelector('.eye-slash-icon');

                if (passwordField) {
                    // Toggle password visibility
                    if (passwordField.type === 'password') {
                        passwordField.type = 'text';
                        if (eyeIcon) eyeIcon.classList.add('hidden');
                        if (eyeSlashIcon) eyeSlashIcon.classList.remove('hidden');
                    } else {
                        passwordField.type = 'password';
                        if (eyeIcon) eyeIcon.classList.remove('hidden');
                        if (eyeSlashIcon) eyeSlashIcon.classList.add('hidden');
                    }
                }
            });
        });

        form.addEventListener('input', function() {
            // Reset all requirements to default
            Object.values(requirements).forEach(req => {
                req.style.color = 'gray';
            });

            // Check password requirements only if current password is not being filled
            if (newPassword.value || confirmPassword.value) {
                const passwordValue = newPassword.value;
                const confirmValue = confirmPassword.value;

                // Minimum length
                if (passwordValue.length >= 8) {
                    requirements.minLength.style.color = 'green';
                } else {
                    requirements.minLength.style.color = 'red';
                }

                // At least one uppercase letter
                if (/[A-Z]/.test(passwordValue)) {
                    requirements.uppercase.style.color = 'green';
                } else {
                    requirements.uppercase.style.color = 'red';
                }

                // At least one number
                if (/[0-9]/.test(passwordValue)) {
                    requirements.number.style.color = 'green';
                } else {
                    requirements.number.style.color = 'red';
                }

                // At least one special character
                if (/[\W_]/.test(passwordValue)) {
                    requirements.specialChar.style.color = 'green';
                } else {
                    requirements.specialChar.style.color = 'red';
                }

                // Passwords must match
                if (passwordValue && confirmValue) {
                    if (passwordValue === confirmValue) {
                        requirements.match.style.color = 'green';
                    } else {
                        requirements.match.style.color = 'red';
                    }
                }

                // Enable or disable the submit button based on validation
                const allValid = Object.values(requirements).every(req => req.style.color === 'green');
                submitButton.disabled = !allValid;
            }
        });

        form.addEventListener('submit', function(event) {
            // Final check before submission
            if (newPassword.value !== confirmPassword.value) {
                event.preventDefault();
                alert('New passwords do not match!');
                return;
            }
        });
    });
</script>