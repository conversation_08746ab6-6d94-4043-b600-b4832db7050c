<!-- Edit Profile Page -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
        <a href="/profile" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
            <%= __('common.back') %> <%= __('profile.myProfile') %>
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <!-- Profile Image Section -->
        <div class="mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 class="text-lg font-semibold mb-4">Profile Image</h3>

            <div class="flex items-center mb-4">
                <% if (user.profile_image) { %>
                    <img src="<%= user.profile_image %>" alt="Profile" class="w-20 h-20 rounded-full object-cover mr-4" id="current-profile-image">
                <% } else { %>
                    <div class="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center mr-4" id="initials-avatar">
                        <%
                            let initials = 'U';
                            if (user.username) {
                                initials = user.username.substring(0, 2).toUpperCase();
                            } else if (user.name) {
                                initials = user.name.substring(0, 2).toUpperCase();
                            }
                        %>
                        <span class="text-xl font-bold text-white"><%= initials %></span>
                    </div>
                <% } %>
                <div class="ml-4">
                    <p class="text-sm text-gray-600">Current profile image</p>
                </div>
            </div>

            <!-- Super Simple Image Upload Form with Multer -->
            <div class="bg-yellow-100 p-4 mb-4 rounded-lg">
                <h3 class="font-bold mb-2">Upload Profile Image</h3>
                <form action="/profile/upload-image" method="POST" enctype="multipart/form-data" id="image-upload-form">
                    <div class="mb-3">
                        <label for="profile_image" class="block font-medium mb-1">Select image file:</label>
                        <input type="file" name="profile_image" id="profile_image" class="border p-2 w-full" accept="image/jpeg,image/png,image/gif">
                        <p class="text-xs text-gray-500 mt-1">Accepted formats: JPG, PNG, GIF. Max size: 5MB</p>
                    </div>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded" id="upload-button">Upload Image</button>
                </form>
            </div>

            <script>
                // Add form submission handler for the image upload form
                document.addEventListener('DOMContentLoaded', function() {
                    const imageForm = document.getElementById('image-upload-form');
                    const uploadButton = document.getElementById('upload-button');
                    const fileInput = document.getElementById('profile_image');

                    if (imageForm && uploadButton && fileInput) {
                        imageForm.addEventListener('submit', function(e) {
                            // Check if a file is selected
                            if (!fileInput.files || fileInput.files.length === 0) {
                                e.preventDefault();
                                alert('Please select an image file first');
                                return false;
                            }

                            // Disable the button to prevent multiple clicks
                            uploadButton.disabled = true;
                            uploadButton.classList.add('opacity-50', 'cursor-not-allowed');
                            uploadButton.innerHTML = 'Uploading...';

                            // Allow the form to submit
                            return true;
                        });
                    }
                });
            </script>

            <!-- Clear Image Link -->
            <% if (user.profile_image) { %>
                <div class="bg-gray-100 p-4 mb-4 rounded-lg">
                    <h3 class="font-bold mb-2">Current Image</h3>
                    <img src="<%= user.profile_image %>" alt="Profile" class="w-32 h-32 rounded-full object-cover mb-3">
                    <a href="/profile/clear-image" class="bg-red-600 text-white px-4 py-2 rounded inline-block">Clear Image</a>
                </div>
            <% } %>
        </div>

        <!-- Separate Profile Info Form -->
        <form action="/profile/update" method="POST" id="profile-update-form">
            <!-- Hidden fields for image handling -->
            <input type="hidden" name="image_changed" id="image_changed" value="false">
            <input type="hidden" name="image_action" id="image_action" value="">

            <!-- Profile Information Section -->

            <!-- Name -->
            <div class="mb-4">
                <label for="name" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.name') %></label>
                <input type="text" id="name" name="name" value="<%= user.name || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
            </div>

            <!-- Email -->
            <div class="mb-4">
                <label for="email" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.email') %></label>
                <input type="email" id="email" name="email" value="<%= user.email %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
            </div>

            <!-- Bio -->
            <div class="mb-6">
                <label for="bio" class="block text-gray-700 text-sm font-bold mb-2"><%= __('profile.bio') %> (Optional)</label>
                <textarea id="bio" name="bio" rows="4" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><%= user.bio || '' %></textarea>
            </div>

            <!-- Education Details Section -->
            <div class="mb-6 border-t border-gray-200 pt-6">
                <h3 class="text-lg font-semibold mb-4">Education Details</h3>

                <!-- Current Institution -->
                <div class="mb-4">
                    <label for="institution" class="block text-gray-700 text-sm font-bold mb-2">Current Institution (Optional)</label>
                    <input type="text" id="institution" name="institution" value="<%= user.institution || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>

                <!-- Grade/Year -->
                <div class="mb-4">
                    <label for="grade" class="block text-gray-700 text-sm font-bold mb-2">Grade/Year (Optional)</label>
                    <input type="text" id="grade" name="grade" value="<%= user.grade || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>

                <!-- Field of Study -->
                <div class="mb-4">
                    <label for="field_of_study" class="block text-gray-700 text-sm font-bold mb-2">Field of Study (Optional)</label>
                    <input type="text" id="field_of_study" name="field_of_study" value="<%= user.field_of_study || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
            </div>

            <!-- Exam Preferences Section -->
            <div class="mb-6 border-t border-gray-200 pt-6">
                <h3 class="text-lg font-semibold mb-4">Exam Preferences</h3>

                <!-- Preferred Subjects -->
                <div class="mb-4">
                    <label for="preferred_subjects" class="block text-gray-700 text-sm font-bold mb-2">Preferred Subjects (Optional)</label>
                    <input type="text" id="preferred_subjects" name="preferred_subjects" value="<%= user.preferred_subjects || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="e.g. Mathematics, Science, English">
                </div>

                <!-- Target Exams -->
                <div class="mb-4">
                    <label for="target_exams" class="block text-gray-700 text-sm font-bold mb-2">Target Exams (Optional)</label>
                    <input type="text" id="target_exams" name="target_exams" value="<%= user.target_exams || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="e.g. SAT, JEE, NEET">
                </div>

                <!-- Study Goal -->
                <div class="mb-4">
                    <label for="study_goal" class="block text-gray-700 text-sm font-bold mb-2">Study Goal (Optional)</label>
                    <input type="text" id="study_goal" name="study_goal" value="<%= user.study_goal || '' %>" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="e.g. Score 90% in final exams">
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="mb-6 border-t border-gray-200 pt-6">
                <h3 class="text-lg font-semibold mb-4">Additional Information</h3>

                <!-- Language Preference -->
                <div class="mb-4">
                    <label for="language_preference" class="block text-gray-700 text-sm font-bold mb-2">Language Preference (Optional)</label>
                    <select id="language_preference" name="language_preference" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="English" <%= user.language_preference === 'English' ? 'selected' : '' %>>English</option>
                        <option value="Hindi" <%= user.language_preference === 'Hindi' ? 'selected' : '' %>>Hindi</option>
                        <option value="Punjabi" <%= user.language_preference === 'Punjabi' ? 'selected' : '' %>>Punjabi</option>
                    </select>
                </div>

                <!-- Time Zone -->
                <div class="mb-4">
                    <label for="time_zone" class="block text-gray-700 text-sm font-bold mb-2">Time Zone (Optional)</label>
                    <select id="time_zone" name="time_zone" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="" <%= !user.time_zone ? 'selected' : '' %>>Select Time Zone</option>
                        <option value="Asia/Kolkata" <%= user.time_zone === 'Asia/Kolkata' ? 'selected' : '' %>>India (GMT+5:30)</option>
                        <option value="America/New_York" <%= user.time_zone === 'America/New_York' ? 'selected' : '' %>>Eastern Time (GMT-5)</option>
                        <option value="Europe/London" <%= user.time_zone === 'Europe/London' ? 'selected' : '' %>>London (GMT+0)</option>
                    </select>
                </div>

                <!-- Accessibility Needs -->
                <div class="mb-4">
                    <label for="accessibility_needs" class="block text-gray-700 text-sm font-bold mb-2">Accessibility Needs (Optional)</label>
                    <select id="accessibility_needs" name="accessibility_needs" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="None" <%= !user.accessibility_needs || user.accessibility_needs === 'None' ? 'selected' : '' %>>None</option>
                        <option value="Screen Reader" <%= user.accessibility_needs === 'Screen Reader' ? 'selected' : '' %>>Screen Reader</option>
                        <option value="High Contrast" <%= user.accessibility_needs === 'High Contrast' ? 'selected' : '' %>>High Contrast</option>
                        <option value="Large Text" <%= user.accessibility_needs === 'Large Text' ? 'selected' : '' %>>Large Text</option>
                    </select>
                </div>
            </div>

            <!-- Submit Button for Profile Info -->
            <div class="flex justify-end">
                <button type="submit" id="profile-submit-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    <%= __('profile.saveChanges') %>
                </button>
            </div>

            <!-- Hidden field to prevent duplicate submissions -->
            <input type="hidden" name="form_timestamp" value="<%= Date.now() %>">
        </form>
    </div>
</div>

<!-- Enhanced form submission script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle profile info form submission to prevent duplicates
        const infoForm = document.getElementById('profile-update-form');
        const infoSubmitBtn = document.getElementById('profile-submit-btn');
        const imageChangedField = document.getElementById('image_changed');
        const imageActionField = document.getElementById('image_action');

        // Handle image upload form
        const imageForm = document.getElementById('image-upload-form');
        const uploadButton = document.getElementById('upload-button');
        const fileInput = document.getElementById('profile_image');

        // Set image fields when file is selected
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files && fileInput.files.length > 0) {
                    // Set hidden fields for the main form
                    imageChangedField.value = 'true';
                    imageActionField.value = 'upload';
                }
            });
        }

        // Handle clear image link
        const clearImageLink = document.querySelector('a[href="/profile/clear-image"]');
        if (clearImageLink) {
            clearImageLink.addEventListener('click', function(e) {
                e.preventDefault();

                // Set hidden fields
                imageChangedField.value = 'true';
                imageActionField.value = 'clear';

                // Submit the main form instead
                infoForm.submit();
            });
        }

        if (infoForm && infoSubmitBtn) {
            infoForm.addEventListener('submit', function(e) {
                // Disable the submit button to prevent multiple clicks
                infoSubmitBtn.disabled = true;
                infoSubmitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                infoSubmitBtn.innerHTML = 'Saving...';

                // Allow the form to submit
                return true;
            });
        }

        if (imageForm && uploadButton && fileInput) {
            imageForm.addEventListener('submit', function(e) {
                // Prevent the default form submission
                e.preventDefault();

                // Check if a file is selected
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('Please select an image file first');
                    return false;
                }

                // Use the dedicated image upload endpoint instead
                uploadButton.disabled = true;
                uploadButton.classList.add('opacity-50', 'cursor-not-allowed');
                uploadButton.innerHTML = 'Uploading...';

                // Submit the image form directly to its own endpoint
                const formData = new FormData(imageForm);

                fetch('/profile/upload-image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else {
                        return response.text();
                    }
                })
                .then(html => {
                    if (html) {
                        // If we got HTML back instead of a redirect, reload the page
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error uploading image. Please try again.');
                    uploadButton.disabled = false;
                    uploadButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    uploadButton.innerHTML = 'Upload Image';
                });

                return false;
            });
        }
    });
</script>