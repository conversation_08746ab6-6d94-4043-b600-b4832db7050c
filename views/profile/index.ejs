<!-- Profile Page Content -->
<div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold"><%= pageTitle %></h1>
            <div class="flex space-x-2">
                <a href="/profile/edit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    <%= __('profile.editProfile') %>
                </a>
                <a href="/profile/change-password" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                    <%= __('profile.changePassword') %>
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Profile Information -->
            <div class="bg-white shadow-md rounded-lg p-6">
                <div class="flex flex-col items-center mb-4">
                    <% if (user.profile_image) { %>
                        <!-- Debug info for profile image path -->
                        <div class="text-xs text-gray-500 mb-2">Image path: <%= user.profile_image %></div>

                        <div class="relative cursor-pointer" id="profile-image-container">
                            <img src="<%= user.profile_image %>" alt="Profile" class="w-32 h-32 rounded-full object-cover mb-4" id="profile-image" onerror="this.onerror=null; this.src='/images/default-avatar.png'; console.error('Failed to load image:', this.src);">
                            <div class="absolute inset-0 bg-black bg-opacity-20 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                                <span class="text-white text-xs font-medium px-2 py-1 bg-black bg-opacity-50 rounded">Click to enlarge</span>
                            </div>
                        </div>
                    <% } else { %>
                        <div class="w-32 h-32 rounded-full bg-blue-500 flex items-center justify-center mb-4">
                            <%
                                let initials = 'U';
                                if (user.username) {
                                    initials = user.username.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                                } else if (user.name) {
                                    initials = user.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                                }
                            %>
                            <span class="text-3xl font-bold text-white"><%= initials %></span>
                        </div>
                    <% } %>
                    <h2 class="text-xl font-semibold"><%= user.name || user.username %></h2>
                    <p class="text-gray-500"><%= user.email %></p>
                    <% if (user.role === 'admin') { %>
                        <span class="mt-2 px-3 py-1 text-xs rounded-full bg-purple-100 text-purple-800">Admin</span>
                    <% } %>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h3 class="text-lg font-semibold mb-2"><%= __('profile.about') %></h3>
                    <p class="text-gray-700"><%= user.bio || __('profile.noBioProvided') %></p>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h3 class="text-lg font-semibold mb-2"><%= __('profile.accountInfo') %></h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-500"><%= __('profile.username') %>:</span>
                            <span class="font-medium"><%= user.username %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500"><%= __('profile.memberSince') %>:</span>
                            <span class="font-medium"><%= formatDate(user.created_at) %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500"><%= __('profile.lastLogin') %>:</span>
                            <span class="font-medium"><%= user.last_login ? formatDateTime(user.last_login) : 'Never' %></span>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h3 class="text-lg font-semibold mb-2">Education Details</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Current Institution:</span>
                            <span class="font-medium"><%= user.institution || 'Not specified' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Grade/Year:</span>
                            <span class="font-medium"><%= user.grade || 'Not specified' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Field of Study:</span>
                            <span class="font-medium"><%= user.field_of_study || 'Not specified' %></span>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h3 class="text-lg font-semibold mb-2">Exam Preferences</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Preferred Subjects:</span>
                            <span class="font-medium"><%= user.preferred_subjects || 'Not specified' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Target Exams:</span>
                            <span class="font-medium"><%= user.target_exams || 'Not specified' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Study Goal:</span>
                            <span class="font-medium"><%= user.study_goal || 'Not specified' %></span>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4 mt-4">
                    <h3 class="text-lg font-semibold mb-2">Additional Information</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Language Preference:</span>
                            <span class="font-medium"><%= user.language_preference || 'English' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Time Zone:</span>
                            <span class="font-medium"><%= user.time_zone || 'Not specified' %></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Accessibility Needs:</span>
                            <span class="font-medium"><%= user.accessibility_needs || 'None' %></span>
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    <!-- Modal Backdrop -->
    <div id="modal-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

    <!-- Image Overlay Modal -->
    <div id="image-overlay" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center p-4">
        <div class="relative max-w-4xl w-full">
            <button id="close-image-overlay" class="absolute top-0 right-0 -mt-12 -mr-12 bg-white rounded-full p-2 text-gray-800 hover:text-gray-600 focus:outline-none">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img id="enlarged-image" src="" alt="Enlarged profile picture" class="max-h-[80vh] max-w-full mx-auto object-contain">
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div id="edit-profile-modal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                <h3 class="text-xl font-semibold"><%= __('profile.editProfile') %></h3>
                <button class="modal-close text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="/profile/update" method="POST" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700"><%= __('profile.username') %></label>
                        <input type="text" id="username" name="username" value="<%= user.username %>" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700"><%= __('profile.email') %></label>
                        <input type="email" id="email" name="email" value="<%= user.email %>" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700"><%= __('profile.dateOfBirth') %></label>
                        <input type="date" id="date_of_birth" name="date_of_birth" value="<%= user.date_of_birth.toISOString().split('T')[0] %>" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="button" class="modal-close bg-gray-200 text-gray-800 py-2 px-4 rounded-md mr-2 hover:bg-gray-300 transition"><%= __('common.cancel') %></button>
                    <button type="submit" class="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition"><%= __('profile.saveChanges') %></button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload Image Modal -->
    <div id="upload-image-modal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                <h3 class="text-xl font-semibold"><%= __('profile.uploadImage') %></h3>
                <button class="modal-close text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="/profile/upload-image" method="POST" enctype="multipart/form-data" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label for="profile_image" class="block text-sm font-medium text-gray-700">Select Image</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="profile_image" class="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-purple-500">
                                        <span>Upload a file</span>
                                        <input id="profile_image" name="profile_image" type="file" class="sr-only" accept="image/*" required>
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="button" class="modal-close bg-gray-200 text-gray-800 py-2 px-4 rounded-md mr-2 hover:bg-gray-300 transition">Cancel</button>
                    <button type="submit" class="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition">Upload</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div id="change-password-modal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                <h3 class="text-xl font-semibold">Change Password</h3>
                <button class="modal-close text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="/profile/change-password" method="POST" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
                        <input type="password" id="current_password" name="current_password" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="new_password" class="block text-sm font-medium text-gray-700">New Password</label>
                        <input type="password" id="new_password" name="new_password" required minlength="8" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="8" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="button" class="modal-close bg-gray-200 text-gray-800 py-2 px-4 rounded-md mr-2 hover:bg-gray-300 transition">Cancel</button>
                    <button type="submit" class="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition">Change Password</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div id="delete-account-modal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="bg-red-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                <h3 class="text-xl font-semibold">Delete Account</h3>
                <button class="modal-close text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form action="/profile/delete-account" method="POST" class="p-6">
                <div class="space-y-4">
                    <div class="bg-red-50 border-l-4 border-red-500 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    This action cannot be undone. This will permanently delete your account and remove your data from our servers.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="confirm_delete" class="block text-sm font-medium text-gray-700">To confirm, type "DELETE" in the box below</label>
                        <input type="text" id="confirm_delete" name="confirm_delete" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500">
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button type="button" class="modal-close bg-gray-200 text-gray-800 py-2 px-4 rounded-md mr-2 hover:bg-gray-300 transition">Cancel</button>
                    <button type="submit" class="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition">Delete Account</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Modal elements
            const modalBackdrop = document.getElementById('modal-backdrop');
            const editProfileModal = document.getElementById('edit-profile-modal');
            const changePasswordModal = document.getElementById('change-password-modal');
            const deleteAccountModal = document.getElementById('delete-account-modal');
            const imageOverlay = document.getElementById('image-overlay');
            const enlargedImage = document.getElementById('enlarged-image');
            const profileImageContainer = document.getElementById('profile-image-container');
            const profileImage = document.getElementById('profile-image');
            const closeImageOverlay = document.getElementById('close-image-overlay');

            // Image enlargement functionality
            if (profileImageContainer && profileImage && imageOverlay && enlargedImage) {
                profileImageContainer.addEventListener('click', function() {
                    // Set the source of the enlarged image
                    enlargedImage.src = profileImage.src;

                    // Show the overlay
                    imageOverlay.classList.remove('hidden');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });

                // Close image overlay when clicking the close button
                if (closeImageOverlay) {
                    closeImageOverlay.addEventListener('click', function() {
                        imageOverlay.classList.add('hidden');
                        document.body.style.overflow = ''; // Restore scrolling
                    });
                }

                // Close image overlay when clicking outside the image
                imageOverlay.addEventListener('click', function(e) {
                    if (e.target === imageOverlay) {
                        imageOverlay.classList.add('hidden');
                        document.body.style.overflow = ''; // Restore scrolling
                    }
                });

                // Close image overlay when pressing Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !imageOverlay.classList.contains('hidden')) {
                        imageOverlay.classList.add('hidden');
                        document.body.style.overflow = ''; // Restore scrolling
                    }
                });
            }

            // Check if modal elements exist before adding event listeners
            if (editProfileModal) {
                const editProfileButton = document.querySelector('.edit-profile-button');
                if (editProfileButton) {
                    editProfileButton.addEventListener('click', function() {
                        modalBackdrop.classList.remove('hidden');
                        editProfileModal.classList.remove('hidden');
                    });
                }
            }
            if (changePasswordModal) {
                const changePasswordButton = document.querySelector('.change-password-button');
                if (changePasswordButton) {
                    changePasswordButton.addEventListener('click', function() {
                        modalBackdrop.classList.remove('hidden');
                        changePasswordModal.classList.remove('hidden');
                    });
                }
            }
            if (deleteAccountModal) {
                const deleteAccountButton = document.querySelector('.delete-account-button');
                if (deleteAccountButton) {
                    deleteAccountButton.addEventListener('click', function() {
                        modalBackdrop.classList.remove('hidden');
                        deleteAccountModal.classList.remove('hidden');
                    });
                }
            }

            // Close modals
            const closeModalButtons = document.querySelectorAll('.modal-close');
            closeModalButtons.forEach(button => {
                button.addEventListener('click', function() {
                    modalBackdrop.classList.add('hidden');
                    editProfileModal.classList.add('hidden');
                    changePasswordModal.classList.add('hidden');
                    deleteAccountModal.classList.add('hidden');
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('form').on('submit', function(e) {
                e.preventDefault();
                const formData = $(this).serialize();
                const action = $(this).attr('action');
                const method = $(this).attr('method');

                $.ajax({
                    url: action,
                    type: method,
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            alert('Operation successful');
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.message);
                    }
                });
            });
        });
    </script>
<!-- End of Profile Page Content -->