<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3b82f6;
        }
        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 5px;
        }
        .report-date {
            font-size: 14px;
            color: #666;
        }
        .report-summary {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .summary-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1f2937;
        }
        .summary-item {
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
            display: inline-block;
            min-width: 200px;
        }
        .report-data {
            margin-bottom: 30px;
        }
        .data-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1f2937;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #f3f4f6;
            text-align: left;
            padding: 10px;
            font-weight: bold;
            border-bottom: 2px solid #d1d5db;
        }
        td {
            padding: 8px 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .report-footer {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #e5e7eb;
        }
        .chart-container {
            margin: 20px 0;
            text-align: center;
        }
        .chart-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .bar-chart {
            height: 200px;
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            margin: 0 auto;
            max-width: 600px;
        }
        .bar {
            width: 40px;
            background-color: #3b82f6;
            margin: 0 5px;
            position: relative;
        }
        .bar-label {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
        }
        .bar-value {
            position: absolute;
            top: -20px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
        }
        @media print {
            body {
                padding: 0;
            }
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <div class="report-header">
        <div class="report-title"><%= title %></div>
        <div class="report-date">Generated on: <%= generatedAt %></div>
    </div>

    <div class="report-summary">
        <div class="summary-title">Summary</div>
        <% if (report.summary) { %>
            <% Object.entries(report.summary).forEach(([key, value]) => { %>
                <% if (typeof value !== 'object') { %>
                    <div class="summary-item">
                        <span class="summary-label"><%= key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %>:</span>
                        <span><%= value %></span>
                    </div>
                <% } %>
            <% }); %>
        <% } %>
    </div>

    <% if (report.summary && report.summary.weakestCategories) { %>
        <div class="chart-container">
            <div class="chart-title">Category Performance</div>
            <div class="bar-chart">
                <% report.summary.weakestCategories.forEach(category => { %>
                    <div class="bar" style="height: <%= category.correctPercentage %>%;">
                        <div class="bar-value"><%= category.correctPercentage %>%</div>
                        <div class="bar-label"><%= category.name.substring(0, 10) %></div>
                    </div>
                <% }); %>
                <% report.summary.strongestCategories.forEach(category => { %>
                    <div class="bar" style="height: <%= category.correctPercentage %>%;">
                        <div class="bar-value"><%= category.correctPercentage %>%</div>
                        <div class="bar-label"><%= category.name.substring(0, 10) %></div>
                    </div>
                <% }); %>
            </div>
        </div>
    <% } %>

    <div class="report-data">
        <div class="data-title">Detailed Data</div>
        <% if (report.data && report.data.length > 0) { %>
            <table>
                <thead>
                    <tr>
                        <% Object.keys(report.data[0]).forEach(key => { %>
                            <th><%= key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) %></th>
                        <% }); %>
                    </tr>
                </thead>
                <tbody>
                    <% report.data.forEach(row => { %>
                        <tr>
                            <% Object.values(row).forEach(value => { %>
                                <td>
                                    <% if (value === null || value === undefined) { %>
                                        -
                                    <% } else if (typeof value === 'object') { %>
                                        <%= JSON.stringify(value) %>
                                    <% } else { %>
                                        <%= value %>
                                    <% } %>
                                </td>
                            <% }); %>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        <% } else { %>
            <p>No data available for this report.</p>
        <% } %>
    </div>

    <div class="report-footer">
        <p>This report was generated by the Exam Prep Platform. For internal use only.</p>
    </div>
</body>
</html>
