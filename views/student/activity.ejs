<!-- Student Activity History -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-blue-600 text-white p-4">
    <h2 class="text-xl font-semibold">Activity History</h2>
  </div>
  <div class="p-6">
    <% if (activity && activity.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity Type</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% activity.forEach(item => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDateTime(item.created_at) %></td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    <% if (item.type === 'test_completed') { %>
                      bg-green-100 text-green-800
                    <% } else if (item.type === 'test_assigned') { %>
                      bg-blue-100 text-blue-800
                    <% } else if (item.type === 'plan_completed') { %>
                      bg-yellow-100 text-yellow-800
                    <% } %>">
                    <%= item.type.replace('_', ' ').toUpperCase() %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <% if (item.type === 'test_completed' || item.type === 'test_assigned') { %>
                    <%= item.exam_name %>
                  <% } else if (item.type === 'plan_completed') { %>
                    <%= item.plan_name %>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if (item.type === 'test_completed' && item.score) { %>
                    <%= item.score %>%
                  <% } else { %>
                    -
                  <% } %>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-4">
        <p class="text-gray-500">No activity found.</p>
      </div>
    <% } %>
  </div>
</div>
