<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">My Assignments</h2>
    </div>

    <div class="p-6">
      <!-- Tabs for different assignment states -->
      <div class="mb-6 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
          <li class="mr-2">
            <a href="#" class="inline-block p-4 border-b-2 border-blue-600 rounded-t-lg active text-blue-600" id="pending-tab" data-tab="pending">
              Pending
            </a>
          </li>
          <li class="mr-2">
            <a href="#" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="submitted-tab" data-tab="submitted">
              Submitted
            </a>
          </li>
          <li class="mr-2">
            <a href="#" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="graded-tab" data-tab="graded">
              Graded
            </a>
          </li>
          <li>
            <a href="#" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="all-tab" data-tab="all">
              All Assignments
            </a>
          </li>
        </ul>
      </div>

      <!-- Pending Assignments Tab Content -->
      <div id="pending-content" class="tab-content">
        <% if (pendingAssignments && pendingAssignments.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
              <thead class="bg-gray-100">
                <tr>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Title</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Subject</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Due Date</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Teacher</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <% pendingAssignments.forEach(assignment => { %>
                  <tr>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.title %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.subject_name || 'N/A' %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= formatDateTime(assignment.due_date) %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.teacher_name || 'N/A' %></td>
                    <td class="py-3 px-4 text-sm">
                      <a href="/student/assignments/<%= assignment.id %>" class="text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded">
                        View Details
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You have no pending assignments at this time.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <!-- Submitted Assignments Tab Content -->
      <div id="submitted-content" class="tab-content hidden">
        <% if (submittedAssignments && submittedAssignments.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
              <thead class="bg-gray-100">
                <tr>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Title</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Subject</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Submission Date</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <% submittedAssignments.forEach(assignment => { %>
                  <tr>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.title %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.subject_name || 'N/A' %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= formatDateTime(assignment.submission_date) %></td>
                    <td class="py-3 px-4 text-sm">
                      <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                        Submitted
                      </span>
                    </td>
                    <td class="py-3 px-4 text-sm">
                      <a href="/student/assignments/<%= assignment.id %>" class="text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded">
                        View Submission
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You have no submitted assignments waiting for grading.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <!-- Graded Assignments Tab Content -->
      <div id="graded-content" class="tab-content hidden">
        <% if (gradedAssignments && gradedAssignments.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
              <thead class="bg-gray-100">
                <tr>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Title</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Subject</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Graded On</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Score</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <% gradedAssignments.forEach(assignment => { %>
                  <tr>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.title %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.subject_name || 'N/A' %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= formatDateTime(assignment.graded_at) %></td>
                    <td class="py-3 px-4 text-sm text-gray-500">
                      <%= assignment.marks_obtained || 0 %> / <%= assignment.total_marks || 10 %>
                    </td>
                    <td class="py-3 px-4 text-sm">
                      <a href="/student/assignments/<%= assignment.id %>" class="text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded">
                        View Feedback
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You have no graded assignments.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <!-- All Assignments Tab Content -->
      <div id="all-content" class="tab-content hidden">
        <%
          const allAssignments = [
            ...(pendingAssignments || []),
            ...(submittedAssignments || []),
            ...(gradedAssignments || [])
          ];
        %>
        <% if (allAssignments && allAssignments.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
              <thead class="bg-gray-100">
                <tr>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Title</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Subject</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Due Date</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                  <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <% allAssignments.forEach(assignment => {
                  let status = 'Pending';
                  let statusClass = 'bg-blue-100 text-blue-800';

                  if (assignment.status === 'submitted') {
                    status = 'Submitted';
                    statusClass = 'bg-yellow-100 text-yellow-800';
                  } else if (assignment.status === 'graded') {
                    status = 'Graded';
                    statusClass = 'bg-green-100 text-green-800';
                  }
                %>
                  <tr>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.title %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= assignment.subject_name || 'N/A' %></td>
                    <td class="py-3 px-4 text-sm text-gray-500"><%= formatDateTime(assignment.due_date) %></td>
                    <td class="py-3 px-4 text-sm">
                      <span class="px-2 py-1 <%= statusClass %> rounded-full text-xs">
                        <%= status %>
                      </span>
                    </td>
                    <td class="py-3 px-4 text-sm">
                      <a href="/student/assignments/<%= assignment.id %>" class="text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded">
                        View Details
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You have no assignments yet.
                </p>
              </div>
            </div>
          </div>
        <% } %>

        <!-- Academic Plan Assessments Section -->
        <% if (assessments && assessments.length > 0) { %>
          <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">Upcoming Assessments</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                  <tr>
                    <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Assessment Type</th>
                    <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Subject</th>
                    <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Date</th>
                    <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <% assessments.forEach(assessment => { %>
                    <tr>
                      <td class="py-3 px-4 text-sm text-gray-500"><%= assessment.assessment_type %></td>
                      <td class="py-3 px-4 text-sm text-gray-500"><%= assessment.subject_name || 'N/A' %></td>
                      <td class="py-3 px-4 text-sm text-gray-500"><%= formatDateTime(assessment.assessment_date) %></td>
                      <td class="py-3 px-4 text-sm">
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          <%= assessment.status || 'Planned' %>
                        </span>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabs = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
      tab.addEventListener('click', function(e) {
        e.preventDefault();

        // Remove active class from all tabs
        tabs.forEach(t => {
          t.classList.remove('active', 'text-blue-600', 'border-blue-600');
          t.classList.add('hover:text-gray-600', 'hover:border-gray-300', 'border-transparent');
        });

        // Add active class to clicked tab
        this.classList.add('active', 'text-blue-600', 'border-blue-600');
        this.classList.remove('hover:text-gray-600', 'hover:border-gray-300', 'border-transparent');

        // Hide all tab contents
        tabContents.forEach(content => {
          content.classList.add('hidden');
        });

        // Show the selected tab content
        const tabId = this.getAttribute('data-tab');
        document.getElementById(`${tabId}-content`).classList.remove('hidden');
      });
    });
  });
</script>

<%- include('../../partials/student/footer') %>
