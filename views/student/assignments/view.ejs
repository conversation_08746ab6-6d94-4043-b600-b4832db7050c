<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Assignment Details</h2>
      <a href="/student/assignments" class="bg-white text-blue-600 px-3 py-1 rounded text-sm hover:bg-gray-100 transition">
        Back to Assignments
      </a>
    </div>

    <div class="p-6">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-2"><%= assignment.title %></h3>
        <div class="flex items-center mb-4">
          <span class="text-gray-500">
            Subject: <span class="font-medium"><%= assignment.subject_name || 'N/A' %></span>
          </span>
          <span class="mx-2">•</span>
          <span class="text-gray-500">
            Teacher: <span class="font-medium"><%= assignment.teacher_name || 'N/A' %></span>
          </span>
          <span class="mx-2">•</span>
          <span class="text-gray-500">
            Due: <span class="font-medium"><%= formatDateTime(assignment.due_date) %></span>
          </span>
        </div>

        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Description</h4>
          <p class="text-gray-700"><%= assignment.description || 'No description provided' %></p>
        </div>

        <% if (hasSubmission) { %>
          <div class="mt-8 border-t border-gray-200 pt-6">
            <h4 class="text-lg font-semibold mb-4">Your Submission</h4>

            <div class="bg-gray-50 p-4 rounded-lg mb-4">
              <div class="mb-4">
                <p class="text-sm text-gray-500 mb-1">Submitted on</p>
                <p class="font-medium"><%= formatDateTime(submission.submission_date) %></p>
              </div>

              <% if (submission.submission_text) { %>
                <div class="mb-4">
                  <p class="text-sm text-gray-500 mb-1">Submission Text</p>
                  <div class="bg-white p-3 rounded border border-gray-200">
                    <%= submission.submission_text %>
                  </div>
                </div>
              <% } %>

              <% if (submission.file_path) { %>
                <div>
                  <p class="text-sm text-gray-500 mb-1">Attached File</p>
                  <a href="<%= submission.file_path %>" class="text-blue-600 hover:text-blue-800 flex items-center" target="_blank">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                    </svg>
                    View Attachment
                  </a>
                </div>
              <% } %>
            </div>

            <% if (submission.status === 'graded') { %>
              <div class="mt-6 bg-green-50 p-4 rounded-lg">
                <h5 class="font-semibold text-green-800 mb-2">Feedback</h5>

                <div class="mb-3">
                  <p class="text-sm text-gray-500 mb-1">Score</p>
                  <p class="font-medium text-green-700">
                    <%= submission.marks_obtained || 0 %> / <%= assignment.total_marks || 10 %>
                  </p>
                </div>

                <% if (submission.feedback) { %>
                  <div>
                    <p class="text-sm text-gray-500 mb-1">Teacher Comments</p>
                    <p class="text-gray-700"><%= submission.feedback %></p>
                  </div>
                <% } %>
              </div>
            <% } %>

            <% if (submission.status !== 'graded' && new Date(assignment.due_date) > new Date()) { %>
              <div class="mt-6">
                <button id="edit-submission-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition">
                  Edit Submission
                </button>
              </div>
            <% } %>
          </div>
        <% } else if (new Date(assignment.due_date) > new Date()) { %>
          <div class="mt-8 border-t border-gray-200 pt-6">
            <h4 class="text-lg font-semibold mb-4">Submit Assignment</h4>

            <form id="submission-form" action="/student/assignments/<%= assignment.id %>/submit" method="POST" enctype="multipart/form-data">
              <div class="mb-4">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Submission Text</label>
                <textarea id="content" name="content" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
              </div>

              <div class="mb-6">
                <label for="file" class="block text-sm font-medium text-gray-700 mb-1">Attachment (Optional)</label>
                <input type="file" id="file" name="file" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">Accepted file types: PDF, DOC, DOCX, JPG, PNG</p>
              </div>

              <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">
                Submit Assignment
              </button>
            </form>
          </div>
        <% } else { %>
          <div class="mt-8 border-t border-gray-200 pt-6">
            <div class="bg-red-50 border-l-4 border-red-400 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-700">
                    The due date for this assignment has passed. You can no longer submit.
                  </p>
                </div>
              </div>
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const editSubmissionBtn = document.getElementById('edit-submission-btn');

    if (editSubmissionBtn) {
      editSubmissionBtn.addEventListener('click', function() {
        // Show submission form
        const submissionForm = document.createElement('form');
        submissionForm.id = 'submission-form';
        submissionForm.action = '/student/assignments/<%= assignment.id %>/submit';
        submissionForm.method = 'POST';
        submissionForm.enctype = 'multipart/form-data';

        submissionForm.innerHTML = `
          <div class="mb-4">
            <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Submission Text</label>
            <textarea id="content" name="content" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><%= submission ? submission.submission_text : '' %></textarea>
          </div>

          <div class="mb-6">
            <label for="file" class="block text-sm font-medium text-gray-700 mb-1">Attachment (Optional)</label>
            <input type="file" id="file" name="file" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <p class="text-xs text-gray-500 mt-1">Accepted file types: PDF, DOC, DOCX, JPG, PNG</p>
            <% if (submission && submission.file_path) { %>
              <p class="text-xs text-gray-700 mt-2">Current file: <a href="<%= submission.file_path %>" class="text-blue-600 hover:text-blue-800" target="_blank">View</a></p>
            <% } %>
          </div>

          <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">
            Update Submission
          </button>
        `;

        // Replace the current submission view with the form
        const submissionView = editSubmissionBtn.parentElement.parentElement;
        submissionView.innerHTML = '';
        submissionView.appendChild(submissionForm);
      });
    }
  });
</script>

<%- include('../../partials/student/footer') %>
