<%- include('../partials/student/header') %>
<!-- Include the enhanced calendar today highlight script -->
<script src="/js/calendar-today-highlight.js"></script>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Academic Calendar</h2>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <button id="prev-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h3 id="current-month" class="text-xl font-semibold mx-4"></h3>
          <button id="next-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
        <button id="today-btn" class="px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition">
          Today
        </button>
      </div>

      <div class="overflow-x-auto">
        <div class="calendar-grid grid grid-cols-7 gap-1">
          <!-- Calendar header -->
          <div class="text-center font-semibold py-2 bg-gray-100">Sun</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Mon</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Tue</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Wed</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Thu</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Fri</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Sat</div>

          <!-- Calendar days will be inserted here by JavaScript -->
          <div id="calendar-days" class="contents"></div>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 flex flex-wrap gap-4">
        <!-- Test Assignments -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-indigo-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Test Assignment</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Test Due Soon</span>
        </div>

        <!-- Assignments -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-orange-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Assignment</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-pink-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Assessment</span>
        </div>

        <!-- Practicals -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Upcoming Practical</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Submitted Practical</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-yellow-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Missed Practical</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-purple-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Graded Practical</span>
        </div>

        <!-- Learning Plans -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-teal-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Learning Plan</span>
        </div>

        <!-- Holidays -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">National Holiday</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-amber-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Festival</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Public Holiday</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 id="modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="modal-content" class="space-y-4">
      <!-- Content will be populated by JavaScript -->
    </div>
    <div class="mt-6 flex justify-end">
      <a id="view-details-btn" href="#" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        View Details
      </a>
    </div>
  </div>
</div>

<!-- Include Day Timetable Modal -->
<%- include('./day-timetable-modal') %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');
    const eventModal = document.getElementById('event-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const viewDetailsBtn = document.getElementById('view-details-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Data from server
    const practicals = <%- JSON.stringify(practicals || []) %>;
    const testAssignments = <%- JSON.stringify(testAssignments || []) %>;
    const assignments = <%- JSON.stringify(assignments || []) %>;
    const assessments = <%- JSON.stringify(assessments || []) %>;
    const learningPlans = <%- JSON.stringify(learningPlans || []) %>;
    const holidays = <%- JSON.stringify(holidays || []) %>;

    // Get user's class information
    let userClasses = [];
    let lectures = [];

    // Fetch user class data and initialize calendar
    fetchUserClassData();

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderCalendar(currentMonth, currentYear);
    });

    todayBtn.addEventListener('click', () => {
      const today = new Date();
      currentMonth = today.getMonth();
      currentYear = today.getFullYear();
      renderCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      eventModal.classList.add('hidden');
    });

    // Render calendar
    function renderCalendar(month, year) {
      // Clear previous calendar
      calendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthEl.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        calendarDays.appendChild(dayEl);
      }

      // Current month's days
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        const dayEl = createDayElement(i, isToday ? 'bg-blue-50 font-bold' : '', month, year);
        calendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        calendarDays.appendChild(dayEl);
      }
    }

    // Create day element
    function createDayElement(day, extraClasses, month, year) {
      // Format date string for comparison
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // Create events container function
      const createEventsContainer = (day, month, year) => {
        // Add events container
        const eventsContainer = document.createElement('div');
        eventsContainer.className = 'mt-1 space-y-1';

        // Add test assignments for this day
        const dayTestAssignments = testAssignments.filter(t => {
          if (!t.end_datetime) return false;
          return t.end_datetime.substring(0, 10) === dateStr;
        });

        if (dayTestAssignments.length > 0) {
          dayTestAssignments.forEach(test => {
            const testEl = document.createElement('div');

            // Determine color based on due date proximity
            const dueDate = new Date(test.end_datetime);
            const now = new Date();
            const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

            let bgColor = 'bg-indigo-100';
            if (daysUntilDue <= 2) {
              bgColor = 'bg-red-100';
            }

            testEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
            testEl.textContent = `Test: ${test.exam_name}`;

            // Add click event to show modal
            testEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showTestModal(test);
            });

            eventsContainer.appendChild(testEl);
          });
        }

        // Add assignments for this day
        const dayAssignments = assignments.filter(a => {
          if (!a.due_date) return false;
          return a.due_date.substring(0, 10) === dateStr;
        });

        if (dayAssignments.length > 0) {
          dayAssignments.forEach(assignment => {
            const assignmentEl = document.createElement('div');

            // Use orange color for assignments
            const bgColor = 'bg-orange-100';

            assignmentEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
            assignmentEl.textContent = `Assignment: ${assignment.title || 'Assignment'}`;

            // Add click event to show modal
            assignmentEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showAssignmentModal(assignment);
            });

            eventsContainer.appendChild(assignmentEl);
          });
        }

        // Add assessments for this day
        const dayAssessments = assessments.filter(a => {
          if (!a.assessment_date) return false;
          return a.assessment_date.substring(0, 10) === dateStr;
        });

        if (dayAssessments.length > 0) {
          dayAssessments.forEach(assessment => {
            const assessmentEl = document.createElement('div');

            // Use pink color for assessments
            const bgColor = 'bg-pink-100';

            assessmentEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
            assessmentEl.textContent = `Assessment: ${assessment.assessment_type || 'Assessment'}`;

            // Add click event to show modal
            assessmentEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showAssessmentModal(assessment);
            });

            eventsContainer.appendChild(assessmentEl);
          });
        }

        // Add practicals for this day
        const dayPracticals = practicals.filter(p => p.date === dateStr);

        if (dayPracticals.length > 0) {
          dayPracticals.forEach(practical => {
            const practicalEl = document.createElement('div');

            // Determine color based on status
            let bgColor = 'bg-blue-100';
            if (practical.status === 'cancelled') {
              bgColor = 'bg-red-100';
            } else if (practical.has_submission) {
              if (practical.submission_status === 'graded') {
                bgColor = 'bg-purple-100';
              } else {
                bgColor = 'bg-green-100';
              }
            } else if (new Date(practical.date) < new Date()) {
              bgColor = 'bg-yellow-100';
            }

            practicalEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
            practicalEl.textContent = `Lab: ${practical.practical_topic || 'Practical'}`;

            // Add click event to show modal
            practicalEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showPracticalModal(practical);
            });

            eventsContainer.appendChild(practicalEl);
          });
        }

        // Add learning plans for this day
        // Use created_at date for learning plans
        const dayLearningPlans = learningPlans.filter(lp => {
          if (!lp.created_at) return false;
          return lp.created_at.substring(0, 10) === dateStr;
        });

        if (dayLearningPlans.length > 0) {
          dayLearningPlans.forEach(plan => {
            const planEl = document.createElement('div');

            // Use teal color for learning plans
            const bgColor = 'bg-teal-100';

            planEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
            planEl.textContent = `Plan: ${plan.title || 'Learning Plan'}`;

            // Add click event to show modal
            planEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showLearningPlanModal(plan);
            });

            eventsContainer.appendChild(planEl);
          });
        }

        // Add holidays for this day
        const dayHolidays = holidays.filter(h => {
          if (!h.holiday_date) return false;

          // Fix for timezone issue - create a date object from the holiday date
          // and compare the year, month, and day directly
          const holidayDate = new Date(h.holiday_date);
          return holidayDate.getFullYear() === year &&
                 holidayDate.getMonth() === month &&
                 holidayDate.getDate() === day;
        });

        if (dayHolidays.length > 0) {
          dayHolidays.forEach(holiday => {
            const holidayEl = document.createElement('div');

            // Determine color based on holiday type
            let bgColor = 'bg-blue-200'; // Default for Public Holiday
            if (holiday.holiday_type === 'National Holiday') {
              bgColor = 'bg-red-200';
            } else if (holiday.holiday_type === 'Festival') {
              bgColor = 'bg-amber-200';
            }

            holidayEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer font-semibold`;
            holidayEl.textContent = holiday.description;

            // Add click event to show modal
            holidayEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showHolidayModal(holiday);
            });

            eventsContainer.appendChild(holidayEl);
          });
        }

        // Add event counts badge
        if (dayTestAssignments.length > 0 || dayAssignments.length > 0 ||
            dayAssessments.length > 0 || dayPracticals.length > 0 ||
            dayLearningPlans.length > 0 || dayHolidays.length > 0) {

          // Create a count badge
          const countBadge = document.createElement('div');
          countBadge.className = 'absolute top-0 right-0 mt-1 mr-1 flex items-center space-x-1';

          // Add test count if any
          if (dayTestAssignments.length > 0) {
            const testBadge = document.createElement('span');
            testBadge.className = 'bg-indigo-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            testBadge.title = `${dayTestAssignments.length} test${dayTestAssignments.length > 1 ? 's' : ''}`;
            testBadge.textContent = dayTestAssignments.length;
            countBadge.appendChild(testBadge);
          }

          // Add assignment count if any
          if (dayAssignments.length > 0) {
            const assignmentBadge = document.createElement('span');
            assignmentBadge.className = 'bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            assignmentBadge.title = `${dayAssignments.length} assignment${dayAssignments.length > 1 ? 's' : ''}`;
            assignmentBadge.textContent = dayAssignments.length;
            countBadge.appendChild(assignmentBadge);
          }

          // Add lecture/practical count if any
          if (dayPracticals.length > 0) {
            const practicalBadge = document.createElement('span');
            practicalBadge.className = 'bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            practicalBadge.title = `${dayPracticals.length} practical${dayPracticals.length > 1 ? 's' : ''}`;
            practicalBadge.textContent = dayPracticals.length;
            countBadge.appendChild(practicalBadge);
          }

          // Add the badge to the events container
          eventsContainer.appendChild(countBadge);

          return eventsContainer;
        }

        return null;
      };

      // Use the enhanced day element function
      const dayEl = createEnhancedDayElement(day, extraClasses, month, year, createEventsContainer);

      // Add click event to show day timetable
      dayEl.addEventListener('click', () => {
        showDayTimetable(dateStr);
      });

      // Add cursor pointer to indicate clickable
      dayEl.classList.add('cursor-pointer');

      return dayEl;
    }

    // Show test assignment modal
    function showTestModal(test) {
      modalTitle.textContent = test.exam_name;

      // Format date using the formatDateTime function
      const formattedDate = formatDateTime(test.end_datetime);

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Due Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Attempts</p>
          <p class="font-medium">${test.attempts_used || 0} of ${test.max_attempts} used</p>
        </div>
      `;

      // Set view details link
      viewDetailsBtn.href = `/tests/take/${test.exam_id}`;
      viewDetailsBtn.style.display = 'block';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Show assignment modal
    function showAssignmentModal(assignment) {
      modalTitle.textContent = assignment.title || 'Assignment';

      // Format date using the formatDateTime function
      const formattedDate = formatDateTime(assignment.due_date);

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Due Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Subject</p>
          <p class="font-medium">${assignment.subject_name || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Description</p>
          <p class="font-medium">${assignment.description || 'No description available'}</p>
        </div>
      `;

      // Set view details link - adjust this path based on your application structure
      viewDetailsBtn.href = `/student/assignments/${assignment.id}`;
      viewDetailsBtn.style.display = 'block';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Show assessment modal
    function showAssessmentModal(assessment) {
      modalTitle.textContent = assessment.assessment_type || 'Assessment';

      // Format date using the formatDateTime function
      const formattedDate = formatDateTime(assessment.assessment_date);

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Subject</p>
          <p class="font-medium">${assessment.subject_name || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Plan</p>
          <p class="font-medium">${assessment.plan_title || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Topics Covered</p>
          <p class="font-medium">${assessment.topics_covered || 'Not specified'}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Status</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold bg-pink-100 text-pink-800">
            ${assessment.status || 'Planned'}
          </span>
        </div>
      `;

      // Set view details link - adjust this path based on your application structure
      viewDetailsBtn.href = `/student/instruction-plans/${assessment.plan_id}`;
      viewDetailsBtn.style.display = 'block';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Show practical modal
    function showPracticalModal(practical) {
      modalTitle.textContent = practical.practical_topic || 'Practical';

      // Format date using the formatDateTime function
      const formattedDate = formatDateTime(practical.date + 'T' + practical.start_time);

      // Determine status text and color
      let statusText = 'Upcoming';
      let statusColor = 'bg-blue-100 text-blue-800';

      if (practical.status === 'cancelled') {
        statusText = 'Cancelled';
        statusColor = 'bg-red-100 text-red-800';
      } else if (practical.has_submission) {
        if (practical.submission_status === 'graded') {
          statusText = 'Graded';
          statusColor = 'bg-purple-100 text-purple-800';
        } else {
          statusText = 'Submitted';
          statusColor = 'bg-green-100 text-green-800';
        }
      } else if (new Date(practical.date) < new Date()) {
        statusText = 'Not Submitted';
        statusColor = 'bg-yellow-100 text-yellow-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date & Time</p>
          <p class="font-medium">${formattedDate}</p>
          <p class="font-medium">${practical.start_time ? practical.start_time.substring(0, 5) : ''} - ${practical.end_time ? practical.end_time.substring(0, 5) : ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Subject</p>
          <p class="font-medium">${practical.subject_name || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Venue</p>
          <p class="font-medium">${practical.venue || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Status</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusColor}">
            ${statusText}
          </span>
        </div>
      `;

      // Set view details link
      viewDetailsBtn.href = `/student/practicals/${practical.id}`;
      viewDetailsBtn.style.display = 'block';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Show learning plan modal
    function showLearningPlanModal(plan) {
      modalTitle.textContent = plan.title || 'Learning Plan';

      // Format date
      const planDate = new Date(plan.created_at);
      const formattedDate = planDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Published Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Subject</p>
          <p class="font-medium">${plan.subject_name || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Teacher</p>
          <p class="font-medium">${plan.teacher_name || ''}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Description</p>
          <p class="font-medium">${plan.description || 'No description available'}</p>
        </div>
      `;

      // Set view details link
      viewDetailsBtn.href = `/student/instruction-plans/${plan.id}`;
      viewDetailsBtn.style.display = 'block';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Show holiday modal
    function showHolidayModal(holiday) {
      modalTitle.textContent = holiday.description;

      // Format date
      const holidayDate = new Date(holiday.holiday_date);
      const formattedDate = holidayDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Determine holiday type badge color
      let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
      if (holiday.holiday_type === 'National Holiday') {
        badgeColor = 'bg-red-100 text-red-800';
      } else if (holiday.holiday_type === 'Festival') {
        badgeColor = 'bg-amber-100 text-amber-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Type</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
            ${holiday.holiday_type}
          </span>
        </div>
      `;

      // Hide view details button for holidays
      viewDetailsBtn.style.display = 'none';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Day timetable modal elements
    const dayTimetableModal = document.getElementById('day-timetable-modal');
    const closeDayModalBtn = document.getElementById('close-day-modal');
    const dayModalTitle = document.getElementById('day-modal-title');
    const dayLoading = document.getElementById('day-loading');
    const dayHolidaysSection = document.getElementById('day-holidays-section');
    const dayHolidaysContent = document.getElementById('day-holidays-content');
    const dayLecturesSection = document.getElementById('day-lectures-section');
    const dayLecturesContent = document.getElementById('day-lectures-content');
    const dayTestsSection = document.getElementById('day-tests-section');
    const dayTestsContent = document.getElementById('day-tests-content');
    const dayPracticalsSection = document.getElementById('day-practicals-section');
    const dayPracticalsContent = document.getElementById('day-practicals-content');
    const dayAssignmentsSection = document.getElementById('day-assignments-section');
    const dayAssignmentsContent = document.getElementById('day-assignments-content');
    const dayNoEvents = document.getElementById('day-no-events');

    // Close day timetable modal
    closeDayModalBtn.addEventListener('click', () => {
      dayTimetableModal.classList.add('hidden');
    });

    // Format time for display (HH:MM)
    function formatTime(timeStr) {
      if (!timeStr) return '';

      // If it's already in HH:MM format, return as is
      if (timeStr.length === 5) return timeStr;

      // If it's in HH:MM:SS format, remove seconds
      if (timeStr.length === 8) return timeStr.substring(0, 5);

      return timeStr;
    }

    // Show day timetable
    async function showDayTimetable(date) {
      // Reset modal content
      resetDayTimetableModal();

      // Format date for display
      const displayDate = new Date(date);
      const formattedDate = displayDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Set modal title
      dayModalTitle.textContent = `Timetable for ${formattedDate}`;

      // Show modal with loading state
      dayTimetableModal.classList.remove('hidden');
      dayLoading.classList.remove('hidden');

      try {
        // Fetch timetable data for this date
        const response = await fetch(`/api/calendar/day-timetable?date=${date}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch timetable data: ${response.status}`);
        }

        const data = await response.json();

        // Hide loading indicator
        dayLoading.classList.add('hidden');

        // Populate modal with data
        populateDayTimetableModal(data);

      } catch (error) {
        console.error('Error fetching day timetable:', error);

        // Hide loading indicator
        dayLoading.classList.add('hidden');

        // Show error message
        dayNoEvents.classList.remove('hidden');
        dayNoEvents.innerHTML = `
          <p class="text-red-500">Error loading timetable data. Please try again.</p>
          <p class="text-gray-500 text-sm mt-2">${error.message}</p>
        `;
      }
    }

    // Reset day timetable modal
    function resetDayTimetableModal() {
      // Hide all sections
      dayHolidaysSection.classList.add('hidden');
      dayLecturesSection.classList.add('hidden');
      dayTestsSection.classList.add('hidden');
      dayPracticalsSection.classList.add('hidden');
      dayAssignmentsSection.classList.add('hidden');
      dayNoEvents.classList.add('hidden');

      // Clear content
      dayHolidaysContent.innerHTML = '';
      dayLecturesContent.innerHTML = '';
      dayTestsContent.innerHTML = '';
      dayPracticalsContent.innerHTML = '';
      dayAssignmentsContent.innerHTML = '';
    }

    // Format date and time for display
    function formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return 'N/A';

      const date = new Date(dateTimeStr);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      });
    }

    // Fetch user class data and lectures
    async function fetchUserClassData() {
      try {
        // Fetch user's classes
        const classesResponse = await fetch('/api/student/classes');
        if (classesResponse.ok) {
          const classesData = await classesResponse.json();
          if (classesData.success) {
            userClasses = classesData.classes || [];

            // Fetch lectures for user's classes
            const lecturesResponse = await fetch('/api/student/lectures');
            if (lecturesResponse.ok) {
              const lecturesData = await lecturesResponse.json();
              if (lecturesData.success) {
                lectures = lecturesData.lectures || [];
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user class data:', error);
      } finally {
        // Render calendar with whatever data we have
        renderCalendar(currentMonth, currentYear);
      }
    }

    // Populate day timetable modal with data
    function populateDayTimetableModal(data) {
      let hasEvents = false;

      // Populate holidays
      if (data.holidays && data.holidays.length > 0) {
        hasEvents = true;
        dayHolidaysSection.classList.remove('hidden');

        const holidaysHtml = data.holidays.map(holiday => {
          let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
          if (holiday.holiday_type === 'National Holiday') {
            badgeColor = 'bg-red-100 text-red-800';
          } else if (holiday.holiday_type === 'Festival') {
            badgeColor = 'bg-amber-100 text-amber-800';
          }

          return `
            <div class="mb-2 last:mb-0">
              <div class="font-medium">${holiday.description}</div>
              <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
                ${holiday.holiday_type}
              </span>
            </div>
          `;
        }).join('');

        dayHolidaysContent.innerHTML = holidaysHtml;
      }

      // Populate lectures
      if (data.lectures && data.lectures.length > 0) {
        hasEvents = true;
        dayLecturesSection.classList.remove('hidden');

        const lecturesHtml = data.lectures.map(lecture => {
          // Determine status badge
          let statusBadge = 'bg-blue-100 text-blue-800'; // Default for pending
          let statusText = lecture.status || 'Pending';

          if (statusText.toLowerCase() === 'completed') {
            statusBadge = 'bg-green-100 text-green-800';
          } else if (statusText.toLowerCase() === 'cancelled') {
            statusBadge = 'bg-red-100 text-red-800';
          }

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b">${formatTime(lecture.start_time)} - ${formatTime(lecture.end_time)}</td>
              <td class="py-2 px-4 border-b">
                <div class="font-medium">${lecture.subject_name || 'N/A'}</div>
                <div class="text-xs text-gray-500">${lecture.topic || 'No topic'}</div>
              </td>
              <td class="py-2 px-4 border-b">${lecture.teacher_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${lecture.location || 'N/A'}</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusBadge}">
                  ${statusText}
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayLecturesContent.innerHTML = lecturesHtml;
      }

      // Populate tests
      if (data.tests && data.tests.length > 0) {
        hasEvents = true;
        dayTestsSection.classList.remove('hidden');

        const testsHtml = data.tests.map(test => {
          // Format date and time
          const dueDate = new Date(test.end_datetime);
          const formattedDueDate = dueDate.toLocaleString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
          });

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b font-medium">${test.exam_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${formattedDueDate}</td>
              <td class="py-2 px-4 border-b">${test.total_questions || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${test.duration || 'N/A'} min</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full bg-indigo-100 text-indigo-800 text-xs font-semibold">
                  ${test.attempts_used || 0} of ${test.max_attempts || 'N/A'}
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayTestsContent.innerHTML = testsHtml;
      }

      // Populate practicals
      if (data.practicals && data.practicals.length > 0) {
        hasEvents = true;
        dayPracticalsSection.classList.remove('hidden');

        const practicalsHtml = data.practicals.map(practical => {
          // Determine status badge
          let statusBadge = 'bg-blue-100 text-blue-800'; // Default for pending
          let statusText = practical.status || 'Pending';

          if (statusText.toLowerCase() === 'completed') {
            statusBadge = 'bg-green-100 text-green-800';
          } else if (statusText.toLowerCase() === 'cancelled') {
            statusBadge = 'bg-red-100 text-red-800';
          }

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b">${formatTime(practical.start_time)} - ${formatTime(practical.end_time)}</td>
              <td class="py-2 px-4 border-b font-medium">${practical.practical_topic || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${practical.subject_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${practical.teacher_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusBadge}">
                  ${statusText}
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayPracticalsContent.innerHTML = practicalsHtml;
      }

      // Populate assignments
      if (data.assignments && data.assignments.length > 0) {
        hasEvents = true;
        dayAssignmentsSection.classList.remove('hidden');

        const assignmentsHtml = data.assignments.map(assignment => {
          // Format date and time
          const dueDate = new Date(assignment.due_date);
          const formattedDueDate = dueDate.toLocaleString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
          });

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b font-medium">${assignment.title || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${formattedDueDate}</td>
              <td class="py-2 px-4 border-b">${assignment.subject_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${assignment.teacher_name || 'N/A'}</td>
            </tr>
          `;
        }).join('');

        dayAssignmentsContent.innerHTML = assignmentsHtml;
      }

      // Show no events message if no events
      if (!hasEvents) {
        dayNoEvents.classList.remove('hidden');
      }
    }
  });
</script>

<%- include('../partials/student/footer') %>
