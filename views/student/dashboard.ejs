<!-- Student Dashboard -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
  <!-- Assigned Tests Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-blue-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Assigned Tests</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats?.assignedTests || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Completed Tests Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-green-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Completed Tests</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats?.completedTests || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Average Score Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-purple-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Average Score</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats?.averageScore || '0%' %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Learning Plans Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-yellow-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Learning Plans</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats?.learningPlans || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Upcoming Practicals Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-indigo-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Lab Practicals</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats?.upcomingPracticals || 0 %></h3>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Upcoming Tests -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden lg:col-span-2">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Upcoming Tests</h2>
    </div>
    <div class="p-6">
      <% if (upcomingTests && upcomingTests.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attempts</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% upcomingTests.forEach(test => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= test.exam_name %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= test.subject || 'General' %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= formatDateTime(test.end_date) %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= test.attempts_used %>/<%= test.max_attempts %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="/student/exams/<%= test.id %>" class="text-blue-600 hover:text-blue-900">Take Test</a>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
        <div class="mt-4 text-right">
          <a href="/student/exams" class="text-blue-600 hover:text-blue-900 font-medium">View All Tests</a>
        </div>
      <% } else { %>
        <div class="text-center py-4">
          <p class="text-gray-500">No upcoming tests found.</p>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Mini Calendar -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Academic Calendar</h2>
    </div>
    <div class="p-4">
      <div class="flex justify-between items-center mb-2">
        <button id="prev-month-btn" class="text-gray-600 hover:text-gray-800">
          <i class="fas fa-chevron-left"></i>
        </button>
        <h3 id="current-month-display" class="text-md font-medium"></h3>
        <button id="next-month-btn" class="text-gray-600 hover:text-gray-800">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>

      <!-- Calendar grid -->
      <div class="grid grid-cols-7 gap-1 text-center text-xs">
        <div class="font-medium text-gray-500">Su</div>
        <div class="font-medium text-gray-500">Mo</div>
        <div class="font-medium text-gray-500">Tu</div>
        <div class="font-medium text-gray-500">We</div>
        <div class="font-medium text-gray-500">Th</div>
        <div class="font-medium text-gray-500">Fr</div>
        <div class="font-medium text-gray-500">Sa</div>
      </div>

      <div id="mini-calendar-days" class="grid grid-cols-7 gap-1 mt-1"></div>

      <!-- Legend -->
      <div class="mt-3 flex flex-wrap gap-2 text-xs">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-indigo-200 rounded-full mr-1"></div>
          <span>Tests</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-200 rounded-full mr-1"></div>
          <span>Practicals</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-orange-200 rounded-full mr-1"></div>
          <span>Assignments</span>
        </div>
      </div>

      <div class="mt-2 text-right">
        <a href="/student/calendar" class="text-blue-600 hover:text-blue-800 text-sm">View Full Calendar</a>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Recent Activity</h2>
    </div>
    <div class="p-6">
      <% if (recentActivity && recentActivity.length > 0) { %>
        <div class="space-y-4">
          <% recentActivity.forEach(activity => { %>
            <div class="flex items-start">
              <% if (activity.type === 'test_completed') { %>
                <div class="bg-green-100 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-600">You completed <span class="font-medium text-gray-900"><%= activity.exam_name %></span> with a score of <span class="font-medium text-gray-900"><%= activity.score %>%</span></p>
                  <p class="text-xs text-gray-500 mt-1"><%= formatDateTime(activity.created_at) %></p>
                </div>
              <% } else if (activity.type === 'test_assigned') { %>
                <div class="bg-blue-100 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-600">New test assigned: <span class="font-medium text-gray-900"><%= activity.exam_name %></span></p>
                  <p class="text-xs text-gray-500 mt-1"><%= formatDateTime(activity.created_at) %></p>
                </div>
              <% } else if (activity.type === 'plan_completed') { %>
                <div class="bg-yellow-100 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-600">Completed learning plan: <span class="font-medium text-gray-900"><%= activity.plan_name %></span></p>
                  <p class="text-xs text-gray-500 mt-1"><%= formatDateTime(activity.created_at) %></p>
                </div>
              <% } %>
            </div>
          <% }); %>
        </div>
        <div class="mt-4 text-right">
          <a href="/student/activity" class="text-blue-600 hover:text-blue-900 font-medium">View All Activity</a>
        </div>
      <% } else { %>
        <div class="text-center py-4">
          <p class="text-gray-500">No recent activity found.</p>
        </div>
      <% } %>
    </div>
  </div>
</div>

<!-- Upcoming Practicals -->
<div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-indigo-600 text-white p-4">
    <h2 class="text-xl font-semibold">Upcoming Lab Practicals</h2>
  </div>
  <div class="p-6">
    <% if (upcomingPracticals && upcomingPracticals.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% upcomingPracticals.forEach(practical => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    <%= formatDateTime(new Date(practical.date + 'T' + practical.start_time)) %>
                  </div>
                  <div class="text-sm text-gray-500">
                    <%= practical.start_time.substring(0, 5) %> - <%= practical.end_time.substring(0, 5) %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= practical.subject_name %></div>
                  <div class="text-sm text-gray-500"><%= practical.class_name %></div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900"><%= practical.practical_topic %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= practical.venue %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (practical.has_submission > 0) { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Submitted
                    </span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/student/practicals/<%= practical.id %>" class="text-blue-600 hover:text-blue-900">View Details</a>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
      <div class="mt-4 text-right">
        <a href="/student/practicals" class="text-indigo-600 hover:text-indigo-900 font-medium">View All Practicals</a>
      </div>
    <% } else { %>
      <div class="text-center py-4">
        <p class="text-gray-500">No upcoming lab practicals found.</p>
      </div>
    <% } %>
  </div>
</div>

<!-- Class Schedule -->
<div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-blue-600 text-white p-4">
    <h2 class="text-xl font-semibold">Today's Class Schedule</h2>
  </div>
  <div class="p-6">
    <% if (todaySchedule && todaySchedule.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% todaySchedule.forEach(schedule => { %>
              <tr class="<%= new Date().toTimeString().slice(0, 5) >= schedule.start_time && new Date().toTimeString().slice(0, 5) <= schedule.end_time ? 'bg-blue-50' : '' %>">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= schedule.start_time %> - <%= schedule.end_time %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= schedule.subject %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= schedule.teacher_name %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= schedule.room %></td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    <%= schedule.type === 'theory' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' %>">
                    <%= schedule.type.toUpperCase() %>
                  </span>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-4">
        <p class="text-gray-500">No classes scheduled for today.</p>
      </div>
    <% } %>
  </div>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-4 max-w-md w-full">
    <div class="flex justify-between items-center mb-3">
      <h3 id="modal-date" class="text-lg font-semibold text-gray-800"></h3>
      <button id="close-modal-btn" class="text-gray-400 hover:text-gray-500">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div id="modal-events" class="space-y-3 max-h-60 overflow-y-auto"></div>
    <div class="mt-4 text-right">
      <a href="/student/calendar" class="text-blue-600 hover:text-blue-800 text-sm">View Full Calendar</a>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Calendar data from server
    const calendarEvents = <%- JSON.stringify(calendarEvents || []) %>;

    // DOM elements
    const miniCalendarDays = document.getElementById('mini-calendar-days');
    const currentMonthDisplay = document.getElementById('current-month-display');
    const prevMonthBtn = document.getElementById('prev-month-btn');
    const nextMonthBtn = document.getElementById('next-month-btn');
    const eventModal = document.getElementById('event-modal');
    const modalDate = document.getElementById('modal-date');
    const modalEvents = document.getElementById('modal-events');
    const closeModalBtn = document.getElementById('close-modal-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Initialize calendar
    renderMiniCalendar(currentMonth, currentYear);

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderMiniCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderMiniCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      eventModal.classList.add('hidden');
    });

    // Render mini calendar
    function renderMiniCalendar(month, year) {
      // Clear previous calendar
      miniCalendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthDisplay.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createMiniDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        miniCalendarDays.appendChild(dayEl);
      }

      // Current month's days
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        const dayEl = createMiniDayElement(i, isToday ? 'bg-blue-100 font-bold' : '', month, year);
        miniCalendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createMiniDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        miniCalendarDays.appendChild(dayEl);
      }
    }

    // Create mini day element
    function createMiniDayElement(day, extraClasses, month, year) {
      const dayEl = document.createElement('div');
      dayEl.className = `h-7 flex flex-col items-center justify-center text-xs relative ${extraClasses}`;

      // Add day number
      const dayNumber = document.createElement('span');
      dayNumber.textContent = day;
      dayEl.appendChild(dayNumber);

      // Format date string for comparison
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // Get events for this day
      const dayEvents = calendarEvents.filter(event => event.date.substring(0, 10) === dateStr);

      // Add event indicators if there are events
      if (dayEvents.length > 0) {
        // Create event indicators container
        const indicators = document.createElement('div');
        indicators.className = 'flex gap-0.5 absolute -bottom-1';

        // Group events by type
        const eventTypes = {};
        dayEvents.forEach(event => {
          if (!eventTypes[event.type]) {
            eventTypes[event.type] = true;
          }
        });

        // Add indicator for each event type
        Object.keys(eventTypes).forEach(type => {
          const indicator = document.createElement('div');
          let bgColor = 'bg-gray-200';

          switch (type) {
            case 'test':
              bgColor = 'bg-indigo-200';
              break;
            case 'practical':
              bgColor = 'bg-blue-200';
              break;
            case 'assignment':
              bgColor = 'bg-orange-200';
              break;
          }

          indicator.className = `w-1.5 h-1.5 rounded-full ${bgColor}`;
          indicators.appendChild(indicator);
        });

        dayEl.appendChild(indicators);

        // Make the day clickable to show events
        dayEl.classList.add('cursor-pointer', 'hover:bg-gray-100');
        dayEl.addEventListener('click', () => showDayEvents(dateStr, dayEvents));
      }

      return dayEl;
    }

    // Show events for a specific day
    function showDayEvents(dateStr, events) {
      // Format date for display
      const eventDate = new Date(dateStr);
      const formattedDate = eventDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Set modal title
      modalDate.textContent = formattedDate;

      // Clear previous events
      modalEvents.innerHTML = '';

      // Group events by type
      const groupedEvents = {
        test: events.filter(e => e.type === 'test'),
        practical: events.filter(e => e.type === 'practical'),
        assignment: events.filter(e => e.type === 'assignment')
      };

      // Add events to modal
      if (groupedEvents.test.length > 0) {
        const testSection = document.createElement('div');
        testSection.innerHTML = `<h4 class="font-medium text-sm mb-1">Tests</h4>`;
        const testList = document.createElement('ul');
        testList.className = 'pl-4 space-y-1';

        groupedEvents.test.forEach(test => {
          const item = document.createElement('li');
          item.className = 'text-sm flex items-center';
          item.innerHTML = `
            <div class="w-2 h-2 bg-indigo-400 rounded-full mr-2"></div>
            <span>${test.title}</span>
          `;
          testList.appendChild(item);
        });

        testSection.appendChild(testList);
        modalEvents.appendChild(testSection);
      }

      if (groupedEvents.practical.length > 0) {
        const practicalSection = document.createElement('div');
        practicalSection.innerHTML = `<h4 class="font-medium text-sm mb-1 mt-2">Lab Practicals</h4>`;
        const practicalList = document.createElement('ul');
        practicalList.className = 'pl-4 space-y-1';

        groupedEvents.practical.forEach(practical => {
          const item = document.createElement('li');
          item.className = 'text-sm flex items-center';
          item.innerHTML = `
            <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
            <span>${practical.title}</span>
          `;
          practicalList.appendChild(item);
        });

        practicalSection.appendChild(practicalList);
        modalEvents.appendChild(practicalSection);
      }

      if (groupedEvents.assignment.length > 0) {
        const assignmentSection = document.createElement('div');
        assignmentSection.innerHTML = `<h4 class="font-medium text-sm mb-1 mt-2">Assignments</h4>`;
        const assignmentList = document.createElement('ul');
        assignmentList.className = 'pl-4 space-y-1';

        groupedEvents.assignment.forEach(assignment => {
          const item = document.createElement('li');
          item.className = 'text-sm flex items-center';
          item.innerHTML = `
            <div class="w-2 h-2 bg-orange-400 rounded-full mr-2"></div>
            <span>${assignment.title}</span>
          `;
          assignmentList.appendChild(item);
        });

        assignmentSection.appendChild(assignmentList);
        modalEvents.appendChild(assignmentSection);
      }

      // Show modal
      eventModal.classList.remove('hidden');
    }
  });
</script>