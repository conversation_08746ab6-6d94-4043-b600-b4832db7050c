<!-- Day Timetable Modal -->
<div id="day-timetable-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h2 id="day-modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-day-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div id="day-modal-content" class="space-y-6">
      <!-- Content will be populated by JavaScript -->
      <div id="day-loading" class="py-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading timetable...</p>
      </div>
      
      <!-- Holidays Section -->
      <div id="day-holidays-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Holidays</h3>
        <div id="day-holidays-content" class="bg-gray-50 rounded-lg p-4"></div>
      </div>
      
      <!-- Lectures Section -->
      <div id="day-lectures-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Lectures</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="day-lectures-content"></tbody>
          </table>
        </div>
      </div>
      
      <!-- Tests Section -->
      <div id="day-tests-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Tests</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attempts</th>
              </tr>
            </thead>
            <tbody id="day-tests-content"></tbody>
          </table>
        </div>
      </div>
      
      <!-- Practicals Section -->
      <div id="day-practicals-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Practicals</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="day-practicals-content"></tbody>
          </table>
        </div>
      </div>
      
      <!-- Assignments Section -->
      <div id="day-assignments-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Assignments</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
              </tr>
            </thead>
            <tbody id="day-assignments-content"></tbody>
          </table>
        </div>
      </div>
      
      <!-- No Events Message -->
      <div id="day-no-events" class="hidden py-8 text-center">
        <p class="text-gray-500">No events scheduled for this day.</p>
      </div>
    </div>
  </div>
</div>
