<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">My Learning Plans</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Available Instruction Plans</h3>

        <% if (plans && plans.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Teacher</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% plans.forEach(plan => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.title %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.teacher_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <%= formatDateTime(plan.created_at) %>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <% if (plan.is_completed) { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Completed
                        </span>
                      <% } else if (plan.is_viewed) { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          In Progress
                        </span>
                      <% } else { %>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                          Not Started
                        </span>
                      <% } %>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <a href="/student/instruction-plans/<%= plan.id %>" class="text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1">
                        View
                      </a>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  No instruction plans are currently available for your subjects.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</div>

<%- include('../../partials/student/footer') %>
