<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Learning Plan Details</h2>
      <a href="/student/instruction-plans" class="bg-white text-blue-600 px-3 py-1 rounded text-sm hover:bg-gray-100 transition">
        Back to Plans
      </a>
    </div>

    <div class="p-6">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-2"><%= plan.title %></h3>
        <div class="flex items-center mb-4">
          <span class="text-gray-500">
            Subject: <span class="font-medium"><%= plan.subject_name %></span>
          </span>
          <span class="mx-2">•</span>
          <span class="text-gray-500">
            Teacher: <span class="font-medium"><%= plan.teacher_name %></span>
          </span>
        </div>
        
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Description</h4>
          <p class="text-gray-700"><%= plan.description || 'No description provided' %></p>
        </div>
        
        <% if (resources && resources.length > 0) { %>
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Learning Resources</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <% resources.forEach(resource => { %>
              <div class="border rounded-lg p-4 bg-gray-50">
                <h5 class="font-medium text-gray-800 mb-2"><%= resource.resource_name %></h5>
                
                <% if (resource.resource_type === 'file') { %>
                  <a href="<%= resource.resource_path %>" 
                     class="inline-flex items-center text-blue-600 hover:underline mt-2" 
                     target="_blank">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                    </svg>
                    Download Resource
                  </a>
                <% } else if (resource.resource_type === 'link') { %>
                  <a href="<%= resource.resource_path %>" 
                     class="inline-flex items-center text-blue-600 hover:underline mt-2" 
                     target="_blank">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                      <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                    </svg>
                    Visit Link
                  </a>
                <% } else if (resource.resource_type === 'text') { %>
                  <div class="text-sm text-gray-600 mt-2">
                    <%= resource.resource_content %>
                  </div>
                <% } %>
              </div>
            <% }); %>
          </div>
        </div>
        <% } %>
        
        <div class="mt-8 pt-4 border-t border-gray-200">
          <button id="mark-completed-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">
            Mark as Completed
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const markCompletedBtn = document.getElementById('mark-completed-btn');
    
    if (markCompletedBtn) {
      markCompletedBtn.addEventListener('click', async function() {
        try {
          const response = await fetch(`/student/instruction-plans/<%= plan.id %>/complete`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          const data = await response.json();
          
          if (data.success) {
            // Show success message
            showToast('Plan marked as completed!', 'success');
            
            // Disable the button
            markCompletedBtn.disabled = true;
            markCompletedBtn.classList.add('bg-gray-400');
            markCompletedBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
            markCompletedBtn.textContent = 'Completed';
          } else {
            showToast(data.message || 'An error occurred', 'error');
          }
        } catch (error) {
          console.error('Error marking plan as completed:', error);
          showToast('An error occurred while marking the plan as completed', 'error');
        }
      });
    }
    
    // Helper function to show toast notifications
    function showToast(message, type = 'info') {
      // Check if the toast container exists, if not create it
      let toastContainer = document.getElementById('toast-container');
      
      if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed bottom-4 right-4 z-50';
        document.body.appendChild(toastContainer);
      }
      
      // Create the toast element
      const toast = document.createElement('div');
      toast.className = `mb-3 p-4 rounded shadow-lg flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
      }`;
      
      // Add the message
      toast.innerHTML = `
        <div class="mr-2">
          ${type === 'success' ? 
            '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
            type === 'error' ?
            '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
            '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'
          }
        </div>
        <div>${message}</div>
      `;
      
      // Add to container
      toastContainer.appendChild(toast);
      
      // Remove after 3 seconds
      setTimeout(() => {
        toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 3000);
    }
  });
</script>

<%- include('../../partials/student/footer') %>
