<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <!-- Navigation -->
  <%- include('../../partials/student/practical-nav') %>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Practical Calendar</h2>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <button id="prev-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h3 id="current-month" class="text-xl font-semibold mx-4"></h3>
          <button id="next-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
        <button id="today-btn" class="px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition">
          Today
        </button>
      </div>

      <div class="overflow-x-auto">
        <div class="calendar-grid grid grid-cols-7 gap-1">
          <!-- Calendar header -->
          <div class="text-center font-semibold py-2 bg-gray-100">Sun</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Mon</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Tue</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Wed</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Thu</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Fri</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Sat</div>

          <!-- Calendar days will be inserted here by JavaScript -->
          <div id="calendar-days" class="contents"></div>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 flex flex-wrap gap-4">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Upcoming</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Submitted</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-yellow-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Not Submitted</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Cancelled</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-purple-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Graded</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Practical Details Modal -->
  <div id="practical-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
      <div class="flex justify-between items-center mb-4">
        <h2 id="modal-title" class="text-xl font-bold text-gray-800"></h2>
        <button id="close-modal" class="text-gray-400 hover:text-gray-500">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div id="modal-content" class="space-y-4">
        <!-- Content will be populated by JavaScript -->
      </div>
      <div class="mt-6 flex justify-end">
        <a id="view-details-btn" href="#" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          View Details
        </a>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');
    const practicalModal = document.getElementById('practical-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const viewDetailsBtn = document.getElementById('view-details-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Practical data from server
    const practicals = <%- JSON.stringify(practicals || []) %>;

    // Initialize calendar
    renderCalendar(currentMonth, currentYear);

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderCalendar(currentMonth, currentYear);
    });

    todayBtn.addEventListener('click', () => {
      const today = new Date();
      currentMonth = today.getMonth();
      currentYear = today.getFullYear();
      renderCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      practicalModal.classList.add('hidden');
    });

    // Render calendar
    function renderCalendar(month, year) {
      // Clear previous calendar
      calendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthEl.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        calendarDays.appendChild(dayEl);
      }

      // Current month's days
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        const dayEl = createDayElement(i, isToday ? 'bg-blue-50 font-bold' : '', month, year);
        calendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        calendarDays.appendChild(dayEl);
      }
    }

    // Create day element
    function createDayElement(day, extraClasses, month, year) {
      const dayEl = document.createElement('div');
      dayEl.className = `min-h-[100px] border p-1 ${extraClasses}`;

      // Add day number
      const dayNumber = document.createElement('div');
      dayNumber.className = 'text-right text-sm';
      dayNumber.textContent = day;
      dayEl.appendChild(dayNumber);

      // Add practicals for this day
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const dayPracticals = practicals.filter(p => p.date === dateStr);

      if (dayPracticals.length > 0) {
        const practicalsContainer = document.createElement('div');
        practicalsContainer.className = 'mt-1 space-y-1';

        dayPracticals.forEach(practical => {
          const practicalEl = document.createElement('div');

          // Determine color based on status
          let bgColor = 'bg-blue-100';
          if (practical.status === 'cancelled') {
            bgColor = 'bg-red-100';
          } else if (practical.has_submission) {
            if (practical.submission_status === 'graded') {
              bgColor = 'bg-purple-100';
            } else {
              bgColor = 'bg-green-100';
            }
          } else if (new Date(practical.date) < new Date()) {
            bgColor = 'bg-yellow-100';
          }

          practicalEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer`;
          practicalEl.textContent = `${practical.start_time.substring(0, 5)} - ${practical.practical_topic}`;

          // Add click event to show modal
          practicalEl.addEventListener('click', () => showPracticalModal(practical));

          practicalsContainer.appendChild(practicalEl);
        });

        dayEl.appendChild(practicalsContainer);
      }

      return dayEl;
    }

    // Show practical modal
    function showPracticalModal(practical) {
      modalTitle.textContent = practical.practical_topic;

      // Format date
      const practicalDate = new Date(practical.date);
      const formattedDate = practicalDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Determine status text and color
      let statusText = 'Upcoming';
      let statusColor = 'bg-blue-100 text-blue-800';

      if (practical.status === 'cancelled') {
        statusText = 'Cancelled';
        statusColor = 'bg-red-100 text-red-800';
      } else if (practical.has_submission) {
        if (practical.submission_status === 'graded') {
          statusText = 'Graded';
          statusColor = 'bg-purple-100 text-purple-800';
        } else {
          statusText = 'Submitted';
          statusColor = 'bg-green-100 text-green-800';
        }
      } else if (new Date(practical.date) < new Date()) {
        statusText = 'Not Submitted';
        statusColor = 'bg-yellow-100 text-yellow-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date & Time</p>
          <p class="font-medium">${formattedDate}</p>
          <p class="font-medium">${practical.start_time.substring(0, 5)} - ${practical.end_time.substring(0, 5)}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Subject</p>
          <p class="font-medium">${practical.subject_name}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Venue</p>
          <p class="font-medium">${practical.venue}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Status</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusColor}">
            ${statusText}
          </span>
        </div>
      `;

      // Set view details link
      viewDetailsBtn.href = `/student/practicals/${practical.id}`;

      // Show modal
      practicalModal.classList.remove('hidden');
    }
  });
</script>

<%- include('../../partials/student/footer') %>
