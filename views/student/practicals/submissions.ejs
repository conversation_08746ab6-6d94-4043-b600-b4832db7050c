<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <!-- Navigation -->
  <%- include('../../partials/student/practical-nav') %>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">My Practical Submissions</h2>
    </div>

    <div class="p-6">
      <% if (submissions && submissions.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Practical Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% submissions.forEach(submission => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <% if (submission.practical_date) { %>
                        <%= formatDateTime(submission.practical_date) %>
                      <% } else { %>
                        N/A
                      <% } %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900"><%= submission.subject_name %></div>
                    <div class="text-sm text-gray-500"><%= submission.class_name %></div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900"><%= submission.practical_topic %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <%= formatDateTime(submission.submission_date) %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if (submission.status === 'pending') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                    <% } else if (submission.status === 'submitted') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        Submitted
                      </span>
                    <% } else if (submission.status === 'graded') { %>
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Graded
                      </span>
                    <% } %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <%= submission.grade || 'Not graded' %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="/student/practicals/submissions/<%= submission.id %>" class="text-blue-600 hover:text-blue-900">View Details</a>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      <% } else { %>
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                You haven't submitted any practical work yet.
              </p>
            </div>
          </div>
        </div>
      <% } %>
    </div>
  </div>
</div>

<%- include('../../partials/student/footer') %>
