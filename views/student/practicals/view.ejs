<%- include('../../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <!-- Navigation -->
  <%- include('../../partials/student/practical-nav') %>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">Practical Details</h2>
    </div>

    <div class="p-6">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-2"><%= practical.practical_topic %></h3>
        <div class="flex flex-wrap items-center mb-4">
          <span class="text-gray-500 mr-4">
            Subject: <span class="font-medium"><%= practical.subject_name %></span>
          </span>
          <span class="text-gray-500 mr-4">
            Class: <span class="font-medium"><%= practical.class_name %></span>
          </span>
          <span class="text-gray-500">
            Teacher: <span class="font-medium"><%= practical.teacher_name %></span>
          </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-700 mb-2">Date & Time</h4>
            <p class="text-gray-800">
              <%= formatDateTime(new Date(practical.date + 'T' + practical.start_time)) %>
            </p>
            <p class="text-gray-600">
              <%= practical.start_time.substring(0, 5) %> - <%= practical.end_time.substring(0, 5) %>
            </p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-700 mb-2">Venue</h4>
            <p class="text-gray-800"><%= practical.venue %></p>
            <p class="text-gray-600">
              Status:
              <% if (practical.status === 'pending') { %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                  Upcoming
                </span>
              <% } else if (practical.status === 'completed') { %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  Completed
                </span>
              <% } else if (practical.status === 'cancelled') { %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                  Cancelled
                </span>
              <% } %>
            </p>
          </div>
        </div>

        <% if (practical.notes) { %>
          <div class="mb-6">
            <h4 class="text-lg font-semibold mb-2 text-gray-700">Notes</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-gray-800 whitespace-pre-line"><%= practical.notes %></p>
            </div>
          </div>
        <% } %>
      </div>

      <!-- Submission Section -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-semibold mb-4 text-gray-700">Your Submission</h3>

        <% if (hasSubmission) { %>
          <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <div class="flex justify-between items-start mb-4">
              <div>
                <h4 class="font-semibold text-gray-700">Submission Status</h4>
                <% if (submission.status === 'pending') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                <% } else if (submission.status === 'submitted') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    Submitted
                  </span>
                <% } else if (submission.status === 'graded') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    Graded
                  </span>
                <% } %>
              </div>
              <div class="text-sm text-gray-500">
                Submitted on: <%= formatDateTime(submission.submission_date) %>
              </div>
            </div>

            <div class="mb-4">
              <h4 class="font-semibold text-gray-700 mb-2">Your Work</h4>
              <div class="bg-white p-3 rounded border border-gray-200">
                <p class="text-gray-800 whitespace-pre-line"><%= submission.content %></p>
              </div>
            </div>

            <% if (submission.attachment) { %>
              <div class="mb-4">
                <h4 class="font-semibold text-gray-700 mb-2">Attachment</h4>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                  </svg>
                  <a href="<%= submission.attachment %>" target="_blank" class="text-blue-600 hover:underline">
                    View Attachment
                  </a>
                </div>
              </div>
            <% } %>

            <% if (submission.status === 'graded') { %>
              <div class="border-t pt-4 mt-4">
                <div class="mb-2">
                  <h4 class="font-semibold text-gray-700">Grade</h4>
                  <p class="text-lg font-bold text-gray-800"><%= submission.grade %></p>
                </div>

                <% if (submission.feedback) { %>
                  <div>
                    <h4 class="font-semibold text-gray-700 mb-2">Teacher Feedback</h4>
                    <div class="bg-white p-3 rounded border border-gray-200">
                      <p class="text-gray-800 whitespace-pre-line"><%= submission.feedback %></p>
                    </div>
                  </div>
                <% } %>
              </div>
            <% } %>

            <% if (submission.status !== 'graded') { %>
              <div class="mt-4 border-t pt-4">
                <h4 class="font-semibold text-gray-700 mb-2">Update Your Submission</h4>
                <form action="/student/practicals/<%= practical.id %>/submit" method="POST" enctype="multipart/form-data">
                  <div class="mb-4">
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content</label>
                    <textarea id="content" name="content" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"><%= submission.content %></textarea>
                  </div>

                  <div class="mb-4">
                    <label for="attachment" class="block text-sm font-medium text-gray-700 mb-1">Attachment (optional)</label>
                    <input type="file" id="attachment" name="attachment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Allowed file types: PDF, DOC, DOCX, JPG, JPEG, PNG (max 10MB)</p>
                  </div>

                  <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      Update Submission
                    </button>
                  </div>
                </form>
              </div>
            <% } %>
          </div>
        <% } else { %>
          <% if (practical.status !== 'cancelled') { %>
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-semibold text-gray-700 mb-4">Submit Your Work</h4>
              <form action="/student/practicals/<%= practical.id %>/submit" method="POST" enctype="multipart/form-data">
                <div class="mb-4">
                  <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content</label>
                  <textarea id="content" name="content" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter your practical work details here..."></textarea>
                </div>

                <div class="mb-4">
                  <label for="attachment" class="block text-sm font-medium text-gray-700 mb-1">Attachment (optional)</label>
                  <input type="file" id="attachment" name="attachment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                  <p class="text-xs text-gray-500 mt-1">Allowed file types: PDF, DOC, DOCX, JPG, JPEG, PNG (max 10MB)</p>
                </div>

                <div class="flex justify-end">
                  <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Submit Work
                  </button>
                </div>
              </form>
            </div>
          <% } else { %>
            <div class="bg-red-50 border-l-4 border-red-400 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-700">
                    This practical has been cancelled. Submissions are not accepted.
                  </p>
                </div>
              </div>
            </div>
          <% } %>
        <% } %>
      </div>
    </div>
  </div>
</div>

<%- include('../../partials/student/footer') %>
