<!-- Student Profile Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-500 to-blue-700">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-800">Student Profile</h1>
            <p class="text-gray-600 mt-2">Academic Journey Dashboard</p>
          </div>
          <div class="flex space-x-3">
            <button id="edit-profile-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-edit mr-2"></i>Edit Profile
            </button>
            <button id="change-password-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-key mr-2"></i>Change Password
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Personal Information Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>

          <div class="p-6">
            <!-- Profile Image -->
            <div class="flex flex-col items-center mb-6">
              <div class="relative">
                <% if (user && user.profile_image) { %>
                  <img src="<%= user.profile_image %>" alt="Student Photo" class="w-32 h-32 rounded-full object-cover border-4 border-blue-600 shadow-lg">
                <% } else { %>
                  <div class="w-32 h-32 rounded-full bg-blue-600 flex items-center justify-center text-white text-4xl font-bold shadow-lg">
                    <%= user && user.name ? user.name.charAt(0).toUpperCase() : 'S' %>
                  </div>
                <% } %>
                <button class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                  <i class="fas fa-camera text-blue-600"></i>
                </button>
              </div>
              <h3 class="text-xl font-bold text-gray-800"><%= user ? user.name : 'Student Name' %></h3>
              <p class="text-blue-600 font-semibold">Student</p>
              <span class="mt-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                <i class="fas fa-graduation-cap mr-1"></i>Academic Learner
              </span>
            </div>

            <!-- Contact Information -->
            <div class="space-y-4">
              <div class="flex items-center">
                <i class="fas fa-envelope text-blue-600 w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.email : '<EMAIL>' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user text-blue-600 w-5"></i>
                <span class="ml-3 text-gray-700"><%= user ? user.username : 'username' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-school text-blue-600 w-5"></i>
                <span class="ml-3 text-gray-700"><%= user && user.institution ? user.institution : 'Not set' %></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-layer-group text-blue-600 w-5"></i>
                <span class="ml-3 text-gray-700">Class: <%= user && user.grade ? user.grade : 'Not set' %></span>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">About</h4>
              <p class="text-gray-600 text-sm leading-relaxed">
                <%= user && user.bio ? user.bio : 'Dedicated student pursuing academic excellence and personal growth. Committed to learning and achieving educational goals.' %>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Academic Details -->
      <div class="lg:col-span-2">
        <div class="space-y-6">
          <!-- Academic Overview -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
              <h2 class="text-xl font-semibold">Academic Information</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Date of Birth</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.date_of_birth ? new Date(user.date_of_birth).toLocaleDateString() : 'Not set' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Field of Study</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.field_of_study ? user.field_of_study : 'Not set' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Preferred Subjects</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.preferred_subjects ? user.preferred_subjects : 'Not set' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Target Exams</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.target_exams ? user.target_exams : 'Not set' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Study Goal</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.study_goal ? user.study_goal : 'Not set' %></p>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-gray-500 mb-2">Language Preference</h4>
                  <p class="text-gray-800 font-medium"><%= user && user.language_preference ? user.language_preference : 'English' %></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
              <h2 class="text-xl font-semibold">Quick Actions</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/student/dashboard" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-tachometer-alt text-blue-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Dashboard</span>
                </a>
                <a href="/student/assignments" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-tasks text-blue-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Assignments</span>
                </a>
                <a href="/student/results" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-chart-line text-blue-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Results</span>
                </a>
                <a href="/student/calendar" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <i class="fas fa-calendar text-blue-600 text-2xl mb-2"></i>
                  <span class="text-sm font-medium text-gray-700">Calendar</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Profile Modal -->
<div id="edit-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Edit Profile</h3>
        <button id="close-edit-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="edit-profile-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input type="text" name="name" value="<%= user ? user.name : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input type="email" name="email" value="<%= user ? user.email : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Field of Study</label>
          <input type="text" name="field_of_study" value="<%= user ? user.field_of_study : '' %>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"><%= user ? user.bio : '' %></textarea>
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-edit" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Change Password</h3>
        <button id="close-password-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="change-password-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
          <input type="password" name="current_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
          <input type="password" name="new_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
          <input type="password" name="confirm_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent">
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-password" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">Update Password</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Modal functionality
  const editProfileBtn = document.getElementById('edit-profile-btn');
  const changePasswordBtn = document.getElementById('change-password-btn');
  const editModal = document.getElementById('edit-profile-modal');
  const passwordModal = document.getElementById('change-password-modal');

  // Edit Profile Modal
  editProfileBtn.addEventListener('click', () => {
    editModal.classList.remove('hidden');
  });

  document.getElementById('close-edit-modal').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  document.getElementById('cancel-edit').addEventListener('click', () => {
    editModal.classList.add('hidden');
  });

  // Change Password Modal
  changePasswordBtn.addEventListener('click', () => {
    passwordModal.classList.remove('hidden');
  });

  document.getElementById('close-password-modal').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  document.getElementById('cancel-password').addEventListener('click', () => {
    passwordModal.classList.add('hidden');
  });

  // Form submissions
  document.getElementById('edit-profile-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    try {
      const response = await fetch('/api/profile/update', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert('Profile updated successfully!');
        editModal.classList.add('hidden');
        location.reload();
      } else {
        alert('Failed to update profile');
      }
    } catch (error) {
      alert('Error updating profile');
    }
  });

  document.getElementById('change-password-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    if (formData.get('new_password') !== formData.get('confirm_password')) {
      alert('New passwords do not match');
      return;
    }

    try {
      const response = await fetch('/api/profile/change-password', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        alert('Password changed successfully!');
        passwordModal.classList.add('hidden');
        e.target.reset();
      } else {
        alert('Failed to change password');
      }
    } catch (error) {
      alert('Error changing password');
    }
  });

  // Close modals on outside click
  editModal.addEventListener('click', (e) => {
    if (e.target === editModal) {
      editModal.classList.add('hidden');
    }
  });

  passwordModal.addEventListener('click', (e) => {
    if (e.target === passwordModal) {
      passwordModal.classList.add('hidden');
    }
  });
});
</script>
