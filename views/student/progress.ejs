<%- include('../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">My Progress</h2>
    </div>

    <div class="p-6">
      <!-- Progress Overview -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">Progress Overview</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-blue-800">Exams Completed</h4>
                <p class="text-2xl font-bold text-blue-600">0</p>
              </div>
              <div class="bg-blue-100 p-3 rounded-full">
                <i class="fas fa-file-alt text-blue-500 text-xl"></i>
              </div>
            </div>
          </div>
          <div class="bg-green-50 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-green-800">Average Score</h4>
                <p class="text-2xl font-bold text-green-600">0%</p>
              </div>
              <div class="bg-green-100 p-3 rounded-full">
                <i class="fas fa-chart-line text-green-500 text-xl"></i>
              </div>
            </div>
          </div>
          <div class="bg-purple-50 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-purple-800">Learning Plans</h4>
                <p class="text-2xl font-bold text-purple-600">0</p>
              </div>
              <div class="bg-purple-100 p-3 rounded-full">
                <i class="fas fa-book text-purple-500 text-xl"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance by Subject -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">Performance by Subject</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
          <canvas id="subjectChart" height="200"></canvas>
        </div>
      </div>

      <!-- Recent Exam Results -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">Recent Exam Results</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Exam Name</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Date</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Score</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="py-3 px-4 text-sm text-gray-500" colspan="4">No exam results available</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Progress Over Time -->
      <div>
        <h3 class="text-lg font-semibold mb-4">Progress Over Time</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
          <canvas id="progressChart" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Sample data for charts
    const subjectData = {
      labels: ['Mathematics', 'Science', 'English', 'Social Studies', 'Computer Science'],
      datasets: [{
        label: 'Average Score (%)',
        data: [0, 0, 0, 0, 0],
        backgroundColor: [
          'rgba(54, 162, 235, 0.5)',
          'rgba(75, 192, 192, 0.5)',
          'rgba(255, 206, 86, 0.5)',
          'rgba(153, 102, 255, 0.5)',
          'rgba(255, 159, 64, 0.5)'
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)'
        ],
        borderWidth: 1
      }]
    };

    const progressData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [{
        label: 'Average Score (%)',
        data: [0, 0, 0, 0, 0, 0],
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }]
    };

    // Create charts
    const subjectChart = new Chart(
      document.getElementById('subjectChart'),
      {
        type: 'bar',
        data: subjectData,
        options: {
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      }
    );

    const progressChart = new Chart(
      document.getElementById('progressChart'),
      {
        type: 'line',
        data: progressData,
        options: {
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          }
        }
      }
    );

    // Fetch actual data from API
    fetch('/student/api/progress')
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error('Failed to fetch progress data');
      })
      .then(data => {
        // Update charts with actual data
        if (data.subjectPerformance) {
          subjectChart.data.labels = data.subjectPerformance.map(item => item.subject);
          subjectChart.data.datasets[0].data = data.subjectPerformance.map(item => item.score);
          subjectChart.update();
        }

        if (data.progressOverTime) {
          progressChart.data.labels = data.progressOverTime.map(item => item.month);
          progressChart.data.datasets[0].data = data.progressOverTime.map(item => item.score);
          progressChart.update();
        }
      })
      .catch(error => {
        console.error('Error fetching progress data:', error);
      });
  });
</script>

<%- include('../partials/student/footer') %>
