<%- include('../partials/student/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-blue-600 text-white p-4">
      <h2 class="text-xl font-semibold">My Results</h2>
    </div>

    <div class="p-6">
      <!-- Tabs for different result types -->
      <div class="mb-6 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
          <li class="mr-2">
            <a href="#" class="inline-block p-4 border-b-2 border-blue-600 rounded-t-lg active text-blue-600" id="exams-tab" data-tab="exams">
              Exams
            </a>
          </li>
          <li class="mr-2">
            <a href="#" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="assignments-tab" data-tab="assignments">
              Assignments
            </a>
          </li>
          <li>
            <a href="#" class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="practicals-tab" data-tab="practicals">
              Practicals
            </a>
          </li>
        </ul>
      </div>

      <!-- Exams Tab Content -->
      <div id="exams-content" class="tab-content">
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Exam Name</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Date</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Score</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="py-3 px-4 text-sm text-gray-500" colspan="5">No exam results available</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Assignments Tab Content -->
      <div id="assignments-content" class="tab-content hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Assignment Name</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Due Date</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Score</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="py-3 px-4 text-sm text-gray-500" colspan="5">No assignment results available</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Practicals Tab Content -->
      <div id="practicals-content" class="tab-content hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Practical Name</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Date</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Score</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Status</th>
                <th class="py-3 px-4 text-left text-sm font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr>
                <td class="py-3 px-4 text-sm text-gray-500" colspan="5">No practical results available</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabs = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        tabs.forEach(t => {
          t.classList.remove('active', 'text-blue-600', 'border-blue-600');
          t.classList.add('hover:text-gray-600', 'hover:border-gray-300', 'border-transparent');
        });
        
        // Add active class to clicked tab
        this.classList.add('active', 'text-blue-600', 'border-blue-600');
        this.classList.remove('hover:text-gray-600', 'hover:border-gray-300', 'border-transparent');
        
        // Hide all tab contents
        tabContents.forEach(content => {
          content.classList.add('hidden');
        });
        
        // Show the selected tab content
        const tabId = this.getAttribute('data-tab');
        document.getElementById(`${tabId}-content`).classList.remove('hidden');
      });
    });
  });
</script>

<%- include('../partials/student/footer') %>
