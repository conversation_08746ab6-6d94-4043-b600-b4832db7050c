<%- include('../partials/header') %>

<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">My Subjects</h1>
  
  <div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Subject Combinations for <span id="trade-name"></span></h2>
    
    <div class="mb-4">
      <p class="text-gray-700">
        Your subjects are organized according to the curriculum requirements. 
        Each student must select subjects based on the trade requirements.
      </p>
    </div>
    
    <div id="subject-groups-container" class="space-y-6">
      <!-- Subject groups will be loaded here -->
      <div class="animate-pulse">
        <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div class="h-8 bg-gray-200 rounded w-full mb-2"></div>
        <div class="h-8 bg-gray-200 rounded w-full mb-2"></div>
        <div class="h-8 bg-gray-200 rounded w-full mb-2"></div>
      </div>
    </div>
  </div>
  
  <div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">My Weekly Timetable</h2>
    
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Time
            </th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Monday
            </th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Tuesday
            </th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Wednesday
            </th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Thursday
            </th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
              Friday
            </th>
          </tr>
        </thead>
        <tbody id="timetable-body">
          <!-- Timetable will be loaded here -->
          <tr>
            <td colspan="6" class="py-4 px-6 border-b text-center text-gray-500">
              Loading timetable...
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Fetch subject groups
    fetch('/api/student/subjects-by-group')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Display trade name
          document.getElementById('trade-name').textContent = data.trade.trade_name;
          
          // Clear loading placeholder
          document.getElementById('subject-groups-container').innerHTML = '';
          
          // Display subject groups
          data.subject_groups.forEach(group => {
            const groupElement = document.createElement('div');
            groupElement.className = 'mb-6';
            
            // Create group header
            const groupHeader = document.createElement('h3');
            groupHeader.className = 'text-lg font-semibold mb-2 border-b pb-2';
            groupHeader.textContent = `${group.group_id}. ${group.group_name}`;
            groupElement.appendChild(groupHeader);
            
            // Create subject list
            const subjectList = document.createElement('div');
            subjectList.className = 'space-y-2 pl-4';
            
            // Add subjects
            group.subjects.forEach((subject, index) => {
              const subjectItem = document.createElement('div');
              subjectItem.className = 'flex items-center';
              
              // Add bullet or index
              const bulletSpan = document.createElement('span');
              bulletSpan.className = 'mr-2 text-gray-600';
              
              // Use roman numerals for sub-items
              const romanNumerals = ['i', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix', 'x'];
              bulletSpan.textContent = romanNumerals[index] ? romanNumerals[index] + '.' : (index + 1) + '.';
              
              subjectItem.appendChild(bulletSpan);
              
              // Add subject name
              const nameSpan = document.createElement('span');
              nameSpan.className = subject.is_enrolled ? 'font-medium' : '';
              nameSpan.textContent = subject.name;
              
              if (subject.is_elective) {
                const elective = document.createElement('span');
                elective.className = 'ml-2 text-sm text-gray-500';
                elective.textContent = '(Elective)';
                nameSpan.appendChild(elective);
              }
              
              subjectItem.appendChild(nameSpan);
              
              // Add enrolled indicator
              if (subject.is_enrolled) {
                const enrolledBadge = document.createElement('span');
                enrolledBadge.className = 'ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full';
                enrolledBadge.textContent = 'Enrolled';
                subjectItem.appendChild(enrolledBadge);
              }
              
              subjectList.appendChild(subjectItem);
            });
            
            groupElement.appendChild(subjectList);
            document.getElementById('subject-groups-container').appendChild(groupElement);
          });
        } else {
          document.getElementById('subject-groups-container').innerHTML = `
            <div class="p-4 bg-red-100 text-red-700 rounded">
              ${data.message || 'Failed to load subject groups'}
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error fetching subject groups:', error);
        document.getElementById('subject-groups-container').innerHTML = `
          <div class="p-4 bg-red-100 text-red-700 rounded">
            Error loading subject groups. Please try again later.
          </div>
        `;
      });
    
    // Fetch timetable
    fetch('/api/student/timetable')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const timetableBody = document.getElementById('timetable-body');
          timetableBody.innerHTML = '';
          
          // Define periods
          const periods = [
            { period: 1, time: '08:00 - 08:40' },
            { period: 2, time: '08:45 - 09:25' },
            { period: 3, time: '09:30 - 10:10' },
            { period: 4, time: '10:15 - 10:55' },
            { period: 5, time: '11:00 - 11:40' },
            { period: 6, time: '11:45 - 12:25' }
          ];
          
          // Create rows for each period
          periods.forEach(periodInfo => {
            const row = document.createElement('tr');
            
            // Add time cell
            const timeCell = document.createElement('td');
            timeCell.className = 'py-2 px-4 border-b border-gray-200 bg-gray-50 font-medium';
            timeCell.textContent = periodInfo.time;
            row.appendChild(timeCell);
            
            // Add cells for each day
            for (let day = 1; day <= 5; day++) {
              const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day];
              const dayLectures = data.timetable[dayName] || [];
              
              // Find lecture for this period
              const lecture = dayLectures.find(l => l.period === periodInfo.period);
              
              const cell = document.createElement('td');
              cell.className = 'py-2 px-4 border-b border-gray-200';
              
              if (lecture) {
                cell.innerHTML = `
                  <div class="font-medium">${lecture.subject_name}</div>
                  <div class="text-xs text-gray-500">${lecture.teacher_name}</div>
                  <div class="text-xs text-gray-500">Room: ${lecture.room}</div>
                `;
              } else {
                cell.textContent = '-';
              }
              
              row.appendChild(cell);
            }
            
            timetableBody.appendChild(row);
          });
        } else {
          document.getElementById('timetable-body').innerHTML = `
            <tr>
              <td colspan="6" class="py-4 px-6 border-b text-center text-red-500">
                ${data.message || 'Failed to load timetable'}
              </td>
            </tr>
          `;
        }
      })
      .catch(error => {
        console.error('Error fetching timetable:', error);
        document.getElementById('timetable-body').innerHTML = `
          <tr>
            <td colspan="6" class="py-4 px-6 border-b text-center text-red-500">
              Error loading timetable. Please try again later.
            </td>
          </tr>
        `;
      });
  });
</script>

<%- include('../partials/footer') %>
