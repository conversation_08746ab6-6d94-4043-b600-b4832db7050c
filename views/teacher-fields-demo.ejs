<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Fields & Timeline Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .teacher-primary { background-color: #4F46E5; }
        .teacher-secondary { background-color: #7C3AED; }
        .teacher-light { background-color: #E0E7FF; }
        .text-teacher-primary { color: #4F46E5; }
        .text-teacher-secondary { color: #7C3AED; }
        .bg-teacher-primary { background-color: #4F46E5; }
        .bg-teacher-secondary { background-color: #7C3AED; }
        .border-teacher-primary { border-color: #4F46E5; }
        .hover\:bg-teacher-secondary:hover { background-color: #7C3AED; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-800">Teacher Fields & Timeline Demo</h1>
                            <p class="text-gray-600 mt-2">Comprehensive display of all teacher database fields with timeline visualization</p>
                        </div>
                        <div class="flex space-x-3">
                            <button id="load-sample-data" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-database mr-2"></i>Load Sample Data
                            </button>
                            <button id="refresh-data" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Fields Overview -->
            <div class="mb-8">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
                        <h2 class="text-xl font-semibold">Database Fields Overview</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Users Table Fields -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-purple-800 mb-3">Users Table Fields</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-purple-500 mr-2"></i>id, username, name, full_name</li>
                                    <li><i class="fas fa-circle text-xs text-purple-500 mr-2"></i>email, password, role</li>
                                    <li><i class="fas fa-circle text-xs text-purple-500 mr-2"></i>profile_image, subjects, bio</li>
                                    <li><i class="fas fa-circle text-xs text-purple-500 mr-2"></i>date_of_birth, gender</li>
                                    <li><i class="fas fa-circle text-xs text-purple-500 mr-2"></i>created_at, last_login, is_active</li>
                                </ul>
                            </div>

                            <!-- Staff Table - Personal Info -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-blue-800 mb-3">Staff Table - Personal</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-blue-500 mr-2"></i>employee_id, designation</li>
                                    <li><i class="fas fa-circle text-xs text-blue-500 mr-2"></i>department, joining_date</li>
                                    <li><i class="fas fa-circle text-xs text-blue-500 mr-2"></i>employment_type, phone</li>
                                    <li><i class="fas fa-circle text-xs text-blue-500 mr-2"></i>address, city, state, pincode</li>
                                    <li><i class="fas fa-circle text-xs text-blue-500 mr-2"></i>emergency_contact</li>
                                </ul>
                            </div>

                            <!-- Staff Table - Education -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-green-800 mb-3">Education Fields</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-green-500 mr-2"></i>class_10_* (board, year, %, school)</li>
                                    <li><i class="fas fa-circle text-xs text-green-500 mr-2"></i>class_12_* (board, year, %, school, stream)</li>
                                    <li><i class="fas fa-circle text-xs text-green-500 mr-2"></i>graduation_* (degree, university, year, %)</li>
                                    <li><i class="fas fa-circle text-xs text-green-500 mr-2"></i>post_graduation_* (degree, university, year, %)</li>
                                    <li><i class="fas fa-circle text-xs text-green-500 mr-2"></i>phd_* (subject, university, year, thesis)</li>
                                </ul>
                            </div>

                            <!-- Staff Table - Experience -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-orange-800 mb-3">Experience Fields</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-orange-500 mr-2"></i>total_experience_years</li>
                                    <li><i class="fas fa-circle text-xs text-orange-500 mr-2"></i>teaching_experience_years</li>
                                    <li><i class="fas fa-circle text-xs text-orange-500 mr-2"></i>administrative_experience_years</li>
                                    <li><i class="fas fa-circle text-xs text-orange-500 mr-2"></i>previous_organizations</li>
                                    <li><i class="fas fa-circle text-xs text-orange-500 mr-2"></i>subjects_taught, classes_handled</li>
                                </ul>
                            </div>

                            <!-- Staff Table - Achievements -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-red-800 mb-3">Achievements & Skills</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-red-500 mr-2"></i>awards_received, publications</li>
                                    <li><i class="fas fa-circle text-xs text-red-500 mr-2"></i>research_papers, conferences_attended</li>
                                    <li><i class="fas fa-circle text-xs text-red-500 mr-2"></i>training_programs, special_skills</li>
                                    <li><i class="fas fa-circle text-xs text-red-500 mr-2"></i>languages_known</li>
                                    <li><i class="fas fa-circle text-xs text-red-500 mr-2"></i>professional_certifications</li>
                                </ul>
                            </div>

                            <!-- Staff Table - Administrative -->
                            <div class="border rounded-lg p-4">
                                <h3 class="font-bold text-lg text-indigo-800 mb-3">Administrative Fields</h3>
                                <ul class="text-sm space-y-1 text-gray-700">
                                    <li><i class="fas fa-circle text-xs text-indigo-500 mr-2"></i>office_location, current_salary</li>
                                    <li><i class="fas fa-circle text-xs text-indigo-500 mr-2"></i>reporting_manager_id</li>
                                    <li><i class="fas fa-circle text-xs text-indigo-500 mr-2"></i>confirmation_date, last_promotion_date</li>
                                    <li><i class="fas fa-circle text-xs text-indigo-500 mr-2"></i>performance_rating</li>
                                    <li><i class="fas fa-circle text-xs text-indigo-500 mr-2"></i>document files, notes</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sample Data Display -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Teacher Profile Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
                            <h2 class="text-xl font-semibold">Teacher Profile</h2>
                        </div>
                        <div class="p-6">
                            <div class="flex flex-col items-center mb-6">
                                <div class="w-24 h-24 rounded-full bg-blue-600 flex items-center justify-center text-white text-2xl font-bold mb-4">
                                    <span id="teacher-initials">TS</span>
                                </div>
                                <h3 id="teacher-name" class="text-xl font-bold text-gray-800">Teacher Sample</h3>
                                <p id="teacher-designation" class="text-blue-600 font-semibold">Computer Science Teacher</p>
                            </div>

                            <div class="space-y-3 text-sm">
                                <div><strong>Employee ID:</strong> <span id="employee-id">EMP0001</span></div>
                                <div><strong>Department:</strong> <span id="department">Academic</span></div>
                                <div><strong>Joining Date:</strong> <span id="joining-date">2020-07-01</span></div>
                                <div><strong>Experience:</strong> <span id="total-experience">10 years</span></div>
                                <div><strong>Phone:</strong> <span id="phone">+91-9876543210</span></div>
                                <div><strong>Email:</strong> <span id="email"><EMAIL></span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Section -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Educational Timeline -->
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
                                <h2 class="text-xl font-semibold flex items-center">
                                    <i class="fas fa-graduation-cap mr-3"></i>
                                    Educational Timeline
                                </h2>
                            </div>
                            <div class="p-6">
                                <div class="relative">
                                    <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                                    <div id="education-timeline" class="space-y-6">
                                        <!-- Timeline items will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Experience Timeline -->
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                            <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
                                <h2 class="text-xl font-semibold flex items-center">
                                    <i class="fas fa-briefcase mr-3"></i>
                                    Professional Experience Timeline
                                </h2>
                            </div>
                            <div class="p-6">
                                <div class="relative">
                                    <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                                    <div id="experience-timeline" class="space-y-6">
                                        <!-- Timeline items will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Skills & Achievements -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-4">
                        <h3 class="text-lg font-semibold">Skills & Achievements</h3>
                    </div>
                    <div class="p-4">
                        <div class="mb-4">
                            <h4 class="font-semibold text-gray-700 mb-2">Special Skills</h4>
                            <div id="skills-display" class="text-sm text-gray-600">
                                <!-- Skills will be populated here -->
                            </div>
                        </div>
                        <div class="mb-4">
                            <h4 class="font-semibold text-gray-700 mb-2">Awards & Recognition</h4>
                            <div id="awards-display" class="text-sm text-gray-600">
                                <!-- Awards will be populated here -->
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-2">Languages Known</h4>
                            <div id="languages-display" class="text-sm text-gray-600">
                                <!-- Languages will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database Query Information -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-gray-600 to-gray-700 text-white p-4">
                        <h3 class="text-lg font-semibold">Database Query</h3>
                    </div>
                    <div class="p-4">
                        <p class="text-sm text-gray-600 mb-3">
                            This demo shows data from the comprehensive teacher query that joins the users and staff tables:
                        </p>
                        <div class="bg-gray-100 p-3 rounded text-xs font-mono">
                            <code>
                                SELECT u.*, s.* FROM users u<br>
                                LEFT JOIN staff s ON u.id = s.user_id<br>
                                WHERE u.role = 'teacher'
                            </code>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">
                            Total fields available: 70+ comprehensive teacher attributes including personal info, education, experience, achievements, and administrative details.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Load sample data on page load
            loadSampleData();

            // Event handlers
            $('#load-sample-data').on('click', loadSampleData);
            $('#refresh-data').on('click', loadSampleData);
        });

        function loadSampleData() {
            // Sample teacher data based on the database structure
            const sampleTeacher = {
                // Users table data
                id: 1,
                username: 'teacher_sample',
                name: 'Dr. Rajesh Kumar',
                full_name: 'Dr. Rajesh Kumar Singh',
                email: '<EMAIL>',
                role: 'teacher',
                subjects: 'Computer Science, Mathematics, Information Technology',
                bio: 'Experienced computer science educator with expertise in programming and digital literacy.',
                date_of_birth: '1985-05-15',
                gender: 'male',
                
                // Staff table data
                employee_id: 'EMP0001',
                designation: 'Senior Computer Science Teacher',
                department: 'Academic - Science & Technology',
                joining_date: '2020-07-01',
                employment_type: 'permanent',
                phone: '+91-9876543210',
                address: 'Teacher Quarters, Block A, School Campus',
                city: 'Chandigarh',
                state: 'Punjab',
                pincode: '160001',
                
                // Educational qualifications
                class_10_board: 'PSEB',
                class_10_year: 2001,
                class_10_percentage: 88.5,
                class_10_school: 'Government Senior Secondary School, Ludhiana',
                
                class_12_board: 'PSEB',
                class_12_year: 2003,
                class_12_percentage: 85.2,
                class_12_school: 'Government Senior Secondary School, Ludhiana',
                class_12_stream: 'Science (PCM)',
                
                graduation_degree: 'Bachelor of Computer Applications (BCA)',
                graduation_university: 'Panjab University, Chandigarh',
                graduation_year: 2006,
                graduation_percentage: 82.1,
                graduation_specialization: 'Computer Science & Programming',
                
                post_graduation_degree: 'Master of Computer Applications (MCA)',
                post_graduation_university: 'Panjab University, Chandigarh',
                post_graduation_year: 2008,
                post_graduation_percentage: 85.7,
                post_graduation_specialization: 'Software Engineering & Database Systems',
                
                phd_subject: 'Computer Science',
                phd_university: 'Punjab Technical University',
                phd_year: 2015,
                phd_thesis_title: 'Machine Learning Applications in Educational Technology',
                
                professional_certifications: 'B.Ed. from Punjab University (2009), Microsoft Certified Educator (2018), Google for Education Certified Trainer (2020)',
                
                // Experience
                total_experience_years: 15,
                teaching_experience_years: 12,
                administrative_experience_years: 3,
                previous_organizations: 'Private Computer Institute, Ludhiana (2008-2012), Government Senior Secondary School, Mohali (2012-2020)',
                subjects_taught: 'Computer Science, Information Technology, Mathematics, Digital Literacy',
                classes_handled: '9th, 10th, 11th, 12th (Science & Commerce)',
                
                // Achievements
                awards_received: 'Best Teacher Award 2022, Excellence in Computer Education 2021, Innovation in Teaching Award 2020',
                publications: 'Research paper on "Digital Learning in Rural Schools" (2021), Article on "Programming Education for Beginners" (2020)',
                training_programs: 'Digital Teaching Methods Workshop (2023), Advanced Programming Techniques Seminar (2022), Educational Technology Conference (2021)',
                special_skills: 'Programming (Python, Java, C++), Web Development, Database Management, Digital Content Creation, Online Teaching Platforms',
                languages_known: 'English, Hindi, Punjabi, Basic French',
                
                // Administrative
                office_location: 'Computer Lab 1, Ground Floor',
                current_salary: 65000,
                performance_rating: 'excellent'
            };

            displayTeacherData(sampleTeacher);
        }

        function displayTeacherData(teacher) {
            // Update basic profile information
            $('#teacher-initials').text(getInitials(teacher.full_name));
            $('#teacher-name').text(teacher.full_name);
            $('#teacher-designation').text(teacher.designation);
            $('#employee-id').text(teacher.employee_id);
            $('#department').text(teacher.department);
            $('#joining-date').text(formatDate(teacher.joining_date));
            $('#total-experience').text(teacher.total_experience_years + ' years');
            $('#phone').text(teacher.phone);
            $('#email').text(teacher.email);

            // Build and display education timeline
            const educationTimeline = [
                {
                    year: teacher.class_10_year,
                    title: 'Class 10th',
                    institution: teacher.class_10_school,
                    details: `${teacher.class_10_board} Board - ${teacher.class_10_percentage}%`,
                    color: 'blue'
                },
                {
                    year: teacher.class_12_year,
                    title: 'Class 12th',
                    institution: teacher.class_12_school,
                    details: `${teacher.class_12_board} Board, ${teacher.class_12_stream} - ${teacher.class_12_percentage}%`,
                    color: 'green'
                },
                {
                    year: teacher.graduation_year,
                    title: teacher.graduation_degree,
                    institution: teacher.graduation_university,
                    details: `${teacher.graduation_specialization} - ${teacher.graduation_percentage}%`,
                    color: 'purple'
                },
                {
                    year: teacher.post_graduation_year,
                    title: teacher.post_graduation_degree,
                    institution: teacher.post_graduation_university,
                    details: `${teacher.post_graduation_specialization} - ${teacher.post_graduation_percentage}%`,
                    color: 'indigo'
                },
                {
                    year: teacher.phd_year,
                    title: `PhD in ${teacher.phd_subject}`,
                    institution: teacher.phd_university,
                    details: `Thesis: ${teacher.phd_thesis_title}`,
                    color: 'red'
                }
            ];

            displayTimeline('education-timeline', educationTimeline);

            // Build and display experience timeline
            const experienceTimeline = [
                {
                    year: 2008,
                    title: 'Computer Instructor',
                    institution: 'Private Computer Institute, Ludhiana',
                    details: '2008-2012 - Teaching basic computer skills and programming',
                    color: 'gray'
                },
                {
                    year: 2012,
                    title: 'Computer Science Teacher',
                    institution: 'Government Senior Secondary School, Mohali',
                    details: '2012-2020 - Teaching 9th-12th grade computer science',
                    color: 'blue'
                },
                {
                    year: 2020,
                    title: teacher.designation,
                    institution: 'Current School',
                    details: '2020-Present - Senior position with administrative responsibilities',
                    color: 'green',
                    isCurrent: true
                }
            ];

            displayTimeline('experience-timeline', experienceTimeline);

            // Display skills and achievements
            displaySkillsAndAchievements(teacher);
        }

        function displayTimeline(containerId, timeline) {
            const container = $(`#${containerId}`);
            container.empty();

            timeline.forEach((item, index) => {
                const timelineItem = $(`
                    <div class="relative flex items-start">
                        <div class="absolute left-6 w-4 h-4 bg-${item.color}-600 rounded-full border-4 border-white shadow-lg ${item.isCurrent ? 'animate-pulse' : ''}"></div>
                        <div class="ml-16">
                            <div class="bg-gradient-to-r from-${item.color}-50 to-${item.color}-100 border-l-4 border-${item.color}-600 rounded-lg p-4">
                                <h4 class="font-bold text-lg text-${item.color}-800">${item.title}</h4>
                                <p class="text-${item.color}-700 font-medium">${item.institution}</p>
                                <p class="text-sm text-${item.color}-600 mt-1">${item.details}</p>
                                <p class="text-sm text-gray-600 mt-2">
                                    <i class="fas fa-calendar mr-1"></i>${item.year}
                                    ${item.isCurrent ? '<span class="ml-2 bg-green-200 text-green-800 text-xs px-2 py-1 rounded-full">Current</span>' : ''}
                                </p>
                            </div>
                        </div>
                    </div>
                `);
                container.append(timelineItem);
            });
        }

        function displaySkillsAndAchievements(teacher) {
            // Skills
            const skills = teacher.special_skills.split(',');
            const skillsHtml = skills.map(skill => 
                `<span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${skill.trim()}</span>`
            ).join('');
            $('#skills-display').html(skillsHtml);

            // Awards
            const awards = teacher.awards_received.split(',');
            const awardsHtml = awards.map(award => 
                `<div class="flex items-start space-x-2 mb-1">
                    <i class="fas fa-trophy text-yellow-600 mt-1"></i>
                    <span class="text-gray-700 text-sm">${award.trim()}</span>
                </div>`
            ).join('');
            $('#awards-display').html(awardsHtml);

            // Languages
            const languages = teacher.languages_known.split(',');
            const languagesHtml = languages.map(language => 
                `<span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${language.trim()}</span>`
            ).join('');
            $('#languages-display').html(languagesHtml);
        }

        function getInitials(name) {
            return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    </script>
</body>
</html>
