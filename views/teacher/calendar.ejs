<%- include('../partials/teacher/header') %>
<!-- Include the enhanced calendar today highlight script -->
<script src="/js/calendar-today-highlight.js"></script>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Academic Calendar</h2>
    </div>

    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <button id="prev-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h3 id="current-month" class="text-xl font-semibold mx-4"></h3>
          <button id="next-month" class="p-2 rounded-full hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
        <button id="today-btn" class="px-3 py-1 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">
          Today
        </button>
      </div>

      <div class="overflow-x-auto">
        <div class="calendar-grid grid grid-cols-7 gap-1">
          <!-- Calendar header -->
          <div class="text-center font-semibold py-2 bg-gray-100">Sun</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Mon</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Tue</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Wed</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Thu</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Fri</div>
          <div class="text-center font-semibold py-2 bg-gray-100">Sat</div>

          <!-- Calendar days will be inserted here by JavaScript -->
          <div id="calendar-days" class="contents"></div>
        </div>
      </div>

      <!-- Legend -->
      <div class="mt-6 flex flex-wrap gap-4">
        <!-- Lectures -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-indigo-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Lecture</span>
        </div>

        <!-- Practicals -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Practical</span>
        </div>

        <!-- Assessments -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-pink-100 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Assessment</span>
        </div>

        <!-- Holidays -->
        <div class="flex items-center">
          <div class="w-4 h-4 bg-red-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">National Holiday</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-amber-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Festival</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-200 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Public Holiday</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 id="modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="modal-content" class="space-y-4">
      <!-- Content will be populated by JavaScript -->
    </div>
    <div class="mt-6 flex justify-end">
      <a id="view-details-btn" href="#" class="px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teacher-primary">
        View Details
      </a>
    </div>
  </div>
</div>

<!-- Day Timetable Modal -->
<div id="day-timetable-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h2 id="day-modal-title" class="text-xl font-bold text-gray-800"></h2>
      <button id="close-day-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div id="day-modal-content" class="space-y-6">
      <!-- Content will be populated by JavaScript -->
      <div id="day-loading" class="py-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teacher-primary"></div>
        <p class="mt-2 text-gray-600">Loading timetable...</p>
      </div>

      <!-- Holidays Section -->
      <div id="day-holidays-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Holidays</h3>
        <div id="day-holidays-content" class="bg-gray-50 rounded-lg p-4"></div>
      </div>

      <!-- Lectures Section -->
      <div id="day-lectures-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Lectures</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="day-lectures-content"></tbody>
          </table>
        </div>
      </div>

      <!-- Tests Section -->
      <div id="day-tests-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Tests</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
              </tr>
            </thead>
            <tbody id="day-tests-content"></tbody>
          </table>
        </div>
      </div>

      <!-- Practicals Section -->
      <div id="day-practicals-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Practicals</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody id="day-practicals-content"></tbody>
          </table>
        </div>
      </div>

      <!-- Assignments Section -->
      <div id="day-assignments-section" class="hidden">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Assignments</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              </tr>
            </thead>
            <tbody id="day-assignments-content"></tbody>
          </table>
        </div>
      </div>

      <!-- No Events Message -->
      <div id="day-no-events" class="hidden py-8 text-center">
        <p class="text-gray-500">No events scheduled for this day.</p>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calendarDays = document.getElementById('calendar-days');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');
    const eventModal = document.getElementById('event-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const viewDetailsBtn = document.getElementById('view-details-btn');

    // Current date
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    // Data from server
    const holidays = <%- JSON.stringify(holidays || []) %>;
    let lectures = [];
    let tests = [];
    let assignments = [];

    // Fetch data and initialize calendar
    fetchCalendarData();

    // Event listeners
    prevMonthBtn.addEventListener('click', () => {
      currentMonth--;
      if (currentMonth < 0) {
        currentMonth = 11;
        currentYear--;
      }
      renderCalendar(currentMonth, currentYear);
    });

    nextMonthBtn.addEventListener('click', () => {
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }
      renderCalendar(currentMonth, currentYear);
    });

    todayBtn.addEventListener('click', () => {
      const today = new Date();
      currentMonth = today.getMonth();
      currentYear = today.getFullYear();
      renderCalendar(currentMonth, currentYear);
    });

    closeModalBtn.addEventListener('click', () => {
      eventModal.classList.add('hidden');
    });

    // Render calendar
    function renderCalendar(month, year) {
      // Clear previous calendar
      calendarDays.innerHTML = '';

      // Set current month display
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      currentMonthEl.textContent = `${monthNames[month]} ${year}`;

      // Get first day of month and total days
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // Previous month's days
      const prevMonthDays = new Date(year, month, 0).getDate();
      for (let i = firstDay - 1; i >= 0; i--) {
        const dayEl = createDayElement(prevMonthDays - i, 'text-gray-400', month === 0 ? 11 : month - 1, month === 0 ? year - 1 : year);
        calendarDays.appendChild(dayEl);
      }

      // Current month's days
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        const dayEl = createDayElement(i, isToday ? 'bg-blue-50 font-bold' : '', month, year);
        calendarDays.appendChild(dayEl);
      }

      // Next month's days
      const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
      const nextMonthDays = totalCells - (firstDay + daysInMonth);
      for (let i = 1; i <= nextMonthDays; i++) {
        const dayEl = createDayElement(i, 'text-gray-400', month === 11 ? 0 : month + 1, month === 11 ? year + 1 : year);
        calendarDays.appendChild(dayEl);
      }
    }

    // Create day element
    function createDayElement(day, extraClasses, month, year) {
      // Format date string for comparison
      const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // Create events container function
      const createEventsContainer = (day, month, year) => {
        // Add events container
        const eventsContainer = document.createElement('div');
        eventsContainer.className = 'mt-1 space-y-1';

        // Get all events for this day
        const dayHolidays = holidays.filter(h => {
          if (!h.holiday_date) return false;

          // Fix for timezone issue - create a date object from the holiday date
          // and compare the year, month, and day directly
          const holidayDate = new Date(h.holiday_date);
          return holidayDate.getFullYear() === year &&
                 holidayDate.getMonth() === month &&
                 holidayDate.getDate() === day;
        });

        // Get lectures for this day
        const dayLectures = lectures.filter(l => {
          if (!l.date) return false;
          return l.date === dateStr;
        });

        // Get tests for this day
        const dayTests = tests.filter(t => {
          if (!t.end_datetime) return false;
          return t.end_datetime.substring(0, 10) === dateStr;
        });

        // Get assignments for this day
        const dayAssignments = assignments.filter(a => {
          if (!a.due_date) return false;
          return a.due_date.substring(0, 10) === dateStr;
        });

        // Add holidays to the events container
        if (dayHolidays.length > 0) {
          dayHolidays.forEach(holiday => {
            const holidayEl = document.createElement('div');

            // Determine color based on holiday type
            let bgColor = 'bg-blue-200'; // Default for Public Holiday
            if (holiday.holiday_type === 'National Holiday') {
              bgColor = 'bg-red-200';
            } else if (holiday.holiday_type === 'Festival') {
              bgColor = 'bg-amber-200';
            }

            holidayEl.className = `${bgColor} text-xs p-1 rounded truncate cursor-pointer font-semibold`;
            holidayEl.textContent = holiday.description;

            // Add click event to show modal
            holidayEl.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent day click event
              showHolidayModal(holiday);
            });

            eventsContainer.appendChild(holidayEl);
          });
        }

        // Add event counts badge if there are any events
        if (dayLectures.length > 0 || dayTests.length > 0 || dayAssignments.length > 0 || dayHolidays.length > 0) {
          // Create a count badge
          const countBadge = document.createElement('div');
          countBadge.className = 'absolute top-0 right-0 mt-1 mr-1 flex items-center space-x-1';

          // Add lecture count if any
          if (dayLectures.length > 0) {
            const lectureBadge = document.createElement('span');
            lectureBadge.className = 'bg-teacher-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            lectureBadge.title = `${dayLectures.length} lecture${dayLectures.length > 1 ? 's' : ''}`;
            lectureBadge.textContent = dayLectures.length;
            countBadge.appendChild(lectureBadge);
          }

          // Add test count if any
          if (dayTests.length > 0) {
            const testBadge = document.createElement('span');
            testBadge.className = 'bg-indigo-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            testBadge.title = `${dayTests.length} test${dayTests.length > 1 ? 's' : ''}`;
            testBadge.textContent = dayTests.length;
            countBadge.appendChild(testBadge);
          }

          // Add assignment count if any
          if (dayAssignments.length > 0) {
            const assignmentBadge = document.createElement('span');
            assignmentBadge.className = 'bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center';
            assignmentBadge.title = `${dayAssignments.length} assignment${dayAssignments.length > 1 ? 's' : ''}`;
            assignmentBadge.textContent = dayAssignments.length;
            countBadge.appendChild(assignmentBadge);
          }

          // Add the badge to the events container
          eventsContainer.appendChild(countBadge);

          return eventsContainer;
        }

        return dayHolidays.length > 0 ? eventsContainer : null;
      };

      // Use the enhanced day element function
      const dayEl = createEnhancedDayElement(day, extraClasses, month, year, createEventsContainer);

      // Add click event to show day timetable
      dayEl.addEventListener('click', () => {
        showDayTimetable(dateStr);
      });

      // Add cursor pointer to indicate clickable
      dayEl.classList.add('cursor-pointer');

      return dayEl;
    }

    // Show holiday modal
    function showHolidayModal(holiday) {
      modalTitle.textContent = holiday.description;

      // Format date
      const holidayDate = new Date(holiday.holiday_date);
      const formattedDate = holidayDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Determine holiday type badge color
      let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
      if (holiday.holiday_type === 'National Holiday') {
        badgeColor = 'bg-red-100 text-red-800';
      } else if (holiday.holiday_type === 'Festival') {
        badgeColor = 'bg-amber-100 text-amber-800';
      }

      // Build modal content
      modalContent.innerHTML = `
        <div>
          <p class="text-sm text-gray-500">Date</p>
          <p class="font-medium">${formattedDate}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">Type</p>
          <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
            ${holiday.holiday_type}
          </span>
        </div>
      `;

      // Hide view details button for holidays
      viewDetailsBtn.style.display = 'none';

      // Show modal
      eventModal.classList.remove('hidden');
    }

    // Day timetable modal elements
    const dayTimetableModal = document.getElementById('day-timetable-modal');
    const closeDayModalBtn = document.getElementById('close-day-modal');
    const dayModalTitle = document.getElementById('day-modal-title');
    const dayLoading = document.getElementById('day-loading');
    const dayHolidaysSection = document.getElementById('day-holidays-section');
    const dayHolidaysContent = document.getElementById('day-holidays-content');
    const dayLecturesSection = document.getElementById('day-lectures-section');
    const dayLecturesContent = document.getElementById('day-lectures-content');
    const dayTestsSection = document.getElementById('day-tests-section');
    const dayTestsContent = document.getElementById('day-tests-content');
    const dayPracticalsSection = document.getElementById('day-practicals-section');
    const dayPracticalsContent = document.getElementById('day-practicals-content');
    const dayAssignmentsSection = document.getElementById('day-assignments-section');
    const dayAssignmentsContent = document.getElementById('day-assignments-content');
    const dayNoEvents = document.getElementById('day-no-events');

    // Close day timetable modal
    closeDayModalBtn.addEventListener('click', () => {
      dayTimetableModal.classList.add('hidden');
    });

    // Format time for display (HH:MM)
    function formatTime(timeStr) {
      if (!timeStr) return '';

      // If it's already in HH:MM format, return as is
      if (timeStr.length === 5) return timeStr;

      // If it's in HH:MM:SS format, remove seconds
      if (timeStr.length === 8) return timeStr.substring(0, 5);

      return timeStr;
    }

    // Show day timetable
    async function showDayTimetable(date) {
      // Reset modal content
      resetDayTimetableModal();

      // Format date for display
      const displayDate = new Date(date);
      const formattedDate = displayDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Set modal title
      dayModalTitle.textContent = `Timetable for ${formattedDate}`;

      // Show modal with loading state
      dayTimetableModal.classList.remove('hidden');
      dayLoading.classList.remove('hidden');

      try {
        // Fetch timetable data for this date
        const response = await fetch(`/api/calendar/day-timetable?date=${date}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch timetable data: ${response.status}`);
        }

        const data = await response.json();

        // Hide loading indicator
        dayLoading.classList.add('hidden');

        // Populate modal with data
        populateDayTimetableModal(data);

      } catch (error) {
        console.error('Error fetching day timetable:', error);

        // Hide loading indicator
        dayLoading.classList.add('hidden');

        // Show error message
        dayNoEvents.classList.remove('hidden');
        dayNoEvents.innerHTML = `
          <p class="text-red-500">Error loading timetable data. Please try again.</p>
          <p class="text-gray-500 text-sm mt-2">${error.message}</p>
        `;
      }
    }

    // Reset day timetable modal
    function resetDayTimetableModal() {
      // Hide all sections
      dayHolidaysSection.classList.add('hidden');
      dayLecturesSection.classList.add('hidden');
      dayTestsSection.classList.add('hidden');
      dayPracticalsSection.classList.add('hidden');
      dayAssignmentsSection.classList.add('hidden');
      dayNoEvents.classList.add('hidden');

      // Clear content
      dayHolidaysContent.innerHTML = '';
      dayLecturesContent.innerHTML = '';
      dayTestsContent.innerHTML = '';
      dayPracticalsContent.innerHTML = '';
      dayAssignmentsContent.innerHTML = '';
    }

    // Fetch calendar data
    async function fetchCalendarData() {
      try {
        // Fetch lectures data
        const lecturesResponse = await fetch('/api/teacher/lectures');
        if (lecturesResponse.ok) {
          const lecturesData = await lecturesResponse.json();
          if (lecturesData.success) {
            lectures = lecturesData.lectures || [];
          }
        }

        // Fetch tests data
        const testsResponse = await fetch('/api/teacher/tests');
        if (testsResponse.ok) {
          const testsData = await testsResponse.json();
          if (testsData.success) {
            tests = testsData.tests || [];
          }
        }

        // Fetch assignments data
        const assignmentsResponse = await fetch('/api/teacher/assignments');
        if (assignmentsResponse.ok) {
          const assignmentsData = await assignmentsResponse.json();
          if (assignmentsData.success) {
            assignments = assignmentsData.assignments || [];
          }
        }
      } catch (error) {
        console.error('Error fetching calendar data:', error);
      } finally {
        // Render calendar with whatever data we have
        renderCalendar(currentMonth, currentYear);
      }
    }

    // Populate day timetable modal with data
    function populateDayTimetableModal(data) {
      let hasEvents = false;

      // Populate holidays
      if (data.holidays && data.holidays.length > 0) {
        hasEvents = true;
        dayHolidaysSection.classList.remove('hidden');

        const holidaysHtml = data.holidays.map(holiday => {
          let badgeColor = 'bg-blue-100 text-blue-800'; // Default for Public Holiday
          if (holiday.holiday_type === 'National Holiday') {
            badgeColor = 'bg-red-100 text-red-800';
          } else if (holiday.holiday_type === 'Festival') {
            badgeColor = 'bg-amber-100 text-amber-800';
          }

          return `
            <div class="mb-2 last:mb-0">
              <div class="font-medium">${holiday.description}</div>
              <span class="px-2 py-1 rounded-full text-xs font-semibold ${badgeColor}">
                ${holiday.holiday_type}
              </span>
            </div>
          `;
        }).join('');

        dayHolidaysContent.innerHTML = holidaysHtml;
      }

      // Populate lectures
      if (data.lectures && data.lectures.length > 0) {
        hasEvents = true;
        dayLecturesSection.classList.remove('hidden');

        const lecturesHtml = data.lectures.map(lecture => {
          // Determine status badge
          let statusBadge = 'bg-blue-100 text-blue-800'; // Default for pending
          let statusText = lecture.status || 'Pending';

          if (statusText.toLowerCase() === 'completed') {
            statusBadge = 'bg-green-100 text-green-800';
          } else if (statusText.toLowerCase() === 'cancelled') {
            statusBadge = 'bg-red-100 text-red-800';
          }

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b">${formatTime(lecture.start_time)} - ${formatTime(lecture.end_time)}</td>
              <td class="py-2 px-4 border-b">
                <div class="font-medium">${lecture.subject_name || 'N/A'}</div>
                <div class="text-xs text-gray-500">${lecture.topic || 'No topic'}</div>
              </td>
              <td class="py-2 px-4 border-b">
                <div>${lecture.class_name || 'N/A'}</div>
                <div class="text-xs text-gray-500">Section ${lecture.section_display || 'N/A'}</div>
              </td>
              <td class="py-2 px-4 border-b">${lecture.location || 'N/A'}</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusBadge}">
                  ${statusText}
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayLecturesContent.innerHTML = lecturesHtml;
      }

      // Populate tests
      if (data.tests && data.tests.length > 0) {
        hasEvents = true;
        dayTestsSection.classList.remove('hidden');

        const testsHtml = data.tests.map(test => {
          // Format date and time
          const dueDate = new Date(test.end_datetime);
          const formattedDueDate = dueDate.toLocaleString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
          });

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b font-medium">${test.exam_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${formattedDueDate}</td>
              <td class="py-2 px-4 border-b">${test.total_questions || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${test.duration || 'N/A'} min</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full bg-indigo-100 text-indigo-800 text-xs font-semibold">
                  ${test.assigned_students || '0'} students
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayTestsContent.innerHTML = testsHtml;
      }

      // Populate practicals
      if (data.practicals && data.practicals.length > 0) {
        hasEvents = true;
        dayPracticalsSection.classList.remove('hidden');

        const practicalsHtml = data.practicals.map(practical => {
          // Determine status badge
          let statusBadge = 'bg-blue-100 text-blue-800'; // Default for pending
          let statusText = practical.status || 'Pending';

          if (statusText.toLowerCase() === 'completed') {
            statusBadge = 'bg-green-100 text-green-800';
          } else if (statusText.toLowerCase() === 'cancelled') {
            statusBadge = 'bg-red-100 text-red-800';
          }

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b">${formatTime(practical.start_time)} - ${formatTime(practical.end_time)}</td>
              <td class="py-2 px-4 border-b font-medium">${practical.practical_topic || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${practical.subject_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${practical.class_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">
                <span class="px-2 py-1 rounded-full text-xs font-semibold ${statusBadge}">
                  ${statusText}
                </span>
              </td>
            </tr>
          `;
        }).join('');

        dayPracticalsContent.innerHTML = practicalsHtml;
      }

      // Populate assignments
      if (data.assignments && data.assignments.length > 0) {
        hasEvents = true;
        dayAssignmentsSection.classList.remove('hidden');

        const assignmentsHtml = data.assignments.map(assignment => {
          // Format date and time
          const dueDate = new Date(assignment.due_date);
          const formattedDueDate = dueDate.toLocaleString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
          });

          return `
            <tr class="hover:bg-gray-50">
              <td class="py-2 px-4 border-b font-medium">${assignment.title || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${formattedDueDate}</td>
              <td class="py-2 px-4 border-b">${assignment.subject_name || 'N/A'}</td>
              <td class="py-2 px-4 border-b">${assignment.class_name || 'N/A'}</td>
            </tr>
          `;
        }).join('');

        dayAssignmentsContent.innerHTML = assignmentsHtml;
      }

      // Show no events message if no events
      if (!hasEvents) {
        dayNoEvents.classList.remove('hidden');
      }
    }
  });
</script>

<%- include('../partials/teacher/footer') %>
