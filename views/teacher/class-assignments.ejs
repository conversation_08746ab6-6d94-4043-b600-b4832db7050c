

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">My Class Assignments</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Current Class Assignments</h3>

        <% if (assignments && assignments.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Class</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Grade</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Trade</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Section</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Theory Lectures</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Practical Lectures</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Lectures</th>
                </tr>
              </thead>
              <tbody>
                <% assignments.forEach(assignment => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.class_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.grade %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.trade %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.section %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.num_theory_lectures %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.num_practical_lectures %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= assignment.total_lectures %></td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You don't have any class assignments yet. Please contact the administrator to assign classes to you.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <% if (isAdmin) { %>
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">Manage Class Assignments (Admin Only)</h3>

          <div class="bg-gray-50 p-4 rounded border border-gray-200">
            <p class="text-sm text-gray-700 mb-4">
              As an administrator, you can manage class assignments for all teachers. This functionality is available in the admin dashboard.
            </p>

            <a href="/admin/class-assignments" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition">
              Go to Admin Class Assignments
            </a>
          </div>
        </div>
      <% } %>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Weekly Lecture Distribution</h2>
    </div>

    <div class="p-4">
      <div class="mb-4">
        <p class="text-sm text-gray-700">
          This chart shows the distribution of your weekly lectures across different subjects and classes.
        </p>
      </div>

      <div class="h-64">
        <canvas id="lectureDistributionChart"></canvas>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Sample data for the chart - in a real implementation, this would come from the server
    const assignments = <%- JSON.stringify(assignments || []) %>;

    if (assignments.length > 0) {
      // Prepare data for the chart
      const subjects = [...new Set(assignments.map(a => a.subject_name))];
      const classes = [...new Set(assignments.map(a => a.class_name))];

      const theoryData = [];
      const practicalData = [];

      subjects.forEach(subject => {
        const subjectAssignments = assignments.filter(a => a.subject_name === subject);
        const theoryLectures = subjectAssignments.reduce((sum, a) => sum + a.num_theory_lectures, 0);
        const practicalLectures = subjectAssignments.reduce((sum, a) => sum + a.num_practical_lectures, 0);

        theoryData.push(theoryLectures);
        practicalData.push(practicalLectures);
      });

      // Create the chart
      const ctx = document.getElementById('lectureDistributionChart').getContext('2d');
      const chart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: subjects,
          datasets: [
            {
              label: 'Theory Lectures',
              data: theoryData,
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            },
            {
              label: 'Practical Lectures',
              data: practicalData,
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Number of Lectures'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Subjects'
              }
            }
          }
        }
      });
    }
  });
</script>


