<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md p-6 overflow-y-auto max-h-[calc(100vh-200px)]">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Class-Subject Summary</h1>
      <p class="text-sm text-gray-600 mt-2">Weekly lecture counts per subject and teacher. Each subject has a specific maximum weekly lecture count per teacher with maximum 8 lectures per day.</p>
    </div>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="py-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-teacher-primary"></div>
      <p class="mt-2 text-gray-600">Loading data...</p>
    </div>

    <!-- Table -->
    <div id="data-container" class="hidden overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 border border-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r border-gray-200">Class</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r border-gray-200">Subject</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r border-gray-200">Teacher</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r border-gray-200">Theory<br>Lectures<br>Per Week</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r border-gray-200">Practical<br>Sessions<br>Per Week</th>
            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">Total<br>Per Week</th>
          </tr>
        </thead>
        <tbody id="data-table-body" class="bg-white divide-y divide-gray-200">
          <!-- Will be populated dynamically -->
        </tbody>
      </table>
    </div>

    <!-- No data message -->
    <div id="no-data-message" class="hidden py-8 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p class="mt-2 text-gray-600">No data available for the selected filters.</p>
    </div>

    <!-- Summary section -->
    <div id="summary-section" class="hidden mt-8 p-4 bg-gray-50 rounded-lg">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Summary</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="summary-cards">
        <!-- Will be populated dynamically -->
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for the page -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the page
  fetchData();
});

// Global variables
let allData = [];

// Fetch data from API
async function fetchData() {
  try {
    console.log('Fetching class-subject data...');

    // Show loading indicator
    document.getElementById('loading-indicator').classList.remove('hidden');
    document.getElementById('data-container').classList.add('hidden');
    document.getElementById('no-data-message').classList.add('hidden');
    document.getElementById('summary-section').classList.add('hidden');

    // Fetch data from API with a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    try {
      const response = await fetch('/api/teacher/class-subject-data', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch data');
      }

      // Store data
      allData = result.data;
      console.log('Data loaded successfully:', allData.length, 'records');

      // Display data
      displayData();

    } catch (fetchError) {
      console.error('Fetch error:', fetchError);

      // If the API call fails, try to generate sample data directly
      console.log('Generating sample data as fallback...');
      generateSampleData();
    }

  } catch (error) {
    console.error('Error in fetchData:', error);
    showErrorMessage(error.message);
  }
}

// Generate sample data as a fallback
function generateSampleData() {
  const classes = [
    '11 Non-Medical A', '11 Non-Medical B',
    '11 Medical A', '11 Medical B',
    '12 Non-Medical A', '12 Non-Medical B',
    '12 Medical A', '12 Medical B'
  ];

  const subjects = {
    'Non-Medical': ['Physics', 'Chemistry', 'Mathematics', 'English', 'Computer Science', 'Punjabi'],
    'Medical': ['Physics', 'Chemistry', 'Biology', 'English', 'Computer Science', 'Punjabi']
  };

  const teachers = {
    'Physics': ['Dr. Sharma', 'Mr. Verma'],
    'Chemistry': ['Mrs. Gupta', 'Dr. Singh'],
    'Mathematics': ['Mr. Kapoor', 'Mrs. Patel'],
    'Biology': ['Dr. Kumar', 'Mrs. Reddy'],
    'English': ['Mrs. Joshi', 'Mr. Malhotra'],
    'Computer Science': ['Mr. Mehta', 'Mrs. Agarwal'],
    'Punjabi': ['Mr. Singh', 'Mrs. Kaur']
  };

  const sampleData = [];

  classes.forEach(className => {
    const streamMatch = className.match(/(Non-Medical|Medical)/);
    const stream = streamMatch ? streamMatch[1] : 'Non-Medical';
    const subjectsForStream = subjects[stream];

    subjectsForStream.forEach(subject => {
      const teachersForSubject = teachers[subject];
      const teacher = teachersForSubject[Math.floor(Math.random() * teachersForSubject.length)];

      // Generate random lecture counts
      const theoryLectures = Math.floor(Math.random() * 20) + 10; // 10-30 theory lectures
      const practicalSessions = ['Physics', 'Chemistry', 'Biology', 'Computer Science'].includes(subject)
        ? Math.floor(Math.random() * 10) + 5 // 5-15 practical sessions for science subjects
        : 0; // No practical sessions for non-science subjects

      sampleData.push({
        class_name: className,
        subject_name: subject,
        teacher_name: teacher,
        theory_lectures: theoryLectures,
        practical_sessions: practicalSessions
      });
    });
  });

  allData = sampleData;
  console.log('Sample data generated:', allData.length, 'records');
  displayData();
}

// Show error message
function showErrorMessage(message) {
  // Hide loading indicator
  document.getElementById('loading-indicator').classList.add('hidden');

  // Show error message
  document.getElementById('no-data-message').classList.remove('hidden');
  document.getElementById('no-data-message').innerHTML = `
    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <p class="mt-2 text-red-600">Error loading data: ${message}</p>
    <button class="mt-4 px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary transition" onclick="fetchData()">
      Try Again
    </button>
  `;
}

// Display data in the table
function displayData() {
  // Hide loading indicator
  document.getElementById('loading-indicator').classList.add('hidden');

  // Check if we have data
  if (!allData || allData.length === 0) {
    document.getElementById('data-container').classList.add('hidden');
    document.getElementById('summary-section').classList.add('hidden');
    document.getElementById('no-data-message').classList.remove('hidden');
    document.getElementById('no-data-message').innerHTML = `
      <svg class="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
      <p class="mt-2 text-gray-600">No data available.</p>
      <button class="mt-4 px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary transition" onclick="fetchData()">
        Try Again
      </button>
    `;
    return;
  }

  // Show data container and summary section
  document.getElementById('data-container').classList.remove('hidden');
  document.getElementById('summary-section').classList.remove('hidden');
  document.getElementById('no-data-message').classList.add('hidden');

  // Add a table showing the maximum weekly lectures per subject
  const maxLecturesTable = document.createElement('div');
  maxLecturesTable.className = 'mb-6 overflow-x-auto';
  maxLecturesTable.innerHTML = `
    <h3 class="text-md font-medium text-gray-700 mb-3 mt-6">Maximum Weekly Lectures Per Subject Per Teacher</h3>
    <table class="min-w-full divide-y divide-gray-200 border border-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Subject</th>
          <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Theory Per Week</th>
          <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Practical Per Week</th>
          <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Total Per Class</th>
          <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Max Weekly Per Teacher</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">English</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">30</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Punjabi</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">30</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Computer Science</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">2</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">2</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">4</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">27</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Physics</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">3</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">14</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Chemistry</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">3</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">14</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Biology</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">3</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">9</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Mathematics</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">27</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Accountancy</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">6</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Business Studies</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">6</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">Economics</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">0</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">6</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">MOP</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">3</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">3</td>
        </tr>
        <tr>
          <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">EBUSINESS</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">6</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">3</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700 border-r border-gray-200">9</td>
          <td class="px-3 py-2 text-sm text-center text-gray-700">3</td>
        </tr>
      </tbody>
    </table>
  `;
  document.getElementById('data-container').after(maxLecturesTable);

  // Populate table
  const tableBody = document.getElementById('data-table-body');
  tableBody.innerHTML = '';

  // Sort data by class, then subject
  const sortedData = [...allData].sort((a, b) => {
    // First sort by class
    const classCompare = a.class_name.localeCompare(b.class_name);
    if (classCompare !== 0) return classCompare;

    // Then by subject
    return a.subject_name.localeCompare(b.subject_name);
  });

  sortedData.forEach(item => {
    const row = document.createElement('tr');
    const total = parseInt(item.theory_lectures) + parseInt(item.practical_sessions);

    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-200">${item.class_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-r border-gray-200">${item.subject_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-r border-gray-200">${item.teacher_name}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center border-r border-gray-200">${item.theory_lectures}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center border-r border-gray-200">${item.practical_sessions}</td>
      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">${total}</td>
    `;

    tableBody.appendChild(row);
  });

  // Generate summary
  generateSummary();
}

// Generate summary statistics
function generateSummary() {
  const summaryContainer = document.getElementById('summary-cards');
  summaryContainer.innerHTML = '';

  // Calculate total lectures by class
  const classTotals = {};
  allData.forEach(item => {
    const className = item.class_name;
    if (!classTotals[className]) {
      classTotals[className] = 0;
    }
    classTotals[className] += parseInt(item.theory_lectures) + parseInt(item.practical_sessions);
  });

  // Calculate total lectures by subject
  const subjectTotals = {};
  allData.forEach(item => {
    const subject = item.subject_name;
    if (!subjectTotals[subject]) {
      subjectTotals[subject] = 0;
    }
    subjectTotals[subject] += parseInt(item.theory_lectures) + parseInt(item.practical_sessions);
  });

  // Calculate total lectures by teacher and track subjects
  const teacherTotals = {};
  const teacherSubjects = {};

  allData.forEach(item => {
    const teacher = item.teacher_name;
    const subject = item.subject_name;

    // Initialize teacher data if not exists
    if (!teacherTotals[teacher]) {
      teacherTotals[teacher] = 0;
      teacherSubjects[teacher] = new Set();
    }

    // Add to total count
    teacherTotals[teacher] += parseInt(item.theory_lectures) + parseInt(item.practical_sessions);

    // Add subject to teacher's subjects
    teacherSubjects[teacher].add(subject);
  });

  // Create summary cards

  // Class summary
  const classCard = document.createElement('div');
  classCard.className = 'bg-white p-4 rounded-lg shadow';
  classCard.innerHTML = `
    <h3 class="text-md font-medium text-gray-700 mb-3">Lectures Per Week by Class</h3>
    <div class="max-h-60 overflow-y-auto">
      <ul class="space-y-2">
        ${Object.entries(classTotals)
          .sort((a, b) => a[0].localeCompare(b[0])) // Sort alphabetically by class name
          .map(([className, total]) => `
            <li class="flex justify-between items-center">
              <span class="text-sm text-gray-600">${className}</span>
              <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">${total}</span>
            </li>
          `).join('')}
      </ul>
    </div>
  `;
  summaryContainer.appendChild(classCard);

  // Subject summary
  const subjectCard = document.createElement('div');
  subjectCard.className = 'bg-white p-4 rounded-lg shadow';
  subjectCard.innerHTML = `
    <h3 class="text-md font-medium text-gray-700 mb-3">Lectures Per Week by Subject</h3>
    <div class="max-h-60 overflow-y-auto">
      <ul class="space-y-2">
        ${Object.entries(subjectTotals)
          .sort((a, b) => a[0].localeCompare(b[0])) // Sort alphabetically by subject name
          .map(([subject, total]) => `
            <li class="flex justify-between items-center">
              <span class="text-sm text-gray-600">${subject}</span>
              <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">${total}</span>
            </li>
          `).join('')}
      </ul>
    </div>
  `;
  summaryContainer.appendChild(subjectCard);

  // Create a more detailed teacher summary with subject breakdown
  const teacherCard = document.createElement('div');
  teacherCard.className = 'bg-white p-4 rounded-lg shadow';

  // Create a table of teachers and their subjects with lecture counts
  const teacherSubjectData = [];

  // Process data to get teacher-subject-count relationships
  allData.forEach(item => {
    const teacher = item.teacher_name;
    const subject = item.subject_name;
    const lectureCount = parseInt(item.theory_lectures) + parseInt(item.practical_sessions);

    // Find if we already have this teacher-subject pair
    const existingEntry = teacherSubjectData.find(entry =>
      entry.teacher === teacher && entry.subject === subject
    );

    if (existingEntry) {
      // Update existing entry
      existingEntry.count += lectureCount;
    } else {
      // Add new entry
      teacherSubjectData.push({
        teacher,
        subject,
        count: lectureCount
      });
    }
  });

  // Sort by teacher name, then by subject
  teacherSubjectData.sort((a, b) => {
    if (a.teacher !== b.teacher) {
      return a.teacher.localeCompare(b.teacher);
    }
    return a.subject.localeCompare(b.subject);
  });

  // Group by teacher
  const teacherGroups = {};
  teacherSubjectData.forEach(entry => {
    if (!teacherGroups[entry.teacher]) {
      teacherGroups[entry.teacher] = [];
    }
    teacherGroups[entry.teacher].push(entry);
  });

  teacherCard.innerHTML = `
    <h3 class="text-md font-medium text-gray-700 mb-3">Lectures Per Week by Teacher</h3>
    <div class="max-h-60 overflow-y-auto">
      <ul class="space-y-4">
        ${Object.entries(teacherGroups)
          .map(([teacher, entries]) => {
            // Calculate total for this teacher
            const teacherTotal = entries.reduce((sum, entry) => sum + entry.count, 0);

            return `
              <li class="border-b border-gray-100 pb-3 last:border-b-0">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700">${teacher}</span>
                  <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">${teacherTotal}</span>
                </div>
                <table class="w-full text-xs">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-2 py-1 text-left text-gray-500">Subject</th>
                      <th class="px-2 py-1 text-center text-gray-500">Lectures Per Week</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${entries.map(entry => `
                      <tr>
                        <td class="px-2 py-1 text-gray-600">${entry.subject}</td>
                        <td class="px-2 py-1 text-center text-gray-600">${entry.count}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </li>
            `;
          }).join('')}
      </ul>
    </div>
  `;
  summaryContainer.appendChild(teacherCard);
}
</script>


