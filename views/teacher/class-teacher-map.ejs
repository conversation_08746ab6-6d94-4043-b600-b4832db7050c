<%- include('../partials/teacher/header') %>

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Class-Subject-Teacher Mind Map</h1>
    
    <div class="mb-4">
      <p class="text-gray-600">This visualization shows which classes are taught by which teachers for each subject.</p>
    </div>
    
    <!-- Visualization container -->
    <div id="mind-map-container" class="w-full h-[600px] border border-gray-200 rounded-lg bg-gray-50">
      <!-- D3 visualization will be rendered here -->
      <div class="flex items-center justify-center h-full" id="loading-indicator">
        <p class="text-gray-500">Loading visualization...</p>
      </div>
    </div>
    
    <!-- Legend -->
    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-medium text-gray-700 mb-2">Legend</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
        <div class="flex items-center">
          <div class="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
          <span class="text-sm">Class Node</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
          <span class="text-sm">Subject Node</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 rounded-full bg-purple-500 mr-2"></div>
          <span class="text-sm">Teacher Node</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include D3.js library -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<!-- Mind map script -->
<script src="/js/teacher/class-teacher-map.js"></script>

<%- include('../partials/teacher/footer') %>
