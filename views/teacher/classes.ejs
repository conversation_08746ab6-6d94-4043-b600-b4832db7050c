

<div class="container mx-auto px-4 py-6">
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
      <h1 class="text-xl font-semibold">My Classes</h1>
      <div class="flex space-x-2">
        <button id="print-classes" class="bg-white text-teacher-primary px-3 py-1 rounded text-sm hover:bg-gray-100 transition flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
          </svg>
          Print
        </button>
        <button id="export-classes" class="bg-white text-teacher-primary px-3 py-1 rounded text-sm hover:bg-gray-100 transition flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
          </svg>
          Export
        </button>
      </div>
    </div>

    <div class="p-6">
      <!-- Teacher Info -->
      <div class="mb-6 pb-4 border-b border-gray-200">
        <div class="flex items-center">
          <div class="w-16 h-16 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center mr-4">
            <% if (teacher.profile_image) { %>
              <img src="<%= teacher.profile_image %>" alt="<%= teacher.full_name %>" class="w-full h-full object-cover">
            <% } else { %>
              <div class="text-2xl font-bold text-gray-400"><%= teacher.full_name.charAt(0) %></div>
            <% } %>
          </div>
          <div>
            <h2 class="text-xl font-bold text-gray-800"><%= teacher.full_name %></h2>
            <p class="text-gray-600">Teacher ID: <%= teacher.id %></p>
            <p class="text-gray-600">Email: <%= teacher.email %></p>
          </div>
        </div>
      </div>

      <!-- Filter Controls -->
      <div class="mb-6">
        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
          <h3 class="text-lg font-medium text-gray-800 mb-3">Filter Classes</h3>
          <div class="flex flex-wrap gap-4">
            <div class="w-full md:w-auto">
              <label for="class-filter" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
              <select id="class-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="">All Classes</option>
                <%
                  // Get unique class names
                  const uniqueClasses = [...new Set(classes.map(cls => cls.class_name))].sort();
                  uniqueClasses.forEach(className => {
                %>
                  <option value="<%= className %>"><%= className %></option>
                <% }); %>
              </select>
            </div>
            <div class="w-full md:w-auto">
              <label for="subject-filter" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
              <select id="subject-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="">All Subjects</option>
                <%
                  // Create a map of subjects with their primary/secondary status
                  const subjectMap = new Map();
                  classes.forEach(cls => {
                    // Get subjects from both primary and secondary lists
                    if (cls.primary_subjects) {
                      cls.primary_subjects.forEach(subj => {
                        subjectMap.set(subj.subject_name, true); // true = primary
                      });
                    }
                    if (cls.secondary_subjects) {
                      cls.secondary_subjects.forEach(subj => {
                        // Only set to secondary if not already set to primary
                        if (!subjectMap.has(subj.subject_name)) {
                          subjectMap.set(subj.subject_name, false); // false = secondary
                        }
                      });
                    }
                  });

                  // Sort subjects alphabetically
                  const sortedSubjects = Array.from(subjectMap.keys()).sort();

                  // First add primary subjects
                  if (isComputerTeacher) {
                    // Add a group for primary subjects
                    %>
                    <optgroup label="Primary Subjects">
                    <%
                    sortedSubjects.forEach(subject => {
                      if (subjectMap.get(subject) === true) { // Primary subject
                    %>
                      <option value="<%= subject %>"><%= subject %></option>
                    <%
                      }
                    });
                    %>
                    </optgroup>
                    <optgroup label="Secondary Subjects">
                    <%
                    // Then add secondary subjects
                    sortedSubjects.forEach(subject => {
                      if (subjectMap.get(subject) === false) { // Secondary subject
                    %>
                      <option value="<%= subject %>"><%= subject %></option>
                    <%
                      }
                    });
                    %>
                    </optgroup>
                    <%
                  } else {
                    // For non-computer teachers, just list all subjects
                    sortedSubjects.forEach(subject => {
                    %>
                      <option value="<%= subject %>"><%= subject %></option>
                    <%
                    });
                  }
                %>
              </select>
            </div>
            <% if (isComputerTeacher) { %>
            <div class="w-full md:w-auto">
              <label for="subject-type-filter" class="block text-sm font-medium text-gray-700 mb-1">Subject Type</label>
              <select id="subject-type-filter" class="w-full md:w-48 rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="">All Types</option>
                <option value="primary">Primary Subjects</option>
                <option value="secondary">Secondary Subjects</option>
              </select>
            </div>
            <% } %>
            <div class="w-full md:w-auto flex items-end">
              <button id="apply-filters" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition mr-2">
                Apply Filters
              </button>
              <button id="reset-filters" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">
                Reset
              </button>
            </div>
          </div>
        </div>

        <!-- Active Filters Display -->
        <div id="active-filters" class="hidden mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            <h3 class="text-sm font-medium text-blue-800">Active Filters:</h3>
            <div id="filter-badges" class="flex flex-wrap ml-2">
              <!-- Filter badges will be added here dynamically -->
            </div>
          </div>
        </div>
      </div>

      <% if (classes.length === 0) { %>
        <!-- No Classes Found Message -->
        <div class="text-center py-12 bg-white rounded-lg shadow">
          <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          <h3 class="mt-4 text-lg font-medium text-gray-900">No Classes Assigned</h3>
          <p class="mt-2 text-base text-gray-500 max-w-md mx-auto">
            You don't have any classes assigned to you yet. Please contact your administrator to get classes assigned.
          </p>
          <div class="mt-6">
            <a href="/teacher/dashboard" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teacher-primary hover:bg-teacher-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teacher-primary">
              <svg class="mr-2 -ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
              Return to Dashboard
            </a>
          </div>
        </div>
      <% } else { %>
        <!-- Classes Table -->
        <div class="overflow-x-auto">
          <table id="classes-table" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students (Boys/Girls)</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classroom</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lectures/Week (Theory + Practical)</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Incharge</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% classes.forEach(cls => { %>
                <%
                  // Calculate total subjects for rowspan
                  const totalSubjects = (cls.primary_subjects ? cls.primary_subjects.length : 0) +
                                       (cls.secondary_subjects ? cls.secondary_subjects.length : 0);
                  let subjectIndex = 0;

                  // Function to render a subject row
                  const renderSubjectRow = (subject, isPrimary, isFirstRow) => {
                    const rowClass = isPrimary ? 'primary-subject' : 'secondary-subject';
                    const subjectType = isPrimary ? 'primary' : 'secondary';
                %>
                  <tr class="class-row <%= rowClass %>" data-class="<%= cls.class_name %>" data-subject="<%= subject.subject_name %>" data-subject-type="<%= subjectType %>">
                    <% if (isFirstRow) { %>
                      <td class="px-6 py-4 whitespace-nowrap" rowspan="<%= totalSubjects %>">
                        <div class="text-sm font-medium text-gray-900"><%= cls.class_name %></div>
                        <% if (cls.is_class_incharge) { %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                            Class Incharge
                          </span>
                        <% } %>
                      </td>
                    <% } %>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="text-sm <%= isPrimary ? 'font-semibold text-gray-900' : 'text-gray-700' %>">
                          <%= subject.subject_name %>
                          <% if (subject.subject_code) { %>
                            <span class="text-xs text-gray-500">(<%= subject.subject_code %>)</span>
                          <% } %>
                        </div>
                        <% if (isComputerTeacher) { %>
                          <% if (isPrimary) { %>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Primary
                            </span>
                          <% } else { %>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Secondary
                            </span>
                          <% } %>
                        <% } %>
                      </div>
                    </td>
                    <% if (isFirstRow) { %>
                      <td class="px-6 py-4 whitespace-nowrap" rowspan="<%= totalSubjects %>">
                        <div class="text-sm text-gray-900"><%= cls.total_students %> (<%= cls.boys_count %>/<%= cls.girls_count %>)</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap" rowspan="<%= totalSubjects %>">
                        <div class="text-sm text-gray-900"><%= cls.classroom_number %></div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap" rowspan="<%= totalSubjects %>">
                        <div class="text-sm text-gray-900"><%= cls.session %></div>
                      </td>
                    <% } %>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        <%= subject.total_lectures %> (<%= subject.theory_lectures %> + <%= subject.practical_lectures %>)
                      </div>
                    </td>
                    <% if (isFirstRow) { %>
                      <td class="px-6 py-4 whitespace-nowrap" rowspan="<%= totalSubjects %>">
                        <div class="flex items-center">
                          <% if (cls.incharge_profile_image) { %>
                            <div class="flex-shrink-0 h-8 w-8 mr-2">
                              <img class="h-8 w-8 rounded-full" src="<%= cls.incharge_profile_image %>" alt="<%= cls.incharge_teacher_name %>">
                            </div>
                          <% } else { %>
                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-teacher-primary text-white flex items-center justify-center mr-2">
                              <span class="text-sm font-medium"><%= cls.incharge_teacher_name ? cls.incharge_teacher_name.charAt(0) : 'N' %></span>
                            </div>
                          <% } %>
                          <div>
                            <div class="text-sm font-medium text-gray-900"><%= cls.incharge_teacher_name || 'Not Assigned' %></div>
                            <% if (cls.incharge_teacher_id === teacher.id) { %>
                              <span class="text-xs text-gray-500">(You)</span>
                            <% } else if (cls.incharge_email) { %>
                              <div class="text-xs text-gray-500"><%= cls.incharge_email %></div>
                            <% } %>
                          </div>
                        </div>
                      </td>
                    <% } %>
                  </tr>
                <%
                  };

                  // First render primary subjects
                  if (cls.primary_subjects && cls.primary_subjects.length > 0) {
                    cls.primary_subjects.forEach((subject, i) => {
                      renderSubjectRow(subject, true, subjectIndex === 0);
                      subjectIndex++;
                    });
                  }

                  // Then render secondary subjects
                  if (cls.secondary_subjects && cls.secondary_subjects.length > 0) {
                    cls.secondary_subjects.forEach((subject, i) => {
                      renderSubjectRow(subject, false, subjectIndex === 0);
                      subjectIndex++;
                    });
                  }
                %>
              <% }); %>
            </tbody>
          </table>
        </div>

        <!-- No Results Message (for filtered results) -->
        <div id="no-results-message" class="hidden text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No classes match your filters</h3>
          <p class="mt-1 text-sm text-gray-500">Try changing your filter criteria or click "Reset" to see all classes.</p>
          <button id="clear-filters-btn" class="mt-4 px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">
            Clear All Filters
          </button>
        </div>
      <% } %>
    </div>
  </div>
</div>

<!-- Print Template -->
<div id="print-template" class="hidden">
  <div class="print-header">
    <h1>Teacher Classes Report</h1>
    <p>Teacher: <%= teacher.full_name %></p>
    <p>Date: <span id="print-date"></span></p>
  </div>
  <table id="print-table"></table>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const classFilter = document.getElementById('class-filter');
    const subjectFilter = document.getElementById('subject-filter');
    const subjectTypeFilter = document.getElementById('subject-type-filter');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    const activeFiltersDiv = document.getElementById('active-filters');
    const filterBadgesDiv = document.getElementById('filter-badges');
    const noResultsMessage = document.getElementById('no-results-message');
    const classRows = document.querySelectorAll('.class-row');

    // Check if we have the necessary elements to apply filters
    if (!classFilter || !subjectFilter || !applyFiltersBtn || !resetFiltersBtn) {
      console.log('Filter elements not found, skipping filter initialization');
      return;
    }

    // Function to create a filter badge
    function createFilterBadge(type, value, displayText) {
      const badge = document.createElement('div');
      badge.className = 'flex items-center bg-blue-100 text-blue-800 text-xs font-medium mr-2 mb-1 px-2.5 py-0.5 rounded-full';
      badge.dataset.filterType = type;
      badge.dataset.filterValue = value;

      badge.innerHTML = `
        <span class="mr-1 font-semibold">${type}:</span>
        <span>${displayText}</span>
        <button class="ml-1 text-blue-500 hover:text-blue-700" data-remove-filter="${type}">
          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
        </button>
      `;

      // Add event listener to remove button
      const removeBtn = badge.querySelector('[data-remove-filter]');
      removeBtn.addEventListener('click', function() {
        removeFilter(type);
      });

      return badge;
    }

    // Function to update active filters display
    function updateActiveFilters() {
      // Clear existing badges
      filterBadgesDiv.innerHTML = '';

      let hasActiveFilters = false;

      // Add class filter badge if selected
      if (classFilter.value) {
        filterBadgesDiv.appendChild(createFilterBadge('Class', classFilter.value, classFilter.value));
        hasActiveFilters = true;
      }

      // Add subject filter badge if selected
      if (subjectFilter.value) {
        filterBadgesDiv.appendChild(createFilterBadge('Subject', subjectFilter.value, subjectFilter.value));
        hasActiveFilters = true;
      }

      // Add subject type filter badge if selected
      if (subjectTypeFilter && subjectTypeFilter.value) {
        const displayText = subjectTypeFilter.value === 'primary' ? 'Primary' : 'Secondary';
        filterBadgesDiv.appendChild(createFilterBadge('Type', subjectTypeFilter.value, displayText));
        hasActiveFilters = true;
      }

      // Show/hide active filters section
      if (hasActiveFilters) {
        activeFiltersDiv.classList.remove('hidden');
      } else {
        activeFiltersDiv.classList.add('hidden');
      }
    }

    // Function to remove a specific filter
    function removeFilter(type) {
      switch (type) {
        case 'Class':
          classFilter.value = '';
          break;
        case 'Subject':
          subjectFilter.value = '';
          break;
        case 'Type':
          if (subjectTypeFilter) {
            subjectTypeFilter.value = '';
          }
          break;
      }

      applyFilters();
    }

    function applyFilters() {
      // Check if we have any classes to filter
      if (classRows.length === 0) {
        // No classes to filter, so just update the active filters display
        updateActiveFilters();
        return;
      }

      const selectedClass = classFilter.value;
      const selectedSubject = subjectFilter.value;
      const selectedSubjectType = subjectTypeFilter ? subjectTypeFilter.value : '';

      // Update active filters display
      updateActiveFilters();

      // First, hide all rows
      classRows.forEach(row => {
        row.classList.add('hidden');
      });

      // Then show matching rows
      let matchCount = 0;
      classRows.forEach(row => {
        const rowClass = row.getAttribute('data-class');
        const rowSubject = row.getAttribute('data-subject');
        const rowSubjectType = row.getAttribute('data-subject-type');

        const classMatch = !selectedClass || rowClass === selectedClass;
        const subjectMatch = !selectedSubject || rowSubject === selectedSubject;
        const typeMatch = !selectedSubjectType || rowSubjectType === selectedSubjectType;

        if (classMatch && subjectMatch && typeMatch) {
          row.classList.remove('hidden');
          matchCount++;

          // If this is the first visible row for a class, make sure the rowspan cells are visible
          if (row.querySelector('td[rowspan]')) {
            // This row already has rowspan cells, so it's fine
          } else {
            // Find the first row for this class that has rowspan cells
            const firstRowWithRowspan = document.querySelector(`tr.class-row[data-class="${rowClass}"]:not(.hidden) td[rowspan]`);
            if (!firstRowWithRowspan) {
              // If no visible row has rowspan cells, find the first row for this class and show it
              const firstRow = document.querySelector(`tr.class-row[data-class="${rowClass}"] td[rowspan]`);
              if (firstRow) {
                firstRow.closest('tr').classList.remove('hidden');
              }
            }
          }
        }
      });

      // Get the classes table element
      const classesTable = document.getElementById('classes-table');

      // Show/hide no results message
      if (matchCount === 0 && (selectedClass || selectedSubject || selectedSubjectType)) {
        if (classesTable) classesTable.classList.add('hidden');
        if (noResultsMessage) noResultsMessage.classList.remove('hidden');
      } else {
        if (classesTable) classesTable.classList.remove('hidden');
        if (noResultsMessage) noResultsMessage.classList.add('hidden');
      }
    }

    // Apply filters button
    applyFiltersBtn.addEventListener('click', applyFilters);

    // Reset filters button
    resetFiltersBtn.addEventListener('click', function() {
      classFilter.value = '';
      subjectFilter.value = '';
      if (subjectTypeFilter) {
        subjectTypeFilter.value = '';
      }
      applyFilters();
    });

    // Clear filters button in no results message
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', function() {
        classFilter.value = '';
        subjectFilter.value = '';
        if (subjectTypeFilter) {
          subjectTypeFilter.value = '';
        }
        applyFilters();
      });
    }

    // Initialize with no filters
    applyFilters();

    // Print functionality
    const printClassesBtn = document.getElementById('print-classes');
    if (printClassesBtn) {
      printClassesBtn.addEventListener('click', function() {
        // Check if we have classes to print
        if (classRows.length === 0) {
          alert('No classes to print');
          return;
        }

        const printWindow = window.open('', '_blank');
        const printTemplate = document.getElementById('print-template');
        if (!printTemplate) {
          console.error('Print template not found');
          return;
        }

        const printTemplateClone = printTemplate.cloneNode(true);
        const printTable = printTemplateClone.querySelector('#print-table');
        const classesTable = document.getElementById('classes-table');

        if (!classesTable || !printTable) {
          console.error('Classes table or print table not found');
          return;
        }

        const originalTable = classesTable.cloneNode(true);

        // Set current date
        const printDate = printTemplateClone.querySelector('#print-date');
        if (printDate) {
          printDate.textContent = new Date().toLocaleDateString();
        }

        // Copy table content
        printTable.innerHTML = originalTable.innerHTML;

        // Create print document
        printWindow.document.write(`
          <html>
            <head>
              <title>Teacher Classes Report</title>
              <style>
                body { font-family: Arial, sans-serif; }
                .print-header { text-align: center; margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
              </style>
            </head>
            <body>
              ${printTemplateClone.innerHTML}
            </body>
          </html>
        `);

        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      });
    }

    // Export functionality
    const exportClassesBtn = document.getElementById('export-classes');
    if (exportClassesBtn) {
      exportClassesBtn.addEventListener('click', function() {
        // Check if we have classes to export
        if (classRows.length === 0) {
          alert('No classes to export');
          return;
        }

        // Create CSV content
        let csv = 'Class,Subject,Type,Students (Boys/Girls),Classroom,Session,Lectures/Week (Theory + Practical),Class Incharge\n';

        // Get visible rows only if filters are applied
        const hasFilters = classFilter.value || subjectFilter.value || (subjectTypeFilter && subjectTypeFilter.value);
        const rows = hasFilters
          ? document.querySelectorAll('tr.class-row:not(.hidden)')
          : document.querySelectorAll('tr.class-row');

        if (rows.length === 0) {
          alert('No classes match the current filters');
          return;
        }

        // Process each visible row
        rows.forEach(row => {
          const className = row.getAttribute('data-class');
          const subjectName = row.getAttribute('data-subject');
          const subjectType = row.getAttribute('data-subject-type') === 'primary' ? 'Primary' : 'Secondary';

          // Get subject details from the row
          const subjectCell = row.querySelector('td:nth-child(2)');
          if (!subjectCell) return;

          const subjectText = subjectCell.textContent.trim();
          const subjectCode = subjectText.match(/\(([^)]+)\)/) ? subjectText.match(/\(([^)]+)\)/)[1] : '';

          // Get student count from the first row of each class
          let studentsCount = '';
          let classroom = '';
          let session = '';
          let incharge = '';

          // Find the row with rowspan for this class
          const rowspanCell = document.querySelector(`tr.class-row[data-class="${className}"] td[rowspan]`);
          if (rowspanCell) {
            const rowspanRow = rowspanCell.closest('tr');
            if (rowspanRow) {
              const studentCell = rowspanRow.querySelector('td:nth-child(3)');
              const classroomCell = rowspanRow.querySelector('td:nth-child(4)');
              const sessionCell = rowspanRow.querySelector('td:nth-child(5)');
              const inchargeCell = rowspanRow.querySelector('td:nth-child(7)');

              studentsCount = studentCell ? studentCell.textContent.trim() : '';
              classroom = classroomCell ? classroomCell.textContent.trim() : '';
              session = sessionCell ? sessionCell.textContent.trim() : '';
              incharge = inchargeCell ? inchargeCell.textContent.trim().replace(/\s+/g, ' ') : 'Not Assigned';
            }
          }

          // Get lectures count from the current row
          const lecturesCell = row.querySelector('td:nth-child(' + (row.querySelector('td[rowspan]') ? '6' : '2') + ')');
          const lectures = lecturesCell ? lecturesCell.textContent.trim() : '';

          // Add to CSV
          csv += `"${className}","${subjectName} (${subjectCode})","${subjectType}","${studentsCount}","${classroom}","${session}","${lectures}","${incharge}"\n`;
        });

        // Create download link
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.setAttribute('hidden', '');
        a.setAttribute('href', url);
        a.setAttribute('download', 'teacher_classes.csv');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      });
    }
  });
</script>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 py-4 mt-auto">
  <div class="container mx-auto px-4 text-center text-gray-600 text-sm">
    &copy; <%= new Date().getFullYear() %> Meritorious Exam Preparation Platform. All rights reserved.
  </div>
</footer>

<!-- Session Timeout Modal -->
<div id="session-timeout-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Session Timeout</h2>
    <p class="text-gray-600 mb-6">Your session will expire in <span id="timeout-countdown">15</span> seconds due to inactivity.</p>
    <div class="flex justify-end space-x-4">
      <button id="logout-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Logout</button>
      <button id="continue-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Continue Session</button>
    </div>
  </div>
</div>

<script>
  // Initialize session timeout
  function initSessionTimeout() {
    const timeoutMinutes = 15;
    const warningSeconds = 15;
    let timeoutTimer;
    let countdownTimer;
    let countdownSeconds = warningSeconds;

    // Elements
    const modal = document.getElementById('session-timeout-modal');
    const countdown = document.getElementById('timeout-countdown');
    const continueBtn = document.getElementById('continue-btn');
    const logoutBtn = document.getElementById('logout-btn');

    // Reset the timer
    function resetTimer() {
      clearTimeout(timeoutTimer);
      timeoutTimer = setTimeout(showWarning, timeoutMinutes * 60 * 1000);
    }

    // Show warning modal
    function showWarning() {
      modal.classList.remove('hidden');
      countdownSeconds = warningSeconds;
      countdown.textContent = countdownSeconds;

      countdownTimer = setInterval(() => {
        countdownSeconds--;
        countdown.textContent = countdownSeconds;

        if (countdownSeconds <= 0) {
          clearInterval(countdownTimer);
          window.location.href = '/logout';
        }
      }, 1000);
    }

    // Continue session
    if (continueBtn) {
      continueBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
        clearInterval(countdownTimer);
        resetTimer();
      });
    }

    // Logout
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        window.location.href = '/logout';
      });
    }

    // Reset timer on user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, resetTimer, false);
    });

    // Start the timer
    resetTimer();
  }

  document.addEventListener('DOMContentLoaded', function() {
    initSessionTimeout();
  });
</script>
</body>
</html>
