<!-- Teacher Dashboard -->

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
  <!-- Today's Lectures Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-blue-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Today's Lectures</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.todayLectures || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Pending Topics Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-yellow-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Pending Topics</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.pendingTopics || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Upcoming Practicals Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-green-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Upcoming Practicals</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.upcomingPracticals || 0 %></h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Syllabus Progress Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
    <div class="p-6">
      <div class="flex items-center">
        <div class="bg-purple-100 rounded-full p-3 mr-4">
          <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
          </svg>
        </div>
        <div>
          <p class="text-gray-500 text-sm">Syllabus Progress</p>
          <h3 class="text-2xl font-bold text-gray-800"><%= stats.syllabusProgress || 0 %>%</h3>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Today's Timetable -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden lg:col-span-2">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Today's Timetable</h2>
    </div>
    <div class="p-6">
      <% if (todayTimetable && todayTimetable.length > 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% todayTimetable.forEach(lecture => { %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= lecture.start_time %> - <%= lecture.end_time %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= lecture.class_name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= lecture.subject_name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <% if (lecture.status === 'delivered') { %>
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Delivered
                      </span>
                    <% } else if (lecture.status === 'rescheduled') { %>
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Rescheduled
                      </span>
                    <% } else { %>
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                        Pending
                      </span>
                    <% } %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <% if (lecture.status !== 'delivered') { %>
                      <button class="text-teacher-primary hover:text-teacher-secondary mr-2" onclick="markAsDelivered(<%= lecture.id %>)">
                        Mark Delivered
                      </button>
                    <% } %>
                    <% if (lecture.status !== 'rescheduled') { %>
                      <button class="text-yellow-600 hover:text-yellow-800" onclick="reschedule(<%= lecture.id %>)">
                        Reschedule
                      </button>
                    <% } %>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      <% } else { %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No lectures scheduled for today</h3>
          <p class="mt-1 text-sm text-gray-500">Check your weekly timetable for upcoming lectures.</p>
        </div>
      <% } %>
      <div class="mt-4 text-right">
        <a href="/teacher/timetable" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teacher-primary hover:bg-teacher-secondary transition">
          View Full Timetable
          <svg class="ml-2 -mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>
    </div>
  </div>

  <!-- Syllabus Progress -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Syllabus Progress</h2>
    </div>
    <div class="p-6">
      <% if (syllabusProgress && syllabusProgress.length > 0) { %>
        <div class="space-y-6">
          <% syllabusProgress.forEach(subject => { %>
            <div>
              <div class="flex justify-between items-center mb-1">
                <h3 class="text-sm font-medium text-gray-700"><%= subject.name %></h3>
                <span class="text-sm font-medium text-gray-700"><%= subject.completed %>/<%= subject.total %> topics</span>
              </div>
              <div class="progress-container">
                <div class="progress-bar bg-<%= subject.progress >= 75 ? 'green' : (subject.progress >= 50 ? 'blue' : (subject.progress >= 25 ? 'yellow' : 'red')) %>-500" style="width: <%= subject.progress %>%"></div>
              </div>
              <div class="mt-1 text-xs text-gray-500 text-right"><%= subject.progress %>% complete</div>
            </div>
          <% }); %>
        </div>
      <% } else { %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No syllabus data available</h3>
          <p class="mt-1 text-sm text-gray-500">Add subjects and topics to track syllabus progress.</p>
        </div>
      <% } %>
      <div class="mt-4 text-right">
        <a href="/teacher/syllabus" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teacher-primary hover:bg-teacher-secondary transition">
          View Syllabus Details
          <svg class="ml-2 -mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Quick Actions</h2>
  </div>
  <div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <a href="/teacher/lectures/mark" class="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
        </svg>
        <span class="text-blue-800 font-medium">Mark Lecture</span>
      </a>

      <a href="/teacher/syllabus/update" class="bg-yellow-50 hover:bg-yellow-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <span class="text-yellow-800 font-medium">Update Syllabus</span>
      </a>

      <a href="/teacher/practicals/schedule" class="bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
        </svg>
        <span class="text-green-800 font-medium">Schedule Practical</span>
      </a>

      <a href="/teacher/practicals/records" class="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="text-purple-800 font-medium">Student Records</span>
      </a>

      <a href="/teacher/reports/export" class="bg-red-50 hover:bg-red-100 p-4 rounded-lg flex flex-col items-center justify-center transition">
        <svg class="w-8 h-8 text-red-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="text-red-800 font-medium">Export Reports</span>
      </a>
    </div>
  </div>
</div>

<!-- Upcoming Practicals -->
<div class="mt-6 bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Upcoming Practicals</h2>
  </div>
  <div class="p-6">
    <% if (upcomingPracticals && upcomingPracticals.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lab</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% upcomingPracticals.forEach(practical => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= practical.date %> | <%= practical.start_time %> - <%= practical.end_time %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= practical.class_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= practical.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= practical.lab_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/teacher/practicals/<%= practical.id %>" class="text-teacher-primary hover:text-teacher-secondary">
                    View Details
                  </a>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No upcoming practicals</h3>
        <p class="mt-1 text-sm text-gray-500">Schedule a new practical session to see it here.</p>
        <div class="mt-6">
          <a href="/teacher/practicals/schedule" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teacher-primary hover:bg-teacher-secondary transition">
            Schedule Practical
          </a>
        </div>
      </div>
    <% } %>
  </div>
</div>

<%- contentFor('script') %>
<script>
  // Function to mark a lecture as delivered
  function markAsDelivered(lectureId) {
    // Use the confirm dialog from common.js
    initConfirmDialog(
      'Mark as Delivered',
      'Are you sure you want to mark this lecture as delivered?',
      () => {
        // Use the apiCall function from common.js
        apiCall(`/api/teacher/lectures/${lectureId}/status`, 'PUT', { status: 'delivered' })
          .then(response => {
            if (response.success) {
              showToast('success', 'Success', 'Lecture marked as delivered');
              // Reload page after a delay
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              showToast('error', 'Error', response.message || 'Failed to update lecture status');
            }
          })
          .catch(error => {
            console.error('Error updating lecture status:', error);
            showToast('error', 'Error', 'Failed to update lecture status');
          });
      }
    );
  }

  // Function to reschedule a lecture
  function reschedule(lectureId) {
    // This is a temporary fix until the reschedule page is implemented
    showToast('info', 'Reschedule', 'Reschedule functionality will be implemented soon');
  }

  // Initialize event listeners when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard script loaded');
  });
</script>
