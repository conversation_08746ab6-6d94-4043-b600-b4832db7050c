<%- contentFor('body') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Edit Practical</h1>
    <div class="flex space-x-2">
      <a href="/teacher/practicals/<%= practical.id %>" class="px-3 py-1 bg-gray-300 rounded hover:bg-gray-400 transition text-sm flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Details
      </a>
    </div>
  </div>
  
  <!-- Edit Practical Form -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Edit Practical Details</h2>
    </div>
    
    <div class="p-6">
      <form id="edit-practical-form">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div>
            <div class="mb-4">
              <label for="subject-name" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
              <select id="subject-name" name="subject_name" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="">Select Subject</option>
                <option value="Physics" <%= practical.subject_name === 'Physics' ? 'selected' : '' %>>Physics</option>
                <option value="Chemistry" <%= practical.subject_name === 'Chemistry' ? 'selected' : '' %>>Chemistry</option>
                <option value="Biology" <%= practical.subject_name === 'Biology' ? 'selected' : '' %>>Biology</option>
                <option value="Computer Science" <%= practical.subject_name === 'Computer Science' ? 'selected' : '' %>>Computer Science</option>
                <option value="Mathematics" <%= practical.subject_name === 'Mathematics' ? 'selected' : '' %>>Mathematics</option>
              </select>
            </div>
            
            <div class="mb-4">
              <label for="practical-topic" class="block text-sm font-medium text-gray-700 mb-1">Practical Topic *</label>
              <input type="text" id="practical-topic" name="practical_topic" value="<%= practical.practical_topic %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            </div>
            
            <div class="mb-4">
              <label for="class-section" class="block text-sm font-medium text-gray-700 mb-1">Class *</label>
              <select id="class-section" name="class_section_id" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="">Select Class</option>
                <% if (classSections && classSections.length > 0) { %>
                  <% 
                    // Group by class name
                    const classGroups = {};
                    classSections.forEach(section => {
                      if (!classGroups[section.class_name]) {
                        classGroups[section.class_name] = [];
                      }
                      classGroups[section.class_name].push(section);
                    });
                    
                    // Sort class names
                    const sortedClassNames = Object.keys(classGroups).sort();
                    
                    // Display options
                    sortedClassNames.forEach(className => {
                      const sections = classGroups[className];
                      sections.forEach(section => {
                        const displayName = `Class ${section.class_name} ${section.section_name}${section.trade_name ? ' (' + section.trade_name + ')' : ''}`;
                        const selected = section.id === practical.class_section_id ? 'selected' : '';
                  %>
                        <option value="<%= section.id %>" <%= selected %>><%= displayName %></option>
                  <% 
                      });
                    });
                  %>
                <% } %>
              </select>
            </div>
            
            <div class="mb-4">
              <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
              <input type="date" id="date" name="date" value="<%= practical.date ? practical.date.toISOString().split('T')[0] : '' %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label for="start-time" class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
                <input type="time" id="start-time" name="start_time" value="<%= practical.start_time %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
              </div>
              
              <div>
                <label for="end-time" class="block text-sm font-medium text-gray-700 mb-1">End Time *</label>
                <input type="time" id="end-time" name="end_time" value="<%= practical.end_time %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
              </div>
            </div>
            
            <div class="mb-4">
              <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location *</label>
              <input type="text" id="location" name="location" value="<%= practical.location || '' %>" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            </div>
          </div>
          
          <!-- Right Column -->
          <div>
            <div class="mb-4">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea id="description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"><%= practical.description || '' %></textarea>
            </div>
            
            <div class="mb-4">
              <label for="materials" class="block text-sm font-medium text-gray-700 mb-1">Materials Required</label>
              <textarea id="materials" name="materials" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"><%= practical.materials || '' %></textarea>
            </div>
            
            <div class="mb-4">
              <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
              <textarea id="notes" name="notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"><%= practical.notes || '' %></textarea>
            </div>
            
            <div class="mb-4">
              <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
              <select id="status" name="status" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
                <option value="scheduled" <%= practical.status === 'scheduled' ? 'selected' : '' %>>Scheduled</option>
                <option value="completed" <%= practical.status === 'completed' ? 'selected' : '' %>>Completed</option>
                <option value="cancelled" <%= practical.status === 'cancelled' ? 'selected' : '' %>>Cancelled</option>
                <option value="rescheduled" <%= practical.status === 'rescheduled' ? 'selected' : '' %>>Rescheduled</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <a href="/teacher/practicals/<%= practical.id %>" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</a>
          <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Form submission
    const form = document.getElementById('edit-practical-form');
    
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Show loading toast
      showToast('info', 'Processing', 'Saving changes...');
      
      // In a real application, you would send an AJAX request to update the practical
      // For this demo, we'll just show a success toast and redirect
      
      setTimeout(() => {
        showToast('success', 'Success', 'Practical updated successfully');
        
        // Redirect back to practical details after a delay
        setTimeout(() => {
          window.location.href = '/teacher/practicals/<%= practical.id %>';
        }, 1500);
      }, 1000);
    });
    
    // Function to show toast notifications
    function showToast(type, title, message) {
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      
      const toastContent = `
        <div class="font-medium">${title}</div>
        <div class="text-sm">${message}</div>
      `;
      
      toast.innerHTML = toastContent;
      
      const toastContainer = document.getElementById('toast-container');
      toastContainer.appendChild(toast);
      
      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
  });
</script>

<style>
  .toast {
    @apply mb-3 px-4 py-3 rounded shadow-md text-white;
  }
  
  .toast-success {
    @apply bg-green-500;
  }
  
  .toast-error {
    @apply bg-red-500;
  }
  
  .toast-warning {
    @apply bg-yellow-500;
  }
  
  .toast-info {
    @apply bg-blue-500;
  }
</style>
