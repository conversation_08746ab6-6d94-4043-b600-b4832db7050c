<!-- Export Reports Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Export Reports</h2>
  </div>
  <div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <% if (reportTypes && reportTypes.length > 0) { %>
        <% reportTypes.forEach(reportType => { %>
          <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300">
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-800 mb-4"><%= reportType.name %></h3>
              
              <div class="mb-4">
                <label for="format-<%= reportType.id %>" class="block text-sm font-medium text-gray-700 mb-1">Export Format</label>
                <select id="format-<%= reportType.id %>" name="format" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm rounded-md">
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
              
              <div class="mb-4">
                <label for="date-range-<%= reportType.id %>" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                <select id="date-range-<%= reportType.id %>" name="date_range" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm rounded-md">
                  <option value="this_month">This Month</option>
                  <option value="last_month">Last Month</option>
                  <option value="this_quarter">This Quarter</option>
                  <option value="last_quarter">Last Quarter</option>
                  <option value="this_year">This Year</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
              
              <div class="custom-date-range-<%= reportType.id %> hidden space-y-4">
                <div>
                  <label for="start-date-<%= reportType.id %>" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <input type="date" id="start-date-<%= reportType.id %>" name="start_date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm">
                </div>
                
                <div>
                  <label for="end-date-<%= reportType.id %>" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <input type="date" id="end-date-<%= reportType.id %>" name="end_date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm">
                </div>
              </div>
              
              <div class="mt-6">
                <button type="button" onclick="exportReport('<%= reportType.id %>')" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teacher-primary hover:bg-teacher-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teacher-primary">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                  </svg>
                  Export <%= reportType.name %>
                </button>
              </div>
            </div>
          </div>
        <% }); %>
      <% } else { %>
        <div class="col-span-2 text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No report types available</h3>
          <p class="mt-1 text-sm text-gray-500">Please contact the administrator to set up report types.</p>
        </div>
      <% } %>
    </div>
  </div>
</div>

<%- contentFor('script') %>
<script>
  $(document).ready(function() {
    // Show/hide custom date range based on selection
    <% if (reportTypes && reportTypes.length > 0) { %>
      <% reportTypes.forEach(reportType => { %>
        $(`#date-range-<%= reportType.id %>`).on('change', function() {
          if ($(this).val() === 'custom') {
            $(`.custom-date-range-<%= reportType.id %>`).removeClass('hidden');
          } else {
            $(`.custom-date-range-<%= reportType.id %>`).addClass('hidden');
          }
        });
      <% }); %>
    <% } %>
  });

  // Function to export report
  function exportReport(reportType) {
    const format = $(`#format-${reportType}`).val();
    const dateRange = $(`#date-range-${reportType}`).val();
    let startDate = null;
    let endDate = null;

    if (dateRange === 'custom') {
      startDate = $(`#start-date-${reportType}`).val();
      endDate = $(`#end-date-${reportType}`).val();

      if (!startDate || !endDate) {
        showToast('error', 'Error', 'Please select both start and end dates for custom range');
        return;
      }
    }

    // Show loading toast
    showToast('info', 'Processing', 'Generating report, please wait...');

    // Build query parameters
    const params = new URLSearchParams({
      format,
      date_range: dateRange
    });

    if (startDate && endDate) {
      params.append('start_date', startDate);
      params.append('end_date', endDate);
    }

    // Redirect to download URL
    window.location.href = `/api/teacher/reports/${reportType}/export?${params.toString()}`;
  }
</script>
