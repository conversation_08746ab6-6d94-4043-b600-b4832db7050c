

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Holiday Calendar</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Upcoming Holidays</h3>

        <%
          // Filter upcoming holidays
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const upcomingHolidays = holidays.filter(holiday => {
            const holidayDate = new Date(holiday.holiday_date);
            return holidayDate >= today && holiday.is_active;
          });

          // Sort by date
          upcomingHolidays.sort((a, b) => new Date(a.holiday_date) - new Date(b.holiday_date));
        %>

        <% if (upcomingHolidays && upcomingHolidays.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                </tr>
              </thead>
              <tbody>
                <% upcomingHolidays.forEach(holiday => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= holiday.date_formatted %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= holiday.description %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        <%= holiday.holiday_type === 'National Holiday' ? 'bg-red-100 text-red-800' :
                           holiday.holiday_type === 'Festival' ? 'bg-yellow-100 text-yellow-800' :
                           'bg-blue-100 text-blue-800' %>">
                        <%= holiday.holiday_type %>
                      </span>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  No upcoming holidays found.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">All Holidays</h3>

        <% if (holidays && holidays.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <% if (isAdmin) { %>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                  <% } %>
                </tr>
              </thead>
              <tbody>
                <% holidays.forEach(holiday => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= holiday.date_formatted %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= holiday.description %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        <%= holiday.holiday_type === 'National Holiday' ? 'bg-red-100 text-red-800' :
                           holiday.holiday_type === 'Festival' ? 'bg-yellow-100 text-yellow-800' :
                           'bg-blue-100 text-blue-800' %>">
                        <%= holiday.holiday_type %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= holiday.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= holiday.is_active ? 'Active' : 'Inactive' %>
                      </span>
                    </td>
                    <% if (isAdmin) { %>
                      <td class="py-2 px-4 border-b border-gray-200">
                        <button
                          class="edit-holiday-btn text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1"
                          data-id="<%= holiday.id %>"
                          data-description="<%= holiday.description %>"
                          data-type="<%= holiday.holiday_type %>"
                        >
                          Edit
                        </button>
                        <button
                          class="toggle-status-btn text-sm px-2 py-1 rounded <%= holiday.is_active ? 'bg-red-500 text-white' : 'bg-green-500 text-white' %>"
                          data-id="<%= holiday.id %>"
                          data-status="<%= holiday.is_active ? '0' : '1' %>"
                        >
                          <%= holiday.is_active ? 'Deactivate' : 'Activate' %>
                        </button>
                      </td>
                    <% } %>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  No holidays found in the calendar.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <% if (isAdmin) { %>
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">Add Holiday (Admin Only)</h3>

          <form id="add-holiday-form" class="bg-gray-50 p-4 rounded border border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="holiday_date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="holiday_date" name="holiday_date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              </div>

              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input type="text" id="description" name="description" placeholder="e.g., Independence Day" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              </div>

              <div>
                <label for="holiday_type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select id="holiday_type" name="holiday_type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                  <option value="National Holiday">National Holiday</option>
                  <option value="Festival">Festival</option>
                  <option value="School Holiday">School Holiday</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>

            <div class="mt-4">
              <button type="submit" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
                Add Holiday
              </button>
            </div>
          </form>
        </div>
      <% } %>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Calendar View</h2>
    </div>

    <div class="p-4">
      <div id="calendar" class="calendar-container"></div>
    </div>
  </div>
</div>

<!-- Edit Holiday Modal (Admin Only) -->
<% if (isAdmin) { %>
  <div id="edit-holiday-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
      <h3 class="text-lg font-semibold mb-4">Edit Holiday</h3>

      <form id="edit-holiday-form">
        <input type="hidden" id="edit_holiday_id" name="id">

        <div class="mb-4">
          <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <input type="text" id="edit_description" name="description" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
        </div>

        <div class="mb-4">
          <label for="edit_holiday_type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
          <select id="edit_holiday_type" name="holiday_type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
            <option value="National Holiday">National Holiday</option>
            <option value="Festival">Festival</option>
            <option value="School Holiday">School Holiday</option>
            <option value="Other">Other</option>
          </select>
        </div>

        <div class="flex justify-end space-x-3">
          <button type="button" id="close-modal-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Cancel</button>
          <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
<% } %>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize calendar
    const calendarEl = document.getElementById('calendar');

    if (calendarEl) {
      const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek'
        },
        events: [
          <% holidays.forEach(holiday => { %>
            {
              title: '<%= holiday.description %>',
              start: '<%= holiday.holiday_date %>',
              allDay: true,
              backgroundColor: '<%=
                holiday.holiday_type === "National Holiday" ? "#ef4444" :
                holiday.holiday_type === "Festival" ? "#f59e0b" :
                "#3b82f6"
              %>',
              borderColor: '<%=
                holiday.holiday_type === "National Holiday" ? "#b91c1c" :
                holiday.holiday_type === "Festival" ? "#d97706" :
                "#2563eb"
              %>',
              textColor: '#ffffff',
              extendedProps: {
                type: '<%= holiday.holiday_type %>',
                active: <%= holiday.is_active %>
              },
              display: '<%= holiday.is_active ? "auto" : "none" %>'
            },
          <% }); %>
        ],
        eventDidMount: function(info) {
          if (!info.event.extendedProps.active) {
            info.el.style.opacity = '0.5';
          }
        }
      });

      calendar.render();
    }

    <% if (isAdmin) { %>
      // Add holiday form submission (Admin only)
      const addHolidayForm = document.getElementById('add-holiday-form');
      if (addHolidayForm) {
        addHolidayForm.addEventListener('submit', async function(e) {
          e.preventDefault();

          const holidayDate = document.getElementById('holiday_date').value;
          const description = document.getElementById('description').value;
          const holidayType = document.getElementById('holiday_type').value;

          if (!holidayDate || !description) {
            showToast('Please fill in all required fields', 'error');
            return;
          }

          try {
            const response = await fetch('/api/teacher/holiday-calendar', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                holiday_date: holidayDate,
                description,
                holiday_type: holidayType
              })
            });

            const data = await response.json();

            if (data.success) {
              showToast(data.message, 'success');
              // Reload the page to show the updated list
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              showToast(data.message, 'error');
            }
          } catch (error) {
            console.error('Error adding holiday:', error);
            showToast('An error occurred while adding holiday', 'error');
          }
        });
      }

      // Toggle status buttons (Admin only)
      const toggleStatusButtons = document.querySelectorAll('.toggle-status-btn');
      toggleStatusButtons.forEach(button => {
        button.addEventListener('click', async function() {
          const id = this.dataset.id;
          const status = this.dataset.status;

          try {
            const response = await fetch('/api/teacher/holiday-calendar', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                id,
                is_active: status === '1'
              })
            });

            const data = await response.json();

            if (data.success) {
              showToast(data.message, 'success');
              // Reload the page to show the updated status
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              showToast(data.message, 'error');
            }
          } catch (error) {
            console.error('Error updating holiday status:', error);
            showToast('An error occurred while updating holiday status', 'error');
          }
        });
      });

      // Edit holiday buttons (Admin only)
      const editHolidayButtons = document.querySelectorAll('.edit-holiday-btn');
      const editHolidayModal = document.getElementById('edit-holiday-modal');
      const closeModalBtn = document.getElementById('close-modal-btn');
      const editHolidayForm = document.getElementById('edit-holiday-form');

      editHolidayButtons.forEach(button => {
        button.addEventListener('click', function() {
          const id = this.dataset.id;
          const description = this.dataset.description;
          const type = this.dataset.type;

          document.getElementById('edit_holiday_id').value = id;
          document.getElementById('edit_description').value = description;
          document.getElementById('edit_holiday_type').value = type;

          editHolidayModal.classList.remove('hidden');
        });
      });

      closeModalBtn.addEventListener('click', function() {
        editHolidayModal.classList.add('hidden');
      });

      editHolidayForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const id = document.getElementById('edit_holiday_id').value;
        const description = document.getElementById('edit_description').value;
        const holidayType = document.getElementById('edit_holiday_type').value;

        if (!description) {
          showToast('Description is required', 'error');
          return;
        }

        try {
          const response = await fetch('/api/teacher/holiday-calendar', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id,
              description,
              holiday_type: holidayType
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            editHolidayModal.classList.add('hidden');
            // Reload the page to show the updated holiday
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error updating holiday:', error);
          showToast('An error occurred while updating holiday', 'error');
        }
      });
    <% } %>
  });
</script>


