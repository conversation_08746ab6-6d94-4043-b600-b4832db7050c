

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">My Instruction Plans</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Current Instruction Plans</h3>

        <% if (plans && plans.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Class</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Topic</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Completion</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% plans.forEach(plan => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.date_formatted %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.start_time_formatted %> - <%= plan.end_time_formatted %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.class_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.topic %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-teacher-primary h-2.5 rounded-full" style="width: <%= plan.topic_completion_percentage %>%"></div>
                      </div>
                      <span class="text-xs"><%= plan.topic_completion_percentage %>%</span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= plan.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= plan.is_active ? 'Active' : 'Inactive' %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <button
                        class="view-plan-btn text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1"
                        data-id="<%= plan.id %>"
                        data-topic="<%= plan.topic %>"
                        data-objectives="<%= plan.objectives || '' %>"
                        data-materials="<%= plan.material_support || '' %>"
                        data-activities="<%= plan.activities || '' %>"
                        data-homework="<%= plan.homework || '' %>"
                        data-notes="<%= plan.notes || '' %>"
                        data-completion="<%= plan.topic_completion_percentage %>"
                      >
                        View
                      </button>
                      <button
                        class="edit-plan-btn text-sm px-2 py-1 rounded bg-yellow-500 text-white mr-1"
                        data-id="<%= plan.id %>"
                        data-topic="<%= plan.topic %>"
                        data-objectives="<%= plan.objectives || '' %>"
                        data-materials="<%= plan.material_support || '' %>"
                        data-activities="<%= plan.activities || '' %>"
                        data-homework="<%= plan.homework || '' %>"
                        data-notes="<%= plan.notes || '' %>"
                        data-completion="<%= plan.topic_completion_percentage %>"
                      >
                        Edit
                      </button>
                      <button
                        class="toggle-status-btn text-sm px-2 py-1 rounded <%= plan.is_active ? 'bg-red-500 text-white' : 'bg-green-500 text-white' %>"
                        data-id="<%= plan.id %>"
                        data-status="<%= plan.is_active ? '0' : '1' %>"
                      >
                        <%= plan.is_active ? 'Deactivate' : 'Activate' %>
                      </button>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You don't have any instruction plans yet. Please add a plan below.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Add Instruction Plan</h3>

        <form id="add-plan-form" class="bg-gray-50 p-4 rounded border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="schedule_id" class="block text-sm font-medium text-gray-700 mb-1">Lecture Schedule</label>
              <select id="schedule_id" name="schedule_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                <option value="">Select a lecture schedule</option>
                <% schedules.forEach(schedule => { %>
                  <option value="<%= schedule.id %>"><%= schedule.display_text %></option>
                <% }); %>
              </select>
            </div>

            <div>
              <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
              <input type="date" id="date" name="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
            </div>
          </div>

          <div class="mb-4">
            <label for="topic" class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
            <input type="text" id="topic" name="topic" placeholder="e.g., Introduction to Quadratic Equations" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          </div>

          <div class="mb-4">
            <label for="objectives" class="block text-sm font-medium text-gray-700 mb-1">Learning Objectives</label>
            <textarea id="objectives" name="objectives" rows="3" placeholder="e.g., Students will be able to identify and solve quadratic equations" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="material_support" class="block text-sm font-medium text-gray-700 mb-1">Materials & Resources</label>
              <textarea id="material_support" name="material_support" rows="3" placeholder="e.g., Textbook pages 45-50, Whiteboard, Calculators" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
            </div>

            <div>
              <label for="activities" class="block text-sm font-medium text-gray-700 mb-1">Learning Activities</label>
              <textarea id="activities" name="activities" rows="3" placeholder="e.g., Group problem-solving, Individual practice, Class discussion" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label for="homework" class="block text-sm font-medium text-gray-700 mb-1">Homework Assignment</label>
              <textarea id="homework" name="homework" rows="3" placeholder="e.g., Textbook exercises 1-10 on page 51" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
            </div>

            <div>
              <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
              <textarea id="notes" name="notes" rows="3" placeholder="e.g., Remember to review prerequisite concepts before starting the lesson" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
            </div>
          </div>

          <div>
            <button type="submit" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
              Add Instruction Plan
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- View Plan Modal -->
<div id="view-plan-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold" id="view-plan-title">Instruction Plan Details</h3>
      <button id="close-view-modal-btn" class="text-gray-500 hover:text-gray-700">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">Topic</h4>
      <p id="view-plan-topic" class="text-gray-700"></p>
    </div>

    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">Learning Objectives</h4>
      <p id="view-plan-objectives" class="text-gray-700"></p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div>
        <h4 class="text-md font-semibold mb-2">Materials & Resources</h4>
        <p id="view-plan-materials" class="text-gray-700"></p>
      </div>

      <div>
        <h4 class="text-md font-semibold mb-2">Learning Activities</h4>
        <p id="view-plan-activities" class="text-gray-700"></p>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div>
        <h4 class="text-md font-semibold mb-2">Homework Assignment</h4>
        <p id="view-plan-homework" class="text-gray-700"></p>
      </div>

      <div>
        <h4 class="text-md font-semibold mb-2">Additional Notes</h4>
        <p id="view-plan-notes" class="text-gray-700"></p>
      </div>
    </div>

    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">Topic Completion</h4>
      <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div id="view-plan-completion-bar" class="bg-teacher-primary h-2.5 rounded-full"></div>
      </div>
      <span id="view-plan-completion-text" class="text-sm"></span>
    </div>

    <div class="flex justify-end">
      <button id="close-view-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Close</button>
    </div>
  </div>
</div>

<!-- Edit Plan Modal -->
<div id="edit-plan-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold">Edit Instruction Plan</h3>
      <button id="close-edit-modal-btn" class="text-gray-500 hover:text-gray-700">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <form id="edit-plan-form">
      <input type="hidden" id="edit_plan_id" name="id">

      <div class="mb-4">
        <label for="edit_topic" class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
        <input type="text" id="edit_topic" name="topic" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
      </div>

      <div class="mb-4">
        <label for="edit_objectives" class="block text-sm font-medium text-gray-700 mb-1">Learning Objectives</label>
        <textarea id="edit_objectives" name="objectives" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label for="edit_material_support" class="block text-sm font-medium text-gray-700 mb-1">Materials & Resources</label>
          <textarea id="edit_material_support" name="material_support" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
        </div>

        <div>
          <label for="edit_activities" class="block text-sm font-medium text-gray-700 mb-1">Learning Activities</label>
          <textarea id="edit_activities" name="activities" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label for="edit_homework" class="block text-sm font-medium text-gray-700 mb-1">Homework Assignment</label>
          <textarea id="edit_homework" name="homework" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
        </div>

        <div>
          <label for="edit_notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
          <textarea id="edit_notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"></textarea>
        </div>
      </div>

      <div class="mb-4">
        <label for="edit_topic_completion_percentage" class="block text-sm font-medium text-gray-700 mb-1">Topic Completion Percentage</label>
        <input type="range" id="edit_topic_completion_percentage" name="topic_completion_percentage" min="0" max="100" step="5" class="w-full">
        <span id="edit_completion_value" class="text-sm">0%</span>
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" id="close-edit-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add plan form submission
    const addPlanForm = document.getElementById('add-plan-form');
    if (addPlanForm) {
      addPlanForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const scheduleId = document.getElementById('schedule_id').value;
        const date = document.getElementById('date').value;
        const topic = document.getElementById('topic').value;
        const objectives = document.getElementById('objectives').value;
        const materialSupport = document.getElementById('material_support').value;
        const activities = document.getElementById('activities').value;
        const homework = document.getElementById('homework').value;
        const notes = document.getElementById('notes').value;

        if (!scheduleId || !date || !topic) {
          showToast('Please fill in all required fields', 'error');
          return;
        }

        try {
          const response = await fetch('/api/teacher/instruction-plans', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              schedule_id: scheduleId,
              date,
              topic,
              objectives,
              material_support: materialSupport,
              activities,
              homework,
              notes
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated list
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error adding instruction plan:', error);
          showToast('An error occurred while adding instruction plan', 'error');
        }
      });
    }

    // Toggle status buttons
    const toggleStatusButtons = document.querySelectorAll('.toggle-status-btn');
    toggleStatusButtons.forEach(button => {
      button.addEventListener('click', async function() {
        const id = this.dataset.id;
        const status = this.dataset.status;

        try {
          const response = await fetch('/api/teacher/instruction-plans', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id,
              is_active: status === '1'
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error updating instruction plan status:', error);
          showToast('An error occurred while updating instruction plan status', 'error');
        }
      });
    });

    // View plan buttons
    const viewPlanButtons = document.querySelectorAll('.view-plan-btn');
    const viewPlanModal = document.getElementById('view-plan-modal');
    const closeViewModalBtn = document.getElementById('close-view-modal-btn');
    const closeViewBtn = document.getElementById('close-view-btn');

    viewPlanButtons.forEach(button => {
      button.addEventListener('click', function() {
        const topic = this.dataset.topic;
        const objectives = this.dataset.objectives;
        const materials = this.dataset.materials;
        const activities = this.dataset.activities;
        const homework = this.dataset.homework;
        const notes = this.dataset.notes;
        const completion = this.dataset.completion;

        document.getElementById('view-plan-topic').textContent = topic;
        document.getElementById('view-plan-objectives').textContent = objectives || 'No objectives specified';
        document.getElementById('view-plan-materials').textContent = materials || 'No materials specified';
        document.getElementById('view-plan-activities').textContent = activities || 'No activities specified';
        document.getElementById('view-plan-homework').textContent = homework || 'No homework assigned';
        document.getElementById('view-plan-notes').textContent = notes || 'No additional notes';

        document.getElementById('view-plan-completion-bar').style.width = `${completion}%`;
        document.getElementById('view-plan-completion-text').textContent = `${completion}%`;

        viewPlanModal.classList.remove('hidden');
      });
    });

    closeViewModalBtn.addEventListener('click', function() {
      viewPlanModal.classList.add('hidden');
    });

    closeViewBtn.addEventListener('click', function() {
      viewPlanModal.classList.add('hidden');
    });

    // Edit plan buttons
    const editPlanButtons = document.querySelectorAll('.edit-plan-btn');
    const editPlanModal = document.getElementById('edit-plan-modal');
    const closeEditModalBtn = document.getElementById('close-edit-modal-btn');
    const closeEditBtn = document.getElementById('close-edit-btn');
    const editPlanForm = document.getElementById('edit-plan-form');
    const editCompletionRange = document.getElementById('edit_topic_completion_percentage');
    const editCompletionValue = document.getElementById('edit_completion_value');

    editPlanButtons.forEach(button => {
      button.addEventListener('click', function() {
        const id = this.dataset.id;
        const topic = this.dataset.topic;
        const objectives = this.dataset.objectives;
        const materials = this.dataset.materials;
        const activities = this.dataset.activities;
        const homework = this.dataset.homework;
        const notes = this.dataset.notes;
        const completion = this.dataset.completion;

        document.getElementById('edit_plan_id').value = id;
        document.getElementById('edit_topic').value = topic;
        document.getElementById('edit_objectives').value = objectives;
        document.getElementById('edit_material_support').value = materials;
        document.getElementById('edit_activities').value = activities;
        document.getElementById('edit_homework').value = homework;
        document.getElementById('edit_notes').value = notes;
        document.getElementById('edit_topic_completion_percentage').value = completion;
        editCompletionValue.textContent = `${completion}%`;

        editPlanModal.classList.remove('hidden');
      });
    });

    closeEditModalBtn.addEventListener('click', function() {
      editPlanModal.classList.add('hidden');
    });

    closeEditBtn.addEventListener('click', function() {
      editPlanModal.classList.add('hidden');
    });

    // Update completion value display when range input changes
    editCompletionRange.addEventListener('input', function() {
      editCompletionValue.textContent = `${this.value}%`;
    });

    editPlanForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      const id = document.getElementById('edit_plan_id').value;
      const topic = document.getElementById('edit_topic').value;
      const objectives = document.getElementById('edit_objectives').value;
      const materialSupport = document.getElementById('edit_material_support').value;
      const activities = document.getElementById('edit_activities').value;
      const homework = document.getElementById('edit_homework').value;
      const notes = document.getElementById('edit_notes').value;
      const topicCompletionPercentage = document.getElementById('edit_topic_completion_percentage').value;

      if (!topic) {
        showToast('Topic is required', 'error');
        return;
      }

      try {
        const response = await fetch('/api/teacher/instruction-plans', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id,
            topic,
            objectives,
            material_support: materialSupport,
            activities,
            homework,
            notes,
            topic_completion_percentage: topicCompletionPercentage
          })
        });

        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          editPlanModal.classList.add('hidden');
          // Reload the page to show the updated plan
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          showToast(data.message, 'error');
        }
      } catch (error) {
        console.error('Error updating instruction plan:', error);
        showToast('An error occurred while updating instruction plan', 'error');
      }
    });
  });
</script>


