<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Edit Instruction Plan</h2>
      <a href="/teacher/instruction-plans" class="bg-white text-teacher-primary px-3 py-1 rounded text-sm hover:bg-gray-100 transition">
        Back to Plans
      </a>
    </div>

    <div class="p-6">
      <form id="edit-plan-form" enctype="multipart/form-data">
        <div class="mb-4">
          <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
          <input type="text" id="title" name="title" value="<%= plan.title %>" required
                 class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
        </div>

        <div class="mb-4">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea id="description" name="description" rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary"><%= plan.description || '' %></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
            <select id="subject_id" name="subject_id" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              <option value="">Select a subject</option>
              <% subjects.forEach(subject => { %>
                <option value="<%= subject.id %>" <%= plan.subject_id === subject.id ? 'selected' : '' %>>
                  <%= subject.name %>
                </option>
              <% }); %>
            </select>
          </div>

          <div>
            <label for="class_id" class="block text-sm font-medium text-gray-700 mb-1">Class (Optional)</label>
            <select id="class_id" name="class_id"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              <option value="">Select a class</option>
              <% classes.forEach(cls => { %>
                <option value="<%= cls.id %>" <%= plan.class_id === cls.id ? 'selected' : '' %>>
                  <%= cls.name %>
                </option>
              <% }); %>
            </select>
          </div>
        </div>

        <% if (trades && trades.length > 0) { %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="trade_id" class="block text-sm font-medium text-gray-700 mb-1">Trade (Optional)</label>
            <select id="trade_id" name="trade_id"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              <option value="">Select a trade</option>
              <% trades.forEach(trade => { %>
                <option value="<%= trade.id %>" <%= plan.trade_id === trade.id ? 'selected' : '' %>>
                  <%= trade.name %>
                </option>
              <% }); %>
            </select>
          </div>

          <div>
            <label for="section_id" class="block text-sm font-medium text-gray-700 mb-1">Section (Optional)</label>
            <select id="section_id" name="section_id"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
              <option value="">Select a section</option>
              <% sections.forEach(section => { %>
                <option value="<%= section.id %>" <%= plan.section_id === section.id ? 'selected' : '' %>>
                  <%= section.name %>
                </option>
              <% }); %>
            </select>
          </div>
        </div>
        <% } %>

        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-2">Resources</h3>
          
          <% if (resources && resources.length > 0) { %>
            <div class="mb-4">
              <h4 class="text-md font-medium mb-2">Current Resources</h4>
              <ul class="list-disc pl-5">
                <% resources.forEach(resource => { %>
                  <li class="mb-2 flex items-center">
                    <a href="/uploads/resources/<%= resource.file_path %>" 
                       class="text-blue-600 hover:underline" 
                       target="_blank">
                      <%= resource.title || resource.file_path %>
                    </a>
                    <button type="button" 
                            class="delete-resource-btn ml-2 text-red-500 hover:text-red-700"
                            data-id="<%= resource.id %>">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                    </button>
                  </li>
                <% }); %>
              </ul>
            </div>
          <% } %>
          
          <div class="mb-4">
            <h4 class="text-md font-medium mb-2">Add New Resources</h4>
            <div id="resource-container">
              <div class="resource-item grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border border-gray-200 rounded">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                  <input type="text" name="resource_titles[]" 
                         class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <input type="text" name="resource_descriptions[]" 
                         class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">File</label>
                  <input type="file" name="resource_files[]" 
                         class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                </div>
              </div>
            </div>
            
            <button type="button" id="add-resource-btn" 
                    class="mt-2 px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition">
              Add Another Resource
            </button>
          </div>
        </div>

        <div class="flex justify-between mt-8">
          <a href="/teacher/instruction-plans/<%= plan.id %>" 
             class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition">
            Cancel
          </a>
          
          <button type="submit" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
            Save Changes
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add resource button
    const addResourceBtn = document.getElementById('add-resource-btn');
    const resourceContainer = document.getElementById('resource-container');
    
    addResourceBtn.addEventListener('click', function() {
      const resourceItem = document.createElement('div');
      resourceItem.className = 'resource-item grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border border-gray-200 rounded';
      resourceItem.innerHTML = `
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
          <input type="text" name="resource_titles[]" 
                 class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <input type="text" name="resource_descriptions[]" 
                 class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
        </div>
        <div class="relative">
          <label class="block text-sm font-medium text-gray-700 mb-1">File</label>
          <input type="file" name="resource_files[]" 
                 class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          <button type="button" class="remove-resource-btn absolute top-0 right-0 text-red-500 hover:text-red-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      `;
      resourceContainer.appendChild(resourceItem);
      
      // Add event listener to the new remove button
      const removeBtn = resourceItem.querySelector('.remove-resource-btn');
      removeBtn.addEventListener('click', function() {
        resourceContainer.removeChild(resourceItem);
      });
    });
    
    // Delete resource buttons
    const deleteResourceBtns = document.querySelectorAll('.delete-resource-btn');
    deleteResourceBtns.forEach(btn => {
      btn.addEventListener('click', async function() {
        const resourceId = this.dataset.id;
        
        if (confirm('Are you sure you want to delete this resource? This action cannot be undone.')) {
          try {
            const response = await fetch(`/teacher/instruction-plans/resources/${resourceId}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const data = await response.json();

            if (data.success) {
              showToast(data.message, 'success');
              // Remove the resource item from the DOM
              this.closest('li').remove();
            } else {
              showToast(data.message, 'error');
            }
          } catch (error) {
            console.error('Error deleting resource:', error);
            showToast('An error occurred while deleting resource', 'error');
          }
        }
      });
    });
    
    // Form submission
    const editPlanForm = document.getElementById('edit-plan-form');
    editPlanForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const formData = new FormData(this);
      
      try {
        const response = await fetch(`/teacher/instruction-plans/<%= plan.id %>`, {
          method: 'POST',
          body: formData
        });

        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          // Redirect to the view page
          setTimeout(() => {
            window.location.href = `/teacher/instruction-plans/<%= plan.id %>`;
          }, 1000);
        } else {
          showToast(data.message, 'error');
        }
      } catch (error) {
        console.error('Error updating instruction plan:', error);
        showToast('An error occurred while updating instruction plan', 'error');
      }
    });
  });
</script>
