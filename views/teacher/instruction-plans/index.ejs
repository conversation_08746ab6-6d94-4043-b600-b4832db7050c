<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">My Instruction Plans</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Current Instruction Plans</h3>

        <% if (plans && plans.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% plans.forEach(plan => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.title %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= plan.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        <%= plan.status === 'published' ? 'bg-green-100 text-green-800' : 
                           plan.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
                           'bg-red-100 text-red-800' %>">
                        <%= plan.status.charAt(0).toUpperCase() + plan.status.slice(1) %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <%= new Date(plan.created_at).toLocaleDateString() %>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <a href="/teacher/instruction-plans/<%= plan.id %>" class="text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1">
                        View
                      </a>
                      <a href="/teacher/instruction-plans/<%= plan.id %>/edit" class="text-sm px-2 py-1 rounded bg-yellow-500 text-white mr-1">
                        Edit
                      </a>
                      <% if (plan.status === 'draft') { %>
                        <button 
                          class="publish-plan-btn text-sm px-2 py-1 rounded bg-green-500 text-white mr-1"
                          data-id="<%= plan.id %>">
                          Publish
                        </button>
                      <% } else if (plan.status === 'published') { %>
                        <button 
                          class="archive-plan-btn text-sm px-2 py-1 rounded bg-red-500 text-white mr-1"
                          data-id="<%= plan.id %>">
                          Archive
                        </button>
                      <% } %>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You don't have any instruction plans yet. Please create a new plan.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <a href="/teacher/instruction-plans/new" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
          Create New Instruction Plan
        </a>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Publish plan buttons
    const publishPlanButtons = document.querySelectorAll('.publish-plan-btn');
    publishPlanButtons.forEach(button => {
      button.addEventListener('click', async function() {
        const id = this.dataset.id;
        
        try {
          const response = await fetch(`/teacher/instruction-plans/${id}/publish`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error publishing instruction plan:', error);
          showToast('An error occurred while publishing instruction plan', 'error');
        }
      });
    });

    // Archive plan buttons
    const archivePlanButtons = document.querySelectorAll('.archive-plan-btn');
    archivePlanButtons.forEach(button => {
      button.addEventListener('click', async function() {
        const id = this.dataset.id;
        
        try {
          const response = await fetch(`/teacher/instruction-plans/${id}/archive`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error archiving instruction plan:', error);
          showToast('An error occurred while archiving instruction plan', 'error');
        }
      });
    });
  });
</script>
