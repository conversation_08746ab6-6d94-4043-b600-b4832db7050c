<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Instruction Plan Details</h2>
      <a href="/teacher/instruction-plans" class="bg-white text-teacher-primary px-3 py-1 rounded text-sm hover:bg-gray-100 transition">
        Back to Plans
      </a>
    </div>

    <div class="p-6">
      <div class="mb-6">
        <h3 class="text-2xl font-bold mb-2"><%= plan.title %></h3>
        <div class="flex items-center mb-4">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
            <%= plan.status === 'published' ? 'bg-green-100 text-green-800' : 
               plan.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
               'bg-red-100 text-red-800' %>">
            <%= plan.status.charAt(0).toUpperCase() + plan.status.slice(1) %>
          </span>
          <span class="ml-4 text-gray-500">
            Created: <%= new Date(plan.created_at).toLocaleDateString() %>
          </span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h4 class="text-lg font-semibold mb-2">Subject</h4>
            <p class="text-gray-700"><%= plan.subject_name %></p>
          </div>
          
          <% if (plan.class_id) { %>
          <div>
            <h4 class="text-lg font-semibold mb-2">Class</h4>
            <p class="text-gray-700"><%= plan.class_name || 'Not specified' %></p>
          </div>
          <% } %>
        </div>
        
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Description</h4>
          <p class="text-gray-700"><%= plan.description || 'No description provided' %></p>
        </div>
        
        <% if (resources && resources.length > 0) { %>
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">Resources</h4>
          <ul class="list-disc pl-5">
            <% resources.forEach(resource => { %>
              <li class="mb-2">
                <a href="/uploads/resources/<%= resource.file_path %>" 
                   class="text-blue-600 hover:underline" 
                   target="_blank">
                  <%= resource.title || resource.file_path %>
                </a>
                <% if (resource.description) { %>
                  <p class="text-sm text-gray-600 ml-5"><%= resource.description %></p>
                <% } %>
              </li>
            <% }); %>
          </ul>
        </div>
        <% } %>
        
        <div class="flex justify-between mt-8">
          <div>
            <a href="/teacher/instruction-plans/<%= plan.id %>/edit" 
               class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition">
              Edit Plan
            </a>
          </div>
          
          <div>
            <% if (plan.status === 'draft') { %>
              <button 
                id="publish-plan-btn"
                class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition mr-2"
                data-id="<%= plan.id %>">
                Publish Plan
              </button>
            <% } else if (plan.status === 'published') { %>
              <button 
                id="archive-plan-btn"
                class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition"
                data-id="<%= plan.id %>">
                Archive Plan
              </button>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Publish plan button
    const publishPlanBtn = document.getElementById('publish-plan-btn');
    if (publishPlanBtn) {
      publishPlanBtn.addEventListener('click', async function() {
        const id = this.dataset.id;
        
        try {
          const response = await fetch(`/teacher/instruction-plans/${id}/publish`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error publishing instruction plan:', error);
          showToast('An error occurred while publishing instruction plan', 'error');
        }
      });
    }

    // Archive plan button
    const archivePlanBtn = document.getElementById('archive-plan-btn');
    if (archivePlanBtn) {
      archivePlanBtn.addEventListener('click', async function() {
        const id = this.dataset.id;
        
        try {
          const response = await fetch(`/teacher/instruction-plans/${id}/archive`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error archiving instruction plan:', error);
          showToast('An error occurred while archiving instruction plan', 'error');
        }
      });
    }
  });
</script>
