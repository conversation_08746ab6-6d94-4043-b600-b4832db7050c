<%- include('../partials/teacher/header') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Lecture Reports</h1>
    <div class="flex space-x-2">
      <a href="/teacher/reports" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Back to Reports
      </a>
    </div>
  </div>

  <!-- Subject-wise Lecture Statistics -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Subject-wise Lecture Statistics</h2>
    
    <% if (lecturesBySubject.length === 0) { %>
      <div class="text-center py-8 text-gray-500">
        No lecture data available. Add lectures to view statistics.
      </div>
    <% } else { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Lectures</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cancelled</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% lecturesBySubject.forEach(subject => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= subject.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= subject.total_lectures %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    <%= subject.delivered || 0 %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    <%= subject.pending || 0 %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                    <%= subject.cancelled || 0 %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% 
                    const completionRate = subject.total_lectures > 0 
                      ? Math.round((subject.delivered || 0) / subject.total_lectures * 100) 
                      : 0;
                  %>
                  <div class="flex items-center">
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                      <div class="bg-teacher-primary h-2.5 rounded-full" style="width: <%= completionRate %>%"></div>
                    </div>
                    <span><%= completionRate %>%</span>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } %>
  </div>

  <!-- Lecture History Chart -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Lecture History (Last 6 Months)</h2>
    
    <% if (lectureHistory.length === 0) { %>
      <div class="text-center py-8 text-gray-500">
        No lecture history available. Add lectures to view history.
      </div>
    <% } else { %>
      <div class="h-80">
        <canvas id="lectureHistoryChart"></canvas>
      </div>
    <% } %>
  </div>

  <!-- Lecture Status Distribution -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Lecture Status Distribution</h2>
    
    <% 
      let totalLectures = 0;
      let deliveredLectures = 0;
      let pendingLectures = 0;
      let cancelledLectures = 0;
      
      lecturesBySubject.forEach(subject => {
        totalLectures += subject.total_lectures;
        deliveredLectures += subject.delivered || 0;
        pendingLectures += subject.pending || 0;
        cancelledLectures += subject.cancelled || 0;
      });
    %>
    
    <% if (totalLectures === 0) { %>
      <div class="text-center py-8 text-gray-500">
        No lecture data available. Add lectures to view status distribution.
      </div>
    <% } else { %>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="h-64">
          <canvas id="lectureStatusChart"></canvas>
        </div>
        <div class="grid grid-cols-1 gap-4">
          <div class="bg-green-50 p-4 rounded-lg">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-medium text-green-800">Delivered</h3>
                <p class="text-sm text-green-600">Completed lectures</p>
              </div>
              <div class="text-3xl font-bold text-green-700"><%= deliveredLectures %></div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-green-500 h-2.5 rounded-full" style="width: <%= Math.round(deliveredLectures / totalLectures * 100) %>%"></div>
              </div>
              <p class="text-xs text-right mt-1 text-green-600"><%= Math.round(deliveredLectures / totalLectures * 100) %>% of total</p>
            </div>
          </div>
          
          <div class="bg-yellow-50 p-4 rounded-lg">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-medium text-yellow-800">Pending</h3>
                <p class="text-sm text-yellow-600">Upcoming lectures</p>
              </div>
              <div class="text-3xl font-bold text-yellow-700"><%= pendingLectures %></div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-yellow-500 h-2.5 rounded-full" style="width: <%= Math.round(pendingLectures / totalLectures * 100) %>%"></div>
              </div>
              <p class="text-xs text-right mt-1 text-yellow-600"><%= Math.round(pendingLectures / totalLectures * 100) %>% of total</p>
            </div>
          </div>
          
          <div class="bg-red-50 p-4 rounded-lg">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-medium text-red-800">Cancelled</h3>
                <p class="text-sm text-red-600">Cancelled lectures</p>
              </div>
              <div class="text-3xl font-bold text-red-700"><%= cancelledLectures %></div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-red-500 h-2.5 rounded-full" style="width: <%= Math.round(cancelledLectures / totalLectures * 100) %>%"></div>
              </div>
              <p class="text-xs text-right mt-1 text-red-600"><%= Math.round(cancelledLectures / totalLectures * 100) %>% of total</p>
            </div>
          </div>
        </div>
      </div>
    <% } %>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    <% if (lectureHistory.length > 0) { %>
      // Lecture History Chart
      const historyCtx = document.getElementById('lectureHistoryChart').getContext('2d');
      new Chart(historyCtx, {
        type: 'bar',
        data: {
          labels: [<% lectureHistory.forEach(month => { %>'<%= month.month %>', <% }); %>],
          datasets: [{
            label: 'Number of Lectures',
            data: [<% lectureHistory.forEach(month => { %><%= month.total_lectures %>, <% }); %>],
            backgroundColor: '#4a6da7',
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                title: function(tooltipItems) {
                  const monthYear = tooltipItems[0].label;
                  const date = new Date(monthYear + '-01');
                  return date.toLocaleString('default', { month: 'long', year: 'numeric' });
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                precision: 0
              }
            }
          }
        }
      });
    <% } %>
    
    <% if (totalLectures > 0) { %>
      // Lecture Status Chart
      const statusCtx = document.getElementById('lectureStatusChart').getContext('2d');
      new Chart(statusCtx, {
        type: 'doughnut',
        data: {
          labels: ['Delivered', 'Pending', 'Cancelled'],
          datasets: [{
            data: [<%= deliveredLectures %>, <%= pendingLectures %>, <%= cancelledLectures %>],
            backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          },
          cutout: '70%'
        }
      });
    <% } %>
  });
</script>

<%- include('../partials/teacher/footer') %>
