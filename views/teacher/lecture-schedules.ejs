

<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">My Lecture Schedules</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Current Lecture Schedules</h3>

        <% if (schedules && schedules.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Day</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Class</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Classroom</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Semester</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% schedules.forEach(schedule => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.day_of_week %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.start_time_formatted %> - <%= schedule.end_time_formatted %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.class_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.classroom %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= schedule.semester %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= schedule.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= schedule.is_active ? 'Active' : 'Inactive' %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <button
                        class="edit-schedule-btn text-sm px-2 py-1 rounded bg-blue-500 text-white mr-1"
                        data-id="<%= schedule.id %>"
                        data-day="<%= schedule.day_of_week %>"
                        data-start="<%= schedule.start_time %>"
                        data-end="<%= schedule.end_time %>"
                        data-classroom="<%= schedule.classroom %>"
                        data-semester="<%= schedule.semester %>"
                      >
                        Edit
                      </button>
                      <button
                        class="toggle-status-btn text-sm px-2 py-1 rounded <%= schedule.is_active ? 'bg-red-500 text-white' : 'bg-green-500 text-white' %>"
                        data-id="<%= schedule.id %>"
                        data-status="<%= schedule.is_active ? '0' : '1' %>"
                      >
                        <%= schedule.is_active ? 'Deactivate' : 'Activate' %>
                      </button>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You don't have any lecture schedules yet. Please add a schedule below.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Add Lecture Schedule</h3>

        <form id="add-schedule-form" class="bg-gray-50 p-4 rounded border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label for="assignment_id" class="block text-sm font-medium text-gray-700 mb-1">Class & Subject</label>
              <select id="assignment_id" name="assignment_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                <option value="">Select a class & subject</option>
                <% assignments.forEach(assignment => { %>
                  <option value="<%= assignment.id %>"><%= assignment.class_name %> - <%= assignment.subject_name %></option>
                <% }); %>
              </select>
            </div>

            <div>
              <label for="day_of_week" class="block text-sm font-medium text-gray-700 mb-1">Day of Week</label>
              <select id="day_of_week" name="day_of_week" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                <option value="">Select a day</option>
                <% days.forEach(day => { %>
                  <option value="<%= day %>"><%= day %></option>
                <% }); %>
              </select>
            </div>

            <div>
              <label for="start_time" class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
              <input type="time" id="start_time" name="start_time" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
            </div>

            <div>
              <label for="end_time" class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
              <input type="time" id="end_time" name="end_time" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
            </div>

            <div>
              <label for="classroom" class="block text-sm font-medium text-gray-700 mb-1">Classroom</label>
              <input type="text" id="classroom" name="classroom" placeholder="e.g., Classroom 1" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
            </div>

            <div>
              <label for="semester" class="block text-sm font-medium text-gray-700 mb-1">Semester</label>
              <select id="semester" name="semester" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                <option value="First">First</option>
                <option value="Second">Second</option>
              </select>
            </div>
          </div>

          <div class="mt-4">
            <button type="submit" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
              Add Schedule
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">Weekly Schedule Overview</h2>
    </div>

    <div class="p-4">
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
          <thead>
            <tr>
              <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
              <% days.forEach(day => { %>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"><%= day %></th>
              <% }); %>
            </tr>
          </thead>
          <tbody>
            <%
              // Define time slots
              const timeSlots = [
                { start: '08:00', end: '09:00' },
                { start: '09:00', end: '10:00' },
                { start: '10:00', end: '11:00' },
                { start: '11:00', end: '12:00' },
                { start: '12:00', end: '13:00' },
                { start: '13:00', end: '14:00' },
                { start: '14:00', end: '15:00' },
                { start: '15:00', end: '16:00' }
              ];

              // Function to check if a schedule is in a time slot
              const isInTimeSlot = (schedule, slot) => {
                const scheduleStart = schedule.start_time.substring(0, 5);
                const scheduleEnd = schedule.end_time.substring(0, 5);
                return scheduleStart === slot.start;
              };
            %>

            <% timeSlots.forEach(slot => { %>
              <tr>
                <td class="py-2 px-4 border-b border-gray-200 font-medium"><%= slot.start %> - <%= slot.end %></td>
                <% days.forEach(day => { %>
                  <td class="py-2 px-4 border-b border-gray-200">
                    <%
                      const daySchedules = schedules.filter(s => s.day_of_week === day && isInTimeSlot(s, slot) && s.is_active);
                      if (daySchedules.length > 0) {
                        const schedule = daySchedules[0];
                    %>
                      <div class="bg-teacher-primary text-white p-2 rounded text-xs">
                        <div class="font-semibold"><%= schedule.subject_name %></div>
                        <div><%= schedule.class_name %></div>
                        <div><%= schedule.classroom %></div>
                      </div>
                    <% } %>
                  </td>
                <% }); %>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Edit Schedule Modal -->
<div id="edit-schedule-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
    <h3 class="text-lg font-semibold mb-4">Edit Lecture Schedule</h3>

    <form id="edit-schedule-form">
      <input type="hidden" id="edit_schedule_id" name="id">

      <div class="mb-4">
        <label for="edit_day_of_week" class="block text-sm font-medium text-gray-700 mb-1">Day of Week</label>
        <select id="edit_day_of_week" name="day_of_week" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          <% days.forEach(day => { %>
            <option value="<%= day %>"><%= day %></option>
          <% }); %>
        </select>
      </div>

      <div class="mb-4">
        <label for="edit_start_time" class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
        <input type="time" id="edit_start_time" name="start_time" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
      </div>

      <div class="mb-4">
        <label for="edit_end_time" class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
        <input type="time" id="edit_end_time" name="end_time" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
      </div>

      <div class="mb-4">
        <label for="edit_classroom" class="block text-sm font-medium text-gray-700 mb-1">Classroom</label>
        <input type="text" id="edit_classroom" name="classroom" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
      </div>

      <div class="mb-4">
        <label for="edit_semester" class="block text-sm font-medium text-gray-700 mb-1">Semester</label>
        <select id="edit_semester" name="semester" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
          <option value="First">First</option>
          <option value="Second">Second</option>
        </select>
      </div>

      <div class="flex justify-end space-x-3">
        <button type="button" id="close-modal-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add schedule form submission
    const addScheduleForm = document.getElementById('add-schedule-form');
    if (addScheduleForm) {
      addScheduleForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const assignmentId = document.getElementById('assignment_id').value;
        const dayOfWeek = document.getElementById('day_of_week').value;
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;
        const classroom = document.getElementById('classroom').value;
        const semester = document.getElementById('semester').value;

        if (!assignmentId || !dayOfWeek || !startTime || !endTime) {
          showToast('Please fill in all required fields', 'error');
          return;
        }

        try {
          const response = await fetch('/api/teacher/lecture-schedules', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              assignment_id: assignmentId,
              day_of_week: dayOfWeek,
              start_time: startTime,
              end_time: endTime,
              classroom: classroom || 'Classroom 1',
              semester: semester || 'First'
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated list
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error adding lecture schedule:', error);
          showToast('An error occurred while adding lecture schedule', 'error');
        }
      });
    }

    // Toggle status buttons
    const toggleStatusButtons = document.querySelectorAll('.toggle-status-btn');
    toggleStatusButtons.forEach(button => {
      button.addEventListener('click', async function() {
        const id = this.dataset.id;
        const status = this.dataset.status;

        try {
          const response = await fetch('/api/teacher/lecture-schedules', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id,
              is_active: status === '1'
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error updating lecture schedule status:', error);
          showToast('An error occurred while updating lecture schedule status', 'error');
        }
      });
    });

    // Edit schedule buttons
    const editScheduleButtons = document.querySelectorAll('.edit-schedule-btn');
    const editScheduleModal = document.getElementById('edit-schedule-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const editScheduleForm = document.getElementById('edit-schedule-form');

    editScheduleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const id = this.dataset.id;
        const day = this.dataset.day;
        const start = this.dataset.start;
        const end = this.dataset.end;
        const classroom = this.dataset.classroom;
        const semester = this.dataset.semester;

        document.getElementById('edit_schedule_id').value = id;
        document.getElementById('edit_day_of_week').value = day;
        document.getElementById('edit_start_time').value = start.substring(0, 5);
        document.getElementById('edit_end_time').value = end.substring(0, 5);
        document.getElementById('edit_classroom').value = classroom;
        document.getElementById('edit_semester').value = semester;

        editScheduleModal.classList.remove('hidden');
      });
    });

    closeModalBtn.addEventListener('click', function() {
      editScheduleModal.classList.add('hidden');
    });

    editScheduleForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      const id = document.getElementById('edit_schedule_id').value;
      const dayOfWeek = document.getElementById('edit_day_of_week').value;
      const startTime = document.getElementById('edit_start_time').value;
      const endTime = document.getElementById('edit_end_time').value;
      const classroom = document.getElementById('edit_classroom').value;
      const semester = document.getElementById('edit_semester').value;

      if (!dayOfWeek || !startTime || !endTime) {
        showToast('Please fill in all required fields', 'error');
        return;
      }

      try {
        const response = await fetch('/api/teacher/lecture-schedules', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id,
            day_of_week: dayOfWeek,
            start_time: startTime,
            end_time: endTime,
            classroom,
            semester
          })
        });

        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          editScheduleModal.classList.add('hidden');
          // Reload the page to show the updated schedule
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          showToast(data.message, 'error');
        }
      } catch (error) {
        console.error('Error updating lecture schedule:', error);
        showToast('An error occurred while updating lecture schedule', 'error');
      }
    });
  });
</script>


