<!-- Teacher Lecture Management View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Lecture Management</h1>
      <p class="text-sm text-gray-600 mt-1">Manage and track all your lectures and classroom sessions</p>
    </div>
    <div>
      <button id="add-lecture-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md flex items-center transition">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add New Lecture
      </button>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-md p-4">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div>
        <label for="filter-date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
        <input type="date" id="filter-date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>
      <div>
        <label for="filter-class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
        <select id="filter-class" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Classes</option>
          <option value="Class 10-A">Class 10-A</option>
          <option value="Class 9-B">Class 9-B</option>
          <option value="Class 11-C">Class 11-C</option>
        </select>
      </div>
      <div>
        <label for="filter-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
        <select id="filter-subject" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Subjects</option>
          <option value="Mathematics">Mathematics</option>
          <option value="Science">Science</option>
          <option value="Computer Science">Computer Science</option>
        </select>
      </div>
      <div>
        <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="filter-status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Status</option>
          <option value="delivered">Delivered</option>
          <option value="pending">Pending</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>
    </div>
    <div class="mt-4 flex justify-end">
      <button id="apply-filters" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md transition">
        Apply Filters
      </button>
    </div>
  </div>

  <!-- Lectures Table -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (lectures && lectures.length > 0) { %>
            <% lectures.forEach(lecture => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.date %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.start_time %> - <%= lecture.end_time %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= lecture.class_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.topic %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <% if (lecture.status === 'delivered') { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Delivered
                    </span>
                  <% } else if (lecture.status === 'cancelled') { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                      Cancelled
                    </span>
                  <% } else { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button class="view-lecture text-blue-600 hover:text-blue-800" data-id="<%= lecture.id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </button>
                    <button class="edit-lecture text-green-600 hover:text-green-800" data-id="<%= lecture.id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                    <% if (lecture.status === 'pending') { %>
                      <button class="mark-delivered text-teacher-primary hover:text-teacher-secondary" data-id="<%= lecture.id %>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </button>
                      <button class="cancel-lecture text-red-600 hover:text-red-800" data-id="<%= lecture.id %>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </button>
                    <% } %>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } else { %>
            <tr>
              <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                No lectures found. Add a new lecture to get started.
              </td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Add/Edit Lecture Modal -->
<div id="lecture-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <h2 id="modal-title" class="text-xl font-bold text-gray-800 mb-4">Add New Lecture</h2>

    <form id="lecture-form">
      <input type="hidden" id="lecture-id" value="">

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label for="lecture-date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
          <input type="date" id="lecture-date" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
        <div>
          <label for="lecture-class" class="block text-sm font-medium text-gray-700 mb-1">Class *</label>
          <select id="lecture-class" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="">Select Class</option>
            <% if (classSections && classSections.length > 0) { %>
              <% classSections.forEach(section => { %>
                <option value="<%= section.id %>"><%= section.display_name %></option>
              <% }); %>
            <% } %>
          </select>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label for="lecture-start-time" class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
          <input type="time" id="lecture-start-time" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
        <div>
          <label for="lecture-end-time" class="block text-sm font-medium text-gray-700 mb-1">End Time *</label>
          <input type="time" id="lecture-end-time" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
      </div>

      <div class="mb-4">
        <label for="lecture-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
        <select id="lecture-subject" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">Select Subject</option>
          <option value="Mathematics">Mathematics</option>
          <option value="Science">Science</option>
          <option value="Computer Science">Computer Science</option>
        </select>
      </div>

      <div class="mb-4">
        <label for="lecture-topic" class="block text-sm font-medium text-gray-700 mb-1">Topic *</label>
        <input type="text" id="lecture-topic" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>

      <div class="mb-4">
        <label for="lecture-notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
        <textarea id="lecture-notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
      </div>

      <div id="status-field" class="mb-4 hidden">
        <label for="lecture-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="lecture-status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="pending">Pending</option>
          <option value="delivered">Delivered</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>

      <div class="flex justify-end space-x-3 mt-6">
        <button type="button" id="cancel-modal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">Cancel</button>
        <button type="submit" id="save-lecture" class="px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary transition">Save Lecture</button>
      </div>
    </form>
  </div>
</div>

<!-- View Lecture Modal -->
<div id="view-lecture-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Lecture Details</h2>

    <div class="space-y-4">
      <div>
        <h3 class="text-sm font-medium text-gray-500">Date & Time</h3>
        <p id="view-date-time" class="text-base text-gray-800">April 24, 2025 | 09:00 AM - 10:00 AM</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Class</h3>
        <p id="view-class" class="text-base text-gray-800">Class 10-A</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Subject</h3>
        <p id="view-subject" class="text-base text-gray-800">Mathematics</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Topic</h3>
        <p id="view-topic" class="text-base text-gray-800">Quadratic Equations</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Status</h3>
        <p id="view-status" class="text-base text-gray-800">
          <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Delivered
          </span>
        </p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Notes</h3>
        <p id="view-notes" class="text-base text-gray-800">Covered basic concepts and examples.</p>
      </div>
    </div>

    <div class="flex justify-end mt-6">
      <button type="button" id="close-view-modal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">Close</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Modal elements
    const lectureModal = document.getElementById('lecture-modal');
    const viewLectureModal = document.getElementById('view-lecture-modal');
    const modalTitle = document.getElementById('modal-title');
    const lectureForm = document.getElementById('lecture-form');
    const statusField = document.getElementById('status-field');

    // Buttons
    const addLectureBtn = document.getElementById('add-lecture-btn');
    const cancelModalBtn = document.getElementById('cancel-modal');
    const closeViewModalBtn = document.getElementById('close-view-modal');

    // Form fields
    const lectureIdInput = document.getElementById('lecture-id');
    const lectureDateInput = document.getElementById('lecture-date');
    const lectureClassInput = document.getElementById('lecture-class');
    const lectureStartTimeInput = document.getElementById('lecture-start-time');
    const lectureEndTimeInput = document.getElementById('lecture-end-time');
    const lectureSubjectInput = document.getElementById('lecture-subject');
    const lectureTopicInput = document.getElementById('lecture-topic');
    const lectureNotesInput = document.getElementById('lecture-notes');
    const lectureStatusInput = document.getElementById('lecture-status');

    // Show add lecture modal
    addLectureBtn.addEventListener('click', function() {
      modalTitle.textContent = 'Add New Lecture';
      lectureForm.reset();
      lectureIdInput.value = '';
      statusField.classList.add('hidden');
      lectureModal.classList.remove('hidden');
    });

    // Hide modals
    cancelModalBtn.addEventListener('click', function() {
      lectureModal.classList.add('hidden');
    });

    closeViewModalBtn.addEventListener('click', function() {
      viewLectureModal.classList.add('hidden');
    });

    // View lecture details
    document.querySelectorAll('.view-lecture').forEach(button => {
      button.addEventListener('click', function() {
        const lectureId = this.getAttribute('data-id');
        // In a real application, fetch the lecture details from the server
        // For this demo, we'll use mock data

        // Find the lecture in the table
        const lectureRow = this.closest('tr');
        const date = lectureRow.cells[0].textContent.trim();
        const time = lectureRow.cells[1].textContent.trim();
        const className = lectureRow.cells[2].textContent.trim();
        const subject = lectureRow.cells[3].textContent.trim();
        const topic = lectureRow.cells[4].textContent.trim();
        const status = lectureRow.cells[5].querySelector('span').textContent.trim();

        // Set the values in the view modal
        document.getElementById('view-date-time').textContent = `${date} | ${time}`;
        document.getElementById('view-class').textContent = className;
        document.getElementById('view-subject').textContent = subject;
        document.getElementById('view-topic').textContent = topic;

        // Set status with appropriate class
        let statusHTML = '';
        if (status === 'Delivered') {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Delivered</span>';
        } else if (status === 'Cancelled') {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Cancelled</span>';
        } else {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>';
        }
        document.getElementById('view-status').innerHTML = statusHTML;

        // For notes, we'll use mock data since it's not displayed in the table
        const lectureId_num = parseInt(lectureId);
        let notes = '';
        if (lectureId_num === 1) {
          notes = 'Covered basic concepts and examples';
        } else if (lectureId_num === 2) {
          notes = 'Discussed elements and their properties';
        } else {
          notes = 'No notes available';
        }
        document.getElementById('view-notes').textContent = notes;

        // Show the view modal
        viewLectureModal.classList.remove('hidden');
      });
    });

    // Edit lecture
    document.querySelectorAll('.edit-lecture').forEach(button => {
      button.addEventListener('click', function() {
        const lectureId = this.getAttribute('data-id');
        modalTitle.textContent = 'Edit Lecture';

        // Fetch the lecture details from the server
        fetch(`/api/teacher/lectures/${lectureId}`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              const lecture = data.lecture;

              // Populate the form
              lectureIdInput.value = lecture.id;
              lectureDateInput.value = lecture.date.split('T')[0]; // Format date
              lectureClassInput.value = lecture.class_section_id;
              lectureStartTimeInput.value = lecture.start_time;
              lectureEndTimeInput.value = lecture.end_time;
              lectureSubjectInput.value = lecture.subject_name;
              lectureTopicInput.value = lecture.topic;
              lectureNotesInput.value = lecture.notes || '';
              lectureStatusInput.value = lecture.status;

              // Show status field for editing
              statusField.classList.remove('hidden');

              // Show the modal
              lectureModal.classList.remove('hidden');
            } else {
              alert(data.message || 'Error fetching lecture details');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while fetching lecture details');

            // For demo purposes, use mock data if API fails
            const lectureRow = this.closest('tr');
            const date = lectureRow.cells[0].textContent.trim();
            const time = lectureRow.cells[1].textContent.trim();
            const times = time.split(' - ');
            const className = lectureRow.cells[2].textContent.trim();
            const subject = lectureRow.cells[3].textContent.trim();
            const topic = lectureRow.cells[4].textContent.trim();
            const status = lectureRow.cells[5].querySelector('span').textContent.trim().toLowerCase();

            // Find class section ID by display name
            let classSectionId = '';
            const classSelect = document.getElementById('lecture-class');
            for (let i = 0; i < classSelect.options.length; i++) {
              if (classSelect.options[i].text === className) {
                classSectionId = classSelect.options[i].value;
                break;
              }
            }

            // Populate the form with data from the table
            lectureIdInput.value = lectureId;
            lectureDateInput.value = formatDateForInput(date);
            lectureClassInput.value = classSectionId;
            lectureStartTimeInput.value = times[0];
            lectureEndTimeInput.value = times[1];
            lectureSubjectInput.value = subject;
            lectureTopicInput.value = topic;
            lectureNotesInput.value = '';
            lectureStatusInput.value = status;

            // Show status field for editing
            statusField.classList.remove('hidden');

            // Show the modal
            lectureModal.classList.remove('hidden');
          });
      });
    });

    // Helper function to format date for input
    function formatDateForInput(dateStr) {
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    }

    // Mark lecture as delivered
    document.querySelectorAll('.mark-delivered').forEach(button => {
      button.addEventListener('click', function() {
        const lectureId = this.getAttribute('data-id');

        // Send AJAX request to update the status
        fetch(`/api/teacher/lectures/${lectureId}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: 'delivered' })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Display success message
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = 'toast toast-success';
            toast.textContent = 'Lecture marked as delivered successfully';
            toastContainer.appendChild(toast);

            // Auto-remove toast after 3 seconds
            setTimeout(() => {
              toast.remove();
              // Reload the page to show the updated data
              window.location.reload();
            }, 1500);
          } else {
            alert(data.message || 'Error updating lecture status');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while updating lecture status');
        });
      });
    });

    // Cancel lecture
    document.querySelectorAll('.cancel-lecture').forEach(button => {
      button.addEventListener('click', function() {
        const lectureId = this.getAttribute('data-id');

        if (confirm('Are you sure you want to cancel this lecture?')) {
          // Send AJAX request to update the status
          fetch(`/api/teacher/lectures/${lectureId}/status`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: 'cancelled' })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Display success message
              const toastContainer = document.getElementById('toast-container');
              const toast = document.createElement('div');
              toast.className = 'toast toast-warning';
              toast.textContent = 'Lecture cancelled successfully';
              toastContainer.appendChild(toast);

              // Auto-remove toast after 3 seconds
              setTimeout(() => {
                toast.remove();
                // Reload the page to show the updated data
                window.location.reload();
              }, 1500);
            } else {
              alert(data.message || 'Error cancelling lecture');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling lecture');
          });
        }
      });
    });

    // Form submission
    lectureForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const formData = {
        date: lectureDateInput.value,
        class_section_id: lectureClassInput.value,
        start_time: lectureStartTimeInput.value,
        end_time: lectureEndTimeInput.value,
        subject_name: lectureSubjectInput.value,
        topic: lectureTopicInput.value,
        notes: lectureNotesInput.value
      };

      // If editing, include status
      if (lectureIdInput.value) {
        formData.status = lectureStatusInput.value;
      }

      // Send AJAX request to save the lecture
      fetch('/api/teacher/lectures', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Display success message
          const toastContainer = document.getElementById('toast-container');
          const toast = document.createElement('div');
          toast.className = 'toast toast-success';
          toast.textContent = lectureIdInput.value ? 'Lecture updated successfully' : 'Lecture added successfully';
          toastContainer.appendChild(toast);

          // Hide the modal
          lectureModal.classList.add('hidden');

          // Auto-remove toast after 3 seconds
          setTimeout(() => {
            toast.remove();
            // Reload the page to show the updated data
            window.location.reload();
          }, 1500);
        } else {
          // Display error message
          alert(data.message || 'Error saving lecture');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the lecture');
      });
    });

    // Add event listener for apply filters button
    document.getElementById('apply-filters').addEventListener('click', function() {
      const dateFilter = document.getElementById('filter-date').value;
      const classFilter = document.getElementById('filter-class').value;
      const subjectFilter = document.getElementById('filter-subject').value;
      const statusFilter = document.getElementById('filter-status').value;

      // Construct the URL with query parameters
      let url = '/teacher/lectures?';
      if (dateFilter) url += `date=${dateFilter}&`;
      if (classFilter) url += `class=${encodeURIComponent(classFilter)}&`;
      if (subjectFilter) url += `subject=${encodeURIComponent(subjectFilter)}&`;
      if (statusFilter) url += `status=${statusFilter}&`;

      // Remove trailing & if present
      if (url.endsWith('&')) url = url.slice(0, -1);

      // Redirect to the filtered URL
      window.location.href = url;
    });
  });
</script>

<style>
  /* Toast styling */
  .toast {
    padding: 1rem;
    border-radius: 0.375rem;
    color: white;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .toast-success {
    background-color: #10B981;
  }

  .toast-warning {
    background-color: #F59E0B;
  }

  .toast-error {
    background-color: #EF4444;
  }
</style>