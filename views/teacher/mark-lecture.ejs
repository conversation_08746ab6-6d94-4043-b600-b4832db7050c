<!-- Mark Lecture Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Mark Lecture as Delivered</h2>
  </div>
  <div class="p-6">
    <% if (pendingLectures && pendingLectures.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% pendingLectures.forEach(lecture => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.date %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.start_time %> - <%= lecture.end_time %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= lecture.class_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lecture.topic %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-teacher-primary hover:text-teacher-secondary mr-2" onclick="markAsDelivered(<%= lecture.id %>)">
                    Mark Delivered
                  </button>
                  <button class="text-yellow-600 hover:text-yellow-800" onclick="reschedule(<%= lecture.id %>)">
                    Reschedule
                  </button>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No pending lectures</h3>
        <p class="mt-1 text-sm text-gray-500">All your lectures have been marked as delivered.</p>
      </div>
    <% } %>
  </div>
</div>

<%- contentFor('script') %>
<script>
  // Function to mark a lecture as delivered
  function markAsDelivered(lectureId) {
    // Use the confirm dialog from common.js
    initConfirmDialog(
      'Mark as Delivered',
      'Are you sure you want to mark this lecture as delivered?',
      () => {
        // Use the apiCall function from common.js
        apiCall(`/api/teacher/lectures/${lectureId}/status`, 'PUT', { status: 'delivered' })
          .then(response => {
            if (response.success) {
              showToast('success', 'Success', 'Lecture marked as delivered');
              // Reload page after a delay
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              showToast('error', 'Error', response.message || 'Failed to update lecture status');
            }
          })
          .catch(error => {
            console.error('Error updating lecture status:', error);
            showToast('error', 'Error', 'Failed to update lecture status');
          });
      }
    );
  }

  // Function to reschedule a lecture
  function reschedule(lectureId) {
    // This is a temporary fix until the reschedule page is implemented
    showToast('info', 'Reschedule', 'Reschedule functionality will be implemented soon');
  }
</script>
