<%- contentFor('body') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Practical Details</h1>
    <div class="flex space-x-2">
      <a href="/teacher/practicals" class="px-3 py-1 bg-gray-300 rounded hover:bg-gray-400 transition text-sm flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Practicals
      </a>
    </div>
  </div>
  
  <!-- Practical Details Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold"><%= practical.practical_topic %></h2>
      <p class="text-sm opacity-90"><%= practical.subject_name %></p>
    </div>
    
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium text-gray-800 mb-4">Practical Information</h3>
          
          <div class="space-y-3">
            <div>
              <p class="text-sm text-gray-600">Class</p>
              <p class="font-medium">
                <%= practical.class_name %> <%= practical.section_name %> 
                <% if (practical.trade_name) { %>
                  (<%= practical.trade_name %>)
                <% } %>
              </p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Date</p>
              <p class="font-medium"><%= new Date(practical.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) %></p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Time</p>
              <p class="font-medium"><%= practical.start_time %> - <%= practical.end_time %></p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Location</p>
              <p class="font-medium"><%= practical.location || 'Science Lab' %></p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Status</p>
              <p class="font-medium">
                <% if (practical.status === 'scheduled') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    Scheduled
                  </span>
                <% } else if (practical.status === 'completed') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    Completed
                  </span>
                <% } else if (practical.status === 'cancelled') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                    Cancelled
                  </span>
                <% } else if (practical.status === 'rescheduled') { %>
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Rescheduled
                  </span>
                <% } %>
              </p>
            </div>
          </div>
        </div>
        
        <div>
          <h3 class="text-lg font-medium text-gray-800 mb-4">Practical Details</h3>
          
          <div class="space-y-3">
            <div>
              <p class="text-sm text-gray-600">Description</p>
              <p class="font-medium"><%= practical.description || 'No description provided' %></p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Materials Required</p>
              <p class="font-medium"><%= practical.materials || 'No materials specified' %></p>
            </div>
            
            <div>
              <p class="text-sm text-gray-600">Notes</p>
              <p class="font-medium"><%= practical.notes || 'No notes provided' %></p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end space-x-3 mt-6">
        <% if (practical.status === 'scheduled') { %>
          <button id="mark-completed-btn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition">
            Mark as Completed
          </button>
          <button id="reschedule-btn" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition">
            Reschedule
          </button>
          <button id="cancel-btn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">
            Cancel
          </button>
        <% } else if (practical.status === 'completed') { %>
          <button id="view-records-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">
            View Student Records
          </button>
        <% } else if (practical.status === 'cancelled') { %>
          <button id="reschedule-btn" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition">
            Reschedule
          </button>
        <% } %>
        <a href="/teacher/practicals/<%= practical.id %>/edit" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
          Edit
        </a>
      </div>
    </div>
  </div>
  
  <!-- Student Records Section -->
  <% if (studentRecords && studentRecords.length > 0) { %>
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="bg-teacher-primary text-white p-4">
        <h2 class="text-xl font-semibold">Student Records</h2>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% studentRecords.forEach(record => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= record.student_name %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= new Date(record.submission_date).toLocaleDateString() %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (record.status === 'pending') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } else if (record.status === 'submitted') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      Submitted
                    </span>
                  <% } else if (record.status === 'graded') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Graded
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= record.grade || '-' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-teacher-primary hover:text-teacher-secondary view-record" data-id="<%= record.id %>">View</button>
                  <% if (record.status === 'submitted') { %>
                    <button class="ml-3 text-teacher-primary hover:text-teacher-secondary grade-record" data-id="<%= record.id %>">Grade</button>
                  <% } %>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
  <% } else { %>
    <div class="bg-white rounded-lg shadow-md p-6 text-center">
      <p class="text-gray-600">No student records found for this practical.</p>
      <% if (practical.status === 'scheduled') { %>
        <p class="text-gray-600 mt-2">Records will be available after the practical is completed.</p>
      <% } %>
    </div>
  <% } %>
</div>

<!-- Confirmation Modal -->
<div id="confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
    <div class="bg-teacher-primary text-white px-4 py-3 flex justify-between items-center">
      <h3 class="text-lg font-semibold" id="modal-title">Confirm Action</h3>
      <button class="close-modal text-white hover:text-gray-200">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="p-6">
      <p class="mb-4" id="modal-message">Are you sure you want to perform this action?</p>
      
      <div class="flex justify-end space-x-3">
        <button class="close-modal px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
        <button id="confirm-action-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Confirm</button>
      </div>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const confirmationModal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const confirmActionBtn = document.getElementById('confirm-action-btn');
    const closeButtons = document.querySelectorAll('.close-modal');
    
    // Close modal
    closeButtons.forEach(button => {
      button.addEventListener('click', function() {
        confirmationModal.classList.add('hidden');
      });
    });
    
    // Mark as Completed button
    const markCompletedBtn = document.getElementById('mark-completed-btn');
    if (markCompletedBtn) {
      markCompletedBtn.addEventListener('click', function() {
        modalTitle.textContent = 'Mark as Completed';
        modalMessage.textContent = 'Are you sure you want to mark this practical as completed?';
        confirmActionBtn.className = 'px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition';
        
        confirmActionBtn.onclick = function() {
          // In a real application, you would send an API request to update the practical status
          showToast('success', 'Success', 'Practical marked as completed');
          confirmationModal.classList.add('hidden');
          
          // Reload the page after a delay
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        };
        
        confirmationModal.classList.remove('hidden');
      });
    }
    
    // Reschedule button
    const rescheduleBtn = document.getElementById('reschedule-btn');
    if (rescheduleBtn) {
      rescheduleBtn.addEventListener('click', function() {
        showToast('info', 'Coming Soon', 'Reschedule functionality will be available soon');
      });
    }
    
    // Cancel button
    const cancelBtn = document.getElementById('cancel-btn');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', function() {
        modalTitle.textContent = 'Cancel Practical';
        modalMessage.textContent = 'Are you sure you want to cancel this practical? This action cannot be undone.';
        confirmActionBtn.className = 'px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition';
        
        confirmActionBtn.onclick = function() {
          // In a real application, you would send an API request to update the practical status
          showToast('success', 'Success', 'Practical cancelled');
          confirmationModal.classList.add('hidden');
          
          // Reload the page after a delay
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        };
        
        confirmationModal.classList.remove('hidden');
      });
    }
    
    // View Records button
    const viewRecordsBtn = document.getElementById('view-records-btn');
    if (viewRecordsBtn) {
      viewRecordsBtn.addEventListener('click', function() {
        window.location.href = `/teacher/practicals/<%= practical.id %>/records`;
      });
    }
    
    // View record buttons
    const viewRecordButtons = document.querySelectorAll('.view-record');
    viewRecordButtons.forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');
        showToast('info', 'Coming Soon', 'View record functionality will be available soon');
      });
    });
    
    // Grade record buttons
    const gradeRecordButtons = document.querySelectorAll('.grade-record');
    gradeRecordButtons.forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');
        showToast('info', 'Coming Soon', 'Grade record functionality will be available soon');
      });
    });
    
    // Function to show toast notifications
    function showToast(type, title, message) {
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      
      const toastContent = `
        <div class="font-medium">${title}</div>
        <div class="text-sm">${message}</div>
      `;
      
      toast.innerHTML = toastContent;
      
      const toastContainer = document.getElementById('toast-container');
      toastContainer.appendChild(toast);
      
      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
  });
</script>

<style>
  .toast {
    @apply mb-3 px-4 py-3 rounded shadow-md text-white;
  }
  
  .toast-success {
    @apply bg-green-500;
  }
  
  .toast-error {
    @apply bg-red-500;
  }
  
  .toast-warning {
    @apply bg-yellow-500;
  }
  
  .toast-info {
    @apply bg-blue-500;
  }
</style>
