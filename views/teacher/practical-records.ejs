<!-- Student Practical Records View -->
<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">Student Practical Records</h1>
      <p class="text-sm text-gray-600 mt-1">Track and manage student practical work and assignments</p>
    </div>
    <div>
      <button id="export-csv-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md flex items-center transition">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
        </svg>
        Export CSV
      </button>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-md p-4">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label for="filter-class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
        <select id="filter-class" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Classes</option>
          <option value="Class 10-A">Class 10-A</option>
          <option value="Class 9-B">Class 9-B</option>
          <option value="Class 11-C">Class 11-C</option>
        </select>
      </div>
      <div>
        <label for="filter-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
        <select id="filter-subject" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Subjects</option>
          <option value="Computer Science">Computer Science</option>
          <option value="Physics">Physics</option>
          <option value="Chemistry">Chemistry</option>
          <option value="Biology">Biology</option>
        </select>
      </div>
      <div>
        <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="filter-status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Status</option>
          <option value="submitted">Submitted</option>
          <option value="graded">Graded</option>
          <option value="pending">Pending</option>
        </select>
      </div>
    </div>
    <div class="mt-4">
      <label for="search-student" class="block text-sm font-medium text-gray-700 mb-1">Search Student</label>
      <div class="relative">
        <input type="text" id="search-student" placeholder="Search by student name..." class="w-full rounded-md border-gray-300 pl-10 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>
    </div>
    <div class="mt-4 flex justify-end">
      <button id="apply-filters" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md transition">
        Apply Filters
      </button>
    </div>
  </div>

  <!-- Student Records Table -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Practical Topic</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (studentRecords && studentRecords.length > 0) { %>
            <% studentRecords.forEach(record => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-full flex items-center justify-center">
                      <span class="text-gray-500 font-medium"><%= record.student_name.split(' ').map(n => n[0]).join('') %></span>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900"><%= record.student_name %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= record.class_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= record.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= record.practical_topic %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= record.submission_date ? record.submission_date : 'Not submitted' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (record.status === 'submitted') { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      Submitted
                    </span>
                  <% } else if (record.status === 'graded') { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Graded
                    </span>
                  <% } else { %>
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= record.grade ? record.grade : '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button class="view-record text-blue-600 hover:text-blue-800" data-id="<%= record.id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </button>
                    <% if (record.status === 'submitted' || record.status === 'graded') { %>
                      <button class="grade-record text-green-600 hover:text-green-800" data-id="<%= record.id %>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                      <button class="download-record text-teacher-primary hover:text-teacher-secondary" data-id="<%= record.id %>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                      </button>
                    <% } %>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } else { %>
            <tr>
              <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
                No practical records found.
              </td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Grade Record Modal -->
<div id="grade-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Grade Practical Record</h2>

    <form id="grade-form">
      <input type="hidden" id="record-id" value="">

      <div class="mb-4">
        <div class="flex justify-between">
          <div>
            <p class="text-sm font-medium text-gray-500">Student</p>
            <p id="student-name" class="text-base font-medium text-gray-900">John Smith</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Class</p>
            <p id="student-class" class="text-base font-medium text-gray-900">Class 10-A</p>
          </div>
        </div>
      </div>

      <div class="mb-4">
        <p class="text-sm font-medium text-gray-500">Practical Topic</p>
        <p id="practical-topic" class="text-base font-medium text-gray-900">HTML & CSS Basics</p>
      </div>

      <div class="mb-4">
        <p class="text-sm font-medium text-gray-500">Submission Date</p>
        <p id="submission-date" class="text-base font-medium text-gray-900">2025-04-20</p>
      </div>

      <div class="mb-4">
        <label for="grade-select" class="block text-sm font-medium text-gray-700 mb-1">Grade *</label>
        <select id="grade-select" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">Select Grade</option>
          <option value="A+">A+</option>
          <option value="A">A</option>
          <option value="B+">B+</option>
          <option value="B">B</option>
          <option value="C+">C+</option>
          <option value="C">C</option>
          <option value="D">D</option>
          <option value="F">F</option>
        </select>
      </div>

      <div class="mb-4">
        <label for="feedback" class="block text-sm font-medium text-gray-700 mb-1">Feedback</label>
        <textarea id="feedback" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
      </div>

      <div class="flex justify-end space-x-3 mt-6">
        <button type="button" id="cancel-grade-modal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">Cancel</button>
        <button type="submit" id="save-grade" class="px-4 py-2 bg-teacher-primary text-white rounded-md hover:bg-teacher-secondary transition">Save Grade</button>
      </div>
    </form>
  </div>
</div>

<!-- View Record Modal -->
<div id="view-record-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Practical Record Details</h2>

    <div class="space-y-4">
      <div>
        <h3 class="text-sm font-medium text-gray-500">Student</h3>
        <p id="view-student-name" class="text-base text-gray-800">John Smith</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Class</h3>
        <p id="view-student-class" class="text-base text-gray-800">Class 10-A</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Subject</h3>
        <p id="view-subject" class="text-base text-gray-800">Computer Science</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Practical Topic</h3>
        <p id="view-practical-topic" class="text-base text-gray-800">HTML & CSS Basics</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Submission Date</h3>
        <p id="view-submission-date" class="text-base text-gray-800">2025-04-20</p>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500">Status</h3>
        <p id="view-status" class="text-base text-gray-800">
          <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Submitted
          </span>
        </p>
      </div>

      <div id="view-grade-container">
        <h3 class="text-sm font-medium text-gray-500">Grade</h3>
        <p id="view-grade" class="text-base text-gray-800">A</p>
      </div>

      <div id="view-feedback-container">
        <h3 class="text-sm font-medium text-gray-500">Teacher Feedback</h3>
        <p id="view-feedback" class="text-base text-gray-800">Excellent work on structuring the HTML. CSS could use some improvement.</p>
      </div>
    </div>

    <div class="flex justify-end mt-6">
      <button type="button" id="close-view-record-modal" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">Close</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Modal elements
    const gradeModal = document.getElementById('grade-modal');
    const viewRecordModal = document.getElementById('view-record-modal');

    // Buttons
    const exportCsvBtn = document.getElementById('export-csv-btn');
    const cancelGradeModalBtn = document.getElementById('cancel-grade-modal');
    const closeViewRecordModalBtn = document.getElementById('close-view-record-modal');

    // Form elements
    const gradeForm = document.getElementById('grade-form');
    const recordIdInput = document.getElementById('record-id');
    const gradeSelect = document.getElementById('grade-select');
    const feedbackInput = document.getElementById('feedback');

    // Toast notification function
    function showToast(message, type = 'success') {
      const toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        const newToastContainer = document.createElement('div');
        newToastContainer.id = 'toast-container';
        newToastContainer.className = 'fixed bottom-4 right-4 z-50';
        document.body.appendChild(newToastContainer);
      }

      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      toast.textContent = message;

      const container = document.getElementById('toast-container');
      container.appendChild(toast);

      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    // Hide modals
    cancelGradeModalBtn.addEventListener('click', function() {
      gradeModal.classList.add('hidden');
    });

    closeViewRecordModalBtn.addEventListener('click', function() {
      viewRecordModal.classList.add('hidden');
    });

    // Export CSV
    exportCsvBtn.addEventListener('click', function() {
      // Get all visible records from the table
      const rows = Array.from(document.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');

      if (rows.length === 0) {
        showToast('No records to export', 'warning');
        return;
      }

      // Create CSV content
      let csvContent = 'Student Name,Class,Subject,Practical Topic,Submission Date,Status,Grade\n';

      rows.forEach(row => {
        // Skip the "No records found" row
        if (row.cells.length === 1) return;

        const studentName = row.cells[0].querySelector('.text-gray-900').textContent.trim();
        const className = row.cells[1].textContent.trim();
        const subject = row.cells[2].textContent.trim();
        const topic = row.cells[3].textContent.trim();
        const submissionDate = row.cells[4].textContent.trim();
        const status = row.cells[5].querySelector('span').textContent.trim();
        const grade = row.cells[6].textContent.trim();

        // Escape fields that might contain commas
        const escapeCsv = (field) => {
          if (field.includes(',') || field.includes('"') || field.includes('\n')) {
            return `"${field.replace(/"/g, '""')}"`;
          }
          return field;
        };

        csvContent += `${escapeCsv(studentName)},${escapeCsv(className)},${escapeCsv(subject)},${escapeCsv(topic)},${escapeCsv(submissionDate)},${escapeCsv(status)},${escapeCsv(grade)}\n`;
      });

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `practical-records-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.display = 'none';

      // Add to document, click and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showToast('CSV export successful', 'success');
    });

    // View record details
    document.querySelectorAll('.view-record').forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');

        // Find the record in the table
        const recordRow = this.closest('tr');
        const studentName = recordRow.cells[0].querySelector('.text-gray-900').textContent.trim();
        const className = recordRow.cells[1].textContent.trim();
        const subject = recordRow.cells[2].textContent.trim();
        const topic = recordRow.cells[3].textContent.trim();
        const submissionDate = recordRow.cells[4].textContent.trim();
        const status = recordRow.cells[5].querySelector('span').textContent.trim();
        const grade = recordRow.cells[6].textContent.trim();

        // Set values in the view modal
        document.getElementById('view-student-name').textContent = studentName;
        document.getElementById('view-student-class').textContent = className;
        document.getElementById('view-subject').textContent = subject;
        document.getElementById('view-practical-topic').textContent = topic;
        document.getElementById('view-submission-date').textContent = submissionDate;

        // Set status with appropriate class
        let statusHTML = '';
        if (status === 'Submitted') {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Submitted</span>';
        } else if (status === 'Graded') {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Graded</span>';
        } else {
          statusHTML = '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>';
        }
        document.getElementById('view-status').innerHTML = statusHTML;

        // Set grade if available
        const gradeContainer = document.getElementById('view-grade-container');
        const feedbackContainer = document.getElementById('view-feedback-container');

        if (grade && grade !== '-') {
          document.getElementById('view-grade').textContent = grade;
          gradeContainer.classList.remove('hidden');

          // Mock feedback (in a real app, this would come from the database)
          let feedback = '';
          if (recordId == 1) {
            feedback = 'Excellent work on structuring the HTML. CSS could use some improvement.';
          } else if (recordId == 2) {
            feedback = 'Good effort, but needs more attention to detail on responsive design.';
          }

          document.getElementById('view-feedback').textContent = feedback || 'No feedback provided.';
          feedbackContainer.classList.remove('hidden');
        } else {
          gradeContainer.classList.add('hidden');
          feedbackContainer.classList.add('hidden');
        }

        // Show the view modal
        viewRecordModal.classList.remove('hidden');
      });
    });

    // Grade record
    document.querySelectorAll('.grade-record').forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');

        // Find the record in the table
        const recordRow = this.closest('tr');
        const studentName = recordRow.cells[0].querySelector('.text-gray-900').textContent.trim();
        const className = recordRow.cells[1].textContent.trim();
        const topic = recordRow.cells[3].textContent.trim();
        const submissionDate = recordRow.cells[4].textContent.trim();
        const grade = recordRow.cells[6].textContent.trim();

        // Set values in the grade modal
        recordIdInput.value = recordId;
        document.getElementById('student-name').textContent = studentName;
        document.getElementById('student-class').textContent = className;
        document.getElementById('practical-topic').textContent = topic;
        document.getElementById('submission-date').textContent = submissionDate;

        // Set existing grade if available
        if (grade && grade !== '-') {
          gradeSelect.value = grade;

          // Mock feedback (in a real app, this would come from the database)
          let feedback = '';
          if (recordId == 1) {
            feedback = 'Excellent work on structuring the HTML. CSS could use some improvement.';
          } else if (recordId == 2) {
            feedback = 'Good effort, but needs more attention to detail on responsive design.';
          }

          feedbackInput.value = feedback;
        } else {
          gradeSelect.value = '';
          feedbackInput.value = '';
        }

        // Show the grade modal
        gradeModal.classList.remove('hidden');
      });
    });

    // Download record
    document.querySelectorAll('.download-record').forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');

        // Find the record in the table
        const recordRow = this.closest('tr');
        const studentName = recordRow.cells[0].querySelector('.text-gray-900').textContent.trim();
        const className = recordRow.cells[1].textContent.trim();
        const subject = recordRow.cells[2].textContent.trim();
        const topic = recordRow.cells[3].textContent.trim();
        const submissionDate = recordRow.cells[4].textContent.trim();
        const status = recordRow.cells[5].querySelector('span').textContent.trim();
        const grade = recordRow.cells[6].textContent.trim();

        // Create a PDF-like text content (in a real app, you'd use a PDF library)
        let content = `
PRACTICAL RECORD DETAILS
------------------------
Student: ${studentName}
Class: ${className}
Subject: ${subject}
Practical Topic: ${topic}
Submission Date: ${submissionDate}
Status: ${status}
${grade !== '-' ? `Grade: ${grade}` : ''}

FEEDBACK:
${recordId == 1 ? 'Excellent work on structuring the HTML. CSS could use some improvement.' :
  recordId == 2 ? 'Good effort, but needs more attention to detail on responsive design.' :
  'No feedback provided.'}
`;

        // Create a blob and download link
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `practical-record-${studentName.replace(/\s+/g, '-')}-${topic.replace(/\s+/g, '-')}.txt`);
        link.style.display = 'none';

        // Add to document, click and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showToast('Record downloaded successfully', 'success');
      });
    });

    // Form submission
    gradeForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const recordId = recordIdInput.value;
      const grade = gradeSelect.value;
      const feedback = feedbackInput.value;

      if (!grade) {
        alert('Please select a grade');
        return;
      }

      // Send API request to save the grade
      fetch(`/api/teacher/practical-records/${recordId}/grade`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          grade: grade,
          feedback: feedback
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to save grade');
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Display success message
          showToast('Grade saved successfully', 'success');

          // Hide the modal
          gradeModal.classList.add('hidden');

          // Reload the page after a delay to show updated data
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          throw new Error(data.message || 'Failed to save grade');
        }
      })
      .catch(error => {
        console.error('Error saving grade:', error);
        showToast(error.message, 'error');
      });
    });

    // Filter application
    document.getElementById('apply-filters').addEventListener('click', function() {
      const classFilter = document.getElementById('filter-class').value.toLowerCase();
      const subjectFilter = document.getElementById('filter-subject').value.toLowerCase();
      const statusFilter = document.getElementById('filter-status').value.toLowerCase();

      const rows = document.querySelectorAll('tbody tr');

      rows.forEach(row => {
        // Skip the "No records found" row
        if (row.cells.length === 1) return;

        const className = row.cells[1].textContent.trim().toLowerCase();
        const subject = row.cells[2].textContent.trim().toLowerCase();
        const status = row.cells[5].querySelector('span').textContent.trim().toLowerCase();

        const matchesClass = !classFilter || className.includes(classFilter);
        const matchesSubject = !subjectFilter || subject.includes(subjectFilter);
        const matchesStatus = !statusFilter || status === statusFilter;

        if (matchesClass && matchesSubject && matchesStatus) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });

      showToast('Filters applied', 'success');
    });

    // Search functionality
    document.getElementById('search-student').addEventListener('input', function() {
      const searchText = this.value.toLowerCase();
      const rows = document.querySelectorAll('tbody tr');

      rows.forEach(row => {
        const studentName = row.cells[0].querySelector('.text-gray-900').textContent.toLowerCase();
        if (studentName.includes(searchText)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  });
</script>

<style>
  /* Toast styling (same as lecture page) */
  .toast {
    padding: 1rem;
    border-radius: 0.375rem;
    color: white;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .toast-success {
    background-color: #10B981;
  }

  .toast-warning {
    background-color: #F59E0B;
  }

  .toast-error {
    background-color: #EF4444;
  }
</style>