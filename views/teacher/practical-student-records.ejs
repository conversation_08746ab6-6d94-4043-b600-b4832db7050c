<%- contentFor('body') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Practical Student Records</h1>
    <div class="flex space-x-2">
      <a href="/teacher/practicals/<%= practical.id %>" class="px-3 py-1 bg-gray-300 rounded hover:bg-gray-400 transition text-sm flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Practical
      </a>
    </div>
  </div>
  
  <!-- Practical Info Card -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold"><%= practical.practical_topic %></h2>
      <p class="text-sm opacity-90"><%= practical.subject_name %></p>
    </div>
    
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p class="text-sm text-gray-600">Class</p>
          <p class="font-medium">
            <%= practical.class_name %> <%= practical.section_name %> 
            <% if (practical.trade_name) { %>
              (<%= practical.trade_name %>)
            <% } %>
          </p>
        </div>
        
        <div>
          <p class="text-sm text-gray-600">Date</p>
          <p class="font-medium"><%= new Date(practical.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) %></p>
        </div>
        
        <div>
          <p class="text-sm text-gray-600">Status</p>
          <p class="font-medium">
            <% if (practical.status === 'scheduled') { %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                Scheduled
              </span>
            <% } else if (practical.status === 'completed') { %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Completed
              </span>
            <% } else if (practical.status === 'cancelled') { %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                Cancelled
              </span>
            <% } else if (practical.status === 'rescheduled') { %>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                Rescheduled
              </span>
            <% } %>
          </p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Student Records Table -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
      <h2 class="text-xl font-semibold">Student Records</h2>
      <div class="flex space-x-2">
        <button id="export-csv-btn" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
          </svg>
          Export CSV
        </button>
      </div>
    </div>
    
    <% if (studentRecords && studentRecords.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% studentRecords.forEach(record => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= record.student_name %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= new Date(record.submission_date).toLocaleDateString() %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (record.status === 'pending') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } else if (record.status === 'submitted') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      Submitted
                    </span>
                  <% } else if (record.status === 'graded') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Graded
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= record.grade || '-' %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-teacher-primary hover:text-teacher-secondary view-record" data-id="<%= record.id %>">View</button>
                  <% if (record.status === 'submitted') { %>
                    <button class="ml-3 text-teacher-primary hover:text-teacher-secondary grade-record" data-id="<%= record.id %>">Grade</button>
                  <% } %>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    <% } else { %>
      <div class="p-6 text-center">
        <p class="text-gray-600">No student records found for this practical.</p>
      </div>
    <% } %>
  </div>
</div>

<!-- View Record Modal -->
<div id="view-record-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
    <div class="bg-teacher-primary text-white px-4 py-3 flex justify-between items-center">
      <h3 class="text-lg font-semibold">Practical Record Details</h3>
      <button class="close-modal text-white hover:text-gray-200">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <p class="text-sm text-gray-600">Student</p>
          <p class="font-medium" id="record-student">John Doe</p>
        </div>
        <div>
          <p class="text-sm text-gray-600">Submission Date</p>
          <p class="font-medium" id="record-date">April 24, 2025</p>
        </div>
      </div>
      
      <div class="mb-4">
        <p class="text-sm text-gray-600">Status</p>
        <p class="font-medium" id="record-status">
          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Graded
          </span>
        </p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm text-gray-600">Submission Notes</p>
        <p class="font-medium" id="record-notes">The experiment was conducted successfully. All observations were recorded accurately.</p>
      </div>
      
      <div id="grade-section" class="mb-4">
        <p class="text-sm text-gray-600">Grade</p>
        <p class="font-medium" id="record-grade">A (Excellent)</p>
      </div>
      
      <div id="feedback-section" class="mb-4">
        <p class="text-sm text-gray-600">Teacher Feedback</p>
        <p class="font-medium" id="record-feedback">Well done! Your observations are accurate and the conclusions are well-reasoned.</p>
      </div>
      
      <div class="flex justify-end space-x-3 mt-6">
        <button class="close-modal px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Grade Record Modal -->
<div id="grade-record-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
    <div class="bg-teacher-primary text-white px-4 py-3 flex justify-between items-center">
      <h3 class="text-lg font-semibold">Grade Practical Record</h3>
      <button class="close-modal text-white hover:text-gray-200">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="p-6">
      <form id="grade-form">
        <input type="hidden" id="grade-record-id">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p class="text-sm text-gray-600">Student</p>
            <p class="font-medium" id="grade-student">John Doe</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">Submission Date</p>
            <p class="font-medium" id="grade-date">April 24, 2025</p>
          </div>
        </div>
        
        <div class="mb-4">
          <p class="text-sm text-gray-600">Submission Notes</p>
          <p class="font-medium" id="grade-notes">The experiment was conducted successfully. All observations were recorded accurately.</p>
        </div>
        
        <div class="mb-4">
          <label for="grade-select" class="block text-sm font-medium text-gray-700 mb-1">Grade *</label>
          <select id="grade-select" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="">Select Grade</option>
            <option value="A">A (Excellent)</option>
            <option value="B">B (Very Good)</option>
            <option value="C">C (Good)</option>
            <option value="D">D (Satisfactory)</option>
            <option value="E">E (Needs Improvement)</option>
            <option value="F">F (Unsatisfactory)</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label for="feedback-textarea" class="block text-sm font-medium text-gray-700 mb-1">Feedback</label>
          <textarea id="feedback-textarea" rows="4" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" class="close-modal px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
          <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Submit Grade</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const viewModal = document.getElementById('view-record-modal');
    const gradeModal = document.getElementById('grade-record-modal');
    const closeButtons = document.querySelectorAll('.close-modal');
    const viewButtons = document.querySelectorAll('.view-record');
    const gradeButtons = document.querySelectorAll('.grade-record');
    const gradeForm = document.getElementById('grade-form');
    const exportCsvBtn = document.getElementById('export-csv-btn');
    
    // Close modals
    closeButtons.forEach(button => {
      button.addEventListener('click', function() {
        viewModal.classList.add('hidden');
        gradeModal.classList.add('hidden');
      });
    });
    
    // View record
    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');
        
        // In a real application, fetch the record details from the server
        // For this demo, we'll use mock data
        const recordRow = this.closest('tr');
        const student = recordRow.cells[0].querySelector('div').textContent;
        const date = recordRow.cells[1].querySelector('div').textContent;
        const status = recordRow.cells[2].querySelector('span').textContent.trim();
        const grade = recordRow.cells[3].querySelector('div').textContent;
        
        // Populate the modal
        document.getElementById('record-student').textContent = student;
        document.getElementById('record-date').textContent = date;
        
        // Set status with appropriate styling
        let statusHTML = '';
        if (status === 'Pending') {
          statusHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>';
        } else if (status === 'Submitted') {
          statusHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Submitted</span>';
        } else if (status === 'Graded') {
          statusHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Graded</span>';
        }
        document.getElementById('record-status').innerHTML = statusHTML;
        
        // Show/hide grade and feedback sections based on status
        const gradeSection = document.getElementById('grade-section');
        const feedbackSection = document.getElementById('feedback-section');
        
        if (status === 'Graded') {
          gradeSection.classList.remove('hidden');
          feedbackSection.classList.remove('hidden');
          document.getElementById('record-grade').textContent = grade;
          document.getElementById('record-feedback').textContent = 'Well done! Your observations are accurate and the conclusions are well-reasoned.';
        } else {
          gradeSection.classList.add('hidden');
          feedbackSection.classList.add('hidden');
        }
        
        // Show the modal
        viewModal.classList.remove('hidden');
      });
    });
    
    // Grade record
    gradeButtons.forEach(button => {
      button.addEventListener('click', function() {
        const recordId = this.getAttribute('data-id');
        
        // In a real application, fetch the record details from the server
        // For this demo, we'll use data from the table row
        const recordRow = this.closest('tr');
        const student = recordRow.cells[0].querySelector('div').textContent;
        const date = recordRow.cells[1].querySelector('div').textContent;
        
        // Populate the modal
        document.getElementById('grade-record-id').value = recordId;
        document.getElementById('grade-student').textContent = student;
        document.getElementById('grade-date').textContent = date;
        document.getElementById('grade-notes').textContent = 'The experiment was conducted successfully. All observations were recorded accurately.';
        
        // Reset form fields
        document.getElementById('grade-select').value = '';
        document.getElementById('feedback-textarea').value = '';
        
        // Show the modal
        gradeModal.classList.remove('hidden');
      });
    });
    
    // Submit grade form
    gradeForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const recordId = document.getElementById('grade-record-id').value;
      const grade = document.getElementById('grade-select').value;
      const feedback = document.getElementById('feedback-textarea').value;
      
      // In a real application, send an AJAX request to save the grade
      // For this demo, we'll just show a toast notification
      
      // Display success message
      showToast('success', 'Success', 'Record graded successfully');
      
      // Hide the modal
      gradeModal.classList.add('hidden');
      
      // Reload the page after a delay
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    });
    
    // Export CSV
    if (exportCsvBtn) {
      exportCsvBtn.addEventListener('click', function() {
        showToast('info', 'Coming Soon', 'Export functionality will be available soon');
      });
    }
    
    // Function to show toast notifications
    function showToast(type, title, message) {
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      
      const toastContent = `
        <div class="font-medium">${title}</div>
        <div class="text-sm">${message}</div>
      `;
      
      toast.innerHTML = toastContent;
      
      const toastContainer = document.getElementById('toast-container');
      toastContainer.appendChild(toast);
      
      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
  });
</script>

<style>
  .toast {
    @apply mb-3 px-4 py-3 rounded shadow-md text-white;
  }
  
  .toast-success {
    @apply bg-green-500;
  }
  
  .toast-error {
    @apply bg-red-500;
  }
  
  .toast-warning {
    @apply bg-yellow-500;
  }
  
  .toast-info {
    @apply bg-blue-500;
  }
</style>
