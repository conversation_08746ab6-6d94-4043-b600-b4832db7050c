<!-- Teacher Practicals Management -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Lab Sessions</h2>
    <div class="flex space-x-2">
      <button id="schedule-practical-btn" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Schedule New Practical
      </button>
    </div>
  </div>

  <!-- Filter Options -->
  <div class="p-4 bg-gray-50 border-b border-gray-200">
    <div class="flex flex-wrap gap-4 items-center">
      <div class="flex items-center">
        <label for="date-filter" class="text-sm text-gray-600 mr-2">Date Range:</label>
        <select id="date-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Dates</option>
          <option value="today">Today</option>
          <option value="this-week">This Week</option>
          <option value="this-month">This Month</option>
          <option value="custom">Custom Range</option>
        </select>
      </div>
      <div class="flex items-center" id="custom-date-range" style="display: none;">
        <input type="date" id="date-from" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50 mr-2">
        <span class="text-sm text-gray-600 mx-1">to</span>
        <input type="date" id="date-to" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>
      <div class="flex items-center">
        <label for="class-filter" class="text-sm text-gray-600 mr-2">Class:</label>
        <select id="class-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Classes</option>
          <% if (classSections && classSections.length > 0) { %>
            <%
              // Group classes with complete information
              const classData = [];
              classSections.forEach(section => {
                // Create full class name (e.g., "11 Non-Medical A")
                const fullClassName = `${section.class_name} ${section.trade || ''} ${section.section || ''}`.trim();
                classData.push({
                  value: fullClassName,
                  label: fullClassName
                });
              });

              // Sort and remove duplicates
              const uniqueClasses = Array.from(new Set(classData.map(c => c.value)))
                .map(value => classData.find(c => c.value === value))
                .sort((a, b) => a.value.localeCompare(b.value));

              // Display options
              uniqueClasses.forEach(classInfo => {
            %>
              <option value="<%= classInfo.value %>"><%= classInfo.label %></option>
            <% }); %>
          <% } %>
        </select>
      </div>
      <div class="flex items-center">
        <label for="status-filter" class="text-sm text-gray-600 mr-2">Status:</label>
        <select id="status-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Status</option>
          <option value="Upcoming">Upcoming</option>
          <option value="Completed">Completed</option>
          <option value="Cancelled">Cancelled</option>
        </select>
      </div>
      <button id="reset-filters" class="px-2 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition" title="Reset Filters">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Practicals List -->
  <div class="p-6">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lab</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <!-- Upcoming Practical -->
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              May 25, 2023<br>10:00 AM - 11:30 AM
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              Class 11
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Computer Science
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Python Functions and Modules
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Lab 1
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                Upcoming
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-teacher-primary hover:text-teacher-secondary view-practical" data-id="201">View</button>
                <button class="text-yellow-600 hover:text-yellow-800 edit-practical" data-id="201">Edit</button>
                <button class="text-red-600 hover:text-red-800 cancel-practical" data-id="201">Cancel</button>
              </div>
            </td>
          </tr>

          <!-- Completed Practical -->
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              May 18, 2023<br>10:00 AM - 11:30 AM
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              Class 11
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Computer Science
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Python Basics
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Lab 1
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Completed
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-teacher-primary hover:text-teacher-secondary view-practical" data-id="202">View</button>
                <button class="text-green-600 hover:text-green-800 view-records" data-id="202">Records</button>
              </div>
            </td>
          </tr>

          <!-- Cancelled Practical -->
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              May 11, 2023<br>10:00 AM - 11:30 AM
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              Class 11
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Computer Science
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Introduction to Programming
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Lab 1
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                Cancelled
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-teacher-primary hover:text-teacher-secondary view-practical" data-id="203">View</button>
                <button class="text-green-600 hover:text-green-800 reschedule-practical" data-id="203">Reschedule</button>
              </div>
            </td>
          </tr>

          <!-- Upcoming Practical for Class 12 -->
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              May 26, 2023<br>1:00 PM - 2:30 PM
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              Class 12
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Computer Science
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Database Connectivity with Python
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Lab 2
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                Upcoming
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-teacher-primary hover:text-teacher-secondary view-practical" data-id="204">View</button>
                <button class="text-yellow-600 hover:text-yellow-800 edit-practical" data-id="204">Edit</button>
                <button class="text-red-600 hover:text-red-800 cancel-practical" data-id="204">Cancel</button>
              </div>
            </td>
          </tr>

          <!-- Completed Practical for Class 12 -->
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              May 19, 2023<br>1:00 PM - 2:30 PM
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              Class 12
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Computer Science
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              File Handling in Python
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              Lab 2
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Completed
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-teacher-primary hover:text-teacher-secondary view-practical" data-id="205">View</button>
                <button class="text-green-600 hover:text-green-800 view-records" data-id="205">Records</button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="mt-6 flex justify-between items-center">
      <div id="results-count" class="text-sm text-gray-700">
        Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">12</span> results
      </div>
      <div class="flex space-x-2">
        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          Previous
        </button>
        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          Next
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50 flex flex-col space-y-2"></div>

<!-- Confirmation Modal -->
<div id="confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 id="confirmation-title" class="text-xl font-bold text-gray-800">Confirm Action</h2>
      <button id="close-confirmation-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <p id="confirmation-message" class="text-gray-600 mb-6">Are you sure you want to proceed with this action?</p>

    <div class="flex justify-end space-x-3">
      <button id="cancel-action-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
      <button id="confirm-action-btn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">Confirm</button>
    </div>
  </div>
</div>

<!-- Add Chosen2 CSS and JS in the head section -->
<link href="https://cdn.jsdelivr.net/npm/chosen-js@1.8.7/chosen.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chosen-js@1.8.7/chosen.jquery.min.js"></script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize common toast container
    initToastContainer();

    // Initialize Chosen2 for topic selection (will be used in the schedule form)
    $(document).ready(function() {
      // Initialize existing Chosen elements
      initializeChosen();
    });

    // Date filter change event
    document.getElementById('date-filter').addEventListener('change', function() {
      if (this.value === 'custom') {
        document.getElementById('custom-date-range').style.display = 'flex';
      } else {
        document.getElementById('custom-date-range').style.display = 'none';
        // Apply filters immediately when date range option changes (except for custom)
        applyFilters();
      }
    });

    // Custom date range inputs
    document.getElementById('date-from').addEventListener('change', applyFilters);
    document.getElementById('date-to').addEventListener('change', applyFilters);

    // Class filter change event
    document.getElementById('class-filter').addEventListener('change', applyFilters);

    // Status filter change event
    document.getElementById('status-filter').addEventListener('change', applyFilters);

    // Reset filters button
    document.getElementById('reset-filters').addEventListener('click', resetFilters);

    // Confirmation modal close buttons
    document.getElementById('close-confirmation-modal').addEventListener('click', closeConfirmationModal);
    document.getElementById('cancel-action-btn').addEventListener('click', closeConfirmationModal);

    // View practical buttons
    document.querySelectorAll('.view-practical').forEach(button => {
      button.addEventListener('click', function() {
        const practicalId = this.getAttribute('data-id');
        viewPractical(practicalId);
      });
    });

    // Edit practical buttons
    document.querySelectorAll('.edit-practical').forEach(button => {
      button.addEventListener('click', function() {
        const practicalId = this.getAttribute('data-id');
        editPractical(practicalId);
      });
    });

    // Cancel practical buttons
    document.querySelectorAll('.cancel-practical').forEach(button => {
      button.addEventListener('click', function() {
        const practicalId = this.getAttribute('data-id');
        cancelPractical(practicalId);
      });
    });

    // Reschedule practical buttons
    document.querySelectorAll('.reschedule-practical').forEach(button => {
      button.addEventListener('click', function() {
        const practicalId = this.getAttribute('data-id');
        reschedulePractical(practicalId);
      });
    });

    // View records buttons
    document.querySelectorAll('.view-records').forEach(button => {
      button.addEventListener('click', function() {
        const practicalId = this.getAttribute('data-id');
        viewRecords(practicalId);
      });
    });

    // Schedule practical button
    document.getElementById('schedule-practical-btn').addEventListener('click', function() {
      schedulePractical();
    });
  });

  // Initialize Chosen for searchable dropdowns
  function initializeChosen() {
    // For any existing elements that need Chosen
    $('.chosen-enable').chosen({
      width: '100%',
      placeholder_text_multiple: "Select or search...",
      no_results_text: "No matches found"
    });
  }

  // Reset all filters to default
  function resetFilters() {
    // Reset all filter elements to default values
    document.getElementById('date-filter').value = 'all';
    document.getElementById('class-filter').value = 'all';
    document.getElementById('status-filter').value = 'all';

    // Hide custom date range
    document.getElementById('custom-date-range').style.display = 'none';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';

    // Apply the reset (show all records)
    applyFilters();

    // Show toast
    showToast('info', 'Filters Reset', 'All filters have been reset to defaults');
  }

  // Initialize toast container
  function initToastContainer() {
    // Make sure the toast container exists and is properly configured
    const toastContainer = document.getElementById('toast-container');
    if (toastContainer) {
      // Clear any existing toasts on page load
      toastContainer.innerHTML = '';
    }
  }

  // Apply filters
  function applyFilters() {
    const dateFilter = document.getElementById('date-filter').value;
    const classFilter = document.getElementById('class-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    let dateFrom = '';
    let dateTo = '';

    if (dateFilter === 'custom') {
      dateFrom = document.getElementById('date-from').value;
      dateTo = document.getElementById('date-to').value;

      if (!dateFrom || !dateTo) {
        showToast('error', 'Error', 'Please select both start and end dates for custom range');
        return;
      }

      // Validate date range
      if (new Date(dateFrom) > new Date(dateTo)) {
        showToast('error', 'Error', 'Start date cannot be after end date');
        return;
      }
    }

    // Show loading toast while filtering
    showToast('info', 'Filtering', 'Applying filters to practical list...');

    // Build filter parameters
    const filterParams = {
      dateRange: dateFilter,
      class: classFilter,
      status: statusFilter
    };

    if (dateFilter === 'custom') {
      filterParams.dateFrom = dateFrom;
      filterParams.dateTo = dateTo;
    }

    // In a real application, you would send these filters to the server and reload the data
    console.log('Applying filters:', filterParams);

    // Simulate API call delay
    setTimeout(() => {
      // Apply filter UI updates
      updateFilteredResults(filterParams);

      // Show success message
      showToast('success', 'Filters Applied', 'The practical list has been filtered');
    }, 800);
  }

  // Update UI to show filtered results (simulation)
  function updateFilteredResults(filters) {
    // Get all practical rows
    const practicalRows = document.querySelectorAll('tbody tr');
    let visibleCount = 0;

    // Helper function to check if a date is within range
    function isDateInRange(dateStr, range, fromDate, toDate) {
      const rowDate = new Date(dateStr);

      if (range === 'all') {
        return true;
      } else if (range === 'today') {
        const today = new Date();
        return rowDate.toDateString() === today.toDateString();
      } else if (range === 'this-week') {
        const today = new Date();
        const firstDay = new Date(today.setDate(today.getDate() - today.getDay())); // Start of current week
        const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6)); // End of current week
        return rowDate >= firstDay && rowDate <= lastDay;
      } else if (range === 'this-month') {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        return rowDate >= firstDay && rowDate <= lastDay;
      } else if (range === 'custom' && fromDate && toDate) {
        const from = new Date(fromDate);
        const to = new Date(toDate);
        return rowDate >= from && rowDate <= to;
      }

      return true; // Default case
    }

    // Process each row
    practicalRows.forEach(row => {
      // Extract data from row
      const dateText = row.querySelector('td:nth-child(1)').textContent.trim().split('\n')[0]; // Get date part before newline
      const classText = row.querySelector('td:nth-child(2)').textContent.trim();
      const statusElement = row.querySelector('td:nth-child(6) span');
      const statusText = statusElement ? statusElement.textContent.trim() : '';

      // Convert date from "May 25, 2023" format to Date object
      const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      let dateObj = null;
      try {
        const dateParts = dateText.split(' ');
        const month = monthNames.indexOf(dateParts[0]);
        const day = parseInt(dateParts[1].replace(',', ''));
        const year = parseInt(dateParts[2]);
        dateObj = new Date(year, month, day);
      } catch (e) {
        console.error('Error parsing date:', dateText, e);
        dateObj = new Date(); // Fallback to today's date on error
      }

      // Apply date filter
      const dateMatch = isDateInRange(
        dateObj,
        filters.dateRange,
        filters.dateFrom,
        filters.dateTo
      );

      // Apply class filter
      const classMatch = filters.class === 'all' || classText.includes(filters.class);

      // Apply status filter
      let statusMatch = true;
      if (filters.status !== 'all') {
        statusMatch = statusText.toLowerCase() === filters.status.toLowerCase();
      }

      // Show/hide row based on all filters
      const shouldShow = dateMatch && classMatch && statusMatch;
      row.style.display = shouldShow ? '' : 'none';

      if (shouldShow) {
        visibleCount++;
      }
    });

    // Update result count text - make sure we're selecting the correct element
    const resultText = document.getElementById('results-count');
    if (resultText) {
      resultText.innerHTML =
        `Showing <span class="font-medium">${visibleCount}</span> of <span class="font-medium">${practicalRows.length}</span> results`;
    }

    // Show message if no results
    if (visibleCount === 0) {
      showToast('info', 'No Results', 'No practical sessions match your filter criteria');
    }
  }

  // Function to show toast notifications
  function showToast(type, title, message) {
    // Check if Toastify is available
    if (typeof Toastify === 'function') {
      // Use Toastify library
      let bgColor = '#4a5568'; // Default gray
      if (type === 'success') bgColor = '#10B981';
      if (type === 'error') bgColor = '#EF4444';
      if (type === 'warning') bgColor = '#F59E0B';
      if (type === 'info') bgColor = '#3B82F6';

      Toastify({
        text: title ? `${title}: ${message}` : message,
        duration: 5000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: bgColor,
        stopOnFocus: true
      }).showToast();

      return;
    }

    // Fallback to custom implementation if Toastify is not available
    // Always use the common toast container
    const toastContainer = document.getElementById('toast-container');

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast ${type} transform transition-all duration-300 ease-in-out`;
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(1rem)';

    let bgColor = 'bg-gray-800';
    if (type === 'success') bgColor = 'bg-green-500';
    if (type === 'error') bgColor = 'bg-red-500';
    if (type === 'warning') bgColor = 'bg-yellow-500';
    if (type === 'info') bgColor = 'bg-blue-500';

    toast.innerHTML = `
      <div class="${bgColor} text-white px-4 py-3 rounded shadow-md flex items-center justify-between">
        <div>
          <p class="font-bold">${title}</p>
          <p class="text-sm">${message}</p>
        </div>
        <button class="ml-4 text-white close-toast">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Add animation effect
    setTimeout(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateY(0)';
    }, 10);

    // Set up close button
    toast.querySelector('.close-toast').addEventListener('click', () => {
      fadeOutToast(toast);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
      fadeOutToast(toast);
    }, 5000);
  }

  // Function to fade out and remove a toast
  function fadeOutToast(toast) {
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(1rem)';

    setTimeout(() => {
      toast.remove();
    }, 300);
  }

  // Cancel practical
  function cancelPractical(practicalId) {
    // Set up confirmation modal
    document.getElementById('confirmation-title').textContent = 'Cancel Practical';
    document.getElementById('confirmation-message').textContent = `Are you sure you want to cancel the practical session? This action cannot be undone.`;

    // Set up confirm button
    const confirmBtn = document.getElementById('confirm-action-btn');
    confirmBtn.textContent = 'Cancel Practical';
    confirmBtn.className = 'px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition';
    confirmBtn.onclick = function() {
      // In a real application, you would send an API request to cancel the practical
      console.log(`Cancelling practical ${practicalId}`);

      // Show success message
      showToast('success', 'Success', 'Practical has been cancelled');

      // Close modal and reload page after a delay
      closeConfirmationModal();
      setTimeout(() => {
        // window.location.reload();
        // For demo, we'll just close the modal
      }, 1000);
    };

    // Show modal
    document.getElementById('confirmation-modal').classList.remove('hidden');
  }

  // Reschedule practical
  function reschedulePractical(practicalId) {
    // Show loading toast
    showToast('info', 'Loading', 'Fetching practical details for rescheduling...');

    // Simulate API call to fetch practical details
    // In a real application, you would make an actual API request
    setTimeout(() => {
      // Create modal for rescheduling practical
      const modalHTML = `
        <div id="reschedule-practical-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold text-gray-800">Reschedule Practical Session</h2>
              <button id="close-reschedule-modal" class="text-gray-400 hover:text-gray-500">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div class="bg-amber-50 p-4 mb-6 rounded border border-amber-200">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-amber-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <div>
                  <p class="text-sm font-medium text-amber-800">You are rescheduling a cancelled practical session.</p>
                  <p class="text-xs text-amber-700 mt-1">The previously cancelled session was scheduled for May 11, 2023, 10:00 AM - 11:30 AM.</p>
                </div>
              </div>
            </div>

            <form id="reschedule-practical-form">
              <input type="hidden" id="reschedule-practical-id" value="${practicalId}">

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label for="reschedule-date" class="block text-sm font-medium text-gray-700 mb-1">New Date</label>
                  <input type="date" id="reschedule-date" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                </div>

                <div>
                  <label for="reschedule-time" class="block text-sm font-medium text-gray-700 mb-1">New Time</label>
                  <div class="grid grid-cols-2 gap-2">
                    <div>
                      <div class="text-xs text-gray-500 mb-1">Start Time</div>
                      <input type="time" id="reschedule-start-time" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    </div>
                    <div>
                      <div class="text-xs text-gray-500 mb-1">End Time</div>
                      <input type="time" id="reschedule-end-time" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label for="reschedule-lab" class="block text-sm font-medium text-gray-700 mb-1">Lab</label>
                <select id="reschedule-lab" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50 mb-4" required>
                  <option value="">Select Lab</option>
                  <option value="Lab 1">Lab 1</option>
                  <option value="Lab 2">Lab 2</option>
                  <option value="Lab 3">Lab 3</option>
                </select>
              </div>

              <div class="mb-4">
                <div class="flex items-center">
                  <input type="checkbox" id="notify-students" class="rounded border-gray-300 text-teacher-primary focus:ring-teacher-primary">
                  <label for="notify-students" class="ml-2 block text-sm text-gray-700">Notify students about rescheduled practical</label>
                </div>
              </div>

              <div class="mb-4">
                <label for="reschedule-notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
                <textarea id="reschedule-notes" rows="2" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" placeholder="Any additional information for students..."></textarea>
              </div>

              <div class="flex justify-end space-x-3">
                <button type="button" id="cancel-reschedule-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
                <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Reschedule Practical</button>
              </div>
            </form>
          </div>
        </div>
      `;

      // Add modal to DOM
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Set minimum date to today
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('reschedule-date').min = today;
      document.getElementById('reschedule-date').value = today;

      // Set default times
      document.getElementById('reschedule-start-time').value = '10:00';
      document.getElementById('reschedule-end-time').value = '11:30';

      // Set lab value
      document.getElementById('reschedule-lab').value = 'Lab 1';

      // Set notify students to checked by default
      document.getElementById('notify-students').checked = true;

      // Set up event listeners
      document.getElementById('close-reschedule-modal').addEventListener('click', closeRescheduleModal);
      document.getElementById('cancel-reschedule-btn').addEventListener('click', closeRescheduleModal);
      document.getElementById('reschedule-practical-form').addEventListener('submit', handleRescheduleFormSubmit);

      // Show success toast
      showToast('success', 'Success', 'Practical details loaded');
    }, 500);
  }

  // Close reschedule practical modal
  function closeRescheduleModal() {
    const modal = document.getElementById('reschedule-practical-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Handle reschedule form submission
  function handleRescheduleFormSubmit(e) {
    e.preventDefault();

    // Get form data
    const practicalId = document.getElementById('reschedule-practical-id').value;
    const date = document.getElementById('reschedule-date').value;
    const startTime = document.getElementById('reschedule-start-time').value;
    const endTime = document.getElementById('reschedule-end-time').value;
    const lab = document.getElementById('reschedule-lab').value;
    const notifyStudents = document.getElementById('notify-students').checked;
    const notes = document.getElementById('reschedule-notes').value;

    // Basic validation
    if (!date || !startTime || !endTime || !lab) {
      showToast('error', 'Error', 'Please fill out all required fields');
      return;
    }

    // Show loading toast
    showToast('info', 'Rescheduling', 'Processing practical rescheduling...');

    // In a real application, you would send this data to the server via API
    console.log(`Rescheduling practical ${practicalId} with new data`, {
      date, startTime, endTime, lab, notifyStudents, notes
    });

    // Simulate API call
    setTimeout(() => {
      // Close modal
      closeRescheduleModal();

      // Show success toast
      showToast('success', 'Success', 'Practical has been rescheduled');

      // In a real application, you would reload the data or update the UI
      // setTimeout(() => window.location.reload(), 1000);
    }, 1000);
  }

  // Close confirmation modal
  function closeConfirmationModal() {
    document.getElementById('confirmation-modal').classList.add('hidden');
  }

  // View practical details
  function viewPractical(practicalId) {
    showToast('info', 'Loading', 'Fetching practical details...');
    console.log(`Viewing practical ${practicalId}`);

    // Simulate API call delay
    setTimeout(() => {
      // Create modal for displaying practical details
      const modalHTML = `
        <div id="view-practical-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold text-gray-800">Practical Session Details</h2>
              <button id="close-view-modal" class="text-gray-400 hover:text-gray-500">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div class="bg-gray-50 p-4 mb-4 rounded border border-gray-200">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div class="mb-3">
                    <h3 class="text-sm font-semibold text-gray-500 uppercase">Session Info</h3>
                    <div class="mt-1">
                      <p class="text-sm"><span class="font-medium">ID:</span> ${practicalId}</p>
                      <p class="text-sm"><span class="font-medium">Status:</span>
                        <span class="px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          Upcoming
                        </span>
                      </p>
                      <p class="text-sm"><span class="font-medium">Created By:</span> John Smith</p>
                      <p class="text-sm"><span class="font-medium">Created On:</span> May 15, 2023</p>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="mb-3">
                    <h3 class="text-sm font-semibold text-gray-500 uppercase">Schedule</h3>
                    <div class="mt-1">
                      <p class="text-sm"><span class="font-medium">Date:</span> May 25, 2023</p>
                      <p class="text-sm"><span class="font-medium">Time:</span> 10:00 AM - 11:30 AM</p>
                      <p class="text-sm"><span class="font-medium">Duration:</span> 1h 30m</p>
                      <p class="text-sm"><span class="font-medium">Lab:</span> Lab 1</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">Class Details</h3>
                <p class="text-sm mb-1"><span class="font-medium">Class:</span> Class 11</p>
                <p class="text-sm mb-1"><span class="font-medium">Section:</span> A, B</p>
                <p class="text-sm mb-1"><span class="font-medium">Subject:</span> Computer Science</p>
                <p class="text-sm mb-1"><span class="font-medium">Students Expected:</span> 35</p>
              </div>
              <div>
                <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">Topic Information</h3>
                <p class="text-sm mb-1"><span class="font-medium">Topic:</span> Python Functions and Modules</p>
                <p class="text-sm mb-1"><span class="font-medium">Pre-requisites:</span> Basic Python syntax</p>
              </div>
            </div>

            <div class="mb-4">
              <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">Description</h3>
              <p class="text-sm text-gray-700">
                In this practical session, students will learn about Python functions and modules.
                They will practice creating and using functions, understanding parameters and return values,
                and importing and using modules from the standard library. The session includes hands-on
                exercises to reinforce these concepts.
              </p>
            </div>

            <div class="mb-4">
              <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">Learning Materials</h3>
              <ul class="list-disc pl-5 text-sm text-gray-700">
                <li><a href="#" class="text-blue-600 hover:underline">Python Functions Documentation</a></li>
                <li><a href="#" class="text-blue-600 hover:underline">Module Usage Guide</a></li>
                <li>Practical Workbook, pages 45-52</li>
              </ul>
            </div>

            <div>
              <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">Equipment Needed</h3>
              <ul class="list-disc pl-5 text-sm text-gray-700">
                <li>Computers with Python 3.8+ installed</li>
                <li>Python IDE (PyCharm/VS Code)</li>
                <li>Practical worksheet</li>
              </ul>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
              <button id="close-view-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Close</button>
              <button id="edit-from-view-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit
              </button>
            </div>
          </div>
        </div>
      `;

      // Add modal to DOM
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Set up event listeners
      document.getElementById('close-view-modal').addEventListener('click', closeViewModal);
      document.getElementById('close-view-btn').addEventListener('click', closeViewModal);
      document.getElementById('edit-from-view-btn').addEventListener('click', function() {
        closeViewModal();
        editPractical(practicalId);
      });

      // Show success toast
      showToast('success', 'Success', 'Practical details loaded');
    }, 500);
  }

  // Close view practical modal
  function closeViewModal() {
    const modal = document.getElementById('view-practical-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Edit practical
  function editPractical(practicalId) {
    // Show loading toast
    showToast('info', 'Loading', 'Fetching practical details...');

    // Simulate API call to fetch practical details
    // In a real application, you would make an actual API request
    setTimeout(() => {
      // Create modal for editing practical
      const modalHTML = `
        <div id="edit-practical-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold text-gray-800">Edit Practical Session</h2>
              <button id="close-edit-modal" class="text-gray-400 hover:text-gray-500">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <form id="edit-practical-form">
              <input type="hidden" id="edit-practical-id" value="${practicalId}">

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label for="edit-date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <input type="date" id="edit-date" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                </div>

                <div>
                  <label for="edit-time" class="block text-sm font-medium text-gray-700 mb-1">Time</label>
                  <div class="grid grid-cols-2 gap-2">
                    <input type="time" id="edit-start-time" class="border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    <input type="time" id="edit-end-time" class="border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label for="edit-class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                  <select id="edit-class" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    <option value="">Select Class</option>
                    <option value="11">Class 11</option>
                    <option value="12">Class 12</option>
                  </select>
                </div>

                <div>
                  <label for="edit-section" class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                  <select id="edit-section" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    <option value="">Select Section</option>
                    <!-- Section options will be populated dynamically -->
                  </select>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label for="edit-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                  <select id="edit-subject" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    <option value="">Select Subject</option>
                    <option value="Computer Science">Computer Science</option>
                    <option value="Physics">Physics</option>
                    <option value="Chemistry">Chemistry</option>
                    <option value="Biology">Biology</option>
                  </select>
                </div>

                <div>
                  <label for="edit-lab" class="block text-sm font-medium text-gray-700 mb-1">Lab</label>
                  <select id="edit-lab" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                    <option value="">Select Lab</option>
                    <option value="Lab 1">Lab 1</option>
                    <option value="Lab 2">Lab 2</option>
                    <option value="Lab 3">Lab 3</option>
                  </select>
                </div>
              </div>

              <div class="mb-4">
                <label for="edit-topic" class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
                <input type="text" id="edit-topic" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
              </div>

              <div class="mb-4">
                <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea id="edit-description" rows="3" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
              </div>

              <div class="mb-4">
                <label for="edit-materials" class="block text-sm font-medium text-gray-700 mb-1">Learning Materials</label>
                <textarea id="edit-materials" rows="2" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
                <p class="text-xs text-gray-500 mt-1">Enter URLs or notes about learning materials</p>
              </div>

              <div class="flex justify-end space-x-3">
                <button type="button" id="cancel-edit-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
                <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
              </div>
            </form>
          </div>
        </div>
      `;

      // Add modal to DOM
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Populate class dropdown with available classes
      populateClassOptions();

      // Populate form with practical data (using sample data for demonstration)
      document.getElementById('edit-date').value = '2023-05-25';
      document.getElementById('edit-start-time').value = '10:00';
      document.getElementById('edit-end-time').value = '11:30';
      document.getElementById('edit-class').value = '11';
      document.getElementById('edit-subject').value = 'Computer Science';
      document.getElementById('edit-lab').value = 'Lab 1';
      document.getElementById('edit-topic').value = 'Python Functions and Modules';
      document.getElementById('edit-description').value = 'Introduction to Python functions and modules. Students will learn to create and use functions and import modules.';
      document.getElementById('edit-materials').value = 'https://docs.python.org/3/tutorial/controlflow.html#defining-functions';

      // Set up event listeners
      document.getElementById('close-edit-modal').addEventListener('click', closeEditModal);
      document.getElementById('cancel-edit-btn').addEventListener('click', closeEditModal);
      document.getElementById('edit-practical-form').addEventListener('submit', handleEditFormSubmit);

      // Update class-section relationship
      document.getElementById('edit-class').addEventListener('change', updateSectionOptions);

      // Initialize sections for the selected class
      updateSectionOptions();

      // Show success toast
      showToast('success', 'Success', 'Practical details loaded');
    }, 500);
  }

  // Populate class options in the edit form
  function populateClassOptions() {
    // In a real application, you would fetch this data from the server
    // Here we're using static options matching the ones in the filter dropdown
    const classFilter = document.getElementById('class-filter');
    const editClass = document.getElementById('edit-class');

    // Clear current options except the first one
    while (editClass.options.length > 1) {
      editClass.remove(1);
    }

    // Copy options from the class filter dropdown (excluding the "All Classes" option)
    for (let i = 1; i < classFilter.options.length; i++) {
      const option = classFilter.options[i];
      const newOption = document.createElement('option');
      newOption.value = option.value;
      newOption.textContent = option.textContent;
      editClass.appendChild(newOption);
    }
  }

  // Close edit practical modal
  function closeEditModal() {
    const modal = document.getElementById('edit-practical-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Handle edit form submission
  function handleEditFormSubmit(e) {
    e.preventDefault();

    // Get form data
    const practicalId = document.getElementById('edit-practical-id').value;
    const date = document.getElementById('edit-date').value;
    const startTime = document.getElementById('edit-start-time').value;
    const endTime = document.getElementById('edit-end-time').value;
    const classValue = document.getElementById('edit-class').value;
    const section = document.getElementById('edit-section').value;
    const subject = document.getElementById('edit-subject').value;
    const lab = document.getElementById('edit-lab').value;
    const topic = document.getElementById('edit-topic').value;
    const description = document.getElementById('edit-description').value;
    const materials = document.getElementById('edit-materials').value;

    // Show loading toast
    showToast('info', 'Saving', 'Updating practical details...');

    // In a real application, you would send this data to the server via API
    console.log(`Updating practical ${practicalId} with new data`, {
      date, startTime, endTime, class: classValue, section, subject, lab, topic, description, materials
    });

    // Simulate API call
    setTimeout(() => {
      // Close modal
      closeEditModal();

      // Show success toast
      showToast('success', 'Success', 'Practical has been updated');

      // In a real application, you would reload the data or update the UI
      // setTimeout(() => window.location.reload(), 1000);
    }, 1000);
  }

  // Update section options based on selected class
  function updateSectionOptions() {
    const classValue = document.getElementById('edit-class').value;
    const sectionSelect = document.getElementById('edit-section');

    // Clear current options
    sectionSelect.innerHTML = '<option value="">Select Section</option>';

    // Add sections based on class
    if (classValue) {
      // In a real application, you would get sections for the selected class from data
      const sections = ['A', 'B', 'C']; // Sample sections

      sections.forEach(section => {
        const option = document.createElement('option');
        option.value = section;
        option.textContent = section;
        sectionSelect.appendChild(option);
      });
    }
  }

  // View practical records
  function viewRecords(practicalId) {
    showToast('info', 'Loading', 'Fetching practical records...');

    // Simulate API call to fetch practical records
    setTimeout(() => {
      // Create modal for displaying records
      const modalHTML = `
        <div id="records-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl w-full">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-bold text-gray-800">Practical Records - Python Basics</h2>
              <button id="close-records-modal" class="text-gray-400 hover:text-gray-500">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div class="mb-4 bg-gray-100 p-3 rounded-lg">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><span class="font-medium">Date:</span> May 18, 2023</p>
                  <p><span class="font-medium">Time:</span> 10:00 AM - 11:30 AM</p>
                  <p><span class="font-medium">Class:</span> 11</p>
                </div>
                <div>
                  <p><span class="font-medium">Subject:</span> Computer Science</p>
                  <p><span class="font-medium">Topic:</span> Python Basics</p>
                  <p><span class="font-medium">Lab:</span> Lab 1</p>
                </div>
              </div>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <!-- Sample student records -->
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Aakash Sharma</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Present</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">100%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Excellent</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-teacher-primary hover:text-teacher-secondary">View Work</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Bhavya Patel</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Present</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">85%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Good</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-teacher-primary hover:text-teacher-secondary">View Work</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Chetan Kumar</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Absent</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N/A</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-gray-400 cursor-not-allowed">View Work</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Divya Singh</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Present</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">92%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Very Good</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-teacher-primary hover:text-teacher-secondary">View Work</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Esha Gupta</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Late</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">75%</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Satisfactory</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-teacher-primary hover:text-teacher-secondary">View Work</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="mt-4 flex justify-end space-x-3">
              <button id="export-records-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Export Records
              </button>
              <button id="close-records-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Close</button>
            </div>
          </div>
        </div>
      `;

      // Add modal to DOM
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Set up event listeners
      document.getElementById('close-records-modal').addEventListener('click', closeRecordsModal);
      document.getElementById('close-records-btn').addEventListener('click', closeRecordsModal);
      document.getElementById('export-records-btn').addEventListener('click', exportRecords);

      // Show success toast
      showToast('success', 'Success', 'Practical records loaded');
    }, 500);
  }

  // Close records modal
  function closeRecordsModal() {
    const modal = document.getElementById('records-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Export records
  function exportRecords() {
    showToast('info', 'Exporting', 'Preparing records export...');

    // Simulate export process
    setTimeout(() => {
      showToast('success', 'Success', 'Records exported successfully');
    }, 1000);
  }

  // Schedule new practical
  function schedulePractical() {
    showToast('info', 'Loading', 'Preparing scheduling form...');

    // Create modal for scheduling new practical
    const modalHTML = `
      <div id="schedule-practical-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-800">Schedule New Practical Session</h2>
            <button id="close-schedule-modal" class="text-gray-400 hover:text-gray-500">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <form id="schedule-practical-form">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label for="schedule-date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="schedule-date" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
              </div>

              <div>
                <label for="schedule-time" class="block text-sm font-medium text-gray-700 mb-1">Time</label>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Start Time</div>
                    <input type="time" id="schedule-start-time" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  </div>
                  <div>
                    <div class="text-xs text-gray-500 mb-1">End Time</div>
                    <input type="time" id="schedule-end-time" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label for="schedule-class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select id="schedule-class" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  <option value="">Select Class</option>
                  <option value="11 Non-Medical A">11 Non-Medical A</option>
                  <option value="11 Non-Medical B">11 Non-Medical B</option>
                  <option value="11 Medical A">11 Medical A</option>
                  <option value="12 Non-Medical A">12 Non-Medical A</option>
                  <option value="12 Commerce A">12 Commerce A</option>
                </select>
              </div>

              <div>
                <label for="schedule-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                <select id="schedule-subject" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  <option value="">Select Subject</option>
                  <option value="Computer Science">Computer Science</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Biology">Biology</option>
                </select>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label for="schedule-lab" class="block text-sm font-medium text-gray-700 mb-1">Lab</label>
                <select id="schedule-lab" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
                  <option value="">Select Lab</option>
                  <option value="Lab 1">Lab 1</option>
                  <option value="Lab 2">Lab 2</option>
                  <option value="Lab 3">Lab 3</option>
                </select>
              </div>

              <div>
                <label for="schedule-topic" class="block text-sm font-medium text-gray-700 mb-1">Topics</label>
                <select id="schedule-topic" class="w-full chosen-enable" multiple required data-placeholder="Select or search for topics">
                  <option value="Python Basics">Python Basics</option>
                  <option value="Python Functions and Modules">Python Functions and Modules</option>
                  <option value="Database Connectivity with Python">Database Connectivity with Python</option>
                  <option value="File Handling in Python">File Handling in Python</option>
                  <option value="Introduction to Programming">Introduction to Programming</option>
                  <option value="Basic Network Configuration">Basic Network Configuration</option>
                  <option value="HTML and CSS Basics">HTML and CSS Basics</option>
                  <option value="JavaScript Fundamentals">JavaScript Fundamentals</option>
                  <option value="Object-Oriented Programming">Object-Oriented Programming</option>
                  <option value="Data Structures">Data Structures</option>
                  <option value="Electricity and Magnetism">Electricity and Magnetism</option>
                  <option value="Wave Optics">Wave Optics</option>
                  <option value="Mechanics and Kinematics">Mechanics and Kinematics</option>
                  <option value="Organic Chemistry Reactions">Organic Chemistry Reactions</option>
                  <option value="Chemical Bonding">Chemical Bonding</option>
                  <option value="Cell Structure and Function">Cell Structure and Function</option>
                  <option value="Human Anatomy">Human Anatomy</option>
                  <option value="Genetics and Evolution">Genetics and Evolution</option>
                </select>
              </div>
            </div>

            <div class="mb-4">
              <label for="schedule-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea id="schedule-description" rows="3" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" placeholder="Provide details about the practical session..."></textarea>
            </div>

            <div class="mb-4">
              <label for="schedule-prerequisites" class="block text-sm font-medium text-gray-700 mb-1">Pre-requisites</label>
              <input type="text" id="schedule-prerequisites" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" placeholder="e.g. Basic Python syntax">
            </div>

            <div class="mb-4">
              <label for="schedule-materials" class="block text-sm font-medium text-gray-700 mb-1">Learning Materials</label>
              <textarea id="schedule-materials" rows="2" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" placeholder="List any URLs, books, or resources needed..."></textarea>
            </div>

            <div class="mb-4">
              <label for="schedule-equipment" class="block text-sm font-medium text-gray-700 mb-1">Equipment Needed</label>
              <textarea id="schedule-equipment" rows="2" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" placeholder="List required equipment..."></textarea>
            </div>

            <div class="flex justify-end space-x-3">
              <button type="button" id="cancel-schedule-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
              <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Schedule Practical</button>
            </div>
          </form>
        </div>
      </div>
    `;

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('schedule-date').min = today;
    document.getElementById('schedule-date').value = today;

    // Set default times (30 minutes from now, rounded to nearest half hour)
    const now = new Date();
    const roundedMinutes = Math.ceil(now.getMinutes() / 30) * 30;
    now.setMinutes(roundedMinutes);
    now.setSeconds(0);

    const startTimeHours = now.getHours().toString().padStart(2, '0');
    const startTimeMinutes = now.getMinutes().toString().padStart(2, '0');

    // End time is 90 minutes after start time
    const endTime = new Date(now.getTime() + 90 * 60000);
    const endTimeHours = endTime.getHours().toString().padStart(2, '0');
    const endTimeMinutes = endTime.getMinutes().toString().padStart(2, '0');

    document.getElementById('schedule-start-time').value = `${startTimeHours}:${startTimeMinutes}`;
    document.getElementById('schedule-end-time').value = `${endTimeHours}:${endTimeMinutes}`;

    // Set up event listeners
    document.getElementById('close-schedule-modal').addEventListener('click', closeScheduleModal);
    document.getElementById('cancel-schedule-btn').addEventListener('click', closeScheduleModal);
    document.getElementById('schedule-practical-form').addEventListener('submit', handleScheduleFormSubmit);

    // Initialize Chosen for the topic field
    setTimeout(() => {
      $('#schedule-topic').chosen({
        width: '100%',
        placeholder_text_multiple: "Select or search for topics",
        no_results_text: "No matches found. Press Enter to add new topic"
      });

      // Fix the z-index of Chosen dropdown
      $('.chosen-container').css('z-index', '1060');
    }, 100);

    // Show success toast
    showToast('success', 'Ready', 'Please fill out the form to schedule a new practical');
  }

  // Close schedule practical modal
  function closeScheduleModal() {
    const modal = document.getElementById('schedule-practical-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Handle schedule form submission
  function handleScheduleFormSubmit(e) {
    e.preventDefault();

    // Get form data
    const date = document.getElementById('schedule-date').value;
    const startTime = document.getElementById('schedule-start-time').value;
    const endTime = document.getElementById('schedule-end-time').value;
    const classValue = document.getElementById('schedule-class').value;

    // Get selected topics (multiple)
    const selectedTopics = $('#schedule-topic').chosen().val() || [];

    const subject = document.getElementById('schedule-subject').value;
    const lab = document.getElementById('schedule-lab').value;
    const description = document.getElementById('schedule-description').value;
    const prerequisites = document.getElementById('schedule-prerequisites').value;
    const materials = document.getElementById('schedule-materials').value;
    const equipment = document.getElementById('schedule-equipment').value;

    // Basic validation
    if (!date || !startTime || !endTime || !classValue || selectedTopics.length === 0 || !subject || !lab) {
      showToast('error', 'Error', 'Please fill out all required fields');
      return;
    }

    // Validate that date and time are in the future
    const now = new Date();
    const scheduledDateTime = new Date(`${date}T${startTime}`);

    if (scheduledDateTime <= now) {
      showToast('error', 'Invalid Time', 'Schedule date and time must be in the future');
      return;
    }

    // Validate that end time is after start time
    const endDateTime = new Date(`${date}T${endTime}`);
    if (endDateTime <= scheduledDateTime) {
      showToast('error', 'Invalid Time', 'End time must be after start time');
      return;
    }

    // Show loading toast
    showToast('info', 'Scheduling', 'Creating new practical session...');

    // In a real application, you would send this data to the server via API
    console.log('Scheduling new practical with data:', {
      date, startTime, endTime, class: classValue,
      topics: selectedTopics,
      subject, lab, description, prerequisites, materials, equipment
    });

    // Simulate API call
    setTimeout(() => {
      // Close modal
      closeScheduleModal();

      // Show success toast
      showToast('success', 'Success', 'Practical has been scheduled');

      // In a real application, you would reload the data or update the UI
      // setTimeout(() => window.location.reload(), 1000);
    }, 1000);
  }
</script>
