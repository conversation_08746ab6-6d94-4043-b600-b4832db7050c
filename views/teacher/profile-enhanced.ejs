<!-- Enhanced Teacher Profile Page with Timeline -->
<div class="min-h-screen bg-gradient-to-br from-teacher-primary to-teacher-secondary">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-800">Enhanced Teacher Profile</h1>
            <p class="text-gray-600 mt-2">Comprehensive Educational Professional Dashboard</p>
          </div>
          <div class="flex space-x-3">
            <button id="edit-profile-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-edit mr-2"></i>Edit Profile
            </button>
            <button id="print-profile-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-print mr-2"></i>Print
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Personal Information Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>
          <div class="p-6">
            <!-- Profile Image -->
            <div class="flex flex-col items-center mb-6">
              <div class="relative">
                <div id="profile-image-container" class="w-32 h-32 rounded-full bg-teacher-primary border-4 border-teacher-primary shadow-lg overflow-hidden flex items-center justify-center">
                  <div id="profile-image-placeholder" class="text-4xl font-bold text-white">T</div>
                  <img id="profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                </div>
              </div>
              <h3 id="teacher-name" class="text-xl font-bold text-gray-800">Loading...</h3>
              <p id="teacher-designation" class="text-teacher-primary font-semibold">Teacher</p>
              <span id="teacher-department" class="mt-2 px-3 py-1 bg-teacher-primary bg-opacity-10 text-teacher-primary rounded-full text-sm font-medium">
                Academic Department
              </span>
            </div>

            <!-- Basic Information -->
            <div class="space-y-4">
              <div class="flex items-center">
                <i class="fas fa-id-badge text-teacher-primary w-5"></i>
                <span id="teacher-employee-id" class="ml-3 text-gray-700">EMP0001</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-envelope text-teacher-primary w-5"></i>
                <span id="teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-phone text-teacher-primary w-5"></i>
                <span id="teacher-phone" class="ml-3 text-gray-700">+91-XXXXXXXXXX</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-calendar text-teacher-primary w-5"></i>
                <span id="teacher-joining-date" class="ml-3 text-gray-700">Joining Date</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-briefcase text-teacher-primary w-5"></i>
                <span id="teacher-employment-type" class="ml-3 text-gray-700">Employment Type</span>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">About</h4>
              <p id="teacher-bio" class="text-gray-600 text-sm leading-relaxed">Loading...</p>
            </div>

            <!-- Contact Information -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">Contact Details</h4>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <i class="fas fa-map-marker-alt text-teacher-primary w-4 mt-1"></i>
                  <span id="teacher-address" class="ml-3 text-gray-700">Address</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-phone-alt text-teacher-primary w-4"></i>
                  <span id="teacher-emergency-contact" class="ml-3 text-gray-700">Emergency Contact</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Stats Card -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mt-6">
          <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-4">
            <h3 class="text-lg font-semibold">Quick Stats</h3>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-2 gap-4 text-center">
              <div>
                <div id="total-experience" class="text-2xl font-bold text-teacher-primary">0</div>
                <div class="text-xs text-gray-600">Total Experience</div>
              </div>
              <div>
                <div id="teaching-experience" class="text-2xl font-bold text-teacher-primary">0</div>
                <div class="text-xs text-gray-600">Teaching Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Timeline and Details Section -->
      <div class="lg:col-span-2">
        <div class="space-y-6">
          <!-- Educational Timeline -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
              <h2 class="text-xl font-semibold flex items-center">
                <i class="fas fa-graduation-cap mr-3"></i>
                Educational Timeline
              </h2>
            </div>
            <div class="p-6">
              <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                
                <!-- Education entries will be populated here -->
                <div id="education-timeline" class="space-y-8">
                  <!-- Timeline entries will be dynamically added -->
                </div>
              </div>
            </div>
          </div>

          <!-- Professional Experience Timeline -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
              <h2 class="text-xl font-semibold flex items-center">
                <i class="fas fa-briefcase mr-3"></i>
                Professional Experience Timeline
              </h2>
            </div>
            <div class="p-6">
              <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
                
                <!-- Experience entries will be populated here -->
                <div id="experience-timeline" class="space-y-8">
                  <!-- Timeline entries will be dynamically added -->
                </div>
              </div>
            </div>
          </div>

          <!-- Subjects and Skills -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Subjects Taught -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
              <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4">
                <h3 class="text-lg font-semibold">Subjects Taught</h3>
              </div>
              <div class="p-4">
                <div id="subjects-list" class="space-y-2">
                  <!-- Subjects will be populated here -->
                </div>
              </div>
            </div>

            <!-- Skills and Languages -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
              <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-4">
                <h3 class="text-lg font-semibold">Skills & Languages</h3>
              </div>
              <div class="p-4">
                <div class="mb-4">
                  <h4 class="font-semibold text-gray-700 mb-2">Special Skills</h4>
                  <div id="skills-list" class="text-sm text-gray-600">
                    <!-- Skills will be populated here -->
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-700 mb-2">Languages Known</h4>
                  <div id="languages-list" class="text-sm text-gray-600">
                    <!-- Languages will be populated here -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Achievements and Recognition -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-6">
              <h2 class="text-xl font-semibold flex items-center">
                <i class="fas fa-trophy mr-3"></i>
                Achievements & Recognition
              </h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold text-gray-700 mb-3">Awards Received</h4>
                  <div id="awards-list" class="text-sm text-gray-600 space-y-2">
                    <!-- Awards will be populated here -->
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-700 mb-3">Training Programs</h4>
                  <div id="training-list" class="text-sm text-gray-600 space-y-2">
                    <!-- Training will be populated here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
    <i class="fas fa-spinner fa-spin text-2xl text-teacher-primary"></i>
    <span class="text-lg">Loading teacher profile...</span>
  </div>
</div>

<script>
$(document).ready(function() {
  // Fetch and display teacher profile data
  fetchEnhancedTeacherProfile();

  // Print profile functionality
  $(document).on('click', '#print-profile-btn', function() {
    window.print();
  });

  // Edit profile functionality (placeholder)
  $(document).on('click', '#edit-profile-btn', function() {
    alert('Edit functionality will be implemented soon!');
  });
});

// Fetch enhanced teacher profile data
async function fetchEnhancedTeacherProfile() {
  try {
    const response = await fetch('/api/teacher/profile-enhanced');
    if (!response.ok) {
      throw new Error('Failed to fetch teacher profile');
    }

    const data = await response.json();
    if (data.success) {
      displayEnhancedTeacherProfile(data.teacher);
      hideLoadingOverlay();
    } else {
      showError('Failed to load teacher profile: ' + data.message);
    }
  } catch (error) {
    console.error('Error fetching teacher profile:', error);
    showError('Error loading teacher profile. Please try again later.');
    hideLoadingOverlay();
  }
}

// Display enhanced teacher profile data
function displayEnhancedTeacherProfile(teacher) {
  console.log('Displaying teacher profile:', teacher);

  // Basic information
  $('#teacher-name').text(teacher.fullName || 'Unknown Teacher');
  $('#teacher-designation').text(teacher.designation || 'Teacher');
  $('#teacher-department').text(teacher.department || 'Academic Department');
  $('#teacher-employee-id').text(teacher.employee_id || 'N/A');
  $('#teacher-email').text(teacher.email || 'No email provided');
  $('#teacher-phone').text(teacher.phone || 'No phone provided');
  $('#teacher-joining-date').text(formatDate(teacher.joining_date) || 'Not specified');
  $('#teacher-employment-type').text(capitalizeFirst(teacher.employment_type) || 'Not specified');
  $('#teacher-bio').text(teacher.bio || 'No bio provided');

  // Address
  const address = [teacher.address, teacher.city, teacher.state, teacher.pincode].filter(Boolean).join(', ');
  $('#teacher-address').text(address || 'Address not provided');
  $('#teacher-emergency-contact').text(teacher.emergency_contact || 'Not provided');

  // Experience stats
  $('#total-experience').text(teacher.total_experience_years || '0');
  $('#teaching-experience').text(teacher.teaching_experience_years || '0');

  // Profile image
  if (teacher.profile_image && teacher.profile_image !== 'null') {
    $('#profile-image').attr('src', teacher.profile_image).removeClass('hidden');
    $('#profile-image-placeholder').addClass('hidden');
  } else {
    const initials = getInitials(teacher);
    $('#profile-image-placeholder').text(initials).removeClass('hidden');
  }

  // Display timelines
  displayEducationTimeline(teacher.educationTimeline || []);
  displayExperienceTimeline(teacher.experienceTimeline || []);

  // Display subjects
  displaySubjectsList(teacher.subjects_taught || teacher.subjects);

  // Display skills and languages
  displaySkillsAndLanguages(teacher.special_skills, teacher.languages_known);

  // Display achievements
  displayAchievements(teacher.awards_received, teacher.training_programs);
}

// Display education timeline
function displayEducationTimeline(timeline) {
  const container = $('#education-timeline');
  container.empty();

  if (timeline.length === 0) {
    container.html('<p class="text-gray-500 text-center">No educational information available</p>');
    return;
  }

  timeline.forEach((item, index) => {
    const isLast = index === timeline.length - 1;
    const timelineItem = $(`
      <div class="relative flex items-start">
        <div class="absolute left-6 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
        <div class="ml-16">
          <div class="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-600 rounded-lg p-4">
            <h4 class="font-bold text-lg text-blue-800">${item.title}</h4>
            <p class="text-blue-700 font-medium">${item.institution}</p>
            ${item.board ? `<p class="text-sm text-blue-600">Board: ${item.board}</p>` : ''}
            ${item.stream ? `<p class="text-sm text-blue-600">Stream: ${item.stream}</p>` : ''}
            ${item.specialization ? `<p class="text-sm text-blue-600">Specialization: ${item.specialization}</p>` : ''}
            ${item.percentage ? `<p class="text-sm text-blue-600">Percentage: ${item.percentage}%</p>` : ''}
            ${item.thesis ? `<p class="text-sm text-blue-600">Thesis: ${item.thesis}</p>` : ''}
            <p class="text-sm text-gray-600 mt-2">
              <i class="fas fa-calendar mr-1"></i>${item.year}
            </p>
          </div>
        </div>
      </div>
    `);

    container.append(timelineItem);
  });
}

// Display experience timeline
function displayExperienceTimeline(timeline) {
  const container = $('#experience-timeline');
  container.empty();

  if (timeline.length === 0) {
    container.html('<p class="text-gray-500 text-center">No experience information available</p>');
    return;
  }

  timeline.forEach((item, index) => {
    const isLast = index === timeline.length - 1;
    const timelineItem = $(`
      <div class="relative flex items-start">
        <div class="absolute left-6 w-4 h-4 ${item.isCurrent ? 'bg-green-600' : 'bg-gray-400'} rounded-full border-4 border-white shadow-lg"></div>
        <div class="ml-16">
          <div class="bg-gradient-to-r ${item.isCurrent ? 'from-green-50 to-green-100 border-green-600' : 'from-gray-50 to-gray-100 border-gray-400'} border-l-4 rounded-lg p-4">
            <h4 class="font-bold text-lg ${item.isCurrent ? 'text-green-800' : 'text-gray-800'}">${item.title}</h4>
            <p class="${item.isCurrent ? 'text-green-700' : 'text-gray-700'} font-medium">${item.institution}</p>
            <p class="text-sm ${item.isCurrent ? 'text-green-600' : 'text-gray-600'} mt-2">
              <i class="fas fa-calendar mr-1"></i>${item.duration}
            </p>
            ${item.isCurrent ? '<span class="inline-block bg-green-200 text-green-800 text-xs px-2 py-1 rounded-full mt-2">Current Position</span>' : ''}
          </div>
        </div>
      </div>
    `);

    container.append(timelineItem);
  });
}

// Display subjects list
function displaySubjectsList(subjects) {
  const container = $('#subjects-list');
  container.empty();

  if (!subjects) {
    container.html('<p class="text-gray-500">No subjects specified</p>');
    return;
  }

  const subjectArray = typeof subjects === 'string' ? subjects.split(',') : subjects;
  subjectArray.forEach(subject => {
    const subjectItem = $(`
      <div class="flex items-center space-x-2">
        <i class="fas fa-book text-purple-600"></i>
        <span class="text-gray-700">${subject.trim()}</span>
      </div>
    `);
    container.append(subjectItem);
  });
}

// Display skills and languages
function displaySkillsAndLanguages(skills, languages) {
  // Skills
  const skillsContainer = $('#skills-list');
  if (skills) {
    const skillsArray = skills.split(',');
    const skillsHtml = skillsArray.map(skill =>
      `<span class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${skill.trim()}</span>`
    ).join('');
    skillsContainer.html(skillsHtml);
  } else {
    skillsContainer.html('<p class="text-gray-500">No skills specified</p>');
  }

  // Languages
  const languagesContainer = $('#languages-list');
  if (languages) {
    const languagesArray = languages.split(',');
    const languagesHtml = languagesArray.map(language =>
      `<span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">${language.trim()}</span>`
    ).join('');
    languagesContainer.html(languagesHtml);
  } else {
    languagesContainer.html('<p class="text-gray-500">No languages specified</p>');
  }
}

// Display achievements
function displayAchievements(awards, training) {
  // Awards
  const awardsContainer = $('#awards-list');
  if (awards) {
    const awardsArray = awards.split(',');
    const awardsHtml = awardsArray.map(award =>
      `<div class="flex items-start space-x-2">
        <i class="fas fa-trophy text-yellow-600 mt-1"></i>
        <span class="text-gray-700">${award.trim()}</span>
      </div>`
    ).join('');
    awardsContainer.html(awardsHtml);
  } else {
    awardsContainer.html('<p class="text-gray-500">No awards specified</p>');
  }

  // Training
  const trainingContainer = $('#training-list');
  if (training) {
    const trainingArray = training.split(',');
    const trainingHtml = trainingArray.map(program =>
      `<div class="flex items-start space-x-2">
        <i class="fas fa-certificate text-green-600 mt-1"></i>
        <span class="text-gray-700">${program.trim()}</span>
      </div>`
    ).join('');
    trainingContainer.html(trainingHtml);
  } else {
    trainingContainer.html('<p class="text-gray-500">No training programs specified</p>');
  }
}

// Utility functions
function getInitials(teacher) {
  const name = teacher.fullName || teacher.name || teacher.username || 'T';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
}

function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function capitalizeFirst(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function hideLoadingOverlay() {
  $('#loading-overlay').fadeOut();
}

function showError(message) {
  alert('Error: ' + message);
}
</script>
