<!-- Teacher Profile Page -->
<div class="min-h-screen bg-gradient-to-br from-teacher-primary to-teacher-secondary">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-800">Teacher Profile</h1>
            <p class="text-gray-600 mt-2">Educational Professional Dashboard</p>
          </div>
          <div class="flex space-x-3">
            <button id="edit-profile-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-edit mr-2"></i>Edit Profile
            </button>
            <button id="change-password-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-key mr-2"></i>Change Password
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Personal Information Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>
          <div class="p-6">
            <!-- Profile Image -->
            <div class="flex flex-col items-center mb-6">
              <div class="relative">
                <div id="profile-image-container" class="w-32 h-32 rounded-full bg-teacher-primary border-4 border-teacher-primary shadow-lg overflow-hidden flex items-center justify-center">
                  <div id="profile-image-placeholder" class="text-4xl font-bold text-white">T</div>
                  <img id="profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                </div>
                <button class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow">
                  <i class="fas fa-camera text-teacher-primary"></i>
                </button>
              </div>
              <h3 id="teacher-name" class="text-xl font-bold text-gray-800">Loading...</h3>
              <p class="text-teacher-primary font-semibold">Teacher</p>
              <span class="mt-2 px-3 py-1 bg-teacher-primary bg-opacity-10 text-teacher-primary rounded-full text-sm font-medium">
                Educational Professional
              </span>
            </div>

            <!-- Contact Information -->
            <div class="space-y-4">
              <div class="flex items-center">
                <i class="fas fa-envelope text-teacher-primary w-5"></i>
                <span id="teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user text-teacher-primary w-5"></i>
                <span id="teacher-username" class="ml-3 text-gray-700">@username</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-graduation-cap text-teacher-primary w-5"></i>
                <span id="teacher-qualifications" class="ml-3 text-gray-700">Loading...</span>
              </div>
            </div>

            <!-- Bio Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
              <h4 class="text-lg font-semibold text-gray-800 mb-3">About</h4>
              <p id="teacher-bio" class="text-gray-600 text-sm leading-relaxed">Loading...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Professional Details -->
      <div class="lg:col-span-2">
        <div class="space-y-6">

          <!-- Current Subjects Card -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Current Subjects</h2>
            </div>
    <div class="p-6">
      <div id="subjects-container" class="space-y-4">
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        </div>
      </div>

      <div id="no-subjects" class="hidden text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects assigned</h3>
        <p class="mt-1 text-sm text-gray-500">Contact the administrator to assign subjects.</p>
      </div>
    </div>
  </div>

          <!-- Assigned Classes Card -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Assigned Classes</h2>
            </div>
    <div class="p-6">
      <div id="classes-container" class="space-y-4">
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        </div>
      </div>

      <div id="no-classes" class="hidden text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No classes assigned</h3>
        <p class="mt-1 text-sm text-gray-500">Contact the administrator to assign classes.</p>
      </div>
            </div>
          </div>

          <!-- Weekly Timetable -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Weekly Timetable</h2>
            </div>
  <div class="p-6">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monday</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tuesday</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wednesday</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thursday</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Friday</th>
          </tr>
        </thead>
        <tbody id="timetable-body" class="bg-white divide-y divide-gray-200">
          <tr>
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              Loading timetable...
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div id="no-timetable" class="hidden text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No timetable available</h3>
      <p class="mt-1 text-sm text-gray-500">Your weekly schedule will appear here once it's set up.</p>
            </div>
          </div>

          <!-- Upcoming Lectures -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6">
              <h2 class="text-xl font-semibold">Upcoming Lectures</h2>
            </div>
  <div class="p-6">
    <div id="lectures-container" class="space-y-4">
      <div class="animate-pulse">
        <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
      </div>
    </div>

    <div id="no-lectures" class="hidden text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No upcoming lectures</h3>
      <p class="mt-1 text-sm text-gray-500">Your upcoming lectures will appear here.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Profile Modal -->
<div id="edit-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Edit Profile</h3>
        <button id="close-edit-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="edit-profile-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input type="text" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Qualifications</label>
          <input type="text" name="qualifications" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent"></textarea>
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-edit" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded-lg hover:bg-teacher-secondary transition-colors">Save Changes</button>
      </div>
    </form>
  </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
    <div class="bg-gradient-to-r from-teacher-primary to-teacher-secondary text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Change Password</h3>
        <button id="close-password-modal" class="text-white hover:text-gray-200">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>
    <form id="change-password-form" class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
          <input type="password" name="current_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
          <input type="password" name="new_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
          <input type="password" name="confirm_password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teacher-primary focus:border-transparent">
        </div>
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" id="cancel-password" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
        <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded-lg hover:bg-teacher-secondary transition-colors">Update Password</button>
      </div>
    </form>
  </div>
</div>
<script>
  // Fetch teacher profile data
  async function fetchTeacherProfile() {
    try {
      const response = await fetch('/api/teacher/profile');
      if (!response.ok) {
        throw new Error('Failed to fetch teacher profile');
      }

      const data = await response.json();
      if (data.success) {
        displayTeacherProfile(data.teacher);
      } else {
        showError('Failed to load teacher profile: ' + data.message);
      }
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
      showError('Error loading teacher profile. Please try again later.');
    }
  }

  // Display teacher profile data
  function displayTeacherProfile(teacher) {
    // Basic info
    document.getElementById('teacher-name').textContent = teacher.full_name || 'Unknown Teacher';
    document.getElementById('teacher-username').textContent = '@' + (teacher.username || 'username');
    document.getElementById('teacher-email').textContent = teacher.email || 'No email provided';
    document.getElementById('teacher-qualifications').textContent = teacher.qualifications || 'Not specified';
    document.getElementById('teacher-bio').textContent = teacher.bio || 'No bio provided';

    // Profile image
    if (teacher.profile_image && teacher.profile_image !== 'null' && teacher.profile_image !== 'undefined') {
      const profileImage = document.getElementById('profile-image');
      profileImage.src = teacher.profile_image;
      profileImage.onerror = function() {
        // If image fails to load, show initials instead
        this.style.display = 'none';
        document.getElementById('profile-image-placeholder').classList.remove('hidden');
        const initials = getInitials(teacher);
        document.getElementById('profile-image-placeholder').textContent = initials;
      };
      profileImage.classList.remove('hidden');
      document.getElementById('profile-image-placeholder').classList.add('hidden');
    } else {
      // Show initials
      const initials = getInitials(teacher);
      document.getElementById('profile-image-placeholder').textContent = initials;
    }

    // Helper function to get initials
    function getInitials(teacher) {
      return teacher.full_name
        ? teacher.full_name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2)
        : (teacher.username ? teacher.username.substring(0, 2).toUpperCase() : '?');
    }

    // Display subjects
    displaySubjects(teacher.assigned_subjects);

    // Display classes
    displayClasses(teacher.assigned_classes);

    // Display timetable
    displayTimetable(teacher.timetable);

    // Display upcoming lectures
    displayUpcomingLectures(teacher.upcoming_lectures);
  }

  // Display subjects
  function displaySubjects(subjects) {
    const container = document.getElementById('subjects-container');
    container.innerHTML = '';

    if (!subjects || subjects.length === 0) {
      document.getElementById('no-subjects').classList.remove('hidden');
      return;
    }

    subjects.forEach(subject => {
      const subjectElement = document.createElement('div');
      subjectElement.className = 'p-3 border border-gray-200 rounded-lg';

      subjectElement.innerHTML = `
        <h3 class="font-medium text-gray-800">${subject.name}</h3>
        <p class="text-sm text-gray-500">${subject.code || ''}</p>
        ${subject.description ? `<p class="text-sm text-gray-600 mt-1">${subject.description}</p>` : ''}
      `;

      container.appendChild(subjectElement);
    });
  }

  // Display classes
  function displayClasses(classes) {
    const container = document.getElementById('classes-container');
    container.innerHTML = '';

    if (!classes || classes.length === 0) {
      document.getElementById('no-classes').classList.remove('hidden');
      return;
    }

    classes.forEach(cls => {
      const classElement = document.createElement('div');
      classElement.className = 'p-3 border border-gray-200 rounded-lg';

      classElement.innerHTML = `
        <h3 class="font-medium text-gray-800">${cls.full_class_name}</h3>
        <p class="text-sm text-gray-500">Room: ${cls.room_number || 'Not assigned'}</p>
      `;

      container.appendChild(classElement);
    });
  }

  // Display timetable
  function displayTimetable(timetable) {
    const timetableBody = document.getElementById('timetable-body');
    timetableBody.innerHTML = '';

    if (!timetable || timetable.length === 0) {
      document.getElementById('no-timetable').classList.remove('hidden');
      return;
    }

    // Group by period
    const periodMap = {};
    timetable.forEach(lecture => {
      if (!periodMap[lecture.period]) {
        periodMap[lecture.period] = {
          period: lecture.period,
          time: `${lecture.start_time} - ${lecture.end_time}`,
          days: {}
        };
      }
      periodMap[lecture.period].days[lecture.day_of_week] = lecture;
    });

    // Sort by period
    const periods = Object.values(periodMap).sort((a, b) => a.period - b.period);

    // Create rows
    periods.forEach(period => {
      const row = document.createElement('tr');

      // Period number
      const periodCell = document.createElement('td');
      periodCell.className = 'px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900';
      periodCell.textContent = period.period;
      row.appendChild(periodCell);

      // Time
      const timeCell = document.createElement('td');
      timeCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
      timeCell.textContent = period.time;
      row.appendChild(timeCell);

      // Days (Monday to Friday)
      for (let day = 1; day <= 5; day++) {
        const dayCell = document.createElement('td');
        dayCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';

        const lecture = period.days[day];
        if (lecture) {
          dayCell.innerHTML = `
            <div class="font-medium text-gray-900">${lecture.subject_name}</div>
            <div class="text-xs text-gray-500">${lecture.class_display_name}</div>
            <div class="text-xs text-gray-500">Room: ${lecture.room || 'N/A'}</div>
          `;
        } else {
          dayCell.textContent = '-';
        }

        row.appendChild(dayCell);
      }

      timetableBody.appendChild(row);
    });
  }

  // Display upcoming lectures
  function displayUpcomingLectures(lectures) {
    const container = document.getElementById('lectures-container');
    container.innerHTML = '';

    if (!lectures || lectures.length === 0) {
      document.getElementById('no-lectures').classList.remove('hidden');
      return;
    }

    // Group lectures by date
    const lecturesByDate = {};
    lectures.forEach(lecture => {
      if (!lecturesByDate[lecture.date]) {
        lecturesByDate[lecture.date] = [];
      }
      lecturesByDate[lecture.date].push(lecture);
    });

    // Create date groups
    Object.keys(lecturesByDate).sort().forEach(date => {
      const dateGroup = document.createElement('div');
      dateGroup.className = 'mb-4';

      // Date header
      const dateHeader = document.createElement('h3');
      dateHeader.className = 'text-md font-medium text-gray-800 mb-2';

      // Format date
      const formattedDate = new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      dateHeader.textContent = formattedDate;
      dateGroup.appendChild(dateHeader);

      // Lectures for this date
      const lecturesList = document.createElement('div');
      lecturesList.className = 'space-y-2 pl-4';

      lecturesByDate[date].sort((a, b) => a.start_time.localeCompare(b.start_time)).forEach(lecture => {
        const lectureItem = document.createElement('div');
        lectureItem.className = 'p-2 border-l-2 border-teacher-primary';

        lectureItem.innerHTML = `
          <div class="flex justify-between">
            <span class="font-medium">${lecture.subject_name}</span>
            <span class="text-sm text-gray-500">${lecture.start_time} - ${lecture.end_time}</span>
          </div>
          <div class="text-sm text-gray-600">${lecture.class_name}</div>
          <div class="text-sm text-gray-500">
            ${lecture.topic ? `Topic: ${lecture.topic}` : ''}
            ${lecture.location ? ` | Room: ${lecture.location}` : ''}
          </div>
        `;

        lecturesList.appendChild(lectureItem);
      });

      dateGroup.appendChild(lecturesList);
      container.appendChild(dateGroup);
    });
  }

  // Show error message
  function showError(message) {
    // Use the toast notification system if available
    if (typeof showToast === 'function') {
      showToast('error', 'Error', message);
    } else {
      alert(message);
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    fetchTeacherProfile();
    initializeModals();
  });

  // Initialize modal functionality
  function initializeModals() {
    const editProfileBtn = document.getElementById('edit-profile-btn');
    const changePasswordBtn = document.getElementById('change-password-btn');
    const editModal = document.getElementById('edit-profile-modal');
    const passwordModal = document.getElementById('change-password-modal');

    // Edit Profile Modal
    editProfileBtn.addEventListener('click', () => {
      editModal.classList.remove('hidden');
    });

    document.getElementById('close-edit-modal').addEventListener('click', () => {
      editModal.classList.add('hidden');
    });

    document.getElementById('cancel-edit').addEventListener('click', () => {
      editModal.classList.add('hidden');
    });

    // Change Password Modal
    changePasswordBtn.addEventListener('click', () => {
      passwordModal.classList.remove('hidden');
    });

    document.getElementById('close-password-modal').addEventListener('click', () => {
      passwordModal.classList.add('hidden');
    });

    document.getElementById('cancel-password').addEventListener('click', () => {
      passwordModal.classList.add('hidden');
    });

    // Form submissions
    document.getElementById('edit-profile-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      try {
        const response = await fetch('/api/profile/update', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          alert('Profile updated successfully!');
          editModal.classList.add('hidden');
          fetchTeacherProfile(); // Reload profile data
        } else {
          alert('Failed to update profile');
        }
      } catch (error) {
        alert('Error updating profile');
      }
    });

    document.getElementById('change-password-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      if (formData.get('new_password') !== formData.get('confirm_password')) {
        alert('New passwords do not match');
        return;
      }

      try {
        const response = await fetch('/api/profile/change-password', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          alert('Password changed successfully!');
          passwordModal.classList.add('hidden');
          e.target.reset();
        } else {
          alert('Failed to change password');
        }
      } catch (error) {
        alert('Error changing password');
      }
    });

    // Close modals on outside click
    editModal.addEventListener('click', (e) => {
      if (e.target === editModal) {
        editModal.classList.add('hidden');
      }
    });

    passwordModal.addEventListener('click', (e) => {
      if (e.target === passwordModal) {
        passwordModal.classList.add('hidden');
      }
    });
  }
</script>
