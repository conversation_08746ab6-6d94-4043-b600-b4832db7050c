<%- contentFor('body') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Teacher Reports</h1>
    <div class="flex space-x-2">
      <button id="export-pdf" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm flex items-center text-white">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
        </svg>
        Export PDF
      </button>
    </div>
  </div>
  
  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="flex flex-wrap items-center gap-4">
      <div class="flex items-center">
        <label for="class-filter" class="text-sm text-gray-600 mr-2">Class:</label>
        <select id="class-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Classes</option>
          <% if (classSections && classSections.length > 0) { %>
            <% 
              // Group by class name
              const classGroups = {};
              classSections.forEach(section => {
                if (!classGroups[section.class_name]) {
                  classGroups[section.class_name] = [];
                }
                classGroups[section.class_name].push(section);
              });
              
              // Sort class names
              const sortedClassNames = Object.keys(classGroups).sort();
              
              // Display options
              sortedClassNames.forEach(className => {
            %>
              <option value="<%= className %>">Class <%= className %></option>
            <% }); %>
          <% } %>
        </select>
      </div>
      
      <div class="flex items-center">
        <label for="subject-filter" class="text-sm text-gray-600 mr-2">Subject:</label>
        <select id="subject-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Subjects</option>
          <option value="Physics">Physics</option>
          <option value="Chemistry">Chemistry</option>
          <option value="Biology">Biology</option>
          <option value="Computer Science">Computer Science</option>
        </select>
      </div>
      
      <div class="flex items-center">
        <label for="date-range" class="text-sm text-gray-600 mr-2">Date Range:</label>
        <select id="date-range" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Time</option>
          <option value="this-week">This Week</option>
          <option value="this-month">This Month</option>
          <option value="last-month">Last Month</option>
          <option value="custom">Custom Range</option>
        </select>
      </div>
      
      <div id="custom-date-range" class="hidden flex items-center space-x-2">
        <input type="date" id="date-from" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        <span class="text-gray-600">to</span>
        <input type="date" id="date-to" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>
      
      <button id="apply-filters" class="ml-auto px-3 py-1 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition text-sm">
        Apply Filters
      </button>
    </div>
  </div>
  
  <!-- Reports Tabs -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="border-b border-gray-200">
      <nav class="flex -mb-px">
        <button class="tab-button active py-4 px-6 border-b-2 border-teacher-primary text-teacher-primary font-medium" data-tab="syllabus-progress">
          Syllabus Progress
        </button>
        <button class="tab-button py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium" data-tab="lecture-summary">
          Lecture Summary
        </button>
        <button class="tab-button py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium" data-tab="practical-summary">
          Practical Summary
        </button>
        <button class="tab-button py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium" data-tab="student-performance">
          Student Performance
        </button>
      </nav>
    </div>
    
    <!-- Tab Content -->
    <div class="p-6">
      <!-- Syllabus Progress Tab -->
      <div id="syllabus-progress-tab" class="tab-content">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Syllabus Completion Status</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Physics Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">Physics</h3>
                <span class="text-sm font-medium text-gray-600">Class 11-A</span>
              </div>
              <div class="mb-2">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>75%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div class="bg-green-500 h-2.5 rounded-full" style="width: 75%"></div>
                </div>
              </div>
              <div class="text-sm text-gray-600">
                <p>Topics Completed: 15/20</p>
                <p>Last Updated: April 20, 2025</p>
              </div>
            </div>
            
            <!-- Chemistry Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">Chemistry</h3>
                <span class="text-sm font-medium text-gray-600">Class 11-A</span>
              </div>
              <div class="mb-2">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>60%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 60%"></div>
                </div>
              </div>
              <div class="text-sm text-gray-600">
                <p>Topics Completed: 12/20</p>
                <p>Last Updated: April 18, 2025</p>
              </div>
            </div>
            
            <!-- Biology Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">Biology</h3>
                <span class="text-sm font-medium text-gray-600">Class 11-A</span>
              </div>
              <div class="mb-2">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>80%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div class="bg-green-500 h-2.5 rounded-full" style="width: 80%"></div>
                </div>
              </div>
              <div class="text-sm text-gray-600">
                <p>Topics Completed: 16/20</p>
                <p>Last Updated: April 22, 2025</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Lecture Summary Tab -->
      <div id="lecture-summary-tab" class="tab-content hidden">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Lecture Delivery Summary</h2>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Lectures</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cancelled</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion %</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Physics</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">35</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">78%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 78%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Chemistry</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">40</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">70%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 70%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Biology</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">36</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">86%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 86%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Practical Summary Tab -->
      <div id="practical-summary-tab" class="tab-content hidden">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Practical Session Summary</h2>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Practicals</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conducted</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cancelled</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion %</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Physics</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">75%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 75%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Chemistry</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">67%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 67%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Biology</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Class 11-A</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex items-center">
                      <span class="mr-2">82%</span>
                      <div class="w-24 bg-gray-200 rounded-full h-2.5">
                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 82%"></div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Student Performance Tab -->
      <div id="student-performance-tab" class="tab-content hidden">
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Student Performance Analysis</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Class Average Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
              <h3 class="text-lg font-medium text-gray-800 mb-4">Class Average Performance</h3>
              
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Physics</span>
                    <span>B+ (85%)</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 85%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Chemistry</span>
                    <span>B (78%)</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 78%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Biology</span>
                    <span>A- (90%)</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 90%"></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Top Performers Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-4">
              <h3 class="text-lg font-medium text-gray-800 mb-4">Top Performers</h3>
              
              <div class="space-y-4">
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                    <span class="text-gray-600 font-medium">AS</span>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-800">Aditya Sharma</p>
                    <p class="text-xs text-gray-500">Class 11-A • Average: A (95%)</p>
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                    <span class="text-gray-600 font-medium">RK</span>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-800">Riya Kapoor</p>
                    <p class="text-xs text-gray-500">Class 11-A • Average: A- (92%)</p>
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                    <span class="text-gray-600 font-medium">VP</span>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-800">Vikram Patel</p>
                    <p class="text-xs text-gray-500">Class 11-A • Average: A- (91%)</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Remove active class from all buttons
        tabButtons.forEach(btn => {
          btn.classList.remove('active');
          btn.classList.remove('border-teacher-primary');
          btn.classList.remove('text-teacher-primary');
          btn.classList.add('border-transparent');
          btn.classList.add('text-gray-500');
        });
        
        // Add active class to clicked button
        this.classList.add('active');
        this.classList.add('border-teacher-primary');
        this.classList.add('text-teacher-primary');
        this.classList.remove('border-transparent');
        this.classList.remove('text-gray-500');
        
        // Hide all tab contents
        tabContents.forEach(content => {
          content.classList.add('hidden');
        });
        
        // Show the selected tab content
        const tabId = this.getAttribute('data-tab');
        document.getElementById(`${tabId}-tab`).classList.remove('hidden');
      });
    });
    
    // Date range filter
    const dateRangeSelect = document.getElementById('date-range');
    const customDateRange = document.getElementById('custom-date-range');
    
    dateRangeSelect.addEventListener('change', function() {
      if (this.value === 'custom') {
        customDateRange.classList.remove('hidden');
      } else {
        customDateRange.classList.add('hidden');
      }
    });
    
    // Apply filters
    document.getElementById('apply-filters').addEventListener('click', function() {
      const dateRange = document.getElementById('date-range').value;
      const subjectFilter = document.getElementById('subject-filter').value;
      const classFilter = document.getElementById('class-filter').value;
      
      let dateFrom = '';
      let dateTo = '';
      
      if (dateRange === 'custom') {
        dateFrom = document.getElementById('date-from').value;
        dateTo = document.getElementById('date-to').value;
        
        if (!dateFrom || !dateTo) {
          // Show error toast
          const toastContainer = document.getElementById('toast-container');
          const toast = document.createElement('div');
          toast.className = 'toast toast-error';
          toast.textContent = 'Please select both start and end dates for custom range';
          toastContainer.appendChild(toast);
          
          // Auto-remove toast after 3 seconds
          setTimeout(() => {
            toast.remove();
          }, 3000);
          
          return;
        }
      }
      
      // Construct the URL with query parameters
      let url = '/teacher/reports?';
      if (dateRange && dateRange !== 'all') url += `date_range=${dateRange}&`;
      if (dateRange === 'custom') {
        url += `date_from=${dateFrom}&date_to=${dateTo}&`;
      }
      if (subjectFilter && subjectFilter !== 'all') url += `subject=${encodeURIComponent(subjectFilter)}&`;
      if (classFilter && classFilter !== 'all') url += `class=${encodeURIComponent(classFilter)}&`;
      
      // Remove trailing & if present
      if (url.endsWith('&')) url = url.slice(0, -1);
      
      // Redirect to the filtered URL
      window.location.href = url;
      
      // Show toast notification
      const toastContainer = document.getElementById('toast-container');
      const toast = document.createElement('div');
      toast.className = 'toast toast-info';
      toast.textContent = 'Filters applied';
      toastContainer.appendChild(toast);
      
      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    });
    
    // Export PDF
    document.getElementById('export-pdf').addEventListener('click', function() {
      // In a real application, this would generate and download a PDF
      // For this demo, we'll just show a toast notification
      
      const toastContainer = document.getElementById('toast-container');
      const toast = document.createElement('div');
      toast.className = 'toast toast-success';
      toast.textContent = 'PDF export initiated';
      toastContainer.appendChild(toast);
      
      // Auto-remove toast after 3 seconds
      setTimeout(() => {
        toast.remove();
      }, 3000);
    });
  });
</script>

<style>
  .toast {
    @apply mb-3 px-4 py-3 rounded shadow-md text-white;
  }
  
  .toast-success {
    @apply bg-green-500;
  }
  
  .toast-error {
    @apply bg-red-500;
  }
  
  .toast-warning {
    @apply bg-yellow-500;
  }
  
  .toast-info {
    @apply bg-blue-500;
  }
</style>
