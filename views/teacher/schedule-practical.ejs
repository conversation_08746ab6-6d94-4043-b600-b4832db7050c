<!-- Schedule New Practical -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Schedule New Practical</h2>
  </div>
  
  <div class="p-6">
    <form id="schedule-practical-form">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label for="practical-date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
          <input type="date" id="practical-date" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
        
        <div>
          <label for="practical-class" class="block text-sm font-medium text-gray-700 mb-1">Class *</label>
          <select id="practical-class" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="">Select Class</option>
            <% if (classSections && classSections.length > 0) { %>
              <% classSections.forEach(section => { %>
                <option value="<%= section.id %>"><%= section.display_name %></option>
              <% }); %>
            <% } %>
          </select>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label for="practical-start-time" class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
          <input type="time" id="practical-start-time" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
        
        <div>
          <label for="practical-end-time" class="block text-sm font-medium text-gray-700 mb-1">End Time *</label>
          <input type="time" id="practical-end-time" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label for="practical-subject" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
          <select id="practical-subject" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="">Select Subject</option>
            <option value="Physics">Physics</option>
            <option value="Chemistry">Chemistry</option>
            <option value="Biology">Biology</option>
            <option value="Computer Science">Computer Science</option>
          </select>
        </div>
        
        <div>
          <label for="practical-venue" class="block text-sm font-medium text-gray-700 mb-1">Lab/Venue *</label>
          <select id="practical-venue" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="">Select Venue</option>
            <option value="Physics Lab">Physics Lab</option>
            <option value="Chemistry Lab">Chemistry Lab</option>
            <option value="Biology Lab">Biology Lab</option>
            <option value="Computer Lab 1">Computer Lab 1</option>
            <option value="Computer Lab 2">Computer Lab 2</option>
          </select>
        </div>
      </div>
      
      <div class="mb-6">
        <label for="practical-topic" class="block text-sm font-medium text-gray-700 mb-1">Practical Topic *</label>
        <input type="text" id="practical-topic" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>
      
      <div class="mb-6">
        <label for="practical-notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
        <textarea id="practical-notes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
      </div>
      
      <div class="flex justify-end space-x-3">
        <a href="/teacher/practicals" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</a>
        <button type="submit" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Schedule Practical</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const scheduleForm = document.getElementById('schedule-practical-form');
    
    scheduleForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Get form data
      const formData = {
        date: document.getElementById('practical-date').value,
        class_section_id: document.getElementById('practical-class').value,
        start_time: document.getElementById('practical-start-time').value,
        end_time: document.getElementById('practical-end-time').value,
        subject_name: document.getElementById('practical-subject').value,
        practical_topic: document.getElementById('practical-topic').value,
        venue: document.getElementById('practical-venue').value,
        notes: document.getElementById('practical-notes').value
      };
      
      // Send AJAX request to save the practical
      fetch('/teacher/api/practicals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Display success message
          showToast('success', 'Success', 'Practical scheduled successfully');
          
          // Redirect to practicals page after a delay
          setTimeout(() => {
            window.location.href = '/teacher/practicals';
          }, 1500);
        } else {
          // Display error message
          showToast('error', 'Error', data.message || 'Error scheduling practical');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Error', 'An error occurred while scheduling the practical');
      });
    });
  });
  
  // Function to show toast notifications
  function showToast(type, title, message) {
    const toast = document.createElement('div');
    toast.className = `toast ${type} animate-fade-in-down`;
    
    let bgColor = 'bg-gray-800';
    if (type === 'success') bgColor = 'bg-green-500';
    if (type === 'error') bgColor = 'bg-red-500';
    if (type === 'warning') bgColor = 'bg-yellow-500';
    if (type === 'info') bgColor = 'bg-blue-500';
    
    toast.innerHTML = `
      <div class="${bgColor} text-white px-4 py-3 rounded shadow-md flex items-center justify-between">
        <div>
          <p class="font-bold">${title}</p>
          <p class="text-sm">${message}</p>
        </div>
        <button class="ml-4 text-white" onclick="this.parentElement.parentElement.remove()">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;
    
    document.getElementById('toast-container').appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      toast.remove();
    }, 5000);
  }
</script>
