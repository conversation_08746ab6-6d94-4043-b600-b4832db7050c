
<div class="container mx-auto px-4 py-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="bg-teacher-primary text-white p-4">
      <h2 class="text-xl font-semibold">My Subject Eligibility</h2>
    </div>

    <div class="p-4">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Current Eligible Subjects</h3>

        <% if (eligibility && eligibility.length > 0) { %>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Subject</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Code</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Theory Lectures</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Practical Lectures</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Lectures</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% eligibility.forEach(subject => { %>
                  <tr>
                    <td class="py-2 px-4 border-b border-gray-200"><%= subject.subject_name %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= subject.subject_code %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= subject.max_theory_lectures %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= subject.max_practical_lectures %></td>
                    <td class="py-2 px-4 border-b border-gray-200"><%= subject.total_lectures_per_week %></td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= subject.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= subject.is_active ? 'Active' : 'Inactive' %>
                      </span>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                      <button
                        class="toggle-status-btn text-sm px-2 py-1 rounded <%= subject.is_active ? 'bg-red-500 text-white' : 'bg-green-500 text-white' %>"
                        data-id="<%= subject.id %>"
                        data-status="<%= subject.is_active ? '0' : '1' %>"
                      >
                        <%= subject.is_active ? 'Deactivate' : 'Activate' %>
                      </button>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  You are not eligible to teach any subjects yet. Please add a subject below.
                </p>
              </div>
            </div>
          </div>
        <% } %>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Add Subject Eligibility</h3>

        <form id="add-subject-form" class="bg-gray-50 p-4 rounded border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
              <select id="subject_id" name="subject_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary">
                <option value="">Select a subject</option>
                <% subjects.forEach(subject => { %>
                  <option value="<%= subject.id %>"><%= subject.name %> (<%= subject.code %>)</option>
                <% }); %>
              </select>
            </div>

            <div class="flex items-end">
              <button type="submit" class="bg-teacher-primary text-white px-4 py-2 rounded hover:bg-teacher-secondary transition">
                Add Subject
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add subject form submission
    const addSubjectForm = document.getElementById('add-subject-form');
    if (addSubjectForm) {
      addSubjectForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const subjectId = document.getElementById('subject_id').value;

        if (!subjectId) {
          showToast('Please select a subject', 'error');
          return;
        }

        try {
          const response = await fetch('/api/teacher/subject-eligibility', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subject_id: subjectId
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated list
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error adding subject eligibility:', error);
          showToast('An error occurred while adding subject eligibility', 'error');
        }
      });
    }

    // Toggle status buttons
    const toggleStatusButtons = document.querySelectorAll('.toggle-status-btn');
    toggleStatusButtons.forEach(button => {
      button.addEventListener('click', async function() {
        const id = this.dataset.id;
        const status = this.dataset.status;

        try {
          const response = await fetch('/api/teacher/subject-eligibility', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id,
              is_active: status === '1'
            })
          });

          const data = await response.json();

          if (data.success) {
            showToast(data.message, 'success');
            // Reload the page to show the updated status
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToast(data.message, 'error');
          }
        } catch (error) {
          console.error('Error updating subject eligibility status:', error);
          showToast('An error occurred while updating subject eligibility status', 'error');
        }
      });
    });
  });
</script>


