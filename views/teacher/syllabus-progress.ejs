<%- include('../partials/teacher/header') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Syllabus Progress</h1>
    <div class="flex space-x-2">
      <button id="update-progress-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Update Progress
      </button>
      <a href="/teacher/syllabus" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Back to Syllabus
      </a>
    </div>
  </div>

  <!-- Overall Progress -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Overall Syllabus Progress</h2>
    
    <% 
    let overallProgress = 0;
    let totalCompleted = 0;
    let totalTopics = 0;
    
    if (progress.length > 0) {
      progress.forEach(subject => {
        totalCompleted += subject.completed;
        totalTopics += subject.total;
      });
      
      if (totalTopics > 0) {
        overallProgress = Math.round((totalCompleted / totalTopics) * 100);
      }
    }
    %>
    
    <div class="mb-6">
      <div class="flex justify-between items-center mb-2">
        <span class="text-gray-700 font-medium">Overall Progress</span>
        <span class="text-gray-700 font-medium"><%= overallProgress %>%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-bar-fill bg-teacher-primary" style="width: <%= overallProgress %>%"></div>
      </div>
      <div class="flex justify-between items-center mt-2 text-sm text-gray-500">
        <span><%= totalCompleted %> topics completed</span>
        <span><%= totalTopics %> total topics</span>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <canvas id="progressChart"></canvas>
      </div>
      <div>
        <canvas id="topicsChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Subject Progress -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Subject-wise Progress</h2>
    
    <% if (progress.length === 0) { %>
      <div class="text-center py-8 text-gray-500">
        No syllabus progress data available. Add topics to track progress.
      </div>
    <% } else { %>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <% progress.forEach(subject => { %>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-lg font-medium text-gray-800"><%= subject.name %></h3>
              <span class="text-<%= subject.progress >= 75 ? 'green' : subject.progress >= 50 ? 'blue' : subject.progress >= 25 ? 'yellow' : 'red' %>-600 font-semibold"><%= subject.progress %>%</span>
            </div>
            <div class="progress-bar mb-2">
              <div class="progress-bar-fill bg-<%= subject.progress >= 75 ? 'green' : subject.progress >= 50 ? 'blue' : subject.progress >= 25 ? 'yellow' : 'red' %>-500" style="width: <%= subject.progress %>%"></div>
            </div>
            <div class="flex justify-between items-center text-sm text-gray-500">
              <span><%= subject.completed %> topics completed</span>
              <span><%= subject.total %> total topics</span>
            </div>
          </div>
        <% }); %>
      </div>
    <% } %>
  </div>
</div>

<!-- Update Progress Modal -->
<div id="progress-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Update Syllabus Progress</h3>
    </div>
    <form id="progress-form">
      <div class="px-6 py-4">
        <div class="mb-4">
          <label for="subject-name" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
          <select id="subject-name" name="subject_name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
            <option value="">Select Subject</option>
            <% subjects.forEach(subject => { %>
              <option value="<%= subject.subject_name %>"><%= subject.subject_name %></option>
            <% }); %>
            <option value="new">+ Add New Subject</option>
          </select>
        </div>
        
        <div id="new-subject-field" class="mb-4 hidden">
          <label for="new-subject" class="block text-sm font-medium text-gray-700 mb-1">New Subject Name</label>
          <input type="text" id="new-subject" name="new_subject" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
        
        <div class="mb-4">
          <label for="total-topics" class="block text-sm font-medium text-gray-700 mb-1">Total Topics</label>
          <input type="number" id="total-topics" name="total_topics" min="1" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
        </div>
        
        <div class="mb-4">
          <label for="completed-topics" class="block text-sm font-medium text-gray-700 mb-1">Completed Topics</label>
          <input type="number" id="completed-topics" name="completed_topics" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
        </div>
      </div>
      <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
        <button type="button" id="cancel-progress" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md">
          Cancel
        </button>
        <button type="submit" id="save-progress" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md">
          Save
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initCharts();
    
    // Show/hide new subject field based on selection
    const subjectField = document.getElementById('subject-name');
    const newSubjectField = document.getElementById('new-subject-field');
    
    subjectField.addEventListener('change', function() {
      if (this.value === 'new') {
        newSubjectField.classList.remove('hidden');
        document.getElementById('new-subject').required = true;
      } else {
        newSubjectField.classList.add('hidden');
        document.getElementById('new-subject').required = false;
      }
    });
    
    // Update Progress Button
    document.getElementById('update-progress-btn').addEventListener('click', function() {
      document.getElementById('progress-form').reset();
      document.getElementById('progress-modal').classList.remove('hidden');
      newSubjectField.classList.add('hidden');
    });
    
    // Cancel Button
    document.getElementById('cancel-progress').addEventListener('click', function() {
      document.getElementById('progress-modal').classList.add('hidden');
    });
    
    // Form Submit
    document.getElementById('progress-form').addEventListener('submit', function(e) {
      e.preventDefault();
      const formData = new FormData(this);
      
      // Convert FormData to JSON
      const data = {};
      formData.forEach((value, key) => {
        data[key] = value;
      });
      
      // Handle new subject
      if (data.subject_name === 'new' && data.new_subject) {
        data.subject_name = data.new_subject;
      }
      delete data.new_subject;
      
      // Validate completed topics <= total topics
      if (parseInt(data.completed_topics) > parseInt(data.total_topics)) {
        showToast('error', 'Error', 'Completed topics cannot exceed total topics');
        return;
      }
      
      // Make API call
      apiCall('/api/teacher/syllabus/progress', 'POST', data)
        .then(response => {
          if (response.success) {
            showToast('success', 'Success', response.message);
            document.getElementById('progress-modal').classList.add('hidden');
            // Reload page to show updated data
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        })
        .catch(error => {
          showToast('error', 'Error', error.message);
        });
    });
    
    // Initialize charts
    function initCharts() {
      // Progress Pie Chart
      const progressCtx = document.getElementById('progressChart').getContext('2d');
      new Chart(progressCtx, {
        type: 'doughnut',
        data: {
          labels: ['Completed', 'Remaining'],
          datasets: [{
            data: [<%= totalCompleted %>, <%= totalTopics - totalCompleted %>],
            backgroundColor: ['#4a6da7', '#e5e7eb'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: 'Overall Completion',
              font: {
                size: 16
              }
            }
          },
          cutout: '70%'
        }
      });
      
      // Subject-wise Bar Chart
      const topicsCtx = document.getElementById('topicsChart').getContext('2d');
      new Chart(topicsCtx, {
        type: 'bar',
        data: {
          labels: [<% progress.forEach(subject => { %>'<%= subject.name %>', <% }); %>],
          datasets: [{
            label: 'Completed Topics',
            data: [<% progress.forEach(subject => { %><%= subject.completed %>, <% }); %>],
            backgroundColor: '#4a6da7',
            borderWidth: 0
          }, {
            label: 'Total Topics',
            data: [<% progress.forEach(subject => { %><%= subject.total %>, <% }); %>],
            backgroundColor: '#a3b9df',
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: 'Subject-wise Topics',
              font: {
                size: 16
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                precision: 0
              }
            }
          }
        }
      });
    }
  });
</script>

<%- include('../partials/teacher/footer') %>
