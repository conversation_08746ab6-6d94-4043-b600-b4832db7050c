<%- include('../partials/teacher/header') %>

<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Syllabus Topics</h1>
    <div class="flex space-x-2">
      <button id="add-topic-btn" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Topic
      </button>
      <a href="/teacher/syllabus" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Back to Syllabus
      </a>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label for="subject-filter" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
        <select id="subject-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Subjects</option>
          <% 
          const subjects = [...new Set(topics.map(topic => topic.subject_name))];
          subjects.forEach(subject => { 
          %>
            <option value="<%= subject %>"><%= subject %></option>
          <% }); %>
        </select>
      </div>
      <div>
        <label for="class-filter" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
        <select id="class-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Classes</option>
          <% 
          const classes = [...new Set(topics.map(topic => topic.class_name))];
          classes.forEach(className => { 
          %>
            <option value="<%= className %>"><%= className %></option>
          <% }); %>
        </select>
      </div>
      <div>
        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="status-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="completed">Completed</option>
        </select>
      </div>
    </div>
    <div class="mt-4 flex justify-end">
      <button id="apply-filters" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md">
        Apply Filters
      </button>
    </div>
  </div>

  <!-- Topics List -->
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% if (topics.length === 0) { %>
            <tr>
              <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                No topics found. Add a new topic to get started.
              </td>
            </tr>
          <% } else { %>
            <% topics.forEach(topic => { %>
              <tr class="topic-row" 
                  data-subject="<%= topic.subject_name %>" 
                  data-class="<%= topic.class_name %>" 
                  data-status="<%= topic.status %>">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= topic.subject_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= topic.class_name %>
                </td>
                <td class="px-6 py-4 text-sm text-gray-500">
                  <%= topic.topic_name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% if (topic.status === 'completed') { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Completed
                    </span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= topic.completion_date ? new Date(topic.completion_date).toLocaleDateString() : 'Not completed' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button class="text-teacher-primary hover:text-teacher-secondary edit-topic-btn" data-topic-id="<%= topic.id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                      </svg>
                    </button>
                    <% if (topic.status === 'pending') { %>
                      <button class="text-green-600 hover:text-green-800 mark-completed-btn" data-topic-id="<%= topic.id %>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </button>
                    <% } %>
                    <button class="text-red-600 hover:text-red-800 delete-topic-btn" data-topic-id="<%= topic.id %>">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Add/Edit Topic Modal -->
<div id="topic-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Add New Topic</h3>
    </div>
    <form id="topic-form">
      <div class="px-6 py-4">
        <input type="hidden" id="topic-id" name="topic_id" value="">
        
        <div class="mb-4">
          <label for="class-section" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
          <select id="class-section" name="class_section_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
            <option value="">Select Class</option>
            <% classSections.forEach(section => { %>
              <option value="<%= section.id %>">
                <%= section.trade_name === 'General' 
                  ? `Class ${section.class_name}-${section.section}` 
                  : `Class ${section.class_name}-${section.section} (${section.trade_name})` %>
              </option>
            <% }); %>
          </select>
        </div>
        
        <div class="mb-4">
          <label for="subject-name" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
          <input type="text" id="subject-name" name="subject_name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
        </div>
        
        <div class="mb-4">
          <label for="topic-name" class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
          <input type="text" id="topic-name" name="topic_name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" required>
        </div>
        
        <div class="mb-4">
          <label for="topic-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea id="topic-description" name="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
        </div>
        
        <div id="status-field" class="mb-4">
          <label for="topic-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select id="topic-status" name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
          </select>
        </div>
        
        <div id="completion-date-field" class="mb-4 hidden">
          <label for="completion-date" class="block text-sm font-medium text-gray-700 mb-1">Completion Date</label>
          <input type="date" id="completion-date" name="completion_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
        </div>
      </div>
      <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
        <button type="button" id="cancel-topic" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md">
          Cancel
        </button>
        <button type="submit" id="save-topic" class="bg-teacher-primary hover:bg-teacher-secondary text-white px-4 py-2 rounded-md">
          Save
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 id="confirmation-title" class="text-lg font-semibold text-gray-900">Confirm Action</h3>
    </div>
    <div class="px-6 py-4">
      <p id="confirmation-message" class="text-gray-700">Are you sure you want to perform this action?</p>
    </div>
    <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2 rounded-b-lg">
      <button id="cancel-action" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md">
        Cancel
      </button>
      <button id="confirm-action" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md">
        Confirm
      </button>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Show/hide completion date field based on status
    const statusField = document.getElementById('topic-status');
    const completionDateField = document.getElementById('completion-date-field');
    
    statusField.addEventListener('change', function() {
      if (this.value === 'completed') {
        completionDateField.classList.remove('hidden');
        document.getElementById('completion-date').required = true;
      } else {
        completionDateField.classList.add('hidden');
        document.getElementById('completion-date').required = false;
      }
    });
    
    // Add Topic Button
    document.getElementById('add-topic-btn').addEventListener('click', function() {
      document.getElementById('modal-title').textContent = 'Add New Topic';
      document.getElementById('topic-form').reset();
      document.getElementById('topic-id').value = '';
      document.getElementById('topic-modal').classList.remove('hidden');
      completionDateField.classList.add('hidden');
    });
    
    // Cancel Button
    document.getElementById('cancel-topic').addEventListener('click', function() {
      document.getElementById('topic-modal').classList.add('hidden');
    });
    
    // Form Submit
    document.getElementById('topic-form').addEventListener('submit', function(e) {
      e.preventDefault();
      const formData = new FormData(this);
      const topicId = document.getElementById('topic-id').value;
      
      // Convert FormData to JSON
      const data = {};
      formData.forEach((value, key) => {
        data[key] = value;
      });
      
      // API endpoint and method
      const endpoint = topicId 
        ? `/api/teacher/syllabus/${topicId}` 
        : '/api/teacher/syllabus';
      const method = topicId ? 'PUT' : 'POST';
      
      // Make API call
      apiCall(endpoint, method, data)
        .then(response => {
          if (response.success) {
            showToast('success', 'Success', response.message);
            document.getElementById('topic-modal').classList.add('hidden');
            // Reload page to show updated data
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        })
        .catch(error => {
          showToast('error', 'Error', error.message);
        });
    });
    
    // Edit Topic Buttons
    document.querySelectorAll('.edit-topic-btn').forEach(button => {
      button.addEventListener('click', function() {
        const topicId = this.getAttribute('data-topic-id');
        
        // Get topic details from API
        apiCall(`/api/teacher/syllabus/${topicId}`)
          .then(response => {
            if (response.success) {
              const topic = response.topic;
              
              document.getElementById('modal-title').textContent = 'Edit Topic';
              document.getElementById('topic-id').value = topic.id;
              document.getElementById('class-section').value = topic.class_section_id;
              document.getElementById('subject-name').value = topic.subject_name;
              document.getElementById('topic-name').value = topic.topic_name;
              document.getElementById('topic-description').value = topic.description || '';
              document.getElementById('topic-status').value = topic.status;
              
              if (topic.status === 'completed') {
                completionDateField.classList.remove('hidden');
                document.getElementById('completion-date').required = true;
                
                // Format date for input
                if (topic.completion_date) {
                  const date = new Date(topic.completion_date);
                  const formattedDate = date.toISOString().split('T')[0];
                  document.getElementById('completion-date').value = formattedDate;
                }
              } else {
                completionDateField.classList.add('hidden');
                document.getElementById('completion-date').required = false;
              }
              
              document.getElementById('topic-modal').classList.remove('hidden');
            }
          })
          .catch(error => {
            showToast('error', 'Error', error.message);
          });
      });
    });
    
    // Mark as Completed Buttons
    document.querySelectorAll('.mark-completed-btn').forEach(button => {
      button.addEventListener('click', function() {
        const topicId = this.getAttribute('data-topic-id');
        
        initConfirmDialog(
          'Mark as Completed',
          'Are you sure you want to mark this topic as completed?',
          () => {
            // Get current date in YYYY-MM-DD format
            const today = new Date().toISOString().split('T')[0];
            
            // Make API call
            apiCall(`/api/teacher/syllabus/${topicId}/status`, 'PUT', {
              status: 'completed',
              completion_date: today
            })
              .then(response => {
                if (response.success) {
                  showToast('success', 'Success', response.message);
                  // Reload page to show updated data
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                }
              })
              .catch(error => {
                showToast('error', 'Error', error.message);
              });
          }
        );
      });
    });
    
    // Delete Topic Buttons
    document.querySelectorAll('.delete-topic-btn').forEach(button => {
      button.addEventListener('click', function() {
        const topicId = this.getAttribute('data-topic-id');
        
        initConfirmDialog(
          'Delete Topic',
          'Are you sure you want to delete this topic? This action cannot be undone.',
          () => {
            // Make API call
            apiCall(`/api/teacher/syllabus/${topicId}`, 'DELETE')
              .then(response => {
                if (response.success) {
                  showToast('success', 'Success', response.message);
                  // Reload page to show updated data
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                }
              })
              .catch(error => {
                showToast('error', 'Error', error.message);
              });
          }
        );
      });
    });
    
    // Filters
    document.getElementById('apply-filters').addEventListener('click', function() {
      const subjectFilter = document.getElementById('subject-filter').value;
      const classFilter = document.getElementById('class-filter').value;
      const statusFilter = document.getElementById('status-filter').value;
      
      document.querySelectorAll('.topic-row').forEach(row => {
        const subject = row.getAttribute('data-subject');
        const className = row.getAttribute('data-class');
        const status = row.getAttribute('data-status');
        
        let visible = true;
        
        if (subjectFilter && subject !== subjectFilter) {
          visible = false;
        }
        
        if (classFilter && className !== classFilter) {
          visible = false;
        }
        
        if (statusFilter && status !== statusFilter) {
          visible = false;
        }
        
        if (visible) {
          row.classList.remove('hidden');
        } else {
          row.classList.add('hidden');
        }
      });
    });
  });
</script>

<%- include('../partials/teacher/footer') %>
