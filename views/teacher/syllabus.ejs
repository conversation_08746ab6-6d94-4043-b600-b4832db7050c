<!-- Teacher Syllabus Overview -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Syllabus Overview</h2>
    <div class="flex space-x-2">
      <button id="manage-topics-btn" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Manage Topics
      </button>
    </div>
  </div>

  <!-- Filter Options -->
  <div class="p-4 bg-gray-50 border-b border-gray-200">
    <div class="flex flex-wrap gap-4 items-center">
      <div class="flex items-center">
        <label for="class-filter" class="text-sm text-gray-600 mr-2">Class:</label>
        <select id="class-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Classes</option>
          <% if (classSections && classSections.length > 0) { %>
            <%
              // Group by class name
              const classGroups = {};
              classSections.forEach(section => {
                if (!classGroups[section.class_name]) {
                  classGroups[section.class_name] = [];
                }
                classGroups[section.class_name].push(section);
              });

              // Sort class names
              const sortedClassNames = Object.keys(classGroups).sort();

              // Display options
              sortedClassNames.forEach(className => {
            %>
              <option value="<%= className %>">Class <%= className %></option>
            <% }); %>
          <% } %>
        </select>
      </div>
      <div class="flex items-center">
        <label for="subject-filter" class="text-sm text-gray-600 mr-2">Subject:</label>
        <select id="subject-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Subjects</option>
          <option value="computer-science">Computer Science</option>
          <option value="informatics-practices">Informatics Practices</option>
        </select>
      </div>
      <div class="flex items-center">
        <label for="status-filter" class="text-sm text-gray-600 mr-2">Status:</label>
        <select id="status-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Status</option>
          <option value="completed">Completed</option>
          <option value="pending">Pending</option>
          <option value="in-progress">In Progress</option>
          <option value="skipped">Skipped</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Syllabus Progress Summary -->
  <div class="p-6 border-b border-gray-200">
    <h3 class="text-lg font-medium text-gray-800 mb-4">Overall Progress</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Class 11 Computer Science -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">Class 11 - Computer Science</h4>
          <span class="text-sm font-medium text-gray-700">65%</span>
        </div>
        <div class="progress-container">
          <div class="progress-bar bg-blue-500" style="width: 65%"></div>
        </div>
        <div class="mt-2 text-xs text-gray-500">26/40 topics completed</div>
      </div>

      <!-- Class 11 Informatics Practices -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">Class 11 - Informatics Practices</h4>
          <span class="text-sm font-medium text-gray-700">45%</span>
        </div>
        <div class="progress-container">
          <div class="progress-bar bg-yellow-500" style="width: 45%"></div>
        </div>
        <div class="mt-2 text-xs text-gray-500">18/40 topics completed</div>
      </div>

      <!-- Class 12 Computer Science -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">Class 12 - Computer Science</h4>
          <span class="text-sm font-medium text-gray-700">80%</span>
        </div>
        <div class="progress-container">
          <div class="progress-bar bg-green-500" style="width: 80%"></div>
        </div>
        <div class="mt-2 text-xs text-gray-500">32/40 topics completed</div>
      </div>

      <!-- Class 12 Informatics Practices -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-sm font-medium text-gray-700">Class 12 - Informatics Practices</h4>
          <span class="text-sm font-medium text-gray-700">55%</span>
        </div>
        <div class="progress-container">
          <div class="progress-bar bg-purple-500" style="width: 55%"></div>
        </div>
        <div class="mt-2 text-xs text-gray-500">22/40 topics completed</div>
      </div>
    </div>
  </div>

  <!-- Syllabus Details -->
  <div class="p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-800">Syllabus Details</h3>
      <div class="flex items-center">
        <input type="text" id="search-topics" placeholder="Search topics..." class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>
    </div>

    <!-- Class 11 Computer Science Section -->
    <div class="mb-8 syllabus-section" data-class="11" data-subject="computer-science">
      <div class="flex items-center justify-between bg-gray-100 p-3 rounded-t-lg cursor-pointer section-toggle" data-section-id="class11-cs">
        <h4 class="text-md font-semibold text-gray-800">Class 11 - Computer Science</h4>
        <div class="flex items-center">
          <span class="text-sm text-gray-600 mr-2">26/40 topics</span>
          <svg id="class11-cs-icon" class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>

      <div id="class11-cs-content" class="border border-gray-200 rounded-b-lg p-4">
        <!-- Unit 1 -->
        <div class="mb-4">
          <h5 class="text-sm font-semibold text-gray-700 mb-2">Unit 1: Computer Fundamentals</h5>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 bg-green-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span class="text-sm">Introduction to Computers</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-green-600 mr-2">Completed</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="101">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-green-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span class="text-sm">Computer Organization</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-green-600 mr-2">Completed</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="102">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-yellow-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                <span class="text-sm">Memory Units and Storage Devices</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-yellow-600 mr-2">In Progress</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="103">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                <span class="text-sm">Software Concepts</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-gray-600 mr-2">Pending</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="104">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Unit 2 -->
        <div class="mb-4">
          <h5 class="text-sm font-semibold text-gray-700 mb-2">Unit 2: Programming Concepts</h5>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 bg-green-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <span class="text-sm">Introduction to Problem Solving</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-green-600 mr-2">Completed</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="105">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-red-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                <span class="text-sm">Algorithms and Flowcharts</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-red-600 mr-2">Skipped</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="106">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div class="flex items-center">
                <span class="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                <span class="text-sm">Introduction to Python</span>
              </div>
              <div class="flex items-center">
                <span class="text-xs text-gray-600 mr-2">Pending</span>
                <button class="text-gray-500 hover:text-gray-700 edit-topic-btn" data-topic-id="107">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- More units would go here -->
      </div>
    </div>

    <!-- Class 12 Computer Science Section (Collapsed by default) -->
    <div class="mb-8 syllabus-section" data-class="12" data-subject="computer-science">
      <div class="flex items-center justify-between bg-gray-100 p-3 rounded-lg cursor-pointer section-toggle" data-section-id="class12-cs">
        <h4 class="text-md font-semibold text-gray-800">Class 12 - Computer Science</h4>
        <div class="flex items-center">
          <span class="text-sm text-gray-600 mr-2">32/40 topics</span>
          <svg id="class12-cs-icon" class="w-5 h-5 transform transition-transform rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>

      <div id="class12-cs-content" class="border border-gray-200 rounded-b-lg p-4 hidden">
        <!-- Content would go here -->
        <div class="text-sm text-gray-600">Click to expand and view Class 12 Computer Science syllabus details.</div>
      </div>
    </div>

    <!-- More sections would go here -->
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

<!-- Topic Edit Modal -->
<div id="topic-edit-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-gray-800">Update Topic Status</h2>
      <button id="close-topic-modal" class="text-gray-400 hover:text-gray-500">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <div id="topic-edit-content">
      <div class="mb-4">
        <label for="topic-name" class="block text-sm font-medium text-gray-700 mb-1">Topic Name</label>
        <input type="text" id="topic-name" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" readonly>
      </div>

      <div class="mb-4">
        <label for="topic-status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select id="topic-status" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="completed">Completed</option>
          <option value="in-progress">In Progress</option>
          <option value="pending">Pending</option>
          <option value="skipped">Skipped</option>
        </select>
      </div>

      <div class="mb-4">
        <label for="completion-date" class="block text-sm font-medium text-gray-700 mb-1">Completion Date</label>
        <input type="date" id="completion-date" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
      </div>

      <div class="mb-4">
        <label for="topic-remarks" class="block text-sm font-medium text-gray-700 mb-1">Remarks</label>
        <textarea id="topic-remarks" rows="3" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50"></textarea>
      </div>
    </div>

    <div class="flex justify-end space-x-3 mt-6">
      <button id="save-topic-btn" class="px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">Save Changes</button>
      <button id="close-topic-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
    </div>
  </div>
</div>

<%- contentFor('script') %>
<script>
  // Edit topic function
  function editTopic(topicId) {
    // Show loading toast
    showToast('info', 'Loading', 'Fetching topic details...');

    // In a real application, you would fetch topic details from the server
    // For demo purposes, we'll use mock data and simulate a delay
    setTimeout(() => {
      const topicDetails = {
        id: topicId,
        name: getTopicNameById(topicId),
        status: getTopicStatusById(topicId),
        completionDate: '2023-05-15',
        remarks: 'Covered in detail with practical examples.'
      };

      // Populate modal content
      document.getElementById('topic-name').value = topicDetails.name;
      document.getElementById('topic-status').value = topicDetails.status;
      document.getElementById('completion-date').value = topicDetails.completionDate;
      document.getElementById('topic-remarks').value = topicDetails.remarks;

      // Set topic ID for save button
      document.getElementById('save-topic-btn').setAttribute('data-topic-id', topicId);

      // Show modal
      document.getElementById('topic-edit-modal').classList.remove('hidden');
    }, 500);
  }

  // Close topic edit modal
  function closeTopicModal() {
    document.getElementById('topic-edit-modal').classList.add('hidden');
  }

  // Save topic changes
  function saveTopic() {
    const topicId = document.getElementById('save-topic-btn').getAttribute('data-topic-id');
    const status = document.getElementById('topic-status').value;
    const completionDate = document.getElementById('completion-date').value;
    const remarks = document.getElementById('topic-remarks').value;

    // In a real application, you would send an API request to update the topic
    console.log(`Updating topic ${topicId} with status: ${status}, completion date: ${completionDate}, remarks: ${remarks}`);

    // Show success message
    showToast('success', 'Success', 'Topic status updated successfully');

    // Close modal and reload page after a delay
    closeTopicModal();
    setTimeout(() => {
      // window.location.reload();
      // For demo, we'll just close the modal
    }, 1000);
  }

  // Toggle section visibility
  function toggleSection(sectionId) {
    const content = document.getElementById(`${sectionId}-content`);
    const icon = document.getElementById(`${sectionId}-icon`);

    if (content.classList.contains('hidden')) {
      content.classList.remove('hidden');
      icon.classList.remove('rotate-180');
    } else {
      content.classList.add('hidden');
      icon.classList.add('rotate-180');
    }
  }

  // Filter syllabus based on selected filters
  function filterSyllabus() {
    console.log('filterSyllabus function called');

    const classFilter = document.getElementById('class-filter').value;
    const subjectFilter = document.getElementById('subject-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    console.log('Filter values:', { classFilter, subjectFilter, statusFilter });

    const sections = document.querySelectorAll('.syllabus-section');
    console.log('Found sections:', sections.length);

    sections.forEach(section => {
      const sectionClass = section.getAttribute('data-class');
      const sectionSubject = section.getAttribute('data-subject');

      console.log('Section attributes:', { sectionClass, sectionSubject });

      let showSection = true;

      if (classFilter !== 'all' && sectionClass !== classFilter) {
        showSection = false;
      }

      if (subjectFilter !== 'all' && sectionSubject !== subjectFilter) {
        showSection = false;
      }

      // Status filtering would need to be more complex in a real application
      // as it would need to check individual topics

      console.log('Show section:', showSection);
      section.style.display = showSection ? 'block' : 'none';
    });

    // Show a toast notification to confirm filtering was applied
    showToast('info', 'Filters Applied', 'Syllabus filtered with selected criteria');
  }

  // Search topics
  function searchTopics() {
    const searchTerm = document.getElementById('search-topics').value.toLowerCase();
    const topicElements = document.querySelectorAll('.syllabus-section .flex.items-center.justify-between.p-2');

    topicElements.forEach(topicElement => {
      const topicName = topicElement.querySelector('span.text-sm').textContent.toLowerCase();

      if (topicName.includes(searchTerm)) {
        topicElement.style.display = 'flex';
      } else {
        topicElement.style.display = 'none';
      }
    });
  }

  // Helper function to get topic name by ID
  function getTopicNameById(topicId) {
    const topicNames = {
      101: 'Introduction to Computers',
      102: 'Computer Organization',
      103: 'Memory Units and Storage Devices',
      104: 'Software Concepts',
      105: 'Introduction to Problem Solving',
      106: 'Algorithms and Flowcharts',
      107: 'Introduction to Python'
    };

    return topicNames[topicId] || `Topic ${topicId}`;
  }

  // Helper function to get topic status by ID
  function getTopicStatusById(topicId) {
    const topicStatuses = {
      101: 'completed',
      102: 'completed',
      103: 'in-progress',
      104: 'pending',
      105: 'completed',
      106: 'skipped',
      107: 'pending'
    };

    return topicStatuses[topicId] || 'pending';
  }

  // Initialize event listeners when DOM is loaded
  $(document).ready(function() {
    // Filter event listeners
    $('#class-filter').on('change', filterSyllabus);
    $('#subject-filter').on('change', filterSyllabus);
    $('#status-filter').on('change', filterSyllabus);
    $('#search-topics').on('input', searchTopics);

    // Modal close buttons
    $('#close-topic-modal, #close-topic-btn').on('click', closeTopicModal);

    // Save topic button
    $('#save-topic-btn').on('click', saveTopic);

    // Section toggle buttons
    $('.section-toggle').on('click', function() {
      const sectionId = $(this).attr('data-section-id');
      toggleSection(sectionId);
    });

    // Add click handler for edit topic buttons
    $(document).on('click', '.edit-topic-btn', function(e) {
      e.preventDefault();
      e.stopPropagation();

      // Get the topic ID from the data attribute
      const topicId = $(this).data('topic-id');
      if (topicId) {
        editTopic(topicId);
      }

      return false;
    });

    // Add click handler for manage topics button
    $('#manage-topics-btn').on('click', function(e) {
      e.preventDefault();
      showToast('info', 'Coming Soon', 'Topic management will be available soon');
      return false;
    });
  });

  // Log to console to verify the script is loaded
  console.log('Syllabus JavaScript loaded successfully');
</script>
