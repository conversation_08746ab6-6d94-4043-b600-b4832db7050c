<!-- Teacher Timetable View with Teacher Filter -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Teacher Timetable</h2>
    <div class="flex space-x-2">
      <button id="prev-week" class="p-1 rounded hover:bg-teacher-secondary transition">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <button id="current-week" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm">
        Current Week
      </button>
      <button id="next-week" class="p-1 rounded hover:bg-teacher-secondary transition">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Week Display -->
  <div class="p-4 bg-gray-50 border-b border-gray-200">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-medium text-gray-800" id="week-display">Week of <span id="week-start-date">May 1, 2023</span> - <span id="week-end-date">May 7, 2023</span></h3>
      <div class="flex items-center space-x-4">
        <!-- Link to Class-Subject Summary Page -->
        <a href="/teacher/class-subject-summary" class="px-3 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition text-sm flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          Class-Subject Summary
        </a>

        <!-- Teacher filter using chosen -->
        <div style="min-width: 300px;">
          <span class="text-sm text-gray-600 block mb-1">Teacher:</span>
          <select id="teacher-filter" class="chosen-select form-select text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" data-placeholder="Select a teacher" style="width: 100%;">
            <option value=""></option>
            <!-- Will be populated dynamically -->
          </select>
        </div>
      </div>
    </div>
    <div class="mt-2 text-sm text-gray-600">
      Currently using schedule effective from: <span id="current-schedule-date" class="font-medium">April 1, 2023</span>
    </div>
  </div>

  <!-- Timetable Grid -->
  <div class="p-6">
    <div class="overflow-auto" style="max-height: 600px;">
      <table class="min-w-full border border-gray-200">
        <thead class="sticky top-0 bg-white">
          <tr id="timetable-content-header">
            <th class="w-20 bg-gray-50 border border-gray-200 px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Monday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Tuesday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Wednesday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Thursday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Friday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Saturday</th>
          </tr>
        </thead>
        <tbody id="timetable-content">
          <!-- Dynamic timetable content will be inserted here -->
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Legend -->
<div class="mt-4 mb-4 p-4 bg-white rounded-lg shadow-sm">
  <h3 class="text-lg font-medium mb-3">Legend</h3>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <!-- Status Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Status</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-green-100 mr-2"></span>
          <span class="text-sm">Delivered</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-gray-100 mr-2"></span>
          <span class="text-sm">Pending</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-yellow-100 mr-2"></span>
          <span class="text-sm">Rescheduled</span>
        </div>
      </div>
    </div>

    <!-- Streams Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Streams</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-blue-400 mr-2"></span>
          <span class="text-sm">Non-Medical</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-purple-400 mr-2"></span>
          <span class="text-sm">Medical</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-teal-400 mr-2"></span>
          <span class="text-sm">Commerce</span>
        </div>
      </div>
    </div>

    <!-- Subjects Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Subjects</h4>
      <div class="grid grid-cols-2 gap-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-blue-50 mr-2"></span>
          <span class="text-sm">Physics</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-green-50 mr-2"></span>
          <span class="text-sm">Chemistry</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-yellow-50 mr-2"></span>
          <span class="text-sm">Mathematics</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-purple-50 mr-2"></span>
          <span class="text-sm">Biology</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-red-50 mr-2"></span>
          <span class="text-sm">Computer Sci</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-indigo-50 mr-2"></span>
          <span class="text-sm">English</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-orange-50 mr-2"></span>
          <span class="text-sm">Punjabi</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-teal-50 mr-2"></span>
          <span class="text-sm">Business St.</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-cyan-50 mr-2"></span>
          <span class="text-sm">Accountancy</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-emerald-50 mr-2"></span>
          <span class="text-sm">Economics</span>
        </div>
      </div>
    </div>

    <!-- Highlights Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Highlights</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-2 ring-blue-500 ring-offset-2 mr-2"></span>
          <span class="text-sm">Current Lecture</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-1 ring-indigo-300 mr-2"></span>
          <span class="text-sm">Your Lecture</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-2 ring-red-500 ring-offset-2 bg-red-100 mr-2"></span>
          <span class="text-sm">Your Current Lecture</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Lecture Details Modal -->
<div id="lecture-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-gray-800">Lecture Details</h2>
      <button id="close-lecture-modal" class="text-gray-400 hover:text-gray-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="lecture-details-content" class="mb-6">
      <!-- Content will be dynamically populated -->
    </div>
    <div class="flex justify-end space-x-3">
      <button id="close-modal-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Close</button>
      <button id="reschedule-btn" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition">Reschedule</button>
      <button id="mark-delivered-btn" data-lecture-id="" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">Mark as Delivered</button>
    </div>
  </div>
</div>

<!-- Add legend for instruction plans and syllabus topics -->
<script>
  // Add to the legend when the document is ready
  $(document).ready(function() {
    // Find the highlights legend
    const highlightsLegend = document.querySelector('.legend .grid > div:last-child .space-y-2');

    if (highlightsLegend) {
      // Add instruction plan and syllabus topic badges to the legend
      const newItems = `
        <div class="flex items-center">
          <span class="inline-block px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs mr-2">IP</span>
          <span class="text-sm">Instruction Plan</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block px-1.5 py-0.5 bg-purple-100 text-purple-800 rounded-full text-xs mr-2">ST</span>
          <span class="text-sm">Syllabus Topic</span>
        </div>
      `;

      // Append to the legend
      highlightsLegend.innerHTML += newItems;
    }

    // Set up click handler for mark delivered button
    $('#mark-delivered-btn').on('click', function() {
      const lectureId = $(this).data('lecture-id');
      if (lectureId && typeof markAsDelivered === 'function') {
        markAsDelivered(lectureId);
      } else {
        console.error('Cannot mark as delivered: missing lecture ID or function');
      }
    });
  });
</script>

<!-- Include the toast notification script if not already included -->
<script>
  // Toast notification function
  function showToast(type, title, message) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast flex items-start p-4 mb-4 w-full max-w-xs rounded shadow-lg';

    // Set color based on type
    if (type === 'success') {
      toast.classList.add('bg-green-50', 'border-l-4', 'border-green-500', 'text-green-800');
    } else if (type === 'error') {
      toast.classList.add('bg-red-50', 'border-l-4', 'border-red-500', 'text-red-800');
    } else if (type === 'info') {
      toast.classList.add('bg-blue-50', 'border-l-4', 'border-blue-500', 'text-blue-800');
    } else {
      toast.classList.add('bg-gray-50', 'border-l-4', 'border-gray-500', 'text-gray-800');
    }

    // Set content
    toast.innerHTML = `
      <div class="flex-shrink-0 mr-2">
        ${type === 'success' ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
          type === 'error' ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
          '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path></svg>'}
      </div>
      <div>
        <p class="font-bold">${title}</p>
        <p class="text-sm">${message}</p>
      </div>
      <button class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 text-gray-500 hover:text-gray-700 focus:outline-none">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
      </button>
    `;

    // Add click handler to close button
    toast.querySelector('button').addEventListener('click', () => {
      toast.remove();
    });

    // Add to container
    toastContainer.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      toast.remove();
    }, 5000);
  }

  // Confirmation dialog
  function initConfirmDialog(title, message, onConfirm) {
    const dialog = document.getElementById('confirmation-dialog');
    if (!dialog) return;

    // Set dialog content
    document.getElementById('confirmation-title').textContent = title;
    document.getElementById('confirmation-message').textContent = message;

    // Set up the confirm action
    const confirmBtn = document.getElementById('confirm-action');
    const cancelBtn = document.getElementById('cancel-action');

    // Remove existing listeners
    const newConfirmBtn = confirmBtn.cloneNode(true);
    const newCancelBtn = cancelBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

    // Add new listeners
    newConfirmBtn.addEventListener('click', () => {
      dialog.classList.add('hidden');
      if (typeof onConfirm === 'function') {
        onConfirm();
      }
    });

    newCancelBtn.addEventListener('click', () => {
      dialog.classList.add('hidden');
    });

    // Show dialog
    dialog.classList.remove('hidden');
  }
</script>

<!-- Ensure chosen library is loaded -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js"></script>

<!-- Custom CSS for chosen dropdown -->
<style>
  /* Fix chosen dropdown styling */
  .chosen-container {
    width: 100% !important;
    min-width: 300px;
  }

  .chosen-container-single .chosen-single {
    height: 38px;
    line-height: 36px;
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    padding: 0 12px;
  }

  .chosen-container-single .chosen-single div b {
    margin-top: 7px;
  }

  .chosen-container-active.chosen-with-drop .chosen-single {
    border-color: #4a5568;
    box-shadow: 0 0 0 2px rgba(74, 85, 104, 0.2);
  }

  .chosen-container .chosen-results li.highlighted {
    background: #4a5568;
  }

  .chosen-container-single .chosen-search input[type="text"] {
    border-color: #d1d5db;
  }

  .chosen-container .chosen-drop {
    border-color: #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
</style>

<!-- Script to check if chosen is loaded -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Check if jQuery is loaded
    if (typeof jQuery === 'undefined') {
      console.error('jQuery is not loaded!');
      return;
    }

    // Check if chosen is loaded
    if (typeof jQuery.fn.chosen === 'undefined') {
      console.error('Chosen plugin is not loaded! Loading it now...');

      // Try to load chosen dynamically
      var chosenScript = document.createElement('script');
      chosenScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js';
      chosenScript.onload = function() {
        console.log('Chosen plugin loaded successfully!');
        initializeChosen();
      };
      chosenScript.onerror = function() {
        console.error('Failed to load chosen plugin!');
      };
      document.head.appendChild(chosenScript);
    } else {
      console.log('Chosen plugin is already loaded!');
      initializeChosen();
    }

    function initializeChosen() {
      try {
        $('#teacher-filter').chosen({
          width: '100%',
          search_contains: true,
          allow_single_deselect: true,
          no_results_text: 'No teachers found matching'
        });
        console.log('Chosen initialized successfully!');
      } catch (e) {
        console.error('Error initializing chosen:', e);
      }
    }
  });
</script>

<!-- External script for timetable functionality -->
<script>
  // Make sure we have a showToast function available globally for the external script
  window.showToast = showToast;
  window.initConfirmDialog = initConfirmDialog;

  // Make sure timetable script loads after this script
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded in teacher-timetable.ejs');
    // Ensure jQuery is available
    if (typeof jQuery === 'undefined') {
      console.error('jQuery is not available. Loading timetable functionality may fail.');
    } else {
      console.log('jQuery is available in teacher-timetable.ejs');

      // Check if chosen is available
      if (typeof jQuery.fn.chosen === 'undefined') {
        console.error('Chosen plugin is not available');
      } else {
        console.log('Chosen plugin is available');
      }
    }
  });
</script>

<!-- Initialize chosen and set up event handlers before loading timetable.js -->
<script>
  // Initialize the teacher filter with chosen when the document is ready
  $(document).ready(function() {
    console.log('Setting up teacher filter change event in teacher-timetable.ejs');

    // Set up the change event for the teacher filter
    $('#teacher-filter').on('change', function() {
      console.log('Teacher filter changed to:', this.value);

      // Make sure currentWeekStart is defined
      if (typeof currentWeekStart !== 'undefined') {
        // Reload timetable data with the selected teacher
        const weekStart = currentWeekStart;
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);

        // Fetch data for the selected teacher
        if (typeof fetchTimetableData === 'function') {
          fetchTimetableData(weekStart, weekEnd);
        } else {
          console.error('fetchTimetableData function is not defined');
        }
      } else {
        console.error('currentWeekStart is not defined');
      }
    });

    console.log('Teacher filter change event handler set up successfully');
  });
</script>

<!-- Now load the timetable.js script -->
<script src="/js/teacher/timetable.js"></script>

<!-- Override fetchTimetableData to use the teacher-timetable API endpoint -->
<script>
  // Wait for timetable.js to load and define fetchTimetableData
  $(document).ready(function() {
    // Make sure fetchTimetableData is defined
    if (typeof window.fetchTimetableData === 'function') {
      console.log('Original fetchTimetableData function found, overriding it');

      // Store the original fetchTimetableData function
      const originalFetchTimetableData = window.fetchTimetableData;

      // Override the fetchTimetableData function to use the teacher-timetable API endpoint
      window.fetchTimetableData = async function(weekStart, weekEnd) {
        try {
          console.log('Custom fetchTimetableData called with:', { weekStart, weekEnd });

          // Format dates for API request
          const startFormatted = weekStart.toISOString().split('T')[0];
          const endFormatted = weekEnd.toISOString().split('T')[0];

          console.log('Formatted dates:', { startFormatted, endFormatted });

          // Show loading state
          const timetableBody = document.querySelector('#timetable-content');
          if (timetableBody) {
            console.log('Setting loading state in timetable body');
            timetableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">Loading timetable data...</td></tr>';
          } else {
            console.error('Could not find timetable-content element');
          }

          // Get selected teacher ID if we have a teacher filter
          let selectedTeacherId = null;
          try {
            const teacherFilter = document.getElementById('teacher-filter');
            if (teacherFilter && teacherFilter.value) {
              selectedTeacherId = teacherFilter.value;
              console.log('Selected teacher ID:', selectedTeacherId);
            }
          } catch (e) {
            console.log('Error getting teacher filter value:', e);
          }

          // Build API URL with query parameters
          let apiUrl = `/api/teacher/teacher-timetable?start_date=${startFormatted}&end_date=${endFormatted}`;
          if (selectedTeacherId) {
            apiUrl += `&teacher_id=${selectedTeacherId}`;
          }

          console.log('Fetching teacher timetable data from API:', apiUrl);

          // Fetch data from API
          const response = await fetch(apiUrl);
          if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
          }

          const data = await response.json();
          console.log('API response:', data);

          if (!data.success) {
            throw new Error(data.message || 'Failed to fetch timetable data');
          }

          // If we're in admin mode and this is the first load, populate the teacher filter
          if (data.teachers && data.teachers.length > 0) {
            console.log('Populating teacher filter with', data.teachers.length, 'teachers');

            try {
              // Make sure populateTeacherFilter is defined
              if (typeof populateTeacherFilter === 'function') {
                populateTeacherFilter(data.teachers, data.currentTeacherId);

                // Refresh chosen to show the updated options
                setTimeout(() => {
                  try {
                    const $teacherFilter = $('#teacher-filter');
                    if ($teacherFilter.length > 0) {
                      $teacherFilter.trigger('chosen:updated');
                      console.log('Triggered chosen:updated event');
                    }
                  } catch (e) {
                    console.error('Error triggering chosen:updated:', e);
                  }
                }, 100);
              } else {
                console.error('populateTeacherFilter function is not defined');
              }
            } catch (e) {
              console.error('Error populating teacher filter:', e);
            }
          }

          // Store full data globally for filtering
          currentTimetableData = { lectures: data.lectures || [] };

          // Render the timetable with the data from the API
          if (typeof renderTimetable === 'function') {
            renderTimetable(currentTimetableData);
          } else {
            console.error('renderTimetable function is not defined');
          }

        } catch (error) {
          console.error('Error fetching timetable data:', error);

          // Show error message
          const timetableBody = document.querySelector('#timetable-content');
          if (timetableBody) {
            timetableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-red-600">Failed to load timetable. Please try again.</td></tr>';
          }

          if (typeof showToast === 'function') {
            showToast('error', 'Error', 'Failed to load timetable data. Please try again.');
          }
        }
      };

      console.log('fetchTimetableData function successfully overridden');
    } else {
      console.error('fetchTimetableData function not found in window object');
    }
  });
</script>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <h2 id="confirmation-title" class="text-xl font-bold text-gray-800 mb-4">Confirm Action</h2>
    <p id="confirmation-message" class="text-gray-600 mb-6">Are you sure you want to proceed?</p>
    <div class="flex justify-end space-x-4">
      <button id="cancel-action" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
      <button id="confirm-action" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">Confirm</button>
    </div>
  </div>
</div>


