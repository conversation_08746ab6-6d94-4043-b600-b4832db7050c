<!-- Teacher Timetable View -->
<% if (typeof isAdmin !== 'undefined' && isAdmin && typeof adminMessage !== 'undefined') { %>
  <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-yellow-700">
          <%= adminMessage %>
        </p>
      </div>
    </div>
  </div>
<% } %>

<!-- Timetable Settings Panel -->
<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Timetable Settings</h2>
    <button id="toggle-settings" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm">
      Show Settings
    </button>
  </div>

  <div id="settings-panel" class="hidden p-6 border-b border-gray-200">
    <div class="grid md:grid-cols-2 gap-6">
      <!-- Timetable Timing Settings -->
      <div>
        <h3 class="text-lg font-medium text-gray-800 mb-4">Time Slots Configuration</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Effective From</label>
            <input type="date" id="effective-date" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Number of Periods</label>
            <select id="period-count" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
              <option value="6">6 Periods</option>
              <option value="7">7 Periods</option>
              <option value="8" selected>8 Periods</option>
              <option value="9">9 Periods</option>
              <option value="10">10 Periods</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Lecture Duration (minutes)</label>
            <select id="lecture-duration" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
              <option value="35">35 minutes</option>
              <option value="40" selected>40 minutes</option>
              <option value="45">45 minutes</option>
              <option value="50">50 minutes</option>
              <option value="60">60 minutes</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Break Duration (minutes)</label>
            <select id="break-duration" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
              <option value="5">5 minutes</option>
              <option value="10">10 minutes</option>
              <option value="15">15 minutes</option>
              <option value="20" selected>20 minutes</option>
              <option value="25">25 minutes</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">School Start Time</label>
            <input type="time" id="start-time" class="w-full border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50" value="08:00">
          </div>

          <button id="generate-slots" class="mt-2 px-4 py-2 bg-teacher-primary text-white rounded hover:bg-teacher-secondary transition">
            Generate Time Slots
          </button>
        </div>
      </div>

      <!-- Generated Time Slots Display -->
      <div>
        <h3 class="text-lg font-medium text-gray-800 mb-4">Time Slots Preview</h3>
        <div id="time-slots-preview" class="bg-gray-50 p-4 rounded-md min-h-[200px] overflow-y-auto max-h-[400px]">
          <p class="text-gray-500 italic">Adjust settings and click "Generate Time Slots" to preview the schedule.</p>
        </div>

        <div class="mt-4">
          <button id="save-time-slots" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition">
            Save & Apply
          </button>

          <button id="reset-time-slots" class="ml-2 px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition">
            Reset to Default
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
  <div class="bg-teacher-primary text-white p-4 flex justify-between items-center">
    <h2 class="text-xl font-semibold">Weekly Timetable</h2>
    <div class="flex space-x-2">
      <button id="prev-week" class="p-1 rounded hover:bg-teacher-secondary transition">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <button id="current-week" class="px-3 py-1 bg-teacher-secondary rounded hover:bg-gray-600 transition text-sm">
        Current Week
      </button>
      <button id="next-week" class="p-1 rounded hover:bg-teacher-secondary transition">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Week Display -->
  <div class="p-4 bg-gray-50 border-b border-gray-200">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-medium text-gray-800" id="week-display">Week of <span id="week-start-date">May 1, 2023</span> - <span id="week-end-date">May 7, 2023</span></h3>
      <div class="flex items-center space-x-2">
        <span class="text-sm text-gray-600">Filter by:</span>
        <select id="class-filter" class="text-sm border-gray-300 rounded-md shadow-sm focus:border-teacher-primary focus:ring focus:ring-teacher-primary focus:ring-opacity-50">
          <option value="all">All Classes</option>
          <option value="11">Class 11</option>
          <option value="12">Class 12</option>
        </select>
        <span id="filter-display" class="ml-2 text-sm font-medium bg-teacher-primary text-white px-2 py-1 rounded">All Classes</span>
      </div>
    </div>
    <div class="mt-2 text-sm text-gray-600">
      Currently using schedule effective from: <span id="current-schedule-date" class="font-medium">April 1, 2023</span>
    </div>
  </div>

  <!-- Timetable Grid -->
  <div class="p-6">
    <div class="overflow-auto" style="max-height: 600px;">
      <table class="min-w-full border border-gray-200">
        <thead class="sticky top-0 bg-white">
          <tr id="timetable-content-header">
            <th class="w-20 bg-gray-50 border border-gray-200 px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Monday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Tuesday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Wednesday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Thursday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Friday</th>
            <th class="bg-gray-50 border border-gray-200 px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Saturday</th>
          </tr>
        </thead>
        <tbody id="timetable-content">
          <!-- Dynamic timetable content will be inserted here -->
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Legend -->
<div class="mt-4 mb-4 p-4 bg-white rounded-lg shadow-sm">
  <h3 class="text-lg font-medium mb-3">Legend</h3>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <!-- Status Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Status</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-green-100 mr-2"></span>
          <span class="text-sm">Delivered</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-gray-100 mr-2"></span>
          <span class="text-sm">Pending</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-yellow-100 mr-2"></span>
          <span class="text-sm">Rescheduled</span>
        </div>
      </div>
    </div>

    <!-- Streams Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Streams</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-blue-400 mr-2"></span>
          <span class="text-sm">Non-Medical</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-purple-400 mr-2"></span>
          <span class="text-sm">Medical</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 border-l-4 border-teal-400 mr-2"></span>
          <span class="text-sm">Commerce</span>
        </div>
      </div>
    </div>

    <!-- Subjects Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Subjects</h4>
      <div class="grid grid-cols-2 gap-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-blue-50 mr-2"></span>
          <span class="text-sm">Physics</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-green-50 mr-2"></span>
          <span class="text-sm">Chemistry</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-yellow-50 mr-2"></span>
          <span class="text-sm">Mathematics</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-purple-50 mr-2"></span>
          <span class="text-sm">Biology</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-red-50 mr-2"></span>
          <span class="text-sm">Computer Sci</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-indigo-50 mr-2"></span>
          <span class="text-sm">English</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-orange-50 mr-2"></span>
          <span class="text-sm">Punjabi</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-teal-50 mr-2"></span>
          <span class="text-sm">Business St.</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-cyan-50 mr-2"></span>
          <span class="text-sm">Accountancy</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full bg-emerald-50 mr-2"></span>
          <span class="text-sm">Economics</span>
        </div>
      </div>
    </div>

    <!-- Highlights Legend -->
    <div>
      <h4 class="font-medium text-gray-700 mb-2">Highlights</h4>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-2 ring-blue-500 ring-offset-2 mr-2"></span>
          <span class="text-sm">Current Lecture</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-1 ring-indigo-300 mr-2"></span>
          <span class="text-sm">Your Lecture</span>
        </div>
        <div class="flex items-center">
          <span class="inline-block w-3 h-3 rounded-full ring-2 ring-red-500 ring-offset-2 bg-red-100 mr-2"></span>
          <span class="text-sm">Your Current Lecture</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Lecture Details Modal -->
<div id="lecture-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold text-gray-800">Lecture Details</h2>
      <button id="close-lecture-modal" class="text-gray-400 hover:text-gray-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div id="lecture-details-content" class="mb-6">
      <!-- Content will be dynamically populated -->
    </div>
    <div class="flex justify-end space-x-3">
      <button id="close-modal-btn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition">Close</button>
      <button id="reschedule-btn" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition">Reschedule</button>
      <button id="mark-delivered-btn" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">Mark as Delivered</button>
    </div>
  </div>
</div>

<!-- Include the toast notification script if not already included -->
<script>
  // Toast notification function
  function showToast(type, title, message) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast flex items-start p-4 mb-4 w-full max-w-xs rounded shadow-lg';

    // Set color based on type
    if (type === 'success') {
      toast.classList.add('bg-green-50', 'border-l-4', 'border-green-500', 'text-green-800');
    } else if (type === 'error') {
      toast.classList.add('bg-red-50', 'border-l-4', 'border-red-500', 'text-red-800');
    } else if (type === 'info') {
      toast.classList.add('bg-blue-50', 'border-l-4', 'border-blue-500', 'text-blue-800');
    } else {
      toast.classList.add('bg-gray-50', 'border-l-4', 'border-gray-500', 'text-gray-800');
    }

    // Set content
    toast.innerHTML = `
      <div class="flex-shrink-0 mr-2">
        ${type === 'success' ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
          type === 'error' ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
          '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path></svg>'}
      </div>
      <div>
        <p class="font-bold">${title}</p>
        <p class="text-sm">${message}</p>
      </div>
      <button class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 text-gray-500 hover:text-gray-700 focus:outline-none">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
      </button>
    `;

    // Add click handler to close button
    toast.querySelector('button').addEventListener('click', () => {
      toast.remove();
    });

    // Add to container
    toastContainer.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      toast.remove();
    }, 5000);
  }

  // Confirmation dialog
  function initConfirmDialog(title, message, onConfirm) {
    const dialog = document.getElementById('confirmation-dialog');
    if (!dialog) return;

    // Set dialog content
    document.getElementById('confirmation-title').textContent = title;
    document.getElementById('confirmation-message').textContent = message;

    // Set up the confirm action
    const confirmBtn = document.getElementById('confirm-action');
    const cancelBtn = document.getElementById('cancel-action');

    // Remove existing listeners
    const newConfirmBtn = confirmBtn.cloneNode(true);
    const newCancelBtn = cancelBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

    // Add new listeners
    newConfirmBtn.addEventListener('click', () => {
      dialog.classList.add('hidden');
      if (typeof onConfirm === 'function') {
        onConfirm();
      }
    });

    newCancelBtn.addEventListener('click', () => {
      dialog.classList.add('hidden');
    });

    // Show dialog
    dialog.classList.remove('hidden');
  }
</script>

<!-- External script for timetable functionality -->
<script>
  // Make sure we have a showToast function available globally for the external script
  window.showToast = showToast;
  window.initConfirmDialog = initConfirmDialog;

  // Make sure timetable script loads after this script
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded in timetable.ejs');
    // Ensure jQuery is available
    if (typeof jQuery === 'undefined') {
      console.error('jQuery is not available. Loading timetable functionality may fail.');
    } else {
      console.log('jQuery is available in timetable.ejs');
    }
  });
</script>

<!-- Ensure the timetable.js script is loaded at the end -->
<script src="/js/teacher/timetable.js"></script>

<!-- Confirmation Dialog -->
<div id="confirmation-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
    <h2 id="confirmation-title" class="text-xl font-bold text-gray-800 mb-4">Confirm Action</h2>
    <p id="confirmation-message" class="text-gray-600 mb-6">Are you sure you want to proceed?</p>
    <div class="flex justify-end space-x-4">
      <button id="cancel-action" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">Cancel</button>
      <button id="confirm-action" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition">Confirm</button>
    </div>
  </div>
</div>
