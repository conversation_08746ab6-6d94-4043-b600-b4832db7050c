<!-- Update Syllabus Page -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
  <div class="bg-teacher-primary text-white p-4">
    <h2 class="text-xl font-semibold">Update Syllabus Progress</h2>
  </div>
  <div class="p-6">
    <% if (subjects && subjects.length > 0) { %>
      <form id="updateSyllabusForm" class="space-y-6">
        <div class="mb-4">
          <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
          <select id="subject" name="subject" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm rounded-md" required>
            <option value="">Select a subject</option>
            <% subjects.forEach(subject => { %>
              <option value="<%= subject.subject_name %>"><%= subject.subject_name %></option>
            <% }); %>
          </select>
        </div>

        <div class="mb-4">
          <label for="class_name" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
          <select id="class_name" name="class_name" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm rounded-md" required>
            <option value="">Select a class</option>
            <option value="11-Science">11 Science</option>
            <option value="11-Commerce">11 Commerce</option>
            <option value="12-Science">12 Science</option>
            <option value="12-Commerce">12 Commerce</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="topic" class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
          <input type="text" id="topic" name="topic" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm" required>
        </div>

        <div class="mb-4">
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm rounded-md" required>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="completion_date" class="block text-sm font-medium text-gray-700 mb-1">Completion Date</label>
          <input type="date" id="completion_date" name="completion_date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm">
        </div>

        <div class="mb-4">
          <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teacher-primary focus:border-teacher-primary sm:text-sm"></textarea>
        </div>

        <div class="flex justify-end">
          <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teacher-primary hover:bg-teacher-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teacher-primary">
            Update Syllabus
          </button>
        </div>
      </form>
    <% } else { %>
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects found</h3>
        <p class="mt-1 text-sm text-gray-500">You need to have at least one lecture scheduled to update syllabus.</p>
      </div>
    <% } %>
  </div>
</div>

<%- contentFor('script') %>
<script>
  $(document).ready(function() {
    // Show/hide completion date based on status
    $('#status').on('change', function() {
      if ($(this).val() === 'completed') {
        $('#completion_date').prop('required', true);
        $('#completion_date').closest('.mb-4').show();
      } else {
        $('#completion_date').prop('required', false);
        $('#completion_date').closest('.mb-4').hide();
      }
    }).trigger('change');

    // Handle form submission
    $('#updateSyllabusForm').on('submit', function(e) {
      e.preventDefault();

      const formData = {
        subject_name: $('#subject').val(),
        class_name: $('#class_name').val(),
        topic: $('#topic').val(),
        status: $('#status').val(),
        completion_date: $('#status').val() === 'completed' ? $('#completion_date').val() : null,
        notes: $('#notes').val()
      };

      // Use the apiCall function from common.js
      apiCall('/api/teacher/syllabus/progress', 'POST', formData)
        .then(response => {
          if (response.success) {
            showToast('success', 'Success', 'Syllabus updated successfully');
            // Reset form
            $('#updateSyllabusForm')[0].reset();
          } else {
            showToast('error', 'Error', response.message || 'Failed to update syllabus');
          }
        })
        .catch(error => {
          console.error('Error updating syllabus:', error);
          showToast('error', 'Error', 'Failed to update syllabus');
        });
    });
  });
</script>
