<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Teacher API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced Teacher API Test</h1>
        
        <div class="section">
            <h2>API Test</h2>
            <button id="testApi">Test API</button>
            <div id="apiResult"></div>
        </div>

        <div class="section">
            <h2>Education Timeline</h2>
            <div id="educationResult"></div>
        </div>

        <div class="section">
            <h2>Experience Timeline</h2>
            <div id="experienceResult"></div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('#testApi').click(function() {
                console.log('Testing API...');
                $('#apiResult').html('<p>Loading...</p>');
                
                fetch('/principal/api/teacher/profile-enhanced?teacher_id=103', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('API Response:', result);
                    
                    if (result.success) {
                        const teacher = result.teacher;
                        
                        $('#apiResult').html(`
                            <div class="success">
                                <h3>API Success!</h3>
                                <p><strong>Teacher:</strong> ${teacher.fullName || teacher.name}</p>
                                <p><strong>Gender:</strong> ${teacher.gender || 'Not specified'}</p>
                                <p><strong>Staff ID:</strong> ${teacher.staff_id}</p>
                                <p><strong>Education Records:</strong> ${teacher.educationTimeline ? teacher.educationTimeline.length : 0}</p>
                                <p><strong>Experience Records:</strong> ${teacher.experienceTimeline ? teacher.experienceTimeline.length : 0}</p>
                            </div>
                        `);
                        
                        // Display education timeline
                        if (teacher.educationTimeline && teacher.educationTimeline.length > 0) {
                            let educationHtml = '<h3>Education Timeline:</h3><ul>';
                            teacher.educationTimeline.forEach((item, index) => {
                                educationHtml += `<li>${item.title} (${item.year}) - ${item.percentage}%</li>`;
                            });
                            educationHtml += '</ul>';
                            $('#educationResult').html(educationHtml);
                        } else {
                            $('#educationResult').html('<p class="error">No education timeline data</p>');
                        }
                        
                        // Display experience timeline
                        if (teacher.experienceTimeline && teacher.experienceTimeline.length > 0) {
                            let experienceHtml = '<h3>Experience Timeline:</h3><ul>';
                            teacher.experienceTimeline.forEach((item, index) => {
                                experienceHtml += `<li>${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}</li>`;
                            });
                            experienceHtml += '</ul>';
                            $('#experienceResult').html(experienceHtml);
                        } else {
                            $('#experienceResult').html('<p class="error">No experience timeline data</p>');
                        }
                        
                        // Show raw data
                        $('#apiResult').append(`
                            <h4>Raw API Response:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        `);
                        
                    } else {
                        $('#apiResult').html(`<div class="error">API Error: ${result.message}</div>`);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    $('#apiResult').html(`<div class="error">Error: ${error.message}</div>`);
                });
            });
        });
    </script>
</body>
</html>
