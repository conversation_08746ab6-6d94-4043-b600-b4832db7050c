<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/styles.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-indigo-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="/" class="text-xl font-bold">Meritorious EP</a>
            <div class="flex items-center space-x-4">
                <a href="/tests" class="hover:underline font-medium">Tests</a>
                <% if (user && user.role === 'admin') { %>
                    <a href="/admin/dashboard" class="hover:underline font-medium">Admin</a>
                <% } %>
                <a href="/profile" class="hover:underline font-medium">Profile</a>
                <a href="/logout" class="bg-indigo-700 hover:bg-indigo-800 px-3 py-1 rounded">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800"><%= title %></h1>
            <a href="/tests" class="text-indigo-600 hover:text-indigo-800">
                &larr; Back to Tests
            </a>
        </div>

        <% if (error) { %>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><%= error %></span>
            </div>
        <% } %>

        <div class="bg-white rounded-lg shadow-md p-6">
            <form id="testForm" class="space-y-6">
                <input type="hidden" id="testId" value="<%= testId %>">

                <div>
                    <label for="exam_name" class="block text-sm font-medium text-gray-700">Test Name</label>
                    <input type="text" id="exam_name" name="exam_name" required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700">Duration (minutes)</label>
                    <input type="number" id="duration" name="duration" min="1" required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Instructions Section - Always Visible -->
                <div class="mb-6 p-4 bg-white rounded-lg shadow-sm">
                    <div class="space-y-4">
                        <div>
                            <label for="instructions" class="block text-sm font-medium text-gray-700">Test Instructions (180-250 words)</label>
                            <div class="mt-1 relative">
                                <textarea
                                    id="instructions"
                                    name="instructions"
                                    rows="8"
                                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    oninput="updateInstructionsWordCount()"
                                ></textarea>
                                <div id="wordCount" class="mt-2 text-sm text-gray-500">0 words</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Publish</label>
                    <div class="mt-2 space-y-2">
                        <div class="flex items-center">
                            <input type="radio" id="publish_now" name="publish_option" value="now" checked
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                            <label for="publish_now" class="ml-2 block text-sm text-gray-700">
                                Publish Now
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="publish_later" name="publish_option" value="later"
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                            <label for="publish_later" class="ml-2 block text-sm text-gray-700">
                                Publish Later
                            </label>
                        </div>
                    </div>
                </div>

                <div id="publish_date_container" class="hidden">
                    <label for="publish_date" class="block text-sm font-medium text-gray-700">Publish Date</label>
                    <input type="datetime-local" id="publish_date" name="publish_date"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div id="sectionTabs" class="flex space-x-4 mb-4"></div>
                <div id="sectionContents"></div>

                <div class="pt-5">
                    <div class="flex justify-end">
                        <button type="button" id="cancel_button"
                            class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </button>
                        <button type="submit" id="save_button"
                            class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Save Test
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <template id="tabTemplate">
        <div class="section-tab flex items-center justify-center w-32 py-2 rounded-md cursor-pointer">
            <span class="section-number text-lg font-medium"></span>
            <button class="remove-section ml-2 text-red-600 hover:text-red-800">
                Remove
            </button>
        </div>
    </template>

    <template id="sectionContentTemplate">
        <div class="section-content">
            <div class="questions-container space-y-3"></div>
            <button class="add-question-btn hidden mt-2 text-sm text-indigo-600 hover:text-indigo-800">
                + Add Question
            </button>
        </div>
    </template>

    <template id="questionTemplate">
        <div class="question-container bg-white p-3 rounded-md border border-gray-200">
            <div class="flex justify-between items-center mb-2">
                <h5 class="text-sm font-medium">Question 1</h5>
                <button class="remove-question text-red-600 hover:text-red-800 text-sm">
                    Remove
                </button>
            </div>
            <div class="mb-2">
                <label class="block text-sm font-medium text-gray-700">Question Type</label>
                <select name="sections[][questions][0][type]"
                    class="question-type mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="mcq">Multiple Choice</option>
                    <option value="true_false">True/False</option>
                    <option value="fill_in">Fill in the Blank</option>
                </select>
            </div>
            <div class="mb-2">
                <label class="block text-sm font-medium text-gray-700">Question Text</label>
                <textarea name="sections[][questions][0][question_text]" required rows="2"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
            </div>
            <div class="mb-2">
                <label class="block text-sm font-medium text-gray-700">Solution/Explanation</label>
                <input type="text" name="sections[][questions][0][solution_text]" value=""
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div class="answer-container">
                <!-- Answer options will be added here based on question type -->
            </div>
        </div>
    </template>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testId = document.getElementById('testId').value;
            const publishNow = document.getElementById('publish_now');
            const publishLater = document.getElementById('publish_later');
            const publishDateContainer = document.getElementById('publish_date_container');
            const testForm = document.getElementById('testForm');
            const cancelButton = document.getElementById('cancel_button');

            // Toggle publish date field
            publishNow.addEventListener('change', function() {
                publishDateContainer.classList.add('hidden');
            });

            publishLater.addEventListener('change', function() {
                publishDateContainer.classList.remove('hidden');
            });

            // Initialize section management
            const sectionTabs = document.getElementById('sectionTabs');
            const sectionContents = document.getElementById('sectionContents');
            const saveDraftBtn = document.getElementById('saveDraftBtn');

            let currentSectionIndex = 0;
            const professionalColors = ['bg-blue-100', 'bg-green-100', 'bg-purple-100', 'bg-indigo-100'];

            // Add section with proper initialization
            function addSection() {
                const sectionIndex = currentSectionIndex++;
                const colorClass = professionalColors[sectionIndex % professionalColors.length];

                // Add tab
                const tabTemplate = document.getElementById('tabTemplate');
                const tab = tabTemplate.content.cloneNode(true).querySelector('.section-tab');
                tab.querySelector('.section-number').textContent = `Section ${sectionIndex + 1}`;
                tab.classList.add(colorClass, 'text-gray-800', 'hover:bg-opacity-75');
                tab.dataset.section = sectionIndex;
                sectionTabs.appendChild(tab);

                // Add content
                const sectionContentTemplate = document.getElementById('sectionContentTemplate');
                const content = sectionContentTemplate.content.cloneNode(true).querySelector('.section-content');
                content.dataset.section = sectionIndex;
                sectionContents.appendChild(content);

                // Update question count
                const updateQuestionCount = () => {
                    const questionCount = content.querySelectorAll('.question-container').length;
                    tab.querySelector('.section-number').textContent = `${questionCount} Questions - Section ${sectionIndex + 1}`;
                };

                // Add question button
                const addQuestionBtn = content.querySelector('.add-question-btn');
                addQuestionBtn.classList.remove('hidden');
                addQuestionBtn.addEventListener('click', () => {
                    addQuestion(content);
                    updateQuestionCount();
                });

                // Add first question automatically
                addQuestion(content);
                updateQuestionCount();

                // Remove section functionality
                tab.querySelector('.remove-section').addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (sectionTabs.children.length > 1) {
                        tab.remove();
                        content.remove();
                        // Ensure at least one section remains
                        if (sectionTabs.children.length === 1) {
                            sectionTabs.querySelector('.section-tab').click();
                        }
                    } else {
                        alert('Test must have at least one section');
                    }
                });
            };

            // Add question functionality
            function addQuestion(sectionContent) {
                const questionTemplate = document.getElementById('questionTemplate');
                const question = questionTemplate.content.cloneNode(true).querySelector('.question-container');
                const questionsContainer = sectionContent.querySelector('.questions-container');

                // Update form control names
                question.querySelectorAll('[name]').forEach(element => {
                    const sectionIndex = sectionContent.dataset.section;
                    element.name = element.name
                        .replace('sections[]', `sections[${sectionIndex}]`);
                });

                // Remove question functionality
                question.querySelector('.remove-question').addEventListener('click', function() {
                    if (questionsContainer.children.length > 1) {
                        question.remove();
                        updateQuestionCount(sectionContent);
                    } else {
                        alert('Section must have at least one question');
                    }
                });

                questionsContainer.appendChild(question);
            };

            // Add first section automatically
            addSection();

            // Add '+' button for new sections
            const addSectionBtn = document.createElement('button');
            addSectionBtn.type = 'button';
            addSectionBtn.className = 'add-section-btn mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500';
            addSectionBtn.textContent = '+ Add Section';
            addSectionBtn.addEventListener('click', addSection);
            sectionTabs.appendChild(addSectionBtn);

            // Handle form submission
            testForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = {
                    exam_name: document.getElementById('exam_name').value,
                    duration: parseInt(document.getElementById('duration').value),
                    instructions: document.getElementById('instructions').value,
                    publish_option: document.querySelector('input[name="publish_option"]:checked').value,
                    publish_date: document.getElementById('publish_date').value,
                    sections: []
                };

                // Collect sections data
                document.querySelectorAll('.section-content').forEach((section, sectionIndex) => {
                    const sectionData = {
                        name: `Section ${sectionIndex + 1}`,
                        questions: []
                    };

                    // Collect questions data
                    section.querySelectorAll('.question-container').forEach((question, questionIndex) => {
                        const questionType = question.querySelector('select').value;
                        const questionData = {
                            type: questionType,
                            question_text: question.querySelector('textarea').value,
                            solution_text: question.querySelector('input[name$="[solution_text]"]').value
                        };

                        if (questionType === 'mcq') {
                            questionData.options = [];
                            questionData.correct_option = parseInt(question.querySelector('input[name$="[correct_option]"]:checked').value);

                            question.querySelectorAll('input[name$="[option_text]"]').forEach(option => {
                                questionData.options.push(option.value);
                            });
                        } else if (questionType === 'true_false') {
                            questionData.correct_option = question.querySelector('input[name$="[tf_answer]"]:checked').value;
                        } else if (questionType === 'fill_in') {
                            questionData.correct_option = question.querySelector('input[name$="[fill_answer]"]').value;
                        }

                        sectionData.questions.push(questionData);
                    });

                    formData.sections.push(sectionData);
                });

                // Send data to server
                const url = testId ? `/tests/update/${testId}` : '/tests/create';
                const method = testId ? 'PUT' : 'POST';

                fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        window.location.href = '/tests';
                    } else {
                        alert(data.error || 'An error occurred');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the test');
                });
            });

            // Cancel button
            cancelButton.addEventListener('click', function() {
                window.location.href = '/tests';
            });

            // Load test data if editing
            if (testId) {
                fetch(`/tests/api/${testId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('exam_name').value = data.exam_name;
                    document.getElementById('duration').value = data.duration;
                    document.getElementById('instructions').value = data.instructions || '';

                    if (data.publish_date) {
                        publishLater.checked = true;
                        publishDateContainer.classList.remove('hidden');
                        document.getElementById('publish_date').value = new Date(data.publish_date).toISOString().slice(0, 16);
                    }

                    // Clear default section
                    sectionContents.innerHTML = '';

                    // Add sections and questions
                    data.sections.forEach((section, sectionIndex) => {
                        const sectionElement = addSection(section.name);

                        // Clear default question
                        sectionElement.querySelector('.questions-container').innerHTML = '';

                        // Add questions
                        section.questions.forEach((question, questionIndex) => {
                            addQuestion(sectionElement, question);
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading test data:', error);
                    alert('Failed to load test data');
                });
            }
        });
    </script>
    <script>
        function updateInstructionsWordCount() {
            const text = document.getElementById('instructions').value;
            const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
            const countElement = document.getElementById('wordCount');

            countElement.textContent = `${wordCount} words`;
            if (wordCount < 180) {
                countElement.className = 'mt-2 text-sm text-orange-500';
            } else if (wordCount > 250) {
                countElement.className = 'mt-2 text-sm text-red-500';
            } else {
                countElement.className = 'mt-2 text-sm text-green-500';
            }
        }

        // Load existing test data if editing
        if (testId) {
            fetch(`/tests/api/${testId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('instructions').value = data.instructions || '';
                    updateInstructionsWordCount();
                })
                .catch(error => {
                    console.error('Error loading test data:', error);
                });
        }

        // Form validation
        document.getElementById('testForm').addEventListener('submit', function(e) {
            const instructions = document.getElementById('instructions').value;
            const wordCount = instructions.trim().split(/\s+/).filter(word => word.length > 0).length;

            if (wordCount < 180 || wordCount > 250) {
                e.preventDefault();
                alert(`Instructions must be between 180-250 words. Current: ${wordCount} words.`);
                return false;
            }
        });
    </script>
</body>
</html>
