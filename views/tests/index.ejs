<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= __('app.name') %></title>
    <link rel="stylesheet" href="/styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="/css/session-timer.css">
    <script src="/js/session-timer.js"></script>
    <script src="/js/chat-icon.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Include the user navbar -->


    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800"><%= title %></h1>
        </div>

        <!-- Test History Section -->
        <% if (locals.attempts && attempts.length > 0) { %>
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('tests.recentAttempts') %></h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.examName') %></th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.dateTaken') %></th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.status') %></th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.scoreLabel') %></th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.durationLabel') %></th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.actions') %></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% attempts.forEach(attempt => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><%= attempt.exam_name %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><%= formatDateTime(attempt.attempt_date) %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <%= attempt.status === 'completed' ? 'bg-green-100 text-green-800' :
                                            attempt.status === 'failed' ? 'bg-red-100 text-red-800' :
                                            attempt.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                            'bg-gray-100 text-gray-800' %>">
                                            <%= attempt.status.replace('_', ' ').charAt(0).toUpperCase() + attempt.status.replace('_', ' ').slice(1) %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <%= attempt.total_score ? Math.round(attempt.total_score) + '%' : 'N/A' %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <% if (attempt.duration_formatted) { %>
                                                <%= attempt.duration_formatted %>
                                            <% } else if (attempt.duration_minutes) { %>
                                                <%= Math.floor(attempt.duration_minutes) %> min
                                            <% } else { %>
                                                N/A
                                            <% } %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <% if (attempt.status === 'in_progress') { %>
                                            <% if (attempt.continuation_count >= 2) { %>
                                                <a href="/tests/results/<%= attempt.attempt_id %>" class="text-indigo-600 hover:text-indigo-900"><%= __('tests.viewResults') %></a>
                                            <% } else { %>
                                                <a href="/tests/take/<%= attempt.exam_id %>/<%= attempt.attempt_id %>" class="text-blue-600 hover:text-blue-900 font-medium"><%= __('tests.continue') %></a>
                                            <% } %>
                                        <% } else { %>
                                            <a href="/tests/results/<%= attempt.attempt_id %>" class="text-indigo-600 hover:text-indigo-900"><%= __('tests.viewResults') %></a>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="/tests/results" class="text-indigo-600 hover:text-indigo-800"><%= __('tests.viewAllResults') %></a>
                </div>
            </div>
        <% } %>

        <!-- Available Tests Section -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('tests.availableTests') %></h2>
            <% if (tests && tests.length > 0) { %>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <% tests.forEach(test => { %>
                        <div class="bg-gray-50 rounded-lg shadow-sm overflow-hidden border border-gray-200">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2"><%= test.exam_name %></h3>
                                <% if (test.description) { %>
                                    <p class="text-gray-600 text-sm mb-3"><%= test.description.substring(0, 100) %><%=test.description.length > 100 ? '...' : '' %></p>
                                <% } %>

                                <div class="flex flex-col space-y-4">
                                    <!-- Test details section -->
                                    <div class="flex justify-between items-center">
                                        <div class="flex flex-col space-y-2">
                                            <div class="flex space-x-2">
                                                <span class="text-xs font-medium bg-gray-100 text-gray-800 px-2 py-1 rounded">
                                                    <%= test.duration %> <%= __('tests.minutes') %>
                                                </span>
                                                <span class="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                    <%= test.question_count %> <%= __('tests.questions') %>
                                                </span>
                                                <%
                                                const completedAttempts = test.completed_attempts || 0;
                                                const maxAttempts = test.max_attempts || 1;
                                                %>
                                                <span class="text-xs font-medium <%= completedAttempts >= maxAttempts ? 'bg-red-100 text-red-800' : 'bg-purple-100 text-purple-800' %> px-2 py-1 rounded">
                                                    <%= completedAttempts %>/<%= maxAttempts %> attempts used
                                                </span>
                                                <% if (test.is_resumable === 1) { %>
                                                <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded">
                                                    Resumable
                                                </span>
                                                <% } %>
                                            </div>
                                            <% if (test.is_assigned) { %>
                                                <div class="flex space-x-2">
                                                    <% if (test.assignment_type === 'user') { %>
                                                        <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded">
                                                            <% if (test.assigned_by_username) { %>
                                                                Assigned by <%= test.assigned_by_username %>
                                                            <% } else { %>
                                                                Assigned to you
                                                            <% } %>
                                                        </span>
                                                    <% } else if (test.assignment_type === 'group') { %>
                                                        <span class="text-xs font-medium bg-purple-100 text-purple-800 px-2 py-1 rounded">
                                                            <% if (test.assigned_by_username) { %>
                                                                Assigned to group: <%= test.group_name %> by <%= test.assigned_by_username %>
                                                            <% } else { %>
                                                                Assigned to group: <%= test.group_name %>
                                                            <% } %>
                                                        </span>
                                                    <% } %>
                                                    <% if (test.end_datetime) { %>
                                                        <span class="text-xs font-medium bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                                            Expires: <%= formatDateTime(test.end_datetime) %>
                                                        </span>
                                                    <% } %>
                                                </div>
                                            <% } %>
                                        </div>
                                    </div>

                                    <!-- Take Test button section - separated at the bottom -->
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <% if (test.in_progress_attempt_id && test.is_resumable) { %>
                                            <%
                                            // Calculate remaining time
                                            let remainingTimeDisplay = '';
                                            if (test.start_time && test.duration) {
                                                const startTime = new Date(test.start_time);
                                                const durationMs = test.duration * 60 * 1000;
                                                const endTime = new Date(startTime.getTime() + durationMs);
                                                const now = new Date();
                                                const remainingMs = endTime - now;

                                                if (remainingMs > 0) {
                                                    const hours = Math.floor(remainingMs / (1000 * 60 * 60));
                                                    const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));

                                                    if (hours > 0) {
                                                        remainingTimeDisplay = `${hours}h ${minutes}m left`;
                                                    } else {
                                                        remainingTimeDisplay = `${minutes}m left`;
                                                    }
                                                } else {
                                                    remainingTimeDisplay = 'Time expired';
                                                }
                                            }
                                            %>
                                            <a href="/tests/take/<%= test.exam_id %>/<%= test.in_progress_attempt_id %>"
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm flex items-center justify-center w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <%= __('tests.resumeTest') %> <% if (remainingTimeDisplay) { %>(<%= remainingTimeDisplay %>)<% } %>
                                            </a>
                                        <% } else { %>
                                            <%
                                            // Calculate remaining attempts
                                            const completedAttempts = test.completed_attempts || 0;
                                            const maxAttempts = test.max_attempts || 1;
                                            const remainingAttempts = maxAttempts - completedAttempts;
                                            %>

                                            <%
                                            // Only show Take Test button if there are remaining attempts
                                            // A test cannot be reassigned if the user has not used all attempts from the previous assignment
                                            if (remainingAttempts > 0) {
                                            %>
                                                <a href="/tests/take/<%= test.exam_id %>"
                                                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded text-sm flex items-center justify-center w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                                    </svg>
                                                    <%= __('tests.takeTest') %>
                                                    <% if (maxAttempts > 1) { %>
                                                        (<%= remainingAttempts %> <%= remainingAttempts === 1 ? 'attempt' : 'attempts' %> left)
                                                    <% } %>
                                                </a>
                                            <% } else { %>
                                                <button type="button"
                                                        onclick="openRequestAccessModal('<%= test.exam_id %>', '<%= test.exam_name %>')"
                                                        class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded text-sm flex items-center justify-center w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                                    </svg>
                                                    Request Access
                                                </button>
                                            <% } %>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% }) %>
                </div>
            <% } else { %>
                <div class="rounded-lg p-8 text-center">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4"><%= __('tests.noTestsAvailable') %></h3>
                    <p class="text-gray-600">
                        <%= __('tests.testsWillAppear') %>
                    </p>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Request Access Modal -->
    <div id="requestAccessModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Request Additional Attempts</h3>
                <button type="button" onclick="closeRequestAccessModal()" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <p class="mb-4 text-sm text-gray-600">You have used all your attempts for <span id="testNamePlaceholder" class="font-semibold"></span>. Send a request to the administrator for additional attempts.</p>

            <form id="requestAccessForm" class="space-y-4" action="/tests/request-access" method="POST">
                <input type="hidden" id="examIdInput" name="examId" value="">

                <div>
                    <label for="requestMessage" class="block text-sm font-medium text-gray-700 mb-1">Message (Optional)</label>
                    <textarea id="requestMessage" name="message" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Explain why you need additional attempts..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeRequestAccessModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Send Request
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function deleteTest(examId) {
            if (confirm('Are you sure you want to delete this test? This action cannot be undone.')) {
                fetch(`/tests/delete/${examId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        window.location.reload();
                    } else {
                        alert(data.error || 'An error occurred');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the test');
                });
            }
        }

        // Request Access Modal Functions
        function openRequestAccessModal(examId, examName) {
            document.getElementById('examIdInput').value = examId;
            document.getElementById('testNamePlaceholder').textContent = examName;
            document.getElementById('requestAccessModal').classList.remove('hidden');
        }

        function closeRequestAccessModal() {
            document.getElementById('requestAccessModal').classList.add('hidden');
            document.getElementById('requestMessage').value = '';
        }

        // This function is no longer needed as we're using a direct form submission
        // Keeping the function definition empty in case it's referenced elsewhere
        function submitAccessRequest() {
            document.getElementById('requestAccessForm').submit();
        }
    </script>
</body>
</html>