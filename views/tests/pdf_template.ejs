<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= exam.exam_name %> - Offline Test</title>

    <%
    // Helper function to get absolute image path
    function getImagePath(relativePath) {
        if (!relativePath) return '';
        const basePath = process.cwd();
        const publicPath = '/public';
        const normalizedPath = relativePath.startsWith('/') ? relativePath : '/' + relativePath;
        return `file://${basePath}${publicPath}${normalizedPath}`;
    }

    // Helper function to check if a path is a URL
    function isURL(path) {
        return path && (path.startsWith('http://') || path.startsWith('https://'));
    }
    %>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1;
            color: #333;
            margin: 0;
            padding: 10px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ddd;
        }
        .exam-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .exam-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
            font-size: 14px;
            white-space: pre-line;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .question {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .question-number {
            font-weight: bold;
        }
        .question-marks {
            font-size: 14px;
            color: #666;
        }
        .question-text {
            margin-bottom: 15px;
            white-space: pre-line;
        }
        .essay {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            font-size: 14px;
        }
        .essay-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .essay-group {
            border-left: 3px solid #6b7280;
            padding-left: 15px;
            margin-bottom: 30px;
        }
        .essay-group .question {
            margin-left: 15px;
        }
        .options-grid {
            margin-left: 20px;
            margin-top: 10px;
        }
        .grid-columns-1 {
            display: block;
        }
        .grid-columns-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 10px;
        }
        .grid-columns-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 10px;
        }
        .option {
            margin-bottom: 10px;
        }
        .option-content {
            display: flex;
            align-items: flex-start;
        }
        .option-text {
            margin-left: 5px;
        }
        .answer-space {
            border: 1px solid #ddd;
            height: 100px;
            margin-top: 10px;
        }
        .true-false {
            display: flex;
            gap: 30px;
        }
        .true-false-option {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .checkbox {
            width: 15px;
            height: 15px;
            border: 1px solid #999;
            display: inline-block;
        }
        .page-number {
            text-align: center;
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #999;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        @media print {
            .page-break {
                page-break-after: always;
            }
        }
    </style>
</head>
<body>
    <!-- Title Page -->
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 80vh; text-align: center;">
        <h1 style="font-size: 24px; margin-bottom: 20px;"><%= exam.exam_name %></h1>
        <div style="font-size: 16px; margin-bottom: 10px;">Duration: <%= exam.duration %> minutes</div>
        <div style="font-size: 16px; margin-bottom: 10px;">Total Marks: <%= totalMarks %></div>
        <div style="font-size: 16px; margin-bottom: 10px;">Passing Marks: <%= parseFloat(exam.passing_marks).toFixed(2) %>%</div>
        <div style="font-size: 14px; margin-top: 30px; color: #666;">Generated on <%= new Date().toLocaleDateString() %></div>
    </div>

    <!-- Force page break after title page -->
    <div class="page-break"></div>

    <!-- Header for content pages -->
    <div class="header">
        <div class="exam-title"><%= exam.exam_name %></div>
        <div class="exam-info">
            <div>Duration: <%= exam.duration %> minutes</div>
            <div>Total Marks: <%= totalMarks %></div>
            <div>Passing Marks: <%= parseFloat(exam.passing_marks).toFixed(2) %>%</div>
        </div>
    </div>

    <% if (exam.instructions) { %>
    <div class="instructions">
        <strong>Instructions:</strong>
        <div class="instruction-content" style="margin-top: 3px; line-height: 0.8; font-family: Arial, sans-serif; font-size: 0.9em;">
            <%
            // Split instructions by newlines and filter out empty lines
            const instructionsArray = exam.instructions.split('\n').filter(line => line.trim());
            instructionsArray.forEach((line, index) => {
            %>
                <p style="margin-top: 0; margin-bottom: 0; padding: 0;"><%= index + 1 %>. <%= line %></p>
            <% }); %>
        </div>
    </div>
    <!-- Force page break after instructions -->
    <div class="page-break"></div>
    <% } %>

    <% let globalQuestionIndex = 1; %>
    <% sections.forEach((section, sectionIndex) => { %>
        <div class="section">
            <div class="section-title">Section <%= sectionIndex + 1 %>: <%= section.name %></div>

            <%
            // Group questions by essay_id
            const essayGroups = {};
            const nonEssayQuestions = [];

            section.questions.forEach(question => {
                if (question.essay_id) {
                    if (!essayGroups[question.essay_id]) {
                        essayGroups[question.essay_id] = {
                            essay: {
                                id: question.essay_id,
                                title: question.essay_title,
                                content: question.essay_content
                            },
                            questions: []
                        };
                    }
                    essayGroups[question.essay_id].questions.push(question);
                } else {
                    nonEssayQuestions.push(question);
                }
            });

            // Display essay groups first
            Object.values(essayGroups).forEach(group => {
            %>
                <div class="essay-group">
                    <div class="essay">
                        <div class="essay-title"><%= group.essay.title %></div>
                        <div><%= group.essay.content %></div>
                    </div>

                    <% group.questions.forEach(question => { %>
                        <div class="question">
                            <div class="question-header">
                                <div class="question-number">Question <%= globalQuestionIndex %>:</div>
                                <div class="question-marks">
                                    <%= parseFloat(question.marks).toFixed(2) %> marks
                                    <% if (parseFloat(question.negative_marks) > 0) { %>
                                        (-<%= parseFloat(question.negative_marks).toFixed(2) %> negative)
                                    <% } %>
                                </div>
                            </div>

                            <div class="question-text"><%= question.question_text %></div>
                            <% if (question.image_path) { %>
                                <div class="question-image">
                                    <img src="<%= isURL(question.image_path) ? question.image_path : getImagePath(question.image_path) %>" alt="Question Image" style="max-width: 320px; max-height: 240px; width: auto; height: auto; margin: 5px auto; border: 1px solid #ddd; border-radius: 4px; display: block;">
                                </div>
                            <% } %>
                            <% if (question.image_url) { %>
                                <div class="question-image">
                                    <img src="<%= question.image_url %>" alt="Question Image" style="max-width: 320px; max-height: 240px; width: auto; height: auto; margin: 5px auto; border: 1px solid #ddd; border-radius: 4px; display: block;">
                                </div>
                            <% } %>

                            <% if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') { %>
                                <div class="options-grid">
                                    <% if (question.options && question.options.length > 0) { %>
                                        <%
                                        // Determine number of columns based on number of options
                                        let numColumns = 1;
                                        if (question.options.length >= 4) {
                                            numColumns = 2;
                                        }
                                        if (question.options.length >= 8) {
                                            numColumns = 3;
                                        }
                                        %>
                                        <div class="grid-columns-<%= numColumns %>">
                                            <% question.options.forEach((option, optionIndex) => { %>
                                                <div class="option">
                                                    <div class="option-content">
                                                        <div class="checkbox"></div>
                                                        <div class="option-text"><%= option.option_text %></div>
                                                    </div>
                                                </div>
                                            <% }); %>
                                        </div>
                                    <% } else { %>
                                        <div>No options available for this question.</div>
                                    <% } %>
                                </div>
                            <% } else if (question.question_type === 'true_false') { %>
                                <div class="true-false">
                                    <div class="true-false-option">
                                        <div class="checkbox"></div>
                                        True
                                    </div>
                                    <div class="true-false-option">
                                        <div class="checkbox"></div>
                                        False
                                    </div>
                                </div>
                            <% } else { %>
                                <div class="answer-space"></div>
                            <% } %>
                        </div>
                        <% globalQuestionIndex++; %>
                    <% }); %>
                </div>
            <% }); %>

            <!-- Display non-essay questions -->
            <% nonEssayQuestions.forEach(question => { %>
                <div class="question">
                    <div class="question-header">
                        <div class="question-number">Question <%= globalQuestionIndex %>:</div>
                        <div class="question-marks">
                            <%= parseFloat(question.marks).toFixed(2) %> marks
                            <% if (parseFloat(question.negative_marks) > 0) { %>
                                (-<%= parseFloat(question.negative_marks).toFixed(2) %> negative)
                            <% } %>
                        </div>
                    </div>

                    <div class="question-text"><%= question.question_text %></div>
                    <% if (question.image_path) { %>
                        <div class="question-image">
                            <img src="<%= isURL(question.image_path) ? question.image_path : getImagePath(question.image_path) %>" alt="Question Image" style="max-width: 320px; max-height: 240px; width: auto; height: auto; margin: 5px auto; border: 1px solid #ddd; border-radius: 4px; display: block;">
                        </div>
                    <% } %>
                    <% if (question.image_url) { %>
                        <div class="question-image">
                            <img src="<%= question.image_url %>" alt="Question Image" style="max-width: 320px; max-height: 240px; width: auto; height: auto; margin: 5px auto; border: 1px solid #ddd; border-radius: 4px; display: block;">
                        </div>
                    <% } %>

                    <% if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') { %>
                        <div class="options-grid">
                            <% if (question.options && question.options.length > 0) { %>
                                <%
                                // Determine number of columns based on number of options
                                let numColumns = 1;
                                if (question.options.length >= 4) {
                                    numColumns = 2;
                                }
                                if (question.options.length >= 8) {
                                    numColumns = 3;
                                }
                                %>
                                <div class="grid-columns-<%= numColumns %>">
                                    <% question.options.forEach((option, optionIndex) => { %>
                                        <div class="option">
                                            <div class="option-content">
                                                <div class="checkbox"></div>
                                                <div class="option-text"><%= option.option_text %></div>
                                            </div>
                                        </div>
                                    <% }); %>
                                </div>
                            <% } else { %>
                                <div>No options available for this question.</div>
                            <% } %>
                        </div>
                    <% } else if (question.question_type === 'true_false') { %>
                        <div class="true-false">
                            <div class="true-false-option">
                                <div class="checkbox"></div>
                                True
                            </div>
                            <div class="true-false-option">
                                <div class="checkbox"></div>
                                False
                            </div>
                        </div>
                    <% } else { %>
                        <div class="answer-space"></div>
                    <% } %>
                </div>
                <% globalQuestionIndex++; %>
            <% }); %>
        </div>

        <% /* No page breaks between sections to optimize space */ %>
    <% }); %>

    <!-- Footer is now handled by PDF generator -->
</body>
</html>
