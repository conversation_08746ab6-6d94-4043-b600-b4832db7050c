<%- contentFor('script') %>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Create charts if data is available
    if (<%= overallStats.total_tests %> > 0) {
      // Create category performance chart
      const categoryCtx = document.getElementById('categoryChart').getContext('2d');
      const categoryData = {
        labels: [<% categoryStats.forEach((cat, index) => { %>'<%= cat.category || "Uncategorized" %>'<%= index < categoryStats.length - 1 ? ',' : '' %><% }); %>],
        datasets: [{
          label: 'Average Score (%)',
          data: [<% categoryStats.forEach((cat, index) => { %><%= cat.average_score || 0 %><%= index < categoryStats.length - 1 ? ',' : '' %><% }); %>],
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }]
      };
      
      new Chart(categoryCtx, {
        type: 'bar',
        data: categoryData,
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          },
          plugins: {
            title: {
              display: true,
              text: 'Performance by Category'
            }
          }
        }
      });
      
      // Create improvement over time chart
      const improvementCtx = document.getElementById('improvementChart').getContext('2d');
      const improvementData = {
        labels: [<% improvementData.forEach((data, index) => { %>'<%= data.month %>'<%= index < improvementData.length - 1 ? ',' : '' %><% }); %>],
        datasets: [{
          label: 'Average Score (%)',
          data: [<% improvementData.forEach((data, index) => { %><%= data.average_score || 0 %><%= index < improvementData.length - 1 ? ',' : '' %><% }); %>],
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 2,
          tension: 0.3,
          fill: true
        }]
      };
      
      new Chart(improvementCtx, {
        type: 'line',
        data: improvementData,
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          },
          plugins: {
            title: {
              display: true,
              text: 'Score Improvement Over Time'
            }
          }
        }
      });
    }
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Test Performance</h1>
    <a href="/tests/results" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
      </svg>
      Back to Results
    </a>
  </div>
  
  <% if (overallStats.total_tests === 0) { %>
    <div class="bg-white rounded-lg shadow-md p-6 text-center">
      <p class="text-gray-600">You haven't completed any tests yet. Complete some tests to see your performance analysis.</p>
      <a href="/tests" class="mt-4 inline-block text-indigo-600 hover:text-indigo-900">
        Go to available tests
      </a>
    </div>
  <% } else { %>
    <!-- Performance Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-white rounded-lg shadow-md p-4">
        <h3 class="text-sm font-medium text-gray-500 uppercase">Total Tests</h3>
        <p class="mt-2 text-3xl font-bold text-gray-900"><%= overallStats.total_tests %></p>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-4">
        <h3 class="text-sm font-medium text-gray-500 uppercase">Tests Passed</h3>
        <p class="mt-2 text-3xl font-bold text-green-600"><%= overallStats.tests_passed %></p>
        <p class="text-sm text-gray-500">
          <%= ((overallStats.tests_passed / overallStats.total_tests) * 100).toFixed(1) %>% pass rate
        </p>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-4">
        <h3 class="text-sm font-medium text-gray-500 uppercase">Average Score</h3>
        <p class="mt-2 text-3xl font-bold text-blue-600"><%= overallStats.average_score ? overallStats.average_score.toFixed(1) : 0 %>%</p>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-4">
        <h3 class="text-sm font-medium text-gray-500 uppercase">Average Time</h3>
        <p class="mt-2 text-3xl font-bold text-purple-600">
          <%= overallStats.average_time_taken ? Math.floor(overallStats.average_time_taken) : 0 %> min
        </p>
      </div>
    </div>
    
    <!-- Recent Test Attempts -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Recent Test Attempts</h2>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Result</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% recentAttempts.forEach(attempt => { %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= attempt.formatted_end_time %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= attempt.exam_name %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= attempt.score %>%</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (attempt.score >= attempt.passing_marks) { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Passed
                    </span>
                  <% } else { %>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                      Failed
                    </span>
                  <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="/tests/result/<%= attempt.id %>" class="text-indigo-600 hover:text-indigo-900">
                    View Details
                  </a>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Performance by Category -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Performance by Category</h2>
      <% if (categoryStats.length > 0) { %>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <canvas id="categoryChart" height="300"></canvas>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attempts</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Score</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pass Rate</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% categoryStats.forEach(category => { %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900"><%= category.category || "Uncategorized" %></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900"><%= category.attempts %></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900"><%= category.average_score ? category.average_score.toFixed(1) : 0 %>%</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900"><%= ((category.passed / category.attempts) * 100).toFixed(1) %>%</div>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>
        </div>
      <% } else { %>
        <p class="text-gray-600">No category data available.</p>
      <% } %>
    </div>
    
    <!-- Improvement Over Time -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Improvement Over Time</h2>
      <% if (improvementData.length > 0) { %>
        <div class="h-80">
          <canvas id="improvementChart"></canvas>
        </div>
      <% } else { %>
        <p class="text-gray-600">Not enough data to show improvement over time. Complete more tests to see your progress.</p>
      <% } %>
    </div>
  <% } %>
</div>
