<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= __('app.name') %></title>
    <link rel="stylesheet" href="/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">

    <div class="container mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6"><%= __('performance.performanceAnalysis') %></h1>

        <% if (locals.error) { %>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold"><%= __('common.error') %>!</strong>
                <span class="block sm:inline"><%= error %></span>
            </div>
        <% } %>

        <% if ((!overallPerformance || overallPerformance.length === 0) &&
               (!attemptStats || attemptStats.length === 0) &&
               (!performanceByCategory.weak || performanceByCategory.weak.length === 0) &&
               (!performanceByCategory.medium || performanceByCategory.medium.length === 0) &&
               (!performanceByCategory.strong || performanceByCategory.strong.length === 0)) { %>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 text-center">
                <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('performance.noDataAvailable') %></h2>
                <p class="text-gray-600 mb-4"><%= __('performance.noDataDescription') %></p>
                <a href="/tests" class="inline-block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition"><%= __('performance.browseAvailableTests') %></a>
            </div>
        <% } else { %>
            <p class="text-gray-600 mb-6">
                <%= __('performance.performanceDescription') %>
            </p>

            <!-- Performance Summary -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- Total Attempts -->
                <div class="bg-blue-50 rounded-lg shadow-md p-6 text-center">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2"><%= __('performance.totalAttempts') %></h3>
                    <p class="text-3xl font-bold text-blue-600">
                        <%
                            let totalAttempts = 0;
                            if (overallPerformance && overallPerformance.length > 0) {
                                overallPerformance.forEach(exam => {
                                    totalAttempts += parseInt(exam.attempt_count || 0);
                                });
                            }
                        %>
                        <%= totalAttempts %>
                    </p>
                </div>

                <!-- Average Score -->
                <div class="bg-green-50 rounded-lg shadow-md p-6 text-center">
                    <h3 class="text-lg font-semibold text-green-800 mb-2"><%= __('performance.averageScore') %></h3>
                    <p class="text-3xl font-bold text-green-600">
                        <%
                            let totalScore = 0;
                            let scoreCount = 0;
                            if (overallPerformance && overallPerformance.length > 0) {
                                overallPerformance.forEach(exam => {
                                    if (exam.average_score) {
                                        totalScore += parseFloat(exam.average_score);
                                        scoreCount++;
                                    }
                                });
                            }
                            const avgScore = scoreCount > 0 ? (totalScore / scoreCount).toFixed(1) : 0;
                        %>
                        <%= avgScore %>%
                    </p>
                </div>

                <!-- Pass Rate -->
                <div class="bg-purple-50 rounded-lg shadow-md p-6 text-center">
                    <h3 class="text-lg font-semibold text-purple-800 mb-2"><%= __('performance.passRate') %></h3>
                    <p class="text-3xl font-bold text-purple-600">
                        <%
                            let totalPasses = 0;
                            let totalFails = 0;
                            if (overallPerformance && overallPerformance.length > 0) {
                                overallPerformance.forEach(exam => {
                                    totalPasses += parseInt(exam.pass_count || 0);
                                    totalFails += parseInt(exam.fail_count || 0);
                                });
                            }
                            const totalExams = totalPasses + totalFails;
                            const passRate = totalExams > 0 ? (totalPasses / totalExams * 100).toFixed(1) : 0;
                        %>
                        <%= passRate %>%
                        <p class="text-sm text-purple-600 mt-1">(<%= totalPasses %>/<%= totalExams %>)</p>
                    </p>
                </div>

                <!-- Time Spent -->
                <div class="bg-orange-50 rounded-lg shadow-md p-6 text-center">
                    <h3 class="text-lg font-semibold text-orange-800 mb-2"><%= __('performance.timeSpent') %></h3>
                    <p class="text-3xl font-bold text-orange-600">
                        <%= timeSpentHours %><span class="text-sm">h</span> <%= timeSpentMinutes %><span class="text-sm">m</span>
                    </p>
                    <p class="text-sm text-orange-600"><%= timeSpentFormatted %></p>
                </div>
            </div>

            <!-- Recent Test Performance -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Test Performance</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('common.date') %></th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.examName') %></th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.scoreLabel') %></th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.durationLabel') %></th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('tests.status') %></th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><%= __('common.actions') %></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% if (attemptStats && attemptStats.length > 0) { %>
                                <% attemptStats.slice(0, 5).forEach(attempt => { %>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                            <%= new Date(attempt.attempt_date).toLocaleDateString('en-US', { day: '2-digit', month: 'short', year: 'numeric' }) %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                            <%= attempt.exam_name %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                            <% if (attempt.total_score !== null && attempt.total_score !== undefined) { %>
                                                <%
                                                    const score = parseFloat(attempt.total_score);
                                                    const passingMark = parseFloat(attempt.passing_marks);
                                                %>
                                                <span class="<%= !isNaN(score) && !isNaN(passingMark) && score >= passingMark ? 'text-green-600' : 'text-red-600' %>">
                                                    <%= typeof score === 'number' && !isNaN(score) ? score.toFixed(1) : '0' %>%
                                                </span>
                                            <% } else { %>
                                                <span class="text-gray-500">Not calculated</span>
                                            <% } %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                            <%
                                                let duration = 'N/A';
                                                if (attempt.duration_seconds) {
                                                    const mins = Math.floor(attempt.duration_seconds / 60);
                                                    const secs = attempt.duration_seconds % 60;
                                                    duration = `${mins} min ${secs} sec`;
                                                }
                                            %>
                                            <%= duration %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <% if (attempt.status === 'completed') { %>
                                                <%
                                                    const score = parseFloat(attempt.total_score);
                                                    const passingMark = parseFloat(attempt.passing_marks);
                                                    const passed = !isNaN(score) && !isNaN(passingMark) && score >= passingMark;
                                                %>
                                                <% if (passed) { %>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"><%= __('tests.passed') %></span>
                                                <% } else { %>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800"><%= __('tests.failed') %></span>
                                                <% } %>
                                            <% } else if (attempt.status === 'in_progress') { %>
                                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800"><%= __('tests.testInProgress') %></span>
                                            <% } else { %>
                                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800"><%= attempt.status %></span>
                                            <% } %>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                            <a href="/tests/results/<%= attempt.attempt_id %>" class="text-indigo-600 hover:text-indigo-900"><%= __('common.details') %></a>
                                        </td>
                                    </tr>
                                <% }); %>
                            <% } else { %>
                                <tr>
                                    <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                                        <%= __('tests.noTestsAvailable') %>
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
                <% if (attemptStats && attemptStats.length > 5) { %>
                    <div class="mt-4 text-center">
                        <a href="/tests/results" class="text-indigo-600 hover:text-indigo-900"><%= __('tests.viewAllResults') %></a>
                    </div>
                <% } %>
            </div>

            <!-- Overall Performance Summary -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('performance.overallPerformance') %></h2>

                <% if (overallPerformance && overallPerformance.length > 0) { %>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-2 px-4 border-b text-left"><%= __('tests.examName') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('performance.totalAttempts') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('performance.averageScore') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('common.max') %> <%= __('tests.scoreLabel') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('performance.passRate') %></th>
                                </tr>
                            </thead>
                            <tbody>
                                <% overallPerformance.forEach(exam => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-2 px-4 border-b"><%= exam.exam_name %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= exam.attempt_count %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= parseFloat(exam.average_score || 0).toFixed(2) %>%</td>
                                        <td class="py-2 px-4 border-b text-center"><%= parseFloat(exam.highest_score || 0).toFixed(2) %>%</td>
                                        <td class="py-2 px-4 border-b text-center">
                                            <%
                                                const total = parseInt(exam.pass_count || 0) + parseInt(exam.fail_count || 0);
                                                const passRate = total > 0 ? (parseInt(exam.pass_count || 0) / total * 100).toFixed(2) : 0;
                                            %>
                                            <%= passRate %>%
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <p class="text-gray-600">No performance data available. Complete some tests to see your performance analysis.</p>
                <% } %>
            </div>

            <!-- Charts Section -->
            <% if (overallPerformance && overallPerformance.length > 0) { %>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4"><%= __('performance.averageScoresByExam') %></h3>
                        <div class="h-64">
                            <canvas id="scoresChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4"><%= __('performance.attemptsByExam') %></h3>
                        <div class="h-64">
                            <canvas id="attemptsChart"></canvas>
                        </div>
                    </div>
                </div>
            <% } %>

            <!-- Performance by Category -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- Strong Areas -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-green-600 mb-4"><%= __('performance.strongAreas') %> (>60%)</h3>
                    <% if (performanceByCategory.strong && performanceByCategory.strong.length > 0) { %>
                        <ul class="space-y-2">
                            <% performanceByCategory.strong.forEach(item => { %>
                                <li class="border-b pb-2">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium"><%= item.section_name %></span>
                                        <span class="text-green-600 font-bold"><%= parseFloat(item.score_percentage || 0).toFixed(2) %>%</span>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <%= item.exam_name %> - <%= new Date(item.created_at).toLocaleDateString('en-US', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-') %>
                                    </div>
                                </li>
                            <% }) %>
                        </ul>
                    <% } else { %>
                        <p class="text-gray-600">No strong areas identified yet.</p>
                    <% } %>
                </div>

                <!-- Medium Areas -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-yellow-600 mb-4"><%= __('performance.improvementAreas') %> (30-60%)</h3>
                    <% if (performanceByCategory.medium && performanceByCategory.medium.length > 0) { %>
                        <ul class="space-y-2">
                            <% performanceByCategory.medium.forEach(item => { %>
                                <li class="border-b pb-2">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium"><%= item.section_name %></span>
                                        <span class="text-yellow-600 font-bold"><%= parseFloat(item.score_percentage || 0).toFixed(2) %>%</span>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <%= item.exam_name %> - <%= new Date(item.created_at).toLocaleDateString('en-US', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-') %>
                                    </div>
                                </li>
                            <% }) %>
                        </ul>
                    <% } else { %>
                        <p class="text-gray-600"><%= __('performance.noDataAvailable') %></p>
                    <% } %>
                </div>

                <!-- Weak Areas -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-red-600 mb-4"><%= __('performance.improvementAreas') %> (<30%)</h3>
                    <% if (performanceByCategory.weak && performanceByCategory.weak.length > 0) { %>
                        <ul class="space-y-2">
                            <% performanceByCategory.weak.forEach(item => { %>
                                <li class="border-b pb-2">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium"><%= item.section_name %></span>
                                        <span class="text-red-600 font-bold"><%= parseFloat(item.score_percentage || 0).toFixed(2) %>%</span>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <%= item.exam_name %> - <%= formatDateTime(item.created_at) %>
                                    </div>
                                </li>
                            <% }) %>
                        </ul>
                    <% } else { %>
                        <p class="text-gray-600"><%= __('performance.noDataAvailable') %></p>
                    <% } %>
                </div>
            </div>

            <!-- Attempt Statistics -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('tests.recentAttempts') %></h2>

                <% if (attemptStats && attemptStats.length > 0) { %>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-2 px-4 border-b text-left"><%= __('tests.examName') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('tests.attempted') %> #</th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('tests.durationLabel') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('tests.totalQuestions') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('tests.correctAnswers') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('tests.scoreLabel') %></th>
                                    <th class="py-2 px-4 border-b text-center"><%= __('common.date') %></th>
                                </tr>
                            </thead>
                            <tbody>
                                <% attemptStats.forEach(attempt => { %>
                                    <tr class="hover:bg-gray-50">
                                        <td class="py-2 px-4 border-b"><%= attempt.exam_name %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= attempt.attempt_number %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= attempt.duration_formatted %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= attempt.total_questions %></td>
                                        <td class="py-2 px-4 border-b text-center"><%= attempt.correct_answers %></td>
                                        <td class="py-2 px-4 border-b text-center">
                                            <%
                                                let scoreColor = '';
                                                if (attempt.score_percentage < 30) {
                                                    scoreColor = 'text-red-600';
                                                } else if (attempt.score_percentage < 60) {
                                                    scoreColor = 'text-yellow-600';
                                                } else {
                                                    scoreColor = 'text-green-600';
                                                }
                                            %>
                                            <span class="<%= scoreColor %> font-medium">
                                                <%= parseFloat(attempt.score_percentage || 0).toFixed(2) %>%
                                            </span>
                                        </td>
                                        <td class="py-2 px-4 border-b text-center">
                                            <%= formatDateTime(attempt.created_at) %>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <p class="text-gray-600"><%= __('performance.noDataDescription') %></p>
                <% } %>
            </div>
        <% } %>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Parse chart data
            const chartData = <%- chartData %>;

            // Create scores chart
            if (chartData && chartData.exams && chartData.exams.length > 0) {
                const scoresCtx = document.getElementById('scoresChart').getContext('2d');
                new Chart(scoresCtx, {
                    type: 'bar',
                    data: {
                        labels: chartData.exams,
                        datasets: [{
                            label: '<%= __('performance.averageScore') %> (%)',
                            data: chartData.scores,
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });

                // Create attempts chart
                const attemptsCtx = document.getElementById('attemptsChart').getContext('2d');
                new Chart(attemptsCtx, {
                    type: 'bar',
                    data: {
                        labels: chartData.exams,
                        datasets: [{
                            label: '<%= __('performance.totalAttempts') %>',
                            data: chartData.attempts,
                            backgroundColor: 'rgba(16, 185, 129, 0.7)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        }
                    }
                });
            } else {
                // Handle case where there's no data
                document.getElementById('scoresChart').parentNode.innerHTML = '<p class="text-gray-500 text-center pt-8">No data available to display chart</p>';
                document.getElementById('attemptsChart').parentNode.innerHTML = '<p class="text-gray-500 text-center pt-8">No data available to display chart</p>';
            }
        });
    </script>

    <%- include('../partials/footer') %>
</body>
</html>