<%- contentFor('script') %>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const reasonTextarea = document.getElementById('reason');
    const charCount = document.getElementById('charCount');
    const maxLength = 500;
    
    function updateCharCount() {
      const currentLength = reasonTextarea.value.length;
      charCount.textContent = `${currentLength}/${maxLength} characters`;
      
      if (currentLength > maxLength) {
        charCount.classList.add('text-red-500');
      } else {
        charCount.classList.remove('text-red-500');
      }
    }
    
    reasonTextarea.addEventListener('input', updateCharCount);
    updateCharCount();
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6 text-gray-800">Request Additional Attempts</h1>
    
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
      <h2 class="text-xl font-semibold mb-2 text-gray-700"><%= exam.exam_name %></h2>
      <div class="flex flex-wrap gap-4 text-sm text-gray-600">
        <div>
          <span class="font-medium">Duration:</span> <%= exam.duration %> minutes
        </div>
        <div>
          <span class="font-medium">Total Questions:</span> <%= exam.total_questions || 'N/A' %>
        </div>
        <div>
          <span class="font-medium">Passing Marks:</span> <%= exam.passing_marks || 'N/A' %>
        </div>
      </div>
    </div>
    
    <form action="/tests/request-access/<%= exam.exam_id %>" method="POST" class="space-y-6">
      <div>
        <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">
          Please explain why you need additional attempts for this test:
        </label>
        <textarea 
          id="reason" 
          name="reason" 
          rows="6" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter your reason here..."
          required
        ></textarea>
        <div id="charCount" class="text-sm text-gray-500 mt-1">0/500 characters</div>
      </div>
      
      <div class="flex items-center justify-between">
        <a href="/tests" class="text-indigo-600 hover:text-indigo-900">
          &larr; Back to Tests
        </a>
        <button 
          type="submit" 
          class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Submit Request
        </button>
      </div>
    </form>
  </div>
</div>
