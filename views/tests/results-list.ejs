<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/styles.css">
</head>
<body class="bg-gray-100 min-h-screen">

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Results Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">My Test Results</h1>
            <a href="/tests" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
                Back to Tests
            </a>
        </div>

        <!-- Results List -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <% if (attempts && attempts.length > 0) { %>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Taken</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% attempts.forEach(attempt => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><%= attempt.exam_name %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500"><%= formatDateTime(attempt.attempt_date) %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <%= attempt.status === 'completed' ? 'bg-green-100 text-green-800' :
                                            attempt.status === 'failed' ? 'bg-red-100 text-red-800' :
                                            attempt.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                            'bg-gray-100 text-gray-800' %>">
                                            <%= attempt.status.replace('_', ' ').charAt(0).toUpperCase() + attempt.status.replace('_', ' ').slice(1) %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <%= attempt.total_score ? Math.round(attempt.total_score) + '%' : 'N/A' %>
                                            <% if (attempt.passing_marks) { %>
                                                <span class="text-xs text-gray-500">(Passing: <%= attempt.passing_marks %>%)</span>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <% if (attempt.status === 'in_progress') { %>
                                            <a href="/tests/take/<%= attempt.exam_id %>/<%= attempt.attempt_id %>" class="text-blue-600 hover:text-blue-900 font-medium">Continue</a>
                                        <% } else { %>
                                            <a href="/tests/results/<%= attempt.attempt_id %>" class="text-indigo-600 hover:text-indigo-900">View Details</a>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="text-center py-10">
                    <p class="text-gray-500 mb-4">You haven't taken any tests yet.</p>
                    <a href="/tests" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded inline-block">
                        Browse Available Tests
                    </a>
                </div>
            <% } %>
        </div>
    </div>
</body>
</html>