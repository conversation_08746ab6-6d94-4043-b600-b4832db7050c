<!-- Add Alpine.js for tab functionality -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<link rel="stylesheet" href="/css/image-styles.css">
<style>
    /* Custom scrollbar for question containers */
    .question-container::-webkit-scrollbar {
        width: 8px;
    }
    .question-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    .question-container::-webkit-scrollbar-thumb {
        background: #c7d2fe;
        border-radius: 10px;
    }
    .question-container::-webkit-scrollbar-thumb:hover {
        background: #818cf8;
    }

    /* Tab styles */
    .tab-active {
        color: #4f46e5;
        border-bottom: 2px solid #4f46e5;
    }
</style>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Results Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-4"><%= attempt.exam_name %> - Results</h1>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-4">
                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium">Date Taken</p>
                    <p class="text-gray-800 font-semibold">
                        <%= formatDateTime(attempt.attempt_date) %>
                    </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium">Status</p>
                    <p class="font-semibold
                        <%= attempt.status === 'completed' ? 'text-green-600' :
                           attempt.status === 'failed' ? 'text-red-600' :
                           attempt.status === 'in_progress' ? 'text-blue-600' : 'text-gray-800' %>">
                        <%= attempt.status.replace('_', ' ').charAt(0).toUpperCase() + attempt.status.replace('_', ' ').slice(1) %>
                    </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium">Score</p>
                    <p class="text-gray-800 font-semibold">
                        <% if (attempt.total_score !== null && attempt.total_score !== undefined) { %>
                            <%= parseFloat(attempt.total_score).toFixed(2) %>%
                        <% } else { %>
                            N/A
                        <% } %>
                        <% if (attempt.passing_marks) { %>
                            <span class="text-sm text-gray-500">(Passing: <%= attempt.passing_marks %>%)</span>
                        <% } %>
                    </p>
                </div>
            </div>

            <div class="mt-4">
                <a href="/tests" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded inline-block">
                    Back to Tests
                </a>
            </div>
        </div>

        <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 mb-6">
            <!-- Left column - basic info -->
            <div class="w-full md:w-1/2 bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Test Summary</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-gray-600">Completion Date:</p>
                        <p class="font-medium"><%= formatDateTime(attempt.end_time || attempt.attempt_date) %></p>
                    </div>
                    <div>
                        <p class="text-gray-600">Duration:</p>
                        <p class="font-medium">
                            <%
                            let duration = 'N/A';
                            if (attempt.start_time && attempt.end_time) {
                                const start = new Date(attempt.start_time);
                                const end = new Date(attempt.end_time);
                                const diff = Math.floor((end - start) / 1000);
                                const hrs = Math.floor(diff / 3600);
                                const mins = Math.floor((diff % 3600) / 60);
                                const secs = diff % 60;
                                duration = `${hrs}h ${mins}m ${secs}s`;
                            }
                            %>
                            <%= duration %>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-600">Score:</p>
                        <p class="font-medium text-xl">
                            <% if (attempt.total_score !== null && attempt.total_score !== undefined) { %>
                                <span class="<%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'text-green-600' : 'text-red-600' %>">
                                    <%= typeof attempt.total_score === 'number' ? attempt.total_score.toFixed(2) : (typeof attempt.total_score === 'string' ? parseFloat(attempt.total_score).toFixed(2) : 'N/A') %>%
                                </span>
                                <% if (attempt.total_marks_obtained !== null && attempt.total_marks_obtained !== undefined && attempt.total_marks_possible !== null && attempt.total_marks_possible !== undefined) { %>
                                    <span class="text-gray-600 text-sm ml-2">
                                        (<%= parseFloat(attempt.total_marks_obtained).toFixed(2) %>/<%= parseFloat(attempt.total_marks_possible).toFixed(2) %>)
                                    </span>
                                <% } %>
                            <% } else { %>
                                <span class="text-gray-600">Not calculated</span>
                            <% } %>
                        </p>
                    </div>
                    <%
                    // Calculate section statistics
                    let totalAttemptedQuestions = 0;
                    let totalSectionQuestions = 0;
                    let totalSectionMarksObtained = 0;
                    let totalSectionMarksPossible = 0;

                    sections.forEach(section => {
                        section.questions.forEach(question => {
                            totalSectionQuestions++;
                            if (question.user_answer) {
                                totalAttemptedQuestions++;
                            }
                            if (question.is_correct === true) {
                                totalSectionMarksObtained += parseFloat(question.marks) || 1.00;
                            } else if (question.user_answer && question.is_correct === false) {
                                // Deduct negative marks for incorrect answers
                                totalSectionMarksObtained -= parseFloat(question.negative_marks) || 0.00;
                            }
                            totalSectionMarksPossible += parseFloat(question.marks) || 1.00;
                        });
                    });

                    // Calculate percentages
                    let attemptPercentage = totalSectionQuestions > 0 ? (totalAttemptedQuestions / totalSectionQuestions) * 100 : 0;
                    let totalSectionMarksPercentage = totalSectionMarksPossible > 0 ?
                        (totalSectionMarksObtained / totalSectionMarksPossible) * 100 : 0;
                    totalSectionMarksPercentage = Math.max(0, totalSectionMarksPercentage); // Ensure not negative
                    %>

                    <div>
                        <p class="text-gray-600">Attempt Percentage:</p>
                        <p class="font-medium text-xl">
                            <span class="text-blue-600">
                                <%= attemptPercentage.toFixed(2) %>%
                            </span>
                            <span class="text-gray-600 text-sm ml-2">
                                (<%= totalAttemptedQuestions %>/<%= totalSectionQuestions %>)
                            </span>
                        </p>
                    </div>

                    <div>
                        <p class="text-gray-600">Section Marks:</p>
                        <p class="font-medium text-xl">
                            <span class="<%= totalSectionMarksPercentage >= parseFloat(attempt.passing_marks) ? 'text-green-600' : 'text-red-600' %>">
                                <%= totalSectionMarksPercentage.toFixed(2) %>%
                            </span>
                            <span class="text-gray-600 text-sm ml-2">
                                (<%= totalSectionMarksObtained.toFixed(2) %>/<%= totalSectionMarksPossible.toFixed(2) %>)
                            </span>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-600">Result:</p>
                        <p class="font-medium">
                            <% if (attempt.status === 'completed') { %>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full <%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                    <%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'Passed' : 'Failed' %>
                                </span>
                            <% } else { %>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Incomplete
                                </span>
                            <% } %>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Right column - quick stats -->
            <div class="w-full md:w-1/2 bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Question Summary</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <%
                        // Calculate question statistics
                        let statsTotalQuestions = 0;
                        let statsAnsweredQuestions = 0;
                        let correctAnswers = 0;
                        let bookmarkedQuestions = 0;

                        sections.forEach(section => {
                            section.questions.forEach(question => {
                                statsTotalQuestions++;

                                if (question.user_answer) {
                                    statsAnsweredQuestions++;

                                    // Check if answer is correct based on the is_correct flag
                                    if (question.is_correct === true) {
                                        correctAnswers++;
                                    }
                                }

                                // Use a safe way to check for bookmarks
                                try {
                                    if (question.hasOwnProperty('is_bookmarked') && question.is_bookmarked) {
                                        bookmarkedQuestions++;
                                    }
                                } catch (e) {
                                    // Ignore errors if is_bookmarked doesn't exist
                                }
                            });
                        });

                        // Calculate accuracy and precision
                        const accuracy = statsTotalQuestions > 0 ? (correctAnswers / statsTotalQuestions) * 100 : 0;
                        const precision = statsAnsweredQuestions > 0 ? (correctAnswers / statsAnsweredQuestions) * 100 : 0;
                    %>
                    <div class="text-center px-2 py-3 bg-blue-50 rounded-lg">
                        <p class="text-2xl font-bold text-blue-700"><%= statsTotalQuestions %></p>
                        <p class="text-sm text-blue-600">Total Questions</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-purple-50 rounded-lg">
                        <p class="text-2xl font-bold text-purple-700"><%= statsAnsweredQuestions %></p>
                        <p class="text-sm text-purple-600">Answered</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-green-50 rounded-lg">
                        <p class="text-2xl font-bold text-green-700"><%= correctAnswers %></p>
                        <p class="text-sm text-green-600">Correct</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-red-50 rounded-lg">
                        <p class="text-2xl font-bold text-red-700"><%= statsAnsweredQuestions - correctAnswers %></p>
                        <p class="text-sm text-red-600">Incorrect</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-yellow-50 rounded-lg">
                        <p class="text-2xl font-bold text-yellow-700"><%= statsTotalQuestions - statsAnsweredQuestions %></p>
                        <p class="text-sm text-yellow-600">Unanswered</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-gray-50 rounded-lg">
                        <p class="text-2xl font-bold text-gray-700"><%= bookmarkedQuestions %></p>
                        <p class="text-sm text-gray-600">Bookmarked</p>
                    </div>
                </div>

                <!-- Accuracy and Precision Section -->
                <div class="mt-6 border-t border-gray-200 pt-4">
                    <h3 class="text-lg font-semibold mb-3">Performance Metrics</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-indigo-50 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-md font-medium text-indigo-800">Accuracy</h4>
                                <span class="text-xl font-bold text-indigo-700"><%= accuracy.toFixed(2) %>%</span>
                            </div>
                            <p class="text-xs text-indigo-600">Percentage of all questions answered correctly (<%= correctAnswers %>/<%= statsTotalQuestions %>)</p>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                <div class="bg-indigo-600 h-2.5 rounded-full" style="width: <%= Math.min(100, accuracy) %>%"></div>
                            </div>
                        </div>

                        <div class="bg-green-50 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-md font-medium text-green-800">Precision</h4>
                                <span class="text-xl font-bold text-green-700"><%= precision.toFixed(2) %>%</span>
                            </div>
                            <p class="text-xs text-green-600">Percentage of attempted questions answered correctly (<%= correctAnswers %>/<%= statsAnsweredQuestions %>)</p>
                            <div class="w-full bg-green-100 rounded-full h-2.5 mt-2">
                                <div class="bg-green-600 h-2.5 rounded-full" style="width: <%= Math.min(100, precision) %>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Results Details with Tabs -->
        <div class="bg-white rounded-lg shadow-md p-6" x-data="{ activeTab: 0 }">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Answers & Solutions</h2>

            <% if (sections && sections.length > 0) { %>
                <!-- Section Tabs -->
                <div class="border-b border-gray-200 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <% sections.forEach((section, index) => { %>
                            <li class="mr-2">
                                <button
                                    @click="activeTab = <%= index %>"
                                    :class="{ 'tab-active': activeTab === <%= index %> }"
                                    class="inline-block p-4 font-medium text-gray-600 hover:text-indigo-600 focus:outline-none">
                                    Section <%= index + 1 %>: <%= section.name %>
                                </button>
                            </li>
                        <% }) %>
                    </ul>
                </div>

                <!-- Section Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <% sections.forEach((section, index) => { %>
                        <%
                        // Calculate section statistics
                        let sectionQuestions = section.questions.length;
                        let sectionAttempted = 0;
                        let sectionCorrect = 0;
                        let sectionMarksObtained = 0;
                        let sectionMarksPossible = 0;

                        section.questions.forEach(question => {
                            if (question.user_answer) {
                                sectionAttempted++;
                            }
                            if (question.is_correct === true) {
                                sectionCorrect++;
                                sectionMarksObtained += parseFloat(question.marks) || 1.00;
                            } else if (question.user_answer && question.is_correct === false) {
                                // Deduct negative marks for incorrect answers
                                sectionMarksObtained -= parseFloat(question.negative_marks) || 0.00;
                            }
                            sectionMarksPossible += parseFloat(question.marks) || 1.00;
                        });

                        // Calculate percentages
                        let attemptPercentage = sectionQuestions > 0 ? (sectionAttempted / sectionQuestions) * 100 : 0;
                        let correctPercentage = sectionAttempted > 0 ? (sectionCorrect / sectionAttempted) * 100 : 0;
                        let marksPercentage = sectionMarksPossible > 0 ? (sectionMarksObtained / sectionMarksPossible) * 100 : 0;
                        marksPercentage = Math.max(0, marksPercentage); // Ensure not negative
                        %>

                        <div x-show="activeTab === <%= index %>" class="bg-white rounded-lg shadow p-4">
                            <h3 class="text-lg font-semibold mb-2">Section <%= index + 1 %>: <%= section.name %> Statistics</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600">Questions:</p>
                                    <p class="font-medium"><%= sectionAttempted %>/<%= sectionQuestions %> (<%= attemptPercentage.toFixed(2) %>%)</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Correct:</p>
                                    <p class="font-medium"><%= sectionCorrect %>/<%= sectionAttempted %> (<%= correctPercentage.toFixed(2) %>%)</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Marks:</p>
                                    <p class="font-medium"><%= sectionMarksObtained.toFixed(2) %>/<%= sectionMarksPossible.toFixed(2) %> (<%= marksPercentage.toFixed(2) %>%)</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Status:</p>
                                    <p class="font-medium">
                                        <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full <%= marksPercentage >= 60 ? 'bg-green-100 text-green-800' : marksPercentage >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                            <%= marksPercentage >= 60 ? 'Good' : marksPercentage >= 40 ? 'Average' : 'Needs Improvement' %>
                                        </span>
                                    </p>
                                </div>
                            </div>

                            <!-- Section Accuracy and Precision -->
                            <div class="mt-4 pt-3 border-t border-gray-200">
                                <h4 class="text-md font-medium mb-2">Performance Metrics</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Accuracy:</p>
                                        <p class="font-medium">
                                            <% const sectionAccuracy = sectionQuestions > 0 ? (sectionCorrect / sectionQuestions) * 100 : 0; %>
                                            <%= sectionAccuracy.toFixed(2) %>% (<%= sectionCorrect %>/<%= sectionQuestions %>)
                                        </p>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                            <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= Math.min(100, sectionAccuracy) %>%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-600">Precision:</p>
                                        <p class="font-medium">
                                            <% const sectionPrecision = sectionAttempted > 0 ? (sectionCorrect / sectionAttempted) * 100 : 0; %>
                                            <%= sectionPrecision.toFixed(2) %>% (<%= sectionCorrect %>/<%= sectionAttempted %>)
                                        </p>
                                        <div class="w-full bg-green-100 rounded-full h-2 mt-1">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: <%= Math.min(100, sectionPrecision) %>%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% }) %>
                </div>

                <!-- Section Content -->
                <div>
                    <% sections.forEach((section, sectionIndex) => { %>
                        <div x-show="activeTab === <%= sectionIndex %>" class="section-results">
                            <div class="question-container max-h-[600px] overflow-y-auto pr-2 space-y-6">
                                <% section.questions.forEach((question, questionIndex) => { %>
                                    <div class="question-result bg-gray-50 p-4 rounded-md">
                                        <div class="flex flex-col mb-2">
                                            <div class="flex justify-between items-center">
                                                <div class="flex items-center">
                                                    <h4 class="text-md font-medium text-gray-800 mr-2">
                                                        Question <%= sectionIndex * section.questions.length + questionIndex + 1 %>
                                                    </h4>
                                                    <span class="text-xs text-gray-500">
                                                        (<%= parseFloat(question.marks).toFixed(2) %> marks<% if (parseFloat(question.negative_marks) > 0) { %>, -<%= parseFloat(question.negative_marks).toFixed(2) %> negative<% } %>)
                                                    </span>
                                                </div>
                                                <div class="flex items-center">
                                                    <% if (question.is_correct === true) { %>
                                                        <span class="text-green-600 font-medium mr-2">Correct (+<%= parseFloat(question.marks).toFixed(2) %>)</span>
                                                    <% } else if (!question.user_answer) { %>
                                                        <span class="text-yellow-600 font-medium mr-2">Unattempted (0.00)</span>
                                                    <% } else { %>
                                                        <span class="text-red-600 font-medium mr-2">Incorrect (-<%= parseFloat(question.negative_marks).toFixed(2) %>)</span>
                                                    <% } %>
                                                    <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full <%= question.is_correct === true ? 'bg-green-100 text-green-800' : !question.user_answer ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                                        <%= question.is_correct === true ? 'Correct' : !question.user_answer ? 'Unattempted' : 'Incorrect' %>
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Question Type and Categories -->
                                            <div class="flex flex-wrap gap-1 mt-2">
                                                <!-- Question Type Badge -->
                                                <% if (question.question_type === 'multiple_choice') { %>
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Multiple Choice</span>
                                                <% } else if (question.question_type === 'true_false') { %>
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">True/False</span>
                                                <% } else if (question.question_type === 'fill_up') { %>
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Fill in the Blank</span>
                                                <% } else if (question.question_type === 'essay') { %>
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Essay</span>
                                                <% } else { %>
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"><%= question.question_type %></span>
                                                <% } %>

                                                <!-- Category Badges -->
                                                <% if (question.category_names) { %>
                                                    <%
                                                    const categoryColors = {
                                                        'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                                                        'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                                        'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                                        'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' }
                                                    };

                                                    const categoryNamesArray = question.category_names.split(',');
                                                    categoryNamesArray.forEach(categoryName => {
                                                        const category = categoryName.trim();
                                                        const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                                                    %>
                                                        <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                                            <%= category %>
                                                        </span>
                                                    <% }); %>
                                                <% } %>
                                            </div>
                                        </div>

                                        <% if (question.essay_id) { %>
                                        <!-- Essay Content -->
                                        <div class="mb-6 p-4 bg-gray-100 border border-gray-200 rounded-lg">
                                            <h4 class="text-lg font-semibold text-gray-800 mb-2"><%= question.essay_title %></h4>
                                            <div class="prose max-w-none mb-4 text-gray-700 overflow-y-auto max-h-64">
                                                <%- question.essay_content.replace(/\n/g, '<br>') %>
                                            </div>
                                            <% if (question.essay_pdf_path) { %>
                                            <div class="mt-2">
                                                <a href="<%= question.essay_pdf_path %>" target="_blank" class="text-blue-600 hover:text-blue-800 flex items-center">
                                                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                                    </svg>
                                                    View PDF Version
                                                </a>
                                            </div>
                                            <% } %>
                                        </div>
                                        <% } %>
                                        <p class="text-gray-800 mb-4"><%- question.question_text.replace(/\n/g, '<br>') %></p>

                                        <% if (question.image_path) { %>
                                        <div class="mb-4">
                                            <img src="<%= question.image_path %>" alt="Question image" class="question-image">
                                        </div>
                                        <% } %>

                                        <div class="mb-3">
                                            <p class="text-sm font-medium text-gray-600">Your Answer:</p>
                                            <p class="<%= question.is_correct === true ? 'text-green-600' : !question.user_answer ? 'text-yellow-600' : 'text-red-600' %> font-medium">
                                                <%= question.user_answer || 'No answer provided' %>
                                            </p>
                                        </div>

                                        <div>
                                            <p class="text-sm font-medium text-gray-600">Correct Answer:</p>
                                            <p class="text-green-600 font-medium">
                                                <% if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') { %>
                                                    <%
                                                        let correctAnswerText = question.correct_answer;
                                                        if (question.options && question.options.length > 0) {
                                                            const correctOption = question.options.find(o => o.is_correct === 1);
                                                            if (correctOption) {
                                                                correctAnswerText = correctOption.option_text;
                                                            }
                                                        }
                                                    %>
                                                    <%= correctAnswerText %>
                                                <% } else { %>
                                                    <%= question.correct_answer %>
                                                <% } %>
                                            </p>
                                        </div>

                                        <% if (question.solution_text || question.solution_image_path) { %>
                                            <div class="mt-3 pt-3 border-t border-gray-200">
                                                <p class="text-sm font-medium text-gray-600">Solution:</p>
                                                <% if (question.solution_text) { %>
                                                <p class="text-gray-800 whitespace-pre-line"><%= question.solution_text %></p>
                                                <% } %>
                                                <% if (question.solution_image_path) { %>
                                                <div class="mt-2">
                                                    <img src="<%= question.solution_image_path %>" alt="Solution image" class="solution-image">
                                                </div>
                                                <% } %>
                                            </div>
                                        <% } %>
                                    </div>
                                <% }) %>
                            </div>
                        </div>
                    <% }) %>
                </div>
            <% } else { %>
                <p class="text-gray-500">No questions found for this test.</p>
            <% } %>
        </div>
    </div>