<!DOCTYPE html>
<html lang="<%= currentLanguage %>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <%= __('app.name') %></title>
    <link rel="stylesheet" href="/styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="/css/session-timer.css">
    <link rel="stylesheet" href="/css/image-styles.css">
    <script src="/js/session-timer.js"></script>
    <script src="/js/chat-icon.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Results Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-4"><%= attempt.exam_name %> - <%= __('tests.results') %></h1>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-4">
                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium"><%= __('tests.dateTaken') %></p>
                    <p class="text-gray-800 font-semibold">
                        <%= formatDateTime(attempt.attempt_date) %>
                    </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium"><%= __('tests.status') %></p>
                    <p class="font-semibold
                        <%= attempt.status === 'completed' ? 'text-green-600' :
                           attempt.status === 'failed' ? 'text-red-600' :
                           attempt.status === 'in_progress' ? 'text-blue-600' : 'text-gray-800' %>">
                        <%= attempt.status.replace('_', ' ').charAt(0).toUpperCase() + attempt.status.replace('_', ' ').slice(1) %>
                    </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-md">
                    <p class="text-gray-500 text-sm font-medium"><%= __('tests.scoreLabel') %></p>
                    <p class="text-gray-800 font-semibold">
                        <%= attempt.score ? Math.round(attempt.score) + '%' : 'N/A' %>
                        <% if (attempt.passing_marks) { %>
                            <span class="text-sm text-gray-500">(<%= __('tests.passingMarks') %>: <%= attempt.passing_marks %>%)</span>
                        <% } %>
                    </p>
                </div>
            </div>

            <div class="mt-4">
                <a href="/tests" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded inline-block">
                    <%= __('tests.backToTests') %>
                </a>
            </div>
        </div>

        <!-- Results Details -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4"><%= __('tests.answersAndSolutions') %></h2>

            <% if (sections && sections.length > 0) { %>
                <div class="space-y-8">
                    <% sections.forEach((section, sectionIndex) => { %>
                        <div class="section-results">
                            <h3 class="text-lg font-medium text-gray-800 mb-3 pb-2 border-b">
                                <%= __('tests.section') %> <%= sectionIndex + 1 %>: <%= section.name %>
                            </h3>

                            <div class="space-y-6">
                                <% section.questions.forEach((question, questionIndex) => { %>
                                    <div class="question-result bg-gray-50 p-4 rounded-md">
                                        <div class="flex justify-between">
                                            <h4 class="text-md font-medium text-gray-800 mb-2">
                                                <%= __('tests.question') %> <%= sectionIndex * section.questions.length + questionIndex + 1 %>
                                            </h4>
                                            <span class="<%= question.is_correct === true ? 'text-green-600' : !question.user_answer ? 'text-yellow-600' : 'text-red-600' %> font-medium">
                                                <%= question.is_correct === true ? __('tests.correct') : !question.user_answer ? __('tests.unattempted') : __('tests.incorrect') %>
                                            </span>
                                        </div>

                                        <p class="text-gray-800 mb-4"><%- question.question_text.replace(/\n/g, '<br>') %></p>

                                        <div class="mb-3">
                                            <p class="text-sm font-medium text-gray-600"><%= __('tests.yourAnswer') %>:</p>
                                            <p class="<%= question.is_correct === true ? 'text-green-600' : question.is_correct === false ? 'text-red-600' : 'text-gray-600' %> font-medium">
                                                <%= question.user_answer || __('tests.unattempted') %>
                                            </p>
                                        </div>

                                        <div>
                                            <p class="text-sm font-medium text-gray-600"><%= __('tests.correctAnswer') %>:</p>
                                            <p class="text-green-600 font-medium">
                                                <% if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') { %>
                                                    <%
                                                        let correctAnswerText = question.correct_answer;
                                                        if (question.options && question.options.length > 0) {
                                                            const correctOption = question.options.find(o => o.is_correct === 1);
                                                            if (correctOption) {
                                                                correctAnswerText = correctOption.option_text;
                                                            }
                                                        }
                                                    %>
                                                    <%= correctAnswerText %>
                                                <% } else { %>
                                                    <%= question.correct_answer %>
                                                <% } %>
                                            </p>
                                        </div>

                                        <% if (question.solution_text) { %>
                                            <div class="mt-3 pt-3 border-t border-gray-200">
                                                <p class="text-sm font-medium text-gray-600"><%= __('tests.explanation') %>:</p>
                                                <p class="text-gray-800"><%= question.solution_text %></p>
                                            </div>
                                        <% } %>
                                    </div>
                                <% }) %>
                            </div>
                        </div>
                    <% }) %>
                </div>
            <% } else { %>
                <p class="text-gray-500"><%= __('tests.noQuestionsFound') %></p>
            <% } %>
        </div>

        <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 mb-6">
            <!-- Left column - basic info -->
            <div class="w-full md:w-1/2 bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4"><%= __('tests.testSummary') %></h2>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-gray-600"><%= __('tests.completionDate') %>:</p>
                        <p class="font-medium"><%= formatDateTime(attempt.end_time || attempt.attempt_date) %></p>
                    </div>
                    <div>
                        <p class="text-gray-600">Duration:</p>
                        <p class="font-medium">
                            <%
                            let duration = 'N/A';
                            if (attempt.start_time && attempt.end_time) {
                                const start = new Date(attempt.start_time);
                                const end = new Date(attempt.end_time);
                                const diff = Math.floor((end - start) / 1000);
                                const hrs = Math.floor(diff / 3600);
                                const mins = Math.floor((diff % 3600) / 60);
                                const secs = diff % 60;
                                duration = `${hrs}h ${mins}m ${secs}s`;
                            }
                            %>
                            <%= duration %>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-600">Score:</p>
                        <p class="font-medium text-xl">
                            <% if (attempt.total_score !== null && attempt.total_score !== undefined) { %>
                                <span class="<%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'text-green-600' : 'text-red-600' %>">
                                    <%= typeof attempt.total_score === 'number' ? attempt.total_score.toFixed(2) : (typeof attempt.total_score === 'string' ? parseFloat(attempt.total_score).toFixed(2) : 'N/A') %>%
                                </span>
                                <% if (attempt.total_marks_obtained !== null && attempt.total_marks_obtained !== undefined && attempt.total_marks_possible !== null && attempt.total_marks_possible !== undefined) { %>
                                    <span class="text-gray-600 text-sm ml-2">
                                        (<%= parseFloat(attempt.total_marks_obtained).toFixed(2) %>/<%= parseFloat(attempt.total_marks_possible).toFixed(2) %>)
                                    </span>
                                <% } %>
                            <% } else { %>
                                <span class="text-gray-600">Not calculated</span>
                            <% } %>
                        </p>
                    </div>
                    <%
                    // Calculate section statistics
                    let totalAttemptedQuestions = 0;
                    let totalSectionQuestions = 0;
                    let totalSectionMarksObtained = 0;
                    let totalSectionMarksPossible = 0;

                    sections.forEach(section => {
                        section.questions.forEach(question => {
                            totalSectionQuestions++;
                            if (question.user_answer) {
                                totalAttemptedQuestions++;
                            }
                            if (question.is_correct === true) {
                                totalSectionMarksObtained += parseFloat(question.marks) || 1.00;
                            } else if (question.user_answer && question.is_correct === false) {
                                // Deduct negative marks for incorrect answers
                                totalSectionMarksObtained -= parseFloat(question.negative_marks) || 0.00;
                            }
                            totalSectionMarksPossible += parseFloat(question.marks) || 1.00;
                        });
                    });

                    // Calculate percentages
                    let attemptPercentage = totalSectionQuestions > 0 ? (totalAttemptedQuestions / totalSectionQuestions) * 100 : 0;
                    let totalSectionMarksPercentage = totalSectionMarksPossible > 0 ?
                        (totalSectionMarksObtained / totalSectionMarksPossible) * 100 : 0;
                    totalSectionMarksPercentage = Math.max(0, totalSectionMarksPercentage); // Ensure not negative
                    %>

                    <div>
                        <p class="text-gray-600">Attempt Percentage:</p>
                        <p class="font-medium text-xl">
                            <span class="text-blue-600">
                                <%= attemptPercentage.toFixed(2) %>%
                            </span>
                            <span class="text-gray-600 text-sm ml-2">
                                (<%= totalAttemptedQuestions %>/<%= totalSectionQuestions %>)
                            </span>
                        </p>
                    </div>

                    <div>
                        <p class="text-gray-600">Section Marks:</p>
                        <p class="font-medium text-xl">
                            <span class="<%= totalSectionMarksPercentage >= parseFloat(attempt.passing_marks) ? 'text-green-600' : 'text-red-600' %>">
                                <%= totalSectionMarksPercentage.toFixed(2) %>%
                            </span>
                            <span class="text-gray-600 text-sm ml-2">
                                (<%= totalSectionMarksObtained.toFixed(2) %>/<%= totalSectionMarksPossible.toFixed(2) %>)
                            </span>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-600">Result:</p>
                        <p class="font-medium">
                            <% if (attempt.status === 'completed') { %>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full <%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                    <%= parseFloat(attempt.total_score) >= parseFloat(attempt.passing_marks) ? 'Passed' : 'Failed' %>
                                </span>
                            <% } else { %>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Incomplete
                                </span>
                            <% } %>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Right column - quick stats -->
            <div class="w-full md:w-1/2 bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Question Summary</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <%
                        // Calculate question statistics
                        let statsTotalQuestions = 0;
                        let statsAnsweredQuestions = 0;
                        let correctAnswers = 0;
                        let bookmarkedQuestions = 0;

                        sections.forEach(section => {
                            section.questions.forEach(question => {
                                statsTotalQuestions++;

                                if (question.user_answer) {
                                    statsAnsweredQuestions++;

                                    // Check if answer is correct based on the is_correct flag
                                    if (question.is_correct === true) {
                                        correctAnswers++;
                                    }
                                }

                                // Use a safe way to check for bookmarks
                                try {
                                    if (question.hasOwnProperty('is_bookmarked') && question.is_bookmarked) {
                                        bookmarkedQuestions++;
                                    }
                                } catch (e) {
                                    // Ignore errors if is_bookmarked doesn't exist
                                }
                            });
                        });
                    %>
                    <div class="text-center px-2 py-3 bg-blue-50 rounded-lg">
                        <p class="text-2xl font-bold text-blue-700"><%= statsTotalQuestions %></p>
                        <p class="text-sm text-blue-600">Total Questions</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-purple-50 rounded-lg">
                        <p class="text-2xl font-bold text-purple-700"><%= statsAnsweredQuestions %></p>
                        <p class="text-sm text-purple-600">Answered</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-green-50 rounded-lg">
                        <p class="text-2xl font-bold text-green-700"><%= correctAnswers %></p>
                        <p class="text-sm text-green-600">Correct</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-red-50 rounded-lg">
                        <p class="text-2xl font-bold text-red-700"><%= statsAnsweredQuestions - correctAnswers %></p>
                        <p class="text-sm text-red-600">Incorrect</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-yellow-50 rounded-lg">
                        <p class="text-2xl font-bold text-yellow-700"><%= statsTotalQuestions - statsAnsweredQuestions %></p>
                        <p class="text-sm text-yellow-600">Unanswered</p>
                    </div>
                    <div class="text-center px-2 py-3 bg-gray-50 rounded-lg">
                        <p class="text-2xl font-bold text-gray-700"><%= bookmarkedQuestions %></p>
                        <p class="text-sm text-gray-600">Bookmarked</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>