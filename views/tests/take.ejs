<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/image-styles.css">
    <style>
        .question-palette .question-btn {
            transition: all 0.2s;
        }
        .question-palette .answered {
            background-color: #10B981;
            color: white;
        }
        .question-palette .bookmarked {
            border: 2px solid #F59E0B;
        }
        .question-palette .current {
            border: 2px solid #3B82F6;
        }
        .question-palette .unanswered {
            background-color: #F3F4F6;
            border: 1px solid #D1D5DB;
        }
        .section-tab.active {
            background-color: #F3F4F6;
            border-bottom: 2px solid #3B82F6;
        }
        #save-indicator {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        #save-indicator.show {
            opacity: 1;
        }
        .mcq-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        .mcq-option {
            padding: 1rem;
            border: 1px solid #E5E7EB;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .mcq-option:hover {
            background-color: #F3F4F6;
        }
        .mcq-option.selected {
            background-color: #EFF6FF;
            border-color: #3B82F6;
        }
        .section-title {
            font-weight: 600;
            color: #4B5563;
            margin-bottom: 0.5rem;
        }
        .section-questions {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .section-questions:last-child {
            margin-bottom: 0;
        }
        #fullscreen-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 9999;
        }
        #exit-fullscreen {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 10000;
            background-color: #EF4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" oncontextmenu="return false;">
    <!-- Navigation -->
    <nav class="bg-indigo-600 text-white shadow-md" id="main-nav">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="/" class="text-xl font-bold">Meritorious EP</a>
            <div class="flex items-center space-x-4">
                <div class="text-sm mr-4">
                    Time Remaining: <span id="timer" class="font-bold">--:--:--</span>
                </div>
                <div class="text-sm">
                    <%= user.username %>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Test Content (Left) -->
        <div class="lg:col-span-3">
            <!-- Test Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2"><%= exam.exam_name %></h1>
                <div class="flex justify-between items-center">
                    <p class="text-gray-600">
                        <span class="font-medium">Duration:</span> <%= exam.duration %> minutes
                    </p>
                    <p class="text-gray-600">
                        <span class="font-medium">Passing Score:</span> <%= exam.passing_marks %>%
                    </p>
                </div>
            </div>

            <!-- Section Tabs -->
            <div class="bg-white rounded-lg shadow-md mb-6 overflow-x-auto">
                <div class="flex space-x-1 border-b p-2">
                    <% sections.forEach((section, index) => { %>
                        <button class="section-tab px-4 py-2 text-sm font-medium text-gray-700 whitespace-nowrap <%= index === 0 ? 'active' : '' %>"
                                data-section="<%= index %>">
                            <%= section.name %>
                        </button>
                    <% }) %>
                </div>
            </div>

            <!-- Question Area -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div id="question-container">
                    <% sections.forEach((section, sectionIndex) => { %>
                        <div class="section-content <%= sectionIndex === 0 ? '' : 'hidden' %>" data-section="<%= sectionIndex %>">
                            <% section.questions.forEach((question, questionIndex) => { %>
                                <div class="question-item <%= (sectionIndex === 0 && questionIndex === 0) ? '' : 'hidden' %>"
                                     data-section="<%= sectionIndex %>"
                                     data-question="<%= questionIndex %>"
                                     data-question-id="<%= question.question_id %>">

                                    <div class="flex justify-between items-start mb-4">
                                        <h3 class="text-lg font-semibold text-gray-800">
                                            Question <%= sectionIndex * section.questions.length + questionIndex + 1 %>
                                        </h3>
                                        <button class="bookmark-btn p-1 rounded <%= question.is_bookmarked ? 'text-yellow-500' : 'text-gray-400' %>"
                                                data-question-id="<%= question.question_id %>">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="<%= question.is_bookmarked ? 'currentColor' : 'none' %>" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                            </svg>
                                        </button>
                                    </div>

                                    <div class="mb-6">
                                        <p class="text-gray-800 mb-4"><%- question.question_text.replace(/\n/g, '<br>') %></p>
                                        <% if (question.image_path) { %>
                                            <div class="mt-2">
                                                <img src="<%= question.image_path %>" alt="Question Image" class="question-image">
                                            </div>
                                        <% } %>
                                        <% if (question.metadata && question.metadata.question_image_url) { %>
                                            <div class="mt-2">
                                                <img src="<%= question.metadata.question_image_url %>" alt="Question Image" class="question-image">
                                            </div>
                                        <% } %>

                                        <% if (question.question_type === 'mcq') { %>
                                            <div class="mcq-grid">
                                                <% for(let i = 0; i < 4; i++) { %>
                                                    <div class="mcq-option <%= question.user_answer === i.toString() ? 'selected' : '' %>"
                                                         data-value="<%= i %>">
                                                        <div class="flex items-center">
                                                            <input type="radio" id="option<%= question.question_id %>_<%= i %>"
                                                                   name="question<%= question.question_id %>"
                                                                   value="<%= i %>"
                                                                   <%= question.user_answer === i.toString() ? 'checked' : '' %>
                                                                   class="answer-radio hidden">
                                                            <label for="option<%= question.question_id %>_<%= i %>"
                                                                   class="ml-2 block text-gray-700 cursor-pointer">
                                                                Option <%= i + 1 %>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <% } %>
                                            </div>
                                        <% } else if (question.question_type === 'true_false') { %>
                                            <div class="space-y-2">
                                                <div class="flex items-center">
                                                    <input type="radio" id="true<%= question.question_id %>"
                                                           name="question<%= question.question_id %>"
                                                           value="true"
                                                           <%= question.user_answer === 'true' ? 'checked' : '' %>
                                                           class="answer-radio h-4 w-4 text-indigo-600">
                                                    <label for="true<%= question.question_id %>"
                                                           class="ml-2 block text-gray-700">
                                                        True
                                                    </label>
                                                </div>
                                                <div class="flex items-center">
                                                    <input type="radio" id="false<%= question.question_id %>"
                                                           name="question<%= question.question_id %>"
                                                           value="false"
                                                           <%= question.user_answer === 'false' ? 'checked' : '' %>
                                                           class="answer-radio h-4 w-4 text-indigo-600">
                                                    <label for="false<%= question.question_id %>"
                                                           class="ml-2 block text-gray-700">
                                                        False
                                                    </label>
                                                </div>
                                            </div>
                                        <% } else { %>
                                            <div>
                                                <textarea id="answer<%= question.question_id %>"
                                                          class="answer-text w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                          rows="3"><%= question.user_answer %></textarea>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            <% }) %>
                        </div>
                    <% }) %>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between mt-8">
                    <button id="prev-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded">
                        Previous
                    </button>
                    <div id="save-indicator" class="text-green-500 text-sm font-medium flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Answer saved
                    </div>
                    <button id="next-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
                        Next
                    </button>
                </div>
            </div>
        </div>

        <!-- Question Palette (Right) -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Question Palette</h2>

                <!-- Legend -->
                <div class="flex flex-wrap justify-between mb-4 text-xs">
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 bg-gray-200 border border-gray-300 mr-1"></div>
                        <span>Unanswered</span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 bg-green-500 mr-1"></div>
                        <span>Answered</span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 border border-yellow-500 mr-1"></div>
                        <span>Bookmarked</span>
                    </div>
                </div>

                <!-- Palette -->
                <div class="space-y-4">
                    <% sections.forEach((section, sectionIndex) => { %>
                        <div>
                            <div class="section-title"><%= section.name %></div>
                            <div class="section-questions">
                                <% section.questions.forEach((question, qIndex) => { %>
                                    <button class="question-btn w-8 h-8 rounded-md text-xs flex items-center justify-center <%= question.user_answer ? 'answered' : 'unanswered' %> <%= question.is_bookmarked ? 'bookmarked' : '' %>"
                                            data-section="<%= sectionIndex %>"
                                            data-question="<%= qIndex %>"
                                            data-question-id="<%= question.question_id %>">
                                        <%= qIndex + 1 %>
                                    </button>
                                <% }) %>
                            </div>
                        </div>
                    <% }) %>
                </div>

                <!-- Submit Button -->
                <form id="submit-form" action="/tests/submit/<%= attemptId %>" method="POST">
                    <button type="submit" id="submit-test-btn" class="w-full mt-8 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                        Submit Test
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Fullscreen Overlay -->
    <div id="fullscreen-overlay"></div>
    <button id="exit-fullscreen" class="hidden">Exit Fullscreen</button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Variables
            const attemptId = "<%= attemptId %>";
            const testDuration = parseInt("<%= exam.duration || 60 %>");
            let currentSection = 0;
            let currentQuestion = 0;
            let saveTimeout;
            let isFullscreen = false;

            // Timer functionality
            setupTimer(testDuration);

            function setupTimer(duration) {
                // Calculate end time based on duration in minutes
                const now = new Date();
                const startTime = new Date("<%= new Date(exam.start_time || Date.now()).toISOString() %>");

                // Calculate remaining time - if start time exists, use that, otherwise use current time
                const elapsedMs = now - startTime;
                const durationMs = duration * 60 * 1000;
                let remainingMs = durationMs - elapsedMs;

                // If remaining time is negative, set to 0
                if (remainingMs < 0) remainingMs = 0;

                // Update timer immediately
                updateTimerDisplay(remainingMs);

                // Update timer every second
                const timerInterval = setInterval(function() {
                    remainingMs -= 1000;

                    if (remainingMs <= 0) {
                        clearInterval(timerInterval);
                        remainingMs = 0;
                        submitTest();
                    }

                    updateTimerDisplay(remainingMs);
                }, 1000);
            }

            function updateTimerDisplay(remainingMs) {
                const hours = Math.floor(remainingMs / (1000 * 60 * 60));
                const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

                const displayHours = String(hours).padStart(2, '0');
                const displayMinutes = String(minutes).padStart(2, '0');
                const displaySeconds = String(seconds).padStart(2, '0');

                document.getElementById('timer').textContent = `${displayHours}:${displayMinutes}:${displaySeconds}`;

                // Add visual warning when time is running low
                if (remainingMs < 5 * 60 * 1000) { // less than 5 minutes
                    document.getElementById('timer').classList.add('text-red-500');

                    if (remainingMs < 1 * 60 * 1000) { // less than 1 minute
                        document.getElementById('timer').classList.add('animate-pulse');
                    }
                }
            }

            // Fullscreen functionality
            function enterFullscreen() {
                const element = document.documentElement;
                if (element.requestFullscreen) {
                    element.requestFullscreen();
                } else if (element.webkitRequestFullscreen) {
                    element.webkitRequestFullscreen();
                } else if (element.msRequestFullscreen) {
                    element.msRequestFullscreen();
                }
                isFullscreen = true;
                document.getElementById('main-nav').style.display = 'none';
                document.getElementById('exit-fullscreen').classList.remove('hidden');
            }

            function exitFullscreen() {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                isFullscreen = false;
                document.getElementById('main-nav').style.display = 'block';
                document.getElementById('exit-fullscreen').classList.add('hidden');
            }

            // Enter fullscreen when test starts
            enterFullscreen();

            // Handle fullscreen exit button
            document.getElementById('exit-fullscreen').addEventListener('click', function() {
                if (confirm('Are you sure you want to exit fullscreen mode? This may affect your test experience.')) {
                    exitFullscreen();
                }
            });

            // Handle fullscreen change events
            document.addEventListener('fullscreenchange', function() {
                if (!document.fullscreenElement) {
                    isFullscreen = false;
                    document.getElementById('main-nav').style.display = 'block';
                    document.getElementById('exit-fullscreen').classList.add('hidden');
                }
            });

            document.addEventListener('webkitfullscreenchange', function() {
                if (!document.webkitFullscreenElement) {
                    isFullscreen = false;
                    document.getElementById('main-nav').style.display = 'block';
                    document.getElementById('exit-fullscreen').classList.add('hidden');
                }
            });

            document.addEventListener('MSFullscreenChange', function() {
                if (!document.msFullscreenElement) {
                    isFullscreen = false;
                    document.getElementById('main-nav').style.display = 'block';
                    document.getElementById('exit-fullscreen').classList.add('hidden');
                }
            });

            // Section Navigation
            const sectionTabs = document.querySelectorAll('.section-tab');
            const sectionContents = document.querySelectorAll('.section-content');

            sectionTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.section);
                    showSection(sectionIndex);
                });
            });

            function showSection(sectionIndex) {
                // Update tabs
                sectionTabs.forEach(tab => tab.classList.remove('active'));
                sectionTabs[sectionIndex].classList.add('active');

                // Update content
                sectionContents.forEach(content => content.classList.add('hidden'));
                sectionContents[sectionIndex].classList.remove('hidden');

                // Show first question of section
                currentSection = sectionIndex;
                currentQuestion = 0;
                showQuestion(currentSection, currentQuestion);
            }

            // Question Navigation
            const questionItems = document.querySelectorAll('.question-item');
            const questionBtns = document.querySelectorAll('.question-btn');

            function showQuestion(sectionIndex, questionIndex) {
                // Hide all questions
                questionItems.forEach(item => item.classList.add('hidden'));

                // Find question to show
                const questionToShow = document.querySelector(`.question-item[data-section="${sectionIndex}"][data-question="${questionIndex}"]`);

                if (questionToShow) {
                    questionToShow.classList.remove('hidden');

                    // Update current section/question
                    currentSection = sectionIndex;
                    currentQuestion = questionIndex;

                    // Update question buttons - add 'current' class to current question
                    questionBtns.forEach(btn => btn.classList.remove('current'));

                    const currentQuestionBtn = document.querySelector(`.question-btn[data-section="${sectionIndex}"][data-question="${questionIndex}"]`);
                    if (currentQuestionBtn) {
                        currentQuestionBtn.classList.add('current');
                    }
                }
            }

            // Question Palette Navigation
            questionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.section);
                    const questionIndex = parseInt(this.dataset.question);

                    // Show correct section
                    showSection(sectionIndex);

                    // Show question
                    showQuestion(sectionIndex, questionIndex);
                });
            });

            // Previous/Next buttons
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            prevBtn.addEventListener('click', function() {
                navigateQuestion(-1);
            });

            nextBtn.addEventListener('click', function() {
                navigateQuestion(1);
            });

            function navigateQuestion(direction) {
                let sections = Array.from(sectionContents);
                let newSection = currentSection;
                let newQuestion = currentQuestion + direction;

                const currentSectionQuestions = document.querySelectorAll(`.question-item[data-section="${currentSection}"]`);

                // Check if we need to change section
                if (newQuestion < 0) {
                    newSection--;
                    if (newSection < 0) {
                        newSection = sections.length - 1;
                    }

                    const newSectionQuestions = document.querySelectorAll(`.question-item[data-section="${newSection}"]`);
                    newQuestion = newSectionQuestions.length - 1;
                } else if (newQuestion >= currentSectionQuestions.length) {
                    newSection++;
                    if (newSection >= sections.length) {
                        newSection = 0;
                    }

                    newQuestion = 0;
                }

                showSection(newSection);
                showQuestion(newSection, newQuestion);
            }

            // Save answers functionality
            const answerRadios = document.querySelectorAll('.answer-radio');
            const answerTexts = document.querySelectorAll('.answer-text');
            const bookmarkBtns = document.querySelectorAll('.bookmark-btn');
            const mcqOptions = document.querySelectorAll('.mcq-option');

            // Function to show save indicator
            function showSaveIndicator() {
                const saveIndicator = document.getElementById('save-indicator');
                saveIndicator.classList.add('show');

                setTimeout(() => {
                    saveIndicator.classList.remove('show');
                }, 2000);
            }

            // Function to save answer
            function saveAnswer(questionId, answer, isBookmarked) {
                fetch('/tests/answer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attemptId: attemptId,
                        questionId: questionId,
                        answer: answer,
                        isBookmarked: isBookmarked
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSaveIndicator();

                        // Update question button to show answered status
                        const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                        if (questionBtn && answer) {
                            questionBtn.classList.remove('unanswered');
                            questionBtn.classList.add('answered');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error saving answer:', error);
                });
            }

            // Add event listeners for MCQ options
            mcqOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const questionId = this.closest('.question-item').dataset.questionId;
                    const value = this.dataset.value;
                    const radio = this.querySelector('input[type="radio"]');

                    // Update radio button
                    radio.checked = true;

                    // Update option styling
                    this.classList.add('selected');
                    this.closest('.mcq-grid').querySelectorAll('.mcq-option').forEach(opt => {
                        if (opt !== this) {
                            opt.classList.remove('selected');
                        }
                    });

                    // Save answer
                    const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`).classList.contains('text-yellow-500');
                    saveAnswer(questionId, value, isBookmarked);
                });
            });

            // Add event listeners for true/false radio buttons
            answerRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    const questionId = this.name.replace('question', '');
                    const value = this.value;

                    // Save answer
                    const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`).classList.contains('text-yellow-500');
                    saveAnswer(questionId, value, isBookmarked);

                    // Update question button to show answered status
                    const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                    if (questionBtn) {
                        questionBtn.classList.remove('unanswered');
                        questionBtn.classList.add('answered');
                    }
                });
            });

            // Add event listeners for text areas
            answerTexts.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    const questionId = this.id.replace('answer', '');

                    // Clear previous timeout
                    if (saveTimeout) {
                        clearTimeout(saveTimeout);
                    }

                    // Save after 1 second of inactivity
                    saveTimeout = setTimeout(() => {
                        const answer = this.value;
                        const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`).classList.contains('text-yellow-500');

                        saveAnswer(questionId, answer, isBookmarked);
                    }, 1000);
                });
            });

            // Add event listeners for bookmark buttons
            bookmarkBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const questionId = this.dataset.questionId;

                    // Toggle bookmark state
                    this.classList.toggle('text-yellow-500');
                    const isBookmarked = this.classList.contains('text-yellow-500');

                    // Update SVG fill
                    const svg = this.querySelector('svg');
                    if (isBookmarked) {
                        svg.setAttribute('fill', 'currentColor');
                    } else {
                        svg.setAttribute('fill', 'none');
                    }

                    // Get current answer
                    let answer = '';
                    const radio = document.querySelector(`input[name="question${questionId}"]:checked`);
                    if (radio) {
                        answer = radio.value;
                    } else {
                        const textarea = document.getElementById(`answer${questionId}`);
                        if (textarea) {
                            answer = textarea.value;
                        }
                    }

                    // Save answer with updated bookmark state
                    saveAnswer(questionId, answer, isBookmarked);

                    // Update question button to show bookmarked status
                    const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                    if (questionBtn) {
                        if (isBookmarked) {
                            questionBtn.classList.add('bookmarked');
                        } else {
                            questionBtn.classList.remove('bookmarked');
                        }
                    }
                });
            });

            // Function to submit test
            function submitTest() {
                const unansweredCount = document.querySelectorAll('.question-btn.unanswered').length;

                if (unansweredCount > 0) {
                    if (confirm(`You have ${unansweredCount} unanswered questions. Are you sure you want to submit the test?`)) {
                        document.getElementById('submit-form').submit();
                    }
                } else {
                    if (confirm('Are you sure you want to submit your test? This action cannot be undone.')) {
                        document.getElementById('submit-form').submit();
                    }
                }
            }

            // Handle test submission
            document.getElementById('submit-test-btn').addEventListener('click', function(e) {
                e.preventDefault();
                submitTest();
            });

            // Handle page refresh/close
            window.addEventListener('beforeunload', function(e) {
                if (document.querySelectorAll('.question-btn.answered').length > 0) {
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });

            // Initialize first question
            showQuestion(0, 0);
        });
    </script>
</body>
</html>