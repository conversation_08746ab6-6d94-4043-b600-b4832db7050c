<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/image-styles.css">
    <style>
        /* Fullscreen styles */
        :fullscreen {
            background-color: white;
        }
        :fullscreen #main-nav,
        :fullscreen .navbar,
        :fullscreen .header,
        :fullscreen nav,
        :fullscreen header {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        .document-fullscreen #main-nav,
        .document-fullscreen .navbar,
        .document-fullscreen .header,
        .document-fullscreen nav,
        .document-fullscreen header {
            display: none !important;
        }
        :-webkit-full-screen #main-nav,
        :-moz-full-screen #main-nav,
        :-ms-fullscreen #main-nav {
            display: none !important;
        }
        :fullscreen .container {
            padding-top: 1rem;
        }

        /* Floating timer for fullscreen mode */
        #floating-timer {
            display: none;
            position: fixed;
            top: 1rem;
            right: 1rem;
            color: white;
            z-index: 1000;
            font-weight: bold;
            width: 200px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Section tabs styling */
        .section-tabs-container {
            overflow-x: auto;
            scrollbar-width: thin;
            -webkit-overflow-scrolling: touch;
        }

        .section-tab {
            min-width: max-content;
            max-width: none;
            transition: all 0.2s ease;
        }

        /* Custom scrollbar for section tabs */
        .section-tabs-container::-webkit-scrollbar {
            height: 6px;
        }

        .section-tabs-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .section-tabs-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        .section-tabs-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        :fullscreen #floating-timer {
            display: block;
        }

        #timer {
            transition: all 0.3s ease;
        }
        :fullscreen #timer {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background-color: #1E40AF;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            z-index: 1000;
        }

        /* Question button styles - applied to both desktop and mobile */
        .question-btn {
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
            background-color: #E5E7EB !important; /* Gray background for all buttons by default */
            border: 1px solid #D1D5DB !important;
            color: #4B5563 !important;
        }
        .question-btn.answered {
            background-color: #10B981 !important;
            color: white !important;
            border: 1px solid #059669 !important;
        }
        .question-btn.bookmarked {
            border: 2px solid #F59E0B !important;
        }
        .question-btn.bookmarked::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 8px 8px 0;
            border-color: transparent #F59E0B transparent transparent;
        }
        .question-btn.current {
            background-color: white !important;
            border: 2px solid #3B82F6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
            color: #4B5563 !important;
        }

        /* Override for answered questions when they are selected */
        .question-btn.answered.current {
            background-color: #10B981 !important;
            color: white !important;
            border: 2px solid #3B82F6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }
        .section-tab.active {
            background-color: #F3F4F6;
            border-bottom: 2px solid #3B82F6;
        }
        #save-indicator {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        #save-indicator.show {
            opacity: 1;
        }
        .mcq-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        .mcq-option {
            padding: 1rem;
            border: 1px solid #E5E7EB;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        /* Special styles for true/false options */
        .tf-option {
            position: relative;
        }

        .tf-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .tf-option label {
            display: block;
            width: 100%;
            height: 100%;
            cursor: pointer;
            z-index: 1;
            position: relative;
        }
        .mcq-option:hover {
            background-color: #F3F4F6;
        }
        .mcq-option.selected {
            background-color: #EFF6FF;
            border-color: #3B82F6;
            position: relative;
        }
        .mcq-option.selected::after {
            content: '✓';
            position: absolute;
            top: 8px;
            right: 8px;
            color: #3B82F6;
            font-weight: bold;
        }
        /* Styles for multiple selection */
        .mcq-option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
        }
        .section-title {
            font-weight: 600;
            color: #4B5563;
            margin-bottom: 0.5rem;
        }
        .section-questions {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .section-questions.collapsed {
            display: none;
        }
        .toggle-icon {
            transition: transform 0.3s ease;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-questions:last-child {
            margin-bottom: 0;
        }
        #fullscreen-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 9999;
        }
        #exit-fullscreen {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 10000;
            background-color: #EF4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
        }

        /* Custom scrollbar styles */
        .question-palette::-webkit-scrollbar,
        .mobile-question-palette::-webkit-scrollbar,
        #mobile-palette-overlay .overflow-y-auto::-webkit-scrollbar {
            width: 8px;
        }

        .question-palette::-webkit-scrollbar-track,
        .mobile-question-palette::-webkit-scrollbar-track,
        #mobile-palette-overlay .overflow-y-auto::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .question-palette::-webkit-scrollbar-thumb,
        .mobile-question-palette::-webkit-scrollbar-thumb,
        #mobile-palette-overlay .overflow-y-auto::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        .question-palette::-webkit-scrollbar-thumb:hover,
        .mobile-question-palette::-webkit-scrollbar-thumb:hover,
        #mobile-palette-overlay .overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Mobile palette overlay styles */
        #mobile-palette-overlay {
            transition: all 0.3s ease-in-out;
        }

        #mobile-palette-overlay .bg-white {
            transition: transform 0.3s ease-in-out;
            transform: translateY(0);
        }

        #mobile-palette-overlay.hidden .bg-white {
            transform: translateY(100%);
        }

        /* Shake animation for the agreement checkbox */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake-animation {
            animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) both;
        }

        /* Disable text selection for the entire test content */
        .container {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        /* Allow text selection only in textarea for essay questions */
        textarea {
            user-select: text !important;
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" oncontextmenu="return false;" oncopy="return false;" oncut="return false;" onpaste="return false;">
    <!-- Flash Messages -->
    <% if (locals.flashSuccess) { %>
        <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50" role="alert">
            <strong class="font-bold">Success!</strong>
            <span class="block sm:inline"><%= flashSuccess %></span>
            <button class="absolute top-0 right-0 mt-1 mr-1 text-green-700" onclick="this.parentElement.style.display='none'">
                <span class="text-xl">×</span>
            </button>
        </div>
    <% } %>
    <% if (locals.flashError) { %>
        <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline"><%= flashError %></span>
            <button class="absolute top-0 right-0 mt-1 mr-1 text-red-700" onclick="this.parentElement.style.display='none'">
                <span class="text-xl">×</span>
            </button>
        </div>
    <% } %>

    <!-- Start Test Overlay -->
    <div id="start-test-overlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-lg flex items-center justify-center z-[9999]">
        <div class="bg-white p-8 rounded-lg shadow-2xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto relative z-[10000]">
            <!-- Test Header with Duration and Marks -->
            <div class="flex justify-between items-center mb-6 border-b pb-4">
                <div class="text-lg font-medium">ਸਮਾਂ : <%= exam.duration %> ਮਿੰਟ</div>
                <div class="text-lg font-medium">ਕੁੱਲ ਅੰਕ : 150</div>
                <div class="text-lg font-medium">Time : <%= exam.duration %> minutes</div>
                <div class="text-lg font-medium">Max. Marks : 150</div>
            </div>

            <!-- Instructions Title -->
            <div class="flex justify-between mb-4">
                <% if (attempts && attempts.length > 0 && attempts[0].start_time) { %>
                    <h2 class="text-xl font-bold text-gray-800">ਜਾਰੀ ਟੈਸਟ ਲਈ ਹਦਾਇਤਾਂ :</h2>
                    <h2 class="text-xl font-bold text-gray-800">Instructions for continued test :</h2>
                <% } else { %>
                    <h2 class="text-xl font-bold text-gray-800">ਉਮੀਦਵਾਰਾਂ ਲਈ ਹਦਾਇਤਾਂ :</h2>
                    <h2 class="text-xl font-bold text-gray-800">Instructions for candidates :</h2>
                <% } %>
            </div>

            <!-- Two-column Instructions -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Punjabi Instructions (Left Column) -->
                <div class="prose max-w-none">
                    <%
                    // Split instructions by newlines and filter out empty lines
                    const instructionsArray = exam.instructions ? exam.instructions.split('\n').filter(line => line.trim()) : [];
                    const midpoint = Math.ceil(instructionsArray.length / 2);
                    const punjabiInstructions = instructionsArray.slice(0, midpoint);
                    %>
                    <% punjabiInstructions.forEach((line, index) => { %>
                        <p class="mb-2"><%= index + 1 %>. <%= line %></p>
                    <% }) %>
                </div>

                <!-- English Instructions (Right Column) -->
                <div class="prose max-w-none">
                    <%
                    const englishInstructions = instructionsArray.slice(midpoint);
                    %>
                    <% englishInstructions.forEach((line, index) => { %>
                        <p class="mb-2"><%= index + 1 %>. <%= line %></p>
                    <% }) %>
                </div>
            </div>

            <!-- Additional Security Instructions -->
            <div class="border-t border-gray-200 pt-4 mt-4">
                <h3 class="text-lg font-semibold text-red-600 mb-2">Important Security Information:</h3>
                <ul class="list-disc pl-5 space-y-2 text-gray-700">
                    <li><strong>Fullscreen Mode:</strong> This test must be taken in fullscreen mode. If you exit fullscreen, you will have <span class="font-bold text-red-600">10 seconds</span> to return or your test will be automatically submitted.</li>
                    <li><strong>Tab Switching:</strong> Switching to another tab or application during the test is not allowed. If detected, you will have <span class="font-bold text-red-600">10 seconds</span> to return or your test will be automatically submitted.</li>
                    <li><strong>Pause/Exit Test:</strong> If you need to pause or exit the test before completion, click the "Pause Test" or "Exit Test" button at the top of the page. Your progress will be saved and you can resume the test later.</li>

                </ul>
            </div>

            <div class="border-t pt-4 mt-4">
                <div class="flex items-center mb-4" id="agreement-container">
                    <input type="checkbox" id="agree-checkbox" class="h-4 w-4 text-indigo-600 mr-2" onchange="toggleStartButton(this)">
                    <label for="agree-checkbox" class="text-gray-700 cursor-pointer" id="agreement-label">I have read and agree to follow all the instructions</label>
                </div>

                <script>
                    // Function to toggle the start button state
                    function toggleStartButton(checkbox) {
                        console.log('Checkbox changed via inline handler, checked:', checkbox.checked);
                        const startButton = document.getElementById('start-test-btn');
                        if (startButton) {
                            // Store the checkbox state in a data attribute on the button
                            startButton.setAttribute('data-agreement-checked', checkbox.checked);
                            console.log('Set data-agreement-checked to:', checkbox.checked);
                        } else {
                            console.error('Start button not found in inline handler');
                        }
                    }
                </script>
                <div class="space-y-2">
                    <% if (attempts && attempts.length > 0 && attempts[0].start_time) { %>
                        <!-- Show Resume Test button for continued tests -->
                        <button id="start-test-btn" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200">
                            Resume Test
                        </button>
                    <% } else { %>
                        <!-- Show Start Test button for new tests -->
                        <button id="start-test-btn" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200">
                            Start Test
                        </button>
                    <% } %>
                    <a href="/tests" id="exit-test-btn" class="block text-center bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200">
                        Exit Test
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-indigo-600 text-white shadow-md" id="main-nav">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="/" class="text-xl font-bold">Meritorious EP</a>
            <div class="flex items-center">
                <!-- Pause button removed -->

                <div class="relative w-48 h-8">
                    <div class="absolute inset-0 bg-gray-300 rounded-md overflow-hidden">
                        <div id="timer-progress" class="bg-green-500 h-full rounded-md transition-all duration-1000" style="width: 100%"></div>
                    </div>
                    <div class="absolute inset-0 flex items-center justify-center text-sm font-bold">
                        <span id="timer" class="z-10 text-white drop-shadow-md">--:--:--</span>
                    </div>
                </div>

                <div class="text-sm ml-4">
                    <%= user.username %>
                </div>
            </div>
        </div>
    </nav>

    <!-- Floating timer for fullscreen mode -->
    <div id="floating-timer" class="flex items-center space-x-2">
        <!-- Pause button removed -->
        <div class="relative h-10 w-48">
            <div class="absolute inset-0 bg-gray-300 rounded-md overflow-hidden">
                <div id="floating-timer-progress" class="bg-green-500 h-full rounded-md transition-all duration-1000" style="width: 100%"></div>
            </div>
            <div class="absolute inset-0 flex items-center justify-center text-sm font-bold">
                <span id="floating-timer-text" class="z-10 text-white drop-shadow-md">--:--:--</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-6 pb-24 md:pb-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Test Content (Left) -->
        <div class="lg:col-span-3">
            <!-- Test Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-center mb-2">
                    <h1 class="text-2xl font-bold text-gray-800"><%= exam.exam_name %></h1>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-gray-600">
                        <span class="font-medium">Duration:</span> <%= exam.duration %> minutes
                    </p>
                    <p class="text-gray-600">
                        <span class="font-medium">Passing Score:</span> <%= exam.passing_marks %>%
                    </p>
                </div>
            </div>

            <!-- Desktop Instructions Button -->
            <% if (exam.instructions) { %>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 hidden md:block">
                <button id="desktop-instructions-btn" class="w-full bg-indigo-100 hover:bg-indigo-200 text-indigo-800 py-3 px-4 rounded flex justify-between items-center">
                    <span class="text-lg font-semibold">View Instructions</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Instructions and Palette Buttons -->
            <div class="md:hidden mb-6">
                <div class="grid grid-cols-2 gap-3">
                    <button id="mobile-instructions-btn" class="bg-indigo-100 hover:bg-indigo-200 text-indigo-800 py-2 px-4 rounded flex justify-between items-center">
                        <span class="font-medium">View Instructions</span>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                    <button id="mobile-palette-btn" class="bg-purple-100 hover:bg-purple-200 text-purple-800 py-2 px-4 rounded flex justify-between items-center">
                        <span class="font-medium">Question Palette</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </button>
                </div>
            </div>
            <% } %>

            <!-- Section Tabs -->
            <div class="bg-white rounded-lg shadow-md mb-6">
                <div class="flex space-x-1 border-b p-2 min-w-full section-tabs-container">
                    <% sections.forEach((section, index) => { %>
                        <button class="section-tab px-4 py-2 text-sm font-medium text-gray-700 whitespace-nowrap <%= index === 0 ? 'active' : '' %>"
                                data-section="<%= index %>" style="min-width: max-content; max-width: none;">
                            <%= section.name %>
                            <div class="flex flex-col mt-1 text-xs">
                                <!-- Question status counts -->
                                <div class="flex space-x-2 mb-1">
                                    <%
                                    let answeredCount = 0;
                                    let unansweredCount = 0;
                                    let bookmarkedCount = 0;

                                    section.questions.forEach(q => {
                                        if (q.user_answer) {
                                            answeredCount++;
                                        } else {
                                            unansweredCount++;
                                        }
                                        if (q.is_bookmarked) {
                                            bookmarkedCount++;
                                        }
                                    });
                                    %>
                                    <span class="bg-green-500 text-white px-1 rounded" title="Answered"><%= answeredCount %></span>
                                    <span class="bg-gray-300 text-gray-700 px-1 rounded" title="Unanswered"><%= unansweredCount %></span>
                                    <% if (bookmarkedCount > 0) { %>
                                        <span class="border border-yellow-500 text-yellow-600 px-1 rounded" title="Bookmarked"><%= bookmarkedCount %></span>
                                    <% } %>
                                </div>

                                <!-- Question type counts -->
                                <div class="flex space-x-2">
                                    <%
                                    let mcqCount = 0;
                                    let trueFalseCount = 0;
                                    let essayCount = 0;

                                    section.questions.forEach(q => {
                                        if (q.question_type === 'mcq' || q.question_type === 'multiple_choice') {
                                            mcqCount++;
                                        } else if (q.question_type === 'true_false') {
                                            trueFalseCount++;
                                        } else if (q.question_type === 'essay') {
                                            essayCount++;
                                        }
                                    });
                                    %>
                                    <% if (mcqCount > 0) { %>
                                        <span class="bg-blue-100 text-blue-800 px-1 rounded" title="Multiple Choice">MCQ: <%= mcqCount %></span>
                                    <% } %>
                                    <% if (trueFalseCount > 0) { %>
                                        <span class="bg-purple-100 text-purple-800 px-1 rounded" title="True/False">T/F: <%= trueFalseCount %></span>
                                    <% } %>
                                    <% if (essayCount > 0) { %>
                                        <span class="bg-pink-100 text-pink-800 px-1 rounded" title="Essay">Essay: <%= essayCount %></span>
                                    <% } %>
                                </div>
                            </div>
                        </button>
                    <% }) %>
                </div>
            </div>

            <!-- Question Area -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div id="question-container">
                    <% sections.forEach((section, sectionIndex) => { %>
                        <div class="section-content <%= sectionIndex === 0 ? '' : 'hidden' %>" data-section="<%= sectionIndex %>">
                            <% section.questions.forEach((question, questionIndex) => { %>
                                <div class="question-item <%= (sectionIndex === 0 && questionIndex === 0) ? '' : 'hidden' %>"
                                     data-section="<%= sectionIndex %>"
                                     data-question="<%= questionIndex %>"
                                     data-question-id="<%= question.question_id %>">

                                    <div class="flex justify-between items-start mb-4">
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-800">
                                                <%
                                                // Generate a consistent color based on section name
                                                const sectionColors = [
                                                    'bg-blue-100 text-blue-800',
                                                    'bg-green-100 text-green-800',
                                                    'bg-purple-100 text-purple-800',
                                                    'bg-pink-100 text-pink-800',
                                                    'bg-yellow-100 text-yellow-800',
                                                    'bg-indigo-100 text-indigo-800',
                                                    'bg-red-100 text-red-800',
                                                    'bg-orange-100 text-orange-800'
                                                ];
                                                const colorIndex = sectionIndex % sectionColors.length;
                                                const sectionColor = sectionColors[colorIndex];
                                                %>
                                                <span class="<%= sectionColor %> px-2 py-1 rounded-md text-sm mr-2"><%= section.section_name %></span>
                                                Question <%=
                                                    /* Calculate global question number */
                                                    (() => {
                                                        let count = questionIndex + 1;
                                                        for (let i = 0; i < sectionIndex; i++) {
                                                            count += sections[i].questions.length;
                                                        }
                                                        return count;
                                                    })()
                                                %>
                                            </h3>

                                            <!-- Category badges -->
                                            <% if (question.category_names) { %>
                                                <div class="flex flex-wrap gap-1 mt-2">
                                                    <%
                                                    const categoryColors = {
                                                        // Academic Subjects
                                                        'Mathematics': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Science': { bg: 'bg-green-100', text: 'text-green-800' },
                                                        'Chemistry': { bg: 'bg-emerald-100', text: 'text-emerald-800' },
                                                        'Biology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Physics': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Computer Science': { bg: 'bg-sky-100', text: 'text-sky-800' },
                                                        'Environmental Science': { bg: 'bg-lime-100', text: 'text-lime-800' },
                                                        'Astronomy': { bg: 'bg-violet-100', text: 'text-violet-800' },
                                                        'Geology': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'Statistics': { bg: 'bg-cyan-100', text: 'text-cyan-800' },
                                                        'Calculus': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Trigonometry': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Algebra': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Geometry': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                                        'World History': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'Ancient History': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Modern History': { bg: 'bg-rose-100', text: 'text-rose-800' },
                                                        'History': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Geography': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'Political Science': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Economics': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                                        'Sociology': { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800' },
                                                        'Psychology': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Philosophy': { bg: 'bg-slate-100', text: 'text-slate-800' },
                                                        'Literature': { bg: 'bg-cyan-100', text: 'text-cyan-800' },
                                                        'Grammar': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Vocabulary': { bg: 'bg-green-100', text: 'text-green-800' },
                                                        'Civics': { bg: 'bg-orange-100', text: 'text-orange-800' },

                                                        // Professional Fields
                                                        'Business Management': { bg: 'bg-slate-100', text: 'text-slate-800' },
                                                        'Marketing': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Finance': { bg: 'bg-emerald-100', text: 'text-emerald-800' },
                                                        'Accounting': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Law': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'Medicine': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Engineering': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Architecture': { bg: 'bg-stone-100', text: 'text-stone-800' },
                                                        'Agriculture': { bg: 'bg-lime-100', text: 'text-lime-800' },
                                                        'Information Technology': { bg: 'bg-sky-100', text: 'text-sky-800' },

                                                        // Skill-Based Categories
                                                        'Critical Thinking': { bg: 'bg-violet-100', text: 'text-violet-800' },
                                                        'Logical Reasoning': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Quantitative Reasoning': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Verbal Reasoning': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Data Interpretation': { bg: 'bg-cyan-100', text: 'text-cyan-800' },
                                                        'Reading Comprehension': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                                        'Writing Skills': { bg: 'bg-emerald-100', text: 'text-emerald-800' },
                                                        'Problem Solving': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'Decision Making': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Creative Thinking': { bg: 'bg-rose-100', text: 'text-rose-800' },

                                                        // Language Categories
                                                        'Language': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'English': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Hindi': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Punjabi': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'French': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Spanish': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                                        'German': { bg: 'bg-slate-100', text: 'text-slate-800' },
                                                        'Chinese': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Japanese': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'Arabic': { bg: 'bg-emerald-100', text: 'text-emerald-800' },
                                                        'Russian': { bg: 'bg-blue-100', text: 'text-blue-800' },

                                                        // Specialized Test Categories
                                                        'Aptitude Test': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'IQ Test': { bg: 'bg-violet-100', text: 'text-violet-800' },
                                                        'Personality Assessment': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Career Assessment': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'Entrance Exam': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Certification Exam': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                                        'Competitive Exam': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Diagnostic Test': { bg: 'bg-amber-100', text: 'text-amber-800' },
                                                        'Practice Test': { bg: 'bg-lime-100', text: 'text-lime-800' },
                                                        'Self-Assessment': { bg: 'bg-sky-100', text: 'text-sky-800' },

                                                        // Question Format Categories
                                                        'Multiple Choice': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'True/False': { bg: 'bg-green-100', text: 'text-green-800' },
                                                        'Fill in the Blank': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                                        'Short Answer': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Essay': { bg: 'bg-red-100', text: 'text-red-800' },
                                                        'Matching': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Ordering': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Diagram Labeling': { bg: 'bg-cyan-100', text: 'text-cyan-800' },
                                                        'Case Study': { bg: 'bg-teal-100', text: 'text-teal-800' },
                                                        'Problem Set': { bg: 'bg-emerald-100', text: 'text-emerald-800' },

                                                        // Difficulty Level Categories
                                                        'Beginner': { bg: 'bg-green-100', text: 'text-green-800' },
                                                        'Intermediate': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
                                                        'Advanced': { bg: 'bg-orange-100', text: 'text-orange-800' },
                                                        'Expert': { bg: 'bg-red-100', text: 'text-red-800' },

                                                        // Cognitive Level Categories
                                                        'Knowledge': { bg: 'bg-blue-100', text: 'text-blue-800' },
                                                        'Comprehension': { bg: 'bg-indigo-100', text: 'text-indigo-800' },
                                                        'Application': { bg: 'bg-purple-100', text: 'text-purple-800' },
                                                        'Analysis': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'Synthesis': { bg: 'bg-rose-100', text: 'text-rose-800' },
                                                        'Evaluation': { bg: 'bg-red-100', text: 'text-red-800' },

                                                        // General Categories
                                                        'Programming': { bg: 'bg-pink-100', text: 'text-pink-800' },
                                                        'General Knowledge': { bg: 'bg-yellow-100', text: 'text-yellow-800' }
                                                    };

                                                    let categoryNamesArray = [];
                                                    if (typeof question.category_names === 'string') {
                                                        categoryNamesArray = question.category_names.split(',');
                                                    } else if (Array.isArray(question.category_names)) {
                                                        categoryNamesArray = question.category_names;
                                                    }

                                                    categoryNamesArray.forEach(categoryName => {
                                                        if (!categoryName) return;
                                                        const category = categoryName.trim();
                                                        const colors = categoryColors[category] || { bg: 'bg-gray-100', text: 'text-gray-800' };
                                                    %>
                                                        <span class="px-2 py-1 text-xs font-medium rounded-full <%= colors.bg %> <%= colors.text %>">
                                                            <%= category %>
                                                        </span>
                                                    <% }); %>
                                                </div>
                                            <% } %>
                                            <div class="text-sm text-gray-600 mt-1">
                                                <span class="font-medium"><%= parseFloat(question.marks).toFixed(2) %> marks</span>
                                                <% if (parseFloat(question.negative_marks) > 0) { %>
                                                    <span class="text-red-500 ml-2">(-<%= parseFloat(question.negative_marks).toFixed(2) %> negative)</span>
                                                <% } %>
                                            </div>
                                        </div>
                                        <button class="bookmark-btn p-1 rounded <%= question.is_bookmarked ? 'text-yellow-500' : 'text-gray-400' %>"
                                                data-question-id="<%= question.question_id %>">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="<%= question.is_bookmarked ? 'currentColor' : 'none' %>" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                            </svg>
                                        </button>
                                    </div>

                                    <% if (question.essay_id) { %>
                                    <!-- Essay Content -->
                                    <div class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                                        <h4 class="text-lg font-semibold text-gray-800 mb-2"><%= question.essay_title %></h4>
                                        <div class="prose max-w-none mb-4 text-gray-700 overflow-y-auto max-h-64">
                                            <%- question.essay_content.replace(/\n/g, '<br>') %>
                                        </div>
                                        <% if (question.essay_pdf_path) { %>
                                        <div class="mt-2">
                                            <a href="<%= question.essay_pdf_path %>" target="_blank" class="text-blue-600 hover:text-blue-800 flex items-center">
                                                <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                                </svg>
                                                View PDF Version
                                            </a>
                                        </div>
                                        <% } %>
                                    </div>
                                    <% } %>
                                    <div class="mb-6">
                                        <p class="text-gray-800 mb-4"><%- question.question_text.replace(/\n/g, '<br>') %></p>

                                        <% if (question.image_path) { %>
                                        <div class="mb-4">
                                            <img src="<%= question.image_path %>" alt="Question image" class="question-image">
                                        </div>
                                        <% } %>

                                        <% if (question.question_type === 'mcq' || question.question_type === 'multiple_choice') { %>
                                            <div class="mcq-grid">
                                                <% if (question.options && question.options.length > 0) { %>
                                                    <%
                                                    // Parse user_answer to handle multiple selections
                                                    let selectedOptions = [];
                                                    if (question.user_answer) {
                                                        // Convert to string first to ensure split works
                                                        const answerStr = String(question.user_answer);
                                                        selectedOptions = answerStr.split(',');
                                                    }
                                                    %>
                                                    <% question.options.forEach((option, index) => { %>
                                                        <div class="mcq-option <%= selectedOptions.includes(option.id.toString()) ? 'selected' : '' %>"
                                                             data-value="<%= option.id %>">
                                                            <div class="flex items-center">
                                                                <input type="checkbox" id="option<%= question.question_id %>_<%= index %>"
                                                                       name="question<%= question.question_id %>[]"
                                                                       value="<%= option.id %>"
                                                                       <%= selectedOptions.includes(option.id.toString()) ? 'checked' : '' %>
                                                                       class="answer-checkbox hidden">
                                                                <label for="option<%= question.question_id %>_<%= index %>"
                                                                       class="ml-2 block text-gray-700 cursor-pointer">
                                                                    <%= option.option_text %>
                                                                    <% if (option.image_path) { %>
                                                                    <div class="mt-2">
                                                                        <img src="<%= option.image_path %>" alt="Option image" class="max-w-full h-auto max-h-40 rounded">
                                                                    </div>
                                                                    <% } %>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    <% }) %>
                                                <% } else { %>
                                                    <div class="col-span-2 text-red-500">No options available for this question.</div>
                                                <% } %>
                                            </div>

                                            <!-- Clear Answer Button -->
                                            <div class="mt-4">
                                                <button type="button" class="clear-answer-btn px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm flex items-center <%= question.user_answer ? 'visible' : 'hidden' %>"
                                                        data-question-id="<%= question.question_id %>">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                    Clear Answer
                                                </button>
                                            </div>
                                        <% } else if (question.question_type === 'true_false') { %>
                                            <div class="mcq-grid">
                                                <div class="mcq-option tf-option <%= question.user_answer === 'true' ? 'selected' : '' %>"
                                                     data-value="true" data-question-id="<%= question.question_id %>">
                                                    <div class="flex items-center">
                                                        <input type="radio" id="true<%= question.question_id %>"
                                                               name="question<%= question.question_id %>"
                                                               value="true"
                                                               <%= question.user_answer === 'true' ? 'checked' : '' %>
                                                               class="answer-radio">
                                                        <label for="true<%= question.question_id %>"
                                                               class="ml-2 block text-gray-700 cursor-pointer w-full h-full">
                                                            True
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mcq-option tf-option <%= question.user_answer === 'false' ? 'selected' : '' %>"
                                                     data-value="false" data-question-id="<%= question.question_id %>">
                                                    <div class="flex items-center">
                                                        <input type="radio" id="false<%= question.question_id %>"
                                                               name="question<%= question.question_id %>"
                                                               value="false"
                                                               <%= question.user_answer === 'false' ? 'checked' : '' %>
                                                               class="answer-radio">
                                                        <label for="false<%= question.question_id %>"
                                                               class="ml-2 block text-gray-700 cursor-pointer w-full h-full">
                                                            False
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Clear Answer Button for True/False -->
                                            <div class="mt-4">
                                                <button type="button" class="clear-answer-btn px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm flex items-center <%= question.user_answer ? 'visible' : 'hidden' %>"
                                                        data-question-id="<%= question.question_id %>">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                    Clear Answer
                                                </button>
                                            </div>
                                        <% } else { %>
                                            <div>
                                                <textarea id="answer<%= question.question_id %>"
                                                          class="answer-text w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                          rows="3"><%= question.user_answer %></textarea>

                                                <!-- Clear Answer Button for Essay/Text -->
                                                <div class="mt-4">
                                                    <button type="button" class="clear-answer-btn px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-sm flex items-center <%= question.user_answer ? 'visible' : 'hidden' %>"
                                                            data-question-id="<%= question.question_id %>">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                        Clear Answer
                                                    </button>
                                                </div>
                                            </div>
                                        <% } %>
                                    </div>
                                </div>
                            <% }) %>
                        </div>
                    <% }) %>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between mt-8">
                    <button id="prev-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded">
                        Previous
                    </button>
                    <div id="save-indicator" class="text-green-500 text-sm font-medium flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Answer saved
                    </div>
                    <button id="next-btn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
                        Next
                    </button>
                </div>

                <!-- We'll move the mobile submit button outside the question container -->
                <!-- No submit button here -->
            </div>
        </div>

        <!-- Mobile Submit Button outside the question container -->
        <div class="md:hidden mt-8 mb-16 px-4">
            <button id="mobile-fixed-submit-btn" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium shadow-md">
                Submit Test
            </button>
        </div>

        <!-- Question Palette (Right) -->
        <div class="lg:col-span-1 hidden md:block">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-4 flex flex-col h-[calc(100vh-2rem)] md:h-[calc(100vh-4rem)] lg:max-h-[800px]">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Question Palette</h2>

                <!-- Summary Stats -->
                <div class="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-4">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Summary</h3>
                    <div class="grid grid-cols-3 gap-2 text-center">
                        <%
                        // Calculate total stats across all sections
                        let totalQuestions = 0;
                        let totalAnswered = 0;
                        let totalUnanswered = 0;
                        let totalBookmarked = 0;

                        sections.forEach(section => {
                            totalQuestions += section.questions.length;

                            section.questions.forEach(q => {
                                if (q.user_answer) {
                                    totalAnswered++;
                                } else {
                                    totalUnanswered++;
                                }
                                if (q.is_bookmarked) {
                                    totalBookmarked++;
                                }
                            });
                        });
                        %>
                        <div>
                            <div class="text-green-600 font-bold text-lg summary-answered" id="summary-answered-desktop"><%= totalAnswered %></div>
                            <div class="text-xs text-gray-600">Answered</div>
                        </div>
                        <div>
                            <div class="text-gray-600 font-bold text-lg summary-unanswered" id="summary-unanswered-desktop"><%= totalUnanswered %></div>
                            <div class="text-xs text-gray-600">Unanswered</div>
                        </div>
                        <div>
                            <div class="text-yellow-600 font-bold text-lg summary-bookmarked" id="summary-bookmarked-desktop"><%= totalBookmarked %></div>
                            <div class="text-xs text-gray-600">Bookmarked</div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="flex flex-wrap justify-between mb-4 text-xs">
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 bg-gray-300 border border-gray-400 mr-1"></div>
                        <span>Unanswered</span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 bg-green-500 mr-1"></div>
                        <span>Answered</span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 border border-yellow-500 mr-1"></div>
                        <span>Bookmarked</span>
                    </div>
                </div>

                <!-- Palette - Scrollable -->
                <div class="space-y-4 question-palette overflow-y-auto flex-grow mb-4 max-h-[calc(100vh-20rem)] md:max-h-[calc(100vh-22rem)] lg:max-h-[500px]">
                    <%
                    let globalQuestionIndex = 0;
                    sections.forEach((section, sectionIndex) => {
                        // Generate a consistent color based on section name
                        const sectionColors = [
                            'bg-blue-100 text-blue-800',
                            'bg-green-100 text-green-800',
                            'bg-purple-100 text-purple-800',
                            'bg-pink-100 text-pink-800',
                            'bg-yellow-100 text-yellow-800',
                            'bg-indigo-100 text-indigo-800',
                            'bg-red-100 text-red-800',
                            'bg-orange-100 text-orange-800'
                        ];
                        const colorIndex = sectionIndex % sectionColors.length;
                        const sectionColor = sectionColors[colorIndex];
                    %>
                        <div class="section-container">
                            <div class="section-title flex justify-between items-center p-2 cursor-pointer <%= sectionColor %> rounded-md toggle-section-btn"
                                 data-section-index="<%= sectionIndex %>">
                                <span class="font-medium"><%= section.name %></span>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div class="section-questions mt-2 flex flex-wrap gap-1" id="section-questions-<%= sectionIndex %>">
                                <% section.questions.forEach((question, qIndex) => {
                                    globalQuestionIndex++;
                                %>
                                    <button class="question-btn w-8 h-8 rounded-md text-xs flex items-center justify-center <%= question.user_answer ? 'answered' : '' %> <%= question.is_bookmarked ? 'bookmarked' : '' %>"
                                            data-section="<%= sectionIndex %>"
                                            data-question="<%= qIndex %>"
                                            data-question-id="<%= question.question_id %>">
                                        <%= globalQuestionIndex %>
                                    </button>
                                <% }) %>
                            </div>
                        </div>
                    <% }) %>
                </div>

                <!-- Submit Button - Now at the bottom of the flex container -->
                <div class="mt-auto">
                    <form id="submit-form" method="POST" action="/tests/submit/<%= attemptId || '' %>">
                        <input type="hidden" id="submit-attempt-id" name="attemptId" value="<%= attemptId || '' %>">
                        <button type="submit" id="submit-test-btn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                            Submit Test
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Fullscreen Overlay -->
    <div id="fullscreen-overlay"></div>

    <!-- Instructions Overlay (for both Mobile and Desktop) -->
    <% if (exam.instructions) { %>
    <div id="instructions-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" style="display: none; align-items: center; justify-content: center;">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Instructions</h2>
                <button id="close-instructions" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Punjabi Instructions -->
                    <div class="prose max-w-none">
                        <h3 class="text-md font-medium text-gray-700 mb-2">Punjabi</h3>
                        <% punjabiInstructions.forEach((line, index) => { %>
                            <p class="mb-2"><%= index + 1 %>. <%= line %></p>
                        <% }) %>
                    </div>

                    <!-- English Instructions -->
                    <div class="prose max-w-none">
                        <h3 class="text-md font-medium text-gray-700 mb-2">English</h3>
                        <% englishInstructions.forEach((line, index) => { %>
                            <p class="mb-2"><%= index + 1 %>. <%= line %></p>
                        <% }) %>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <% } %>

    <!-- Mobile Question Palette Overlay -->
    <div id="mobile-palette-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="bg-white rounded-t-lg shadow-xl w-full h-[90vh] absolute bottom-0 overflow-hidden flex flex-col">
            <div class="flex justify-between items-center p-4 border-b">
                <h2 class="text-lg font-semibold text-gray-800">Question Palette</h2>
                <button id="close-mobile-palette" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Palette Content -->
            <div class="p-4 overflow-y-auto flex-grow">
                <!-- Summary Stats -->
                <div class="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-4">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Summary</h3>
                    <div class="grid grid-cols-3 gap-2 text-center">
                        <div>
                            <div class="text-green-600 font-bold text-lg summary-answered-mobile" id="summary-answered-mobile"><%= totalAnswered %></div>
                            <div class="text-xs text-gray-600">Answered</div>
                        </div>
                        <div>
                            <div class="text-gray-600 font-bold text-lg summary-unanswered-mobile" id="summary-unanswered-mobile"><%= totalUnanswered %></div>
                            <div class="text-xs text-gray-600">Unanswered</div>
                        </div>
                        <div>
                            <div class="text-yellow-600 font-bold text-lg summary-bookmarked-mobile" id="summary-bookmarked-mobile"><%= totalBookmarked %></div>
                            <div class="text-xs text-gray-600">Bookmarked</div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="flex flex-wrap justify-between mb-4 text-xs">
                    <div class="flex items-center mb-2 mr-2">
                        <div class="w-3 h-3 bg-gray-300 border border-gray-400 mr-1"></div>
                        <span>Unanswered</span>
                    </div>
                    <div class="flex items-center mb-2 mr-2">
                        <div class="w-3 h-3 bg-green-500 mr-1"></div>
                        <span>Answered</span>
                    </div>
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 border border-yellow-500 mr-1"></div>
                        <span>Bookmarked</span>
                    </div>
                </div>

                <!-- Mobile Palette Sections -->
                <div class="space-y-4 mobile-question-palette">
                    <%
                    let mobileGlobalQuestionIndex = 0;
                    sections.forEach((section, sectionIndex) => {
                        const sectionColors = [
                            'bg-blue-100 text-blue-800',
                            'bg-green-100 text-green-800',
                            'bg-purple-100 text-purple-800',
                            'bg-pink-100 text-pink-800',
                            'bg-yellow-100 text-yellow-800',
                            'bg-indigo-100 text-indigo-800',
                            'bg-red-100 text-red-800',
                            'bg-orange-100 text-orange-800'
                        ];
                        const colorIndex = sectionIndex % sectionColors.length;
                        const sectionColor = sectionColors[colorIndex];
                    %>
                        <div class="section-container">
                            <div class="section-title flex justify-between items-center p-2 cursor-pointer <%= sectionColor %> rounded-md toggle-section-btn-mobile"
                                 data-section-index="<%= sectionIndex %>">
                                <span class="font-medium"><%= section.name %></span>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div class="section-questions-mobile mt-2 flex flex-wrap gap-1" id="mobile-section-questions-<%= sectionIndex %>">
                                <% section.questions.forEach((question, qIndex) => {
                                    mobileGlobalQuestionIndex++;
                                %>
                                    <button class="question-btn w-8 h-8 rounded-md text-xs flex items-center justify-center <%= question.user_answer ? 'answered' : '' %> <%= question.is_bookmarked ? 'bookmarked' : '' %> mobile-question-btn"
                                            data-section="<%= sectionIndex %>"
                                            data-question="<%= qIndex %>"
                                            data-question-id="<%= question.question_id %>">
                                        <%= mobileGlobalQuestionIndex %>
                                    </button>
                                <% }) %>
                            </div>
                        </div>
                    <% }) %>
                </div>
            </div>

            <!-- No submit button in mobile palette overlay -->
            <!-- Hidden form for submission -->
            <form id="mobile-submit-form" action="/tests/submit/<%= attemptId %>" method="POST" class="hidden"></form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Variables
            const attemptId = "<%= attemptId %>";
            const testDuration = parseInt("<%= exam.duration || 60 %>");
            let currentSection = 0;
            let currentQuestion = 0;
            let saveTimeout;
            let isFullscreen = false;
            let timerInterval = null;
            let isSubmitting = false; // Flag to track if test is being submitted
            let isAutoSubmit = false; // Flag to indicate auto-submission by timer
            let tabVisibilityWarningShown = false; // Flag to track if tab visibility warning is shown
            let testStarted = false; // Flag to track if the test has officially started

            // Get the attempt ID for storage
            const attemptIdForStorage = '<%= attemptId %>';

            // Initialize back button detection
            window.isBackButtonClicked = false; // Flag to track if back button was clicked

            // Prevent copying of test content
            function preventCopyPaste() {
                // Disable text selection for the entire test content
                const testContent = document.querySelector('.container');
                if (testContent) {
                    testContent.style.userSelect = 'none';
                    testContent.style.webkitUserSelect = 'none';
                    testContent.style.msUserSelect = 'none';
                    testContent.style.mozUserSelect = 'none';
                }

                // Allow text selection only in textarea for essay questions
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.style.userSelect = 'text';
                    textarea.style.webkitUserSelect = 'text';
                    textarea.style.msUserSelect = 'text';
                    textarea.style.mozUserSelect = 'text';
                });

                // Add event listeners for copy, cut, paste attempts
                document.addEventListener('copy', function(e) {
                    if (document.activeElement.tagName !== 'TEXTAREA') {
                        e.preventDefault();
                        showCopyPasteNotification('Copying is disabled during the test');
                    }
                });

                document.addEventListener('cut', function(e) {
                    if (document.activeElement.tagName !== 'TEXTAREA') {
                        e.preventDefault();
                        showCopyPasteNotification('Cutting is disabled during the test');
                    }
                });

                document.addEventListener('paste', function(e) {
                    if (document.activeElement.tagName !== 'TEXTAREA') {
                        e.preventDefault();
                        showCopyPasteNotification('Pasting is disabled during the test');
                    }
                });
            }

            // Show notification when copy/paste is attempted
            function showCopyPasteNotification(message) {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md z-50';
                notification.innerHTML = `
                    <div class="flex items-center">
                        <div class="py-1">
                            <svg class="h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div>
                            <p class="font-bold">Security Notice</p>
                            <p class="text-sm">${message}</p>
                        </div>
                    </div>
                `;

                // Add to the document
                document.body.appendChild(notification);

                // Remove after 3 seconds
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 3000);
            }

            // Call the function to prevent copy-paste
            preventCopyPaste();

            // Prevent drag and drop actions
            document.addEventListener('dragstart', function(e) {
                // Allow drag and drop only for specific elements if needed
                if (e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable right-click context menu on the entire test page
            document.addEventListener('contextmenu', function(e) {
                // Only prevent context menu if test has started
                if (testStarted) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable right-click context menu on images (additional protection)
            document.querySelectorAll('img').forEach(img => {
                img.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });
            });

            // Check if this is a continued test
            const isTestContinued = <%= attempts && attempts.length > 0 && attempts[0].start_time ? 'true' : 'false' %>;

            // For continued tests, automatically check the agreement checkbox
            if (isTestContinued) {
                const agreeCheckbox = document.getElementById('agree-checkbox');
                if (agreeCheckbox) {
                    agreeCheckbox.checked = true;
                    // Enable the continue button
                    const startButton = document.getElementById('start-test-btn');
                    if (startButton) {
                        startButton.disabled = false;
                        startButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
                        startButton.classList.add('bg-indigo-600', 'hover:bg-indigo-700');

                        // Update the continue button text
                        if (isTestContinued) {
                            startButton.innerHTML = 'Resume Test';
                        }
                    }
                }
            }

            // Initialize timer with default values - will be set properly when test starts
            // Initialize timer display
            const timerElement = document.getElementById('timer');
            const floatingTimerElement = document.getElementById('floating-timer-text');
            const timerProgress = document.getElementById('timer-progress');
            const floatingTimerProgress = document.getElementById('floating-timer-progress');

            if (timerElement) timerElement.textContent = formatTime(testDuration * 60);
            if (floatingTimerElement) floatingTimerElement.textContent = formatTime(testDuration * 60);
            if (timerProgress) timerProgress.style.width = '100%';
            if (floatingTimerProgress) floatingTimerProgress.style.width = '100%';

            // Helper function to format time
            function formatTime(totalSeconds) {
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            }

            // Check if there's a stored end time in localStorage when the page loads
            function checkStoredTimer() {
                // Check if this is a paused test
                const isPaused = <%= attempts && attempts.length > 0 && attempts[0].status === 'paused' ? 'true' : 'false' %>;
                const storedPausedTime = localStorage.getItem('test_paused_time');
                const storedEndTime = localStorage.getItem('test_end_time');

                // If the test is paused and we have a stored paused time, use that
                if (isPaused && storedPausedTime) {
                    const remainingMs = parseInt(storedPausedTime, 10);
                    console.log('Found paused test with', remainingMs, 'ms remaining');

                    // Calculate a new end time based on the current time plus the remaining time
                    const now = new Date().getTime();
                    const newEndTime = now + remainingMs;
                    localStorage.setItem('test_end_time', newEndTime.toString());

                    // Clear the paused time as we're resuming the test
                    localStorage.removeItem('test_paused_time');

                    // Update the attempt status to in_progress in the database
                    if (attemptId) {
                        fetch(`/tests/resume/${attemptId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ remainingTime: remainingMs })
                        }).catch(error => {
                            console.error('Error resuming test:', error);
                        });
                    }

                    // Start the timer with the remaining time
                    startTimer(remainingMs);
                    return true;
                }
                // Otherwise, check for a stored end time
                else if (storedEndTime && testStarted) {
                    const endTime = parseInt(storedEndTime, 10);
                    const now = new Date().getTime();
                    const remainingMs = endTime - now;

                    if (remainingMs > 0) {
                        console.log('Found stored end time, resuming timer with', remainingMs, 'ms remaining');
                        // Start the timer with the remaining time
                        startTimer(remainingMs);
                        return true;
                    } else {
                        console.log('Found expired stored end time, clearing');
                        localStorage.removeItem('test_end_time');
                        // Auto-submit the test if time has expired
                        isSubmitting = true;
                        isAutoSubmit = true;
                        submitTest();
                    }
                }
                return false;
            }

            // Function to start the timer with a given remaining time
            function startTimer(remainingMs) {
                // Calculate a new end time based on the current time plus the remaining time
                const now = new Date().getTime();
                const endTime = now + remainingMs;
                localStorage.setItem('test_end_time', endTime.toString());

                // Clear any existing interval
                if (timerInterval) {
                    clearInterval(timerInterval);
                }

                // Start a new timer with the remaining time
                timerInterval = setInterval(function() {
                    const currentTime = new Date().getTime();
                    const currentRemainingMs = endTime - currentTime;

                    // Only log every 10 seconds to reduce console spam
                    if (Math.floor(currentRemainingMs / 1000) % 10 === 0) {
                        console.log('Timer tick, remaining:', currentRemainingMs, 'ms');
                    }

                    if (currentRemainingMs <= 0) {
                        console.log('Timer ended, submitting test');
                        clearInterval(timerInterval);

                        // Show a message that time is up and test is being submitted
                        const timeUpMessage = document.createElement('div');
                        timeUpMessage.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]';
                        timeUpMessage.innerHTML = `
                            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl text-center">
                                <h2 class="text-xl font-bold mb-4 text-red-600">Time's Up!</h2>
                                <p class="mb-6 text-gray-700">Your test is being submitted automatically.</p>
                                <div class="flex justify-center">
                                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-red-700"></div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(timeUpMessage);

                        // Auto-submit without confirmation when timer ends
                        isSubmitting = true; // Set flag to prevent beforeunload warning
                        isAutoSubmit = true; // Set flag to indicate auto-submission

                        // Clear local storage for this test
                        const storageKey = `test_${attemptId}_answers`;
                        localStorage.removeItem(storageKey);
                        localStorage.removeItem('test_end_time');
                        localStorage.removeItem('test_started');
                        console.log('Cleared test data from local storage on timer expiration');

                        // Only submit if we have a valid attemptId
                        if (attemptId) {
                            // Make sure we have the correct attemptId
                            const submitForm = document.getElementById('submit-form');
                            const submitAttemptIdInput = document.getElementById('submit-attempt-id');

                            // Update the form action with the current attemptId
                            submitForm.action = `/tests/submit/${attemptId}`;
                            submitAttemptIdInput.value = attemptId;

                            // Submit the form after a short delay to show the message
                            setTimeout(() => {
                                // Force submit without any confirmation
                                submitForm.submit();
                            }, 1500);
                        } else {
                            console.error('Cannot submit test: No valid attemptId');
                            // Redirect to tests page instead of submitting
                            setTimeout(() => {
                                window.location.href = '/tests';
                            }, 1500);
                        }
                        return;
                    }

                    updateTimerDisplay(currentRemainingMs);
                }, 1000);

                // Update timer display immediately
                updateTimerDisplay(remainingMs);
            }

            // Function to initialize the test
            function initializeTest() {
                // Enter fullscreen mode
                enterFullscreen();

                // Set the test as officially started
                testStarted = true;

                // Store the test started flag in localStorage
                localStorage.setItem('test_started', 'true');

                // Set up additional navigation prevention
                setupNavigationPrevention();
            }

            // Function to set up all navigation prevention mechanisms
            function setupNavigationPrevention() {
                // Add additional history entries to ensure back button is captured
                history.pushState(null, document.title, window.location.href);
                history.pushState(null, document.title, window.location.href);

                // Disable browser navigation buttons
                disableBrowserNavigation();
            }

            // Function to disable browser navigation buttons
            function disableBrowserNavigation() {
                // This creates a situation where the back button is effectively disabled
                // because we've added extra history entries and we're handling the popstate event
                window.addEventListener('popstate', function(e) {
                    // If we go back, immediately go forward again
                    if (history.state === null) {
                        history.forward();
                    }

                    // Show our custom dialog
                    if (testStarted && !isExitDialogShowing && !isSubmitting) {
                        isExitDialogShowing = true;
                        showExitConfirmationDialog();
                        setTimeout(() => {
                            isExitDialogShowing = false;
                        }, 100);
                    }

                    // Prevent default navigation
                    e.preventDefault();
                    return false;
                });
            }

            // Check for stored timer on page load
            window.addEventListener('load', function() {
                // Check if test was previously started
                const wasTestStarted = localStorage.getItem('test_started') === 'true';

                // Check if we're on the instructions page or the test page
                const startTestOverlay = document.getElementById('start-test-overlay');
                const isInstructionsPage = startTestOverlay && window.getComputedStyle(startTestOverlay).display !== 'none';

                if (wasTestStarted && !isInstructionsPage) {
                    // Only restore state if we're not on the instructions page
                    console.log('Test was previously started and we are not on instructions page, restoring state');
                    testStarted = true;

                    // If the start test overlay is visible, hide it
                    if (startTestOverlay) {
                        startTestOverlay.style.display = 'none';
                    }

                    // Enter fullscreen mode
                    setTimeout(() => {
                        enterFullscreen();
                    }, 500);

                    // Check for stored timer
                    checkStoredTimer();
                } else if (wasTestStarted && isInstructionsPage) {
                    console.log('Test was previously started but we are on instructions page, not restoring timer');
                    // Clear the stored timer to prevent it from running in the background
                    localStorage.removeItem('test_end_time');
                }
            });

            // Completely override browser back button behavior
            // First, add an extra history entry to ensure back button triggers our handler
            history.pushState(null, document.title, window.location.href);

            // Then handle the popstate event
            window.addEventListener('popstate', function(e) {
                // Prevent the default browser behavior
                e.preventDefault();

                // Push another state to prevent actual navigation
                history.pushState(null, document.title, window.location.href);
                console.log('Popstate event detected (back/forward button) - prevented default');

                // Show our custom confirmation dialog
                if (testStarted && !isExitDialogShowing && !isSubmitting) {
                    isExitDialogShowing = true;
                    showExitConfirmationDialog();
                    setTimeout(() => {
                        isExitDialogShowing = false;
                    }, 100);
                }

                // Return false to try to prevent default behavior in some browsers
                return false;
            });

            // Variable to track if our custom dialog is showing
            let isExitDialogShowing = false;

            // Handle browser close or page refresh
            window.addEventListener('beforeunload', function(e) {
                // Only prevent unload if test is in progress and not being submitted
                if (testStarted && !isSubmitting) {
                    // For browser close/refresh, we can't show our custom dialog
                    // but we can prevent navigation with the browser's generic message
                    e.preventDefault();
                    e.returnValue = '';
                    return '';
                }
            });

            // Add a special handler for keydown events to catch browser shortcuts
            window.addEventListener('keydown', function(e) {
                // Check if test has started
                if (testStarted && !isSubmitting) {
                    // Detect common browser shortcuts
                    // Alt+Left (browser back)
                    if ((e.altKey && e.keyCode === 37) ||
                        // Backspace when not in a form field
                        (e.keyCode === 8 && document.activeElement.tagName !== 'INPUT' &&
                         document.activeElement.tagName !== 'TEXTAREA') ||
                        // Ctrl+W (close tab)
                        (e.ctrlKey && e.keyCode === 87) ||
                        // Ctrl+R (refresh)
                        (e.ctrlKey && e.keyCode === 82) ||
                        // F5 (refresh)
                        (e.keyCode === 116)) {

                        // Prevent the default action
                        e.preventDefault();

                        // Show our custom dialog
                        if (!isExitDialogShowing) {
                            isExitDialogShowing = true;
                            showExitConfirmationDialog();
                            setTimeout(() => {
                                isExitDialogShowing = false;
                            }, 100);
                        }

                        return false;
                    }
                }
            });

            // Function to show exit confirmation dialog
            function showExitConfirmationDialog() {
                // If dialog is already showing, don't show another one
                if (document.querySelector('.exit-confirmation-dialog')) {
                    return;
                }

                // Create the dialog overlay
                const dialogOverlay = document.createElement('div');
                dialogOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] exit-confirmation-dialog';

                // Create the dialog content
                const dialogContent = document.createElement('div');
                dialogContent.className = 'bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl';
                dialogContent.innerHTML = `
                    <h2 class="text-xl font-bold mb-4 text-red-600">Warning: You Are About to Leave the Test</h2>
                    <p class="mb-6 text-gray-700">If you leave now, your test will be submitted with your current answers. Are you sure you want to proceed?</p>
                    <div class="flex justify-between">
                        <button id="continue-test-btn" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                            Continue Test
                        </button>
                        <button id="submit-exit-btn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                            Submit and Exit
                        </button>
                    </div>
                    <div class="mt-4 text-center">
                        <p class="text-gray-500">Auto-submitting in <span id="exit-dialog-countdown">10</span> seconds</p>
                    </div>
                `;

                // Add the dialog to the page
                dialogOverlay.appendChild(dialogContent);
                document.body.appendChild(dialogOverlay);

                // Start countdown for auto-submit
                let secondsLeft = 10;
                const countdownElement = document.getElementById('exit-dialog-countdown');

                const countdownTimer = setInterval(() => {
                    secondsLeft--;
                    if (countdownElement) {
                        countdownElement.textContent = secondsLeft;
                    }

                    if (secondsLeft <= 0) {
                        clearInterval(countdownTimer);
                        // Auto-submit the test if dialog is still showing
                        if (document.body.contains(dialogOverlay)) {
                            document.body.removeChild(dialogOverlay);
                            isSubmitting = true; // Set flag to prevent beforeunload warning
                            submitTest();
                        }
                    }
                }, 1000);

                // Add event listeners to the buttons
                document.getElementById('continue-test-btn').addEventListener('click', function() {
                    // Clear the countdown timer
                    clearInterval(countdownTimer);
                    // Remove the dialog
                    if (document.body.contains(dialogOverlay)) {
                        document.body.removeChild(dialogOverlay);
                    }
                });

                document.getElementById('submit-exit-btn').addEventListener('click', function() {
                    // Clear the countdown timer
                    clearInterval(countdownTimer);
                    // Remove the dialog
                    if (document.body.contains(dialogOverlay)) {
                        document.body.removeChild(dialogOverlay);
                    }
                    // Set isSubmitting to true to prevent the beforeunload warning
                    isSubmitting = true;

                    // Submit the test
                    submitTest();
                });

                // Prevent the dialog from being closed by clicking outside
                dialogOverlay.addEventListener('click', function(e) {
                    if (e.target === dialogOverlay) {
                        e.stopPropagation();
                    }
                });
            }

            // Mobile instructions button event listener
            const mobileInstructionsBtn = document.getElementById('mobile-instructions-btn');
            if (mobileInstructionsBtn) {
                mobileInstructionsBtn.addEventListener('click', function() {
                    console.log('Mobile instructions button clicked');
                    const overlay = document.getElementById('instructions-overlay');
                    overlay.style.display = 'flex';
                    overlay.classList.remove('hidden');
                });
            }

            // Desktop instructions button event listener
            const desktopInstructionsBtn = document.getElementById('desktop-instructions-btn');
            if (desktopInstructionsBtn) {
                desktopInstructionsBtn.addEventListener('click', function() {
                    console.log('Desktop instructions button clicked');
                    const overlay = document.getElementById('instructions-overlay');
                    overlay.style.display = 'flex';
                    overlay.classList.remove('hidden');
                });
            }

            // Close instructions button event listener
            const closeInstructionsBtn = document.getElementById('close-instructions');
            if (closeInstructionsBtn) {
                closeInstructionsBtn.addEventListener('click', function() {
                    console.log('Close instructions button clicked');
                    const overlay = document.getElementById('instructions-overlay');
                    overlay.classList.add('hidden');
                    setTimeout(() => {
                        overlay.style.display = 'none';
                    }, 100);
                });
            }

            // Mobile question palette button event listener
            const mobilePaletteBtn = document.getElementById('mobile-palette-btn');
            if (mobilePaletteBtn) {
                mobilePaletteBtn.addEventListener('click', toggleMobilePalette);
            }

            // Close mobile palette button event listener
            const closeMobilePaletteBtn = document.getElementById('close-mobile-palette');
            if (closeMobilePaletteBtn) {
                closeMobilePaletteBtn.addEventListener('click', toggleMobilePalette);
            }

            // Mobile question buttons event listeners
            document.querySelectorAll('.mobile-question-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.section);
                    const questionIndex = parseInt(this.dataset.question);

                    // Show the correct section and question
                    showSection(sectionIndex);
                    showQuestion(sectionIndex, questionIndex);

                    // Close the mobile palette overlay
                    toggleMobilePalette();
                });
            });

            // Mobile section toggle buttons
            document.querySelectorAll('.toggle-section-btn-mobile').forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.sectionIndex);
                    const questionsContainer = document.getElementById(`mobile-section-questions-${sectionIndex}`);
                    const toggleIcon = this.querySelector('.toggle-icon');

                    if (questionsContainer.style.display === 'none') {
                        questionsContainer.style.display = 'flex';
                        toggleIcon.textContent = '▼';
                    } else {
                        questionsContainer.style.display = 'none';
                        toggleIcon.textContent = '►';
                    }
                });
            });

            // Mobile submit button event listener (only the fixed button at bottom)
            const mobileSubmitBtns = [
                document.getElementById('mobile-fixed-submit-btn')
            ];

            mobileSubmitBtns.forEach(btn => {
                if (btn) {
                    btn.addEventListener('click', function() {
                        // Use the same submit function as desktop
                        submitTest();
                    });
                }
            });

            // We're now using an inline handler for the checkbox, so we don't need this event listener
            // But we'll still check if the checkbox is checked for continued tests
            const agreeCheckbox = document.getElementById('agree-checkbox');
            if (agreeCheckbox && isTestContinued) {
                // For continued tests, automatically check the agreement checkbox
                agreeCheckbox.checked = true;
                // Call the toggle function to enable the button
                toggleStartButton(agreeCheckbox);
            }

            // Start/Continue test button handler
            const startButton = document.getElementById('start-test-btn');
            if (startButton) {
                startButton.addEventListener('click', function() {
                    // Check if the agreement checkbox is checked
                    const agreeCheckbox = document.getElementById('agree-checkbox');
                    const agreementLabel = document.getElementById('agreement-label');
                    const agreementContainer = document.getElementById('agreement-container');

                    if (!agreeCheckbox || !agreeCheckbox.checked) {
                        console.log('Agreement checkbox not checked, preventing test start');

                        // Highlight the checkbox and label in red
                        if (agreementLabel) {
                            agreementLabel.classList.remove('text-gray-700');
                            agreementLabel.classList.add('text-red-600', 'font-bold');
                        }

                        if (agreementContainer) {
                            agreementContainer.classList.add('shake-animation');
                            // Remove the animation class after it completes
                            setTimeout(() => {
                                agreementContainer.classList.remove('shake-animation');
                            }, 820); // Animation duration + a little extra
                        }

                        return;
                    }

                    // Reset the label color if it was previously red
                    if (agreementLabel) {
                        agreementLabel.classList.remove('text-red-600', 'font-bold');
                        agreementLabel.classList.add('text-gray-700');
                    }

                    // Check if this is a continued test
                    const startTimeStr = "<%= attempts && attempts.length > 0 && attempts[0].start_time ? new Date(attempts[0].start_time).toISOString() : '' %>";
                    const isInProgressTest = <%= attempts && attempts.length > 0 && attempts[0].status === 'in_progress' ? 'true' : 'false' %>;
                    console.log('Start time:', startTimeStr, 'Status:', "<%= attempts && attempts.length > 0 ? attempts[0].status : 'none' %>", 'Is continued test:', isInProgressTest);

                    // First try to enter fullscreen
                    enterFullscreen();

                    // Set the test as officially started
                    testStarted = true;

                    // Show security notification
                    showSecurityNotification();

                    // Then hide the overlay
                    setTimeout(() => {
                        this.closest('#start-test-overlay').style.display = 'none';

                        if (isInProgressTest && startTimeStr) {
                            // This is a continued test
                            console.log('Continuing test with existing start time:', startTimeStr);
                            setupTimerForContinuedTest(testDuration, startTimeStr);
                        } else {

                            // Clear any previous answers from local storage for this test
                            const storageKey = `test_${attemptId}_answers`;
                            localStorage.removeItem(storageKey);
                            console.log('Cleared previous answers from local storage for new test');

                            // Start timer from beginning
                            console.log('Starting new test with duration:', testDuration, 'minutes');
                            setupTimer(testDuration);

                            // Record start time in database - create a new attempt if needed
                            fetch('/tests/record-start', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    examId: <%= exam.exam_id %>,
                                    attemptId: <%= attemptId ? attemptId : 'null' %>
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    if (data.autoSubmitted) {
                                        // Test was auto-submitted because this was the last continuation
                                        console.log('Test auto-submitted:', data.message);

                                        // Show a message to the user
                                        alert(data.message);

                                        // Redirect to the tests page
                                        window.location.href = '/tests';
                                        return;
                                    } else if (data.attemptId) {
                                        // Update the attemptId if a new one was created
                                        attemptId = data.attemptId;
                                        console.log('Test started with attempt ID:', attemptId);
                                    }
                                }
                            })
                            .catch(error => {
                                console.error('Error recording start time:', error);
                            });
                        }
                    }, 500); // Small delay to ensure fullscreen is entered first
                });
            } else {
                console.error('Start button not found');
            }

            // Timer functionality using end time approach to be more reliable
            function setupTimer(duration) {
                console.log('Setting up timer with duration:', duration, 'minutes');

                // Check if there's a remaining time from a previous session
                const remainingTimeFromDB = <%= attempts && attempts.length > 0 && attempts[0].remaining_time ? attempts[0].remaining_time : 0 %>;

                // If we have a remaining time from the database, use that
                if (remainingTimeFromDB > 0) {
                    console.log('Found test with', remainingTimeFromDB, 'ms remaining from database');

                    // Calculate a new end time based on the current time plus the remaining time
                    const now = new Date().getTime();
                    const newEndTime = now + remainingTimeFromDB;
                    localStorage.setItem('test_end_time', newEndTime.toString());

                    // Start the timer with the remaining time
                    startTimerWithEndTime(newEndTime);
                } else {
                    // Calculate end time based on duration in minutes
                    const now = new Date();
                    // Store the end time as a timestamp
                    const endTime = now.getTime() + (duration * 60 * 1000);

                    // Store the end time in localStorage to persist across tab switches/refreshes
                    localStorage.setItem('test_end_time', endTime.toString());
                    console.log('End time set to:', new Date(endTime).toISOString());

                    // Start the timer with the calculated end time
                    startTimerWithEndTime(endTime);
                }
            }

            // Function to start the timer with a given end time
            function startTimerWithEndTime(endTime) {
                // Calculate initial remaining time
                const now = new Date().getTime();
                let remainingMs = endTime - now;

                // Update timer immediately
                updateTimerDisplay(remainingMs);
                console.log('Initial timer display updated with:', remainingMs, 'ms');

                // Clear any existing timer
                if (timerInterval) {
                    console.log('Clearing existing timer interval');
                    clearInterval(timerInterval);
                }

                // Update timer every second, but calculate remaining time based on end time
                console.log('Starting new timer interval');
                timerInterval = setInterval(function() {
                    // Calculate remaining time based on current time and end time
                    const currentTime = new Date().getTime();
                    remainingMs = endTime - currentTime;
                    // Only log every 10 seconds to reduce console spam
                    if (Math.floor(remainingMs / 1000) % 10 === 0) {
                        console.log('Timer tick, remaining:', remainingMs, 'ms');
                    }

                    if (remainingMs <= 0) {
                        console.log('Timer ended, submitting test');
                        clearInterval(timerInterval);
                        remainingMs = 0;
                        // Auto-submit without confirmation when timer ends
                        isSubmitting = true; // Set flag to prevent beforeunload warning
                        isAutoSubmit = true; // Set flag to indicate auto-submission

                        // Clear local storage for this test
                        const storageKey = `test_${attemptId}_answers`;
                        localStorage.removeItem(storageKey);
                        localStorage.removeItem('test_end_time');
                        localStorage.removeItem('test_started');
                        localStorage.removeItem('test_paused_time');
                        console.log('Cleared test data from local storage on timer expiration');

                        // Only submit if we have a valid attemptId
                        if (attemptId) {
                            // Make sure we have the correct attemptId
                            const submitForm = document.getElementById('submit-form');
                            const submitAttemptIdInput = document.getElementById('submit-attempt-id');

                            // Update the form action with the current attemptId
                            submitForm.action = `/tests/submit/${attemptId}`;
                            submitAttemptIdInput.value = attemptId;

                            // Submit the form
                            submitForm.submit();
                        } else {
                            console.error('Cannot submit test: No valid attemptId');
                            // Redirect to tests page instead of submitting
                            window.location.href = '/tests';
                        }
                    }

                    updateTimerDisplay(remainingMs);
                }, 1000);
            }

            // Timer functionality for continued tests using end time approach
            function setupTimerForContinuedTest(duration, startTimeStr) {
                console.log('Setting up continued test timer with duration:', duration, 'minutes, start time:', startTimeStr);

                // Calculate remaining time based on when the test was started
                const now = new Date();
                const startTime = new Date(startTimeStr);

                // Calculate elapsed time in milliseconds
                const elapsedMs = now - startTime;
                console.log('Elapsed time:', elapsedMs, 'ms');

                // Calculate total duration in milliseconds
                const durationMs = duration * 60 * 1000;

                // Calculate end time
                let endTime = startTime.getTime() + durationMs;

                // Calculate remaining time
                let remainingMs = endTime - now.getTime();

                // If remaining time is negative or zero, set to minimum time (1 minute)
                if (remainingMs <= 0) {
                    remainingMs = 60 * 1000; // Give at least 1 minute to complete the test
                    endTime = now.getTime() + remainingMs; // Update end time accordingly
                    console.log('Test time has expired, giving 1 minute grace period');
                }

                // Store the end time in localStorage to persist across tab switches/refreshes
                localStorage.setItem('test_end_time', endTime.toString());
                console.log('End time set to:', new Date(endTime).toISOString());

                // Update timer immediately
                updateTimerDisplay(remainingMs);
                console.log('Initial timer display updated with:', remainingMs, 'ms');

                // Clear any existing timer
                if (timerInterval) {
                    console.log('Clearing existing timer interval');
                    clearInterval(timerInterval);
                }

                // Update timer every second, but calculate remaining time based on end time
                console.log('Starting new timer interval');
                timerInterval = setInterval(function() {
                    // Calculate remaining time based on current time and end time
                    const currentTime = new Date().getTime();
                    remainingMs = endTime - currentTime;
                    // Only log every 10 seconds to reduce console spam
                    if (Math.floor(remainingMs / 1000) % 10 === 0) {
                        console.log('Timer tick, remaining:', remainingMs, 'ms');
                    }

                    if (remainingMs <= 0) {
                        console.log('Timer ended, submitting test');
                        clearInterval(timerInterval);
                        remainingMs = 0;
                        // Auto-submit without confirmation when timer ends
                        isSubmitting = true; // Set flag to prevent beforeunload warning
                        isAutoSubmit = true; // Set flag to indicate auto-submission

                        // Clear local storage for this test
                        const storageKey = `test_${attemptId}_answers`;
                        localStorage.removeItem(storageKey);
                        localStorage.removeItem('test_end_time');
                        localStorage.removeItem('test_started');
                        console.log('Cleared test data from local storage on timer expiration');

                        // Only submit if we have a valid attemptId
                        if (attemptId) {
                            // Make sure we have the correct attemptId
                            const submitForm = document.getElementById('submit-form');
                            const submitAttemptIdInput = document.getElementById('submit-attempt-id');

                            // Update the form action with the current attemptId
                            submitForm.action = `/tests/submit/${attemptId}`;
                            submitAttemptIdInput.value = attemptId;

                            // Submit the form
                            submitForm.submit();
                        } else {
                            console.error('Cannot submit test: No valid attemptId');
                            // Redirect to tests page instead of submitting
                            window.location.href = '/tests';
                        }
                    }

                    updateTimerDisplay(remainingMs);
                }, 1000);
            }

            function updateTimerDisplay(remainingMs) {
                const hours = Math.floor(remainingMs / (1000 * 60 * 60));
                const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

                const displayHours = String(hours).padStart(2, '0');
                const displayMinutes = String(minutes).padStart(2, '0');
                const displaySeconds = String(seconds).padStart(2, '0');

                const timeString = `${displayHours}:${displayMinutes}:${displaySeconds}`;
                document.getElementById('timer').textContent = timeString;
                document.getElementById('floating-timer-text').textContent = timeString;

                // Calculate total duration in milliseconds (from the testDuration variable)
                const totalDurationMs = testDuration * 60 * 1000;

                // Calculate percentage of time remaining
                const percentRemaining = (remainingMs / totalDurationMs) * 100;

                // Update progress bars
                const timerProgress = document.getElementById('timer-progress');
                const floatingTimerProgress = document.getElementById('floating-timer-progress');

                timerProgress.style.width = `${percentRemaining}%`;
                floatingTimerProgress.style.width = `${percentRemaining}%`;

                // Set color based on percentage
                let progressColor = 'bg-green-500';

                if (percentRemaining <= 10) {
                    progressColor = 'bg-red-500';
                    // Add pulse animation
                    timerProgress.classList.add('animate-pulse');
                    floatingTimerProgress.classList.add('animate-pulse');
                } else if (percentRemaining <= 40) {
                    progressColor = 'bg-orange-500';
                } else if (percentRemaining <= 70) {
                    progressColor = 'bg-yellow-500';
                }

                // Remove all color classes and add the appropriate one
                timerProgress.classList.remove('bg-green-500', 'bg-yellow-500', 'bg-orange-500', 'bg-red-500');
                floatingTimerProgress.classList.remove('bg-green-500', 'bg-yellow-500', 'bg-orange-500', 'bg-red-500');

                timerProgress.classList.add(progressColor);
                floatingTimerProgress.classList.add(progressColor);
            }

            // Fullscreen functionality
            function enterFullscreen() {
                const element = document.documentElement;

                // Try to enter fullscreen mode, but handle errors gracefully
                try {
                    if (element.requestFullscreen) {
                        element.requestFullscreen().catch(e => {
                            console.warn('Fullscreen request failed:', e);
                            // Continue anyway without showing compatibility dialog
                        });
                    } else if (element.webkitRequestFullscreen) {
                        element.webkitRequestFullscreen();
                    } else if (element.msRequestFullscreen) {
                        element.msRequestFullscreen();
                    } else {
                        // Browser doesn't support fullscreen API
                        console.warn('Fullscreen not supported, continuing anyway');
                    }
                } catch (error) {
                    console.warn('Error entering fullscreen mode:', error);
                    // Continue without fullscreen
                }

                // Even if fullscreen fails, apply our custom fullscreen class
                document.body.classList.add('document-fullscreen');
                isFullscreen = true;
                document.getElementById('main-nav').style.display = 'none';
                document.getElementById('floating-timer').style.display = 'block';

                // Force nav elements to be hidden
                const navElements = document.querySelectorAll('nav, .navbar, .header, header');
                navElements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });

                // Log fullscreen entry
                console.log('Entered fullscreen mode');
            }

            // Function to show security notification
            function showSecurityNotification() {
                // Create a notification element
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded shadow-md z-50';
                notification.id = 'security-notification';
                notification.innerHTML = `
                    <div class="flex items-center">
                        <div class="py-1">
                            <svg class="h-6 w-6 text-yellow-500 mr-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div>
                            <p class="font-bold">Security Notice</p>
                            <p class="text-sm">Please remain in fullscreen mode. Exiting fullscreen or switching tabs may result in automatic test submission.</p>
                        </div>
                    </div>
                `;

                // Add to the document
                document.body.appendChild(notification);

                // Remove after 5 seconds
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 5000);
            }

            function exitFullscreen() {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                isFullscreen = false;
                document.getElementById('main-nav').style.display = 'block';
                document.getElementById('floating-timer').style.display = 'none';
            }

            // Handle fullscreen change events - active at all times during the test
            document.addEventListener('fullscreenchange', function() {
                // Check if test has officially started
                if (testStarted && !document.fullscreenElement) {
                    // User exited fullscreen mode
                    console.log('Fullscreen exit detected - showing warning');
                    showFullscreenWarning();
                }
            });

            document.addEventListener('webkitfullscreenchange', function() {
                // Check if test has officially started
                if (testStarted && !document.webkitFullscreenElement) {
                    // User exited fullscreen mode
                    console.log('Webkit fullscreen exit detected - showing warning');
                    showFullscreenWarning();
                }
            });

            document.addEventListener('MSFullscreenChange', function() {
                // Check if test has officially started
                if (testStarted && !document.msFullscreenElement) {
                    // User exited fullscreen mode
                    console.log('MS fullscreen exit detected - showing warning');
                    showFullscreenWarning();
                }
            });

            // Prevent exiting fullscreen and block copy-paste keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Check if test has officially started
                if (testStarted && e.key === 'Escape' && isFullscreen) {
                    e.preventDefault();
                    // Show warning that fullscreen is required
                    console.log('Escape key detected - showing fullscreen warning');
                    showFullscreenWarning();
                }

                // Block copy-paste keyboard shortcuts
                // Check if Ctrl or Command key is pressed
                if (e.ctrlKey || e.metaKey) {
                    // Block Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+A, Ctrl+S, Ctrl+P, Ctrl+U
                    const blockedKeys = ['c', 'v', 'x', 'a', 's', 'p', 'u'];

                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X only in textareas
                    if (blockedKeys.includes(e.key.toLowerCase())) {
                        // Check if the active element is a textarea
                        const isTextarea = document.activeElement.tagName === 'TEXTAREA';

                        // If not in a textarea, or if it's a key we want to block everywhere
                        if (!isTextarea || ['s', 'p', 'u'].includes(e.key.toLowerCase())) {
                            console.log('Blocked keyboard shortcut:', e.key);
                            e.preventDefault();
                            return false;
                        }
                    }
                }

                // Block F12 key (developer tools)
                if (e.key === 'F12') {
                    console.log('Blocked F12 key');
                    e.preventDefault();
                    return false;
                }
            });

            // Handle tab visibility and app switching changes - active at all times during the test
            document.addEventListener('visibilitychange', function() {
                // Check if test has officially started
                if (testStarted) {
                    if (document.visibilityState === 'hidden') {
                        // User switched to another tab or minimized the window
                        console.log('Tab visibility changed to hidden - showing exit dialog');

                        // Show the exit confirmation dialog
                        if (!isExitDialogShowing && !isSubmitting) {
                            isExitDialogShowing = true;
                            setTimeout(() => {
                                showExitConfirmationDialog();
                                isExitDialogShowing = false;
                            }, 1);
                        }
                    }
                }
            });

            // Handle app switching (blur/focus events) - active at all times during the test
            window.addEventListener('blur', function() {
                // Check if test has officially started
                if (testStarted) {
                    // User switched to another app
                    console.log('Window blur detected - showing exit dialog');

                    // Show the exit confirmation dialog
                    if (!isExitDialogShowing && !isSubmitting) {
                        isExitDialogShowing = true;
                        setTimeout(() => {
                            showExitConfirmationDialog();
                            isExitDialogShowing = false;
                        }, 1);
                    }
                }
            });

            // We don't need the focus handler anymore since we show the dialog immediately on blur

            // Unified warning dialog for all security events (fullscreen exit, tab switch, navigation attempt)
            function showSecurityWarning(type) {
                // If a warning is already showing, don't show another one
                if (document.querySelector('.security-warning-dialog')) {
                    return;
                }

                // Set the title, message, and styling based on the warning type
                let title, message, continueButtonText, bgColor, iconColor, buttonId;

                switch(type) {
                    case 'fullscreen':
                        title = 'Fullscreen Required';
                        message = 'Fullscreen mode is required for this test to prevent cheating.';
                        continueButtonText = 'Continue in Fullscreen';
                        bgColor = 'bg-red-600';
                        iconColor = 'text-red-600';
                        buttonId = 'enter-fullscreen-btn';
                        break;
                    case 'tab':
                        title = 'Tab Switching Detected';
                        message = 'Switching tabs during the test is not allowed to prevent cheating.';
                        continueButtonText = 'Continue Test';
                        bgColor = 'bg-orange-600';
                        iconColor = 'text-orange-600';
                        buttonId = 'continue-test-btn';
                        break;
                    case 'navigation':
                        title = 'Warning: You Are About to Leave the Test';
                        message = 'If you leave now, your test will be submitted with your current answers.';
                        continueButtonText = 'Continue Test';
                        bgColor = 'bg-blue-600';
                        iconColor = 'text-blue-600';
                        buttonId = 'continue-test-btn';
                        break;
                    default:
                        title = 'Security Warning';
                        message = 'A security violation has been detected.';
                        continueButtonText = 'Continue Test';
                        bgColor = 'bg-red-600';
                        iconColor = 'text-red-600';
                        buttonId = 'continue-test-btn';
                }

                // Create warning message
                const warningDiv = document.createElement('div');
                warningDiv.className = `fixed top-0 left-0 right-0 bottom-0 ${bgColor} bg-opacity-90 text-white flex flex-col items-center justify-center z-[9999] security-warning-dialog`;
                warningDiv.innerHTML = `
                    <div class="bg-white p-6 rounded-lg shadow-xl max-w-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 ${iconColor} mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">${title}</h2>
                        <p class="text-gray-600 mb-6">${message} The test will be automatically submitted in <span id="security-warning-countdown" class="font-bold text-red-600">10</span> seconds.</p>

                        <div class="flex flex-col space-y-3">
                            <button id="${buttonId}" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200">
                                ${continueButtonText}
                            </button>
                            <button id="submit-test-btn" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200">
                                Submit Test Now
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(warningDiv);

                // Start the 10-second countdown timer immediately
                let secondsLeft = 10;
                const countdownElement = document.getElementById('security-warning-countdown');

                // Start the countdown immediately
                const countdownTimer = setInterval(() => {
                    secondsLeft--;
                    if (countdownElement) {
                        countdownElement.textContent = secondsLeft;
                    }

                    if (secondsLeft <= 0) {
                        clearInterval(countdownTimer);
                        // Auto-submit the test
                        if (document.body.contains(warningDiv)) {
                            document.body.removeChild(warningDiv);
                            isSubmitting = true; // Set flag to prevent beforeunload warning
                            isAutoSubmit = true; // Set flag to indicate auto-submission

                            // Clear local storage for this test
                            const storageKey = `test_${attemptId}_answers`;
                            localStorage.removeItem(storageKey);
                            localStorage.removeItem('test_end_time');
                            localStorage.removeItem('test_started');
                            console.log(`Cleared test data from local storage on ${type} warning auto-submit`);

                            // Force submit without confirmation
                            const submitForm = document.getElementById('submit-form');
                            submitForm.submit();
                        }
                    }
                }, 1000);

                // Add event listener to the continue button
                document.getElementById(buttonId).addEventListener('click', function() {
                    clearInterval(countdownTimer); // Stop the countdown
                    document.body.removeChild(warningDiv);

                    // If this was a fullscreen warning or tab warning, make sure we're back in fullscreen
                    if (type === 'fullscreen' || type === 'tab') {
                        enterFullscreen();
                    }
                });

                // Add event listener to the submit button
                document.getElementById('submit-test-btn').addEventListener('click', function() {
                    clearInterval(countdownTimer); // Stop the countdown
                    document.body.removeChild(warningDiv);
                    isSubmitting = true; // Set flag to prevent beforeunload warning
                    isAutoSubmit = true; // Set flag to indicate auto-submission

                    // Force submit without confirmation
                    const submitForm = document.getElementById('submit-form');
                    submitForm.submit();
                });

                // Prevent the dialog from being closed by clicking outside
                warningDiv.addEventListener('click', function(e) {
                    if (e.target === warningDiv) {
                        e.stopPropagation();
                    }
                });
            }

            // Alias functions for backward compatibility
            function showFullscreenWarning() {
                showSecurityWarning('fullscreen');
            }

            function showTabVisibilityWarning() {
                showSecurityWarning('tab');
            }

            function showExitConfirmationDialog() {
                showSecurityWarning('navigation');
            }

            // We've removed the browser compatibility dialog as requested

            // Section Navigation
            const sectionTabs = document.querySelectorAll('.section-tab');
            const sectionContents = document.querySelectorAll('.section-content');

            sectionTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.section);
                    showSection(sectionIndex);
                });
            });

            function showSection(sectionIndex) {
                // Update tabs
                sectionTabs.forEach(tab => tab.classList.remove('active'));
                sectionTabs[sectionIndex].classList.add('active');

                // Update content
                sectionContents.forEach(content => content.classList.add('hidden'));
                sectionContents[sectionIndex].classList.remove('hidden');

                // Show first question of section
                currentSection = sectionIndex;
                currentQuestion = 0;
                showQuestion(currentSection, currentQuestion);
            }

            // Question Navigation
            const questionItems = document.querySelectorAll('.question-item');
            const questionBtns = document.querySelectorAll('.question-btn');

            function showQuestion(sectionIndex, questionIndex) {
                // Hide all questions
                questionItems.forEach(item => item.classList.add('hidden'));

                // Find question to show
                const questionToShow = document.querySelector(`.question-item[data-section="${sectionIndex}"][data-question="${questionIndex}"]`);

                if (questionToShow) {
                    questionToShow.classList.remove('hidden');

                    // Update current section/question
                    currentSection = sectionIndex;
                    currentQuestion = questionIndex;

                    // Update question buttons - add 'current' class to current question
                    questionBtns.forEach(btn => btn.classList.remove('current'));

                    const currentQuestionBtn = document.querySelector(`.question-btn[data-section="${sectionIndex}"][data-question="${questionIndex}"]`);
                    if (currentQuestionBtn) {
                        currentQuestionBtn.classList.add('current');
                    }
                }
            }

            // Question Palette Navigation
            questionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionIndex = parseInt(this.dataset.section);
                    const questionIndex = parseInt(this.dataset.question);

                    // Show correct section
                    showSection(sectionIndex);

                    // Show question
                    showQuestion(sectionIndex, questionIndex);
                });
            });

            // Previous/Next buttons
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            prevBtn.addEventListener('click', function() {
                navigateQuestion(-1);
            });

            nextBtn.addEventListener('click', function() {
                navigateQuestion(1);
            });

            function navigateQuestion(direction) {
                let sections = Array.from(sectionContents);
                let newSection = currentSection;
                let newQuestion = currentQuestion + direction;

                const currentSectionQuestions = document.querySelectorAll(`.question-item[data-section="${currentSection}"]`);

                // Check if we need to change section
                if (newQuestion < 0) {
                    newSection--;
                    if (newSection < 0) {
                        newSection = sections.length - 1;
                    }

                    const newSectionQuestions = document.querySelectorAll(`.question-item[data-section="${newSection}"]`);
                    newQuestion = newSectionQuestions.length - 1;
                } else if (newQuestion >= currentSectionQuestions.length) {
                    newSection++;
                    if (newSection >= sections.length) {
                        newSection = 0;
                    }

                    newQuestion = 0;
                }

                showSection(newSection);
                showQuestion(newSection, newQuestion);
            }

            // Save answers functionality
            const answerRadios = document.querySelectorAll('.answer-radio');
            const answerTexts = document.querySelectorAll('.answer-text');
            const bookmarkBtns = document.querySelectorAll('.bookmark-btn');
            const mcqOptions = document.querySelectorAll('.mcq-option');

            // Function to show save indicator
            function showSaveIndicator(message = 'Answer saved') {
                const saveIndicator = document.getElementById('save-indicator');

                // Update both text and icon
                if (message === 'Answer cleared') {
                    // For cleared answers, change the icon and text
                    saveIndicator.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Answer cleared
                    `;
                } else {
                    // For saved answers, use the checkmark icon
                    saveIndicator.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        ${message}
                    `;
                }

                saveIndicator.classList.add('show');

                setTimeout(() => {
                    saveIndicator.classList.remove('show');
                    // Reset to default message after animation completes
                    setTimeout(() => {
                        saveIndicator.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Answer saved
                        `;
                    }, 500);
                }, 2000);
            }

            // Function to save answer
            function saveAnswer(questionId, answer, isBookmarked) {
                // For MCQ questions, save the option ID
                if (answer && answer.match(/^\d+$/)) {
                    // Verify the option exists before saving
                    const option = document.querySelector(`.mcq-option[data-value="${answer}"]`);
                    if (option) {
                        // The option exists in the DOM, proceed with save
                        saveAnswerToServer(questionId, answer, isBookmarked);
                        // Also save to local storage to prevent loss on refresh
                        saveAnswerToLocalStorage(questionId, answer, isBookmarked);
                    } else {
                        console.error('Option not found in DOM');
                        showErrorIndicator('Error: Invalid option');
                    }
                } else {
                    // For non-numeric answers (essay, etc.), save directly
                    saveAnswerToServer(questionId, answer, isBookmarked);
                    // Also save to local storage to prevent loss on refresh
                    saveAnswerToLocalStorage(questionId, answer, isBookmarked);
                }
            }

            // Function to save answer to local storage
            function saveAnswerToLocalStorage(questionId, answer, isBookmarked) {
                // Get existing answers from local storage or initialize empty object
                const storageKey = `test_${attemptId}_answers`;
                let savedAnswers = JSON.parse(localStorage.getItem(storageKey) || '{}');

                // Update the answer for this question
                savedAnswers[questionId] = {
                    answer: answer,
                    isBookmarked: isBookmarked
                };

                // Save back to local storage
                localStorage.setItem(storageKey, JSON.stringify(savedAnswers));
            }

            // Function to show error indicator
            function showErrorIndicator(message) {
                const saveIndicator = document.getElementById('save-indicator');
                saveIndicator.textContent = message;
                saveIndicator.classList.add('text-red-500');
                saveIndicator.classList.add('show');

                setTimeout(() => {
                    saveIndicator.classList.remove('show');
                    saveIndicator.classList.remove('text-red-500');
                    saveIndicator.textContent = 'Answer saved';
                }, 2000);
            }

            // Function to send the save request to the server
            function saveAnswerToServer(questionId, answer, isBookmarked) {
                // Only save to server if we have a valid attemptId
                if (!attemptId) {
                    console.log('No attemptId available yet, saving to local storage only');
                    return;
                }

                console.log('Saving answer to server:', { attemptId, questionId, answer, isBookmarked });

                fetch('/tests/answer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attemptId: attemptId,
                        questionId: questionId,
                        answer: answer,
                        isBookmarked: isBookmarked
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showSaveIndicator();

                        // Update question button to show answered status
                        const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                        if (questionBtn && answer) {
                            questionBtn.classList.remove('unanswered');
                            questionBtn.classList.add('answered');
                        }

                        // Update bookmarked status
                        if (isBookmarked && questionBtn) {
                            questionBtn.classList.add('bookmarked');
                        } else if (questionBtn) {
                            questionBtn.classList.remove('bookmarked');
                        }

                        // Update the entire question palette
                        updateQuestionPalette();
                    } else {
                        console.error('Error saving answer:', data.message);
                        showErrorIndicator('Error saving answer');
                    }
                })
                .catch(error => {
                    console.error('Error saving answer:', error);
                    showErrorIndicator('Error saving answer');
                });
            }

            // Add event listeners for MCQ options
            document.querySelectorAll('.mcq-option').forEach(option => {
                option.addEventListener('click', function() {
                    const questionItem = this.closest('.question-item');
                    if (!questionItem) return;

                    const questionId = questionItem.dataset.questionId;
                    const value = this.dataset.value;
                    const checkbox = this.querySelector('input[type="checkbox"]');

                    if (!checkbox || !value) {
                        console.error('Checkbox input or value not found');
                        return;
                    }

                    // Toggle checkbox state
                    checkbox.checked = !checkbox.checked;

                    // Toggle option styling
                    this.classList.toggle('selected', checkbox.checked);

                    // Get all selected values for this question
                    const selectedValues = [];
                    this.closest('.mcq-grid').querySelectorAll('.mcq-option input[type="checkbox"]:checked').forEach(cb => {
                        selectedValues.push(cb.value);
                    });

                    // Save answer (as a comma-separated string)
                    const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`)?.classList.contains('text-yellow-500') || false;
                    saveAnswer(questionId, selectedValues.join(','), isBookmarked);

                    // Show the clear answer button
                    const clearBtn = questionItem.querySelector('.clear-answer-btn');
                    if (clearBtn) {
                        clearBtn.classList.remove('hidden');
                        clearBtn.classList.add('visible');
                    }
                });
            });

            // Add event listeners for true/false radio buttons
            answerRadios.forEach(radio => {
                if (radio.name.startsWith('question') && (radio.value === 'true' || radio.value === 'false')) {
                    radio.addEventListener('change', function() {
                        const questionId = this.name.replace('question', '');
                        const value = this.value;

                        // Update the selected class on the parent mcq-option
                        const tfOptions = document.querySelectorAll(`.tf-option[data-question-id="${questionId}"], .tf-option`);
                        tfOptions.forEach(option => {
                            if (option.dataset.value === value) {
                                option.classList.add('selected');
                            } else {
                                option.classList.remove('selected');
                            }
                        });

                        // Save answer
                        const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`)?.classList.contains('text-yellow-500') || false;
                        saveAnswer(questionId, value, isBookmarked);

                        // Update question button to show answered status
                        const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                        if (questionBtn) {
                            questionBtn.classList.remove('unanswered');
                            questionBtn.classList.add('answered');
                        }

                        // Show the clear answer button
                        const questionItem = this.closest('.question-item');
                        if (questionItem) {
                            const clearBtn = questionItem.querySelector('.clear-answer-btn');
                            if (clearBtn) {
                                clearBtn.classList.remove('hidden');
                                clearBtn.classList.add('visible');
                            }
                        }
                    });
                }
            });

            // Add click event listeners for true/false option containers
            document.querySelectorAll('.tf-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    // Prevent default to avoid any browser-specific issues
                    e.preventDefault();

                    // Find the radio button
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        // Check the radio button
                        radio.checked = true;

                        // Update visual state immediately
                        const questionId = this.dataset.questionId;
                        const tfOptions = document.querySelectorAll(`.tf-option[data-question-id="${questionId}"]`);
                        tfOptions.forEach(opt => {
                            if (opt === this) {
                                opt.classList.add('selected');
                            } else {
                                opt.classList.remove('selected');
                            }
                        });

                        // Show the clear answer button
                        const questionItem = this.closest('.question-item');
                        if (questionItem) {
                            const clearBtn = questionItem.querySelector('.clear-answer-btn');
                            if (clearBtn) {
                                clearBtn.classList.remove('hidden');
                                clearBtn.classList.add('visible');
                            }
                        }

                        // Trigger change event to save the answer
                        radio.dispatchEvent(new Event('change'));

                        // Update question palette
                        const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                        if (questionBtn) {
                            questionBtn.classList.remove('unanswered');
                            questionBtn.classList.add('answered');
                        }

                        // Show save indicator
                        showSaveIndicator();
                    }
                });

                // Also add click handler for the label inside
                const label = option.querySelector('label');
                if (label) {
                    label.addEventListener('click', function(e) {
                        // Stop propagation to prevent double-firing
                        e.stopPropagation();
                        // Trigger click on parent
                        option.click();
                    });
                }
            });

            // Add event listeners for text areas
            answerTexts.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    const questionId = this.id.replace('answer', '');

                    // Clear previous timeout
                    if (saveTimeout) {
                        clearTimeout(saveTimeout);
                    }

                    // Save after 1 second of inactivity
                    saveTimeout = setTimeout(() => {
                        const answer = this.value;
                        const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`).classList.contains('text-yellow-500');

                        saveAnswer(questionId, answer, isBookmarked);

                        // Show or hide the clear answer button based on whether there's text
                        const questionItem = this.closest('.question-item');
                        if (questionItem) {
                            const clearBtn = questionItem.querySelector('.clear-answer-btn');
                            if (clearBtn) {
                                if (typeof answer === 'string' && answer.trim()) {
                                    clearBtn.classList.remove('hidden');
                                    clearBtn.classList.add('visible');
                                } else {
                                    clearBtn.classList.add('hidden');
                                    clearBtn.classList.remove('visible');
                                }
                            }
                        }
                    }, 1000);
                });
            });

            // Add event listeners for bookmark buttons
            bookmarkBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const questionId = this.dataset.questionId;

                    // Toggle bookmark state
                    this.classList.toggle('text-yellow-500');
                    const isBookmarked = this.classList.contains('text-yellow-500');

                    // Update SVG fill
                    const svg = this.querySelector('svg');
                    if (isBookmarked) {
                        svg.setAttribute('fill', 'currentColor');
                    } else {
                        svg.setAttribute('fill', 'none');
                    }

                    // Get current answer
                    let answer = '';
                    const radio = document.querySelector(`input[name="question${questionId}"]:checked`);
                    if (radio) {
                        answer = radio.value;
                    } else {
                        const textarea = document.getElementById(`answer${questionId}`);
                        if (textarea) {
                            answer = textarea.value;
                        }
                    }

                    // Save answer with updated bookmark state
                    saveAnswer(questionId, answer, isBookmarked);

                    // Update question button to show bookmarked status
                    const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                    if (questionBtn) {
                        if (isBookmarked) {
                            questionBtn.classList.add('bookmarked');
                        } else {
                            questionBtn.classList.remove('bookmarked');
                        }
                    }
                });
            });

            // Add event listeners for clear answer buttons
            const clearAnswerBtns = document.querySelectorAll('.clear-answer-btn');
            clearAnswerBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const questionId = this.dataset.questionId;
                    if (!questionId) return;

                    // Find the question item
                    const questionItem = document.querySelector(`.question-item[data-question-id="${questionId}"]`);
                    if (!questionItem) return;

                    // Determine question type and clear the answer
                    const mcqOptions = questionItem.querySelectorAll('.mcq-option');
                    const textarea = questionItem.querySelector(`#answer${questionId}`);
                    const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`)?.classList.contains('text-yellow-500') || false;

                    if (mcqOptions.length > 0) {
                        // Clear MCQ or True/False selection
                        mcqOptions.forEach(option => {
                            option.classList.remove('selected');
                            const radio = option.querySelector('input[type="radio"]');
                            const checkbox = option.querySelector('input[type="checkbox"]');
                            if (radio) radio.checked = false;
                            if (checkbox) checkbox.checked = false;
                        });
                    } else if (textarea) {
                        // Clear text answer
                        textarea.value = '';
                    }

                    // Save empty answer to server
                    saveAnswer(questionId, '', isBookmarked);

                    // Update question button to show unanswered status
                    const questionBtns = document.querySelectorAll(`.question-btn[data-question-id="${questionId}"]`);
                    questionBtns.forEach(btn => {
                        btn.classList.remove('answered');
                        btn.classList.add('unanswered');
                    });

                    // Hide the clear button
                    this.classList.add('hidden');
                    this.classList.remove('visible');

                    // Show answer cleared indicator
                    showSaveIndicator('Answer cleared');
                });
            });

            // Function to submit test
            function submitTest(forceSubmit = false) {
                // Calculate unanswered count properly - exclude mobile buttons to avoid double counting
                const totalQuestions = document.querySelectorAll('.question-btn:not(.mobile-question-btn)').length;
                const answeredQuestions = document.querySelectorAll('.question-btn.answered:not(.mobile-question-btn)').length;
                let unansweredCount = totalQuestions - answeredQuestions;

                // If auto-submit is triggered by timer or force submit is requested, submit without confirmation
                if (isAutoSubmit || forceSubmit) {
                    isSubmitting = true; // Set flag to prevent beforeunload warning
                    isAutoSubmit = true; // Set flag to indicate auto-submission

                    // Clear local storage for this test
                    const storageKey = `test_${attemptId}_answers`;
                    localStorage.removeItem(storageKey);
                    localStorage.removeItem('test_end_time');
                    localStorage.removeItem('test_started');
                    console.log('Cleared test data from local storage on auto-submission');

                    // Make sure we have the correct attemptId
                    const submitForm = document.getElementById('submit-form');
                    const submitAttemptIdInput = document.getElementById('submit-attempt-id');

                    // Update the form action with the current attemptId
                    if (attemptId) {
                        submitForm.action = `/tests/submit/${attemptId}`;
                        submitAttemptIdInput.value = attemptId;

                        // Submit the form
                        submitForm.submit();
                    } else {
                        console.error('Cannot submit test: No valid attemptId');
                        // Redirect to tests page instead of submitting
                        window.location.href = '/tests';
                    }
                    return;
                }

                // Show custom confirmation dialog
                const confirmDialog = document.createElement('div');
                confirmDialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                confirmDialog.id = 'confirm-dialog';

                // Get the current stats - make sure we're only counting each question once
                // We need to exclude mobile buttons to avoid double counting
                const allQuestionButtons = document.querySelectorAll('.question-btn:not(.mobile-question-btn)');
                const answeredCount = document.querySelectorAll('.question-btn.answered:not(.mobile-question-btn)').length;
                const totalCount = allQuestionButtons.length;
                const bookmarkedCount = document.querySelectorAll('.question-btn.bookmarked:not(.mobile-question-btn)').length;

                // Update unansweredCount with the latest value
                unansweredCount = totalCount - answeredCount;

                confirmDialog.innerHTML = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <h2 class="text-xl font-bold mb-4">Confirm Test Submission</h2>
                        <div class="mb-4 grid grid-cols-2 gap-2">

                            <div class="bg-green-50 p-2 rounded">
                                <p class="text-gray-700 font-semibold">✅ Answered: <span class="text-green-600 font-bold">${answeredCount}</span></p>
                            </div>

                            <div class="bg-red-50 p-2 rounded">
                                <p class="text-gray-700 font-semibold ${unansweredCount > 0 ? 'text-red-600' : ''}">❌ Unanswered: <span class="font-bold">${unansweredCount}</span></p>
                            </div>

                            <div class="bg-yellow-50 p-2 rounded">
                                <p class="text-gray-700 font-semibold">🔖 Bookmarked: <span class="text-yellow-600 font-bold">${bookmarkedCount}</span></p>
                            </div>

                            <div class="bg-purple-50 p-2 rounded">
                                <p class="text-gray-700 font-semibold">📝 Total: <span class="text-purple-600 font-bold">${totalCount}</span></p>
                            </div>
                        </div>

                        <p class="text-gray-700 mb-6 font-medium">Are you sure you want to submit this test? This action cannot be undone.</p>

                        <div class="flex justify-end space-x-4">
                            <button id="cancel-submit" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100">
                                Cancel
                            </button>
                            <button id="confirm-submit" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                                Submit Test
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(confirmDialog);

                // Handle button clicks
                document.getElementById('cancel-submit').addEventListener('click', function() {
                    document.body.removeChild(confirmDialog);
                });

                document.getElementById('confirm-submit').addEventListener('click', function() {
                    document.body.removeChild(confirmDialog);
                    // Set the isSubmitting flag to true to prevent the beforeunload warning
                    isSubmitting = true;

                    // Clear local storage for this test
                    const storageKey = `test_${attemptId}_answers`;
                    localStorage.removeItem(storageKey);
                    localStorage.removeItem('test_end_time');
                    localStorage.removeItem('test_started');
                    console.log('Cleared test data from local storage on submission');

                    // Make sure we have the correct attemptId
                    const submitForm = document.getElementById('submit-form');
                    const submitAttemptIdInput = document.getElementById('submit-attempt-id');

                    // Update the form action with the current attemptId
                    if (attemptId) {
                        submitForm.action = `/tests/submit/${attemptId}`;
                        submitAttemptIdInput.value = attemptId;

                        // Submit the form
                        submitForm.submit();
                    } else {
                        console.error('Cannot submit test: No valid attemptId');
                        // Redirect to tests page instead of submitting
                        window.location.href = '/tests';
                    }
                });
            }

            // Handle test submission
            document.getElementById('submit-test-btn').addEventListener('click', function(e) {
                e.preventDefault();
                submitTest();
            });

            // Show first question and initialize listeners
            function initializeTest() {
                // Initialize first question
                showQuestion(0, 0);

                // Refresh question btn states
                refreshQuestionButtons();

                // Also update the question palette
                updateQuestionPalette();

                // Check if this is a continued test with existing start time
                const isTestContinuedWithStartTime = <%= attempts && attempts.length > 0 && attempts[0].start_time ? 'true' : 'false' %>;
                const startTimeStr = "<%= attempts && attempts.length > 0 && attempts[0].start_time ? new Date(attempts[0].start_time).toISOString() : '' %>";

                // For continued tests, we'll show the instructions overlay first
                // The test will start when the user clicks the Continue Test button
                if (isTestContinuedWithStartTime && startTimeStr) {
                    console.log('This is a continued test with existing start time:', startTimeStr);

                    // Make sure the start test overlay is visible
                    const startTestOverlay = document.getElementById('start-test-overlay');
                    if (startTestOverlay) {
                        startTestOverlay.style.display = 'flex';
                    }

                    // The actual test will start when the user clicks the Continue Test button
                    // The button click handler will call enterFullscreen() and start the timer
                }

                // Apply these changes after a short delay to ensure everything is loaded
                setTimeout(() => {
                    refreshQuestionButtons();
                    updateQuestionPalette();
                }, 200);
            }

            // Function to refresh question button states
            function refreshQuestionButtons() {
                const questionBtns = document.querySelectorAll('.question-btn');

                questionBtns.forEach(btn => {
                    const questionId = btn.dataset.questionId;

                    // Check if answered
                    const answerElement = document.querySelector(`input[name="question${questionId}"]:checked`) ||
                                         document.querySelector(`#answer${questionId}`);

                    if (answerElement && (answerElement.checked || answerElement.value)) {
                        btn.classList.remove('unanswered');
                        btn.classList.add('answered');
                    } else {
                        btn.classList.remove('answered');
                        btn.classList.add('unanswered');
                    }

                    // Check if bookmarked
                    const isBookmarked = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`)?.classList.contains('text-yellow-500');
                    if (isBookmarked) {
                        btn.classList.add('bookmarked');
                    } else {
                        btn.classList.remove('bookmarked');
                    }
                });
            }

            // Load answers from local storage if available
            function loadAnswersFromLocalStorage() {
                const storageKey = `test_${attemptId}_answers`;
                const savedAnswers = JSON.parse(localStorage.getItem(storageKey) || '{}');

                // Apply saved answers to the UI
                Object.keys(savedAnswers).forEach(questionId => {
                    const savedData = savedAnswers[questionId];
                    const answer = savedData.answer;
                    const isBookmarked = savedData.isBookmarked;

                    // Find the question element
                    const questionElement = document.querySelector(`.question-item[data-question-id="${questionId}"]`);
                    if (!questionElement) return;

                    // Apply answer based on question type
                    if (answer && answer.match(/^\d+$/)) {
                        // MCQ question
                        const option = questionElement.querySelector(`.mcq-option[data-value="${answer}"]`);
                        if (option) {
                            const radio = option.querySelector('input[type="radio"]');
                            if (radio) radio.checked = true;
                            option.classList.add('selected');
                        }
                    } else if (answer === 'true' || answer === 'false') {
                        // True/False question
                        const radio = questionElement.querySelector(`input[name="question${questionId}"][value="${answer}"]`);
                        if (radio) radio.checked = true;
                    } else if (answer) {
                        // Text answer
                        const textarea = questionElement.querySelector(`#answer${questionId}`);
                        if (textarea) textarea.value = answer;
                    }

                    // Apply bookmark status
                    if (isBookmarked) {
                        const bookmarkBtn = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`);
                        if (bookmarkBtn) {
                            bookmarkBtn.classList.add('text-yellow-500');

                            // Update question button to show bookmarked status
                            const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                            if (questionBtn) {
                                questionBtn.classList.add('bookmarked');
                            }
                        }
                    }

                    // Update question button to show answered status if there's an answer
                    if (answer) {
                        const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                        if (questionBtn) {
                            questionBtn.classList.remove('unanswered');
                            questionBtn.classList.add('answered');
                        }
                    }

                    // Also save to server to ensure it's persisted in the database
                    saveAnswerToServer(questionId, answer, isBookmarked);
                });
            }

            // Add event listeners for section toggle buttons after the page is fully loaded
            window.addEventListener('load', function() {
                // Add click event listeners to all toggle section buttons
                const toggleButtons = document.querySelectorAll('.toggle-section-btn');
                toggleButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const sectionIndex = this.getAttribute('data-section-index');
                        const sectionQuestions = document.getElementById(`section-questions-${sectionIndex}`);
                        const toggleIcon = this.querySelector('.toggle-icon');

                        if (sectionQuestions.classList.contains('collapsed')) {
                            // Expand
                            sectionQuestions.classList.remove('collapsed');
                            toggleIcon.classList.remove('collapsed');
                            toggleIcon.textContent = '▼';
                        } else {
                            // Collapse
                            sectionQuestions.classList.add('collapsed');
                            toggleIcon.classList.add('collapsed');
                            toggleIcon.textContent = '►';
                        }
                    });
                });
            });

            // Function to apply database answers to the UI
            function applyDatabaseAnswers() {
                <% sections.forEach((section, sectionIndex) => { %>
                    <% section.questions.forEach((question, questionIndex) => { %>
                        <% if (question.user_answer) { // Check if user_answer exists %>
                            // Use a self-executing function to create a new scope for each question
                            (function() {
                                const questionId = '<%= question.question_id %>';
                                const answer = String('<%= question.user_answer %>');
                                const isBookmarked = <%= question.is_bookmarked ? 'true' : 'false' %>;

                                // Find the question element
                                const questionElement = document.querySelector(`.question-item[data-question-id="${questionId}"]`);
                                if (!questionElement) return;

                                // Apply answer based on question type
                                if (answer && answer.match(/^\d+$/)) {
                                    // MCQ question
                                    const option = questionElement.querySelector(`.mcq-option[data-value="${answer}"]`);
                                    if (option) {
                                        const radio = option.querySelector('input[type="radio"]');
                                        if (radio) radio.checked = true;
                                        option.classList.add('selected');
                                    }
                                } else if (answer === 'true' || answer === 'false') {
                                    // True/False question
                                    const radio = questionElement.querySelector(`input[name="question${questionId}"][value="${answer}"]`);
                                    if (radio) radio.checked = true;
                                } else if (answer) {
                                    // Text answer
                                    const textarea = questionElement.querySelector(`#answer${questionId}`);
                                    if (textarea) textarea.value = answer;
                                }

                                // Apply bookmark status
                                if (isBookmarked) {
                                    const bookmarkBtn = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`);
                                    if (bookmarkBtn) {
                                        bookmarkBtn.classList.add('text-yellow-500');
                                    }
                                }

                                // Update question button to show answered status if there's an answer
                                if (answer) {
                                    const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                                    if (questionBtn) {
                                        questionBtn.classList.remove('unanswered');
                                        questionBtn.classList.add('answered');
                                    }
                                }

                                // Update question button to show bookmarked status
                                if (isBookmarked) {
                                    const questionBtn = document.querySelector(`.question-btn[data-question-id="${questionId}"]`);
                                    if (questionBtn) {
                                        questionBtn.classList.add('bookmarked');
                                    }
                                }
                            })(); // End of self-executing function
                        <% } %>
                    <% }); %>
                <% }); %>

                // Update the question palette
                updateQuestionPalette();
            }

            // Initialize first question
            initializeTest();

            // Always load answers from local storage first
            console.log('Loading answers from local storage');
            loadAnswersFromLocalStorage();

            // Then, for continued tests, also apply database answers if available
            const hasTestStartTime = <%= attempts && attempts.length > 0 && attempts[0].start_time ? 'true' : 'false' %>;
            const hasUserAnswers = <%= sections.some(section => section.questions.some(question => question.user_answer)) ? 'true' : 'false' %>;

            if (hasTestStartTime && hasUserAnswers) {
                // Also load answers from database if this is a continued test AND there are user answers
                console.log('Loading answers from database for continued test');
                applyDatabaseAnswers();
            }

            // Add a function to update question palette styling and summary counts
            function updateQuestionPalette() {
                const questionBtns = document.querySelectorAll('.question-btn');

                // Initialize counters for summary
                let totalAnswered = 0;
                let totalUnanswered = 0;
                let totalBookmarked = 0;

                questionBtns.forEach(btn => {
                    const questionId = btn.dataset.questionId;
                    const sectionIndex = parseInt(btn.dataset.section);
                    const questionIndex = parseInt(btn.dataset.question);
                    const isCurrent = (sectionIndex === currentSection && questionIndex === currentQuestion);

                    // Find the corresponding question
                    const questionItem = document.querySelector(`.question-item[data-question-id="${questionId}"]`);
                    if (!questionItem) return;

                    // Check for answer
                    let isAnswered = false;

                    // Check if MCQ and has selected option
                    const selectedOption = questionItem.querySelector('.mcq-option.selected');
                    if (selectedOption) {
                        isAnswered = true;
                    }

                    // Check if essay/text and has content
                    const textArea = questionItem.querySelector('textarea');
                    if (textArea && textArea.value && typeof textArea.value === 'string' && textArea.value.trim()) {
                        isAnswered = true;
                    }

                    // Check if true/false and has selection
                    const radioChecked = questionItem.querySelector('input[type="radio"]:checked');
                    if (radioChecked) {
                        isAnswered = true;
                    }

                    // Check for bookmark
                    const bookmarkBtn = document.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`);
                    const isBookmarked = bookmarkBtn && bookmarkBtn.classList.contains('text-yellow-500');

                    // Update button classes
                    if (isAnswered) {
                        btn.classList.remove('unanswered');
                        btn.classList.add('answered');
                    } else {
                        btn.classList.remove('answered');
                    }

                    if (isBookmarked) {
                        btn.classList.add('bookmarked');
                    } else {
                        btn.classList.remove('bookmarked');
                    }

                    // Handle current question highlighting
                    if (isCurrent) {
                        btn.classList.add('current');
                    } else {
                        btn.classList.remove('current');
                    }

                    // Update summary counters
                    if (isAnswered) {
                        totalAnswered++;
                    } else {
                        totalUnanswered++;
                    }
                    if (isBookmarked) {
                        totalBookmarked++;
                    }
                });

                // Use the new updateAllSummaryStats function instead of updating only desktop stats
                updateAllSummaryStats();
            }

            // Function to update section tabs with question counts
            function updateSectionTabs() {
                const sections = document.querySelectorAll('.section-content');

                sections.forEach((section, sectionIndex) => {
                    const questions = section.querySelectorAll('.question-item');
                    let answeredCount = 0;
                    let unansweredCount = 0;
                    let bookmarkedCount = 0;
                    let mcqCount = 0;
                    let trueFalseCount = 0;
                    let essayCount = 0;

                    questions.forEach(question => {
                        const questionId = question.dataset.questionId;

                        // Check if answered
                        const selectedOption = question.querySelector('.mcq-option.selected');
                        const textArea = question.querySelector('textarea');
                        const radioChecked = question.querySelector('input[type="radio"]:checked');

                        if ((selectedOption) ||
                            (textArea && textArea.value && typeof textArea.value === 'string' && textArea.value.trim()) ||
                            (radioChecked)) {
                            answeredCount++;
                        } else {
                            unansweredCount++;
                        }

                        // Check if bookmarked
                        const bookmarkBtn = question.querySelector(`.bookmark-btn[data-question-id="${questionId}"]`);
                        if (bookmarkBtn && bookmarkBtn.classList.contains('text-yellow-500')) {
                            bookmarkedCount++;
                        }

                        // Count question types
                        if (question.classList.contains('mcq-question') || question.querySelector('.mcq-grid')) {
                            mcqCount++;
                        } else if (question.classList.contains('true-false-question') || question.querySelector('input[type="radio"][value="true"]')) {
                            trueFalseCount++;
                        } else if (question.classList.contains('essay-question') || question.querySelector('textarea')) {
                            essayCount++;
                        }
                    });

                    // Update the section tab
                    const sectionTab = document.querySelector(`.section-tab[data-section="${sectionIndex}"]`);
                    if (sectionTab) {
                        // Update status counts
                        const statusCountsDiv = sectionTab.querySelector('div > div:first-child');
                        if (statusCountsDiv) {
                            const answeredSpan = statusCountsDiv.querySelector('span:nth-child(1)');
                            const unansweredSpan = statusCountsDiv.querySelector('span:nth-child(2)');
                            let bookmarkedSpan = statusCountsDiv.querySelector('span:nth-child(3)');

                            if (answeredSpan) answeredSpan.textContent = answeredCount;
                            if (unansweredSpan) unansweredSpan.textContent = unansweredCount;

                            if (bookmarkedCount > 0) {
                                if (bookmarkedSpan) {
                                    bookmarkedSpan.textContent = bookmarkedCount;
                                } else {
                                    // Create bookmarked span if it doesn't exist
                                    bookmarkedSpan = document.createElement('span');
                                    bookmarkedSpan.className = 'border border-yellow-500 text-yellow-600 px-1 rounded';
                                    bookmarkedSpan.title = 'Bookmarked';
                                    bookmarkedSpan.textContent = bookmarkedCount;
                                    statusCountsDiv.appendChild(bookmarkedSpan);
                                }
                            } else if (bookmarkedSpan) {
                                // Remove bookmarked span if count is 0
                                bookmarkedSpan.remove();
                            }
                        }

                        // Update question type counts
                        const typeCountsDiv = sectionTab.querySelector('div > div:last-child');
                        if (typeCountsDiv) {
                            // Clear existing spans
                            typeCountsDiv.innerHTML = '';

                            // Add MCQ count
                            if (mcqCount > 0) {
                                const mcqSpan = document.createElement('span');
                                mcqSpan.className = 'bg-blue-100 text-blue-800 px-1 rounded';
                                mcqSpan.title = 'Multiple Choice';
                                mcqSpan.textContent = `MCQ: ${mcqCount}`;
                                typeCountsDiv.appendChild(mcqSpan);
                            }

                            // Add True/False count
                            if (trueFalseCount > 0) {
                                const tfSpan = document.createElement('span');
                                tfSpan.className = 'bg-purple-100 text-purple-800 px-1 rounded';
                                tfSpan.title = 'True/False';
                                tfSpan.textContent = `T/F: ${trueFalseCount}`;
                                typeCountsDiv.appendChild(tfSpan);
                            }

                            // Add Essay count
                            if (essayCount > 0) {
                                const essaySpan = document.createElement('span');
                                essaySpan.className = 'bg-pink-100 text-pink-800 px-1 rounded';
                                essaySpan.title = 'Essay';
                                essaySpan.textContent = `Essay: ${essayCount}`;
                                typeCountsDiv.appendChild(essaySpan);
                            }
                        }
                    }
                });
            }

            // Call the update functions when page loads
            updateQuestionPalette();
            updateSectionTabs();

            // Also update after any user interaction with questions
            document.addEventListener('click', function(e) {
                if (e.target.closest('.mcq-option') ||
                    e.target.closest('.bookmark-btn') ||
                    e.target.classList.contains('answer-radio')) {
                    // Use setTimeout to allow the DOM to update first
                    setTimeout(() => {
                        updateQuestionPalette();
                        updateSectionTabs();
                    }, 100);
                }
            });

            // Listen for input in textareas
            document.querySelectorAll('.answer-text').forEach(textarea => {
                textarea.addEventListener('input', function() {
                    setTimeout(() => {
                        updateQuestionPalette();
                        updateSectionTabs();
                    }, 100);
                });
            });
        });

        // Function to toggle instructions overlay (for both mobile and desktop) is defined below

        // Function to toggle instructions overlay (for both mobile and desktop)
        function toggleInstructionsOverlay() {
            const overlay = document.getElementById('instructions-overlay');
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                overlay.classList.add('flex');
                document.body.classList.add('overflow-hidden');
            } else {
                overlay.classList.add('hidden');
                overlay.classList.remove('flex');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Function to toggle mobile question palette overlay
        function toggleMobilePalette() {
            const overlay = document.getElementById('mobile-palette-overlay');
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                overlay.classList.add('block');
                document.body.classList.add('overflow-hidden');

                // Update the mobile summary stats
                updateMobileSummaryStats();
            } else {
                overlay.classList.add('hidden');
                overlay.classList.remove('block');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Function to update all summary stats (both desktop and mobile)
        function updateAllSummaryStats() {
            // Only count desktop question buttons to avoid double counting
            // We exclude mobile-question-btn class to ensure we only count each question once
            const desktopButtons = document.querySelectorAll('.question-btn:not(.mobile-question-btn)');

            let totalCount = 0;
            let answeredCount = 0;
            let bookmarkedCount = 0;

            // Count the stats from desktop buttons
            desktopButtons.forEach(btn => {
                totalCount++;
                if (btn.classList.contains('answered')) answeredCount++;
                if (btn.classList.contains('bookmarked')) bookmarkedCount++;
            });

            const unansweredCount = totalCount - answeredCount;

            // Update desktop summary stats
            const desktopAnsweredEl = document.querySelector('.summary-answered');
            const desktopUnansweredEl = document.querySelector('.summary-unanswered');
            const desktopBookmarkedEl = document.querySelector('.summary-bookmarked');

            if (desktopAnsweredEl) desktopAnsweredEl.textContent = answeredCount;
            if (desktopUnansweredEl) desktopUnansweredEl.textContent = unansweredCount;
            if (desktopBookmarkedEl) desktopBookmarkedEl.textContent = bookmarkedCount;

            // Update mobile summary stats
            const mobileAnsweredEl = document.querySelector('.summary-answered-mobile');
            const mobileUnansweredEl = document.querySelector('.summary-unanswered-mobile');
            const mobileBookmarkedEl = document.querySelector('.summary-bookmarked-mobile');

            if (mobileAnsweredEl) mobileAnsweredEl.textContent = answeredCount;
            if (mobileUnansweredEl) mobileUnansweredEl.textContent = unansweredCount;
            if (mobileBookmarkedEl) mobileBookmarkedEl.textContent = bookmarkedCount;
        }

        // Function to update mobile summary stats (alias for backward compatibility)
        function updateMobileSummaryStats() {
            updateAllSummaryStats();
        }

        // Handle exit test button click to show confirmation dialog
        document.getElementById('exit-test-btn').addEventListener('click', function(e) {
            e.preventDefault();

            // Show the exit confirmation dialog
            showExitConfirmationDialog();
        });

        // Pause functionality removed

        // Exit test button in header has been removed
    </script>
</body>
</html>