const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const cookie = require('cookie');
const sessionParser = require('./config/session-config').sessionParser;

// Store active connections
const clients = new Map();

// Initialize WebSocket server
function initWebSocketServer(server) {
    const wss = new WebSocket.Server({ noServer: true });

    // Handle WebSocket connection
    wss.on('connection', function connection(ws, req, userId) {
        console.log(`WebSocket connection established for user ${userId}`);

        // Store the connection with the user ID
        if (!clients.has(userId)) {
            clients.set(userId, new Set());
        }
        clients.get(userId).add(ws);

        // Handle messages from clients
        ws.on('message', function incoming(message) {
            console.log(`Received message from user ${userId}: ${message}`);
        });

        // Handle connection close
        ws.on('close', function() {
            console.log(`WebSocket connection closed for user ${userId}`);
            // Remove the connection from the clients map
            if (clients.has(userId)) {
                clients.get(userId).delete(ws);
                if (clients.get(userId).size === 0) {
                    clients.delete(userId);
                }
            }
        });

        // Send a welcome message
        ws.send(JSON.stringify({
            type: 'connection',
            message: 'Connected to WebSocket server'
        }));
    });

    // Handle upgrade requests
    server.on('upgrade', function upgrade(request, socket, head) {
        const pathname = url.parse(request.url).pathname;

        if (pathname === '/ws') {
            // Parse the cookies
            const cookies = cookie.parse(request.headers.cookie || '');
            const sessionId = cookies['connect.sid'];

            if (!sessionId) {
                socket.destroy();
                return;
            }

            // Parse the session using the session middleware
            sessionParser(request, {}, (err) => {
                if (err) {
                    console.error('Session parsing error:', err);
                    socket.destroy();
                    return;
                }

                if (!request.session || !request.session.userId) {
                    console.log('No valid session or userId found');
                    socket.destroy();
                    return;
                }

                const userId = request.session.userId;
                console.log(`Upgrading WebSocket connection for user ${userId}`);

                // Authenticate the WebSocket connection
                wss.handleUpgrade(request, socket, head, function done(ws) {
                    wss.emit('connection', ws, request, userId);
                });
            });
        } else {
            socket.destroy();
        }
    });

    return wss;
}

// Function to force logout a user
function forceLogout(userId) {
    if (clients.has(userId)) {
        console.log(`Forcing logout for user ${userId}`);
        const userConnections = clients.get(userId);

        // Send logout message to all connections for this user
        userConnections.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'force-logout',
                    message: 'You have been logged out by an administrator'
                }));
            }
        });

        return true;
    }
    return false;
}

module.exports = {
    initWebSocketServer,
    forceLogout
};
